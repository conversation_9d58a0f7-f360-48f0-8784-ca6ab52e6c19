#
# Build file for 3G Dual Mode Protocol Stack Library on Hermon Platform
#
#   $Id: //central/main/tplgsm/bldstore/hsiupdlibdev/BUILD/hsiupdlibdev.bld#144 $
#   $Revision: #144 $
#   $DateTime: 2007/03/30 13:53:25 $
#
# Global definitions
#
[GLOBALS]
  BLD_DIRS     = %BLDDIR%
  BLD_DIRS     = %TPLGSM%\bldinc

  ALL_BEEPS_OFF
  #
  # TTPCom Protocol Stack Includes
  #
  INCLUDE_DIRS = %BLDDIR%

!include <include_dirs_hermon.bfi>
!include <include_dirs_common.bfi>

  INCLUDE_DIRS = %TPLGSM%\PLATFORMS\HERMON\DMINC;

  INCLUDE_DIRS = %TPLGSM%\;
  INCLUDE_DIRS = %TPLGSM%\UTINC;
  INCLUDE_DIRS = %TPLGSM%\ZLIB;
  INCLUDE_DIRS = %TPLGSM%\IPINC;
  INCLUDE_DIRS = %TPLGSM%\ALTEST\NVSIM;
  INCLUDE_DIRS = %TPLGSM%\L1INC;
  INCLUDE_DIRS = %TPLGSM%\DMINC;

  # Tavor platform includes using Intel directory structure
  # should be before Hermon (Hermon dirs don't need to be wipered off).
# <INCLUDE_DIRS type="Tavor" enabled="TRUE">
  INCLUDE_DIRS = %IROOT%\HOP\BSP\INC;
  INCLUDE_DIRS = %IROOT%\HOP\CORE\INC;
  INCLUDE_DIRS = %IROOT%\HOP\CPMU\INC;
  INCLUDE_DIRS = %IROOT%\HOP\CSW_MEMORY\INC;
  INCLUDE_DIRS = %IROOT%\HOP\DMA\INC;
  INCLUDE_DIRS = %IROOT%\HOP\EEHANDLER\INC;
  INCLUDE_DIRS = %IROOT%\HOP\FDI\INC;
  INCLUDE_DIRS = %IROOT%\HOP\INTC\INC;
  INCLUDE_DIRS = %IROOT%\HOP\KEYPAD\INC;
  INCLUDE_DIRS = %IROOT%\HOP\PM\INC;
  INCLUDE_DIRS = %IROOT%\HOP\PMU\INC;
  INCLUDE_DIRS = %IROOT%\HOP\RTC\INC;
  INCLUDE_DIRS = %IROOT%\HOP\STUBS\INC;
  INCLUDE_DIRS = %IROOT%\HOP\TICKMANAGER\INC;
  INCLUDE_DIRS = %IROOT%\HOP\UART\INC;
  INCLUDE_DIRS = %IROOT%\HOP\UART\SRC;
  INCLUDE_DIRS = %IROOT%\HOP\USIM_DRV\INC;
  INCLUDE_DIRS = %IROOT%\HOP\USIM_DRV\SRC;
  INCLUDE_DIRS = %IROOT%\HOP\WCIPHER\INC;
  INCLUDE_DIRS = %IROOT%\HOP\TIMER\INC;
  INCLUDE_DIRS = %IROOT%\CROSSPLATFORMSW\NVMCLIENT\INC\INCLUDE;
  INCLUDE_DIRS = %IROOT%\CROSSPLATFORMSW\NVMCLIENT\INC\FDI_ADD;
  INCLUDE_DIRS = %IROOT%\CROSSPLATFORMSW\NVMCLIENT\INC\FM_INC;
  INCLUDE_DIRS = %IROOT%\TAVOR\ARBEL\INC;
  INCLUDE_DIRS = %IROOT%\TAVOR\ENV\INC;
  INCLUDE_DIRS = %IROOT%\CSW\PLATFORM\INC_ARBEL;
  INCLUDE_DIRS = %IROOT%\CSW\PLATFORM\INC;
  INCLUDE_DIRS = %IROOT%\CSW\SYSCFG\INC;
  INCLUDE_DIRS = %IROOT%\AGPS\CORE\INC;
  INCLUDE_DIRS = %IROOT%\SPI\SPI_Wrapper\inc;
  INCLUDE_DIRS = %IROOT%\SPI\NXP\inc;
  INCLUDE_DIRS = %IROOT%\SPI\SPI_Wrapper\src\include;
  INCLUDE_DIRS = %IROOT%\SPI\SDV\inc;

# </INCLUDE_DIRS>

  #
  # Hermon platform includes using Intel directory structure
  #
# <lte_umts_gge type="tdscdma" enabled="FALSE">
   #INCLUDE_DIRS = %IROOT%\CRD\WCIPHER\INC;
   #INCLUDE_DIRS = \tdl1c\TDL1C\api\inc
   #INCLUDE_DIRS = \tdl1c\TDL1C\pub\src
   #INCLUDE_DIRS = \tdl1c\TDL1C\other
   #INCLUDE_DIRS = %tplgsm%\modem\phy\2g.typ\api\inc
   #INCLUDE_DIRS = %tplgsm%\modem\phy\2g.mod\api\inc
   #INCLUDE_DIRS = %tplgsm%\modem\phy\gp.mod\api\inc
   #INCLUDE_DIRS = %tplgsm%\modem\phy\3g.mod\api\inc
   #INCLUDE_DIRS = %tplgsm%\modem\phy\3g.mod\api\shinc
   #INCLUDE_DIRS = %tplgsm%\modem\phy\2g.typ\api\inc
   #INCLUDE_DIRS = %tplgsm%\modem\phy\2g.mod\api\inc
# </lte_umts_gge>


# <lte_umts_gge type="wcdma" enabled="TRUE">
  INCLUDE_DIRS = %IROOT%\APLP\APLP\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\PLW\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\PLUGIN\INC
  INCLUDE_DIRS = %IROOT%\APLP\APLP_MISC\INC;
  INCLUDE_DIRS = %IROOT%\APLP\MCL\INC;
  INCLUDE_DIRS = %IROOT%\CSW\BSP\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\DRAT\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\WB\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\PLW\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\CELLULARPOWERAPPLICATION\INC;
  INCLUDE_DIRS = %IROOT%\ENV\WIN32\INC;
  INCLUDE_DIRS = %IROOT%\GPLC\GPLC\INC;
  INCLUDE_DIRS = %IROOT%\GPLC\L1C\INC;
  INCLUDE_DIRS = %IROOT%\GPLC\L1C\SRC\INCLUDE;
  INCLUDE_DIRS = %IROOT%\GPLC\L1GKI\INC;
  INCLUDE_DIRS = %IROOT%\GPLC\L1GKI\INC\GPINC;
  INCLUDE_DIRS = %IROOT%\GPLC\L1GKI\INC\PSINC;
  INCLUDE_DIRS = %IROOT%\GPLC\ABP\INC;
  INCLUDE_DIRS = %IROOT%\GPLC\PLKINC;
  INCLUDE_DIRS = %IROOT%\gplc\GenericRfDriver\inc;
  INCLUDE_DIRS = %XROOT%\hop\intc\inc
  INCLUDE_DIRS = %XROOT%\hop\intc\inc
  INCLUDE_DIRS = %XROOT%\csw\pm\inc
  INCLUDE_DIRS = %XROOT%\hop\pm\inc
  INCLUDE_DIRS = %XROOT%\softutil\tickmanager\inc
  INCLUDE_DIRS = %XROOT%\hop\timer\inc
  INCLUDE_DIRS = %XROOT%\csw\syscfg\inc
  INCLUDE_DIRS = %XROOT%\hal\gpio\inc
  INCLUDE_DIRS = %XROOT%\gplc\l1c\src\include
  INCLUDE_DIRS = %XROOT%\gplc\l1gki\inc\psinc
  INCLUDE_DIRS = %XROOT%\aplp\aplp\inc
  INCLUDE_DIRS = %XROOT%\gplc\l1gki\inc
  INCLUDE_DIRS = %XROOT%\crd\dtc\inc
# </lte_umts_gge>

  INCLUDE_DIRS = %IROOT%\DRAT\PLW\INC;
  INCLUDE_DIRS = %IROOT%\CSW\BSP\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\DRAT\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\WB\INC;
  INCLUDE_DIRS = %IROOT%\DRAT\PLW\INC;
  INCLUDE_DIRS = %IROOT%\ENV\WIN32\INC;
  INCLUDE_DIRS = %IROOT%\GPLC\GPLC\INC;
  INCLUDE_DIRS = %IROOT%\HERMON\HERMOND\INC;
  INCLUDE_DIRS = %IROOT%\os\threadx\inc;
  INCLUDE_DIRS = %IROOT%\OS\NU_XSCALE\INC;
  INCLUDE_DIRS = %IROOT%\os\osa\inc;
  INCLUDE_DIRS = %IROOT%\sac\SAC\INC;
  INCLUDE_DIRS = %IROOT%\pcac\CI\INC;
  INCLUDE_DIRS = %IROOT%\pcac\atcmdsrv\inc;

  INCLUDE_DIRS = %IROOT%\HAL\CLCD\INC;
  INCLUDE_DIRS = %IROOT%\HAL\CLCD\SRC;
  INCLUDE_DIRS = %IROOT%\HAL\CORE\INC;
  INCLUDE_DIRS = %IROOT%\HAL\DMA\INC;
  INCLUDE_DIRS = %IROOT%\HAL\GPIO\INC;
  INCLUDE_DIRS = %IROOT%\HAL\I2S\INC;
  INCLUDE_DIRS = %IROOT%\HAL\I2S\SRC;
  INCLUDE_DIRS = %IROOT%\HAL\INTC\INC;
  INCLUDE_DIRS = %IROOT%\HAL\KEYPAD\INC;
  INCLUDE_DIRS = %IROOT%\HAL\LCD\INC;
  INCLUDE_DIRS = %IROOT%\HAL\LCD\SRC;
  INCLUDE_DIRS = %IROOT%\HAL\LCDIF\INC;
  INCLUDE_DIRS = %IROOT%\HAL\MMCSD\INC;
  INCLUDE_DIRS = %IROOT%\HAL\MMCSD\SRC;
  INCLUDE_DIRS = %IROOT%\HAL\MMU\INC;
  INCLUDE_DIRS = %IROOT%\HAL\RTC\INC;
  INCLUDE_DIRS = %IROOT%\HAL\PMU\INC;
  INCLUDE_DIRS = %IROOT%\HAL\UART\INC;
  INCLUDE_DIRS = %IROOT%\HAL\UART\SRC;
  INCLUDE_DIRS = %IROOT%\HAL\USB\INC;
  INCLUDE_DIRS = %IROOT%\HAL\USB\SRC;
  INCLUDE_DIRS = %IROOT%\HAL\USBMGR\INC;
  INCLUDE_DIRS = %IROOT%\HAL\USBMGR\SRC;
  INCLUDE_DIRS = %IROOT%\HAL\USB_CABLE\INC;
  INCLUDE_DIRS = %IROOT%\HAL\USB_DEVICE\INC;
  INCLUDE_DIRS = %IROOT%\HAL\USB_STANDART\INC;
  INCLUDE_DIRS = %IROOT%\HAL\USIM\INC;
  INCLUDE_DIRS = %IROOT%\HAL\USIM\SRC;
  INCLUDE_DIRS = %IROOT%\CRD\WCIPHER\INC;
  INCLUDE_DIRS = %IROOT%\CRD\DTC\INC;
  INCLUDE_DIRS = %IROOT%\HAL\TIMER\INC;

  INCLUDE_DIRS = %IROOT%\AUD_SW\ACM\INC;
  INCLUDE_DIRS = %IROOT%\AUD_SW\AUC\INC;

  INCLUDE_DIRS = %IROOT%\SOFTUTIL\CSW_MEMORY\INC;
  INCLUDE_DIRS = %IROOT%\DIAG\DIAG_LOGIC\INC;
  INCLUDE_DIRS = %IROOT%\DIAG\DIAG_LOGIC\SRC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\DIAG\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\DIAG\SRC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\EEHANDLER\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI\SRC\DAV_INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI\SRC\ETC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI\SRC\FDI_ADD;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI\SRC\FM_INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI\SRC\INCLUDE;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI_7_1\SRC\COMMON\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI_7_1\SRC\FFSCORE\MFM\MUSB\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI_7_1\SRC\PLATFORM\FLASHAPI\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI_7_1\SRC\PLATFORM\MTD\RTOS\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI_7_1\SRC\PLATFORM\OSLAYER\RTOS\NUCLEUS\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\FDI_7_1\SRC\PLATFORM\OSLAYER\RTOS\API\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\NVM\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\SOFTUTIL\INC;
  INCLUDE_DIRS = %IROOT%\SOFTUTIL\TICKMANAGER\INC;
  INCLUDE_DIRS = %IROOT%\CSW\PM\INC;

### STUB directory should be the last directory referenced
  INCLUDE_DIRS = %TPLGSM%\PLATFORMS\HERMON\HASTUB;

  SOURCE_DIRS  = .
  SOURCE_DIRS  = %TPLGSM%\platforms\hermon\dmcode
  SOURCE_DIRS  = %TPLGSM%\DMCODE
  SOURCE_DIRS  = %TPLGSM%\sys\gki.mod\pub\src
  SOURCE_DIRS  = %TPLGSM%\utcode
  SOURCE_DIRS  = %TPLGSM%\modem\pscommon\%3g_asn1_mode_dir%\lib\src

  OBJDIR       = obj
  FLSDIR       = .;%TPLGSM%\cat
  TGTDIR       = lib

  STRICT_CHECK_OFF
#  OPT_DEPEND_ON

  <PRE_OPS>
   if exist %TPLGSM%\bldstore\%TTPCOM_BUILD%\build\commond\ps_version.o del %TPLGSM%\bldstore\%TTPCOM_BUILD%\build\commond\ps_version.o
    echo.
    echo 3G Dual Mode Protocol Stack Library Build on Hermon Platform
    echo.

: Environment variables used by tcc.bat and diag post build commands




# <EVB_type type="Hermon_PDK" enabled="FALSE">
       #echo Target is Hermon PDK platform
# </EVB_type>

# <EVB_type type="Tavor_EVBII" enabled="TRUE">
      echo Target is Tavor EVBII platform
# </EVB_type>

# <2CHIP type="ON" enabled="TRUE">
      echo Target is 2CHIP platform
# </2CHIP>

# <TINI_MODEL type="ON" enabled="FALSE">
      #echo Target is 2CHIP+TINI_MODEL platform
# </TINI_MODEL>

# <Application_Layer type!="ABAPP" enabled="FALSE">
      #echo Target without AB_APP layer
# </Application_Layer>
#######################################################
## NVRAM over FDI adaptation
#######################################################
# <NVRAM type="OFF" enabled="FALSE">
#      echo NVRAM is OFF
# </NVRAM>
# <NVRAM type="ON" enabled="TRUE">
      echo NVRAM is ON
# </NVRAM>
#######################################################

#######################################################
## File system over FDI adaptation
#######################################################
# <Filesystem type="OFF" enabled="TRUE">
    echo FileSystem is OFF
# </Filesystem>
# <Filesystem type="ON" enabled="FALSE">
     #echo FileSystem is ON
# </Filesystem>
#######################################################

#######################################################
## USB state
#######################################################
# <USB type="OFF" enabled="FALSE">
    #echo USB is OFF
# </USB>
#------------------------------------------------------
# <USB type="ON" enabled="TRUE">
   echo USB is ON
# </USB>
#------------------------------------------------------
# <USB_INTERFACE type="NONE" enabled="FALSE">
   #echo USB_INTERFACE is not defined
# </USB_INTERFACE>
#------------------------------------------------------
# <USB_INTERFACE type="COMPOSITE [EMMI_MODEM_MASS_STORAGE]" enabled="FALSE">
   #echo USB_INTERFACE is COMPOSITE [EMMI_MODEM_MASS_STORAGE]
# </USB_INTERFACE>
#------------------------------------------------------
# <USB_INTERFACE type="COMPOSITE [MODEM_MASS_STORAGE]" enabled="FALSE">
   #echo USB_INTERFACE is COMPOSITE [MODEM_MASS_STORAGE]
# </USB_INTERFACE>
#------------------------------------------------------
# <USB_INTERFACE type="COMPOSITE [EMMI_MODEM]" enabled="FALSE">
   #echo USB_INTERFACE is COMPOSITE [EMMI_MODEM]
# </USB_INTERFACE>
#------------------------------------------------------
# <USB_INTERFACE type="COMPOSITE [MODEM]" enabled="TRUE">
   echo USB_INTERFACE is COMPOSITE [MODEM]
# </USB_INTERFACE>
#------------------------------------------------------
# <USB_INTERFACE type="COMPOSITE_[EMMI]" enabled="FALSE">
   #echo USB_INTERFACE is COMPOSITE [EMMI]
# </USB_INTERFACE>
#------------------------------------------------------
# <USB_INTERFACE type="DYNAMIC [Mo_Ma_Em_Di][Mo_Ma]" enabled="FALSE">
  #echo USB_INTERFACE is DYNAMIC [EMMI_MODEM_MASS_STORAGE] OR [MODEM_MASS_STORAGE]
# </USB_INTERFACE>

#######################################################
# <Debugging type="HermonSysTools" enabled="TRUE">
  echo SysTools debugging enabled.
# </Debugging>
# <Debugging type="OFF" enabled="FALSE">
#   echo SysTools debugging disabled.
# </Debugging>
# <FlexibleTrace type="ON" enabled="TRUE">
  echo Flexible Trace Framework enabled.
# </FlexibleTrace>
# <FlexibleTrace type="OFF" enabled="FALSE">
#   echo Flexible Trace Framework disabled.
# </FlexibleTrace>

#######################################################
## AssistedGPS state
#######################################################
# <AssistedGPS type="ON" enabled="TRUE">
   echo AssistedGPS enabled.
# </AssistedGPS>
# <AssistedGPS type="OFF" enabled="FALSE">
  #echo AssistedGPS disabled.
# </AssistedGPS>

#######################################################
## geranBuild
#######################################################
# <geranBuild type="OFF" enabled="TRUE">
   echo geranBuild disabled.
# </geranBuild>
# <geranBuild type="ON" enabled="FALSE">
  #echo geranBuild enabled.
# </geranBuild>

#######################################################
## EngineeringMode options
#######################################################
# <EngineeringMode type="AT + LCD" enabled="TRUE">
   echo Engineering Mode: AT interface enabled
# </EngineeringMode>
# <EngineeringMode type="LCD" enabled="FALSE">
   #echo Engineering Mode: AT interface disabled
# </EngineeringMode>

#######################################################
## 3GPP Release Version and Features
#######################################################
# <3gppReleaseVersionFeatures type="RELEASE_3" enabled="FALSE">
#echo 3GPP Release Version is R99
# </3gppReleaseVersionFeatures>
# <3gppReleaseVersionFeatures type="RELEASE_4" enabled="FALSE">
#echo 3GPP Release Version is Release 4
# </3gppReleaseVersionFeatures>
# <3gppReleaseVersionFeatures type="RELEASE_5" enabled="FALSE">
#echo 3GPP Release Version is Release 5
# </3gppReleaseVersionFeatures>
# <3gppReleaseVersionFeatures type="RELEASE_5_HSDPA" enabled="FALSE">
#echo 3GPP Release Version is Release 5 with HSDPA
# </3gppReleaseVersionFeatures>
# <3gppReleaseVersionFeatures type="RELEASE_6" enabled="FALSE">
#echo 3GPP Release Version is Release 6
# </3gppReleaseVersionFeatures>
#######################################################

#######################################################
## Build without debug and link using pre-built L1 libs
#######################################################
# <LOCAL_LINK type="OFF" enabled="TRUE">
echo Local link is OFF
# </LOCAL_LINK>
# <LOCAL_LINK type="ON" enabled="FALSE">
#echo Local link is ON
# </LOCAL_LINK>
#######################################################
## Genie over ICAT
#######################################################
# <GenieOverICAT type="ON" enabled="TRUE">
 echo Genie over ICAT is ON
# </GenieOverICAT>

# Keep a copy of the current TPLGSM & IROOT env variables
    set OLDTPLGSM=%TPLGSM%
    set OLDIROOT=%IROOT%
# Change to the root directory for the current drive
    pushd \
# Search for a drive letter ("?:\") in TPLGSM
    echo %TPLGSM% | findstr :\
# If a drive letter is not found in TPLGSM, prepend the current drive letter to %TPLGSM%
    if errorlevel 1 set TPLGSM=%CD%%TPLGSM%
# Search for a drive letter ("?:\") in IROOT
    echo %IROOT% | findstr :\
# If a drive letter is not found in IROOT, prepend the current drive letter to %IROOT%
    if errorlevel 1 set IROOT=%CD%%IROOT%
# Restore to the build directory
    popd
# Generate the .i file
    %TPLGSM%\genie\geniebld -g%BLDDIR%\..\test\%BLDNAME%.gni -b%BLDDIR%\%BLDNAME%.bld -s%BLDDIR%\..\test\%BLDNAME%.i
    if %ERRORLEVEL% NEQ 0  (
    echo Install Borland 5 or Visual Studio C/C++ compiler or check the return status
    goto makeerror )
# Generate kistaticfilter.h file
 ATTRIB -R  %TPLGSM%\sys\gki.mod\pub\src\kistaticfilter.h
# <lte_umts_gge type="wcdma" enabled="TRUE">
 %TPLGSM%\tools\SetStaticFilter.exe  %BLDDIR%\..\test\%BLDNAME%.i   %TPLGSM%\sys\gki.mod\pub\src\kistaticfilter.h     %BLDDIR%\lwg_signal_filter.txt
# </lte_umts_gge> 
# <lte_umts_gge type="tdscdma" enabled="FALSE">
#%TPLGSM%\tools\SetStaticFilter.exe  %BLDDIR%\..\test\%BLDNAME%.i   %TPLGSM%\sys\gki.mod\pub\src\kistaticfilter.h     %BLDDIR%\ltg_signal_filter.txt
# </lte_umts_gge> 
    if %ERRORLEVEL% NEQ 0  (
    echo SetStaticFilter.exe failed to create kistaticfilter.h
    goto makeerror )

# Restore the original value of TPLGSM & IROOT env variables
    set TPLGSM=%OLDTPLGSM%
    set IROOT=%OLDIROOT%
  <POST_OPS>
    pushd obj
    if exist 3g_ps_all.pp del 3g_ps_all.pp /q
    echo.
    echo archive pp files
    echo.
 
#   FOR /F  %%i IN ('dir /s /b *.pp') DO type %%i >> 3g_ps_all.pp
    copy *.pp 3g_ps_all.pp > nul
    popd

    pushd lib
    FOR /F  %%i IN ('dir /s /b *.lib') DO %TPLGSM%\tools\size.exe -t %%i > %%i.size.csv
    FOR /F  %%i IN ('dir /s /b *.lib') DO %TPLGSM%\tools\nm.exe -s --size-sort %%i > %%i.nm.csv
    popd

    echo.
    echo 3G Dual Mode Protocol Stack Library Build on Hermon Platform
    echo.

[TARGETS]

#######################################################
## Link using pre-build Intel Libraries
#######################################################
# <LOCAL_LINK type="ON" enabled="FALSE">
   #{hermond.bin}
     #TOOLSET     = ARM
     #TGT_DEPS    = %IROOT%\HERMON\HERMOND\BIN\hermond.axf
     #<RESOLVES>
         #call fromelf -bin %IROOT%\HERMON\HERMOND\BIN\hermond.axf -output %IROOT%\HERMON\HERMOND\BIN\hermond.bin
         #dwarf2bd -hx -sd /=K:/ -sd \=K:\ %IROOT%\HERMON\HERMOND\BIN\hermond.axf %IROOT%\HERMON\HERMOND\BIN\hermond.bd
         #echo "DONE" > %TGTDIR%\hermond.bin

   #{hermond.axf}
     #TGT_DEPS = HERMOND_INT.LIB HERMOND_EXT1.LIB HERMOND_EXT2.LIB
     #TOOLSET  = ARM
     #<RESOLVES>
         #LINKER ARM_LINK (OBJECTS)
         #if exist %IROOT%\HERMON\HERMOND\BIN\hermond.axf del %IROOT%\HERMON\HERMOND\BIN\hermond.axf
         #copy %TGTDIR%\hermond.axf %IROOT%\HERMON\HERMOND\BIN\hermond.axf

# </LOCAL_LINK>



  {USBD.LIB}
    TOOLSET  = ARM
    OBJDIR   = usbd
    RESOLVE  = LIBRARIAN LIB_PRE (OBJECTS)
    <FLS_DEPS>
# <USB type="ON" enabled="TRUE">
       ## USB Adaptation Layer
       USBHERMON             COMPILER     TCC     UPSDEFS COMMON  DEBUG_PLATFORM
       DV_USB_CLASS_PUB      COMPILER     TCC     UPSDEFS COMMON  DEBUG_PLATFORM
# </USB>








#########################################################################################################################
# <Application_Layer type="ABAPP" enabled="TRUE">
       AB_APP            COMPILER    TCC     UPSDEFS COMMON  DEBUG_AB
# </Application_Layer>

       # Display Drivers
       DI2               COMPILER   TCC      UPSDEFS COMMON  DEBUG_PLATFORM

       # LCD Adaptation Layer
       HALCD             COMPILER    TCC     UPSDEFS COMMON  DEBUG_HAW

# ABSORB Task. The absorb task receives all signal types and deletes them.
# This is useful when a destination task is needed for IND and CNF signals.
# E.g. testing a task on EVB using GENIE/COOLART scripts.
#
#      absorb           COMPILER     TCC     UPSDEFS COMMON

[OPTIONS]

  {LIB_PRE}

  {TCC}

# < RVCT_ARMV4T_LE type="ON" enabled="TRUE">
    ## Compiles ANSI standard C
    --c90
    --diag_error 1,9,47,68,69,111,117,120,127,144,152,167,174,175,177,186,187,188,192,223,513,549,550,940,991,1295,2874,236,401,2084,2748,3017
    ## Compile only (no link)
    -c
    --thumb
    --cpu Cortex-R4 --no_unaligned_access ##Xscale

    ## Debug info (for source-level emulation)
    ## Defining this will substantially increase compile/link time
    ## -g

    ## adsabi directive is planned to be removed later.
    --apcs /noswst/inter
    --library_interface=aeabi_clib

    ## Enumerations as integers
    ## -fy

    ## K&R search rules
    --kandr_include

    ## For removing unreferenced symbols at link time
    ##--split_sections

    ## List file options
    ## -list
    ## -fi
    ## -fj

    ## Assembler output options
    ## Define these to cause the .o file to contain assembler and *not* object code
    ## -fs
    ## -S
    ##--strict_warnings
    ## suppress C++ style comment (//) errors as they are used widely in Intel code.
    --diag_remark 169
    ##--remarks
    ## 2084 - suppress "support for --apcs /adsabi is deprecated".
    ##--diag_suppress 186,236,401,2084
# </RVCT_ARMV4T_LE>
# <ADS_ARMV4T_LE type="ON" enabled="FALSE">
    ## Generate ANSI 'C' code
    #-ansi
    ## K&R search rules
    #-fk
    ## Flags delivered from CBA environment
    #-c
    #-cpu Xscale
    #-Wy
    #-apcs /noswst/inter
    #-j"%ARMINC%"
    #-I"%ARMINC%"

    ## Create Dependance Files
    ##-MD
# </ADS_ARMV4T_LE>
# <CodeOptimisation type="O2" enabled="TRUE">
    -O2
    -Ospace

# </CodeOptimisation>
# <CodeOptimisation type="O1" enabled="FALSE">
    #-O1
# </CodeOptimisation>

   {UPSDEFS}
  %BLD_INCLUDES%
  #-DDEFAULT_MTC_FTF_LOGGING_CATEGORY=0xffffffff
  #-DDEFAULT_SMC_FTF_LOGGING_CATEGORY=0xffffffff
   -DDEFAULT_CSR_FTF_LOGGING_CATEGORY=0x00000205
  #-DDEFAULT_SIR_FTF_LOGGING_CATEGORY=0xffffffff
   -DDEFAULT_CER_FTF_LOGGING_CATEGORY=0xffffffff
  #-DDEFAULT_RBC_FTF_LOGGING_CATEGORY=0xffffffff
  #-DDEFAULT_CMR_FTF_LOGGING_CATEGORY=0xffffffff
  #-DDEFAULT_AIS_FTF_LOGGING_CATEGORY=0xffffffff
  #-DDEFAULT_MCR_FTF_LOGGING_CATEGORY=0xffffffff

-DENABLE_SIR_STRICT_CHECKING

##  -DDM_EXCLUDE_PM_DEVICE_MANAGER

##  -DDM_EXCLUDE_BACKLIGHT_PLATFORM_DRIVER

##    -DDM_EXCLUDE_ADC_DEVICE_MANAGER
  -DDM_EXCLUDE_ADC_DEVICE_DRIVER

#### GPIO not accessible on Hermon
  -DDM_EXCLUDE_GPIO_PLATFORM_DRIVER

    # ABSORB Task. The absorb task receives all signal types and deletes them.
    # This is useful when a destination task is needed for IND and CNF signals.
    # E.g. testing a task on EVB using GENIE/COOLART scripts.
    #
    #-DUSE_ABSORB_TASK

# <GenieOverICAT type="ON" enabled="TRUE">
    -DNO_EMMI_INTERFACE
    -DKI_COMMS_TASK_ID=HA_COMMS_TASK_ID
# </GenieOverICAT>

## Reduce code and RAM size
##
##   The sizeof(RAM-LOG line) = 20 bytes
##
# <2CHIP type="OFF" enabled="FALSE">
    ## increase the GKI log memory by eight times
    #-DKI_MAX_LOGGING_MEMORY=245760
# </2CHIP>
# <2CHIP type="ON" enabled="TRUE">
    ## DevAssert size, RAM-LOG size
    -DSHORT_DEV_ASSERTS

    ## GKE Ramlog buffer should be a power of 2, for increment optimization
    -DKI_RLG_MAX_RLOG_GKE_ENTRIES=16384

    ## Use this value for SMALL RAM modell - e.g. 2CHIP-MCP or HARBELL 10MB
    ##-DKI_RLG_MAX_RLOG_GKE_ENTRIES=512
# </2CHIP>

    # Kernel Defines
# Assertfail configuration
    -DKI_ASSERTFAIL_ENABLE_CONFIG
## Serial port configuration

# <lte_umts_gge type="wcdma" enabled="TRUE">
    ### Enabele RLC trace log
    ##-DKI_RLG_ENABLE_URLC_TRACE_LOG

    ### Enable RLC task flow protection semaphore
    -DURLC_TASK_FLOW_PROTECT

    ### Enabele L2 control signals trace log
    -DKI_RLG_ENABLE_L2_CONTROL_SIG_TRACE_LOG
# </lte_umts_gge>

    # Enable KI_USE_ASSERT_SIGNALS to continue after an assert and have the assert
    # logged. This should only be used when logging, and if you know that the assert
    # is non-fatal (otherwise it might not get out of the target before it crashes)
    #-DKI_USE_ASSERT_SIGNALS

    #-DKI_STATISTICS
    #-DKI_MEM_POOL_STATISTICS


    ##-DDTC_TEST_MODE
    # add define
    #-DKI_DISABLE_TASK_SETS
    # Additional define
    -DTRANSFER_MODIFICATION
    -DDISABLE_L1AL_AUDIO
    -DDISABLE_L1AM
    #-DENABLE_NEW_URLC_OPTIMISATIONS
    #log all signals before filter is received - not used anymore - using C8 solution
    #-DKI_TTI_LOG_ALL_SIGNALS_FROM_BOOT
    -DKI_TTI_ENABLE_STATIC_FILTER
    # Disable the following define to use TMM
    #-DDATA_IN_SIGNAL
    -DUPGRADE_45_08_CR_250_LB_MS
    -DUPGRADE_4460_CR_49_TBF_ESTAB
    -DUPGRADE_4460_CR_665_EXT_UL_TBF
    -DUPGRADE_44_60_CR_599_ENHANCED_PSCD
    -DUPGRADE_44_60_CR_622_ENHANCED_PNCD
    -DUPGRADE_45_08_CR_245_FDD_BARRED
    -DUPGRADE_25_331_CR_2104_CELL_LESS_REDIRECTION
    -DUPGRADE_44060_CR_595_FDD_THR_2
    -DUPGRADE_DUAL_LINK
    -DUPGRADE_44065_CR_15_UNKWN_ALGO_TYPE
    -DUPGRADE_44064_CR_8_UI_FRAME_NO_INFO
   #-DUPGRADE_44006_CR_A015_RANDOMIZE_L2_FRAME_FILL_BITS
### Log GSM reselection
    -DGRR_RESELECTION_MONITOR
    -DUPGRADE_FAST_ACQUISITION

    -DUPGRADE_MORE_THAN_3_FDD_FREQ_IN_GSM
    -DUPGRADE_Q6_GRR
    -DLTE_RR_MEAS_Q6
    -DLTE_RR_MEAS_Q6_2
    -DL1A_MEMORY_OPTIMIZATION
    -DLTE_DS_SUSPEND_BY_SIM2
### -DUPGRADE_FDD_UTILIZE_IDLE_SC_FOR_PTM

#<3gppReleaseVersionFeatures type="RELEASE_4" enabled="FALSE">
#    -DUPGRADE_3G_RELEASE_4
#</3gppReleaseVersionFeatures>
#<3gppReleaseVersionFeatures type="RELEASE_5" enabled="FALSE">
    #-DUPGRADE_3G_RELEASE_4
    #-DUPGRADE_3G_RELEASE_5
    #-DUPGRADE_REL5
    #-DUPGRADE_R4_FS1
    #-DENABLE_3G_CCO_GERAN_SI_HANDLING
#</3gppReleaseVersionFeatures>
#<3gppReleaseVersionFeatures type="RELEASE_5_HSDPA" enabled="FALSE">
    #-DUPGRADE_3G_RELEASE_4
    #-DUPGRADE_3G_RELEASE_5
    #-DUPGRADE_REL5
    #-DUPGRADE_R4_FS1
    #-DUPGRADE_3G_HSDPA
    #-DUPS_CFG_HS_DSCH_CATEGORY_8
#</3gppReleaseVersionFeatures>

    -DENABLE_3G_CCO_GERAN_SI_HANDLING
    -DUPGRADE_REL6_SAIC
    
#<3gppReleaseVersionFeatures type="RELEASE_6" enabled="FALSE">
    #-DUPGRADE_3G_RELEASE_4
    #-DUPGRADE_3G_RELEASE_5
    #-DUPGRADE_REL5
    #-DUPGRADE_R4_FS1
    #-DENABLE_3G_CCO_GERAN_SI_HANDLING
    #-DUPGRADE_3G_RELEASE_6
	#-DUPGRADE_3G_RELEASE_7
    #-DUPGRADE_3G_EDCH
    #-DRLCDEC_R6
    ##-DUPGRADE_3G_FDPCH remove for cat1 lwg
##    -DUPGRADE_3G_FDPCH_DEBUG
    #-DUPGRADE_REL6_SAIC
    #-DL1_UPGRADE_R6
	#-DL1_UPGRADE_R7
    #-DUPGRADE_3G_HSDPA
    #-DUPS_CFG_HS_DSCH_CATEGORY_8
    #-DENABLE_OPT_DATA_FLOW_OVER_SHMEM
#</3gppReleaseVersionFeatures>
#<lte_umts_gge type="tdscdma" enabled="FALSE">
    #-DUPGRADE_3G_RELEASE_4
    #-DUPGRADE_3G_RELEASE_5
    #-DUPGRADE_REL5
    #-DUPGRADE_3G_HSDPA
    #-DUPS_CFG_HS_DSCH_CATEGORY_14
    #-DUPGRADE_3G_RELEASE_7
    #-DUPGRADE_3G_HSUPA
    #-DUPS_CFG_EDCH_CATEGORY_6
    #-DENABLE_NEW_URLC_OPTIMISATIONS
    #-DUPGRADE_4G
    #-DUM_RX_OPT
    #-DUPGRADE_R4_FS1
    #-DENABLE_HS_PDULISTINFO_FUNCTION_CALL
    #-DUMA_HS_ENABLE_DATAIND_FUNC
    #-DENABLE_TVRTD_MULTICARRIER_OPT
    #-DENABLE_ULDATA_FUNCTION_CALL
    #-DENABLE_PDULISTINFO_FUNCTION_CALL
# </lte_umts_gge>

#<lte_umts_gge type="wcdma" enabled="TRUE">
    -DUPGRADE_3G_RELEASE_4
    -DUPGRADE_3G_RELEASE_5
    -DUPGRADE_REL5
    -DUPGRADE_R4_FS1
    -DENABLE_3G_CCO_GERAN_SI_HANDLING
    -DUPGRADE_3G_RELEASE_6
    -DUPGRADE_3G_RELEASE_7
    -DUPGRADE_ASN1_R9
    ##-DUPGRADE_3G_EDCH  remove for cat1 lwg
    -DRLCDEC_R6
    ##-DUPGRADE_3G_FDPCH  remove for cat1 lwg
    -DUPGRADE_REL6_SAIC
    -DL1_UPGRADE_R6
    -DL1_UPGRADE_R7
    ##-DUPGRADE_3G_HSDPA remove for cat1 lwg
    -DUM_RX_OPT
    -DRLC_LOW_PRIOR_OPT
    -DUPS_CFG_HS_DSCH_CATEGORY_10
    -DUPS_CFG_HS_DSCH_CATEGORY_EXT_14
    ## ********** begin
    -DENABLE_END_OF_DRX_MEAS_IND  
    ## ********** end
    ##-DUPGRADE_CSG1
    ##-DCSG_NAS_INTEGRATION
    ##-DCSG_IS_READY_FOR_INTEGRATION
    
    ##-DUPS_CFG_EDCH_CATEGORY_7      remove for cat1 lwg
    
    -DRRC_SIR_OPTIMIZATION
    -DVOID_SPECIAL_VALUE_OF_HE
    -DENABLE_READ_SIB19_FOR_23G
    -DCR_4979_CORRECTION_OF_SRB1_FOR_FACH
    ##-DDEBUG_MEMORY_LEAK_ON_PC
    -DENABLE_OPT_DATA_FLOW_OVER_SHMEM
    -DUPGRADE_24_08_CR_974_CELL_UPDATE
    -DUPGRADE_44_18_CR_287_REDIRECTION
    -DINTEL_UPGRADE_AMR_L1_INTEG
    -DUPGRADE_AMR_PS
    ##-DURL_DEBUG_HSDPA_OVER_DTC    remove for cat1 lwg
    ##-DURL_DEBUG_EDCH_OVER_DTC     remove for cat1 lwg
    -DPS_L2_R8_API
    -DUPGRADE_R8

    -DUPGRADE_LTE_MEAS_FACH
	
    ##********** - htfeng 
    -DUPGRADE_DSDS
    ##-DUPGRADE_DSDS_EXCLUDE_SIM2_L1

    ##**********
    ##-DUPGRADE_ECID  remove for cat1 lwg
    ##-DUPGRADE_ESCC  remove for cat1 lwg
    ##-DL1_ESCC_READY remove for cat1 lwg
    ##-DUPGRADE_ERR_CSG_USING_PLMS
    ## ********** Begin
    
    ## 3G E-DPDCH Power Interpolation
    ##-DUPGRADE_3G_POWER_INTERPOLATION  remove for cat1 lwg

    ## 3G UL 16QAM
    ##-DUPGRADE_3G_UL16QAM    remove for cat1 lwg     
    
    ## 3G Dual Carrier in URR
    ##-DRRC_DC_HSDPA   remove for cat1 lwg
    ##-DUPGRADE_UL_ECF
    ## Enhanced L2  
    ##-DUPGRADE_3G_UL_EL2    remove for cat1 lwg
    ##-DUMACE_ENABLE_MACI_DEBUG  remove for cat1 lwg

    ## 3G UL Enahanced Cell Fach (Common E-DCH)
    ##-DUPGRADE_UL_ECF  remove for cat1 lwg
    ##-DUPGRADE_HS_FACH_DRX    remove for cat1 lwg
	
    ## 3G Adjacent Frequency Measurements without Compress Mode
    ##-DUPGRADE_RRC_NO_CM_ON_ADJ_FREQ
	   
    ## 3G DBDC
    ##-DUPGRADE_3G_DBDC  remove for cat1 lwg

	  ## ********** enable CBS on FB since L1 can support it now
    ## ********** added: Disable CBS on FB
    ##-DUPGRADE_FB_DISABLE_CBS
    -DUPGRADE_SMC_REL10
    -DUPGRADE_4G_FROM_3G_MFBI
    -DUPGRADE_4G_FROM_2G_MFBI	
    ## ********** fix merge issue  
    -DNEZHA3
    ## ********** add
    -DLTE_MEAS_AT_GSM_OPTIMIZATION 
    -DL1A_PMU_OPTIMIZATION
    -DFG_RSSI_USING_APLP_GPLC
    ##-DUMTS7_PRE_R8  remove for cat1 lwg
##DS2.0 begin    
    -DUPGRADE_DSDSWB
    -DUPGRADE_DSDSWB_L2
    -DUPGRADE_DS_PHASE_II_WITH_NAS
    -DUPGRADE_COMMON_STORE
    -DENABLE_SEARCH_ABORT_BY_SIM2_SERVICE
    -DENABLE_SEARCH_ABORT_BY_SIM2_DEACT
    -DUPGRADE_SHARED_SYSINFO
    -DUPGRADE_SPEED_CAMP
    ##-DUPGRADE_DISABLE_WB_BCH
    ##-DENABLE_WB_PCH_SIM2_MEASUREMENT
    -DENABLE_WAIT_ABORT_SEARCH_CNF
    -DUPGRADE_PLMS_DSDS 
    -DKI_DYNMEM_2_OSA
    ##-DSUPPORT_GPLC2_RSSI_WITH_L1A1

    -DUPGRADE_DSDSLTE
    -DUPGRADE_LTE_SIM1_PS_SIM2_RESELECTION
    -DUPGRADE_DISABLE_LTE_ECGI
    -DUPGRADE_DISABLE_LTE_BCH
    ##-DENABLE_WB_PCH_LTE_SIM2_MEASUREMENT
    -DDISABLE_EMBMS_ENABLED_ON_TWO_SIM
    ##-DUPGRADE_LWG_IRAT_PLMN  remove for LTE only
    -DUPGRADE_FAKE_EXTENDED_SERVICE
##DS2.0 end

##move from inc to bld begin    
    ##-DUPGRADE_EMBMS    remove for ds3.0
    ##-DSUPPORT_LTE_IRAT  remove for LTE only
	-DSIM_EMULATION_ON
##move from inc to bld end  

##phonebook support begin
	-DSUPPORT_PB
##phonebook support end

##DS3.0 begin  
    -DDISABLE_MDT
    -DDISABLE_RLF
    -DUPGRADE_L2_MEM_OPT
    -DUPGRADE_RR_MEM_OPT
    -DUPGRADE_RR_MEM_OPT_GSM
    -DGRR_SPLIT_MALLOC_SYS_DB
    -DGRR_SPLIT_MALLOC_DATA
    -DDISABLE_LTE_CA  
    -DSUPPORT_ERRC_FG_PLMN
    -DDS3_CAT1
    -DDS3_CAT1_CATEGORY
    -DDS3_LTE_ONLY
    -DCRANE_Z1
    -DCRANE_PLATFORM
    -DCRANE_L1
    -DPS_TCM_CODE
    -DROHC_SIMPLE_VOLTE
    -DREMOVE_POOL
    -DUPGRADE_WIFI
    -DOPTIMIZATION_START_FROM_CRANE
    -DFOTA_MINI
    -DENABLE_SET_CELL_SELECT_CONFIG
    -DPLMS_EXCLUDE_UMTS_GSM

##DS3.0 end    
    -DUPGRADE_4G_BAND_EXT 
    -DZTE_SAR
##Move from inc file by 1802s test
    -DUPGRADE_GPLC_SUSPEND
    -DUPGRADE_Q6_GRR 
    -DUPGRADE_DSDSL1  
##Move from inc file by 1802s test
    -DENABLE_PREFER_BANDS
    -DLTE_HIGH_MOBILITY_OPTIMIZATION

##R13 begin
    ##-DUPGRADE_LTE_ASN_R15
    -DOPTIMIZATION_PINGPONG_HANDOVER
    ##-DUPGRADE_ERRC_R13_AC
    ##-DUPGRADE_R13_EDRX
    ##-DUPGRADE_R13_L2
    ##-DUPGRADE_R13_CP_OPT
    -DFEATURE_DETECT_DEADLOCK
    -DLTE_MR_TIMER
    -DGRR_CHECK_FAKE_BASE_STATION
    ##-DUPGRADE_INCREASED_MEAS_FREQ_NUM
##R13 end    

##CAT-M begin
    ##-DSUPPORT_PSM_EDRX
    ##-DSUPPORT_CP_DATA_CONTROL
##CAT-M begin

##diag optimization begin
    -DDISABLE_DIAG
    
##CAT-M END
##support CB for LTE ONLY FP
	-DSUPPORT_CB
    ##-DENABLE_NETWORK_SCAN_REPORT
    -DCRANEL_FP_8M8M_L2
	-DSUPPORT_MEP
    -DDISABLE_SOME_R15_IES
	-DSUPPORT_SMS
    -DSUPPORT_LTE_EAB
# </lte_umts_gge>

    # Strict checking for URRMCR, can be disabled for FT to prevent assertions
    -DUPGRADE_MCR_STRICT_CHECKING

#<ArmulatorTestDefs type="ON" enabled="FALSE">
    #-DARMULATOR_TEST_BUILD
    #-DKI_RLG_OUTPUT_PRINTF_LOG_POINTS_ONLY
    #-DCACHE_OPTIMIZATION
    #-J"\3g_ps\rls\tplgsm\bldstore\ArmulatorTests\Edch","%RVCT31INC%"
#</ArmulatorTestDefs>

#<RlcDebugVersion type="DEBUG_OVER_GKI_SIGNALS" enabled ="FALSE">
    ## Default (TTPCOM) RLC decoder, tag defined just for wiper_gui presentation
#</RlcDebugVersion>
#<RlcDebugVersion type="HSDPA_DEBUG_OVER_DTC" enabled ="FALSE">
    #-DURL_DEBUG_HSDPA_OVER_DTC
#</RlcDebugVersion>
#<RlcDebugVersion type="HSDPA_AND_EDCH_DEBUG_OVER_DTC" enabled ="FALSE">
    #-DURL_DEBUG_HSDPA_OVER_DTC
    #-DURL_DEBUG_EDCH_OVER_DTC
#</RlcDebugVersion>


#<AssistedGPS type="ON" enabled="TRUE">
    ##-DUPGRADE_APP_INFO
    -DUPGRADE_AGPS
    -DENABLE_AGPS_DEBUG
   ##-DLCS_SS_TEST
#</AssistedGPS>

    -DUSE_ABAPP
#<TTY_CTM type="ON" enabled="TRUE">
  ## Enable CTM/TTY feature support
 -DUPGRADE_CTM
#</TTY_CTM>

#<3G_PB_CACHE type="ON" enabled="FALSE">
  ## Enable 3G PB Cache
    #-DUPGRADE_PB_CACHE
#</3G_PB_CACHE>

#<BT_SAP type="ON" enabled="FALSE">
  ## Enable BlueTooth SIM SAP (BT SAP)
    ##-DUPGRADE_BTSAP
#</BT_SAP>
#<BT_RSAP type="ON" enabled="FALSE">
  ## Enable BlueTooth SIM Remote-SAP (BT RSAP)
    ##-DUPGRADE_BT_RSAP
#</BT_RSAP>

#<HOMEZONE type="ON" enabled="FALSE">
  ## Enable O2 Homezone capability
    #-DUPGRADE_HOMEZONE
#</HOMEZONE>
# <NVRAM type="OFF" enabled="FALSE">
    ## NVRAM is turned off by excluding the following
    ##-DDM_EXCLUDE_NVRAM_DEVICE_MANAGER
    #-DDM_EXCLUDE_NVRAM_DEVICE_DRIVER
    #-DDM_EXCLUDE_NVRAM_PLATFORM_DRIVER
# </NVRAM>


    ## ENABLE_CIRM_DATA_IND turns on output in RAVEN
    -DENABLE_CIRM_DATA_IND

### VG_DEFAULT_IPR sets the default baud rate for the data uart.
###  0=1200
###  1=2400
###  2=4800
###  3=9600
###  4=14400
###  5=19200
###  6=28800
###  7=38400
###  8=57600
### 9=115200
    -DVG_DEFAULT_IPR=9

   ## the following defines shouldn't be platform dependent
   ## For the flash file system and TE adaption layers.
# <Filesystem type="ON" enabled="FALSE">
    #-DUPGRADE_MIME_REGISTRY
# </Filesystem>

  ##
  #########################################################################
  ## Static configurations of composite devices
  ##
  ## It is possible to statically configure a 'composite' device to give
  ## multiple functionality but this may be prevented by limitations on
  ## the silicon, or the Windows device driver.
  #########################################################################
  ##
# <USB_INTERFACE type="COMPOSITE [EMMI_MODEM_MASS_STORAGE]" enabled="FALSE">
  ##-DUSB_INTEL_DIAG_INTERFACE
  #-DUSB_TTPCOM_EMMI_INTERFACE
  #-DUSB_COMMS_DEVICE_INTERFACE
  #-DCONFIG_TTP_MUX_USBNULL
  #-DUSB_MASS_STORAGE_BULK_ONLY_INTERFACE

  ## Remove the serial port EMMI interface and direct
  ## logging data to the USB EMMI task
  #-DNO_EMMI_INTERFACE
  #-DKI_COMMS_TASK_ID=USB_EMMI_COMMS_TASK_ID
# </USB_INTERFACE>

# <USB_INTERFACE type="COMPOSITE [MODEM_MASS_STORAGE]" enabled="FALSE">
  #-DUSB_COMMS_DEVICE_INTERFACE
  #-DCONFIG_TTP_MUX_USBNULL
  #-DUSB_MASS_STORAGE_BULK_ONLY_INTERFACE
# </USB_INTERFACE>

# <USB_INTERFACE type="COMPOSITE [EMMI_MODEM]" enabled="FALSE">
  #-DUSB_TTPCOM_EMMI_INTERFACE
  #-DUSB_COMMS_DEVICE_INTERFACE
  #-DCONFIG_TTP_MUX_USBNULL

  ## Remove the serial port EMMI interface and direct
  ## logging data to the USB EMMI task
  #-DNO_EMMI_INTERFACE
  #-DKI_COMMS_TASK_ID=USB_EMMI_COMMS_TASK_ID
# </USB_INTERFACE>

# <USB_INTERFACE type="COMPOSITE [MODEM]" enabled="TRUE">
  -DUSB_COMMS_DEVICE_INTERFACE
  -DCONFIG_TTP_MUX_USBNULL
# </USB_INTERFACE>

# <USB_INTERFACE type="COMPOSITE_[EMMI]" enabled="FALSE">
  #-DUSB_TTPCOM_EMMI_INTERFACE
# </USB_INTERFACE>
  ##
  #########################################################################
  ## Dynamic configuration of a single-function-at-a-time device.
  ##
  ## USB Dynamic Configuration allows for run-time selection of the
  ## interface using a signal.
  ##
  ## The 'dynamic' setup is *mutually exclusive* with the 'static' options
  ## above.
  #########################################################################
  ##
# <USB_INTERFACE type="DYNAMIC [Mo_Ma_Em_Di][Mo_Ma]" enabled="FALSE">
  ##-DUSB_INTEL_DIAG_INTERFACE
  #-DUSB_DYNAMIC_CONFIGURATION
  #-DUSB_TTPCOM_EMMI_INTERFACE
  #-DUSB_COMMS_DEVICE_INTERFACE
  #-DCONFIG_TTP_MUX_USBNULL
  #-DUSB_MASS_STORAGE_BULK_ONLY_INTERFACE

  ## Remove the serial port EMMI interface and direct
  ## logging data to the USB EMMI task
  #-DNO_EMMI_INTERFACE
  #-DKI_COMMS_TASK_ID=USB_EMMI_COMMS_TASK_ID
# </USB_INTERFACE>
##End USB

   #-DENABLE_ABAPP_FULL_POWER_DOWN
   
#<lte_umts_gge type="wcdma" enabled="TRUE">
	##-DKI_INT_MEM_POOL_STATISTICS  Disable RAMlog
	##-DKI_INT_MEM_TASK_SIZES_INFO  Disable RAMlog
	##-DKI_INT_MEM_ALLOC_TYPE_INFO  Disable RAMlog
	##-DKI_INT_CHECK_BUFFER_OVERRUN  Disable RAMlog
	##-DKI_INT_MEM_ON_PC_DUMP  Disable RAMlog
#</lte_umts_gge>


  ## Enable symbol debug on selected modules
  {DEBUG_AB}
      -g

  {DEBUG_3GAS}
      -g

  {DEBUG_LTEAS}
      -g

  {DEBUG_2GAS}
      -g

  {DEBUG_GKI}
      -g

  {DEBUG_NAS}
      -g

  {DEBUG_VGOP}
      -g

  {DEBUG_PPP}
      -g

  {DEBUG_UT}
      -g

  {DEBUG_HAW}
      -g

  {DEBUG_PLATFORM}
      -g

  {ARMASM}
    ####%BLD_INCLUDES%
# <RVCT_ARMV4T_LE type="ON" enabled="TRUE">
    --cpu Cortex-R4 --no_unaligned_access ##Xscale
    --apcs /noswst/inter
    --keep
    --fpu None
    ##-md
# </RVCT_ARMV4T_LE>
# <ADS_ARMV4T_LE type="ON" enabled="FALSE">
    #-cpu XScale
    #-apcs /noswst/inter
    #-keep
    #-fpu None
    ###-md
# </ADS_ARMV4T_LE>

  {ARM_LINK}
    %IROOT%\hermon\hermond\obj\ps_init.o
    %IROOT%\hermon\hermond\obj\ps_nvm.o
    %IROOT%\hermon\hermond\obj\usbmgrttpal.o
    %IROOT%\hermon\hermond\obj\diagDB.o
    %IROOT%\hermon\hermond\obj\os-osx.lib
    %IROOT%\hermon\hermond\obj\os-osa.lib
    %IROOT%\hermon\hermond\obj\csw-BSP.lib
    %IROOT%\hermon\hermond\obj\3g_ps-dps.lib
    %IROOT%\hermon\hermond\obj\aplp_etc-DLM.lib
    %IROOT%\hermon\hermond\obj\aplp-MODEM_MGR.lib
    %IROOT%\hermon\hermond\obj\hal-HAL.lib
    %IROOT%\hermon\hermond\obj\softutil-softutil.lib
    %IROOT%\hermon\hermond\obj\aud_sw-Audio.lib
# <lte_umts_gge type="wcdma" enabled="TRUE">
    %IROOT%\hermon\hermond\obj\aplp-APLP.lib
    %IROOT%\hermon\hermond\lib\aplp-MSR.lib
    %IROOT%\hermon\hermond\lib\aplp-TM.lib
#</lte_umts_gge>
    %IROOT%\hermon\hermond\obj\drat-DRAT.lib
    %IROOT%\hermon\hermond\obj\gplc-GPLC.lib
    %IROOT%\hermon\hermond\obj\obj_csw_bsp\init.o
    %TGTDIR%\HERMOND_EXT1.LIB
    %TGTDIR%\HERMOND_EXT2.LIB
    %TGTDIR%\HERMOND_INT.LIB
    %IROOT%\hermon\hermond\lib\os-nu_xscale.lib
    %IROOT%\hermon\hermond\lib\hal-icam_ocam.lib
# <RVCT_ARMV4T_LE type="ON" enabled="TRUE">
    --elf
    --scatter %IROOT%\hermon\hermond\build\WhiteSail.sct
    --libpath %ARMLIB%
    --map
    --symbols
    --info sizes,totals
    --list %IROOT%\HERMON\HERMOND\BIN\hermond.map
    --keep init.o(Header)
    --keep init.o(Vectors)
# </RVCT_ARMV4T_LE>
# <ADS_ARMV4T_LE type="ON" enabled="FALSE">
    #-elf
    #-scatter %IROOT%\hermon\hermond\build\WhiteSail.sct
    #-libpath %ARMLIB%
    #-map
    #-symbols
    #-info sizes,totals
    #-list %IROOT%\HERMON\HERMOND\BIN\hermond.map
    #-keep init.o(Header)
    #-keep init.o(Vectors)
# </ADS_ARMV4T_LE>
# Platform specific tools
[TOOLS]
  {ARM}
# <RVCT_ARMV4T_LE type="ON" enabled="TRUE">
    <COMPILER>
      EXE         =fcc
      INTYPE      =c
      SUBFILE     ="--via "
      OUTTYPE     =o
      OUTOPT      ="-o "
      SEVERALSUB  =YES
      LISTOPT     ="-list "
      !include <%TPLGSM%\platforms\hermon\hastub\mult.txt>
      INCLUDEOPT  =-I
      DEFINEOPT   = -D
      ##MULTIPLESOURCE
      ##MULTIPLEQUIET
    <ASM32>
      EXE         =ARMASM
      INTYPE      =s
      OUTTYPE     =o
      SUBFILE     ="--via "
      OUTOPT      ="-o "
      SEVERALSUB  =YES
      INCLUDEOPT  =-I
    <LINKER>
      EXE         =ARMLINK
      INTYPE      =o
      SUBFILE     ="--via "
      OUTOPT      ="-o "
      SEVERALSUB  =YES
      FLSCONVPRE  =" %OBJDIR%\"
      FLSCONVPOST =.%INTYPE%
    <LIBRARIAN>
      EXE         =ARMAR
      INTYPE      =o
      OUTOPT      ="--create "
      SUBFILE     ="--via "
      FLSCONVPRE  =" %OBJDIR%\"
      FLSCONVPOST =.%INTYPE%
      SEVERALSUB  =YES
# </RVCT_ARMV4T_LE>
# <ADS_ARMV4T_LE type="ON" enabled="FALSE">
    #<COMPILER>
      #EXE         = TCC
      #INTYPE      =c
      #SUBFILE     ="-via "
      #OUTTYPE     =o
      #OUTOPT      ="-o "
      #SEVERALSUB  =YES
      #LISTOPT     ="-list "
      #!include <%TPLGSM%\platforms\hermon\hastub\mult.txt>
      #INCLUDEOPT  =-I
    #<ASM32>
      #EXE         =ARMASM
      #INTYPE      =s
      #OUTTYPE     =o
      #SUBFILE     ="-via "
      #OUTOPT      ="-o "
      #SEVERALSUB  =YES
      #INCLUDEOPT  = -I
    #<LINKER>
      #EXE         =ARMLINK
      #INTYPE      =a
      #SUBFILE     ="-via "
      #FLSCONVPRE  =" %OBJDIR%\"
      #OUTOPT      ="-o "
      #SEVERALSUB  =YES
      #FLSCONVPRE  =" %OBJDIR%\"
      #FLSCONVPOST =.%INTYPE%
    #<LIBRARIAN>
      #EXE         =ARMAR
      #INTYPE      =o
      #SUBFILE     ="-via "
      #FLSCONVPRE  =" %OBJDIR%\"
      #OUTOPT      ="-create "
      #SEVERALSUB  =YES
      #FLSCONVPRE  =" %OBJDIR%\"
      #FLSCONVPOST =.%INTYPE%
# </ADS_ARMV4T_LE>
  {NOTHING}
    <COMPILER>
      EXE         = @echo.>nul
    <ASM32>
      EXE         = @echo.>nul
    <LINKER>
      EXE         = @echo.>nul
    <LIBRARIAN>
      EXE         = @echo nothing>
      OUTOPT      =" "
      FLSCONVPOST =.%INTYPE%

## This should _ALWAYS_ be at the end of the file
!include <modemhermon_lteonly.inc>
