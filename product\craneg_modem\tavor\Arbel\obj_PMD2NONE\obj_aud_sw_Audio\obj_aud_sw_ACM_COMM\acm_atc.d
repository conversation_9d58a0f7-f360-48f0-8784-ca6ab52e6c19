\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \aud_sw\ACM_COMM\src\acm_atc.c
\aud_sw\ACM_COMM\src\acm_atc.c:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\inc\diag.h
\diag\diag_logic\inc\diag.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\inc\diag_API.h
\diag\diag_logic\inc\diag_API.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\inc\diag_types.h
\diag\diag_logic\inc\diag_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\armv7r\include\alios_type.h
\os\alios\kernel\armv7r\include\alios_type.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\osa_old_api.h
\os\osa\inc\osa_old_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\osa_utils.h
\os\osa\inc\osa_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\BSP\inc\bsp_hisr.h
\csw\BSP\inc\bsp_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\nu_xscale\inc\nucleus.h
\os\nu_xscale\inc\nucleus.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\osa_internals.h
\os\osa\inc\osa_internals.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_api.h
\os\alios\kernel\rhino\include\k_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\asr3601\config\k_config.h
\os\alios\asr3601\config\k_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_default_config.h
\os\alios\kernel\rhino\include\k_default_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\armv7r\include\k_types.h
\os\alios\kernel\armv7r\include\k_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\armv7r\include\k_compiler.h
\os\alios\kernel\armv7r\include\k_compiler.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_err.h
\os\alios\kernel\rhino\include\k_err.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_sys.h
\os\alios\kernel\rhino\include\k_sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_critical.h
\os\alios\kernel\rhino\include\k_critical.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_spin_lock.h
\os\alios\kernel\rhino\include\k_spin_lock.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_list.h
\os\alios\kernel\rhino\include\k_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_obj.h
\os\alios\kernel\rhino\include\k_obj.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_sched.h
\os\alios\kernel\rhino\include\k_sched.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_task.h
\os\alios\kernel\rhino\include\k_task.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_ringbuf.h
\os\alios\kernel\rhino\include\k_ringbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_queue.h
\os\alios\kernel\rhino\include\k_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_buf_queue.h
\os\alios\kernel\rhino\include\k_buf_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_sem.h
\os\alios\kernel\rhino\include\k_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_task_sem.h
\os\alios\kernel\rhino\include\k_task_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_mutex.h
\os\alios\kernel\rhino\include\k_mutex.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_timer.h
\os\alios\kernel\rhino\include\k_timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_time.h
\os\alios\kernel\rhino\include\k_time.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_event.h
\os\alios\kernel\rhino\include\k_event.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_stats.h
\os\alios\kernel\rhino\include\k_stats.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_mm_debug.h
\os\alios\kernel\rhino\include\k_mm_debug.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_mm.h
\os\alios\kernel\rhino\include\k_mm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_mm_blk.h
\os\alios\kernel\rhino\include\k_mm_blk.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_mm_region.h
\os\alios\kernel\rhino\include\k_mm_region.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_workqueue.h
\os\alios\kernel\rhino\include\k_workqueue.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_internal.h
\os\alios\kernel\rhino\include\k_internal.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_trace.h
\os\alios\kernel\rhino\include\k_trace.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_soc.h
\os\alios\kernel\rhino\include\k_soc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_hook.h
\os\alios\kernel\rhino\include\k_hook.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\rhino\include\k_bitmap.h
\os\alios\kernel\rhino\include\k_bitmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\armv7r\include\port.h
\os\alios\kernel\armv7r\include\port.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\armv7r\include\k_vector.h
\os\alios\kernel\armv7r\include\k_vector.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\armv7r\include\k_cache.h
\os\alios\kernel\armv7r\include\k_cache.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\alios\kernel\armv7r\include\k_mmu.h
\os\alios\kernel\armv7r\include\k_mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\osa_ali.h
\os\osa\inc\osa_ali.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\alios_hisr.h
\os\osa\inc\alios_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\osa\inc\osa_um_extr.h
\os\osa\inc\osa_um_extr.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \os\nu_xscale\inc\um_defs.h
\os\nu_xscale\inc\um_defs.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\inc\diag_config.h
\diag\diag_logic\inc\diag_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\inc\diag_osif.h
\diag\diag_logic\inc\diag_osif.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\BSP\inc\asserts.h
\csw\BSP\inc\asserts.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\timer\inc\timer.h
\hop\timer\inc\timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\SysCfg\inc\syscfg.h
\csw\SysCfg\inc\syscfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\timer\inc\timer_config.h
\hop\timer\inc\timer_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\inc\diag_pdu.h
\diag\diag_logic\inc\diag_pdu.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\platform\inc\ICAT_config.h
\csw\platform\inc\ICAT_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\BSP\inc\bsp.h
\csw\BSP\inc\bsp.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\PM\inc\powerManagement.h
\csw\PM\inc\powerManagement.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\pm\inc\pm_config.h
\hop\pm\inc\pm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \softutil\TickManager\inc\tick_manager.h
\softutil\TickManager\inc\tick_manager.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\intc_list.h
\hop\intc\inc\intc_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hal\GPIO\inc\gpio_config.h
\hal\GPIO\inc\gpio_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\intc_list_xirq.h
\hop\intc\inc\intc_list_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\xirq_config.h
\hop\intc\inc\xirq_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hal\GPIO\inc\gpio.h
\hal\GPIO\inc\gpio.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hal\GPIO\inc\cgpio_HW.h
\hal\GPIO\inc\cgpio_HW.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\intc_xirq.h
\hop\intc\inc\intc_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\BSP\inc\levante_hw.h
\hop\BSP\inc\levante_hw.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hop\BSP\inc\levante.h
\hop\BSP\inc\levante.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\src\diag_tx.h
\diag\diag_logic\src\diag_tx.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \diag\diag_logic\src\diag_API_var.h
\diag\diag_logic\src\diag_API_var.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\acm_comm.h
\tavor\Arbel\obj_PMD2NONE\inc\acm_comm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\acmTypes.h
\tavor\Arbel\obj_PMD2NONE\inc\acmTypes.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\audio_atc.h
\tavor\Arbel\obj_PMD2NONE\inc\audio_atc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\acm_audio_primitive.h
\tavor\Arbel\obj_PMD2NONE\inc\acm_audio_primitive.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\aam.h
\tavor\Arbel\obj_PMD2NONE\inc\aam.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \csw\PM\inc\pm_dbg_types.h
\csw\PM\inc\pm_dbg_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\aam_config.h
\tavor\Arbel\obj_PMD2NONE\inc\aam_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\commpm.h
\tavor\Arbel\obj_PMD2NONE\inc\commpm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \aud_sw\AuC\inc\audio_amr.h
\aud_sw\AuC\inc\audio_amr.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\msl_sal_stbc_type.h
\tavor\Arbel\obj_PMD2NONE\inc\msl_sal_stbc_type.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \genlib\fsm\inc\fsm_sys.h
\genlib\fsm\inc\fsm_sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \genlib\fsm\inc\fsm_types.h
\genlib\fsm\inc\fsm_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \genlib\fsm\inc\fsm_app.h
\genlib\fsm\inc\fsm_app.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \genlib\fsm\inc\fsm_config.h
\genlib\fsm\inc\fsm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\inc\gbl_config.h
\tavor\Arbel\inc\gbl_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\msl_dl\inc\msldl.h
\pcac\msl_dl\inc\msldl.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\msl_dl\inc\msldl_cfg.h
\pcac\msl_dl\inc\msldl_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\msl_dl\inc\msldl_config.h
\pcac\msl_dl\inc\msldl_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\pca_components\inc\pcac_gm_config.h
\pcac\pca_components\inc\pcac_gm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\inc\gbl_config.h
\tavor\Arbel\inc\gbl_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\inc\gbl_config.h
\tavor\Arbel\inc\gbl_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\msl_utils\inc\msl_mem.h
\pcac\msl_utils\inc\msl_mem.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\msl_utils\inc\msl_trace.h
\pcac\msl_utils\inc\msl_trace.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\msl_utils\inc\msl_trmsg.h
\pcac\msl_utils\inc\msl_trmsg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \pcac\msl_utils\inc\msl_measures.h
\pcac\msl_utils\inc\msl_measures.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\audio_stub.h
\tavor\Arbel\obj_PMD2NONE\inc\audio_stub.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \hal\ACIPC\inc\acipc_data.h
\hal\ACIPC\inc\acipc_data.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_ACM_COMM/acm_atc.o : \tavor\Arbel\obj_PMD2NONE\inc\audio_stub_config.h
\tavor\Arbel\obj_PMD2NONE\inc\audio_stub_config.h:
