/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "SUPL-REPORT"
 * 	found in "supl202.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_ResultCode_H_
#define	_ResultCode_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum ResultCode {
	ResultCode_outofradiocoverage	= 1,
	ResultCode_noposition	= 2,
	ResultCode_nomeasurement	= 3,
	ResultCode_nopositionnomeasurement	= 4,
	ResultCode_outofmemory	= 5,
	ResultCode_outofmemoryintermediatereporting	= 6,
	ResultCode_other	= 7
	/*
	 * Enumeration is extensible
	 */
} e_ResultCode;

/* ResultCode */
typedef long	 ResultCode_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_ResultCode;
asn_struct_free_f ResultCode_free;
asn_struct_print_f ResultCode_print;
asn_constr_check_f ResultCode_constraint;
ber_type_decoder_f ResultCode_decode_ber;
der_type_encoder_f ResultCode_encode_der;
xer_type_decoder_f ResultCode_decode_xer;
xer_type_encoder_f ResultCode_encode_xer;
per_type_decoder_f ResultCode_decode_uper;
per_type_encoder_f ResultCode_encode_uper;

#ifdef __cplusplus
}
#endif

#endif	/* _ResultCode_H_ */
#include <asn_internal.h>
