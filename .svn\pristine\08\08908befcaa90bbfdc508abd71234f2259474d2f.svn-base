;#<FEEDBACK># ARM Linker, 5040049: Last Updated: Tue Jul 07 12:49:26 2020
;#<FEEDBACK># local udpate by xia<PERSON><EMAIL> for common LTEONLY FP
;VERSION 0.2
;FILE AAtomizer.o
_ZN14streamingmedia9AAtomizer4HashEPKc <= USED 0
;FILE ABuffer.o
_ZN14streamingmedia7ABuffer18setFarewellMessageEN7android2spINS_8AMessageEEE <= USED 0
_ZN14streamingmedia7ABuffer4metaEv <= USED 0
_ZN14streamingmedia7ABufferC1EPvj <= USED 0
;FILE ALooper.o
;FILE ALooperRoster.o
_ZN14streamingmedia13ALooperRoster10findLooperEi <= USED 0
_ZN14streamingmedia13ALooperRoster20postAndAwaitResponseERKN7android2spINS_8AMessageEEEPS4_ <= USED 0
_ZN14streamingmedia13ALooperRoster9postReplyEjRKN7android2spINS_8AMessageEEE <= USED 0
;FILE AMRRTPAssembler.o
;FILE AMessage.o
_ZN14streamingmedia8AMessage10FromParcelERKNS_6ParcelE <= USED 0
_ZN14streamingmedia8AMessage10setMessageEPKcRKN7android2spIS0_EE <= USED 0
_ZN14streamingmedia8AMessage17setObjectInternalEPKcRKN7android2spINS3_7RefBaseEEENS0_4TypeE <= USED 0
_ZN14streamingmedia8AMessage20postAndAwaitResponseEPN7android2spIS0_EE <= USED 0
_ZN14streamingmedia8AMessage5clearEv <= USED 0
_ZN14streamingmedia8AMessage7setRectEPKciiii <= USED 0
_ZN14streamingmedia8AMessage7setSizeEPKcj <= USED 0
_ZN14streamingmedia8AMessage8freeItemEPNS0_4ItemE <= USED 0
_ZN14streamingmedia8AMessage8setFloatEPKcf <= USED 0
_ZN14streamingmedia8AMessage9postReplyEj <= USED 0
_ZN14streamingmedia8AMessage9setBufferEPKcRKN7android2spINS_7ABufferEEE <= USED 0
_ZN14streamingmedia8AMessage9setDoubleEPKcd <= USED 0
_ZN14streamingmedia8AMessage9setTargetEi <= USED 0
_ZNK14streamingmedia8AMessage10findBufferEPKcPN7android2spINS_7ABufferEEE <= USED 0
_ZNK14streamingmedia8AMessage10findDoubleEPKcPd <= USED 0
_ZNK14streamingmedia8AMessage10findStringEPKcPNS_7AStringE <= USED 0
_ZNK14streamingmedia8AMessage11debugStringEi <= USED 0
_ZNK14streamingmedia8AMessage11findMessageEPKcPN7android2spIS0_EE <= USED 0
_ZNK14streamingmedia8AMessage12countEntriesEv <= USED 0
_ZNK14streamingmedia8AMessage13writeToParcelEPNS_6ParcelE <= USED 0
_ZNK14streamingmedia8AMessage14getEntryNameAtEjPNS0_4TypeE <= USED 0
_ZNK14streamingmedia8AMessage20senderAwaitsResponseEPj <= USED 0
_ZNK14streamingmedia8AMessage8findRectEPKcPiS3_S3_S3_ <= USED 0
_ZNK14streamingmedia8AMessage8findSizeEPKcPj <= USED 0
_ZNK14streamingmedia8AMessage9findFloatEPKcPf <= USED 0
;FILE AString.o
_ZN14streamingmedia12StringPrintfEPKcz <= USED 0
_ZN14streamingmedia7AString4trimEv <= USED 0
_ZN14streamingmedia7AString5clearEv <= USED 0
_ZN14streamingmedia7AString5eraseEjj <= USED 0
_ZN14streamingmedia7AString5setToEPKc <= USED 0
_ZN14streamingmedia7AString6appendEPv <= USED 0
_ZN14streamingmedia7AString6appendERKS0_jj <= USED 0
_ZN14streamingmedia7AString6appendEd <= USED 0
_ZN14streamingmedia7AString6appendEf <= USED 0
_ZN14streamingmedia7AString6appendEl <= USED 0
_ZN14streamingmedia7AString6appendEm <= USED 0
_ZN14streamingmedia7AString6appendEx <= USED 0
_ZN14streamingmedia7AString6appendEy <= USED 0
_ZN14streamingmedia7AString6insertEPKcjj <= USED 0
_ZN14streamingmedia7AString6insertERKS0_j <= USED 0
_ZN14streamingmedia7AString7astrdupEPKc <= USED 0
_ZN14streamingmedia7AString7tolowerEv <= USED 0
_ZN14streamingmedia7AStringC1ERKS0_jj <= USED 0
_ZNK14streamingmedia7AString10startsWithEPKc <= USED 0
_ZNK14streamingmedia7AString4findEPKcj <= USED 0
_ZNK14streamingmedia7AString4hashEv <= USED 0
_ZNK14streamingmedia7AString4sizeEv <= USED 0
_ZNK14streamingmedia7AString7compareERKS0_ <= USED 0
_ZNK14streamingmedia7AString8endsWithEPKc <= USED 0
_ZNK14streamingmedia7AStringgtERKS0_ <= USED 0
_ZNK14streamingmedia7AStringltERKS0_ <= USED 0
;FILE ATimers.o
_ZN7android13DurationTimer13addTotimevalTEP8timevalTl <= USED 0
_ZN7android13DurationTimer17subtracttimevalTsEPK8timevalTS3_ <= USED 0
_ZN7android13DurationTimer4stopEv <= USED 0
_ZN7android13DurationTimer5startEv <= USED 0
_ZNK7android13DurationTimer13durationUsecsEv <= USED 0
toMillisecondTimeoutDelay <= USED 0
;FILE AmrBEPayloadEncap.o
_ZN14streamingmedia17AmrBEPayloadEncap13releaseframesERN7android6VectorIPNS_18MediaBufferWrapperEEE <= USED 0
;FILE AmrOAPayloadEncap.o
_ZN14streamingmedia17AmrOAPayloadEncap13releaseframesERN7android6VectorIPNS_18MediaBufferWrapperEEE <= USED 0
;FILE AmrPayloadEncap.o
_ZN14streamingmedia15AmrPayloadEncap9setNewCMREh <= USED 0
;FILE Andratomic.o
android_atomic_acquire_cas <= USED 0
android_atomic_acquire_load <= USED 0
android_atomic_acquire_store <= USED 0
android_atomic_and <= USED 0
android_atomic_cas <= USED 0
android_atomic_cmpxchg_thumb <= USED 0
android_atomic_or <= USED 0
android_atomic_release_load <= USED 0
android_atomic_release_store <= USED 0
android_compiler_barrier <= USED 0
android_memory_barrier <= USED 0
android_memory_store_barrier <= USED 0
;FILE Andrlog.o
__android_log_btwrite <= USED 0
__android_log_buf_print <= USED 0
__android_log_bwrite <= USED 0
__android_log_vprint <= USED 0
__non_inline_android_log_write <= USED 0
getLogLevel <= USED 0
setLogLevel <= USED 0
;FILE AucCodecStub.o
_ZN14streamingmedia13amrTxCallBackEP15amrFrameInfo_ts <= USED 0
_ZN14streamingmedia19amrRxCallBack_neteqEPv <= USED 0
;FILE AucEncoder.o
_ZN14streamingmedia10AucEncoder7getMuteEv <= USED 0
;FILE AudioAMRFormatParameter.o
;FILE AudioExtern.o
;FILE AudioHAL_Control.o
AudioHAL_AifBindCodec_CB <= USED 0
AudioHAL_AifBindHeadsetDetectionCB <= USED 0
AudioHAL_AifBindSpeakerPA_CB <= USED 0
AudioHAL_AifConfigure <= USED 0
AudioHAL_AifGetTxCodecGainStage1 <= USED 0
AudioHAL_AifGetTxCodecGainStage2 <= USED 0
AudioHAL_AifHeadsetDetection <= USED 0
AudioHAL_AifSetSideTone <= USED 0
AudioHAL_AifSetTxCodecGain <= USED 0
AudioHAL_AifTonePause <= USED 0
AudioHAL_IsUsingFM <= USED 0
AudioHAL_sspaSetDelay <= USED 0
;FILE AudioHAL_Stream.o
AUDIOHAL_GetBufferSize <= USED 0
AudioHAL_FadingControl <= USED 0
AudioHAL_IsMediaStreamOn <= USED 0
;FILE AudioInit.o
ACMAudioFormatSupported <= USED 0
;FILE AudioTriState.o
;FILE Bler_test.o
;FILE BtService.o
_ZN9BtService10bt_bondingE7bt_addr <= USED 0
_ZN9BtService10bt_bondingEj <= USED 0
_ZN9BtService12bt_pin_replyE7bt_addrPc <= USED 0
_ZN9BtService14bt_acl_connectE7bt_addr <= USED 0
_ZN9BtService14bt_hfp_connectEi <= USED 0
_ZN9BtService15bt_a2dp_connectE7bt_addr <= USED 0
_ZN9BtService15bt_a2dp_connectEj <= USED 0
_ZN9BtService17bt_a2dp_send_dataEjPhjthhh <= USED 0
_ZN9BtService17bt_acl_disconnectEt <= USED 0
_ZN9BtService18bt_a2dp_disconnectEv <= USED 0
_ZN9BtService18bt_a2dp_send_startEv <= USED 0
_ZN9BtService18bt_connect_headsetEji <= USED 0
_ZN9BtService20bt_a2dp_send_suspendEv <= USED 0
_ZN9BtService20bt_init_connect_listEv <= USED 0
_ZN9BtService22bt_deinit_connect_listEv <= USED 0
_ZN9BtService23bt_is_headset_connectedEv <= USED 0
_ZN9BtService26bt_a2dp_send_data_completeEv <= USED 0
_ZN9BtService9bt_unbondE7bt_addr <= USED 0
;FILE COMCfg.o
COMCfgGetParameter <= USED 0
COMCfgPhase1Init <= USED 0
IsCOMCfgValid <= USED 0
;FILE COMCfg_parser.o
;FILE CPMediaManagerClient.o
_ZN14streamingmedia20CPMediaManagerClient11AddBearerIdEi <= USED 0
_ZN14streamingmedia20CPMediaManagerClient11EnableVoLTEEb <= USED 0
_ZN14streamingmedia20CPMediaManagerClient14GetMediaConfigE15SDPMeida_Config <= USED 0
_ZN14streamingmedia20CPMediaManagerClient14RemoveBearerIdEi <= USED 0
_ZN14streamingmedia20CPMediaManagerClient15SetVoLTEAddressEPKc <= USED 0
_ZN14streamingmedia20CPMediaManagerClient15getMediaSessionEb <= USED 0
_ZN14streamingmedia20CPMediaManagerClient24SetConfigCommonInterfaceEjRKNS_6ParcelE <= USED 0
_ZN14streamingmedia20CPMediaManagerClient7releaseEv <= USED 0
_ZN14streamingmedia20CPMediaManagerClient8UnitTestERKN7android7String8Ei <= USED 0
_ZN14streamingmedia20CPMediaManagerClientD2Ev <= USED 0
;FILE CPMediaSessionClient.o
_ZN14streamingmedia20CPMediaSessionClient10getRtpPortEv <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7getMuteEb <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7releaseEv <= USED 0
_ZN14streamingmedia20CPMediaSessionClient7setMuteEbb <= USED 0
_ZN14streamingmedia20CPMediaSessionClient8SendDTMFEPK16Media_DTMFData_t <= USED 0
;FILE CPMediaStreamClient.o
_ZN14streamingmedia19CPMediaStreamClient12GetLocalPortEPi <= USED 0
_ZN14streamingmedia19CPMediaStreamClient20GetStreamCodecConfigE26Media_SessionCodecConfig_ti15Media_DirectionPjPv <= USED 0
;FILE CapNeg.o
;FILE CapNegMediaStreamToSDP.o
;FILE CapNegSDPToMediaStream.o
;FILE CapNegUtils.o
CNGetAttributeFromList <= USED 0
CNGetProfileLevelIdValue <= USED 0
CN_Destroy_MediaStreamList <= USED 0
CN_Destroy_SdpSessionInfo <= USED 0
dump_sdp_session_info <= USED 0
;FILE CellularAL_AT.o
CellularAL_Reject <= USED 0
;FILE CpVolume.o
;FILE DSPDebugConfig.o
;FILE DSPDebugConfig_NVM.o
;FILE DTMFFormatParameter.o
;FILE DTMF_Para_Gen.o
;FILE DiagSig_PS.o
GetCurrentHISRPointer <= USED 0
L1TraceSignal <= USED 0
;FILE DigRf3GPor.o
DcxoCalibration_CreateNVMFile <= USED 0
GetPmaxReductionFlagIn23GNVM <= USED 0
Termistor_VCC_18V <= USED 0
WBRxCompensateValue <= USED 0
;FILE DigRf3GRspEngine.o
RspDigRf3GRfInitAsyncEngineSkyLark <= USED 0
RspDigRf3GStrobeEngine <= USED 0
RspDigRf3GTriggerTcuGpo <= USED 0
;FILE DigRf3GRspSequence.o
ConfigGsmInitAfterWBPiReceivingPel <= USED 0
RspCalRx <= USED 0
RspCalTx <= USED 0
;FILE Drc_Loudspeaker.o
;FILE ECM.o
Ecm_indicate_status_msg <= USED 0
Ecm_remove_hdr <= USED 0
;FILE EEHandler.o
eeHandlerSaveDescFileLineParamWarning <= USED 0
eeHandlerSaveDescFileLineWarning <= USED 0
eeHandlerSaveDescWarning <= USED 0
errHandlerSaveToFDIPeriodic <= USED 0
errorHandlerPhase1Init <= USED 0
;FILE EEHandler_Serial.o
DBG_Heartbeat <= USED 0
DBG_HeartbeatMsgSet <= USED 0
;FILE EEHandler_config.o
IsDumpSilentEnable <= USED 0
IsSilentResetEnable <= USED 0
;FILE EEHandler_fatal.o
FatalErrorHandler <= USED 0
PrefetchAbortHandler <= USED 0
SilentReset_set <= USED 0
eeDeferredFinalActionRunEx <= USED 0
eeExtExceptionHandlerBind <= USED 0
eeForceNonDeferred <= USED 0
eeGetPcStep <= USED 0
eeHandlerSaveDescFileLineAssertRegs <= USED 0
eeParkThreadEx <= USED 0
eeh_set_data_abort <= USED 0
errHandlerFinalAction <= USED 0
lcdDisplayExceptionInfo <= USED 0
;FILE EEHandler_hal.o
AllfilesDone <= USED 0
EELOG_finish_ <= USED 0
SspEeSendEehHalRcvEvent <= USED 0
WDTSRDYActive <= USED 0
WDTSRDYInactive <= USED 0
assertSRDYActive <= USED 0
assertSRDYInactive <= USED 0
ee_delay <= USED 0
ee_delay_us <= USED 0
;FILE EEHandler_handlers.o
;FILE EE_Postmortem.o
;FILE EE_silentReset.o
GetBacklightStatusBeforeReset <= USED 0
Is_silentReset <= USED 0
check_data <= USED 0
clear_privatearea_keepdata <= USED 0
get_cprunmode <= USED 0
read_privatearea_keepdata <= USED 0
reset_flag_read <= USED 0
set_cprunmode <= USED 0
silentReset_Check <= USED 0
write_privatearea_keepdata <= USED 0
;FILE EE_wdtManager.o
WDTMoniterTrigger <= USED 0
;FILE FDI2FS_API_ttc.o
FDI2FS_Init <= USED 0
FDI_fclose_2chip_test <= USED 0
FDI_feof_2chip <= USED 0
FDI_fopen_2chip_test <= USED 0
FDI_fread_2chip_test <= USED 0
FDI_fseek_2chip_test <= USED 0
FDI_fwrite_2chip_test <= USED 0
;FILE FDI_Transport.o
ACATwrapper_print_nameList <= USED 0
;FILE FDI_stub.o
FDI_Delete <= USED 0
FDI_Get <= USED 0
FDI_Read <= USED 0
FDI_ReclaimEnable <= USED 0
FDI_Write <= USED 0
;FILE FatSysWrapper.o
FDI5_fileinfo_date2str <= USED 0
FDI5_fileinfo_time2str <= USED 0
FDI_ChDir <= USED 0
FDI_MakeDir <= USED 0
FDI_Stat <= USED 0
FDI_feof <= USED 0
FDI_feof_fatsys <= USED 0
FDI_freadEx_fatsys <= USED 0
FDI_ftell_fatsys <= USED 0
FDI_fwriteEx_fatsys <= USED 0
FDI_timercount <= USED 0
FatTask_entry <= USED 0
fat_flush2flash <= USED 0
;FILE FormatParameter.o
;FILE FreqChange.o
Crane_Get_Svc_Level_Values <= USED 0
Delay_32k <= USED 0
Delta_32k_Tick <= USED 0
GetCurrentCpCoreFreq <= USED 0
GetLastCpuIdleRate <= USED 0
cpu_dvc_init <= USED 0
efuse_dro <= USED 0
get_prf_num_by_dro_crn <= USED 0
read_dro <= USED 0
;FILE GBMemoryDump.o
GBMemDumpGetAssertCauseFlag <= USED 0
;FILE GKITick.o
;FILE GPLC_init.o
L1GetRfDcxoIsUsingFlag <= USED 0
;FILE GenericFS_API.o
GenericFileSysFileClose_test <= USED 0
GenericFileSysFileERead_test <= USED 0
GenericFileSysFileEof <= USED 0
GenericFileSysFileOpen_test <= USED 0
GenericFileSysFileSeek_test <= USED 0
GenericFileSysFileWrite_test <= USED 0
;FILE GenericNACKFeedBackPacket.o
_ZN14streamingmedia25GenericNACKFeedBackPacket19ConstructPacketHeadEv <= USED 0
_ZN14streamingmedia25GenericNACKFeedBackPacket6SetBLPEt <= USED 0
_ZN14streamingmedia25GenericNACKFeedBackPacket6SetPIDEt <= USED 0
_ZNK14streamingmedia25GenericNACKFeedBackPacket6GetBLPEv <= USED 0
_ZNK14streamingmedia25GenericNACKFeedBackPacket6GetPIDEv <= USED 0
;FILE GenericRfDriver.o
GsmRF_RAMInit <= USED 0
RspTxRampTypeCalc <= USED 0
SfsSigEventInc <= USED 0
;FILE GplcSpy.o
GplcSpyGetBufferSize <= USED 0
GtcuSpyInit <= USED 0
;FILE I2C_ttc.o
CheckLevanteID <= USED 0
I2CBlockingMasterReceive <= USED 0
I2CBusStateGetDirect <= USED 0
I2CConfigureDi <= USED 0
I2CConfigureDi_temp <= USED 0
I2CControl <= USED 0
I2CD2Prepare <= USED 0
I2CD2Recover <= USED 0
I2CEnableclockandPin <= USED 0
I2CMasterReceiveDataDirect_CB <= USED 0
I2CMasterSendByReferance <= USED 0
I2CPhase1Init <= USED 0
I2CPhase2Init <= USED 0
I2CUnRegister <= USED 0
PM8609Buck2_set_1_8 <= USED 0
PM8609LDO_4_set <= USED 0
PM8609LDO_4_set_1_8 <= USED 0
PM8609LDO_4_set_2_9 <= USED 0
PMC_GetLevanteRealType <= USED 0
PMC_GetLevanteType <= USED 0
;FILE IMEI.o
IMEI2ReadOnly <= USED 0
IMEI2ReadStr <= USED 0
IMEIReadOnly <= USED 0
;FILE InputStream.o
_ZN14streamingmedia11InputStream12prepareAsyncEv <= USED 0
_ZN14streamingmedia11InputStream14initRenderer_lEv <= USED 0
_ZN14streamingmedia11InputStream16postVideoEvent_lEx <= USED 0
_ZN14streamingmedia11InputStream17notifyVideoSize_lEv <= USED 0
_ZN14streamingmedia11InputStream18cancelPlayerEventsEb <= USED 0
_ZN14streamingmedia11InputStream18startAudioPlayer_lEb <= USED 0
_ZN14streamingmedia11InputStream19onPrepareAsyncEventEv <= USED 0
_ZN14streamingmedia11InputStream19postVideoLagEvent_lEv <= USED 0
_ZN14streamingmedia11InputStream19setVideoScalingModeEi <= USED 0
_ZN14streamingmedia11InputStream20postBufferingEvent_lEv <= USED 0
_ZN14streamingmedia11InputStream21postStreamDoneEvent_lEi <= USED 0
_ZN14streamingmedia11InputStream21setVideoScalingMode_lEi <= USED 0
_ZN14streamingmedia11InputStream22shutdownVideoDecoder_lEv <= USED 0
_ZN14streamingmedia11InputStream25postCheckAudioStatusEventEx <= USED 0
_ZN14streamingmedia11InputStream4initEv <= USED 0
_ZN14streamingmedia11InputStream5resetEv <= USED 0
_ZN14streamingmedia11InputStream7pause_lEb <= USED 0
_ZN14streamingmedia19InputStreamRendererC2Ev <= USED 0
_ZNK14streamingmedia11InputStream4dumpEiRKN7android6VectorINS1_8String16EEE <= USED 0
_ZNK14streamingmedia11InputStream9isPlayingEv <= USED 0
;FILE L1NscGsm.o
L1GsmNscCreateBchConfigReq <= USED 0
L1GsmNscCreateNcellMeasReq <= USED 0
L1GsmNscCreateTiTchReq <= USED 0
L1GsmNscSaveMeasResults <= USED 0
;FILE L1RspScenarios.o
L1CfgLteScanFinishRsp <= USED 0
L1CfgRxSelfCalibrationRsp <= USED 0
L1CfgTxContigRsp <= USED 0
L1CfgTxSelfCalibrationRsp <= USED 0
L1CfgTxStartRsp <= USED 0
L1CfgTxStopRsp <= USED 0
L1CfgUtranMeasBeginRsp <= USED 0
;FILE MQTTRTOS.o
NetworkConnect <= USED 0
NetworkInit <= USED 0
ThreadStart <= USED 0
TimerCountdown <= USED 0
TimerCountdownMS <= USED 0
TimerIsExpired <= USED 0
TimerLeftMS <= USED 0
;FILE MRD.o
BtIDRead <= USED 0
MRDFileDirFirst <= USED 0
MRDFileDirNext <= USED 0
MRDGetBufferAddr <= USED 0
MRDInit <= USED 0
imei_data_to_rdisk <= USED 0
isMRDOperationAllowed <= USED 0
mrd_read_item_to_rdisk <= USED 0
nvm_data_to_rdisk <= USED 0
rdisk_to_imei_data <= USED 0
version_compare <= USED 0
;FILE Mathhalf.o
L_abs <= USED 0
L_deposit_h <= USED 0
L_deposit_l <= USED 0
L_msu <= USED 0
L_shift_r <= USED 0
abs_s <= USED 0
divide_s <= USED 0
mac_r <= USED 0
msu_r <= USED 0
mult <= USED 0
mult_r <= USED 0
negate <= USED 0
norm_l <= USED 0
norm_s <= USED 0
round <= USED 0
shift_r <= USED 0
sub <= USED 0
;FILE MediaAudioStream.o
;FILE MediaBufferGroupT.o
;FILE MediaBufferPool.o
;FILE MediaDescription.o
;FILE MediaManager.o
_ZN14streamingmedia12MediaManager12getDTMFEventEPjPc <= USED 0
_ZN14streamingmedia12MediaManager14GetMediaConfigEv <= USED 0
_ZN14streamingmedia12MediaManager14logMediaconfigE17SDPMedia_Config_t <= USED 0
_ZN14streamingmedia12MediaManager15SetCameraConfigEi14Media_CamCap_t <= USED 0
_ZN14streamingmedia12MediaManager18getCodecCapabilityEPcP16Media_CodecCap_t <= USED 0
_ZN14streamingmedia12MediaManager19GetCameraCapabilityEiPiR14Media_CamCap_t <= USED 0
_ZN14streamingmedia12MediaManager24GetCameraCapabilityCountEv <= USED 0
_ZN14streamingmedia12MediaManager9getCameraEv <= USED 0
_ZN14streamingmedia12MediaManager9setCameraEP14Media_CamCap_t <= USED 0
;FILE MediaSession.o
_ZN14streamingmedia12MediaSession10getRtpPortEv <= USED 0
;FILE MediaSource.o
_ZN14streamingmedia11MediaSource11ReadOptions11clearSeekToEv <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions5resetEv <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions9setLateByEx <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptions9setSeekToExNS1_8SeekModeE <= USED 0
_ZN14streamingmedia11MediaSource11ReadOptionsC1Ev <= USED 0
_ZNK14streamingmedia11MediaSource11ReadOptions9getLateByEv <= USED 0
_ZNK14streamingmedia11MediaSource11ReadOptions9getSeekToEPxPNS1_8SeekModeE <= USED 0
;FILE MediaSourceT.o
_ZN14streamingmedia12MediaSourceT12readVecBlockERN7android6VectorIPNS_18MediaBufferWrapperEEEx <= USED 0
_ZN14streamingmedia12MediaSourceT9readBlockEPPNS_11MediaBufferEx <= USED 0
_ZN14streamingmedia12MediaSourceT9setConfigEPNS_8MetaDataE <= USED 0
;FILE MediaStream.o
_ZN14streamingmedia11MediaStream11stopReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream12pauseReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream12startReceiveEv <= USED 0
_ZN14streamingmedia11MediaStream13resumeReceiveEv <= USED 0
;FILE MetaData.o
_ZN14streamingmedia8MetaData10setCStringEjPKc <= USED 0
_ZN14streamingmedia8MetaData10setPointerEjPv <= USED 0
_ZN14streamingmedia8MetaData10typed_data11freeStorageEv <= USED 0
_ZN14streamingmedia8MetaData10typed_data15allocateStorageEj <= USED 0
_ZN14streamingmedia8MetaData10typed_data5clearEv <= USED 0
_ZN14streamingmedia8MetaData10typed_dataC1Ev <= USED 0
_ZN14streamingmedia8MetaData10typed_dataD1Ev <= USED 0
_ZN14streamingmedia8MetaData10typed_dataaSERKS1_ <= USED 0
_ZN14streamingmedia8MetaData11findCStringEjPPKc <= USED 0
_ZN14streamingmedia8MetaData6removeEj <= USED 0
_ZN14streamingmedia8MetaData7setRectEjiiii <= USED 0
_ZN14streamingmedia8MetaData8findRectEjPiS1_S1_S1_ <= USED 0
_ZN14streamingmedia8MetaData8setFloatEjf <= USED 0
_ZN14streamingmedia8MetaData8setInt64Ejx <= USED 0
_ZN14streamingmedia8MetaData9findFloatEjPf <= USED 0
_ZN14streamingmedia8MetaData9findInt64EjPx <= USED 0
_ZN14streamingmedia8MetaDataC1ERKS0_ <= USED 0
_ZNK14streamingmedia8MetaData10typed_data7getDataEPjPPKvS2_ <= USED 0
_ZNK14streamingmedia8MetaData10typed_data8asStringEv <= USED 0
_ZNK14streamingmedia8MetaData9dumpToLogEv <= USED 0
;FILE MrvXML.o
AddAttrToXML <= USED 0
AddElementToXML <= USED 0
AddToList <= USED 0
DeleteAttrFromXML <= USED 0
DeleteElementFromXML <= USED 0
DeleteFromList <= USED 0
DeleteFromListTial <= USED 0
FreeList <= USED 0
InitList <= USED 0
MrvAddCData <= USED 0
MrvAttributeAttach <= USED 0
MrvCountElement <= USED 0
MrvCreateElements <= USED 0
MrvCreateXMLString <= USED 0
MrvCreateXMLStringR <= USED 0
MrvEnumPrintf <= USED 0
MrvGetClearTags <= USED 0
MrvGetError <= USED 0
;FILE MrvXMLUtil.o
;FILE NTPTime.o
_ZN14streamingmedia7NTPTime7addmsecEj <= USED 0
_ZN14streamingmedia7NTPTimeltERKS0_ <= USED 0
;FILE NUtick.o
;FILE NetEQWrapper.o
RTPInjection2IMS <= USED 0
ReceiveRTP <= USED 0
_ZN14streamingmedia12NetEQWrapper12getAudioInfoERiS1_S1_ <= USED 0
_ZN14streamingmedia12NetEQWrapper13extractPacketEPs <= USED 0
;FILE NetworkSelectionAL_AT.o
networkSelectionAL_HdlRegisterNotify <= USED 0
networkSelectionAL_HdlUnregisterNotify <= USED 0
;FILE OutputStream.o
_ZN14streamingmedia12OutputStream17createAudioSourceEv <= USED 0
_ZN14streamingmedia12OutputStream17setupVideoEncoderEN7android2spINS_11MediaSourceEEEiPS4_ <= USED 0
_ZN14streamingmedia12OutputStream19decoderProfileLevelEhhh <= USED 0
_ZN14streamingmedia12OutputStream5resetEv <= USED 0
_ZNK14streamingmedia12OutputStream5mutedEv <= USED 0
;FILE PCA_api.o
AucSwitchAudioOn <= USED 0
AudioPatch_DisablePcmStream <= USED 0
RegisterCallback <= USED 0
SetCTMControl <= USED 0
SetDebugCmd <= USED 0
SetStreamPriority <= USED 0
SetVocoderStreamType <= USED 0
SetVoiceEnhanceModuleControl <= USED 0
;FILE Parcel.o
_ZN14streamingmedia14acquire_objectERKN7android2spINS_12ProcessStateEEERKNS_18flat_binder_objectEPKv <= USED 0
_ZN14streamingmedia14flatten_binderERKN7android2spINS_12ProcessStateEEERKNS0_2wpINS_7IBinderEEEPNS_6ParcelE <= USED 0
_ZN14streamingmedia14flatten_binderERKN7android2spINS_12ProcessStateEEERKNS1_INS_7IBinderEEEPNS_6ParcelE <= USED 0
_ZN14streamingmedia14release_objectERKN7android2spINS_12ProcessStateEEERKNS_18flat_binder_objectEPKv <= USED 0
_ZN14streamingmedia16unflatten_binderERKN7android2spINS_12ProcessStateEEERKNS_6ParcelEPNS0_2wpINS_7IBinderEEE <= USED 0
_ZN14streamingmedia16unflatten_binderERKN7android2spINS_12ProcessStateEEERKNS_6ParcelEPNS1_INS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel10appendFromEPKS0_jj <= USED 0
_ZN14streamingmedia6Parcel10writeFloatEf <= USED 0
_ZN14streamingmedia6Parcel10writeInt32Ei <= USED 0
_ZN14streamingmedia6Parcel10writeInt64Ex <= USED 0
_ZN14streamingmedia6Parcel11finishWriteEj <= USED 0
_ZN14streamingmedia6Parcel11setDataSizeEj <= USED 0
_ZN14streamingmedia6Parcel11writeDoubleEd <= USED 0
_ZN14streamingmedia6Parcel11writeIntPtrEi <= USED 0
_ZN14streamingmedia6Parcel11writeObjectERKNS_18flat_binder_objectEb <= USED 0
_ZN14streamingmedia6Parcel12pushAllowFdsEb <= USED 0
_ZN14streamingmedia6Parcel12restartWriteEj <= USED 0
_ZN14streamingmedia6Parcel12writeCStringEPKc <= USED 0
_ZN14streamingmedia6Parcel12writeInplaceEj <= USED 0
_ZN14streamingmedia6Parcel12writeString8ERKN7android7String8E <= USED 0
_ZN14streamingmedia6Parcel13continueWriteEj <= USED 0
_ZN14streamingmedia6Parcel13writeString16EPKtj <= USED 0
_ZN14streamingmedia6Parcel13writeString16ERKN7android8String16E <= USED 0
_ZN14streamingmedia6Parcel13writeUnpaddedEPKvj <= USED 0
_ZN14streamingmedia6Parcel14acquireObjectsEv <= USED 0
_ZN14streamingmedia6Parcel14releaseObjectsEv <= USED 0
_ZN14streamingmedia6Parcel15restoreAllowFdsEb <= USED 0
_ZN14streamingmedia6Parcel15setDataCapacityEj <= USED 0
_ZN14streamingmedia6Parcel15writeWeakBinderERKN7android2wpINS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel16writeNoExceptionEv <= USED 0
_ZN14streamingmedia6Parcel17writeStrongBinderERKN7android2spINS_7IBinderEEE <= USED 0
_ZN14streamingmedia6Parcel19ipcSetDataReferenceEPKhjPKjjPFvPS0_S2_jS4_jPvES6_ <= USED 0
_ZN14streamingmedia6Parcel19writeFileDescriptorEib <= USED 0
_ZN14streamingmedia6Parcel19writeInterfaceTokenERKN7android8String16E <= USED 0
_ZN14streamingmedia6Parcel20closeFileDescriptorsEv <= USED 0
_ZN14streamingmedia6Parcel22writeDupFileDescriptorEi <= USED 0
_ZN14streamingmedia6Parcel5writeEPKvj <= USED 0
_ZN14streamingmedia6Parcel5writeERKN7android11FlattenableE <= USED 0
_ZN14streamingmedia6Parcel6removeEjj <= USED 0
_ZN14streamingmedia6Parcel7setDataEPKhj <= USED 0
_ZN14streamingmedia6Parcel8freeDataEv <= USED 0
_ZN14streamingmedia6Parcel8growDataEj <= USED 0
_ZN14streamingmedia6Parcel8setErrorEi <= USED 0
_ZNK14streamingmedia6Parcel10errorCheckEv <= USED 0
_ZNK14streamingmedia6Parcel10ipcObjectsEv <= USED 0
_ZNK14streamingmedia6Parcel10readDoubleEPd <= USED 0
_ZNK14streamingmedia6Parcel10readDoubleEv <= USED 0
_ZNK14streamingmedia6Parcel10readIntPtrEPi <= USED 0
_ZNK14streamingmedia6Parcel10readIntPtrEv <= USED 0
_ZNK14streamingmedia6Parcel10readObjectEb <= USED 0
_ZNK14streamingmedia6Parcel10scanForFdsEv <= USED 0
_ZNK14streamingmedia6Parcel11ipcDataSizeEv <= USED 0
_ZNK14streamingmedia6Parcel11readCStringEv <= USED 0
_ZNK14streamingmedia6Parcel11readInplaceEj <= USED 0
_ZNK14streamingmedia6Parcel11readString8Ev <= USED 0
_ZNK14streamingmedia6Parcel12dataCapacityEv <= USED 0
_ZNK14streamingmedia6Parcel12dataPositionEv <= USED 0
_ZNK14streamingmedia6Parcel12objectsCountEv <= USED 0
_ZNK14streamingmedia6Parcel12readString16Ev <= USED 0
_ZNK14streamingmedia6Parcel14checkInterfaceEPNS_7IBinderE <= USED 0
_ZNK14streamingmedia6Parcel14readWeakBinderEv <= USED 0
_ZNK14streamingmedia6Parcel15ipcObjectsCountEv <= USED 0
_ZNK14streamingmedia6Parcel15setDataPositionEj <= USED 0
_ZNK14streamingmedia6Parcel16enforceInterfaceERKN7android8String16EPNS_14IPCThreadStateE <= USED 0
_ZNK14streamingmedia6Parcel16readStrongBinderEv <= USED 0
_ZNK14streamingmedia6Parcel17readExceptionCodeEv <= USED 0
_ZNK14streamingmedia6Parcel18hasFileDescriptorsEv <= USED 0
_ZNK14streamingmedia6Parcel18readFileDescriptorEv <= USED 0
_ZNK14streamingmedia6Parcel19readString16InplaceEPj <= USED 0
_ZNK14streamingmedia6Parcel4dataEv <= USED 0
_ZNK14streamingmedia6Parcel4readERN7android11FlattenableE <= USED 0
_ZNK14streamingmedia6Parcel7ipcDataEv <= USED 0
_ZNK14streamingmedia6Parcel7objectsEv <= USED 0
_ZNK14streamingmedia6Parcel8dataSizeEv <= USED 0
_ZNK14streamingmedia6Parcel9dataAvailEv <= USED 0
_ZNK14streamingmedia6Parcel9readFloatEPf <= USED 0
_ZNK14streamingmedia6Parcel9readFloatEv <= USED 0
_ZNK14streamingmedia6Parcel9readInt32EPi <= USED 0
_ZNK14streamingmedia6Parcel9readInt64EPx <= USED 0
_ZNK14streamingmedia6Parcel9readInt64Ev <= USED 0
;FILE PayloadEncap.o
;FILE PicoIms3gppImsXmlInterpreter.o
;FILE PicoImsInit.o
IMS_CallDeflect <= USED 0
IMS_CallForward <= USED 0
IMS_ClearOptionalHeader <= USED 0
IMS_ClearPreCondition <= USED 0
IMS_ClearProxy <= USED 0
IMS_ClearServer <= USED 0
IMS_DeleteUser <= USED 0
IMS_DisableCallForward <= USED 0
IMS_DisableSendingRegister <= USED 0
IMS_EnableCallForward <= USED 0
IMS_GetDefaultUser <= USED 0
IMS_GetNumPublicUser <= USED 0
IMS_GetRegisteredContact <= USED 0
IMS_GetSecAgreeConfig <= USED 0
IMS_GetSecAgreeStatus <= USED 0
IMS_GetSessionTimerValue <= USED 0
IMS_GetStackProperty <= USED 0
IMS_GetTimer <= USED 0
IMS_GetTransport <= USED 0
IMS_GetUAHd <= USED 0
IMS_IsStackInit <= USED 0
IMS_PrintSessionsState <= USED 0
IMS_SMIPSendAbortSMMA <= USED 0
IMS_SMIPSetMaxSmmaRetry <= USED 0
IMS_SMIPSetRecvSupport <= USED 0
IMS_SMIPSetSendSupport <= USED 0
IMS_SMIPSetStoragePref <= USED 0
IMS_SMIPSetTr2m <= USED 0
IMS_SMIPSetTram <= USED 0
IMS_SendCapability <= USED 0
IMS_SendInfo <= USED 0
IMS_SetAudioRtcpOption <= USED 0
IMS_SetContentCaps <= USED 0
IMS_SetDNSServer <= USED 0
IMS_SetInstanceId <= USED 0
IMS_SetLocalIdentity <= USED 0
IMS_SetNetworkDetail <= USED 0
IMS_SetOptionalHeader <= USED 0
IMS_SetPUAHd <= USED 0
IMS_SetPacketDumpCallback <= USED 0
IMS_SetRtpBasePort <= USED 0
IMS_SetSecAgreeConfig <= USED 0
IMS_SetSessionTimerValue <= USED 0
IMS_SetTosOption <= USED 0
IMS_SetTransport <= USED 0
IMS_StartMWI <= USED 0
IMS_StopMWI <= USED 0
PICO_IMS_BuildDateGet <= USED 0
PICO_IMS_BuildMachineGet <= USED 0
PICO_IMS_BuildTimeGet <= USED 0
PICO_IMS_BuildUserGet <= USED 0
PICO_IMS_BuildVersionGet <= USED 0
;FILE PicoImsMwi.o
;FILE PicoImsReg.o
;FILE PlatformConfig.o
ChipSelect0 <= USED 0
ChipSelect2 <= USED 0
ChipSelectDFC <= USED 0
ChipSelectOneNAND <= USED 0
ConfigRegRestore <= USED 0
ConfigRegResume <= USED 0
ConfigRegSave <= USED 0
ConfigRegWrite <= USED 0
RestoreDefaultConfig <= USED 0
SaveDefaultConfig <= USED 0
;FILE RDP.o
RDPDiagCpyPeriodicData <= USED 0
RDPGetEnablePeriodicDataFlag <= USED 0
RDPRegisterAckHandle <= USED 0
;FILE RDisk.o
Rdisk_addto_filelist <= USED 0
;FILE RTC_stub.o
RTCNotifyOnTimeSetBind <= USED 0
RTCNotifyOnTimeSetUnBind <= USED 0
RTCNotifyUsers <= USED 0
RTCPhase1Init <= USED 0
RTCPhase2Init <= USED 0
RTCPowerUpAlarmCheck <= USED 0
RTCToANSIConvert <= USED 0
;FILE RTPAssembler.o
;FILE RdGenData.o
RDGenGetUniqueId <= USED 0
;FILE RefBase.o
_ZN7android7RefBase12weakref_type14attemptIncWeakEPKv <= USED 0
_ZN7android7RefBase12weakref_type7trackMeEbb <= USED 0
_ZN7android7RefBase20extendObjectLifetimeEi <= USED 0
_ZNK7android7RefBase11getWeakRefsEv <= USED 0
_ZNK7android7RefBase12weakref_type12getWeakCountEv <= USED 0
_ZNK7android7RefBase12weakref_type7refBaseEv <= USED 0
_ZNK7android7RefBase12weakref_type9printRefsEv <= USED 0
_ZNK7android7RefBase14forceIncStrongEPKv <= USED 0
_ZNK7android7RefBase14getStrongCountEv <= USED 0
;FILE ReliableData.o
ReliableDataPhase1 <= USED 0
mrd_nvm_unpack <= USED 0
rd_linkstatus_ind <= USED 0
rf_calibration_status <= USED 0
strStartsWith <= USED 0
;FILE Rf_bind.o
LwgAndLtgBindCfInitRfCalData <= USED 0
LwgAndLtgBindGsmRF_RAMInitDoneMsgHandler <= USED 0
LwgAndLtgBindRfCalData_CreateNVMFile <= USED 0
LwgAndLtgBindRspDoSniff <= USED 0
LwgAndLtgBindRspIdleToMonRx <= USED 0
LwgAndLtgBindRspIdleToNormalRx <= USED 0
LwgAndLtgBindRspIdleToTx_1 <= USED 0
LwgAndLtgBindRspIdleToTx_2 <= USED 0
LwgAndLtgBindRspIdleToTx_3 <= USED 0
LwgAndLtgBindRspIdleToTx_4 <= USED 0
LwgAndLtgBindRspRxToIdle <= USED 0
LwgAndLtgBindRspSetAfc <= USED 0
LwgAndLtgBindRspSetAfcAsync <= USED 0
LwgAndLtgBindRspTxToIdle <= USED 0
LwgAndLtgBindRxStartAdvanceGet <= USED 0
LwgAndLtgBindSqCfgCorrectRssi <= USED 0
LwgAndLtgBindSqCfgSfsRadioPowerUp <= USED 0
LwgAndLtgBindplgReadRFSchemeSetting <= USED 0
;FILE RmiFunctions.o
;FILE RtcpByePacket.o
;FILE RtcpCompoundPacket.o
;FILE RtcpRRPacket.o
_ZNK14streamingmedia12RtcpRRPacket10GotoReportEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket13GetSenderSSRCEv <= USED 0
_ZNK14streamingmedia12RtcpRRPacket15GetFractionLostEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket18GetLostPacketCountEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket32GetExtendedHighestSequenceNumberEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket7GetSSRCEj <= USED 0
_ZNK14streamingmedia12RtcpRRPacket9GetJitterEj <= USED 0
;FILE RtcpSDESPacket.o
_ZN14streamingmedia14RtcpSDESPacket11GetItemDataEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket12GotoNextItemEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket13GotoFirstItemEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket13GotoNextChunkEv <= USED 0
_ZN14streamingmedia14RtcpSDESPacket14GotoFirstChunkEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket11GetItemTypeEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket12GetChunkSSRCEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket13GetChunkCountEv <= USED 0
_ZNK14streamingmedia14RtcpSDESPacket13GetItemLengthEv <= USED 0
;FILE RtcpSRPacket.o
_ZNK14streamingmedia12RtcpSRPacket13GetSenderSSRCEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket15GetFractionLostEj <= USED 0
_ZNK14streamingmedia12RtcpSRPacket18GetLostPacketCountEj <= USED 0
_ZNK14streamingmedia12RtcpSRPacket19GetSenderOctetCountEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket20GetSenderPacketCountEv <= USED 0
_ZNK14streamingmedia12RtcpSRPacket7GetSSRCEj <= USED 0
;FILE RtcpSession.o
_ZN14streamingmedia11RtcpSession10processBYEEN7android2spINS_10RtcpPacketEEE <= USED 0
_ZN14streamingmedia11RtcpSession11processSDESEN7android2spINS_10RtcpPacketEEE <= USED 0
_ZN14streamingmedia11RtcpSession15getRtcpTimeInfoERxRi <= USED 0
_ZN14streamingmedia11RtcpSession16onByeRequestSendERKN7android2spINS_8AMessageEEE <= USED 0
_ZN14streamingmedia11RtcpSession7sendByeEv <= USED 0
_ZN14streamingmedia11RtcpSession8sendAVPFERKN7android2spINS_8AMessageEEE <= USED 0
;FILE RtpSession.o
_ZN14streamingmedia10RtpSession10getRtpPortEv <= USED 0
_ZN14streamingmedia13StreamAdapter12getDirectionEv <= USED 0
_ZN14streamingmedia13StreamAdapter14onSetDirectionEv <= USED 0
_ZN14streamingmedia13StreamAdapter15getRtcpTimeInfoERxRi <= USED 0
_ZN14streamingmedia13StreamAdapter21setNATKeepAliveSchemeEb26MEDIA_RTPKeepaliveScheme_t <= USED 0
_ZN14streamingmedia13StreamAdapter9parseRTCPEPNS_18MediaBufferWrapperE <= USED 0
_ZN14streamingmedia18RecRtpSocketLooperC1Ev <= USED 0
;FILE RtpSource.o
_ZN14streamingmedia9RtpSource4stopEv <= USED 0
_ZN14streamingmedia9RtpSource5pauseEv <= USED 0
;FILE RtpWriter.o
_ZN14streamingmedia9RtpWriter12addRTPHeaderEPNS_18MediaBufferWrapperE <= USED 0
_ZN14streamingmedia9RtpWriter13resetSourceIdEj <= USED 0
;FILE SBSearch_test.o
sbSearchTest <= USED 0
;FILE SMIP.o
IMS_RLCb <= USED 0
IMS_RLToLLCb <= USED 0
;FILE SMIP2.o
ims_HdlSMIP2SendAck <= USED 0
;FILE SMRL.o
SMRL_GetConfig <= USED 0
;FILE Saic_test.o
;FILE Se_ber.o
;FILE SessionDescription.o
_ZN14streamingmedia18SessionDescription3logEPKc <= USED 0
;FILE SettingsBluetooth.o
;FILE SettingsBluetoothOnOff.o
;FILE SettingsBluetoothStart.o
;FILE SettingsBtAutoConnect.o
;FILE SettingsBtConnect.o
;FILE SettingsBtCurDevice.o
;FILE SettingsBtFindList.o
;FILE SettingsBtMatchedList.o
;FILE SettingsBtName.o
;FILE SettingsBtOption.o
;FILE SettingsBtPair.o
;FILE SettingsBtScan.o
;FILE SettingsBtSearch.o
;FILE SettingsBtSetup.o
;FILE SettingsBtVisible.o
;FILE SharedBuffer.o
_ZNK7android12SharedBuffer4editEv <= USED 0
_ZNK7android12SharedBuffer5resetEj <= USED 0
;FILE SimCard.o
;FILE SimCardAL.o
SimCardAL_Apps_Get <= USED 0
SimCardAL_IMSI_Get <= USED 0
SimCardAL_IMS_HomeDN_Get <= USED 0
;FILE SimCardUtil.o
ascii_dump <= USED 0
;FILE SimFiles.o
;FILE SimSmsStorage.o
SimCardAL_SMIP_GetMWIStatus <= USED 0
SimCardAL_SMIP_GetPreferredMemoryForStorage <= USED 0
SimCardAL_SMIP_SetMWIStatus <= USED 0
;FILE Sqcfgcal.o
CfOverwriteDigRfCalData <= USED 0
CfOverwriteRfCalData <= USED 0
GetGsmAfcParameter <= USED 0
SendAFCParametersToMSA <= USED 0
cfCheckIsLegalTemperatureZone <= USED 0
cfGetLastRxLnaState <= USED 0
;FILE Sqsfsrad.o
SqCfgCorrectRssiGainToLnaState <= USED 0
SqCfgSfsRadioInitialized <= USED 0
SqCfgSfsRadioPowerUpSparrow <= USED 0
areFwParamsSent <= USED 0
setFwParamsSentFlag <= USED 0
;FILE Sqsfsrx.o
L1CfgUtranMeasBegin <= USED 0
L1CfgUtranMeasFinish <= USED 0
SqCfgSfsUtranRssiMeasSparrow <= USED 0
SqCfgSfsUtranWbMeasBegin <= USED 0
SqCfgSfsUtranWbMeasFinish <= USED 0
;FILE Sqsfstx.o
;FILE StreamingMediaCodecFactory.o
;FILE String16.o
_ZN7android18terminate_string16Ev <= USED 0
_ZN7android8String1610replaceAllEtt <= USED 0
_ZN7android8String165setToEPKt <= USED 0
_ZN7android8String165setToEPKtj <= USED 0
_ZN7android8String165setToERKS0_ <= USED 0
_ZN7android8String165setToERKS0_jj <= USED 0
_ZN7android8String166appendEPKtj <= USED 0
_ZN7android8String166appendERKS0_ <= USED 0
_ZN7android8String166insertEjPKt <= USED 0
_ZN7android8String166insertEjPKtj <= USED 0
_ZN7android8String166removeEjj <= USED 0
_ZN7android8String169makeLowerEv <= USED 0
_ZN7android8String16C1EPKc <= USED 0
_ZN7android8String16C1EPKcj <= USED 0
_ZN7android8String16C1EPKt <= USED 0
_ZN7android8String16C1EPKtj <= USED 0
_ZN7android8String16C1ERKNS_7String8E <= USED 0
_ZN7android8String16C1ERKS0_ <= USED 0
_ZN7android8String16C1ERKS0_jj <= USED 0
_ZN7android8String16C1Ev <= USED 0
_ZN7android8String16D1Ev <= USED 0
_ZNK7android8String1610startsWithEPKt <= USED 0
_ZNK7android8String1610startsWithERKS0_ <= USED 0
_ZNK7android8String168findLastEt <= USED 0
_ZNK7android8String169findFirstEt <= USED 0
;FILE String8.o
_ZN7android17terminate_string8Ev <= USED 0
_ZN7android7String810appendPathEPKc <= USED 0
_ZN7android7String810lockBufferEj <= USED 0
_ZN7android7String811setPathNameEPKc <= USED 0
_ZN7android7String811setPathNameEPKcj <= USED 0
_ZN7android7String812appendFormatEPKcz <= USED 0
_ZN7android7String812unlockBufferEj <= USED 0
_ZN7android7String812unlockBufferEv <= USED 0
_ZN7android7String813appendFormatVEPKcSt9__va_list <= USED 0
_ZN7android7String816convertToResPathEv <= USED 0
_ZN7android7String85setToEPKcj <= USED 0
_ZN7android7String85setToEPKjj <= USED 0
_ZN7android7String85setToEPKtj <= USED 0
_ZN7android7String86appendERKS0_ <= USED 0
_ZN7android7String86formatEPKcz <= USED 0
_ZN7android7String87formatVEPKcSt9__va_list <= USED 0
_ZN7android7String87toLowerEjj <= USED 0
_ZN7android7String87toLowerEv <= USED 0
_ZN7android7String87toUpperEjj <= USED 0
_ZN7android7String87toUpperEv <= USED 0
_ZN7android7String8C1EPKcj <= USED 0
_ZN7android7String8C1EPKj <= USED 0
_ZN7android7String8C1EPKjj <= USED 0
_ZN7android7String8C1EPKt <= USED 0
_ZN7android7String8C1EPKtj <= USED 0
_ZN7android7String8C1ERKNS_8String16E <= USED 0
_ZNK7android7String810getPathDirEv <= USED 0
_ZNK7android7String810getUtf32AtEjPj <= USED 0
_ZNK7android7String811getBasePathEv <= USED 0
_ZNK7android7String811getPathLeafEv <= USED 0
_ZNK7android7String814find_extensionEv <= USED 0
_ZNK7android7String814getUtf32LengthEv <= USED 0
_ZNK7android7String816getPathExtensionEv <= USED 0
_ZNK7android7String88getUtf32EPj <= USED 0
_ZNK7android7String88walkPathEPS0_ <= USED 0
;FILE SysDynSilicon.o
SysUSIMWakeupIntResetEna <= USED 0
Sys_ACIPCIntEna <= USED 0
Sys_AplpRFD_GetRxTxDelay <= USED 0
Sys_CSSR_WDT_KICK_MODE_BIT_SetIsEnable <= USED 0
Sys_CommpmMemConfRequired <= USED 0
Sys_CommpmUsimNmosEngageInUse <= USED 0
Sys_CrdGBdump <= USED 0
Sys_CrdGBdump_L1DataABuffSize <= USED 0
Sys_IsDataOMSLOverACIPC <= USED 0
Sys_IsProductIDTavorLlike <= USED 0
Sys_IsWorkingWith3dBOffset <= USED 0
Sys_TavorSiliconIs_PV_B0orUpper <= USED 0
Sys_getDSSPMode <= USED 0
Sys_intcIsThirdRegisterExist <= USED 0
Sys_isCpuEshel <= USED 0
Sys_mslGetCpuID <= USED 0
Sys_usbVersion <= USED 0
Sys_usimDetectGpioPinNo <= USED 0
Sys_usimUseDma <= USED 0
Sys_wdtKickRegAddress <= USED 0
;FILE Threads.o
_Z24androidCreateThreadGetIDPFvPvES_PS_ <= USED 0
_ZN7android6Thread11_threadLoopEPv <= USED 0
_ZN7android6ThreadC1Eb <= USED 0
_ZNK7android6Thread11exitPendingEv <= USED 0
androidCreateThread <= USED 0
androidCreateThreadEtc <= USED 0
androidGetTid <= USED 0
androidSetCreateThreadFunc <= USED 0
;FILE TimedEventQueue.o
_ZN14streamingmedia15TimedEventQueue11cancelEventEi <= USED 0
_ZN14streamingmedia15TimedEventQueue12cancelEventsEPFbPvRKN7android2spINS0_5EventEEEES1_b <= USED 0
_ZN14streamingmedia15TimedEventQueue13ThreadWrapperEPv <= USED 0
_ZN14streamingmedia15TimedEventQueue15postEventToBackERKN7android2spINS0_5EventEEE <= USED 0
_ZN14streamingmedia15TimedEventQueue18postEventWithDelayERKN7android2spINS0_5EventEEEx <= USED 0
_ZN14streamingmedia15TimedEventQueue5startEv <= USED 0
_ZN14streamingmedia15TimedEventQueue9postEventERKN7android2spINS0_5EventEEE <= USED 0
;FILE UART.o
IsProductionMode <= USED 0
IsUartDiagMode <= USED 0
UARTBaudRateSelfAdapt <= USED 0
UARTCharacterRead <= USED 0
UARTClearDCD <= USED 0
UARTClearRI <= USED 0
UARTConfigurationGet <= USED 0
UARTConfigureINTCtoGPIO <= USED 0
UARTCreateTimer <= USED 0
UARTD2Prepare <= USED 0
UARTGetTraceMode <= USED 0
UARTHwInterruptEnable <= USED 0
UARTIntEnable <= USED 0
UARTIrdaModeControl <= USED 0
UARTLineStatusGet <= USED 0
UARTOff <= USED 0
UARTOn <= USED 0
UARTPrintString <= USED 0
UARTRxWakeLISR <= USED 0
UARTSetDCD <= USED 0
UARTSetRI <= USED 0
UARTTxTimerInit <= USED 0
UARTTxTimerStart <= USED 0
UARTUnConfigureINTCtoGPIO <= USED 0
UARTVertionGet <= USED 0
UARTWakeupEventHandler <= USED 0
UARTWorkLock <= USED 0
diag_send_data_2uart <= USED 0
disable_uart_print <= USED 0
enable_uart_print <= USED 0
isr_arb_timeout2_cp <= USED 0
pmUartInitForce <= USED 0
resume_uart_print <= USED 0
send_data_2uart <= USED 0
send_string_2uart <= USED 0
set_at_diag_mode <= USED 0
uartRxTestCallB <= USED 0
;FILE USBMTTransmission.o
InitRxBufferForGpc <= USED 0
;FILE USBMgrStub.o
USBDeviceStatusGet_Stub <= USED 0
USBMgrDevicePlugIn_Stub <= USED 0
USBMgrDeviceUnplug_Stub <= USED 0
USBMgrEndpointStall_Stub <= USED 0
USBMgrFlushRxBuffer_Stub <= USED 0
USBMgrFlushTxQueue_Stub <= USED 0
USBMgrGetHWCfgEPMaxPacketSize_Stub <= USED 0
USBMgrRegister_Stub <= USED 0
USBMgrRxConfirmationExt_Stub <= USED 0
USBMgrRxConfirmation_Stub <= USED 0
USBMgrSetupCommandRsp_Stub <= USED 0
USBMgrTempFuncUpdateHardCodedUSBDescriptor_Stub <= USED 0
USBMgrTransmit_Stub <= USED 0
UsbMgrGetIfIndexByAppId_Stub <= USED 0
;FILE USBMgrTunnel.o
USBMgrTnlUsbMaskGet <= USED 0
;FILE USSI.o
USSI_MsgHandler <= USED 0
ims_HdlCancelUSSI <= USED 0
ims_HdlInitUSSI <= USED 0
ims_HdlSendUSSI <= USED 0
ims_HdlShutUSSI <= USED 0
;FILE Unicode.o
strcmp16 <= USED 0
strcpy16 <= USED 0
strlen16 <= USED 0
strlen32 <= USED 0
strncmp16 <= USED 0
strncpy16 <= USED 0
strnlen16 <= USED 0
strnlen32 <= USED 0
strzcmp16 <= USED 0
strzcmp16_h_n <= USED 0
utf16_to_utf8 <= USED 0
utf16_to_utf8_length <= USED 0
utf32_from_utf8_at <= USED 0
utf32_to_utf8 <= USED 0
utf32_to_utf8_length <= USED 0
utf8_length <= USED 0
utf8_to_utf16 <= USED 0
utf8_to_utf16_length <= USED 0
utf8_to_utf16_no_null_terminator <= USED 0
utf8_to_utf32 <= USED 0
utf8_to_utf32_length <= USED 0
;FILE UsbMgr.o
M_KiOsGetSignalInt <= USED 0
USBMgrEndpointStall <= USED 0
USBMgrFlushRxBuffer <= USED 0
USBMgrFlushTxQueue <= USED 0
USBMgrGetHWCfgEPMaxPacketSize <= USED 0
USBMgrRegister <= USED 0
USBMgrTempFuncUpdateHardCodedUSBDescriptor <= USED 0
;FILE Utils.o
;FILE VectorImpl.o
_ZN7android10VectorImpl11appendArrayEPKvj <= USED 0
_ZN7android10VectorImpl11setCapacityEj <= USED 0
_ZN7android10VectorImpl12appendVectorERKS0_ <= USED 0
_ZN7android10VectorImpl13insertArrayAtEPKvjj <= USED 0
_ZN7android10VectorImpl14insertVectorAtERKS0_j <= USED 0
_ZN7android10VectorImpl3addEv <= USED 0
_ZN7android10VectorImpl3popEv <= USED 0
_ZN7android10VectorImpl4pushEv <= USED 0
_ZN7android10VectorImpl4sortEPFiPKvS2_E <= USED 0
_ZN7android10VectorImpl4sortEPFiPKvS2_PvES3_ <= USED 0
_ZN7android10VectorImpl8insertAtEjj <= USED 0
_ZN7android10VectorImpl9replaceAtEj <= USED 0
_ZN7android16SortedVectorImpl5mergeERKNS_10VectorImplE <= USED 0
_ZN7android16SortedVectorImpl5mergeERKS0_ <= USED 0
_ZN7android16SortedVectorImplC2ERKNS_10VectorImplE <= USED 0
_ZN7android16SortedVectorImplaSERKS0_ <= USED 0
_ZNK7android10VectorImpl8itemSizeEv <= USED 0
_ZNK7android16SortedVectorImpl7orderOfEPKv <= USED 0
;FILE WS_CmdMsg.o
IPCCommMessageRead <= USED 0
IPCCommReplyRequiredCommandSend <= USED 0
IPCCommReplyRequiredNewOpcodeCommandSend <= USED 0
cmdMsgReturnOSObjects <= USED 0
handleReplyRequiredRdIntToDSP <= USED 0
;FILE WS_CmnSrv.o
APLP_AAAPModeRegistration <= USED 0
BsramMemoryDumpRoutine <= USED 0
IPCCommClose <= USED 0
IPCCommConfig <= USED 0
IPCCommGetConfigure <= USED 0
IPCCommGetVersion <= USED 0
IPCCommMemoryDumpRoutine <= USED 0
IPCCommPhase1Init <= USED 0
IPCCommSpyCmd <= USED 0
IPCCommStatus <= USED 0
IPCCommUnSpyCmd <= USED 0
cmnSrvCheckAppIDValidity <= USED 0
cmnSrvReturnOSObjects <= USED 0
plDratApplicationIdReturn <= USED 0
;FILE WS_Data.o
IPCCommSendSelfEvent <= USED 0
IPCErrorIndicationCallBackBind <= USED 0
dataReturnOSObjects <= USED 0
;FILE WS_HwAcs.o
hwAcsConfigureInterrupts <= USED 0
hwAcsDisableInterrupts <= USED 0
reset_MSA_fix_silent_reset <= USED 0
;FILE WS_IPCICATFunc.o
AAAPDataBufferFreeNotification <= USED 0
AAAPDataChannelFreeNotification <= USED 0
AAAPDataReceivedNotification <= USED 0
AAAPGetDataPointer <= USED 0
getFilterArrayPointer <= USED 0
;FILE WS_TcmSrv.o
IPCCreateEvent <= USED 0
;FILE Xmlussdformatter.o
;FILE aam.o
AAMAcquireHandle <= USED 0
AAMAcquireHandleForLptBeforeIntDis <= USED 0
AAMBeforeIntDisRegister <= USED 0
AAMPhase1Init <= USED 0
AAMPhase2Init <= USED 0
AAM_GET_BIT_MASK_BY_HANDLE <= USED 0
mmiAAMInit <= USED 0
mmiAAMRegister <= USED 0
mmiAAMRequest <= USED 0
mmiAAMUnregister <= USED 0
;FILE abai_fnc.o
;FILE abcb_fnc.o
;FILE abcb_lcl.o
;FILE abcc_aoc.o
;FILE abcc_ect.o
;FILE abcc_fie.o
;FILE abcc_fnc.o
getMoMtCallStatus <= USED 0
;FILE abcc_log.o
;FILE abccapex.o
abccApexCcCallDeflectReq <= USED 0
abccSrvccMakeMobileOriginatedSetup <= USED 0
;FILE abccblcc.o
;FILE abcccaus.o
;FILE abccdata.o
abccHighLayerCompatibility <= USED 0
abccLowLayerCompatibility <= USED 0
abccProgressIndicator <= USED 0
abccSubaddressCompatibility <= USED 0
;FILE abccdeft.o
;FILE abcchand.o
abccCheckRedialCall <= USED 0
;FILE abcclist.o
;FILE abccmncc.o
;FILE abccmpty.o
abccCheckLastMpty <= USED 0
;FILE abccsend.o
;FILE abccsess.o
;FILE abccsndm.o
;FILE abcfg.o
abcfCustomAlphaFind <= USED 0
abcfMccSetKnownBandMode <= USED 0
abcfMepAddNetwork <= USED 0
abcfMepDeleteNetwork <= USED 0
ablmSetTeClose <= USED 0
ablmSetTeOpen <= USED 0
;FILE abem_fnc.o
;FILE abgl_fnc.o
abglPowerDownSelf <= USED 0
printContextInfo <= USED 0
;FILE abgp_fnc.o
ConvertApnToAp <= USED 0
abgpDoAlsiReadNcpipCnf <= USED 0
abgpDoApexAbgpApnDeleteReq <= USED 0
abgpDoApexAbgpApnReadReq <= USED 0
abgpDoApexAbgpApnStatusReq <= USED 0
abgpDoApexAbgpApnWriteReq <= USED 0
abgpProcessApnQueue <= USED 0
;FILE ablm_fn2.o
ablmInitialiseFdnList <= USED 0
;FILE ablm_pb.o
;FILE ablm_ufn.o
ablmAlphaComparePartial <= USED 0
ablmFindAlphaRecord <= USED 0
;FILE ablm_util.o
ablmDnCopyToSim <= USED 0
;FILE abmm_blm.o
abmmBlCopyBaListInfo <= USED 0
abmmBlInitialBaList <= USED 0
abmmBlProcessBaListInfo <= USED 0
abmmBlSimNOk <= USED 0
abmmBlmGetBaInfo <= USED 0
;FILE abmm_bmm.o
abmmBmCheckPendingRatChange <= USED 0
abmmBmGetGsmQuadBandModeLock <= USED 0
abmmBmNewFddLteBandMode <= USED 0
abmmBmNewTdLteBandMode <= USED 0
abmmBmPendRatChange <= USED 0
abmmBmResetNetworkMode <= USED 0
abmmBmResetUserMode <= USED 0
abmmBmSetGsmQuadBandModeLock <= USED 0
abmmDoDelayedApexDevWriteBandModeReq <= USED 0
abmmIsIncludeLteFddBand <= USED 0
abmmRmResetModeWithNonCmccCard <= USED 0
;FILE abmm_cdm.o
abmmBmUpdateOppositeCustomizationData <= USED 0
abmmDoApexMmDynamicReleaseReq <= USED 0
abmmDoMmrUpdateNvmInd <= USED 0
;FILE abmm_jd.o
abmmJdUpdateServiceStatus <= USED 0
;FILE abmm_plm.o
abmmPlCopyAvailableLtePlmns <= USED 0
abmmPlCreateNetworkNameWithSpn <= USED 0
abmmPlDeleteFromPlmnListWithAct <= USED 0
abmmPlDestroyTempForbiddenList <= USED 0
abmmPlIsLastPlmn <= USED 0
abmmPlIsManagedRoamingBlocked <= USED 0
abmmPlIsPowerOn <= USED 0
abmmPlManagedRoamingClearIfDontMatch <= USED 0
abmmPlManagedRoamingClearIfMatch <= USED 0
abmmPlManagedRoamingUpdate <= USED 0
abmmPlNumPrefPlmns <= USED 0
abmmPlPrintPlmnList <= USED 0
abmmPlResetLteManualSearch <= USED 0
abmmPlSuspendResumeDecideSuspendOrAbort <= USED 0
abmmPlUserWaitingForPlmnList <= USED 0
;FILE abmm_pmm.o
abmmPmResuming <= USED 0
;FILE abmm_rmm.o
abmmCheckReselectStatus <= USED 0
abmmDoMmrAbortRegCnf <= USED 0
abmmDoMmrReselectInd <= USED 0
abmmIsMoPsDetach <= USED 0
abmmIsNeedReselectToLteDisableEutranFail <= USED 0
abmmIsSim2ModeCorrectForLw <= USED 0
abmmPlIsRoamingForbidenPlmn <= USED 0
abmmRmAreAllLteOnlyHplmnsInList <= USED 0
abmmRmCheckInvalidImei <= USED 0
abmmRmFindAnyHplmn <= USED 0
abmmRmGetManualPlmn <= USED 0
abmmRmHplmnIsBoltSmartfrenOrnSim <= USED 0
abmmRmHplmnIsCMHKSim <= USED 0
abmmRmHplmnIsChinaPlmn <= USED 0
abmmRmHplmnIsPlay <= USED 0
abmmRmInformSACRegCmccRoaming <= USED 0
abmmRmInterruptReg <= USED 0
abmmRmIsDoneNotPrefHplmn <= USED 0
abmmRmIsDoneThirdPrefHplmn <= USED 0
abmmRmIsHpplmnSearch <= USED 0
abmmRmIsNarrowHplmnSearchNeededAndCalcNetworkMode <= USED 0
abmmRmIsNeedTryMoreOnLteForOos <= USED 0
abmmRmIsPlmnAwaitingMmRetry <= USED 0
abmmRmIsTestSimMcc <= USED 0
abmmRmPartiallyRegistered <= USED 0
abmmRmPeerTaskRecoverStatus <= USED 0
abmmRmReceivedForbiddenNatLaInDualRatManualMode <= USED 0
abmmRmReceivedForbiddenPlmnInDualRatManualMode <= USED 0
abmmRmReregister <= USED 0
abmmRmStopTimersWithoutHplmnTimer <= USED 0
abmmRmWhetherEPlmnlistNeedUpdate <= USED 0
debug_abmmRmActionDummy <= USED 0
debug_abmmRmStateDummy <= USED 0
debug_abmmRmStateSilentAttach <= USED 0
debug_abmmRmStateSilentDetach <= USED 0
get_commonReserved_2_value <= USED 0
get_commonReserved_3_value <= USED 0
;FILE abmm_sig.o
abmmDoAnrm2DeleteDataCnf <= USED 0
abmmRmGetnumberOfSeparateGsmBandSearches <= USED 0
abmmRmSetSeparateGsmSearchBand <= USED 0
abmmSendAnrm2DeleteDataReq <= USED 0
abmmSendAnrm2DeleteFileRecordReq <= USED 0
abmmSendAnrm2ListFileRecordsReq <= USED 0
abmmSendAnrm2ReadFileRecordReq <= USED 0
abmmSendAnrm2WriteFileRecordReq <= USED 0
abmmSendApexMmPlmnSearchedInd <= USED 0
abmmSendApexMmPlmnSelectInd <= USED 0
abmmSendApexMmSetHSPAConfigCnf <= USED 0
abmmSendApexMmWriteBandModeCnf <= USED 0
abmmSendApexMmWriteUdPlmnListCnf <= USED 0
abmmSendMmrAbortRegReq <= USED 0
abmmSendMmrExplicitEcallInactivityReq <= USED 0
abmmSgIsOpPrefPlmn <= USED 0
abmmSigGetNumOfBand <= USED 0
abmmsendMmrSetMedata <= USED 0
;FILE abmm_utils.o
strnlen_util <= USED 0
;FILE abmmdebug.o
;FILE abmmmain.o
abmmCheckOtherCardSendPlmnSearch <= USED 0
abmmRmHplmnIsChinaUnicom <= USED 0
;FILE abnv_init.o
ICATsetgpAlwaysAttach <= USED 0
SetCipheringA5Algo <= USED 0
SetGprsAlgo <= USED 0
abmmCdHandleMeCustomConfig <= USED 0
;FILE abps_dbg.o
;FILE abps_fnc.o
;FILE abps_rrlp.o
;FILE abpsagps.o
abpsAgpsStartTimer <= USED 0
abpsAgpsStopTimer <= USED 0
;FILE absh_fnc.o
abshSendIndExcludeTask <= USED 0
;FILE absh_lcl.o
abshDoMmiKeyPressInd <= USED 0
abshDoMmiPowerKeyInd <= USED 0
abshOnQueue <= USED 0
abshSendApexGlErrorInd <= USED 0
;FILE abshmain.o
abshTask1 <= USED 0
abshTask21 <= USED 0
abshTask2ExitRoutine <= USED 0
abshTaskExitRoutine <= USED 0
;FILE absi_fnc.o
absiGetCurrentHplmn <= USED 0
absiGetImsi <= USED 0
absiGetMsAdminAdditionalInfo <= USED 0
absiGetOperatorName <= USED 0
absiGetOperatorNamewithPnn <= USED 0
absiGetPlmnFromImsi <= USED 0
absiIsNetworkCodeInUse <= USED 0
absiIsPlmnValid <= USED 0
absiIsinSpdiList <= USED 0
absiResetMepCodes <= USED 0
absiResetNetworkCodes <= USED 0
absiSetRatBalancingMode <= USED 0
;FILE absisigs.o
absiDoAlsiCphsReadCffCnf <= USED 0
absiDoAlsiCphsWriteCffCnf <= USED 0
absiDoApexSimIdleReq <= USED 0
absiDoApexSimReadCffReq <= USED 0
absiDoApexSimReadMepNetworkIdReq <= USED 0
absiDoApexSimWriteCffReq <= USED 0
absiDoApexSimWriteMepNetworkIdReq <= USED 0
absiFetchSimDataIfAny <= USED 0
absiSetMepFromStoredData <= USED 0
;FILE absm_apx.o
;FILE absm_ems.o
;FILE absm_ini.o
;FILE absm_nv.o
;FILE absm_sim.o
;FILE absm_trn.o
;FILE absm_ts.o
;FILE absm_uti.o
absmDeleteSmspParameters <= USED 0
absmGetFreeStoredMsg <= USED 0
absmGetMsgByReference <= USED 0
;FILE abss_dec.o
abssAdvAsnPointer <= USED 0
;FILE abss_enc.o
abssEncErrorParameter <= USED 0
abssEncSsCallDeflectInfo <= USED 0
abssEncUssUserData <= USED 0
abssEncodeBoolean <= USED 0
abssEncodeImplicitOctetString <= USED 0
abssEncodeSingleOctet <= USED 0
abssEncodeSsIncomp <= USED 0
abssEncodeSubsViolation <= USED 0
;FILE abss_etb.o
;FILE abss_fn2.o
abssPrintCFCache <= USED 0
isCfServicePresent <= USED 0
;FILE abss_fnc.o
abssEncInvCallDeflectInfo <= USED 0
abssEncInvProcUssData <= USED 0
abssEncReject <= USED 0
abssEncReturnError <= USED 0
abssGetUssMsgUserData <= USED 0
;FILE abss_sub.o
abssGetLinkedInvokeId <= USED 0
abssValidAbortHandle <= USED 0
abssValidCallReference <= USED 0
;FILE abss_tbl.o
;FILE abss_tec.o
;FILE abss_usd.o
;FILE abst_fn2.o
;FILE abst_fn3.o
;FILE abst_fn4.o
;FILE abst_fn5.o
;FILE abst_fn6.o
;FILE abst_fn7.o
;FILE abst_fnc.o
;FILE abtecfg.o
asstCloseTeSetUpMenu <= USED 0
asstOpenTeSetUpMenu <= USED 0
;FILE access_intf.o
AccessIntf_CallDeflect <= USED 0
AccessIntf_CallTransfer <= USED 0
AccessIntf_GetSSConfig <= USED 0
AccessIntf_NW_GetCaps <= USED 0
AccessIntf_NW_GetConfig <= USED 0
AccessIntf_NW_GetProperty <= USED 0
AccessIntf_NW_SetConfig <= USED 0
AccessIntf_NW_SetProperty <= USED 0
AccessIntf_PS_GetConfig <= USED 0
AccessIntf_PS_GetProperty <= USED 0
AccessIntf_QueryFirstAvailableNetwork <= USED 0
AccessIntf_QueryNetwork <= USED 0
AccessIntf_QueryNextAvailableNetwork <= USED 0
AccessIntf_SelectNetwork <= USED 0
AccessIntf_SetSSConfig <= USED 0
;FILE acmDspIf.o
ACMDspIfAudioCTMCtrl <= USED 0
ACMDspIfAudioDownLinkModeGet <= USED 0
ACMDspIfAudioSetSspConfiguration <= USED 0
ACMDspIfAudioUpLinkModeGet <= USED 0
;FILE acm_COMM.o
;FILE acm_Codec.o
GSSP_SEL_BY_CP <= USED 0
GSSP_SEL_REG_ADDR <= USED 0
IsCPOnlyBoard <= USED 0
;FILE acm_al.o
;FILE acm_atc.o
ATC_Process_PCM_data <= USED 0
;FILE acm_audio_effect.o
acm_audio_effect_init <= USED 0
;FILE acm_audio_record.o
acm_audio_record_control <= USED 0
acm_audio_record_get_softgain <= USED 0
acm_audio_record_set_softgain <= USED 0
;FILE acm_audio_track.o
acm_audio_track_get_softgain <= USED 0
;FILE acm_calibration.o
;FILE acm_control.o
ACMAudio_GetDSPSettings <= USED 0
ACMUpdateAudioNVM <= USED 0
ACMUpdateAudioNVMString <= USED 0
ACMWriteAudioNVM <= USED 0
;FILE acm_pm812.o
pm812_audio_init <= USED 0
;FILE acm_stream.o
ACMAudioStreamOutStart <= USED 0
ACMAudioStreamOutStop <= USED 0
ACMStreamReset <= USED 0
;FILE adups_HMD5.o
adups_hmd5_calc <= USED 0
;FILE adups_cjson.o
adups_cJSON_AddItemReferenceToArray <= USED 0
adups_cJSON_AddItemReferenceToObject <= USED 0
adups_cJSON_CreateArray <= USED 0
adups_cJSON_CreateBool <= USED 0
adups_cJSON_CreateDoubleArray <= USED 0
adups_cJSON_CreateFalse <= USED 0
adups_cJSON_CreateFloatArray <= USED 0
adups_cJSON_CreateIntArray <= USED 0
adups_cJSON_CreateNull <= USED 0
adups_cJSON_CreateStringArray <= USED 0
adups_cJSON_CreateTrue <= USED 0
adups_cJSON_DeleteItemFromArray <= USED 0
adups_cJSON_DeleteItemFromObject <= USED 0
adups_cJSON_DetachItemFromArray <= USED 0
adups_cJSON_DetachItemFromObject <= USED 0
adups_cJSON_Duplicate <= USED 0
adups_cJSON_GetArrayItem <= USED 0
adups_cJSON_GetArraySize <= USED 0
adups_cJSON_GetErrorPtr <= USED 0
adups_cJSON_GetObjectItem <= USED 0
adups_cJSON_Minify <= USED 0
adups_cJSON_Parse <= USED 0
adups_cJSON_ParseWithOpts <= USED 0
adups_cJSON_PrintUnformatted <= USED 0
adups_cJSON_ReplaceItemInArray <= USED 0
adups_cJSON_ReplaceItemInObject <= USED 0
adups_json_get_value <= USED 0
;FILE adups_cjsonint.o
;FILE adups_config.o
adups_get_device_network_type <= USED 0
;FILE adups_fota.o
AdupsUseFlash <= USED 0
Adups_Get_Block_Size <= USED 0
dump_data <= USED 0
;FILE adups_md5.o
;FILE adups_request.o
;FILE adups_stdlib.o
adups_atoi <= USED 0
adups_dt_curtime2utcsec <= USED 0
adups_memcmp <= USED 0
adups_strchr <= USED 0
adups_strncat <= USED 0
adups_wstrcmp <= USED 0
;FILE adups_user.o
;FILE aes.o
mbedtls_aes_decrypt <= USED 0
mbedtls_aes_encrypt <= USED 0
;FILE aes_basic.o
;FILE aes_test.o
;FILE afc_test.o
AFCDriverToIntegrationTest <= USED 0
AFCShiftFrequencyOffsetBackward <= USED 0
AFCShiftFrequencyOffsetForward <= USED 0
;FILE ah.o
;FILE amidala_md5.o
sd_md5 <= USED 0
;FILE amr_payload.o
unpackAmrFrame <= USED 0
;FILE api_lib.o
netconn_close <= USED 0
netconn_gethostbyname <= USED 0
netconn_sendto <= USED 0
netconn_shutdown <= USED 0
netconn_write <= USED 0
;FILE api_msg.o
;FILE aplp_stub_ds3.o
BcchGsmToUtranReselection <= USED 0
PlMsrCheckIfCellSearchActiveAndAbort <= USED 0
crcLengthConvert <= USED 0
dlmControllerSend <= USED 0
dlmPrintf <= USED 0
getBchFromOtherRatFlag <= USED 0
getCycleBchCounter <= USED 0
gwiAbortBch <= USED 0
gwiAckEndOfWbRfAllocation <= USED 0
gwiDsdsGsm2WbSleepReq <= USED 0
gwiDsdsGsmActivateRsp <= USED 0
gwiDsdsSchdGsmGapFinishedInd <= USED 0
gwiDsdsSchdGsmGapReq <= USED 0
gwiDsdsSuspendWbReq <= USED 0
gwiDsdsWbActivateRsp <= USED 0
gwiDsdsWbDeactivateRsp <= USED 0
gwiGetWbMeasIndicator <= USED 0
gwiStartBch <= USED 0
plAMPlpTransReq <= USED 0
plAMgetWbResumeFlag <= USED 0
plAmCheckValidityOfBchDecodeParameters <= USED 0
plAmSpyWriteRrcCnfEvent <= USED 0
plAmUpdatePlpSm <= USED 0
plAtlGetSfn <= USED 0
plDataHandleFrameInterrupt <= USED 0
plMSPccpchSetNumOfShifts <= USED 0
plMSRRakeUpdatePathInfoEvent <= USED 0
plMsDratSet3GRestore <= USED 0
plMsrAfcDacTableFileUpdate <= USED 0
plMsrBcchTimerExpired <= USED 0
plMsrClearMcl <= USED 0
plMsrDbClearAllPathsMeasuremnts <= USED 0
plMsrGsWbReset <= USED 0
plMsrGsmPlmnPrintDb <= USED 0
plMsrGsmSrvGSMPowerup <= USED 0
plMsrGsmSrvGSMReset <= USED 0
plMsrGsmSrvGSMStartup <= USED 0
plMsrGsmSrvGSMTerminate <= USED 0
plMsrInGsmRestoreMsrModeAndStateIfExist <= USED 0
plMsrInLteRestoreMsrModeAndStateIfExist <= USED 0
plMsrPlpGsmRefTimeSet <= USED 0
plMsrSendAPLPApiEvent <= USED 0
plMsrSendMultiBcchReportToRrc <= USED 0
plMsrStopSkipPreventor <= USED 0
plMsr_AM_SearchEngInit <= USED 0
plRFDGetBandByUarfcn <= USED 0
plRFDGetSupportedRfBands <= USED 0
plRFDIsDlUarfcnValid <= USED 0
plRFDIsMaxPowerActive <= USED 0
plTCCDefaultBindFunction <= USED 0
plTccSetStopShift <= USED 0
pldRDASetGsm <= USED 0
pldSetWcdma <= USED 0
pldWcdmaTerminate <= USED 0
plwBindCphyDetectedCellMeasInd <= USED 0
plwBindCphyFreqScanInd <= USED 0
plwBindCphyIntraFreqCellMeasurementInd <= USED 0
plwBindCphyMeasuredIntraFreqCellsInd <= USED 0
plwBindCphyOutOfSyncInd <= USED 0
plwBindCphyPsOutOfSyncInd <= USED 0
plwBindCphyPsSyncInd <= USED 0
plwBindCphyRlReleaseCnf <= USED 0
plwBindCphyRlSetupCnf <= USED 0
plwBindCphySyncInd <= USED 0
plwBindPhyDataInd <= USED 0
plwBindPhyDownlinkDataTransferEnd <= USED 0
plwBindPhyHsDataInd <= USED 0
plwBindPhyUplinkDataSync <= USED 0
plwBindPhyUplinkDataTransferEnd <= USED 0
plwCphyDeactivateReq_internal <= USED 0
plwCphyDetectedCellMeasReq <= USED 0
plwCphyDpchSetupReq <= USED 0
plwCphyFreqScanReq <= USED 0
plwCphyIntraFreqCellMeasReq <= USED 0
plwCphyPccpchSetupReq <= USED 0
plwCphyRlReleaseReq <= USED 0
plwCphySetCompressedModeParams <= USED 0
plwDpchDlEstablished <= USED 0
plwLoopBackMode2 <= USED 0
plwNotifyNextTtiTfc <= USED 0
plwPhyDataReq <= USED 0
plwPhyHsPointerAssignReq <= USED 0
resetBchFromOtherRatFlag <= USED 0
schdAbortIntrabchWhenResumeGsmorWb <= USED 0
setBcchDecodeState <= USED 0
;FILE arc4random.o
;FILE arm946e_sCP15.o
Arm946eCP15DrainWriteBuffer <= USED 0
Arm946eCP15FlushDataCache <= USED 0
Arm946eCP15FlushInstCache <= USED 0
Arm946eCP15GetAlternateVectorSelect <= USED 0
Arm946eCP15GetBigEndian <= USED 0
Arm946eCP15GetCacheTypeReg <= USED 0
Arm946eCP15GetDataCacheEnable <= USED 0
Arm946eCP15GetDataCacheSize <= USED 0
Arm946eCP15GetDataTCMEnable <= USED 0
Arm946eCP15GetDataTCMLoadMode <= USED 0
Arm946eCP15GetDataTCMRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetDataTCMSize <= USED 0
Arm946eCP15GetInstCacheEnable <= USED 0
Arm946eCP15GetInstTCMEnable <= USED 0
Arm946eCP15GetInstTCMLoadMode <= USED 0
Arm946eCP15GetInstTCMRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetInstTCMSize <= USED 0
Arm946eCP15GetInstructionCacheSize <= USED 0
Arm946eCP15GetRegionBaseAndSizeReg <= USED 0
Arm946eCP15GetTCMSizeReg <= USED 0
Arm946eCP15Read <= USED 0
Arm946eCP15RestoreDataTCMEnable <= USED 0
Arm946eCP15RestoreInstTCMEnable <= USED 0
Arm946eCP15SetAlternateVectorSelect <= USED 0
Arm946eCP15SetBigEndian <= USED 0
Arm946eCP15SetControlReg <= USED 0
Arm946eCP15SetDataCacheDisable <= USED 0
Arm946eCP15SetDataTCMDisable <= USED 0
Arm946eCP15SetDataTCMLoadMode <= USED 0
Arm946eCP15SetInstCacheDisable <= USED 0
Arm946eCP15SetInstTCMDisable <= USED 0
Arm946eCP15SetInstTCMLoadMode <= USED 0
Arm946eCP15SetLowPowerState <= USED 0
Arm946eCP15Write <= USED 0
;FILE arm946e_sCP15_memRetain_TCS.o
Arm946eCP15GetControlReg_DS <= USED 0
Arm946eCP15GetMPUEnalbe_DS <= USED 0
Arm946eCP15RestoreMPUEnableDisable_DS <= USED 0
Arm946eCP15SetDataCacheEnable_DS <= USED 0
Arm946eCP15SetDataTCMEnable_DS <= USED 0
Arm946eCP15SetDataTCMRegionBaseAndSizeReg_DS <= USED 0
Arm946eCP15SetInstCacheEnable_DS <= USED 0
Arm946eCP15SetInstTCMEnable_DS <= USED 0
Arm946eCP15SetInstTCMRegionBaseAndSizeReg_DS <= USED 0
Arm946eCP15SetMPUDisable_DS <= USED 0
Arm946eCP15SetMPUEnalbe_DS <= USED 0
Arm946eCP15SetRegionAccessPermission_DS <= USED 0
Arm946eCP15SetRegionBaseAndSizeReg_DS <= USED 0
;FILE ascb_fnc.o
;FILE ascc_fnc.o
isCallControlActive <= USED 0
isMosmControlActive <= USED 0
simatCommandType <= USED 0
;FILE ascs_fnc.o
ascsGetFirstNwStateInd <= USED 0
simatGetEventList <= USED 0
simatGetLanguageCode <= USED 0
;FILE asgki.o
;FILE asgl_cfg.o
;FILE asgl_fnc.o
;FILE asic_fnc.o
;FILE asmn_fnc.o
simatTask1 <= USED 0
simatTask21 <= USED 0
;FILE asmn_proc2.o
;FILE asms_fnc.o
;FILE aspl_fnc.o
provideLocalInfoAvailable <= USED 0
;FILE aspp_fnc.o
;FILE asr3601s_fota.o
Asr3601s_fotaFlash_Read <= USED 0
asr3601_SPI_MutexLock <= USED 0
asr3601_SPI_MutexUnlock <= USED 0
asr3601_qspiflash_readid <= USED 0
asr3601spi_nor_do_erase <= USED 0
asr3601spi_nor_do_read_single <= USED 0
;FILE asr_adups.o
Adups_fota_backup_st <= USED 0
dump_fotapack <= USED 0
fotaboot_prepare <= USED 0
is_deviceregistered <= USED 0
;FILE asr_bt_prepare.o
;FILE asrd_fnc.o
simatRefreshInit <= USED 0
;FILE assd_fnc.o
simatSdCallHeld <= USED 0
;FILE assm_fnc.o
;FILE asss_fnc.o
simatSsIsIdle <= USED 0
;FILE asss_utl.o
;FILE astm_fnc.o
;FILE atParser.o
ATReParse <= USED 0
utlAtParserInitCommandParameters <= USED 0
utlCloseAtParser <= USED 0
utlSetEcho <= USED 0
;FILE at_cmds_mmi.o
;FILE at_common.o
;FILE at_iointf_requestor.o
;FILE at_parser.o
;FILE at_prop.o
ATPROP_GetCMEMFULL <= USED 0
ATPROP_GetCSMS <= USED 0
ATPROP_GetCVMOD <= USED 0
ATPROP_GetNewSMSIndicationSettings <= USED 0
ATPROP_GetPreferredMemoryForStorage <= USED 0
;FILE at_provider.o
AT_SendTestResultCode <= USED 0
at_provider_get_proxy_req_group_name <= USED 0
at_provider_update_crnt_proxy_req <= USED 0
;FILE at_proxy.o
AT_Proxy_SetIO <= USED 0
;FILE at_proxy_provider.o
at_Proxy_SetInitOwner <= USED 0
;FILE at_requestor.o
AT_CancelRequest <= USED 0
AT_CancelSyncRequest <= USED 0
;FILE atcmd_hdlr.o
atcmd_map_from_sdp_media_dir <= USED 0
;FILE atcmd_list.o
at_cmgr_addr_or_fo_param_cb <= USED 0
;FILE audioDspIf.o
;FILE audio_bind.o
POCVoice_MuteTTSPlay <= USED 0
POCVoice_StartTTSPlay <= USED 0
POCVoice_StopTTSPlay <= USED 0
audioBindDoDataSwapBytes <= USED 0
audioBindGetRAT <= USED 0
;FILE audio_pcm.o
tg_pcm_close <= USED 0
tg_pcm_open <= USED 0
tg_pcm_read <= USED 0
tg_pcm_set_state_listener <= USED 0
tg_pcm_start <= USED 0
tg_pcm_stop <= USED 0
tg_pcm_write <= USED 0
tg_volume_set <= USED 0
;FILE audio_resample.o
;FILE audio_server_api_ttc.o
mux27010_dlc_open <= USED 0
mux27010_read_ack <= USED 0
mux27010_write <= USED 0
;FILE audio_server_task_ttc.o
audioDownLink20msCB <= USED 0
;FILE audio_voice.o
;FILE automode.o
WebRtcNetEQ_BufferLevelFilter <= USED 0
;FILE backend_flash.o
backend_free_env <= USED 0
;FILE bspUartManager.o
bspUartApplUseAltFunc <= USED 0
bspUartGetIoUart <= USED 0
bspUartNumOfUartsGet <= USED 0
bspUartPhase1Init <= USED 0
bspUartPlatUseAltFunc <= USED 0
;FILE bsp_hisr.o
OSATaskGetCurrentRefExt <= USED 0
Os_Delete_HISR <= USED 0
bspParkThread <= USED 0
;FILE bsp_tavor.o
DelayBeforeCPOff <= USED 0
EnableDataPktInfo <= USED 0
EnableDataPktInfoCmd <= USED 0
EnableDebugLog <= USED 0
GetUmtsChipVersion <= USED 0
InitGPIOWakeupForUSB <= USED 0
IsChipCrane <= USED 0
IsChipCraneG_A0 <= USED 0
IsChipCraneG_A1 <= USED 0
IsChipCraneG_Z1 <= USED 0
IsChipCraneG_Z2 <= USED 0
IsChipCraneM_A0 <= USED 0
IsChipCraneM_A1 <= USED 0
IsChipCraneM_Z1 <= USED 0
IsChipCraneM_Z2 <= USED 0
IsChip_CraneG_A0_or_Above <= USED 0
IsChip_CraneM_A0_or_Above <= USED 0
IsChip_Crane_A0_or_Above <= USED 0
ReEnableUSIMGPIODetection <= USED 0
SetspecialPShandle <= USED 0
USIM_GPIO_WK <= USED 0
bootup_mode_cfun4 <= USED 0
bspBoardIsDKB <= USED 0
bspGetIpcInts <= USED 0
bspGetPlpFrameInt <= USED 0
bspI2CToNAInit <= USED 0
bspI2cInit <= USED 0
bspInit <= USED 0
bspInitRfInterface <= USED 0
bspIpcInit <= USED 0
eeh_gpio_ind <= USED 0
hsi_gpio_init <= USED 0
hsic_emu_wait_ap_gpio <= USED 0
hsic_gpio_init <= USED 0
hsic_ready_gpio_ind <= USED 0
hsic_ready_gpio_set_after_enum <= USED 0
is_pmic801_used_func <= USED 0
platform_type_init <= USED 0
set_cp2ap_pm_gpio_high <= USED 0
set_cp2ap_pm_gpio_low <= USED 0
set_ps_dev_chnl_ready <= USED 0
usim2_enable <= USED 0
usim_gpio_init <= USED 0
;FILE bt_api.o
appbt_config_bt_mode <= USED 0
appbt_get_open_status <= USED 0
appbt_pin_negative_reply <= USED 0
get_bt2host_gpio_no <= USED 0
;FILE bt_api_a2dp.o
asrbt_a2dp_sbc_process <= USED 0
;FILE bt_api_avrcp.o
;FILE bt_api_hfp.o
appbt_get_sco_status <= USED 0
appbt_hfp_reject_connection <= USED 0
appbt_switch_voice_to_headset <= USED 0
appbt_switch_voice_to_phone <= USED 0
;FILE bt_api_le.o
appbt_le_connect <= USED 0
appbt_le_create_adv_data <= USED 0
appbt_le_disconnect <= USED 0
appbt_le_mtu_request <= USED 0
appbt_le_notify <= USED 0
appbt_le_read_by_group_type_request <= USED 0
appbt_le_register_att_event_handle <= USED 0
appbt_le_register_service <= USED 0
appbt_le_set_adv_data <= USED 0
appbt_le_set_adv_parameters <= USED 0
appbt_le_set_random_address <= USED 0
;FILE bt_api_obex.o
;FILE bt_mgr.o
appbt_get_a2dp_status <= USED 0
appbt_get_device_status <= USED 0
;FILE bt_uart.o
bt_dma_transfer_request <= USED 0
bt_uart_clear_receive_fifo <= USED 0
bt_uart_clear_transfer_fifo <= USED 0
bt_uart_enable_int <= USED 0
bt_uart_flush <= USED 0
bt_uart_get_IntStatus <= USED 0
bt_uart_get_base_addr <= USED 0
bt_uart_getc <= USED 0
bt_uart_getchar_noblock <= USED 0
bt_uart_init <= USED 0
bt_uart_is_RxInt <= USED 0
bt_uart_set_baudrate <= USED 0
;FILE bufstats_decision.o
WebRtcNetEQ_BufstatsDecision <= USED 0
;FILE c_ip.o
;FILE c_udp.o
;FILE cb.o
CbTask1 <= USED 0
CbTask21 <= USED 0
CbTaskExitRoutine <= USED 0
CbTaskExitRoutine2 <= USED 0
;FILE cbencdec.o
;FILE cbschd.o
;FILE cc_api.o
CC_SyncAudio <= USED 0
PARSER_START_RINGING <= USED 0
resetAllCalls <= USED 0
;FILE ccmakmsg.o
;FILE ccmakprm.o
;FILE ccmodify.o
;FILE ccprocms.o
;FILE ccprocpr.o
;FILE ccrec.o
;FILE ccrouter.o
CcTask1 <= USED 0
CcTaskExitRoutine <= USED 0
CcTask_21 <= USED 0
CcTask_2ExitRoutine <= USED 0
;FILE ccstmach.o
;FILE cellular.o
;FILE cellular_cs.o
;FILE cellular_ps.o
;FILE cgpio.o
GpioClearEdgeDetection_wakeup <= USED 0
GpioCommonWakeupCallback <= USED 0
GpioDisableEdgeDetection <= USED 0
GpioDisableEdgeDetection_wakeup <= USED 0
GpioEnableEdgeDetection_wakeup <= USED 0
GpioPhase1Init <= USED 0
;FILE ci_api.o
;FILE ci_cc_mem.o
;FILE ci_client_task_ttc.o
;FILE ci_client_ttc.o
Bcd2String <= USED 0
ReleaseCiClientMem <= USED 0
ciRequest_Transfer_client <= USED 0
ciRespond_1_client <= USED 0
ciShDeregisterReq_1_client <= USED 0
ciShRegisterReq_1_client <= USED 0
ciShRequest_1_client <= USED 0
clearCciActionlist <= USED 0
clientCiDefConfirmCallback_client <= USED 0
clientCiSgFreeReqMem <= USED 0
clientCiShConfirmCallback_client <= USED 0
findCiSgId <= USED 0
string2Number <= USED 0
;FILE ci_dat_mem.o
;FILE ci_dev_mem.o
;FILE ci_mem.o
cimem_CiSgCnfIndAllocMem <= USED 0
cimem_CiSgFreeMem <= USED 0
cimem_CiShAllocCnfMem <= USED 0
cimem_CiShAllocReqMem <= USED 0
cimem_CiShFreeCnfMem <= USED 0
cimem_GetCiNumericListDataSize <= USED 0
cimem_GetCiShCnfDataSize <= USED 0
cimem_GetCiShReqDataSize <= USED 0
;FILE ci_mm_mem.o
CiClearAnayArfcnNvm <= USED 0
CiGetArfcnFromNvm <= USED 0
CiInsertArfcnNvm <= USED 0
CiUpdateAllArfcnNvm <= USED 0
;FILE ci_msg_mem.o
;FILE ci_msl.o
GPC_TransmitRequest <= USED 0
GPC_dataReceiveReady <= USED 0
MslAlignFree <= USED 0
MslUtilsMemDeInit <= USED 0
MslUtilsMemInit <= USED 0
;FILE ci_pb_mem.o
;FILE ci_ps_mem.o
;FILE ci_server_task_ttc.o
ACIPCDImsRxdefault <= USED 0
ACIPCDImsTxdefault <= USED 0
;FILE ci_sim_mem.o
;FILE ci_ss_mem.o
;FILE cid.o
;FILE cimodem.o
DataServiceResumed <= USED 0
Deactive_PDP_Context_by_Itself <= USED 0
LinkStatusIndCB <= USED 0
SendATCmdChars <= USED 0
ci_modem_rx <= USED 0
ci_modem_set_channel_data_stop_flag <= USED 0
mprintf <= USED 0
;FILE cimodem_usb.o
atmodemDataRxInd <= USED 0
atmodemDataRxInd_modem2 <= USED 0
usb_tx <= USED 0
;FILE cinit1.o
Arm946eDisableInterrupts <= USED 0
Arm946eEnableInterrupts <= USED 0
Arm946eMemcpy <= USED 0
Arm946eRestoreInterrupts <= USED 0
Cinit1 <= USED 0
Configure_RTC_WTC <= USED 0
DisableMFPRGSSPFunction <= USED 0
DynamicMemoryInit <= USED 0
EnableMFPRGSSPFunction <= USED 0
GetSiliconFoundry <= USED 0
IS_TDNewChip <= USED 0
MFPR_Config <= USED 0
TDChipIS_A0 <= USED 0
TDChipIS_A1 <= USED 0
TDChipIS_Y2 <= USED 0
WA_40LP_PLL1_stablize <= USED 0
copyRWtoImage <= USED 0
mmiHeapInit <= USED 0
;FILE cinit2.o
Cinit2 <= USED 0
;FILE cmdintf.o
;FILE cng.o
WebRtcNetEQ_CngUpdateState <= USED 0
;FILE codec_db.o
WebRtcNetEQ_DbAdd <= USED 0
WebRtcNetEQ_DbGetCodec <= USED 0
WebRtcNetEQ_DbGetPtrs <= USED 0
WebRtcNetEQ_DbRemove <= USED 0
;FILE comBasicCfg_nvm.o
;FILE comcfgprc.o
PsComCfgProcNoNvmPChar <= USED 0
PsComCfgProcNvm <= USED 0
PsComCfgProcNvmMultiple <= USED 0
PsComfCfgNvmExec <= USED 0
;FILE commpm.o
CLibInit <= USED 0
CommPMCP15D2Prepare <= USED 0
CommPMD4Enter <= USED 0
CommPMDisableUsbWakeupSource <= USED 0
CommPMFakeSleepEnable <= USED 0
CommPMGetWBWakeupTime <= USED 0
CommPMHardwareInit <= USED 0
CommPMPowerdownUsbPhy <= USED 0
CommPMPowerupUsbPhy <= USED 0
DEBUG_ASSERT <= USED 0
DumpCPADebugInfo <= USED 0
DumpFlashFlushDebugInfo <= USED 0
DumpTimerThreadDebugInfo <= USED 0
EnableL1Register <= USED 0
ForceMSADisableSleep <= USED 0
GetDDRLock <= USED 0
GetPMWakupEvent <= USED 0
SetMSASLPEN <= USED 0
SetTDRFPinFloating <= USED 0
SetTDRFPinPulldown <= USED 0
SetWakeupMaskForTDSCDMA <= USED 0
UnsetWakeupMaskForTDSCDMA <= USED 0
getUSBConnectStatus <= USED 0
mmiC1CallbackRegister <= USED 0
mmiD2CallbackRegister <= USED 0
mmiStatusCallbackRegister <= USED 0
timer_thread_hook_before <= USED 0
;FILE commpm_debug.o
CommPMTestTask <= USED 0
perf_cnt_begin <= USED 0
perf_cnt_stop <= USED 0
test_sample <= USED 0
;FILE comp_ip_id_offset.o
rohc_comp_detect_ip_id_behavior <= USED 0
;FILE comp_list.o
;FILE comp_list_ipv6.o
;FILE comp_rfc4996.o
c_field_scaling <= USED 0
c_optional_ip_id_lsb <= USED 0
c_static_or_irreg16 <= USED 0
c_static_or_irreg32 <= USED 0
c_static_or_irreg8 <= USED 0
c_zero_or_irreg16 <= USED 0
c_zero_or_irreg32 <= USED 0
dscp_encode <= USED 0
rsf_index_enc <= USED 0
rsf_index_enc_possible <= USED 0
tcp_is_ack_scaled_possible <= USED 0
tcp_is_ack_stride_static <= USED 0
variable_length_32_enc <= USED 0
;FILE comp_scaled_rtp_ts.o
;FILE comp_wlsb.o
wlsb_ack <= USED 0
wlsb_get_k_8bits <= USED 0
wlsb_get_kp_16bits <= USED 0
wlsb_get_kp_8bits <= USED 0
wlsb_is_kp_possible_16bits <= USED 0
wlsb_is_kp_possible_32bits <= USED 0
wlsb_is_kp_possible_8bits <= USED 0
;FILE configReader.o
;FILE connect_management.o
CM_ConfigAuthInfo <= USED 0
CM_GetDataOnRoaming <= USED 0
CM_GetDefaultApnName <= USED 0
CM_GetDefaultConnectStatus <= USED 0
CM_GetRoamingStatus <= USED 0
CM_Get_ConnectionNum <= USED 0
CM_Get_LinkStatus <= USED 0
CM_RegisterIpChangedCb <= USED 0
CM_SetConnectionSwitch <= USED 0
CM_SetDataOnRoaming <= USED 0
CM_Set_ConnectType_WEBUI <= USED 0
CM_clear_first_dial_flag <= USED 0
getLwipNetifDns <= USED 0
getLwipNetifRef <= USED 0
getRadioPowerFunc <= USED 0
initDualSimType <= USED 0
lwipNetifExist <= USED 0
resetDefaultCidReplaceFlag <= USED 0
setDualSimType <= USED 0
setLwipNetifDns <= USED 0
setRadioPowerFunc <= USED 0
updateApnInfoAuthType2Str <= USED 0
;FILE copy_set_operations.o
;FILE cpdc_sig.o
CpdcpSendConfigReq <= USED 0
CpdcpSendReleaseReq <= USED 0
CpdcpSendRelocReq <= USED 0
;FILE crane_ds_mpu.o
;FILE crc.o
compute_crc_ctrl_fields <= USED 0
;FILE crossPlatformSW.o
FAT_Format <= USED 0
FAT_GetCurrentDir <= USED 0
FAT_GetFSInfo <= USED 0
FAT_GetFileSize <= USED 0
FAT_IsFormatted <= USED 0
FAT_MakeDir_M <= USED 0
FAT_RemoveDir <= USED 0
FAT_SetCurrentDir <= USED 0
FAT_eof <= USED 0
FAT_fmount <= USED 0
FAT_fread <= USED 0
FAT_fseek <= USED 0
FAT_rename <= USED 0
FDI_GetUsedSpaceSize <= USED 0
FDI_IsFormatted <= USED 0
FDI_dir_test <= USED 0
FDI_eof <= USED 0
FDI_fclose_test <= USED 0
FDI_fopen_test <= USED 0
FDI_fread_test <= USED 0
FDI_fseek_test <= USED 0
FDI_ftell <= USED 0
FDI_fwrite_test <= USED 0
FS_RemoveIntFile <= USED 0
crossPlatformSWPhase1Init <= USED 0
crossPlatformSWPhaseAfterAllNvmInit <= USED 0
cross_flash_erase <= USED 0
cross_flash_read <= USED 0
fat32_read_write_test <= USED 0
forward_print_List <= USED 0
fs_mode_is_ramfs <= USED 0
get_list_cache_len <= USED 0
get_server_eeh_dump_data <= USED 0
get_server_eeh_dump_len <= USED 0
heap_size_is_low <= USED 0
kv_flash_set_sector_cnt <= USED 0
kv_flash_set_sector_len <= USED 0
length_List <= USED 0
nvm_tfs_feof <= USED 0
nvm_tfs_ftell <= USED 0
nvm_tfs_init_lists <= USED 0
nvm_tfs_is_small <= USED 0
nvm_tfs_read_data_blk_offset <= USED 0
nvm_tfs_time_test <= USED 0
server_eeh_dump_test <= USED 0
set_fs_mode <= USED 0
sever_eeh_dump_send_done <= USED 0
show_flush_status_eeh <= USED 0
tfs_all_sync_done_set <= USED 0
;FILE csw_mem.o
GetMMIPool <= USED 0
PhysicalToVirtualNonCached <= USED 0
SetMMIPool <= USED 0
VirtualToPhysical <= USED 0
alignFree <= USED 0
app_free <= USED 0
app_malloc <= USED 0
calloc_ex <= USED 0
dtcmHeapInit <= USED 0
extMemHeapInit <= USED 0
intMemHeapInit <= USED 0
memClean <= USED 0
memPoolX_Init <= USED 0
romAlloc <= USED 0
romFree <= USED 0
;FILE d_ip.o
;FILE d_udp.o
;FILE dataflash.o
DataFlash_GetFlashType <= USED 0
DataFlash_Init <= USED 0
NAND_DirectReadSctor <= USED 0
NAND_ProgramPageDMA <= USED 0
NAND_ProgramSectorSpare <= USED 0
NAND_ReadPageDMA <= USED 0
NAND_ReadSectorSpare <= USED 0
;FILE dec.o
Decode3GPP2SMSToText <= USED 0
SmsUtils_Decode3gpp2SMS <= USED 0
SmsUtils_IsAdminSms <= USED 0
decode_data <= USED 0
decode_oa <= USED 0
;FILE decomp_ip_id_offset.o
;FILE decomp_list.o
;FILE decomp_list_ipv6.o
;FILE decomp_scaled_rtp_ts.o
;FILE decomp_wlsb.o
rohc_lsb_is_ready <= USED 0
;FILE def.o
lwip_itoa <= USED 0
lwip_stricmp <= USED 0
lwip_strnstr <= USED 0
;FILE des.o
mbedtls_des3_free <= USED 0
mbedtls_des3_init <= USED 0
mbedtls_des3_set2key_dec <= USED 0
mbedtls_des3_set2key_enc <= USED 0
mbedtls_des_crypt_cbc <= USED 0
mbedtls_des_crypt_ecb <= USED 0
mbedtls_des_free <= USED 0
mbedtls_des_init <= USED 0
mbedtls_des_key_check_key_parity <= USED 0
mbedtls_des_key_check_weak <= USED 0
mbedtls_des_key_set_parity <= USED 0
mbedtls_des_setkey_dec <= USED 0
mbedtls_des_setkey_enc <= USED 0
;FILE dev_api.o
DEV_GetCurrCfunValue <= USED 0
DEV_GetEngineModeInfo <= USED 0
DEV_GetRsrpReq <= USED 0
DEV_GetRsrqReq <= USED 0
DEV_GetRssiReq <= USED 0
DEV_GetSinrReq <= USED 0
DEV_SetGsmModeTxRxReq <= USED 0
GetCfunValue <= USED 0
GlbGetFuncConf <= USED 0
GlbSetFuncConf <= USED 0
SetCfunValue <= USED 0
SysSleepProcessSetFunc <= USED 0
resetDevParas <= USED 0
sim_libConvertNumToHexString <= USED 0
wait_cfun_done <= USED 0
;FILE dhcp.o
dhcp_cleanup <= USED 0
dhcp_inform <= USED 0
dhcp_network_changed <= USED 0
dhcp_set_struct <= USED 0
;FILE dhcp6.o
dhcp6_client_tmr <= USED 0
dhcp6_reply_opt_dns <= USED 0
dhcp6_reply_opt_domain <= USED 0
dhcp6_reply_opt_xid <= USED 0
;FILE dhcp6d.o
dhcp6d_router_dns6 <= USED 0
dhcpv6_dump_packet <= USED 0
;FILE dhcp_cntrl.o
dhcp_HdlInit <= USED 0
dhcp_HdlShutdown <= USED 0
frame_generic_packet <= USED 0
;FILE dhcp_interface.o
;FILE dhcpd.o
dhcpd_check_dongle_dns1 <= USED 0
dhcpd_check_dongle_dns2 <= USED 0
dhcpd_check_dongle_gw <= USED 0
dhcpd_check_dongle_ip <= USED 0
dhcpd_check_dongle_mask <= USED 0
dhcpd_get_opt43_flag <= USED 0
dhcpd_release_manual <= USED 0
dhcpd_set_opt43_flag <= USED 0
;FILE di2stub.o
di_bufferCreated <= USED 0
di_bufferDestroyed <= USED 0
di_displayLine <= USED 0
di_getInfo <= USED 0
di_getPixel <= USED 0
di_invalidateRect <= USED 0
di_powerDown <= USED 0
di_reset <= USED 0
di_setContrast <= USED 0
di_setPixel <= USED 0
di_updateDisplay <= USED 0
;FILE diagDB.o
__trace_APLP__AM__AM_SET_WCDMA_CALLED <= USED 0
__trace_APLP__AM__AM_SET_WCDMA_TERMINATE_CNF_CALLED <= USED 0
__trace_APLP__AM__plAML1CapabilityReportReq_Called <= USED 0
__trace_APLP__AM__plAMSetGsm <= USED 0
__trace_APLP__AM__plAMSetGsmCnf1 <= USED 0
__trace_APLP__AM__plAMSetGsmCnf1_jbcao <= USED 0
__trace_APLP__AM__plAMTerminateGsm_jbcao <= USED 0
__trace_APLP__AM__plAMsendSetSysDet2otherSim <= USED 0
__trace_APLP__AM__plAMsendSetSysDet_JJ <= USED 0
__trace_APLP__AM__plAMsendSetSysDet_P <= USED 0
__trace_APLP__AM__plAmGetRatSetCause <= USED 0
__trace_APLP__AM__plAmGetSharedOpcode <= USED 0
__trace_APLP__AM__plAmPrintTempAndVbatReading <= USED 0
__trace_APLP__AM__plAmReadComCfgValues_ILIGAL_HANDLE <= USED 0
__trace_APLP__AM__plAmReadComCfgValues_VALUE_INVALID <= USED 0
__trace_APLP__AM__plAmSendPldGsmCnf <= USED 0
__trace_APLP__AM__pldSetGsmCnfAtBcch1 <= USED 0
__trace_APLP__AM__pldSetGsmCnfAtBcch1_StopSendingDeactTaskFlag <= USED 0
__trace_APLP__AM__pldSetGsmCnfAtBcch2 <= USED 0
__trace_APLP__AM__pldSetGsmCnfAtBcchNew1 <= USED 0
__trace_APLP__INIT__SET_INITAL_DATA_RESET <= USED 0
__trace_APLP__MSR__pldSetGsmCnfAtBcch_Err <= USED 0
__trace_AUDIO__ACM_STREAM__ACMAudioStreamOutStart_0 <= USED 0
__trace_AUDIO__ACM_STREAM__ACMAudioStreamOutStart_1 <= USED 0
__trace_AUDIO__ACM_STREAM__ACMAudioStreamOutStop <= USED 0
__trace_AUDIO__ACM_STREAM__ACMAudioStreamOutStop_1 <= USED 0
__trace_AUDIO__ACM__ACMAudio_GetDSPSettings <= USED 0
__trace_AUDIO__ACM__ACMAudio_GetDSPSettings_Dump <= USED 0
__trace_AUDIO__ACM__ACMUpdateAudioNVM <= USED 0
__trace_AUDIO__ACM__ACMUpdateAudioNVMString <= USED 0
__trace_AUDIO__ACM__ACMUpdateAudioNVMString_error <= USED 0
__trace_AUDIO__ACM__GSSPRead_WrongCookie <= USED 0
__trace_AUDIO__ACM__GSSP_SEL_BY_CP_dump <= USED 0
__trace_AUDIO__ACM__WriteAudioNVM <= USED 0
__trace_AUDIO__ACM__getItem_error <= USED 0
__trace_AUDIO__ATC__ATC_Process_PCM_data_0 <= USED 0
__trace_AUDIO__ATC__ATC_Process_PCM_data_2 <= USED 0
__trace_AUDIO__ATC__ReceivePCMFromAP <= USED 0
__trace_AUDIO__AUDIO_PATH__acm_audio_effect_init <= USED 0
__trace_AUDIO__CODEC812__pm812_audio_init <= USED 0
__trace_AUDIO__HAL__AUDIOHAL_GetBufferSize_error <= USED 0
__trace_AUDIO__HAL__AifBindHeadsetDetectionCB <= USED 0
__trace_AUDIO__HAL__AifConfigure <= USED 0
__trace_AUDIO__HAL__AifHeadsetDetection <= USED 0
__trace_AUDIO__HAL__AifSetSideTone <= USED 0
__trace_AUDIO__HAL__AifSetSideTone1 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Err1 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Err2 <= USED 0
__trace_AUDIO__HAL__AifSetSideTone_Fixed <= USED 0
__trace_AUDIO__HAL__AifTonePause <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBindCodec_CB <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifBindSpeakerPA_CB <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetTxCodecGainStage1 <= USED 0
__trace_AUDIO__HAL__AudioHAL_AifGetTxCodecGainStage2 <= USED 0
__trace_AUDIO__HAL__AudioHAL_AudioHAL_sspaSetDelay_error <= USED 0
__trace_AUDIO__HAL__AudioHAL_FadingCtl <= USED 0
__trace_AUDIO__HAL__AudioHAL_sspaSetDelay <= USED 0
__trace_AUDIO__HAL__PCMControl <= USED 0
__trace_AUDIO__HAL__PCMControlErr <= USED 0
__trace_AUDIO__HAL__UpdateShadowRegisters <= USED 0
__trace_AUDIO__HAL__UpdateShadowRegistersErr <= USED 0
__trace_AUDIO__HAL__ningbo_power_i2c_read_error <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_BufstatsDecision_0 <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_BufstatsDecision_1 <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_BufstatsDecision_CngUpdateMissed <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_PacketBufferExtract_0 <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_PacketBufferFindLowestTimestamp_0 <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NewCodecOrReinit <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NewCodecWorkaround <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_NoPacketFoundButRequired <= USED 0
__trace_AUDIO__NETEQ__WebRtcNetEQ_SignalMcu_SamplesOverflowCheck <= USED 0
__trace_AUDIO__NETEQ__WorkaroundForAquila1826 <= USED 0
__trace_AUDIO__PCA_API__RegisterCallback <= USED 0
__trace_AUDIO__PCA_API__SetStreamPriority <= USED 0
__trace_AUDIO__RAT__audioBindGetRAT <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_control <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_get_softgain <= USED 0
__trace_AUDIO__RECORD__acm_audio_record_set_softgain <= USED 0
__trace_AUDIO__StubServer__audioDownLink20msCB_from_PS <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_close <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error_play <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_open_error_record <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_read_return <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start_play <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_start_record <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_stop <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write_1 <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write_return <= USED 0
__trace_AUDIO__TG_PCM__tg_pcm_write_wait <= USED 0
__trace_AUDIO__TG_PCM__tg_volume_set <= USED 0
__trace_AUDIO__TRACK__acm_audio_track_get_softgain <= USED 0
__trace_AUDIO__Voice__PcmStreamRecordInvalidBuffer <= USED 0
__trace_AUDIO__Voice__ReceivePCMFromDSP <= USED 0
__trace_AUDIO__Voice__SendPCMToDSP <= USED 0
__trace_AUDIO__Voice__SetDebugCmdErr <= USED 0
__trace_AUDIO__Voice__SetVoiceEnhanceModuleControl <= USED 0
__trace_AUDIO__Voice__handlePcmStreamRecordMsg <= USED 0
__trace_AUDIO__Voice__vpathCTMControl <= USED 0
__trace_AUDIO__Voice__vpathCTMControl_0 <= USED 0
__trace_AUDIO__Voice__vpathDebugCmd <= USED 0
__trace_AUDIO__Voice__vpathDmaIntInd <= USED 0
__trace_AUDIO__Voice__vpathGSSPControl <= USED 0
__trace_AUDIO__Voice__vpathGSSPRead <= USED 0
__trace_AUDIO__Voice__vpathRxbControl <= USED 0
__trace_AUDIO__Voice__vpathSetSelfInvocation <= USED 0
__trace_AUDIO__Voice__vpathVoiceControl <= USED 0
__trace_CIModem__DLC_DISC__Deactive_Arg_error <= USED 0
__trace_CIModem__DLC_DISC__Deactive_req_error <= USED 0
__trace_CIModem__DLC_DISC__PSDATA_RUN <= USED 0
__trace_CIModem__RecTask__linkstatusind <= USED 0
__trace_CIModem__RecTask__linkstatusind_abnormal <= USED 0
__trace_CRD__RDP__RDPDiagCpyPeriodicDataPart1_called <= USED 0
__trace_CRD__RDP__RDPDiagCpyPeriodicDataPart2_called <= USED 0
__trace_CRD__RDP__RDPDiagCpyPeriodicDataSize_called <= USED 0
__trace_CRD__RDP__RDPDiagCpyPeriodicData_called <= USED 0
__trace_CRD__RDP__RDPDiagCpyPeriodicDividedDataSizePart1_called <= USED 0
__trace_CRD__RDP__RDPDiagCpyPeriodicDividedDataSizePart2_called <= USED 0
__trace_CRD__RDP__RDPRegisterAckHandle1 <= USED 0
__trace_CRD__RDP__RDPRegisterAckHandle2 <= USED 0
__trace_CRD__RDP__RDP_MSG_HANDLE_ID_ERR <= USED 0
__trace_CRD__RDP__RFD_IPC_REG_ERROR <= USED 0
__trace_ControlLoop_TEST_EM__TimeTrackingAfcTest__ControlLoopEndOfRapidSwitchSimulation <= USED 0
__trace_ControlLoop_TEST_EM__TimeTrackingAfcTest__ControlLoopMoveToIDLE <= USED 0
__trace_ControlLoop_TEST_EM__TimeTrackingAfcTest__ControlLoopMoveToPTM <= USED 0
__trace_DRAT__DVFM__DVFMChangePP_platformStub <= USED 0
__trace_DRAT__DVFM__DVFMChangePP_platformStub_returnVal <= USED 0
__trace_DRAT__DVFM__callPendingReqOnDown <= USED 0
__trace_DRAT__DVFM__callPendingReqOnUp <= USED 0
__trace_DRAT__DVFM__callPendingReqOnUp2 <= USED 0
__trace_DRAT__DVFM__handleMsReqFromBase_increase_platReturnValueLower <= USED 0
__trace_DRAT__GSM_AM__plAMTerminateGsm_input <= USED 0
__trace_DRAT__pl_am_d_dflt__plAMsendSetSysDet2otherSim_1 <= USED 0
__trace_Diag__Port__USBTxDoneInd <= USED 0
__trace_Diag__Utils__setAPVersion_log <= USED 0
__trace_FDI__FDI5to6__FDI_feof <= USED 0
__trace_FDI__fatsys__F_Makedir <= USED 0
__trace_FDI__fatsys__F_Makedir_succeed <= USED 0
__trace_FDI__fatsys__F_eof_start <= USED 0
__trace_FDI__fatsys__F_eof_succeed <= USED 0
__trace_FDI__fatsys__F_readEx_error <= USED 0
__trace_FDI__fatsys__F_tell_start <= USED 0
__trace_FDI__fatsys__F_tell_succeed <= USED 0
__trace_FDI__fatsys__F_writeEx_error <= USED 0
__trace_FDI__fatsys__F_writeEx_start <= USED 0
__trace_FDI__fatsys__F_writeEx_succeed <= USED 0
__trace_FOTA__FOTACOMM__ui_fota_bind <= USED 0
__trace_FOTA__FOTAINTERFACE__ui_notify_curr_download_percent <= USED 0
__trace_FOTA__FOTAINTERFACE__ui_notify_restart <= USED 0
__trace_FOTA__FOTAINTERFACE__ui_notify_result <= USED 0
__trace_FOTA__FOTAINTERFACE__ui_notify_version <= USED 0
__trace_GPLC_APIS__GPLC_APIS__rxFastCalibStepIndnum <= USED 0
__trace_GPLC_APIS__GPRS_GMPH_CNF__plgGmphUlMbConfigCnf <= USED 0
__trace_GPLC_APIS__GPRS_GMPH_IND__plgGmphEngModeGprsEdgeLinkQualInd <= USED 0
__trace_GPLC_APIS__GSM_MPH_IND__plgMphEngModeDedSrvCellInfoInd <= USED 0
__trace_GPLC_APIS__GSM_MPH_REQ__plgMphBcchSyncReqReport <= USED 0
__trace_GPLC_COMM__AFC__CfConvertAfcDacToPpb <= USED 0
__trace_GPLC_COMM__AFC__CfSetDcxoTempCompenVal <= USED 0
__trace_GPLC_COMM__AFC__CfSetDcxoTempCompenVal_Fail <= USED 0
__trace_GPLC_COMM__PTM__TX_PAYLOAD_CS_USED2 <= USED 0
__trace_GPLC_COMM__TERMINATE_GSM__SEND_DEACTIVATE_FROM_TERMINATE_3 <= USED 0
__trace_GPLC_DUAL_G_T__BCCH_DECODE_IN_IRAT_REQ__L1BgBcchDecodeInIratProcessTest_1 <= USED 0
__trace_GPLC_DUAL_G_T__BCCH_DECODE_IN_IRAT_REQ__L1BgBcchDecodeInIratProcessTest_2 <= USED 0
__trace_GPLC_DUAL_G_T__BCCH_DECODE_IN_IRAT_REQ__L1BgBcchDecodeInIratProcessTest_3 <= USED 0
__trace_GPLC_DUAL_G_T__BCCH_DECODE_IN_TD_REQ__L1BgBcchDecodeInTdProcessTest_21 <= USED 0
__trace_GPLC_DUAL_G_T__LATCH_TDMA_RESULT__ProcessGsmLatchTdmaAck2 <= USED 0
__trace_GPLC_DUAL_G_T__LATCH_TDMA_RESULT__ProcessGsmLatchTdmaAck3 <= USED 0
__trace_GPLC_DUAL_G_T__LATCH_TDMA_RESULT__ProcessGsmLatchTdmaAck4 <= USED 0
__trace_GPLC_DUAL_G_W__ENH_MEAS__L1BgClearTcbAfterG2WHoSuccess <= USED 0
__trace_GPLC_DUAL_G_W__HIGH_PRIO_WB_MEAS__DeleteIdleHighPrioWbMeasSequencer <= USED 0
__trace_GPLC_DUAL_G_W__IDLE__L1BgProcessTcbRebuildSuspended <= USED 0
__trace_GPLC_DUAL_G_W__WB_PI__DeleteIdleWbPiSequencer <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx168 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx178 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx198 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx199 <= USED 0
__trace_GPLC_DUAL_SIM__IDLE__StartIdleMode_jjx200 <= USED 0
__trace_GPLC_DUAL_SIM__L1FRIDLE__L1FrResumeOneSimTcb <= USED 0
__trace_GPLC_DUAL_SIM__L1FRIDLE__L1FrSuspendOneSimTcb <= USED 0
__trace_GPLC_DUAL_T_G__SCELL_BCCH_IN_GAP__DeleteLteIdleRssiScanInGapSequencer_1 <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__DEACTIVATE_DUE_TO_UPDATE <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__IN_START_LIST <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__L1BgActionsUponMultiBcchDeactivation_1 <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__L1BgBcchForGsmRestoreCellInfoPerArfcn_1 <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__L1BgSaveCellInfoForGsmMultiBcchList_1 <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__L1BgSaveCellInfoForGsmMultiBcchList_2 <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__L1IsWaitingAnchorSych <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__SetUtevStartBcchList_0 <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__SetUtevStartBcchList_1 <= USED 0
__trace_GPLC_DUAL__BCCH_LIST__UPDATE_ABORT_ARFCN_BCCH <= USED 0
__trace_GPLC_DUAL__WB_BCH_DECODE__CHECK_ANY_EVENT <= USED 0
__trace_GPLC_DUAL__WB_BCH_DECODE__L1FrLdtUtranBchRemove <= USED 0
__trace_GPLC_DUAL__WB_BCH_LIST_DECODE__L1CellInfoAddSortIndex <= USED 0
__trace_GPLC_DUAL__WB_CELL_MEAS__L1FrLdRemoveFromListFound <= USED 0
__trace_GPLC_DUAL__WB_MEAS__DeleteUtranDedCchCellMeasSequencer <= USED 0
__trace_GPLC_DUAL__WB_MEAS__DeleteUtranDedCchRssiMeasSequencer <= USED 0
__trace_GPLC_DUAL__WB_MEAS__DeleteUtranDedTchCellMeasSequencer <= USED 0
__trace_GPLC_DUAL__WB_MEAS__DeleteUtranDedTchRssiMeasSequencer <= USED 0
__trace_GPLC_DUAL__WB_MEAS__DeleteUtranIdleCellMeasSequencer <= USED 0
__trace_GPLC_DUAL__WB_MEAS__DeleteUtranIdleRssiMeasSequencer <= USED 0
__trace_GPLC_DUAL__WB_MEAS__L1BgUtranMsrResetPeriodicMsr <= USED 0
__trace_GPLC_DUAL__WB_MEAS__L1FrWbMeasSetNextRssiUarfcnMeas <= USED 0
__trace_GPLC_DUAL__WB_MEAS__L1FrWbMeasUpdateIndicationOnRssiResults <= USED 0
__trace_GPLC_DUAL__WB_MEAS__SFS_UTRAN_MEAS_BEGIN <= USED 0
__trace_GPLC_DUAL__WB_MEAS__SFS_UTRAN_MEAS_FINISH <= USED 0
__trace_GPLC_DUAL__WB_MEAS__SqFbsStartIdleUtranRssiLdt <= USED 0
__trace_GPLC_MULTI__LTE_PLMN__L1FrIdle_NclLteStartCellPlmn <= USED 0
__trace_GPLC_PLAT__POWER__DEBUG_SLEEP_ENTRY <= USED 0
__trace_GPLC_RF__CALIBRATION__L1FrTestRxFastCalibCalcAfcParams <= USED 0
__trace_GPLC_RF__CALIBRATION__L1FrTestRxFastCalibCalcAfcParams_BEFORE <= USED 0
__trace_GPLC_RF__CALIBRATION__L1FrTestRxFastCalibCalcAfcParams_ERROR <= USED 0
__trace_GPLC_RF__CALIBRATION__NVM_WRITE_FAILURE_CfOverwriteRfCalData_NEW <= USED 0
__trace_GPLC_RF__CALIBRATION__NVM_WRITE_FAILURE_CfOverwriteRfCalData_OLD <= USED 0
__trace_GPLC_RF__CALIBRATION__NVM_WRITE_SUCCESS_CfOverwriteRfCalData_NEW <= USED 0
__trace_GPLC_RF__CALIBRATION__NVM_WRITE_SUCCESS_CfOverwriteRfCalData_OLD <= USED 0
__trace_GPLC_RF__CALIBRATION__RSSI_CORR <= USED 0
__trace_GPLC_RF__CALIBRATION__SendGsmAfcParameterToTdL1_2 <= USED 0
__trace_GPLC_RF__DcxoCalibration__DcxoCalibration_CreateNVMFile_Fail <= USED 0
__trace_GPLC_RF__RF_INIT__DCXO_CALIBRATION_FILE_CREAT_OK <= USED 0
__trace_GPLC_RF__rf__SendAFCParametersToMSA <= USED 0
__trace_GPLC_SYS__ERROR__L1KI_TRACE <= USED 0
__trace_GPLC_SYS__INIT__MULTISLOT_POWER_PROFILE_DISABLED_INIT <= USED 0
__trace_GPLC_SYS__VERSION__RELEASE_COMMENTS <= USED 0
__trace_GPLC_SYS__VERSION__RELEASE_DATE <= USED 0
__trace_GPLC_SYS__VERSION__RELEASE_FULL_NAME <= USED 0
__trace_GPLC_TEST__AFC__END_SIMULATION <= USED 0
__trace_GPLC_TEST__AFC__SHIFT_FREQ_BACKWARDS <= USED 0
__trace_GPLC_TEST__AFC__SHIFT_FREQ_FORWARD <= USED 0
__trace_GPLC_TEST__DSDT__L1BgDisableSIMAFakeScellRSSIReport <= USED 0
__trace_GPLC_TEST__NSC__BCH_CONFIG <= USED 0
__trace_GPLC_TEST__NSC__NCELL_MEAS_REQ <= USED 0
__trace_GPLC_TEST__NSC__NscGsmSaveMeasResults <= USED 0
__trace_GPLC_TEST__NSC__TI_TCH_REQ <= USED 0
__trace_GPLC_TEST__TT__TT_END_SIMULATION <= USED 0
__trace_GPLC_TEST__TT__TT_MOVE_SORTER_FWD <= USED 0
__trace_GPLC_TEST__TT__TT_TEST <= USED 0
__trace_GPLC__ACT_MTRX__L1AmDebugCurrentActivityMatrix <= USED 0
__trace_GPLC__CSFB_OPT__L1BgStartSyncSearch <= USED 0
__trace_GPLC__GTCU_SPY__GtcuSpyInit <= USED 0
__trace_GPLC__GW__gwiSendGsmTerminateCnf_stub <= USED 0
__trace_GPLC__L1BgDeactivateSecondaryOtherSimFrameIsr__IgnoredSignal <= USED 0
__trace_GPLC__L1_FR_CELL_LOCK__UmphDetectedCellMeasInd_2G_3G <= USED 0
__trace_GPLC__L1_FR_CELL_LOCK__UmphDetectedCellMeasInd_WrongState <= USED 0
__trace_GPLC__L1_FR_CELL_LOCK__UmphRssiMeasInd_2G_3G <= USED 0
__trace_GPLC__L1_FR_CELL_LOCK__UmphRssiScanInd_2G_3G <= USED 0
__trace_GPLC__L1_FR_CELL_LOCK__UmphRssiScanInd_WrongState <= USED 0
__trace_GPLC__L1_FR_CELL_LOCK__UmphRssimeasInd_WrongState <= USED 0
__trace_GPLC__L1c__GsmStatusIsIdleOrDedicatedFlg <= USED 0
__trace_GPLC__L1c__GsmStatusIsTestFlg <= USED 0
__trace_GPLC__MONITOR__SetLowestRssiThreshold <= USED 0
__trace_GPLC__NCELL_PACKET_DEBUG__L1BgMeasurementComplete <= USED 0
__trace_GPLC__RF__Set23GPmaxReductionFlag2 <= USED 0
__trace_GPLC__SET_GSM_REQ__L1BgSetGsmReq <= USED 0
__trace_GPLC__SET_GSM_REQ__wgiBgSendRdaSetGsmCnf <= USED 0
__trace_GPLC__wgiResetUtranMeas1__plMsrGsWbReset2 <= USED 0
__trace_HAL__IPC__DATA_ALLOC_ERROR_DSP <= USED 0
__trace_HAL__PM812__PM812_LDO11_2V8 <= USED 0
__trace_HAL__PM812__PM812_LDO12_3V0 <= USED 0
__trace_HAL__PM812__PM812_LDO15_2V8 <= USED 0
__trace_HAL__PM812__PM812_LDO16_2V8 <= USED 0
__trace_HAL__PM812__PM812_LDO17_2V8 <= USED 0
__trace_HAL__PM812__PM812_LDO18_1V8 <= USED 0
__trace_HAL__PM812__PM812_LDO19_1V8 <= USED 0
__trace_HAL__PM812__PM812_LDO5_3V1 <= USED 0
__trace_HAL__PM812__PM812_LDO6_1V8 <= USED 0
__trace_HAL__PM812__PM812_LDO6_2V8 <= USED 0
__trace_HAL__PM812__PM812_LDO8_2V8 <= USED 0
__trace_HAL__PM812__PM812_LDO9_1V8 <= USED 0
__trace_HAL__PM812__PM812_REG_DUMP <= USED 0
__trace_HAL__PM813__PM813_LDO10_2V8 <= USED 0
__trace_HAL__PM813__PM813_LDO11_2V8 <= USED 0
__trace_HAL__PM813__PM813_LDO12_1V8 <= USED 0
__trace_HAL__PM813__PM813_LDO12_2V8 <= USED 0
__trace_HAL__PM813__PM813_LDO13_1V8 <= USED 0
__trace_HAL__PM813__PM813_LDO13_3V1 <= USED 0
__trace_HAL__PM813__PM813_LDO4_1V8 <= USED 0
__trace_HAL__PM813__PM813_LDO4_3V0 <= USED 0
__trace_HAL__PM813__PM813_LDO8_2V8 <= USED 0
__trace_HAL__PM813__PM813_LDO9_3V3 <= USED 0
__trace_HAL__USIM_DL__USIM_DL_T1decode <= USED 0
__trace_HAL__USIM_DL__USIM_DL_T1decode1 <= USED 0
__trace_HAL__USIM_DL__USIM_DL_decodeIBlock <= USED 0
__trace_HAL__USIM_DL__USIM_DL_decodeSBlock <= USED 0
__trace_HAL__USIM_DL__USIM_DLrequest2 <= USED 0
__trace_HAL__USIM__USIMVersionGet <= USED 0
__trace_HAL__USIM__USIM_Clck_Check <= USED 0
__trace_HAL__USIM__USIM_Vcc_Deactivate_Fast <= USED 0
__trace_IMS__IMS_STUB__ACIPCDImsRxdefault <= USED 0
__trace_IMS__IMS_STUB__ACIPCDImsTxdefault <= USED 0
__trace_IMS__SetStChannel__ENTER_IN <= USED 0
__trace_L1A__L1ACAL__L1aHandleAdditionalMprTableData_1 <= USED 0
__trace_L1A__L1ACAL__L1aHandleAdditionalMprTableData_failOpen <= USED 0
__trace_L1A__L1ACAL__L1aHandleDcxoCalData_1 <= USED 0
__trace_L1A__L1ACAL__L1aHandleDcxoCalData_failOpen <= USED 0
__trace_L1A__L1ACAL__L1aHandleDigRf4BusLoopbackTestResultAck_2 <= USED 0
__trace_L1A__L1ACAL__L1aHandleEcphyL1AssertDebugInd_enter <= USED 0
__trace_L1A__L1ACAL__L1aHandleLteQtEtFreqAdjTableData_1 <= USED 0
__trace_L1A__L1ACAL__L1aHandleLteQtEtFreqAdjTableData_failOpen <= USED 0
__trace_L1A__L1ACAL__L1aHandlePowerDetectorResultAck_2_1 <= USED 0
__trace_L1A__L1ACAL__L1aHandleRfConfigTableData_1 <= USED 0
__trace_L1A__L1ACAL__L1aHandleRfConfigTableData_failOpen <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_10 <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_11 <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_12 <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_13 <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_14 <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_enter <= USED 0
__trace_L1A__L1ACAL__L1aSendDefaultTableWithZero_remainLength <= USED 0
__trace_L1A__L1ACAL__L1aTempReadingCallback_1 <= USED 0
__trace_L1A__L1ACAL__L1aTempReadingCallback_2 <= USED 0
__trace_L1A__L1ACAL__getTxPowerBackoffMode_00 <= USED 0
__trace_L1A__MULTI_IRAT_COMMON__multiIratL1GetRAT_2 <= USED 0
__trace_L1A__l1a_drat__L1aSetSimWbActivated_SIM1 <= USED 0
__trace_L1A__l1a_drat__L1aSetSimWbActivated_SIM2 <= USED 0
__trace_L1A__l1a_dsds__L1aSendSchdGsmGapAbortReq_enter <= USED 0
__trace_L1A__l1a_dsds__gliDsdsGsmUrgentReq_enter <= USED 0
__trace_L1A__l1a_dsds__gliIratcurrentProcedure_end <= USED 0
__trace_L1A__l1a_dsds__gliIratcurrentProcedure_enter <= USED 0
__trace_L1A__l1a_dsds__wliDsdsLteIsSleepState_enter <= USED 0
__trace_L1A__l1a_dsds__wliDsdsRcvLtePchInWbPsReq_entry <= USED 0
__trace_L1A__l1a_dsds__wliDsdsResumeLteReq_entry <= USED 0
__trace_L1A__l1a_dsds__wliDsdsSchdLteGapInd_enter <= USED 0
__trace_L1A__l1a_dsds__wliDsdsSchdWbGapCanCelInd_entry <= USED 0
__trace_L1A__l1a_dsds__wliDsdsSchdWbGapFinishedInd_entry <= USED 0
__trace_L1A__l1a_dsds__wliDsdsSchdWbGapReq_entry <= USED 0
__trace_L1A__l1a_dsds__wliDsdsSchdWbGapReq_pending <= USED 0
__trace_L1A__l1a_dsds__wliDsdsSuspendLteReq_entry <= USED 0
__trace_L1A__l1a_dsds__wliDsdsWbActivateInd_IPC <= USED 0
__trace_L1A__l1a_dsds__wliDsdsWbActivateInd_end <= USED 0
__trace_L1A__l1a_dsds__wliDsdsWbActivateInd_enter <= USED 0
__trace_L1A__l1a_dsds__wliDsdsWbDeactivateInd_enter <= USED 0
__trace_L1A__l1a_dsds__wliDsdsWbEarlyWakeUpReq_entry <= USED 0
__trace_L1A__l1a_dsds__wliDsdsWbSleepReq_enter <= USED 0
__trace_L1A__l1a_irat__L1aHandleSetLteCnfDuringHoToFddUtraFail_entry <= USED 0
__trace_L1A__l1a_irat__L1aHandleSetLteCnfDuringHoToTddUtraFail_entry <= USED 0
__trace_L1A__l1a_irat__L1aHandleTddUtraNcellMeasInd_entry <= USED 0
__trace_L1A__l1a_irat__L1aStoppingTddUtraBchReading_entry <= USED 0
__trace_L1A__l1a_irat__L1aSwapLteMeasInGsm1 <= USED 0
__trace_L1A__l1a_irat__gsmBsicMeasInfoIsPlmn1 <= USED 0
__trace_L1A__l1a_irat__teiLteNcellBchReq_entry <= USED 0
__trace_L1A__l1a_irat__teiLteNcellBchStopReq_entry <= USED 0
__trace_L1A__l1a_irat__teiLteTdNcellBchStopCnf_entry <= USED 0
__trace_L1A__l1a_irat__teiReselectToLteCnf_entry <= USED 0
__trace_L1A__l1a_irat__teiTdNcellBchInd_entry <= USED 0
__trace_L1A__l1a_irat__teiTdNcellBchStopCnf_entry <= USED 0
__trace_L1A__l1a_irat__weiFddNcellBchInd_entry <= USED 0
__trace_L1A__l1a_irat__weiFddNcellBchStopCnf_entry <= USED 0
__trace_L1A__l1a_irat__weiHandoverToLteInd_entry <= USED 0
__trace_L1A__l1a_irat__weiLteNcellBchReq_entry <= USED 0
__trace_L1A__l1a_irat__weiLteNcellBchStopReq_entry <= USED 0
__trace_L1A__l1a_irat__weiReselectToLteCnf_entry <= USED 0
__trace_L1A__l1a_irat__weiReselectToUtraFddReq_entry <= USED 0
__trace_L1A__l1a_irat__weiSwitchRatToLteCnf_entry <= USED 0
__trace_L1A__l1a_irat__weiSwitchRatToUmtsCnf_entry <= USED 0
__trace_L1A__l1a_phy__L1aCheckIfNeedStopGsmDrxScanning1 <= USED 0
__trace_L1A__l1a_phy__L1aCheckIfNeedStopTdDrxScanning1 <= USED 0
__trace_L1A__l1a_phy__L1aCheckIfNeedStopWbDrxScanning1 <= USED 0
__trace_L1A__l1a_phy__L1aHandleEcphyFindCellReqCheckIfNeedSuspend_1 <= USED 0
__trace_LTE_PS__ERRC_BAND__ErrcDsIsErrcSuspended_entry <= USED 0
__trace_LTE_PS__ERRC_BAND__LteRrcCsrCheckIfNcellOnIntendedBand_err1 <= USED 0
__trace_LTE_PS__ERRC_BAND__LteRrcCsrCheckIfNcellOnIntendedBand_err2 <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_check <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_head <= USED 0
__trace_LTE_PS__ERRC_CELL__LteRrcCellMgrDebugAssert466_skip <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerConstructAndSendEutraProximityInd_earfcnErr <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerGetRplmn_false <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerHandleT301Expiry_1 <= USED 0
__trace_LTE_PS__ERRC_CER__LteCerHandleT301Expiry_2 <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcBuildConnEstNcellMeasResultGeran_enter <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcBuildConnEstNcellMeasResultGeran_noCell <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcBuildConnEstNcellMeasResultUtra_enter <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcBuildConnEstNcellMeasResultUtra_noCell <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcBuildMeasResultList2UTRA_r9_CellLoopFdd <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcBuildMeasResultList2UTRA_r9_FreqLoopFdd <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcBuildMeasResultListGERAN_CellLoop <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_Cell <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_CellNum <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_Freq <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcCheckMeasResultListEUTRA_FreqNum <= USED 0
__trace_LTE_PS__ERRC_CER__LteRrcClearHandoverTimer_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrAddCurrentCellToEarfcnList_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrCheckIfDrxFindCellisNeed_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrHandlePlmnSearchNcellBchInd_Mib_crcOk <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrIsRequestPlmnListContainCMCC_no <= USED 0
__trace_LTE_PS__ERRC_CSR__LteCsrIsRequestPlmnListContainCMCC_noReq <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcCheckIfCsPagingOrImsPaging_1 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcCheckIfCsPagingOrImsPaging_2 <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcCsrCheckIfNcellOnIntendedBand_leave <= USED 0
__trace_LTE_PS__ERRC_CSR__LteRrcQueryTddConfig_error <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDLnkReselectDetermineFromGsmToEutra_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDLnkReselectDetermineFromGsmToEutra_2 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDLnkReselectDetermineFromGsmToEutra_3 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDLnkReselectDetermineFromGsmToEutra_high <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDLnkReselectDetermineFromGsmToEutra_low1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDLnkReselectDetermineFromGsmToEutra_low2 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDLnkReselectPrepareReq_all <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkCheckIfTimestampIsTimeout_end <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkCheckTreselectionAndCampOnTime_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkCheckTreselectionAndCampOnTime_2 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkCheckTreselectionAndCampOnTime_end <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkGetHPRIOParsIndB_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkGetNonEutraNcellIndex_end <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkGetParsForGsmToLteResel_defualt <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkGetThresholdGeranLowParsIndB_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkGetTreselectionParsInSec_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleGrrErrcGsmScellInfoInd_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleGrrErrcGsmScellInfoInd_resel <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_2 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_3 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_4 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_5 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_6 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_end1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleLteScellInfoInd_end2l <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandlePsLinkActResult_invalid_enum <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleUrrErrcUmtsFddScellInfoInd_resel <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkHandleUrrErrcUmtsTddScellInfoInd_resel <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkLteGetGsmReselParas_end <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToGsm_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToGsm_2 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToGsm_3 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToGsm_4 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToGsm_5 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToGsm_6 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraFdd_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraFdd_2 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraFdd_3 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraFdd_4 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraTdd_1 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraTdd_2 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraTdd_3 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraTdd_4 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkReselectDetermineFromEutraToUtraTdd_5 <= USED 0
__trace_LTE_PS__ERRC_DLNK__IratDlnkSendReselectRequire_end <= USED 0
__trace_LTE_PS__ERRC_DLNK__LteRrcCheckCardNum_1 <= USED 0
__trace_LTE_PS__ERRC_DSDS__LteRrcDsDsCheckIfNeedTriggerCellSelectionAfterResume_4 <= USED 0
__trace_LTE_PS__ERRC_DSDS__LteRrcDsdsGetSim2Activated_begin <= USED 0
__trace_LTE_PS__ERRC_ETE__LteAisEncodeUeCapabilityRatContainer_1 <= USED 0
__trace_LTE_PS__ERRC_ETE__LteAisEncodeUeCapabilityRatContainer_2 <= USED 0
__trace_LTE_PS__ERRC_ETE__LteEteSendEcphyStopEngInfoReq_notStat <= USED 0
__trace_LTE_PS__ERRC_ETE__LteRrcCopyEuCellInfoListUTRA_TDD_r10_1 <= USED 0
__trace_LTE_PS__ERRC_ETE__LteRrcSendErrcDeactReq_enter <= USED 0
__trace_LTE_PS__ERRC_IRAT_CELL__LteMcrIratVarMeasReportDelGeranCell_enter <= USED 0
__trace_LTE_PS__ERRC_IRAT_CELL__LteRrcCellMgrAddLteFreqCellFromMeasObject_enter <= USED 0
__trace_LTE_PS__ERRC_IRAT_CELL__LteRrcIratCellMgrDelAllMeasIdOnFddUtraFreqs_1 <= USED 0
__trace_LTE_PS__ERRC_IRAT_CELL__LteRrcIratCellMgrDelAllMeasIdOnFddUtraFreqs_notFind <= USED 0
__trace_LTE_PS__ERRC_IRAT_CELL__LteRrcIratCellMgrDelAllMeasIdOnGeranNcellFreqsEx_1 <= USED 0
__trace_LTE_PS__ERRC_IRAT_CELL__LteRrcIratCellMgrDelMeasOjtGeran_ObjNotFind <= USED 0
__trace_LTE_PS__ERRC_IRAT_CELL__LteRrcIratCellMgrDelMeasUtraFreq_FreqNotFind <= USED 0
__trace_LTE_PS__ERRC_IRAT_CSRP__LteCsrIratHandlePlmnListReq_wrongState <= USED 0
__trace_LTE_PS__ERRC_IRAT_CSRP__LteIratCsrpDoGsmPlmnSearch_err <= USED 0
__trace_LTE_PS__ERRC_IRAT_CSRP__LteIratCsrpHandlePlmnListReq_info <= USED 0
__trace_LTE_PS__ERRC_IRAT__LteCsrIratReselFromLteToFddUtraEvaluation_equal_Prio <= USED 0
__trace_LTE_PS__ERRC_IRAT__LteCsrIratReselFromLteToGsmEvaluation_equal_Prio <= USED 0
__trace_LTE_PS__ERRC_MCR__LteCsrSendErrcSacLocationReq_end <= USED 0
__trace_LTE_PS__ERRC_MCR__LteMcrChangeA3ConditionTimeToTrigger_changed <= USED 0
__trace_LTE_PS__ERRC_SIR__LteCsrIratDecodePlmn_error_check <= USED 0
__trace_LTE_PS__ERRC_SIR__LteCsrIratDecodePlmn_not_sib1 <= USED 0
__trace_LTE_PS__ERRC_SIR__LteSirClearVisitedCellDb_Del <= USED 0
__trace_LTE_PS__ERRC__LteRrcVarMemInit_1 <= USED 0
__trace_LTE_PS__ERRC__LteRrcVarMemInit_2 <= USED 0
__trace_LTE_PS__ERR_UTIL__LteRrcCsrGetOtherCardLteIsOos_enter <= USED 0
__trace_LTE_PS__ERR_UTIL__LteRrcDsdsSendErrcSuspendCnfWhenLeavingConnected_1 <= USED 0
__trace_LTE__BM__LteBmProcessDataAbortInd0 <= USED 0
__trace_LTE__BM__LtePDCPReleaseRxL2BBlock2t <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer0 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer000726 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer000727 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer008 <= USED 0
__trace_LTE__BM__UMTSMacAllocateL2RxBuffer09w <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer0 <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer009 <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2RxBuffer0_umts <= USED 0
__trace_LTE__BM__UMTSRlcReleaseL2buffer0001 <= USED 0
__trace_LTE__DHCP__LteUlGetEpsBearerIdFromTft1 <= USED 0
__trace_LTE__DHCP__LteUlGetEpsBearerIdFromTft2 <= USED 0
__trace_LTE__ESM__CheckPfid_2 <= USED 0
__trace_LTE__MACDL__LteMacdlHandleEphyDataIndDiscardTB <= USED 0
__trace_LTE__MACUL__LteMacTrafficReq4 <= USED 0
__trace_LTE__MACUT__LteMacutDumpUlDataFromIPC_3 <= USED 0
__trace_LTE__MACUT__LteMacutGsmVoiceCallInform1 <= USED 0
__trace_LTE__MACUT__tLteMacRlcTask_psdebug <= USED 0
__trace_LTE__PDCP__LtePdcpAmDataCnf1 <= USED 0
__trace_LTE__PDCP__LtePdcpAmDataCnf_01 <= USED 0
__trace_LTE__PDCP__LtePdcpHandleUlPdicDoneInd1 <= USED 0
__trace_LTE__PDCP__LtePdcpHandleUlPdicDoneInd1fjo <= USED 0
__trace_LTE__PDCP__LtePdcpHandleUlPdicDoneInd1fjo2 <= USED 0
__trace_LTE__PDCP__LtePdcpSrbSoftEea000 <= USED 0
__trace_LTE__PDCP__LtePdcpSrbSoftEea1010 <= USED 0
__trace_LTE__PDCP__LtePdcpSrbSoftEea111 <= USED 0
__trace_LTE__PDCP__LtePdcpSrbSoftEea222 <= USED 0
__trace_LTE__PDCP__LtePdcpSrbSoftEea333 <= USED 0
__trace_LTE__PDCP__LtePdcpSubUlPdcpDataLength_1 <= USED 0
__trace_LTE__PDCP__LteTraceDataList <= USED 0
__trace_LTE__PDCP__UL_DRB_UM_RelSnUlSdu <= USED 0
__trace_LTE__PDCP__UL_DRB_store <= USED 0
__trace_LTE__PDCP__tLtePdcpEea1 <= USED 0
__trace_LTE__PDCP__tLtePdcpLListDeleteDlNsduInfo <= USED 0
__trace_LTE__PDCP__tLtePdcpSrbDataIntegrityError <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_5869 <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_9858 <= USED 0
__trace_LTE__RABM__LteRabmCheckSnDataReq_gg68 <= USED 0
__trace_LTE__RABM__LteRabmEnablePendingSnDataReq12222222 <= USED 0
__trace_LTE__RABM__LteRabmEnablePendingSnDataReq12222222111 <= USED 0
__trace_LTE__RABM__LteRabmEnablePendingSnDataReq12222288 <= USED 0
__trace_LTE__RABM__LteRabmEnablePendingSnDataReq1228899 <= USED 0
__trace_LTE__RABM__LteRabmEnablePendingSnDataReq12540 <= USED 0
__trace_LTE__RABM__LteRabmEnablePendingSnDataReq12541 <= USED 0
__trace_LTE__RABM__LteRabmProcessSnMultiDataListReq_66 <= USED 0
__trace_LTE__RABM__LteRabmReleaseEpsSnDataList <= USED 0
__trace_LTE__RABM__LteRabmReleaseLtePdcpSduList1 <= USED 0
__trace_LTE__RABM__LteRabmReleaseLtePdcpSduList2 <= USED 0
__trace_LTE__RABM__LteRabmReleasePendingSnDataReq111 <= USED 0
__trace_LTE__RABM__LteRabmReleaseSnDataReq1 <= USED 0
__trace_LTE__RABM__LteRabmStartWaitToReestTimer1 <= USED 0
__trace_LTE__RABM__LteUlIpPkgCompatibleWithTft1 <= USED 0
__trace_LTE__RABM__LteUlIpPkgCompatibleWithTft2 <= USED 0
__trace_LTE__RABM__LteUlIpPkgCompatibleWithTft3 <= USED 0
__trace_LTE__RLC__LteAmRlcDiscardUlPackets0a0 <= USED 0
__trace_LTE__SM__SmDoAbgpSmApnReadInd <= USED 0
__trace_MAT__MATHandleResponse___MATConfIndCB_0 <= USED 0
__trace_MEM__DUMP__GetUUID_High <= USED 0
__trace_MEM__DUMP__GetUUID_Low <= USED 0
__trace_MIFI__LOG__SaveToFS <= USED 0
__trace_MIFI__LWIP__lwip_api_1062 <= USED 0
__trace_MIFI__LWIP__lwip_api_1070 <= USED 0
__trace_MIFI__LWIP__lwip_api_1097 <= USED 0
__trace_MIFI__LWIP__lwip_api_1121 <= USED 0
__trace_MIFI__LWIP__lwip_api_1125 <= USED 0
__trace_MIFI__LWIP__lwip_api_1979 <= USED 0
__trace_MIFI__LWIP__lwip_api_1996 <= USED 0
__trace_MIFI__LWIP__lwip_api_2001 <= USED 0
__trace_MIFI__LWIP__lwip_api_2106 <= USED 0
__trace_MIFI__LWIP__lwip_api_2108 <= USED 0
__trace_MIFI__LWIP__lwip_api_2120 <= USED 0
__trace_MIFI__LWIP__lwip_api_2128 <= USED 0
__trace_MIFI__LWIP__lwip_api_2272 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_751 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_950 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_954 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_965 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_970 <= USED 0
__trace_MIFI__LWIP__lwip_api_lib_977 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6_524 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6_527 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6_87 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_127 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_128 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp6d_133 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_829 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_835 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_847 <= USED 0
__trace_MIFI__LWIP__lwip_dhcp_851 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_1314 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_1319 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_1330 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2231 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2234 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2239 <= USED 0
__trace_MIFI__LWIP__lwip_dhcpd_2244 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_2134 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_693 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_719 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_744 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_752 <= USED 0
__trace_MIFI__LWIP__lwip_etharp_757 <= USED 0
__trace_MIFI__LWIP__lwip_ip6_addr_301 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_741 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_871 <= USED 0
__trace_MIFI__LWIP__lwip_ip_frag_881 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_267 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2682 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2699 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2712 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2736 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2742 <= USED 0
__trace_MIFI__LWIP__lwip_ip_nat_2755 <= USED 0
__trace_MIFI__LWIP__lwip_nd6_2857 <= USED 0
__trace_MIFI__LWIP__lwip_nd6_2867 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_392 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_481 <= USED 0
__trace_MIFI__LWIP__lwip_net_bridge_484 <= USED 0
__trace_MIFI__LWIP__lwip_netdb_336 <= USED 0
__trace_MIFI__LWIP__lwip_netdb_338 <= USED 0
__trace_MIFI__LWIP__lwip_netdb_353 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1004 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1040 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1650 <= USED 0
__trace_MIFI__LWIP__lwip_netif_1669 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2518 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2598 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2695 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2700 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2725 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2740 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2745 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2750 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2775 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2780 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2800 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2810 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2815 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2820 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2850 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2860 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2865 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2880 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2890 <= USED 0
__trace_MIFI__LWIP__lwip_netif_2970 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3000 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3003 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3080 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3095 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3100 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3115 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3150 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3175 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3180 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3190 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3200 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3210 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3215 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3219 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3220 <= USED 0
__trace_MIFI__LWIP__lwip_netif_3255 <= USED 0
__trace_MIFI__LWIP__lwip_netif_712 <= USED 0
__trace_MIFI__LWIP__lwip_netif_716 <= USED 0
__trace_MIFI__LWIP__lwip_netif_985 <= USED 0
__trace_MIFI__LWIP__lwip_netif_998 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_05191630 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_228 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_270 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_328 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_332 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_371 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_381 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_389 <= USED 0
__trace_MIFI__LWIP__lwip_netif_pc_397 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_102 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_108 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_110 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_421 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_638 <= USED 0
__trace_MIFI__LWIP__lwip_netif_ppp_642 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_1394 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_202006021720 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2118 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2120 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2140 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_2437 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_417 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_796 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_820 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_823 <= USED 0
__trace_MIFI__LWIP__lwip_netif_td_869 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_335 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_398 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_417 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_421 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_425 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_432 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_557 <= USED 0
__trace_MIFI__LWIP__lwip_netif_wifi_uap_570 <= USED 0
__trace_MIFI__LWIP__lwip_pbuf_1179 <= USED 0
__trace_MIFI__LWIP__lwip_pbuf_1182 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_1471 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_2149 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_2159 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_2164 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_2177 <= USED 0
__trace_MIFI__LWIP__lwip_sockets_2204 <= USED 0
__trace_MIFI__LWIP__lwip_stats_109 <= USED 0
__trace_MIFI__LWIP__lwip_stats_121 <= USED 0
__trace_MIFI__LWIP__lwip_stats_505 <= USED 0
__trace_MIFI__LWIP__lwip_stats_514 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1752 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_1754 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2333 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2335 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2339 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2341 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2345 <= USED 0
__trace_MIFI__LWIP__lwip_tcp_2347 <= USED 0
__trace_MIFI__LWIP__lwip_tcpip_1225 <= USED 0
__trace_MIFI__LWIP__lwip_tcpip_1230 <= USED 0
__trace_MIFI__LWIP__lwip_tcpip_1622 <= USED 0
__trace_MIFI__RNDIS__HALT_MSG <= USED 0
__trace_MIFI__RNDIS__INITIALIZE_MSG <= USED 0
__trace_MIFI__RNDIS__RESET_MSG <= USED 0
__trace_MIFI__RNDIS__Rndis_init_response <= USED 0
__trace_MIFI__RNDIS__Rndis_init_response1 <= USED 0
__trace_MIFI__RNDIS__Rndis_remove_hdr <= USED 0
__trace_MIFI__RNDIS__Rndis_remove_hdr1 <= USED 0
__trace_MIFI__RNDIS__UNKNOWN_MSG <= USED 0
__trace_PCAC__ATCMDSrv__convertDecIntToBinStr_result <= USED 0
__trace_PCAC__CI__convertBinStrToDecInt_FAIL <= USED 0
__trace_PCAC__CI__convertBinStrToDecInt_PASS <= USED 0
__trace_PM__CommPM__TCMStartFITimer_1 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_011 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_3 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_33 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_4 <= USED 0
__trace_PM__test_mips__CPM_INFO_0826_44 <= USED 0
__trace_PSLTE__DTC__LteHandlePdicHisrCh0BeforeY00 <= USED 0
__trace_PSLTE__DTC__LtePdcpErrorIpHeader_inconsecutive_ipid <= USED 0
__trace_PSNAS__ESM__EsmSendNotificationABCC_1 <= USED 0
__trace_PSNAS__abmm__abmmCheckReselectStatus_0000 <= USED 0
__trace_PSNAS__abmm__abmmCheckReselectStatus_000001 <= USED 0
__trace_PSNAS__abmm__abmmDoMmrReselectInd_001 <= USED 0
__trace_PSNAS__abmm__abmmRmStopTimersWithoutHplmnTimer_1 <= USED 0
__trace_PS_2G__GRR_MBCCH__MULTI_BCCH_ABORT <= USED 0
__trace_PS_2G__GRR_TRAT_IDLE__GrrComAlignFddMeas_1 <= USED 0
__trace_PS_2G__GRR__GrrComAlignFddMeas_2 <= USED 0
__trace_PS_2G__HAG__Hag_Update_State0 <= USED 0
__trace_PS_2G__HAG__Hag_Update_State1 <= USED 0
__trace_PS_3G__ABGP__abmmsendMmrSetMedata_0 <= USED 0
__trace_PS_3G__ABMM__JdHandleIntCellSelectInd_1 <= USED 0
__trace_PS_3G__ABMM__abmmBlCopyBaListInfo_001 <= USED 0
__trace_PS_3G__ABMM__abmmBlCopyBaListInfo_002 <= USED 0
__trace_PS_3G__ABMM__abmmBmCheckPendingRatChange_1 <= USED 0
__trace_PS_3G__ABMM__abmmBmNewFddLteBandMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmBmNewTdLteBandMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmBmUpdateOppositeWriteAbleData_000 <= USED 0
__trace_PS_3G__ABMM__abmmBmUpdateOppositeWriteAbleData_001 <= USED 0
__trace_PS_3G__ABMM__abmmBmUpdateOppositeWriteAbleData_1 <= USED 0
__trace_PS_3G__ABMM__abmmBmUpdateOppositeWriteAbleData_12 <= USED 0
__trace_PS_3G__ABMM__abmmBmUpdateOppositeWriteAbleData_2 <= USED 0
__trace_PS_3G__ABMM__abmmBmUpdateOppositeWriteAbleData_3 <= USED 0
__trace_PS_3G__ABMM__abmmCheckOtherCardSendPlmnSearch_0 <= USED 0
__trace_PS_3G__ABMM__abmmCheckOtherCardSendPlmnSearch_01 <= USED 0
__trace_PS_3G__ABMM__abmmCheckOtherCardSendPlmnSearch_02 <= USED 0
__trace_PS_3G__ABMM__abmmCheckOtherCardSendPlmnSearch_03 <= USED 0
__trace_PS_3G__ABMM__abmmCheckOtherCardSendPlmnSearch_1 <= USED 0
__trace_PS_3G__ABMM__abmmCheckOtherCardSendPlmnSearch_3 <= USED 0
__trace_PS_3G__ABMM__abmmCheckReselectStatus_plmn_num0 <= USED 0
__trace_PS_3G__ABMM__abmmDoApexMmDynamicReleaseReq_1 <= USED 0
__trace_PS_3G__ABMM__abmmDoDelayedApexDevWriteBandModeReq_1 <= USED 0
__trace_PS_3G__ABMM__abmmDoMmrAbortRegCnf_1 <= USED 0
__trace_PS_3G__ABMM__abmmGetCommonReserved2Value_0 <= USED 0
__trace_PS_3G__ABMM__abmmGetCommonReserved3alue_0 <= USED 0
__trace_PS_3G__ABMM__abmmPlCopyAvailableLtePlmns_4 <= USED 0
__trace_PS_3G__ABMM__abmmPlCopyAvailableLtePlmns_5 <= USED 0
__trace_PS_3G__ABMM__abmmPlCopyAvailableLtePlmns_6 <= USED 0
__trace_PS_3G__ABMM__abmmPlCopyAvailableLtePlmns_7 <= USED 0
__trace_PS_3G__ABMM__abmmPlManagedRoaming_1 <= USED 0
__trace_PS_3G__ABMM__abmmPlSuspendResumeDecideSuspendOrAbort_2 <= USED 0
__trace_PS_3G__ABMM__abmmPlmPrintPlmnList_1 <= USED 0
__trace_PS_3G__ABMM__abmmPlmPrintPlmnList_2 <= USED 0
__trace_PS_3G__ABMM__abmmPlmPrintPlmnList_3 <= USED 0
__trace_PS_3G__ABMM__abmmPlmPrintPlmnList_4 <= USED 0
__trace_PS_3G__ABMM__abmmRmInterruptReg_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsNeedTryMoreOnLteForOos_0 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsNeedTryMoreOnLteForOos_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsNeedTryMoreOnLteForOos_2 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsNeedTryMoreOnLteForOos_21 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsNeedTryMoreOnLteForOos_3 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsNeedTryMoreOnLteForOos_4 <= USED 0
__trace_PS_3G__ABMM__abmmRmIsNeedTryMoreOnLteForOos_5 <= USED 0
__trace_PS_3G__ABMM__abmmRmNarrowHplmnSearchNetworkMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmNarrowHplmnSearchNetworkMode_2 <= USED 0
__trace_PS_3G__ABMM__abmmRmNarrowHplmnSearchNetworkMode_3 <= USED 0
__trace_PS_3G__ABMM__abmmRmPartiallyRegistered_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmReceivedForbiddenNatLaInDualRatManualMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmReceivedForbiddenPlmnInDualRatManualMode_1 <= USED 0
__trace_PS_3G__ABMM__abmmRmResetModeWithNonCmccCard_2 <= USED 0
__trace_PS_3G__ABNV__ICATsethsdpdaCategory_1_002 <= USED 0
__trace_PS_3G__ABNV__ICATsethsdpdaCategory_2_001 <= USED 0
__trace_PS_3G__ABSM__absmProcessAlsiWriteSmCnfForStore_2 <= USED 0
__trace_PS_3G__GRR_CSEL__CsGetBandModeForPerformingStoredListSelection_2 <= USED 0
__trace_PS_3G__GRR_CSEL__InitCcchCellSelection1 <= USED 0
__trace_PS_3G__GRR_PLMN__PlmnListHandleMphBcchMeasInd3 <= USED 0
__trace_PS_3G__GRR_PLMN__PlmnListProcMphBsicDecodeInd1 <= USED 0
__trace_PS_3G__GRR_PLMN__PlmnListProcMphBsicDecodeInd2 <= USED 0
__trace_PS_3G__GRR_PLMN__PlmnListProcMphBsicDecodeInd3 <= USED 0
__trace_PS_3G__GRR_PLMN__PlmnListProcMphBsicDecodeInd4 <= USED 0
__trace_PS_3G__GRR_QB__QB_PlmnListCheckRequestedPlmnId <= USED 0
__trace_PS_3G__GRR_QB__QB_PlmnListHandleMphBcchMeasInd2 <= USED 0
__trace_PS_3G__GRR__CsSaveOrUseStoredArfcnList_1 <= USED 0
__trace_PS_3G__GRR__CsSaveOrUseStoredArfcnList_2 <= USED 0
__trace_PS_3G__GRR__GrrDsIsMtEstabOnGoing_1 <= USED 0
__trace_PS_3G__GRR__GrrDsIsSimbActive_1 <= USED 0
__trace_PS_3G__GRR__GrrIdleStorePscToFddNcellList_0 <= USED 0
__trace_PS_3G__GRR__GrrNcellCheckFddNcell_1 <= USED 0
__trace_PS_3G__GRR__GrrNcellCheckFddNcell_2 <= USED 0
__trace_PS_3G__GRR__GrrNcellIsInRachFailBarredList_1 <= USED 0
__trace_PS_3G__GRR__GrrNcellIsInRachFailBarredList_2 <= USED 0
__trace_PS_3G__GRR__GrrPlmnSetLteBandsToScan <= USED 0
__trace_PS_3G__GRR__PidleHdlEutraScellInfoInd_1 <= USED 0
__trace_PS_3G__GRR__QB_PlmnListCheckRequestedPlmnId_2 <= USED 0
__trace_PS_3G__GRR__QB_PlmnListCheckRequestedPlmnId_3 <= USED 0
__trace_PS_3G__GRR__RESEL_NCELL_10 <= USED 0
__trace_PS_3G__HAW__pldBindGsmCnf_3 <= USED 0
__trace_PS_3G__KIOS__WarnAssertFail <= USED 0
__trace_PS_3G__MAC__MacRxEgprsSdsDataInd_1 <= USED 0
__trace_PS_3G__MM__EmmMmxxEstReq_2 <= USED 0
__trace_PS_3G__MM__GmmStartPsConEstGuardTimer_1 <= USED 0
__trace_PS_3G__MM__MmDualGrrChekDMmIsMmInDedicate_0 <= USED 0
__trace_PS_3G__MM__MmDualIsDmmInLTEMode_1 <= USED 0
__trace_PS_3G__MM__MmDualIsDmmPendingMtCall0 <= USED 0
__trace_PS_3G__MM__MmDualIsDmmPendingMtCall1 <= USED 0
__trace_PS_3G__MM__MmDualIsDmmPendingMtCall2 <= USED 0
__trace_PS_3G__MM__MmDualMmHandlePageInd_1 <= USED 0
__trace_PS_3G__MM__MmDualSendSignal_20171116_02 <= USED 0
__trace_PS_3G__MM__MmDualUmmHandleServiceRequestBeforeSuspend_01 <= USED 0
__trace_PS_3G__MM__MmDualUpdateAsAndSendCampReq <= USED 0
__trace_PS_3G__MM__MmInitiRatGuardTimers_1 <= USED 0
__trace_PS_3G__MM__MmPendGmmSmUnitDataReq_1 <= USED 0
__trace_PS_3G__MM__MmPendGmmSmUnitDataReq_22 <= USED 0
__trace_PS_3G__MM__MmPendRrcPageInd_1 <= USED 0
__trace_PS_3G__MM__MmPendRrcPageInd_22 <= USED 0
__trace_PS_3G__MM__MmPrcessMmxxEstReqUnderLte_20141217 <= USED 0
__trace_PS_3G__MM__MmPrcessMmxxEstReqUnderLte_20150109 <= USED 0
__trace_PS_3G__MM__MmPrcessMmxxEstReqUnderLte_20150128 <= USED 0
__trace_PS_3G__MM__MmPrcessMmxxEstReqUnderLte_2015080401 <= USED 0
__trace_PS_3G__MM__MmPrcessMmxxEstReqUnderLte_2015080402 <= USED 0
__trace_PS_3G__MM__MmPrcessMmxxEstReqUnderLte_2015080403 <= USED 0
__trace_PS_3G__MM__MmPrcessMmxxEstReqUnderLte_20181009 <= USED 0
__trace_PS_3G__MM__MmReceiveMTCall_1 <= USED 0
__trace_PS_3G__MM__MmStart3212Timer_1 <= USED 0
__trace_PS_3G__MM__MmStart3212Timer_2 <= USED 0
__trace_PS_3G__MM__MmStartCsConEstGuardTimer_1 <= USED 0
__trace_PS_3G__MM__MmStartCsConEstGuardTimer_2 <= USED 0
__trace_PS_3G__MM__MmStartPsConEstGuardTimer_2 <= USED 0
__trace_PS_3G__RABM__DlbgUpdeReceiveRlcSdu <= USED 0
__trace_PS_3G__RD_TX__UL_THROUGHPUT_TIMER_CFG_INTERN <= USED 0
__trace_PS_3G__RR_COM__RrDsCheckEnterSameOperatorModeSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__RrDsCheckWorkOnSameSuitableRatSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__RrDsGetIratDsAbortSearchCnfSendingSubstituted_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetUmtsPagingDrxLength_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsGetUmtsPagingDrxLength_1 <= USED 0
__trace_PS_3G__RR_COM__rrDsSendIratDsCancelAbortGsmPsReq_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsSendIratDsPhyConfigFinishInd_0 <= USED 0
__trace_PS_3G__RR_COM__rrDsSendIratDsVoiceBadInd_0 <= USED 0
__trace_PS_3G__SAC_DAT__sacGetCurrentNsapi <= USED 0
__trace_PS_3G__SM__cancelRel5QoS_1 <= USED 0
__trace_PS_3G__UBND_CFG__GetFddBandRegionForBandIndex <= USED 0
__trace_PS_3G__ULBGRABMDTC__ulbgRabmSplitLteSnMultiDataReq11 <= USED 0
__trace_PS_3G__ULBGRABMDTC__ulbgRabmSplitLteSnMultiDataReq_ACK12 <= USED 0
__trace_PS_3G__ULBGRABMDTC__ulbgRabmSplitLteSnMultiDataReq_DAT3 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced0 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced01 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced02 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced04 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced05 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced10 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced11 <= USED 0
__trace_PS_3G__UMAC__f8Enhanced14 <= USED 0
__trace_PS_3G__URRC_CSRR__IsRedirectionFailureHandlingEnable_0 <= USED 0
__trace_PS_3G__URRC_CSR_Priority_1__RrIsTestSimGcfMode_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsCheckIfFgPlmnOngoing_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsDbIsPscInUmtsFindCellCnf_1 <= USED 0
__trace_PS_4G_ERRC__PLMS__PlmsFndAddEarfcnToScanOrderTable_1 <= USED 0
__trace_PS_4G__MM__MmPendEmmEsmUnitDataReq_1 <= USED 0
__trace_PS_4G__MM__MmPendEmmEsmUnitDataReq_2 <= USED 0
__trace_PS_4G__MM__MmSendMmrFlushFlashEnd_0 <= USED 0
__trace_PS_4G__MM__MmSimIsCMCC_0 <= USED 0
__trace_PS_4G__MM__MmSimIsCMCC_1 <= USED 0
__trace_PS_4G__MM__MmSimIsCUCC_0 <= USED 0
__trace_PS_4G__MM__MmSimIsCUCC_1 <= USED 0
__trace_PS_4G__PLMS__PlmsStoreSeqNum_1 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_allocmem_extend_debug_0 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_allocmem_extend_debug_1 <= USED 0
__trace_PS_PLT__IPNET_DL__usbnet_freemem_debug <= USED 0
__trace_PS_PLT__IPNET_UL__IpNetLwipGetUlPacketPoll_debug_0 <= USED 0
__trace_PS_PLT__IPNET_UL__IpNetLwipGetUlPacketPoll_debug_1 <= USED 0
__trace_PS_PLT__IpNetUsbGetUlPacketPoll__DiscardPacket <= USED 0
__trace_PS_PLT__IpNetUsbGetUlPacketPoll__GetPacket <= USED 0
__trace_PS_PLT__LWIP_DL__lwip_downlink_putmem_debug <= USED 0
__trace_PS_PLT__ModemTFT__DirectionAndNextHeader <= USED 0
__trace_PS_PLT__ModemTFT__FindOldTft <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_1 <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_2 <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_dstport <= USED 0
__trace_PS_PLT__ModemTFT__MatchInfofindIp6_sorceport <= USED 0
__trace_PS_PLT__ModemTFT__PFNodeAllocFailed <= USED 0
__trace_PS_PLT__ModemTFT__PFReconfigure <= USED 0
__trace_PS_PLT__ModemTFT__PacketFilter_id <= USED 0
__trace_PS_PLT__ModemTFT__QosInfo <= USED 0
__trace_PS_PLT__ModemTFT__RemoteAddrInfo <= USED 0
__trace_PS_PLT__ModemTFT__SpiInfo <= USED 0
__trace_PS_PLT__ModemTFT____tft_delete_tft <= USED 0
__trace_PS_PLT__ModemTFT____tft_delete_tft_1 <= USED 0
__trace_PS_PLT__ModemTFT__addNewPFOK <= USED 0
__trace_PS_PLT__ModemTFT__addTFTInorderEPI_0 <= USED 0
__trace_PS_PLT__ModemTFT__addTFTInorderEPI_1 <= USED 0
__trace_PS_PLT__ModemTFT__filtercount <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_cid_port_IP6_Add <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_cid_port_IP6_port <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_epsid1forIp4_No_Proto <= USED 0
__trace_PS_PLT__ModemTFT__ip_nat_get_epsid1forIp6 <= USED 0
__trace_PS_PLT__ModemTFT__ipfragment_failure <= USED 0
__trace_PS_PLT__ModemTFT__ipv6fragment_failure <= USED 0
__trace_PS_PLT__ModemTFT__portInfo <= USED 0
__trace_PS_PLT__ModemTFT__submasksetip6 <= USED 0
__trace_PS_PLT__ModemTFT__submasksetipv4 <= USED 0
__trace_PS_PLT__ModemTFT__submasksetipv7 <= USED 0
__trace_PS_PLT__PrintCid__GetDefault4g_parameter_invalid <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektAllocERROR <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektAllocFailed <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektAllocOk <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektHighLevel <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektPush1 <= USED 0
__trace_PS_PLT__VoLteBuffer__DLPacektPush2 <= USED 0
__trace_PS_PLT__VoLteBuffer__VoLTERxRelease <= USED 0
__trace_PS_PLT__VoLteBuffer__VoLTETxRegister <= USED 0
__trace_PS_PLT__VoLteToAP__ToAP <= USED 0
__trace_PS_PLT__VoLteToLocal__ToVolteEngine <= USED 0
__trace_PS_PLT__VolteUpLink__AllocBufferfail <= USED 0
__trace_PS_PLT__VolteUpLink__Allocparameterfail <= USED 0
__trace_PS_PLT__VolteUpLink__Allocreturnbuffer <= USED 0
__trace_PS_PLT__VolteUpLink__Freecparameterfail <= USED 0
__trace_PS_PLT__VolteUpLink__uplinkAddNode <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_0 <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_2 <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_Leave <= USED 0
__trace_PS_PLT__Volte__AddIp4Filter_enter <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_0 <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_2 <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_Leave <= USED 0
__trace_PS_PLT__Volte__AddIp6Filter_enter <= USED 0
__trace_PS_PLT__Volte__IpFilterComparison <= USED 0
__trace_PS_PLT__Volte__RemoveIp4Filter_enter <= USED 0
__trace_PS_PLT__Volte__RemoveIp4Filter_leave <= USED 0
__trace_PS_PLT__resume__ipnet_rx_buffer <= USED 0
__trace_PS_RR__COMDB__RrComDbSetServingUtraFddCell_1 <= USED 0
__trace_PS_RR__COMDB__RrComDbSetServingUtraFddCell_2 <= USED 0
__trace_PS_RR__COMDB__RrComDbSetUeSupportUtraBands_1 <= USED 0
__trace_PS_RR__COMDB__rrcComDbCheckIfUrrcCellBarred_1 <= USED 0
__trace_PS_RR__COMDB__rrcComDbGetNumOfGrrBarredCellList <= USED 0
__trace_PS_RR__COMDB__rrcComDbRemoveUrrcBarredCellList_1 <= USED 0
__trace_PS_RR__COMDB__rrcComDbSetUrrcBarredCellList_1 <= USED 0
__trace_PS__ABCF__PsComCfgProcNvmMultiple_1 <= USED 0
__trace_PS__ABCF__PsComCfgProcNvmMultiple_3 <= USED 0
__trace_PS__ComCfg__NoNvmProc_Invalid <= USED 0
__trace_PS__ComCfg__NoNvmProc_Ok <= USED 0
__trace_PS__ComCfg__NvmProcMultiple_Invalid <= USED 0
__trace_PS__ComCfg__NvmProcMultiple_Invalid2 <= USED 0
__trace_PS__ComCfg__NvmProcMultiple_Ok <= USED 0
__trace_PS__ComCfg__NvmProc_Invalid <= USED 0
__trace_PS__ComCfg__NvmProc_Invalid2 <= USED 0
__trace_PS__ComCfg__NvmProc_Ok <= USED 0
__trace_PS__ComCfg__PsComCfg_3gBandSet <= USED 0
__trace_PS__ComCfg__PsComCfg_ResetVendorFlags <= USED 0
__trace_PS__ComCfg__SetBandMode <= USED 0
__trace_SAC__DATA__SAC_TRACE_API <= USED 0
__trace_SAC__DEV__sacDevSetBandModeCgattReq_1 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeCgattReq_2 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeCgattReq_3 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeCgattReq_4 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeCgattReq_5 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeGLSwapReq_1 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeGLSwapReq_2 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeGLSwapReq_3 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeGLSwapReq_4 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeGLSwapReq_5 <= USED 0
__trace_SAC__DEV__sacDevSetBandModeSrvDomainReq_gl_1 <= USED 0
__trace_SAC__DEV__setRoamingForbidenPlmnReq_0 <= USED 0
__trace_SAC__PS__sacPsGetSim4GQciValue_1 <= USED 0
__trace_SAC__SACDEV__sacCiDevPrimGetInternalRevisionIdReqFunc_01 <= USED 0
__trace_SAC__SACLIB__sacShCheckSpecPendingCiReq_1 <= USED 0
__trace_SAC__SACPS__sacPsDeactivateAllPdpContext_01 <= USED 0
__trace_SAC__SACPS__sacPsDeactivateAllPdpContext_02 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_1 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_2 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_3 <= USED 0
__trace_SAC__SACPS__sacPsProcessApexMmBandInd_4 <= USED 0
__trace_SAC__SACPS__sacSigSmRegPdpActivateCnfFunc_4 <= USED 0
__trace_SAC__SACPS__sacSigSmRegPdpActivateCnfFunc_5 <= USED 0
__trace_SAC__SACPS__sacSigSmRegPdpActivateCnfFunc_6 <= USED 0
__trace_SAC__SACPS__sacSigSmRegPdpActivateSecRejFunc_1 <= USED 0
__trace_SAC__SACPS__sacSigSmRegPdpActivateSecRejFunc_2 <= USED 0
__trace_SSIPC__NET_SIPC__MATGetDataFromStr1 <= USED 0
__trace_SSIPC__NET_SIPC__MATGetDataFromStr2 <= USED 0
__trace_SW_PLAT__COMCfg__GetParamNotFound <= USED 0
__trace_SW_PLAT__COMCfg__GetParamOK <= USED 0
__trace_SW_PLAT__COMCfg__GetParam_value_len_err1 <= USED 0
__trace_SW_PLAT__COMCfg__GetParam_value_len_err2 <= USED 0
__trace_SW_PLAT__COMCfg__Get_Platfrom_Name <= USED 0
__trace_SW_PLAT__COMCfg__Get_invalid_strlen <= USED 0
__trace_SW_PLAT__DIAG__setGLFeatureFlag <= USED 0
__trace_SW_PLAT__GripNotifyAP__GRIPSENSOR0 <= USED 0
__trace_SW_PLAT__GripNotifyAP__GRIPSENSOR1 <= USED 0
__trace_SW_PLAT__I2C__I2CMasterNotifyDataReceived2 <= USED 0
__trace_SW_PLAT__I2C__NotifyErrorCBack <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive1 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive1_retry <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterReceive2 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend1 <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend1_retry <= USED 0
__trace_SW_PLAT__I2C__i2cMicco_I2CMasterSend2 <= USED 0
__trace_SW_PLAT__KEYPAD__KEYPAD_INFO <= USED 0
__trace_SW_PLAT__KEYPAD__KEYPAD_INFO2 <= USED 0
__trace_SW_PLAT__KEYPAD__KEYPAD_INFO3 <= USED 0
__trace_SW_PLAT__KEYPAD__KEYPAD_ISR_ERR <= USED 0
__trace_SW_PLAT__KEYPAD__KEYPAD_ISR_ERR2 <= USED 0
__trace_SW_PLAT__KEYPAD__MULTIKEY <= USED 0
__trace_SW_PLAT__NVM__BAD_ComBasicCfg <= USED 0
__trace_SW_PLAT__NVM__NoFile <= USED 0
__trace_SW_PLAT__NVM__OldVerUpdate <= USED 0
__trace_SW_PLAT__PERFORMANCE__IdleTaskCreateError <= USED 0
__trace_SW_PLAT__RDA__ReliableDataUnPackDo <= USED 0
__trace_SW_PLAT__RDA__rd_Port_DataInd <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_1 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_2 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_3 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_4 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_5 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_7 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_8 <= USED 0
__trace_SYSTEM__SYSNVM__psVendorPrintf_9 <= USED 0
__trace_TFSOP__FEOF__LAB001 <= USED 0
__trace_TFSOP__FTELL__LAB001 <= USED 0
__trace_generic_mini_no_params_func <= USED 0
__trace_generic_mini_params_func <= USED 0
__trace_generic_no_params_func_000 <= USED 0
__trace_generic_no_params_func_001 <= USED 0
__trace_generic_no_params_func_002 <= USED 0
__trace_generic_no_params_func_003 <= USED 0
__trace_generic_no_params_func_004 <= USED 0
__trace_generic_no_params_func_005 <= USED 0
__trace_generic_no_params_func_006 <= USED 0
__trace_generic_no_params_func_007 <= USED 0
__trace_generic_no_params_func_008 <= USED 0
__trace_generic_no_params_func_009 <= USED 0
__trace_generic_no_params_func_010 <= USED 0
__trace_generic_no_params_func_011 <= USED 0
__trace_generic_no_params_func_012 <= USED 0
__trace_generic_no_params_func_013 <= USED 0
__trace_generic_no_params_func_014 <= USED 0
__trace_generic_no_params_func_015 <= USED 0
__trace_generic_no_params_func_016 <= USED 0
__trace_generic_no_params_func_017 <= USED 0
__trace_generic_no_params_func_018 <= USED 0
__trace_generic_no_params_func_019 <= USED 0
__trace_generic_no_params_func_020 <= USED 0
__trace_generic_no_params_func_021 <= USED 0
__trace_generic_no_params_func_022 <= USED 0
__trace_generic_no_params_func_023 <= USED 0
__trace_generic_no_params_func_024 <= USED 0
__trace_generic_no_params_func_025 <= USED 0
__trace_generic_no_params_func_026 <= USED 0
__trace_generic_no_params_func_027 <= USED 0
__trace_generic_no_params_func_028 <= USED 0
__trace_generic_no_params_func_029 <= USED 0
__trace_generic_no_params_func_030 <= USED 0
__trace_generic_no_params_func_031 <= USED 0
__trace_generic_no_params_func_032 <= USED 0
__trace_generic_no_params_func_033 <= USED 0
__trace_generic_no_params_func_034 <= USED 0
__trace_generic_no_params_func_035 <= USED 0
__trace_generic_no_params_func_036 <= USED 0
__trace_generic_no_params_func_037 <= USED 0
__trace_generic_no_params_func_038 <= USED 0
__trace_generic_no_params_func_039 <= USED 0
__trace_generic_no_params_func_040 <= USED 0
__trace_generic_no_params_func_041 <= USED 0
__trace_generic_no_params_func_042 <= USED 0
__trace_generic_no_params_func_043 <= USED 0
__trace_generic_no_params_func_044 <= USED 0
__trace_generic_no_params_func_045 <= USED 0
__trace_generic_no_params_func_046 <= USED 0
__trace_generic_no_params_func_047 <= USED 0
__trace_generic_no_params_func_048 <= USED 0
__trace_generic_no_params_func_049 <= USED 0
__trace_generic_no_params_func_050 <= USED 0
__trace_generic_no_params_func_051 <= USED 0
__trace_generic_no_params_func_052 <= USED 0
__trace_generic_no_params_func_053 <= USED 0
__trace_generic_no_params_func_054 <= USED 0
__trace_generic_no_params_func_055 <= USED 0
__trace_generic_no_params_func_056 <= USED 0
__trace_generic_no_params_func_057 <= USED 0
__trace_generic_no_params_func_058 <= USED 0
__trace_generic_no_params_func_059 <= USED 0
__trace_generic_no_params_func_060 <= USED 0
__trace_generic_no_params_func_061 <= USED 0
__trace_generic_no_params_func_062 <= USED 0
__trace_generic_no_params_func_063 <= USED 0
__trace_generic_no_params_func_064 <= USED 0
__trace_generic_no_params_func_065 <= USED 0
__trace_generic_no_params_func_066 <= USED 0
__trace_generic_no_params_func_067 <= USED 0
__trace_generic_no_params_func_068 <= USED 0
__trace_generic_no_params_func_069 <= USED 0
__trace_generic_no_params_func_070 <= USED 0
__trace_generic_no_params_func_071 <= USED 0
__trace_generic_no_params_func_072 <= USED 0
__trace_generic_no_params_func_073 <= USED 0
__trace_generic_no_params_func_074 <= USED 0
__trace_generic_no_params_func_075 <= USED 0
__trace_generic_no_params_func_076 <= USED 0
__trace_generic_no_params_func_077 <= USED 0
__trace_generic_no_params_func_078 <= USED 0
__trace_generic_no_params_func_079 <= USED 0
__trace_generic_params_func_000 <= USED 0
__trace_generic_params_func_001 <= USED 0
__trace_generic_params_func_002 <= USED 0
__trace_generic_params_func_003 <= USED 0
__trace_generic_params_func_004 <= USED 0
__trace_generic_params_func_005 <= USED 0
__trace_generic_params_func_006 <= USED 0
__trace_generic_params_func_007 <= USED 0
__trace_generic_params_func_008 <= USED 0
__trace_generic_params_func_009 <= USED 0
__trace_generic_params_func_010 <= USED 0
__trace_generic_params_func_011 <= USED 0
__trace_generic_params_func_012 <= USED 0
__trace_generic_params_func_013 <= USED 0
__trace_generic_params_func_014 <= USED 0
__trace_generic_params_func_015 <= USED 0
__trace_generic_params_func_016 <= USED 0
__trace_generic_params_func_017 <= USED 0
__trace_generic_params_func_018 <= USED 0
__trace_generic_params_func_019 <= USED 0
__trace_generic_params_func_020 <= USED 0
__trace_generic_params_func_021 <= USED 0
__trace_generic_params_func_022 <= USED 0
__trace_generic_params_func_023 <= USED 0
__trace_generic_params_func_024 <= USED 0
__trace_generic_params_func_025 <= USED 0
__trace_generic_params_func_026 <= USED 0
__trace_generic_params_func_027 <= USED 0
__trace_generic_params_func_028 <= USED 0
__trace_generic_params_func_029 <= USED 0
__trace_generic_params_func_030 <= USED 0
__trace_generic_params_func_031 <= USED 0
__trace_generic_params_func_032 <= USED 0
__trace_generic_params_func_033 <= USED 0
__trace_generic_params_func_034 <= USED 0
__trace_generic_params_func_035 <= USED 0
__trace_generic_params_func_036 <= USED 0
__trace_generic_params_func_037 <= USED 0
__trace_generic_params_func_038 <= USED 0
__trace_generic_params_func_039 <= USED 0
__trace_generic_params_func_040 <= USED 0
__trace_generic_params_func_041 <= USED 0
__trace_generic_params_func_042 <= USED 0
__trace_generic_params_func_043 <= USED 0
__trace_generic_params_func_044 <= USED 0
__trace_generic_params_func_045 <= USED 0
__trace_generic_params_func_046 <= USED 0
__trace_generic_params_func_047 <= USED 0
__trace_generic_params_func_048 <= USED 0
__trace_generic_params_func_049 <= USED 0
__trace_generic_params_func_050 <= USED 0
__trace_generic_params_func_051 <= USED 0
__trace_generic_params_func_052 <= USED 0
__trace_generic_params_func_053 <= USED 0
__trace_generic_params_func_054 <= USED 0
__trace_generic_params_func_055 <= USED 0
__trace_generic_params_func_056 <= USED 0
__trace_generic_params_func_057 <= USED 0
__trace_generic_params_func_058 <= USED 0
__trace_generic_params_func_059 <= USED 0
__trace_generic_params_func_060 <= USED 0
__trace_generic_params_func_061 <= USED 0
__trace_generic_params_func_062 <= USED 0
__trace_generic_params_func_063 <= USED 0
__trace_generic_params_func_064 <= USED 0
__trace_generic_params_func_065 <= USED 0
__trace_generic_params_func_066 <= USED 0
__trace_generic_params_func_067 <= USED 0
__trace_generic_params_func_068 <= USED 0
__trace_generic_params_func_069 <= USED 0
__trace_generic_params_func_070 <= USED 0
__trace_generic_params_func_071 <= USED 0
__trace_generic_params_func_072 <= USED 0
__trace_generic_params_func_073 <= USED 0
__trace_generic_params_func_074 <= USED 0
__trace_generic_params_func_075 <= USED 0
__trace_generic_params_func_076 <= USED 0
__trace_generic_params_func_077 <= USED 0
__trace_generic_params_func_078 <= USED 0
__trace_generic_params_func_079 <= USED 0
__trace_generic_params_func_080 <= USED 0
__trace_generic_params_func_081 <= USED 0
__trace_generic_params_func_082 <= USED 0
__trace_generic_params_func_083 <= USED 0
__trace_generic_params_func_084 <= USED 0
__trace_generic_params_func_085 <= USED 0
__trace_generic_params_func_086 <= USED 0
__trace_generic_params_func_087 <= USED 0
__trace_generic_params_func_088 <= USED 0
__trace_generic_params_func_089 <= USED 0
__trace_generic_params_func_090 <= USED 0
__trace_generic_params_func_091 <= USED 0
__trace_generic_params_func_092 <= USED 0
__trace_generic_params_func_093 <= USED 0
__trace_generic_params_func_094 <= USED 0
__trace_generic_params_func_095 <= USED 0
__trace_generic_params_func_096 <= USED 0
__trace_generic_params_func_097 <= USED 0
__trace_generic_params_func_098 <= USED 0
__trace_generic_params_func_099 <= USED 0
__trace_generic_params_func_100 <= USED 0
__trace_generic_params_func_101 <= USED 0
__trace_generic_params_func_102 <= USED 0
__trace_generic_params_func_103 <= USED 0
__trace_generic_params_func_104 <= USED 0
__trace_generic_params_func_105 <= USED 0
__trace_generic_params_func_106 <= USED 0
__trace_generic_params_func_107 <= USED 0
__trace_generic_params_func_108 <= USED 0
__trace_generic_params_func_109 <= USED 0
__trace_generic_params_func_110 <= USED 0
__trace_generic_params_func_111 <= USED 0
__trace_generic_params_func_112 <= USED 0
__trace_generic_params_func_113 <= USED 0
__trace_generic_params_func_114 <= USED 0
__trace_generic_params_func_115 <= USED 0
__trace_generic_params_func_116 <= USED 0
__trace_generic_params_func_117 <= USED 0
__trace_generic_params_func_118 <= USED 0
__trace_generic_params_func_119 <= USED 0
__trace_generic_params_func_120 <= USED 0
__trace_generic_params_func_121 <= USED 0
__trace_generic_params_func_122 <= USED 0
__trace_generic_params_func_123 <= USED 0
__trace_generic_params_func_124 <= USED 0
__trace_generic_params_func_125 <= USED 0
__trace_generic_params_func_126 <= USED 0
__trace_generic_params_func_127 <= USED 0
__trace_generic_params_func_128 <= USED 0
__trace_generic_params_func_129 <= USED 0
;FILE diag_API.o
diagStructPrintf_Empty <= USED 0
isDiagTracePermitted <= USED 0
setDiagTraceProhibited <= USED 0
;FILE diag_Utils.o
DiagAPI_GetTimeStamp <= USED 0
;FILE diag_buff.o
SetDiagTxMsgCounterPtr <= USED 0
updateStatsOnApps <= USED 0
;FILE diag_comm.o
;FILE diag_comm_EXTif.o
CopyDiagDirectToRingbuf <= USED 0
diagCommTransmitToRingBuf <= USED 0
diagExtIFStatusDiscNotify <= USED 0
diagExtIFstatusConnectNotify <= USED 0
;FILE diag_comm_EXTif_OSA_NUCLEUS.o
DiagTimerTx <= USED 0
diagCommMultiTransmitToUsb <= USED 0
diagCommReceiveLISR <= USED 0
diagCommTransmitToUart1 <= USED 0
diagTxTransactionCompleted <= USED 0
diag_comm_sd_get_hdr <= USED 0
diag_hsi_tx_done <= USED 0
diag_os_TransmitToExtIf1 <= USED 0
diag_output_dev_is_shm <= USED 0
diag_output_dev_is_spi <= USED 0
diag_output_dev_is_uart <= USED 0
diag_output_dev_set_shm <= USED 0
diag_output_dev_set_uart <= USED 0
get_diag_cache_flag <= USED 0
uart2UsbRxCB <= USED 0
;FILE diag_comm_INTif.o
;FILE diag_comm_L2.o
;FILE diag_comm_L4.o
diagCommL4Init <= USED 0
;FILE diag_comm_if.o
diagCommSetChunk <= USED 0
;FILE diag_header_handler.o
FindSoTOffset <= USED 0
GetMsgIDFromTxHeader <= USED 0
GetRxMsgSourceID <= USED 0
GetUsbMsgLen <= USED 0
IsC2CStats <= USED 0
;FILE diag_init.o
SetTraceBeFiltered <= USED 0
SetTraceNotFiltered <= USED 0
diagEnterBootLoaderCBFuncBind <= USED 0
diagICATReadyNotifyEventBind <= USED 0
diagPhase1Init <= USED 0
diagStubPsCheckOKfn <= USED 0
isTraceFiltered <= USED 0
;FILE diag_mem.o
DiagMemFreeInternalItem <= USED 0
rtdm_memory_pool_delete <= USED 0
;FILE diag_nvm.o
diagCfgFilter <= USED 0
diagCfgUseHighUart <= USED 0
diagStartPsCfg <= USED 0
eeh_dump_output_dev_is_spi <= USED 0
eeh_dump_output_dev_set_spi <= USED 0
eeh_dump_output_dev_set_usb_sd <= USED 0
getDiagUartSpeed <= USED 0
setEehDumpDevType <= USED 0
setGLFeatureFlag <= USED 0
sulog_output_dev_set_sd <= USED 0
sulog_output_dev_set_usb <= USED 0
;FILE diag_osif.o
diagBSPconfig <= USED 0
diagOSspecificPhase1Init <= USED 0
diagOSspecificPhase2Init <= USED 0
;FILE diag_port.o
diagPortRxIndCB <= USED 0
diagPortTxAllowed <= USED 0
diag_Port_LowWmInd <= USED 0
diag_Port_TxDoneInd <= USED 0
diag_Port_TxDoneInd_Mux <= USED 0
is_diag_enable <= USED 0
;FILE diag_restore_fixups.o
;FILE diag_rx.o
ResumeDiagMsgs <= USED 0
UpdateExtIfConnectionStatus <= USED 0
diagGetLibsVersion <= USED 0
diagPreDefCMMInit <= USED 0
;FILE diag_rx_OSA_NUCLEUS.o
;FILE diag_tx.o
;FILE dial.o
at_cmux_resp_cb <= USED 0
;FILE dialer_task.o
Is_2Gmode <= USED 0
dialer_get_compare_auth_flag <= USED 0
dialer_set_compare_auth_flag <= USED 0
getCurrentConnectNum <= USED 0
get_auto_apn_name <= USED 0
get_cgdf_save_mode <= USED 0
set_cgdf_save_mode <= USED 0
set_dialer_connmode <= USED 0
;FILE dirent.o
ChkSum <= USED 0
GB2Unicode <= USED 0
UNI_Uni2GB <= USED 0
fatDIRENT_EntryNum <= USED 0
fatDIRENT_L2SFilename <= USED 0
fatDIRENT_ReadAttrib <= USED 0
fatDIRENT_ReadFilename <= USED 0
fatDIRENT_ReadSize <= USED 0
fatDIRENT_ReadStartCluster <= USED 0
fatDIRENT_ReadTimeDate <= USED 0
fatDIRENT_WriteAttrib <= USED 0
fatDIRENT_WriteFilename <= USED 0
fatDIRENT_WriteLFilename <= USED 0
fatDIRENT_WriteSFilename <= USED 0
fatDIRENT_WriteSize <= USED 0
fatDIRENT_WriteStartCluster <= USED 0
fatDIRENT_WriteTimeDate <= USED 0
fatDIRENT_isLongName <= USED 0
fatDIRENT_isShortName <= USED 0
;FILE diskio.o
ff_test <= USED 0
ff_test0 <= USED 0
test_diskio <= USED 0
;FILE division_operations.o
WebRtcSpl_DivU32U16 <= USED 0
WebRtcSpl_DivW32W16ResW16 <= USED 0
;FILE dlGsmIpc.o
;FILE dlTemperatureDetector.o
;FILE dlaudio.o
DlAudio20msTick_HighPriority <= USED 0
DlAudioCanSlowClock <= USED 0
DlAudioGetSlowClockStatus <= USED 0
DlAudioInitialise <= USED 0
;FILE dlbggki.o
;FILE dlbgrabmdtc.o
;FILE dlbgrabmpdp.o
;FILE dlbgrabmutil.o
SendSigToUlbgTask <= USED 0
;FILE dlbgrasnmain.o
UpDlbgTask1 <= USED 0
;FILE dlbgrasnmain2.o
UpDlbg2Task1 <= USED 0
;FILE dlbgref.o
GetDlbgSndcpEntity <= USED 0
;FILE dlbgupdce.o
;FILE dlbgupde.o
;FILE dlbgupde_rb.o
DlbgUpdcpRlcSap <= USED 0
;FILE dlciphermcu.o
;FILE dlcoreirq.o
;FILE dlirqutl.o
dlDisableHwWdtInt <= USED 0
dlEnableHwSIMErrInt <= USED 0
dlEnableHwSIMInt <= USED 0
dlEnableHwSimRxDmaInt <= USED 0
dlEnableHwWdtInt <= USED 0
dlInitTdqs <= USED 0
;FILE dlmbbc.o
;FILE dlmbbcnp.o
DlBbcPowerDown <= USED 0
DlBbcPowerSave <= USED 0
DlBbcPowerUp <= USED 0
dlBbcGetPowerRampVal <= USED 0
dlBbcInitialise <= USED 0
dlReadIRxOffset <= USED 0
dlReadITxOffset <= USED 0
dlReadQRxOffset <= USED 0
dlReadQTxOffset <= USED 0
;FILE dlmnsloclk.o
;FILE dlmtone.o
;FILE dlmwdcfg.o
;FILE dlpwrsav.o
DlIsDspRequired <= USED 0
;FILE dlslow.o
DebugPrintSleepEntryTracesOren <= USED 0
DlScTriggerPMTask2MsaAfterWakeupLte <= USED 0
DlSlDsDteifFifoPollTimer <= USED 0
isSlowClockingEnabled <= USED 0
mnSpecificGSMWakeup <= USED 0
;FILE dlspdrv.o
;FILE dmacc.o
;FILE dmacccfg.o
;FILE dmaccfnc.o
DmAccExtAudioAccConfigure <= USED 0
DmAccExtAudioAccTerminate <= USED 0
DmAccHslAccConfigure <= USED 0
DmAccHslInitialise <= USED 0
DmAccHslTerminate <= USED 0
DmAccNotifyOfMicKeyChange <= USED 0
DmDaiCloseDown <= USED 0
DmDaiInitialise <= USED 0
;FILE dmadchermon.o
DmAdcHandleSigTmrExpiry <= USED 0
;FILE dmnvmfile.o
;FILE dmnvminit.o
;FILE dmnvmlcl.o
dmNvMTest <= USED 0
sendDmNvPReadCalReq <= USED 0
;FILE dmnvmtask.o
DmNvMTask1 <= USED 0
;FILE dmnvptask.o
DmNvPHandleReadCalCnf <= USED 0
DmNvPTask1 <= USED 0
;FILE dmpmdown.o
;FILE dmpmup.o
DmPmStartUpFromCharging <= USED 0
;FILE dmrtc.o
;FILE dmrtcddif.o
dmRtcCausedPowerOn <= USED 0
;FILE dmrtcfnc.o
aclkGetCurrentUnixTime <= USED 0
;FILE dmrtclcl.o
;FILE dmrtctask.o
DmRtcTask1 <= USED 0
DmRtcTaskExitRoutine <= USED 0
;FILE dmrtctmr.o
dmRtcStartTimer <= USED 0
dmRtcStopTimer <= USED 0
;FILE dmstubs.o
L1LpIsBusy <= USED 0
L1LpResetWatchVole <= USED 0
;FILE dmtask.o
DmTask1 <= USED 0
;FILE dmtimers.o
DmStopTimer <= USED 0
DmTimerChangeInaccurancy <= USED 0
;FILE dns.o
dns_get_DNSserv <= USED 0
;FILE dns_relay.o
dnsr_check_serverip <= USED 0
dnsr_get_ip4_server1 <= USED 0
dnsr_get_ip4_server2 <= USED 0
webui_set_dns_server <= USED 0
;FILE drat_coma_bsp_adap.o
dratComGetL1Version <= USED 0
dratComaBspAdapSetInfineonRfcontGpio <= USED 0
;FILE drat_coma_power_adap.o
;FILE dsp_filters.o
;FILE dtc_api.o
dtcPostF8TransferReq <= USED 0
dtcPreF8TransferReq <= USED 0
dtcResetHw <= USED 0
dtcSetDtcResetAfterRequest <= USED 0
dtcTransferReq <= USED 0
;FILE dtc_spy.o
;FILE dump.o
_Z11dump_vectorRKN7android6VectorIjEEPKc <= USED 0
_Z23dump_frame_info_verbosebRK9MediaInfoPKcS3_ <= USED 0
;FILE duster_common.o
ConvertASCToChar <= USED 0
ConvertASCtoString <= USED 0
ConvertIntegertoString <= USED 0
ConvertStrToChar <= USED 0
ConvertStrToInteger64 <= USED 0
ConvertV6IntegertoString <= USED 0
SimRemove_cb <= USED 0
clear_pdp_list <= USED 0
duster_at_tok_nextint <= USED 0
duster_auto_select_netwrok <= USED 0
enableAutoTimeZone <= USED 0
expr <= USED 0
free_link_status <= USED 0
getBindSimId <= USED 0
getMncLen <= USED 0
get_china_sim_operator <= USED 0
get_flash_block_size <= USED 0
get_mccmnc <= USED 0
get_ntp_time_type <= USED 0
get_qci_status <= USED 0
get_rx_byte <= USED 0
get_rx_byte_all <= USED 0
get_sim_iccid <= USED 0
get_sys_mode <= USED 0
get_tx_byte <= USED 0
get_tx_byte_all <= USED 0
htonl <= USED 0
htons <= USED 0
isAutoTimeZone <= USED 0
isDualUsbModem <= USED 0
is_mcc_mnc_rau <= USED 0
is_pdp_connected <= USED 0
sendCFUNMsg <= USED 0
sendSetRemapFlagMsg <= USED 0
sendUartDataToBindChannel <= USED 0
setBindSimId <= USED 0
setUsbModemBindChannel <= USED 0
set_ntp_time_type <= USED 0
set_qci_status <= USED 0
sim_check_wrong_msg <= USED 0
wan_free_MncApn <= USED 0
wan_get_apn_iptype_change_type <= USED 0
;FILE duster_dongle.o
;FILE dvfmApi.o
;FILE dvfmDebug.o
DVFMChangePP_stub <= USED 0
callPendingReqOnDown <= USED 0
callPendingReqOnUp <= USED 0
;FILE dvfmManager.o
;FILE dvfmSpy.o
;FILE dvfmStateMachine.o
;FILE eLoop_test.o
;FILE ebndcfg.o
EbndCombinationFddAndTddBands <= USED 0
EbndDoEutraBandsDlOverlap <= USED 0
EbndGetOffsetInBandForEarfcn <= USED 0
EbndIsEarfcnInStoredList <= USED 0
;FILE egi_bind.o
egiBindGetFujitsuAFCValues <= USED 0
;FILE emacbgctl.o
;FILE emaccf.o
;FILE emacdl.o
;FILE emacl1rx.o
MacRxEgprsSdsDataInd <= USED 0
;FILE emacl1seq.o
;FILE emacl1tx.o
;FILE emacl1utl.o
;FILE emacmain.o
GpMacTask1 <= USED 0
;FILE emacra.o
;FILE emacsig.o
KiLteL2SendFreqIntSignal <= USED 0
KiLteL2SendFreqSignal <= USED 0
;FILE emacul.o
LteMacMemoryCheck <= USED 0
LtePdcpSetLochData <= USED 0
;FILE emacut.o
LteMacGetL2Stat <= USED 0
LteMacutCheckUlDataThroughput <= USED 0
LteMacutDumpUlDataFromIPC <= USED 0
LteMacutGsmVoiceCallInform <= USED 0
LteMacutRecordIPCStatsData <= USED 0
LteMacutTraceIPCStatsData <= USED 0
isOneSubframePeriod <= USED 0
utCommUdpChecksum <= USED 0
;FILE emacutil.o
;FILE emm_errc.o
EmmEncodeDecode <= USED 0
;FILE emm_esm.o
;FILE emm_mmr.o
;FILE emm_psms.o
;FILE emm_rxmsg.o
;FILE emm_security.o
;FILE emm_sim.o
;FILE emm_timer.o
;FILE emm_txmsg.o
;FILE emm_utils.o
EmmCheckIfMmrNregIndNeeded <= USED 0
EmmModeSwitch <= USED 0
EmmReduceServiceType <= USED 0
EmmSendMmsiStoreLocationInfoReq <= USED 0
;FILE enc.o
smipEncodeMO3gpp2SMS <= USED 0
smipEncodeMO3gppSMS <= USED 0
;FILE epdcp.o
LteBlockAlloc <= USED 0
LteBlockRelease <= USED 0
LteDeleteUlBlockStub <= USED 0
tLtePdcpTask <= USED 0
;FILE epdcpcipherandintegrity.o
LteChangeTcpWnd <= USED 0
LteDtcCh0InfoInit <= USED 0
LteHandlePdicHisrCh0BeforeY0 <= USED 0
LteHisrConstructRlcDataListCh1 <= USED 0
LteHisrConstructRrcDataInd <= USED 0
LteHisrConstructRrcIntegrityCalcCnf <= USED 0
LteHisrConstructRrcIntegrityCheckErrorInd <= USED 0
LteHisrConstructSnDataInd <= USED 0
LteHisrConstructSnDataReq <= USED 0
LteHisrHandleSrbDataInd <= USED 0
LteIpChksumAdjust <= USED 0
LtePdcpDlTraceIpId <= USED 0
LtePdcpEia <= USED 0
LtePdcpReUpdateSrbDecipherInfo <= USED 0
LtePdcpReverseMacI <= USED 0
LtePdcpSrbSoftEea <= USED 0
LtePdcpUpdataDeintegrityInfo <= USED 0
LtePdcpUpdateSrbDecipherInfo <= USED 0
;FILE epdcplinklist.o
LtePdcpDeleteDlNsduInfo <= USED 0
LteTraceDataList <= USED 0
;FILE epdcprlcsap.o
LtePdcpAddSnDataIndLList <= USED 0
LtePdcpConstructIpDlDataIndNode <= USED 0
LtePdcpConstructIpDlDataIndNodeForLoop <= USED 0
LtePdcpConstructRrcIntegrityCheckErrorInd <= USED 0
LtePdcpConstructSnUlDataReqNode <= USED 0
LtePdcpFreeDlIpMem <= USED 0
LtePdcpMaciCompare <= USED 0
LtePdcpRlcTriggerPdcpReportInd <= USED 0
LtePdcpTriggerStatisticReportInd <= USED 0
;FILE epdcprrcsap.o
LtePdcpAddPduHeaderForRrcData <= USED 0
;FILE epdcpupsap.o
LtePdcpAddPduHeaderForUserData <= USED 0
LtePdcpHandleUlPdicDoneInd <= USED 0
LtePdcpSetUlPdcpDataLengthToZero <= USED 0
LtePdcpSnDataReq <= USED 0
LtePdcpSubUlPdcpDataLength <= USED 0
LtePdcpUlDiscardSnSdu <= USED 0
enablePdcpDiag <= USED 0
;FILE err.o
;FILE esmab.o
SendAbgpSmApnReadRsp <= USED 0
SendEsmRegPdpActivateFailureInd <= USED 0
;FILE esmdevfeature.o
HandleEsmActDedEPSBearerContextReq <= USED 0
HandleEsmSnsmActivateRsp <= USED 0
;FILE esmemm.o
EsmSendNotificationABCC <= USED 0
;FILE esmmain.o
;FILE esmreg.o
SendSmRegPdpActivateInd <= USED 0
SendSmregPdpModInd <= USED 0
;FILE esmsn.o
;FILE esmtimer.o
;FILE esmutil.o
CheckPfid <= USED 0
ConvertIntToLteAttachType <= USED 0
EsmComparePdpAddresses <= USED 0
EsmQosAcceptable <= USED 0
epsQosConvertToQos <= USED 0
;FILE esp.o
;FILE etharp.o
etharp_add_static_entry <= USED 0
etharp_del_arp_cb <= USED 0
etharp_find_addr <= USED 0
etharp_remove_static_entry <= USED 0
ip_mac_stat_bytes <= USED 0
lwip_wan_delete_device <= USED 0
mac_header_validcheck <= USED 0
;FILE ethip6.o
;FILE export_tasks.o
Task2_Main <= USED 0
;FILE fat.o
fatFAT_Area <= USED 0
fatFAT_BytesFree <= USED 0
fatFAT_BytesUsed <= USED 0
fatFAT_ClusterToSector <= USED 0
fatFAT_EnterProtectedArea <= USED 0
fatFAT_EnterUserArea <= USED 0
fatFAT_LinkCluster <= USED 0
fatFAT_NextFreeCluster <= USED 0
fatFAT_PreviousCluster <= USED 0
fatFAT_ReadCurrentDir <= USED 0
fatFAT_ReadFAT <= USED 0
fatFAT_ReadPartitionBootSector <= USED 0
fatFAT_Release <= USED 0
fatFAT_SectorToCluster <= USED 0
fatFAT_SectorsToSearch <= USED 0
fatFAT_WriteCurrentDir <= USED 0
fatFAT_WriteFAT <= USED 0
;FILE fat_os_api.o
;FILE fatdir.o
fatDIR_ChDir <= USED 0
fatDIR_FullPath <= USED 0
fatDIR_GetCWD <= USED 0
fatDIR_MakePath <= USED 0
fatDIR_MkDir <= USED 0
fatDIR_RmDir <= USED 0
fatDIR_SplitPath <= USED 0
;FILE fatwk_psm.o
FDI_fread_psm <= USED 0
FDI_fseek_psm <= USED 0
FDI_fwrite_psm <= USED 0
PSM_EraseAll <= USED 0
PSM_GetDefaultPSMAddress <= USED 0
PSM_ReadFlash <= USED 0
PSM_WriteFlash <= USED 0
get_psm_data_addr <= USED 0
psm_read_flash <= USED 0
;FILE feedback_create.o
;FILE feedback_parse.o
;FILE ff.o
f_chmod <= USED 0
f_getcwd <= USED 0
f_lseek <= USED 0
f_read <= USED 0
f_rename <= USED 0
f_stat <= USED 0
f_truncate <= USED 0
f_utime <= USED 0
ff_dot_at_end_config <= USED 0
ffst_print_all_file <= USED 0
ffst_read_end_status <= USED 0
ffst_read_start_status <= USED 0
;FILE ff_dump_mem.o
;FILE ffsystem.o
;FILE ffunicode.o
;FILE find.o
fatFIND_Cluster <= USED 0
fatFIND_Delete <= USED 0
fatFIND_Filename <= USED 0
fatFIND_Object <= USED 0
fatFIND_Objects <= USED 0
fatFIND_Rename <= USED 0
toupperEx <= USED 0
;FILE fota_interface.o
Fota_LCD_DisplayText <= USED 0
Fota_display_init <= USED 0
ui_fota_bind <= USED 0
ui_notify_curr_download_percent <= USED 0
ui_notify_restart <= USED 0
ui_notify_result <= USED 0
ui_notify_version <= USED 0
;FILE fota_part.o
;FILE fota_psram.o
;FILE fota_sdsys.o
FotaRamBlockAdvance <= USED 0
FotaRamBlockDelete <= USED 0
FotaRamBlockDeleteList <= USED 0
FotaRamBlockFindPrevious <= USED 0
FotaRamBlockInsert <= USED 0
FotaRamBlockIsEmpty <= USED 0
FotaRamBlockIsLast <= USED 0
FotaRamBlockListFirst <= USED 0
FotaRamBlockListHeader <= USED 0
FotaRamBlockRetrieve <= USED 0
PrintFotaRamBlockList <= USED 0
fota_get_block_from_sector <= USED 0
fota_get_offset_from_sector <= USED 0
fota_sector_to_blk <= USED 0
fota_sem_lock_check <= USED 0
;FILE fsm_app.o
fsmAppGetCurrentEvent <= USED 0
fsmAppProcessEvent <= USED 0
fsmAppRelease <= USED 0
fsmAppSetTraceInfo <= USED 0
fsmGetVersion <= USED 0
;FILE fstdio_wrap.o
WRAP_fclose <= USED 0
WRAP_fopen <= USED 0
WRAP_fprintf <= USED 0
WRAP_fread <= USED 0
WRAP_fwrite <= USED 0
fstdio_bindDefault <= USED 0
;FILE gba.o
;FILE gbndcfg.o
GbndGetMaxArfcn1ForGsmBand <= USED 0
GbndGetMaxArfcn2ForGsmBand <= USED 0
GbndGetMinArfcn1ForGsmBand <= USED 0
GbndGetMinArfcn2ForGsmBand <= USED 0
GbndIsAccessTechnologySupported <= USED 0
GbndIsArfcnInBaList <= USED 0
GbndIsArfcnInGsmBands <= USED 0
GbndIsGsmBandHigh <= USED 0
;FILE gcerror.o
;FILE gm_rxmsg.o
;FILE gm_timer.o
GmmStartPsConEstGuardTimer <= USED 0
;FILE gm_txmsg.o
;FILE gm_utils.o
GmmGetPtimsiMobileId <= USED 0
GmmNewServiceType <= USED 0
;FILE gmm_llc.o
;FILE gmm_mmr.o
GmmRpmCheckRegReq <= USED 0
;FILE gmm_rd.o
;FILE gmm_rr.o
;FILE gmm_sm.o
;FILE gpdec_ie.o
GpSkipUtranFddNcellParam <= USED 0
;FILE gpdecgrr.o
GpDecPsi4ChannelGroup <= USED 0
GpDecPsiType4 <= USED 0
;FILE gpdecrlc.o
GpDecPacketDownlinkAssignmentBody <= USED 0
GpDecPacketTimeslotRecfg <= USED 0
GpDecPacketUplinkAssignmentBody <= USED 0
;FILE gpdecut.o
NbOneBit <= USED 0
;FILE gpedcmpr.o
;FILE gpenc_ie.o
GpEncEutranMeasurementReport <= USED 0
;FILE gpencrlc.o
GpEncPacketPsiStatus <= USED 0
;FILE gplc_version.o
gplcModemVersionPrint <= USED 0
gplcVersionPrint <= USED 0
;FILE gprsal.o
GPRSAL_ActivateDedicatedBearer <= USED 0
GPRSAL_DeactivateDedicatedBearer <= USED 0
GPRSAL_DelBearer <= USED 0
GPRSAL_GetAllActivePDPCid <= USED 0
GPRSAL_GetAllBearerState <= USED 0
GPRSAL_GetAllDefaultBearer <= USED 0
GPRSAL_GetBearerAddress <= USED 0
GPRSAL_GetCASIMS <= USED 0
GPRSAL_GetCAVIMS <= USED 0
GPRSAL_GetCGCLASS <= USED 0
GPRSAL_GetCPIN <= USED 0
GPRSAL_GetCREG <= USED 0
GPRSAL_GetCapsNegQOS <= USED 0
GPRSAL_GetMinUQOS <= USED 0
GPRSAL_ModifyBearer <= USED 0
GPRSAL_SetCEN <= USED 0
GPRSAL_SetCMEE <= USED 0
GPRSAL_SetCREG <= USED 0
GRPSAL_GETBANDIND <= USED 0
GetNegDedicatedBearer <= USED 0
;FILE gprsal_interface.o
configInterface <= USED 0
disableDAD <= USED 0
getInterfaceAddr <= USED 0
isInterfaceUp <= USED 0
;FILE grr.o
GrrIsCmccNetwork <= USED 0
GrrTask1 <= USED 0
GrrTask21 <= USED 0
;FILE grrbcch.o
GrrStoreRtdDescription <= USED 0
;FILE grrcidle.o
GrrIdleCheckFddPredefConfig <= USED 0
GrrIdleNextFddCellIndex <= USED 0
GrrIdleProcessScellSi2qua <= USED 0
GrrIdleStorePscToFddNcellList <= USED 0
;FILE grrcom.o
GrrCheckIfMncInRequestedPlmn <= USED 0
GrrComAlignFddMeas <= USED 0
GrrComCellSelectionReqd <= USED 0
GrrComMultiBcchAbort <= USED 0
GrrSetAllMultibandBits <= USED 0
GrrSetDfltPrimaryBand <= USED 0
GrrSetTxDiversityIndic <= USED 0
;FILE grrcrel.o
;FILE grrcsel.o
CsGetBandModeForPerformingStoredListSelection <= USED 0
CsSaveOrUseStoredArfcnList <= USED 0
GrrAndSearchChannelLists <= USED 0
;FILE grrdch.o
;FILE grrds.o
GrrDsGetTheOtherSimReselectionState <= USED 0
GrrDsIsMtEstabOnGoing <= USED 0
GrrDsIsRrCellSearchingSetByCurrentTask <= USED 0
GrrDsIsSimbActive <= USED 0
GrrDsIsTheOtherSimIdleAndNotReseletion <= USED 0
GrrDsIsTheOtherSimIdleAndReseletion <= USED 0
GrrDsSaveGrrEstReq <= USED 0
GrrSendGrrDsSuspendReq <= USED 0
GrrSendGrrPowerOffCompleteInd <= USED 0
GrrSendMphDsStartPchReq <= USED 0
;FILE grrflai.o
;FILE grrgpcom.o
GrrCalcNumRlcBlocks <= USED 0
;FILE grrgpsig.o
;FILE grrirat4g.o
GrrActivateOrderedGprsMeas4g <= USED 0
GrrCalcRsrqSixBitsMapping <= USED 0
GrrDeleteIndividualPriorities <= USED 0
GrrHdl_rx_reselect_to_Umts_CNF_todo <= USED 0
GrrIratActionAfterTriedOneIratNcell_todo <= USED 0
GrrPktProcessIdleReselParamReselGsmNcell_old <= USED 0
GrrPrioReselCheckIfTryGsmNcell <= USED 0
GrrPrioReselCheckIfTryGsmNcell_todo <= USED 0
GrrReselAlgChangeConvert3gMeasResult <= USED 0
Grr_func <= USED 0
LteBandGetLteBandIndexForEuarfcn <= USED 0
PidleHdlEutraScellInfoInd <= USED 0
PlmnListProcessSrchReselParamReselGsmNcell <= USED 0
;FILE grrirr.o
;FILE grrl1sig.o
GrrSendExtMeasReq <= USED 0
GrrSendFddRssiScanCnf <= USED 0
GrrSendMphTxPowerBackOffReq <= USED 0
GrrSendUmphSwitchToGsmReq <= USED 0
GrrSetMacTestModeParams <= USED 0
;FILE grrl2est.o
;FILE grrl3.o
;FILE grrmeas.o
;FILE grrncell.o
GrrNcellCheckFddNcell <= USED 0
GrrNcellCreateC32OrderedReselList <= USED 0
GrrNcellIsInRachFailBarredList <= USED 0
GrrNcellReloadPbcch <= USED 0
;FILE grrpbcch.o
GrrDecodePsi1Header <= USED 0
;FILE grrpdch.o
;FILE grrpidle.o
;FILE grrpl3.o
GrrConstructMsRadAccCapab <= USED 0
;FILE grrplmn.o
GrrCheckIfFddMccInSearchedPlmn <= USED 0
GrrConvertLtePlmnId <= USED 0
PlmnListCalcUarfcnsRawGradesOffsets <= USED 0
PlmnListCopyLtePlmnList <= USED 0
;FILE grrprach.o
;FILE grrpssig.o
GrrPlmnSetLteBandsToScan <= USED 0
GrrSendGrrRcGsmPlmnListIndSinglePlmn <= USED 0
GrrSendIrrGetLteSibsCnf <= USED 0
;FILE grrrach.o
;FILE grrtim.o
GrrTimestamp1IsLarge <= USED 0
;FILE grrwifi.o
;FILE gsmPwronoff_Stub.o
gwiSendGsmTerminateCnf_stub <= USED 0
;FILE gsmdssp.o
SSPSetField <= USED 0
drCommonRfSSPInit <= USED 0
drInitSSPForIQPort <= USED 0
drInitSSPForSynth <= USED 0
drInitSSPForTxPort <= USED 0
;FILE guilin.o
GuilinDisableWDT <= USED 0
Guilin_VBUCK1_Set_FPWM <= USED 0
Guilin_VBUCK_Set_DVC_Enable <= USED 0
Guilin_VsimSleep_Disable <= USED 0
Guilin_VsimSleep_Enable <= USED 0
Guilin_miccoConfigUsimV <= USED 0
Guilin_miccoDisableUsimV <= USED 0
Guilin_miccoEnableUsimV <= USED 0
guilin_read_volt_meas_val <= USED 0
;FILE hacomtsk.o
HaCommsTask1 <= USED 0
;FILE hadsimhd.o
hadSetUiccTransmissionProtocolT0Only <= USED 0
;FILE hadsimmn.o
L1SimDriverTask1 <= USED 0
L1SimDriverTask21 <= USED 0
;FILE hafailcf.o
di_LockDisplay <= USED 0
;FILE hagbgtsk.o
HagDetermineArfcnListBitMask <= USED 0
L1BackGroundTask1 <= USED 0
L1BackGroundTask21 <= USED 0
hagCheckL1bgTaskStack <= USED 0
hagUpdateState0 <= USED 0
hagUpdateState1 <= USED 0
;FILE hagcb.o
hagUnBindBgCallbackFunctions <= USED 0
hawSendMphHandoverCnf <= USED 0
plgCalDevGsmBurstCnf <= USED 0
plgCalDevGsmCnf <= USED 0
plgCalDevGsmFinishCnf <= USED 0
plgCalDevGsmFrameDefineCnf <= USED 0
plgCalDevGsmFrameTimingCnf <= USED 0
plgCalDevGsmFrameUseCnf <= USED 0
plgCalDevGsmFreqOffsetMeasCnf <= USED 0
plgCalDevGsmGainProgramCnf <= USED 0
plgCalDevGsmLoopBackCnf <= USED 0
plgCalDevGsmLoopBackDataCnf <= USED 0
plgCalDevGsmRampScaleCnf <= USED 0
plgCalDevGsmRssiCnf <= USED 0
plgCalDevGsmRxControlCnf <= USED 0
plgCalDevGsmSetAfcDacCnf <= USED 0
plgCalDevGsmSetBandModeCnf <= USED 0
plgCalDevGsmSetBurstDataCnf <= USED 0
plgCalDevGsmSetPowerRampCnf <= USED 0
plgCalDevGsmSlotDefineCnf <= USED 0
plgGrrCbControlCnf <= USED 0
plgLowPriorityTask <= USED 0
;FILE hagphtsk.o
L1PhTask1 <= USED 0
;FILE hal_init.o
ui_get_udid <= USED 0
ui_init_phase1 <= USED 0
ui_set_udid <= USED 0
;FILE haw_stub_ds3.o
hawValidUarfcnIsValid <= USED 0
;FILE hexdump.o
_ZN14streamingmedia7hexdumpEPKvjjPNS_7AStringE <= USED 0
;FILE http_parser.o
http_body_is_final <= USED 0
http_method_str <= USED 0
http_parser_pause <= USED 0
http_parser_version <= USED 0
;FILE httpcon.o
;FILE httpencdec.o
Http_GetParam <= USED 0
;FILE icmp.o
;FILE icmp6.o
;FILE idletask.o
idleHookBind <= USED 0
idleInit <= USED 0
;FILE imsCapApi.o
;FILE imsConfEvent.o
;FILE imsMediaFsm.o
hdlCreateInRemoved <= USED 0
hdlRemovedInWFRnReady <= USED 0
imsRTPSession_SessionFeedback <= USED 0
;FILE imsSessApi.o
IMS_GetSessionInfo <= USED 0
IMS_SendChatMsg <= USED 0
IMS_SetReferToUser <= USED 0
;FILE imsSessConf.o
;FILE imsSessEarlyMedia.o
;FILE imsSessEvents.o
ims_FindSessEventDataSize <= USED 0
ims_PostMessageEvent2App <= USED 0
ims_SessHdlBitrateEvent <= USED 0
ims_SessPostMediaConnLostEvent <= USED 0
ims_SessPostMediaStartEvent <= USED 0
ims_SessPostRtpRtcpInactivityEvent <= USED 0
;FILE imsSessFsm.o
;FILE imsSessFsmModify.o
hdlOptionsStackInDialog <= USED 0
;FILE imsSessFsmOrig.o
hdlTerminateAppInResendInvite <= USED 0
;FILE imsSessFsmTerm.o
;FILE imsSessUtils.o
ImagePush_FileHash <= USED 0
ims_FreeBWInfoList <= USED 0
ims_FreeMediaStreamCapsMembers <= USED 0
ims_GetMediaIdFromMSRPId <= USED 0
ims_IsMediaDirUpgraded <= USED 0
ims_UpdateUserListForNewFork <= USED 0
ims_UpdateVideoCapsForDirChange <= USED 0
ims_findMediaIndexByMediaSessHd <= USED 0
;FILE imsUICC.o
IMSUICC_DeriveCarrierFromIMSI <= USED 0
;FILE imsUSSIFsmFunc.o
;FILE imsUssiXmlInterpreter.o
;FILE ims_Media.o
Media_AudioModeSet <= USED 0
Media_GetSessionCodecConfig <= USED 0
Media_GetSessionConfig <= USED 0
Media_LinkAVSessions <= USED 0
RetreiveVideoBandwidth <= USED 0
_ZN18MediaEventListenerC1Ev <= USED 0
media_DirIdToString <= USED 0
media_VideoDefaultLevelDimension <= USED 0
media_VideoPlatformId2String <= USED 0
;FILE ims_api.o
IMS_CallDivert <= USED 0
IMS_CallTransfer <= USED 0
IMS_Modify <= USED 0
IMS_QueryCallCaps <= USED 0
IMS_QueryFirstAvailableNetwork <= USED 0
IMS_QueryFirstCall <= USED 0
IMS_QueryNetwork <= USED 0
IMS_QueryNextAvailableNetwork <= USED 0
IMS_QueryNextCall <= USED 0
IMS_Reject <= USED 0
IMS_ReloadConfig <= USED 0
IMS_SMS_CancelOperation <= USED 0
IMS_Set_Auto_Answ <= USED 0
;FILE ims_api_psram.o
;FILE ims_main.o
;FILE ims_syssocket.o
SysSocket_Attach <= USED 0
SysSocket_GetIoInterface <= USED 0
SysSocket_GetNameInfo <= USED 0
SysSocket_GetPeerName <= USED 0
SysSocket_GetSockName <= USED 0
SysSocket_Inet_Pton2 <= USED 0
ims_set_at_channel <= USED 0
;FILE ims_test_main.o
ASSERT_EQ_imp <= USED 0
ASSERT_NE_imp <= USED 0
;FILE ims_ua_ipsec.o
;FILE ims_uaclient.o
SIPUA_GetFqdn <= USED 0
;FILE imsd.o
;FILE inet6.o
;FILE inet_chksum.o
;FILE init.o
BBU_UART_Open <= USED 0
BBU_puthexd <= USED 0
initGPIO <= USED 0
setGPIO121_HIGH <= USED 0
setGPIO121_LOW <= USED 0
setGPIO122_HIGH <= USED 0
setGPIO122_LOW <= USED 0
setGPIO60_HIGH <= USED 0
setGPIO60_LOW <= USED 0
setGPIO79_HIGH <= USED 0
setGPIO79_LOW <= USED 0
setGPIO81_HIGH <= USED 0
setGPIO81_LOW <= USED 0
setGPIO82_HIGH <= USED 0
setGPIO82_LOW <= USED 0
setGPIO83_HIGH <= USED 0
setGPIO83_LOW <= USED 0
setGPIO84_HIGH <= USED 0
setGPIO84_LOW <= USED 0
;FILE initTaskUtil.o
DSPResetAck <= USED 0
Delay <= USED 0
InitHSITimers <= USED 0
PMIC8211workaroundForFirstLDOoperation <= USED 0
PeripheralInits <= USED 0
StartDiagThroughPutTimer <= USED 0
SyncTimerInit <= USED 0
TCMStartFITimer <= USED 0
dumpTimerRegs <= USED 0
hwAcs_IPC_REG__READ <= USED 0
sendTestIPCcommand <= USED 0
timer_monitor <= USED 0
;FILE initatcmdsvr.o
initATCmdSvr <= USED 0
initATCmdSvr_Thread <= USED 0
;FILE intc_memRetain.o
INTCEnableInterruptOutput <= USED 0
;FILE intc_xirq.o
GPIOClearUsedForInterrupt <= USED 0
GPIOInterruptClear <= USED 0
GPIOSetUsedForInterrupt <= USED 0
Get_ICU_INTC_STATUS_0 <= USED 0
Get_ICU_INTC_STATUS_1 <= USED 0
INTCConfigurationGet <= USED 0
INTCConfigurationGet_XIRQ <= USED 0
INTCD2rmD2Register <= USED 0
INTCDisableAllInts <= USED 0
INTCGetIntVirtualNum <= USED 0
INTCISRGet <= USED 0
INTCISRGet_XIRQ <= USED 0
INTCIdleMaskDisable <= USED 0
INTCIdleMaskEnable <= USED 0
INTCInterruptHandlerFIQ <= USED 0
INTCInterruptHandlerFIQ_XIRQ <= USED 0
INTCInterruptHandlerIRQ <= USED 0
INTCPhase1Init <= USED 0
INTCPhase1Init_XIRQ <= USED 0
INTC_P_ICU_ConfigurationGet <= USED 0
INTC_P_ICU_Init <= USED 0
XIRQ_init <= USED 0
;FILE interval.o
rohc_interval_get_rfc5225_id_id_p <= USED 0
rohc_interval_get_rfc5225_msn_p <= USED 0
;FILE intf_api.o
;FILE ip.o
;FILE ip4.o
ip_route <= USED 0
;FILE ip4_addr.o
ip4_addr_netmask_valid <= USED 0
;FILE ip6.o
ip6_route <= USED 0
ip6_route_opt <= USED 0
;FILE ip6_addr.o
ip6addr_print_withnote <= USED 0
;FILE ip6_frag.o
;FILE ip_frag.o
ip_frag <= USED 0
;FILE ip_nat.o
ip_nat_table_getby_index <= USED 0
ip_port_fwd_rule_add <= USED 0
ip_port_fwd_rules <= USED 0
;FILE ip_numbers.o
rohc_get_ip_proto_descr <= USED 0
;FILE ipnetbuf.o
ACIPC_PS_uplink_indication <= USED 0
IpNetLwipGetUlPacketPoll <= USED 0
ipnet_uplink_indication <= USED 0
ipnet_uplink_resume <= USED 0
;FILE ippcDES_emb.o
CopyBlock24 <= USED 0
CopyBlock32 <= USED 0
CopyBlock8 <= USED 0
FillBlock24 <= USED 0
FillBlock32 <= USED 0
FillBlock8 <= USED 0
SetKey_DES <= USED 0
XorBlock <= USED 0
XorBlock24 <= USED 0
XorBlock32 <= USED 0
XorBlock8 <= USED 0
ippsDESBufferSize <= USED 0
ippsDESDecryptCBC <= USED 0
ippsDESDecryptCFB <= USED 0
ippsDESDecryptECB <= USED 0
ippsDESDecrypt_I <= USED 0
ippsDESEncryptCBC <= USED 0
ippsDESEncryptCFB <= USED 0
ippsDESEncryptECB <= USED 0
ippsDESEncrypt_I <= USED 0
ippsDESInit <= USED 0
ippsTDESDecryptCBC <= USED 0
ippsTDESDecryptCFB <= USED 0
ippsTDESDecryptECB <= USED 0
ippsTDESDecrypt_I <= USED 0
ippsTDESEncryptCBC <= USED 0
ippsTDESEncryptCFB <= USED 0
ippsTDESEncryptECB <= USED 0
ippsTDESEncrypt_I <= USED 0
;FILE ippcRIJ128.o
ippsRijndael128DecryptCFB <= USED 0
ippsRijndael128DecryptECB <= USED 0
ippsRijndael128EncryptCFB <= USED 0
ippsRijndael128EncryptECB <= USED 0
;FILE ippcRIJTools.o
;FILE ipsec.o
;FILE ipsec_aes.o
;FILE ipsec_des.o
;FILE ipsec_md5.o
;FILE ipsec_sha1.o
;FILE ipsec_util.o
ipsec_inet_addr <= USED 0
ipsec_inet_aton <= USED 0
ipsec_inet_ntoa <= USED 0
ipsec_print_ip <= USED 0
;FILE ipsecdev.o
ipsec_set_tunnel <= USED 0
ipsecdev_netlink_output <= USED 0
ipsecdev_service <= USED 0
;FILE kinu.o
KiPoolBlockInUse <= USED 0
;FILE kiosfail.o
warnAssertPrint <= USED 0
warnCheckParam <= USED 0
;FILE kiosinit.o
KiOsSystemIsInitialised <= USED 0
;FILE kioslow.o
KiOsIsMemorySignal <= USED 0
;FILE kiosmem.o
KiOsAllocPollMemory <= USED 0
KiOsResizeMemory <= USED 0
;FILE kiosq.o
KiOsFlushBlockQueue <= USED 0
;FILE kiossem.o
KiOsIncIntSemaphore <= USED 0
KiOsPollSemaphore <= USED 0
;FILE kiossig.o
KiOsReceiveNonTraceSignal <= USED 0
KiOsRequestSignal <= USED 0
KiOsRequestZeroSignal <= USED 0
KiOsSendNonTraceIntSignal <= USED 0
KiOsSendSignalPoll <= USED 0
;FILE kiosstat.o
KiCheckStackOverFlow <= USED 0
KiSendDevAssertInd <= USED 0
KiSendDevCheckInd <= USED 0
KiSendDevFailInd <= USED 0
KiSendDumpMemReq <= USED 0
;FILE kiostask.o
KiGetErrnoAddr <= USED 0
KiOsIsSim1Task <= USED 0
;FILE kiostim.o
KiOsRestartTimer <= USED 0
KiTimGetActiveListHeadAddr <= USED 0
KiTimGetCurrentTickTimeAddr <= USED 0
KiTimGetTimerArrayAddr <= USED 0
;FILE kiostti.o
KiOsGetTheFreeHeaderAddr <= USED 0
KiTtiFilterLogSignal <= USED 0
KiTtiGetFirstLoggedSignal <= USED 0
KiTtiGetNextLoggedSignal <= USED 0
KiTtiProcessReceivedSignal <= USED 0
;FILE kiosvole.o
KiOsWatchVoleCount <= USED 0
KiOsWatchVoleUpdate <= USED 0
;FILE kiprintf.o
_printf <= USED 0
vprintf <= USED 0
;FILE kmdynmem.o
KmMemoryCreatePool <= USED 0
KmMemoryFree <= USED 0
KmMemoryGet <= USED 0
KmMemoryGetFail <= USED 0
KmMemoryGetSize <= USED 0
KmMemoryReSize <= USED 0
KmMemoryWalk <= USED 0
KmMemoryWalkStart <= USED 0
;FILE l1_default_diag_filter.o
;FILE l1a_cal.o
GetLtePmaxReductionFlag <= USED 0
L1aGetCalibrationModeState <= USED 0
L1aHandleAdditionalMprTableData <= USED 0
L1aHandleDcxoCalData <= USED 0
L1aHandleLteQtEtFreqAdjTableData <= USED 0
L1aHandleRfConfigTableData <= USED 0
L1aSendDefaultTableWithZero <= USED 0
L1aTempReadingCallback <= USED 0
getTxPowerBackoffMode <= USED 0
;FILE l1a_drat.o
L1aHandleL1cSetLteCnf <= USED 0
L1aMultiIratL1GetRAT <= USED 0
L1aSetL1aDspDuringSleep <= USED 0
L1aSetSimWbActivated <= USED 0
plDratsendGsmMeasureParams <= USED 0
;FILE l1a_dsds.o
L1aDsdsHandleL1aReselectToTddUtraReq <= USED 0
L1aSendDsWbDeactInd <= USED 0
L1aSendSchdGsmGapAbortReq <= USED 0
L1aSendSim2ActivateInd <= USED 0
L1aSendSim2DeactivateInd <= USED 0
gliDsdsGsmUrgentReq <= USED 0
gliIratcurrentProcedure <= USED 0
teiReselectToLteReq <= USED 0
weiReselectToUtraFddReq <= USED 0
wliDsdsLteIsSleepState <= USED 0
wliDsdsRcvLtePchInWbPsReq <= USED 0
wliDsdsResumeLteReq <= USED 0
wliDsdsSchdLteGapInd <= USED 0
wliDsdsSchdWbGapAbortCnf <= USED 0
wliDsdsSchdWbGapCanCelInd <= USED 0
wliDsdsSchdWbGapFinishedInd <= USED 0
wliDsdsSchdWbGapReq <= USED 0
wliDsdsSuspendLteReq <= USED 0
wliDsdsWbActivateInd <= USED 0
wliDsdsWbDeactivateInd <= USED 0
wliDsdsWbEarlyWakeUpReq <= USED 0
wliDsdsWbSleepReq <= USED 0
;FILE l1a_dsds_lwi_stub.o
lwiDsdsSchdWbGapAbortReq <= USED 0
lwiDsdsWbActivateRsp <= USED 0
lwiDsdsWbDeactivateRsp <= USED 0
;FILE l1a_irat.o
L1aHandleRssiScanReqInForegroundReadLteBchInFddUtra <= USED 0
L1aHandleSetLteCnfDuringHoToFddUtraFail <= USED 0
L1aHandleSetLteCnfDuringHoToTddUtraFail <= USED 0
L1aHandleTddUtraNcellMeasInd <= USED 0
L1aStoppingTddUtraBchReading <= USED 0
L1aSwapLteMeasInGsm <= USED 0
L1cCalcGsmBsicFrameNQbitOffsets <= USED 0
etiReselectToLteReq <= USED 0
gsmBsicMeasInfoIsPlmn <= USED 0
gtiL1SetRatSetCause <= USED 0
teiLteNcellBchReq <= USED 0
teiLteNcellBchStopReq <= USED 0
teiLteTdNcellBchStopCnf <= USED 0
teiReselectToLteCnf <= USED 0
teiReselectToTddUtraReq <= USED 0
teiSetL1aCurrentModeToGsm <= USED 0
teiTdNcellBchInd <= USED 0
teiTdNcellBchStopCnf <= USED 0
tgiGetGsmTimingParams <= USED 0
weiFddNcellBchInd <= USED 0
weiFddNcellBchStopCnf <= USED 0
weiHandoverToLteInd <= USED 0
weiLteFddNcellBchStopCnf <= USED 0
weiLteNcellBchReq <= USED 0
weiLteNcellBchStopReq <= USED 0
weiReselectToLteCnf <= USED 0
weiSwitchRatToLteCnf <= USED 0
weiSwitchRatToUmtsCnf <= USED 0
;FILE l1a_irat_stub.o
;FILE l1a_main.o
L1aSetLteCalQtEtFreqAdjTableDataShareMemoryAddress <= USED 0
L1aSetLteCalRfConfigTableDataShareMemoryAddress <= USED 0
;FILE l1a_meas.o
L1aAdaptForEcphyInterFreqInfoReq <= USED 0
L1aAdaptForEcphyIntraFreqInfoReq <= USED 0
L1aAdaptForEcphyMonitorInterFreqCellReq <= USED 0
;FILE l1a_phy.o
L1aAdaptForEcphyHandoverReq <= USED 0
L1aAdaptForEcphyRlCommonConfigReq <= USED 0
L1aAdaptForEcphyRlCommonSib1ConfigReq <= USED 0
L1aAdaptForEcphyRlDedicatedConfigReq <= USED 0
L1aCheckIfNeedStopGsmDrxScanning <= USED 0
L1aCheckIfNeedStopTdDrxScanning <= USED 0
L1aCheckIfNeedStopWbDrxScanning <= USED 0
L1aGetCpTimerSleepInd <= USED 0
L1aHandleEcphyFindCellReqCheckIfNeedSuspend <= USED 0
etiHandoverToLteFailReq <= USED 0
etiReselectToLteFailReq <= USED 0
ewiHandoverToLteFailReq <= USED 0
;FILE l1a_response.o
L1aHandleEcphyL1AssertDebugInd <= USED 0
;FILE l1a_sys.o
L1aAdaptForEcphyClassmarkReq <= USED 0
L1aCheckIfTimestampIsTimeout <= USED 0
L1aGetCurrentServingEuarfcn <= USED 0
L1aIsInConnectedStatus <= USED 0
L1aMemCpyInt16 <= USED 0
L1aMemCpyInt32 <= USED 0
;FILE l1a_task.o
LteL1aTask1 <= USED 0
;FILE l1a_task2.o
LteL1aTask21 <= USED 0
;FILE l1actmtrx.o
L1AmActivitiesOverlap <= USED 0
L1AmCopyActivityMatrix <= USED 0
L1AmDebugActivityMatrix <= USED 0
L1AmGetActivityMatrixSlot <= USED 0
L1AmResetActivityMatrix <= USED 0
L1AmShiftActivityMatrix <= USED 0
;FILE l1agccfg.o
;FILE l1alaucf.o
L1AlAuCfCallToneReqHandle <= USED 0
L1AlAuCfChannelDisableIndHandle <= USED 0
L1AlAuCfChannelMuteReqHandler <= USED 0
L1AlAuCfChannelNextDevice <= USED 0
L1AlAuCfChannelRate <= USED 0
L1AlAuCfChannelSetupReqHandle <= USED 0
L1AlAuCfChannelStereoMode <= USED 0
L1AlAuCfChannelTerminateReqHandle <= USED 0
L1AlAuCfChannelVolume <= USED 0
L1AlAuCfChannelVolumeDescription <= USED 0
L1AlAuCfChannelVolumeRampReqHandle <= USED 0
L1AlAuCfChannelVolumeReqHandle <= USED 0
L1AlAuCfDTMFReqHandle <= USED 0
L1AlAuCfDeviceChannel <= USED 0
L1AlAuCfDeviceMuteReqHandler <= USED 0
L1AlAuCfEnableNrec <= USED 0
L1AlAuCfIdentifyAudioCodec <= USED 0
L1AlAuCfIsToneChannel <= USED 0
L1AlAuCfSpeechReqHandle <= USED 0
L1AlAuCfToneMutesSpeech <= USED 0
L1AlAuCfVerifyChannel <= USED 0
L1AlAuChannelInsert <= USED 0
L1AlAuChannelIsLowerPriority <= USED 0
L1AlAuChannelRemove <= USED 0
L1AlAudioChannelDisableReqHandler <= USED 0
L1AlAudioChannelEnableReqHandler <= USED 0
L1AlAudioStatusReqHandler <= USED 0
L1AlIsSpeechChannelActive <= USED 0
L1AlPrintChannelsetup <= USED 0
;FILE l1alcfg.o
;FILE l1alfrau.o
;FILE l1alfrev.o
L1AlFrCanSleep <= USED 0
L1AlFrHandler_HighPriority <= USED 0
;FILE l1alfrgp.o
;FILE l1alfrpm.o
;FILE l1alfrpw.o
;FILE l1alhitask.o
L1AlHiTask1 <= USED 0
L1AlHiTaskTrigger <= USED 0
;FILE l1almemo.o
L1AlMemoBufferSetupHandler <= USED 0
L1AlMemoEraseMemoHandler <= USED 0
L1AlMemoHandleL1AlAudioLoopBackInd <= USED 0
L1AlMemoLoopBackReqHandler <= USED 0
L1AlMemoPlaybackReqHandler <= USED 0
L1AlMemoRecordChannelDisabledNotification <= USED 0
L1AlMemoRecordReqHandler <= USED 0
L1AlMemoStopReqHandler <= USED 0
L1AlMemoStoreMemoHandler <= USED 0
;FILE l1alpdef.o
L1AlHandleFreeBufferMemoryInd <= USED 0
L1AlPdCfAudioMelodyBufferDataRspHandler <= USED 0
L1AlPdCfEnablePredefChannel <= USED 0
L1AlPdCfPreDefinedBufferSetup <= USED 0
L1AlPdCfPreDefinedErase <= USED 0
L1AlPdCfPreDefinedReqHandle <= USED 0
L1AlPdCfPreDefinedStopReqHandle <= USED 0
L1AlPdCfPreDefinedStore <= USED 0
;FILE l1alpmcf.o
L1AlPwmBacklight <= USED 0
L1AlPwmGpioInit <= USED 0
;FILE l1alsend.o
L1AlSendL1AlAudioCallToneCnf <= USED 0
L1AlSendL1AlAudioChannelDisableCnf <= USED 0
L1AlSendL1AlAudioDtmfCnf <= USED 0
L1AlSendL1AlAudioErrorInd <= USED 0
L1AlSendL1AlAudioIoInd <= USED 0
L1AlSendL1AlAudioLoopBackCnf <= USED 0
L1AlSendL1AlAudioPredefinedEndOfPlayback <= USED 0
L1AlSendL1AlAudioPredefinedRestartInd <= USED 0
L1AlSendL1AlAudioPredefinedStopInd <= USED 0
L1AlSendL1AlAudioStatusCnf <= USED 0
L1AlSendL1AlPCMStreamingStopCnf <= USED 0
L1AlSendMemoRecordStopInd <= USED 0
;FILE l1altask.o
L1AlSendBbcDebugReq <= USED 0
L1AlTask1 <= USED 0
;FILE l1apcal.o
;FILE l1aucal.o
;FILE l1aucfg.o
L1AlAudGetInterpolatedTransducerValue <= USED 0
L1AuCfgFormatIsARingtoneFormat <= USED 0
L1AuCfgVolumeIndex <= USED 0
L1CfgAuIsHandoverInProgress <= USED 0
L1CfgAuStartEncoder <= USED 0
L1CfgAuStopEncoder <= USED 0
L1CfgAuSuspendEncoder <= USED 0
;FILE l1audsq.o
;FILE l1bgbcchwb.o
L1BgActionsUponMultiBcchDeactivation <= USED 0
L1BgBcchForGsmRestoreCellInfoPerArfcn <= USED 0
L1BgBuildMphMultiBcchDecodeInd <= USED 0
L1BgSaveCellInfoForGsmMultiBcchList <= USED 0
L1BgStartBcchListForWb <= USED 0
L1BgStartMultiBcchListProcessing <= USED 0
L1IsWaitingAnchorSych <= USED 0
SetUtevStartBcchList <= USED 0
getratInitiatedMbcch <= USED 0
;FILE l1bgcelllock.o
;FILE l1bgcfg_l1only.o
;FILE l1bgcpro.o
;FILE l1bgded.o
;FILE l1bgdualsimsignal.o
CalFnoffSet <= USED 0
CalcNextWBPiReceivingFrame <= USED 0
GplcRTUChips2QbitsConversion <= USED 0
IsOtherSimInWbMode <= USED 0
L1BgAplpGsmBcchDecodeInWbReq <= USED 0
L1BgAplpGsmPchReqInWbPsCnf <= USED 0
L1BgAplpGsmTurnOnRatCnf <= USED 0
L1BgAplpHighPriorityWbMeasFinishInd <= USED 0
L1BgAplpWbPiFinishInd <= USED 0
L1BgDualsimSignalReset <= USED 0
L1BgMhpDsStartPchReq <= USED 0
L1BgMhpDsStopPchReq <= USED 0
L1BgResumeWbCnf <= USED 0
L1BgSuspendWbCnf <= USED 0
ProcessGsmLatchRtuAck <= USED 0
RemoveonarfcnfromDualsimGsmBcchInfoReqDB <= USED 0
gwiCreateAplpGsmBcchDecodeInWbReq <= USED 0
gwiCreateAplpHighPrioWbMeasFinishInd <= USED 0
gwiCreateAplpHighPrioWbMeasReq <= USED 0
gwiCreateAplpPiFinishInd <= USED 0
gwiCreateAplpPositivePiInd <= USED 0
gwiCreateAplpStopWbPiReq <= USED 0
gwiCreateAplpSuspendGsmReq <= USED 0
gwiCreateAplpWbAbortMeasCnf <= USED 0
gwiCreateAplpWbMeasFinishInd <= USED 0
gwiCreateAplpWbMeasReq <= USED 0
gwiCreateAplpWbMeasStopReq <= USED 0
gwiCreateDsControlStartPCH <= USED 0
gwiCreateDsControlStopPCH <= USED 0
gwiCreateGsmPchReqInWbPsCnf <= USED 0
gwiL1SetStartWBPiCnf <= USED 0
gwiL1SetStopWBPiCnf <= USED 0
gwiL1SetWBPiFinishInd <= USED 0
;FILE l1bgestb_INT.o
;FILE l1bgidle.o
L1BgGetGsmCapability <= USED 0
L1BgProcessTcbRebuildSuspended <= USED 0
l1MnAllowSleepforPLMN <= USED 0
;FILE l1bginit.o
L1BgInitDataFromGRFDB <= USED 0
;FILE l1bgltedsdssignal.o
;FILE l1bgmeas.o
L1BgBcchDecodeInIratProcessOnlyBcch <= USED 0
L1BgBcchDecodeInWbProcess <= USED 0
L1BgGetIdleMeasPara <= USED 0
L1BgMeasStartHplmnSearch <= USED 0
L1BgSetGsmReq <= USED 0
ResettheidleSearchActive <= USED 0
SetLowestRssiThreshold <= USED 0
SortMeasurements <= USED 0
caldeltaoffset <= USED 0
;FILE l1bgncl.o
L1BgBcchForWbActivityAbort <= USED 0
L1BgBcchForWbActivityUpdate <= USED 0
L1BgUtranMsrResetPeriodicMsr <= USED 0
getncellStatusTablepoint <= USED 0
;FILE l1bgpt_INT.o
;FILE l1bgsig.o
L1BgSendEprachCnf <= USED 0
L1BgSendMphRachCnf <= USED 0
L1BgSendTiDaiCnf <= USED 0
L1BgSendUtranBchDecodeErrInd <= USED 0
L1BgSendUtranBchDecodeInd <= USED 0
;FILE l1bgsync.o
L1BgSecondarySyncToScellAfterResume <= USED 0
L1BgStartSecondarySyncSearchForGsmBcchInWb <= USED 0
L1BgStartSyncSearch <= USED 0
L1BgSyncToScell <= USED 0
L1BgSyncToScellAfterResume <= USED 0
;FILE l1bgtask.o
IsDeactiveEventSet <= USED 0
L1BgDeactivateSecondaryOtherSimFrameIsr <= USED 0
L1BgDisableSIMAFakeScellRSSIReport <= USED 0
L1BgGetStateForAplp <= USED 0
SendGsmBcchDecodeInWbReq <= USED 0
plgL1BackGroundTask1 <= USED 0
plgL1BackGroundTask_21 <= USED 0
wgiBgSendRdaSetGsmCnf <= USED 0
;FILE l1bgtest.o
L1DmCalDevGsmInitialise <= USED 0
;FILE l1bgutil.o
;FILE l1bgvalidation.o
;FILE l1bndcfg.o
SetBandBit <= USED 0
SetBandMode <= USED 0
resetL1CurrentBandMode <= USED 0
;FILE l1cell.o
L1CellInfoAddSortIndex <= USED 0
L1GetCurrentBaListPtr <= USED 0
;FILE l1cellularPowerApplication.o
CPAStateChangeLGOosToWOos <= USED 0
CPAStateChangeLWOosToGsmOos <= USED 0
CellularPowerAppGetDualLinkLteState <= USED 0
L1cCellularPowerAppDisableD2 <= USED 0
L1cCellularPowerAppEnableD2 <= USED 0
UMTSResetStickBit <= USED 0
l1GsmRebindTCUinSetGsmSimb <= USED 0
;FILE l1cfg.o
;FILE l1cipher.o
;FILE l1cprosq.o
GenerateCipherTcuSequences <= USED 0
SqFbsStartCipherSequencer <= USED 0
;FILE l1frafc.o
AfcAgingTimerInvalidateAndStop <= USED 0
AfcAgingTimerValidateAndStart <= USED 0
CfConvertAfcDacToPpb <= USED 0
CfSetDcxoTempCompenVal <= USED 0
;FILE l1fragc.o
L1AgcChangeSc <= USED 0
;FILE l1framr.o
;FILE l1frcelllock.o
L1FrCellLockUmphDetectedCellMeasInd <= USED 0
L1FrCellLockUmphRssiMeasInd <= USED 0
L1FrCellLockUmphRssiScanInd <= USED 0
;FILE l1frcpro.o
;FILE l1frdebug.o
;FILE l1frdec.o
;FILE l1frded.o
L1BgClearTcbAfterG2WHoSuccess <= USED 0
L1FrDedNclWbStopRssiMeas <= USED 0
;FILE l1frdmon.o
;FILE l1frdncl.o
;FILE l1frdutl.o
;FILE l1frexpr.o
;FILE l1frgims.o
L1FrImsAnySchedulerIsActive <= USED 0
;FILE l1frgitm.o
;FILE l1frgpwr.o
;FILE l1fridle.o
CheckEventsAreClearForWbBchDecode <= USED 0
L1FrIdleGetNcellMeasDrxCycleCounter <= USED 0
L1FrIdleGetScEstDrxCycleCounter <= USED 0
L1FrIdleIncDrxCycleCounters <= USED 0
L1FrIdleNclLteStartCellPlmn <= USED 0
L1FrIdleResetNcellMeasDrxCycleCounter <= USED 0
L1FrStateIsIdle <= USED 0
gplcGetBcchInfo <= USED 0
gplcResetBcchInfo <= USED 0
l1SetGapIndex <= USED 0
;FILE l1frinev.o
;FILE l1frint.o
L1GetCellFrameNumber <= USED 0
L1SqNbCanTurnSpeechOn <= USED 0
ReportGsmFrameNumberForLte <= USED 0
plgL1GetFrameNumber <= USED 0
;FILE l1friutl.o
;FILE l1frldt.o
L1BgFreeLdt <= USED 0
L1BgLdtAlloc <= USED 0
;FILE l1frlmt.o
;FILE l1frloop_INT.o
UpdateAfcForSimulation <= USED 0
;FILE l1frlpctest_INT.o
;FILE l1frltencl.o
;FILE l1frmeas.o
;FILE l1frmstest.o
L1FrTestMonEvents <= USED 0
L1FrTestRxEvents <= USED 0
;FILE l1frnull.o
SendDeactivateCnfToGrr <= USED 0
;FILE l1frpnclWB_INT.o
;FILE l1frpncl_INT.o
;FILE l1frprch.o
;FILE l1frpreserv_INT.o
L1FrPtmWinIsFree <= USED 0
L1FrPtmdynamicReservationUpdateBitmapOdd <= USED 0
;FILE l1frpt.o
;FILE l1frptds_INT.o
L1FrPtmGetNextRampTds <= USED 0
L1FrPtmGetNextSynTds <= USED 0
;FILE l1frptev.o
;FILE l1frptit.o
;FILE l1frptmc_INT.o
;FILE l1frptpa_INT.o
;FILE l1frptrb.o
;FILE l1frptrc.o
L1FrCipherXorData <= USED 0
L1FrPtmUpdOddSlotBitmaps <= USED 0
;FILE l1frptsh_INT.o
;FILE l1frptst.o
;FILE l1frpttu.o
;FILE l1frrept.o
;FILE l1frseq.o
;FILE l1frsig.o
L1FrSendDebugInd <= USED 0
L1FrSendEmptyEmphDetectedCellMeasInd <= USED 0
L1FrSendEmptyUmphDetectedCellMeasInd <= USED 0
L1FrSendLteBchDecodeErrInd <= USED 0
L1FrSendUmphDetectedCellMeasInd <= USED 0
L1FrSendUmphHoldGsmCnf <= USED 0
L1FrSendUmphRestoreGsmCnf <= USED 0
L1FrSendUmphRssiScanInd <= USED 0
L1FrSendUtranBchDecodeErrInd <= USED 0
L1FrSendsevingcellbcchreqTOBGtask <= USED 0
;FILE l1frspag.o
;FILE l1frsync.o
;FILE l1frtcb.o
L1FrCanPowerDownRadio <= USED 0
L1FrChangeTriggerAction <= USED 0
L1FrLdtRemoveFromList <= USED 0
L1FrResumeOneSimTcb <= USED 0
L1FrStartAllTriggerCounters_Frames <= USED 0
L1FrSuspendOneSimTcb <= USED 0
;FILE l1frtds.o
L1FrGetAfeTds <= USED 0
L1FrGetGpIdleChuTds <= USED 0
;FILE l1frtest.o
L1FrTestRampProfileUpdateHandler <= USED 0
L1FrTestRxFastCalibCalcAfcParams <= USED 0
StartTxFastCalibrationBurstSeq <= USED 0
;FILE l1frwbncl_INT.o
L1FrLdtUtranBchRemove <= USED 0
L1FrUtranGetWbBchState <= USED 0
L1FrUtranMinimumTimeForRssi <= USED 0
L1FrWbMeasSetNextRssiUarfcnMeas <= USED 0
L1FrWbMeasUpdateIndicationOnRssiResults <= USED 0
gwiWbMeasTimerExpirationWalkaround <= USED 0
;FILE l1frwifi.o
;FILE l1gpcfg.o
CheckPowerReductionPerSlot <= USED 0
L1BgMppInitialise <= USED 0
;FILE l1gsmcfg.o
GenericRfDriverPeripheralsInit <= USED 0
;FILE l1kifail.o
kiExtTrace <= USED 0
;FILE l1kiinit.o
;FILE l1kimem.o
L1KiAllocMemory <= USED 0
L1KiAllocZeroMemory <= USED 0
L1KiFreeMemory <= USED 0
L1KiReallocMemory <= USED 0
L1KiRequestMemory <= USED 0
L1KiRerequestMemory <= USED 0
;FILE l1kiqueue.o
L1KiFlushQueue <= USED 0
;FILE l1kisig.o
L1KiCreateZeroSignal <= USED 0
L1KiReceiveSignalPoll <= USED 0
L1KiSignalLength <= USED 0
;FILE l1kitask.o
L1KiCurrentIsL1Task <= USED 0
L1KiDisableInterrupts <= USED 0
L1KiEnableInterrupts <= USED 0
;FILE l1kitimer.o
L1KiGetRelativeTime <= USED 0
L1TimerGetSettings <= USED 0
SetL1KiTimerInitilizedFlag <= USED 0
;FILE l1pelcfg.o
;FILE l1phtask.o
plgL1PhTask1 <= USED 0
;FILE l1pload.o
L1CountBadRadioBlocks <= USED 0
L1CountGoodRadioBlocks <= USED 0
L1IncTxRadioBlocks <= USED 0
L1LpTaskStatistics <= USED 0
L1SetDSPLoadLedOnThreshold <= USED 0
;FILE l1pwrctl.o
L1CfgClipPowerLevelEgprs <= USED 0
;FILE l1pwrptm.o
L1ConvertPowerDbmToControlLevel <= USED 0
;FILE l1pwrsav.o
;FILE l1ratscch.o
;FILE l1slow.o
L1SlowCreatePelToGenerateLtuLatchPulse <= USED 0
L1SlowCreatePelToGenerateLtuReconstructPulse <= USED 0
l1DisableSleepFrom3G <= USED 0
l1EnableSleepFrom3G <= USED 0
l1MnGetTcuIsaSleepStatus <= USED 0
;FILE l1sqafc.o
dlAfcAsyncWrite <= USED 0
;FILE l1sqagc.o
;FILE l1sqfb.o
GsmStatusIsIdleOrDedicatedFlg <= USED 0
GsmStatusIsTestFlg <= USED 0
;FILE l1sqfmon.o
SqIdleProcessMonitorAccumulate <= USED 0
SqIdleProcessMonitorCalcAverage <= USED 0
SqIdleProcessMonitorInitAcc <= USED 0
;FILE l1sqimon.o
;FILE l1sqint.o
;FILE l1sqlpc.o
;FILE l1sqlte.o
DeleteLteIdleRssiScanInGapSequencer <= USED 0
;FILE l1sqmon.o
L1SqMonIdleSeqStart <= USED 0
;FILE l1sqnb.o
;FILE l1sqrad.o
;FILE l1sqrx.o
SqFbsStartRxPchForPsPagingInWb <= USED 0
;FILE l1sqrxcd.o
;FILE l1sqrxtc.o
;FILE l1sqsb.o
;FILE l1sqtim.o
;FILE l1sqtx.o
;FILE l1sqtxcd.o
SqFbsStartCtrlRWSeq <= USED 0
plgCalDevCtrlRWResult <= USED 0
;FILE l1sqtxdc.o
;FILE l1sqtxid.o
;FILE l1sqtxra.o
;FILE l1sqtxtc.o
;FILE l1sqwb.o
DeleteIdleHighPrioWbMeasSequencer <= USED 0
DeleteIdleWbPiSequencer <= USED 0
DeleteUtranDedCchCellMeasSequencer <= USED 0
DeleteUtranDedCchRssiMeasSequencer <= USED 0
DeleteUtranDedTchCellMeasSequencer <= USED 0
DeleteUtranDedTchRssiMeasSequencer <= USED 0
DeleteUtranIdleCellMeasSequencer <= USED 0
DeleteUtranIdleRssiMeasSequencer <= USED 0
SqFbsStartIdleUtranRssiLdt <= USED 0
;FILE l1syndrv.o
;FILE l1tcb.o
L1BgFreeOneSimTcb <= USED 0
;FILE l1timers.o
L1TimersInitialise <= USED 0
;FILE l1uthop.o
;FILE l2_stub_ds3.o
UrlAmTxUrlcAmDataReqFn <= USED 0
UrlTmTxUrlcTmDataReqFn <= USED 0
UrlUmTxUrlcUmDataReqFn <= USED 0
;FILE l2ack.o
;FILE l2cbch.o
;FILE l2chan.o
;FILE l2dd.o
;FILE l2frame.o
;FILE l2proces.o
L2CbchTask1 <= USED 0
L2CbchTaskExitRoutine <= USED 0
L2Dcch0Task1 <= USED 0
L2Dcch0TaskExitRoutine <= USED 0
L2Dcch3Task1 <= USED 0
L2Dcch3TaskExitRoutine <= USED 0
L2DdTask1 <= USED 0
L2DdTaskExitRoutine <= USED 0
L2Sacch0Task1 <= USED 0
L2Sacch0TaskExitRoutine <= USED 0
L2Sacch3Task1 <= USED 0
L2Sacch3TaskExitRoutine <= USED 0
;FILE l2queue.o
L2EnquireTxQueueStatus <= USED 0
L2ReturnNextUiFrameToTransmit <= USED 0
;FILE l2signal.o
L2SendDlDataInd <= USED 0
L2SendL2cbDataInd <= USED 0
;FILE l2unack.o
;FILE l3dec_ie.o
DecodeEmergencyCategoryIE <= USED 0
DecodeStreamIdentifierIE <= USED 0
;FILE l3decgm.o
DecodePtmsiSignature2IE <= USED 0
DecodeRequestedMSInfoIE <= USED 0
DecodeUplinkDataStatusIE <= USED 0
;FILE l3declow.o
;FILE l3declte.o
DecodeCsfbResponseIE <= USED 0
DecodeEmmExServiceTypeIE <= USED 0
DecodeEpsAttachTypeIE <= USED 0
DecodeEpsAuthParamResIE <= USED 0
DecodeEpsMoDetachTypeIE <= USED 0
DecodeEpsUpdateTypeIE <= USED 0
DecodeKsiAndSnIE <= USED 0
DecodeSpareHalfOctetIE <= USED 0
DecodeSupportedCodecsListIE <= USED 0
DecodeTrackingAreaIdentityIE <= USED 0
DecodeUeNetworkCapabilityIE <= USED 0
DecodeUeRadioCapabilityInfoUpdateNeededIE <= USED 0
;FILE l3decmn.o
;FILE l3decode.o
;FILE l3decrm.o
;FILE l3enc_ie.o
EncodeChannelModeIE <= USED 0
EncodeLaiWith3DigitMncIE <= USED 0
EncodeMobileTimeDifferenceIE <= USED 0
EncodeRRCauseIE <= USED 0
;FILE l3encgm.o
EncodeGprsTimer2IE <= USED 0
EncodeNsapiIE <= USED 0
EncodeSmCauseIE <= USED 0
cancelRel5QoS <= USED 0
;FILE l3enclte.o
EncodeAPNAMaxBitRateIE <= USED 0
EncodeAdditionalUpdateResultIE <= USED 0
EncodeCliIE <= USED 0
EncodeEmergencyNumberListIE <= USED 0
EncodeEpsAttachResultIE <= USED 0
EncodeEpsNetworkFeatureSupportIE <= USED 0
EncodeEpsUpdateResultIE <= USED 0
EncodeLcsClientIdentityIE <= USED 0
EncodeLcsIndicatorIE <= USED 0
EncodeNasSecurityAlgorithmsIE <= USED 0
EncodePagingIdentityIE <= USED 0
EncodeSpareHalfOctetIE <= USED 0
EncodeSsCodeIE <= USED 0
EncodeTaiListIE <= USED 0
EncodeUeSecurityCapabilityIE <= USED 0
;FILE l3encode.o
GpEncSparePaddingBits <= USED 0
;FILE led.o
LedInits <= USED 0
LedOn <= USED 0
;FILE lfs.o
lfs_access <= USED 0
lfs_dir_rewind <= USED 0
lfs_dir_seek <= USED 0
lfs_dir_tell <= USED 0
lfs_file_rewind <= USED 0
lfs_file_tell <= USED 0
lfs_file_truncate <= USED 0
lfs_mkdir <= USED 0
lfs_stat <= USED 0
lfs_unmount <= USED 0
;FILE lfs_api.o
lfs_io_Access <= USED 0
lfs_io_dir_make <= USED 0
lfs_io_eof <= USED 0
lfs_io_ftell <= USED 0
lfs_io_readEx <= USED 0
lfs_io_stat <= USED 0
lfs_io_writeEx <= USED 0
lfs_to_fatstat <= USED 0
;FILE lfs_cache.o
LfsMsgQPut <= USED 0
LfsMsgQRemove <= USED 0
lfs_alloc_block_cache <= USED 0
lfs_erase_all <= USED 0
lfs_flash_cache_read <= USED 0
lfs_flash_cache_write <= USED 0
lfs_flush_unlock <= USED 0
lfs_get_block_cache <= USED 0
lfs_read_block_to_buf <= USED 0
lfs_write_back <= USED 0
lfs_write_back_one <= USED 0
;FILE lfs_crc.o
;FILE llc.o
GpLlc2Task1 <= USED 0
GpLlcTask1 <= USED 0
;FILE llc_2.o
;FILE llc_gki.o
;FILE llc_ref.o
;FILE lldata.o
;FILE lle.o
;FILE lle_i.o
;FILE lle_s1.o
;FILE lle_s2.o
;FILE lle_s3.o
;FILE lle_s4.o
;FILE lle_s5.o
;FILE lle_s6.o
;FILE lle_ui.o
;FILE lle_xid.o
;FILE llfcs.o
;FILE llheader.o
LlHeaderDecodeIABit <= USED 0
LlHeaderDecodeSABit <= USED 0
;FILE llm_sig.o
LlgmmSendAssignReq <= USED 0
LlgmmSendIovReq <= USED 0
LlgmmSendResetReq <= USED 0
LlgmmSendResumeReq <= USED 0
LlgmmSendSuspendReq <= USED 0
LlgmmSendTriggerReq <= USED 0
;FILE llme.o
;FILE llmux.o
;FILE loadTable.o
StrtupIsPowerup <= USED 0
commImageTableInit <= USED 0
getCommImageBaseAddr <= USED 0
getCommNumOfLife <= USED 0
get_apn_begin_address <= USED 0
get_apn_bin_size <= USED 0
get_apn_end_address <= USED 0
get_btbin_begin_address <= USED 0
get_btbin_bin_size <= USED 0
get_btbin_end_address <= USED 0
get_btlst_begin_address <= USED 0
get_btlst_bin_size <= USED 0
get_btlst_end_address <= USED 0
get_cp_binary_size <= USED 0
get_dsp_bin_size <= USED 0
get_factory_a_bin_size <= USED 0
get_fota_param_end_address <= USED 0
get_fota_param_start_address <= USED 0
get_fota_pkg_end_address <= USED 0
get_fota_pkg_start_address <= USED 0
get_mmipool_end_address <= USED 0
get_mmipool_start_address <= USED 0
get_nvm_bin_size <= USED 0
get_ps_mode <= USED 0
get_rf_partition_size <= USED 0
get_system_end_address <= USED 0
get_system_start_address <= USED 0
get_updater_backup_end_address <= USED 0
get_updater_backup_start_address <= USED 0
get_updater_copy_size <= USED 0
get_updater_end_address <= USED 0
get_updater_start_address <= USED 0
incrementCommNumOfLife <= USED 0
whether_RW_region_compressed <= USED 0
;FILE log.o
_log_output_ICAT <= USED 0
_log_output_ICAT_ln <= USED 0
_log_output_empty <= USED 0
_log_output_uart <= USED 0
log_dump <= USED 0
log_flush <= USED 0
log_get_module_level <= USED 0
log_init <= USED 0
log_module_get <= USED 0
log_set_trace_level <= USED 0
;FILE lowtasks.o
LowEventUnBind <= USED 0
;FILE lrm.o
;FILE lrm_gml.o
lrmGml_Destroy <= USED 0
lrmGml_Init <= USED 0
;FILE ltebm.o
LteBmHandleUlUmDataHarqCnf <= USED 0
LteBmProcessDataAbortInd <= USED 0
LteIPCReleaseRxSBBlock <= USED 0
LteL2BHighWaterWarning <= USED 0
LteMacKeepReservedRxBlock <= USED 0
LteMacSFAllocateRxL2BBlock <= USED 0
LteMacSfSendIPCDlBlockMove <= USED 0
UMTSAllocateTxL2Buffer <= USED 0
UMTSMacAllocateL1Buffer <= USED 0
UMTSMacAllocateL2RxBuffer <= USED 0
UMTSMacReleaseL1Buffer <= USED 0
UMTSMacSetL1BufferNum <= USED 0
UMTSReleaseTxL2Buffer <= USED 0
UMTSRlcReleaseL2RxBuffer <= USED 0
;FILE ltenetworkcard.o
LteConstructSnDataNodeE <= USED 0
checksum <= USED 0
usbNetTaskInit <= USED 0
usbRxEnable <= USED 0
usbRxUnBlocked <= USED 0
;FILE lterabmgmm.o
;FILE lterabmisc.o
;FILE lterabmmain.o
;FILE lterabmpdp.o
LteRabmEnablePendingMultiSnDataReq <= USED 0
LteRabmProcessSnMultiDataListReq <= USED 0
LteRabmReleaseEpsSnDataList <= USED 0
LteRabmReleasePendingSnDataReq <= USED 0
LteRabmReleaseSnDataList <= USED 0
LteRabmSendDataInQueue <= USED 0
LteRabmSendSignalToPdp <= USED 0
LteRabmSnLteSnDataRsp <= USED 0
RabmProcessWaitToReestExpireInd <= USED 0
checkRabmPendingData <= USED 0
;FILE lterabmrrc.o
LteRabmRabmRrcEstablishRej <= USED 0
LteRabmRabmRrcEstablishRes <= USED 0
LteRabmRabmRrcReleaseRes <= USED 0
;FILE lterabmsm.o
LteUlGetEpsBearerIdFromTft <= USED 0
LteUlIpPkgCompatibleWithTft <= USED 0
;FILE lterabmtimers.o
LteRabmStartWaitToReestTimer <= USED 0
;FILE lterabmutil.o
LteRabmAddDlCounter <= USED 0
LteRabmAddUlCounter <= USED 0
LteRabmFillStatistics <= USED 0
LteRabmReleaseLtePdcpSduList <= USED 0
LteRabmReleaseSnDataReq <= USED 0
LteRabmReplacePacketFilter <= USED 0
;FILE lterlc.o
LteMacDataInd <= USED 0
LteMacDataReq <= USED 0
LteRlcGetUplaneData <= USED 0
LteRlcProcessPdcpDiscardReq <= USED 0
LteRlcUMTransmitIndSetData <= USED 0
lteRlcHeaderMinLen <= USED 0
;FILE lterlcam.o
LteDiscardUlPacketsDueToHwm <= USED 0
LteRlcMemoryCheck <= USED 0
;FILE lterlccommon.o
ErlcPrintf <= USED 0
LteRlcGetIdxFromSn <= USED 0
LteRlcGetSnFromIdx <= USED 0
LteRlcSetHeader <= USED 0
;FILE lterlcum.o
LteUmRlcAddPdcpData <= USED 0
LteUmRlcProcessPdcpDiscardReq <= USED 0
;FILE lterrc_decb.o
PerDec_EuBandCombinationParameters_v1250_commSupportedBandsPerBC_r12_str <= USED 0
PerDec_EuBandCombinationParameters_v1250_supportedNAICS_2CRS_AP_r12_str <= USED 0
PerDec_EuCSFBParametersRequestCDMA2000_v8a0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuCSG_Identity <= USED 0
PerDec_EuC_RNTI <= USED 0
PerDec_EuCellGlobalIdCDMA2000_cellGlobalId1XRTT_str <= USED 0
PerDec_EuCellGlobalIdCDMA2000_cellGlobalIdHRPD_str <= USED 0
PerDec_EuCellIdentity <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig0_r11_str <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11_subframeConfig6_r11_str <= USED 0
PerDec_EuMBMSCountingRequest_r10_nonCriticalExtension_str <= USED 0
PerDec_EuMBMSCountingResponse_r10_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMBSFNAreaConfiguration_v12xy_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuMCCH_MessageType_later_messageClassExtension_str <= USED 0
PerDec_EuMMEC <= USED 0
PerDec_EuMeasurementReport_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerDec_EuNeighCellConfig <= USED 0
PerDec_EuRAND_CDMA2000 <= USED 0
PerDec_EuRRCConnectionReconfigurationComplete_v12xy_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuShortMAC_I <= USED 0
PerDec_EuSupportedBandwidthCombinationSet_r10_str <= USED 0
PerDec_EuSystemInformationBlockType1_v12xy_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuTrackingAreaCode <= USED 0
PerDec_EuUE_EUTRA_Capability_v10c0_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuUE_EUTRA_Capability_v1250_IEs_nonCriticalExtension_str <= USED 0
PerDec_EuUL_DCCH_MessageType_messageClassExtension_messageClassExtensionFuture_r11_str <= USED 0
;FILE lterrc_decc.o
PerDec_EuBandCombinationParameters_v1250_supportedCellGrouping_r12 <= USED 0
PerDec_EuCellGlobalIdCDMA2000 <= USED 0
PerDec_EuCellsTriggeredList_physCellIdUTRA <= USED 0
PerDec_EuCellsTriggeredList_seq <= USED 0
PerDec_EuIDC_SubframePattern_r11 <= USED 0
PerDec_EuIDC_SubframePattern_r11_subframePatternTDD_r11 <= USED 0
PerDec_EuMCCH_MessageType <= USED 0
PerDec_EuMCCH_MessageType_c1 <= USED 0
PerDec_EuMCCH_MessageType_c2 <= USED 0
PerDec_EuMCCH_MessageType_later <= USED 0
PerDec_EuRLF_Report_r9_physCellId_r11 <= USED 0
PerDec_EuRLF_Report_r9_physCellId_r11_1 <= USED 0
PerDec_EuTMGI_r9_plmn_Id_r9 <= USED 0
PerDec_EuVarMeasConfig_speedStatePars <= USED 0
PerDec_EuVisitedCellInfo_r12_visitedCellId_r12 <= USED 0
;FILE lterrc_dece.o
PerDec_EuBandCombinationParameters_r11_multipleTimingAdvance_r11 <= USED 0
PerDec_EuBandCombinationParameters_r11_simultaneousRx_Tx_r11 <= USED 0
PerDec_EuBandCombinationParameters_v1130_multipleTimingAdvance_r11 <= USED 0
PerDec_EuBandCombinationParameters_v1130_simultaneousRx_Tx_r11 <= USED 0
PerDec_EuBandCombinationParameters_v1250_asynchronous_r12 <= USED 0
PerDec_EuBandParameters_r11_supportedCSI_Proc_r11 <= USED 0
PerDec_EuBandParameters_v1130_supportedCSI_Proc_r11 <= USED 0
PerDec_EuDC_Parameters_r12_drb_TypeSCG_r12 <= USED 0
PerDec_EuDC_Parameters_r12_drb_TypeSplit_r12 <= USED 0
PerDec_EuEstablishmentCause <= USED 0
PerDec_EuMAC_Parameters_r12_logicalChannelSR_ProhibitTimer_r12 <= USED 0
PerDec_EuMAC_Parameters_r12_longDRX_Command_r12 <= USED 0
PerDec_EuMBMS_Parameters_r11_mbms_NonServingCell_r11 <= USED 0
PerDec_EuMBMS_Parameters_r11_mbms_SCell_r11 <= USED 0
PerDec_EuMBMS_Parameters_v1250_mbms_AsyncDC_r12 <= USED 0
PerDec_EuMBSFNAreaConfiguration_r9_commonSF_AllocPeriod_r9 <= USED 0
PerDec_EuMeasParameters_v1130_rsrqMeasWideband_r11 <= USED 0
PerDec_EuMeasParameters_v11a0_benefitsFromInterruption_r11 <= USED 0
PerDec_EuMeasParameters_v1250_alternativeTimeToTrigger_r12 <= USED 0
PerDec_EuMeasParameters_v1250_crs_DiscoverySignalsMeas_r12 <= USED 0
PerDec_EuMeasParameters_v1250_csi_RS_DiscoverySignalsMeas_r12 <= USED 0
PerDec_EuMeasParameters_v1250_extendedMaxMeasId_r12 <= USED 0
PerDec_EuMeasParameters_v1250_extendedRSRQ_LowerRange_r12 <= USED 0
PerDec_EuMeasParameters_v1250_incMonEUTRA_r12 <= USED 0
PerDec_EuMeasParameters_v1250_incMonUTRA_r12 <= USED 0
PerDec_EuMeasParameters_v1250_rsrq_OnAllSymbols_r12 <= USED 0
PerDec_EuMeasParameters_v1250_timerT312_r12 <= USED 0
PerDec_EuNAICS_Capability_Entry_r12_numberOfAggregatedPRB_r12 <= USED 0
PerDec_EuOTDOA_PositioningCapabilities_r10_interFreqRSTD_Measurement_r10 <= USED 0
PerDec_EuOTDOA_PositioningCapabilities_r10_otdoa_UE_Assisted_r10 <= USED 0
PerDec_EuOther_Parameters_r11_inDeviceCoexInd_r11 <= USED 0
PerDec_EuOther_Parameters_r11_powerPrefInd_r11 <= USED 0
PerDec_EuOther_Parameters_r11_ue_Rx_TxTimeDiffMeasurements_r11 <= USED 0
PerDec_EuPDCP_Parameters_v1130_pdcp_SN_Extension_r11 <= USED 0
PerDec_EuPDCP_Parameters_v1130_supportRohcContextContinue_r11 <= USED 0
PerDec_EuPMCH_Config_r12_mch_SchedulingPeriod_r12 <= USED 0
PerDec_EuPMCH_Config_r9_mch_SchedulingPeriod_r9 <= USED 0
PerDec_EuPhyLayerParameters_v1130_crs_InterfHandl_r11 <= USED 0
PerDec_EuPhyLayerParameters_v1130_ePDCCH_r11 <= USED 0
PerDec_EuPhyLayerParameters_v1130_multiACK_CSI_Reporting_r11 <= USED 0
PerDec_EuPhyLayerParameters_v1130_ss_CCH_InterfHandl_r11 <= USED 0
PerDec_EuPhyLayerParameters_v1130_tdd_SpecialSubframe_r11 <= USED 0
PerDec_EuPhyLayerParameters_v1130_txDiv_PUCCH1b_ChSelect_r11 <= USED 0
PerDec_EuPhyLayerParameters_v1130_ul_CoMP_r11 <= USED 0
PerDec_EuPhyLayerParameters_v1250_csi_SubframeSet_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_discoverySignalsInDeactSCell_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_e_HARQ_Pattern_FDD_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_enhanced_4TxCodebook_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_noResourceRestrictionForTTIBundling_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_phy_TDD_ReConfig_FDD_PCell_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_phy_TDD_ReConfig_TDD_PCell_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_pusch_FeedbackMode_r12 <= USED 0
PerDec_EuPhyLayerParameters_v1250_pusch_SRS_PowerControl_SubframeSet_r12 <= USED 0
PerDec_EuPhyLayerParameters_v9d0_tm5_FDD_r9 <= USED 0
PerDec_EuPhyLayerParameters_v9d0_tm5_TDD_r9 <= USED 0
PerDec_EuRF_Parameters_v1180_freqBandRetrieval_r11 <= USED 0
PerDec_EuRF_Parameters_v1250_freqBandPriorityAdjustment_r12 <= USED 0
PerDec_EuRLC_Parameters_r12_extended_RLC_LI_Field_r12 <= USED 0
PerDec_EuRRCConnectionReconfigurationComplete_v12xy_IEs_logMeasAvailableMBSFN_r12 <= USED 0
PerDec_EuRRCConnectionReestablishmentComplete_v1020_IEs_logMeasAvailable_r10 <= USED 0
PerDec_EuSL_Parameters_r12_commSimultaneousTx_r12 <= USED 0
PerDec_EuSL_Parameters_r12_discScheduledResourceAlloc_r12 <= USED 0
PerDec_EuSL_Parameters_r12_discSupportedProc_r12 <= USED 0
PerDec_EuSL_Parameters_r12_disc_SLSS_r12 <= USED 0
PerDec_EuSL_Parameters_r12_disc_UE_SelectedResourceAlloc_r12 <= USED 0
PerDec_EuSupportedBandEUTRA_v1250_dl_256QAM_r12 <= USED 0
PerDec_EuSupportedBandEUTRA_v1250_ul_64QAM_r12 <= USED 0
PerDec_EuSupportedBandInfo_r12_support_r12 <= USED 0
PerDec_EuSystemInformationBlockType1_v12xy_IEs_category0Allowed_r12 <= USED 0
PerDec_EuUE_BasedNetwPerfMeasParameters_r10_loggedMeasurementsIdle_r10 <= USED 0
PerDec_EuUE_BasedNetwPerfMeasParameters_r10_standaloneGNSS_Location_r10 <= USED 0
PerDec_EuUE_BasedNetwPerfMeasParameters_v1250_loggedMBSFNMeasurements_r12 <= USED 0
PerDec_EuWLAN_IW_Parameters_r12_wlan_IW_ANDSF_Policies_r12 <= USED 0
PerDec_EuWLAN_IW_Parameters_r12_wlan_IW_RAN_Rules_r12 <= USED 0
;FILE lterrc_deco.o
PerDec_EuMBMSCountingRequest_r10_lateNonCriticalExtension_str <= USED 0
PerDec_EuMBMSCountingResponse_r10_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuMBMS_SessionInfo_r9_sessionId_r9_str <= USED 0
PerDec_EuMBSFNAreaConfiguration_v930_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuTMGI_r9_serviceId_r9_str <= USED 0
PerDec_EuUE_EUTRA_Capability_v9h0_IEs_lateNonCriticalExtension_str <= USED 0
PerDec_EuULHandoverPreparationTransfer_v8a0_IEs_lateNonCriticalExtension_str <= USED 0
;FILE lterrc_decs.o
PerDec_EuBandCombinationListEUTRA_r10 <= USED 0
PerDec_EuBandInfoEUTRA <= USED 0
PerDec_EuBandListEUTRA <= USED 0
PerDec_EuCellGlobalIdGERAN <= USED 0
PerDec_EuCellGlobalIdUTRA <= USED 0
PerDec_EuCellsTriggeredList <= USED 0
PerDec_EuCellsTriggeredList_physCellIdGERAN <= USED 0
PerDec_EuCommonSF_AllocPatternList_r9 <= USED 0
PerDec_EuCountingRequestInfo_r10 <= USED 0
PerDec_EuCountingRequestList_r10 <= USED 0
PerDec_EuCountingResponseInfo_r10 <= USED 0
PerDec_EuCountingResponseList_r10 <= USED 0
PerDec_EuDL_CCCH_Message <= USED 0
PerDec_EuDL_DCCH_Message <= USED 0
PerDec_EuFreqBandIndicatorListEUTRA_r12 <= USED 0
PerDec_EuInterFreqBandInfo <= USED 0
PerDec_EuInterFreqBandList <= USED 0
PerDec_EuInterRAT_BandInfo <= USED 0
PerDec_EuInterRAT_BandList <= USED 0
PerDec_EuMBMSCountingRequest_r10 <= USED 0
PerDec_EuMBMS_Parameters_r11 <= USED 0
PerDec_EuMBMS_Parameters_v1250 <= USED 0
PerDec_EuMBMS_SessionInfoList_r9 <= USED 0
PerDec_EuMBMS_SessionInfo_r9 <= USED 0
PerDec_EuMBSFNAreaConfiguration_r9 <= USED 0
PerDec_EuMBSFNAreaConfiguration_v12xy_IEs <= USED 0
PerDec_EuMBSFNAreaConfiguration_v930_IEs <= USED 0
PerDec_EuMCCH_Message <= USED 0
PerDec_EuMeasParameters <= USED 0
PerDec_EuMeasParameters_v1020 <= USED 0
PerDec_EuMeasParameters_v1130 <= USED 0
PerDec_EuMeasParameters_v11a0 <= USED 0
PerDec_EuMeasParameters_v1250 <= USED 0
PerDec_EuMeasResults_measResultPCell <= USED 0
PerDec_EuNAICS_Capability_Entry_r12 <= USED 0
PerDec_EuNAICS_Capability_List_r12 <= USED 0
PerDec_EuOTDOA_PositioningCapabilities_r10 <= USED 0
PerDec_EuOther_Parameters_r11 <= USED 0
PerDec_EuPLMN_IdentityList2 <= USED 0
PerDec_EuPMCH_Config_r12 <= USED 0
PerDec_EuPMCH_Config_r9 <= USED 0
PerDec_EuPMCH_InfoExt_r12 <= USED 0
PerDec_EuPMCH_InfoListExt_r12 <= USED 0
PerDec_EuPMCH_InfoList_r9 <= USED 0
PerDec_EuPMCH_Info_r9 <= USED 0
PerDec_EuRegisteredMME <= USED 0
PerDec_EuSL_Parameters_r12 <= USED 0
PerDec_EuSupportedBandEUTRA <= USED 0
PerDec_EuSupportedBandEUTRA_v1250 <= USED 0
PerDec_EuSupportedBandEUTRA_v9e0 <= USED 0
PerDec_EuSupportedBandInfoList_r12 <= USED 0
PerDec_EuSupportedBandInfo_r12 <= USED 0
PerDec_EuSupportedBandListEUTRA <= USED 0
PerDec_EuSupportedBandListEUTRA_v1250 <= USED 0
PerDec_EuSupportedBandListEUTRA_v9e0 <= USED 0
PerDec_EuSystemInformationBlockType1_v12xy_IEs <= USED 0
PerDec_EuSystemInformationBlockType1_v12xy_IEs_cellAccessRelatedInfo_v12xy <= USED 0
PerDec_EuTMGI_r9 <= USED 0
PerDec_EuUE_BasedNetwPerfMeasParameters_r10 <= USED 0
PerDec_EuUE_BasedNetwPerfMeasParameters_v1250 <= USED 0
PerDec_EuVarMeasConfig_setup <= USED 0
PerDec_EuVarMeasReport <= USED 0
PerDec_EuVarMeasReportList <= USED 0
PerDec_EuVarShortMAC_Input <= USED 0
PerDec_EuVisitedCellInfoList_r12 <= USED 0
PerDec_EuVisitedCellInfo_r12 <= USED 0
PerDec_EuVisitedCellInfo_r12_pci_arfcn_r12 <= USED 0
PerDec_EuWLAN_IW_Parameters_r12 <= USED 0
;FILE lterrc_encb.o
PerEnc_EuCSG_Identity <= USED 0
PerEnc_EuC_RNTI <= USED 0
PerEnc_EuCellIdentity <= USED 0
PerEnc_EuDLInformationTransfer_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuDL_DCCH_MessageType_messageClassExtension_str <= USED 0
PerEnc_EuHandoverFromEUTRAPreparationRequest_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuMMEC <= USED 0
PerEnc_EuMobilityFromEUTRACommand_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuNeighCellConfig <= USED 0
PerEnc_EuRRCConnectionReconfiguration_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuRRCConnectionReestablishment_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuRRCConnectionReject_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuRRCConnectionReject_v1130_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuRRCConnectionRelease_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuRRCConnectionRelease_v9e0_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuRRCConnectionSetup_criticalExtensions_criticalExtensionsFuture_str <= USED 0
PerEnc_EuShortMAC_I <= USED 0
PerEnc_EuTrackingAreaCode <= USED 0
PerEnc_EuUECapabilityEnquiry_v1180_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUEInformationResponse_v9e0_IEs_nonCriticalExtension_str <= USED 0
PerEnc_EuUE_EUTRA_Capability_v1250_IEs_nonCriticalExtension_str <= USED 0
;FILE lterrc_encc.o
PerEnc_EuCarrierFreqsGERAN_followingARFCNs <= USED 0
PerEnc_EuCellsTriggeredList_physCellIdUTRA <= USED 0
PerEnc_EuCellsTriggeredList_seq <= USED 0
;FILE lterrc_ence.o
PerEnc_EuCQI_ReportConfigSCell_r10_pmi_RI_Report_r10 <= USED 0
PerEnc_EuCRS_AssistanceInfo_r11_antennaPortsCount_r11 <= USED 0
PerEnc_EuPDCP_Config_pdcp_SN_Size <= USED 0
PerEnc_EuPUSCH_ConfigDedicatedSCell_r10_dmrs_WithOCC_Activated_r10 <= USED 0
PerEnc_EuPUSCH_ConfigDedicatedSCell_r10_groupHoppingDisabled_r10 <= USED 0
PerEnc_EuPhysCellIdRange_range <= USED 0
PerEnc_EuRLF_TimersAndConstants_r9_n310_r9 <= USED 0
PerEnc_EuRLF_TimersAndConstants_r9_n311_r9 <= USED 0
PerEnc_EuRLF_TimersAndConstants_r9_t301_r9 <= USED 0
PerEnc_EuRLF_TimersAndConstants_r9_t310_r9 <= USED 0
PerEnc_EuRLF_TimersAndConstants_r9_t311_r9 <= USED 0
PerEnc_EuReleaseCause <= USED 0
PerEnc_EuReportConfigEUTRA_includeLocationInfo_r10 <= USED 0
PerEnc_EuReportConfigEUTRA_reportAddNeighMeas_r10 <= USED 0
PerEnc_EuReportConfigEUTRA_reportAmount <= USED 0
PerEnc_EuReportConfigEUTRA_reportQuantity <= USED 0
PerEnc_EuReportConfigEUTRA_si_RequestForHO_r9 <= USED 0
PerEnc_EuReportConfigEUTRA_triggerQuantity <= USED 0
PerEnc_EuReportConfigEUTRA_ue_RxTxTimeDiffPeriodical_r9 <= USED 0
PerEnc_EuReportConfigEUTRA_useT312_r12 <= USED 0
PerEnc_EuSchedulingInfo_si_Periodicity <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_v1250_loggedMBSFNMeasurements_r12 <= USED 0
PerEnc_EuUplinkPowerControlDedicatedSCell_r10_deltaMCS_Enabled_r10 <= USED 0
PerEnc_EuUplinkPowerControlDedicatedSCell_r10_pathlossReferenceLinking_r10 <= USED 0
;FILE lterrc_enco.o
PerEnc_EuCarrierFreqsGERAN_followingARFCNs_variableBitMapOfARFCNs_str <= USED 0
PerEnc_EuCounterCheck_v8a0_IEs_lateNonCriticalExtension_str <= USED 0
PerEnc_EuVarLogMeasReport_r11_tce_Id_r10_str <= USED 0
PerEnc_EuVarLogMeasReport_r11_traceRecordingSessionRef_r10_str <= USED 0
;FILE lterrc_encs.o
PerEnc_EuCarrierFreqsGERAN <= USED 0
PerEnc_EuCarrierFreqsGERAN_equallySpacedARFCNs <= USED 0
PerEnc_EuCellsTriggeredList <= USED 0
PerEnc_EuCellsTriggeredList_physCellIdGERAN <= USED 0
PerEnc_EuCrossCarrierSchedulingConfig_r10_other_r10 <= USED 0
PerEnc_EuCrossCarrierSchedulingConfig_r10_own_r10 <= USED 0
PerEnc_EuExplicitListOfARFCNs <= USED 0
PerEnc_EuIMSI <= USED 0
PerEnc_EuMeasResultList2GERAN_r10 <= USED 0
PerEnc_EuPLMN_IdentityList3_r11 <= USED 0
PerEnc_EuPUCCH_ConfigDedicated_v1130_setup_1 <= USED 0
PerEnc_EuPUSCH_ConfigDedicatedSCell_r10 <= USED 0
PerEnc_EuRLF_TimersAndConstants_r9_setup <= USED 0
PerEnc_EuUEInformationResponse_v9e0_IEs <= USED 0
PerEnc_EuUE_BasedNetwPerfMeasParameters_v1250 <= USED 0
PerEnc_EuVarConnEstFailReport_r11 <= USED 0
PerEnc_EuVarMeasReport <= USED 0
PerEnc_EuVarMeasReportList <= USED 0
;FILE lterrcais.o
LteAisEncodeUeCapabilityRatContainer <= USED 0
;FILE lterrcband.o
;FILE lterrccell.o
LteRrcCellIsPcell <= USED 0
LteRrcCellMgrCalcEutraNcellNum <= USED 0
LteRrcCellMgrCheckCellPool <= USED 0
LteRrcCellMgrCheckFreqPool <= USED 0
LteRrcCellMgrCheckRliPool <= USED 0
LteRrcCellMgrClearFreq <= USED 0
LteRrcCellMgrDebugAssert466 <= USED 0
LteRrcCellMgrExtractCell <= USED 0
lteRrcCellSwapIntraInterFreq <= USED 0
;FILE lterrccer.o
LteCerConstructAndSendEutraProximityInd <= USED 0
LteCerGetRplmn <= USED 0
LteCerHandleT301Expiry <= USED 0
LteCerInitialReestRecord <= USED 0
LteCerSortFddUtraCellSrxlev <= USED 0
LteCerSortFddUtraFreqRscp <= USED 0
LteCerSortFreqRsrp <= USED 0
LtePdcpGmmReestablishReqForTest <= USED 0
LteRrcBuildConnEstNcellMeasResultGeran <= USED 0
LteRrcBuildConnEstNcellMeasResultUtra <= USED 0
LteRrcBuildMeasResultList2EUTRA_v9e0 <= USED 0
LteRrcBuildMeasResultList2UTRA_r9 <= USED 0
LteRrcBuildMeasResultListEUTRA_v1130 <= USED 0
LteRrcBuildMeasResultListGERAN <= USED 0
LteRrcCerSetAutoRedirect <= USED 0
LteRrcCheckMeasResultListEUTRA <= USED 0
LteRrcClearHandoverProtectTimer <= USED 0
LteRrcFreeMemEpsAsSecurityContextBeforeHandover <= USED 0
LteRrcSendEcphyMeasSubframePatternPCellConfigReq <= USED 0
LteRrcTranslatePresentNeedOP <= USED 0
;FILE lterrccerrb.o
LteCerRbGetNotSuspendedSrbs <= USED 0
;FILE lterrccsr.o
ErrcDsIsErrcSuspended <= USED 0
LteCsrAddCurrentCellToEarfcnList <= USED 0
LteCsrCheckIfDrxFindCellisNeed <= USED 0
LteCsrGetSib5Index <= USED 0
LteCsrIratDecodePlmn <= USED 0
LteCsrIsNorthAmericanMcc <= USED 0
LteCsrIsRequestPlmnListContainCMCC <= USED 0
LteCsrSearchNorthAmericaMccInSib1 <= USED 0
LteCsrSendErrcSacLocationCnf <= USED 0
LteCsrSetLtePlmnStateForIcsSelection <= USED 0
LteCsrSetSib6Sib7 <= USED 0
LteCsrStartTreselection <= USED 0
LteRrcCheckIfLowThanMinSuitableCellRsrp <= USED 0
LteRrcCsrCheckIfNcellOnIntendedBand <= USED 0
LteRrcCsrPlmnSearchContextInit <= USED 0
LteRrcCsrSaveRsrpRsrq <= USED 0
LteRrcQueryTddConfig <= USED 0
;FILE lterrccsrics.o
;FILE lterrccsrplms.o
LteCsrGetPlmnFromNcellSib1AndSendToUrr <= USED 0
;FILE lterrccsrutils.o
;FILE lterrcdsds.o
ERRC_GRR_2_TASK_ID <= USED 0
LteRrcDsdsGetSim2Activated <= USED 0
LteRrcDsdsSendErrcSuspendCnfWhenLeavingConnected <= USED 0
;FILE lterrcete.o
LteEteHandleErrcActivateTestModeReq <= USED 0
LteEteHandleErrcDeactivateTestModeReq <= USED 0
LteEteHandleErrcLteModeReq <= USED 0
LteEteHandleUnitaryTestInit <= USED 0
LteEteSendEcphyEngInfoReq <= USED 0
LteEteSendEcphyStopEngInfoReq <= USED 0
LteRrcCheckCardNum <= USED 0
LteRrcCheckIfCsPagingOrImsPaging <= USED 0
LteRrcCopyAsn1Bits <= USED 0
LteRrcCopyEuCellInfoListUTRA_TDD_r10 <= USED 0
LteRrcCopyLteCrsAssistanceInfo <= USED 0
LteRrcCopyLteMbsfnSubframeConfig <= USED 0
LteRrcGetTddFddMode <= USED 0
LteRrcPhyConfigDedParaFitForNonCA <= USED 0
LteRrcReoderSignalSequence <= USED 0
LteRrcSendErrcDeactReq <= USED 0
LteRrcVarMemInit <= USED 0
;FILE lterrciratcell.o
LteMcrIratVarMeasReportDelGeranCell <= USED 0
LteRrcIratCellMgrCalcFddNcellNum <= USED 0
LteRrcIratCellMgrCalcGsmNcellNum <= USED 0
LteRrcIratCellMgrDelAllMeasIdOnFddUtraFreqs <= USED 0
LteRrcIratCellMgrDelAllMeasIdOnGeranNcellFreqsEx <= USED 0
LteRrcIratCellMgrDelMeasObjectGeran <= USED 0
LteRrcIratCellMgrDelMeasUtraFreq <= USED 0
LteRrcIratCellMgrDiscardReselPriorityFromIdleMobilityInfoOrOtherRat <= USED 0
;FILE lterrciratcsrp.o
LteIratCsrpDoFddUmtsPlmnSearch <= USED 0
LteIratCsrpHandlePlmnListReq <= USED 0
LteIratSetFddDrxFindParaForResume <= USED 0
LteIratSetGsmBsicParaForResume <= USED 0
LteLogIratDebugAirInterfaceRxBchInd <= USED 0
LteRrcIratMbcchGsmCellInfoFree <= USED 0
LteRrcIratMbcchGsmCellInfoInit <= USED 0
;FILE lterrclogcxt.o
LteRrcHandleCellInfoLogConfig <= USED 0
LteRrcHandleStatisticsLogConfig <= USED 0
LteRrcUpdateChangeCellReestSuccessStatistics <= USED 0
LteRrcUpdateEstFailureStatistics <= USED 0
LteRrcUpdateEstStartStatistics <= USED 0
LteRrcUpdateEstSuccessStatistics <= USED 0
LteRrcUpdateNotChangeCellReestSuccessStatistics <= USED 0
LteRrcUpdateReconfCompleteStatistics <= USED 0
LteRrcUpdateReconfStartStatistics <= USED 0
LteRrcUpdateReestFailureStatistics <= USED 0
LteRrcUpdateReestStartStatistics <= USED 0
LteRrcUpdateReselFailureStatistics <= USED 0
LteRrcUpdateReselStartStatistics <= USED 0
LteRrcUpdateRlFailureStatistics <= USED 0
;FILE lterrcmcr.o
LteMcrChangeA3ConditionTimeToTrigger <= USED 0
LteMcrCheckProximityEnteredForEutra <= USED 0
LteMcrCheckProximityEnteredForUtra <= USED 0
LteMcrGetMeasObjectFddUtra <= USED 0
LteMcrGetMeasObjectGeran <= USED 0
LteRrcCellMgrAddLteFreqCellFromMeasObject <= USED 0
LteRrcCellMgrAddMeasIdOnLteFreqbyMeasConfig <= USED 0
;FILE lterrcsecurity.o
;FILE lterrcsir.o
LteSirClearVisitedCellDb <= USED 0
LteSirCopySib8Data <= USED 0
LteSirFlushCmasSib12 <= USED 0
LteSirFlushEtwsSib11 <= USED 0
;FILE lterrctask.o
LteRrcTask1 <= USED 0
;FILE lterrctask2.o
LteRrcTask21 <= USED 0
;FILE lterrcutils.o
LteRrcCsrGetOtherCardLteIsOos <= USED 0
LteRrcGetLogInfo <= USED 0
LteRrcGetLogInit <= USED 0
LteRrcGetSystemInformationBlock <= USED 0
;FILE lterrcwifi.o
;FILE lwip_api.o
lwip_debug_dumppbufs <= USED 0
lwip_get_adjust_pp_flag <= USED 0
lwip_get_debug_setting_by_name <= USED 0
lwip_get_default_nw6_ifname <= USED 0
lwip_get_default_nw_ifname <= USED 0
lwip_get_dns_api_cache_flag <= USED 0
lwip_get_dns_bind_port <= USED 0
lwip_get_dns_max_retries <= USED 0
lwip_get_dns_relay_server <= USED 0
lwip_get_dns_wait_tmr <= USED 0
lwip_get_loopif_input_flag <= USED 0
lwip_get_netif_lte6_ifname <= USED 0
lwip_get_netif_pc_ifname <= USED 0
lwip_get_netif_wifi_ifname <= USED 0
lwip_get_nw_status <= USED 0
lwip_get_nw_status_ip6 <= USED 0
lwip_get_rndis_hotplug_flag <= USED 0
lwip_get_rs_backoff <= USED 0
lwip_get_send_ra_all_flag <= USED 0
lwip_get_tcp_backoff <= USED 0
lwip_get_tcp_msl_val <= USED 0
lwip_get_tcp_persist_backoff <= USED 0
lwip_get_tcp_rto_init <= USED 0
lwip_get_tcp_sync_backoff <= USED 0
lwip_get_wan_access_port <= USED 0
lwip_get_wan_access_web_flag <= USED 0
lwip_ifexist <= USED 0
lwip_manul_adjust_pp <= USED 0
lwip_nwst_set_callback <= USED 0
lwip_port_fwd_rule_add <= USED 0
lwip_reset_dns_relay_server <= USED 0
lwip_set_adjust_pp_flag <= USED 0
lwip_set_debug_setting <= USED 0
lwip_set_dhcpd_server_flag <= USED 0
lwip_set_dns_api_cache_flag <= USED 0
lwip_set_dns_max_retries <= USED 0
lwip_set_dns_relay_port <= USED 0
lwip_set_dns_relay_server <= USED 0
lwip_set_dns_wait_tmr <= USED 0
lwip_set_dns_wait_tmr_default <= USED 0
lwip_set_dnsr_hijack_flag <= USED 0
lwip_set_loopif_input_flag <= USED 0
lwip_set_mifi_flag <= USED 0
lwip_set_ota_ip6addr_rsra_flag <= USED 0
lwip_set_ota_ip6addr_using_flag <= USED 0
lwip_set_reuseaddr_sp1_flag <= USED 0
lwip_set_rndis_hotplug_flag <= USED 0
lwip_set_rs_backoff <= USED 0
lwip_set_rs_backoff_default <= USED 0
lwip_set_rs_retry_counter <= USED 0
lwip_set_send_ra_all_flag <= USED 0
lwip_set_solinger_sp1_flag <= USED 0
lwip_set_tcp_backoff <= USED 0
lwip_set_tcp_backoff_default <= USED 0
lwip_set_tcp_max_rtx <= USED 0
lwip_set_tcp_persist_backoff <= USED 0
lwip_set_tcp_persist_backoff_default <= USED 0
lwip_set_tcp_rto_init <= USED 0
lwip_set_tcp_rto_update_flag <= USED 0
lwip_set_tcp_rto_update_value <= USED 0
lwip_set_tcp_syn_max_rtx <= USED 0
lwip_set_tcp_sync_backoff <= USED 0
lwip_set_tcp_sync_backoff_default <= USED 0
lwip_set_tcpip_rate <= USED 0
lwip_set_wan_access_port <= USED 0
lwip_set_wan_access_web_flag <= USED 0
;FILE lwip_atctl.o
;FILE lwip_customer.o
lwip_parameters_customer_defined <= USED 0
;FILE lwip_init.o
;FILE lwip_minis.o
ip_flow_stats_er <= USED 0
lwip_cus_packet_to_modem <= USED 0
lwip_get_dns_max_wait_time <= USED 0
lwip_set_arp_mask_match_flag <= USED 0
lwip_set_dhcp6d_statefull_en <= USED 0
lwip_set_mac_filter_flag <= USED 0
lwip_set_tcp_msl_val <= USED 0
;FILE lwip_stats.o
_ip_flow_stats_er <= USED 0
get_ip_flow_stats <= USED 0
lwip_get_dlrate_flag <= USED 0
lwip_get_dlrate_rate <= USED 0
lwip_set_dlrate_flag <= USED 0
lwip_set_dlrate_rate <= USED 0
netif_get_all_packets <= USED 0
netif_get_uap_packets <= USED 0
netif_get_usb_packets <= USED 0
netif_get_wifi_packets <= USED 0
;FILE lwip_sttest.o
lwip_sttest_delet_task_table <= USED 0
;FILE lzop.o
write_header <= USED 0
;FILE lzop_buf.o
lzo_write16 <= USED 0
lzo_write32 <= USED 0
lzo_write_buf <= USED 0
write_magic <= USED 0
;FILE macstats.o
;FILE mat_response.o
MATGetDataFromStr <= USED 0
MATGetStrFromData <= USED 0
_MATConfIndCB <= USED 0
;FILE mat_token.o
MATCheckBitStreamToken <= USED 0
MATTokenDump <= USED 0
;FILE mbndcfg.o
;FILE mci_audio.o
MCI_AUD_StreamPlay <= USED 0
MCI_AUD_StreamPlayPCM <= USED 0
MCI_AddedData <= USED 0
MCI_AifSetPhoneStatus <= USED 0
MCI_AudioClosePath <= USED 0
MCI_AudioFinished <= USED 0
MCI_AudioGetDurationTime <= USED 0
MCI_AudioGetFileInformation <= USED 0
MCI_AudioGetOpenedPath <= USED 0
MCI_AudioOpenPath <= USED 0
MCI_AudioPauseTone <= USED 0
MCI_AudioPlayBuffer <= USED 0
MCI_AudioPlayBufferWithType <= USED 0
MCI_AudioPlayPause <= USED 0
MCI_AudioPlayResume <= USED 0
MCI_AudioPlayTone <= USED 0
MCI_AudioPlayWithOffloadMode <= USED 0
MCI_AudioPlayWithType <= USED 0
MCI_AudioResumeTone <= USED 0
MCI_AudioSeekTo <= USED 0
MCI_AudioStopBuffer <= USED 0
MCI_AudioStopStreaming <= USED 0
MCI_AudioStopTone <= USED 0
MCI_AudioStreamingPlay <= USED 0
MCI_DataFinished <= USED 0
MCI_DeviceSetForceUse <= USED 0
MCI_GetBufPosition <= USED 0
MCI_GetRemain <= USED 0
MCI_GetRoute <= USED 0
MCI_GetWriteBuffer <= USED 0
MCI_RingFinished <= USED 0
MCI_SetBuffer <= USED 0
MCI_SetDeviceConnectionState <= USED 0
MCI_SetDeviceMute <= USED 0
MCI_SetVoiceState <= USED 0
_Z15MCI_AudioGetID3Pc <= USED 0
;FILE mcu_address_init.o
;FILE mcu_dsp_common.o
WebRtcNetEQ_DSP2MCUinterrupt <= USED 0
;FILE mcu_reset.o
;FILE md.o
mbedtls_md <= USED 0
mbedtls_md_clone <= USED 0
mbedtls_md_finish <= USED 0
mbedtls_md_get_name <= USED 0
mbedtls_md_get_size <= USED 0
mbedtls_md_get_type <= USED 0
mbedtls_md_hmac_reset <= USED 0
mbedtls_md_info_from_string <= USED 0
mbedtls_md_init_ctx <= USED 0
mbedtls_md_list <= USED 0
mbedtls_md_process <= USED 0
mbedtls_md_starts <= USED 0
mbedtls_md_update <= USED 0
;FILE md5.o
mbedtls_md5 <= USED 0
mbedtls_md5_process <= USED 0
;FILE mdi_testbench.o
;FILE media_format_type.o
_ZN14streamingmedia19transformToMimeTypeE15MediaFormatType <= USED 0
_ZN14streamingmedia23transformToAudioEncoderE15MediaFormatType <= USED 0
_ZN14streamingmedia23transformToVideoEncoderE15MediaFormatType <= USED 0
;FILE media_log.o
_Z23media_log_filter_removeN7android7String8E <= USED 0
__media_log_print <= USED 0
;FILE memory_recorder.o
;FILE mep.o
MEPPhase1Iinit <= USED 0
MEPPhase1Iinit_2 <= USED 0
MEP_UpdateSignature <= USED 0
MEP_UpdateToMRD <= USED 0
UDP_PutASL <= USED 0
UDP_PutASL_Callback <= USED 0
UdpInit <= USED 0
UdpInit_2 <= USED 0
;FILE min_max.o
max16 <= USED 0
max8 <= USED 0
min16 <= USED 0
min8 <= USED 0
;FILE minilzo.o
lzo1x_1_compress <= USED 0
;FILE mm.o
MmTask1 <= USED 0
;FILE mm_api.o
MM_GetIndStatus <= USED 0
MM_SetWbCellLock <= USED 0
resetMmParas <= USED 0
;FILE mm_as.o
IratDLParameterSynchronization <= USED 0
MmDualGrrChekDMmIsMmInDedicate <= USED 0
MmEmmProcessIratCsfbFromLteFailInd <= USED 0
MmSendRegOrCampInd <= USED 0
;FILE mm_cb.o
;FILE mm_cm.o
;FILE mm_lu.o
;FILE mm_mmr.o
MmIsUmtsMultiMode <= USED 0
MmSendMmrFlushFlashEnd <= USED 0
MmSendMmrLlcInfoInd <= USED 0
;FILE mm_rr.o
;FILE mm_rxmsg.o
;FILE mm_sim.o
;FILE mm_timer.o
MmInitiRatGuardTimer <= USED 0
MmStart3212Timer <= USED 0
MmStartCsConEstGuardTimer <= USED 0
;FILE mm_txmsg.o
;FILE mm_utils.o
MmDualHandleLogOn <= USED 0
MmDualIsDmmActStatus <= USED 0
MmDualIsDmmInLTEMode <= USED 0
MmDualIsDmmPendingMtCall <= USED 0
MmDualMmHandlePageInd <= USED 0
MmDualMmHandleReqWhenDMmInPLMNSearch <= USED 0
MmDualMmIsDMmLiveDedicate <= USED 0
MmDualMmLogMessage <= USED 0
MmDualUmmHandleServiceRequestBeforeSuspend <= USED 0
MmDualUpdateAsAndSendCampReq <= USED 0
MmIsGsmSupported <= USED 0
MmNwModeBitToNwMode <= USED 0
MmPendEmmEsmUnitDataReq <= USED 0
MmPendGmmSmUnitDataReq <= USED 0
MmPendRrcPageInd <= USED 0
MmPrcessMmxxEstReqUnderLte <= USED 0
MmReceiveMTCall <= USED 0
MmSimIsCMCC <= USED 0
MmSimIsCUCC <= USED 0
MmTddBandBitToTddBandNum <= USED 0
MmUtGetSimData <= USED 0
ischinamcc <= USED 0
mmEmeiRead <= USED 0
nvm_flush_ps_release <= USED 0
;FILE modem_mgr.o
;FILE modemlwip.o
ims_get_register_flag <= USED 0
ims_set_register_flag <= USED 0
lwip_downlink_is_reach_hwm <= USED 0
usb_uplink_is_reach_hwm <= USED 0
;FILE modemvolte.o
ModemCiPrimitiveCnfFileter <= USED 0
ModemDlIpFilterToLte <= USED 0
ModemIsVolteUplinkDataInQueue <= USED 0
ModemParserCidNeedToVolte <= USED 0
ModemVolteRxDataPoll <= USED 0
ModemVolteRxDataRelease <= USED 0
ModemVolteTxPacketGet <= USED 0
ModemVolteTxPacketPut <= USED 0
ModemVolteTxPacketPutAll <= USED 0
VoLteTxProcessCallBackRegister <= USED 0
VoLteTxProcessDone <= USED 0
VoLteUpLinkPacketAlloc <= USED 0
VoLteUpLinkPacketFree <= USED 0
add_ipv4_filter_rule <= USED 0
add_ipv6_filter_rule <= USED 0
isInVolteBuffer <= USED 0
remove_ip_filter_rule <= USED 0
;FILE mpu.o
;FILE msg_api.o
MSG_CRSM_DeleteMsg <= USED 0
MSG_CRSM_ReadMsg <= USED 0
MSG_CRSM_WriteMsgWithIndex <= USED 0
MSG_LockSMSStatus <= USED 0
MSG_NewMsgAck <= USED 0
MSG_RSTMemFull <= USED 0
resetMsgOperFlag <= USED 0
resetMsgParas <= USED 0
;FILE multi_irat_common.o
SetUeMobilitySimuEnable <= USED 0
multiIratL1GetRAT <= USED 0
;FILE multipart.o
MultiPart_GetDiscardText <= USED 0
;FILE multipart_utils.o
multiPart_FormatDiscardText <= USED 0
;FILE mvUsbDevCh9.o
USB_IS_CONNECTED <= USED 0
ch9Class <= USED 0
mvUsbCh9ProcessVendorRequest <= USED 0
;FILE mvUsbDevMain.o
_usb_device_deinit_endpoint <= USED 0
_usb_device_get_handle <= USED 0
_usb_device_get_max_endpoint <= USED 0
_usb_device_get_transfer_details <= USED 0
_usb_device_shutdown <= USED 0
_usb_device_trace_dtd_information <= USED 0
;FILE mvUsbDevRecv.o
;FILE mvUsbDevSend.o
;FILE mvUsbDevUtl.o
_usb_debug_get_flags <= USED 0
_usb_debug_init_trace_log <= USED 0
_usb_debug_print_trace_log <= USED 0
_usb_device_assert_resume <= USED 0
_usb_device_unstall_endpoint <= USED 0
_usb_dump_regs <= USED 0
_usb_ep_status <= USED 0
;FILE mvUsbHsDevCncl.o
;FILE mvUsbHsDevMain.o
_usb_dci_vusb20_deinit_endpoint <= USED 0
_usb_dci_vusb20_diag_tx_isr <= USED 0
_usb_dci_vusb20_get_transfer_details <= USED 0
_usb_dci_vusb20_remote_wakeup <= USED 0
_usb_dci_vusb20_shutdown <= USED 0
_usb_dci_vusb20_suspend_tx_transter <= USED 0
_usb_dci_vusb20_trace_dtd_information <= USED 0
;FILE mvUsbHsDevUtl.o
_usb_dci_vusb20_assert_resume <= USED 0
;FILE mvUsbLog.o
mvUsbLogGetInfo <= USED 0
mvUsbLogSaveToFileSystem <= USED 0
mvUsbLoggingUninit <= USED 0
;FILE mvUsbMemory.o
;FILE mvUsbModem.o
mvUsbModemUartInitialize <= USED 0
mvUsbModemUartSendData <= USED 0
mvUsbQueryATPort <= USED 0
mvUsbSetModemParameters <= USED 0
;FILE mvUsbNet.o
MbimConnectionSpeedChangeNotification <= USED 0
MbimNetworkConnectionNotification <= USED 0
MbimSendRspAvailableNotification <= USED 0
mvUsbCIDRespFromMbim <= USED 0
mvUsbNetCtrlHISR <= USED 0
mvUsbNetCtrlTxLISR <= USED 0
mvUsbNetMacCmp <= USED 0
mvUsbNetRemoveHdr <= USED 0
mvUsbNetReqTask <= USED 0
mvUsbNetResetRspQ <= USED 0
mvUsbNetRxHISR <= USED 0
mvUsbNetRxLISR <= USED 0
mvUsbNetTxDequeueAndTransfer <= USED 0
mvUsbNetTxHISR <= USED 0
mvUsbNetTxLISR <= USED 0
mvUsbSetNetType <= USED 0
;FILE mvUsbStorage.o
_process_Rezero_Unit <= USED 0
mvUsbStorage_test_unit_ready_response <= USED 0
;FILE mylinux_imp.o
;FILE nand.o
GetNANDProperties <= USED 0
InitializeNANDDevice <= USED 0
RawNAND_DirectReadSctor <= USED 0
RawNAND_ProgramPageDMA <= USED 0
RawNAND_ProgramSectorSpare <= USED 0
RawNAND_ReadPageDMA <= USED 0
RawNAND_ReadSectorSpare <= USED 0
ResetRawNAND <= USED 0
;FILE nand_bbm.o
NAND_GetNewBlock <= USED 0
NAND_GetRemapBlock <= USED 0
NAND_IdentifyBadBlock <= USED 0
NAND_LoadMapTabletoBuf <= USED 0
NAND_MapTableWriteBack <= USED 0
NAND_MarkBadBlock <= USED 0
;FILE nand_hal.o
NAND_Init <= USED 0
NAND_ReadBlocktoBuf <= USED 0
NAND_ReadSector <= USED 0
NAND_WriteBack <= USED 0
NAND_WriteSector <= USED 0
hal_NandGetCapacity <= USED 0
hal_NandReadBlock <= USED 0
hal_NandWriteBlock <= USED 0
;FILE nas_cfg.o
;FILE nat_gre.o
;FILE nat_pptp.o
pptp_info_tmr <= USED 0
;FILE nd6.o
nd6_get_prefix_length <= USED 0
reset_netif_ipv6_flg_all <= USED 0
reset_netif_ipv6_flg_part <= USED 0
;FILE netIntfListener.o
NetIf_RemoveRouteEx <= USED 0
NetIntf_StartListenOn <= USED 0
;FILE net_bridge.o
net_pool_fetch_head <= USED 0
net_pool_fetch_size <= USED 0
;FILE net_pkt.o
;FILE netbuf.o
netbuf_alloc <= USED 0
netbuf_chain <= USED 0
netbuf_data <= USED 0
netbuf_first <= USED 0
netbuf_new <= USED 0
netbuf_next <= USED 0
;FILE netdb.o
lwip_getaddrinfo_with_pcid <= USED 0
lwip_gethostbyname_r <= USED 0
lwip_gethostbyname_r_with_netif <= USED 0
lwip_gethostbyname_r_with_pcid <= USED 0
lwip_gethostbyname_with_pcid <= USED 0
;FILE neteq_rtp.o
;FILE netif.o
_lwip_set_netif_tune <= USED 0
lwip_build_netif_tune_ifname <= USED 0
lwip_set_netif_tune <= USED 0
netif_check_nw_status_ip6 <= USED 0
netif_create_ip6_linklocal_address <= USED 0
netif_delete_tune <= USED 0
netif_find_by_cid <= USED 0
netif_find_by_name <= USED 0
netif_find_by_scid <= USED 0
netif_find_by_scid_dsim <= USED 0
netif_get_status <= USED 0
netif_get_status_by_cid <= USED 0
netif_get_status_by_cid_dsim <= USED 0
netif_local_if_exist <= USED 0
netif_nw_router_check <= USED 0
netif_pc_get_mac <= USED 0
netif_poll <= USED 0
netif_ppp_set_status <= USED 0
netif_ready <= USED 0
netif_remove_bypass <= USED 0
netif_remove_callback <= USED 0
netif_remove_tune <= USED 0
netif_set_bypass <= USED 0
netif_set_default_nw_status <= USED 0
netif_set_default_nw_status_ip6 <= USED 0
netif_set_ip6_global <= USED 0
netif_set_link_down <= USED 0
netif_set_link_up <= USED 0
netif_set_remove_callback <= USED 0
netif_set_tune <= USED 0
netif_share_nw_exist <= USED 0
netif_tune_output <= USED 0
netif_tune_output_ipv4 <= USED 0
netif_tune_output_ipv6 <= USED 0
netif_tune_process <= USED 0
netif_tune_set_ipv4 <= USED 0
netif_tune_set_ipv6 <= USED 0
netif_uap_get_mac <= USED 0
netif_uap_get_status <= USED 0
netif_uap_set_status <= USED 0
netif_wifi_status <= USED 0
pc_netif_init_done <= USED 0
;FILE netif_pc.o
is_usb_mbox_avaliable <= USED 0
mvUsbNetGetPacket <= USED 0
netif_pc_rndis_init <= USED 0
;FILE netif_ppp.o
netif_ppp_reset_ip <= USED 0
pppnet_GetPacket_cb <= USED 0
;FILE netif_td.o
lte_construct_data_for_lte <= USED 0
lte_dl_mem_free_for_wifi <= USED 0
lte_ul_pbuf_reshape_with_cpy <= USED 0
lwip_set_scid_table_dsim <= USED 0
netif_get_default_nw_ip6addr <= USED 0
netif_td_send_data <= USED 0
td_get_packet <= USED 0
td_get_rx_speed <= USED 0
td_get_tx_speed <= USED 0
;FILE netif_wifi_uap.o
netif_recv_from_wifi <= USED 0
netif_wifi_init <= USED 0
netif_wifi_internal_init <= USED 0
netifapi_get_wifi_macaddr <= USED 0
;FILE netifapi.o
;FILE netifconfig.o
_Z16EnableInfterfaceb <= USED 0
_Z20addInferfaceBearerIdi <= USED 0
_Z23removeInferfaceBearerIdi <= USED 0
_Z24net_add_ipv4_filter_rulehjtjt <= USED 0
_Z24net_add_ipv6_filter_rulehPhtS_t <= USED 0
_Z25net_remove_ip_filter_rulei <= USED 0
;FILE network_selection.o
;FILE ningbo.o
NingboDisableWDT <= USED 0
NingboLcdBackLightDisable <= USED 0
NingboLcdBackLightEnable <= USED 0
NingboLcdBackLightSwitch <= USED 0
NingboTorchLightDisable <= USED 0
NingboTorchLightEnable <= USED 0
NingboVibratorCtrl <= USED 0
NingboVibratorDisable <= USED 0
NingboVibratorEnable <= USED 0
Ningbo_Aditional_Workaround <= USED 0
Ningbo_BIAS_OUT_OFF <= USED 0
Ningbo_BIAS_OUT_ON <= USED 0
Ningbo_DISABLE_BATTEMP_MEAS <= USED 0
Ningbo_ENABLE_BATTEMP_MEAS <= USED 0
Ningbo_GPADC_READ_TEMP_MEAS <= USED 0
Ningbo_Ldo_10_set <= USED 0
Ningbo_Ldo_10_set_2_8 <= USED 0
Ningbo_Ldo_11_set <= USED 0
Ningbo_Ldo_11_set_2_8 <= USED 0
Ningbo_Ldo_12_set <= USED 0
Ningbo_Ldo_12_set_1_8 <= USED 0
Ningbo_Ldo_12_set_2_8 <= USED 0
Ningbo_Ldo_13_set <= USED 0
Ningbo_Ldo_13_set_1_8 <= USED 0
Ningbo_Ldo_13_set_3_0 <= USED 0
Ningbo_Ldo_4_set_1_8 <= USED 0
Ningbo_Ldo_4_set_3_0 <= USED 0
Ningbo_Ldo_8_set_2_8 <= USED 0
Ningbo_Ldo_9_set_3_3 <= USED 0
Ningbo_SoftwareNotProceedUntilReach3400 <= USED 0
Ningbo_VBUCK1_Set_FPWM <= USED 0
Ningbo_VBUCK_Set_DVC_Enable <= USED 0
Ningbo_VsimSleep_Disable <= USED 0
Ningbo_VsimSleep_Enable <= USED 0
Ningbo_miccoConfigUsimV <= USED 0
Ningbo_miccoDisableUsimV <= USED 0
Ningbo_miccoEnableUsimV <= USED 0
isLcdEverInitedUponBooting <= USED 0
ningbo_read_volt_meas_val <= USED 0
pm813_cc_current_get <= USED 0
pm813_cc_current_set <= USED 0
pm813_cc_current_set_toohigh <= USED 0
pm813_cccv_timer_set <= USED 0
pm813_charge_termination_current_set <= USED 0
pm813_charger_disable_vbus_uv_set <= USED 0
pm813_charger_force_check <= USED 0
pm813_charger_in_charging <= USED 0
pm813_charger_internal_drv_segment_set <= USED 0
pm813_charger_mosfet_drv_segment_set <= USED 0
pm813_charger_restore <= USED 0
pm813_charger_set_cc_mode <= USED 0
pm813_charger_set_normal_mode <= USED 0
pm813_charger_timer_switch <= USED 0
pm813_charger_vbat_4p4_set <= USED 0
pm813_charger_vbus_detect_int_select_set <= USED 0
pm813_current_check_timer_set <= USED 0
pm813_fault_clear <= USED 0
pm813_force_charger_state_get <= USED 0
pm813_force_charger_state_set <= USED 0
pm813_get_GPADC1_0_Diff_meas <= USED 0
pm813_get_Icharge_meas_cur_mA <= USED 0
pm813_get_Vcharge_meas_vol_mv <= USED 0
pm813_get_bat_slp_vol <= USED 0
pm813_get_bat_vol <= USED 0
pm813_get_batid_meas_vol_mv <= USED 0
pm813_get_battemp_meas_vol_mv <= USED 0
pm813_get_battery_status <= USED 0
pm813_get_battery_voltage_withoutCharger <= USED 0
pm813_get_chargerError <= USED 0
pm813_get_charger_FSM_state <= USED 0
pm813_get_charger_status <= USED 0
pm813_gpadc_write_vol_meas <= USED 0
pm813_if_charge_full <= USED 0
pm813_if_charge_in_cv_mode <= USED 0
pm813_keypadbacklight_status_get <= USED 0
pm813_lcdbacklight_status_get <= USED 0
pm813_long_onkey_to_powerdown_enable <= USED 0
pm813_measured_current_means_charging <= USED 0
pm813_precharge_current_set <= USED 0
pm813_precharge_timer_set <= USED 0
pm813_set_battery_temp_reference <= USED 0
pm813_set_charger_fsm_uvsys_3v2 <= USED 0
pm813_set_max_cc_current <= USED 0
pm813_torchlight_status_get <= USED 0
pm813_trickle_timer_set <= USED 0
pm813_userdata_get_app_lcd_ever_configed <= USED 0
pm813_userdata_get_for_automation_test <= USED 0
pm813_userdata_set_app_lcd_ever_configed <= USED 0
pm813_userdata_set_for_automation_test <= USED 0
pm813_vbat_set <= USED 0
pm813_vibrator_status_get <= USED 0
pm813_voltage_check_timer_set <= USED 0
;FILE ntp.o
GetNTPStatus <= USED 0
GetNtpResult <= USED 0
SetNTPStatus <= USED 0
;FILE nvmClient_ttc.o
NVMCFileEof_ram <= USED 0
NVMCLinkStatusIndCB_Mux <= USED 0
NVMCPhase1Init <= USED 0
NVMCPhase1Init_ram <= USED 0
NVMCPhase2Init_ram <= USED 0
NVMCRxInd_ACIPC <= USED 0
NVMCRxInd_Mux <= USED 0
NVMCTxDoneCnf_ACIPC <= USED 0
NVMCTxDoneCnf_Mux <= USED 0
NVMCTxReq_Mux <= USED 0
NVM_RAM_chmod <= USED 0
NVM_RAM_clearerr <= USED 0
NVM_RAM_fchmod <= USED 0
NVM_RAM_feof <= USED 0
NVM_RAM_ferror <= USED 0
NVM_RAM_fileterminate <= USED 0
NVM_RAM_fstat <= USED 0
NVM_RAM_ftell <= USED 0
NVM_RAM_rename <= USED 0
NVM_RAM_stat <= USED 0
;FILE oneNAND.o
CheckReadDisturb <= USED 0
ComputeDivisorShiftFromPowerOf2 <= USED 0
CopyBackProgram <= USED 0
EraseResume <= USED 0
EraseSuspend <= USED 0
EraseVerifyRead <= USED 0
GetBlockNums <= USED 0
GetBlockSize <= USED 0
GetDeviceType <= USED 0
GetManufactureID <= USED 0
GetMuxType <= USED 0
GetPageSize <= USED 0
LoadSector <= USED 0
LoadSpareSector <= USED 0
LockNANDArray <= USED 0
LockTightNANDArray <= USED 0
MultiBlockErase <= USED 0
OTPAccess <= USED 0
OneNAND_DirectReadSctor <= USED 0
OneNAND_EraseAll <= USED 0
OneNAND_Init <= USED 0
OneNAND_ProgramPageDMA <= USED 0
OneNAND_ProgramSectorSpare <= USED 0
OneNAND_ReadPageDMA <= USED 0
OneNAND_ReadSectorSpare <= USED 0
ProgramSector <= USED 0
ProgramSpareSector <= USED 0
ResetNANDCore <= USED 0
ResetOneNAND <= USED 0
;FILE osa_common_init.o
OSAMemPoolCreateExt <= USED 0
OSATaskCreateEx <= USED 0
OsaGetMaxThreadCount <= USED 0
;FILE osa_common_run.o
uiTaskFlagSet <= USED 0
uiTaskMsgQAndFlagCreate <= USED 0
uiTaskMsgQAndFlagWait <= USED 0
uiTaskMsgQReceive <= USED 0
uiTaskMsgQSend <= USED 0
;FILE osa_mem.o
IsLFSForceFlushPool <= USED 0
OsaMemAllocAgain <= USED 0
OsaMemGetFirstPoolRef <= USED 0
OsaMemGetUserParam <= USED 0
OsaMemSetUserParam <= USED 0
OsaMem_InitPools <= USED 0
;FILE osa_net_if.o
OSANetif_GetIPv6Info <= USED 0
OSA_Arp_Add <= USED 0
OSA_Arp_Remove <= USED 0
OSA_NetIF_Register_Cb <= USED 0
OSA_NetIf_Details_Get <= USED 0
OSA_NetIf_Details_Set <= USED 0
OSA_NetIf_Disable <= USED 0
OSA_NetIf_Down <= USED 0
OSA_NetIf_Enumerate <= USED 0
OSA_NetIf_Get2AdaptName <= USED 0
OSA_NetIf_Get_IPv4_Address <= USED 0
OSA_NetIf_Init <= USED 0
OSA_NetIf_Is_Interface_Up <= USED 0
OSA_NetIf_Set_IPv4_Address <= USED 0
OSA_NetIf_Set_IPv6_Address <= USED 0
OSA_NetIf_Set_MTU <= USED 0
OSA_NetIf_Shudown <= USED 0
OSA_NetIf_StatusToStr <= USED 0
OSA_NetIf_Status_Get <= USED 0
OSA_NetIf_Up <= USED 0
OSA_Netif_Get_IPv6_Address <= USED 0
OSA_Route_Add <= USED 0
OSA_Route_Remove <= USED 0
;FILE osa_tx_init.o
OsaCriticalSectionDelete <= USED 0
OsaHISRGetPriority <= USED 0
OsaHisrDel <= USED 0
OsaInit <= USED 0
OsaMailboxQDelete <= USED 0
OsaRun <= USED 0
OsaTaskCreateEx <= USED 0
OsaTaskStackMagic <= USED 0
OsaTaskTerminate <= USED 0
;FILE osa_tx_run.o
OsaContextLockExt <= USED 0
OsaContextRestoreExt <= USED 0
OsaMailboxQSend <= USED 0
OsaTaskYield <= USED 0
Osa_Init <= USED 0
;FILE osa_tx_utils.o
OSAHISRGetAppParam1 <= USED 0
OSAHISRGetEntry <= USED 0
OSAHISRGetName <= USED 0
OSAHISRGetStackSize <= USED 0
OSAHISRGetStackStart <= USED 0
OSAHISRList <= USED 0
OSAHISRSetAppParam1 <= USED 0
OSAMsgQFrontSend <= USED 0
OSAPartitionInUse <= USED 0
OSATaskGetEntryParam <= USED 0
OSATaskGetStackSize <= USED 0
OSATaskGetSysParam1 <= USED 0
OSATaskGetSysParam2 <= USED 0
OSATaskIsValid <= USED 0
OSATaskList <= USED 0
OSATaskSetSysParam1 <= USED 0
OSATaskSetSysParam2 <= USED 0
OsaGetCreatedThreadCount <= USED 0
OsaGetCurrentThreadRef <= USED 0
OsaGetThreadListHead <= USED 0
OsaThreadList <= USED 0
;FILE osa_utils.o
;FILE packet_buffer.o
WebRtcNetEQ_PacketBufferExtract <= USED 0
WebRtcNetEQ_PacketBufferFindLowestTimestamp <= USED 0
WebRtcNetEQ_PacketBufferGetDuration <= USED 0
;FILE pb_api.o
PB_CRSM_DeleteEntry <= USED 0
PB_CRSM_ReadOneEntry <= USED 0
PB_CRSM_WriteEntry <= USED 0
PB_SQueryRead <= USED 0
PB_SQueryWrite <= USED 0
PB_SWriteEntry <= USED 0
PB_WriteEntry <= USED 0
decode_alpha_tag <= USED 0
encode_alpha_tag <= USED 0
encode_character <= USED 0
libGetSubaddrInfo <= USED 0
resetPbParas <= USED 0
;FILE pbuf.o
pbuf_add_header <= USED 0
pbuf_coalesce <= USED 0
pbuf_dechain <= USED 0
pbuf_fill_chksum <= USED 0
pbuf_free_mem <= USED 0
pbuf_get_at <= USED 0
pbuf_memcmp <= USED 0
pbuf_memfind <= USED 0
pbuf_strstr <= USED 0
pbuf_take <= USED 0
;FILE pcpsha1ca.o
;FILE pcscf_discovery.o
;FILE pdacchermon.o
;FILE pdbklhermon.o
;FILE pdcpgki.o
;FILE pdgpiostub.o
PdGpioAssertLine <= USED 0
PdGpioConfigure <= USED 0
PdGpioDeassertLine <= USED 0
PdGpioHandleDebounceTmrExp <= USED 0
PdGpioIntDisable <= USED 0
PdGpioIntEnable <= USED 0
PdGpioIntReEnable <= USED 0
PdGpioIsOutputLine <= USED 0
PdGpioLookUpReference <= USED 0
PdGpioReadInputLine <= USED 0
PdGpioReadOutputLine <= USED 0
PdGpoConfigure <= USED 0
;FILE pdkeypadhermon.o
PdKeypadInterrupt <= USED 0
PdKeypadSendMicKeyInd <= USED 0
;FILE pdpmhermon.o
PdPowerLatchOff <= USED 0
PdPowerLatchOn <= USED 0
;FILE pdrtchermon.o
PdRtcAlarmInt <= USED 0
PdRtcAlarmPoweredUp <= USED 0
PdRtcCausedPowerOn <= USED 0
PdRtcClearAlarmPowerUp <= USED 0
PdRtcInterrupt <= USED 0
;FILE pduscstub.o
DlCfUscGpio <= USED 0
DlCfUscInitialise <= USED 0
DlCfUscRouting <= USED 0
;FILE ping.o
;FILE pl_adap.o
;FILE pl_adap_dual.o
SetGsmL1RatToResetMode <= USED 0
getGsmDualModeFlag <= USED 0
setGsmDualModeFlag <= USED 0
wgiResetUtranMeasAfterSetUtran <= USED 0
;FILE pl_am_d_bind.o
plAMDRebindBindArray <= USED 0
plAMResetDBindArray <= USED 0
plAmGetBindFunction <= USED 0
plAmGsmSetCnfRebind <= USED 0
plAmGsmSetCnfRebind2 <= USED 0
plAmGsmSetCnfUnbind <= USED 0
plAmGsmSetCnfUnbind2 <= USED 0
plAmWbSetCnfRebind <= USED 0
plAmWbSetCnfUnbind <= USED 0
pldBindGsmCnf1 <= USED 0
pldBindGsmTerminateCnfOnlySim1 <= USED 0
pldBindL1WcdmaCapabilityReportInd <= USED 0
pldBindLteCnf <= USED 0
pldBindWcdmaCnf <= USED 0
pldBindWcdmaTerminateCnf <= USED 0
pldSetGsmCnfAtBcch <= USED 0
pldSetGsmCnfAtBcch2 <= USED 0
pldSetGsmCnfAtBcchNew <= USED 0
plwBindEdchSharedMemAddrInd <= USED 0
plwBindNoActionInd <= USED 0
plwMsRebindGsmTermCnf <= USED 0
plwMsRebindGsmTermCnf2 <= USED 0
plwMsUnbindGsmTermCnf <= USED 0
plwMsUnbindGsmTermCnf2 <= USED 0
;FILE pl_am_d_dflt.o
plAMGetBypassGsmRFRAMInitFlag <= USED 0
plAMGetRfInStandby <= USED 0
plAML1WbCapabilityReportReq <= USED 0
plAMReset <= USED 0
plAMSetGsm <= USED 0
plAMSetGsmCnf <= USED 0
plAMSetGsmHandle <= USED 0
plAMSetLte <= USED 0
plAMSetLteCnf <= USED 0
plAMSetRfInStandby <= USED 0
plAMTerminateGsm <= USED 0
plAMsendSetSysDet <= USED 0
plAMsendSetSysDet2otherSim <= USED 0
plAmGetRatSetCause <= USED 0
plAmGetSharedOpcode <= USED 0
plAmPrintTempAndVbatReading <= USED 0
plAmReadComCfgValues <= USED 0
plAmSendPldGsmCnf <= USED 0
;FILE pl_audioIf_api.o
plgAudioConversationOnOffSet <= USED 0
plgAudioHandOverStageGet <= USED 0
;FILE pl_d_api_traces.o
pldWcdmaCnfTrace <= USED 0
pldWcdmaTerminateCnfTrace <= USED 0
;FILE pl_dm_adap.o
pldmAdapAuChnDlConfigure <= USED 0
pldmAdapAuChnUlConfigure <= USED 0
pldmAdapAuNoiseSuppressionSetting <= USED 0
pldmAdapAuPlayBufferFree <= USED 0
pldmAdapAuPlayBufferGet <= USED 0
pldmAdapAuRecordBufferFree <= USED 0
pldmAdapAuRecordBufferGet <= USED 0
pldmAdapAuRecordStopSdvr <= USED 0
pldmAdapAuStopEncoder <= USED 0
pldmAdapAudioInit <= USED 0
pldmAdapBgDaiInOperation <= USED 0
pldmAdapInterruptInit <= USED 0
pldmAdapMicInitialise <= USED 0
pldmAdapNumAdcSeqRunning <= USED 0
pldmAdapNvramSelectCal <= USED 0
pldmAdapSendAdcIntReadCnf <= USED 0
pldmAdcSeqRunning <= USED 0
;FILE pl_dm_api.o
pldmBindAdcEvents <= USED 0
pldmBindAdcSeqRunning <= USED 0
pldmBindAuCfForceCodec <= USED 0
pldmBindAuChnDlConfigure <= USED 0
pldmBindAuChnUlConfigure <= USED 0
pldmBindAuEchoCancelSetting <= USED 0
pldmBindAuEchoSuppressionSetting <= USED 0
pldmBindAuNoiseSuppressionSetting <= USED 0
pldmBindAuPlayBufferFree <= USED 0
pldmBindAuPlayBufferGet <= USED 0
pldmBindAuRecordBufferFree <= USED 0
pldmBindAuRecordBufferGet <= USED 0
pldmBindAuRecordStopSdvr <= USED 0
pldmBindAudioDspIfTest <= USED 0
pldmBindAudioDtxEnabled <= USED 0
pldmBindAudioInit <= USED 0
pldmBindBgDaiInOperation <= USED 0
pldmBindBgDaiMode <= USED 0
pldmBindFrCanSleep <= USED 0
pldmBindInitAuTds <= USED 0
pldmBindInitialise <= USED 0
pldmBindInterruptInit <= USED 0
pldmBindMicInitialise <= USED 0
pldmBindNumAdcSeqRunning <= USED 0
pldmBindNvramInitialise <= USED 0
pldmBindNvramSelectCal <= USED 0
pldmBindSendAdcIntReadCnf <= USED 0
;FILE pl_gprs_api.o
plgBBindGmphEngModeGprsEdgeLinkQualInd <= USED 0
plgBindGmphPktOpCtrlInd <= USED 0
;FILE pl_gsm_api.o
pldL1GsmCapabilityReportReq2 <= USED 0
plgBindEmphDeactiveCnf <= USED 0
plgBindEmphDrxAbortFindLteCellReq <= USED 0
plgBindEmphHandoverToLteCnf <= USED 0
plgBindEmphHandoverToLteFailCnf <= USED 0
plgBindEmphLteSibUnitDataInd <= USED 0
plgBindEmphMonitorLteNcellReq <= USED 0
plgBindGrrCbControlCnf <= USED 0
plgBindL1GSMInitialize <= USED 0
plgBindMphEngModeDedSrvCellInfoInd <= USED 0
plgBindUtranRssiMeasInd <= USED 0
plgL1aEmphLteSibUnitDataInd <= USED 0
plgMphAbortCellSearchReq <= USED 0
plgMphAbortCellSearchReq2 <= USED 0
plgUmphDeactivateReq <= USED 0
;FILE pl_gw_diag.o
plgGmphEngModeGprsEdgeLinkQualInd_diag <= USED 0
plgGmphUlMbConfigCnf_diag <= USED 0
plgMphBcchSyncReq_diag <= USED 0
plgMphEngModeDedSrvCellInfoInd_diag <= USED 0
;FILE pl_test_api.o
plgBindCalDevGsmBurstCnf <= USED 0
plgBindCalDevGsmCnf <= USED 0
plgBindCalDevGsmDcOffsetCnf <= USED 0
plgBindCalDevGsmFinishCnf <= USED 0
plgBindCalDevGsmFrameDefineCnf <= USED 0
plgBindCalDevGsmFrameTimingCnf <= USED 0
plgBindCalDevGsmFrameUseCnf <= USED 0
plgBindCalDevGsmFreqOffsetMeasCnf <= USED 0
plgBindCalDevGsmGainProgramCnf <= USED 0
plgBindCalDevGsmIqFetchCnf <= USED 0
plgBindCalDevGsmLmtSequenceCnf <= USED 0
plgBindCalDevGsmLoopBackCnf <= USED 0
plgBindCalDevGsmLoopBackDataCnf <= USED 0
plgBindCalDevGsmPowerRampAttributesCnf <= USED 0
plgBindCalDevGsmRampScaleCnf <= USED 0
plgBindCalDevGsmRssiCnf <= USED 0
plgBindCalDevGsmRxControlCnf <= USED 0
plgBindCalDevGsmRxFastCalibrationSequenceCnf <= USED 0
plgBindCalDevGsmRxFastCalibrationStepInd <= USED 0
plgBindCalDevGsmRxSelfCalibrationCnf <= USED 0
plgBindCalDevGsmSetAfcDacCnf <= USED 0
plgBindCalDevGsmSetBandModeCnf <= USED 0
plgBindCalDevGsmSetBurstDataCnf <= USED 0
plgBindCalDevGsmSetPowerRampCnf <= USED 0
plgBindCalDevGsmSlotDefineCnf <= USED 0
plgBindCalDevGsmTxFastCalibrationSequenceCnf <= USED 0
plgBindCalDevGsmTxFastCalibrationStepInd <= USED 0
plgBindCalDevGsmTxSelfCalibrationCnf <= USED 0
plgBindDspTaskTestCfgCopyBufCnf <= USED 0
plgBindDspTaskTestDspCopyBufCnf <= USED 0
plgBindDspTaskTestDspFreeBufCnf <= USED 0
plgBindDspTaskTestDspGetBufCnf <= USED 0
plgBindDspTaskTestDspResetCnf <= USED 0
plgBindDspTaskTestDspRunTaskCnf <= USED 0
plgBindDspTaskTestIniTstHarnessCnf <= USED 0
plgBindDspTaskTestMcuAllocBufCnf <= USED 0
plgBindDspTaskTestMcuCopyBufCnf <= USED 0
plgBindDspTaskTestMcuFreeBufCnf <= USED 0
plgBindDspTaskTestRenameLobFileCnf <= USED 0
plgBindDspTaskTestRenamePlkSimFileCnf <= USED 0
plgBindDspTaskTestSimCopyBufCnf <= USED 0
plgBindDspTaskTestTdsAllocBufCnf <= USED 0
plgBindDspTaskTestTdsCopyBufCnf <= USED 0
plgBindDspTaskTestTdsFreeBufCnf <= USED 0
;FILE pl_voice_adap.o
;FILE platform_intf.o
;FILE platform_nvm.o
PlatformNvm_CreateAciCfgFile <= USED 0
PlatformNvm_Init <= USED 0
nvm_file_test <= USED 0
;FILE platform_shim_api.o
BipCmdTaskInit <= USED 0
EraseFlash <= USED 0
GetCurrentCompTime <= USED 0
GetFlashLayoutConfig <= USED 0
GetLocalTime <= USED 0
GetWiFiType <= USED 0
GetWifiStatus <= USED 0
Get_backtime_frompsm <= USED 0
Giga_Disable4BytesMode <= USED 0
Giga_Enable4BytesMode <= USED 0
IsGsmRatForMIFI <= USED 0
Is_dedicateAPN <= USED 0
LteConstructSnDataNodeEForMifi <= USED 0
OnKeyPoweroff_Start <= USED 0
OsaMsgQFreeRate <= USED 0
PMIC_Reset <= USED 0
Rdisk_FlashRead <= USED 0
ReadFlash <= USED 0
SaveCalDataFromMfg <= USED 0
Update_RTC_Time <= USED 0
WIFI_Sendto_Wapi <= USED 0
WIFI_Sendto_Wps <= USED 0
WriteFlash <= USED 0
asrbt_main <= USED 0
embms_data_input <= USED 0
get_cgreg_status_rsp <= USED 0
get_download_flag_for_tigx <= USED 0
get_network_mode <= USED 0
get_subnet_mask <= USED 0
get_uart2_baudrate_for_tigx <= USED 0
handle_dial <= USED 0
isAutoApn <= USED 0
is_aes_cipher <= USED 0
lte_lwip_free <= USED 0
lwip_chk_value <= USED 0
mac_filter_forward_chain <= USED 0
mbim_rx_comm_cb <= USED 0
modem_channel_buffer_reset <= USED 0
modem_init <= USED 0
modem_update_reg_option <= USED 0
need_ppp_process_at_response <= USED 0
need_ppp_process_ato_request <= USED 0
ppp_clear_use_eps_apn <= USED 0
ppp_enable <= USED 0
ppp_get_connect_flag <= USED 0
ppp_store_configure_params <= USED 0
process_ppp_atd <= USED 0
process_ppp_ato <= USED 0
redirect_get_header <= USED 0
send_data_to_modem_router <= USED 0
set_download_flag_for_tigx <= USED 0
set_uart2_baudrate_for_tigx <= USED 0
set_uart_selfadapt <= USED 0
uap_packet_free <= USED 0
uart_init_restart <= USED 0
usb_net_init <= USED 0
usb_net_rx <= USED 0
usb_net_tx <= USED 0
wan_delete_device <= USED 0
watchdog_reset <= USED 0
woal_uap_do_ioctl <= USED 0
;FILE platform_util.o
;FILE plkmbufm.o
;FILE plkmerr.o
PlkErrorHandlerDataExtract <= USED 0
;FILE plkmhsl.o
HslPrintf <= USED 0
PlkMHslConfigHandler <= USED 0
PlkMHslPutString <= USED 0
PlkMHslPxutData <= USED 0
PlkMStartHsl <= USED 0
;FILE plkminit.o
PlkMInitialiseTdqsWithPriority <= USED 0
;FILE plkmirq.o
dlClearSWICarry <= USED 0
dlDisableTcuUnderflowWarning <= USED 0
dlEnableDspFatalInt <= USED 0
;FILE plkmpelm.o
pelMgrOptEvent <= USED 0
;FILE plkmsdrv.o
;FILE plkmsort.o
;FILE plms.o
PlmsCheckIfFgPlmnOngoing <= USED 0
PlmsConvertMibBW <= USED 0
PlmsPrintCheckSearchInfoDb <= USED 0
PlmsSendCphyDrxFindLteCellAbortReq <= USED 0
PlmsSendCphyStopFindCellReq <= USED 0
PlmsTask1 <= USED 0
;FILE plmsdb.o
PlmsDbIsPscInUmtsFindCellCnf <= USED 0
PlmsStoreSeqNum <= USED 0
;FILE plmsfnd.o
PlmsFndExcludeEarfcnFromSearch <= USED 0
PlmsFndExcludeLteCellFromSearch <= USED 0
;FILE plmsscn.o
PlmsLogResultTable <= USED 0
PlmsScnDetermineL1ForFGRssi <= USED 0
;FILE plmssi.o
;FILE pm.o
;FILE pm_debug.o
pmLogInit <= USED 0
;FILE pmic.o
Camera_Avdd_Power_set <= USED 0
Camera_Dvdd_Power_set <= USED 0
Camera_IOvdd_Power_set <= USED 0
I2CDeviceCheckStatus <= USED 0
I2CRecordDeviceStatus <= USED 0
PM812_32K_OUT2_Enable <= USED 0
PM812_GET_POWER_DOWN_REASON <= USED 0
PM812_GET_POWER_DOWN_REASON2 <= USED 0
PM812_GET_POWER_UP_REASON <= USED 0
PM812_IS_ALARM_TRIGGERD_POWERUP <= USED 0
PM812_Ldo_11_set <= USED 0
PM812_Ldo_11_set_2_8 <= USED 0
PM812_Ldo_12_set_3_0 <= USED 0
PM812_Ldo_15_set <= USED 0
PM812_Ldo_15_set_2_8 <= USED 0
PM812_Ldo_16_set <= USED 0
PM812_Ldo_16_set_2_8 <= USED 0
PM812_Ldo_17_set <= USED 0
PM812_Ldo_17_set_2_8 <= USED 0
PM812_Ldo_18_set <= USED 0
PM812_Ldo_18_set_1_8 <= USED 0
PM812_Ldo_19_Config <= USED 0
PM812_Ldo_5_set <= USED 0
PM812_Ldo_5_set_3_1 <= USED 0
PM812_Ldo_6_set <= USED 0
PM812_Ldo_6_set_1_8 <= USED 0
PM812_Ldo_6_set_2_8 <= USED 0
PM812_Ldo_8_set <= USED 0
PM812_Ldo_8_set_2_8 <= USED 0
PM812_Ldo_9_set <= USED 0
PM812_Ldo_9_set_1_8 <= USED 0
PM812_PwmSetLevel <= USED 0
PM812_REG_DUMP <= USED 0
PM812_RTC_SET_CONTROL_REG <= USED 0
PM812_VLDO7_SWITCH <= USED 0
PM812_WD_MODE <= USED 0
PM812_bk4clkfll_set_2m <= USED 0
PM812_vbuck1_set_fpwm <= USED 0
PM812_vbuck3_set_1_25 <= USED 0
PM812_vbuck4_set_1_85 <= USED 0
PM812_vbuck4_set_2_00 <= USED 0
PMIC_IS_PM813_A2 <= USED 0
PMIC_Phase1Init <= USED 0
PMIC_PowerDown <= USED 0
PMIC_RTC_GET_EXPIR_1_ENABLE_STATE <= USED 0
PlatformVcoreConfigForDro <= USED 0
PlatformVcoreConfigHigh <= USED 0
PlatformVcoreConfigLow <= USED 0
PlatformVcoreConfigTop <= USED 0
SysRestartReasonGetGlobal <= USED 0
isSysRestartByAlarm <= USED 0
isSysRestartByCharging <= USED 0
isSysRestartByError <= USED 0
isSysRestartByNormal <= USED 0
pmic_user_defined_flags_get <= USED 0
pmic_user_defined_flags_set <= USED 0
ustica_I2CEnableclockandPin <= USED 0
ustica_I2CInit <= USED 0
ustica_USB_shutdown <= USED 0
ustica_vbuck1_0_set <= USED 0
;FILE pmic_battery.o
;FILE pmic_charger.o
;FILE pmic_onkey.o
PM812_LONKEY_PRESS_TIME_SET <= USED 0
;FILE pmic_rtc.o
PMIC_RTC_IS_ALARM_POWERUP <= USED 0
PMIC_RTC_IS_SYS_RTC_PEER_SYNCED <= USED 0
pmic_rtc_offset_storage_type_detect <= USED 0
;FILE pmic_wdt.o
;FILE pmu_stub.o
PMUDSPReset <= USED 0
;FILE prm_hrbl.o
PRMResourceStatusGet <= USED 0
PRMSWReset <= USED 0
PRMServiceFreqGet <= USED 0
PRMServiceFreqSet <= USED 0
prmFreqRetCode <= USED 0
prmRetCode <= USED 0
;FILE process_dialogmsg.o
API_SIP_DM_IsSubPresent <= USED 0
API_SIP_DM_SUBS_INCR <= USED 0
API_SIP_DM_SUBS_MINUS <= USED 0
sipDM_GetDialogInState <= USED 0
;FILE property.o
PROP_FindKeyById <= USED 0
PROP_GetProperty <= USED 0
;FILE ps_api.o
PS_GetSysGPRSRegStatus <= USED 0
PS_GetTFTCapsREQ <= USED 0
PS_SetPsPlusPaging <= USED 0
addToList <= USED 0
clearErrorIndFlagAll <= USED 0
deactive_ps_connection <= USED 0
getCellID <= USED 0
getCeregStatus <= USED 0
getCgregStatus <= USED 0
getLac <= USED 0
getPsRegOption <= USED 0
getQueryTftCtxInProcess <= USED 0
initPsPdpCtx <= USED 0
parseAddr <= USED 0
removeNodeByCid <= USED 0
resetCidList <= USED 0
resetPsParas <= USED 0
searchListByCid <= USED 0
sendDefineDefaultPdpContext <= USED 0
sendmatchCgdcontapn <= USED 0
subnetmask_str2len <= USED 0
telGetDefaultPdpApn <= USED 0
telUpdateActiveApnInfo <= USED 0
;FILE ps_init.o
ipcTriggerInit <= USED 0
psKiOsMaximumSleep <= USED 0
psKiOsTick <= USED 0
psNvmAccessIs_ReducedOnlyCfun0Cfun1 <= USED 0
;FILE ps_itcm.o
;FILE ps_nvm.o
psNvmAciGkiIsWorking <= USED 0
psNvmGetIMSNWReportInfo <= USED 0
psNvmReadIMSNWReportInfo <= USED 0
psNvmSetIMSNWReportNvmDefaultParams <= USED 0
psVendorPrintf <= USED 0
;FILE psm.o
psm_close <= USED 0
psm_get_int <= USED 0
psm_get_namespace <= USED 0
psm_get_wrapper_expose <= USED 0
psm_pop_subspace <= USED 0
psm_safe_set <= USED 0
psm_set_int <= USED 0
psm_set_wrapper_expose <= USED 0
;FILE psm_wrapper.o
clear_need_ascii_to_html <= USED 0
psm_commit_other__ <= USED 0
psm_flush <= USED 0
psm_get_version <= USED 0
psm_get_wrapper_with_variable_name <= USED 0
psm_match__ <= USED 0
psm_set_version <= USED 0
psmfile_get_wrapper <= USED 0
psmfile_set_wrapper <= USED 0
set_need_ascii_to_html <= USED 0
;FILE pssqcfgpwr.o
;FILE pssqsfsrad.o
;FILE pssqsfsrx.o
L1CfgClearAgc <= USED 0
L1CfgTestRxNbDsp <= USED 0
;FILE pssqsfsseq.o
;FILE pssqsfstx.o
;FILE ptable.o
ptable_dump <= USED 0
ptable_init <= USED 0
;FILE qmgr.o
qBlockGet <= USED 0
qBlockPut <= USED 0
qDelete <= USED 0
qRoom <= USED 0
;FILE qspi_core.o
buf_dump <= USED 0
flash_erase_nvm <= USED 0
flash_init_apn <= USED 0
flash_init_btbin <= USED 0
flash_init_btlst <= USED 0
flash_init_factory <= USED 0
flash_init_nvm <= USED 0
flash_partion_init <= USED 0
flash_read_btbin <= USED 0
flash_read_btlst <= USED 0
flash_read_nvm <= USED 0
flash_write_nvm <= USED 0
qspi_nor_erase_all <= USED 0
qspi_rx_mode_name <= USED 0
qspi_tx_mode_name <= USED 0
spi_nor_do_read_uid <= USED 0
;FILE qspi_dma.o
_dma_m2m_xfer <= USED 0
dma_clr_irq <= USED 0
dma_config_desc <= USED 0
dma_disconnect_irq_handler <= USED 0
dma_enable <= USED 0
dma_get_des_num <= USED 0
dma_read_status <= USED 0
dma_reg_printf <= USED 0
dma_set_dcsr <= USED 0
dma_set_des <= USED 0
dma_stop_irq_dis <= USED 0
dma_stop_irq_en <= USED 0
qspi_dma_test <= USED 0
;FILE qspi_host.o
_writel <= USED 0
;FILE qspi_nor.o
spi_nor_resume_task_xip <= USED 0
spi_nor_suspend_task_xip <= USED 0
;FILE rabmgmm.o
;FILE raw.o
raw_bind_netif <= USED 0
raw_disconnect <= USED 0
raw_new_ip6 <= USED 0
;FILE rdasspdu.o
;FILE rdbg.o
;FILE rdmain.o
GpRdTask1 <= USED 0
GpRdTask21 <= USED 0
;FILE rdsegpdu.o
;FILE rdutil.o
RdUlThroughputTimerCfg <= USED 0
;FILE recin.o
;FILE ripc.o
RIPCPhase1Init <= USED 0
ripc_interrupt_clear <= USED 0
ripc_interrupt_set <= USED 0
;FILE rlcmacfcf.o
;FILE rm.o
CloseAllClk <= USED 0
RMBeforeIntDisRegister <= USED 0
RMD2PrepareWithFastClk <= USED 0
RMIsResourceFree <= USED 0
RMIsResourceMulti <= USED 0
RMPhase1Init <= USED 0
RMSWReset <= USED 0
RMServiceFreqGet <= USED 0
RMServiceFreqSelect <= USED 0
RMwuEnabled <= USED 0
TWSIClockOnOff <= USED 0
XIRQClockOnOff <= USED 0
;FILE rm_debug.o
;FILE rm_memRetain.o
RMD2RecoverINTC <= USED 0
;FILE rndis.o
RndisMsgParser <= USED 0
RndisParamInit <= USED 0
Rndis_indicate_status_msg <= USED 0
Rndis_init_response <= USED 0
Rndis_keepalive_response <= USED 0
Rndis_merge_mult_packet <= USED 0
Rndis_query_gen_resp <= USED 0
Rndis_query_response <= USED 0
Rndis_remove_hdr <= USED 0
Rndis_reset_response <= USED 0
Rndis_set_gen_resp <= USED 0
Rndis_set_response <= USED 0
;FILE rohc_add_cid.o
;FILE rohc_comp.o
rohc_comp_deliver_feedback2 <= USED 0
rohc_comp_disable_profile <= USED 0
rohc_comp_disable_profiles <= USED 0
rohc_comp_enable_profile <= USED 0
rohc_comp_enable_profiles <= USED 0
rohc_comp_force_contexts_reinit <= USED 0
rohc_comp_get_cid_type <= USED 0
rohc_comp_get_general_info <= USED 0
rohc_comp_get_last_packet_info2 <= USED 0
rohc_comp_get_max_cid <= USED 0
rohc_comp_get_mrru <= USED 0
rohc_comp_get_segment2 <= USED 0
rohc_comp_get_state_descr <= USED 0
rohc_comp_pad <= USED 0
rohc_comp_profile_enabled <= USED 0
rohc_comp_reinit_context <= USED 0
rohc_comp_set_features <= USED 0
rohc_comp_set_mrru <= USED 0
rohc_comp_set_traces_cb2 <= USED 0
;FILE rohc_comp_rfc3095.o
;FILE rohc_decomp.o
rohc_decomp_disable_profile <= USED 0
rohc_decomp_disable_profiles <= USED 0
rohc_decomp_enable_profile <= USED 0
rohc_decomp_enable_profiles <= USED 0
rohc_decomp_get_cid_type <= USED 0
rohc_decomp_get_context_info <= USED 0
rohc_decomp_get_general_info <= USED 0
rohc_decomp_get_last_packet_info <= USED 0
rohc_decomp_get_max_cid <= USED 0
rohc_decomp_get_mrru <= USED 0
rohc_decomp_get_prtt <= USED 0
rohc_decomp_get_rate_limits <= USED 0
rohc_decomp_get_state_descr <= USED 0
rohc_decomp_set_features <= USED 0
rohc_decomp_set_mrru <= USED 0
rohc_decomp_set_traces_cb2 <= USED 0
;FILE rohc_decomp_detect_packet.o
;FILE rohc_decomp_rfc3095.o
;FILE rohc_list.o
;FILE rohc_packets.o
rohc_get_ext_descr <= USED 0
rohc_get_packet_descr <= USED 0
rohc_get_packet_type <= USED 0
;FILE rohc_profiles.o
rohc_get_profile_descr <= USED 0
rohc_profile_get_other_version <= USED 0
rohc_profile_is_rohcv1 <= USED 0
;FILE rohc_traces_internal.o
;FILE rohc_utils.o
;FILE root.o
dummyRootFunction <= USED 0
;FILE rrcdsutils.o
RrDsCheckEnterSameOperatorModeSubstituted <= USED 0
RrDsCheckWorkOnSameSuitableRatSubstituted <= USED 0
RrDsGetIratDsAbortSearchCnfSendingSubstituted <= USED 0
rrDsGetUmtsPagingDrxLength <= USED 0
rrDsSendIratDsCampOnReq <= USED 0
rrDsSendIratDsCancelAbortGsmPsReq <= USED 0
rrDsSendIratDsPhyConfigFinishInd <= USED 0
rrDsSendIratDsVoiceBadInd <= USED 0
;FILE rrcomdb.o
RrComDbGetPlmsSimId <= USED 0
RrComDbGetSimPresent <= USED 0
RrComDbSetServingUtraFddCell <= USED 0
RrComDbSetUeSupportUtraBands <= USED 0
rrcComDbCheckIfUrrcCellBarred <= USED 0
rrcComDbCheckIfUrrcCellIsInList <= USED 0
rrcComDbGetNumOfGrrBarredCellList <= USED 0
rrcComDbRemoveUrrcBarredCellList <= USED 0
rrcComDbSetUrrcBarredCellList <= USED 0
;FILE rrutility.o
IsDeutscheTelekomInRequestedPlmnList <= USED 0
IsProximusPlmn <= USED 0
IsRedirectionFailureHandlingEnable <= USED 0
IsSoftbankPlmn <= USED 0
RrIsTestSimGcfMode <= USED 0
;FILE sa.o
ipsec_sad_flush <= USED 0
ipsec_sad_print <= USED 0
ipsec_sad_print_single <= USED 0
ipsec_spd_flush <= USED 0
ipsec_spd_print <= USED 0
ipsec_spd_print_single <= USED 0
ipsec_spd_release_dbs <= USED 0
;FILE sac_api.o
ciAtDataInd <= USED 0
sacSendGkiAudioEcallToApInfoInd <= USED 0
;FILE sac_cc.o
sacCcGetCurrentEcallSimId <= USED 0
setSacCcDisableCsCallFlag <= USED 0
;FILE sac_cfg.o
;FILE sac_dat.o
sacGetCurrentCid <= USED 0
sacGetCurrentNsapi <= USED 0
;FILE sac_dev.o
sacCiDevSetLpmEnabledFunc <= USED 0
sacDevGetSimCfunState <= USED 0
sacDevIsSpecificPlmnForCHN <= USED 0
sacDevMinFuncMode <= USED 0
sacDevReadCallEndCause <= USED 0
sacDevSetBandModeGLSwapReq <= USED 0
sacDevSetBandModeSrvDomainReq <= USED 0
sacDevSetDipOption <= USED 0
sacDevShutDown <= USED 0
sacDevWriteCallEndCause <= USED 0
;FILE sac_libs.o
sacShAdnDigitsToBcd <= USED 0
sacShCheckSpecPendingCiReq <= USED 0
sacShCheckWaitCiReq <= USED 0
sacShConvertIpToLong <= USED 0
sacShDecodeUcs2HexData <= USED 0
sacShDelayCiReq <= USED 0
sacShEncodeUcs2HexData <= USED 0
sacShGetInfoFromPrimitiveId <= USED 0
sacShGetOpShHandle <= USED 0
sacShKeepCurSignal <= USED 0
sacShReset <= USED 0
sacShSendCiAccessDeniedCnf <= USED 0
sacShSendCiAccessDeniedCnfWithInfo <= USED 0
sacShSendCiHasNoSupportCnfWithInfo <= USED 0
;FILE sac_mm.o
SacClearAnayArfcnNvm <= USED 0
SacGetArfcnFromNvm <= USED 0
SacInsertArfcnNvm <= USED 0
SacUpdateAllArfcnNvm <= USED 0
;FILE sac_msg.o
SacMsgGetCurrentMtMsgConfigFunc <= USED 0
;FILE sac_pb.o
;FILE sac_ps.o
sacPsDeactivateAllPdpContext <= USED 0
sacPsDevShutDown <= USED 0
sacPsGetEutranAct <= USED 0
sacPsGetSim4GQciValue <= USED 0
sacPsIsSpecificPlmnForMET <= USED 0
sacPsLteDefaultApnDefined <= USED 0
sacPsProcessApexMmBandInd <= USED 0
sacPsSetForcedReportNwRegInd <= USED 0
;FILE sac_queues.o
sacSgQueueReadFirstSignal <= USED 0
sacSgQueueRemoveFirstSignal <= USED 0
;FILE sac_ref.o
;FILE sac_shell.o
sacShSetKeepControlMask <= USED 0
;FILE sac_shell_engine.o
VgCiTask1 <= USED 0
afshTask1 <= USED 0
;FILE sac_sim.o
sacSimOtaCcReady <= USED 0
sacSimOtaUssdReady <= USED 0
sacSimShutDown <= USED 0
;FILE sac_ss.o
sacSsGetCurrentUus1Info <= USED 0
;FILE sac_trace.o
sacDataTrace <= USED 0
;FILE scc.o
Scc_CallConference <= USED 0
Scc_GetCaps <= USED 0
Scc_GetConfig <= USED 0
Scc_GetFirstAccess <= USED 0
Scc_GetNextAccess <= USED 0
Scc_GetProperty <= USED 0
Scc_Media_Cancel <= USED 0
Scc_Media_CreateChannel <= USED 0
Scc_Media_DeleteChannel <= USED 0
Scc_Media_GetIoInterface <= USED 0
Scc_Media_Recv <= USED 0
Scc_Media_Send <= USED 0
Scc_QueryAccessStatus <= USED 0
Scc_SetProperty <= USED 0
;FILE scc_access_sm.o
scc_AccessSM_DeactivateXcapConn <= USED 0
scc_AccessSM_RestartAllAccessOnCondition <= USED 0
scc_AccessSM_SetNetworkOperator <= USED 0
;FILE scc_access_utils.o
scc_CopyAccessInfo <= USED 0
scc_GetAccessCtx <= USED 0
;FILE scc_call_sm.o
;FILE scc_call_utils.o
scc_GetAltAccess <= USED 0
scc_appmsg_carrier_ack_hdlr <= USED 0
;FILE scc_cs_intf.o
;FILE scc_pref_eval.o
;FILE scc_ps_intf.o
scc_UpdateDialog <= USED 0
;FILE scc_sms.o
;FILE scc_utils.o
scc_DeleteIMSCapabilities <= USED 0
scc_FreeScratchBuf <= USED 0
;FILE sdcard.o
SD_CARD_Insert_Remove_Task <= USED 0
dump_sd_lisr <= USED 0
sdcard_clk_off <= USED 0
sdcard_fat_is_ok <= USED 0
sdcard_get_errflag <= USED 0
sdcard_reinit <= USED 0
sdcard_set_mode <= USED 0
sdcard_test <= USED 0
sdcard_transmit_status <= USED 0
;FILE sdp.o
;FILE sdp_base_grammer.o
;FILE sdp_formatter.o
format_acap_attribute_fields <= USED 0
format_acfg_attribute_fields <= USED 0
format_attrib <= USED 0
format_attrib_a <= USED 0
format_attrib_c <= USED 0
format_attrib_d <= USED 0
format_attrib_e <= USED 0
format_attrib_g <= USED 0
format_attrib_i <= USED 0
format_attrib_l <= USED 0
format_attrib_m <= USED 0
format_attrib_n <= USED 0
format_attrib_o <= USED 0
format_attrib_p <= USED 0
format_attrib_q <= USED 0
format_attrib_r <= USED 0
format_attrib_s <= USED 0
format_attrib_t <= USED 0
format_connection_field <= USED 0
format_email_fields <= USED 0
format_fmtp_attribute_fields <= USED 0
format_framesize_attribute_field <= USED 0
format_group_attribute_fields <= USED 0
format_image_attribute_fields <= USED 0
format_information_field <= USED 0
format_key_field <= USED 0
format_pcfg_attribute_fields <= USED 0
format_phone_fields <= USED 0
format_property_attrib <= USED 0
format_repeat_interval_fields <= USED 0
format_rtpmap_attribute_fields <= USED 0
format_tcap_attribute_fields <= USED 0
format_time_zone_fields <= USED 0
format_uri_field <= USED 0
format_valid_atttib_value <= USED 0
sdp_format_precond_confirmstatus <= USED 0
sdp_format_precond_currentstatus <= USED 0
sdp_format_precond_desiredstatus <= USED 0
sdp_validate_precond_directiontag <= USED 0
sdp_validate_precond_statustype <= USED 0
sdp_validate_precond_strengthtag <= USED 0
validate_acap <= USED 0
validate_acfg <= USED 0
validate_bandwidth <= USED 0
validate_byte_string <= USED 0
validate_creq <= USED 0
validate_csup <= USED 0
validate_fmtp <= USED 0
validate_group <= USED 0
validate_pcfg <= USED 0
validate_rtpmap <= USED 0
validate_tcap <= USED 0
validate_time_field <= USED 0
;FILE sdp_parser_utils.o
parse_IP6addr <= USED 0
;FILE sdp_util.o
SDP_IsMediaTypePresent <= USED 0
transfer_buffer <= USED 0
;FILE sdsys.o
Advance <= USED 0
DeleteList <= USED 0
Insert <= USED 0
RamBlockListFirst <= USED 0
RamBlockListFirstValue <= USED 0
RamBlockListHeader <= USED 0
Retrieve <= USED 0
fatFAT_DumpFATDDRSave <= USED 0
fatSYS_PhysicalFlush <= USED 0
fatSYS_PhysicalFlushEx <= USED 0
fdi_flush_lock <= USED 0
get_block_from_sector <= USED 0
get_offset_from_sector <= USED 0
isFatInitOK <= USED 0
isSDFatInitOK <= USED 0
nvm_phy_write_blk_time <= USED 0
qspi_nvm_block_info_init <= USED 0
ram_block_list_empty <= USED 0
sdSYS_GetMedia <= USED 0
sdSYS_SetMedia <= USED 0
;FILE sdvl.o
;FILE sector.o
fatSECTOR_AllocateSector <= USED 0
fatSECTOR_WriteBlankCluster <= USED 0
fatSECTOR_WriteCurrentSector <= USED 0
fatSECTOR_WriteDuplicateSector <= USED 0
fatSECTOR_Writeback <= USED 0
;FILE set_fs.o
;FILE sfs_test.o
;FILE sha1.o
mbedtls_sha1 <= USED 0
mbedtls_sha1_process <= USED 0
;FILE sha256.o
mbedtls_sha256 <= USED 0
mbedtls_sha256_finish <= USED 0
mbedtls_sha256_process <= USED 0
mbedtls_sha256_starts <= USED 0
mbedtls_sha256_update <= USED 0
;FILE sha512.o
mbedtls_sha512 <= USED 0
mbedtls_sha512_finish <= USED 0
mbedtls_sha512_process <= USED 0
mbedtls_sha512_starts <= USED 0
mbedtls_sha512_update <= USED 0
;FILE signal_mcu.o
WebRtcNetEQ_SignalMcu <= USED 0
;FILE simPin.o
assert_cnt_flag_init <= USED 0
sector_timeout_info_init_no_remap <= USED 0
;FILE sim_api.o
SIM_GetSpn <= USED 0
checkSIMRet <= USED 0
isTestCard <= USED 0
resetSimParas <= USED 0
;FILE simatdec.o
SimatDecodeAtCommand <= USED 0
SimatDecodeBearer <= USED 0
SimatDecodeBearerDesc <= USED 0
SimatDecodeBrowserId <= USED 0
SimatDecodeBufferSize <= USED 0
SimatDecodeChanDataLgth <= USED 0
SimatDecodeChannelData <= USED 0
SimatDecodeDtmfString <= USED 0
SimatDecodeItem <= USED 0
SimatDecodeItemIconIdList <= USED 0
SimatDecodeItemId <= USED 0
SimatDecodeItemList <= USED 0
SimatDecodeLanguageCode <= USED 0
SimatDecodeMenuItems <= USED 0
SimatDecodeNetworkAccessName <= USED 0
SimatDecodeNextActIndicator <= USED 0
SimatDecodeOtherAddress <= USED 0
SimatDecodeProvFileRef <= USED 0
SimatDecodeResponseLength <= USED 0
SimatDecodeSmsTpdu <= USED 0
SimatDecodeTextAttributeList <= USED 0
SimatDecodeTextString <= USED 0
SimatDecodeToneType <= USED 0
SimatDecodeTransLevel <= USED 0
SimatDecodeUrl <= USED 0
SimatDecodeUssdString <= USED 0
;FILE simatenc.o
SimEncodeTerminalProfile <= USED 0
SimatEncodeBearerDesc <= USED 0
SimatEncodeBufferSize <= USED 0
SimatEncodeChanDataLgth <= USED 0
SimatEncodeChanStatusEvent <= USED 0
SimatEncodeChannelData <= USED 0
SimatEncodeChannelStatus <= USED 0
SimatEncodeCsdBearer <= USED 0
SimatEncodeDataAvailEvent <= USED 0
SimatEncodeGprsBearer <= USED 0
UsimEncodeTerminalProfile <= USED 0
;FILE simdec.o
SimDecodeCbmid <= USED 0
SimDecodeNia <= USED 0
USIMOemFlagClear <= USED 0
USIMOemFlagGet <= USED 0
USIMOemFlagSet <= USED 0
;FILE simenc.o
SimEncodeAccessClass <= USED 0
SimEncodeAdnFdn <= USED 0
;FILE simproc.o
SimManagerTask1 <= USED 0
SimManagerTaskExitRoutine <= USED 0
;FILE simproc2.o
SimManagerTask21 <= USED 0
SimManagerTask2ExitRoutine <= USED 0
;FILE simsig.o
SimSendL1siSwitchReq <= USED 0
;FILE sipResolver.o
;FILE sipUaRefer.o
;FILE sipUaReferDialog.o
SipUAS_UpdateReferDialog <= USED 0
;FILE sipUaReferFsmFunc.o
;FILE sipUaUtils.o
API_SIP_UA_FreeOtherHeader <= USED 0
FreeResponseParameters <= USED 0
SIP_UAS_Add_AcceptEncoding_Header <= USED 0
SIP_UAS_Add_AcceptLanguage_Header <= USED 0
SIP_UA_RemoveIMSSpecificHdrs <= USED 0
sipUAC_AddDiversionHeader <= USED 0
sipUAS_AddAcceptHeader <= USED 0
sipUA_CloneFeatureParam <= USED 0
sipUA_RemoveOptionTag <= USED 0
sipUA_SetPMediaAuthorizationHdr <= USED 0
;FILE sipUacAppReq.o
SipUACSendInviteSessTimer <= USED 0
SipUACSendUpdateSessTimer <= USED 0
;FILE sipUacStackResp.o
;FILE sipUasAppResp.o
SipUASSendInvite5XX <= USED 0
SipUASSendPrack4XXConfirmed <= USED 0
SipUASSendPrack4XXEarly <= USED 0
;FILE sipUasStackReq.o
SIP_UAS_ProcessRefer <= USED 0
SIP_UAS_ProcessSubscribe <= USED 0
;FILE sipUseragent.o
;FILE sipUseragentReg.o
SIPUAC_UnSubscribe <= USED 0
SIPUA_DeleteUser <= USED 0
;FILE sipUseragentRegFsmFunc.o
SipUaGotChallengeDeRegisterNotifyEn <= USED 0
SipUaGotChallengeReRegisterNotifyEn <= USED 0
;FILE sip_auth.o
;FILE sip_formatter.o
;FILE sip_os_utils.o
;FILE sip_transceiver.o
SIPTransceiver_GetConfig <= USED 0
SIP_Get_Received <= USED 0
;FILE sipen.o
SIPEN_ReSubscribe <= USED 0
;FILE sipenfsmfunc.o
;FILE siptransaction.o
SIPTransaction_SendRequest <= USED 0
sipTrxn_GenerateViaBranch <= USED 0
;FILE siptransactioninvclifsm.o
;FILE siptransactioninvclifsmfunc.o
;FILE siptransactioninvsrvfsm.o
;FILE siptransactioninvsrvfsmfunc.o
;FILE siptransactionlist.o
SipTransactionListPrint <= USED 0
;FILE siptransactionnoninvclifsm.o
;FILE siptransactionnoninvclifsmfunc.o
;FILE siptransactionnoninvsrvfsm.o
;FILE siptransactionnoninvsrvfsmfunc.o
;FILE sipuri.o
SIP_GetDisplayUriName <= USED 0
;FILE siputil.o
SIP_CloneContent <= USED 0
SIP_CopyContentCaps <= USED 0
SIP_CopyContentEncoding <= USED 0
SIP_CopyContentLanguage <= USED 0
SIP_CopyContentType <= USED 0
;FILE smabgp.o
SmAbgpSmConfigInd <= USED 0
SmDoAbgpSmApnReadInd <= USED 0
SmDoAbgpSmSetApnReq <= USED 0
SmSendAbgpSmApnReadRsp <= USED 0
SmSendAbgpSmApnSetCnf <= USED 0
;FILE smcmprot.o
SmcmTask1 <= USED 0
SmcmTask_21 <= USED 0
;FILE smencdec.o
;FILE smgki.o
;FILE smgmm.o
SendSmGprsDeactPdpContextAccept <= USED 0
SmSendGmmsmFastDormantReq <= USED 0
;FILE smip_common.o
;FILE smip_storage.o
smip_IsSMSStored <= USED 0
smip_IsVoiceMail <= USED 0
;FILE smmain.o
GpSmTask1 <= USED 0
;FILE smmain2.o
GpSmTask21 <= USED 0
;FILE smrdwr.o
;FILE smref.o
;FILE smreg.o
;FILE smrlprot.o
SmrlTask1 <= USED 0
SmrlTask_21 <= USED 0
;FILE smsal_at.o
SMSAL_ErrorHandler <= USED 0
SMSAL_GetSMSC <= USED 0
SMSAL_HdlInitialize <= USED 0
SMSAL_HdlShutdown <= USED 0
SMSAL_Hdl_Get_SCA <= USED 0
SMSAL_Hdl_Update_LastUseTPMR <= USED 0
SMSAL_MsgHandler <= USED 0
SMSAL_SetSMSFormat <= USED 0
SMSAL_SetUEAvailability <= USED 0
SMSAL_Update_LastUseTPMR <= USED 0
smsal_CmdIntf_AT_RestrictedSimAccess <= USED 0
smsal_SimCard_GetSCA <= USED 0
;FILE smsn.o
;FILE smtimers.o
;FILE smtlprot.o
SmtlTask1 <= USED 0
SmtlTaskExitRoutine <= USED 0
SmtlTask_21 <= USED 0
SmtlTask_2ExitRoutine <= USED 0
;FILE smutil.o
SmUpdatePsApnEntity <= USED 0
;FILE sncompr.o
SnSelectCompressionValues <= USED 0
;FILE snllc.o
;FILE snmain.o
;FILE snmem.o
;FILE snow_3g.o
MULxPOW <= USED 0
;FILE snpdp.o
SnSnConfigReq <= USED 0
SndcpCfg_TraceNpdus <= USED 0
;FILE snseg.o
;FILE snsignal.o
;FILE snsm.o
;FILE sntimers.o
;FILE snutil.o
SnDeleteNpduBuffer <= USED 0
SnEnableDisableDataReqMaxLimit <= USED 0
SnNumActivePdps <= USED 0
;FILE snv42bis.o
;FILE snxid.o
;FILE sockets.o
lwip_eventfd <= USED 0
lwip_getpeername <= USED 0
lwip_getsockname <= USED 0
lwip_getsocktype <= USED 0
lwip_shutdown <= USED 0
lwip_socket_with_callback <= USED 0
readline <= USED 0
readn <= USED 0
recv_peek <= USED 0
writen <= USED 0
;FILE spi_nor.o
enableSlaveSSP <= USED 0
masterRxTx <= USED 0
slaveRxTx <= USED 0
spi_dma_TXRX <= USED 0
spi_dma_read <= USED 0
spi_erase_delay <= USED 0
spi_intc_config <= USED 0
spi_irq_handler <= USED 0
spi_master_init <= USED 0
spi_nor_test <= USED 0
spi_read_write_word <= USED 0
spi_rw_test <= USED 0
spi_rw_test_init <= USED 0
spi_slave_init <= USED 0
spinorTest <= USED 0
spinor_cache_read_pio <= USED 0
spinor_page_program_pio <= USED 0
spinor_perf <= USED 0
spinor_read_status_erase <= USED 0
spinor_rw_test <= USED 0
;FILE split_and_insert.o
;FILE ss_api.o
resetSsParas <= USED 0
;FILE ss_main.o
;FILE ssp_host.o
Endian_Convert <= USED 0
spi_assert_cs <= USED 0
spi_deassert_cs <= USED 0
ssp_common_config <= USED 0
ssp_config <= USED 0
ssp_fifo_reset <= USED 0
ssp_flush <= USED 0
ssp_get_txfifo_req <= USED 0
ssp_port_enable <= USED 0
ssp_read_word <= USED 0
ssp_reg_printf <= USED 0
ssp_set_dma_rx_enable <= USED 0
ssp_set_dma_tx_enable <= USED 0
ssp_set_selfloop <= USED 0
ssp_trailing_owner_select <= USED 0
ssp_trailing_set_timeout <= USED 0
ssp_write_word <= USED 0
;FILE ssrouter.o
SsTask1 <= USED 0
SsTaskExitRoutine <= USED 0
SsTask_21 <= USED 0
SsTask_2ExitRoutine <= USED 0
;FILE stdlib_util.o
;FILE stream_db.o
WebRtcNetEQ_StreamRemove <= USED 0
;FILE streamingmedia.o
_Z17media_service_runv <= USED 0
streamingmedia_init <= USED 0
;FILE stubfun.o
errHandlerSaveDescFileLineWarning <= USED 0
errHandlerSaveDescWarning <= USED 0
;FILE stubs.o
L1FrGetUsedBbcTdsIfCount <= USED 0
;FILE sulog.o
SulogDmaCopy <= USED 0
;FILE supp_service.o
CB_Free <= USED 0
CDIV_Free <= USED 0
SuppService_CB_Group_Payload <= USED 0
SuppService_CB_MapToCBOption <= USED 0
SuppService_CB_Payload <= USED 0
SuppService_Enable_or_Disable_CB <= USED 0
SuppService_Enable_or_Disable_CB_Payload <= USED 0
SuppService_Set_CB <= USED 0
SuppService_Set_CB_Group <= USED 0
SuppService_Set_CDIV_NoReplyTimer <= USED 0
SuppService_Set_CDIV_NoReplyTimerPayload <= USED 0
Xdmc_Parse_Supp_CDIV_NoReplyTimer <= USED 0
Xdmc_Parse_Supp_CDIV_Status <= USED 0
;FILE sys_arch.o
;FILE sysapi.o
SysDebug_Flush <= USED 0
SysDebug_GetLogName <= USED 0
SysDebug_LogBuf_Cleanup <= USED 0
SysDebug_SetLogName <= USED 0
Sys_SmartBuf_IncrRefImpl <= USED 0
sys_printnl_buf <= USED 0
;FILE sysbuf.o
SysBuffer_CopyToMem <= USED 0
SysVector2Buffer_Set <= USED 0
;FILE syscache.o
;FILE syscrypt.o
SysCrypt_DigestGetHashLength <= USED 0
;FILE sysevent.o
SysEvent_Reset <= USED 0
;FILE sysio.o
SysGenericIo_Create <= USED 0
SysGenericIo_Delete <= USED 0
SysGenericIo_GetFlags <= USED 0
SysGenericIo_GetIoInterface <= USED 0
SysGenericIo_GetVector <= USED 0
SysGenericIo_SetIoState <= USED 0
SysIo_SetErrorState <= USED 0
SysLoopbackIo_Create <= USED 0
SysLoopbackIo_Delete <= USED 0
SysLoopbackIo_GetIoInterface <= USED 0
;FILE syslist.o
SysList_Cycle <= USED 0
;FILE sysmod.o
SysModule_Dump <= USED 0
SysObject_Dump <= USED 0
;FILE sysport.o
SysTimer_Time_To_Ticks <= USED 0
;FILE sysqueue.o
;FILE syssocket.o
SysGetSockName <= USED 0
SysGetSockopt <= USED 0
SysInet_pton4 <= USED 0
SysInet_pton6 <= USED 0
Sys_MapErrCode <= USED 0
;FILE systhread.o
;FILE systimer.o
SysTimer_SetLocalTimer <= USED 0
;FILE sysutil.o
SysUtil_MapEventCode <= USED 0
;FILE sysutils.o
;FILE syswait.o
checkObj <= USED 0
;FILE tavor_packages.o
plValTSInit <= USED 0
;FILE tbl.o
;FILE tcp.o
tcp_bind_netif <= USED 0
tcp_debug_print_pcbs <= USED 0
tcp_new_ip6 <= USED 0
tcp_setprio <= USED 0
;FILE tcp_in.o
;FILE tcp_out.o
;FILE tcpip.o
lwip_get_ul_non_cpy <= USED 0
lwip_mq_fetch <= USED 0
lwip_mq_size <= USED 0
lwip_set_rx_app_rate <= USED 0
lwip_tcpip_set_rate <= USED 0
tcpip_callbackmsg_delete <= USED 0
tcpip_callbackmsg_new <= USED 0
tcpip_input_eth_check <= USED 0
tcpip_timeout <= USED 0
tcpip_trycallback <= USED 0
tcpip_untimeout <= USED 0
;FILE telatci.o
CiInitCallBack <= USED 0
createIndForProxy <= USED 0
handle_CMEE_code <= USED 0
handle_CMSE_code <= USED 0
;FILE telcc.o
ciSyncAudio <= USED 0
;FILE telcontroller.o
Switch_Modem_State <= USED 0
ciTestTimeout <= USED 0
getChannelInfo <= USED 0
is_verbose_response <= USED 0
tcCreateMSGQ <= USED 0
tcOpenDevice <= USED 0
tcOpenExtSerialPort <= USED 0
;FILE teldat.o
Pssdc_Get <= USED 0
ciGpCGSink <= USED 0
initDataPPP <= USED 0
initDirectIP <= USED 0
search_handlelist_sequence <= USED 0
sendSinkData <= USED 0
;FILE teldbg.o
ciBTA2DP <= USED 0
ciBTHF <= USED 0
dbg_dump_buffer <= USED 0
telGetLwipCtrlMode <= USED 0
;FILE teldev.o
GripNotifyAP <= USED 0
ciRSSI <= USED 0
;FILE telmm.o
ciWbCellLock <= USED 0
;FILE telmsg.o
ciLockSMSStatus <= USED 0
telMsgIsSmsDataModeOn <= USED 0
;FILE telpb.o
ciSWritePB <= USED 0
;FILE telps.o
Encode4GBitrate <= USED 0
ciNetActivate <= USED 0
ciNetif <= USED 0
ciNetifCm <= USED 0
ciNetifDns <= USED 0
ciNetifRef <= USED 0
ciPSAttachWithCause <= USED 0
ciPsPlusPaging <= USED 0
ciStarMultiPdpSameApn <= USED 0
setMultiPdpSameApnFlag <= USED 0
;FILE telsim.o
ciGetCardMode <= USED 0
ciGetSpn <= USED 0
;FILE telss.o
get_com_stream_flag <= USED 0
;FILE telutl.o
GenerateAddressType <= USED 0
;FILE temp0.o
HCI_AMPTest <= USED 0
HCI_AMPTestEnd <= USED 0
HCI_AcceptLogicalLink <= USED 0
HCI_AcceptPhysicalLink <= USED 0
HCI_BaseBand_RXTest <= USED 0
HCI_BaseBand_TXTest <= USED 0
HCI_BaseBand_TestEnd <= USED 0
HCI_CalibClock <= USED 0
HCI_ChangeConnectionLinkKey <= USED 0
HCI_ChangeConnectionPacketType <= USED 0
HCI_CreateConnectionCancel <= USED 0
HCI_CreateLogicalLink <= USED 0
HCI_CreatePhysicalLink <= USED 0
HCI_DisconnectLogicalLink <= USED 0
HCI_DisconnectPhysicalLink <= USED 0
HCI_EnableAMPReceiverReports <= USED 0
HCI_EnhancedFlush <= USED 0
HCI_ExitParkMode <= USED 0
HCI_ExitPeriodicInquiryMode <= USED 0
HCI_FlowSpecModify <= USED 0
HCI_FlowSpecification <= USED 0
HCI_Flush <= USED 0
HCI_GetLinkQuality <= USED 0
HCI_HoldMode <= USED 0
HCI_HostNumberOfCompletedPackets <= USED 0
HCI_LEAddDeviceToWhiteList <= USED 0
HCI_LEClearWhiteList <= USED 0
HCI_LECreateConnectionCancel <= USED 0
HCI_LEEncrypt <= USED 0
HCI_LERand <= USED 0
HCI_LEReadAdvertisingChannelTxPower <= USED 0
HCI_LEReadBufferSize <= USED 0
HCI_LEReadChannelMap <= USED 0
HCI_LEReadLocalSupportedFeatures <= USED 0
HCI_LEReadRemoteUsedFeatures <= USED 0
HCI_LEReadSupportedStates <= USED 0
HCI_LEReadWhiteListSize <= USED 0
HCI_LEReceiverTest <= USED 0
HCI_LERemoveDeviceFromWhiteList <= USED 0
HCI_LESetHostChannelClassification <= USED 0
HCI_LESetScanResponseData <= USED 0
HCI_LETestEnd <= USED 0
HCI_LETransmitterTest <= USED 0
HCI_LocalSupportedCommands <= USED 0
HCI_LogicalLinkCancel <= USED 0
HCI_MasterLinkKey <= USED 0
HCI_ParkMode <= USED 0
HCI_PeriodicInquiryMode <= USED 0
HCI_QoSSetup <= USED 0
HCI_ReadAFHChannelAssessmentMode <= USED 0
HCI_ReadAFHChannelMap <= USED 0
HCI_ReadAuthenticationEnable <= USED 0
HCI_ReadAutomaticFlushTimeout <= USED 0
HCI_ReadBestEffortFlushTimeout <= USED 0
HCI_ReadClassOfDevice <= USED 0
HCI_ReadClock <= USED 0
HCI_ReadConnectionAcceptTimeout <= USED 0
HCI_ReadCurrentIACLAP <= USED 0
HCI_ReadDataBlockSize <= USED 0
HCI_ReadDefalultErroneousDataReporting <= USED 0
HCI_ReadDefaultLinkPolicySettings <= USED 0
HCI_ReadEncryptionKeySize <= USED 0
HCI_ReadEnhancedTransmitPowerLevel <= USED 0
HCI_ReadExtendedInquiryResponse <= USED 0
HCI_ReadFailedContactCounter <= USED 0
HCI_ReadFlowControlMode <= USED 0
HCI_ReadHoldModeActivity <= USED 0
HCI_ReadInquiryMode <= USED 0
HCI_ReadInquiryResponseTransmitPowerLevel <= USED 0
HCI_ReadInquiryScanActivity <= USED 0
HCI_ReadInquiryScanType <= USED 0
HCI_ReadLMPHandle <= USED 0
HCI_ReadLeHostSupported <= USED 0
HCI_ReadLinkPolicySettings <= USED 0
HCI_ReadLinkSupervisionTimeout <= USED 0
HCI_ReadLocalAMPInfo <= USED 0
HCI_ReadLocalAMPassoc <= USED 0
HCI_ReadLocationData <= USED 0
HCI_ReadLogicalLinkAcceptTimeout <= USED 0
HCI_ReadLoopbackMode <= USED 0
HCI_ReadNumBroadcastRetransmissions <= USED 0
HCI_ReadNumberOfSupportedIAC <= USED 0
HCI_ReadPINType <= USED 0
HCI_ReadPageScanActivity <= USED 0
HCI_ReadPageScanType <= USED 0
HCI_ReadPageTimeout <= USED 0
HCI_ReadRSSI <= USED 0
HCI_ReadRemoteExtendedFeatures <= USED 0
HCI_ReadRemoteVersionInformation <= USED 0
HCI_ReadSCOFlowControlEnable <= USED 0
HCI_ReadScanEnable <= USED 0
HCI_ReadSimplePairingMode <= USED 0
HCI_ReadStoredLinkKey <= USED 0
HCI_ReadTransmitPowerLevel <= USED 0
HCI_ReadVoiceSetting <= USED 0
HCI_RejectSynchronousConnectionRequest <= USED 0
HCI_ResetFailedContactCounter <= USED 0
HCI_RoleDiscovery <= USED 0
HCI_SendKeyPressNotification <= USED 0
HCI_SetAFHHostChannelClassification <= USED 0
HCI_SetEventFilter <= USED 0
HCI_SetEventMaskPage2 <= USED 0
HCI_ShortRangeMode <= USED 0
HCI_SniffMode <= USED 0
HCI_SniffSubrating <= USED 0
HCI_SwitchRole <= USED 0
HCI_Wakeup <= USED 0
HCI_WarmReset <= USED 0
HCI_WriteAFHChannelAssessmentMode <= USED 0
HCI_WriteBestEffortFlushTimeout <= USED 0
HCI_WriteConnectionAcceptTimeout <= USED 0
HCI_WriteDefaultErroneousDataReporting <= USED 0
HCI_WriteDefaultLinkPolicySettings <= USED 0
HCI_WriteFlowControlMode <= USED 0
HCI_WriteHoldModeActivity <= USED 0
HCI_WriteInquiryScanType <= USED 0
HCI_WriteInquiryTransmitPowerLevel <= USED 0
HCI_WriteLocalDeviceAddress <= USED 0
HCI_WriteLocationData <= USED 0
HCI_WriteLogicalLinkAcceptTimeout <= USED 0
HCI_WriteLoopbackMode <= USED 0
HCI_WriteNumBroadcastRetransmissions <= USED 0
HCI_WritePINType <= USED 0
HCI_WritePageScanActivity <= USED 0
HCI_WritePageScanType <= USED 0
HCI_WritePageTimeout <= USED 0
HCI_WriteRemoteAMPassoc <= USED 0
HCI_WriteSCOFlowControlEnable <= USED 0
HCI_WriteSimplePairingDebugMode <= USED 0
HCI_WriteStoredLinkKey <= USED 0
HCI_WriteUARTBaudrate <= USED 0
HCI_Write_Local_Key <= USED 0
HCI_Write_Peer_Key <= USED 0
;FILE temp1.o
;FILE temp10.o
;FILE temp100.o
MGR_LEReleaseACL <= USED 0
MGR_LEReleaseACLFromAddr <= USED 0
bt_mgr_le_free_all_devlist <= USED 0
bt_mgr_le_get_DevEntryByAddress <= USED 0
;FILE temp101.o
;FILE temp102.o
;FILE temp103.o
gatt_client_req <= USED 0
gatt_connect <= USED 0
gatt_connection_ind <= USED 0
gatt_disconn_ind <= USED 0
gatt_exchange_mtu <= USED 0
gatt_get_reconn_address <= USED 0
gatt_is_privacy_enabled <= USED 0
gatt_register_module <= USED 0
gatt_unregister_module <= USED 0
gatt_update_ll_param <= USED 0
;FILE temp104.o
att_client_request <= USED 0
;FILE temp105.o
att_get_service_range <= USED 0
att_srv_l2cap_connect_ind <= USED 0
;FILE temp106.o
SMP_GenRandomAddress <= USED 0
SMP_PINResponse <= USED 0
SMP_SecRequest <= USED 0
SMP_StartPair <= USED 0
;FILE temp107.o
;FILE temp108.o
ah <= USED 0
;FILE temp11.o
bt_timer_dump <= USED 0
;FILE temp12.o
MGR_BaseBand_RXTest <= USED 0
MGR_BaseBand_TXTest <= USED 0
MGR_BaseBand_TestEnd <= USED 0
MGR_GetConnectableMode <= USED 0
MGR_GetDeviceVersion <= USED 0
MGR_GetDiscoverableMode <= USED 0
MGR_GetLocalFeatures <= USED 0
MGR_GetLocalName <= USED 0
MGR_GetLocalOOBData <= USED 0
MGR_LEAddWhiteList <= USED 0
MGR_LEGetBondMode <= USED 0
MGR_LEPrivacySupport <= USED 0
MGR_LERemoveWhiteList <= USED 0
MGR_LESetBondMode <= USED 0
MGR_LESetDirectAddr <= USED 0
MGR_LESetSecLevel <= USED 0
MGR_WriteLocalDeviceAddress <= USED 0
bt_mgr_get_dut_mode <= USED 0
le_clear_advert_data <= USED 0
mgr_write_pagescan_parameters <= USED 0
;FILE temp13.o
MGR_ParseHexBDAddress <= USED 0
MGR_PrintLinkKey <= USED 0
bt_mgr_readHexBDAddress <= USED 0
;FILE temp14.o
MGR_GetConnectionRecord <= USED 0
bt_mgr_free_all_devicelist <= USED 0
;FILE temp15.o
MGR_Write_DHKey <= USED 0
;FILE temp16.o
MGR_GetRemoteName <= USED 0
;FILE temp17.o
;FILE temp19.o
SDP_DeviceServiceSearch <= USED 0
SDP_FreeAddressList <= USED 0
SDP_GenericBrowse <= USED 0
SDP_ServiceSearch <= USED 0
;FILE temp2.o
;FILE temp20.o
bt_sdp_flush_database <= USED 0
bt_sdp_generate_eir_service_uuid <= USED 0
;FILE temp21.o
;FILE temp22.o
bt_sdp_generate_attribute_request <= USED 0
bt_sdp_generate_search_request <= USED 0
;FILE temp23.o
;FILE temp24.o
;FILE temp25.o
;FILE temp26.o
;FILE temp27.o
bt_sdp_find_services <= USED 0
bt_sdp_generic_browse <= USED 0
bt_sdp_search_attributes <= USED 0
bt_sdp_w4_attributes <= USED 0
bt_sdp_w4_connections <= USED 0
bt_sdp_w4_service <= USED 0
;FILE temp28.o
;FILE temp29.o
bt_rfcomm_remove_server_inst_by_schan <= USED 0
;FILE temp3.o
bt_hci_eventCode2string <= USED 0
bt_hci_hciOpcode2string <= USED 0
bt_hci_linkPolicyMode2string <= USED 0
bt_hci_lmpOpcode2string <= USED 0
;FILE temp30.o
;FILE temp31.o
HandleRfcommTimerMultiCb <= USED 0
bt_rfcomm_dlc_next_state <= USED 0
;FILE temp32.o
bt_rfcomm_dump_frame <= USED 0
;FILE temp33.o
;FILE temp34.o
BT_RFCOMM_UE_Close_Req <= USED 0
BT_RFCOMM_UE_Deregister_Server <= USED 0
BT_RFCOMM_UE_Disable_Credit <= USED 0
BT_RFCOMM_UE_Enable_Credit <= USED 0
BT_RFCOMM_UE_Flow_Req <= USED 0
BT_RFCOMM_UE_Parneg_Credit_Req <= USED 0
BT_RFCOMM_UE_Parneg_Req <= USED 0
BT_RFCOMM_UE_Portneg_Req <= USED 0
BT_RFCOMM_UE_Set_Channel_Closed <= USED 0
BT_RFCOMM_UE_Test_Req <= USED 0
;FILE temp35.o
bt_rfcomm_cc_create_rpn_req <= USED 0
bt_rfcomm_cc_destroy_test <= USED 0
;FILE temp38.o
BT_RFCOMM_UE_Issue_Credits <= USED 0
BT_RFCOMM_UE_Set_Credit_Mode <= USED 0
;FILE temp39.o
;FILE temp4.o
hci_ReleaseCommandBuffer <= USED 0
;FILE temp40.o
;FILE temp41.o
RSE_SrvDeregisterPort <= USED 0
;FILE temp42.o
;FILE temp43.o
BT_RFCOMM_UE_Get_Test_Buffer <= USED 0
;FILE temp45.o
bt_l2_add_fcs <= USED 0
bt_l2_check_fcs <= USED 0
;FILE temp46.o
L2_Set_C_Bit <= USED 0
;FILE temp47.o
L2_Get_Flushable_WriteBuffer <= USED 0
L2_Urgency_Write <= USED 0
;FILE temp48.o
bt_l2_acl_shutdown_all <= USED 0
;FILE temp49.o
;FILE temp5.o
hci_flush_urgency_data_queue <= USED 0
hci_get_max_sco_size <= USED 0
hci_is_link_queue_empty <= USED 0
;FILE temp50.o
;FILE temp51.o
bt_l2_chn_disable_ERTX_timer <= USED 0
;FILE temp54.o
bt_l2_is_local_addr_large <= USED 0
;FILE temp55.o
bt_l2_config_aggregate_qos <= USED 0
bt_l2_config_flush_timeout_complete <= USED 0
bt_l2_config_link_supervision_timeout_complete <= USED 0
;FILE temp56.o
L2_Connect <= USED 0
L2_Dont_Send_Disconnect_Rsp <= USED 0
L2_Dont_Send_Echo_Response <= USED 0
L2_Ping <= USED 0
;FILE temp57.o
bt_l2cap_config_helper_find_connection <= USED 0
;FILE temp58.o
;FILE temp6.o
;FILE temp60.o
;FILE temp61.o
HF_Call_Answer <= USED 0
HF_Call_Hold <= USED 0
HF_Call_Request <= USED 0
HF_Call_Request_Msg <= USED 0
HF_Call_Status <= USED 0
HF_Clean_ghfp_callState <= USED 0
HF_Deactivate <= USED 0
HF_Get_Current_BdAddr <= USED 0
HF_Get_Current_Call_Status <= USED 0
HF_Get_Current_Profile <= USED 0
HF_Get_Device_State <= USED 0
HF_Get_Number <= USED 0
HF_Send_Button_Press <= USED 0
HF_Set_MemorySize <= USED 0
HF_Set_NetworkOperator <= USED 0
hfp_connect_after_sdp <= USED 0
;FILE temp62.o
HF_Enable_Band_Ring <= USED 0
HF_Send_CCWA <= USED 0
HF_Send_NoCarrier <= USED 0
;FILE temp63.o
HFRedailConfirmStatusClean <= USED 0
HF_Activate_Voice_Recognition <= USED 0
HF_Call_Accept <= USED 0
HF_Call_Reject <= USED 0
HF_Call_Release <= USED 0
HF_DeActivate_CLIP <= USED 0
HF_DeActivate_NR_EC <= USED 0
HF_DeActivate_Voice_Recognition <= USED 0
HF_Deactivate_Indicators_Event_Reporting <= USED 0
HF_Disable_Call_Waiting <= USED 0
HF_Enable_Called_Waiting <= USED 0
HF_Get_CallStatus <= USED 0
HF_Get_Clcc <= USED 0
HF_Get_NetworkOperator <= USED 0
HF_Get_PhoneBook <= USED 0
HF_Multiparty_Call_Handling <= USED 0
HF_Pbap_Clean <= USED 0
HF_Retrieve_Indicator_Status <= USED 0
HF_Retrieve_Memory <= USED 0
HF_Send_BINP_2 <= USED 0
HF_Send_BTRH <= USED 0
HF_Send_Call_Setup <= USED 0
HF_Send_Cnum <= USED 0
HF_Send_DTMF <= USED 0
HF_Send_NREC <= USED 0
HF_Set_PhoneBook_UTF8 <= USED 0
HF_Terminate_Call <= USED 0
HF_Voice_Recognition <= USED 0
Is_AG_Support_In_Band_Ring <= USED 0
;FILE temp64.o
Avdtp_Is_First_Conection <= USED 0
Avdtp_L2cap_Connection_Cfm_Cb <= USED 0
Avdtp_L2cap_Qos_Violation_Ind_Cb <= USED 0
Avdtp_Set_First_Conection <= USED 0
Avdtp_l2cap_config_ind_cb <= USED 0
;FILE temp65.o
Avdtp_Change_Sep_State <= USED 0
Avdtp_Check_MP3_Codecs_Compatible <= USED 0
Avdtp_Check_SBC_Codecs_Compatible <= USED 0
Avdtp_Disconnect_Media_Channel <= USED 0
Avdtp_Find_Matching_Codec_Specific_Information <= USED 0
Avdtp_Get_BdAddr <= USED 0
Avdtp_Get_Codec_Type <= USED 0
Avdtp_Select_Optimal_Codec <= USED 0
Avdtp_Select_Optimal_Sbc_Caps_Source <= USED 0
Avdtp_Stop_Data <= USED 0
Get_Sep_State <= USED 0
avdtp_is_media_cid <= USED 0
avdtp_match_audio_capabilities <= USED 0
;FILE temp66.o
;FILE temp67.o
Avdtp_Send_Abort <= USED 0
Avdtp_Send_GetConfig <= USED 0
Avdtp_Send_Reconfigure <= USED 0
;FILE temp68.o
;FILE temp69.o
Avdtp_Send_Sep_Capabilities_Get_Res <= USED 0
Avdtp_Send_Sep_Discover_Res <= USED 0
Avdtp_Send_Sep_Stream_Abort_Res <= USED 0
Avdtp_Send_Sep_Stream_Close_Res <= USED 0
Avdtp_Send_Sep_Stream_Config_Res <= USED 0
Avdtp_Send_Sep_Stream_Open_Res <= USED 0
Avdtp_Send_Sep_Stream_Start_Res <= USED 0
Avdtp_Send_Sep_Stream_Suspend_Res <= USED 0
;FILE temp7.o
BT_SetLocalCountryCode <= USED 0
BT_SetLocalDeviceAddress <= USED 0
;FILE temp70.o
Avrcp_L2cap_Config_Ind_Cb <= USED 0
Avrcp_L2cap_Connect_Cfm_Cb <= USED 0
Avrcp_L2cap_Qos_Violation_Ind_Cb <= USED 0
;FILE temp71.o
Avrcp_Get_Media_Attribute <= USED 0
Avrcp_Get_UnitInfo <= USED 0
Avrcp_Send_Key <= USED 0
Avrcp_Send_Press <= USED 0
Avrcp_Send_Release <= USED 0
Avrcp_Update_Player_Status <= USED 0
Get_Avrcp_State <= USED 0
;FILE temp72.o
SPP_Connect_Req <= USED 0
SPP_Disconnect_Req <= USED 0
SppFindClientDevice <= USED 0
spp_search_port <= USED 0
;FILE temp73.o
OBEX_GetCID <= USED 0
OBEX_GetConnectionInfo_RF <= USED 0
OBEX_ServerTransportDisconnect <= USED 0
bt_obex_allocate_cid <= USED 0
bt_obex_compair_registration <= USED 0
;FILE temp74.o
OBEX_GetFolderListing <= USED 0
OBEX_GetMsg <= USED 0
OBEX_GetMsgList <= USED 0
OBEX_MapSetNotificationRegistration <= USED 0
OBEX_PutMsg <= USED 0
OBEX_SetMap <= USED 0
bt_obex_map_set_name <= USED 0
bt_obex_map_set_param <= USED 0
map_envelop_message <= USED 0
;FILE temp75.o
BT_OPPC_PutHandle <= USED 0
OBEX_Abort <= USED 0
OBEX_Get <= USED 0
OBEX_GetMTU <= USED 0
;FILE temp76.o
OPP_SetDiscovery <= USED 0
get_obex_state <= USED 0
get_transport_tid <= USED 0
printf_oppc_tid <= USED 0
;FILE temp77.o
OBEX_CreateByteHeader <= USED 0
;FILE temp79.o
OBEX_PhoneBookDisconnect <= USED 0
OBEX_PullPhoneBook <= USED 0
OBEX_PullvCardEntry <= USED 0
OBEX_PullvCardList <= USED 0
OBEX_SetPhoneBook <= USED 0
OBEX_SetPhoneBookFormat <= USED 0
Strcat <= USED 0
bt_obex_phonebook_set_name <= USED 0
bt_obex_phonebook_set_param <= USED 0
;FILE temp8.o
;FILE temp80.o
OBEX_GenerateDigest <= USED 0
;FILE temp81.o
OBEX_AuthCheck <= USED 0
OBEX_AuthResponse <= USED 0
;FILE temp82.o
OBEX_EnableAuthentication <= USED 0
OBEX_L2_ACL_Expired <= USED 0
OBEX_ObjectPull <= USED 0
bt_obex_op_stream_callback <= USED 0
;FILE temp85.o
OBEX_CreateFolder <= USED 0
OBEX_Delete <= USED 0
OBEX_FTPConnect <= USED 0
OBEX_FTPPullFile <= USED 0
OBEX_FTPPushFile <= USED 0
OBEX_FindFTPServer <= USED 0
OBEX_FindObjectFtpServer <= USED 0
OBEX_GetFolderListingObject <= USED 0
bt_get_list_length <= USED 0
bt_obex_followed_by_GetFLO_callback <= USED 0
bt_obex_ftp_client_search_callback <= USED 0
bt_obex_ftp_client_search_callback2 <= USED 0
bt_obex_ftp_gather_callback <= USED 0
bt_obex_ftp_stream_callback <= USED 0
bt_read_uuid <= USED 0
;FILE temp87.o
sbc_decode <= USED 0
sbc_get_frame_duration <= USED 0
sbc_get_implementation_info <= USED 0
sbc_init_a2dp <= USED 0
sbc_init_msbc <= USED 0
sbc_parse <= USED 0
sbc_reinit <= USED 0
sbc_reinit_a2dp <= USED 0
;FILE temp88.o
;FILE temp89.o
;FILE temp9.o
;FILE temp90.o
ASRBT_ExitSniff <= USED 0
ASRBT_Get_Srand <= USED 0
ASRBT_Sdp_Add_Did_Service <= USED 0
ASR_Debug_PrintEX <= USED 0
LogLayer_Encode <= USED 0
asrbt_shutdown <= USED 0
is_zero_btaddr <= USED 0
;FILE temp91.o
;FILE temp92.o
;FILE temp93.o
;FILE temp94.o
AVDTP_Connect_Service <= USED 0
;FILE temp96.o
hci_transport_h5_enable_bcsp_mode <= USED 0
hci_transport_h5_instance <= USED 0
hci_transport_h5_set_auto_sleep <= USED 0
log_debug_hexdump <= USED 0
;FILE temp97.o
;FILE temp98.o
bt_obex_pbap_global_reset <= USED 0
;FILE temp99.o
bt_mgr_extract_eirdata <= USED 0
;FILE testprog.o
module_set_var <= USED 0
psm_show_list <= USED 0
psm_test <= USED 0
;FILE tft.o
tft_get_ip_info <= USED 0
;FILE tftcore.o
ModemGetCidByTft <= USED 0
SearchTFTNodeToFindCid <= USED 0
TftAddNewItem <= USED 0
tft_delete_tft <= USED 0
;FILE tick_manager.o
TickPhase1Init <= USED 0
;FILE tim.o
SendLteActTestModeCmp <= USED 0
SendLteCloseTestModeCmp <= USED 0
SendLteDeactTestModeCmp <= USED 0
SendLteOpenTestModeCmp <= USED 0
TimTask1 <= USED 0
TimTask_21 <= USED 0
;FILE timeM.o
;FILE timeTracking_test.o
TTDriverToIntegrationTest <= USED 0
TTDriverToRapidSwitchingTest <= USED 0
;FILE timer.o
AccTimerCreate <= USED 0
AccTimerDelete <= USED 0
AccTimerStart <= USED 0
AccTimerStartEx <= USED 0
AccTimerStop <= USED 0
GetTimerStatus <= USED 0
getTime_us <= USED 0
timerDidOSTimerExpire <= USED 0
timerEnableClock <= USED 0
timerPhase1Init <= USED 0
timerReschedule <= USED 0
timerResume <= USED 0
timerTSRGet <= USED 0
timerVersionGet <= USED 0
timerWakeupEventHandler <= USED 0
;FILE timers.o
lwip_timer_1s <= USED 0
lwip_timer_60s <= USED 0
lwip_timer_init <= USED 0
tcpip_timer_sleep_long <= USED 0
tcpip_timer_sleep_short <= USED 0
;FILE tmm.o
TmmCanAllocFromPool <= USED 0
TmmGetBlockLength <= USED 0
TmmGetBlockPoolId <= USED 0
TmmSetHwmAllocCount <= USED 0
;FILE tmmcfg.o
;FILE tsip_common.o
;FILE tsip_free.o
SIP_FreeAuthenticationInfo <= USED 0
SIP_FreeContentLanguage <= USED 0
SIP_FreeDigestChallenge <= USED 0
;FILE tsip_parser.o
cpytodelim <= USED 0
;FILE tsip_parser_uri.o
;FILE tx_block_allocate.o
;FILE tx_block_pool_cleanup.o
;FILE tx_block_pool_create.o
;FILE tx_block_pool_prioritize.o
;FILE tx_block_release.o
;FILE tx_event_flags_cleanup.o
;FILE tx_event_flags_create.o
;FILE tx_event_flags_delete.o
;FILE tx_event_flags_get.o
;FILE tx_event_flags_set.o
;FILE tx_hisr.o
_tx_hisr_delete <= USED 0
;FILE tx_initialize_high_level.o
_tx_initialize_high_level <= USED 0
;FILE tx_initialize_kernel_enter.o
_tx_initialize_kernel_enter <= USED 0
tx_application_define <= USED 0
;FILE tx_mutex_cleanup.o
;FILE tx_mutex_create.o
;FILE tx_mutex_delete.o
;FILE tx_mutex_get.o
;FILE tx_mutex_prioritize.o
;FILE tx_mutex_priority_change.o
;FILE tx_mutex_put.o
;FILE tx_queue_cleanup.o
;FILE tx_queue_create.o
;FILE tx_queue_delete.o
;FILE tx_queue_front_send.o
_tx_queue_front_send <= USED 0
;FILE tx_queue_prioritize.o
;FILE tx_queue_receive.o
_tx_queue_receive <= USED 0
_tx_queue_receive_ex <= USED 0
;FILE tx_queue_send.o
_tx_queue_send <= USED 0
_tx_queue_send_ex <= USED 0
;FILE tx_semaphore_cleanup.o
;FILE tx_semaphore_create.o
;FILE tx_semaphore_delete.o
;FILE tx_semaphore_get.o
;FILE tx_semaphore_prioritize.o
;FILE tx_semaphore_put.o
;FILE tx_thread_create.o
;FILE tx_thread_delete.o
;FILE tx_thread_info_get.o
;FILE tx_thread_initialize.o
_tx_thread_initialize <= USED 0
;FILE tx_thread_preemption_change.o
;FILE tx_thread_priority_change.o
;FILE tx_thread_relinquish.o
_tx_thread_relinquish <= USED 0
;FILE tx_thread_reset.o
;FILE tx_thread_resume.o
;FILE tx_thread_shell_entry.o
;FILE tx_thread_suspend.o
;FILE tx_thread_system_preempt_check.o
;FILE tx_thread_system_resume.o
;FILE tx_thread_system_suspend.o
;FILE tx_thread_terminate.o
;FILE tx_thread_time_slice.o
;FILE tx_thread_timeout.o
;FILE tx_timer_activate.o
;FILE tx_timer_change.o
;FILE tx_timer_create.o
;FILE tx_timer_deactivate.o
;FILE tx_timer_delete.o
;FILE tx_timer_expiration_process.o
;FILE tx_timer_info_get.o
_tx_timer_info_get <= USED 0
;FILE tx_timer_initialize.o
_tx_timer_initialize <= USED 0
;FILE tx_timer_system_activate.o
;FILE tx_timer_system_deactivate.o
;FILE tx_timer_thread_entry.o
_tx_timer_thread_register_hook <= USED 0
;FILE txe_queue_front_send.o
_txe_queue_front_send <= USED 0
;FILE txe_queue_receive.o
_txe_queue_receive_ex <= USED 0
;FILE txe_queue_send.o
_txe_queue_send_ex <= USED 0
;FILE txe_thread_relinquish.o
_txe_thread_relinquish <= USED 0
;FILE uac_dialogmgr.o
;FILE uas_dialogmgr.o
;FILE ubmc.o
UbmcUpdatePeriodEndedInd <= USED 0
;FILE ubmcschd.o
;FILE ubnd_cfg.o
UbndFddBandCanBeIdentifiedByUarfcn <= USED 0
UbndFddBandModeToFddBandNum <= USED 0
UbndFreqToUarfcn <= USED 0
UbndGetAddUarfcnEquivalent <= USED 0
UbndGetFddBandRegionForBandIndex <= USED 0
UbndGetFddBandTxRxSeparationForUarfcn <= USED 0
UbndGetOverlappingBand <= USED 0
UbndGetRegionMaskForFddBandRegion <= USED 0
UbndIsFddBandInRegion <= USED 0
UbndIsUarfcnBelongsToMoreThanOneBand <= USED 0
UbndIsUarfcnGcfTestUarfcn <= USED 0
UbndIsUarfcnInRegion <= USED 0
UbndIsUarfcnInStaticPrimeList <= USED 0
UbndMapBandFDDToBitMask <= USED 0
UbndShouldOverlappingBandBeScanned <= USED 0
UbndSib5BisIsScheduledInBand <= USED 0
;FILE udc_driver.o
UDCDriverActivateHardware <= USED 0
UDCDriverClearEndpointInterrupt <= USED 0
UDCDriverClearEventInterrupt <= USED 0
UDCDriverConfigureEndpoints <= USED 0
UDCDriverDatabaseReset <= USED 0
UDCDriverDeactivateHardware <= USED 0
UDCDriverDisableEndpointInterrupt <= USED 0
UDCDriverDisableEventInterrupt <= USED 0
UDCDriverEnableEndpointInterrupt <= USED 0
UDCDriverEnableEventInterrupt <= USED 0
UDCDriverEndpointActivate <= USED 0
UDCDriverEndpointClearReceiveStatus <= USED 0
UDCDriverEndpointClearStall <= USED 0
UDCDriverEndpointDeactivate <= USED 0
UDCDriverEndpointFlush <= USED 0
UDCDriverEndpointSetupIN <= USED 0
UDCDriverEndpointSetupINMultiTransmitWithDma <= USED 0
UDCDriverEndpointSetupOUT <= USED 0
UDCDriverEndpointStall <= USED 0
UDCDriverForceHostResume <= USED 0
UDCDriverGetCurrentConfigurationSettings <= USED 0
UDCDriverIsDeviceControllerEnabled <= USED 0
UDCDriverIsHostEnabledRemoteWakeup <= USED 0
UDCDriverPhase1Init <= USED 0
UDCDriverPhase2Init <= USED 0
UDCDriverReadFromFifo <= USED 0
UDCDriverResume <= USED 0
UDCDriverSuspend <= USED 0
UDCDriverWriteToFifo <= USED 0
UDCPadActivate <= USED 0
;FILE udp.o
udp_bind_netif <= USED 0
udp_send <= USED 0
;FILE ui_hal_uicus.o
_Z13getBootUpTypev <= USED 0
_Z13setBootUpTypei <= USED 0
isBootUpStandbydone <= USED 0
shutdown_process_nvm_flush_rsp <= USED 0
;FILE ulbgrabmdtc.o
ulbgRabmHandleSnDataReqInState <= USED 0
ulbgRabmProcessSnDataReq <= USED 0
ulbgRabmSplitLteSnMultiDataReq <= USED 0
;FILE ulbgrabmisc.o
;FILE ulbgrabmmain.o
;FILE ulbgrabmpdp.o
;FILE ulbgrabmrrc.o
UlbgRabmRabmRrcEstablishRej <= USED 0
UlbgRabmRabmRrcEstablishRes <= USED 0
;FILE ulbgrabmsm.o
;FILE ulbgrabmtimers.o
UlbgRabmStartReestabRejTimer <= USED 0
;FILE ulbgrabmutil.o
;FILE ulbgupdce.o
;FILE ulbgupdcp.o
;FILE ulbgupde.o
;FILE ulbgupde_rab.o
;FILE ulbgupde_rb.o
;FILE umm_psms.o
;FILE umm_rrc.o
;FILE umm_sim.o
;FILE umm_utils.o
;FILE ummtimer.o
;FILE updllist.o
;FILE urrc_ds3_stub.o
;FILE usb1_device.o
USB1DeviceCableDetectionNotify <= USED 0
USB1DeviceEP0InterruptHandler <= USED 0
USB1DeviceEP0ProcessSetupCmd <= USED 0
USB1DeviceEP0ProcessSetupState <= USED 0
USB1DeviceEP0TransferCompleted <= USED 0
USB1DeviceEndpointAbort <= USED 0
USB1DeviceEndpointClose <= USED 0
USB1DeviceEndpointGetHWCfg <= USED 0
USB1DeviceEndpointMultiTransmit <= USED 0
USB1DeviceEndpointOpen <= USED 0
USB1DeviceEndpointReceive <= USED 0
USB1DeviceEndpointReceiveCompleted <= USED 0
USB1DeviceEndpointReceiveCompletedExt <= USED 0
USB1DeviceEndpointStall <= USED 0
USB1DeviceEndpointTransmit <= USED 0
USB1DeviceEventNotify <= USED 0
USB1DeviceIsControllerEnabled <= USED 0
USB1DeviceMultiTransmitNotifyFn <= USED 0
USB1DevicePhase1Init <= USED 0
USB1DevicePhase2Init <= USED 0
USB1DeviceReceiveNotifyFn <= USED 0
USB1DeviceRegister <= USED 0
USB1DeviceRegisterPatchTempFunc <= USED 0
USB1DeviceTransmitNotifyFn <= USED 0
USB1DeviceVendorClassResponse <= USED 0
;FILE usb2_device.o
Check_AP2CP_Conflict <= USED 0
ClearResumePending <= USED 0
EnablePhy28 <= USED 0
EnablePhy28_Nezha3 <= USED 0
GPIOED_LISR <= USED 0
GPIO_InitWakeup <= USED 0
GetResumePending <= USED 0
GetUsbDDRLock <= USED 0
Get_GPIO_WakeupLevel <= USED 0
OSCR0IntervalInMicro_self <= USED 0
PauseMsec <= USED 0
SendUsbRemoteWakeup <= USED 0
SetUsbDDRLock <= USED 0
USB2DevicePhase1Init <= USED 0
USB2GetDTDEntry <= USED 0
USB_GPIO_WK <= USED 0
_usb_cancel_all_transfer <= USED 0
hsic_delay <= USED 0
hsic_poll_init <= USED 0
hsic_poll_rx_done <= USED 0
hsic_poll_tx_done <= USED 0
hsic_read_poll <= USED 0
hsic_write_poll <= USED 0
usbDeviceEndpoint_init <= USED 0
usb_get_dtd_nums <= USED 0
;FILE usb_descriptor.o
USB2ConfigureCdromDescriptor <= USED 0
USB2MgrDeviceUnplugPlug_SD <= USED 0
USB2MgrDeviceUnplugPlug_WebDav <= USED 0
USB2MgrMassStorageEnable <= USED 0
USBIsConnected <= USED 0
get_current_usb_app_mask <= USED 0
massStorageEnabled <= USED 0
;FILE usb_device.o
USBCableDetectionNotify <= USED 0
USBDeviceGetUSBSpeedInUse <= USED 0
USBDeviceGetUSBVersionInUse <= USED 0
USBDevicePhase1Init <= USED 0
USBDeviceRegisterPatchTempFunc <= USED 0
USBDeviceSetDescriptors <= USED 0
USBDeviceStatusGet <= USED 0
;FILE usb_init.o
mvUsbCheckProdctionMode <= USED 0
mvUsbGetRndisbitrate <= USED 0
mvUsbMassStorageInit <= USED 0
mvUsbStorageRead <= USED 0
mvUsbStorageWrite <= USED 0
;FILE usbcomdv.o
usbCommDevInitialise <= USED 0
usbCommDevMaxOutTransfer <= USED 0
;FILE usbcomnotify.o
usbCommNotifyConfigure <= USED 0
usbCommNotifyInitialise <= USED 0
usbCommNotifyTxSerialState <= USED 0
;FILE usbmgrttpal.o
USBMgrTTPALUpdateEnabledApps <= USED 0
UsbGetRequestID <= USED 0
UsbMgrTTPALInit <= USED 0
UsbMgrTTPALRegister <= USED 0
UsbMgrTTPALSetMuxUART <= USED 0
UsbMgrTTPALSetMuxUART_INT <= USED 0
UsbMgrTTPALSetMuxUSB <= USED 0
UsbMgrTTPALSetMuxUSB_INT <= USED 0
usbAppifDefaultRxBufferStructure <= USED 0
usbAppifDynamicSelectionValid <= USED 0
usbAppifFlushRxBuffer <= USED 0
usbAppifFlushTransmitRequestQueue <= USED 0
usbAppifGetDefaultConfiguration <= USED 0
usbAppifInitRxBuffer <= USED 0
usbAppifInitTxQueue <= USED 0
usbAppifPauseRxFlow <= USED 0
usbAppifRequestReceiveBufferFreeSpace <= USED 0
usbAppifRequestReceiveData <= USED 0
usbAppifSelectDynamicConfiguration <= USED 0
usbAppifStallInEndpoint <= USED 0
usbAppifStallOutEndpoint <= USED 0
usbHandleError <= USED 0
usbTargetTask1 <= USED 0
;FILE usbnet.o
ModeVolteDLParseCidToPacketType <= USED 0
usbnet_allocmem_extend <= USED 0
usbnet_leave_directip <= USED 0
;FILE usim.o
GetUSIMDDRLock <= USED 0
SIM_hotplug_GPIO_Config <= USED 0
USIMATRGet <= USED 0
USIMGPIOIRQHandler <= USED 0
USIMPhase1Init <= USED 0
USIMRxFIFO_Clear <= USED 0
USIMStatusGet <= USED 0
USIMVersionGet <= USED 0
USIMWarmReset <= USED 0
get__USIMDLstates_string <= USED 0
get_glfeatureflag <= USED 0
get_sim_swap_flag <= USED 0
set_glfeatureflag <= USED 0
;FILE usim_d2.o
;FILE usim_dl.o
;FILE usim_hw.o
USIM_HW_RXRead_Dummy <= USED 0
USIM_HW_deactivate_fast <= USED 0
USIM_HW_init <= USED 0
;FILE usim_transport.o
;FILE usimcfg.o
;FILE usimcmd.o
SimUiccExecuteGetChallengeCommand <= USED 0
;FILE usimdec.o
SimUiccDecodeAasRecord <= USED 0
;FILE usimemu.o
SimEmuCloseImageFile <= USED 0
SimEmuSaveImage <= USED 0
SimemuSendCardRemoveInd <= USED 0
;FILE usimenc.o
;FILE usiminit.o
;FILE usimmmreq.o
;FILE usimreqp1.o
SimUiccIsReadPlmnMore <= USED 0
;FILE usimreqp2.o
SimUiccSimEmuGenAccessReq <= USED 0
;FILE usimreqp3.o
;FILE usimrqp2p.o
;FILE usimstate.o
SetSimManagerExternalTasks <= USED 0
SimSendGLSwapFlag <= USED 0
;FILE usimutil.o
SimUiccDeleteExt1Chain <= USED 0
SimUiccDeleteUid <= USED 0
SimUiccSetHomezoneDir <= USED 0
;FILE ut_llsig.o
UtBrowseSignalList <= USED 0
UtSplitSignalList <= USED 0
;FILE ut_mcc_mnc.o
utmmPlIsIndianMcc <= USED 0
;FILE ut_sm.o
SmReadSemiOctetData <= USED 0
SmWriteSemiOctetData <= USED 0
utsmConvertOctetsToChars <= USED 0
utsmConvertUCS2ArrayToInt8Array <= USED 0
utsmGetHeaderLengthLen <= USED 0
utsmGetUserMessage <= USED 0
utsmGetUserMsgCharLen <= USED 0
utsmReadAddressData <= USED 0
utsmWriteAddressData <= USED 0
;FILE utbitfnc.o
;FILE utebmdyn.o
utEbmmFreeAllTaskMemory <= USED 0
;FILE uterr.o
utLogPdu <= USED 0
;FILE util.o
;FILE utilities.o
DbgEnableUart <= USED 0
DbgInitFifo <= USED 0
DbgLog <= USED 0
DbgPrintf_sim <= USED 0
DelayInMilliSecond <= USED 0
GetGpioLvl <= USED 0
GetTimer0CNT <= USED 0
RPCFuncRegister <= USED 0
ReadCCNT_with_PP <= USED 0
Read_Crane_CPU_UID <= USED 0
Read_Crane_Flash_UID <= USED 0
Timer0IntervalInMicro <= USED 0
Timer0_Switch <= USED 0
check_stack <= USED 0
cp_clear_gpio_ed <= USED 0
cp_gpio_init <= USED 0
cp_init_gpio_ed <= USED 0
date_to_utc <= USED 0
dfifo_cnt <= USED 0
dfifo_init <= USED 0
dump_stack_info <= USED 0
dump_task_info <= USED 0
get_lfs_cache_flush_delay_cnt <= USED 0
get_lfs_def_heap_free_level <= USED 0
get_lfs_def_heap_free_level_boot <= USED 0
get_lfs_mmi_heap_free_level <= USED 0
get_lfs_mmi_heap_free_level_boot <= USED 0
hsic_pm_gpio_setting <= USED 0
isChip_3601S <= USED 0
is_uart_to_diag <= USED 0
log_sdcard_set_partition <= USED 0
loop_delay <= USED 0
qspi_get_clk_setting <= USED 0
recordPreExcepInfo <= USED 0
record_pure_resume_context <= USED 0
record_pure_suspend_context <= USED 0
rti_SendQueue_record <= USED 0
rti_SetEvent_record <= USED 0
rti_get_eehandler_magic <= USED 0
rti_icu_switch_to <= USED 0
rti_pm_sleep_state <= USED 0
rti_pm_sleep_step <= USED 0
rti_pm_sleep_time <= USED 0
rti_printf <= USED 0
rti_record_aam_status <= USED 0
rti_set_CPMnewPowerState <= USED 0
rti_set_wakeupBits <= USED 0
rti_switch_check_data <= USED 0
rti_user_stamp <= USED 0
rti_user_value <= USED 0
setuartlogoff <= USED 0
sw_jtag_cmd <= USED 0
switch_to <= USED 0
timerTCRconfigureForSync <= USED 0
timerTMRsetForSync <= USED 0
uart_dump <= USED 0
usb_uart_connect <= USED 0
usb_uart_init <= USED 0
usb_uart_printf <= USED 0
usb_uart_rx <= USED 0
usb_uart_rx_data_out <= USED 0
usb_uart_tx <= USED 0
;FILE utils_c.o
GetCpuFamily <= USED 0
;FILE utl_trmsg.o
;FILE utlconvert.o
;FILE utllinkedList.o
utlDoGetTailNode <= USED 0
utlPutHeadList <= USED 0
utlPutTailList <= USED 0
;FILE utlsemaphore.o
utlAcquireSharedAccess <= USED 0
utlInitSemaphore_queue <= USED 0
utlReleaseSharedAccess <= USED 0
;FILE utltime.o
utlFormatDate <= USED 0
utlFormatDateTime <= USED 0
utlFormatTime <= USED 0
utlPause <= USED 0
;FILE utltimer.o
utlIsTimerRunning <= USED 0
utlQueryTimer <= USED 0
utlStartAbsoluteTimer <= USED 0
;FILE utltrace.o
utlAddTraceCriteria <= USED 0
utlDeleteTraceCriteria <= USED 0
utlDumpTracePegs <= USED 0
utlSetProcessName <= USED 0
;FILE utper.o
PerDecEndOfBitAlignedDataWithLength <= USED 0
PerDecSignedInt32 <= USED 0
PerDecStartOfBitAlignedDataWithLength <= USED 0
PerDecVariableOctetStringWithoutLength <= USED 0
PerEncConstrainedLengthWithoutMax <= USED 0
PerEncEndOfBitAlignedDataWithLength <= USED 0
PerEncEndOfBitAlignedDataWithLengthCap <= USED 0
PerEncSignedInt16 <= USED 0
PerEncSignedInt32 <= USED 0
PerEncStartOfBitAlignedDataWithLength <= USED 0
PerEncStartOfBitAlignedDataWithLengthCap <= USED 0
PerEncUnconstrainedBitString <= USED 0
PerEncVariableOctetString <= USED 0
PerEncVariableOctetStringWithoutMax <= USED 0
PerReturnLteCapExceed <= USED 0
PerReturnLteCapExceed1 <= USED 0
PerReturnLteCapExceed2 <= USED 0
PerReturnLteCapExceed2Initialized <= USED 0
PerSetLteCapExceed <= USED 0
PerSetLteCapExceed1 <= USED 0
PerSetLteCapExceed2 <= USED 0
PerSetLteCapExceedInit <= USED 0
PerSkipUnconstrainedBitString <= USED 0
;FILE utte_cfg.o
TeGetDataId <= USED 0
TeGetDataSubId <= USED 0
UtteCloseCheck <= USED 0
UtteOpenCheck <= USED 0
;FILE utte_fnc.o
TeClose <= USED 0
TeDestroySignal <= USED 0
TeFlush <= USED 0
TeOpen <= USED 0
TeReceiveSignalPoll <= USED 0
TeWaitForSignal <= USED 0
;FILE uttime.o
utDateAddDays <= USED 0
utGeneralizedTimeToRtcTime <= USED 0
utUTCTimeToRtcTime <= USED 0
utUnixToCurrentTime <= USED 0
;FILE utuc_fnc.o
mapUcs2ToChar <= USED 0
utByteAlignedEGsm7BitToUcs2 <= USED 0
utByteAlignedGsm7BitToChar <= USED 0
utDoubleOctetToUcs2 <= USED 0
utGetEGsmLengthOfUcs2 <= USED 0
utIsUcs2StringAllEGsm <= USED 0
utLengthAlphaIdToUcs2 <= USED 0
utSmsUcs2ToByteAlignedEGsm7Bit <= USED 0
utUcs2StringToDebug <= USED 0
utUcs2ToAlphaId <= USED 0
utUcs2ToByteAlignedEGsm7Bit <= USED 0
utUcs2ToChar <= USED 0
utUcs2ToDoubleOctet <= USED 0
;FILE utvjcomp.o
;FILE version.o
GetRfDcxoIsUsingFlgforplatform <= USED 0
GetSeagullFWVersionAndDate <= USED 0
GetUUID <= USED 0
setAPVersion <= USED 0
;FILE vgmxusbnull.o
VgMuxUsbNullTask1 <= USED 0
usbCommDevAppInitialise <= USED 0
;FILE vpath_ctrl.o
vpathCTMControl <= USED 0
vpathCodecClockSet <= USED 0
vpathDebugCmd <= USED 0
vpathDmaIntInd <= USED 0
vpathGSSPControl <= USED 0
vpathGSSPRead <= USED 0
vpathIsPcmSelfInvoked <= USED 0
vpathPlayStream <= USED 0
vpathRecordStream <= USED 0
vpathRxbControl <= USED 0
vpathSetSelfInvocation <= USED 0
vpathVoiceControl <= USED 0
;FILE vpath_data.o
;FILE vpath_handler.o
IMSBindRxFrame <= USED 0
amrBindRxPcmVoLTE <= USED 0
amrBindRxRequest <= USED 0
handleAudioIslandCompanderReport <= USED 0
handleAudioIslandRxAMR <= USED 0
handleAudioIslandTxAMR <= USED 0
;FILE vpath_mgr.o
;FILE wamr_rate.o
;FILE wamr_rx.o
;FILE wamr_services.o
amrBindConnectionClose <= USED 0
amrBindConnectionEstablish <= USED 0
ctmNegotiationReportBind <= USED 0
getGlobalDTMFCode <= USED 0
;FILE wamr_tx.o
IMSBindTxFrame <= USED 0
amrBindTxBitstreamFrameVoLTE <= USED 0
amrBindTxFrame <= USED 0
;FILE watchdog.o
PMIC_WD_TIMER_SET_DYNAMIC <= USED 0
watchdogIndicationClear <= USED 0
;FILE webrtc_neteq.o
WebRtcNetEQ_CodecDbAdd <= USED 0
WebRtcNetEQ_CodecDbGetCodecInfo <= USED 0
WebRtcNetEQ_CodecDbGetSizeInfo <= USED 0
WebRtcNetEQ_CodecDbRemove <= USED 0
WebRtcNetEQ_CodecDbReset <= USED 0
WebRtcNetEQ_GetErrorCode <= USED 0
WebRtcNetEQ_GetErrorName <= USED 0
WebRtcNetEQ_SetExtraDelay <= USED 0
WebRtcNetEQ_SetMaximumDelay <= USED 0
WebRtcNetEQ_SetMinimumDelay <= USED 0
WebRtcNetEQ_StreamDBReset <= USED 0
WebRtcNetEQ_StreamDbRemove <= USED 0
WebRtcNetEQ_strncpy <= USED 0
;FILE wgi_bind.o
wgiBindBypassGsmRfRAMInit <= USED 0
wgiBindGetBcchInfo <= USED 0
wgiBindReportWbFrameNumber <= USED 0
wgiBindResetBcchInfo <= USED 0
wgiBindRetreiveWbBchParameters <= USED 0
wgiBindSendUtranBchDecodeResults <= USED 0
wgiBindSetUtevStartBcchForWb <= USED 0
;FILE wificontroll.o
WifiSpyrrDsSendIratDsWifiAbortCnf <= USED 0
;FILE wifispy.o
;FILE xcapclient.o
;FILE xdmc_api.o
Xdmc_Close <= USED 0
Xdmc_SupServ_CDIV_SetNoReplyTimer <= USED 0
;FILE xdmc_common.o
Xdmc_Generate_CmdIdentifier <= USED 0
Xdmc_Generate_Listname <= USED 0
Xdmc_Get_Header <= USED 0
allocXdmcData <= USED 0
vrealloc <= USED 0
xdmc_random <= USED 0
;FILE xdmc_core.o
Xdmc_Init_DNS <= USED 0
;FILE xdmc_dns.o
;FILE xdmc_selector.o
addDocSelectorStr <= USED 0
;FILE xllp_dfc_1.o
XllpCheckDeviceType <= USED 0
XllpDfcInit <= USED 0
XllpLoadProperties <= USED 0
;FILE xllp_dfc_support.o
getAddr <= USED 0
getSpareAddr <= USED 0
xdfc_calculateOnfiCrc <= USED 0
xdfc_getECCArea_LP <= USED 0
xdfc_getInt <= USED 0
xdfc_getSpareArea_LP <= USED 0
xdfc_getStatusPadding <= USED 0
xdfc_read <= USED 0
xdfc_readID <= USED 0
xdfc_read_LP <= USED 0
xdfc_read_ONFI_ID <= USED 0
xdfc_read_ONFI_ParameterPages <= USED 0
xdfc_read_page <= USED 0
xdfc_read_page_sector <= USED 0
xdfc_read_page_spare <= USED 0
xdfc_save_ONFI_Parameters <= USED 0
xdfc_setTiming <= USED 0
xdfc_stripPad <= USED 0
xdfc_validateOnfiSignature <= USED 0
xdfc_write <= USED 0
xdfc_write_page <= USED 0
xdfc_write_page_spare <= USED 0
;FILE xllp_dma.o
XLLP_Return_PDmacHandle <= USED 0
XllpDmacClearDmaInterrupt <= USED 0
XllpDmacDescriptorFetch <= USED 0
XllpDmacDisableEORInterrupt <= USED 0
XllpDmacDisableStopIrqInterrupt <= USED 0
XllpDmacEnableEORInterrupt <= USED 0
XllpDmacEnableStopIrqInterrupt <= USED 0
configDescriptor <= USED 0
loadDescriptor <= USED 0
;FILE xmlConfInfoParser.o
;FILE xmlConfRecipientListParser.o
;FILE xrabmmain.o
;FILE xrabmutil.o
XRabmUpdatePdpState <= USED 0
;FILE xscale_stubs.o
PMUPhase2Init <= USED 0
bspBTClockReqEnable <= USED 0
;FILE xsmmain.o
;FILE ymodem.o
zError <= USED 0
zlibCompileFlags <= USED 0
zlibVersion <= USED 0
