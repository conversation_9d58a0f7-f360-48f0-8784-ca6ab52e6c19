#ifdef COMMENTSTART
__COMMENTSTART = DIAG_START_COMMENT,
#undef COMMENTSTART
#endif

DIAG(note_add_deprecation_attr, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "add a deprecation attribute to the declaration to silence this warning", 0, SFINAE_Suppress, false, false, 25)
DIAG(note_doc_block_command_previous, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "previous command '%select{\\|@}0%1' here", 0, SFINAE_Suppress, false, false, 25)
DIAG(note_doc_block_command_previous_alias, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "previous command '%select{\\|@}0%1' (an alias of '\\%2') here", 0, SFINAE_Suppress, false, false, 25)
DIAG(note_doc_html_end_tag, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "end tag", 0, <PERSON><PERSON><PERSON><PERSON>_Suppress, false, false, 25)
DIAG(note_doc_html_tag_started_here, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "HTML tag started here", 0, SFINAE_Suppress, false, false, 25)
DIAG(note_doc_param_name_suggestion, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "did you mean '%0'?", 0, SFINAE_Suppress, false, false, 25)
DIAG(note_doc_param_previous, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "previous documentation", 0, SFINAE_Suppress, false, false, 25)
DIAG(note_doc_tparam_name_suggestion, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "did you mean '%0'?", 0, SFINAE_Suppress, false, false, 25)
DIAG(note_doc_tparam_previous, CLASS_NOTE, (unsigned)diag::Severity::Fatal, "previous documentation", 0, SFINAE_Suppress, false, false, 25)
DIAG(warn_correct_comment_command_name, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unknown command tag name '%0'; did you mean '%1'?", 189, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_api_container_decl_mismatch, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0%select{class|interface|protocol|struct|union}1' command should not be used in a comment attached to a non-%select{class|interface|protocol|struct|union}2 declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_block_command_duplicate, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "duplicated command '%select{\\|@}0%1'", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_block_command_empty_paragraph, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "empty paragraph passed to '%select{\\|@}0%1' command", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_container_decl_mismatch, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0%select{classdesign|coclass|dependency|helper|helperclass|helps|instancesize|ownership|performance|security|superclass}1' command should not be used in a comment attached to a non-container declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_deprecated_not_sync, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "declaration is marked with '\\deprecated' command but does not have a deprecation attribute", 186, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_function_method_decl_mismatch, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0%select{function|functiongroup|method|methodgroup|callback}1' command should be used in a comment attached to %select{a function|a function|an Objective-C method|an Objective-C method|a pointer to function}2 declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_html_end_forbidden, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "HTML end tag '%0' is forbidden", 187, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_html_end_unbalanced, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "HTML end tag does not match any start tag", 187, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_html_missing_end_tag, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "HTML tag '%0' requires an end tag", 187, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_html_start_end_mismatch, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "HTML start tag '%0' closed by '%1'", 187, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_html_start_tag_expected_ident_or_greater, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "HTML start tag prematurely ended, expected attribute name or '>'", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_html_start_tag_expected_quoted_string, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "expected quoted string after equals sign", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_param_duplicate, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "parameter '%0' is already documented", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_param_invalid_direction, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unrecognized parameter passing direction, valid directions are '[in]', '[out]' and '[in,out]'", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_param_not_attached_to_a_function_decl, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0param' command used in a comment that is not attached to a function declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_param_not_found, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "parameter '%0' not found in the function declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_param_spaces_in_direction, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "whitespace is not allowed in parameter passing direction", 188, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_returns_attached_to_a_void_function, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0%1' command used in a comment that is attached to a %select{function returning void|constructor|destructor|method returning void}2", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_returns_not_attached_to_a_function_decl, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0%1' command used in a comment that is not attached to a function or method declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_tparam_duplicate, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "template parameter '%0' is already documented", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_tparam_not_attached_to_a_template_decl, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0tparam' command used in a comment that is not attached to a template declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_doc_tparam_not_found, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "template parameter '%0' not found in the template declaration", 185, SFINAE_Suppress, false, false, 25)
DIAG(warn_unknown_comment_command_name, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "unknown command tag name", 189, SFINAE_Suppress, false, false, 25)
DIAG(warn_verbatim_block_end_without_start, CLASS_WARNING, (unsigned)diag::Severity::Ignored, "'%select{\\|@}0%1' command does not terminate a verbatim text block", 185, SFINAE_Suppress, false, false, 25)
