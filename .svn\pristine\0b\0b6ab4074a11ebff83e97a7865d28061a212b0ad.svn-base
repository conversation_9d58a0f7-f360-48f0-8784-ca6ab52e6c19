if (DEFINED CONFIG_BUILD_CAMERA_TARGET)
    if (DEFINED CONFIG_BUILD_GC030A)
        fp_sources(cam_sensor_mipi_gc030a.c)
        fp_compile_definitions("SENSOR_GC030A_ACTIVE")
    endif()

    if (DEFINED CONFIG_BUILD_BF30A2)
        fp_sources(cam_sensor_spi_bf30a2.c)
        fp_compile_definitions("SENSOR_BF30A2_ACTIVE")
    endif()

    if (DEFINED CONFIG_BUILD_GC032A)
        fp_sources(cam_sensor_spi_gc032a.c)
        fp_compile_definitions("SENSOR_GC032A_ACTIVE")
    endif()

    if (DEFINED CONFIG_BUILD_GC0312)
        fp_sources(cam_sensor_dvp_gc0312.c)
        fp_compile_definitions("SENSOR_GC0312_ACTIVE")
    endif()

    if (DEFINED CONFIG_BUILD_GC2145)
        fp_sources(cam_sensor_dvp_gc2145.c)
        fp_compile_definitions("SENSOR_GC2145_ACTIVE")
    endif()

    if (DEFINED CONFIG_BUILD_GC2385)
        fp_sources(cam_sensor_mipi_gc2385.c)
        fp_compile_definitions("SENSOR_GC2385_ACTIVE")
    endif()

    if (DEFINED CONFIG_BUILD_GC6133C)
        fp_sources(cam_sensor_spi_gc6133c.c)
        fp_compile_definitions("SENSOR_GC6133C_ACTIVE")
    endif()

    fp_sources(
        cam_sensor.c
    )

    fp_include_directories(
        ./
        tuning
    )

    fp_install_include_files(tuning)
endif()
