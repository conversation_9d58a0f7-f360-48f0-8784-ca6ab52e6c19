#ifndef _PANEL_LIST_H_
#define _PANEL_LIST_H_
#ifdef CRANE_MCU_DONGLE
#include "plat_types.h"
#endif

typedef enum{
#ifdef LCD_GC9305_MCU
	GC9305_MCU,
#endif

#ifdef LCD_GC9305_SPI_3WIRE_1LANE_1IF
	GC9305_SPI_3WIRE_1LANE_1IF,
#endif

#ifdef LCD_GC9305_SPI_3WIRE_2LANE_1IF
	GC9305_SPI_3WIRE_2LANE_1IF,
#endif

#ifdef LCD_GC9305_SPI_4WIRE_1LANE_1IF
	GC9305_SPI_4WIRE_1LANE_1IF,
#endif

#ifdef LCD_GC9306_SPI_3WIRE_1LANE_1IF
	GC9306_SPI_3WIRE_1LANE_1IF,
#endif

#ifdef LCD_GC9306_SPI_3WIRE_2LANE_1IF
	GC9306_SPI_3WIRE_2LANE_1IF,
#endif

#ifdef LCD_GC9306_SPI_4WIRE_1LANE_1IF
	GC9306_SPI_4WIRE_1LANE_1IF,
#endif

#ifdef LCD_ST7789V_MCU
	ST7789V_MCU,
#endif

#ifdef LCD_ST7789V_SPI_3WIRE_1LANE_1IF
	ST7789V_SPI_3WIRE_1LANE_1IF,
#endif

#ifdef LCD_ST7789V_SPI_3WIRE_2LANE_1IF
	ST7789V_SPI_3WIRE_2LANE_1IF,
#endif

#ifdef LCD_ST7789V_SPI_4WIRE_1LANE_1IF
	ST7789V_SPI_4WIRE_1LANE_1IF,
#endif

#ifdef LCD_ST7789V_SPI_4WIRE_1LANE_2IF
	ST7789V_SPI_4WIRE_1LANE_2IF,
#endif

#ifdef LCD_DUMMY_MCU
	DUMMY_MCU,
#endif

#ifdef LCD_DUMMY_SPI_3WIRE_2LANE_1IF
	DUMMY_SPI,
#endif
#if defined(CONFIG_BOARD_CRANEM_EVB)
#ifdef LCD_ST7735S_SPI_4WIRE_1LANE_1IF
	ST7735S_SPI_4WIRE_1LANE_1IF,
#endif

#ifdef LCD_ST7735S_SPI_3WIRE_1LANE_1IF
	ST7735S_SPI_3WIRE_1LANE_1IF,
#endif
#endif

	PANEL_MAX
}PANEL_ID;

int get_panel_list(struct panel_spec*** plist);

#endif /*_PANEL_LIST_H_*/
