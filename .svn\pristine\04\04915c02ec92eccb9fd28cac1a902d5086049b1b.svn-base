/**
 * @file power_off_charging.h
 *
 */
#ifndef POWER_OFF_CHARGING_H
#define POWER_OFF_CHARGING_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
*      INCLUDES
*********************/

#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lvgl.h"
#include "lv_watch_conf.h"
#else
#include "../../../lvgl/lvgl.h"
#include "../../../lv_watch_conf.h"
#endif

#if USE_LV_WATCH_POWER_OFF_CHARGING != 0

/*********************
*      DEFINES
*********************/

/**********************
*      TYPEDEFS
**********************/
typedef struct
{
    lv_watch_obj_ext_t oldext;
    lv_task_t * battery_task;
    lv_obj_t *bat_img_list[6];
    uint8_t display_index;
    lv_task_t * ril_check_task;
} lv_power_off_charging_ext_t;

/**********************
* GLOBAL PROTOTYPES
**********************/
lv_obj_t *power_off_charging_create(lv_obj_t *activity_obj);

/**********************
*      MACROS
**********************/

#endif /*USE_LV_WATCH_POWER_OFF_CHARGING*/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*POWER_OFF_CHARGING_H*/
