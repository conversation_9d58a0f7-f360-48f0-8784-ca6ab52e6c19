/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2006, 2007 Marvell DSPC Ltd. All Rights Reserved.
-------------------------------------------------------------------------------------------------------------------*/

/************************************************************************/
/*  COPYRIGHT (C) 2002 Intel Corporation.                               */
/*                                                                      */
/*  This file and the software in it is furnished under                 */
/*  license and may only be used or copied in accordance with the terms */
/*  of the license. The information in this file is furnished for       */
/*  informational use only, is subject to change without notice, and    */
/*  should not be construed as a commitment by Intel Corporation.       */
/*  Intel Corporation assumes no responsibility or liability for any    */
/*  errors or inaccuracies that may appear in this document or any      */
/*  software that may be provided in association with this document.    */
/*  Except as permitted by such license, no part of this document may   */
/*  be reproduced, stored in a retrieval system, or transmitted in any  */
/*  form or by any means without the express written consent of Intel   */
/*  Corporation.                                                        */
/*                                                                      */
/* Title: Non Volotile Memory implementation File                       */
/*                                                                      */
/* Filename: nvm.c                                                      */
/*                                                                      */
/* Author:   Miriam Yovel                                               */
/*                                                                      */
/* Project, Target, subsystem: Tavor, Tavor ,software_util    	     	*/
/*																		*/
/* Remarks: -                                                           */
/*    													                */
/* Created: 11/4/2005                                                   */
/*                                                                      */
/* Modified:                                                            */
/************************************************************************/
#ifdef NVM_INCLUDE

/*----------- External include files -----------------------------------------*/
#include <stdio.h> // needed for the 'memclr' and 'memset' functions.
#include "global_types.h"
#include "timer.h"
#include "osa.h"
#include "diags.h"
#include "diag_API.h"
/*----------- Local include files --------------------------------------------*/

#include "bsp.h"
#include "csw_mem.h"
#include "nvmClient.h"
#include "nvmClient_def.h"
#include "nvmClient_utils.h"
#include "acipc_data.h"
#include "bspLog.h"
#include "mux27010.h"
#include "log.h"
#include "nvm_header.h"
#include "nvmClient_ttc.h"


/************************************************************************/
/*                      Global Parameters                           */
/************************************************************************/

#define NVMStartReqProcId 	0x0001
#define NVMStartCnfProcId 	0x0002
#define NVMReqProcId 		0x0003
#define NVMCnfProcId 		0x0004

#define NVMServiceId		0x02

//#define NVMC_TRACE	DbgPrintf
#define NVMC_TRACE(...)	
BspCustomType NVMcustomtype;
	

typedef struct _ShmNVMMsg
{
	int svcId;
	int procId;
	int msglen;
}ShmNVMMsgS;
#define SHM_HEADER_SIZE sizeof(struct _ShmNVMMsg)

static BOOL NVMC_LinkStatus = FALSE;
static OSAFlagRef HandShakeFlagRef;
static OSSemaRef      nvmBlockSemaRef;
static OSMailboxQRef  nvmRxIndMboxQRef;

/*the following 2 varaibles use by ICAT to display the amount on space left*/
UINT32 FdvBlockSize = 128*1024; /*block size*/
UINT16 FdvBlockCount= 10; /*numbers of block*/

extern ERR_CODE FDI_errno;
extern ACIPCD_ReturnCodeE ACIPCDSetPriority(ACIPCD_ServiceIdE ServiceId, UINT32 priority);
extern void DbgPrintf(char* fmt, ...);
void NVM_INIT_FILE(char *name, char *mode, UINT32 index_list, UINT32 index_open_list);
int NVMFileNameMatch(const char *src, const char *dest);
void NVMSetFileInfo(UINT32 index, FILE_INFO *file_information);
void NVM_RAM_initLists(void);
void NVM_RAM_SemaphoreInit(void);
extern FILE_ID FDI_fopen_test(const char *filename_ptr, const char *mode);
extern size_t FDI_fread_test(void *buffer_ptr, size_t element_size, size_t count, FILE_ID stream);
extern int FDI_fclose_test(FILE_ID stream);
extern int FDI_fseek_test(FILE_ID stream, long offset, int wherefrom);
extern size_t FDI_fwrite_test(const void *buffer_ptr,size_t element_size,size_t count,FILE_ID stream);
rfs_file_info_t * nvm_rfs_get_file_info(UINT32 index);
rfs_file_blk_t* nvm_rfs_get_blk(UINT32 index);
void NVMSetFileInfo_rfs(UINT32 index, FILE_INFO *file_information);
extern void ATRecv(int AtpIndex, void* data, UINT32 length);
#ifdef CRANEL_FP_8MRAM
extern int uart_printf(const char* fmt, ...);
extern unsigned int Fatsys_time(unsigned int *timer);
extern int FDI5to6_cTime2Time(const unsigned int clock);
extern int FDI5to6_cTime2Date(const unsigned int clock);
#endif
//ERR_CODE FDI_errno  = ERR_NOTEXISTS;

/*----------- Local function declarations ------------------------------------*/


/************************************************************************/
/*                      Callback functions from ACIPCD_TTC                     */
/************************************************************************/

void NVMCRxInd_ACIPC(void *ptr, UINT32 len)
{
	OSA_STATUS osaStatus;
	ShmNVMMsgS *shmArgs;

	shmArgs = (struct _ShmNVMMsg*)ptr;


	{
		NVM_OP_CODE *op_code;
		op_code = (NVM_OP_CODE *)((UINT8*)shmArgs + SHM_HEADER_SIZE);
		NVMC_TRACE("----NVMC Rx, opcode %d", *op_code);
	}


	/* handle handshake */
	if (shmArgs->procId == NVMStartReqProcId)
	{
		NVMC_LinkStatus = TRUE;

		/* reply ACK */
		/* handshake ok */
		OSAFlagSet(HandShakeFlagRef, 1, OSA_FLAG_OR);
		return;
	}

	if (shmArgs->procId == NVMCnfProcId)
	{
		/* send message to the msgq */
		osaStatus = OSAMailboxQSend(nvmRxIndMboxQRef, (void*)((UINT8*)shmArgs + SHM_HEADER_SIZE), OS_NO_SUSPEND);
		ASSERT(osaStatus == OS_SUCCESS );
	}


	return;
}

void NVMCTxDoneCnf_ACIPC(void *ptr)
{
	/* free buffer */
	free(ptr);
	
	return;
}

NVMC_ReturnCodeE NVMCTxReq_ACIPC(void* ptr, int length)
{
	ACIPCD_ReturnCodeE rc;
	NVMC_ReturnCodeE nvm_rc;
	ShmNVMMsgS *shmArgs;

	nvm_rc = NVM_RC_OK;

	shmArgs = (ShmNVMMsgS*)ptr;
	shmArgs->svcId = NVMServiceId;
	shmArgs->procId = NVMReqProcId;
	shmArgs->msglen = length;


	/* add a header for transmission */
	rc = ACIPCD_RC_HIGH_WM ;
	while (rc == ACIPCD_RC_HIGH_WM || rc == ACIPCD_RC_Q_FULL)
	{
		if (rc != ACIPCD_RC_OK)
			OSATaskSleep(1);
	}

	if (rc != ACIPCD_RC_OK)
	{
		nvm_rc = NVM_RC_ERROR;
	}

	return nvm_rc;
}


static 	UINT16 dlc_num;

void NVMCRxInd_Mux(unsigned int dlci, void *ptr, unsigned int len)
{
	OSA_STATUS osaStatus;
	ShmNVMMsgS *shmArgs;

	shmArgs = (struct _ShmNVMMsg*)ptr;

	{
		NVM_OP_CODE *op_code;
		op_code = (NVM_OP_CODE *)((UINT8*)shmArgs + SHM_HEADER_SIZE);
		NVMC_TRACE("----NVMC Rx, opcode %d\r\n", *op_code);
	}


	/* handle handshake */
	if (shmArgs->procId == NVMStartReqProcId)
	{
		NVMC_LinkStatus = TRUE;
		OSAFlagSet(HandShakeFlagRef, 1, OSA_FLAG_OR);
		return;
	}

	if (shmArgs->procId == NVMCnfProcId)
	{
		/* send message to the msgq */
		//uart_printf("got buf: 0x%x\r\n", (UINT32)((UINT8*)shmArgs + SHM_HEADER_SIZE));
		osaStatus = OSAMailboxQSend(nvmRxIndMboxQRef, (void*)((UINT8*)shmArgs + SHM_HEADER_SIZE), OS_NO_SUSPEND);
		ASSERT(osaStatus == OS_SUCCESS );
	}


	return;
}

void NVMCTxDoneCnf_Mux(unsigned int dlci, void *ptr, unsigned int len)
{
	/* free buffer */
	free(ptr);
	
	return;
}

NVMC_ReturnCodeE NVMCTxReq_Mux(void* ptr, int length)
{

	return NVM_RC_ERROR;

}

NVMC_ReturnCodeE NVMCTxReq(void* ptr, int length)
{
	if(AC_IS_2CHIP)
	{
		return NVMCTxReq_Mux(ptr,length);
	}else if(AC_IS_SHM)
	{
		
		return NVMCTxReq_ACIPC(ptr,length);
	}
	else
	{
		return NVM_RC_ERROR;
	}

}


/************************************************************************/
/*                      Internal use functions                          */
/************************************************************************/


/******************************************************************************
* Function     :   NVMCWaitForOpenResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCWaitForOpenResponse( UINT32 *retError ,NVMCFILE_ID *fileID)
{
	OSA_STATUS        				osaStatus;
	NVM_FILE_OPEN_RESPONSE_STRUCT   *openRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;

	NVMC_TRACE("NVM File WaitforOpenResponse: %x\r\n", fileID);
	
	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&openRsp, NVM_WAIT_TIMEOUT);
	ASSERT(osaStatus == OS_SUCCESS );

	if(openRsp->op_code == NVM_OP_CODE_FILE_OPEN)
	{
		*retError = (UINT32)openRsp->error_code;
		*fileID   = (NVMCFILE_ID)openRsp->hFile;

		/*For debug*/
		/*coverity[extra_comma]*/
		NVMC_TRACE("NVMC Open: file id %d, error code %d", openRsp->hFile, openRsp->error_code);
		DIAG_FILTER(SW_PLAT,NVM,NVMCWaitForOpenResponse,DIAG_INFORMATION)
		diagPrintf("NVMC Open: file id %d, error code %d", openRsp->hFile, openRsp->error_code);
	}
	else
	{
		NVMC_TRACE("NVM Open Response ERROR");

		DIAG_FILTER(SW_PLAT,NVM,NVM_OPEN_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM Open Response ERROR");
		rc = NVM_RC_ERROR;
	}

	if(AC_IS_SHM)
	{
		NVMC_TRACE("release buf: 0x%x\r\n", (UINT32)openRsp );
	}
	else
	{
		NVMC_TRACE("release buf: 0x%x, dlc_num: %d\r\n", (UINT32)openRsp, dlc_num);
	}
	return(rc);
}


/******************************************************************************
* Function     :   NVMCWaitForCloseResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCWaitForCloseResponse( UINT32 *retError )
{
	OSA_STATUS        				osaStatus;
	NVM_FILE_CLOSE_RESPONSE_STRUCT  *closeRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;

	NVMC_TRACE("NVM File WaitforCloseResponse\r\n");

	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&closeRsp, NVM_WAIT_TIMEOUT);
	ASSERT(osaStatus == OS_SUCCESS );

	if(closeRsp->op_code == NVM_OP_CODE_FILE_CLOSE)
	{
		*retError = (UINT32)closeRsp->error_code;
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_CLOSE_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC Close Response ERROR");
		rc = NVM_RC_ERROR;
	}



	return(rc);
}

/******************************************************************************
* Function     :   NVMCWaitForWriteResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCWaitForWriteResponse( UINT32 *retError ,UINT32 *actualWrite )
{
	OSA_STATUS        				osaStatus;
	NVM_FILE_WRITE_RESPONSE_STRUCT  *writeRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;

	NVMC_TRACE("NVM File WaitforWriteResponse\r\n");

	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&writeRsp, NVM_WAIT_TIMEOUT);
	if(osaStatus != OS_SUCCESS)
	{	//[gsfan] add this if condition block for tracing
		DIAG_FILTER(SW_PLAT,NVM,NVM_WRITE_RSP_ASSERT,DIAG_INFORMATION)
		diagPrintf("NVM GPC Write Response ASSERT, Ref:0x%x, osaStatus:0x%x", nvmRxIndMboxQRef, osaStatus);
	}
	ASSERT(osaStatus == OS_SUCCESS );

	if(writeRsp->op_code == NVM_OP_CODE_FILE_WRITE)
	{
		*retError 	 = (UINT32)writeRsp->error_code;
		*actualWrite = writeRsp->nNumberActuallItemsWrite;
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_WRITE_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC Write Response ERROR");
		rc = NVM_RC_ERROR;
	}


	return(rc);
}

static NVMC_ReturnCodeE NVMCWaitForSyncResponse( UINT32 *retError ,UINT32 *retopcode )
{
	OSA_STATUS        				osaStatus;
	RFS_SYNC_RESPONSE_STRUCT		*sendRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;

	NVMC_TRACE("NVM File WaitforWriteResponse\r\n");

	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&sendRsp, NVM_WAIT_TIMEOUT);
	if(osaStatus != OS_SUCCESS)
	{	//[gsfan] add this if condition block for tracing
		DIAG_FILTER(SW_PLAT,NVM,NVM_WRITE_RSP_ASSERT,DIAG_INFORMATION)
		diagPrintf("NVM GPC Write Response ASSERT, Ref:0x%x, osaStatus:0x%x", nvmRxIndMboxQRef, osaStatus);
	}
	ASSERT(osaStatus == OS_SUCCESS );

	DbgPrintf("Response %d\r\n",sendRsp->op_code);

	if((sendRsp->op_code == RFS_OP_CODE_START)||(sendRsp->op_code == RFS_OP_CODE_DATA)||(sendRsp->op_code == RFS_OP_CODE_END))
	{
		
		*retError	= (UINT32)sendRsp->error_code;
		*retopcode	= (UINT32)sendRsp->op_code;
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_WRITE_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC Write Response ERROR");
		rc = NVM_RC_ERROR;
	}


	return(rc);
}


/******************************************************************************
* Function     :   NVMCWaitForReadResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCWaitForReadResponse(UINT32 *retError ,UINT32 *actualRead ,void ** readBuf)
{
	OSA_STATUS        				osaStatus;
	NVM_FILE_READ_RESPONSE_STRUCT   *readRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;

	NVMC_TRACE("NVM File WaitforReadResponse\r\n");

	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&readRsp, NVM_WAIT_TIMEOUT);
	ASSERT(osaStatus == OS_SUCCESS );

	// UartLogPrintf("receive read response from mb");
	
	if(readRsp->op_code == NVM_OP_CODE_FILE_READ)
	{
		memcpy(*readBuf , readRsp->DataBuffer , readRsp->nNumberActuallItemsRead);
		*actualRead  = readRsp->nNumberActuallItemsRead;

		if( (readRsp->error_code == NVM_STATUS_SUCCESS)
				&& (readRsp->nNumberActuallItemsRead < readRsp->nDataBufferLength) )
		{  //FileSys not always returns the EOF. If read aborted, let's suppose the EOF
			*retError = NVM_STATUS_READ_EOF;  //It will be NVMC_TRANSLATE_ERR() into NVM_RC_EOF
		}
		else
		{
			*retError = (UINT32)readRsp->error_code;
		}
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_READ_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC Read Response ERROR");
		rc = NVM_RC_ERROR;
	}


	return(rc);
}

/******************************************************************************
* Function     :   NVMCWaitForSeekResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCWaitForSeekResponse( UINT32 *retError  )
{
	OSA_STATUS        				osaStatus;
	NVM_FILE_SEEK_RESPONSE_STRUCT   *seekRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;

	NVMC_TRACE("NVM File WaitforSeekResponse\r\n");

	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&seekRsp, NVM_WAIT_TIMEOUT);
	ASSERT(osaStatus == OS_SUCCESS );

	if(seekRsp->op_code == NVM_OP_CODE_FILE_SEEK)
	{
		*retError 	 = (UINT32)seekRsp->error_code;
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_SEEK_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC SEEK Response ERROR");
		rc = NVM_RC_ERROR;
	}

	return(rc);
}


/******************************************************************************
* Function     :   NVMCWaitRemoveResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCWaitForRemoveResponse( UINT32 *retError  )
{
	OSA_STATUS        				osaStatus;
	NVM_FILE_REMOVE_RESPONSE_STRUCT *removeRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;

	NVMC_TRACE("NVM File WaitforRemoveResponse\r\n");

	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&removeRsp, NVM_WAIT_TIMEOUT);
	ASSERT(osaStatus == OS_SUCCESS );

	if(removeRsp->op_code == NVM_OP_CODE_FILE_REMOVE)
	{
		*retError 	 = (UINT32)removeRsp->error_code;
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_REMOVE_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC REMOVE Response ERROR");
		rc = NVM_RC_ERROR;
	}


	return(rc);
}


/******************************************************************************
* Function     :   NVMCWaitFileFindFirstResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCWaitFileFindFirstResponse(NVMCFileInfoStruct *recFileParms,UINT32 *retError)
{
	OSA_STATUS osaStatus;
	NVM_FILE_FIND_FIRST_RESPONSE_STRUCT *fileFindFirstRsp;
	NVMC_ReturnCodeE rc = NVM_RC_OK;

	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&fileFindFirstRsp, NVM_WAIT_TIMEOUT);
	ASSERT(osaStatus == OS_SUCCESS );

	if(fileFindFirstRsp->op_code == NVM_OP_CODE_FILE_FIND_FIRST)
	{
		*retError 	    = (UINT32)fileFindFirstRsp->error_code;

		CONVERT_FROM_SHARED_STRUCT((&(fileFindFirstRsp->fileParms)),(recFileParms));
#if defined(NVM_DEBUG_ENABLE)
		DIAG_FILTER(NVM_remote,NVM_DEBUG,NVM_FILE_FIND_FIRST_RESPONSE_ST,DIAG_INFORMATION)
		diagStructPrintf("%S{NVM_FILE_FIND_FIRST_RESPONSE_STRUCT}",(VOID*)fileFindFirstRsp, sizeof(NVM_FILE_FIND_FIRST_RESPONSE_STRUCT));
#endif
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_FILE_FIND_FIRST_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC FILE FIND FIRST Response ERROR");
		rc = NVM_RC_ERROR;
	}


	return(rc);
}


/******************************************************************************
* Function     :   NVMCWaitFileFindNextResponse
* *******************************************************************************
*
* Description  :  Wait for Response from Remote side
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
*
* Notes:
******************************************************************************/

static NVMC_ReturnCodeE NVMCWaitFileFindNextResponse( NVMCFileInfoStruct *recFileParms,NVM_STATUS_T *retError)
{
	OSA_STATUS        				   osaStatus;
	NVM_FILE_FIND_NEXT_RESPONSE_STRUCT *fileFindNextRsp;
	NVMC_ReturnCodeE                 rc = NVM_RC_OK;


	osaStatus = OSAMailboxQRecv(nvmRxIndMboxQRef, (void**)&fileFindNextRsp, NVM_WAIT_TIMEOUT);
	ASSERT(osaStatus == OS_SUCCESS );

	if (fileFindNextRsp->op_code == NVM_OP_CODE_FILE_FIND_NEXT)
	{
		/*copy from gpc buffer back to user location*/
		*retError 	    = fileFindNextRsp->error_code; 
		CONVERT_FROM_SHARED_STRUCT((&(fileFindNextRsp->fileParms)),(recFileParms));

#if defined(NVM_DEBUG_ENABLE)
		DIAG_FILTER(NVM_remote,NVM_DEBUG,NVM_FILE_FINDNEXT_ST,DIAG_INFORMATION)
		diagStructPrintf("%S{NVM_FILE_FIND_NEXT_RESPONSE_STRUCT}",(VOID*)fileFindNextRsp, sizeof(NVM_FILE_FIND_NEXT_RESPONSE_STRUCT));
#endif
	}
	else
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_FILE_FIND_NEXT_RSP_ERR,DIAG_INFORMATION)
		diagPrintf("NVM GPC FILE FIND NEXT Response ERROR");
		rc = NVM_RC_ERROR;
	}


	return(rc);
}

/******************************************************************************
* Function     :   NVMCFileOpen
*******************************************************************************
*
* Description  :  Transmits Open file request to NVM Server.blocks until the request message is transfered succesfully and the
*                file ID is received from remote side or timeout
*
* Parameters   :   fileOpenName - name of file to be opened
*                  mode -   whether the file will be open for read write or both
*
* Output Param :   fileID - ID that represents the file that was opened
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
					NVM_RC_ALREADY_OPEN
*
* Notes:
******************************************************************************/
NVMC_ReturnCodeE   NVMCFileOpen(const char *fileOpenName,const char *attMode,NVMCFILE_ID *fileID)
{
	UINT8 					  	 *bufPtr;
	NVM_FILE_OPEN_REQUEST_STRUCT   *openBufPtr;
	NVMC_ReturnCodeE                nvmRCStatus = NVM_RC_OK;
	UINT32                          errorCode;

	*fileID = 0;

	NVMC_TRACE("NVM File Open: %s, fileID: %x\r\n", fileOpenName, *fileID);
	
	if( NVMC_LinkStatus == FALSE)
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_CHANNEL_EST_ERR_OPEN,DIAG_INFORMATION)
		diagPrintf("NVM Channel Did not established yet - ERROR");
		return (NVM_RC_ERROR );
	}   
	ASSERT (strlen(fileOpenName) < NVM_FILE_NAME_MAX_LENGHT);

	bufPtr = (UINT8 *)alignMalloc( sizeof(NVM_FILE_OPEN_REQUEST_STRUCT )+ SHM_HEADER_SIZE );
	ASSERT(bufPtr != NULL);

	memset(bufPtr,0 ,sizeof(NVM_FILE_OPEN_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	openBufPtr=(NVM_FILE_OPEN_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);
	openBufPtr->op_code 			= NVM_OP_CODE_FILE_OPEN;
	strcpy(openBufPtr->szFileName , fileOpenName );
	strcpy(openBufPtr->szFileAttributes , attMode );

	NVMC_BLOCK_SERVICE;

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr , sizeof(NVM_FILE_OPEN_REQUEST_STRUCT ));
	if (nvmRCStatus != NVM_RC_OK)
	{
		NVMC_RELEASE_SERVICE;
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitForOpenResponse(&errorCode ,fileID);
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		NVMC_RELEASE_SERVICE;
		return(NVM_RC_GPC_ERROR);
	}
	else NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	NVMC_RELEASE_SERVICE;      /*Enable other clients to use NVM services */

	return(nvmRCStatus);
}

/******************************************************************************
* Function     :   NVMCFileClose
*******************************************************************************
*
* Description  :    Transmits Close file request to NVM Server .blocks until the request message is transfered succesfully
*                  and an answer is received from remote side or timeout
*
* Parameters   :   fileID - ID that represents the file that need to be closed*
* Output Param :    None.
*
* Return value :    NVM_RC_OK ,
					NVM_RC_ERROR,
					NVM_RC_ALREADY_CLOSE
*
* Notes:
******************************************************************************/
NVMC_ReturnCodeE  NVMCFileClose(NVMCFILE_ID fileID)
{
	UINT8  						   *bufPtr;
	NVM_FILE_CLOSE_REQUEST_STRUCT  *closeBufPtr;
	NVMC_ReturnCodeE                nvmRCStatus;
	UINT32                          errorCode;

	bufPtr = (UINT8*) alignMalloc( sizeof(NVM_FILE_CLOSE_REQUEST_STRUCT)+ SHM_HEADER_SIZE );
	ASSERT(bufPtr != NULL);

	NVMC_TRACE("NVM File Close: %x\r\n", fileID);
	
	memset(bufPtr,0 ,sizeof(NVM_FILE_CLOSE_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	closeBufPtr=(NVM_FILE_CLOSE_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);
	closeBufPtr->op_code 	= NVM_OP_CODE_FILE_CLOSE;
	closeBufPtr->hFile 		= (UINT32)fileID;

	NVMC_BLOCK_SERVICE;

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr , sizeof(NVM_FILE_CLOSE_REQUEST_STRUCT ));
	if (nvmRCStatus != NVM_RC_OK)
	{
		NVMC_RELEASE_SERVICE;
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitForCloseResponse( &errorCode );
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		NVMC_RELEASE_SERVICE;
		return(NVM_RC_GPC_ERROR);
	}
	else NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	NVMC_RELEASE_SERVICE;      /*Enable other clients to use NVM services */

	return(nvmRCStatus);
}

/******************************************************************************
* Function     :   NVMCReadDataPacket 
*******************************************************************************
*
* Description  :    Transmits Read file request to NVM Server .blocks until the request message is transfered succesfully
*                  and the data of read file  is received from remote side or timeout
* Parameters   :    filePtr   - pointer to buffer to place data read
*                   elementSize - size of element referenced by buffer pointer
					bytes2Read    number of elements to be read
					fileID  - the ID of file that has to be read

* Output Param :    actualRead - number of elements succesfully read
*
* Return value :    None.
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCReadDataPacket (UINT8 *dataPtr , UINT32 elementSize , UINT32 element2Read ,UINT32 *actualRead , NVMCFILE_ID fileID )
{

	UINT8 				          *bufPtr;
	NVM_FILE_READ_REQUEST_STRUCT  *readBufPtr;
	UINT32                         errorCode;
	NVMC_ReturnCodeE               nvmRCStatus;

	bufPtr =(UINT8*) alignMalloc( sizeof(NVM_FILE_READ_REQUEST_STRUCT)+ SHM_HEADER_SIZE );
	ASSERT(bufPtr != NULL);

	memset(bufPtr,0 ,sizeof(NVM_FILE_READ_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	readBufPtr=(NVM_FILE_READ_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);

	readBufPtr->op_code 		= NVM_OP_CODE_FILE_READ;
	readBufPtr->nItemSize    	= elementSize; 
	readBufPtr->nNumberOfItems  = element2Read;
	readBufPtr->hFile           = (UINT32)fileID;


	NVMC_TRACE("[NVMCReadDataPacket]: nItemSize=%d, nNumberOfItems=%d",elementSize, element2Read);

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr , sizeof(NVM_FILE_READ_REQUEST_STRUCT ));
	if (nvmRCStatus != NVM_RC_OK)
	{
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitForReadResponse( &errorCode ,actualRead ,(void**)&dataPtr);
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		return(NVM_RC_GPC_ERROR);
	}
	else
	NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	return(nvmRCStatus);
}


/******************************************************************************
* Function     :   NVMCFileRead
*******************************************************************************
*
* Description  :    Transmits Read file request to NVM Server .blocks until the request message is transfered succesfully
*                  and the data of read file  is received from remote side or timeout
* Parameters   :    filePtr   - pointer to buffer to place data read
*                   elementSize - size of element referenced by buffer pointer
					bytes2Read    number of elements to be read
					fileID  - the ID of file that has to be read

* Output Param :    actualRead - number of elements succesfully read
*
* Return value :    None.
*
* Notes: 
******************************************************************************/
NVMC_ReturnCodeE  NVMCFileRead(void *filePtr, UINT32 elementSize,UINT32 element2Read, UINT32 *actualElementsRead ,NVMCFILE_ID fileID)
{
	UINT8                          *tempFilePtr;
	NVMC_ReturnCodeE               nvmRCStatus = NVM_RC_ERROR;
	UINT32                         totalSize ,totalPackets ,lastPacketSize;
	UINT32                         numOfPackets;
	UINT32  						 totalActualRead = 0,actualByteReadPerItr=0;

	ASSERT(elementSize!=0); /* this should not happened it could cause unexpect result that will disapear in the uppear layers
						better to reveal here*/	

	/* obtain semaphore in order to block other NVM Clients that need service in the same time*/
	NVMC_BLOCK_SERVICE;

	NVMC_TRACE("NVM File Read: %x\r\n", fileID);
	
	totalSize   = (elementSize * element2Read); /*in bytes*/
	tempFilePtr = (UINT8 *)filePtr;

	if ( totalSize > READ_DATA_RESPONSE_SIZE_IN_BYTES)
	{
		totalPackets  = totalSize/(UINT32)READ_DATA_RESPONSE_SIZE_IN_BYTES;
		lastPacketSize  = totalSize%READ_DATA_RESPONSE_SIZE_IN_BYTES;

		for(numOfPackets =0 ; numOfPackets < totalPackets ; numOfPackets++)
		{
			/*elevy- was a a bug here that pass the arguments in the wrong order. the second argument should be the element size and the third should be the
		number of element. as long as the request elements is the same as the actual element read, the bug will not appear. 
		but if the amount of elements requested is greater then the amount that been read(ICAT) the FDI7 read function return 0 instead of the actual element read*/
			nvmRCStatus = NVMCReadDataPacket (tempFilePtr + (numOfPackets*READ_DATA_RESPONSE_SIZE_IN_BYTES) , sizeof(UINT8) , READ_DATA_RESPONSE_SIZE_IN_BYTES ,&actualByteReadPerItr , fileID );
			if((nvmRCStatus != NVM_RC_OK)&&(nvmRCStatus != NVM_RC_EOF))
			goto EXIT;
			totalActualRead += actualByteReadPerItr;
			if(nvmRCStatus == NVM_RC_EOF)
			goto EXIT;
			/* elevy - we could add optimiziation here to check if the amount of elements received is less then READ_DATA_RESPONSE_SIZE_IN_BYTES. if so we can
		exit the loop and skip the last packet read (mainly happen if FDI_Transport initiate the read)*/
		}
		/*if everything OK - TRANSFERS LAST PACKET */
		if(lastPacketSize)
		{
			nvmRCStatus = NVMCReadDataPacket (tempFilePtr+(numOfPackets*READ_DATA_RESPONSE_SIZE_IN_BYTES) , sizeof(UINT8) , lastPacketSize ,&actualByteReadPerItr , fileID );
			if((nvmRCStatus != NVM_RC_OK)&&(nvmRCStatus != NVM_RC_EOF))
			goto EXIT;
			totalActualRead += actualByteReadPerItr;
			if(nvmRCStatus == NVM_RC_EOF)
			goto EXIT;
		}
		
	} /*end of total size > 1004 */
	else
	{  /* size < 1004*/
		nvmRCStatus = NVMCReadDataPacket (tempFilePtr , sizeof(UINT8) , totalSize ,&actualByteReadPerItr , fileID );
		if((nvmRCStatus != NVM_RC_OK)&&(nvmRCStatus != NVM_RC_EOF))
		goto EXIT;
		totalActualRead=actualByteReadPerItr;	
	}

EXIT:
	if((nvmRCStatus != NVM_RC_OK) && (nvmRCStatus != NVM_RC_EOF))
	{
		*actualElementsRead = 0;
		DIAG_FILTER(SW_PLAT, NVM, READ_FAILED , DIAG_INFORMATION)
		diagPrintf("NVM_remote - WARNING read FAILED");
		DIAG_FILTER(SW_PLAT, NVM, READ_FAILED1, DIAG_INFORMATION)
		diagPrintf("~~~~~~~~~ NVM_remote Read FAILED");
		WARNING(0); //or ASSERT
	}
	else
	{
		if ((totalActualRead / elementSize) != element2Read)
		{
			if(totalActualRead==0)
			{//Valid case when "New" file is opened as "wb"
				DIAG_FILTER(SW_PLAT,NVM,READ_WARNING_ZERO,DIAG_INFORMATION)
				diagPrintf("NVM_remote - WARNING read Request_%u != Actual_%u", (elementSize*element2Read), totalActualRead);
				//[klockwork][issue id: 2225]
				//PRINT_BSPLOG_FAST(elementSize, element2Read);
			}
			else
			{
				//ACAT FlashExplorer always requsts 1024. Let's ignore this but not othres
				if(elementSize*element2Read != 1024)
				{
					DIAG_FILTER(SW_PLAT,NVM,READ_WARNING,DIAG_INFORMATION)
					diagPrintf("NVM_remote - WARNING read Request_%u != Actual_%u", (elementSize*element2Read), totalActualRead);
					//PRINT_BSPLOG_FAST(elementSize*element2Read, totalActualRead);
					// requested num bytes   vs  obtained num bytes
				}
			}
			//nvmRCStatus == NVM_RC_EOF;
		}
#if defined(NVM_DEBUG_ENABLE)
		else
		{
			DIAG_FILTER(SW_PLAT,NVM,NVMCFileReadSucceed,DIAG_INFORMATION)
			diagPrintf("NVMCFileRead succeed element size = %d,requestCount = %d,actualCount = %d",elementSize,element2Read,totalActualRead);
		}
#endif	

		*actualElementsRead = totalActualRead/elementSize; /* return to the user the amount of elements read*/
	}

	NVMC_RELEASE_SERVICE; 
	return(nvmRCStatus);
}


/******************************************************************************
* Function     :   NVMCWriteDataPacket
*******************************************************************************
* Description  :   Transmits Write file REQUEST_STRUCT to NVM Server .blocks until the request message is transfered succesfully
*                  and write file indication  is received from remote side or timeout
*
*  Parameters   :   filePtr   - pointer to buffer to be wrriten
*                   elementSize - size of element referenced by buffer pointer
					bytes2Write -   number of elements to be written
					fileID  - the ID of file that has to be write to

*  Parameters   :
*
* Output Param :    None.
*
* Return value :    None.
*
* Notes:            1. from this point on the handle is free to acquire.
*                   2. When in debug mode this function will halt for unallocated handles.
******************************************************************************/
static NVMC_ReturnCodeE NVMCWriteDataPacket (UINT8 *dataPtr , UINT32 dataSize , UINT32 element2Write ,UINT32 *actualWrite , NVMCFILE_ID fileID )
{

	UINT8  		   				  *bufPtr;
	UINT32          				   totalSize2Send;
	NVM_FILE_WRITE_REQUEST_STRUCT  *writeBufPtr;
	UINT32                          errorCode;
	NVMC_ReturnCodeE                nvmRCStatus;

	totalSize2Send = sizeof(NVM_FILE_WRITE_REQUEST_STRUCT) - WRITE_DATA_SIZE_IN_BYTES + dataSize + SHM_HEADER_SIZE;

	bufPtr = alignMalloc( sizeof(NVM_FILE_WRITE_REQUEST_STRUCT) + SHM_HEADER_SIZE);
	ASSERT(bufPtr != NULL);

	/* initialize the temporary buffer */
	memset(bufPtr,0, sizeof(NVM_FILE_WRITE_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	writeBufPtr=(NVM_FILE_WRITE_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);

	writeBufPtr->op_code 		=  NVM_OP_CODE_FILE_WRITE;
	writeBufPtr->nItemSize      =  dataSize/element2Write; //elementSize;
	writeBufPtr->nNumberOfItems =  element2Write;
	writeBufPtr->hFile          =  (UINT32)fileID;
	memcpy(writeBufPtr->DataBuffer ,dataPtr , dataSize );

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr, totalSize2Send - SHM_HEADER_SIZE);  /*Send only NVM file size + header */
	if (nvmRCStatus != NVM_RC_OK)
	{
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitForWriteResponse( &errorCode ,actualWrite );
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		return(NVM_RC_GPC_ERROR);
	}
	else
	NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	return(nvmRCStatus);
}


static NVMC_ReturnCodeE NVMCRFSSyncPacket (UINT8 *dataPtr , UINT32 dataSize, UINT32 type, UINT32 *actualWrite)
{

	UINT8  		   				  	*bufPtr;
	UINT32          				totalSize2Send;
	RFS_SYNC_REQUEST_STRUCT  		*sendBufPtr;
	UINT32                          errorCode;
	NVMC_ReturnCodeE                nvmRCStatus;
	UINT32 temp001=0;

	DbgPrintf("Sync %d\r\n",type);

	totalSize2Send = sizeof(RFS_SYNC_REQUEST_STRUCT) - RFS_PACKAGE_MAX_LENGTH + dataSize + SHM_HEADER_SIZE;

	bufPtr = alignMalloc( sizeof(RFS_SYNC_REQUEST_STRUCT) + SHM_HEADER_SIZE);
	ASSERT(bufPtr != NULL);

	/* initialize the temporary buffer */
	memset(bufPtr,0, sizeof(RFS_SYNC_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	sendBufPtr=(RFS_SYNC_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);

	sendBufPtr->op_code 		=  type;
	sendBufPtr->buffer_len		=  dataSize;
	memcpy(sendBufPtr->DataBuffer ,dataPtr , dataSize );

	//temp001 = totalSize2Send - SHM_HEADER_SIZE;
	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr, totalSize2Send - SHM_HEADER_SIZE);  /*Send only NVM file size + header */
	if (nvmRCStatus != NVM_RC_OK)
	{
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}
	//alignFree((void*)bufPtr);

	nvmRCStatus = NVMCWaitForSyncResponse( &errorCode ,actualWrite );
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		return(NVM_RC_GPC_ERROR);
	}
	else
		NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	return(nvmRCStatus);

}


/******************************************************************************
* Function     :   NVMCFileWrite
*******************************************************************************
* Description  :   Transmits Write file REQUEST_STRUCT to NVM Server .blocks until the request message is transfered succesfully
*                  and write file indication  is received from remote side or timeout
*
*  Parameters   :   filePtr   - pointer to buffer to be wrriten
*                   elementSize - size of element referenced by buffer pointer
					bytes2Write -   number of elements to be written
					fileID  - the ID of file that has to be write to

*  Parameters   :
*
* Output Param :    None.
*
* Return value :    None.
*
* Notes:            1. from this point on the handle is free to acquire.
*                   2. When in debug mode this function will halt for unallocated handles.
******************************************************************************/
NVMC_ReturnCodeE  NVMCFileWrite(void *filePtr, UINT32 elementSize,UINT32 element2Write, UINT32 *actualWrite ,NVMCFILE_ID fileID)
{
	UINT8                *tempFilePtr;
	NVMC_ReturnCodeE      nvmRCStatus = NVM_RC_ERROR;
	UINT32                numOfPackets;
	UINT32                totalPackets , totalSize ,lastPacketSize;
	UINT32                totalActualWrite = 0;

	/* obtain semaphore in order to block other NVM Clients that need service in the same time*/
	NVMC_BLOCK_SERVICE;

	NVMC_TRACE("NVM File Write: %x\r\n", fileID);

	totalSize = (elementSize * element2Write);

	tempFilePtr = (UINT8 *)filePtr;

	if( totalSize > (UINT32)WRITE_DATA_SIZE_IN_BYTES )
	{
		totalPackets  = totalSize/(UINT32)WRITE_DATA_SIZE_IN_BYTES;
		lastPacketSize  = totalSize%WRITE_DATA_SIZE_IN_BYTES ;

		for(numOfPackets =0 ; numOfPackets < totalPackets ; numOfPackets++)
		{
			nvmRCStatus = NVMCWriteDataPacket (tempFilePtr + (numOfPackets*WRITE_DATA_SIZE_IN_BYTES) , WRITE_DATA_SIZE_IN_BYTES , NUMBER_OF_ELEMENT2WRITE ,actualWrite , fileID );
			if( nvmRCStatus != NVM_RC_OK)
			goto EXIT;
			totalActualWrite += (*actualWrite);
		}
		/*if everything OK - TRANSFERS LAST PACKET */
		if(lastPacketSize)
		{
			nvmRCStatus = NVMCWriteDataPacket (tempFilePtr+(numOfPackets*WRITE_DATA_SIZE_IN_BYTES) , lastPacketSize , NUMBER_OF_ELEMENT2WRITE ,actualWrite , fileID );
			if( nvmRCStatus != NVM_RC_OK)
			goto EXIT;
			totalActualWrite += (*actualWrite);
		}

		/*if every thing OK */
		if ( totalActualWrite == (totalPackets+1))  //for(totalPackets) + lastPacket
		{  //The totalActualWrite is number of records, but NOT a number of bytes to be returned
			*actualWrite = element2Write;
		}
	} /*of total size > 1004 */
	else
	{
		nvmRCStatus = NVMCWriteDataPacket (tempFilePtr , totalSize , NUMBER_OF_ELEMENT2WRITE,actualWrite , fileID );
		if( nvmRCStatus != NVM_RC_OK)
		goto EXIT;
		if ( *actualWrite == NUMBER_OF_ELEMENT2WRITE)
		{  //The totalActualWrite is number of records, but NOT a number of bytes to be returned
			*actualWrite     = element2Write;
		}
	}

EXIT:
	if((nvmRCStatus != NVM_RC_OK) && (nvmRCStatus != NVM_RC_EOF))
	{
		*actualWrite = 0;
		DIAG_FILTER(SW_PLAT, NVM, WRITE_FAILED , DIAG_INFORMATION)
		diagPrintf("NVM_remote - WARNING write FAILED");
		DIAG_FILTER(SW_PLAT, NVM, WRITE_FAILED1, DIAG_INFORMATION)
		diagPrintf("~~~~~~~~~ NVM_remote Write FAILED");
		WARNING(0); //or ASSERT
	}

	NVMC_RELEASE_SERVICE;
	return(nvmRCStatus);
}

/******************************************************************************
* Function     :   NVMCFileSeek
*******************************************************************************
* Description  :   Allows random access within a file.  t
*  Parameters   :  offset   - specifing a number of chracter
*                  whereFrom - a "seek code" indicating from what point in the file
*                             offset should be measured; modes supported:
*                    SEEK_SET = set file offset to offset
*                    SEEK_CUR = set file offset to current plus offset
*                    SEEK_END = set file offset to end plus offset (offset
*                               must be <= 0 since seeking beyond current
*                               end of file is not supported and returns
*                               error)
				fileID  - the ID of file
*
* Output Param :    None.
*
* Return value :    None.
*
******************************************************************************/
NVMC_ReturnCodeE  NVMCFileSeek(UINT32 offset, NVM_SEEK_ORIGIN whereFrom ,NVMCFILE_ID fileID)
{
	UINT8 						 *bufPtr;
	NVM_FILE_SEEK_REQUEST_STRUCT   *seekBufPtr;
	NVMC_ReturnCodeE                nvmRCStatus;
	UINT32                          errorCode;

	if( NVMC_LinkStatus == FALSE)
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_CHANNEL_EST_ERR_SEEK,DIAG_INFORMATION)
		diagPrintf("NVM GPC Channel Did not established yet - ERROR");
		return (NVM_RC_ERROR );
	}

	NVMC_TRACE("NVM File Seek: %x\r\n", fileID);
	
	bufPtr = (UINT8 *)alignMalloc( sizeof(NVM_FILE_SEEK_REQUEST_STRUCT)+ SHM_HEADER_SIZE );
	ASSERT(bufPtr != NULL);

	memset(bufPtr,0 ,sizeof(NVM_FILE_SEEK_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	seekBufPtr=(NVM_FILE_SEEK_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);
	seekBufPtr->op_code 		= NVM_OP_CODE_FILE_SEEK;
	seekBufPtr->hFile 		= (UINT32)fileID;
	seekBufPtr->nOffset       = offset;
	seekBufPtr->nOrigin       = whereFrom;

	NVMC_BLOCK_SERVICE;

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr , sizeof(NVM_FILE_SEEK_REQUEST_STRUCT ));
	if (nvmRCStatus != NVM_RC_OK)
	{
		NVMC_RELEASE_SERVICE;
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitForSeekResponse( &errorCode );
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		NVMC_RELEASE_SERVICE;
		return(NVM_RC_GPC_ERROR);
	}
	else NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	NVMC_RELEASE_SERVICE;      /*Enable other clients to use NVM services */

	return(nvmRCStatus);
}

/******************************************************************************
* Function     :   NVMCFileRemove
* *******************************************************************************
* Description  :    removes or deletes the named file
*  Parameters   :   file name   - the name of the file
*
* Output Param :    None.
*
* Return value :    None.
*
******************************************************************************/
NVMC_ReturnCodeE  NVMCFileRemove(const char *fileName)
{

	UINT8 						   *bufPtr;
	NVM_FILE_REMOVE_REQUEST_STRUCT *removeBufPtr;
	NVMC_ReturnCodeE                nvmRCStatus;
	UINT32                          errorCode;

	ASSERT (strlen(fileName) < NVM_FILE_NAME_MAX_LENGHT);

	NVMC_TRACE("NVM File Remove: %s\r\n", fileName);
	
	bufPtr = (UINT8 *)alignMalloc( sizeof(NVM_FILE_REMOVE_REQUEST_STRUCT)+ SHM_HEADER_SIZE );
	ASSERT(bufPtr != NULL);

	memset(bufPtr,0 ,sizeof(NVM_FILE_REMOVE_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	removeBufPtr=(NVM_FILE_REMOVE_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);
	removeBufPtr->op_code 	= NVM_OP_CODE_FILE_REMOVE;
	strcpy(removeBufPtr->szFileName ,fileName );

	NVMC_BLOCK_SERVICE;

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr , sizeof(NVM_FILE_REMOVE_REQUEST_STRUCT ));
	if (nvmRCStatus != NVM_RC_OK)
	{
		NVMC_RELEASE_SERVICE;
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitForRemoveResponse( &errorCode );
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		NVMC_RELEASE_SERVICE;
		return(NVM_RC_GPC_ERROR);
	}
	else NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	NVMC_RELEASE_SERVICE;      /*Enable other clients to use NVM services */

	return(nvmRCStatus);
}

/******************************************************************************
* Function     :   NVMCFileFindFirst
* *******************************************************************************
* Description  :  begins a search for files specified by the name
*###    wildcards.  The parameter filename_ptr is a string specifying the
*###    file name.  Wildcard match characters (* and ?) are supported.  The
*###    parameter fileinfo_ptr is a pointer to the type FILE_INFO which is
*###    filled with the file information.


*  Parameters   :   file name   - the name of the file
*                   file info -   pointer to the type FILE_INFO which is
								filled with the file information.
*
* Return value :    None.
*
******************************************************************************/
NVMC_ReturnCodeE  NVMCFileFindFirst(const char *fileName, NVMCFileInfoStruct *fileInfo)
{
	UINT8 							    *bufPtr;
	NVM_FILE_FIND_FIRST_REQUEST_STRUCT  *fileFindFirstBufPtr;
	NVMC_ReturnCodeE                     nvmRCStatus;
	UINT32                               errorCode;
	 
	if( NVMC_LinkStatus == FALSE)
	{
		DIAG_FILTER(SW_PLAT,NVM,NVM_CHANNEL_EST_ERR0,DIAG_INFORMATION)
		diagPrintf("NVM GPC Channel Did not established yet - ERROR");
		return (NVM_RC_ERROR );
	}
	ASSERT (strlen(fileName) < NVM_FILE_NAME_MAX_LENGHT);

	bufPtr = (UINT8 *)alignMalloc( sizeof(NVM_FILE_FIND_FIRST_REQUEST_STRUCT)+ SHM_HEADER_SIZE );
	ASSERT(bufPtr != NULL);

	memset(bufPtr,0 ,sizeof(NVM_FILE_FIND_FIRST_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	fileFindFirstBufPtr=(NVM_FILE_FIND_FIRST_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);
	fileFindFirstBufPtr->op_code 		= NVM_OP_CODE_FILE_FIND_FIRST;
	strcpy(fileFindFirstBufPtr->szFileName ,fileName);

#if defined(NVM_DEBUG_ENABLE)
	DIAG_FILTER(SW_PLAT,NVM,FIND_FIRST_BEFORE_GPC_TX,DIAG_INFORMATION)
	diagPrintf("Find first request has been sent. size of file_info struct %d", sizeof(NVMCFileInfoStruct));
#endif

	NVMC_BLOCK_SERVICE;

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr , sizeof(NVM_FILE_FIND_FIRST_REQUEST_STRUCT));
	if (nvmRCStatus != NVM_RC_OK)
	{
		NVMC_RELEASE_SERVICE;
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitFileFindFirstResponse(fileInfo,&errorCode);
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		NVMC_RELEASE_SERVICE;
		return(NVM_RC_GPC_ERROR);
	}
	else NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	NVMC_RELEASE_SERVICE;      /*Enable other clients to use NVM services */

	return(nvmRCStatus);
}

/******************************************************************************
* Function     :   NVMCFileFindNext
* *******************************************************************************
* Description  : fetches subsequent files that match the
*###    file name given in NVMCFileFindFirst function.  The parameter
*###    fileinfo_ptr is a pointer to the type FILE_INFO which is filled with
*###    the file information.


*  Parameters   :   none

* Output       :           file info -   pointer to the type FILE_INFO which is
								filled with the file information.
* Return value :    None.
*
******************************************************************************/
NVMC_ReturnCodeE  NVMCFileFindNext( NVMCFileInfoStruct *fileInfo)
{
	UINT8 							  *bufPtr;
	NVM_FILE_FIND_NEXT_REQUEST_STRUCT *fileFindNextBufPtr;
	NVMC_ReturnCodeE                  nvmRCStatus;
	NVM_STATUS_T					  errorCode;

	bufPtr = (UINT8 *)alignMalloc( sizeof(NVM_FILE_FIND_NEXT_REQUEST_STRUCT )+ SHM_HEADER_SIZE );
	ASSERT(bufPtr != NULL);

	memset(bufPtr,0 ,sizeof(NVM_FILE_FIND_NEXT_REQUEST_STRUCT )+ SHM_HEADER_SIZE);
	fileFindNextBufPtr=(NVM_FILE_FIND_NEXT_REQUEST_STRUCT*)(bufPtr+SHM_HEADER_SIZE);
	fileFindNextBufPtr->op_code = NVM_OP_CODE_FILE_FIND_NEXT;
	CONVERT_TO_SHARED_STRUCT((fileInfo),(&(fileFindNextBufPtr->fileParms)));

#if defined(NVM_DEBUG_ENABLE)
	DIAG_FILTER(SW_PLAT,NVM,FIND_NEXT_BEFORE_GPC_TX,DIAG_INFORMATION)
	diagPrintf("Find next request has been sent. size of struct %d", sizeof(NVM_FILE_FIND_NEXT_REQUEST_STRUCT));
#endif
	
	NVMC_BLOCK_SERVICE;

	nvmRCStatus = NVMCTxReq((UINT8*)bufPtr , sizeof(NVM_FILE_FIND_NEXT_REQUEST_STRUCT ));
	if (nvmRCStatus != NVM_RC_OK)
	{
		NVMC_RELEASE_SERVICE;
		alignFree((void*)bufPtr);
		return(NVM_RC_GPC_ERROR);
	}

	nvmRCStatus = NVMCWaitFileFindNextResponse(fileInfo,&errorCode);
	if (nvmRCStatus == NVM_RC_ERROR)
	{
		NVMC_RELEASE_SERVICE;
		return(NVM_RC_GPC_ERROR);
	}
	else NVMC_TRANSLATE_ERR(errorCode,nvmRCStatus);

	NVMC_RELEASE_SERVICE;      /*Enable other clients to use NVM services */

	return(nvmRCStatus);
}

/******************************************************************************
* Function     :  NVMCOpenChannel
*******************************************************************************
*
* Description  :   Registers to GPC services and opens GPC channel for NVM Client modoule
*
* Parameters   :   None.
*
* Output Param :   None.
*
*Return Value  :   NVM_RC_OK ,
				NVM_RC_ERROR,
* Return value :
*
* Notes        :  Should be called during intialization stage AFTER GPC modoule was intialized
******************************************************************************/
NVMC_ReturnCodeE  NVMCOpenChannel_ACIPC(void)
{
    ACIPCD_CallBackFuncS CallBackFunc;
    ACIPCD_ConfigS Config;
    ACIPCD_ReturnCodeE rc;
	UINT32 historicEvent;

	/* wait on handshake */
	OSAFlagWait(HandShakeFlagRef, 1, OSA_FLAG_OR_CLEAR, &historicEvent, OSA_SUSPEND);
	NVMC_TRACE("NVM Client Handshake ok");


	DIAG_FILTER(SW_PLAT, NVM, NVMCOpenChannel, DIAG_INFORMATION)
	diagPrintf("NVM Client Handshake ok");

    return NVM_RC_OK ;
}

void NVMCLinkStatusIndCB_Mux(unsigned int dlci,DlcLinkStatus status)
{
}
NVMC_ReturnCodeE  NVMCOpenChannel_Mux(void)
{
    ACIPCD_CallBackFuncS CallBackFunc;
    ACIPCD_ConfigS Config;
    ACIPCD_ReturnCodeE rc;
	MUX_CallBackFuncS funs;
	UINT32 historicEvent;
	int stat;


    memset( &CallBackFunc, 0, sizeof(CallBackFunc) ) ;
    memset( &Config, 0, sizeof(Config) ) ;
    Config.RpcTimout = NVM_WAIT_TIMEOUT ;
    Config.TxAction = ACIPCD_HANDLE_CACHE ;
    Config.BlockOnRegister = TRUE ;

	funs.RxIndCB = NVMCRxInd_Mux;
	funs.TxDoneCnfCB = NVMCTxDoneCnf_Mux;
	funs.LinkStatusIndCB = NVMCLinkStatusIndCB_Mux;
	//xwu
	NVMC_TRACE("DIAG Port: try to open channel\r\n");

	NVMC_TRACE("before\r\n");
	OSAFlagWait(HandShakeFlagRef, 1, OSA_FLAG_OR_CLEAR, &historicEvent, OSA_SUSPEND);
	NVMC_TRACE("after\r\n");
	NVMC_TRACE("NVM Client Handshake ok");
	/* send handshake ok */
	{
		ShmNVMMsgS* cnf = (ShmNVMMsgS*) malloc (sizeof(ShmNVMMsgS));
		//[klockwork][issue id: 2222]
		if(cnf == NULL)
		{
			ASSERT(0);
			return NVM_RC_ERROR;
			
		}
		cnf->svcId = NVMServiceId;
		cnf->procId = NVMStartCnfProcId;
		cnf->msglen = 0;

		free(cnf);

	}

	DIAG_FILTER(SW_PLAT, NVM, NVMCOpenChannel, DIAG_INFORMATION)
	diagPrintf("NVM Client Handshake ok");

    return NVM_RC_OK ;
}
NVMC_ReturnCodeE  NVMCOpenChannel(void)
{
	
	if (AC_IS_SHM)
	{
		return NVMCOpenChannel_ACIPC();
	}
	else
	{
		return NVMCOpenChannel_Mux();
	}
}


/******************************************************************************
* Function     :  NVMPhase1Init
*******************************************************************************
*
* Description  :   Initializes NVM CLIENT internal variables
*
* Parameters   :   None.
*
* Output Param :   None.
*
* Return value :   None.
*
* Notes        :
******************************************************************************/

void NVMCPhase1Init( void )
{
	NVMC_LinkStatus = FALSE;
	NVMcustomtype=bspGetBoardType();
}

/******************************************************************************
* Function     :  NVMPhase2Init
*******************************************************************************
*
* Description  :   Phase 2 initialization for NVM Client
*
* Parameters   :   None.
*
* Output Param :   None.
*
*Return Value  :   NVM_RC_OK ,
				NVM_RC_ERROR,
* Return value :
*
* Notes        :  Should be called during intialization stage AFTER GPC modoule was intialized
******************************************************************************/
void  NVMCPhase2Init(void)
{
	OSA_STATUS        osaStatus;

	/*******Intialize Osa  elements for NVM Client *****************/
	osaStatus = OSASemaphoreCreate( &nvmBlockSemaRef, 1, OS_FIFO);
	ASSERT(osaStatus == OS_SUCCESS);

	/* create event flag group for handshake */
	osaStatus = OSAFlagCreate(&HandShakeFlagRef);
	ASSERT(osaStatus == OS_SUCCESS);
	NVMC_TRACE("HandShakeFlagRef flag creates OK.\r\n");

#ifdef OSA_QUEUE_NAMES
	osaStatus = OSAMailboxQCreate( &nvmRxIndMboxQRef, "NVM Rec Queue" ,NVM_MAX_ITEMS_IN_MBX , OS_FIFO );
#else
	osaStatus = OSAMailboxQCreate( &nvmRxIndMboxQRef, NVM_MAX_ITEMS_IN_MBX , OS_FIFO );
#endif
	ASSERT(osaStatus == OS_SUCCESS);
}


#ifdef NVM_TEST
/*==============================================================================*/
#define TEST_NVM_BIG_FILE_STACK_SIZE       3200   /* The FDI_fopen() requires ~3k */
#define TEST_NVM_BIG_FILE_NAME          "testBigFile.bin"
#define TEST_NVM_BIG_FILE_SIZE          (20*1024)

static OSTaskRef  TestNvmBigFileTaskRef = NULL;
static char       TestNvmBigFileTaskStack[TEST_NVM_BIG_FILE_STACK_SIZE];
static char       TestNvmBigFileBuf   [TEST_NVM_BIG_FILE_SIZE];
static BOOL       TestNvmBigFileRun;
static UINT32     TestNvmBigFileCntr;

static void TestNvmBigFileTask(void* arg);
extern int dlmPrintf(const char* fmt,...);

//ICAT EXPORTED FUNCTION - Validation,NvmClient,BigFile
void TestNvmBigFile(void)
{
  if(!TestNvmBigFileRun)
  {
    OS_STATUS status;
    TestNvmBigFileRun = TRUE;
    TestNvmBigFileCntr = 0;

    status = OSATaskCreate(&TestNvmBigFileTaskRef, TestNvmBigFileTaskStack, TEST_NVM_BIG_FILE_STACK_SIZE-16, 
                            250, "nvmBigF", TestNvmBigFileTask, NULL);
         ASSERT(status == OS_SUCCESS);
  }
}

//ICAT EXPORTED FUNCTION - Validation,NvmClient,BigFileEND
void TestNvmBigFileEND(void)
{
    TestNvmBigFileRun = FALSE; //just stop it
    OSATaskSleep(500);
    OSATaskDelete(TestNvmBigFileTaskRef);
    OSATaskSleep(200);
}

#define TEST_NVM_BIG_FILE_ABORT(COND, STRing)   if(!(COND)) {TestNvmBigFilePrintErr(STRing); break;}

static void TestNvmBigFilePrintErr(char* str)
{
    DIAG_FILTER(Validation,NvmClient, TestNvmFAIL, DIAG_INFORMATION)
    diagPrintf("%s FAIL cnt_%d %s", TEST_NVM_BIG_FILE_NAME, TestNvmBigFileCntr, str);
    TestNvmBigFileRun = FALSE;
}

#define NVM_CHECK_BUFFER(buffer, value)	\
{ \
	UINT32 i; for (i = 0; i < TEST_NVM_BIG_FILE_SIZE; i++) \
		TEST_NVM_BIG_FILE_ABORT(((buffer)[i] == (value)), "buffer check failed!"); \
}

static void TestNvmBigFileTask(void* arg)
{
    UINT32    fRc;
    FILE_ID   fdiID;

	while(TestNvmBigFileRun)
	{
        fdiID = FDI_fopen(TEST_NVM_BIG_FILE_NAME,"wb");
            TEST_NVM_BIG_FILE_ABORT(fdiID != 0, "open-WR");

		/* 20k */
		memset(TestNvmBigFileBuf, TestNvmBigFileCntr % 255, sizeof(TestNvmBigFileBuf));
	    fRc = FDI_fwrite(TestNvmBigFileBuf, sizeof(char),TEST_NVM_BIG_FILE_SIZE, fdiID);
            TEST_NVM_BIG_FILE_ABORT(fRc == TEST_NVM_BIG_FILE_SIZE, "cant WR");
		/* 20k */
		memset(TestNvmBigFileBuf, (TestNvmBigFileCntr + 1) % 255, sizeof(TestNvmBigFileBuf));
		fRc = FDI_fwrite(TestNvmBigFileBuf, sizeof(char),TEST_NVM_BIG_FILE_SIZE, fdiID);
			TEST_NVM_BIG_FILE_ABORT(fRc == TEST_NVM_BIG_FILE_SIZE, "cant WR");
		/* 20k */
		memset(TestNvmBigFileBuf, (TestNvmBigFileCntr + 2) % 255, sizeof(TestNvmBigFileBuf));
		fRc = FDI_fwrite(TestNvmBigFileBuf, sizeof(char),TEST_NVM_BIG_FILE_SIZE, fdiID);
			TEST_NVM_BIG_FILE_ABORT(fRc == TEST_NVM_BIG_FILE_SIZE, "cant WR");

        fRc = FDI_fclose(fdiID);
            //TEST_NVM_BIG_FILE_ABORT(fRc == TEST_NVM_BIG_FILE_SIZE, "close WR");

        OSATaskSleep(2);

        fdiID = FDI_fopen(TEST_NVM_BIG_FILE_NAME,"rb");
            TEST_NVM_BIG_FILE_ABORT(fdiID != 0, "open-Read");

		/* 20k */
	    fRc = FDI_fread(TestNvmBigFileBuf,sizeof(char),TEST_NVM_BIG_FILE_SIZE, fdiID);
            TEST_NVM_BIG_FILE_ABORT(fRc == TEST_NVM_BIG_FILE_SIZE, "cant Read");
			NVM_CHECK_BUFFER(TestNvmBigFileBuf, TestNvmBigFileCntr % 255);

		/* 20k */
		fRc = FDI_fread(TestNvmBigFileBuf,sizeof(char),TEST_NVM_BIG_FILE_SIZE, fdiID);
			TEST_NVM_BIG_FILE_ABORT(fRc == TEST_NVM_BIG_FILE_SIZE, "cant Read");
			NVM_CHECK_BUFFER(TestNvmBigFileBuf, (TestNvmBigFileCntr + 1) % 255);

		/* 20k */
		fRc = FDI_fread(TestNvmBigFileBuf,sizeof(char),TEST_NVM_BIG_FILE_SIZE, fdiID);
			TEST_NVM_BIG_FILE_ABORT(fRc == TEST_NVM_BIG_FILE_SIZE, "cant Read");
			NVM_CHECK_BUFFER(TestNvmBigFileBuf, (TestNvmBigFileCntr + 2) % 255);

        fRc = FDI_fclose(fdiID);

        OSATaskSleep(200);
        if((++TestNvmBigFileCntr % 8) ==0)  dlmPrintf("TestNvmClient runs %d", TestNvmBigFileCntr);
	}//while

  DIAG_FILTER(Validation,NvmClient, TestNvmBigFileTask, DIAG_INFORMATION)
  diagPrintf("%s STOPPED", TEST_NVM_BIG_FILE_NAME);
}
#endif




/*----------------------------------------RFS---------------------------------------------*/

__align(32)

UINT8 nvm_rfs_buf_sync[NVM_RFS_SYNC_BUF_SIZE];
rfs_open_file_info_t rfs_open_file_info_array[NVM_RFS_FILE_NUM_MAX];

UINT32 rfs_total_blk_cnt=0;
UINT32 rfs_total_mrd_blk_cnt=0;

UINT32 rfs_total_fat_blk_cnt=0;
UINT32 rfs_total_data_blk_cnt=0;

UINT32 rfs_total_sync_item_cnt=0;
UINT32 rfs_sync_item_cnt=0;


/*Fix klocwork[Uninitialized Variable]*/
rfs_file_blk_t *p_rfs_file_blk_array = NULL;
rfs_file_info_t *p_rfs_file_info_array = NULL; 
rfs_open_file_info_t *p_rfs_open_file_info_array = NULL;
UINT8* p_nvm_rfs_buf = NULL;
UINT8* p_nvm_rfs_buf_mirror = NULL;
UINT8* p_nvm_rfs_buf_sync = NULL;






static UINT32 last_found_rfs = 0;
static char wildcards_rfs[NVM_RFS_FILE_NAME_LEN_MAX];
static OSSemaRef rfs_op_sema_ref;

UINT8 block_bitmap_buf[NVM_RFS_BIN_TOTAL_SIZE/(NVM_RFS_BLK_SIZE*8)+1];

#define RFS_TASK_STACK_SIZE     2048
#define RFS_TASK_PRIORITY       80
static OSTaskRef RfsTaskRef;
static UINT8 RfsTaskStack[RFS_TASK_STACK_SIZE];
UINT32 nvm_rfs_enable_simulator=1;
//@Hongji
#if (defined (PHS_SW_TAVORP_YARDEN_CP) || defined (PHS_SW_DEMO_TTC))
#include "diag.h"
#include "fdi_list.h"
#endif




/*--------------------------------OSA parameters----------------------------*/
static OSAFlagRef     nvmTxConfFlagRef;
static OSSemaRef      nvmBlockSemaRef;
static OSMailboxQRef  nvmRxIndMboxQRef;




 

/*----------- Local function declarations ------------------------------------*/


/************************************************************************/
/*                      Callback functions from GPC                     */
/************************************************************************/
/******************************************************************************
* Function     :   NVMCGPCEventIndNotifyCallBack
 * *******************************************************************************
*
* Description  :  Callback function from GPC to notify events
*
* Parameters   :   event
				   parms

* Output Param :   none
*
* Return value :  none
*
* Notes:
******************************************************************************/
	//@Hongji add nvm ram file system interface function
#if (defined(PHS_SW_TAVORP_YARDEN_CP) || defined(PHS_SW_DEMO_TTC))
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	/*----------- Local defines --------------------------------------------------*/
	
#define MAX_NUM_OF_FILES 100
#define INITIAL_FILE_LENGTH 300
#define FDI_RAM_MAX_FILE_NAME_LENTGH 70

/*----------- Local variable definitions -------------------------------------*/

static FILE_INFO_RAM FILE_LIST_RAM[MAX_NUM_OF_FILES] ;					//holds the files which are currently
																	//exists in the system
static FILE_INFO_OPEN_RAM FILE_OPEN_LIST_RAM[MAX_NUM_OF_FILES]; 		//holds the open files list
static UINT32 LastFound = 0;										//used for the find find first\next functions.
//static FDI_TCHAR *WILDCARDS = NULL;								//used for the find find first\next functions
static char WILDCARDS[FDI_RAM_MAX_FILE_NAME_LENTGH];		//used for the find find first\next functions	   
static OSASemaRef	_SemaRef_RAM = 0;									//semaphore which is used to prevent simultaneously files usage.
/*Fix klocwork[Uninitialized Variable]*/
ERR_CODE ERROR_CODE = ERR_NONE;										//used to hold the error code of the functions

#endif

/*----------- Local function declarations ------------------------------------*/


/************************************************************************/
/*						Callback functions from GPC 					*/
/************************************************************************/
/******************************************************************************
* Function	   :   NVMCGPCEventIndNotifyCallBack
 * *******************************************************************************
*
* Description  :  Callback function from GPC to notify events
*
* Parameters   :   event
				   parms

* Output Param :   none
*
* Return value :  none
*
* Notes:
******************************************************************************/
/*temp for MMI, allocate pool 2M*/
#if 0//remove the 2k heap_MMI_NV previously allocated for MMI, for large file when RAMFS of Z1 XIP version. 2019.03.29
	OsaRefT OsaRef_MMI_NV ;
#define Heapsize_MMI_NV 1024*2
	char heap_MMI_NV[Heapsize_MMI_NV];//pool:2M
	
	UINT32 MMI_NV_HeapInit(void )
	{
		
		OsaMemCreateParamsT
			Params ;
	
		 if(Heapsize_MMI_NV==0) return 0;
		uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
	
		memset( (void *)&Params, 0, sizeof(Params) ) ;
		Params.name = "MMI_NV_Pool" ;
		Params.poolBase = NULL ;		//	Pool header will be taken from the default pool.
		Params.poolSize = 0 ;
		ASSERT( OS_SUCCESS == OsaMemCreatePool(&OsaRef_MMI_NV,&Params) ) ;
		uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
	
		//OsaMemAddMemoryToPool( OsaRef_MMI, (void *)start, size, NULL ) ;
		OsaMemAddMemoryToPool( OsaRef_MMI_NV, (void *)heap_MMI_NV, Heapsize_MMI_NV, NULL ) ;
		uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
	
		return 1;
	}
#endif


/*Add by hongji to finish data proceed from RAM*/
NVMCFILE_ID NVM_RAM_fopen(const char *name, char *mode)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	DWORD index;
	DWORD *index_open_list;
	UINT32 index_list = MAX_NUM_OF_FILES;
	BOOL flag_exist = 0;
	UINT32 resMode1;
	UINT32 resMode2;
	UINT32 resName;
	OSA_STATUS			OSAStatus;


	DbgPrintf("NVM_RAM_fopen %s,%s\r\n",name, mode);

	/*acquire the semaphore in order to prevent files changes during the file opening */

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);


	//index_open_list = malloc(sizeof(UINT32));
	//ASSERT(index_open_list != NULL);

	// *index_open_list = MAX_NUM_OF_FILES;
	/* check if there is already an open file with this name. if so, return 0
	   if not, and find the first place where the file can be stored.*/

	/* check if there is already an existing file with this name
	   and find the first place where the file can be stored.*/

	for (index = 1; index < MAX_NUM_OF_FILES; index++)
	{
		if (FILE_LIST_RAM[index].flag_exist == 1 && strcmp(FILE_LIST_RAM[index].file_name, name) == 0)
		{
			flag_exist = 1;
			index_list = index;
			break;
		}
		else
		{
			if (FILE_LIST_RAM[index].flag_exist == 0 && index < index_list) 
				index_list = index;
			//return (WORD)0;
		}
	}

	DIAG_FILTER(NVM,RAM22,HONGJI,DIAG_INFORMATION)
	diagPrintf("The operation file index is %d", index_list);

	DbgPrintf("The operation file index is %d\r\n", index_list);
	
	/*if the file open mode is for reading it must exist.*/
	resMode1 = strcmp(mode, "rb");
	resMode2 = strcmp(mode, "rb+");

	if (!flag_exist && (resMode1 == 0 || resMode2 == 0))
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		//return (NULL);
		DbgPrintf("No file found\r\n");
		ERROR_CODE = ERR_NOTEXISTS;
		return (WORD)0;

	}

	/* initialize the lists of files.*/

	NVM_INIT_FILE((char*)name, mode, index_list, index_list);

	/*release the semaphore*/

	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);

	/*return the index of the new open file in the list of open files.*/

	//return (WORD)*index_open_list;
	DbgPrintf("NVM_RAM_fopen return 0x%x\r\n",index_list);
	return index_list;
#endif



}	/*End of FDI_RAM_fopen*/


/****************************************************************************************
 * Function 	: INIT_FILE 															*
 ****************************************************************************************
 *																						*
 * Description	: initialization of the new file										*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *				  index_list - the index of the file in the files list					*
 *				  index_open_list - the index of the file in the open files list.		*																*
 * Return:		  the file-id (index of the file in the files array)					*
 *																						*
 ****************************************************************************************/
void NVM_INIT_FILE(char *name, char *mode, UINT32 index_list, UINT32 index_open_list)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 resmode1 = 0;
	UINT32 resmode2 = 0;
	UINT32 size;

	/*initializing the files list in case that the file does not exist*/

	if (!FILE_LIST_RAM[index_list].flag_exist)
	{
		DbgPrintf("Lab01\r\n");
		size = strlen (name);
#if 0//#ifdef RUN_XIP_MODE
		FILE_LIST_RAM[index_list].file_name = OsaMemAlloc(OsaRef_MMI_NV, strlen (name) + 1);
#else
		FILE_LIST_RAM[index_list].file_name = malloc(strlen (name) + 1);
#endif
		ASSERT(FILE_LIST_RAM[index_list].file_name != NULL);
		strcpy (FILE_LIST_RAM[index_list].file_name, name);
		//FILE_LIST_RAM[index_list].date = getdate();
		FILE_LIST_RAM[index_list].flag_exist = 1;
		FILE_LIST_RAM[index_list].size = 0;
		//FILE_LIST_RAM[index_list].time = gettime();
#if 0
		FILE_LIST_RAM[index_list].file_start = OsaMemAlloc(OsaRef_MMI_NV,INITIAL_FILE_LENGTH*sizeof(BYTE));
#else
		FILE_LIST_RAM[index_list].file_start = malloc(INITIAL_FILE_LENGTH*sizeof(BYTE));
#endif
		ASSERT (FILE_LIST_RAM[index_list].file_start != NULL);
		FILE_LIST_RAM[index_list].file_end = FILE_LIST_RAM[index_list].file_start;
		FILE_LIST_RAM[index_list].access_mode = 0x1c0 | 0x038 | 0x007;		   //rwx
		//FILE_LIST_RAM[index_list].time ;
		FILE_LIST_RAM[index_list].MemPlace = INITIAL_FILE_LENGTH;
	}

	else /* if the file already exists */
	{

		/* if the file is open for writing then all of its content should be deleted*/
		DbgPrintf("Lab02\r\n");
		resmode1 = strcmp (mode, "wb");
		resmode2 = strcmp (mode, "wb+");

		if (FILE_LIST_RAM[index_list].file_start != NULL &&
			(resmode1 == 0 || resmode2 == 0))
		{
			DbgPrintf("Lab03\r\n");
#if 0
			OsaMemFree(FILE_LIST_RAM[index_list].file_start);
#else
			free(FILE_LIST_RAM[index_list].file_start);
#endif
#if 0
			FILE_LIST_RAM[index_list].file_start = OsaMemAlloc(OsaRef_MMI_NV,INITIAL_FILE_LENGTH*sizeof(BYTE));
#else
			FILE_LIST_RAM[index_list].file_start = malloc(INITIAL_FILE_LENGTH*sizeof(BYTE));
#endif
			ASSERT(FILE_LIST_RAM[index_list].file_start != NULL);
			FILE_LIST_RAM[index_list].MemPlace = INITIAL_FILE_LENGTH;
			FILE_LIST_RAM[index_list].file_end = FILE_LIST_RAM[index_list].file_start;
			FILE_LIST_RAM[index_list].size = 0;


		}
	}

	/*initialization of the file open list*/

#if 0
	FILE_OPEN_LIST_RAM[index_open_list].mode = OsaMemAlloc(OsaRef_MMI_NV,strlen(mode) + 1);
#else
	FILE_OPEN_LIST_RAM[index_open_list].mode = malloc(strlen(mode) + 1);
#endif
	ASSERT(FILE_OPEN_LIST_RAM[index_open_list].mode != NULL);
	strcpy (FILE_OPEN_LIST_RAM[index_open_list].mode, mode);
	FILE_OPEN_LIST_RAM[index_open_list].file_name = FILE_LIST_RAM[index_list].file_name;
	//strcpy (FILE_OPEN_LIST_RAM[index_open_list].file_name, name);
	FILE_OPEN_LIST_RAM[index_open_list].flag_exist = 1;
	FILE_OPEN_LIST_RAM[index_open_list].index_list = index_list;
	FILE_OPEN_LIST_RAM[index_open_list].access_mode = 0x1c0 | 0x038 | 0x007;

	/* if we open the file for reading\writing, then the pointer should point to the file start*/
	if (strcmp (mode, "rb") == 0  ||
		strcmp (mode, "wb") == 0  ||
		strcmp (mode, "rb+") == 0 ||
		strcmp (mode, "wb+") == 0	)
		FILE_OPEN_LIST_RAM[index_open_list].data = FILE_LIST_RAM[index_list].file_start;
	/* if we open the file for appending, then the pointer should point to the file end*/
	else
		FILE_OPEN_LIST_RAM[index_open_list].data = FILE_LIST_RAM[index_list].file_end;
#endif

}  /* end of INIT_FILE */


/****************************************************************************************
 * Function 	: FDI_RAM_fwrite														*
 ****************************************************************************************
 *																						*
 * Description	: write in a file in a binary mode. 									*
 *																						*
 * Input:		  buffer - the buffer which contain the data.							*
 *				  size - the size of one element of the buffer. 						*
 *				  size_t - the number of elements in the buffer.						*
 *				  file_id - the file identifier.										*
 * Return:		  number of elements which were written.								*
 *																						*
 ****************************************************************************************/
size_t NVM_RAM_fwrite(const void *buffer, size_t size, size_t count_size, NVMCFILE_ID file_id1)
{

#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	DWORD counter = 0;
	BYTE   *ptr = (BYTE *)buffer;
	BYTE   *ptr2 ;
	DWORD file_id = (DWORD)file_id1;
	UINT32 res1;
	UINT32 res2;
	UINT32 res3;
	UINT32 res4;
	UINT32 res5;
	UINT32 offset;
	BYTE *temp;


	OSA_STATUS			OSAStatus;


	DbgPrintf("NVM_RAM_fwrite %d,%d\r\n",size,count_size);

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);

	/*-----------------11/30/2005 3:44PM----------------
	 * if the file does not exist return -1 - error.
	 * --------------------------------------------------*/
	if (FILE_OPEN_LIST_RAM[file_id].flag_exist == 0)
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;

		return 0;
	}
	/*-----------------11/30/2005 3:45PM----------------
	 * check if the file mode is for writing. else, return -1 - error.
	 * --------------------------------------------------*/
	res1 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "wb");
	res2 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "rb+");
	res3 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "wb+");
	res4 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "ab");
	res5 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "ab+");
	if (res1 && res2 && res3 && res4 && res5)

	{
		DbgPrintf("Lab10\r\n");
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_OPENMODE;
		return 0;
	}
	/*-----------------11/30/2005 3:46PM----------------
	 * temporary pointer to the place where the data should be written.
	 * --------------------------------------------------*/
	ptr2 = FILE_OPEN_LIST_RAM[file_id].data;

	/*-----------------11/30/2005 3:47PM----------------
	 * copy from the buffer to the file - loop until we reach the size we wanted to write.
	 * --------------------------------------------------*/
	 //add because fseek does not affect fileSize
	 if(FILE_OPEN_LIST_RAM[file_id].data> FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end)	
	 {
	 	 FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end=FILE_OPEN_LIST_RAM[file_id].data;
		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end-FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start;
		//uart_printf("clab data>end\r\n");
	 }
	 
	while (FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size <
		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace && counter < count_size * size)
	{
			*ptr2 &= 0;
			*ptr2 |= *ptr;
			ptr ++ ;
			ptr2++ ;
			if (FILE_OPEN_LIST_RAM[file_id].data
				== FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end)
			FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size ++;
			FILE_OPEN_LIST_RAM[file_id].data ++;
			//in case that the size of the file became bigger than the
			//file size allocated, we allocate more memory space for the file - twice the current size.
			if (FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace ==
				FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size)
			{	//offset holds the offset of the data from the file start in order to know where to put the offset again.
				offset = FILE_OPEN_LIST_RAM[file_id].data -
					FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start;
				temp = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start;
#if 0
				FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start =
				OsaMemAlloc(OsaRef_MMI_NV, sizeof(char) *
					FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace * 2);
#else
				FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start =
					malloc (sizeof(char) *
					FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace * 2);
#endif
				ASSERT(FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start != NULL);
				memcpy (FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start,
					temp, FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size);
#if 0
				OsaMemFree(temp);
#else
				free (temp);
#endif
				FILE_OPEN_LIST_RAM[file_id].data =
					FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start + offset;
				FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace *= 2;
			}
			FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end =
			FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start +
			FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size;
			ptr2 = FILE_OPEN_LIST_RAM[file_id].data;
			counter++;
	}

	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);

//	uart_printf("NVM_RAM_fwrite return %d\r\n",(counter / size));
	DbgPrintf("NVM_RAM_fwrite return %d\r\n",(counter / size));
	return (counter / size);
#endif

}  /* end of FDI_RAM_fwrite */



/****************************************************************************************
 * Function 	: FDI_RAM_fread 														*
 ****************************************************************************************
 *																						*
 * Description	: read from file														*
 *																						*
 * Input:		buffer - the buffer which will hold the data.							*
 *				  size - the size of one element to be read.							*
 *				  size_t - the number of elements to be read.							*
 *				  file_id - the file identifier.										*
 * Return:		  number of elements which were read.									*
 *																						*
 ****************************************************************************************/
size_t NVM_RAM_fread(void *buffer, size_t size, size_t count_size, NVMCFILE_ID file_id1)
{

#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 counter = 0;
	BYTE   *ptr ;
	BYTE   *ptr2 ;
	DWORD file_id = (DWORD)file_id1;
	UINT32 res1;
	UINT32 res2;
	UINT32 res3;
	UINT32 res4;

	OSA_STATUS			OSAStatus;

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);

DbgPrintf("NVM_RAM_fread %d,%d\r\n",size,count_size);
	ptr = buffer;

	//check if the file exist, if not return error
	if (FILE_OPEN_LIST_RAM[file_id].flag_exist == 0)
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;
		return 0;
	}
	//check if the file was open for reading.
	res1 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "rb");
	res2 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "rb+");
	res3 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "wb+");
	res4 = strcmp(FILE_OPEN_LIST_RAM[file_id].mode, "ab+");
	if (!(res1 == 0 ||
		  res2 == 0 ||
		  res3 == 0 ||
		  res4 == 0))
		  {
		  	DbgPrintf("Lab15\r\n");
			//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
			//ASSERT(OSAStatus == OS_SUCCESS);
			ERROR_CODE = ERR_OPENMODE;
			return 0;
		  }


	ptr2 = (FILE_OPEN_LIST_RAM[file_id].data );
	//copy from the file to the buffer
	while (FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end > FILE_OPEN_LIST_RAM[file_id].data
			&& counter < count_size * size)
	{
			*ptr &= 0;
			*ptr |= *ptr2;
			ptr ++ ;
			ptr2++ ;
			FILE_OPEN_LIST_RAM[file_id].data ++;

			counter++;
	}
	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);

//	uart_printf("NVM_RAM_fread return %d\r\n",(counter / size));
	DbgPrintf("NVM_RAM_fread return %d\r\n",(counter / size));
	return (counter / size);
#endif


} /* end of FDI_RAM_fread */



 /****************************************************************************************
 * Function 	: FDI_RAM_fclose														 *
 ****************************************************************************************
 *																						*
 * Description	: open fdi file - if the file is not already exist it will be created.	*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
int NVM_RAM_fclose(NVMCFILE_ID file_id1)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	DWORD file_id = (DWORD)file_id1;
	OSA_STATUS			OSAStatus;



	DbgPrintf("NVM_RAM_fclose %d\r\n",file_id);

	FILE_OPEN_LIST_RAM[file_id].flag_exist = 0;
	FILE_OPEN_LIST_RAM[file_id].file_name=NULL;

	return 0;
#endif
} /* end of FDI_RAM_fclose */





 /****************************************************************************************
 * Function 	: FDI_RAM_fseek 														*
 ****************************************************************************************
 *																						*
 * Description	: This function sets the file position indicator of the file specified	*
 *				  by stream. The new position, measured in bytes from the beginning of	*
 *				  the file, is obtained by adding offset to the position				*
 *				  specified by wherefrom.												*
 *																						*
 * Input:		  file_id - the file identifier.										*
 *				  offset - the offset to obtain to the position indicator (in bytes)	*
 *				  wherefrom - the position to add offset. (SEEK_SET, SEEK_CUR, SEEK_END)*
 *																						*
 * Return:		  0 if success, -1 if error.											*
 *																						*
 ****************************************************************************************/
int NVM_RAM_fseek(NVMCFILE_ID file_id1, long offset, int wherefrom)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	//DWORD index = 0;
	DWORD file_id = (DWORD)file_id1;

	OSA_STATUS			OSAStatus;
	BYTE *temp;
	unsigned int v1,v2;

	int FilePos = 0;

	DbgPrintf("NVM_RAM_fseek\r\n");
	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);

	if (FILE_OPEN_LIST_RAM[file_id].flag_exist == 0)
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;
		return -1;
	}


	switch (wherefrom) {
	case 0:	// beginning of the file

			FilePos=offset;
			break;
				


	case 1: // the current position
		v1=(unsigned int)(FILE_OPEN_LIST_RAM[file_id].data);
		v2=(unsigned int)(FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start );

		  FilePos = v1-v2+offset;
		  break;



	case 2: // the end of the file
		FilePos = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size + offset;
		break;
		
	

	}

	if (FilePos < 0)
	{
		ERROR_CODE = ERR_NOTEXISTS;
		return -1;
	}


	uart_printf("FilePos=%d,%d, %d\r\n",FilePos, FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace, FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size);



	if((FilePos+1) >= FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace)
	{
		temp = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start;

		if((FilePos+1)>FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace * 2)//+1
			v1=(FilePos+1)*2;
		else
			v1=FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace * 2;

		uart_printf("v1=%d\r\n",v1);
	
#if 0
		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start =OsaMemAlloc(OsaRef_MMI_NV, v1);
#else
		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start =malloc(v1);
#endif
		ASSERT(FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start != NULL);

		memset(FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start, 0, v1);
		memcpy (FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start,temp, FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size);
#if 0
		OsaMemFree(temp);
#else
		free (temp);
#endif
		FILE_OPEN_LIST_RAM[file_id].data = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start + FilePos;
		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].MemPlace = v1;

		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start + FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size;		



	}
	else
	{
		FILE_OPEN_LIST_RAM[file_id].data = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start + FilePos;
	}


	if(FilePos>FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size)
	{
		//FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size=FilePos;
		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end = FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_start + FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size; 	
	
	}

	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);
	return 0;
#endif

} /* end of FDI_RAM_fseek */


unsigned int NVM_RAM_fsize(NVMCFILE_ID file_id1)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	//DWORD index = 0;
	DWORD file_id = (DWORD)file_id1;



		return (unsigned int)(FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].size);
#endif


} /* end of FDI_RAM_fseek */



/****************************************************************************************
 * Function 	: FDI_RAM_findfirst 													*
 ****************************************************************************************
 *																						*
 * Description	: search for the first file which is compatible to the wildcard and copy*
 *				  its information to the file_information pointer.						*
 *																						*
 * Input:		  wildcard - the file name while asterisk can be any combination of 	*
 *				  characters and ? can be any single character. 						*
 *																						*
 * Return:		  the file information is set to the file found information 			*
 *				  the function returns 0 if success -1 if it didn't find any file match *
 ****************************************************************************************/
int NVM_RAM_findfirst(const char *wildcard , FILE_INFO *file_information)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 index;

	DbgPrintf("NVM_RAM_findfirst\r\n");
	for (index = 1; index < MAX_NUM_OF_FILES; index ++)
	{
		/*elevy - fileNameMatch should compare between the existing files database and the receive file name ( and not file_information)*/
		//if (FileNameMatch(file_information, wildcard) && FILE_LIST_RAM[index].flag_exist)
		if (NVMFileNameMatch(FILE_LIST_RAM[index].file_name, wildcard) && FILE_LIST_RAM[index].flag_exist)
		{
			NVMSetFileInfo(index, file_information);
			/*the index of the file which was last found and the wildcard should be saved in order to use
			 * it later in the find next function */
			LastFound = index;
			strcpy(WILDCARDS,wildcard);
			return 0;
		}

	}
	ERROR_CODE = ERR_NOTEXISTS;
	return (-1);
#endif

} /* end of FDI_RAM_findfirst */


/****************************************************************************************
 * Function 	: FDI_RAM_findnext													   *
 ****************************************************************************************
 *																						*
 * Description	: search for the next file which is compatible to the widcard and copy. *
 *				  its information to the file_information pointer.						*
 *				  this function must come after using the findfilrst function and it	*
 *				  uses the data which was sent to the find first.						*
 *																						*
 * Input:		  wildcard - the file name while asterisk can be any combination of 	*
 *				  characters and ? can be any single character. 						*
 *																						*
 * Return:		  the file information is set to the file found information 			*
 *				  the function returns 0 if success -1 if it didn't find any file match *
 ****************************************************************************************/
int NVM_RAM_findnext(FILE_INFO *file_information)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 index;
	DbgPrintf("NVM_RAM_findnext\r\n");
	for (index = (LastFound+1); index < MAX_NUM_OF_FILES; index ++)
	{
		if (NVMFileNameMatch(FILE_LIST_RAM[index].file_name, WILDCARDS) && FILE_LIST_RAM[index].flag_exist)
		{
			NVMSetFileInfo(index, file_information);
			LastFound = index;
			return 0;
		}

	}
	return (-1);
#endif
} /* end of FDI_RAM_findnext */


/****************************************************************************************
 * Function 	: FDI_RAM_remove														*
 ****************************************************************************************
 *																						*
 * Description	: remove the file which is compatible to the file name from the existing*
 *				  files list															*
 *																						*
 * Input:		  name - name of the file to be removed.								*
 *																						*
 * Return:		  0 if success -1 on error												*
 *																						*
 ****************************************************************************************/
int NVM_RAM_remove(const char *name)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 index = 0;
	OSA_STATUS			OSAStatus;

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);

	for (index = 1; index < MAX_NUM_OF_FILES; index ++)
	{
		if (FILE_LIST_RAM[index].flag_exist && (strcmp(FILE_LIST_RAM[index].file_name, name) == 0))
		{
			FILE_LIST_RAM[index].flag_exist = 0;
#if 0
			OsaMemFree(FILE_LIST_RAM[index].file_name);
#else
			free(FILE_LIST_RAM[index].file_name);
#endif

			FILE_LIST_RAM[index].file_name = NULL;

			break;
		}
	}
	if (index == MAX_NUM_OF_FILES)
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;
		return -1;
	}

	else
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		return 0;
	}

	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);
	//return 0;
#endif

} /* end of FDI_RAM_remove */


/****************************************************************************************
 * Function 	: FDI_RAM_rename														 *
 ****************************************************************************************
 *																						*
 * Description	: changes the file name to the new_name 								*
 *																						*
 * Input:		  name - name of the file, new_name - the new name of the file			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
int NVM_RAM_rename(const char *name, const char *new_name)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 index;
	OSA_STATUS			OSAStatus;
	UINT32 res, size;

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);


	for (index = 1; index < MAX_NUM_OF_FILES; index ++)
	{
		res = strcmp(FILE_LIST_RAM[index].file_name, name);
		if (res == 0)
		{
#if 0
			OsaMemFree(FILE_LIST_RAM[index].file_name);
#else
			free(FILE_LIST_RAM[index].file_name);
#endif
			size = strlen (new_name);
#if 0
			FILE_LIST_RAM[index].file_name = OsaMemAlloc(OsaRef_MMI_NV,strlen (new_name) + 1);
#else
			FILE_LIST_RAM[index].file_name = malloc(strlen (new_name) + 1);
#endif
			ASSERT(FILE_LIST_RAM[index].file_name != NULL);
			strcpy (FILE_LIST_RAM[index].file_name, new_name);
			FILE_LIST_RAM[index].file_name[size] = '\0';
			break;
		}
	}

	if (index == MAX_NUM_OF_FILES)
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;
		return 1;
	}
	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);
	ERROR_CODE = ERR_NONE;
	return 0;
#endif

} /* end of FDI_RAM_rename */


/****************************************************************************************
 * Function 	: FDI_RAM_fileinit														*
 ****************************************************************************************
 *																						*
 * Description	: init the lists of files (existing files list and open files list		*
 *				  and create semaphore
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
int NVM_RAM_fileinit()
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);

	NVM_RAM_initLists();
	
	uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
	NVM_RAM_SemaphoreInit();
	
	uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
	return 0;
#endif


} /* FDI_RAM_fileinit */

 /****************************************************************************************
 * Function 	: FDI_RAM_feof														   *
 ****************************************************************************************
 *																						*
 * Description	: in the fdi ram version, doesn't do anything...						*
 *																						*
 * Input:																				*
 *																						*
 * Return:																				*
 *																						*
 ****************************************************************************************/
int NVM_RAM_feof(NVMCFILE_ID file_id)
{
	//return 0;

	
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	return (FILE_OPEN_LIST_RAM[file_id].data >= FILE_LIST_RAM[FILE_OPEN_LIST_RAM[file_id].index_list].file_end ? TRUE : FALSE);
#endif
	
	
} /* FDI_RAM_feof */


/****************************************************************************************
 * Function 	: FDI_RAM_ferror														 *
 ****************************************************************************************
 *																						*
 * Description	: return the error code of the fdi										*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
ERR_CODE NVM_RAM_ferror(NVMCFILE_ID file_id)
{
#ifndef CRANE_MCU_DONGLE	//remove code for optimizing psram for DM,19.06.17
	return (ERROR_CODE);
#endif
} /* end of FDI_RAM_ferror */
/****************************************************************************************
 * Function 	: FDI_RAM_clearerr														 *
 ****************************************************************************************
 *																						*
 * Description	: set the error code to err_none										*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
void NVM_RAM_clearerr(NVMCFILE_ID file_id)
{
#ifndef CRANE_MCU_DONGLE	//remove code for optimizing psram for DM,19.06.17
	ERROR_CODE = ERR_NONE;
#endif
} /* end of FDI_RAM_clearerr */
/****************************************************************************************
 * Function 	: FDI_RAM_chmod 														*
 ****************************************************************************************
 *																						*
 * Description	: change the access mode of the file to the new mode given as input 	*
 *				  - only not open file													*
 *																						*
 * Input:		  file_name - the file name which we want to change its mode.			*
 *																						*
 * Return:		  -1 on error, 0 on success.											*
 *																						*
 ****************************************************************************************/
int NVM_RAM_chmod(const char *file_name, int mode)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 index;
	OSA_STATUS			OSAStatus;

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);


	for (index = 1; index < MAX_NUM_OF_FILES; index ++)
	{
		if (strcmp(FILE_LIST_RAM[index].file_name, file_name) == 0)
		break;
	}

	/*Fixed coverity[overrun-local]*/
	#if 0
	if (index > MAX_NUM_OF_FILES || FILE_LIST_RAM[index].flag_exist == 0)
	#else
	if (index >= MAX_NUM_OF_FILES || FILE_LIST_RAM[index].flag_exist == 0)
	#endif
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;

		return (-1);
	}

	else
		FILE_LIST_RAM[index].access_mode = mode;

	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);
	return 0;
#endif

} /* end of FDI_RAM_chmod */
/****************************************************************************************
 * Function 	: FDI_RAM_stat														   *
 ****************************************************************************************
 *																						*
 * Description	: return the access mode of an unopen file. 							*
 *																						*
 * Input:		  file name which we want to know, pointer to char string in which the	*
 *				  function will return the mode.										*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
int NVM_RAM_stat(const char *file_name, int *mode)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 index;
	OSA_STATUS			OSAStatus;

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);

	for (index = 1; index < MAX_NUM_OF_FILES; index ++)
	{
		if (strcmp(FILE_LIST_RAM[index].file_name, file_name) == 0)
		break;
	}

	/*Fixed coverity[overrun-local]*/
	#if 0
	if (index > MAX_NUM_OF_FILES || FILE_LIST_RAM[index].flag_exist == 0)
	#else
	if (index >= MAX_NUM_OF_FILES || FILE_LIST_RAM[index].flag_exist == 0)
	#endif
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;
		return (-1) ;
	}
	else
	{
		*mode =  FILE_LIST_RAM[index].access_mode;
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		ERROR_CODE = ERR_NONE;
		return 0;
	}
	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);
#endif

} /* end of FDI_RAM_stat */
/****************************************************************************************
 * Function 	: FDI_RAM_fchmod														 *
 ****************************************************************************************
 *																						*
 * Description	: changes the access mode of an open file to the new mode given as input*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
int NVM_RAM_fchmod(NVMCFILE_ID index1 , int mode)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	DWORD index = (DWORD) index1;
	OSA_STATUS			OSAStatus;

	//OSAStatus = OSASemaphoreAcquire(_SemaRef_RAM,OSA_NO_SUSPEND);
	//ASSERT (OSAStatus == OS_SUCCESS);


	/*Fixed coverity[overrun-local]*/
	#if 0
	if (index > MAX_NUM_OF_FILES || FILE_LIST_RAM[index].flag_exist == 0)
	#else
	if (index >= MAX_NUM_OF_FILES || FILE_LIST_RAM[index].flag_exist == 0)
	#endif
	{
		//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
		//ASSERT(OSAStatus == OS_SUCCESS);
		ERROR_CODE = ERR_NOTEXISTS;
		return (-1) ;
	}

		else
	{
		FILE_OPEN_LIST_RAM[index].access_mode = mode;
		FILE_LIST_RAM[FILE_OPEN_LIST_RAM[index].index_list].access_mode = mode;
	}

	FILE_OPEN_LIST_RAM[index].access_mode = mode;
	FILE_LIST_RAM[FILE_OPEN_LIST_RAM[index].index_list].access_mode = mode;
	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);
	ERROR_CODE = ERR_NONE;
	return 0;
#endif

} /* end of FDI_RAM_fchmod */
/****************************************************************************************
 * Function 	: FDI_RAM_fstat 														*
 ****************************************************************************************
 *																						*
 * Description	: return the access mode of an open file.								*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
int NVM_RAM_fstat(NVMCFILE_ID index1 , int *mode)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	DWORD index = (DWORD) index1;
	OSA_STATUS			OSAStatus;

	if (index >= MAX_NUM_OF_FILES || FILE_LIST_RAM[index].flag_exist == 0)
	{

		ERROR_CODE = ERR_NOTEXISTS;
		return (-1);
	}
	else
	{

		ERROR_CODE = ERR_NONE;
		return FILE_OPEN_LIST_RAM[index].access_mode;
	}
#endif

} /* end of FDI_RAM_fstat */
/****************************************************************************************
 * Function 	: FDI_RAM_ftell 														*
 ****************************************************************************************
 *																						*
 * Description	: tells the offset of the data pointer from the file start in bytes 	*
 *																						*
 * Input:		  the file id of an open file											*
 *																						*
 * Return:		  the offset of the data from the file start							*
 *																						*
 ****************************************************************************************/
long int NVM_RAM_ftell(NVMCFILE_ID index1)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	DWORD index = (DWORD) index1;
	OSA_STATUS			OSAStatus;




	if (FILE_OPEN_LIST_RAM[index].flag_exist == 0)
	{
		ERROR_CODE = ERR_NOTEXISTS;
		return (-1);
	}
	else
	{
		ERROR_CODE = ERR_NONE;

		return
			(FILE_OPEN_LIST_RAM[index].data -
			FILE_LIST_RAM[FILE_OPEN_LIST_RAM[index].index_list].file_start);
	}
	//OSAStatus = OSASemaphoreRelease(_SemaRef_RAM);
	//ASSERT(OSAStatus == OS_SUCCESS);
#endif

} /* end of FDI_RAM_ftell */
/****************************************************************************************
 * Function 	: FDI_RAM_fileterminate 														*
 ****************************************************************************************
 *																						*
 * Description	: open fdi file - if the file is not already exist it will be created.	*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
int NVM_RAM_fileterminate(void)
{
	 return 0;
} /* end of FDI_RAM_fileterminate */

char* strdup(const char* str)
{
	char* dst;

	dst = (char*) malloc_ex (strlen(str) + 1,__return_address());
	if (dst != NULL)
	{
		strcpy(dst, str);
	}

	return dst;
}

/****************************************************************************************
 * Function 	: FDI_RAM_initLists 														*
 ****************************************************************************************
 *																						*
 * Description	: initialize the two lists- the existing files list and the open files	*
 *				  list. 																*
 * Input:		  none																	*
 *																						*
 * Return:		  none																	*
 *																						*
 ****************************************************************************************/
void NVM_RAM_initLists(void)
{
#ifndef CRANE_MCU_DONGLE	//remove  code for optimizing psram for DM,19.06.17
	UINT32 i;


	  /*existing files list */
	for (i=0; i<MAX_NUM_OF_FILES; i++)
	{
		FILE_LIST_RAM[i].file_name = NULL;
		FILE_LIST_RAM[i].flag_exist = 0;
		FILE_LIST_RAM[i].file_start = NULL;
		FILE_LIST_RAM[i].file_start = FILE_LIST_RAM[i].file_end = NULL;
		FILE_LIST_RAM[i].size = 0;
	}
	  /*open files list */
	for (i = 0; i<MAX_NUM_OF_FILES ; i++)
	{
		FILE_OPEN_LIST_RAM[i].data = NULL;
		FILE_OPEN_LIST_RAM[i].file_name = NULL;
		FILE_OPEN_LIST_RAM[i].index_list = 0;
		FILE_OPEN_LIST_RAM[i].mode = NULL;
	}

#endif

} /* end of FDI_RAM_initLists */


/****************************************************************************************
 * Function 	: SetFileInfo														  *
 ****************************************************************************************
 *																						*
 * Description	: open fdi file - if the file is not already exist it will be created.	*
 *																						*
 * Input:		  name - name of the file, mode - the mode of the open file 			*
 *																						*
 * Return:		  the file-id (index of the file in the array)							*
 *																						*
 ****************************************************************************************/
void NVMSetFileInfo(UINT32 index, FILE_INFO *file_information)
{
#ifndef CRANE_MCU_DONGLE	//remove code for optimizing psram for DM,19.06.17
	if(!PLATFORM_IS_FPGA)//Z2 HAPS has no PMIC
	{
		unsigned long clock;
		clock=Fatsys_time(NULL);
	
		FILE_LIST_RAM[index].time=FDI5to6_cTime2Time(clock);
		FILE_LIST_RAM[index].date=FDI5to6_cTime2Date(clock);
	}
	
	 file_information->data_id = index;
	file_information->date = FILE_LIST_RAM[index].date;
	file_information->time = FILE_LIST_RAM[index].time;
	strcpy (file_information->file_name, FILE_LIST_RAM[index].file_name);
	file_information->size = FILE_LIST_RAM[index].size;
	file_information->permissions = FILE_LIST_RAM[index].access_mode;
	ERROR_CODE = ERR_NONE;
#endif

} /* end of SetFileInfo */


/*############################################################################
 *### FileNameMatch
 *###
 *### DESCRIPTION:
 *###		   Checks to see if filenames match, including wildcards
 *### USAGE:
 *###	 status = FileNameMatch("filetemp.ext", "*.ex?");
 *###
 *### PARAMETERS:
 *###
 *### INPUTS:
 *###  src		   const character string for source file name to check
 *###  dest 	   const character string to compare src against
 *###
 *### OUTPUTS:
 *###
 *### RETURNS:
 *###  Returns TRUE if files match or FALSE if files do not match
 *###*/

int
NVMFileNameMatch(const char *src, const char *dest)
{
   const FDI_TCHAR *src_ptr  = src;   /* pointer to parse src string */
   const FDI_TCHAR *dest_ptr = dest;  /* pointer to parse dest string */

   do	/* loop through dest */
   {
	  /* check for multiple character wildcard */
	  if (*dest_ptr == FILE_MCWILDCARD)
	  {
		 /* skip by multiple character and
			repeated multiple character wildcards */
		 while (*dest_ptr == FILE_MCWILDCARD)
		 {
			dest_ptr++;
		 }

		 /* if at the end of dest then must be a match */
		 if (*dest_ptr == FILE_EOS)
		 {
			return TRUE;
		 }

		 /* parse through src looking for match */
		 while (*src_ptr != FILE_EOS)
		 {
			if (NVMFileNameMatch(src_ptr, dest_ptr) == TRUE)
			{
			   return TRUE;
			}
			/* not a match, try next character in src */
			src_ptr++;
		 }

		 /* no match after multiple character wildcard found */
		 return FALSE;
	  }
	  else	/* not multiple character wildcard */
	  {
		 /* check if character matches */
		 if ((*dest_ptr != *src_ptr) && (*dest_ptr != FILE_SCWILDCARD))
		 {
			return FALSE;
		 }

		 /* make sure if the single character wildcard
			that src isn't at end */
		 if ((*dest_ptr == FILE_SCWILDCARD) && (*src_ptr == FILE_EOS))
		 {
			return FALSE;
		 }

		 /* on to next character... */
		 src_ptr++;
		 dest_ptr++;
	  }

   } while (*dest_ptr != FILE_EOS);

   /* make sure parsed all of src */
   return (*src_ptr == FILE_EOS);
}									   /* ENDOF FileNameMatch */


/*############################################################################
 *### FileGetTime
 *###
 *### DESCRIPTION:
 *###  This is a default function.	An OS specific function should replace
 *###  this in the #define in the header file.
 *###
 *### USAGE:
 *###
 *### PARAMETERS:
 *###
 *### INPUTS:
 *###
 *### OUTPUTS:
 *###
 *### RETURNS:
 *###*/
static int
NVMFileGetTime(void)
{
#ifndef CRANE_MCU_DONGLE	//remove code for optimizing psram for DM,19.06.17
#if !defined(UPGRADE_MANITOBA_PLATFORM) && !defined(_HERMON_)
	static DWORD value = 0x55555555;

	value = ~value;

#else
	RTC_CalendarTime calendarTime;
	RTC_ReturnCode retCode;
	static DWORD value;
	retCode = RTCCurrentDateAndTimeGet	(&calendarTime);

	value = calendarTime.hour<<(6+6);
	value += calendarTime.minute<<6;
	value += calendarTime.second;
#endif
   return (int)value;
#endif
}

/*############################################################################
 *### FileGetDate
 *###
 *### DESCRIPTION:
 *###  This is a default function.	An OS specific function should replace
 *###  this in the #define in the header file.
 *###
 *### USAGE:
 *###
 *### PARAMETERS:
 *###
 *### INPUTS:
 *###
 *### OUTPUTS:
 *###
 *### RETURNS:
 *###*/
static int
NVMFileGetDate(void)
{
#ifndef CRANE_MCU_DONGLE	//remove code for optimizing psram for DM,19.06.17
#if !defined(UPGRADE_MANITOBA_PLATFORM) && !defined(_HERMON_)
  static DWORD value = 0xaaaaaaaa;

   value = ~value;
#else
   RTC_CalendarTime calendarTime;
   RTC_ReturnCode retCode;
   static DWORD value;
   retCode = RTCCurrentDateAndTimeGet  (&calendarTime);

   value = calendarTime.day<<(12+4);
   value += calendarTime.month<<12;
   value += calendarTime.year;
#endif
   return (int)value;
#endif
}

void NVM_RAM_SemaphoreInit()
{
#ifndef CRANE_MCU_DONGLE	//remove code for optimizing psram for DM,19.06.17
	OSA_STATUS			OSAStatus;

	OSAStatus = OSASemaphoreCreate(&_SemaRef_RAM, 1, OSA_PRIORITY);
	ASSERT(OSAStatus == OS_SUCCESS);
#endif
}

#endif
//end of PHS_SW_DEMO_TTC defined



/******************************************************************************
* Function     :   NVMCGPCChannelConfig
 * *******************************************************************************
*
* Description  :  configure NVM over GPC channel
*
* Parameters   :   none

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
                    NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static 	void NVMCGPCChannelConfig(void)
{

}

/******************************************************************************
* Function     :   NVMCOpenGPCChannel
 * *******************************************************************************
*
* Description  :  Regiter to GPC Services and connect GPC
*
* Parameters   :

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
                    NVM_RC_ERROR,
*
* Notes:
******************************************************************************/
static NVMC_ReturnCodeE NVMCOpenGPCChannel( void)
{

	return(  NVM_RC_OK);
}



/******************************************************************************
* Function     :   NVMCGPCTransmit
 * *******************************************************************************
*
* Description  :  Transmits an MVM buffer over GPC
*
* Parameters   :   txBuf - buffer to transmit
*                  bufLen - length of Transmit buffer
*                  nvmCookie- cookie that will be returned when buffer was transmited (for checking )

* Output Param :   none
*
* Return value :    NVM_RC_OK ,
                    NVM_RC_ERROR,
*
* Notes:
******************************************************************************/

 static NVMC_ReturnCodeE NVMCGPCTransmit(UINT8 *txBuf ,UINT32 bufLen ,UINT32 nvmCookie)
 {

 	return(NVM_RC_OK);

 }


NVMC_ReturnCodeE   NVMCFileOpen_ram(const char *fileOpenName,const char *attMode,NVMCFILE_ID *fileID)
{

	NVMC_ReturnCodeE			   nvmRCStatus = NVM_RC_OK;
	DIAG_FILTER(NVM, FILEOPEN, NAME, DIAG_INFORMATION)
	diagPrintf("NVMCFileOpen file name is %s", fileOpenName);


	*fileID=NVM_RAM_fopen(fileOpenName,(char *)attMode);

	if(*fileID==0)
		nvmRCStatus = NVM_RC_ERROR;

	DIAG_FILTER(NVM, FILEOPEN2, NAME, DIAG_INFORMATION)
	diagPrintf(" %s has been opened", fileOpenName);
	return(nvmRCStatus);


}

NVMC_ReturnCodeE  NVMCFileClose_ram(NVMCFILE_ID fileID)
{

	NVMC_ReturnCodeE			   nvmRCStatus = NVM_RC_OK;	
	nvmRCStatus=(NVMC_ReturnCodeE)NVM_RAM_fclose(fileID);
	return(nvmRCStatus);

}


NVMC_ReturnCodeE  NVMCFileRead_ram(void *filePtr, UINT32 elementSize,UINT32 element2Read, UINT32 *actualElementsRead ,NVMCFILE_ID fileID)
{


	NVMC_ReturnCodeE			   nvmRCStatus = NVM_RC_OK;
	*actualElementsRead=NVM_RAM_fread(filePtr, (size_t)elementSize, (size_t) element2Read,	fileID);
	return(nvmRCStatus);

}

NVMC_ReturnCodeE  NVMCFileWrite_ram(void *filePtr, UINT32 elementSize,UINT32 element2Write, UINT32 *actualWrite ,NVMCFILE_ID fileID)
{

	NVMC_ReturnCodeE		nvmRCStatus = NVM_RC_OK;
  	*actualWrite=NVM_RAM_fwrite((const void *)filePtr, (size_t) elementSize, (size_t) element2Write,  fileID);
  	return(nvmRCStatus);

}


NVMC_ReturnCodeE  NVMCFileSeek_ram(long offset, int whereFrom ,NVMCFILE_ID fileID)
{


	NVMC_ReturnCodeE	   nvmRCStatus = NVM_RC_OK;


	nvmRCStatus=(NVMC_ReturnCodeE)NVM_RAM_fseek(fileID, offset, whereFrom);
	return(nvmRCStatus);


}

unsigned int  NVMCFileSize_ram(NVMCFILE_ID fileID)
{





	return NVM_RAM_fsize(fileID);



	}

NVMC_ReturnCodeE  NVMCFileRemove_ram(const char *fileName)
{

	NVMC_ReturnCodeE	  nvmRCStatus = NVM_RC_OK;
	nvmRCStatus=(NVMC_ReturnCodeE)NVM_RAM_remove(fileName);
	return(nvmRCStatus);

  
}

NVMC_ReturnCodeE  NVMCFileFindFirst_ram(const char *fileName, NVMCFileInfoStruct *fileInfo)
{

	NVMC_ReturnCodeE	  nvmRCStatus = NVM_RC_OK;
	nvmRCStatus=(NVMC_ReturnCodeE)NVM_RAM_findfirst(fileName , (FILE_INFO *)fileInfo);
	return(nvmRCStatus);

}

/******************************************************************************
* Function     :   NVMCFileFindNext
 * *******************************************************************************
* Description  : fetches subsequent files that match the
 *###    file name given in NVMCFileFindFirst function.  The parameter
 *###    fileinfo_ptr is a pointer to the type FILE_INFO which is filled with
 *###    the file information.


*  Parameters   :   none

* Output       :           file info -   pointer to the type FILE_INFO which is
                                   filled with the file information.
* Return value :    None.
*
******************************************************************************/
 NVMC_ReturnCodeE  NVMCFileFindNext_ram( NVMCFileInfoStruct *fileInfo)
{


	NVMC_ReturnCodeE	  nvmRCStatus = NVM_RC_OK;
	nvmRCStatus=(NVMC_ReturnCodeE) NVM_RAM_findnext((FILE_INFO *)fileInfo);
	return(nvmRCStatus);


}

int NVMCFileEof_ram(NVMCFILE_ID fileID)
{

	return NVM_RAM_feof(fileID);


}



/******************************************************************************
* Function     :  NVMPhase1Init
*******************************************************************************
*
* Description  :   Initializes NVM CLIENT internal variables
*
* Parameters   :   None.
*
* Output Param :   None.
*
* Return value :   None.
*
* Notes        :
******************************************************************************/

void NVMCPhase1Init_ram( void )
{
}

/******************************************************************************
* Function     :  NVMPhase2Init
*******************************************************************************
*
* Description  :   Phase 2 initialization for NVM Client
*
* Parameters   :   None.
*
* Output Param :   None.
*
*Return Value  :   NVM_RC_OK ,
                   NVM_RC_ERROR,
* Return value :
*
* Notes        :  Should be called during intialization stage AFTER GPC modoule was intialized
******************************************************************************/
void  NVMCPhase2Init_ram(void)
{
}




#pragma arm section rwdata="NVMUPDATE"
__align(32)
//UINT8 nvm_update_buffer[0x20000] = {0xFF,0xFF,0xFF,0xFF}; 
UINT8 mep_update_buffer[4] = {0xFF,0xFF,0xFF,0xFF};
#pragma arm section rwdata



#endif//NVM_INCLUDE
