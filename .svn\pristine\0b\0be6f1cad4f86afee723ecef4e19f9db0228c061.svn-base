/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_GNSS_RealTimeIntegrityReq_H_
#define	_GNSS_RealTimeIntegrityReq_H_


#include <asn_application.h>

/* Including external dependencies */
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* GNSS-RealTimeIntegrityReq */
typedef struct GNSS_RealTimeIntegrityReq {
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GNSS_RealTimeIntegrityReq_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GNSS_RealTimeIntegrityReq;

#ifdef __cplusplus
}
#endif

#endif	/* _GNSS_RealTimeIntegrityReq_H_ */
#include <asn_internal.h>
