/* This file is auto-generated by opth-gen.awk.  */

#ifndef OPTIONS_H
#define OPTIONS_H

#include "flag-types.h"

#include "config/arm/arm-opts.h"

#if !defined(IN_LIBGCC2) && !defined(IN_TARGET_LIBS) && !defined(IN_RTS)
#ifndef GENERATOR_FILE
#if !defined(IN_LIBGCC2) && !defined(IN_TARGET_LIBS)
struct GTY(()) gcc_options
#else
struct gcc_options
#endif
{
#endif
#ifdef GENERATOR_FILE
extern HOST_WIDE_INT frame_larger_than_size;
#else
  HOST_WIDE_INT x_frame_larger_than_size;
#define frame_larger_than_size global_options.x_frame_larger_than_size
#endif
#ifdef GENERATOR_FILE
extern HOST_WIDE_INT function_entry_patch_area_size;
#else
  HOST_WIDE_INT x_function_entry_patch_area_size;
#define function_entry_patch_area_size global_options.x_function_entry_patch_area_size
#endif
#ifdef GENERATOR_FILE
extern HOST_WIDE_INT function_entry_patch_area_start;
#else
  HOST_WIDE_INT x_function_entry_patch_area_start;
#define function_entry_patch_area_start global_options.x_function_entry_patch_area_start
#endif
#ifdef GENERATOR_FILE
extern HOST_WIDE_INT larger_than_size;
#else
  HOST_WIDE_INT x_larger_than_size;
#define larger_than_size global_options.x_larger_than_size
#endif
#ifdef GENERATOR_FILE
extern bool dump_base_name_prefixed;
#else
  bool x_dump_base_name_prefixed;
#define dump_base_name_prefixed global_options.x_dump_base_name_prefixed
#endif
#ifdef GENERATOR_FILE
extern bool exit_after_options;
#else
  bool x_exit_after_options;
#define exit_after_options global_options.x_exit_after_options
#endif
#ifdef GENERATOR_FILE
extern bool flag_disable_hsa;
#else
  bool x_flag_disable_hsa;
#define flag_disable_hsa global_options.x_flag_disable_hsa
#endif
#ifdef GENERATOR_FILE
extern bool flag_dump_all_passed;
#else
  bool x_flag_dump_all_passed;
#define flag_dump_all_passed global_options.x_flag_dump_all_passed
#endif
#ifdef GENERATOR_FILE
extern bool flag_opts_finished;
#else
  bool x_flag_opts_finished;
#define flag_opts_finished global_options.x_flag_opts_finished
#endif
#ifdef GENERATOR_FILE
extern bool flag_stack_usage_info;
#else
  bool x_flag_stack_usage_info;
#define flag_stack_usage_info global_options.x_flag_stack_usage_info
#endif
#ifdef GENERATOR_FILE
extern bool flag_warn_unused_result;
#else
  bool x_flag_warn_unused_result;
#define flag_warn_unused_result global_options.x_flag_warn_unused_result
#endif
#ifdef GENERATOR_FILE
extern bool in_lto_p;
#else
  bool x_in_lto_p;
#define in_lto_p global_options.x_in_lto_p
#endif
#ifdef GENERATOR_FILE
extern bool use_gnu_debug_info_extensions;
#else
  bool x_use_gnu_debug_info_extensions;
#define use_gnu_debug_info_extensions global_options.x_use_gnu_debug_info_extensions
#endif
#ifdef GENERATOR_FILE
extern bool warn_frame_larger_than;
#else
  bool x_warn_frame_larger_than;
#define warn_frame_larger_than global_options.x_warn_frame_larger_than
#endif
#ifdef GENERATOR_FILE
extern bool warn_larger_than;
#else
  bool x_warn_larger_than;
#define warn_larger_than global_options.x_warn_larger_than
#endif
#ifdef GENERATOR_FILE
extern char *help_enum_printed;
#else
  char * x_help_enum_printed;
#define help_enum_printed global_options.x_help_enum_printed
#endif
#ifdef GENERATOR_FILE
extern char *help_printed;
#else
  char * x_help_printed;
#define help_printed global_options.x_help_printed
#endif
#ifdef GENERATOR_FILE
extern const char *main_input_basename;
#else
  const char * x_main_input_basename;
#define main_input_basename global_options.x_main_input_basename
#endif
#ifdef GENERATOR_FILE
extern const char *main_input_filename;
#else
  const char * x_main_input_filename;
#define main_input_filename global_options.x_main_input_filename
#endif
#ifdef GENERATOR_FILE
extern enum debug_info_levels debug_info_level;
#else
  enum debug_info_levels x_debug_info_level;
#define debug_info_level global_options.x_debug_info_level
#endif
#ifdef GENERATOR_FILE
extern enum debug_info_type write_symbols;
#else
  enum debug_info_type x_write_symbols;
#define write_symbols global_options.x_write_symbols
#endif
#ifdef GENERATOR_FILE
extern enum debug_struct_file debug_struct_generic[DINFO_USAGE_NUM_ENUMS];
#else
  enum debug_struct_file x_debug_struct_generic[DINFO_USAGE_NUM_ENUMS];
#define debug_struct_generic global_options.x_debug_struct_generic
#endif
#ifdef GENERATOR_FILE
extern enum debug_struct_file debug_struct_ordinary[DINFO_USAGE_NUM_ENUMS];
#else
  enum debug_struct_file x_debug_struct_ordinary[DINFO_USAGE_NUM_ENUMS];
#define debug_struct_ordinary global_options.x_debug_struct_ordinary
#endif
#ifdef GENERATOR_FILE
extern enum dwarf_gnat_encodings gnat_encodings;
#else
  enum dwarf_gnat_encodings x_gnat_encodings;
#define gnat_encodings global_options.x_gnat_encodings
#endif
#ifdef GENERATOR_FILE
extern enum stack_check_type flag_stack_check;
#else
  enum stack_check_type x_flag_stack_check;
#define flag_stack_check global_options.x_flag_stack_check
#endif
#ifdef GENERATOR_FILE
extern int *param_values;
#else
  int * x_param_values;
#define param_values global_options.x_param_values
#endif
#ifdef GENERATOR_FILE
extern int flag_complex_method;
#else
  int x_flag_complex_method;
#define flag_complex_method global_options.x_flag_complex_method
#endif
#ifdef GENERATOR_FILE
extern int flag_debug_asm;
#else
  int x_flag_debug_asm;
#define flag_debug_asm global_options.x_flag_debug_asm
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_rtl_in_asm;
#else
  int x_flag_dump_rtl_in_asm;
#define flag_dump_rtl_in_asm global_options.x_flag_dump_rtl_in_asm
#endif
#ifdef GENERATOR_FILE
extern int flag_gen_aux_info;
#else
  int x_flag_gen_aux_info;
#define flag_gen_aux_info global_options.x_flag_gen_aux_info
#endif
#ifdef GENERATOR_FILE
extern int flag_generate_lto;
#else
  int x_flag_generate_lto;
#define flag_generate_lto global_options.x_flag_generate_lto
#endif
#ifdef GENERATOR_FILE
extern int flag_generate_offload;
#else
  int x_flag_generate_offload;
#define flag_generate_offload global_options.x_flag_generate_offload
#endif
#ifdef GENERATOR_FILE
extern int flag_incremental_link;
#else
  int x_flag_incremental_link;
#define flag_incremental_link global_options.x_flag_incremental_link
#endif
#ifdef GENERATOR_FILE
extern int flag_print_asm_name;
#else
  int x_flag_print_asm_name;
#define flag_print_asm_name global_options.x_flag_print_asm_name
#endif
#ifdef GENERATOR_FILE
extern int flag_shlib;
#else
  int x_flag_shlib;
#define flag_shlib global_options.x_flag_shlib
#endif
#ifdef GENERATOR_FILE
extern int main_input_baselength;
#else
  int x_main_input_baselength;
#define main_input_baselength global_options.x_main_input_baselength
#endif
#ifdef GENERATOR_FILE
extern int optimize;
#else
  int x_optimize;
#define optimize global_options.x_optimize
#endif
#ifdef GENERATOR_FILE
extern int optimize_debug;
#else
  int x_optimize_debug;
#define optimize_debug global_options.x_optimize_debug
#endif
#ifdef GENERATOR_FILE
extern int optimize_fast;
#else
  int x_optimize_fast;
#define optimize_fast global_options.x_optimize_fast
#endif
#ifdef GENERATOR_FILE
extern int optimize_size;
#else
  int x_optimize_size;
#define optimize_size global_options.x_optimize_size
#endif
#ifdef GENERATOR_FILE
extern int rtl_dump_and_exit;
#else
  int x_rtl_dump_and_exit;
#define rtl_dump_and_exit global_options.x_rtl_dump_and_exit
#endif
#ifdef GENERATOR_FILE
extern int target_flags;
#else
  int x_target_flags;
#define target_flags global_options.x_target_flags
#endif
#ifdef GENERATOR_FILE
extern unsigned int flag_sanitize;
#else
  unsigned int x_flag_sanitize;
#define flag_sanitize global_options.x_flag_sanitize
#endif
#ifdef GENERATOR_FILE
extern unsigned int flag_sanitize_coverage;
#else
  unsigned int x_flag_sanitize_coverage;
#define flag_sanitize_coverage global_options.x_flag_sanitize_coverage
#endif
#ifdef GENERATOR_FILE
extern unsigned int flag_sanitize_recover;
#else
  unsigned int x_flag_sanitize_recover;
#define flag_sanitize_recover global_options.x_flag_sanitize_recover
#endif
#ifdef GENERATOR_FILE
extern unsigned int help_columns;
#else
  unsigned int x_help_columns;
#define help_columns global_options.x_help_columns
#endif
#ifdef GENERATOR_FILE
extern unsigned int initial_max_fld_align;
#else
  unsigned int x_initial_max_fld_align;
#define initial_max_fld_align global_options.x_initial_max_fld_align
#endif
#ifdef GENERATOR_FILE
extern void *flag_instrument_functions_exclude_files;
#else
  void * x_flag_instrument_functions_exclude_files;
#define flag_instrument_functions_exclude_files global_options.x_flag_instrument_functions_exclude_files
#endif
#ifdef GENERATOR_FILE
extern void *flag_instrument_functions_exclude_functions;
#else
  void * x_flag_instrument_functions_exclude_functions;
#define flag_instrument_functions_exclude_functions global_options.x_flag_instrument_functions_exclude_functions
#endif
#ifdef GENERATOR_FILE
extern int help_flag;
#else
  int x_help_flag;
#define help_flag global_options.x_help_flag
#endif
#ifdef GENERATOR_FILE
extern int no_sysroot_suffix;
#else
  int x_no_sysroot_suffix;
#define no_sysroot_suffix global_options.x_no_sysroot_suffix
#endif
#ifdef GENERATOR_FILE
extern int flag_preprocess_only;
#else
  int x_flag_preprocess_only;
#define flag_preprocess_only global_options.x_flag_preprocess_only
#endif
#ifdef GENERATOR_FILE
extern int warn_abi;
#else
  int x_warn_abi;
#define warn_abi global_options.x_warn_abi
#endif
#ifdef GENERATOR_FILE
extern int warn_abi_tag;
#else
  int x_warn_abi_tag;
#define warn_abi_tag global_options.x_warn_abi_tag
#endif
#ifdef GENERATOR_FILE
extern int warn_address;
#else
  int x_warn_address;
#define warn_address global_options.x_warn_address
#endif
#ifdef GENERATOR_FILE
extern int warn_aggregate_return;
#else
  int x_warn_aggregate_return;
#define warn_aggregate_return global_options.x_warn_aggregate_return
#endif
#ifdef GENERATOR_FILE
extern int warn_aggressive_loop_optimizations;
#else
  int x_warn_aggressive_loop_optimizations;
#define warn_aggressive_loop_optimizations global_options.x_warn_aggressive_loop_optimizations
#endif
#ifdef GENERATOR_FILE
extern int warn_aliasing;
#else
  int x_warn_aliasing;
#define warn_aliasing global_options.x_warn_aliasing
#endif
#ifdef GENERATOR_FILE
extern int warn_align_commons;
#else
  int x_warn_align_commons;
#define warn_align_commons global_options.x_warn_align_commons
#endif
#ifdef GENERATOR_FILE
extern int warn_aligned_new;
#else
  int x_warn_aligned_new;
#define warn_aligned_new global_options.x_warn_aligned_new
#endif
#ifdef GENERATOR_FILE
extern const char *warn_alloc_size_limit;
#else
  const char *x_warn_alloc_size_limit;
#define warn_alloc_size_limit global_options.x_warn_alloc_size_limit
#endif
#ifdef GENERATOR_FILE
extern int warn_alloc_zero;
#else
  int x_warn_alloc_zero;
#define warn_alloc_zero global_options.x_warn_alloc_zero
#endif
#ifdef GENERATOR_FILE
extern int warn_alloca;
#else
  int x_warn_alloca;
#define warn_alloca global_options.x_warn_alloca
#endif
#ifdef GENERATOR_FILE
extern int warn_alloca_limit;
#else
  int x_warn_alloca_limit;
#define warn_alloca_limit global_options.x_warn_alloca_limit
#endif
#ifdef GENERATOR_FILE
extern int warn_ampersand;
#else
  int x_warn_ampersand;
#define warn_ampersand global_options.x_warn_ampersand
#endif
#ifdef GENERATOR_FILE
extern int warn_argument_mismatch;
#else
  int x_warn_argument_mismatch;
#define warn_argument_mismatch global_options.x_warn_argument_mismatch
#endif
#ifdef GENERATOR_FILE
extern int warn_array_bounds;
#else
  int x_warn_array_bounds;
#define warn_array_bounds global_options.x_warn_array_bounds
#endif
#ifdef GENERATOR_FILE
extern int warn_array_temporaries;
#else
  int x_warn_array_temporaries;
#define warn_array_temporaries global_options.x_warn_array_temporaries
#endif
#ifdef GENERATOR_FILE
extern int warn_assign_intercept;
#else
  int x_warn_assign_intercept;
#define warn_assign_intercept global_options.x_warn_assign_intercept
#endif
#ifdef GENERATOR_FILE
extern int warn_attributes;
#else
  int x_warn_attributes;
#define warn_attributes global_options.x_warn_attributes
#endif
#ifdef GENERATOR_FILE
extern int warn_bad_function_cast;
#else
  int x_warn_bad_function_cast;
#define warn_bad_function_cast global_options.x_warn_bad_function_cast
#endif
#ifdef GENERATOR_FILE
extern int warn_bool_compare;
#else
  int x_warn_bool_compare;
#define warn_bool_compare global_options.x_warn_bool_compare
#endif
#ifdef GENERATOR_FILE
extern int warn_bool_op;
#else
  int x_warn_bool_op;
#define warn_bool_op global_options.x_warn_bool_op
#endif
#ifdef GENERATOR_FILE
extern int warn_builtin_declaraion_mismatch;
#else
  int x_warn_builtin_declaraion_mismatch;
#define warn_builtin_declaraion_mismatch global_options.x_warn_builtin_declaraion_mismatch
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_builtin_macro_redefined;
#else
  int x_cpp_warn_builtin_macro_redefined;
#define cpp_warn_builtin_macro_redefined global_options.x_cpp_warn_builtin_macro_redefined
#endif
#ifdef GENERATOR_FILE
extern int warn_cxx_compat;
#else
  int x_warn_cxx_compat;
#define warn_cxx_compat global_options.x_warn_cxx_compat
#endif
#ifdef GENERATOR_FILE
extern int warn_cxx11_compat;
#else
  int x_warn_cxx11_compat;
#define warn_cxx11_compat global_options.x_warn_cxx11_compat
#endif
#ifdef GENERATOR_FILE
extern int warn_cxx14_compat;
#else
  int x_warn_cxx14_compat;
#define warn_cxx14_compat global_options.x_warn_cxx14_compat
#endif
#ifdef GENERATOR_FILE
extern int warn_cxx17_compat;
#else
  int x_warn_cxx17_compat;
#define warn_cxx17_compat global_options.x_warn_cxx17_compat
#endif
#ifdef GENERATOR_FILE
extern int warn_c_binding_type;
#else
  int x_warn_c_binding_type;
#define warn_c_binding_type global_options.x_warn_c_binding_type
#endif
#ifdef GENERATOR_FILE
extern int warn_c90_c99_compat;
#else
  int x_warn_c90_c99_compat;
#define warn_c90_c99_compat global_options.x_warn_c90_c99_compat
#endif
#ifdef GENERATOR_FILE
extern int warn_c99_c11_compat;
#else
  int x_warn_c99_c11_compat;
#define warn_c99_c11_compat global_options.x_warn_c99_c11_compat
#endif
#ifdef GENERATOR_FILE
extern int warn_cast_align;
#else
  int x_warn_cast_align;
#define warn_cast_align global_options.x_warn_cast_align
#endif
#ifdef GENERATOR_FILE
extern int warn_cast_function_type;
#else
  int x_warn_cast_function_type;
#define warn_cast_function_type global_options.x_warn_cast_function_type
#endif
#ifdef GENERATOR_FILE
extern int warn_cast_qual;
#else
  int x_warn_cast_qual;
#define warn_cast_qual global_options.x_warn_cast_qual
#endif
#ifdef GENERATOR_FILE
extern int warn_catch_value;
#else
  int x_warn_catch_value;
#define warn_catch_value global_options.x_warn_catch_value
#endif
#ifdef GENERATOR_FILE
extern int warn_char_subscripts;
#else
  int x_warn_char_subscripts;
#define warn_char_subscripts global_options.x_warn_char_subscripts
#endif
#ifdef GENERATOR_FILE
extern int warn_character_truncation;
#else
  int x_warn_character_truncation;
#define warn_character_truncation global_options.x_warn_character_truncation
#endif
#ifdef GENERATOR_FILE
extern int warn_chkp;
#else
  int x_warn_chkp;
#define warn_chkp global_options.x_warn_chkp
#endif
#ifdef GENERATOR_FILE
extern int warn_class_memaccess;
#else
  int x_warn_class_memaccess;
#define warn_class_memaccess global_options.x_warn_class_memaccess
#endif
#ifdef GENERATOR_FILE
extern int warn_clobbered;
#else
  int x_warn_clobbered;
#define warn_clobbered global_options.x_warn_clobbered
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_comment;
#else
  int x_cpp_warn_comment;
#define cpp_warn_comment global_options.x_cpp_warn_comment
#endif
#ifdef GENERATOR_FILE
extern int warn_compare_reals;
#else
  int x_warn_compare_reals;
#define warn_compare_reals global_options.x_warn_compare_reals
#endif
#ifdef GENERATOR_FILE
extern int warn_conditionally_supported;
#else
  int x_warn_conditionally_supported;
#define warn_conditionally_supported global_options.x_warn_conditionally_supported
#endif
#ifdef GENERATOR_FILE
extern int warn_conversion;
#else
  int x_warn_conversion;
#define warn_conversion global_options.x_warn_conversion
#endif
#ifdef GENERATOR_FILE
extern int warn_conversion_extra;
#else
  int x_warn_conversion_extra;
#define warn_conversion_extra global_options.x_warn_conversion_extra
#endif
#ifdef GENERATOR_FILE
extern int warn_conversion_null;
#else
  int x_warn_conversion_null;
#define warn_conversion_null global_options.x_warn_conversion_null
#endif
#ifdef GENERATOR_FILE
extern int warn_coverage_mismatch;
#else
  int x_warn_coverage_mismatch;
#define warn_coverage_mismatch global_options.x_warn_coverage_mismatch
#endif
#ifdef GENERATOR_FILE
extern int warn_cpp;
#else
  int x_warn_cpp;
#define warn_cpp global_options.x_warn_cpp
#endif
#ifdef GENERATOR_FILE
extern int warn_ctor_dtor_privacy;
#else
  int x_warn_ctor_dtor_privacy;
#define warn_ctor_dtor_privacy global_options.x_warn_ctor_dtor_privacy
#endif
#ifdef GENERATOR_FILE
extern int warn_dangling_else;
#else
  int x_warn_dangling_else;
#define warn_dangling_else global_options.x_warn_dangling_else
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_date_time;
#else
  int x_cpp_warn_date_time;
#define cpp_warn_date_time global_options.x_cpp_warn_date_time
#endif
#ifdef GENERATOR_FILE
extern int warn_declaration_after_statement;
#else
  int x_warn_declaration_after_statement;
#define warn_declaration_after_statement global_options.x_warn_declaration_after_statement
#endif
#ifdef GENERATOR_FILE
extern int warn_delete_incomplete;
#else
  int x_warn_delete_incomplete;
#define warn_delete_incomplete global_options.x_warn_delete_incomplete
#endif
#ifdef GENERATOR_FILE
extern int warn_delnonvdtor;
#else
  int x_warn_delnonvdtor;
#define warn_delnonvdtor global_options.x_warn_delnonvdtor
#endif
#ifdef GENERATOR_FILE
extern int warn_deprecated;
#else
  int x_warn_deprecated;
#define warn_deprecated global_options.x_warn_deprecated
#endif
#ifdef GENERATOR_FILE
extern int warn_deprecated_decl;
#else
  int x_warn_deprecated_decl;
#define warn_deprecated_decl global_options.x_warn_deprecated_decl
#endif
#ifdef GENERATOR_FILE
extern int warn_designated_init;
#else
  int x_warn_designated_init;
#define warn_designated_init global_options.x_warn_designated_init
#endif
#ifdef GENERATOR_FILE
extern int warn_disabled_optimization;
#else
  int x_warn_disabled_optimization;
#define warn_disabled_optimization global_options.x_warn_disabled_optimization
#endif
#ifdef GENERATOR_FILE
extern int warn_discarded_array_qualifiers;
#else
  int x_warn_discarded_array_qualifiers;
#define warn_discarded_array_qualifiers global_options.x_warn_discarded_array_qualifiers
#endif
#ifdef GENERATOR_FILE
extern int warn_discarded_qualifiers;
#else
  int x_warn_discarded_qualifiers;
#define warn_discarded_qualifiers global_options.x_warn_discarded_qualifiers
#endif
#ifdef GENERATOR_FILE
extern int warn_div_by_zero;
#else
  int x_warn_div_by_zero;
#define warn_div_by_zero global_options.x_warn_div_by_zero
#endif
#ifdef GENERATOR_FILE
extern int warn_do_subscript;
#else
  int x_warn_do_subscript;
#define warn_do_subscript global_options.x_warn_do_subscript
#endif
#ifdef GENERATOR_FILE
extern int warn_double_promotion;
#else
  int x_warn_double_promotion;
#define warn_double_promotion global_options.x_warn_double_promotion
#endif
#ifdef GENERATOR_FILE
extern int warn_duplicate_decl_specifier;
#else
  int x_warn_duplicate_decl_specifier;
#define warn_duplicate_decl_specifier global_options.x_warn_duplicate_decl_specifier
#endif
#ifdef GENERATOR_FILE
extern int warn_duplicated_branches;
#else
  int x_warn_duplicated_branches;
#define warn_duplicated_branches global_options.x_warn_duplicated_branches
#endif
#ifdef GENERATOR_FILE
extern int warn_duplicated_cond;
#else
  int x_warn_duplicated_cond;
#define warn_duplicated_cond global_options.x_warn_duplicated_cond
#endif
#ifdef GENERATOR_FILE
extern int warn_ecpp;
#else
  int x_warn_ecpp;
#define warn_ecpp global_options.x_warn_ecpp
#endif
#ifdef GENERATOR_FILE
extern int warn_empty_body;
#else
  int x_warn_empty_body;
#define warn_empty_body global_options.x_warn_empty_body
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_endif_labels;
#else
  int x_cpp_warn_endif_labels;
#define cpp_warn_endif_labels global_options.x_cpp_warn_endif_labels
#endif
#ifdef GENERATOR_FILE
extern int warn_enum_compare;
#else
  int x_warn_enum_compare;
#define warn_enum_compare global_options.x_warn_enum_compare
#endif
#ifdef GENERATOR_FILE
extern int warnings_are_errors;
#else
  int x_warnings_are_errors;
#define warnings_are_errors global_options.x_warnings_are_errors
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_expansion_to_defined;
#else
  int x_cpp_warn_expansion_to_defined;
#define cpp_warn_expansion_to_defined global_options.x_cpp_warn_expansion_to_defined
#endif
#ifdef GENERATOR_FILE
extern int extra_warnings;
#else
  int x_extra_warnings;
#define extra_warnings global_options.x_extra_warnings
#endif
#ifdef GENERATOR_FILE
extern int warn_extra_semi;
#else
  int x_warn_extra_semi;
#define warn_extra_semi global_options.x_warn_extra_semi
#endif
#ifdef GENERATOR_FILE
extern int flag_fatal_errors;
#else
  int x_flag_fatal_errors;
#define flag_fatal_errors global_options.x_flag_fatal_errors
#endif
#ifdef GENERATOR_FILE
extern int warn_float_conversion;
#else
  int x_warn_float_conversion;
#define warn_float_conversion global_options.x_warn_float_conversion
#endif
#ifdef GENERATOR_FILE
extern int warn_float_equal;
#else
  int x_warn_float_equal;
#define warn_float_equal global_options.x_warn_float_equal
#endif
#ifdef GENERATOR_FILE
extern int warn_format_contains_nul;
#else
  int x_warn_format_contains_nul;
#define warn_format_contains_nul global_options.x_warn_format_contains_nul
#endif
#ifdef GENERATOR_FILE
extern int warn_format_extra_args;
#else
  int x_warn_format_extra_args;
#define warn_format_extra_args global_options.x_warn_format_extra_args
#endif
#ifdef GENERATOR_FILE
extern int warn_format_nonliteral;
#else
  int x_warn_format_nonliteral;
#define warn_format_nonliteral global_options.x_warn_format_nonliteral
#endif
#ifdef GENERATOR_FILE
extern int warn_format_overflow;
#else
  int x_warn_format_overflow;
#define warn_format_overflow global_options.x_warn_format_overflow
#endif
#ifdef GENERATOR_FILE
extern int warn_format_security;
#else
  int x_warn_format_security;
#define warn_format_security global_options.x_warn_format_security
#endif
#ifdef GENERATOR_FILE
extern int warn_format_signedness;
#else
  int x_warn_format_signedness;
#define warn_format_signedness global_options.x_warn_format_signedness
#endif
#ifdef GENERATOR_FILE
extern int warn_format_trunc;
#else
  int x_warn_format_trunc;
#define warn_format_trunc global_options.x_warn_format_trunc
#endif
#ifdef GENERATOR_FILE
extern int warn_format_y2k;
#else
  int x_warn_format_y2k;
#define warn_format_y2k global_options.x_warn_format_y2k
#endif
#ifdef GENERATOR_FILE
extern int warn_format_zero_length;
#else
  int x_warn_format_zero_length;
#define warn_format_zero_length global_options.x_warn_format_zero_length
#endif
#ifdef GENERATOR_FILE
extern int warn_format;
#else
  int x_warn_format;
#define warn_format global_options.x_warn_format
#endif
#ifdef GENERATOR_FILE
extern int warn_frame_address;
#else
  int x_warn_frame_address;
#define warn_frame_address global_options.x_warn_frame_address
#endif
#ifdef GENERATOR_FILE
extern int warn_free_nonheap_object;
#else
  int x_warn_free_nonheap_object;
#define warn_free_nonheap_object global_options.x_warn_free_nonheap_object
#endif
#ifdef GENERATOR_FILE
extern int flag_warn_frontend_loop_interchange;
#else
  int x_flag_warn_frontend_loop_interchange;
#define flag_warn_frontend_loop_interchange global_options.x_flag_warn_frontend_loop_interchange
#endif
#ifdef GENERATOR_FILE
extern int warn_function_elimination;
#else
  int x_warn_function_elimination;
#define warn_function_elimination global_options.x_warn_function_elimination
#endif
#ifdef GENERATOR_FILE
extern int warn_hsa;
#else
  int x_warn_hsa;
#define warn_hsa global_options.x_warn_hsa
#endif
#ifdef GENERATOR_FILE
extern int warn_if_not_aligned;
#else
  int x_warn_if_not_aligned;
#define warn_if_not_aligned global_options.x_warn_if_not_aligned
#endif
#ifdef GENERATOR_FILE
extern int warn_ignored_attributes;
#else
  int x_warn_ignored_attributes;
#define warn_ignored_attributes global_options.x_warn_ignored_attributes
#endif
#ifdef GENERATOR_FILE
extern int warn_ignored_qualifiers;
#else
  int x_warn_ignored_qualifiers;
#define warn_ignored_qualifiers global_options.x_warn_ignored_qualifiers
#endif
#ifdef GENERATOR_FILE
extern int warn_implicit;
#else
  int x_warn_implicit;
#define warn_implicit global_options.x_warn_implicit
#endif
#ifdef GENERATOR_FILE
extern int warn_implicit_fallthrough;
#else
  int x_warn_implicit_fallthrough;
#define warn_implicit_fallthrough global_options.x_warn_implicit_fallthrough
#endif
#ifdef GENERATOR_FILE
extern int warn_implicit_function_declaration;
#else
  int x_warn_implicit_function_declaration;
#define warn_implicit_function_declaration global_options.x_warn_implicit_function_declaration
#endif
#ifdef GENERATOR_FILE
extern int warn_implicit_int;
#else
  int x_warn_implicit_int;
#define warn_implicit_int global_options.x_warn_implicit_int
#endif
#ifdef GENERATOR_FILE
extern int warn_implicit_interface;
#else
  int x_warn_implicit_interface;
#define warn_implicit_interface global_options.x_warn_implicit_interface
#endif
#ifdef GENERATOR_FILE
extern int warn_implicit_procedure;
#else
  int x_warn_implicit_procedure;
#define warn_implicit_procedure global_options.x_warn_implicit_procedure
#endif
#ifdef GENERATOR_FILE
extern int warn_incompatible_pointer_types;
#else
  int x_warn_incompatible_pointer_types;
#define warn_incompatible_pointer_types global_options.x_warn_incompatible_pointer_types
#endif
#ifdef GENERATOR_FILE
extern int warn_inh_var_ctor;
#else
  int x_warn_inh_var_ctor;
#define warn_inh_var_ctor global_options.x_warn_inh_var_ctor
#endif
#ifdef GENERATOR_FILE
extern int warn_init_self;
#else
  int x_warn_init_self;
#define warn_init_self global_options.x_warn_init_self
#endif
#ifdef GENERATOR_FILE
extern int warn_inline;
#else
  int x_warn_inline;
#define warn_inline global_options.x_warn_inline
#endif
#ifdef GENERATOR_FILE
extern int warn_int_conversion;
#else
  int x_warn_int_conversion;
#define warn_int_conversion global_options.x_warn_int_conversion
#endif
#ifdef GENERATOR_FILE
extern int warn_int_in_bool_context;
#else
  int x_warn_int_in_bool_context;
#define warn_int_in_bool_context global_options.x_warn_int_in_bool_context
#endif
#ifdef GENERATOR_FILE
extern int warn_int_to_pointer_cast;
#else
  int x_warn_int_to_pointer_cast;
#define warn_int_to_pointer_cast global_options.x_warn_int_to_pointer_cast
#endif
#ifdef GENERATOR_FILE
extern int warn_integer_division;
#else
  int x_warn_integer_division;
#define warn_integer_division global_options.x_warn_integer_division
#endif
#ifdef GENERATOR_FILE
extern int warn_intrinsic_shadow;
#else
  int x_warn_intrinsic_shadow;
#define warn_intrinsic_shadow global_options.x_warn_intrinsic_shadow
#endif
#ifdef GENERATOR_FILE
extern int warn_intrinsics_std;
#else
  int x_warn_intrinsics_std;
#define warn_intrinsics_std global_options.x_warn_intrinsics_std
#endif
#ifdef GENERATOR_FILE
extern int warn_invalid_memory_model;
#else
  int x_warn_invalid_memory_model;
#define warn_invalid_memory_model global_options.x_warn_invalid_memory_model
#endif
#ifdef GENERATOR_FILE
extern int warn_invalid_offsetof;
#else
  int x_warn_invalid_offsetof;
#define warn_invalid_offsetof global_options.x_warn_invalid_offsetof
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_invalid_pch;
#else
  int x_cpp_warn_invalid_pch;
#define cpp_warn_invalid_pch global_options.x_cpp_warn_invalid_pch
#endif
#ifdef GENERATOR_FILE
extern int warn_jump_misses_init;
#else
  int x_warn_jump_misses_init;
#define warn_jump_misses_init global_options.x_warn_jump_misses_init
#endif
#ifdef GENERATOR_FILE
extern int warn_line_truncation;
#else
  int x_warn_line_truncation;
#define warn_line_truncation global_options.x_warn_line_truncation
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_literal_suffix;
#else
  int x_cpp_warn_literal_suffix;
#define cpp_warn_literal_suffix global_options.x_cpp_warn_literal_suffix
#endif
#ifdef GENERATOR_FILE
extern int warn_logical_not_paren;
#else
  int x_warn_logical_not_paren;
#define warn_logical_not_paren global_options.x_warn_logical_not_paren
#endif
#ifdef GENERATOR_FILE
extern int warn_logical_op;
#else
  int x_warn_logical_op;
#define warn_logical_op global_options.x_warn_logical_op
#endif
#ifdef GENERATOR_FILE
extern int warn_long_long;
#else
  int x_warn_long_long;
#define warn_long_long global_options.x_warn_long_long
#endif
#ifdef GENERATOR_FILE
extern int warn_lto_type_mismatch;
#else
  int x_warn_lto_type_mismatch;
#define warn_lto_type_mismatch global_options.x_warn_lto_type_mismatch
#endif
#ifdef GENERATOR_FILE
extern int warn_main;
#else
  int x_warn_main;
#define warn_main global_options.x_warn_main
#endif
#ifdef GENERATOR_FILE
extern int warn_maybe_uninitialized;
#else
  int x_warn_maybe_uninitialized;
#define warn_maybe_uninitialized global_options.x_warn_maybe_uninitialized
#endif
#ifdef GENERATOR_FILE
extern int warn_memset_elt_size;
#else
  int x_warn_memset_elt_size;
#define warn_memset_elt_size global_options.x_warn_memset_elt_size
#endif
#ifdef GENERATOR_FILE
extern int warn_memset_transposed_args;
#else
  int x_warn_memset_transposed_args;
#define warn_memset_transposed_args global_options.x_warn_memset_transposed_args
#endif
#ifdef GENERATOR_FILE
extern int warn_misleading_indentation;
#else
  int x_warn_misleading_indentation;
#define warn_misleading_indentation global_options.x_warn_misleading_indentation
#endif
#ifdef GENERATOR_FILE
extern int warn_missing_attributes;
#else
  int x_warn_missing_attributes;
#define warn_missing_attributes global_options.x_warn_missing_attributes
#endif
#ifdef GENERATOR_FILE
extern int warn_missing_braces;
#else
  int x_warn_missing_braces;
#define warn_missing_braces global_options.x_warn_missing_braces
#endif
#ifdef GENERATOR_FILE
extern int warn_missing_declarations;
#else
  int x_warn_missing_declarations;
#define warn_missing_declarations global_options.x_warn_missing_declarations
#endif
#ifdef GENERATOR_FILE
extern int warn_missing_field_initializers;
#else
  int x_warn_missing_field_initializers;
#define warn_missing_field_initializers global_options.x_warn_missing_field_initializers
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_missing_include_dirs;
#else
  int x_cpp_warn_missing_include_dirs;
#define cpp_warn_missing_include_dirs global_options.x_cpp_warn_missing_include_dirs
#endif
#ifdef GENERATOR_FILE
extern int warn_missing_parameter_type;
#else
  int x_warn_missing_parameter_type;
#define warn_missing_parameter_type global_options.x_warn_missing_parameter_type
#endif
#ifdef GENERATOR_FILE
extern int warn_missing_prototypes;
#else
  int x_warn_missing_prototypes;
#define warn_missing_prototypes global_options.x_warn_missing_prototypes
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_multichar;
#else
  int x_cpp_warn_multichar;
#define cpp_warn_multichar global_options.x_cpp_warn_multichar
#endif
#ifdef GENERATOR_FILE
extern int warn_multiple_inheritance;
#else
  int x_warn_multiple_inheritance;
#define warn_multiple_inheritance global_options.x_warn_multiple_inheritance
#endif
#ifdef GENERATOR_FILE
extern int warn_multistatement_macros;
#else
  int x_warn_multistatement_macros;
#define warn_multistatement_macros global_options.x_warn_multistatement_macros
#endif
#ifdef GENERATOR_FILE
extern int warn_namespaces;
#else
  int x_warn_namespaces;
#define warn_namespaces global_options.x_warn_namespaces
#endif
#ifdef GENERATOR_FILE
extern int warn_narrowing;
#else
  int x_warn_narrowing;
#define warn_narrowing global_options.x_warn_narrowing
#endif
#ifdef GENERATOR_FILE
extern int warn_nested_externs;
#else
  int x_warn_nested_externs;
#define warn_nested_externs global_options.x_warn_nested_externs
#endif
#ifdef GENERATOR_FILE
extern int warn_noexcept;
#else
  int x_warn_noexcept;
#define warn_noexcept global_options.x_warn_noexcept
#endif
#ifdef GENERATOR_FILE
extern int warn_noexcept_type;
#else
  int x_warn_noexcept_type;
#define warn_noexcept_type global_options.x_warn_noexcept_type
#endif
#ifdef GENERATOR_FILE
extern int warn_nontemplate_friend;
#else
  int x_warn_nontemplate_friend;
#define warn_nontemplate_friend global_options.x_warn_nontemplate_friend
#endif
#ifdef GENERATOR_FILE
extern int warn_nonvdtor;
#else
  int x_warn_nonvdtor;
#define warn_nonvdtor global_options.x_warn_nonvdtor
#endif
#ifdef GENERATOR_FILE
extern int warn_nonnull;
#else
  int x_warn_nonnull;
#define warn_nonnull global_options.x_warn_nonnull
#endif
#ifdef GENERATOR_FILE
extern int warn_nonnull_compare;
#else
  int x_warn_nonnull_compare;
#define warn_nonnull_compare global_options.x_warn_nonnull_compare
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_normalize;
#else
  int x_cpp_warn_normalize;
#define cpp_warn_normalize global_options.x_cpp_warn_normalize
#endif
#ifdef GENERATOR_FILE
extern int warn_null_dereference;
#else
  int x_warn_null_dereference;
#define warn_null_dereference global_options.x_warn_null_dereference
#endif
#ifdef GENERATOR_FILE
extern int warn_odr_violations;
#else
  int x_warn_odr_violations;
#define warn_odr_violations global_options.x_warn_odr_violations
#endif
#ifdef GENERATOR_FILE
extern int warn_old_style_cast;
#else
  int x_warn_old_style_cast;
#define warn_old_style_cast global_options.x_warn_old_style_cast
#endif
#ifdef GENERATOR_FILE
extern int warn_old_style_declaration;
#else
  int x_warn_old_style_declaration;
#define warn_old_style_declaration global_options.x_warn_old_style_declaration
#endif
#ifdef GENERATOR_FILE
extern int warn_old_style_definition;
#else
  int x_warn_old_style_definition;
#define warn_old_style_definition global_options.x_warn_old_style_definition
#endif
#ifdef GENERATOR_FILE
extern int warn_openmp_simd;
#else
  int x_warn_openmp_simd;
#define warn_openmp_simd global_options.x_warn_openmp_simd
#endif
#ifdef GENERATOR_FILE
extern int warn_overflow;
#else
  int x_warn_overflow;
#define warn_overflow global_options.x_warn_overflow
#endif
#ifdef GENERATOR_FILE
extern int warn_overlength_strings;
#else
  int x_warn_overlength_strings;
#define warn_overlength_strings global_options.x_warn_overlength_strings
#endif
#ifdef GENERATOR_FILE
extern int warn_overloaded_virtual;
#else
  int x_warn_overloaded_virtual;
#define warn_overloaded_virtual global_options.x_warn_overloaded_virtual
#endif
#ifdef GENERATOR_FILE
extern int warn_override_init;
#else
  int x_warn_override_init;
#define warn_override_init global_options.x_warn_override_init
#endif
#ifdef GENERATOR_FILE
extern int warn_override_init_side_effects;
#else
  int x_warn_override_init_side_effects;
#define warn_override_init_side_effects global_options.x_warn_override_init_side_effects
#endif
#ifdef GENERATOR_FILE
extern int warn_packed;
#else
  int x_warn_packed;
#define warn_packed global_options.x_warn_packed
#endif
#ifdef GENERATOR_FILE
extern int warn_packed_bitfield_compat;
#else
  int x_warn_packed_bitfield_compat;
#define warn_packed_bitfield_compat global_options.x_warn_packed_bitfield_compat
#endif
#ifdef GENERATOR_FILE
extern int warn_packed_not_aligned;
#else
  int x_warn_packed_not_aligned;
#define warn_packed_not_aligned global_options.x_warn_packed_not_aligned
#endif
#ifdef GENERATOR_FILE
extern int warn_padded;
#else
  int x_warn_padded;
#define warn_padded global_options.x_warn_padded
#endif
#ifdef GENERATOR_FILE
extern int warn_parentheses;
#else
  int x_warn_parentheses;
#define warn_parentheses global_options.x_warn_parentheses
#endif
#ifdef GENERATOR_FILE
extern int pedantic;
#else
  int x_pedantic;
#define pedantic global_options.x_pedantic
#endif
#ifdef GENERATOR_FILE
extern int warn_placement_new;
#else
  int x_warn_placement_new;
#define warn_placement_new global_options.x_warn_placement_new
#endif
#ifdef GENERATOR_FILE
extern int warn_pmf2ptr;
#else
  int x_warn_pmf2ptr;
#define warn_pmf2ptr global_options.x_warn_pmf2ptr
#endif
#ifdef GENERATOR_FILE
extern int warn_pointer_arith;
#else
  int x_warn_pointer_arith;
#define warn_pointer_arith global_options.x_warn_pointer_arith
#endif
#ifdef GENERATOR_FILE
extern int warn_pointer_compare;
#else
  int x_warn_pointer_compare;
#define warn_pointer_compare global_options.x_warn_pointer_compare
#endif
#ifdef GENERATOR_FILE
extern int warn_pointer_sign;
#else
  int x_warn_pointer_sign;
#define warn_pointer_sign global_options.x_warn_pointer_sign
#endif
#ifdef GENERATOR_FILE
extern int warn_pointer_to_int_cast;
#else
  int x_warn_pointer_to_int_cast;
#define warn_pointer_to_int_cast global_options.x_warn_pointer_to_int_cast
#endif
#ifdef GENERATOR_FILE
extern int warn_pragmas;
#else
  int x_warn_pragmas;
#define warn_pragmas global_options.x_warn_pragmas
#endif
#ifdef GENERATOR_FILE
extern int warn_property_assign_default;
#else
  int x_warn_property_assign_default;
#define warn_property_assign_default global_options.x_warn_property_assign_default
#endif
#ifdef GENERATOR_FILE
extern int warn_protocol;
#else
  int x_warn_protocol;
#define warn_protocol global_options.x_warn_protocol
#endif
#ifdef GENERATOR_FILE
extern int warn_psabi;
#else
  int x_warn_psabi;
#define warn_psabi global_options.x_warn_psabi
#endif
#ifdef GENERATOR_FILE
extern int warn_real_q_constant;
#else
  int x_warn_real_q_constant;
#define warn_real_q_constant global_options.x_warn_real_q_constant
#endif
#ifdef GENERATOR_FILE
extern int warn_realloc_lhs;
#else
  int x_warn_realloc_lhs;
#define warn_realloc_lhs global_options.x_warn_realloc_lhs
#endif
#ifdef GENERATOR_FILE
extern int warn_realloc_lhs_all;
#else
  int x_warn_realloc_lhs_all;
#define warn_realloc_lhs_all global_options.x_warn_realloc_lhs_all
#endif
#ifdef GENERATOR_FILE
extern int warn_redundant_decls;
#else
  int x_warn_redundant_decls;
#define warn_redundant_decls global_options.x_warn_redundant_decls
#endif
#ifdef GENERATOR_FILE
extern int warn_register;
#else
  int x_warn_register;
#define warn_register global_options.x_warn_register
#endif
#ifdef GENERATOR_FILE
extern int warn_reorder;
#else
  int x_warn_reorder;
#define warn_reorder global_options.x_warn_reorder
#endif
#ifdef GENERATOR_FILE
extern int warn_restrict;
#else
  int x_warn_restrict;
#define warn_restrict global_options.x_warn_restrict
#endif
#ifdef GENERATOR_FILE
extern int warn_return_local_addr;
#else
  int x_warn_return_local_addr;
#define warn_return_local_addr global_options.x_warn_return_local_addr
#endif
#ifdef GENERATOR_FILE
extern int warn_return_type;
#else
  int x_warn_return_type;
#define warn_return_type global_options.x_warn_return_type
#endif
#ifdef GENERATOR_FILE
extern int warn_selector;
#else
  int x_warn_selector;
#define warn_selector global_options.x_warn_selector
#endif
#ifdef GENERATOR_FILE
extern int warn_sequence_point;
#else
  int x_warn_sequence_point;
#define warn_sequence_point global_options.x_warn_sequence_point
#endif
#ifdef GENERATOR_FILE
extern int warn_shadow;
#else
  int x_warn_shadow;
#define warn_shadow global_options.x_warn_shadow
#endif
#ifdef GENERATOR_FILE
extern int warn_shadow_ivar;
#else
  int x_warn_shadow_ivar;
#define warn_shadow_ivar global_options.x_warn_shadow_ivar
#endif
#ifdef GENERATOR_FILE
extern int warn_shadow_compatible_local;
#else
  int x_warn_shadow_compatible_local;
#define warn_shadow_compatible_local global_options.x_warn_shadow_compatible_local
#endif
#ifdef GENERATOR_FILE
extern int warn_shadow_local;
#else
  int x_warn_shadow_local;
#define warn_shadow_local global_options.x_warn_shadow_local
#endif
#ifdef GENERATOR_FILE
extern int warn_shift_count_negative;
#else
  int x_warn_shift_count_negative;
#define warn_shift_count_negative global_options.x_warn_shift_count_negative
#endif
#ifdef GENERATOR_FILE
extern int warn_shift_count_overflow;
#else
  int x_warn_shift_count_overflow;
#define warn_shift_count_overflow global_options.x_warn_shift_count_overflow
#endif
#ifdef GENERATOR_FILE
extern int warn_shift_negative_value;
#else
  int x_warn_shift_negative_value;
#define warn_shift_negative_value global_options.x_warn_shift_negative_value
#endif
#ifdef GENERATOR_FILE
extern int warn_shift_overflow;
#else
  int x_warn_shift_overflow;
#define warn_shift_overflow global_options.x_warn_shift_overflow
#endif
#ifdef GENERATOR_FILE
extern int warn_sign_compare;
#else
  int x_warn_sign_compare;
#define warn_sign_compare global_options.x_warn_sign_compare
#endif
#ifdef GENERATOR_FILE
extern int warn_sign_conversion;
#else
  int x_warn_sign_conversion;
#define warn_sign_conversion global_options.x_warn_sign_conversion
#endif
#ifdef GENERATOR_FILE
extern int warn_sign_promo;
#else
  int x_warn_sign_promo;
#define warn_sign_promo global_options.x_warn_sign_promo
#endif
#ifdef GENERATOR_FILE
extern int warn_sized_deallocation;
#else
  int x_warn_sized_deallocation;
#define warn_sized_deallocation global_options.x_warn_sized_deallocation
#endif
#ifdef GENERATOR_FILE
extern int warn_sizeof_array_argument;
#else
  int x_warn_sizeof_array_argument;
#define warn_sizeof_array_argument global_options.x_warn_sizeof_array_argument
#endif
#ifdef GENERATOR_FILE
extern int warn_sizeof_pointer_div;
#else
  int x_warn_sizeof_pointer_div;
#define warn_sizeof_pointer_div global_options.x_warn_sizeof_pointer_div
#endif
#ifdef GENERATOR_FILE
extern int warn_sizeof_pointer_memaccess;
#else
  int x_warn_sizeof_pointer_memaccess;
#define warn_sizeof_pointer_memaccess global_options.x_warn_sizeof_pointer_memaccess
#endif
#ifdef GENERATOR_FILE
extern int warn_stack_protect;
#else
  int x_warn_stack_protect;
#define warn_stack_protect global_options.x_warn_stack_protect
#endif
#ifdef GENERATOR_FILE
extern int warn_stack_usage;
#else
  int x_warn_stack_usage;
#define warn_stack_usage global_options.x_warn_stack_usage
#endif
#ifdef GENERATOR_FILE
extern int warn_strict_aliasing;
#else
  int x_warn_strict_aliasing;
#define warn_strict_aliasing global_options.x_warn_strict_aliasing
#endif
#ifdef GENERATOR_FILE
extern int warn_strict_null_sentinel;
#else
  int x_warn_strict_null_sentinel;
#define warn_strict_null_sentinel global_options.x_warn_strict_null_sentinel
#endif
#ifdef GENERATOR_FILE
extern int warn_strict_overflow;
#else
  int x_warn_strict_overflow;
#define warn_strict_overflow global_options.x_warn_strict_overflow
#endif
#ifdef GENERATOR_FILE
extern int warn_strict_prototypes;
#else
  int x_warn_strict_prototypes;
#define warn_strict_prototypes global_options.x_warn_strict_prototypes
#endif
#ifdef GENERATOR_FILE
extern int warn_strict_selector_match;
#else
  int x_warn_strict_selector_match;
#define warn_strict_selector_match global_options.x_warn_strict_selector_match
#endif
#ifdef GENERATOR_FILE
extern int warn_stringop_overflow;
#else
  int x_warn_stringop_overflow;
#define warn_stringop_overflow global_options.x_warn_stringop_overflow
#endif
#ifdef GENERATOR_FILE
extern int warn_stringop_truncation;
#else
  int x_warn_stringop_truncation;
#define warn_stringop_truncation global_options.x_warn_stringop_truncation
#endif
#ifdef GENERATOR_FILE
extern int warn_subobject_linkage;
#else
  int x_warn_subobject_linkage;
#define warn_subobject_linkage global_options.x_warn_subobject_linkage
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_attribute_cold;
#else
  int x_warn_suggest_attribute_cold;
#define warn_suggest_attribute_cold global_options.x_warn_suggest_attribute_cold
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_attribute_const;
#else
  int x_warn_suggest_attribute_const;
#define warn_suggest_attribute_const global_options.x_warn_suggest_attribute_const
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_attribute_format;
#else
  int x_warn_suggest_attribute_format;
#define warn_suggest_attribute_format global_options.x_warn_suggest_attribute_format
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_attribute_malloc;
#else
  int x_warn_suggest_attribute_malloc;
#define warn_suggest_attribute_malloc global_options.x_warn_suggest_attribute_malloc
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_attribute_noreturn;
#else
  int x_warn_suggest_attribute_noreturn;
#define warn_suggest_attribute_noreturn global_options.x_warn_suggest_attribute_noreturn
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_attribute_pure;
#else
  int x_warn_suggest_attribute_pure;
#define warn_suggest_attribute_pure global_options.x_warn_suggest_attribute_pure
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_final_methods;
#else
  int x_warn_suggest_final_methods;
#define warn_suggest_final_methods global_options.x_warn_suggest_final_methods
#endif
#ifdef GENERATOR_FILE
extern int warn_suggest_final_types;
#else
  int x_warn_suggest_final_types;
#define warn_suggest_final_types global_options.x_warn_suggest_final_types
#endif
#ifdef GENERATOR_FILE
extern int warn_override;
#else
  int x_warn_override;
#define warn_override global_options.x_warn_override
#endif
#ifdef GENERATOR_FILE
extern int warn_surprising;
#else
  int x_warn_surprising;
#define warn_surprising global_options.x_warn_surprising
#endif
#ifdef GENERATOR_FILE
extern int warn_switch;
#else
  int x_warn_switch;
#define warn_switch global_options.x_warn_switch
#endif
#ifdef GENERATOR_FILE
extern int warn_switch_bool;
#else
  int x_warn_switch_bool;
#define warn_switch_bool global_options.x_warn_switch_bool
#endif
#ifdef GENERATOR_FILE
extern int warn_switch_default;
#else
  int x_warn_switch_default;
#define warn_switch_default global_options.x_warn_switch_default
#endif
#ifdef GENERATOR_FILE
extern int warn_switch_enum;
#else
  int x_warn_switch_enum;
#define warn_switch_enum global_options.x_warn_switch_enum
#endif
#ifdef GENERATOR_FILE
extern int warn_switch_unreachable;
#else
  int x_warn_switch_unreachable;
#define warn_switch_unreachable global_options.x_warn_switch_unreachable
#endif
#ifdef GENERATOR_FILE
extern int warn_sync_nand;
#else
  int x_warn_sync_nand;
#define warn_sync_nand global_options.x_warn_sync_nand
#endif
#ifdef GENERATOR_FILE
extern int warn_synth;
#else
  int x_warn_synth;
#define warn_synth global_options.x_warn_synth
#endif
#ifdef GENERATOR_FILE
extern int warn_system_headers;
#else
  int x_warn_system_headers;
#define warn_system_headers global_options.x_warn_system_headers
#endif
#ifdef GENERATOR_FILE
extern int warn_tabs;
#else
  int x_warn_tabs;
#define warn_tabs global_options.x_warn_tabs
#endif
#ifdef GENERATOR_FILE
extern int warn_target_lifetime;
#else
  int x_warn_target_lifetime;
#define warn_target_lifetime global_options.x_warn_target_lifetime
#endif
#ifdef GENERATOR_FILE
extern int warn_tautological_compare;
#else
  int x_warn_tautological_compare;
#define warn_tautological_compare global_options.x_warn_tautological_compare
#endif
#ifdef GENERATOR_FILE
extern int warn_templates;
#else
  int x_warn_templates;
#define warn_templates global_options.x_warn_templates
#endif
#ifdef GENERATOR_FILE
extern int warn_terminate;
#else
  int x_warn_terminate;
#define warn_terminate global_options.x_warn_terminate
#endif
#ifdef GENERATOR_FILE
extern int warn_traditional;
#else
  int x_warn_traditional;
#define warn_traditional global_options.x_warn_traditional
#endif
#ifdef GENERATOR_FILE
extern int warn_traditional_conversion;
#else
  int x_warn_traditional_conversion;
#define warn_traditional_conversion global_options.x_warn_traditional_conversion
#endif
#ifdef GENERATOR_FILE
extern int warn_trampolines;
#else
  int x_warn_trampolines;
#define warn_trampolines global_options.x_warn_trampolines
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_trigraphs;
#else
  int x_cpp_warn_trigraphs;
#define cpp_warn_trigraphs global_options.x_cpp_warn_trigraphs
#endif
#ifdef GENERATOR_FILE
extern int warn_type_limits;
#else
  int x_warn_type_limits;
#define warn_type_limits global_options.x_warn_type_limits
#endif
#ifdef GENERATOR_FILE
extern int warn_undeclared_selector;
#else
  int x_warn_undeclared_selector;
#define warn_undeclared_selector global_options.x_warn_undeclared_selector
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_undef;
#else
  int x_cpp_warn_undef;
#define cpp_warn_undef global_options.x_cpp_warn_undef
#endif
#ifdef GENERATOR_FILE
extern int warn_undefined_do_loop;
#else
  int x_warn_undefined_do_loop;
#define warn_undefined_do_loop global_options.x_warn_undefined_do_loop
#endif
#ifdef GENERATOR_FILE
extern int warn_underflow;
#else
  int x_warn_underflow;
#define warn_underflow global_options.x_warn_underflow
#endif
#ifdef GENERATOR_FILE
extern int warn_uninitialized;
#else
  int x_warn_uninitialized;
#define warn_uninitialized global_options.x_warn_uninitialized
#endif
#ifdef GENERATOR_FILE
extern int warn_unknown_pragmas;
#else
  int x_warn_unknown_pragmas;
#define warn_unknown_pragmas global_options.x_warn_unknown_pragmas
#endif
#ifdef GENERATOR_FILE
extern int warn_unsuffixed_float_constants;
#else
  int x_warn_unsuffixed_float_constants;
#define warn_unsuffixed_float_constants global_options.x_warn_unsuffixed_float_constants
#endif
#ifdef GENERATOR_FILE
extern int warn_unused;
#else
  int x_warn_unused;
#define warn_unused global_options.x_warn_unused
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_but_set_parameter;
#else
  int x_warn_unused_but_set_parameter;
#define warn_unused_but_set_parameter global_options.x_warn_unused_but_set_parameter
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_but_set_variable;
#else
  int x_warn_unused_but_set_variable;
#define warn_unused_but_set_variable global_options.x_warn_unused_but_set_variable
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_const_variable;
#else
  int x_warn_unused_const_variable;
#define warn_unused_const_variable global_options.x_warn_unused_const_variable
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_dummy_argument;
#else
  int x_warn_unused_dummy_argument;
#define warn_unused_dummy_argument global_options.x_warn_unused_dummy_argument
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_function;
#else
  int x_warn_unused_function;
#define warn_unused_function global_options.x_warn_unused_function
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_label;
#else
  int x_warn_unused_label;
#define warn_unused_label global_options.x_warn_unused_label
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_local_typedefs;
#else
  int x_warn_unused_local_typedefs;
#define warn_unused_local_typedefs global_options.x_warn_unused_local_typedefs
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_unused_macros;
#else
  int x_cpp_warn_unused_macros;
#define cpp_warn_unused_macros global_options.x_cpp_warn_unused_macros
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_parameter;
#else
  int x_warn_unused_parameter;
#define warn_unused_parameter global_options.x_warn_unused_parameter
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_result;
#else
  int x_warn_unused_result;
#define warn_unused_result global_options.x_warn_unused_result
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_value;
#else
  int x_warn_unused_value;
#define warn_unused_value global_options.x_warn_unused_value
#endif
#ifdef GENERATOR_FILE
extern int warn_unused_variable;
#else
  int x_warn_unused_variable;
#define warn_unused_variable global_options.x_warn_unused_variable
#endif
#ifdef GENERATOR_FILE
extern int warn_use_without_only;
#else
  int x_warn_use_without_only;
#define warn_use_without_only global_options.x_warn_use_without_only
#endif
#ifdef GENERATOR_FILE
extern int warn_useless_cast;
#else
  int x_warn_useless_cast;
#define warn_useless_cast global_options.x_warn_useless_cast
#endif
#ifdef GENERATOR_FILE
extern int warn_varargs;
#else
  int x_warn_varargs;
#define warn_varargs global_options.x_warn_varargs
#endif
#ifdef GENERATOR_FILE
extern int cpp_warn_variadic_macros;
#else
  int x_cpp_warn_variadic_macros;
#define cpp_warn_variadic_macros global_options.x_cpp_warn_variadic_macros
#endif
#ifdef GENERATOR_FILE
extern int warn_vector_operation_performance;
#else
  int x_warn_vector_operation_performance;
#define warn_vector_operation_performance global_options.x_warn_vector_operation_performance
#endif
#ifdef GENERATOR_FILE
extern int warn_virtual_inheritance;
#else
  int x_warn_virtual_inheritance;
#define warn_virtual_inheritance global_options.x_warn_virtual_inheritance
#endif
#ifdef GENERATOR_FILE
extern int warn_virtual_move_assign;
#else
  int x_warn_virtual_move_assign;
#define warn_virtual_move_assign global_options.x_warn_virtual_move_assign
#endif
#ifdef GENERATOR_FILE
extern int warn_vla;
#else
  int x_warn_vla;
#define warn_vla global_options.x_warn_vla
#endif
#ifdef GENERATOR_FILE
extern int warn_vla_limit;
#else
  int x_warn_vla_limit;
#define warn_vla_limit global_options.x_warn_vla_limit
#endif
#ifdef GENERATOR_FILE
extern int warn_volatile_register_var;
#else
  int x_warn_volatile_register_var;
#define warn_volatile_register_var global_options.x_warn_volatile_register_var
#endif
#ifdef GENERATOR_FILE
extern int warn_write_strings;
#else
  int x_warn_write_strings;
#define warn_write_strings global_options.x_warn_write_strings
#endif
#ifdef GENERATOR_FILE
extern int warn_zero_as_null_pointer_constant;
#else
  int x_warn_zero_as_null_pointer_constant;
#define warn_zero_as_null_pointer_constant global_options.x_warn_zero_as_null_pointer_constant
#endif
#ifdef GENERATOR_FILE
extern int warn_zerotrip;
#else
  int x_warn_zerotrip;
#define warn_zerotrip global_options.x_warn_zerotrip
#endif
#ifdef GENERATOR_FILE
extern const char *aux_info_file_name;
#else
  const char *x_aux_info_file_name;
#define aux_info_file_name global_options.x_aux_info_file_name
#endif
#ifdef GENERATOR_FILE
extern const char *aux_base_name;
#else
  const char *x_aux_base_name;
#define aux_base_name global_options.x_aux_base_name
#endif
#ifdef GENERATOR_FILE
extern const char *dump_base_name;
#else
  const char *x_dump_base_name;
#define dump_base_name global_options.x_dump_base_name
#endif
#ifdef GENERATOR_FILE
extern const char *dump_dir_name;
#else
  const char *x_dump_dir_name;
#define dump_dir_name global_options.x_dump_dir_name
#endif
#ifdef GENERATOR_FILE
extern int flag_pic;
#else
  int x_flag_pic;
#define flag_pic global_options.x_flag_pic
#endif
#ifdef GENERATOR_FILE
extern int flag_pie;
#else
  int x_flag_pie;
#define flag_pie global_options.x_flag_pie
#endif
#ifdef GENERATOR_FILE
extern int flag_abi_compat_version;
#else
  int x_flag_abi_compat_version;
#define flag_abi_compat_version global_options.x_flag_abi_compat_version
#endif
#ifdef GENERATOR_FILE
extern int flag_abi_version;
#else
  int x_flag_abi_version;
#define flag_abi_version global_options.x_flag_abi_version
#endif
#ifdef GENERATOR_FILE
extern int flag_access_control;
#else
  int x_flag_access_control;
#define flag_access_control global_options.x_flag_access_control
#endif
#ifdef GENERATOR_FILE
extern const char *ada_specs_parent;
#else
  const char *x_ada_specs_parent;
#define ada_specs_parent global_options.x_ada_specs_parent
#endif
#ifdef GENERATOR_FILE
extern int flag_aggressive_function_elimination;
#else
  int x_flag_aggressive_function_elimination;
#define flag_aggressive_function_elimination global_options.x_flag_aggressive_function_elimination
#endif
#ifdef GENERATOR_FILE
extern int flag_aggressive_loop_optimizations;
#else
  int x_flag_aggressive_loop_optimizations;
#define flag_aggressive_loop_optimizations global_options.x_flag_aggressive_loop_optimizations
#endif
#ifdef GENERATOR_FILE
extern int flag_align_commons;
#else
  int x_flag_align_commons;
#define flag_align_commons global_options.x_flag_align_commons
#endif
#ifdef GENERATOR_FILE
extern int align_functions;
#else
  int x_align_functions;
#define align_functions global_options.x_align_functions
#endif
#ifdef GENERATOR_FILE
extern int align_jumps;
#else
  int x_align_jumps;
#define align_jumps global_options.x_align_jumps
#endif
#ifdef GENERATOR_FILE
extern int align_labels;
#else
  int x_align_labels;
#define align_labels global_options.x_align_labels
#endif
#ifdef GENERATOR_FILE
extern int align_loops;
#else
  int x_align_loops;
#define align_loops global_options.x_align_loops
#endif
#ifdef GENERATOR_FILE
extern int aligned_new_threshold;
#else
  int x_aligned_new_threshold;
#define aligned_new_threshold global_options.x_aligned_new_threshold
#endif
#ifdef GENERATOR_FILE
extern int flag_all_intrinsics;
#else
  int x_flag_all_intrinsics;
#define flag_all_intrinsics global_options.x_flag_all_intrinsics
#endif
#ifdef GENERATOR_FILE
extern int flag_allow_leading_underscore;
#else
  int x_flag_allow_leading_underscore;
#define flag_allow_leading_underscore global_options.x_flag_allow_leading_underscore
#endif
#ifdef GENERATOR_FILE
extern int flag_allow_parameterless_variadic_functions;
#else
  int x_flag_allow_parameterless_variadic_functions;
#define flag_allow_parameterless_variadic_functions global_options.x_flag_allow_parameterless_variadic_functions
#endif
#ifdef GENERATOR_FILE
extern void *common_deferred_options;
#else
  void *x_common_deferred_options;
#define common_deferred_options global_options.x_common_deferred_options
#endif
#ifdef GENERATOR_FILE
extern int flag_no_asm;
#else
  int x_flag_no_asm;
#define flag_no_asm global_options.x_flag_no_asm
#endif
#ifdef GENERATOR_FILE
extern int flag_associative_math;
#else
  int x_flag_associative_math;
#define flag_associative_math global_options.x_flag_associative_math
#endif
#ifdef GENERATOR_FILE
extern int flag_asynchronous_unwind_tables;
#else
  int x_flag_asynchronous_unwind_tables;
#define flag_asynchronous_unwind_tables global_options.x_flag_asynchronous_unwind_tables
#endif
#ifdef GENERATOR_FILE
extern int flag_auto_inc_dec;
#else
  int x_flag_auto_inc_dec;
#define flag_auto_inc_dec global_options.x_flag_auto_inc_dec
#endif
#ifdef GENERATOR_FILE
extern int flag_auto_profile;
#else
  int x_flag_auto_profile;
#define flag_auto_profile global_options.x_flag_auto_profile
#endif
#ifdef GENERATOR_FILE
extern const char *auto_profile_file;
#else
  const char *x_auto_profile_file;
#define auto_profile_file global_options.x_auto_profile_file
#endif
#ifdef GENERATOR_FILE
extern int flag_automatic;
#else
  int x_flag_automatic;
#define flag_automatic global_options.x_flag_automatic
#endif
#ifdef GENERATOR_FILE
extern int flag_backslash;
#else
  int x_flag_backslash;
#define flag_backslash global_options.x_flag_backslash
#endif
#ifdef GENERATOR_FILE
extern int flag_backtrace;
#else
  int x_flag_backtrace;
#define flag_backtrace global_options.x_flag_backtrace
#endif
#ifdef GENERATOR_FILE
extern int flag_blas_matmul_limit;
#else
  int x_flag_blas_matmul_limit;
#define flag_blas_matmul_limit global_options.x_flag_blas_matmul_limit
#endif
#ifdef GENERATOR_FILE
extern int flag_bounds_check;
#else
  int x_flag_bounds_check;
#define flag_bounds_check global_options.x_flag_bounds_check
#endif
#ifdef GENERATOR_FILE
extern int flag_branch_on_count_reg;
#else
  int x_flag_branch_on_count_reg;
#define flag_branch_on_count_reg global_options.x_flag_branch_on_count_reg
#endif
#ifdef GENERATOR_FILE
extern int flag_branch_probabilities;
#else
  int x_flag_branch_probabilities;
#define flag_branch_probabilities global_options.x_flag_branch_probabilities
#endif
#ifdef GENERATOR_FILE
extern int flag_branch_target_load_optimize;
#else
  int x_flag_branch_target_load_optimize;
#define flag_branch_target_load_optimize global_options.x_flag_branch_target_load_optimize
#endif
#ifdef GENERATOR_FILE
extern int flag_branch_target_load_optimize2;
#else
  int x_flag_branch_target_load_optimize2;
#define flag_branch_target_load_optimize2 global_options.x_flag_branch_target_load_optimize2
#endif
#ifdef GENERATOR_FILE
extern int flag_btr_bb_exclusive;
#else
  int x_flag_btr_bb_exclusive;
#define flag_btr_bb_exclusive global_options.x_flag_btr_bb_exclusive
#endif
#ifdef GENERATOR_FILE
extern int flag_building_libgcc;
#else
  int x_flag_building_libgcc;
#define flag_building_libgcc global_options.x_flag_building_libgcc
#endif
#ifdef GENERATOR_FILE
extern int flag_no_builtin;
#else
  int x_flag_no_builtin;
#define flag_no_builtin global_options.x_flag_no_builtin
#endif
#ifdef GENERATOR_FILE
extern int flag_c_prototypes;
#else
  int x_flag_c_prototypes;
#define flag_c_prototypes global_options.x_flag_c_prototypes
#endif
#ifdef GENERATOR_FILE
extern int flag_caller_saves;
#else
  int x_flag_caller_saves;
#define flag_caller_saves global_options.x_flag_caller_saves
#endif
#ifdef GENERATOR_FILE
extern enum cf_protection_level flag_cf_protection;
#else
  enum cf_protection_level x_flag_cf_protection;
#define flag_cf_protection global_options.x_flag_cf_protection
#endif
#ifdef GENERATOR_FILE
extern int flag_check_data_deps;
#else
  int x_flag_check_data_deps;
#define flag_check_data_deps global_options.x_flag_check_data_deps
#endif
#ifdef GENERATOR_FILE
extern int flag_check_new;
#else
  int x_flag_check_new;
#define flag_check_new global_options.x_flag_check_new
#endif
#ifdef GENERATOR_FILE
extern int flag_check_pointer_bounds;
#else
  int x_flag_check_pointer_bounds;
#define flag_check_pointer_bounds global_options.x_flag_check_pointer_bounds
#endif
#ifdef GENERATOR_FILE
extern int flag_checking;
#else
  int x_flag_checking;
#define flag_checking global_options.x_flag_checking
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_incomplete_type;
#else
  int x_flag_chkp_incomplete_type;
#define flag_chkp_incomplete_type global_options.x_flag_chkp_incomplete_type
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_check_read;
#else
  int x_flag_chkp_check_read;
#define flag_chkp_check_read global_options.x_flag_chkp_check_read
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_check_write;
#else
  int x_flag_chkp_check_write;
#define flag_chkp_check_write global_options.x_flag_chkp_check_write
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_first_field_has_own_bounds;
#else
  int x_flag_chkp_first_field_has_own_bounds;
#define flag_chkp_first_field_has_own_bounds global_options.x_flag_chkp_first_field_has_own_bounds
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_flexible_struct_trailing_arrays;
#else
  int x_flag_chkp_flexible_struct_trailing_arrays;
#define flag_chkp_flexible_struct_trailing_arrays global_options.x_flag_chkp_flexible_struct_trailing_arrays
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_instrument_calls;
#else
  int x_flag_chkp_instrument_calls;
#define flag_chkp_instrument_calls global_options.x_flag_chkp_instrument_calls
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_instrument_marked_only;
#else
  int x_flag_chkp_instrument_marked_only;
#define flag_chkp_instrument_marked_only global_options.x_flag_chkp_instrument_marked_only
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_narrow_bounds;
#else
  int x_flag_chkp_narrow_bounds;
#define flag_chkp_narrow_bounds global_options.x_flag_chkp_narrow_bounds
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_narrow_to_innermost_arrray;
#else
  int x_flag_chkp_narrow_to_innermost_arrray;
#define flag_chkp_narrow_to_innermost_arrray global_options.x_flag_chkp_narrow_to_innermost_arrray
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_optimize;
#else
  int x_flag_chkp_optimize;
#define flag_chkp_optimize global_options.x_flag_chkp_optimize
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_store_bounds;
#else
  int x_flag_chkp_store_bounds;
#define flag_chkp_store_bounds global_options.x_flag_chkp_store_bounds
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_zero_dynamic_size_as_infinite;
#else
  int x_flag_chkp_zero_dynamic_size_as_infinite;
#define flag_chkp_zero_dynamic_size_as_infinite global_options.x_flag_chkp_zero_dynamic_size_as_infinite
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_use_fast_string_functions;
#else
  int x_flag_chkp_use_fast_string_functions;
#define flag_chkp_use_fast_string_functions global_options.x_flag_chkp_use_fast_string_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_use_nochk_string_functions;
#else
  int x_flag_chkp_use_nochk_string_functions;
#define flag_chkp_use_nochk_string_functions global_options.x_flag_chkp_use_nochk_string_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_use_static_bounds;
#else
  int x_flag_chkp_use_static_bounds;
#define flag_chkp_use_static_bounds global_options.x_flag_chkp_use_static_bounds
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_use_static_const_bounds;
#else
  int x_flag_chkp_use_static_const_bounds;
#define flag_chkp_use_static_const_bounds global_options.x_flag_chkp_use_static_const_bounds
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_use_wrappers;
#else
  int x_flag_chkp_use_wrappers;
#define flag_chkp_use_wrappers global_options.x_flag_chkp_use_wrappers
#endif
#ifdef GENERATOR_FILE
extern int flag_chkp_zero_input_bounds_for_main;
#else
  int x_flag_chkp_zero_input_bounds_for_main;
#define flag_chkp_zero_input_bounds_for_main global_options.x_flag_chkp_zero_input_bounds_for_main
#endif
#ifdef GENERATOR_FILE
extern int flag_cilkplus;
#else
  int x_flag_cilkplus;
#define flag_cilkplus global_options.x_flag_cilkplus
#endif
#ifdef GENERATOR_FILE
extern enum gfc_fcoarray flag_coarray;
#else
  enum gfc_fcoarray x_flag_coarray;
#define flag_coarray global_options.x_flag_coarray
#endif
#ifdef GENERATOR_FILE
extern int flag_code_hoisting;
#else
  int x_flag_code_hoisting;
#define flag_code_hoisting global_options.x_flag_code_hoisting
#endif
#ifdef GENERATOR_FILE
extern int flag_combine_stack_adjustments;
#else
  int x_flag_combine_stack_adjustments;
#define flag_combine_stack_adjustments global_options.x_flag_combine_stack_adjustments
#endif
#ifdef GENERATOR_FILE
extern int flag_no_common;
#else
  int x_flag_no_common;
#define flag_no_common global_options.x_flag_no_common
#endif
#ifdef GENERATOR_FILE
extern int flag_compare_debug;
#else
  int x_flag_compare_debug;
#define flag_compare_debug global_options.x_flag_compare_debug
#endif
#ifdef GENERATOR_FILE
extern const char *flag_compare_debug_opt;
#else
  const char *x_flag_compare_debug_opt;
#define flag_compare_debug_opt global_options.x_flag_compare_debug_opt
#endif
#ifdef GENERATOR_FILE
extern int flag_compare_elim_after_reload;
#else
  int x_flag_compare_elim_after_reload;
#define flag_compare_elim_after_reload global_options.x_flag_compare_elim_after_reload
#endif
#ifdef GENERATOR_FILE
extern int flag_concepts;
#else
  int x_flag_concepts;
#define flag_concepts global_options.x_flag_concepts
#endif
#ifdef GENERATOR_FILE
extern int flag_conserve_space;
#else
  int x_flag_conserve_space;
#define flag_conserve_space global_options.x_flag_conserve_space
#endif
#ifdef GENERATOR_FILE
extern int flag_conserve_stack;
#else
  int x_flag_conserve_stack;
#define flag_conserve_stack global_options.x_flag_conserve_stack
#endif
#ifdef GENERATOR_FILE
extern int max_constexpr_depth;
#else
  int x_max_constexpr_depth;
#define max_constexpr_depth global_options.x_max_constexpr_depth
#endif
#ifdef GENERATOR_FILE
extern int constexpr_loop_limit;
#else
  int x_constexpr_loop_limit;
#define constexpr_loop_limit global_options.x_constexpr_loop_limit
#endif
#ifdef GENERATOR_FILE
extern enum gfc_convert flag_convert;
#else
  enum gfc_convert x_flag_convert;
#define flag_convert global_options.x_flag_convert
#endif
#ifdef GENERATOR_FILE
extern int flag_cprop_registers;
#else
  int x_flag_cprop_registers;
#define flag_cprop_registers global_options.x_flag_cprop_registers
#endif
#ifdef GENERATOR_FILE
extern int flag_cray_pointer;
#else
  int x_flag_cray_pointer;
#define flag_cray_pointer global_options.x_flag_cray_pointer
#endif
#ifdef GENERATOR_FILE
extern int flag_crossjumping;
#else
  int x_flag_crossjumping;
#define flag_crossjumping global_options.x_flag_crossjumping
#endif
#ifdef GENERATOR_FILE
extern int flag_cse_follow_jumps;
#else
  int x_flag_cse_follow_jumps;
#define flag_cse_follow_jumps global_options.x_flag_cse_follow_jumps
#endif
#ifdef GENERATOR_FILE
extern int flag_cx_fortran_rules;
#else
  int x_flag_cx_fortran_rules;
#define flag_cx_fortran_rules global_options.x_flag_cx_fortran_rules
#endif
#ifdef GENERATOR_FILE
extern int flag_cx_limited_range;
#else
  int x_flag_cx_limited_range;
#define flag_cx_limited_range global_options.x_flag_cx_limited_range
#endif
#ifdef GENERATOR_FILE
extern int flag_data_sections;
#else
  int x_flag_data_sections;
#define flag_data_sections global_options.x_flag_data_sections
#endif
#ifdef GENERATOR_FILE
extern int flag_dce;
#else
  int x_flag_dce;
#define flag_dce global_options.x_flag_dce
#endif
#ifdef GENERATOR_FILE
extern int flag_debug_types_section;
#else
  int x_flag_debug_types_section;
#define flag_debug_types_section global_options.x_flag_debug_types_section
#endif
#ifdef GENERATOR_FILE
extern int flag_dec;
#else
  int x_flag_dec;
#define flag_dec global_options.x_flag_dec
#endif
#ifdef GENERATOR_FILE
extern int flag_dec_intrinsic_ints;
#else
  int x_flag_dec_intrinsic_ints;
#define flag_dec_intrinsic_ints global_options.x_flag_dec_intrinsic_ints
#endif
#ifdef GENERATOR_FILE
extern int flag_dec_math;
#else
  int x_flag_dec_math;
#define flag_dec_math global_options.x_flag_dec_math
#endif
#ifdef GENERATOR_FILE
extern int flag_dec_static;
#else
  int x_flag_dec_static;
#define flag_dec_static global_options.x_flag_dec_static
#endif
#ifdef GENERATOR_FILE
extern int flag_dec_structure;
#else
  int x_flag_dec_structure;
#define flag_dec_structure global_options.x_flag_dec_structure
#endif
#ifdef GENERATOR_FILE
extern int flag_declone_ctor_dtor;
#else
  int x_flag_declone_ctor_dtor;
#define flag_declone_ctor_dtor global_options.x_flag_declone_ctor_dtor
#endif
#ifdef GENERATOR_FILE
extern int flag_deduce_init_list;
#else
  int x_flag_deduce_init_list;
#define flag_deduce_init_list global_options.x_flag_deduce_init_list
#endif
#ifdef GENERATOR_FILE
extern int flag_default_double;
#else
  int x_flag_default_double;
#define flag_default_double global_options.x_flag_default_double
#endif
#ifdef GENERATOR_FILE
extern int flag_default_integer;
#else
  int x_flag_default_integer;
#define flag_default_integer global_options.x_flag_default_integer
#endif
#ifdef GENERATOR_FILE
extern int flag_default_real_10;
#else
  int x_flag_default_real_10;
#define flag_default_real_10 global_options.x_flag_default_real_10
#endif
#ifdef GENERATOR_FILE
extern int flag_default_real_16;
#else
  int x_flag_default_real_16;
#define flag_default_real_16 global_options.x_flag_default_real_16
#endif
#ifdef GENERATOR_FILE
extern int flag_default_real_8;
#else
  int x_flag_default_real_8;
#define flag_default_real_8 global_options.x_flag_default_real_8
#endif
#ifdef GENERATOR_FILE
extern int flag_defer_pop;
#else
  int x_flag_defer_pop;
#define flag_defer_pop global_options.x_flag_defer_pop
#endif
#ifdef GENERATOR_FILE
extern int flag_delayed_branch;
#else
  int x_flag_delayed_branch;
#define flag_delayed_branch global_options.x_flag_delayed_branch
#endif
#ifdef GENERATOR_FILE
extern int flag_delete_dead_exceptions;
#else
  int x_flag_delete_dead_exceptions;
#define flag_delete_dead_exceptions global_options.x_flag_delete_dead_exceptions
#endif
#ifdef GENERATOR_FILE
extern int flag_delete_null_pointer_checks;
#else
  int x_flag_delete_null_pointer_checks;
#define flag_delete_null_pointer_checks global_options.x_flag_delete_null_pointer_checks
#endif
#ifdef GENERATOR_FILE
extern int flag_devirtualize;
#else
  int x_flag_devirtualize;
#define flag_devirtualize global_options.x_flag_devirtualize
#endif
#ifdef GENERATOR_FILE
extern int flag_ltrans_devirtualize;
#else
  int x_flag_ltrans_devirtualize;
#define flag_ltrans_devirtualize global_options.x_flag_ltrans_devirtualize
#endif
#ifdef GENERATOR_FILE
extern int flag_devirtualize_speculatively;
#else
  int x_flag_devirtualize_speculatively;
#define flag_devirtualize_speculatively global_options.x_flag_devirtualize_speculatively
#endif
#ifdef GENERATOR_FILE
extern int flag_diagnostics_show_color;
#else
  int x_flag_diagnostics_show_color;
#define flag_diagnostics_show_color global_options.x_flag_diagnostics_show_color
#endif
#ifdef GENERATOR_FILE
extern int flag_diagnostics_generate_patch;
#else
  int x_flag_diagnostics_generate_patch;
#define flag_diagnostics_generate_patch global_options.x_flag_diagnostics_generate_patch
#endif
#ifdef GENERATOR_FILE
extern int flag_diagnostics_parseable_fixits;
#else
  int x_flag_diagnostics_parseable_fixits;
#define flag_diagnostics_parseable_fixits global_options.x_flag_diagnostics_parseable_fixits
#endif
#ifdef GENERATOR_FILE
extern int flag_diagnostics_show_caret;
#else
  int x_flag_diagnostics_show_caret;
#define flag_diagnostics_show_caret global_options.x_flag_diagnostics_show_caret
#endif
#ifdef GENERATOR_FILE
extern int flag_diagnostics_show_option;
#else
  int x_flag_diagnostics_show_option;
#define flag_diagnostics_show_option global_options.x_flag_diagnostics_show_option
#endif
#ifdef GENERATOR_FILE
extern int flag_diagnostics_show_template_tree;
#else
  int x_flag_diagnostics_show_template_tree;
#define flag_diagnostics_show_template_tree global_options.x_flag_diagnostics_show_template_tree
#endif
#ifdef GENERATOR_FILE
extern int flag_dollar_ok;
#else
  int x_flag_dollar_ok;
#define flag_dollar_ok global_options.x_flag_dollar_ok
#endif
#ifdef GENERATOR_FILE
extern int flag_dse;
#else
  int x_flag_dse;
#define flag_dse global_options.x_flag_dse
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_ada_spec;
#else
  int x_flag_dump_ada_spec;
#define flag_dump_ada_spec global_options.x_flag_dump_ada_spec
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_ada_spec_slim;
#else
  int x_flag_dump_ada_spec_slim;
#define flag_dump_ada_spec_slim global_options.x_flag_dump_ada_spec_slim
#endif
#ifdef GENERATOR_FILE
extern const char *flag_dump_final_insns;
#else
  const char *x_flag_dump_final_insns;
#define flag_dump_final_insns global_options.x_flag_dump_final_insns
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_fortran_optimized;
#else
  int x_flag_dump_fortran_optimized;
#define flag_dump_fortran_optimized global_options.x_flag_dump_fortran_optimized
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_fortran_original;
#else
  int x_flag_dump_fortran_original;
#define flag_dump_fortran_original global_options.x_flag_dump_fortran_original
#endif
#ifdef GENERATOR_FILE
extern const char *flag_dump_go_spec;
#else
  const char *x_flag_dump_go_spec;
#define flag_dump_go_spec global_options.x_flag_dump_go_spec
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_locations;
#else
  int x_flag_dump_locations;
#define flag_dump_locations global_options.x_flag_dump_locations
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_noaddr;
#else
  int x_flag_dump_noaddr;
#define flag_dump_noaddr global_options.x_flag_dump_noaddr
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_passes;
#else
  int x_flag_dump_passes;
#define flag_dump_passes global_options.x_flag_dump_passes
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_unnumbered;
#else
  int x_flag_dump_unnumbered;
#define flag_dump_unnumbered global_options.x_flag_dump_unnumbered
#endif
#ifdef GENERATOR_FILE
extern int flag_dump_unnumbered_links;
#else
  int x_flag_dump_unnumbered_links;
#define flag_dump_unnumbered_links global_options.x_flag_dump_unnumbered_links
#endif
#ifdef GENERATOR_FILE
extern int flag_dwarf2_cfi_asm;
#else
  int x_flag_dwarf2_cfi_asm;
#define flag_dwarf2_cfi_asm global_options.x_flag_dwarf2_cfi_asm
#endif
#ifdef GENERATOR_FILE
extern int flag_early_inlining;
#else
  int x_flag_early_inlining;
#define flag_early_inlining global_options.x_flag_early_inlining
#endif
#ifdef GENERATOR_FILE
extern int flag_elide_constructors;
#else
  int x_flag_elide_constructors;
#define flag_elide_constructors global_options.x_flag_elide_constructors
#endif
#ifdef GENERATOR_FILE
extern int flag_elide_type;
#else
  int x_flag_elide_type;
#define flag_elide_type global_options.x_flag_elide_type
#endif
#ifdef GENERATOR_FILE
extern int flag_debug_only_used_symbols;
#else
  int x_flag_debug_only_used_symbols;
#define flag_debug_only_used_symbols global_options.x_flag_debug_only_used_symbols
#endif
#ifdef GENERATOR_FILE
extern int flag_eliminate_unused_debug_types;
#else
  int x_flag_eliminate_unused_debug_types;
#define flag_eliminate_unused_debug_types global_options.x_flag_eliminate_unused_debug_types
#endif
#ifdef GENERATOR_FILE
extern int flag_emit_class_debug_always;
#else
  int x_flag_emit_class_debug_always;
#define flag_emit_class_debug_always global_options.x_flag_emit_class_debug_always
#endif
#ifdef GENERATOR_FILE
extern int flag_enforce_eh_specs;
#else
  int x_flag_enforce_eh_specs;
#define flag_enforce_eh_specs global_options.x_flag_enforce_eh_specs
#endif
#ifdef GENERATOR_FILE
extern int flag_exceptions;
#else
  int x_flag_exceptions;
#define flag_exceptions global_options.x_flag_exceptions
#endif
#ifdef GENERATOR_FILE
extern enum excess_precision flag_excess_precision_cmdline;
#else
  enum excess_precision x_flag_excess_precision_cmdline;
#define flag_excess_precision_cmdline global_options.x_flag_excess_precision_cmdline
#endif
#ifdef GENERATOR_FILE
extern int flag_expensive_optimizations;
#else
  int x_flag_expensive_optimizations;
#define flag_expensive_optimizations global_options.x_flag_expensive_optimizations
#endif
#ifdef GENERATOR_FILE
extern int flag_ext_numeric_literals;
#else
  int x_flag_ext_numeric_literals;
#define flag_ext_numeric_literals global_options.x_flag_ext_numeric_literals
#endif
#ifdef GENERATOR_FILE
extern int flag_extern_tls_init;
#else
  int x_flag_extern_tls_init;
#define flag_extern_tls_init global_options.x_flag_extern_tls_init
#endif
#ifdef GENERATOR_FILE
extern int flag_external_blas;
#else
  int x_flag_external_blas;
#define flag_external_blas global_options.x_flag_external_blas
#endif
#ifdef GENERATOR_FILE
extern int flag_f2c;
#else
  int x_flag_f2c;
#define flag_f2c global_options.x_flag_f2c
#endif
#ifdef GENERATOR_FILE
extern int flag_fat_lto_objects;
#else
  int x_flag_fat_lto_objects;
#define flag_fat_lto_objects global_options.x_flag_fat_lto_objects
#endif
#ifdef GENERATOR_FILE
extern int flag_finite_math_only;
#else
  int x_flag_finite_math_only;
#define flag_finite_math_only global_options.x_flag_finite_math_only
#endif
#ifdef GENERATOR_FILE
extern int flag_fixed_line_length;
#else
  int x_flag_fixed_line_length;
#define flag_fixed_line_length global_options.x_flag_fixed_line_length
#endif
#ifdef GENERATOR_FILE
extern int flag_float_store;
#else
  int x_flag_float_store;
#define flag_float_store global_options.x_flag_float_store
#endif
#ifdef GENERATOR_FILE
extern int flag_new_for_scope;
#else
  int x_flag_new_for_scope;
#define flag_new_for_scope global_options.x_flag_new_for_scope
#endif
#ifdef GENERATOR_FILE
extern int flag_forward_propagate;
#else
  int x_flag_forward_propagate;
#define flag_forward_propagate global_options.x_flag_forward_propagate
#endif
#ifdef GENERATOR_FILE
extern enum fp_contract_mode flag_fp_contract_mode;
#else
  enum fp_contract_mode x_flag_fp_contract_mode;
#define flag_fp_contract_mode global_options.x_flag_fp_contract_mode
#endif
#ifdef GENERATOR_FILE
extern int flag_fp_int_builtin_inexact;
#else
  int x_flag_fp_int_builtin_inexact;
#define flag_fp_int_builtin_inexact global_options.x_flag_fp_int_builtin_inexact
#endif
#ifdef GENERATOR_FILE
extern int flag_free_line_length;
#else
  int x_flag_free_line_length;
#define flag_free_line_length global_options.x_flag_free_line_length
#endif
#ifdef GENERATOR_FILE
extern int flag_friend_injection;
#else
  int x_flag_friend_injection;
#define flag_friend_injection global_options.x_flag_friend_injection
#endif
#ifdef GENERATOR_FILE
extern int flag_frontend_loop_interchange;
#else
  int x_flag_frontend_loop_interchange;
#define flag_frontend_loop_interchange global_options.x_flag_frontend_loop_interchange
#endif
#ifdef GENERATOR_FILE
extern int flag_frontend_optimize;
#else
  int x_flag_frontend_optimize;
#define flag_frontend_optimize global_options.x_flag_frontend_optimize
#endif
#ifdef GENERATOR_FILE
extern int flag_no_function_cse;
#else
  int x_flag_no_function_cse;
#define flag_no_function_cse global_options.x_flag_no_function_cse
#endif
#ifdef GENERATOR_FILE
extern int flag_function_sections;
#else
  int x_flag_function_sections;
#define flag_function_sections global_options.x_flag_function_sections
#endif
#ifdef GENERATOR_FILE
extern int flag_gcse;
#else
  int x_flag_gcse;
#define flag_gcse global_options.x_flag_gcse
#endif
#ifdef GENERATOR_FILE
extern int flag_gcse_after_reload;
#else
  int x_flag_gcse_after_reload;
#define flag_gcse_after_reload global_options.x_flag_gcse_after_reload
#endif
#ifdef GENERATOR_FILE
extern int flag_gcse_las;
#else
  int x_flag_gcse_las;
#define flag_gcse_las global_options.x_flag_gcse_las
#endif
#ifdef GENERATOR_FILE
extern int flag_gcse_lm;
#else
  int x_flag_gcse_lm;
#define flag_gcse_lm global_options.x_flag_gcse_lm
#endif
#ifdef GENERATOR_FILE
extern int flag_gcse_sm;
#else
  int x_flag_gcse_sm;
#define flag_gcse_sm global_options.x_flag_gcse_sm
#endif
#ifdef GENERATOR_FILE
extern int flag_gimple;
#else
  int x_flag_gimple;
#define flag_gimple global_options.x_flag_gimple
#endif
#ifdef GENERATOR_FILE
extern int flag_no_gnu_keywords;
#else
  int x_flag_no_gnu_keywords;
#define flag_no_gnu_keywords global_options.x_flag_no_gnu_keywords
#endif
#ifdef GENERATOR_FILE
extern int flag_next_runtime;
#else
  int x_flag_next_runtime;
#define flag_next_runtime global_options.x_flag_next_runtime
#endif
#ifdef GENERATOR_FILE
extern int flag_tm;
#else
  int x_flag_tm;
#define flag_tm global_options.x_flag_tm
#endif
#ifdef GENERATOR_FILE
extern int flag_gnu_unique;
#else
  int x_flag_gnu_unique;
#define flag_gnu_unique global_options.x_flag_gnu_unique
#endif
#ifdef GENERATOR_FILE
extern int flag_gnu89_inline;
#else
  int x_flag_gnu89_inline;
#define flag_gnu89_inline global_options.x_flag_gnu89_inline
#endif
#ifdef GENERATOR_FILE
extern int go_check_divide_overflow;
#else
  int x_go_check_divide_overflow;
#define go_check_divide_overflow global_options.x_go_check_divide_overflow
#endif
#ifdef GENERATOR_FILE
extern int go_check_divide_zero;
#else
  int x_go_check_divide_zero;
#define go_check_divide_zero global_options.x_go_check_divide_zero
#endif
#ifdef GENERATOR_FILE
extern int go_compiling_runtime;
#else
  int x_go_compiling_runtime;
#define go_compiling_runtime global_options.x_go_compiling_runtime
#endif
#ifdef GENERATOR_FILE
extern int go_debug_escape_level;
#else
  int x_go_debug_escape_level;
#define go_debug_escape_level global_options.x_go_debug_escape_level
#endif
#ifdef GENERATOR_FILE
extern const char *go_debug_escape_hash;
#else
  const char *x_go_debug_escape_hash;
#define go_debug_escape_hash global_options.x_go_debug_escape_hash
#endif
#ifdef GENERATOR_FILE
extern int flag_graphite;
#else
  int x_flag_graphite;
#define flag_graphite global_options.x_flag_graphite
#endif
#ifdef GENERATOR_FILE
extern int flag_graphite_identity;
#else
  int x_flag_graphite_identity;
#define flag_graphite_identity global_options.x_flag_graphite_identity
#endif
#ifdef GENERATOR_FILE
extern int flag_guess_branch_prob;
#else
  int x_flag_guess_branch_prob;
#define flag_guess_branch_prob global_options.x_flag_guess_branch_prob
#endif
#ifdef GENERATOR_FILE
extern int flag_hoist_adjacent_loads;
#else
  int x_flag_hoist_adjacent_loads;
#define flag_hoist_adjacent_loads global_options.x_flag_hoist_adjacent_loads
#endif
#ifdef GENERATOR_FILE
extern int flag_no_ident;
#else
  int x_flag_no_ident;
#define flag_no_ident global_options.x_flag_no_ident
#endif
#ifdef GENERATOR_FILE
extern int flag_if_conversion;
#else
  int x_flag_if_conversion;
#define flag_if_conversion global_options.x_flag_if_conversion
#endif
#ifdef GENERATOR_FILE
extern int flag_if_conversion2;
#else
  int x_flag_if_conversion2;
#define flag_if_conversion2 global_options.x_flag_if_conversion2
#endif
#ifdef GENERATOR_FILE
extern int flag_implement_inlines;
#else
  int x_flag_implement_inlines;
#define flag_implement_inlines global_options.x_flag_implement_inlines
#endif
#ifdef GENERATOR_FILE
extern int flag_implicit_inline_templates;
#else
  int x_flag_implicit_inline_templates;
#define flag_implicit_inline_templates global_options.x_flag_implicit_inline_templates
#endif
#ifdef GENERATOR_FILE
extern int flag_implicit_none;
#else
  int x_flag_implicit_none;
#define flag_implicit_none global_options.x_flag_implicit_none
#endif
#ifdef GENERATOR_FILE
extern int flag_implicit_templates;
#else
  int x_flag_implicit_templates;
#define flag_implicit_templates global_options.x_flag_implicit_templates
#endif
#ifdef GENERATOR_FILE
extern int flag_indirect_inlining;
#else
  int x_flag_indirect_inlining;
#define flag_indirect_inlining global_options.x_flag_indirect_inlining
#endif
#ifdef GENERATOR_FILE
extern int flag_inhibit_size_directive;
#else
  int x_flag_inhibit_size_directive;
#define flag_inhibit_size_directive global_options.x_flag_inhibit_size_directive
#endif
#ifdef GENERATOR_FILE
extern int flag_init_derived;
#else
  int x_flag_init_derived;
#define flag_init_derived global_options.x_flag_init_derived
#endif
#ifdef GENERATOR_FILE
extern enum gfc_init_local_real flag_init_real;
#else
  enum gfc_init_local_real x_flag_init_real;
#define flag_init_real global_options.x_flag_init_real
#endif
#ifdef GENERATOR_FILE
extern int flag_no_inline;
#else
  int x_flag_no_inline;
#define flag_no_inline global_options.x_flag_no_inline
#endif
#ifdef GENERATOR_FILE
extern int flag_inline_atomics;
#else
  int x_flag_inline_atomics;
#define flag_inline_atomics global_options.x_flag_inline_atomics
#endif
#ifdef GENERATOR_FILE
extern int flag_inline_functions;
#else
  int x_flag_inline_functions;
#define flag_inline_functions global_options.x_flag_inline_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_inline_functions_called_once;
#else
  int x_flag_inline_functions_called_once;
#define flag_inline_functions_called_once global_options.x_flag_inline_functions_called_once
#endif
#ifdef GENERATOR_FILE
extern int flag_inline_matmul_limit;
#else
  int x_flag_inline_matmul_limit;
#define flag_inline_matmul_limit global_options.x_flag_inline_matmul_limit
#endif
#ifdef GENERATOR_FILE
extern int flag_inline_small_functions;
#else
  int x_flag_inline_small_functions;
#define flag_inline_small_functions global_options.x_flag_inline_small_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_instrument_function_entry_exit;
#else
  int x_flag_instrument_function_entry_exit;
#define flag_instrument_function_entry_exit global_options.x_flag_instrument_function_entry_exit
#endif
#ifdef GENERATOR_FILE
extern int flag_integer4_kind;
#else
  int x_flag_integer4_kind;
#define flag_integer4_kind global_options.x_flag_integer4_kind
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_bit_cp;
#else
  int x_flag_ipa_bit_cp;
#define flag_ipa_bit_cp global_options.x_flag_ipa_bit_cp
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_cp;
#else
  int x_flag_ipa_cp;
#define flag_ipa_cp global_options.x_flag_ipa_cp
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_cp_clone;
#else
  int x_flag_ipa_cp_clone;
#define flag_ipa_cp_clone global_options.x_flag_ipa_cp_clone
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_icf;
#else
  int x_flag_ipa_icf;
#define flag_ipa_icf global_options.x_flag_ipa_icf
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_icf_functions;
#else
  int x_flag_ipa_icf_functions;
#define flag_ipa_icf_functions global_options.x_flag_ipa_icf_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_icf_variables;
#else
  int x_flag_ipa_icf_variables;
#define flag_ipa_icf_variables global_options.x_flag_ipa_icf_variables
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_profile;
#else
  int x_flag_ipa_profile;
#define flag_ipa_profile global_options.x_flag_ipa_profile
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_pta;
#else
  int x_flag_ipa_pta;
#define flag_ipa_pta global_options.x_flag_ipa_pta
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_pure_const;
#else
  int x_flag_ipa_pure_const;
#define flag_ipa_pure_const global_options.x_flag_ipa_pure_const
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_ra;
#else
  int x_flag_ipa_ra;
#define flag_ipa_ra global_options.x_flag_ipa_ra
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_reference;
#else
  int x_flag_ipa_reference;
#define flag_ipa_reference global_options.x_flag_ipa_reference
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_sra;
#else
  int x_flag_ipa_sra;
#define flag_ipa_sra global_options.x_flag_ipa_sra
#endif
#ifdef GENERATOR_FILE
extern int flag_ipa_vrp;
#else
  int x_flag_ipa_vrp;
#define flag_ipa_vrp global_options.x_flag_ipa_vrp
#endif
#ifdef GENERATOR_FILE
extern enum ira_algorithm flag_ira_algorithm;
#else
  enum ira_algorithm x_flag_ira_algorithm;
#define flag_ira_algorithm global_options.x_flag_ira_algorithm
#endif
#ifdef GENERATOR_FILE
extern int flag_ira_hoist_pressure;
#else
  int x_flag_ira_hoist_pressure;
#define flag_ira_hoist_pressure global_options.x_flag_ira_hoist_pressure
#endif
#ifdef GENERATOR_FILE
extern int flag_ira_loop_pressure;
#else
  int x_flag_ira_loop_pressure;
#define flag_ira_loop_pressure global_options.x_flag_ira_loop_pressure
#endif
#ifdef GENERATOR_FILE
extern enum ira_region flag_ira_region;
#else
  enum ira_region x_flag_ira_region;
#define flag_ira_region global_options.x_flag_ira_region
#endif
#ifdef GENERATOR_FILE
extern int flag_ira_share_save_slots;
#else
  int x_flag_ira_share_save_slots;
#define flag_ira_share_save_slots global_options.x_flag_ira_share_save_slots
#endif
#ifdef GENERATOR_FILE
extern int flag_ira_share_spill_slots;
#else
  int x_flag_ira_share_spill_slots;
#define flag_ira_share_spill_slots global_options.x_flag_ira_share_spill_slots
#endif
#ifdef GENERATOR_FILE
extern int flag_ira_verbose;
#else
  int x_flag_ira_verbose;
#define flag_ira_verbose global_options.x_flag_ira_verbose
#endif
#ifdef GENERATOR_FILE
extern int flag_isolate_erroneous_paths_attribute;
#else
  int x_flag_isolate_erroneous_paths_attribute;
#define flag_isolate_erroneous_paths_attribute global_options.x_flag_isolate_erroneous_paths_attribute
#endif
#ifdef GENERATOR_FILE
extern int flag_isolate_erroneous_paths_dereference;
#else
  int x_flag_isolate_erroneous_paths_dereference;
#define flag_isolate_erroneous_paths_dereference global_options.x_flag_isolate_erroneous_paths_dereference
#endif
#ifdef GENERATOR_FILE
extern enum ivar_visibility default_ivar_visibility;
#else
  enum ivar_visibility x_default_ivar_visibility;
#define default_ivar_visibility global_options.x_default_ivar_visibility
#endif
#ifdef GENERATOR_FILE
extern int flag_ivopts;
#else
  int x_flag_ivopts;
#define flag_ivopts global_options.x_flag_ivopts
#endif
#ifdef GENERATOR_FILE
extern int flag_jump_tables;
#else
  int x_flag_jump_tables;
#define flag_jump_tables global_options.x_flag_jump_tables
#endif
#ifdef GENERATOR_FILE
extern int flag_keep_gc_roots_live;
#else
  int x_flag_keep_gc_roots_live;
#define flag_keep_gc_roots_live global_options.x_flag_keep_gc_roots_live
#endif
#ifdef GENERATOR_FILE
extern int flag_keep_inline_dllexport;
#else
  int x_flag_keep_inline_dllexport;
#define flag_keep_inline_dllexport global_options.x_flag_keep_inline_dllexport
#endif
#ifdef GENERATOR_FILE
extern int flag_keep_inline_functions;
#else
  int x_flag_keep_inline_functions;
#define flag_keep_inline_functions global_options.x_flag_keep_inline_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_keep_static_consts;
#else
  int x_flag_keep_static_consts;
#define flag_keep_static_consts global_options.x_flag_keep_static_consts
#endif
#ifdef GENERATOR_FILE
extern int flag_keep_static_functions;
#else
  int x_flag_keep_static_functions;
#define flag_keep_static_functions global_options.x_flag_keep_static_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_lax_vector_conversions;
#else
  int x_flag_lax_vector_conversions;
#define flag_lax_vector_conversions global_options.x_flag_lax_vector_conversions
#endif
#ifdef GENERATOR_FILE
extern int flag_leading_underscore;
#else
  int x_flag_leading_underscore;
#define flag_leading_underscore global_options.x_flag_leading_underscore
#endif
#ifdef GENERATOR_FILE
extern int flag_lifetime_dse;
#else
  int x_flag_lifetime_dse;
#define flag_lifetime_dse global_options.x_flag_lifetime_dse
#endif
#ifdef GENERATOR_FILE
extern int flag_limit_function_alignment;
#else
  int x_flag_limit_function_alignment;
#define flag_limit_function_alignment global_options.x_flag_limit_function_alignment
#endif
#ifdef GENERATOR_FILE
extern enum lto_linker_output flag_lto_linker_output;
#else
  enum lto_linker_output x_flag_lto_linker_output;
#define flag_lto_linker_output global_options.x_flag_lto_linker_output
#endif
#ifdef GENERATOR_FILE
extern int flag_live_range_shrinkage;
#else
  int x_flag_live_range_shrinkage;
#define flag_live_range_shrinkage global_options.x_flag_live_range_shrinkage
#endif
#ifdef GENERATOR_FILE
extern int flag_local_ivars;
#else
  int x_flag_local_ivars;
#define flag_local_ivars global_options.x_flag_local_ivars
#endif
#ifdef GENERATOR_FILE
extern int flag_loop_interchange;
#else
  int x_flag_loop_interchange;
#define flag_loop_interchange global_options.x_flag_loop_interchange
#endif
#ifdef GENERATOR_FILE
extern int flag_loop_nest_optimize;
#else
  int x_flag_loop_nest_optimize;
#define flag_loop_nest_optimize global_options.x_flag_loop_nest_optimize
#endif
#ifdef GENERATOR_FILE
extern int flag_loop_parallelize_all;
#else
  int x_flag_loop_parallelize_all;
#define flag_loop_parallelize_all global_options.x_flag_loop_parallelize_all
#endif
#ifdef GENERATOR_FILE
extern int flag_unroll_jam;
#else
  int x_flag_unroll_jam;
#define flag_unroll_jam global_options.x_flag_unroll_jam
#endif
#ifdef GENERATOR_FILE
extern int flag_lra_remat;
#else
  int x_flag_lra_remat;
#define flag_lra_remat global_options.x_flag_lra_remat
#endif
#ifdef GENERATOR_FILE
extern int flag_lto_compression_level;
#else
  int x_flag_lto_compression_level;
#define flag_lto_compression_level global_options.x_flag_lto_compression_level
#endif
#ifdef GENERATOR_FILE
extern int flag_lto_odr_type_mering;
#else
  int x_flag_lto_odr_type_mering;
#define flag_lto_odr_type_mering global_options.x_flag_lto_odr_type_mering
#endif
#ifdef GENERATOR_FILE
extern enum lto_partition_model flag_lto_partition;
#else
  enum lto_partition_model x_flag_lto_partition;
#define flag_lto_partition global_options.x_flag_lto_partition
#endif
#ifdef GENERATOR_FILE
extern int flag_lto_report;
#else
  int x_flag_lto_report;
#define flag_lto_report global_options.x_flag_lto_report
#endif
#ifdef GENERATOR_FILE
extern int flag_lto_report_wpa;
#else
  int x_flag_lto_report_wpa;
#define flag_lto_report_wpa global_options.x_flag_lto_report_wpa
#endif
#ifdef GENERATOR_FILE
extern const char *flag_lto;
#else
  const char *x_flag_lto;
#define flag_lto global_options.x_flag_lto
#endif
#ifdef GENERATOR_FILE
extern int flag_ltrans;
#else
  int x_flag_ltrans;
#define flag_ltrans global_options.x_flag_ltrans
#endif
#ifdef GENERATOR_FILE
extern const char *ltrans_output_list;
#else
  const char *x_ltrans_output_list;
#define ltrans_output_list global_options.x_ltrans_output_list
#endif
#ifdef GENERATOR_FILE
extern int flag_errno_math;
#else
  int x_flag_errno_math;
#define flag_errno_math global_options.x_flag_errno_math
#endif
#ifdef GENERATOR_FILE
extern int flag_max_array_constructor;
#else
  int x_flag_max_array_constructor;
#define flag_max_array_constructor global_options.x_flag_max_array_constructor
#endif
#ifdef GENERATOR_FILE
extern int flag_max_errors;
#else
  int x_flag_max_errors;
#define flag_max_errors global_options.x_flag_max_errors
#endif
#ifdef GENERATOR_FILE
extern int flag_max_stack_var_size;
#else
  int x_flag_max_stack_var_size;
#define flag_max_stack_var_size global_options.x_flag_max_stack_var_size
#endif
#ifdef GENERATOR_FILE
extern int flag_max_subrecord_length;
#else
  int x_flag_max_subrecord_length;
#define flag_max_subrecord_length global_options.x_flag_max_subrecord_length
#endif
#ifdef GENERATOR_FILE
extern int mem_report;
#else
  int x_mem_report;
#define mem_report global_options.x_mem_report
#endif
#ifdef GENERATOR_FILE
extern int mem_report_wpa;
#else
  int x_mem_report_wpa;
#define mem_report_wpa global_options.x_mem_report_wpa
#endif
#ifdef GENERATOR_FILE
extern int flag_merge_constants;
#else
  int x_flag_merge_constants;
#define flag_merge_constants global_options.x_flag_merge_constants
#endif
#ifdef GENERATOR_FILE
extern int flag_merge_debug_strings;
#else
  int x_flag_merge_debug_strings;
#define flag_merge_debug_strings global_options.x_flag_merge_debug_strings
#endif
#ifdef GENERATOR_FILE
extern int flag_module_private;
#else
  int x_flag_module_private;
#define flag_module_private global_options.x_flag_module_private
#endif
#ifdef GENERATOR_FILE
extern int flag_modulo_sched;
#else
  int x_flag_modulo_sched;
#define flag_modulo_sched global_options.x_flag_modulo_sched
#endif
#ifdef GENERATOR_FILE
extern int flag_modulo_sched_allow_regmoves;
#else
  int x_flag_modulo_sched_allow_regmoves;
#define flag_modulo_sched_allow_regmoves global_options.x_flag_modulo_sched_allow_regmoves
#endif
#ifdef GENERATOR_FILE
extern int flag_move_loop_invariants;
#else
  int x_flag_move_loop_invariants;
#define flag_move_loop_invariants global_options.x_flag_move_loop_invariants
#endif
#ifdef GENERATOR_FILE
extern int flag_ms_extensions;
#else
  int x_flag_ms_extensions;
#define flag_ms_extensions global_options.x_flag_ms_extensions
#endif
#ifdef GENERATOR_FILE
extern int flag_new_inheriting_ctors;
#else
  int x_flag_new_inheriting_ctors;
#define flag_new_inheriting_ctors global_options.x_flag_new_inheriting_ctors
#endif
#ifdef GENERATOR_FILE
extern int flag_new_ttp;
#else
  int x_flag_new_ttp;
#define flag_new_ttp global_options.x_flag_new_ttp
#endif
#ifdef GENERATOR_FILE
extern int flag_nil_receivers;
#else
  int x_flag_nil_receivers;
#define flag_nil_receivers global_options.x_flag_nil_receivers
#endif
#ifdef GENERATOR_FILE
extern int flag_non_call_exceptions;
#else
  int x_flag_non_call_exceptions;
#define flag_non_call_exceptions global_options.x_flag_non_call_exceptions
#endif
#ifdef GENERATOR_FILE
extern int flag_no_nonansi_builtin;
#else
  int x_flag_no_nonansi_builtin;
#define flag_no_nonansi_builtin global_options.x_flag_no_nonansi_builtin
#endif
#ifdef GENERATOR_FILE
extern int flag_nothrow_opt;
#else
  int x_flag_nothrow_opt;
#define flag_nothrow_opt global_options.x_flag_nothrow_opt
#endif
#ifdef GENERATOR_FILE
extern int flag_objc_abi;
#else
  int x_flag_objc_abi;
#define flag_objc_abi global_options.x_flag_objc_abi
#endif
#ifdef GENERATOR_FILE
extern int flag_objc_call_cxx_cdtors;
#else
  int x_flag_objc_call_cxx_cdtors;
#define flag_objc_call_cxx_cdtors global_options.x_flag_objc_call_cxx_cdtors
#endif
#ifdef GENERATOR_FILE
extern int flag_objc_direct_dispatch;
#else
  int x_flag_objc_direct_dispatch;
#define flag_objc_direct_dispatch global_options.x_flag_objc_direct_dispatch
#endif
#ifdef GENERATOR_FILE
extern int flag_objc_exceptions;
#else
  int x_flag_objc_exceptions;
#define flag_objc_exceptions global_options.x_flag_objc_exceptions
#endif
#ifdef GENERATOR_FILE
extern int flag_objc_gc;
#else
  int x_flag_objc_gc;
#define flag_objc_gc global_options.x_flag_objc_gc
#endif
#ifdef GENERATOR_FILE
extern int flag_objc_nilcheck;
#else
  int x_flag_objc_nilcheck;
#define flag_objc_nilcheck global_options.x_flag_objc_nilcheck
#endif
#ifdef GENERATOR_FILE
extern int flag_objc_sjlj_exceptions;
#else
  int x_flag_objc_sjlj_exceptions;
#define flag_objc_sjlj_exceptions global_options.x_flag_objc_sjlj_exceptions
#endif
#ifdef GENERATOR_FILE
extern int flag_objc1_only;
#else
  int x_flag_objc1_only;
#define flag_objc1_only global_options.x_flag_objc1_only
#endif
#ifdef GENERATOR_FILE
extern enum offload_abi flag_offload_abi;
#else
  enum offload_abi x_flag_offload_abi;
#define flag_offload_abi global_options.x_flag_offload_abi
#endif
#ifdef GENERATOR_FILE
extern int flag_omit_frame_pointer;
#else
  int x_flag_omit_frame_pointer;
#define flag_omit_frame_pointer global_options.x_flag_omit_frame_pointer
#endif
#ifdef GENERATOR_FILE
extern int flag_openacc;
#else
  int x_flag_openacc;
#define flag_openacc global_options.x_flag_openacc
#endif
#ifdef GENERATOR_FILE
extern const char *flag_openacc_dims;
#else
  const char *x_flag_openacc_dims;
#define flag_openacc_dims global_options.x_flag_openacc_dims
#endif
#ifdef GENERATOR_FILE
extern int flag_openmp;
#else
  int x_flag_openmp;
#define flag_openmp global_options.x_flag_openmp
#endif
#ifdef GENERATOR_FILE
extern int flag_openmp_simd;
#else
  int x_flag_openmp_simd;
#define flag_openmp_simd global_options.x_flag_openmp_simd
#endif
#ifdef GENERATOR_FILE
extern int flag_opt_info;
#else
  int x_flag_opt_info;
#define flag_opt_info global_options.x_flag_opt_info
#endif
#ifdef GENERATOR_FILE
extern int flag_optimize_sibling_calls;
#else
  int x_flag_optimize_sibling_calls;
#define flag_optimize_sibling_calls global_options.x_flag_optimize_sibling_calls
#endif
#ifdef GENERATOR_FILE
extern int flag_optimize_strlen;
#else
  int x_flag_optimize_strlen;
#define flag_optimize_strlen global_options.x_flag_optimize_strlen
#endif
#ifdef GENERATOR_FILE
extern int flag_pack_derived;
#else
  int x_flag_pack_derived;
#define flag_pack_derived global_options.x_flag_pack_derived
#endif
#ifdef GENERATOR_FILE
extern int flag_pack_struct;
#else
  int x_flag_pack_struct;
#define flag_pack_struct global_options.x_flag_pack_struct
#endif
#ifdef GENERATOR_FILE
extern int flag_partial_inlining;
#else
  int x_flag_partial_inlining;
#define flag_partial_inlining global_options.x_flag_partial_inlining
#endif
#ifdef GENERATOR_FILE
extern int flag_pcc_struct_return;
#else
  int x_flag_pcc_struct_return;
#define flag_pcc_struct_return global_options.x_flag_pcc_struct_return
#endif
#ifdef GENERATOR_FILE
extern int flag_peel_loops;
#else
  int x_flag_peel_loops;
#define flag_peel_loops global_options.x_flag_peel_loops
#endif
#ifdef GENERATOR_FILE
extern int flag_no_peephole;
#else
  int x_flag_no_peephole;
#define flag_no_peephole global_options.x_flag_no_peephole
#endif
#ifdef GENERATOR_FILE
extern int flag_peephole2;
#else
  int x_flag_peephole2;
#define flag_peephole2 global_options.x_flag_peephole2
#endif
#ifdef GENERATOR_FILE
extern int flag_permissive;
#else
  int x_flag_permissive;
#define flag_permissive global_options.x_flag_permissive
#endif
#ifdef GENERATOR_FILE
extern enum permitted_flt_eval_methods flag_permitted_flt_eval_methods;
#else
  enum permitted_flt_eval_methods x_flag_permitted_flt_eval_methods;
#define flag_permitted_flt_eval_methods global_options.x_flag_permitted_flt_eval_methods
#endif
#ifdef GENERATOR_FILE
extern int flag_plan9_extensions;
#else
  int x_flag_plan9_extensions;
#define flag_plan9_extensions global_options.x_flag_plan9_extensions
#endif
#ifdef GENERATOR_FILE
extern int flag_plt;
#else
  int x_flag_plt;
#define flag_plt global_options.x_flag_plt
#endif
#ifdef GENERATOR_FILE
extern int post_ipa_mem_report;
#else
  int x_post_ipa_mem_report;
#define post_ipa_mem_report global_options.x_post_ipa_mem_report
#endif
#ifdef GENERATOR_FILE
extern int pre_ipa_mem_report;
#else
  int x_pre_ipa_mem_report;
#define pre_ipa_mem_report global_options.x_pre_ipa_mem_report
#endif
#ifdef GENERATOR_FILE
extern int flag_predictive_commoning;
#else
  int x_flag_predictive_commoning;
#define flag_predictive_commoning global_options.x_flag_predictive_commoning
#endif
#ifdef GENERATOR_FILE
extern int flag_prefetch_loop_arrays;
#else
  int x_flag_prefetch_loop_arrays;
#define flag_prefetch_loop_arrays global_options.x_flag_prefetch_loop_arrays
#endif
#ifdef GENERATOR_FILE
extern int flag_pretty_templates;
#else
  int x_flag_pretty_templates;
#define flag_pretty_templates global_options.x_flag_pretty_templates
#endif
#ifdef GENERATOR_FILE
extern int flag_printf_return_value;
#else
  int x_flag_printf_return_value;
#define flag_printf_return_value global_options.x_flag_printf_return_value
#endif
#ifdef GENERATOR_FILE
extern int profile_flag;
#else
  int x_profile_flag;
#define profile_flag global_options.x_profile_flag
#endif
#ifdef GENERATOR_FILE
extern int profile_abs_path_flag;
#else
  int x_profile_abs_path_flag;
#define profile_abs_path_flag global_options.x_profile_abs_path_flag
#endif
#ifdef GENERATOR_FILE
extern int profile_arc_flag;
#else
  int x_profile_arc_flag;
#define profile_arc_flag global_options.x_profile_arc_flag
#endif
#ifdef GENERATOR_FILE
extern int flag_profile_correction;
#else
  int x_flag_profile_correction;
#define flag_profile_correction global_options.x_flag_profile_correction
#endif
#ifdef GENERATOR_FILE
extern const char *profile_data_prefix;
#else
  const char *x_profile_data_prefix;
#define profile_data_prefix global_options.x_profile_data_prefix
#endif
#ifdef GENERATOR_FILE
extern int flag_profile_reorder_functions;
#else
  int x_flag_profile_reorder_functions;
#define flag_profile_reorder_functions global_options.x_flag_profile_reorder_functions
#endif
#ifdef GENERATOR_FILE
extern int profile_report;
#else
  int x_profile_report;
#define profile_report global_options.x_profile_report
#endif
#ifdef GENERATOR_FILE
extern enum profile_update flag_profile_update;
#else
  enum profile_update x_flag_profile_update;
#define flag_profile_update global_options.x_flag_profile_update
#endif
#ifdef GENERATOR_FILE
extern int flag_profile_use;
#else
  int x_flag_profile_use;
#define flag_profile_use global_options.x_flag_profile_use
#endif
#ifdef GENERATOR_FILE
extern int flag_profile_values;
#else
  int x_flag_profile_values;
#define flag_profile_values global_options.x_flag_profile_values
#endif
#ifdef GENERATOR_FILE
extern int flag_protect_parens;
#else
  int x_flag_protect_parens;
#define flag_protect_parens global_options.x_flag_protect_parens
#endif
#ifdef GENERATOR_FILE
extern int flag_range_check;
#else
  int x_flag_range_check;
#define flag_range_check global_options.x_flag_range_check
#endif
#ifdef GENERATOR_FILE
extern int flag_real4_kind;
#else
  int x_flag_real4_kind;
#define flag_real4_kind global_options.x_flag_real4_kind
#endif
#ifdef GENERATOR_FILE
extern int flag_real8_kind;
#else
  int x_flag_real8_kind;
#define flag_real8_kind global_options.x_flag_real8_kind
#endif
#ifdef GENERATOR_FILE
extern int flag_realloc_lhs;
#else
  int x_flag_realloc_lhs;
#define flag_realloc_lhs global_options.x_flag_realloc_lhs
#endif
#ifdef GENERATOR_FILE
extern int flag_reciprocal_math;
#else
  int x_flag_reciprocal_math;
#define flag_reciprocal_math global_options.x_flag_reciprocal_math
#endif
#ifdef GENERATOR_FILE
extern int flag_record_gcc_switches;
#else
  int x_flag_record_gcc_switches;
#define flag_record_gcc_switches global_options.x_flag_record_gcc_switches
#endif
#ifdef GENERATOR_FILE
extern int flag_record_marker;
#else
  int x_flag_record_marker;
#define flag_record_marker global_options.x_flag_record_marker
#endif
#ifdef GENERATOR_FILE
extern int flag_recursive;
#else
  int x_flag_recursive;
#define flag_recursive global_options.x_flag_recursive
#endif
#ifdef GENERATOR_FILE
extern int flag_ree;
#else
  int x_flag_ree;
#define flag_ree global_options.x_flag_ree
#endif
#ifdef GENERATOR_FILE
extern int flag_rename_registers;
#else
  int x_flag_rename_registers;
#define flag_rename_registers global_options.x_flag_rename_registers
#endif
#ifdef GENERATOR_FILE
extern int flag_reorder_blocks;
#else
  int x_flag_reorder_blocks;
#define flag_reorder_blocks global_options.x_flag_reorder_blocks
#endif
#ifdef GENERATOR_FILE
extern enum reorder_blocks_algorithm flag_reorder_blocks_algorithm;
#else
  enum reorder_blocks_algorithm x_flag_reorder_blocks_algorithm;
#define flag_reorder_blocks_algorithm global_options.x_flag_reorder_blocks_algorithm
#endif
#ifdef GENERATOR_FILE
extern int flag_reorder_blocks_and_partition;
#else
  int x_flag_reorder_blocks_and_partition;
#define flag_reorder_blocks_and_partition global_options.x_flag_reorder_blocks_and_partition
#endif
#ifdef GENERATOR_FILE
extern int flag_reorder_functions;
#else
  int x_flag_reorder_functions;
#define flag_reorder_functions global_options.x_flag_reorder_functions
#endif
#ifdef GENERATOR_FILE
extern int flag_repack_arrays;
#else
  int x_flag_repack_arrays;
#define flag_repack_arrays global_options.x_flag_repack_arrays
#endif
#ifdef GENERATOR_FILE
extern int flag_replace_objc_classes;
#else
  int x_flag_replace_objc_classes;
#define flag_replace_objc_classes global_options.x_flag_replace_objc_classes
#endif
#ifdef GENERATOR_FILE
extern int flag_report_bug;
#else
  int x_flag_report_bug;
#define flag_report_bug global_options.x_flag_report_bug
#endif
#ifdef GENERATOR_FILE
extern int go_require_return_statement;
#else
  int x_go_require_return_statement;
#define go_require_return_statement global_options.x_go_require_return_statement
#endif
#ifdef GENERATOR_FILE
extern int flag_rerun_cse_after_loop;
#else
  int x_flag_rerun_cse_after_loop;
#define flag_rerun_cse_after_loop global_options.x_flag_rerun_cse_after_loop
#endif
#ifdef GENERATOR_FILE
extern int flag_resched_modulo_sched;
#else
  int x_flag_resched_modulo_sched;
#define flag_resched_modulo_sched global_options.x_flag_resched_modulo_sched
#endif
#ifdef GENERATOR_FILE
extern int flag_rounding_math;
#else
  int x_flag_rounding_math;
#define flag_rounding_math global_options.x_flag_rounding_math
#endif
#ifdef GENERATOR_FILE
extern int flag_rtti;
#else
  int x_flag_rtti;
#define flag_rtti global_options.x_flag_rtti
#endif
#ifdef GENERATOR_FILE
extern int flag_sanitize_address_use_after_scope;
#else
  int x_flag_sanitize_address_use_after_scope;
#define flag_sanitize_address_use_after_scope global_options.x_flag_sanitize_address_use_after_scope
#endif
#ifdef GENERATOR_FILE
extern int flag_sanitize_undefined_trap_on_error;
#else
  int x_flag_sanitize_undefined_trap_on_error;
#define flag_sanitize_undefined_trap_on_error global_options.x_flag_sanitize_undefined_trap_on_error
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_critical_path_heuristic;
#else
  int x_flag_sched_critical_path_heuristic;
#define flag_sched_critical_path_heuristic global_options.x_flag_sched_critical_path_heuristic
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_dep_count_heuristic;
#else
  int x_flag_sched_dep_count_heuristic;
#define flag_sched_dep_count_heuristic global_options.x_flag_sched_dep_count_heuristic
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_group_heuristic;
#else
  int x_flag_sched_group_heuristic;
#define flag_sched_group_heuristic global_options.x_flag_sched_group_heuristic
#endif
#ifdef GENERATOR_FILE
extern int flag_schedule_interblock;
#else
  int x_flag_schedule_interblock;
#define flag_schedule_interblock global_options.x_flag_schedule_interblock
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_last_insn_heuristic;
#else
  int x_flag_sched_last_insn_heuristic;
#define flag_sched_last_insn_heuristic global_options.x_flag_sched_last_insn_heuristic
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_pressure;
#else
  int x_flag_sched_pressure;
#define flag_sched_pressure global_options.x_flag_sched_pressure
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_rank_heuristic;
#else
  int x_flag_sched_rank_heuristic;
#define flag_sched_rank_heuristic global_options.x_flag_sched_rank_heuristic
#endif
#ifdef GENERATOR_FILE
extern int flag_schedule_speculative;
#else
  int x_flag_schedule_speculative;
#define flag_schedule_speculative global_options.x_flag_schedule_speculative
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_spec_insn_heuristic;
#else
  int x_flag_sched_spec_insn_heuristic;
#define flag_sched_spec_insn_heuristic global_options.x_flag_sched_spec_insn_heuristic
#endif
#ifdef GENERATOR_FILE
extern int flag_schedule_speculative_load;
#else
  int x_flag_schedule_speculative_load;
#define flag_schedule_speculative_load global_options.x_flag_schedule_speculative_load
#endif
#ifdef GENERATOR_FILE
extern int flag_schedule_speculative_load_dangerous;
#else
  int x_flag_schedule_speculative_load_dangerous;
#define flag_schedule_speculative_load_dangerous global_options.x_flag_schedule_speculative_load_dangerous
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_stalled_insns;
#else
  int x_flag_sched_stalled_insns;
#define flag_sched_stalled_insns global_options.x_flag_sched_stalled_insns
#endif
#ifdef GENERATOR_FILE
extern int flag_sched_stalled_insns_dep;
#else
  int x_flag_sched_stalled_insns_dep;
#define flag_sched_stalled_insns_dep global_options.x_flag_sched_stalled_insns_dep
#endif
#ifdef GENERATOR_FILE
extern int sched_verbose_param;
#else
  int x_sched_verbose_param;
#define sched_verbose_param global_options.x_sched_verbose_param
#endif
#ifdef GENERATOR_FILE
extern int flag_sched2_use_superblocks;
#else
  int x_flag_sched2_use_superblocks;
#define flag_sched2_use_superblocks global_options.x_flag_sched2_use_superblocks
#endif
#ifdef GENERATOR_FILE
extern int flag_schedule_fusion;
#else
  int x_flag_schedule_fusion;
#define flag_schedule_fusion global_options.x_flag_schedule_fusion
#endif
#ifdef GENERATOR_FILE
extern int flag_schedule_insns;
#else
  int x_flag_schedule_insns;
#define flag_schedule_insns global_options.x_flag_schedule_insns
#endif
#ifdef GENERATOR_FILE
extern int flag_schedule_insns_after_reload;
#else
  int x_flag_schedule_insns_after_reload;
#define flag_schedule_insns_after_reload global_options.x_flag_schedule_insns_after_reload
#endif
#ifdef GENERATOR_FILE
extern int flag_second_underscore;
#else
  int x_flag_second_underscore;
#define flag_second_underscore global_options.x_flag_second_underscore
#endif
#ifdef GENERATOR_FILE
extern int flag_section_anchors;
#else
  int x_flag_section_anchors;
#define flag_section_anchors global_options.x_flag_section_anchors
#endif
#ifdef GENERATOR_FILE
extern int flag_sel_sched_pipelining;
#else
  int x_flag_sel_sched_pipelining;
#define flag_sel_sched_pipelining global_options.x_flag_sel_sched_pipelining
#endif
#ifdef GENERATOR_FILE
extern int flag_sel_sched_pipelining_outer_loops;
#else
  int x_flag_sel_sched_pipelining_outer_loops;
#define flag_sel_sched_pipelining_outer_loops global_options.x_flag_sel_sched_pipelining_outer_loops
#endif
#ifdef GENERATOR_FILE
extern int flag_sel_sched_reschedule_pipelined;
#else
  int x_flag_sel_sched_reschedule_pipelined;
#define flag_sel_sched_reschedule_pipelined global_options.x_flag_sel_sched_reschedule_pipelined
#endif
#ifdef GENERATOR_FILE
extern int flag_selective_scheduling;
#else
  int x_flag_selective_scheduling;
#define flag_selective_scheduling global_options.x_flag_selective_scheduling
#endif
#ifdef GENERATOR_FILE
extern int flag_selective_scheduling2;
#else
  int x_flag_selective_scheduling2;
#define flag_selective_scheduling2 global_options.x_flag_selective_scheduling2
#endif
#ifdef GENERATOR_FILE
extern const char *flag_self_test;
#else
  const char *x_flag_self_test;
#define flag_self_test global_options.x_flag_self_test
#endif
#ifdef GENERATOR_FILE
extern int flag_semantic_interposition;
#else
  int x_flag_semantic_interposition;
#define flag_semantic_interposition global_options.x_flag_semantic_interposition
#endif
#ifdef GENERATOR_FILE
extern int flag_short_enums;
#else
  int x_flag_short_enums;
#define flag_short_enums global_options.x_flag_short_enums
#endif
#ifdef GENERATOR_FILE
extern int flag_short_wchar;
#else
  int x_flag_short_wchar;
#define flag_short_wchar global_options.x_flag_short_wchar
#endif
#ifdef GENERATOR_FILE
extern int flag_show_column;
#else
  int x_flag_show_column;
#define flag_show_column global_options.x_flag_show_column
#endif
#ifdef GENERATOR_FILE
extern int flag_shrink_wrap;
#else
  int x_flag_shrink_wrap;
#define flag_shrink_wrap global_options.x_flag_shrink_wrap
#endif
#ifdef GENERATOR_FILE
extern int flag_shrink_wrap_separate;
#else
  int x_flag_shrink_wrap_separate;
#define flag_shrink_wrap_separate global_options.x_flag_shrink_wrap_separate
#endif
#ifdef GENERATOR_FILE
extern int flag_sign_zero;
#else
  int x_flag_sign_zero;
#define flag_sign_zero global_options.x_flag_sign_zero
#endif
#ifdef GENERATOR_FILE
extern int flag_signaling_nans;
#else
  int x_flag_signaling_nans;
#define flag_signaling_nans global_options.x_flag_signaling_nans
#endif
#ifdef GENERATOR_FILE
extern int flag_signed_bitfields;
#else
  int x_flag_signed_bitfields;
#define flag_signed_bitfields global_options.x_flag_signed_bitfields
#endif
#ifdef GENERATOR_FILE
extern int flag_signed_char;
#else
  int x_flag_signed_char;
#define flag_signed_char global_options.x_flag_signed_char
#endif
#ifdef GENERATOR_FILE
extern int flag_signed_zeros;
#else
  int x_flag_signed_zeros;
#define flag_signed_zeros global_options.x_flag_signed_zeros
#endif
#ifdef GENERATOR_FILE
extern enum vect_cost_model flag_simd_cost_model;
#else
  enum vect_cost_model x_flag_simd_cost_model;
#define flag_simd_cost_model global_options.x_flag_simd_cost_model
#endif
#ifdef GENERATOR_FILE
extern int flag_single_precision_constant;
#else
  int x_flag_single_precision_constant;
#define flag_single_precision_constant global_options.x_flag_single_precision_constant
#endif
#ifdef GENERATOR_FILE
extern int flag_sized_deallocation;
#else
  int x_flag_sized_deallocation;
#define flag_sized_deallocation global_options.x_flag_sized_deallocation
#endif
#ifdef GENERATOR_FILE
extern int flag_split_ivs_in_unroller;
#else
  int x_flag_split_ivs_in_unroller;
#define flag_split_ivs_in_unroller global_options.x_flag_split_ivs_in_unroller
#endif
#ifdef GENERATOR_FILE
extern int flag_split_loops;
#else
  int x_flag_split_loops;
#define flag_split_loops global_options.x_flag_split_loops
#endif
#ifdef GENERATOR_FILE
extern int flag_split_paths;
#else
  int x_flag_split_paths;
#define flag_split_paths global_options.x_flag_split_paths
#endif
#ifdef GENERATOR_FILE
extern int flag_split_stack;
#else
  int x_flag_split_stack;
#define flag_split_stack global_options.x_flag_split_stack
#endif
#ifdef GENERATOR_FILE
extern int flag_split_wide_types;
#else
  int x_flag_split_wide_types;
#define flag_split_wide_types global_options.x_flag_split_wide_types
#endif
#ifdef GENERATOR_FILE
extern int flag_ssa_backprop;
#else
  int x_flag_ssa_backprop;
#define flag_ssa_backprop global_options.x_flag_ssa_backprop
#endif
#ifdef GENERATOR_FILE
extern int flag_ssa_phiopt;
#else
  int x_flag_ssa_phiopt;
#define flag_ssa_phiopt global_options.x_flag_ssa_phiopt
#endif
#ifdef GENERATOR_FILE
extern enum scalar_storage_order_kind default_sso;
#else
  enum scalar_storage_order_kind x_default_sso;
#define default_sso global_options.x_default_sso
#endif
#ifdef GENERATOR_FILE
extern int flag_stack_arrays;
#else
  int x_flag_stack_arrays;
#define flag_stack_arrays global_options.x_flag_stack_arrays
#endif
#ifdef GENERATOR_FILE
extern int flag_stack_clash_protection;
#else
  int x_flag_stack_clash_protection;
#define flag_stack_clash_protection global_options.x_flag_stack_clash_protection
#endif
#ifdef GENERATOR_FILE
extern int flag_stack_protect;
#else
  int x_flag_stack_protect;
#define flag_stack_protect global_options.x_flag_stack_protect
#endif
#ifdef GENERATOR_FILE
extern enum stack_reuse_level flag_stack_reuse;
#else
  enum stack_reuse_level x_flag_stack_reuse;
#define flag_stack_reuse global_options.x_flag_stack_reuse
#endif
#ifdef GENERATOR_FILE
extern int flag_stack_usage;
#else
  int x_flag_stack_usage;
#define flag_stack_usage global_options.x_flag_stack_usage
#endif
#ifdef GENERATOR_FILE
extern int flag_detailed_statistics;
#else
  int x_flag_detailed_statistics;
#define flag_detailed_statistics global_options.x_flag_detailed_statistics
#endif
#ifdef GENERATOR_FILE
extern int flag_stdarg_opt;
#else
  int x_flag_stdarg_opt;
#define flag_stdarg_opt global_options.x_flag_stdarg_opt
#endif
#ifdef GENERATOR_FILE
extern int flag_store_merging;
#else
  int x_flag_store_merging;
#define flag_store_merging global_options.x_flag_store_merging
#endif
#ifdef GENERATOR_FILE
extern int flag_strict_aliasing;
#else
  int x_flag_strict_aliasing;
#define flag_strict_aliasing global_options.x_flag_strict_aliasing
#endif
#ifdef GENERATOR_FILE
extern int flag_strict_enums;
#else
  int x_flag_strict_enums;
#define flag_strict_enums global_options.x_flag_strict_enums
#endif
#ifdef GENERATOR_FILE
extern int flag_strict_volatile_bitfields;
#else
  int x_flag_strict_volatile_bitfields;
#define flag_strict_volatile_bitfields global_options.x_flag_strict_volatile_bitfields
#endif
#ifdef GENERATOR_FILE
extern int flag_strong_eval_order;
#else
  int x_flag_strong_eval_order;
#define flag_strong_eval_order global_options.x_flag_strong_eval_order
#endif
#ifdef GENERATOR_FILE
extern int flag_sync_libcalls;
#else
  int x_flag_sync_libcalls;
#define flag_sync_libcalls global_options.x_flag_sync_libcalls
#endif
#ifdef GENERATOR_FILE
extern int flag_syntax_only;
#else
  int x_flag_syntax_only;
#define flag_syntax_only global_options.x_flag_syntax_only
#endif
#ifdef GENERATOR_FILE
extern int template_backtrace_limit;
#else
  int x_template_backtrace_limit;
#define template_backtrace_limit global_options.x_template_backtrace_limit
#endif
#ifdef GENERATOR_FILE
extern int flag_test_coverage;
#else
  int x_flag_test_coverage;
#define flag_test_coverage global_options.x_flag_test_coverage
#endif
#ifdef GENERATOR_FILE
extern int flag_test_forall_temp;
#else
  int x_flag_test_forall_temp;
#define flag_test_forall_temp global_options.x_flag_test_forall_temp
#endif
#ifdef GENERATOR_FILE
extern int flag_thread_jumps;
#else
  int x_flag_thread_jumps;
#define flag_thread_jumps global_options.x_flag_thread_jumps
#endif
#ifdef GENERATOR_FILE
extern int flag_threadsafe_statics;
#else
  int x_flag_threadsafe_statics;
#define flag_threadsafe_statics global_options.x_flag_threadsafe_statics
#endif
#ifdef GENERATOR_FILE
extern int time_report;
#else
  int x_time_report;
#define time_report global_options.x_time_report
#endif
#ifdef GENERATOR_FILE
extern int time_report_details;
#else
  int x_time_report_details;
#define time_report_details global_options.x_time_report_details
#endif
#ifdef GENERATOR_FILE
extern enum tls_model flag_tls_default;
#else
  enum tls_model x_flag_tls_default;
#define flag_tls_default global_options.x_flag_tls_default
#endif
#ifdef GENERATOR_FILE
extern int flag_toplevel_reorder;
#else
  int x_flag_toplevel_reorder;
#define flag_toplevel_reorder global_options.x_flag_toplevel_reorder
#endif
#ifdef GENERATOR_FILE
extern int flag_tracer;
#else
  int x_flag_tracer;
#define flag_tracer global_options.x_flag_tracer
#endif
#ifdef GENERATOR_FILE
extern int flag_trampolines;
#else
  int x_flag_trampolines;
#define flag_trampolines global_options.x_flag_trampolines
#endif
#ifdef GENERATOR_FILE
extern int flag_trapping_math;
#else
  int x_flag_trapping_math;
#define flag_trapping_math global_options.x_flag_trapping_math
#endif
#ifdef GENERATOR_FILE
extern int flag_trapv;
#else
  int x_flag_trapv;
#define flag_trapv global_options.x_flag_trapv
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_bit_ccp;
#else
  int x_flag_tree_bit_ccp;
#define flag_tree_bit_ccp global_options.x_flag_tree_bit_ccp
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_builtin_call_dce;
#else
  int x_flag_tree_builtin_call_dce;
#define flag_tree_builtin_call_dce global_options.x_flag_tree_builtin_call_dce
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_ccp;
#else
  int x_flag_tree_ccp;
#define flag_tree_ccp global_options.x_flag_tree_ccp
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_ch;
#else
  int x_flag_tree_ch;
#define flag_tree_ch global_options.x_flag_tree_ch
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_coalesce_vars;
#else
  int x_flag_tree_coalesce_vars;
#define flag_tree_coalesce_vars global_options.x_flag_tree_coalesce_vars
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_copy_prop;
#else
  int x_flag_tree_copy_prop;
#define flag_tree_copy_prop global_options.x_flag_tree_copy_prop
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_cselim;
#else
  int x_flag_tree_cselim;
#define flag_tree_cselim global_options.x_flag_tree_cselim
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_dce;
#else
  int x_flag_tree_dce;
#define flag_tree_dce global_options.x_flag_tree_dce
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_dom;
#else
  int x_flag_tree_dom;
#define flag_tree_dom global_options.x_flag_tree_dom
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_dse;
#else
  int x_flag_tree_dse;
#define flag_tree_dse global_options.x_flag_tree_dse
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_forwprop;
#else
  int x_flag_tree_forwprop;
#define flag_tree_forwprop global_options.x_flag_tree_forwprop
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_fre;
#else
  int x_flag_tree_fre;
#define flag_tree_fre global_options.x_flag_tree_fre
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_loop_distribute_patterns;
#else
  int x_flag_tree_loop_distribute_patterns;
#define flag_tree_loop_distribute_patterns global_options.x_flag_tree_loop_distribute_patterns
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_loop_distribution;
#else
  int x_flag_tree_loop_distribution;
#define flag_tree_loop_distribution global_options.x_flag_tree_loop_distribution
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_loop_if_convert;
#else
  int x_flag_tree_loop_if_convert;
#define flag_tree_loop_if_convert global_options.x_flag_tree_loop_if_convert
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_loop_im;
#else
  int x_flag_tree_loop_im;
#define flag_tree_loop_im global_options.x_flag_tree_loop_im
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_loop_ivcanon;
#else
  int x_flag_tree_loop_ivcanon;
#define flag_tree_loop_ivcanon global_options.x_flag_tree_loop_ivcanon
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_loop_optimize;
#else
  int x_flag_tree_loop_optimize;
#define flag_tree_loop_optimize global_options.x_flag_tree_loop_optimize
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_loop_vectorize;
#else
  int x_flag_tree_loop_vectorize;
#define flag_tree_loop_vectorize global_options.x_flag_tree_loop_vectorize
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_live_range_split;
#else
  int x_flag_tree_live_range_split;
#define flag_tree_live_range_split global_options.x_flag_tree_live_range_split
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_parallelize_loops;
#else
  int x_flag_tree_parallelize_loops;
#define flag_tree_parallelize_loops global_options.x_flag_tree_parallelize_loops
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_partial_pre;
#else
  int x_flag_tree_partial_pre;
#define flag_tree_partial_pre global_options.x_flag_tree_partial_pre
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_phiprop;
#else
  int x_flag_tree_phiprop;
#define flag_tree_phiprop global_options.x_flag_tree_phiprop
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_pre;
#else
  int x_flag_tree_pre;
#define flag_tree_pre global_options.x_flag_tree_pre
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_pta;
#else
  int x_flag_tree_pta;
#define flag_tree_pta global_options.x_flag_tree_pta
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_reassoc;
#else
  int x_flag_tree_reassoc;
#define flag_tree_reassoc global_options.x_flag_tree_reassoc
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_scev_cprop;
#else
  int x_flag_tree_scev_cprop;
#define flag_tree_scev_cprop global_options.x_flag_tree_scev_cprop
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_sink;
#else
  int x_flag_tree_sink;
#define flag_tree_sink global_options.x_flag_tree_sink
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_slp_vectorize;
#else
  int x_flag_tree_slp_vectorize;
#define flag_tree_slp_vectorize global_options.x_flag_tree_slp_vectorize
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_slsr;
#else
  int x_flag_tree_slsr;
#define flag_tree_slsr global_options.x_flag_tree_slsr
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_sra;
#else
  int x_flag_tree_sra;
#define flag_tree_sra global_options.x_flag_tree_sra
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_switch_conversion;
#else
  int x_flag_tree_switch_conversion;
#define flag_tree_switch_conversion global_options.x_flag_tree_switch_conversion
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_tail_merge;
#else
  int x_flag_tree_tail_merge;
#define flag_tree_tail_merge global_options.x_flag_tree_tail_merge
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_ter;
#else
  int x_flag_tree_ter;
#define flag_tree_ter global_options.x_flag_tree_ter
#endif
#ifdef GENERATOR_FILE
extern int flag_tree_vrp;
#else
  int x_flag_tree_vrp;
#define flag_tree_vrp global_options.x_flag_tree_vrp
#endif
#ifdef GENERATOR_FILE
extern int flag_unconstrained_commons;
#else
  int x_flag_unconstrained_commons;
#define flag_unconstrained_commons global_options.x_flag_unconstrained_commons
#endif
#ifdef GENERATOR_FILE
extern int flag_underscoring;
#else
  int x_flag_underscoring;
#define flag_underscoring global_options.x_flag_underscoring
#endif
#ifdef GENERATOR_FILE
extern int flag_unit_at_a_time;
#else
  int x_flag_unit_at_a_time;
#define flag_unit_at_a_time global_options.x_flag_unit_at_a_time
#endif
#ifdef GENERATOR_FILE
extern int flag_unroll_all_loops;
#else
  int x_flag_unroll_all_loops;
#define flag_unroll_all_loops global_options.x_flag_unroll_all_loops
#endif
#ifdef GENERATOR_FILE
extern int flag_unroll_loops;
#else
  int x_flag_unroll_loops;
#define flag_unroll_loops global_options.x_flag_unroll_loops
#endif
#ifdef GENERATOR_FILE
extern int flag_unsafe_math_optimizations;
#else
  int x_flag_unsafe_math_optimizations;
#define flag_unsafe_math_optimizations global_options.x_flag_unsafe_math_optimizations
#endif
#ifdef GENERATOR_FILE
extern int flag_unswitch_loops;
#else
  int x_flag_unswitch_loops;
#define flag_unswitch_loops global_options.x_flag_unswitch_loops
#endif
#ifdef GENERATOR_FILE
extern int flag_unwind_tables;
#else
  int x_flag_unwind_tables;
#define flag_unwind_tables global_options.x_flag_unwind_tables
#endif
#ifdef GENERATOR_FILE
extern int flag_use_cxa_atexit;
#else
  int x_flag_use_cxa_atexit;
#define flag_use_cxa_atexit global_options.x_flag_use_cxa_atexit
#endif
#ifdef GENERATOR_FILE
extern int flag_use_cxa_get_exception_ptr;
#else
  int x_flag_use_cxa_get_exception_ptr;
#define flag_use_cxa_get_exception_ptr global_options.x_flag_use_cxa_get_exception_ptr
#endif
#ifdef GENERATOR_FILE
extern int flag_use_linker_plugin;
#else
  int x_flag_use_linker_plugin;
#define flag_use_linker_plugin global_options.x_flag_use_linker_plugin
#endif
#ifdef GENERATOR_FILE
extern int flag_var_tracking;
#else
  int x_flag_var_tracking;
#define flag_var_tracking global_options.x_flag_var_tracking
#endif
#ifdef GENERATOR_FILE
extern int flag_var_tracking_assignments;
#else
  int x_flag_var_tracking_assignments;
#define flag_var_tracking_assignments global_options.x_flag_var_tracking_assignments
#endif
#ifdef GENERATOR_FILE
extern int flag_var_tracking_assignments_toggle;
#else
  int x_flag_var_tracking_assignments_toggle;
#define flag_var_tracking_assignments_toggle global_options.x_flag_var_tracking_assignments_toggle
#endif
#ifdef GENERATOR_FILE
extern int flag_var_tracking_uninit;
#else
  int x_flag_var_tracking_uninit;
#define flag_var_tracking_uninit global_options.x_flag_var_tracking_uninit
#endif
#ifdef GENERATOR_FILE
extern int flag_variable_expansion_in_unroller;
#else
  int x_flag_variable_expansion_in_unroller;
#define flag_variable_expansion_in_unroller global_options.x_flag_variable_expansion_in_unroller
#endif
#ifdef GENERATOR_FILE
extern enum vect_cost_model flag_vect_cost_model;
#else
  enum vect_cost_model x_flag_vect_cost_model;
#define flag_vect_cost_model global_options.x_flag_vect_cost_model
#endif
#ifdef GENERATOR_FILE
extern int flag_verbose_asm;
#else
  int x_flag_verbose_asm;
#define flag_verbose_asm global_options.x_flag_verbose_asm
#endif
#ifdef GENERATOR_FILE
extern int flag_visibility_ms_compat;
#else
  int x_flag_visibility_ms_compat;
#define flag_visibility_ms_compat global_options.x_flag_visibility_ms_compat
#endif
#ifdef GENERATOR_FILE
extern enum symbol_visibility default_visibility;
#else
  enum symbol_visibility x_default_visibility;
#define default_visibility global_options.x_default_visibility
#endif
#ifdef GENERATOR_FILE
extern int flag_value_profile_transformations;
#else
  int x_flag_value_profile_transformations;
#define flag_value_profile_transformations global_options.x_flag_value_profile_transformations
#endif
#ifdef GENERATOR_FILE
extern enum vtv_priority flag_vtable_verify;
#else
  enum vtv_priority x_flag_vtable_verify;
#define flag_vtable_verify global_options.x_flag_vtable_verify
#endif
#ifdef GENERATOR_FILE
extern int flag_vtv_counts;
#else
  int x_flag_vtv_counts;
#define flag_vtv_counts global_options.x_flag_vtv_counts
#endif
#ifdef GENERATOR_FILE
extern int flag_vtv_debug;
#else
  int x_flag_vtv_debug;
#define flag_vtv_debug global_options.x_flag_vtv_debug
#endif
#ifdef GENERATOR_FILE
extern int flag_weak;
#else
  int x_flag_weak;
#define flag_weak global_options.x_flag_weak
#endif
#ifdef GENERATOR_FILE
extern int flag_web;
#else
  int x_flag_web;
#define flag_web global_options.x_flag_web
#endif
#ifdef GENERATOR_FILE
extern int flag_whole_program;
#else
  int x_flag_whole_program;
#define flag_whole_program global_options.x_flag_whole_program
#endif
#ifdef GENERATOR_FILE
extern int flag_working_directory;
#else
  int x_flag_working_directory;
#define flag_working_directory global_options.x_flag_working_directory
#endif
#ifdef GENERATOR_FILE
extern const char *flag_wpa;
#else
  const char *x_flag_wpa;
#define flag_wpa global_options.x_flag_wpa
#endif
#ifdef GENERATOR_FILE
extern int flag_wrapv;
#else
  int x_flag_wrapv;
#define flag_wrapv global_options.x_flag_wrapv
#endif
#ifdef GENERATOR_FILE
extern int flag_wrapv_pointer;
#else
  int x_flag_wrapv_pointer;
#define flag_wrapv_pointer global_options.x_flag_wrapv_pointer
#endif
#ifdef GENERATOR_FILE
extern int flag_zero_initialized_in_bss;
#else
  int x_flag_zero_initialized_in_bss;
#define flag_zero_initialized_in_bss global_options.x_flag_zero_initialized_in_bss
#endif
#ifdef GENERATOR_FILE
extern int flag_zero_link;
#else
  int x_flag_zero_link;
#define flag_zero_link global_options.x_flag_zero_link
#endif
#ifdef GENERATOR_FILE
extern int dwarf2out_as_loc_support;
#else
  int x_dwarf2out_as_loc_support;
#define dwarf2out_as_loc_support global_options.x_dwarf2out_as_loc_support
#endif
#ifdef GENERATOR_FILE
extern int dwarf2out_as_locview_support;
#else
  int x_dwarf2out_as_locview_support;
#define dwarf2out_as_locview_support global_options.x_dwarf2out_as_locview_support
#endif
#ifdef GENERATOR_FILE
extern int debug_column_info;
#else
  int x_debug_column_info;
#define debug_column_info global_options.x_debug_column_info
#endif
#ifdef GENERATOR_FILE
extern int dwarf_version;
#else
  int x_dwarf_version;
#define dwarf_version global_options.x_dwarf_version
#endif
#ifdef GENERATOR_FILE
extern int flag_gen_declaration;
#else
  int x_flag_gen_declaration;
#define flag_gen_declaration global_options.x_flag_gen_declaration
#endif
#ifdef GENERATOR_FILE
extern int debug_generate_pub_sections;
#else
  int x_debug_generate_pub_sections;
#define debug_generate_pub_sections global_options.x_debug_generate_pub_sections
#endif
#ifdef GENERATOR_FILE
extern int debug_inline_points;
#else
  int x_debug_inline_points;
#define debug_inline_points global_options.x_debug_inline_points
#endif
#ifdef GENERATOR_FILE
extern int debug_internal_reset_location_views;
#else
  int x_debug_internal_reset_location_views;
#define debug_internal_reset_location_views global_options.x_debug_internal_reset_location_views
#endif
#ifdef GENERATOR_FILE
extern int dwarf_record_gcc_switches;
#else
  int x_dwarf_record_gcc_switches;
#define dwarf_record_gcc_switches global_options.x_dwarf_record_gcc_switches
#endif
#ifdef GENERATOR_FILE
extern int dwarf_split_debug_info;
#else
  int x_dwarf_split_debug_info;
#define dwarf_split_debug_info global_options.x_dwarf_split_debug_info
#endif
#ifdef GENERATOR_FILE
extern int debug_nonbind_markers_p;
#else
  int x_debug_nonbind_markers_p;
#define debug_nonbind_markers_p global_options.x_debug_nonbind_markers_p
#endif
#ifdef GENERATOR_FILE
extern int dwarf_strict;
#else
  int x_dwarf_strict;
#define dwarf_strict global_options.x_dwarf_strict
#endif
#ifdef GENERATOR_FILE
extern int flag_gtoggle;
#else
  int x_flag_gtoggle;
#define flag_gtoggle global_options.x_flag_gtoggle
#endif
#ifdef GENERATOR_FILE
extern int debug_variable_location_views;
#else
  int x_debug_variable_location_views;
#define debug_variable_location_views global_options.x_debug_variable_location_views
#endif
#ifdef GENERATOR_FILE
extern const char *imultiarch;
#else
  const char *x_imultiarch;
#define imultiarch global_options.x_imultiarch
#endif
#ifdef GENERATOR_FILE
extern const char *plugindir_string;
#else
  const char *x_plugindir_string;
#define plugindir_string global_options.x_plugindir_string
#endif
#ifdef GENERATOR_FILE
extern enum arm_abi_type arm_abi;
#else
  enum arm_abi_type x_arm_abi;
#define arm_abi global_options.x_arm_abi
#endif
#ifdef GENERATOR_FILE
extern const char *arm_arch_string;
#else
  const char *x_arm_arch_string;
#define arm_arch_string global_options.x_arm_arch_string
#endif
#ifdef GENERATOR_FILE
extern int inline_asm_unified;
#else
  int x_inline_asm_unified;
#define inline_asm_unified global_options.x_inline_asm_unified
#endif
#ifdef GENERATOR_FILE
extern int arm_branch_cost;
#else
  int x_arm_branch_cost;
#define arm_branch_cost global_options.x_arm_branch_cost
#endif
#ifdef GENERATOR_FILE
extern int use_cmse;
#else
  int x_use_cmse;
#define use_cmse global_options.x_use_cmse
#endif
#ifdef GENERATOR_FILE
extern const char *arm_cpu_string;
#else
  const char *x_arm_cpu_string;
#define arm_cpu_string global_options.x_arm_cpu_string
#endif
#ifdef GENERATOR_FILE
extern int fix_cm3_ldrd;
#else
  int x_fix_cm3_ldrd;
#define fix_cm3_ldrd global_options.x_fix_cm3_ldrd
#endif
#ifdef GENERATOR_FILE
extern int TARGET_FLIP_THUMB;
#else
  int x_TARGET_FLIP_THUMB;
#define TARGET_FLIP_THUMB global_options.x_TARGET_FLIP_THUMB
#endif
#ifdef GENERATOR_FILE
extern enum float_abi_type arm_float_abi;
#else
  enum float_abi_type x_arm_float_abi;
#define arm_float_abi global_options.x_arm_float_abi
#endif
#ifdef GENERATOR_FILE
extern enum arm_fp16_format_type arm_fp16_format;
#else
  enum arm_fp16_format_type x_arm_fp16_format;
#define arm_fp16_format global_options.x_arm_fp16_format
#endif
#ifdef GENERATOR_FILE
extern enum fpu_type arm_fpu_index;
#else
  enum fpu_type x_arm_fpu_index;
#define arm_fpu_index global_options.x_arm_fpu_index
#endif
#ifdef GENERATOR_FILE
extern int use_neon_for_64bits;
#else
  int x_use_neon_for_64bits;
#define use_neon_for_64bits global_options.x_use_neon_for_64bits
#endif
#ifdef GENERATOR_FILE
extern int arm_pic_data_is_text_relative;
#else
  int x_arm_pic_data_is_text_relative;
#define arm_pic_data_is_text_relative global_options.x_arm_pic_data_is_text_relative
#endif
#ifdef GENERATOR_FILE
extern const char *arm_pic_register_string;
#else
  const char *x_arm_pic_register_string;
#define arm_pic_register_string global_options.x_arm_pic_register_string
#endif
#ifdef GENERATOR_FILE
extern int print_tune_info;
#else
  int x_print_tune_info;
#define print_tune_info global_options.x_print_tune_info
#endif
#ifdef GENERATOR_FILE
extern int target_pure_code;
#else
  int x_target_pure_code;
#define target_pure_code global_options.x_target_pure_code
#endif
#ifdef GENERATOR_FILE
extern int arm_restrict_it;
#else
  int x_arm_restrict_it;
#define arm_restrict_it global_options.x_arm_restrict_it
#endif
#ifdef GENERATOR_FILE
extern int target_slow_flash_data;
#else
  int x_target_slow_flash_data;
#define target_slow_flash_data global_options.x_target_slow_flash_data
#endif
#ifdef GENERATOR_FILE
extern int arm_structure_size_boundary;
#else
  int x_arm_structure_size_boundary;
#define arm_structure_size_boundary global_options.x_arm_structure_size_boundary
#endif
#ifdef GENERATOR_FILE
extern enum arm_tls_type target_tls_dialect;
#else
  enum arm_tls_type x_target_tls_dialect;
#define target_tls_dialect global_options.x_target_tls_dialect
#endif
#ifdef GENERATOR_FILE
extern enum arm_tp_type target_thread_pointer;
#else
  enum arm_tp_type x_target_thread_pointer;
#define target_thread_pointer global_options.x_target_thread_pointer
#endif
#ifdef GENERATOR_FILE
extern const char *arm_tune_string;
#else
  const char *x_arm_tune_string;
#define arm_tune_string global_options.x_arm_tune_string
#endif
#ifdef GENERATOR_FILE
extern int unaligned_access;
#else
  int x_unaligned_access;
#define unaligned_access global_options.x_unaligned_access
#endif
#ifdef GENERATOR_FILE
extern int arm_verbose_cost;
#else
  int x_arm_verbose_cost;
#define arm_verbose_cost global_options.x_arm_verbose_cost
#endif
#ifdef GENERATOR_FILE
extern int target_word_relocations;
#else
  int x_target_word_relocations;
#define target_word_relocations global_options.x_target_word_relocations
#endif
#ifdef GENERATOR_FILE
extern const char *asm_file_name;
#else
  const char *x_asm_file_name;
#define asm_file_name global_options.x_asm_file_name
#endif
#ifdef GENERATOR_FILE
extern int pass_exit_codes;
#else
  int x_pass_exit_codes;
#define pass_exit_codes global_options.x_pass_exit_codes
#endif
#ifdef GENERATOR_FILE
extern int flag_pedantic_errors;
#else
  int x_flag_pedantic_errors;
#define flag_pedantic_errors global_options.x_flag_pedantic_errors
#endif
#ifdef GENERATOR_FILE
extern int use_pipes;
#else
  int x_use_pipes;
#define use_pipes global_options.x_use_pipes
#endif
#ifdef GENERATOR_FILE
extern const char *print_file_name;
#else
  const char *x_print_file_name;
#define print_file_name global_options.x_print_file_name
#endif
#ifdef GENERATOR_FILE
extern int print_multi_directory;
#else
  int x_print_multi_directory;
#define print_multi_directory global_options.x_print_multi_directory
#endif
#ifdef GENERATOR_FILE
extern int print_multi_lib;
#else
  int x_print_multi_lib;
#define print_multi_lib global_options.x_print_multi_lib
#endif
#ifdef GENERATOR_FILE
extern int print_multi_os_directory;
#else
  int x_print_multi_os_directory;
#define print_multi_os_directory global_options.x_print_multi_os_directory
#endif
#ifdef GENERATOR_FILE
extern int print_multiarch;
#else
  int x_print_multiarch;
#define print_multiarch global_options.x_print_multiarch
#endif
#ifdef GENERATOR_FILE
extern const char *print_prog_name;
#else
  const char *x_print_prog_name;
#define print_prog_name global_options.x_print_prog_name
#endif
#ifdef GENERATOR_FILE
extern int print_search_dirs;
#else
  int x_print_search_dirs;
#define print_search_dirs global_options.x_print_search_dirs
#endif
#ifdef GENERATOR_FILE
extern int print_sysroot;
#else
  int x_print_sysroot;
#define print_sysroot global_options.x_print_sysroot
#endif
#ifdef GENERATOR_FILE
extern int print_sysroot_headers_suffix;
#else
  int x_print_sysroot_headers_suffix;
#define print_sysroot_headers_suffix global_options.x_print_sysroot_headers_suffix
#endif
#ifdef GENERATOR_FILE
extern int quiet_flag;
#else
  int x_quiet_flag;
#define quiet_flag global_options.x_quiet_flag
#endif
#ifdef GENERATOR_FILE
extern int report_times;
#else
  int x_report_times;
#define report_times global_options.x_report_times
#endif
#ifdef GENERATOR_FILE
extern int flag_undef;
#else
  int x_flag_undef;
#define flag_undef global_options.x_flag_undef
#endif
#ifdef GENERATOR_FILE
extern int verbose_flag;
#else
  int x_verbose_flag;
#define verbose_flag global_options.x_verbose_flag
#endif
#ifdef GENERATOR_FILE
extern int version_flag;
#else
  int x_version_flag;
#define version_flag global_options.x_version_flag
#endif
#ifdef GENERATOR_FILE
extern int inhibit_warnings;
#else
  int x_inhibit_warnings;
#define inhibit_warnings global_options.x_inhibit_warnings
#endif
#ifdef GENERATOR_FILE
extern const char *wrapper_string;
#else
  const char *x_wrapper_string;
#define wrapper_string global_options.x_wrapper_string
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_associative_math;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_cx_limited_range;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_excess_precision_cmdline;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_finite_math_only;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_errno_math;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_reciprocal_math;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_rounding_math;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_signaling_nans;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_signed_zeros;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_trapping_math;
#endif
#ifndef GENERATOR_FILE
  bool frontend_set_flag_unsafe_math_optimizations;
#endif
#ifndef GENERATOR_FILE
};
extern struct gcc_options global_options;
extern const struct gcc_options global_options_init;
extern struct gcc_options global_options_set;
#define target_flags_explicit global_options_set.x_target_flags
#endif
#endif

#if !defined(IN_LIBGCC2) && !defined(IN_TARGET_LIBS) && !defined(IN_RTS)

/* Structure to save/restore optimization and target specific options.  */
struct GTY(()) cl_optimization
{
  int x_align_functions;
  int x_align_jumps;
  int x_align_labels;
  int x_align_loops;
  int x_flag_sched_stalled_insns;
  int x_flag_sched_stalled_insns_dep;
  int x_flag_tree_parallelize_loops;
  enum fp_contract_mode x_flag_fp_contract_mode;
  enum ira_algorithm x_flag_ira_algorithm;
  enum ira_region x_flag_ira_region;
  enum reorder_blocks_algorithm x_flag_reorder_blocks_algorithm;
  enum vect_cost_model x_flag_simd_cost_model;
  enum stack_reuse_level x_flag_stack_reuse;
  enum vect_cost_model x_flag_vect_cost_model;
  unsigned char x_optimize;
  unsigned char x_optimize_size;
  unsigned char x_optimize_debug;
  signed char x_flag_aggressive_loop_optimizations;
  signed char x_flag_associative_math;
  signed char x_flag_asynchronous_unwind_tables;
  signed char x_flag_auto_inc_dec;
  signed char x_flag_branch_on_count_reg;
  signed char x_flag_branch_probabilities;
  signed char x_flag_branch_target_load_optimize;
  signed char x_flag_branch_target_load_optimize2;
  signed char x_flag_btr_bb_exclusive;
  signed char x_flag_caller_saves;
  signed char x_flag_code_hoisting;
  signed char x_flag_combine_stack_adjustments;
  signed char x_flag_compare_elim_after_reload;
  signed char x_flag_conserve_stack;
  signed char x_flag_cprop_registers;
  signed char x_flag_crossjumping;
  signed char x_flag_cse_follow_jumps;
  signed char x_flag_cx_fortran_rules;
  signed char x_flag_cx_limited_range;
  signed char x_flag_dce;
  signed char x_flag_defer_pop;
  signed char x_flag_delayed_branch;
  signed char x_flag_delete_dead_exceptions;
  signed char x_flag_delete_null_pointer_checks;
  signed char x_flag_devirtualize;
  signed char x_flag_devirtualize_speculatively;
  signed char x_flag_dse;
  signed char x_flag_early_inlining;
  signed char x_flag_exceptions;
  signed char x_flag_expensive_optimizations;
  signed char x_flag_finite_math_only;
  signed char x_flag_float_store;
  signed char x_flag_forward_propagate;
  signed char x_flag_fp_int_builtin_inexact;
  signed char x_flag_no_function_cse;
  signed char x_flag_gcse;
  signed char x_flag_gcse_after_reload;
  signed char x_flag_gcse_las;
  signed char x_flag_gcse_lm;
  signed char x_flag_gcse_sm;
  signed char x_flag_graphite;
  signed char x_flag_graphite_identity;
  signed char x_flag_guess_branch_prob;
  signed char x_flag_hoist_adjacent_loads;
  signed char x_flag_if_conversion;
  signed char x_flag_if_conversion2;
  signed char x_flag_indirect_inlining;
  signed char x_flag_no_inline;
  signed char x_flag_inline_atomics;
  signed char x_flag_inline_functions;
  signed char x_flag_inline_functions_called_once;
  signed char x_flag_inline_small_functions;
  signed char x_flag_ipa_bit_cp;
  signed char x_flag_ipa_cp;
  signed char x_flag_ipa_cp_clone;
  signed char x_flag_ipa_icf;
  signed char x_flag_ipa_icf_functions;
  signed char x_flag_ipa_icf_variables;
  signed char x_flag_ipa_profile;
  signed char x_flag_ipa_pta;
  signed char x_flag_ipa_pure_const;
  signed char x_flag_ipa_ra;
  signed char x_flag_ipa_reference;
  signed char x_flag_ipa_sra;
  signed char x_flag_ipa_vrp;
  signed char x_flag_ira_hoist_pressure;
  signed char x_flag_ira_loop_pressure;
  signed char x_flag_ira_share_save_slots;
  signed char x_flag_ira_share_spill_slots;
  signed char x_flag_isolate_erroneous_paths_attribute;
  signed char x_flag_isolate_erroneous_paths_dereference;
  signed char x_flag_ivopts;
  signed char x_flag_jump_tables;
  signed char x_flag_keep_gc_roots_live;
  signed char x_flag_lifetime_dse;
  signed char x_flag_limit_function_alignment;
  signed char x_flag_live_range_shrinkage;
  signed char x_flag_loop_interchange;
  signed char x_flag_loop_nest_optimize;
  signed char x_flag_loop_parallelize_all;
  signed char x_flag_unroll_jam;
  signed char x_flag_lra_remat;
  signed char x_flag_errno_math;
  signed char x_flag_modulo_sched;
  signed char x_flag_modulo_sched_allow_regmoves;
  signed char x_flag_move_loop_invariants;
  signed char x_flag_non_call_exceptions;
  signed char x_flag_nothrow_opt;
  signed char x_flag_omit_frame_pointer;
  signed char x_flag_opt_info;
  signed char x_flag_optimize_sibling_calls;
  signed char x_flag_optimize_strlen;
  signed char x_flag_pack_struct;
  signed char x_flag_partial_inlining;
  signed char x_flag_peel_loops;
  signed char x_flag_no_peephole;
  signed char x_flag_peephole2;
  signed char x_flag_plt;
  signed char x_flag_predictive_commoning;
  signed char x_flag_prefetch_loop_arrays;
  signed char x_flag_printf_return_value;
  signed char x_flag_reciprocal_math;
  signed char x_flag_pcc_struct_return;
  signed char x_flag_rename_registers;
  signed char x_flag_reorder_blocks;
  signed char x_flag_reorder_blocks_and_partition;
  signed char x_flag_reorder_functions;
  signed char x_flag_rerun_cse_after_loop;
  signed char x_flag_resched_modulo_sched;
  signed char x_flag_rounding_math;
  signed char x_flag_rtti;
  signed char x_flag_sched_critical_path_heuristic;
  signed char x_flag_sched_dep_count_heuristic;
  signed char x_flag_sched_group_heuristic;
  signed char x_flag_schedule_interblock;
  signed char x_flag_sched_last_insn_heuristic;
  signed char x_flag_sched_pressure;
  signed char x_flag_sched_rank_heuristic;
  signed char x_flag_schedule_speculative;
  signed char x_flag_sched_spec_insn_heuristic;
  signed char x_flag_schedule_speculative_load;
  signed char x_flag_schedule_speculative_load_dangerous;
  signed char x_flag_sched2_use_superblocks;
  signed char x_flag_schedule_fusion;
  signed char x_flag_schedule_insns;
  signed char x_flag_schedule_insns_after_reload;
  signed char x_flag_section_anchors;
  signed char x_flag_sel_sched_pipelining;
  signed char x_flag_sel_sched_pipelining_outer_loops;
  signed char x_flag_sel_sched_reschedule_pipelined;
  signed char x_flag_selective_scheduling;
  signed char x_flag_selective_scheduling2;
  signed char x_flag_short_enums;
  signed char x_flag_short_wchar;
  signed char x_flag_shrink_wrap;
  signed char x_flag_shrink_wrap_separate;
  signed char x_flag_signaling_nans;
  signed char x_flag_signed_zeros;
  signed char x_flag_single_precision_constant;
  signed char x_flag_split_ivs_in_unroller;
  signed char x_flag_split_loops;
  signed char x_flag_split_paths;
  signed char x_flag_split_wide_types;
  signed char x_flag_ssa_backprop;
  signed char x_flag_ssa_phiopt;
  signed char x_flag_stack_clash_protection;
  signed char x_flag_stack_protect;
  signed char x_flag_stdarg_opt;
  signed char x_flag_store_merging;
  signed char x_flag_strict_aliasing;
  signed char x_flag_strict_enums;
  signed char x_flag_strict_volatile_bitfields;
  signed char x_flag_thread_jumps;
  signed char x_flag_threadsafe_statics;
  signed char x_flag_tracer;
  signed char x_flag_trapping_math;
  signed char x_flag_trapv;
  signed char x_flag_tree_bit_ccp;
  signed char x_flag_tree_builtin_call_dce;
  signed char x_flag_tree_ccp;
  signed char x_flag_tree_ch;
  signed char x_flag_tree_coalesce_vars;
  signed char x_flag_tree_copy_prop;
  signed char x_flag_tree_cselim;
  signed char x_flag_tree_dce;
  signed char x_flag_tree_dom;
  signed char x_flag_tree_dse;
  signed char x_flag_tree_forwprop;
  signed char x_flag_tree_fre;
  signed char x_flag_tree_loop_distribute_patterns;
  signed char x_flag_tree_loop_distribution;
  signed char x_flag_tree_loop_if_convert;
  signed char x_flag_tree_loop_im;
  signed char x_flag_tree_loop_ivcanon;
  signed char x_flag_tree_loop_optimize;
  signed char x_flag_tree_loop_vectorize;
  signed char x_flag_tree_live_range_split;
  signed char x_flag_tree_partial_pre;
  signed char x_flag_tree_phiprop;
  signed char x_flag_tree_pre;
  signed char x_flag_tree_pta;
  signed char x_flag_tree_reassoc;
  signed char x_flag_tree_scev_cprop;
  signed char x_flag_tree_sink;
  signed char x_flag_tree_slp_vectorize;
  signed char x_flag_tree_slsr;
  signed char x_flag_tree_sra;
  signed char x_flag_tree_switch_conversion;
  signed char x_flag_tree_tail_merge;
  signed char x_flag_tree_ter;
  signed char x_flag_tree_vrp;
  signed char x_flag_unconstrained_commons;
  signed char x_flag_unroll_all_loops;
  signed char x_flag_unroll_loops;
  signed char x_flag_unsafe_math_optimizations;
  signed char x_flag_unswitch_loops;
  signed char x_flag_unwind_tables;
  signed char x_flag_var_tracking;
  signed char x_flag_var_tracking_assignments;
  signed char x_flag_var_tracking_assignments_toggle;
  signed char x_flag_var_tracking_uninit;
  signed char x_flag_variable_expansion_in_unroller;
  signed char x_flag_value_profile_transformations;
  signed char x_flag_web;
  signed char x_flag_wrapv;
  signed char x_flag_wrapv_pointer;
};

/* Structure to save/restore selected target specific options.  */
struct GTY(()) cl_target_option
{
  const char *x_arm_arch_string;
  const char *x_arm_cpu_string;
  const char *x_arm_tune_string;
  enum fpu_type x_arm_fpu_index;
  int x_target_flags;
  signed char x_inline_asm_unified;
  signed char x_arm_restrict_it;
  signed char x_unaligned_access;
};


/* Save optimization variables into a structure.  */
extern void cl_optimization_save (struct cl_optimization *, struct gcc_options *);

/* Restore optimization variables from a structure.  */
extern void cl_optimization_restore (struct gcc_options *, struct cl_optimization *);

/* Print optimization variables from a structure.  */
extern void cl_optimization_print (FILE *, int, struct cl_optimization *);

/* Print different optimization variables from structures provided as arguments.  */
extern void cl_optimization_print_diff (FILE *, int, cl_optimization *ptr1, cl_optimization *ptr2);

/* Save selected option variables into a structure.  */
extern void cl_target_option_save (struct cl_target_option *, struct gcc_options *);

/* Restore selected option variables from a structure.  */
extern void cl_target_option_restore (struct gcc_options *, struct cl_target_option *);

/* Print target option variables from a structure.  */
extern void cl_target_option_print (FILE *, int, struct cl_target_option *);

/* Print different target option variables from structures provided as arguments.  */
extern void cl_target_option_print_diff (FILE *, int, cl_target_option *ptr1, cl_target_option *ptr2);

/* Compare two target option variables from a structure.  */
extern bool cl_target_option_eq (const struct cl_target_option *, const struct cl_target_option *);

/* Hash option variables from a structure.  */
extern hashval_t cl_target_option_hash (const struct cl_target_option *);

/* Hash optimization from a structure.  */
extern hashval_t cl_optimization_hash (const struct cl_optimization *);

/* Generator files may not have access to location_t, and don't need these.  */
#if defined(UNKNOWN_LOCATION)
bool                                                                  
common_handle_option_auto (struct gcc_options *opts,                  
                           struct gcc_options *opts_set,              
                           const struct cl_decoded_option *decoded,   
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
Ada_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
AdaSCIL_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
AdaWhy_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
BRIG_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
C_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
CXX_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
Fortran_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
Go_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
LTO_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
ObjC_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
bool                                                                  
ObjCXX_handle_option_auto (struct gcc_options *opts,              
                           struct gcc_options *opts_set,              
                           size_t scode, const char *arg, int value,  
                           unsigned int lang_mask, int kind,          
                           location_t loc,                            
                           const struct cl_option_handlers *handlers, 
                           diagnostic_context *dc);                   
void cpp_handle_option_auto (const struct gcc_options * opts, size_t scode,
                             struct cpp_options * cpp_opts);
void init_global_opts_from_cpp(struct gcc_options * opts,      
                               const struct cpp_options * cpp_opts);
#endif
#endif

#define MASK_ABORT_NORETURN (1U << 0)
#define MASK_APCS_FRAME (1U << 1)
#define MASK_APCS_REENT (1U << 2)
#define MASK_APCS_STACK (1U << 3)
#define MASK_THUMB (1U << 4)
#define MASK_BE8 (1U << 5)
#define MASK_BIG_END (1U << 6)
#define MASK_CALLEE_INTERWORKING (1U << 7)
#define MASK_CALLER_INTERWORKING (1U << 8)
#define MASK_LONG_CALLS (1U << 9)
#define MASK_POKE_FUNCTION_NAME (1U << 10)
#define MASK_SCHED_PROLOG (1U << 11)
#define MASK_SINGLE_PIC_BASE (1U << 12)
#define MASK_INTERWORK (1U << 13)
#define MASK_TPCS_FRAME (1U << 14)
#define MASK_TPCS_LEAF_FRAME (1U << 15)
#define MASK_NEON_VECTORIZE_DOUBLE (1U << 16)

#define TARGET_ABORT_NORETURN ((target_flags & MASK_ABORT_NORETURN) != 0)
#define TARGET_ABORT_NORETURN_P(target_flags) (((target_flags) & MASK_ABORT_NORETURN) != 0)
#define TARGET_APCS_FRAME ((target_flags & MASK_APCS_FRAME) != 0)
#define TARGET_APCS_FRAME_P(target_flags) (((target_flags) & MASK_APCS_FRAME) != 0)
#define TARGET_APCS_REENT ((target_flags & MASK_APCS_REENT) != 0)
#define TARGET_APCS_REENT_P(target_flags) (((target_flags) & MASK_APCS_REENT) != 0)
#define TARGET_APCS_STACK ((target_flags & MASK_APCS_STACK) != 0)
#define TARGET_APCS_STACK_P(target_flags) (((target_flags) & MASK_APCS_STACK) != 0)
#define TARGET_THUMB ((target_flags & MASK_THUMB) != 0)
#define TARGET_THUMB_P(target_flags) (((target_flags) & MASK_THUMB) != 0)
#define TARGET_BE8 ((target_flags & MASK_BE8) != 0)
#define TARGET_BE8_P(target_flags) (((target_flags) & MASK_BE8) != 0)
#define TARGET_BIG_END ((target_flags & MASK_BIG_END) != 0)
#define TARGET_BIG_END_P(target_flags) (((target_flags) & MASK_BIG_END) != 0)
#define TARGET_CALLEE_INTERWORKING ((target_flags & MASK_CALLEE_INTERWORKING) != 0)
#define TARGET_CALLEE_INTERWORKING_P(target_flags) (((target_flags) & MASK_CALLEE_INTERWORKING) != 0)
#define TARGET_CALLER_INTERWORKING ((target_flags & MASK_CALLER_INTERWORKING) != 0)
#define TARGET_CALLER_INTERWORKING_P(target_flags) (((target_flags) & MASK_CALLER_INTERWORKING) != 0)
#define TARGET_LONG_CALLS ((target_flags & MASK_LONG_CALLS) != 0)
#define TARGET_LONG_CALLS_P(target_flags) (((target_flags) & MASK_LONG_CALLS) != 0)
#define TARGET_POKE_FUNCTION_NAME ((target_flags & MASK_POKE_FUNCTION_NAME) != 0)
#define TARGET_POKE_FUNCTION_NAME_P(target_flags) (((target_flags) & MASK_POKE_FUNCTION_NAME) != 0)
#define TARGET_SCHED_PROLOG ((target_flags & MASK_SCHED_PROLOG) != 0)
#define TARGET_SCHED_PROLOG_P(target_flags) (((target_flags) & MASK_SCHED_PROLOG) != 0)
#define TARGET_SINGLE_PIC_BASE ((target_flags & MASK_SINGLE_PIC_BASE) != 0)
#define TARGET_SINGLE_PIC_BASE_P(target_flags) (((target_flags) & MASK_SINGLE_PIC_BASE) != 0)
#define TARGET_INTERWORK ((target_flags & MASK_INTERWORK) != 0)
#define TARGET_INTERWORK_P(target_flags) (((target_flags) & MASK_INTERWORK) != 0)
#define TARGET_TPCS_FRAME ((target_flags & MASK_TPCS_FRAME) != 0)
#define TARGET_TPCS_FRAME_P(target_flags) (((target_flags) & MASK_TPCS_FRAME) != 0)
#define TARGET_TPCS_LEAF_FRAME ((target_flags & MASK_TPCS_LEAF_FRAME) != 0)
#define TARGET_TPCS_LEAF_FRAME_P(target_flags) (((target_flags) & MASK_TPCS_LEAF_FRAME) != 0)
#define TARGET_NEON_VECTORIZE_DOUBLE ((target_flags & MASK_NEON_VECTORIZE_DOUBLE) != 0)
#define TARGET_NEON_VECTORIZE_DOUBLE_P(target_flags) (((target_flags) & MASK_NEON_VECTORIZE_DOUBLE) != 0)


#define CL_Ada        (1U << 0)
#define CL_AdaSCIL    (1U << 1)
#define CL_AdaWhy     (1U << 2)
#define CL_BRIG       (1U << 3)
#define CL_C          (1U << 4)
#define CL_CXX        (1U << 5)
#define CL_Fortran    (1U << 6)
#define CL_Go         (1U << 7)
#define CL_LTO        (1U << 8)
#define CL_ObjC       (1U << 9)
#define CL_ObjCXX     (1U << 10)
#define CL_LANG_ALL   ((1U << 11) - 1)

enum opt_code
{
  OPT____ = 0,                               /* -### */
  /* OPT__all_warnings = 1, */               /* --all-warnings */
  /* OPT__ansi = 2, */                       /* --ansi */
  /* OPT__assemble = 3, */                   /* --assemble */
  /* OPT__assert = 4, */                     /* --assert */
  /* OPT__assert_ = 5, */                    /* --assert= */
  /* OPT__comments = 6, */                   /* --comments */
  /* OPT__comments_in_macros = 7, */         /* --comments-in-macros */
  /* OPT__compile = 8, */                    /* --compile */
  /* OPT__coverage = 9, */                   /* --coverage */
  /* OPT__debug = 10, */                     /* --debug */
  /* OPT__define_macro = 11, */              /* --define-macro */
  /* OPT__define_macro_ = 12, */             /* --define-macro= */
  /* OPT__dependencies = 13, */              /* --dependencies */
  /* OPT__dump = 14, */                      /* --dump */
  /* OPT__dump_ = 15, */                     /* --dump= */
  /* OPT__dumpbase = 16, */                  /* --dumpbase */
  /* OPT__dumpdir = 17, */                   /* --dumpdir */
  /* OPT__entry = 18, */                     /* --entry */
  /* OPT__entry_ = 19, */                    /* --entry= */
  /* OPT__extra_warnings = 20, */            /* --extra-warnings */
  /* OPT__for_assembler = 21, */             /* --for-assembler */
  /* OPT__for_assembler_ = 22, */            /* --for-assembler= */
  /* OPT__for_linker = 23, */                /* --for-linker */
  /* OPT__for_linker_ = 24, */               /* --for-linker= */
  /* OPT__force_link = 25, */                /* --force-link */
  /* OPT__force_link_ = 26, */               /* --force-link= */
  OPT__help = 27,                            /* --help */
  OPT__help_ = 28,                           /* --help= */
  /* OPT__imacros = 29, */                   /* --imacros */
  /* OPT__imacros_ = 30, */                  /* --imacros= */
  /* OPT__include = 31, */                   /* --include */
  /* OPT__include_barrier = 32, */           /* --include-barrier */
  /* OPT__include_directory = 33, */         /* --include-directory */
  /* OPT__include_directory_after = 34, */   /* --include-directory-after */
  /* OPT__include_directory_after_ = 35, */  /* --include-directory-after= */
  /* OPT__include_directory_ = 36, */        /* --include-directory= */
  /* OPT__include_prefix = 37, */            /* --include-prefix */
  /* OPT__include_prefix_ = 38, */           /* --include-prefix= */
  /* OPT__include_with_prefix = 39, */       /* --include-with-prefix */
  /* OPT__include_with_prefix_after = 40, */ /* --include-with-prefix-after */
  /* OPT__include_with_prefix_after_ = 41, *//* --include-with-prefix-after= */
  /* OPT__include_with_prefix_before = 42, *//* --include-with-prefix-before */
  /* OPT__include_with_prefix_before_ = 43, *//* --include-with-prefix-before= */
  /* OPT__include_with_prefix_ = 44, */      /* --include-with-prefix= */
  /* OPT__include_ = 45, */                  /* --include= */
  /* OPT__language = 46, */                  /* --language */
  /* OPT__language_ = 47, */                 /* --language= */
  /* OPT__library_directory = 48, */         /* --library-directory */
  /* OPT__library_directory_ = 49, */        /* --library-directory= */
  /* OPT__no_canonical_prefixes = 50, */     /* --no-canonical-prefixes */
  /* OPT__no_integrated_cpp = 51, */         /* --no-integrated-cpp */
  /* OPT__no_line_commands = 52, */          /* --no-line-commands */
  /* OPT__no_standard_includes = 53, */      /* --no-standard-includes */
  /* OPT__no_standard_libraries = 54, */     /* --no-standard-libraries */
  OPT__no_sysroot_suffix = 55,               /* --no-sysroot-suffix */
  /* OPT__no_warnings = 56, */               /* --no-warnings */
  /* OPT__optimize = 57, */                  /* --optimize */
  /* OPT__output = 58, */                    /* --output */
  OPT__output_pch_ = 59,                     /* --output-pch= */
  /* OPT__output_ = 60, */                   /* --output= */
  OPT__param = 61,                           /* --param */
  /* OPT__param_ = 62, */                    /* --param= */
  /* OPT__pass_exit_codes = 63, */           /* --pass-exit-codes */
  /* OPT__pedantic = 64, */                  /* --pedantic */
  /* OPT__pedantic_errors = 65, */           /* --pedantic-errors */
  /* OPT__pie = 66, */                       /* --pie */
  /* OPT__pipe = 67, */                      /* --pipe */
  /* OPT__prefix = 68, */                    /* --prefix */
  /* OPT__prefix_ = 69, */                   /* --prefix= */
  /* OPT__preprocess = 70, */                /* --preprocess */
  /* OPT__print_file_name = 71, */           /* --print-file-name */
  /* OPT__print_file_name_ = 72, */          /* --print-file-name= */
  /* OPT__print_libgcc_file_name = 73, */    /* --print-libgcc-file-name */
  /* OPT__print_missing_file_dependencies = 74, *//* --print-missing-file-dependencies */
  /* OPT__print_multi_directory = 75, */     /* --print-multi-directory */
  /* OPT__print_multi_lib = 76, */           /* --print-multi-lib */
  /* OPT__print_multi_os_directory = 77, */  /* --print-multi-os-directory */
  /* OPT__print_multiarch = 78, */           /* --print-multiarch */
  /* OPT__print_prog_name = 79, */           /* --print-prog-name */
  /* OPT__print_prog_name_ = 80, */          /* --print-prog-name= */
  /* OPT__print_search_dirs = 81, */         /* --print-search-dirs */
  /* OPT__print_sysroot = 82, */             /* --print-sysroot */
  /* OPT__print_sysroot_headers_suffix = 83, *//* --print-sysroot-headers-suffix */
  /* OPT__profile = 84, */                   /* --profile */
  /* OPT__save_temps = 85, */                /* --save-temps */
  /* OPT__shared = 86, */                    /* --shared */
  /* OPT__specs = 87, */                     /* --specs */
  /* OPT__specs_ = 88, */                    /* --specs= */
  /* OPT__static = 89, */                    /* --static */
  /* OPT__static_pie = 90, */                /* --static-pie */
  /* OPT__symbolic = 91, */                  /* --symbolic */
  /* OPT__sysroot = 92, */                   /* --sysroot */
  OPT__sysroot_ = 93,                        /* --sysroot= */
  OPT__target_help = 94,                     /* --target-help */
  /* OPT__time = 95, */                      /* --time */
  /* OPT__trace_includes = 96, */            /* --trace-includes */
  /* OPT__traditional = 97, */               /* --traditional */
  /* OPT__traditional_cpp = 98, */           /* --traditional-cpp */
  /* OPT__trigraphs = 99, */                 /* --trigraphs */
  /* OPT__undefine_macro = 100, */           /* --undefine-macro */
  /* OPT__undefine_macro_ = 101, */          /* --undefine-macro= */
  /* OPT__user_dependencies = 102, */        /* --user-dependencies */
  /* OPT__verbose = 103, */                  /* --verbose */
  OPT__version = 104,                        /* --version */
  /* OPT__write_dependencies = 105, */       /* --write-dependencies */
  /* OPT__write_user_dependencies = 106, */  /* --write-user-dependencies */
  OPT_A = 107,                               /* -A */
  OPT_B = 108,                               /* -B */
  OPT_C = 109,                               /* -C */
  OPT_CC = 110,                              /* -CC */
  OPT_D = 111,                               /* -D */
  OPT_E = 112,                               /* -E */
  OPT_F = 113,                               /* -F */
  OPT_H = 114,                               /* -H */
  OPT_I = 115,                               /* -I */
  OPT_J = 116,                               /* -J */
  OPT_L = 117,                               /* -L */
  OPT_M = 118,                               /* -M */
  OPT_MD = 119,                              /* -MD */
  OPT_MF = 120,                              /* -MF */
  OPT_MG = 121,                              /* -MG */
  OPT_MM = 122,                              /* -MM */
  OPT_MMD = 123,                             /* -MMD */
  OPT_MP = 124,                              /* -MP */
  OPT_MQ = 125,                              /* -MQ */
  OPT_MT = 126,                              /* -MT */
  OPT_N = 127,                               /* -N */
  OPT_O = 128,                               /* -O */
  OPT_Ofast = 129,                           /* -Ofast */
  OPT_Og = 130,                              /* -Og */
  OPT_Os = 131,                              /* -Os */
  OPT_P = 132,                               /* -P */
  OPT_Q = 133,                               /* -Q */
  OPT_Qn = 134,                              /* -Qn */
  OPT_Qy = 135,                              /* -Qy */
  OPT_R = 136,                               /* -R */
  OPT_S = 137,                               /* -S */
  OPT_T = 138,                               /* -T */
  OPT_Tbss = 139,                            /* -Tbss */
  OPT_Tbss_ = 140,                           /* -Tbss= */
  OPT_Tdata = 141,                           /* -Tdata */
  OPT_Tdata_ = 142,                          /* -Tdata= */
  OPT_Ttext = 143,                           /* -Ttext */
  OPT_Ttext_ = 144,                          /* -Ttext= */
  OPT_U = 145,                               /* -U */
  /* OPT_W = 146, */                         /* -W */
  OPT_Wa_ = 147,                             /* -Wa, */
  OPT_Wabi = 148,                            /* -Wabi */
  OPT_Wabi_tag = 149,                        /* -Wabi-tag */
  OPT_Wabi_ = 150,                           /* -Wabi= */
  OPT_Waddress = 151,                        /* -Waddress */
  OPT_Waggregate_return = 152,               /* -Waggregate-return */
  OPT_Waggressive_loop_optimizations = 153,  /* -Waggressive-loop-optimizations */
  OPT_Waliasing = 154,                       /* -Waliasing */
  OPT_Walign_commons = 155,                  /* -Walign-commons */
  /* OPT_Waligned_new = 156, */              /* -Waligned-new */
  OPT_Waligned_new_ = 157,                   /* -Waligned-new= */
  OPT_Wall = 158,                            /* -Wall */
  OPT_Walloc_size_larger_than_ = 159,        /* -Walloc-size-larger-than= */
  OPT_Walloc_zero = 160,                     /* -Walloc-zero */
  OPT_Walloca = 161,                         /* -Walloca */
  OPT_Walloca_larger_than_ = 162,            /* -Walloca-larger-than= */
  OPT_Wampersand = 163,                      /* -Wampersand */
  OPT_Wargument_mismatch = 164,              /* -Wargument-mismatch */
  OPT_Warray_bounds = 165,                   /* -Warray-bounds */
  OPT_Warray_bounds_ = 166,                  /* -Warray-bounds= */
  OPT_Warray_temporaries = 167,              /* -Warray-temporaries */
  OPT_Wassign_intercept = 168,               /* -Wassign-intercept */
  OPT_Wattribute_alias = 169,                /* -Wattribute-alias */
  OPT_Wattributes = 170,                     /* -Wattributes */
  OPT_Wbad_function_cast = 171,              /* -Wbad-function-cast */
  OPT_Wbool_compare = 172,                   /* -Wbool-compare */
  OPT_Wbool_operation = 173,                 /* -Wbool-operation */
  OPT_Wbuiltin_declaration_mismatch = 174,   /* -Wbuiltin-declaration-mismatch */
  OPT_Wbuiltin_macro_redefined = 175,        /* -Wbuiltin-macro-redefined */
  OPT_Wc___compat = 176,                     /* -Wc++-compat */
  /* OPT_Wc__0x_compat = 177, */             /* -Wc++0x-compat */
  OPT_Wc__11_compat = 178,                   /* -Wc++11-compat */
  OPT_Wc__14_compat = 179,                   /* -Wc++14-compat */
  OPT_Wc__17_compat = 180,                   /* -Wc++17-compat */
  /* OPT_Wc__1z_compat = 181, */             /* -Wc++1z-compat */
  OPT_Wc_binding_type = 182,                 /* -Wc-binding-type */
  OPT_Wc90_c99_compat = 183,                 /* -Wc90-c99-compat */
  OPT_Wc99_c11_compat = 184,                 /* -Wc99-c11-compat */
  OPT_Wcast_align = 185,                     /* -Wcast-align */
  OPT_Wcast_align_strict = 186,              /* -Wcast-align=strict */
  OPT_Wcast_function_type = 187,             /* -Wcast-function-type */
  OPT_Wcast_qual = 188,                      /* -Wcast-qual */
  /* OPT_Wcatch_value = 189, */              /* -Wcatch-value */
  OPT_Wcatch_value_ = 190,                   /* -Wcatch-value= */
  OPT_Wchar_subscripts = 191,                /* -Wchar-subscripts */
  OPT_Wcharacter_truncation = 192,           /* -Wcharacter-truncation */
  OPT_Wchkp = 193,                           /* -Wchkp */
  OPT_Wclass_memaccess = 194,                /* -Wclass-memaccess */
  OPT_Wclobbered = 195,                      /* -Wclobbered */
  OPT_Wcomment = 196,                        /* -Wcomment */
  /* OPT_Wcomments = 197, */                 /* -Wcomments */
  OPT_Wcompare_reals = 198,                  /* -Wcompare-reals */
  OPT_Wconditionally_supported = 199,        /* -Wconditionally-supported */
  OPT_Wconversion = 200,                     /* -Wconversion */
  OPT_Wconversion_extra = 201,               /* -Wconversion-extra */
  OPT_Wconversion_null = 202,                /* -Wconversion-null */
  OPT_Wcoverage_mismatch = 203,              /* -Wcoverage-mismatch */
  OPT_Wcpp = 204,                            /* -Wcpp */
  OPT_Wctor_dtor_privacy = 205,              /* -Wctor-dtor-privacy */
  OPT_Wdangling_else = 206,                  /* -Wdangling-else */
  OPT_Wdate_time = 207,                      /* -Wdate-time */
  OPT_Wdeclaration_after_statement = 208,    /* -Wdeclaration-after-statement */
  OPT_Wdelete_incomplete = 209,              /* -Wdelete-incomplete */
  OPT_Wdelete_non_virtual_dtor = 210,        /* -Wdelete-non-virtual-dtor */
  OPT_Wdeprecated = 211,                     /* -Wdeprecated */
  OPT_Wdeprecated_declarations = 212,        /* -Wdeprecated-declarations */
  OPT_Wdesignated_init = 213,                /* -Wdesignated-init */
  OPT_Wdisabled_optimization = 214,          /* -Wdisabled-optimization */
  OPT_Wdiscarded_array_qualifiers = 215,     /* -Wdiscarded-array-qualifiers */
  OPT_Wdiscarded_qualifiers = 216,           /* -Wdiscarded-qualifiers */
  OPT_Wdiv_by_zero = 217,                    /* -Wdiv-by-zero */
  OPT_Wdo_subscript = 218,                   /* -Wdo-subscript */
  OPT_Wdouble_promotion = 219,               /* -Wdouble-promotion */
  OPT_Wduplicate_decl_specifier = 220,       /* -Wduplicate-decl-specifier */
  OPT_Wduplicated_branches = 221,            /* -Wduplicated-branches */
  OPT_Wduplicated_cond = 222,                /* -Wduplicated-cond */
  OPT_Weffc__ = 223,                         /* -Weffc++ */
  OPT_Wempty_body = 224,                     /* -Wempty-body */
  OPT_Wendif_labels = 225,                   /* -Wendif-labels */
  OPT_Wenum_compare = 226,                   /* -Wenum-compare */
  OPT_Werror = 227,                          /* -Werror */
  /* OPT_Werror_implicit_function_declaration = 228, *//* -Werror-implicit-function-declaration */
  OPT_Werror_ = 229,                         /* -Werror= */
  OPT_Wexpansion_to_defined = 230,           /* -Wexpansion-to-defined */
  OPT_Wextra = 231,                          /* -Wextra */
  OPT_Wextra_semi = 232,                     /* -Wextra-semi */
  OPT_Wfatal_errors = 233,                   /* -Wfatal-errors */
  OPT_Wfloat_conversion = 234,               /* -Wfloat-conversion */
  OPT_Wfloat_equal = 235,                    /* -Wfloat-equal */
  /* OPT_Wformat = 236, */                   /* -Wformat */
  OPT_Wformat_contains_nul = 237,            /* -Wformat-contains-nul */
  OPT_Wformat_extra_args = 238,              /* -Wformat-extra-args */
  OPT_Wformat_nonliteral = 239,              /* -Wformat-nonliteral */
  /* OPT_Wformat_overflow = 240, */          /* -Wformat-overflow */
  OPT_Wformat_overflow_ = 241,               /* -Wformat-overflow= */
  OPT_Wformat_security = 242,                /* -Wformat-security */
  OPT_Wformat_signedness = 243,              /* -Wformat-signedness */
  /* OPT_Wformat_truncation = 244, */        /* -Wformat-truncation */
  OPT_Wformat_truncation_ = 245,             /* -Wformat-truncation= */
  OPT_Wformat_y2k = 246,                     /* -Wformat-y2k */
  OPT_Wformat_zero_length = 247,             /* -Wformat-zero-length */
  OPT_Wformat_ = 248,                        /* -Wformat= */
  OPT_Wframe_address = 249,                  /* -Wframe-address */
  OPT_Wframe_larger_than_ = 250,             /* -Wframe-larger-than= */
  OPT_Wfree_nonheap_object = 251,            /* -Wfree-nonheap-object */
  OPT_Wfrontend_loop_interchange = 252,      /* -Wfrontend-loop-interchange */
  OPT_Wfunction_elimination = 253,           /* -Wfunction-elimination */
  OPT_Whsa = 254,                            /* -Whsa */
  OPT_Wif_not_aligned = 255,                 /* -Wif-not-aligned */
  OPT_Wignored_attributes = 256,             /* -Wignored-attributes */
  OPT_Wignored_qualifiers = 257,             /* -Wignored-qualifiers */
  OPT_Wimplicit = 258,                       /* -Wimplicit */
  /* OPT_Wimplicit_fallthrough = 259, */     /* -Wimplicit-fallthrough */
  OPT_Wimplicit_fallthrough_ = 260,          /* -Wimplicit-fallthrough= */
  OPT_Wimplicit_function_declaration = 261,  /* -Wimplicit-function-declaration */
  OPT_Wimplicit_int = 262,                   /* -Wimplicit-int */
  OPT_Wimplicit_interface = 263,             /* -Wimplicit-interface */
  OPT_Wimplicit_procedure = 264,             /* -Wimplicit-procedure */
  /* OPT_Wimport = 265, */                   /* -Wimport */
  OPT_Wincompatible_pointer_types = 266,     /* -Wincompatible-pointer-types */
  OPT_Winherited_variadic_ctor = 267,        /* -Winherited-variadic-ctor */
  OPT_Winit_self = 268,                      /* -Winit-self */
  OPT_Winline = 269,                         /* -Winline */
  OPT_Wint_conversion = 270,                 /* -Wint-conversion */
  OPT_Wint_in_bool_context = 271,            /* -Wint-in-bool-context */
  OPT_Wint_to_pointer_cast = 272,            /* -Wint-to-pointer-cast */
  OPT_Winteger_division = 273,               /* -Winteger-division */
  OPT_Wintrinsic_shadow = 274,               /* -Wintrinsic-shadow */
  OPT_Wintrinsics_std = 275,                 /* -Wintrinsics-std */
  OPT_Winvalid_memory_model = 276,           /* -Winvalid-memory-model */
  OPT_Winvalid_offsetof = 277,               /* -Winvalid-offsetof */
  OPT_Winvalid_pch = 278,                    /* -Winvalid-pch */
  OPT_Wjump_misses_init = 279,               /* -Wjump-misses-init */
  OPT_Wl_ = 280,                             /* -Wl, */
  /* OPT_Wlarger_than_ = 281, */             /* -Wlarger-than- */
  OPT_Wlarger_than_ = 282,                   /* -Wlarger-than= */
  OPT_Wline_truncation = 283,                /* -Wline-truncation */
  OPT_Wliteral_suffix = 284,                 /* -Wliteral-suffix */
  OPT_Wlogical_not_parentheses = 285,        /* -Wlogical-not-parentheses */
  OPT_Wlogical_op = 286,                     /* -Wlogical-op */
  OPT_Wlong_long = 287,                      /* -Wlong-long */
  OPT_Wlto_type_mismatch = 288,              /* -Wlto-type-mismatch */
  OPT_Wmain = 289,                           /* -Wmain */
  OPT_Wmaybe_uninitialized = 290,            /* -Wmaybe-uninitialized */
  OPT_Wmemset_elt_size = 291,                /* -Wmemset-elt-size */
  OPT_Wmemset_transposed_args = 292,         /* -Wmemset-transposed-args */
  OPT_Wmisleading_indentation = 293,         /* -Wmisleading-indentation */
  OPT_Wmissing_attributes = 294,             /* -Wmissing-attributes */
  OPT_Wmissing_braces = 295,                 /* -Wmissing-braces */
  OPT_Wmissing_declarations = 296,           /* -Wmissing-declarations */
  OPT_Wmissing_field_initializers = 297,     /* -Wmissing-field-initializers */
  /* OPT_Wmissing_format_attribute = 298, */ /* -Wmissing-format-attribute */
  OPT_Wmissing_include_dirs = 299,           /* -Wmissing-include-dirs */
  /* OPT_Wmissing_noreturn = 300, */         /* -Wmissing-noreturn */
  OPT_Wmissing_parameter_type = 301,         /* -Wmissing-parameter-type */
  OPT_Wmissing_prototypes = 302,             /* -Wmissing-prototypes */
  /* OPT_Wmudflap = 303, */                  /* -Wmudflap */
  OPT_Wmultichar = 304,                      /* -Wmultichar */
  OPT_Wmultiple_inheritance = 305,           /* -Wmultiple-inheritance */
  OPT_Wmultistatement_macros = 306,          /* -Wmultistatement-macros */
  OPT_Wnamespaces = 307,                     /* -Wnamespaces */
  OPT_Wnarrowing = 308,                      /* -Wnarrowing */
  OPT_Wnested_externs = 309,                 /* -Wnested-externs */
  OPT_Wnoexcept = 310,                       /* -Wnoexcept */
  OPT_Wnoexcept_type = 311,                  /* -Wnoexcept-type */
  OPT_Wnon_template_friend = 312,            /* -Wnon-template-friend */
  OPT_Wnon_virtual_dtor = 313,               /* -Wnon-virtual-dtor */
  OPT_Wnonnull = 314,                        /* -Wnonnull */
  OPT_Wnonnull_compare = 315,                /* -Wnonnull-compare */
  /* OPT_Wnormalized = 316, */               /* -Wnormalized */
  OPT_Wnormalized_ = 317,                    /* -Wnormalized= */
  OPT_Wnull_dereference = 318,               /* -Wnull-dereference */
  OPT_Wodr = 319,                            /* -Wodr */
  OPT_Wold_style_cast = 320,                 /* -Wold-style-cast */
  OPT_Wold_style_declaration = 321,          /* -Wold-style-declaration */
  OPT_Wold_style_definition = 322,           /* -Wold-style-definition */
  OPT_Wopenmp_simd = 323,                    /* -Wopenmp-simd */
  OPT_Woverflow = 324,                       /* -Woverflow */
  OPT_Woverlength_strings = 325,             /* -Woverlength-strings */
  OPT_Woverloaded_virtual = 326,             /* -Woverloaded-virtual */
  OPT_Woverride_init = 327,                  /* -Woverride-init */
  OPT_Woverride_init_side_effects = 328,     /* -Woverride-init-side-effects */
  OPT_Wp_ = 329,                             /* -Wp, */
  OPT_Wpacked = 330,                         /* -Wpacked */
  OPT_Wpacked_bitfield_compat = 331,         /* -Wpacked-bitfield-compat */
  OPT_Wpacked_not_aligned = 332,             /* -Wpacked-not-aligned */
  OPT_Wpadded = 333,                         /* -Wpadded */
  OPT_Wparentheses = 334,                    /* -Wparentheses */
  OPT_Wpedantic = 335,                       /* -Wpedantic */
  /* OPT_Wplacement_new = 336, */            /* -Wplacement-new */
  OPT_Wplacement_new_ = 337,                 /* -Wplacement-new= */
  OPT_Wpmf_conversions = 338,                /* -Wpmf-conversions */
  OPT_Wpointer_arith = 339,                  /* -Wpointer-arith */
  OPT_Wpointer_compare = 340,                /* -Wpointer-compare */
  OPT_Wpointer_sign = 341,                   /* -Wpointer-sign */
  OPT_Wpointer_to_int_cast = 342,            /* -Wpointer-to-int-cast */
  OPT_Wpragmas = 343,                        /* -Wpragmas */
  OPT_Wproperty_assign_default = 344,        /* -Wproperty-assign-default */
  OPT_Wprotocol = 345,                       /* -Wprotocol */
  OPT_Wpsabi = 346,                          /* -Wpsabi */
  OPT_Wreal_q_constant = 347,                /* -Wreal-q-constant */
  OPT_Wrealloc_lhs = 348,                    /* -Wrealloc-lhs */
  OPT_Wrealloc_lhs_all = 349,                /* -Wrealloc-lhs-all */
  OPT_Wredundant_decls = 350,                /* -Wredundant-decls */
  OPT_Wregister = 351,                       /* -Wregister */
  OPT_Wreorder = 352,                        /* -Wreorder */
  OPT_Wrestrict = 353,                       /* -Wrestrict */
  OPT_Wreturn_local_addr = 354,              /* -Wreturn-local-addr */
  OPT_Wreturn_type = 355,                    /* -Wreturn-type */
  OPT_Wscalar_storage_order = 356,           /* -Wscalar-storage-order */
  OPT_Wselector = 357,                       /* -Wselector */
  OPT_Wsequence_point = 358,                 /* -Wsequence-point */
  OPT_Wshadow = 359,                         /* -Wshadow */
  /* OPT_Wshadow_compatible_local = 360, */  /* -Wshadow-compatible-local */
  OPT_Wshadow_ivar = 361,                    /* -Wshadow-ivar */
  /* OPT_Wshadow_local = 362, */             /* -Wshadow-local */
  OPT_Wshadow_compatible_local = 363,        /* -Wshadow=compatible-local */
  /* OPT_Wshadow_global = 364, */            /* -Wshadow=global */
  OPT_Wshadow_local = 365,                   /* -Wshadow=local */
  OPT_Wshift_count_negative = 366,           /* -Wshift-count-negative */
  OPT_Wshift_count_overflow = 367,           /* -Wshift-count-overflow */
  OPT_Wshift_negative_value = 368,           /* -Wshift-negative-value */
  /* OPT_Wshift_overflow = 369, */           /* -Wshift-overflow */
  OPT_Wshift_overflow_ = 370,                /* -Wshift-overflow= */
  OPT_Wsign_compare = 371,                   /* -Wsign-compare */
  OPT_Wsign_conversion = 372,                /* -Wsign-conversion */
  OPT_Wsign_promo = 373,                     /* -Wsign-promo */
  OPT_Wsized_deallocation = 374,             /* -Wsized-deallocation */
  OPT_Wsizeof_array_argument = 375,          /* -Wsizeof-array-argument */
  OPT_Wsizeof_pointer_div = 376,             /* -Wsizeof-pointer-div */
  OPT_Wsizeof_pointer_memaccess = 377,       /* -Wsizeof-pointer-memaccess */
  OPT_Wstack_protector = 378,                /* -Wstack-protector */
  OPT_Wstack_usage_ = 379,                   /* -Wstack-usage= */
  OPT_Wstrict_aliasing = 380,                /* -Wstrict-aliasing */
  OPT_Wstrict_aliasing_ = 381,               /* -Wstrict-aliasing= */
  OPT_Wstrict_null_sentinel = 382,           /* -Wstrict-null-sentinel */
  OPT_Wstrict_overflow = 383,                /* -Wstrict-overflow */
  OPT_Wstrict_overflow_ = 384,               /* -Wstrict-overflow= */
  OPT_Wstrict_prototypes = 385,              /* -Wstrict-prototypes */
  OPT_Wstrict_selector_match = 386,          /* -Wstrict-selector-match */
  /* OPT_Wstringop_overflow = 387, */        /* -Wstringop-overflow */
  OPT_Wstringop_overflow_ = 388,             /* -Wstringop-overflow= */
  OPT_Wstringop_truncation = 389,            /* -Wstringop-truncation */
  OPT_Wsubobject_linkage = 390,              /* -Wsubobject-linkage */
  OPT_Wsuggest_attribute_cold = 391,         /* -Wsuggest-attribute=cold */
  OPT_Wsuggest_attribute_const = 392,        /* -Wsuggest-attribute=const */
  OPT_Wsuggest_attribute_format = 393,       /* -Wsuggest-attribute=format */
  OPT_Wsuggest_attribute_malloc = 394,       /* -Wsuggest-attribute=malloc */
  OPT_Wsuggest_attribute_noreturn = 395,     /* -Wsuggest-attribute=noreturn */
  OPT_Wsuggest_attribute_pure = 396,         /* -Wsuggest-attribute=pure */
  OPT_Wsuggest_final_methods = 397,          /* -Wsuggest-final-methods */
  OPT_Wsuggest_final_types = 398,            /* -Wsuggest-final-types */
  OPT_Wsuggest_override = 399,               /* -Wsuggest-override */
  OPT_Wsurprising = 400,                     /* -Wsurprising */
  OPT_Wswitch = 401,                         /* -Wswitch */
  OPT_Wswitch_bool = 402,                    /* -Wswitch-bool */
  OPT_Wswitch_default = 403,                 /* -Wswitch-default */
  OPT_Wswitch_enum = 404,                    /* -Wswitch-enum */
  OPT_Wswitch_unreachable = 405,             /* -Wswitch-unreachable */
  OPT_Wsync_nand = 406,                      /* -Wsync-nand */
  OPT_Wsynth = 407,                          /* -Wsynth */
  OPT_Wsystem_headers = 408,                 /* -Wsystem-headers */
  OPT_Wtabs = 409,                           /* -Wtabs */
  OPT_Wtarget_lifetime = 410,                /* -Wtarget-lifetime */
  OPT_Wtautological_compare = 411,           /* -Wtautological-compare */
  OPT_Wtemplates = 412,                      /* -Wtemplates */
  OPT_Wterminate = 413,                      /* -Wterminate */
  OPT_Wtraditional = 414,                    /* -Wtraditional */
  OPT_Wtraditional_conversion = 415,         /* -Wtraditional-conversion */
  OPT_Wtrampolines = 416,                    /* -Wtrampolines */
  OPT_Wtrigraphs = 417,                      /* -Wtrigraphs */
  OPT_Wtype_limits = 418,                    /* -Wtype-limits */
  OPT_Wundeclared_selector = 419,            /* -Wundeclared-selector */
  OPT_Wundef = 420,                          /* -Wundef */
  OPT_Wundefined_do_loop = 421,              /* -Wundefined-do-loop */
  OPT_Wunderflow = 422,                      /* -Wunderflow */
  OPT_Wuninitialized = 423,                  /* -Wuninitialized */
  OPT_Wunknown_pragmas = 424,                /* -Wunknown-pragmas */
  /* OPT_Wunreachable_code = 425, */         /* -Wunreachable-code */
  /* OPT_Wunsafe_loop_optimizations = 426, *//* -Wunsafe-loop-optimizations */
  OPT_Wunsuffixed_float_constants = 427,     /* -Wunsuffixed-float-constants */
  OPT_Wunused = 428,                         /* -Wunused */
  OPT_Wunused_but_set_parameter = 429,       /* -Wunused-but-set-parameter */
  OPT_Wunused_but_set_variable = 430,        /* -Wunused-but-set-variable */
  /* OPT_Wunused_const_variable = 431, */    /* -Wunused-const-variable */
  OPT_Wunused_const_variable_ = 432,         /* -Wunused-const-variable= */
  OPT_Wunused_dummy_argument = 433,          /* -Wunused-dummy-argument */
  OPT_Wunused_function = 434,                /* -Wunused-function */
  OPT_Wunused_label = 435,                   /* -Wunused-label */
  OPT_Wunused_local_typedefs = 436,          /* -Wunused-local-typedefs */
  OPT_Wunused_macros = 437,                  /* -Wunused-macros */
  OPT_Wunused_parameter = 438,               /* -Wunused-parameter */
  OPT_Wunused_result = 439,                  /* -Wunused-result */
  OPT_Wunused_value = 440,                   /* -Wunused-value */
  OPT_Wunused_variable = 441,                /* -Wunused-variable */
  OPT_Wuse_without_only = 442,               /* -Wuse-without-only */
  OPT_Wuseless_cast = 443,                   /* -Wuseless-cast */
  OPT_Wvarargs = 444,                        /* -Wvarargs */
  OPT_Wvariadic_macros = 445,                /* -Wvariadic-macros */
  OPT_Wvector_operation_performance = 446,   /* -Wvector-operation-performance */
  OPT_Wvirtual_inheritance = 447,            /* -Wvirtual-inheritance */
  OPT_Wvirtual_move_assign = 448,            /* -Wvirtual-move-assign */
  OPT_Wvla = 449,                            /* -Wvla */
  OPT_Wvla_larger_than_ = 450,               /* -Wvla-larger-than= */
  OPT_Wvolatile_register_var = 451,          /* -Wvolatile-register-var */
  OPT_Wwrite_strings = 452,                  /* -Wwrite-strings */
  OPT_Wzero_as_null_pointer_constant = 453,  /* -Wzero-as-null-pointer-constant */
  OPT_Wzerotrip = 454,                       /* -Wzerotrip */
  OPT_Xassembler = 455,                      /* -Xassembler */
  OPT_Xlinker = 456,                         /* -Xlinker */
  OPT_Xpreprocessor = 457,                   /* -Xpreprocessor */
  OPT_Z = 458,                               /* -Z */
  OPT_ansi = 459,                            /* -ansi */
  OPT_aux_info = 460,                        /* -aux-info */
  /* OPT_aux_info_ = 461, */                 /* -aux-info= */
  OPT_auxbase = 462,                         /* -auxbase */
  OPT_auxbase_strip = 463,                   /* -auxbase-strip */
  OPT_c = 464,                               /* -c */
  OPT_coverage = 465,                        /* -coverage */
  OPT_cpp = 466,                             /* -cpp */
  OPT_cpp_ = 467,                            /* -cpp= */
  OPT_d = 468,                               /* -d */
  OPT_dumpbase = 469,                        /* -dumpbase */
  OPT_dumpdir = 470,                         /* -dumpdir */
  OPT_dumpfullversion = 471,                 /* -dumpfullversion */
  OPT_dumpmachine = 472,                     /* -dumpmachine */
  OPT_dumpspecs = 473,                       /* -dumpspecs */
  OPT_dumpversion = 474,                     /* -dumpversion */
  OPT_e = 475,                               /* -e */
  OPT_export_dynamic = 476,                  /* -export-dynamic */
  OPT_fPIC = 477,                            /* -fPIC */
  OPT_fPIE = 478,                            /* -fPIE */
  OPT_fRTS_ = 479,                           /* -fRTS= */
  OPT_fabi_compat_version_ = 480,            /* -fabi-compat-version= */
  OPT_fabi_version_ = 481,                   /* -fabi-version= */
  OPT_faccess_control = 482,                 /* -faccess-control */
  OPT_fada_spec_parent_ = 483,               /* -fada-spec-parent= */
  OPT_faggressive_function_elimination = 484,/* -faggressive-function-elimination */
  OPT_faggressive_loop_optimizations = 485,  /* -faggressive-loop-optimizations */
  OPT_falign_commons = 486,                  /* -falign-commons */
  OPT_falign_functions = 487,                /* -falign-functions */
  OPT_falign_functions_ = 488,               /* -falign-functions= */
  OPT_falign_jumps = 489,                    /* -falign-jumps */
  OPT_falign_jumps_ = 490,                   /* -falign-jumps= */
  OPT_falign_labels = 491,                   /* -falign-labels */
  OPT_falign_labels_ = 492,                  /* -falign-labels= */
  OPT_falign_loops = 493,                    /* -falign-loops */
  OPT_falign_loops_ = 494,                   /* -falign-loops= */
  /* OPT_faligned_new = 495, */              /* -faligned-new */
  OPT_faligned_new_ = 496,                   /* -faligned-new= */
  OPT_fall_intrinsics = 497,                 /* -fall-intrinsics */
  /* OPT_fall_virtual = 498, */              /* -fall-virtual */
  OPT_fallow_leading_underscore = 499,       /* -fallow-leading-underscore */
  OPT_fallow_parameterless_variadic_functions = 500,/* -fallow-parameterless-variadic-functions */
  /* OPT_falt_external_templates = 501, */   /* -falt-external-templates */
  /* OPT_fargument_alias = 502, */           /* -fargument-alias */
  /* OPT_fargument_noalias = 503, */         /* -fargument-noalias */
  /* OPT_fargument_noalias_anything = 504, *//* -fargument-noalias-anything */
  /* OPT_fargument_noalias_global = 505, */  /* -fargument-noalias-global */
  OPT_fasan_shadow_offset_ = 506,            /* -fasan-shadow-offset= */
  OPT_fasm = 507,                            /* -fasm */
  OPT_fassociative_math = 508,               /* -fassociative-math */
  OPT_fasynchronous_unwind_tables = 509,     /* -fasynchronous-unwind-tables */
  OPT_fauto_inc_dec = 510,                   /* -fauto-inc-dec */
  OPT_fauto_profile = 511,                   /* -fauto-profile */
  OPT_fauto_profile_ = 512,                  /* -fauto-profile= */
  OPT_fautomatic = 513,                      /* -fautomatic */
  OPT_fbackslash = 514,                      /* -fbackslash */
  OPT_fbacktrace = 515,                      /* -fbacktrace */
  OPT_fblas_matmul_limit_ = 516,             /* -fblas-matmul-limit= */
  OPT_fbounds_check = 517,                   /* -fbounds-check */
  OPT_fbranch_count_reg = 518,               /* -fbranch-count-reg */
  OPT_fbranch_probabilities = 519,           /* -fbranch-probabilities */
  OPT_fbranch_target_load_optimize = 520,    /* -fbranch-target-load-optimize */
  OPT_fbranch_target_load_optimize2 = 521,   /* -fbranch-target-load-optimize2 */
  OPT_fbtr_bb_exclusive = 522,               /* -fbtr-bb-exclusive */
  OPT_fbuilding_libgcc = 523,                /* -fbuilding-libgcc */
  OPT_fbuiltin = 524,                        /* -fbuiltin */
  OPT_fbuiltin_ = 525,                       /* -fbuiltin- */
  OPT_fbuiltin_printf = 526,                 /* -fbuiltin-printf */
  OPT_fc_prototypes = 527,                   /* -fc-prototypes */
  OPT_fcall_saved_ = 528,                    /* -fcall-saved- */
  OPT_fcall_used_ = 529,                     /* -fcall-used- */
  OPT_fcaller_saves = 530,                   /* -fcaller-saves */
  OPT_fcanonical_system_headers = 531,       /* -fcanonical-system-headers */
  /* OPT_fcf_protection = 532, */            /* -fcf-protection */
  OPT_fcf_protection_ = 533,                 /* -fcf-protection= */
  OPT_fcheck_array_temporaries = 534,        /* -fcheck-array-temporaries */
  OPT_fcheck_data_deps = 535,                /* -fcheck-data-deps */
  OPT_fcheck_new = 536,                      /* -fcheck-new */
  OPT_fcheck_pointer_bounds = 537,           /* -fcheck-pointer-bounds */
  OPT_fcheck_ = 538,                         /* -fcheck= */
  OPT_fchecking = 539,                       /* -fchecking */
  OPT_fchecking_ = 540,                      /* -fchecking= */
  OPT_fchkp_check_incomplete_type = 541,     /* -fchkp-check-incomplete-type */
  OPT_fchkp_check_read = 542,                /* -fchkp-check-read */
  OPT_fchkp_check_write = 543,               /* -fchkp-check-write */
  OPT_fchkp_first_field_has_own_bounds = 544,/* -fchkp-first-field-has-own-bounds */
  OPT_fchkp_flexible_struct_trailing_arrays = 545,/* -fchkp-flexible-struct-trailing-arrays */
  OPT_fchkp_instrument_calls = 546,          /* -fchkp-instrument-calls */
  OPT_fchkp_instrument_marked_only = 547,    /* -fchkp-instrument-marked-only */
  OPT_fchkp_narrow_bounds = 548,             /* -fchkp-narrow-bounds */
  OPT_fchkp_narrow_to_innermost_array = 549, /* -fchkp-narrow-to-innermost-array */
  OPT_fchkp_optimize = 550,                  /* -fchkp-optimize */
  OPT_fchkp_store_bounds = 551,              /* -fchkp-store-bounds */
  OPT_fchkp_treat_zero_dynamic_size_as_infinite = 552,/* -fchkp-treat-zero-dynamic-size-as-infinite */
  OPT_fchkp_use_fast_string_functions = 553, /* -fchkp-use-fast-string-functions */
  OPT_fchkp_use_nochk_string_functions = 554,/* -fchkp-use-nochk-string-functions */
  OPT_fchkp_use_static_bounds = 555,         /* -fchkp-use-static-bounds */
  OPT_fchkp_use_static_const_bounds = 556,   /* -fchkp-use-static-const-bounds */
  OPT_fchkp_use_wrappers = 557,              /* -fchkp-use-wrappers */
  OPT_fchkp_zero_input_bounds_for_main = 558,/* -fchkp-zero-input-bounds-for-main */
  OPT_fcilkplus = 559,                       /* -fcilkplus */
  OPT_fcoarray_ = 560,                       /* -fcoarray= */
  OPT_fcode_hoisting = 561,                  /* -fcode-hoisting */
  OPT_fcombine_stack_adjustments = 562,      /* -fcombine-stack-adjustments */
  OPT_fcommon = 563,                         /* -fcommon */
  OPT_fcompare_debug = 564,                  /* -fcompare-debug */
  OPT_fcompare_debug_second = 565,           /* -fcompare-debug-second */
  OPT_fcompare_debug_ = 566,                 /* -fcompare-debug= */
  OPT_fcompare_elim = 567,                   /* -fcompare-elim */
  OPT_fconcepts = 568,                       /* -fconcepts */
  OPT_fcond_mismatch = 569,                  /* -fcond-mismatch */
  OPT_fconserve_space = 570,                 /* -fconserve-space */
  OPT_fconserve_stack = 571,                 /* -fconserve-stack */
  OPT_fconstant_string_class_ = 572,         /* -fconstant-string-class= */
  OPT_fconstexpr_depth_ = 573,               /* -fconstexpr-depth= */
  OPT_fconstexpr_loop_limit_ = 574,          /* -fconstexpr-loop-limit= */
  OPT_fconvert_ = 575,                       /* -fconvert= */
  OPT_fcprop_registers = 576,                /* -fcprop-registers */
  OPT_fcray_pointer = 577,                   /* -fcray-pointer */
  OPT_fcrossjumping = 578,                   /* -fcrossjumping */
  OPT_fcse_follow_jumps = 579,               /* -fcse-follow-jumps */
  /* OPT_fcse_skip_blocks = 580, */          /* -fcse-skip-blocks */
  OPT_fcx_fortran_rules = 581,               /* -fcx-fortran-rules */
  OPT_fcx_limited_range = 582,               /* -fcx-limited-range */
  OPT_fd_lines_as_code = 583,                /* -fd-lines-as-code */
  OPT_fd_lines_as_comments = 584,            /* -fd-lines-as-comments */
  OPT_fdata_sections = 585,                  /* -fdata-sections */
  OPT_fdbg_cnt_list = 586,                   /* -fdbg-cnt-list */
  OPT_fdbg_cnt_ = 587,                       /* -fdbg-cnt= */
  OPT_fdce = 588,                            /* -fdce */
  OPT_fdebug_cpp = 589,                      /* -fdebug-cpp */
  OPT_fdebug_prefix_map_ = 590,              /* -fdebug-prefix-map= */
  OPT_fdebug_types_section = 591,            /* -fdebug-types-section */
  OPT_fdec = 592,                            /* -fdec */
  OPT_fdec_intrinsic_ints = 593,             /* -fdec-intrinsic-ints */
  OPT_fdec_math = 594,                       /* -fdec-math */
  OPT_fdec_static = 595,                     /* -fdec-static */
  OPT_fdec_structure = 596,                  /* -fdec-structure */
  OPT_fdeclone_ctor_dtor = 597,              /* -fdeclone-ctor-dtor */
  OPT_fdeduce_init_list = 598,               /* -fdeduce-init-list */
  OPT_fdefault_double_8 = 599,               /* -fdefault-double-8 */
  /* OPT_fdefault_inline = 600, */           /* -fdefault-inline */
  OPT_fdefault_integer_8 = 601,              /* -fdefault-integer-8 */
  OPT_fdefault_real_10 = 602,                /* -fdefault-real-10 */
  OPT_fdefault_real_16 = 603,                /* -fdefault-real-16 */
  OPT_fdefault_real_8 = 604,                 /* -fdefault-real-8 */
  OPT_fdefer_pop = 605,                      /* -fdefer-pop */
  OPT_fdelayed_branch = 606,                 /* -fdelayed-branch */
  OPT_fdelete_dead_exceptions = 607,         /* -fdelete-dead-exceptions */
  OPT_fdelete_null_pointer_checks = 608,     /* -fdelete-null-pointer-checks */
  OPT_fdevirtualize = 609,                   /* -fdevirtualize */
  OPT_fdevirtualize_at_ltrans = 610,         /* -fdevirtualize-at-ltrans */
  OPT_fdevirtualize_speculatively = 611,     /* -fdevirtualize-speculatively */
  /* OPT_fdiagnostics_color = 612, */        /* -fdiagnostics-color */
  OPT_fdiagnostics_color_ = 613,             /* -fdiagnostics-color= */
  OPT_fdiagnostics_generate_patch = 614,     /* -fdiagnostics-generate-patch */
  OPT_fdiagnostics_parseable_fixits = 615,   /* -fdiagnostics-parseable-fixits */
  OPT_fdiagnostics_show_caret = 616,         /* -fdiagnostics-show-caret */
  OPT_fdiagnostics_show_location_ = 617,     /* -fdiagnostics-show-location= */
  OPT_fdiagnostics_show_option = 618,        /* -fdiagnostics-show-option */
  OPT_fdiagnostics_show_template_tree = 619, /* -fdiagnostics-show-template-tree */
  OPT_fdirectives_only = 620,                /* -fdirectives-only */
  OPT_fdisable_ = 621,                       /* -fdisable- */
  OPT_fdollar_ok = 622,                      /* -fdollar-ok */
  OPT_fdollars_in_identifiers = 623,         /* -fdollars-in-identifiers */
  OPT_fdse = 624,                            /* -fdse */
  OPT_fdump_ = 625,                          /* -fdump- */
  OPT_fdump_ada_spec = 626,                  /* -fdump-ada-spec */
  OPT_fdump_ada_spec_slim = 627,             /* -fdump-ada-spec-slim */
  /* OPT_fdump_core = 628, */                /* -fdump-core */
  OPT_fdump_final_insns = 629,               /* -fdump-final-insns */
  OPT_fdump_final_insns_ = 630,              /* -fdump-final-insns= */
  OPT_fdump_fortran_optimized = 631,         /* -fdump-fortran-optimized */
  OPT_fdump_fortran_original = 632,          /* -fdump-fortran-original */
  OPT_fdump_go_spec_ = 633,                  /* -fdump-go-spec= */
  OPT_fdump_internal_locations = 634,        /* -fdump-internal-locations */
  OPT_fdump_noaddr = 635,                    /* -fdump-noaddr */
  /* OPT_fdump_parse_tree = 636, */          /* -fdump-parse-tree */
  OPT_fdump_passes = 637,                    /* -fdump-passes */
  OPT_fdump_unnumbered = 638,                /* -fdump-unnumbered */
  OPT_fdump_unnumbered_links = 639,          /* -fdump-unnumbered-links */
  OPT_fdwarf2_cfi_asm = 640,                 /* -fdwarf2-cfi-asm */
  OPT_fearly_inlining = 641,                 /* -fearly-inlining */
  OPT_felide_constructors = 642,             /* -felide-constructors */
  OPT_felide_type = 643,                     /* -felide-type */
  /* OPT_feliminate_dwarf2_dups = 644, */    /* -feliminate-dwarf2-dups */
  OPT_feliminate_unused_debug_symbols = 645, /* -feliminate-unused-debug-symbols */
  OPT_feliminate_unused_debug_types = 646,   /* -feliminate-unused-debug-types */
  OPT_femit_class_debug_always = 647,        /* -femit-class-debug-always */
  OPT_femit_struct_debug_baseonly = 648,     /* -femit-struct-debug-baseonly */
  OPT_femit_struct_debug_detailed_ = 649,    /* -femit-struct-debug-detailed= */
  OPT_femit_struct_debug_reduced = 650,      /* -femit-struct-debug-reduced */
  OPT_fenable_ = 651,                        /* -fenable- */
  OPT_fenforce_eh_specs = 652,               /* -fenforce-eh-specs */
  /* OPT_fenum_int_equiv = 653, */           /* -fenum-int-equiv */
  OPT_fexceptions = 654,                     /* -fexceptions */
  OPT_fexcess_precision_ = 655,              /* -fexcess-precision= */
  OPT_fexec_charset_ = 656,                  /* -fexec-charset= */
  OPT_fexpensive_optimizations = 657,        /* -fexpensive-optimizations */
  OPT_fext_numeric_literals = 658,           /* -fext-numeric-literals */
  OPT_fextended_identifiers = 659,           /* -fextended-identifiers */
  OPT_fextern_tls_init = 660,                /* -fextern-tls-init */
  OPT_fexternal_blas = 661,                  /* -fexternal-blas */
  /* OPT_fexternal_templates = 662, */       /* -fexternal-templates */
  OPT_ff2c = 663,                            /* -ff2c */
  OPT_ffast_math = 664,                      /* -ffast-math */
  OPT_ffat_lto_objects = 665,                /* -ffat-lto-objects */
  OPT_ffile_prefix_map_ = 666,               /* -ffile-prefix-map= */
  OPT_ffinite_math_only = 667,               /* -ffinite-math-only */
  OPT_ffixed_ = 668,                         /* -ffixed- */
  OPT_ffixed_form = 669,                     /* -ffixed-form */
  OPT_ffixed_line_length_ = 670,             /* -ffixed-line-length- */
  OPT_ffixed_line_length_none = 671,         /* -ffixed-line-length-none */
  OPT_ffloat_store = 672,                    /* -ffloat-store */
  OPT_ffor_scope = 673,                      /* -ffor-scope */
  /* OPT_fforce_addr = 674, */               /* -fforce-addr */
  OPT_fforward_propagate = 675,              /* -fforward-propagate */
  OPT_ffp_contract_ = 676,                   /* -ffp-contract= */
  OPT_ffp_int_builtin_inexact = 677,         /* -ffp-int-builtin-inexact */
  OPT_ffpe_summary_ = 678,                   /* -ffpe-summary= */
  OPT_ffpe_trap_ = 679,                      /* -ffpe-trap= */
  OPT_ffree_form = 680,                      /* -ffree-form */
  OPT_ffree_line_length_ = 681,              /* -ffree-line-length- */
  OPT_ffree_line_length_none = 682,          /* -ffree-line-length-none */
  OPT_ffreestanding = 683,                   /* -ffreestanding */
  OPT_ffriend_injection = 684,               /* -ffriend-injection */
  OPT_ffrontend_loop_interchange = 685,      /* -ffrontend-loop-interchange */
  OPT_ffrontend_optimize = 686,              /* -ffrontend-optimize */
  OPT_ffunction_cse = 687,                   /* -ffunction-cse */
  OPT_ffunction_sections = 688,              /* -ffunction-sections */
  OPT_fgcse = 689,                           /* -fgcse */
  OPT_fgcse_after_reload = 690,              /* -fgcse-after-reload */
  OPT_fgcse_las = 691,                       /* -fgcse-las */
  OPT_fgcse_lm = 692,                        /* -fgcse-lm */
  OPT_fgcse_sm = 693,                        /* -fgcse-sm */
  OPT_fgimple = 694,                         /* -fgimple */
  OPT_fgnat_encodings_ = 695,                /* -fgnat-encodings= */
  OPT_fgnu_keywords = 696,                   /* -fgnu-keywords */
  OPT_fgnu_runtime = 697,                    /* -fgnu-runtime */
  OPT_fgnu_tm = 698,                         /* -fgnu-tm */
  OPT_fgnu_unique = 699,                     /* -fgnu-unique */
  OPT_fgnu89_inline = 700,                   /* -fgnu89-inline */
  OPT_fgo_c_header_ = 701,                   /* -fgo-c-header= */
  OPT_fgo_check_divide_overflow = 702,       /* -fgo-check-divide-overflow */
  OPT_fgo_check_divide_zero = 703,           /* -fgo-check-divide-zero */
  OPT_fgo_compiling_runtime = 704,           /* -fgo-compiling-runtime */
  OPT_fgo_debug_escape = 705,                /* -fgo-debug-escape */
  OPT_fgo_debug_escape_hash_ = 706,          /* -fgo-debug-escape-hash= */
  OPT_fgo_dump_ = 707,                       /* -fgo-dump- */
  OPT_fgo_optimize_ = 708,                   /* -fgo-optimize- */
  OPT_fgo_pkgpath_ = 709,                    /* -fgo-pkgpath= */
  OPT_fgo_prefix_ = 710,                     /* -fgo-prefix= */
  OPT_fgo_relative_import_path_ = 711,       /* -fgo-relative-import-path= */
  OPT_fgraphite = 712,                       /* -fgraphite */
  OPT_fgraphite_identity = 713,              /* -fgraphite-identity */
  OPT_fguess_branch_probability = 714,       /* -fguess-branch-probability */
  /* OPT_fguiding_decls = 715, */            /* -fguiding-decls */
  /* OPT_fhandle_exceptions = 716, */        /* -fhandle-exceptions */
  /* OPT_fhelp = 717, */                     /* -fhelp */
  /* OPT_fhelp_ = 718, */                    /* -fhelp= */
  OPT_fhoist_adjacent_loads = 719,           /* -fhoist-adjacent-loads */
  /* OPT_fhonor_std = 720, */                /* -fhonor-std */
  OPT_fhosted = 721,                         /* -fhosted */
  /* OPT_fhuge_objects = 722, */             /* -fhuge-objects */
  OPT_fident = 723,                          /* -fident */
  OPT_fif_conversion = 724,                  /* -fif-conversion */
  OPT_fif_conversion2 = 725,                 /* -fif-conversion2 */
  OPT_fimplement_inlines = 726,              /* -fimplement-inlines */
  OPT_fimplicit_inline_templates = 727,      /* -fimplicit-inline-templates */
  OPT_fimplicit_none = 728,                  /* -fimplicit-none */
  OPT_fimplicit_templates = 729,             /* -fimplicit-templates */
  OPT_findirect_inlining = 730,              /* -findirect-inlining */
  OPT_finhibit_size_directive = 731,         /* -finhibit-size-directive */
  OPT_finit_character_ = 732,                /* -finit-character= */
  OPT_finit_derived = 733,                   /* -finit-derived */
  OPT_finit_integer_ = 734,                  /* -finit-integer= */
  OPT_finit_local_zero = 735,                /* -finit-local-zero */
  OPT_finit_logical_ = 736,                  /* -finit-logical= */
  OPT_finit_real_ = 737,                     /* -finit-real= */
  OPT_finline = 738,                         /* -finline */
  OPT_finline_atomics = 739,                 /* -finline-atomics */
  OPT_finline_functions = 740,               /* -finline-functions */
  OPT_finline_functions_called_once = 741,   /* -finline-functions-called-once */
  /* OPT_finline_limit_ = 742, */            /* -finline-limit- */
  OPT_finline_limit_ = 743,                  /* -finline-limit= */
  OPT_finline_matmul_limit_ = 744,           /* -finline-matmul-limit= */
  OPT_finline_small_functions = 745,         /* -finline-small-functions */
  OPT_finput_charset_ = 746,                 /* -finput-charset= */
  OPT_finstrument_functions = 747,           /* -finstrument-functions */
  OPT_finstrument_functions_exclude_file_list_ = 748,/* -finstrument-functions-exclude-file-list= */
  OPT_finstrument_functions_exclude_function_list_ = 749,/* -finstrument-functions-exclude-function-list= */
  OPT_finteger_4_integer_8 = 750,            /* -finteger-4-integer-8 */
  OPT_fintrinsic_modules_path = 751,         /* -fintrinsic-modules-path */
  OPT_fintrinsic_modules_path_ = 752,        /* -fintrinsic-modules-path= */
  OPT_fipa_bit_cp = 753,                     /* -fipa-bit-cp */
  OPT_fipa_cp = 754,                         /* -fipa-cp */
  /* OPT_fipa_cp_alignment = 755, */         /* -fipa-cp-alignment */
  OPT_fipa_cp_clone = 756,                   /* -fipa-cp-clone */
  OPT_fipa_icf = 757,                        /* -fipa-icf */
  OPT_fipa_icf_functions = 758,              /* -fipa-icf-functions */
  OPT_fipa_icf_variables = 759,              /* -fipa-icf-variables */
  /* OPT_fipa_matrix_reorg = 760, */         /* -fipa-matrix-reorg */
  OPT_fipa_profile = 761,                    /* -fipa-profile */
  OPT_fipa_pta = 762,                        /* -fipa-pta */
  OPT_fipa_pure_const = 763,                 /* -fipa-pure-const */
  OPT_fipa_ra = 764,                         /* -fipa-ra */
  OPT_fipa_reference = 765,                  /* -fipa-reference */
  OPT_fipa_sra = 766,                        /* -fipa-sra */
  /* OPT_fipa_struct_reorg = 767, */         /* -fipa-struct-reorg */
  OPT_fipa_vrp = 768,                        /* -fipa-vrp */
  OPT_fira_algorithm_ = 769,                 /* -fira-algorithm= */
  OPT_fira_hoist_pressure = 770,             /* -fira-hoist-pressure */
  OPT_fira_loop_pressure = 771,              /* -fira-loop-pressure */
  OPT_fira_region_ = 772,                    /* -fira-region= */
  OPT_fira_share_save_slots = 773,           /* -fira-share-save-slots */
  OPT_fira_share_spill_slots = 774,          /* -fira-share-spill-slots */
  OPT_fira_verbose_ = 775,                   /* -fira-verbose= */
  OPT_fisolate_erroneous_paths_attribute = 776,/* -fisolate-erroneous-paths-attribute */
  OPT_fisolate_erroneous_paths_dereference = 777,/* -fisolate-erroneous-paths-dereference */
  OPT_fivar_visibility_ = 778,               /* -fivar-visibility= */
  OPT_fivopts = 779,                         /* -fivopts */
  OPT_fjump_tables = 780,                    /* -fjump-tables */
  OPT_fkeep_gc_roots_live = 781,             /* -fkeep-gc-roots-live */
  OPT_fkeep_inline_dllexport = 782,          /* -fkeep-inline-dllexport */
  OPT_fkeep_inline_functions = 783,          /* -fkeep-inline-functions */
  OPT_fkeep_static_consts = 784,             /* -fkeep-static-consts */
  OPT_fkeep_static_functions = 785,          /* -fkeep-static-functions */
  /* OPT_flabels_ok = 786, */                /* -flabels-ok */
  OPT_flax_vector_conversions = 787,         /* -flax-vector-conversions */
  OPT_fleading_underscore = 788,             /* -fleading-underscore */
  OPT_flifetime_dse = 789,                   /* -flifetime-dse */
  OPT_flifetime_dse_ = 790,                  /* -flifetime-dse= */
  OPT_flimit_function_alignment = 791,       /* -flimit-function-alignment */
  OPT_flinker_output_ = 792,                 /* -flinker-output= */
  OPT_flive_range_shrinkage = 793,           /* -flive-range-shrinkage */
  OPT_flocal_ivars = 794,                    /* -flocal-ivars */
  /* OPT_floop_block = 795, */               /* -floop-block */
  /* OPT_floop_flatten = 796, */             /* -floop-flatten */
  OPT_floop_interchange = 797,               /* -floop-interchange */
  OPT_floop_nest_optimize = 798,             /* -floop-nest-optimize */
  /* OPT_floop_optimize = 799, */            /* -floop-optimize */
  OPT_floop_parallelize_all = 800,           /* -floop-parallelize-all */
  /* OPT_floop_strip_mine = 801, */          /* -floop-strip-mine */
  OPT_floop_unroll_and_jam = 802,            /* -floop-unroll-and-jam */
  OPT_flra_remat = 803,                      /* -flra-remat */
  OPT_flto = 804,                            /* -flto */
  OPT_flto_compression_level_ = 805,         /* -flto-compression-level= */
  OPT_flto_odr_type_merging = 806,           /* -flto-odr-type-merging */
  OPT_flto_partition_ = 807,                 /* -flto-partition= */
  OPT_flto_report = 808,                     /* -flto-report */
  OPT_flto_report_wpa = 809,                 /* -flto-report-wpa */
  OPT_flto_ = 810,                           /* -flto= */
  OPT_fltrans = 811,                         /* -fltrans */
  OPT_fltrans_output_list_ = 812,            /* -fltrans-output-list= */
  OPT_fmacro_prefix_map_ = 813,              /* -fmacro-prefix-map= */
  OPT_fmath_errno = 814,                     /* -fmath-errno */
  OPT_fmax_array_constructor_ = 815,         /* -fmax-array-constructor= */
  OPT_fmax_errors_ = 816,                    /* -fmax-errors= */
  OPT_fmax_identifier_length_ = 817,         /* -fmax-identifier-length= */
  OPT_fmax_stack_var_size_ = 818,            /* -fmax-stack-var-size= */
  OPT_fmax_subrecord_length_ = 819,          /* -fmax-subrecord-length= */
  OPT_fmem_report = 820,                     /* -fmem-report */
  OPT_fmem_report_wpa = 821,                 /* -fmem-report-wpa */
  OPT_fmerge_all_constants = 822,            /* -fmerge-all-constants */
  OPT_fmerge_constants = 823,                /* -fmerge-constants */
  OPT_fmerge_debug_strings = 824,            /* -fmerge-debug-strings */
  OPT_fmessage_length_ = 825,                /* -fmessage-length= */
  OPT_fmodule_private = 826,                 /* -fmodule-private */
  OPT_fmodulo_sched = 827,                   /* -fmodulo-sched */
  OPT_fmodulo_sched_allow_regmoves = 828,    /* -fmodulo-sched-allow-regmoves */
  OPT_fmove_loop_invariants = 829,           /* -fmove-loop-invariants */
  OPT_fms_extensions = 830,                  /* -fms-extensions */
  /* OPT_fmudflap = 831, */                  /* -fmudflap */
  /* OPT_fmudflapir = 832, */                /* -fmudflapir */
  /* OPT_fmudflapth = 833, */                /* -fmudflapth */
  /* OPT_fname_mangling_version_ = 834, */   /* -fname-mangling-version- */
  /* OPT_fnew_abi = 835, */                  /* -fnew-abi */
  OPT_fnew_inheriting_ctors = 836,           /* -fnew-inheriting-ctors */
  OPT_fnew_ttp_matching = 837,               /* -fnew-ttp-matching */
  OPT_fnext_runtime = 838,                   /* -fnext-runtime */
  OPT_fnil_receivers = 839,                  /* -fnil-receivers */
  OPT_fnon_call_exceptions = 840,            /* -fnon-call-exceptions */
  OPT_fnonansi_builtins = 841,               /* -fnonansi-builtins */
  /* OPT_fnonnull_objects = 842, */          /* -fnonnull-objects */
  OPT_fnothrow_opt = 843,                    /* -fnothrow-opt */
  OPT_fobjc_abi_version_ = 844,              /* -fobjc-abi-version= */
  OPT_fobjc_call_cxx_cdtors = 845,           /* -fobjc-call-cxx-cdtors */
  OPT_fobjc_direct_dispatch = 846,           /* -fobjc-direct-dispatch */
  OPT_fobjc_exceptions = 847,                /* -fobjc-exceptions */
  OPT_fobjc_gc = 848,                        /* -fobjc-gc */
  OPT_fobjc_nilcheck = 849,                  /* -fobjc-nilcheck */
  OPT_fobjc_sjlj_exceptions = 850,           /* -fobjc-sjlj-exceptions */
  OPT_fobjc_std_objc1 = 851,                 /* -fobjc-std=objc1 */
  OPT_foffload_abi_ = 852,                   /* -foffload-abi= */
  OPT_foffload_ = 853,                       /* -foffload= */
  OPT_fomit_frame_pointer = 854,             /* -fomit-frame-pointer */
  OPT_fopenacc = 855,                        /* -fopenacc */
  OPT_fopenacc_dim_ = 856,                   /* -fopenacc-dim= */
  OPT_fopenmp = 857,                         /* -fopenmp */
  OPT_fopenmp_simd = 858,                    /* -fopenmp-simd */
  OPT_foperator_names = 859,                 /* -foperator-names */
  OPT_fopt_info = 860,                       /* -fopt-info */
  OPT_fopt_info_ = 861,                      /* -fopt-info- */
  /* OPT_foptimize_register_move = 862, */   /* -foptimize-register-move */
  OPT_foptimize_sibling_calls = 863,         /* -foptimize-sibling-calls */
  OPT_foptimize_strlen = 864,                /* -foptimize-strlen */
  /* OPT_foptional_diags = 865, */           /* -foptional-diags */
  OPT_fpack_derived = 866,                   /* -fpack-derived */
  OPT_fpack_struct = 867,                    /* -fpack-struct */
  OPT_fpack_struct_ = 868,                   /* -fpack-struct= */
  OPT_fpartial_inlining = 869,               /* -fpartial-inlining */
  OPT_fpatchable_function_entry_ = 870,      /* -fpatchable-function-entry= */
  OPT_fpcc_struct_return = 871,              /* -fpcc-struct-return */
  OPT_fpch_deps = 872,                       /* -fpch-deps */
  OPT_fpch_preprocess = 873,                 /* -fpch-preprocess */
  OPT_fpeel_loops = 874,                     /* -fpeel-loops */
  OPT_fpeephole = 875,                       /* -fpeephole */
  OPT_fpeephole2 = 876,                      /* -fpeephole2 */
  OPT_fpermissive = 877,                     /* -fpermissive */
  OPT_fpermitted_flt_eval_methods_ = 878,    /* -fpermitted-flt-eval-methods= */
  OPT_fpic = 879,                            /* -fpic */
  OPT_fpie = 880,                            /* -fpie */
  OPT_fplan9_extensions = 881,               /* -fplan9-extensions */
  OPT_fplt = 882,                            /* -fplt */
  OPT_fplugin_arg_ = 883,                    /* -fplugin-arg- */
  OPT_fplugin_ = 884,                        /* -fplugin= */
  OPT_fpost_ipa_mem_report = 885,            /* -fpost-ipa-mem-report */
  OPT_fpre_ipa_mem_report = 886,             /* -fpre-ipa-mem-report */
  OPT_fpredictive_commoning = 887,           /* -fpredictive-commoning */
  OPT_fprefetch_loop_arrays = 888,           /* -fprefetch-loop-arrays */
  OPT_fpreprocessed = 889,                   /* -fpreprocessed */
  OPT_fpretty_templates = 890,               /* -fpretty-templates */
  OPT_fprintf_return_value = 891,            /* -fprintf-return-value */
  OPT_fprofile = 892,                        /* -fprofile */
  OPT_fprofile_abs_path = 893,               /* -fprofile-abs-path */
  OPT_fprofile_arcs = 894,                   /* -fprofile-arcs */
  OPT_fprofile_correction = 895,             /* -fprofile-correction */
  OPT_fprofile_dir_ = 896,                   /* -fprofile-dir= */
  OPT_fprofile_generate = 897,               /* -fprofile-generate */
  OPT_fprofile_generate_ = 898,              /* -fprofile-generate= */
  OPT_fprofile_reorder_functions = 899,      /* -fprofile-reorder-functions */
  OPT_fprofile_report = 900,                 /* -fprofile-report */
  OPT_fprofile_update_ = 901,                /* -fprofile-update= */
  OPT_fprofile_use = 902,                    /* -fprofile-use */
  OPT_fprofile_use_ = 903,                   /* -fprofile-use= */
  OPT_fprofile_values = 904,                 /* -fprofile-values */
  OPT_fprotect_parens = 905,                 /* -fprotect-parens */
  OPT_frandom_seed = 906,                    /* -frandom-seed */
  OPT_frandom_seed_ = 907,                   /* -frandom-seed= */
  OPT_frange_check = 908,                    /* -frange-check */
  OPT_freal_4_real_10 = 909,                 /* -freal-4-real-10 */
  OPT_freal_4_real_16 = 910,                 /* -freal-4-real-16 */
  OPT_freal_4_real_8 = 911,                  /* -freal-4-real-8 */
  OPT_freal_8_real_10 = 912,                 /* -freal-8-real-10 */
  OPT_freal_8_real_16 = 913,                 /* -freal-8-real-16 */
  OPT_freal_8_real_4 = 914,                  /* -freal-8-real-4 */
  OPT_frealloc_lhs = 915,                    /* -frealloc-lhs */
  OPT_freciprocal_math = 916,                /* -freciprocal-math */
  OPT_frecord_gcc_switches = 917,            /* -frecord-gcc-switches */
  OPT_frecord_marker_4 = 918,                /* -frecord-marker=4 */
  OPT_frecord_marker_8 = 919,                /* -frecord-marker=8 */
  OPT_frecursive = 920,                      /* -frecursive */
  OPT_free = 921,                            /* -free */
  OPT_freg_struct_return = 922,              /* -freg-struct-return */
  /* OPT_fregmove = 923, */                  /* -fregmove */
  OPT_frename_registers = 924,               /* -frename-registers */
  OPT_freorder_blocks = 925,                 /* -freorder-blocks */
  OPT_freorder_blocks_algorithm_ = 926,      /* -freorder-blocks-algorithm= */
  OPT_freorder_blocks_and_partition = 927,   /* -freorder-blocks-and-partition */
  OPT_freorder_functions = 928,              /* -freorder-functions */
  OPT_frepack_arrays = 929,                  /* -frepack-arrays */
  OPT_freplace_objc_classes = 930,           /* -freplace-objc-classes */
  OPT_frepo = 931,                           /* -frepo */
  OPT_freport_bug = 932,                     /* -freport-bug */
  OPT_frequire_return_statement = 933,       /* -frequire-return-statement */
  OPT_frerun_cse_after_loop = 934,           /* -frerun-cse-after-loop */
  /* OPT_frerun_loop_opt = 935, */           /* -frerun-loop-opt */
  OPT_freschedule_modulo_scheduled_loops = 936,/* -freschedule-modulo-scheduled-loops */
  OPT_fresolution_ = 937,                    /* -fresolution= */
  OPT_frounding_math = 938,                  /* -frounding-math */
  OPT_frtti = 939,                           /* -frtti */
  OPT_fsanitize_address_use_after_scope = 940,/* -fsanitize-address-use-after-scope */
  OPT_fsanitize_coverage_ = 941,             /* -fsanitize-coverage= */
  OPT_fsanitize_recover = 942,               /* -fsanitize-recover */
  OPT_fsanitize_recover_ = 943,              /* -fsanitize-recover= */
  OPT_fsanitize_sections_ = 944,             /* -fsanitize-sections= */
  OPT_fsanitize_undefined_trap_on_error = 945,/* -fsanitize-undefined-trap-on-error */
  OPT_fsanitize_ = 946,                      /* -fsanitize= */
  OPT_fsched_critical_path_heuristic = 947,  /* -fsched-critical-path-heuristic */
  OPT_fsched_dep_count_heuristic = 948,      /* -fsched-dep-count-heuristic */
  OPT_fsched_group_heuristic = 949,          /* -fsched-group-heuristic */
  OPT_fsched_interblock = 950,               /* -fsched-interblock */
  OPT_fsched_last_insn_heuristic = 951,      /* -fsched-last-insn-heuristic */
  OPT_fsched_pressure = 952,                 /* -fsched-pressure */
  OPT_fsched_rank_heuristic = 953,           /* -fsched-rank-heuristic */
  OPT_fsched_spec = 954,                     /* -fsched-spec */
  OPT_fsched_spec_insn_heuristic = 955,      /* -fsched-spec-insn-heuristic */
  OPT_fsched_spec_load = 956,                /* -fsched-spec-load */
  OPT_fsched_spec_load_dangerous = 957,      /* -fsched-spec-load-dangerous */
  OPT_fsched_stalled_insns = 958,            /* -fsched-stalled-insns */
  OPT_fsched_stalled_insns_dep = 959,        /* -fsched-stalled-insns-dep */
  OPT_fsched_stalled_insns_dep_ = 960,       /* -fsched-stalled-insns-dep= */
  OPT_fsched_stalled_insns_ = 961,           /* -fsched-stalled-insns= */
  OPT_fsched_verbose_ = 962,                 /* -fsched-verbose= */
  OPT_fsched2_use_superblocks = 963,         /* -fsched2-use-superblocks */
  /* OPT_fsched2_use_traces = 964, */        /* -fsched2-use-traces */
  OPT_fschedule_fusion = 965,                /* -fschedule-fusion */
  OPT_fschedule_insns = 966,                 /* -fschedule-insns */
  OPT_fschedule_insns2 = 967,                /* -fschedule-insns2 */
  OPT_fsecond_underscore = 968,              /* -fsecond-underscore */
  OPT_fsection_anchors = 969,                /* -fsection-anchors */
  /* OPT_fsee = 970, */                      /* -fsee */
  OPT_fsel_sched_pipelining = 971,           /* -fsel-sched-pipelining */
  OPT_fsel_sched_pipelining_outer_loops = 972,/* -fsel-sched-pipelining-outer-loops */
  OPT_fsel_sched_reschedule_pipelined = 973, /* -fsel-sched-reschedule-pipelined */
  OPT_fselective_scheduling = 974,           /* -fselective-scheduling */
  OPT_fselective_scheduling2 = 975,          /* -fselective-scheduling2 */
  OPT_fself_test_ = 976,                     /* -fself-test= */
  OPT_fsemantic_interposition = 977,         /* -fsemantic-interposition */
  OPT_fshort_enums = 978,                    /* -fshort-enums */
  OPT_fshort_wchar = 979,                    /* -fshort-wchar */
  OPT_fshow_column = 980,                    /* -fshow-column */
  OPT_fshrink_wrap = 981,                    /* -fshrink-wrap */
  OPT_fshrink_wrap_separate = 982,           /* -fshrink-wrap-separate */
  OPT_fsign_zero = 983,                      /* -fsign-zero */
  OPT_fsignaling_nans = 984,                 /* -fsignaling-nans */
  OPT_fsigned_bitfields = 985,               /* -fsigned-bitfields */
  OPT_fsigned_char = 986,                    /* -fsigned-char */
  OPT_fsigned_zeros = 987,                   /* -fsigned-zeros */
  OPT_fsimd_cost_model_ = 988,               /* -fsimd-cost-model= */
  OPT_fsingle_precision_constant = 989,      /* -fsingle-precision-constant */
  OPT_fsized_deallocation = 990,             /* -fsized-deallocation */
  OPT_fsplit_ivs_in_unroller = 991,          /* -fsplit-ivs-in-unroller */
  OPT_fsplit_loops = 992,                    /* -fsplit-loops */
  OPT_fsplit_paths = 993,                    /* -fsplit-paths */
  OPT_fsplit_stack = 994,                    /* -fsplit-stack */
  OPT_fsplit_wide_types = 995,               /* -fsplit-wide-types */
  /* OPT_fsquangle = 996, */                 /* -fsquangle */
  OPT_fssa_backprop = 997,                   /* -fssa-backprop */
  OPT_fssa_phiopt = 998,                     /* -fssa-phiopt */
  OPT_fsso_struct_ = 999,                    /* -fsso-struct= */
  OPT_fstack_arrays = 1000,                  /* -fstack-arrays */
  /* OPT_fstack_check = 1001, */             /* -fstack-check */
  OPT_fstack_check_ = 1002,                  /* -fstack-check= */
  OPT_fstack_clash_protection = 1003,        /* -fstack-clash-protection */
  OPT_fstack_limit = 1004,                   /* -fstack-limit */
  OPT_fstack_limit_register_ = 1005,         /* -fstack-limit-register= */
  OPT_fstack_limit_symbol_ = 1006,           /* -fstack-limit-symbol= */
  OPT_fstack_protector = 1007,               /* -fstack-protector */
  OPT_fstack_protector_all = 1008,           /* -fstack-protector-all */
  OPT_fstack_protector_explicit = 1009,      /* -fstack-protector-explicit */
  OPT_fstack_protector_strong = 1010,        /* -fstack-protector-strong */
  OPT_fstack_reuse_ = 1011,                  /* -fstack-reuse= */
  OPT_fstack_usage = 1012,                   /* -fstack-usage */
  OPT_fstats = 1013,                         /* -fstats */
  OPT_fstdarg_opt = 1014,                    /* -fstdarg-opt */
  OPT_fstore_merging = 1015,                 /* -fstore-merging */
  /* OPT_fstrength_reduce = 1016, */         /* -fstrength-reduce */
  OPT_fstrict_aliasing = 1017,               /* -fstrict-aliasing */
  OPT_fstrict_enums = 1018,                  /* -fstrict-enums */
  OPT_fstrict_overflow = 1019,               /* -fstrict-overflow */
  /* OPT_fstrict_prototype = 1020, */        /* -fstrict-prototype */
  OPT_fstrict_volatile_bitfields = 1021,     /* -fstrict-volatile-bitfields */
  /* OPT_fstrong_eval_order = 1022, */       /* -fstrong-eval-order */
  OPT_fstrong_eval_order_ = 1023,            /* -fstrong-eval-order= */
  OPT_fsync_libcalls = 1024,                 /* -fsync-libcalls */
  OPT_fsyntax_only = 1025,                   /* -fsyntax-only */
  OPT_ftabstop_ = 1026,                      /* -ftabstop= */
  /* OPT_ftarget_help = 1027, */             /* -ftarget-help */
  OPT_ftemplate_backtrace_limit_ = 1028,     /* -ftemplate-backtrace-limit= */
  /* OPT_ftemplate_depth_ = 1029, */         /* -ftemplate-depth- */
  OPT_ftemplate_depth_ = 1030,               /* -ftemplate-depth= */
  OPT_ftest_coverage = 1031,                 /* -ftest-coverage */
  OPT_ftest_forall_temp = 1032,              /* -ftest-forall-temp */
  /* OPT_fthis_is_variable = 1033, */        /* -fthis-is-variable */
  OPT_fthread_jumps = 1034,                  /* -fthread-jumps */
  OPT_fthreadsafe_statics = 1035,            /* -fthreadsafe-statics */
  OPT_ftime_report = 1036,                   /* -ftime-report */
  OPT_ftime_report_details = 1037,           /* -ftime-report-details */
  OPT_ftls_model_ = 1038,                    /* -ftls-model= */
  OPT_ftoplevel_reorder = 1039,              /* -ftoplevel-reorder */
  OPT_ftracer = 1040,                        /* -ftracer */
  OPT_ftrack_macro_expansion = 1041,         /* -ftrack-macro-expansion */
  OPT_ftrack_macro_expansion_ = 1042,        /* -ftrack-macro-expansion= */
  OPT_ftrampolines = 1043,                   /* -ftrampolines */
  OPT_ftrapping_math = 1044,                 /* -ftrapping-math */
  OPT_ftrapv = 1045,                         /* -ftrapv */
  OPT_ftree_bit_ccp = 1046,                  /* -ftree-bit-ccp */
  OPT_ftree_builtin_call_dce = 1047,         /* -ftree-builtin-call-dce */
  OPT_ftree_ccp = 1048,                      /* -ftree-ccp */
  OPT_ftree_ch = 1049,                       /* -ftree-ch */
  /* OPT_ftree_coalesce_inlined_vars = 1050, *//* -ftree-coalesce-inlined-vars */
  OPT_ftree_coalesce_vars = 1051,            /* -ftree-coalesce-vars */
  OPT_ftree_copy_prop = 1052,                /* -ftree-copy-prop */
  /* OPT_ftree_copyrename = 1053, */         /* -ftree-copyrename */
  OPT_ftree_cselim = 1054,                   /* -ftree-cselim */
  OPT_ftree_dce = 1055,                      /* -ftree-dce */
  OPT_ftree_dominator_opts = 1056,           /* -ftree-dominator-opts */
  OPT_ftree_dse = 1057,                      /* -ftree-dse */
  OPT_ftree_forwprop = 1058,                 /* -ftree-forwprop */
  OPT_ftree_fre = 1059,                      /* -ftree-fre */
  OPT_ftree_loop_distribute_patterns = 1060, /* -ftree-loop-distribute-patterns */
  OPT_ftree_loop_distribution = 1061,        /* -ftree-loop-distribution */
  OPT_ftree_loop_if_convert = 1062,          /* -ftree-loop-if-convert */
  /* OPT_ftree_loop_if_convert_stores = 1063, *//* -ftree-loop-if-convert-stores */
  OPT_ftree_loop_im = 1064,                  /* -ftree-loop-im */
  OPT_ftree_loop_ivcanon = 1065,             /* -ftree-loop-ivcanon */
  /* OPT_ftree_loop_linear = 1066, */        /* -ftree-loop-linear */
  OPT_ftree_loop_optimize = 1067,            /* -ftree-loop-optimize */
  OPT_ftree_loop_vectorize = 1068,           /* -ftree-loop-vectorize */
  OPT_ftree_lrs = 1069,                      /* -ftree-lrs */
  OPT_ftree_parallelize_loops_ = 1070,       /* -ftree-parallelize-loops= */
  OPT_ftree_partial_pre = 1071,              /* -ftree-partial-pre */
  OPT_ftree_phiprop = 1072,                  /* -ftree-phiprop */
  OPT_ftree_pre = 1073,                      /* -ftree-pre */
  OPT_ftree_pta = 1074,                      /* -ftree-pta */
  OPT_ftree_reassoc = 1075,                  /* -ftree-reassoc */
  /* OPT_ftree_salias = 1076, */             /* -ftree-salias */
  OPT_ftree_scev_cprop = 1077,               /* -ftree-scev-cprop */
  OPT_ftree_sink = 1078,                     /* -ftree-sink */
  OPT_ftree_slp_vectorize = 1079,            /* -ftree-slp-vectorize */
  OPT_ftree_slsr = 1080,                     /* -ftree-slsr */
  OPT_ftree_sra = 1081,                      /* -ftree-sra */
  /* OPT_ftree_store_ccp = 1082, */          /* -ftree-store-ccp */
  /* OPT_ftree_store_copy_prop = 1083, */    /* -ftree-store-copy-prop */
  OPT_ftree_switch_conversion = 1084,        /* -ftree-switch-conversion */
  OPT_ftree_tail_merge = 1085,               /* -ftree-tail-merge */
  OPT_ftree_ter = 1086,                      /* -ftree-ter */
  /* OPT_ftree_vect_loop_version = 1087, */  /* -ftree-vect-loop-version */
  OPT_ftree_vectorize = 1088,                /* -ftree-vectorize */
  /* OPT_ftree_vectorizer_verbose_ = 1089, *//* -ftree-vectorizer-verbose= */
  OPT_ftree_vrp = 1090,                      /* -ftree-vrp */
  OPT_funconstrained_commons = 1091,         /* -funconstrained-commons */
  OPT_funderscoring = 1092,                  /* -funderscoring */
  OPT_funit_at_a_time = 1093,                /* -funit-at-a-time */
  OPT_funroll_all_loops = 1094,              /* -funroll-all-loops */
  OPT_funroll_loops = 1095,                  /* -funroll-loops */
  /* OPT_funsafe_loop_optimizations = 1096, *//* -funsafe-loop-optimizations */
  OPT_funsafe_math_optimizations = 1097,     /* -funsafe-math-optimizations */
  OPT_funsigned_bitfields = 1098,            /* -funsigned-bitfields */
  OPT_funsigned_char = 1099,                 /* -funsigned-char */
  OPT_funswitch_loops = 1100,                /* -funswitch-loops */
  OPT_funwind_tables = 1101,                 /* -funwind-tables */
  OPT_fuse_cxa_atexit = 1102,                /* -fuse-cxa-atexit */
  OPT_fuse_cxa_get_exception_ptr = 1103,     /* -fuse-cxa-get-exception-ptr */
  OPT_fuse_ld_bfd = 1104,                    /* -fuse-ld=bfd */
  OPT_fuse_ld_gold = 1105,                   /* -fuse-ld=gold */
  OPT_fuse_linker_plugin = 1106,             /* -fuse-linker-plugin */
  OPT_fvar_tracking = 1107,                  /* -fvar-tracking */
  OPT_fvar_tracking_assignments = 1108,      /* -fvar-tracking-assignments */
  OPT_fvar_tracking_assignments_toggle = 1109,/* -fvar-tracking-assignments-toggle */
  OPT_fvar_tracking_uninit = 1110,           /* -fvar-tracking-uninit */
  OPT_fvariable_expansion_in_unroller = 1111,/* -fvariable-expansion-in-unroller */
  /* OPT_fvect_cost_model = 1112, */         /* -fvect-cost-model */
  OPT_fvect_cost_model_ = 1113,              /* -fvect-cost-model= */
  OPT_fverbose_asm = 1114,                   /* -fverbose-asm */
  /* OPT_fversion = 1115, */                 /* -fversion */
  OPT_fvisibility_inlines_hidden = 1116,     /* -fvisibility-inlines-hidden */
  OPT_fvisibility_ms_compat = 1117,          /* -fvisibility-ms-compat */
  OPT_fvisibility_ = 1118,                   /* -fvisibility= */
  OPT_fvpt = 1119,                           /* -fvpt */
  /* OPT_fvtable_gc = 1120, */               /* -fvtable-gc */
  /* OPT_fvtable_thunks = 1121, */           /* -fvtable-thunks */
  OPT_fvtable_verify_ = 1122,                /* -fvtable-verify= */
  OPT_fvtv_counts = 1123,                    /* -fvtv-counts */
  OPT_fvtv_debug = 1124,                     /* -fvtv-debug */
  OPT_fweak = 1125,                          /* -fweak */
  OPT_fweb = 1126,                           /* -fweb */
  /* OPT_fwhole_file = 1127, */              /* -fwhole-file */
  OPT_fwhole_program = 1128,                 /* -fwhole-program */
  OPT_fwide_exec_charset_ = 1129,            /* -fwide-exec-charset= */
  OPT_fworking_directory = 1130,             /* -fworking-directory */
  OPT_fwpa = 1131,                           /* -fwpa */
  OPT_fwpa_ = 1132,                          /* -fwpa= */
  OPT_fwrapv = 1133,                         /* -fwrapv */
  OPT_fwrapv_pointer = 1134,                 /* -fwrapv-pointer */
  /* OPT_fxref = 1135, */                    /* -fxref */
  /* OPT_fzee = 1136, */                     /* -fzee */
  OPT_fzero_initialized_in_bss = 1137,       /* -fzero-initialized-in-bss */
  OPT_fzero_link = 1138,                     /* -fzero-link */
  OPT_g = 1139,                              /* -g */
  OPT_gant = 1140,                           /* -gant */
  OPT_gas_loc_support = 1141,                /* -gas-loc-support */
  OPT_gas_locview_support = 1142,            /* -gas-locview-support */
  /* OPT_gcoff = 1143, */                    /* -gcoff */
  /* OPT_gcoff1 = 1144, */                   /* -gcoff1 */
  /* OPT_gcoff2 = 1145, */                   /* -gcoff2 */
  /* OPT_gcoff3 = 1146, */                   /* -gcoff3 */
  OPT_gcolumn_info = 1147,                   /* -gcolumn-info */
  OPT_gdwarf = 1148,                         /* -gdwarf */
  OPT_gdwarf_ = 1149,                        /* -gdwarf- */
  OPT_gen_decls = 1150,                      /* -gen-decls */
  OPT_ggdb = 1151,                           /* -ggdb */
  OPT_ggnu_pubnames = 1152,                  /* -ggnu-pubnames */
  OPT_ginline_points = 1153,                 /* -ginline-points */
  OPT_ginternal_reset_location_views = 1154, /* -ginternal-reset-location-views */
  OPT_gnat = 1155,                           /* -gnat */
  OPT_gnatO = 1156,                          /* -gnatO */
  OPT_gno_ = 1157,                           /* -gno- */
  OPT_gno_pubnames = 1158,                   /* -gno-pubnames */
  OPT_gpubnames = 1159,                      /* -gpubnames */
  OPT_grecord_gcc_switches = 1160,           /* -grecord-gcc-switches */
  OPT_gsplit_dwarf = 1161,                   /* -gsplit-dwarf */
  OPT_gstabs = 1162,                         /* -gstabs */
  OPT_gstabs_ = 1163,                        /* -gstabs+ */
  OPT_gstatement_frontiers = 1164,           /* -gstatement-frontiers */
  OPT_gstrict_dwarf = 1165,                  /* -gstrict-dwarf */
  OPT_gtoggle = 1166,                        /* -gtoggle */
  OPT_gvariable_location_views = 1167,       /* -gvariable-location-views */
  OPT_gvariable_location_views_incompat5 = 1168,/* -gvariable-location-views=incompat5 */
  OPT_gvms = 1169,                           /* -gvms */
  OPT_gxcoff = 1170,                         /* -gxcoff */
  OPT_gxcoff_ = 1171,                        /* -gxcoff+ */
  OPT_gz = 1172,                             /* -gz */
  OPT_gz_ = 1173,                            /* -gz= */
  OPT_h = 1174,                              /* -h */
  OPT_idirafter = 1175,                      /* -idirafter */
  OPT_imacros = 1176,                        /* -imacros */
  OPT_imultiarch = 1177,                     /* -imultiarch */
  OPT_imultilib = 1178,                      /* -imultilib */
  OPT_include = 1179,                        /* -include */
  OPT_iplugindir_ = 1180,                    /* -iplugindir= */
  OPT_iprefix = 1181,                        /* -iprefix */
  OPT_iquote = 1182,                         /* -iquote */
  OPT_isysroot = 1183,                       /* -isysroot */
  OPT_isystem = 1184,                        /* -isystem */
  OPT_iwithprefix = 1185,                    /* -iwithprefix */
  OPT_iwithprefixbefore = 1186,              /* -iwithprefixbefore */
  OPT_k8 = 1187,                             /* -k8 */
  OPT_l = 1188,                              /* -l */
  OPT_lang_asm = 1189,                       /* -lang-asm */
  OPT_mabi_ = 1190,                          /* -mabi= */
  OPT_mabort_on_noreturn = 1191,             /* -mabort-on-noreturn */
  OPT_mapcs = 1192,                          /* -mapcs */
  OPT_mapcs_frame = 1193,                    /* -mapcs-frame */
  OPT_mapcs_reentrant = 1194,                /* -mapcs-reentrant */
  OPT_mapcs_stack_check = 1195,              /* -mapcs-stack-check */
  OPT_march_ = 1196,                         /* -march= */
  OPT_marm = 1197,                           /* -marm */
  OPT_masm_syntax_unified = 1198,            /* -masm-syntax-unified */
  OPT_mbe32 = 1199,                          /* -mbe32 */
  OPT_mbe8 = 1200,                           /* -mbe8 */
  OPT_mbig_endian = 1201,                    /* -mbig-endian */
  OPT_mbranch_cost_ = 1202,                  /* -mbranch-cost= */
  OPT_mcallee_super_interworking = 1203,     /* -mcallee-super-interworking */
  OPT_mcaller_super_interworking = 1204,     /* -mcaller-super-interworking */
  OPT_mcmse = 1205,                          /* -mcmse */
  OPT_mcpu_ = 1206,                          /* -mcpu= */
  OPT_mfix_cortex_m3_ldrd = 1207,            /* -mfix-cortex-m3-ldrd */
  OPT_mflip_thumb = 1208,                    /* -mflip-thumb */
  OPT_mfloat_abi_ = 1209,                    /* -mfloat-abi= */
  OPT_mfp16_format_ = 1210,                  /* -mfp16-format= */
  OPT_mfpu_ = 1211,                          /* -mfpu= */
  /* OPT_mhard_float = 1212, */              /* -mhard-float */
  OPT_mlittle_endian = 1213,                 /* -mlittle-endian */
  OPT_mlong_calls = 1214,                    /* -mlong-calls */
  OPT_mneon_for_64bits = 1215,               /* -mneon-for-64bits */
  OPT_mpic_data_is_text_relative = 1216,     /* -mpic-data-is-text-relative */
  OPT_mpic_register_ = 1217,                 /* -mpic-register= */
  OPT_mpoke_function_name = 1218,            /* -mpoke-function-name */
  OPT_mprint_tune_info = 1219,               /* -mprint-tune-info */
  OPT_mpure_code = 1220,                     /* -mpure-code */
  OPT_mrestrict_it = 1221,                   /* -mrestrict-it */
  OPT_msched_prolog = 1222,                  /* -msched-prolog */
  OPT_msingle_pic_base = 1223,               /* -msingle-pic-base */
  OPT_mslow_flash_data = 1224,               /* -mslow-flash-data */
  /* OPT_msoft_float = 1225, */              /* -msoft-float */
  OPT_mstructure_size_boundary_ = 1226,      /* -mstructure-size-boundary= */
  OPT_mthumb = 1227,                         /* -mthumb */
  OPT_mthumb_interwork = 1228,               /* -mthumb-interwork */
  OPT_mtls_dialect_ = 1229,                  /* -mtls-dialect= */
  OPT_mtp_ = 1230,                           /* -mtp= */
  OPT_mtpcs_frame = 1231,                    /* -mtpcs-frame */
  OPT_mtpcs_leaf_frame = 1232,               /* -mtpcs-leaf-frame */
  OPT_mtune_ = 1233,                         /* -mtune= */
  OPT_munaligned_access = 1234,              /* -munaligned-access */
  OPT_mvectorize_with_neon_double = 1235,    /* -mvectorize-with-neon-double */
  OPT_mvectorize_with_neon_quad = 1236,      /* -mvectorize-with-neon-quad */
  OPT_mverbose_cost_dump = 1237,             /* -mverbose-cost-dump */
  OPT_mword_relocations = 1238,              /* -mword-relocations */
  OPT_n = 1239,                              /* -n */
  OPT_no_canonical_prefixes = 1240,          /* -no-canonical-prefixes */
  OPT_no_integrated_cpp = 1241,              /* -no-integrated-cpp */
  OPT_no_pie = 1242,                         /* -no-pie */
  OPT_nocpp = 1243,                          /* -nocpp */
  OPT_nodefaultlibs = 1244,                  /* -nodefaultlibs */
  OPT_nostartfiles = 1245,                   /* -nostartfiles */
  OPT_nostdinc = 1246,                       /* -nostdinc */
  OPT_nostdinc__ = 1247,                     /* -nostdinc++ */
  OPT_nostdlib = 1248,                       /* -nostdlib */
  OPT_o = 1249,                              /* -o */
  OPT_p = 1250,                              /* -p */
  OPT_pass_exit_codes = 1251,                /* -pass-exit-codes */
  /* OPT_pedantic = 1252, */                 /* -pedantic */
  OPT_pedantic_errors = 1253,                /* -pedantic-errors */
  OPT_pg = 1254,                             /* -pg */
  OPT_pie = 1255,                            /* -pie */
  OPT_pipe = 1256,                           /* -pipe */
  OPT_print_file_name_ = 1257,               /* -print-file-name= */
  OPT_print_libgcc_file_name = 1258,         /* -print-libgcc-file-name */
  OPT_print_multi_directory = 1259,          /* -print-multi-directory */
  OPT_print_multi_lib = 1260,                /* -print-multi-lib */
  OPT_print_multi_os_directory = 1261,       /* -print-multi-os-directory */
  OPT_print_multiarch = 1262,                /* -print-multiarch */
  OPT_print_objc_runtime_info = 1263,        /* -print-objc-runtime-info */
  OPT_print_prog_name_ = 1264,               /* -print-prog-name= */
  OPT_print_search_dirs = 1265,              /* -print-search-dirs */
  OPT_print_sysroot = 1266,                  /* -print-sysroot */
  OPT_print_sysroot_headers_suffix = 1267,   /* -print-sysroot-headers-suffix */
  OPT_quiet = 1268,                          /* -quiet */
  OPT_r = 1269,                              /* -r */
  OPT_remap = 1270,                          /* -remap */
  OPT_s = 1271,                              /* -s */
  OPT_save_temps = 1272,                     /* -save-temps */
  OPT_save_temps_ = 1273,                    /* -save-temps= */
  OPT_shared = 1274,                         /* -shared */
  OPT_shared_libgcc = 1275,                  /* -shared-libgcc */
  /* OPT_specs = 1276, */                    /* -specs */
  OPT_specs_ = 1277,                         /* -specs= */
  OPT_static = 1278,                         /* -static */
  OPT_static_libasan = 1279,                 /* -static-libasan */
  OPT_static_libgcc = 1280,                  /* -static-libgcc */
  OPT_static_libgfortran = 1281,             /* -static-libgfortran */
  OPT_static_libgo = 1282,                   /* -static-libgo */
  OPT_static_liblsan = 1283,                 /* -static-liblsan */
  OPT_static_libmpx = 1284,                  /* -static-libmpx */
  OPT_static_libmpxwrappers = 1285,          /* -static-libmpxwrappers */
  OPT_static_libstdc__ = 1286,               /* -static-libstdc++ */
  OPT_static_libtsan = 1287,                 /* -static-libtsan */
  OPT_static_libubsan = 1288,                /* -static-libubsan */
  OPT_static_pie = 1289,                     /* -static-pie */
  /* OPT_std_c__03 = 1290, */                /* -std=c++03 */
  /* OPT_std_c__0x = 1291, */                /* -std=c++0x */
  OPT_std_c__11 = 1292,                      /* -std=c++11 */
  OPT_std_c__14 = 1293,                      /* -std=c++14 */
  OPT_std_c__17 = 1294,                      /* -std=c++17 */
  /* OPT_std_c__1y = 1295, */                /* -std=c++1y */
  /* OPT_std_c__1z = 1296, */                /* -std=c++1z */
  OPT_std_c__2a = 1297,                      /* -std=c++2a */
  OPT_std_c__98 = 1298,                      /* -std=c++98 */
  OPT_std_c11 = 1299,                        /* -std=c11 */
  OPT_std_c17 = 1300,                        /* -std=c17 */
  /* OPT_std_c18 = 1301, */                  /* -std=c18 */
  /* OPT_std_c1x = 1302, */                  /* -std=c1x */
  /* OPT_std_c89 = 1303, */                  /* -std=c89 */
  OPT_std_c90 = 1304,                        /* -std=c90 */
  OPT_std_c99 = 1305,                        /* -std=c99 */
  /* OPT_std_c9x = 1306, */                  /* -std=c9x */
  OPT_std_f2003 = 1307,                      /* -std=f2003 */
  OPT_std_f2008 = 1308,                      /* -std=f2008 */
  OPT_std_f2008ts = 1309,                    /* -std=f2008ts */
  OPT_std_f2018 = 1310,                      /* -std=f2018 */
  OPT_std_f95 = 1311,                        /* -std=f95 */
  OPT_std_gnu = 1312,                        /* -std=gnu */
  /* OPT_std_gnu__03 = 1313, */              /* -std=gnu++03 */
  /* OPT_std_gnu__0x = 1314, */              /* -std=gnu++0x */
  OPT_std_gnu__11 = 1315,                    /* -std=gnu++11 */
  OPT_std_gnu__14 = 1316,                    /* -std=gnu++14 */
  OPT_std_gnu__17 = 1317,                    /* -std=gnu++17 */
  /* OPT_std_gnu__1y = 1318, */              /* -std=gnu++1y */
  /* OPT_std_gnu__1z = 1319, */              /* -std=gnu++1z */
  OPT_std_gnu__2a = 1320,                    /* -std=gnu++2a */
  OPT_std_gnu__98 = 1321,                    /* -std=gnu++98 */
  OPT_std_gnu11 = 1322,                      /* -std=gnu11 */
  OPT_std_gnu17 = 1323,                      /* -std=gnu17 */
  /* OPT_std_gnu18 = 1324, */                /* -std=gnu18 */
  /* OPT_std_gnu1x = 1325, */                /* -std=gnu1x */
  /* OPT_std_gnu89 = 1326, */                /* -std=gnu89 */
  OPT_std_gnu90 = 1327,                      /* -std=gnu90 */
  OPT_std_gnu99 = 1328,                      /* -std=gnu99 */
  /* OPT_std_gnu9x = 1329, */                /* -std=gnu9x */
  /* OPT_std_iso9899_1990 = 1330, */         /* -std=iso9899:1990 */
  OPT_std_iso9899_199409 = 1331,             /* -std=iso9899:199409 */
  /* OPT_std_iso9899_1999 = 1332, */         /* -std=iso9899:1999 */
  /* OPT_std_iso9899_199x = 1333, */         /* -std=iso9899:199x */
  /* OPT_std_iso9899_2011 = 1334, */         /* -std=iso9899:2011 */
  /* OPT_std_iso9899_2017 = 1335, */         /* -std=iso9899:2017 */
  /* OPT_std_iso9899_2018 = 1336, */         /* -std=iso9899:2018 */
  OPT_std_legacy = 1337,                     /* -std=legacy */
  OPT_symbolic = 1338,                       /* -symbolic */
  OPT_t = 1339,                              /* -t */
  OPT_time = 1340,                           /* -time */
  OPT_time_ = 1341,                          /* -time= */
  OPT_traditional = 1342,                    /* -traditional */
  OPT_traditional_cpp = 1343,                /* -traditional-cpp */
  OPT_trigraphs = 1344,                      /* -trigraphs */
  OPT_u = 1345,                              /* -u */
  OPT_undef = 1346,                          /* -undef */
  OPT_v = 1347,                              /* -v */
  OPT_version = 1348,                        /* -version */
  OPT_w = 1349,                              /* -w */
  OPT_wrapper = 1350,                        /* -wrapper */
  OPT_x = 1351,                              /* -x */
  OPT_z = 1352,                              /* -z */
  N_OPTS,
  OPT_SPECIAL_unknown,
  OPT_SPECIAL_ignore,
  OPT_SPECIAL_program_name,
  OPT_SPECIAL_input_file
};

#ifdef GCC_C_COMMON_C
/* Mapping from cpp message reasons to the options that enable them.  */
#include <cpplib.h>
struct cpp_reason_option_codes_t
{
  const int reason;		/* cpplib message reason.  */
  const int option_code;	/* gcc option that controls this message.  */
};

static const struct cpp_reason_option_codes_t cpp_reason_option_codes[] = {
  {CPP_W_BUILTIN_MACRO_REDEFINED,           OPT_Wbuiltin_macro_redefined},
  {CPP_W_CXX_OPERATOR_NAMES,                OPT_Wc___compat},
  {CPP_W_CXX11_COMPAT,                      OPT_Wc__11_compat},
  {CPP_W_C90_C99_COMPAT,                    OPT_Wc90_c99_compat},
  {CPP_W_COMMENTS,                          OPT_Wcomment},
  {CPP_W_WARNING_DIRECTIVE,                 OPT_Wcpp},
  {CPP_W_DATE_TIME,                         OPT_Wdate_time},
  {CPP_W_DEPRECATED,                        OPT_Wdeprecated},
  {CPP_W_ENDIF_LABELS,                      OPT_Wendif_labels},
  {CPP_W_EXPANSION_TO_DEFINED,              OPT_Wexpansion_to_defined},
  {CPP_W_INVALID_PCH,                       OPT_Winvalid_pch},
  {CPP_W_LITERAL_SUFFIX,                    OPT_Wliteral_suffix},
  {CPP_W_LONG_LONG,                         OPT_Wlong_long},
  {CPP_W_MISSING_INCLUDE_DIRS,              OPT_Wmissing_include_dirs},
  {CPP_W_MULTICHAR,                         OPT_Wmultichar},
  {CPP_W_NORMALIZE,                         OPT_Wnormalized_},
  {CPP_W_PEDANTIC,                          OPT_Wpedantic},
  {CPP_W_TRADITIONAL,                       OPT_Wtraditional},
  {CPP_W_TRIGRAPHS,                         OPT_Wtrigraphs},
  {CPP_W_UNDEF,                             OPT_Wundef},
  {CPP_W_UNUSED_MACROS,                     OPT_Wunused_macros},
  {CPP_W_VARIADIC_MACROS,                   OPT_Wvariadic_macros},
  {CPP_W_NONE,                              0},
};
#endif

#endif /* OPTIONS_H */
