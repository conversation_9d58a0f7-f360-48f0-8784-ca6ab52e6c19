/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "ULP-Version-2-parameter-extensions"
 * 	found in "supl202.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_SatellitesListRelatedDataList_H_
#define	_SatellitesListRelatedDataList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct SatellitesListRelatedData;

/* SatellitesListRelatedDataList */
typedef struct SatellitesListRelatedDataList {
	A_SEQUENCE_OF(struct SatellitesListRelatedData) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} SatellitesListRelatedDataList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_SatellitesListRelatedDataList;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "SatellitesListRelatedData.h"

#endif	/* _SatellitesListRelatedDataList_H_ */
#include <asn_internal.h>
