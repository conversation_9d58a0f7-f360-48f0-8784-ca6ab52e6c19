\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\ecp_curves.c
\pcac\mbedTLS\mbedTLS_3_2_1\library\ecp_curves.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\common.h
\pcac\mbedTLS\mbedTLS_3_2_1\library\common.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : \tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h
\tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\bn_mul.h
\pcac\mbedTLS\mbedTLS_3_2_1\library\bn_mul.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\bignum_internal.h
\pcac\mbedTLS\mbedTLS_3_2_1\library\bignum_internal.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ecp_curves.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\ecp_invasive.h
\pcac\mbedTLS\mbedTLS_3_2_1\library\ecp_invasive.h:
