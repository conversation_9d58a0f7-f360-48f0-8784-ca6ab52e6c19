/*------------------------------------------------------------
(C) Copyright [20XX-20XX] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**********************************************************************************************
*
* Filename: pl_schd_common.h
*
* Programmers: Qingwen Wang
*
* Functions:
*
* Description:
*
* -----------------------------------------------------------------------------------------
* Revision History
*
* Date                            Who               Version                            Description
* -----------------------------------------------------------------------------------------
* 15/Dec/2010       Qingwen wang           V0.1                define the basic structs of SCHD
* 
**********************************************************************************************/

#ifndef PL_SCHD_COMMON_H
#define PL_SCHD_COMMON_H

#include "global_def.h"
#include "pl_msr_plp.h"
#include "pl_schd.h"
#include "pl_schd_chains.h"
#include "pl_schd_modes.h"

//Definition enum of task state type
//ICAT EXPORTED ENUM
typedef enum{
    NO_ACK_WAIT   = 0,                               
    WAIT_FOR_SEND = 1,    
    WAIT_FOR_RUN  = 2,
}WbGsmInterState;

typedef struct
{
  //added for redmine 64715
    UINT32 actionType;
	//added for redmine 64715 end
    opera_fun_rf *funRf;
    opera_fun_tracker *funTracker;
    opera_fun_interrupt *funInterrupt;
}actionRscOpera_ts;

/*in WL WB extgap, aplp send the lastruning extgap's finishind at the point of 
original mother gap finishind, and mother gap finishind has send to LTE beforehand
CQ00110614 and CQ00110611*/
typedef struct
{
	Bool    IsExtGapValid;/*TRUE means need to send the lastruning extgap's finishind at the point of original mother gap finishind*/
	UINT16  GapIndex; /*record the lastruning extgap's gapindex*/
	UINT16  MotherGapIndex;
	int     MotherGapType;
   
}extgapparameter;


extern actionRscOpera_ts  	actionOpenRf[SIM_NULL];
extern actionRscOpera_ts  	actionCloseRf[SIM_NULL];
extern SCHD_WB_States		wbNextStateAfterRfAction[SIM_NULL];
extern SCHD_WW_States		wwNextStateAfterRfAction[SIM_NULL];
extern SCHD_WG_States		wgNextStateAfterRfAction[SIM_NULL];
extern SCHD_WL_States		wlNextStateAfterRfAction[SIM_NULL];

extern Bool            SchdFreeBuffer;
extern OSATimerRef     SleepRatioAlgoPreventorTimer;
extern APLP_MSG_QUEUE  MsgSourceQueue[2];

//unify fhguan 20170228
//extern UINT16 PiCbsTimingArrived;
#ifdef APLP_DUALSIM_SUPPORT
extern BOOL PsGapSafetyWndFlg;
#endif
extern Bool ResumeReqSentToGplc;
extern UINT8 ReasonSuspendResumeSimB;

#define SLEEP_RATIO_ALGO_NEED_TIME          (110+ABS_NO_BB_SLEEP_NEED_TIME) //for 32KHz Timter algo + time for AllowBB(5)
#define SLEEP_RATIO_ALGO_TIMER_EXPIRE_TIME  (95) 
#define PS_GAP_SAFETY_WINDOW_SIZE           (70)  // 20*4.615(20GSM frame) - 6(gapTrigger before RTU GSM required) - 3(aplp reserved) - 12(plp reserved) 
#define HIGH_PRI_REQ_PROTECT_TIME           (2000)
#define APLP_MTU_TIME_CALI_PERIOD           (5000)

#if 1// W+L 
#define WB_URGENT_GAP_START_OFFSET         (11) //in ms
/*transfer delaytime(lterejectind) to mtu time, compensate time differece
mainly because latch inaccurate and us to ms inaccurate*/
#define TIME_DIFF_APLP_LTEL1         (2) //in ms

#endif


#define setSchdFreeBuffer(val)     SchdFreeBuffer = val
#define getSchdFreeBuffer()        SchdFreeBuffer
#ifdef APLP_DUALSIM_SUPPORT
#define getPsPagingState()         PsPagingState
#define setPsGapSafetyWndFlg(val)   PsGapSafetyWndFlg = val
#define getPsGapSafetyWndFlg()      PsGapSafetyWndFlg
#endif
#define SUSPEND_WB_PENDED_TASK_BITMAP_MASK  ((1<<STTD_MODIFY_TASK)\
                                            |(1<<RECEIVE_PI)|(1<<SETUP_PICH)|(1<<RELEASE_PICH)\
                                            |(1<<TURN_ON_RAT)|(1<<TURN_OFF_RAT)|(1<<DEACT_TASK)|(1<<HOLD_WCDMA))

void plSchdNullEventHandler(aplp_event event, VOID_PTR msg);
void plSchdAlternateQueue(DualQue dstSim);
void plSchdAlternate2RfQueue(DualQue toBeActiveSim);
void schdEmptyQueue(OSMsgQRef *queuePtr);
void schdPassMsg(aplpMsgHeader *aplpMsg,DualSim SimId_X);
void schdRejectMsg(aplpMsgHeader *aplpMsg, Bool msgNotReceived);
void schdPendMsg(aplpMsgHeader *aplpMsg, Bool msgNotReceived);
VOID setSchdBitmap(UINT8 simx, TaskID_Type taskId, TaskStateType state);
TaskStateType getSchdBitmap(UINT8 simx, TaskID_Type taskId);
void clearSchdBitmap(UINT8 simx, TaskStateType type);
void setSchdRunnBitmap(UINT8 simx, TaskID_Type taskId);
void rstSchdRunnBitmap(UINT8 simx, TaskID_Type taskId);
void setSchdWaitBitmap(UINT8 simx, TaskID_Type taskId);
void rstSchdWaitBitmap(UINT8 simx, TaskID_Type taskId);
void schdRfChangeCnf(INT16 dummy);
void setSchdSimState(UINT8 simx, TaskID_Type taskId);
Bool checkAllowTimeBeforePi(RtuTime_Type needTime);
Bool checkAllowTimeBeforePi2(RtuTime_Type needTime2);
RtuTime_Type RemainTimeBeforePi(DualSim simx);
UINT8 getPiBelongSim(void);
void recvAppointPendMsg(aplpMsgHeader *aplpMsg, TaskID_Type taskId);
void plSetCurrentSimConfig(DualSim simId, SimRatAction_Type ratType);
void setPendQueueBlockFlag(DualSim simx, QueueBlockType blockFlag);
void setPsPagingState(PsPagingStateType state);
void plSchdClearTimers(Bool ClearState);
UINT32 getMsgNumInQueue(OSMsgQRef *queuePtr);
void plSchdClearWb(UINT8 simid);
void sleepRatioAlgoTimerExpired(UINT32 simId);
BOOL dualsimAllowBbSleep(SleepReason sleepreason);
//void schdActionAfterAbnormalSuspendInd(void);
void schdPendMsg2(aplpMsgHeader *aplpMsg, DualSim simID, Bool msgNotReceived);

void sendReqSignalToGsm(TaskID_Type taskId, SchdMsgMark reason,UINT8 resumeReason);
void sendSchdSignalToGsm(schdMsgHeader *SchdMsg, SchdMsgMark reason);
void sendCnfSignalToGsm(TaskID_Type taskId, SchdMsgMark reason);

void sendReqSignalToLte(TaskID_Type taskId, SchdMsgMark reason, UINT8 resumeReason);
void sendSchdSignalToLte(schdMsgHeader *schdMsg, SchdMsgMark rslt);
void sendCnfSignalToLte(TaskID_Type taskId, SchdMsgMark reason);

void schdDualSimEnterOpenRfState(UINT16 token,UINT16 state);
void schdDualSimExitOpenRfState(void);
void schdDualSimEnterCloseRfState(void);
void schdDualSimExitCloseRfState(void);

void schdWakeupOperation(void);
void schdWakeupComplete(void);
void schdOpenRfOperation(void);
void schdOpenRfComplete(void);
void schdOpenTrackerOperation(void);
void schdOpenTrakcerComplete(void);
void schdOpenFrameIntOperation(void);
void schdOpenFrameIntComplete(void);
void schdAbortMeasOperation(void);
	void schdAbortMeasComplete(void);

void schdCloseTrackerOperation(void);
void schdCloseTrakcerComplete(void);
void schdCloseRfOperation(void);
void schdCloseRfComplete(void);
void schdSleepOperation(void);
void schdSleepComplete(void);
void recvAppointPendMsg2(aplpMsgHeader *aplpMsg, TaskID_Type taskId, DualSim simID);
void schdClearFlagForAckOfABS5(void);
void schdStartNoBlockProcess(DualSim);

void schdCancelPiCbsReq(void);
void schdClearPiReq(void);
void schdClearCbsReq(void);
void schdDfltSetWuParaB4Sleep(void);
void aplpMtuTimeCaliTimerExpired(UINT32 reserved);
void schdAbnormalTaskProtection(VOID_PTR msg);

Bool schdFachDpchExistState(void);
void schdSetFachDpchExistState(Bool);

//extern void plMsSendSuspendCnf(void);
#ifdef APLP_DUALSIM_SUPPORT
extern VOID plMsSendResumeCnf(UINT8);
#endif
void plSchdWWNullEventHandler(aplp_event event, VOID_PTR msg);
void  plschdSendMsgToSchd(TaskID_Type taskid,DualSim dstsim, DualQue dstque);
void plSchdWGNullEventHandler(aplp_event event, VOID_PTR msg);
VOID plschdSendMsgFromRfqueToMainque(VOID);


#endif

