/*====*====*====*====*====*====*====*====*====*====*====*====*====*====*====*

                paltform_pmu.c


GENERAL DESCRIPTION

    This file is for Power management.

<PERSON>XTERNAL<PERSON>ZED FUNCTIONS

INITIALIZATION AND SEQUENCING REQUIREMENTS

   Copyright (c) 2019 by ASR, Incorporated.  All Rights Reserved.
*====*====*====*====*====*====*====*====*====*====*====*====*====*====*====*

*===========================================================================

                        EDIT HISTORY FOR MODULE

  This section contains comments describing changes made to the module.
  Notice that changes are listed in reverse chronological order.


when         who        what, where, why
--------   ------     ----------------------------------------------------------
05/30/2013   zhoujin    Created module
===========================================================================*/
/*===========================================================================

                     INCLUDE FILES FOR MODULE

===========================================================================*/
#include "osa.h"
#include "utils.h"
#include "cpmu_def.h"
#include "utilities.h"
#include "usb_macro.h"
#include "platform.h"
#include "mvUsbLog.h"
#include "cgpio.h"

/*===========================================================================

                                LOCAL MACRO
===========================================================================*/

/*Read data from register*/
#define BU_REG_READ(x) (*(volatile unsigned long *)(x))

/*Write data to register*/
#define BU_REG_WRITE(x,y) ((*(volatile unsigned long *)(x)) = y )

/* Write data to register based on original setting*/
#define BU_REG_RDMDFYWR(x,y)  (BU_REG_WRITE(x,((BU_REG_READ(x))|y)))

#ifdef CRANE_MCU_DONGLE
/*===========================================================================

            LOCAL DEFINITIONS AND DECLARATIONS FOR MODULE

This section contains local definitions for constants, macros, types,
variables and other items needed by this module.

===========================================================================*/

/* Enter D2 count */
char D2EntryCnt = 0;

/* System sleep enable flag */
BOOL SystemSleepEnable_flag = TRUE;

/*===========================================================================

            EXTERN DEFINITIONS AND DECLARATIONS FOR MODULE

===========================================================================*/

/* PM usb busy flag */
extern volatile UINT32 pm_usb_busy;

/*===========================================================================

                          EXTERNAL FUNCTION DEFINITIONS

===========================================================================*/

/* UART busy flag */
extern unsigned int uart_busy_flag;
#ifdef BT_SUPPORT
extern int get_bt2host_gpio_no(void);
#endif
#ifdef WIFI_FUNCTION_SUPPORT
extern int get_sdio2host_gpio_no(void);
#endif
/*===========================================================================

                          INTERNAL FUNCTION DEFINITIONS

===========================================================================*/
#if defined(BT_SUPPORT) || defined(WIFI_FUNCTION_SUPPORT)
BOOL GPIOWakeupEnable(int pin)
{
    volatile unsigned int temp = 0;
    unsigned int addr = 0xD401E0DC; // address of GPIO 0

    switch(pin)
    {
        case 12: // GPIO 12
            addr = 0xD401E10C;
            break;
        case 60: // GPIO 60
            addr = 0xD401E304;
            break;
        case 86: // GPIO 86
            addr = 0xD401E1F4;
			break;
		case 28: //GPIO 28
			addr = 0xD401E14C;
			break;
		case 34: //GPIO 34
			addr = 0xD401E164;
			break;
		case 33: //GPIO 33
			addr = 0xD401E160;
			break;
        default:
            return FALSE;
    }

    temp = BU_REG_READ(addr);
    temp = (temp | BIT_4 | BIT_5) & (~BIT_6);
    BU_REG_WRITE(addr, temp);
    return TRUE;
}

BOOL GPIOWakeupDisable(int pin)
{
    volatile unsigned int temp = 0;
    unsigned int addr = 0xD401E0DC; // address of GPIO 0

    switch(pin)
    {
        case 12: // GPIO 12
            addr = 0xD401E10C;
            break;
        case 60: // GPIO 60
            addr = 0xD401E304;
            break;
        case 86: // GPIO 86
            addr = 0xD401E1F4;
			break;
		case 28: //GPIO 28
			addr = 0xD401E14C;
			break;
		case 34: //GPIO 34
			addr = 0xD401E164;
			break;
		case 33: //GPIO 33
			addr = 0xD401E160;
			break;
        default:
            return FALSE;
    }

    temp = BU_REG_READ(addr);
    temp = temp | BIT_6;
    BU_REG_WRITE(addr, temp);
	return TRUE;
}
#endif

/*FUNCTION*-------------------------------------------------------------
*
*  Function Name  : CommPMVbusDetect
*  Returned Value : None
*  Comments       :
*        check Vbus detect bit
*
*END*-----------------------------------------------------------------*/

BOOL CommPMVbusDetect(void)
{
    /* Check Vbus on bit.*/
    if((*(volatile UINT32 *)0xD428287C) & BIT_15)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}

/*FUNCTION*-------------------------------------------------------------
*
*  Function Name  : CommPMUsbWakeupHandle
*  Returned Value : None
*  Comments       :
*        Services transaction complete interrupt
*
*END*-----------------------------------------------------------------*/

void CommPMUsbWakeupHandle(int state)
{
    volatile unsigned long wakeup_src;

    /*Management WakeUp Event*/
    wakeup_src = BU_REG_READ(PMUM_CWUCRS) & BU_REG_READ(PMUM_CWUCRM);

    /* Check whether it is USB wakeup source or not.*/
    if(wakeup_src&(0x1<<5))
    {
        /* Check Vbus on bit.*/
        if(CommPMVbusDetect())
        {
            pm_usb_busy = 1;

            if(state)
            {
                USB_TRACE("C1: usb wakeup");
            }
            else
            {
                USB_TRACE("D2: usb wakeup");
            }
        }
        else
        {
            //pm_usb_busy = 0;

            if(state)
            {
                USB_TRACE("C1: usb vbus off");
            }
            else
            {
                USB_TRACE("D2: usb vbus off");
            }
        }
    }
}


/*FUNCTION*-------------------------------------------------------------
*
*  Function Name  : CommPMEnableUbusWakeupSource
*  Returned Value : None
*  Comments       :
*        Services transaction complete interrupt
*
*END*-----------------------------------------------------------------*/
void CommPMEnableUbusWakeupSource(void)
{
    volatile unsigned int temp = 0;

    /* USB wakeup */
    temp = BU_REG_READ(PMUM_CPCR);
    temp &= ~(PMUM_CPCR_SLPWP5);
    BU_REG_WRITE(PMUM_CPCR, temp);

    temp = BU_REG_READ(PMUM_CWUCRM);
    temp |= PMUM_CWUCRM_WAKEUP5;
    BU_REG_WRITE(PMUM_CWUCRM, temp);

#if defined(BT_SUPPORT) || defined(WIFI_FUNCTION_SUPPORT)
    /* GPIO wakeup */
    temp = BU_REG_READ(PMUM_CPCR);
    temp &= ~(PMUM_CPCR_SLPWP2);
    BU_REG_WRITE(PMUM_CPCR, temp);

    temp = BU_REG_READ(PMUM_CWUCRM);
    temp |= PMUM_CWUCRM_WAKEUP2;
    BU_REG_WRITE(PMUM_CWUCRM, temp);
#endif
    /* Clear USB interrupt */
    temp = BU_REG_READ(0xD428287C);
    BU_REG_WRITE(0xD428287C, temp | BIT_4 | BIT_7);
#ifdef BT_SUPPORT
    {
        int no = get_bt2host_gpio_no();
        if(no > 0)
        {
            GPIOWakeupEnable(no);
        }
    }
    
#endif

#ifdef WIFI_FUNCTION_SUPPORT
    {
        int wifi_no = get_sdio2host_gpio_no();
        if(wifi_no != -1)
        {
            GPIOWakeupEnable(wifi_no);
        }
    }
#endif
}

/*FUNCTION*-------------------------------------------------------------
*
*  Function Name  : CommPMDisableUbusWakeupSource
*  Returned Value : None
*  Comments       :
*        Services transaction complete interrupt
*
*END*-----------------------------------------------------------------*/
void CommPMDisableUbusWakeupSource(void)
{
    volatile unsigned int temp = 0;

    /* Mask usb wake up source.*/
    temp = BU_REG_READ(PMUM_CWUCRM);
	temp &= ~(PMUM_CWUCRM_WAKEUP5);
	BU_REG_WRITE(PMUM_CWUCRM, temp);

	temp = BU_REG_READ(PMUM_CPCR);
	temp |= (PMUM_CPCR_SLPWP5);
	BU_REG_WRITE(PMUM_CPCR, temp);

#if defined(BT_SUPPORT) || defined(WIFI_FUNCTION_SUPPORT)
    /* Mask GPIO wake up source.*/
    temp = BU_REG_READ(PMUM_CWUCRM);
	temp &= ~(PMUM_CWUCRM_WAKEUP2);
	BU_REG_WRITE(PMUM_CWUCRM, temp);

	temp = BU_REG_READ(PMUM_CPCR);
	temp |= (PMUM_CPCR_SLPWP2);
	BU_REG_WRITE(PMUM_CPCR, temp);
#ifdef BT_SUPPORT
    {
        int no = get_bt2host_gpio_no();
        if(no > 0)
        {
            GPIOWakeupDisable(no);
        }
    }
#endif
#ifdef WIFI_FUNCTION_SUPPORT
    {
        int wifi_no = get_sdio2host_gpio_no();
        if(wifi_no != -1)
        {
            GPIOWakeupDisable(wifi_no);
        }
    }
#endif
#endif
}

/*===========================================================================

FUNCTION PlatformC1Prepare

DESCRIPTION
  This function consists of the platform internal C1 preparations..

DEPENDENCIES
  None.

RETURN VALUE
  none

SIDE EFFECTS
  none

===========================================================================*/
void PlatformC1Prepare(void)
{
    CommPMEnableUbusWakeupSource();

	return;
}

/*===========================================================================

FUNCTION PlatformC1Recover

DESCRIPTION
  This function consists of the platform internal C1 recovery.

DEPENDENCIES
  None.

RETURN VALUE
  none

SIDE EFFECTS
  none

===========================================================================*/
void PlatformC1Recover(void)
{
    CommPMUsbWakeupHandle(1);
    CommPMDisableUbusWakeupSource();


	return;
}


/*===========================================================================

FUNCTION PlatformD2Prepare

DESCRIPTION
  This function consists of the platform internal D2 preparations..

DEPENDENCIES
  None.

RETURN VALUE
  none

SIDE EFFECTS
  none

===========================================================================*/
void PlatformD2Prepare(void)
{
    if(D2EntryCnt == 0)
	{
		D2EntryCnt = 1;
        USB_TRACE("->Enter D2");
    }

    CommPMEnableUbusWakeupSource();

	return;
}

/*===========================================================================

FUNCTION PlatformD2Recover

DESCRIPTION
  This function consists of the platform internal D2 recovery.

DEPENDENCIES
  None.

RETURN VALUE
  none

SIDE EFFECTS
  none

===========================================================================*/
void PlatformD2Recover(void)
{
    CommPMUsbWakeupHandle(0);
    CommPMDisableUbusWakeupSource();


	return;
}


/*===========================================================================

FUNCTION PlatformPmLock

DESCRIPTION
  This function get platform work lock flag.

DEPENDENCIES
  None.

RETURN VALUE
  none

SIDE EFFECTS
  none

===========================================================================*/
BOOL PlatformWorkLock(void)
{
    if ((!SystemSleepEnable_flag) || uart_busy_flag)
    {
        return TRUE;
    }
    else
    {
        return FALSE;
    }
}
#else

#define MAX_EDGE_DET_PADS   16
UINT32 edge_detect_pad_mfpr[MAX_EDGE_DET_PADS]={0};
UINT8 edge_detect_pad_mfpr_index = MAX_EDGE_DET_PADS;

typedef struct
{
    UINT8           occupied;
	UINT8			num;
	GPIOCallback    wakeup_func;
}Gpio_Edge_Wakeup_Handler;

#define MAX_GPIO_EDGE_DET_PADS   10
Gpio_Edge_Wakeup_Handler gpio_wakeup_handler[MAX_GPIO_EDGE_DET_PADS] = {0};
UINT8 gpio_wakeup_handler_index = MAX_GPIO_EDGE_DET_PADS;

#define MAX_GPIO_EDGE_DET_PADS_EXT   10
Gpio_Edge_Wakeup_Handler gpio_wakeup_handler_ext[MAX_GPIO_EDGE_DET_PADS_EXT] = {0};
UINT8 gpio_wakeup_handler_index_ext = MAX_GPIO_EDGE_DET_PADS_EXT;

void EdgeDetectWakeupPrepare(void)
{
    volatile unsigned int temp, i, gpio, gpio_mfpr;

    if (gpio_wakeup_handler_index != MAX_GPIO_EDGE_DET_PADS)
    {
        for (i = 0; i <= gpio_wakeup_handler_index; i++)
    	{
    	    gpio = gpio_wakeup_handler[i].num;
            //enable GPIO edge during sleep
			gpio_mfpr = GPIO_MFPR_ADDR(gpio);
            temp = BU_REG_READ(gpio_mfpr);
            BU_REG_WRITE(gpio_mfpr, temp & (~BIT_6));
    	}
	}

    if (edge_detect_pad_mfpr_index != MAX_EDGE_DET_PADS)
    {
        for (i = 0; i <= edge_detect_pad_mfpr_index; i++)
        {
            temp = BU_REG_READ(edge_detect_pad_mfpr[i]);
            temp = (temp | BIT_4 | BIT_5) & (~BIT_6);
            BU_REG_WRITE(edge_detect_pad_mfpr[i], temp);
        }
    }

    /* GPIO wakeup */
    temp = BU_REG_READ(PMUM_CPCR);
    temp &= ~(PMUM_CPCR_SLPWP2);
    BU_REG_WRITE(PMUM_CPCR, temp);

    temp = BU_REG_READ(PMUM_CWUCRM);
    temp |= PMUM_CWUCRM_WAKEUP2;
    BU_REG_WRITE(PMUM_CWUCRM, temp);
}

void EdgeDetectWakeupRecover(void)
{
    volatile unsigned int temp, i, gpio, gpio_mfpr;
	
    if (gpio_wakeup_handler_index != MAX_GPIO_EDGE_DET_PADS)
    {
        for (i = 0; i <= gpio_wakeup_handler_index; i++)
    	{
    	    gpio = gpio_wakeup_handler[i].num;
    		if(GpioIsEdgeDetected_wakeup(gpio))
    		{
    			GpioClearEdgeDetection_wakeup(gpio);
                 //disable GPIO edge during active
    			gpio_mfpr = GPIO_MFPR_ADDR(gpio);
                temp = BU_REG_READ(gpio_mfpr);
                BU_REG_WRITE(gpio_mfpr, temp | BIT_6);
    			
    			if(gpio_wakeup_handler[i].wakeup_func)
    				gpio_wakeup_handler[i].wakeup_func();
    		}
    	}
	}

    if (edge_detect_pad_mfpr_index != MAX_EDGE_DET_PADS)
    {
        for (i = 0; i <= edge_detect_pad_mfpr_index; i++)
        {
            temp = BU_REG_READ(edge_detect_pad_mfpr[i]);
            temp = temp | BIT_6;
            BU_REG_WRITE(edge_detect_pad_mfpr[i], temp);
        }
    }

    /* Mask GPIO wake up source.*/
    temp = BU_REG_READ(PMUM_CWUCRM);
	temp &= ~(PMUM_CWUCRM_WAKEUP2);
	BU_REG_WRITE(PMUM_CWUCRM, temp);

	temp = BU_REG_READ(PMUM_CPCR);
	temp |= (PMUM_CPCR_SLPWP2);
	BU_REG_WRITE(PMUM_CPCR, temp);
}

void EdgeDetectWakeupRegister(UINT32 MFPR_addr)
{
    volatile unsigned int i;
    
    if ((MFPR_addr & 0xFFFFF000) != 0xD401E000)
    {
        CP_LOGE("%s: not MFPR addr!\r\n", __func__);
        ASSERT(0);
        return;
    }

    for (i = 0; i < MAX_EDGE_DET_PADS; i++)
    {
        if (MFPR_addr == edge_detect_pad_mfpr[i]) //repeated, quit
        {
            CP_LOGW("%s: repeated MFPR reg!\r\n", __func__);
            return;
        }
        if (edge_detect_pad_mfpr[i] == 0)
        {
            edge_detect_pad_mfpr_index = i;
            break;
        }
    }
    if (i == MAX_EDGE_DET_PADS)
    {
        CP_LOGE("%s: Registered edge detect wakeup sources exceed!\r\n", __func__);
        ASSERT(0);
        return;
    }

    edge_detect_pad_mfpr[edge_detect_pad_mfpr_index] = MFPR_addr;
    CP_LOGI("%s: 0x%x recorded for index %d.\r\n", __func__, MFPR_addr, edge_detect_pad_mfpr_index);
    
#if 1
    uart_printf("**** Registered Edge Detect MFPRs ****\r\n");
    for (i = 0; i <= edge_detect_pad_mfpr_index; i++)
        uart_printf("index %d: 0x%08x\r\n", i, edge_detect_pad_mfpr[i]);
#endif
}
void GpioEdgeDetectWakeupRegister(UINT8 gpio_num, GPIOCallback gpio_wakeup_func)
{
    volatile unsigned int i, temp, mfpr;

    if (gpio_num >= 128)
    {
        CP_LOGW("%s: invalid GPIO number!\r\n", __func__);
        return;
    }
    for (i = 0; i < MAX_GPIO_EDGE_DET_PADS; i++)
    {
        if (gpio_wakeup_handler[i].occupied == 0)
        {
            gpio_wakeup_handler[i].occupied = 0xA5; //mark registered
            gpio_wakeup_handler_index = i;
            gpio_wakeup_handler[i].wakeup_func = gpio_wakeup_func;
            gpio_wakeup_handler[i].num = gpio_num;
            CP_LOGI("%s: GPIO%d recorded for index %d.\r\n", __func__, gpio_num, gpio_wakeup_handler_index);
            break;
        }
    }
    if (i == MAX_GPIO_EDGE_DET_PADS)
    {
        CP_LOGE("%s: Registered GPIO edge detect wakeup sources exceed!\r\n", __func__);
        return;
    }
}

void GpioEdgeDetectWakeupRegister_Ext(UINT8 gpio_num, GPIOTransitionType gpio_edge_wakeup_type, GPIOCallback gpio_wakeup_func)
{
    volatile unsigned int i, temp, mfpr;

    if (gpio_num >= 128){
        CP_LOGW("%s: invalid GPIO_Ext number!\r\n", __func__);
        return;
    }
    if (gpio_edge_wakeup_type > GPIO_TWO_EDGE){
        CP_LOGW("%s: invalid GPIO_Ext wakeup type!\r\n", __func__);
        return;
    }
    
    for (i = 0; i < MAX_GPIO_EDGE_DET_PADS_EXT; i++)
    {
        if (gpio_wakeup_handler_ext[i].occupied == 0)
        {
            gpio_wakeup_handler_ext[i].occupied = 0xA5; //mark registered
            gpio_wakeup_handler_index_ext = i;
            gpio_wakeup_handler_ext[i].wakeup_func = gpio_wakeup_func;
            gpio_wakeup_handler_ext[i].num = gpio_num;

			mfpr = GPIO_MFPR_ADDR(gpio_num);
            temp = BU_REG_READ(mfpr)&~(0x7<<4);
            if (gpio_edge_wakeup_type == GPIO_NO_EDGE)
                temp |= 0x4<<4;
            else
                temp |= gpio_edge_wakeup_type<<4;
            BU_REG_WRITE(mfpr, temp);
            CP_LOGI("%s: GPIO_Ext%d recorded for index %d.\r\n", __func__, gpio_num, gpio_wakeup_handler_index_ext);
            break;
        }
    }
    if (i == MAX_GPIO_EDGE_DET_PADS_EXT)
    {
        CP_LOGE("%s: Registered GPIO_EXT edge detect wakeup sources exceed!\r\n", __func__);
        return;
    }
}

void find_clear_gpio_edge_ints(void)
{
    volatile unsigned int temp, i, gpio_mfpr;

    for (i = 0; i < 128; i++)
    {
        gpio_mfpr = GPIO_MFPR_ADDR(i);
        if (gpio_mfpr == 0xFFFFFFFF) continue; //invalid mfpr

        if (GpioIsEdgeDetected_wakeup(i))
        {
            temp = BU_REG_READ(gpio_mfpr);
            BU_REG_WRITE(gpio_mfpr, temp | BIT_6);
            CP_LOGW("Clear unregistered edge int of GPIO%d with @0x%x=0x%x\r\n", i, gpio_mfpr, temp);
        }
    }
}

void GpioEdgeWakeupIntHandler_Ext(void)
{
    volatile unsigned int temp = 0xa55a, i, gpio, gpio_mfpr;
    if (gpio_wakeup_handler_index_ext != MAX_GPIO_EDGE_DET_PADS_EXT)
    {
        for (i = 0; i <= gpio_wakeup_handler_index_ext; i++)
    	{
    	    gpio = gpio_wakeup_handler_ext[i].num;
    		if(GpioIsEdgeDetected_wakeup(gpio))
    		{
    			GpioClearEdgeDetection_wakeup(gpio);
                 //disable GPIO edge during active
    			gpio_mfpr = GPIO_MFPR_ADDR(gpio);
                temp = BU_REG_READ(gpio_mfpr);
                BU_REG_WRITE(gpio_mfpr, temp | BIT_6);
                BU_REG_WRITE(gpio_mfpr, temp &~ BIT_6);

    			if(gpio_wakeup_handler_ext[i].wakeup_func)
    				gpio_wakeup_handler_ext[i].wakeup_func();
    		}
    	}
	}
	if (temp == 0xa55a)
	    find_clear_gpio_edge_ints();
}

void disable_gpio_edge_ints(void)
{/*called before silent reset in case any pad int pending and 
  causes trapping in pad int once the int is enabled after reset.*/
    volatile unsigned int temp, i, gpio_mfpr;

    for (i = 0; i < 128; i++)
    {
        gpio_mfpr = GPIO_MFPR_ADDR(i);
        if (gpio_mfpr == 0xFFFFFFFF) continue; //invalid mfpr
        temp = BU_REG_READ(gpio_mfpr);
        BU_REG_WRITE(gpio_mfpr, temp | BIT_6);
    }
}

#endif
