#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#--------------------------------------------------------------------------------------------------------------------
# INTEL CONFIDENTIAL
# Copyright 2006 Intel Corporation All Rights Reserved.
# The source code contained or described herein and all documents related to the source code ("Material") are owned
# by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
# its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
# Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
# treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
# transmitted, distributed, or disclosed in any way without Intel's prior express written permission.
#
# No license under any patent, copyright, trade secret or other intellectual property right is granted to or
# conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
# estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
# Intel in writing.
# -------------------------------------------------------------------------------------------------------------------

#=========================================================================
# File Name      : diag_comm.mak
# Description    : Main make file for the diag/diag_comm package.
#
# Usage          : make [-s] -f diag_comm.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
#
#		 : the diag packaged moved from softutil to diag vob
#=========================================================================

# Package build options
include ${OPT_FILE}

# Package Makefile information
GEN_PACK_MAKEFILE = ${BUILD_ROOT}/env/${HOST}/build/package.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = diag_comm
PACKAGE_BASE     = diag
PACKAGE_DEP_FILE = diag_comm_dep.mak
PACKAGE_PATH     = ${BUILD_ROOT}/${PACKAGE_BASE}/${PACKAGE_NAME}

# The relative path locations of source and include file directories.
PACKAGE_SRC_PATH    = ${PACKAGE_PATH}/src
PACKAGE_INC_PATHS   = $(PACKAGE_PATH)/src $(PACKAGE_PATH)/inc \
                        ${BUILD_ROOT}\hop\ssp\inc \
                        ${BUILD_ROOT}\hop\ssp\src \
			${BUILD_ROOT}\hop\utilities\inc \
                        ${BUILD_ROOT}\softutil\tester\inc \
			${BUILD_ROOT}\pcac\gpc\inc  \
			${BUILD_ROOT}\pcac\msl_dl\src\llc  \
			${BUILD_ROOT}\pcac\msl_dl\inc	\
			${BUILD_ROOT}\pcac\msl_utils\inc \
			${BUILD_ROOT}\pcac\pca_components\inc \
			${BUILD_ROOT}\virtio\virtio_vrecom_driver\inc \
			${BUILD_ROOT}\pcac\gen_stub\inc
			
ifneq (,$(findstring SPIMUX_SUPPORT,${VARIANT_LIST}))			
	PACKAGE_INC_PATHS += ${BUILD_ROOT}\hop\AMUX\src
endif	

# Package source files, paths not required
PACKAGE_SRC_FILES =								\
				diag_comm.c						\
				diag_comm_if.c					\
				diag_comm_EXTif.c				\
				diag_comm_INTif.c				\
				diag_comm_EXTif_OSA_NUCLEUS.c	\
				diag_comm_L2.c					\
				diag_comm_L4.c

# These are the tool flags specific to the diag_comm package only.
# The environment, target, and group also set flags.
PACKAGE_CFLAGS  =
PACKAGE_ARFLAGS =
PACKAGE_DFLAGS  =
#PACKAGE_DFLAGS  = -DMSL_IF_VIA_LEGACY_GPC=1
#PACKAGE_DFLAGS  = -DMSL_IF_VIA_NEW_GPC=1
#PACKAGE_DFLAGS  = -DMSL_IF_VIA_SAL=1

#PACKAGE_SRC_FILES += diag_comm_INTIf_TTC.c diag_port.c test_port.c
PACKAGE_SRC_FILES += diag_port.c


#//DIAGUSEGPC - use GPC inteface for MSL , otherwise use SAL
ifneq (,$(findstring DIAGUSEGPC, ${VARIANT_LIST}))
#found DIAGUSEGPC in variant.
PACKAGE_DFLAGS  += -DMSL_IF_VIA_LEGACY_GPC=1
else
PACKAGE_DFLAGS  += -DMSL_IF_VIA_SAL=1
endif

## MSL interface options (set to 1 - for active)
# MSL_IF_VIA_NEW_GPC
# MSL_IF_VIA_SAL
# MSL_IF_VIA_LEGACY_GPC


# Define Package Variants -------------------------------

# look for the variants in the VARIANT_LIST and override
# setting from the previous section. The variables
# diag_comm_VARIANT_1 and diag_comm_VARIANT_2
# are meant to be overwritten with actual variant names.

# handle the diag_comm_VARIANT_1 variant ------------
ifneq (,$(findstring diag_comm_VARIANT_1 ,${VARIANT_LIST}))

PACKAGE_VARIANT = diag_comm_VARIANT_1

# Package source files, paths not required
PACKAGE_SRC_FILES = ^SRC_FILE_LIST

# These are the tool flags specific to the diag_comm package only.
# The environment, target, and group also set flags.
PACKAGE_CFLAGS  =
PACKAGE_DFLAGS  =
PACKAGE_ARFLAGS =

endif

# handle the diag_comm_VARIANT_2 variant ------------
ifneq (,$(findstring diag_comm_VARIANT_2 ,${VARIANT_LIST}))

PACKAGE_VARIANT = diag_comm_VARIANT_2

# Package source files, paths not required
PACKAGE_SRC_FILES = ^SRC_FILE_LIST

# These are the tool flags specific to the diag_comm package only.
# The environment, target, and group also set flags.
PACKAGE_CFLAGS  =
PACKAGE_DFLAGS  =
PACKAGE_ARFLAGS =

endif


# Include the Standard Package Make File ---------------
include ${GEN_PACK_MAKEFILE}

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include ${PACKAGE_DEP_FILE}









