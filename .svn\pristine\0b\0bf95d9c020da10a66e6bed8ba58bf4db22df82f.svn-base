/*
 * ppp_platform.c -- platform specific functions
 *
 * (C) Copyright [2006-2008] Marvell International Ltd.
 * All Rights Reserved
 *
 */

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include "ppp_types.h"
#include "modem_platform.h"

extern PppControlS PppControl;

#define PPP_HANDLE_PRIORITY 80
#define PPP_HANDLER_STACK_SIZE 1024 * 8
static void *ppp_handler_stack = NULL;
static PPP_TASK_t ppp_task_ref = NULL;
static PPP_TASK_t ppp_handler_task_ref = NULL;

BOOL ppp_trace_enable = FALSE;
/************************************************************************/
/*                  Task Functions                                      */
/************************************************************************/
void ppp_create_task(PPP_TASK_t *task_ref, void (*start_routine)(void *), void *arg)
{
	OSA_STATUS osa_status;
	if (*task_ref == PppControl.ppp_handler_ref)
	{
	    ppp_handler_stack = malloc(PPP_HANDLER_STACK_SIZE);
	    ASSERT(ppp_handler_stack != NULL);
		osa_status  = OSATaskCreate(task_ref,
	                           ppp_handler_stack,
	                           PPP_HANDLER_STACK_SIZE,
	                           PPP_HANDLE_PRIORITY,
	                           (char *)"pppHandler" ,
	                           start_routine,
	                           (void *)arg);
		ASSERT(osa_status ==  OS_SUCCESS);
	}
}

/************************************************************************/
/*                  Semaphore Functions                                 */
/************************************************************************/
void ppp_init_semaphore(SEM_t *sem, int pshared, unsigned int value)
{
   OSA_STATUS osa_status;
   UNUSED(pshared);
	
    osa_status = OSASemaphoreCreate (sem, value, OSA_FIFO);
    ASSERT(osa_status == OS_SUCCESS);
}

int ppp_acquire_semaphore(SEM_t sem, long timeout)
{
    OSA_STATUS osa_status;

    if (sem == NULL)
	return -1;

	/*timeout seems not works well*/
    osa_status = OSASemaphoreAcquire(sem, timeout);
    //osa_status = OSASemaphoreAcquire(sem, OS_SUSPEND);
    if (osa_status == OS_SUCCESS)
    	return 0;

    if (osa_status == OS_TIMEOUT)
	return -2;
    else
	return -1;
    
}

void ppp_release_semaphore(SEM_t sem)
{
    OSA_STATUS osa_status;

   if (sem != NULL)
   {
	    osa_status = OSASemaphoreRelease(sem);
	    ASSERT(osa_status == OS_SUCCESS);
   }
}

/************************************************************************/
/*                  Mutex Functions                                 */
/************************************************************************/
void ppp_init_mutex(MUTEX_t *mutex)
{
   OSA_STATUS osa_status;
	
    osa_status = OSAMutexCreate (mutex, OSA_FIFO);
    ASSERT(osa_status == OS_SUCCESS);
}

void ppp_lock_mutex(MUTEX_t mutex)
{	
	if (mutex != NULL)
		OSAMutexLock(mutex, OSA_SUSPEND);
}

void ppp_unlock_mutex(MUTEX_t mutex)
{
	if (mutex != NULL)
		OSAMutexUnlock(mutex);
}

/************************************************************************/
/*                  Timer Functions                                     */
/************************************************************************/
void ppp_create_timer(TIMER_t *timerid)
{
	OS_STATUS osa_status;

    osa_status = OSATimerCreate(timerid);
    ASSERT(osa_status == OS_SUCCESS);
}

/*the limit period should be 5ms, only timeout once after ms expired*/
void ppp_enable_timer(TIMER_t timerid, long ms, TIMER_HANDLER timeout_handler)
{
	OS_STATUS osa_status;
	if (timerid != NULL)
	{
        osa_status = OSATimerStart(timerid, ms/5, 0, timeout_handler, 0);
        ASSERT(osa_status == OS_SUCCESS);
	}
}

void ppp_disable_timer(TIMER_t timerid)
{
	OS_STATUS osa_status;
	if (timerid != NULL)
	{
		osa_status = OSATimerStop( timerid );
		ASSERT( osa_status == OS_SUCCESS );
	}
}

void ppp_delete_timer(TIMER_t timerid)
{
	if (timerid != NULL)
		OSATimerDelete(timerid);
}

/************************************************************************/
/*                  Message Queue Functions                             */
/************************************************************************/

PPP_MsgQ_t ppp_create_msg_queue(U_INT queue_size, U_INT msg_size)
{
	OSA_STATUS osa_status;
	OSMsgQRef ppp_msgq_ref;

	osa_status = OSAMsgQCreate(&ppp_msgq_ref,
#ifdef  OSA_QUEUE_NAMES
	                          "pppMsgQ",
#endif
	                         msg_size,
	                         queue_size,
	                          OSA_PRIORITY);
	ASSERT(osa_status == OS_SUCCESS);
	
	DBGMSG("%s: done \r\n", __func__);
	return ppp_msgq_ref;
}

int ppp_send_msg(PPP_MsgQ_t qRef, U_CHAR *ptr, U_INT size)
{
	if(!qRef || !ptr)
	{
		ERRMSG("%s: invalid input parameters\n", __func__);
		return PPP_FAIL;
	}

	OSA_STATUS osa_status;
 	osa_status = OSAMsgQSend(qRef, size, (UINT8*)ptr, OSA_NO_SUSPEND);

	return osa_status;
}

int ppp_recv_msg(PPP_MsgQ_t qRef, U_CHAR *ptr, U_INT size, U_INT timeout)
{
	if(!qRef || !ptr || ((timeout != PPP_SUSPEND) && (timeout != PPP_NO_SUSPEND)))
	{
		ERRMSG("%s: invalid input parameters \r\n", __func__);
		return PPP_FAIL;
	}

	OSA_STATUS osa_status;
	osa_status = OSAMsgQRecv(qRef, (UINT8 *)ptr, size, timeout);
	ASSERT(osa_status == OS_SUCCESS);

	return PPP_SUCCESS;
}

void ppp_delete_msg_queue(PPP_MsgQ_t qRef)
{
	OSA_STATUS osa_status;
 	osa_status = OSAMsgQDelete(qRef);
	ASSERT(osa_status == OS_SUCCESS);
}

/************************************************************************/
/* Debug Functions                                                      */
/************************************************************************/
void ppp_trace_buf(char const *string, char *buf, U_INT length)
{
	if (ppp_trace_enable == FALSE)
		return;
	U_INT i, len;
	char dbg_buf[256] = { '\0' }; /*too big dbg_buf size will make stack overflow*/
	char temp_buf[10] = { '\0' };

	//sprintf(dbg_buf, "Trace Buf \"%s\" String [len = %u]: \n", string, length);
	sprintf(dbg_buf, "[%s] [len = %u]: ", string, length);
	if (length > 100)
		len = 100;
	else
		len = length;

	for(i = 0; i < len; i++)
	{
		sprintf(temp_buf, "%02x", buf[i]);
		strcat(dbg_buf, temp_buf);
	}
	strcat(dbg_buf, "\n");
	
	DBGMSG("%s", dbg_buf);
}

/************************************************************************/
/* Random Number Generator                                              */
/************************************************************************/
U_INT ppp_rand()
{
	unsigned int seed;
	unsigned int r;

	seed = OSAGetTicks();
	srand(seed);

	r = rand();

	INFOMSG("ppp_rand: %d\r\n", r);
	return r;
}

void ppp_set_trace(int enable)
{
	if (enable > 0)
		ppp_trace_enable = TRUE;
	else
		ppp_trace_enable = FALSE;
}
