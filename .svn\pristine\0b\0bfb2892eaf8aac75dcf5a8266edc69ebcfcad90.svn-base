
#include "plat_types.h"
#include "ui_log_api.h"
#include "task_cnf.h"
#include "ui_os_message.h"
#include "ui_os_task.h"
#include "ui_os_api.h"

#include "event.h"
#include "drv.h"
#include "dm.h"
#include "base_prv.h"
#include "charger_api.h"
#include "pmic_api.h"
#include "plat_basic_api.h"
#include "gpio_cus.h"
#include "mci_lcd.h"

extern BOOL dm_SendKeyMessage( UI_EVENT* pEv);
typedef VOID (*PFN_DEVICE_HANDLER)(UI_EVENT*);
extern BOOL is_mUI_enabled(void);
extern HANDLE mUI_get_adp_task_handle(VOID);
//extern BOOL UOS_WaitEvent(HANDLE hTask, UI_EVENT* pEvent,UINT32 nTimeOut);
extern BOOL SUL_ZeroMemory32(VOID*  pBuf,UINT32 count);

extern VOID DM_CheckPowerOnCause(void);

extern BOOL pm_BatteryInit(void);
extern BOOL pm_BatteryMonitorProc(void);
extern VOID pm_UsbMonitorProc(VOID);
extern HANDLE get_dev_mon_task(void);
UINT32  PowerOn_Cause = DM_POWRN_ON_CAUSE_UNKOWN;
extern volatile UINT32 Time_To_MMI_Evt;
extern UINT8 g_mciLcdBrightnessLevel;

// =============================================================================
// OS_POWER_CAUSE_T
// -----------------------------------------------------------------------------
/// Describes the cause of a system power on. This type is returned by the function
/// #hal_SysGetPowerupCause.
// =============================================================================
typedef enum
{
    /// Normal cause, ie onkey power up
    OS_POWER_CAUSE_NORMAL, 

    /// The power up was caused by charger insert
    OS_POWER_CAUSE_CHARGER,

    /// The power up was caused by ExtOn low, triggered by the usb
    OS_POWER_CAUSE_EXTON,
	/// The power up caused by alarm, from the calendar.
	OS_POWER_CAUSE_ALARM = 4,
    /// The POWER was watch dog,long_onkey
    OS_POWER_CAUSE_FAULT,
     /// The power up was caused by battery insert
    OS_POWER_CAUSE_BATTERY,
    OS_POWER_CAUSE_RESV

} OS_POWERON_CAUSE_T;

extern HANDLE mUI_get_mmi_task_handle(void);
unsigned long s_dm_SendISRKeyMessage_missed=0;
BOOL dm_SendISRKeyMessage( UI_EVENT* pEv)
{
#ifndef CONFIG_RELEASE_WITH_NO_ASR_UI
	u8 mbox;
	TASK_HANDLE *mmi_task_handle = NULL;
	
 	mmi_task_handle = (TASK_HANDLE *)mUI_get_mmi_task_handle();

	if(mmi_task_handle != NULL)
	{
		mbox = mmi_task_handle->nMailBoxId;

		if (UOS_MsgQEnqueued(mbox) > (UOS_GetMessageQueueSize(mbox) >> 1))
		{
			s_dm_SendISRKeyMessage_missed++;
			return FALSE;
		}

		UOS_SendEvent(mUI_get_mmi_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
	}
#endif
	return TRUE;
}

#if 0
#include "keycode.h"
char key_code_status_keydrv[128] = {0};
void repeat_key_check_keydrv(UI_EVENT* pEv)
{

	if (key_code_status_keydrv[pEv->nParam3] == pEv->nEventId) {
		raw_uart_log("%s%d: ====keycode = %d, laststatus = %d, currstatus = %d\n",
			__func__, __LINE__, pEv->nParam3, key_code_status_keydrv[pEv->nParam3],  pEv->nEventId);

//		assert(0);
	}

	key_code_status_keydrv[pEv->nParam3] = pEv->nEventId;
}
#endif

BOOL dm_SendKeyMessage( UI_EVENT* pEv)
{
#ifndef CONFIG_RELEASE_WITH_NO_ASR_UI
	static u32 discard_key_events_num = 0;
	u8 mbox;
	TASK_HANDLE *mmi_task_handle = NULL;
#endif

//	repeat_key_check_keydrv(pEv);

#ifndef CONFIG_RELEASE_WITH_NO_ASR_UI
 	mmi_task_handle = (TASK_HANDLE *)mUI_get_mmi_task_handle();

	if(mmi_task_handle != NULL)
	{
		mbox = mmi_task_handle->nMailBoxId;
		if (UOS_MsgQEnqueued(mbox) > (UOS_GetMessageQueueSize(mbox) >> 1))
		{
	        discard_key_events_num++;
	//		raw_uart_log("\n !!keyboard mailbox full \n");
	        return FALSE;
		}

		UOS_SendEvent(mUI_get_mmi_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
	}
#endif
	return TRUE;
}

int UI_SendKeyMessage(UINT32 KeyEvent, UINT32 KeyValue)
{
	UI_EVENT key_evt = {0};

	if(KeyEvent == 1)
		key_evt.nEventId = EV_KEY_UP;
	else
		key_evt.nEventId = EV_KEY_DOWN;

	key_evt.nParam1 = key_evt.nParam3 = KeyValue;
	return dm_SendKeyMessage(&key_evt);
}

BOOL dm_SendTpMessage( UI_EVENT* pEv)
{
#ifdef CONFIG_TOUCH_PANEL_SUPPORT//TOUCHPANEL_SUPPORT

#if 0
	if(is_mUI_enabled())
	{
		UOS_SendEvent(mUI_get_mmi_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
	}
#endif
#ifndef CONFIG_RELEASE_WITH_NO_ASR_UI
		//static u32 discard_key_events_num = 0;
		u8 mbox;
		TASK_HANDLE *mmi_task_handle = NULL;

		mmi_task_handle = (TASK_HANDLE *)mUI_get_mmi_task_handle();

		if(mmi_task_handle != NULL)
		{
			mbox = mmi_task_handle->nMailBoxId;
			if (UOS_MsgQEnqueued(mbox) > (UOS_GetMessageQueueSize(mbox) >> 1))
			{
				//discard_key_events_num++;
				raw_uart_log("\n !!Tp mailbox full \n");
				return FALSE;
			}

			UOS_SendEvent(mUI_get_mmi_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
		}
#endif

#endif
	return TRUE;
}

BOOL dm_SendPowerOnMessage( UI_EVENT* pEv)
{
	if(is_mUI_enabled())
	{
		UOS_SendEvent(mUI_get_adp_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
	}

	return TRUE;
}

BOOL dm_SendArlarmMessage( UI_EVENT* pEv)
{
	if(is_mUI_enabled())
	{
		UOS_SendEvent(mUI_get_adp_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
	}

	return TRUE;
}

BOOL dm_SendSwitchLcdMessage( UI_EVENT* pEv)
{
	if(is_mUI_enabled())
	{
		UOS_SendEvent(mUI_get_adp_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
	}
	
	return TRUE;
}

BOOL dm_SendPMMessage( UI_EVENT* pEv)
{
	if(is_mUI_enabled())
	{
		UOS_SendEvent(mUI_get_adp_task_handle(), pEv, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
	}
	return TRUE;
}

// ============================================================================
// hal_KeyOnOffStateAtPowerOn
// ----------------------------------------------------------------------------
/// Tell if the key ON key was pressed at power-on time.
/// @return \c TRUE if the ON/OFF button was pressed \n
///         \c FALSE otherwise
// ============================================================================
PUBLIC BOOL hal_KeyOnOffStateAtPowerOn(VOID)
{
   	unsigned int i,j;
	j = 0;
	for(i=0;i<10;i++)//for(i=0;i<50;i++)
	{
		if(!pmic_onkey_is_detected())
			j++;
		else
			j = 0;
		if(j > 5)
			return FALSE;
	}
	return TRUE;
}

// =============================================================================
// hal_SysGetPowerUpCause
// -----------------------------------------------------------------------------
/// Get the cause of the last power up.
/// This function only returns the power up cause. It does not proceed to any test
/// to now if the boot was due to a press on the Power On button or because
/// the charger was plugged. Those tests are to be done by the user code, 
/// @return The last power up cause
// =============================================================================
PUBLIC OS_POWERON_CAUSE_T hal_SysGetPowerUpCause(VOID)
{
	unsigned char pmic_powerup_cause;
	OS_POWERON_CAUSE_T PowerupCause;
	pmic_powerup_cause = pmic_get_powerup_reason();
	raw_uart_log("Pmic wake up reg =0x%x.\n", pmic_powerup_cause);
	if(Pmic_is_pm803())
	{
		pmic_powerup_cause &= PM803WAKEUP_MASK;
	    switch (pmic_powerup_cause)
        {
    	case PM803BAT_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_BATTERY;
			break;
		case PM803EXTON1_WAKEUP:
			if(pm812_get_charger_status())
				PowerupCause = OS_POWER_CAUSE_EXTON;
			else
				PowerupCause = OS_POWER_CAUSE_NORMAL;
			break;
		case PM803RTC_ALARM_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_ALARM;
			break;
		case PM803FAULT_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_FAULT;
			break;
		case PM803ONKEY_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_NORMAL;
			break;
		default:
//deal with more wake up source
			if(pmic_powerup_cause & PM803RTC_ALARM_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_ALARM;
			else if(pmic_powerup_cause & PM803FAULT_WAKEUP){
                if(pmic_get_powerdown_reason() & 0x31) //wdg,longonkey,and ovtemp
				    PowerupCause = OS_POWER_CAUSE_FAULT;
				else if(pmic_powerup_cause & PM803EXTON1_WAKEUP) {
					if(pm812_get_charger_status())
						PowerupCause = OS_POWER_CAUSE_EXTON;
					else
						PowerupCause = OS_POWER_CAUSE_NORMAL;
                } else if(pmic_powerup_cause & PM803ONKEY_WAKEUP)
				    PowerupCause = OS_POWER_CAUSE_NORMAL;
			    else
				    PowerupCause = OS_POWER_CAUSE_RESV;
			} else if(pmic_powerup_cause & PM803EXTON1_WAKEUP) {
				if(pm812_get_charger_status())
					PowerupCause = OS_POWER_CAUSE_EXTON;
				else
					PowerupCause = OS_POWER_CAUSE_NORMAL;
			}else if(pmic_powerup_cause & PM803ONKEY_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_NORMAL;
			else
				PowerupCause = OS_POWER_CAUSE_RESV;
			break;
        }
	}else if(!Pmic_is_pm812()){
        pmic_powerup_cause &= PM813WAKEUP_MASK; 
	    switch (pmic_powerup_cause)
        {
    	case PM813BAT_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_BATTERY;
			break;
		case PM813EXTON1_WAKEUP:
		case PM813EXTON2_WAKEUP:
			if(pm812_get_charger_status())
				PowerupCause = OS_POWER_CAUSE_EXTON;
			else
				PowerupCause = OS_POWER_CAUSE_NORMAL;
			break;
		case PM813USB_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_CHARGER;
			break;
		case PM813RTC_ALARM_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_ALARM;
			break;
		case PM813FAULT_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_FAULT;
			break;
		case PM813ONKEY_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_NORMAL;
			break;
		default:
//deal with more wake up source
			if(pmic_powerup_cause & PM813RTC_ALARM_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_ALARM;
			else if(pmic_powerup_cause & PM813FAULT_WAKEUP){
                if(pmic_get_powerdown_reason() & 0x31) //wdg,longonkey,and ovtemp
				    PowerupCause = OS_POWER_CAUSE_FAULT;
				else if(pmic_powerup_cause & (PM813EXTON1_WAKEUP |PM813EXTON2_WAKEUP)) {
					if(pm812_get_charger_status())
						PowerupCause = OS_POWER_CAUSE_EXTON;
					else
						PowerupCause = OS_POWER_CAUSE_NORMAL;
                } else if(pmic_powerup_cause & PM813USB_WAKEUP)
				    PowerupCause = OS_POWER_CAUSE_CHARGER;
                else if(pmic_powerup_cause & PM813ONKEY_WAKEUP)
				    PowerupCause = OS_POWER_CAUSE_NORMAL;
			    else
				    PowerupCause = OS_POWER_CAUSE_RESV;
			} else if(pmic_powerup_cause & (PM813EXTON1_WAKEUP |PM813EXTON2_WAKEUP)) {
				if(pm812_get_charger_status())
					PowerupCause = OS_POWER_CAUSE_EXTON;
				else
					PowerupCause = OS_POWER_CAUSE_NORMAL;
			} else if(pmic_powerup_cause & PM813USB_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_CHARGER;
			else if(pmic_powerup_cause & PM813ONKEY_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_NORMAL;
			else
				PowerupCause = OS_POWER_CAUSE_RESV;
			break;
        }
    }else{
	    pmic_powerup_cause &= WAKEUP_MASK; 
	    switch (pmic_powerup_cause)
        {
    	case ONKEY_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_NORMAL;
			break;
		case CHG_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_CHARGER;
			break;
		case EXTON_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_EXTON;
			break;
		case RTC_ALARM_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_ALARM;
			break;
		case FAULT_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_FAULT;
			break;
		case BAT_WAKEUP:
			PowerupCause = OS_POWER_CAUSE_BATTERY;
			break;
		default:
//deal with more wake up source
			if(pmic_powerup_cause & RTC_ALARM_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_ALARM;
			else if(pmic_powerup_cause & FAULT_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_FAULT;
			else if(pmic_powerup_cause & EXTON_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_EXTON;
			else if(pmic_powerup_cause & ONKEY_WAKEUP)
				PowerupCause = OS_POWER_CAUSE_NORMAL;
			else
				PowerupCause = OS_POWER_CAUSE_RESV;
			break;
	    }
    }
	return PowerupCause;
}

//#define		hal_ShutDown pmic_power_down

VOID DM_CheckPowerOnCause(VOID)
{
  UINT8 POSSIBLY_UNUSED Causeflag;

    OS_POWERON_CAUSE_T powerCause;
	unsigned char userdata;

    powerCause = hal_SysGetPowerUpCause();
	userdata = pmic_userdata_reg_read();
    raw_uart_log("DM_CheckPowerOnCause =%d,userdata=0x%x\n",powerCause,userdata);

	if(startup_is_silent_reset())
	{
		raw_uart_log("###DM_CheckPowerOnCause silent reset\n");
		if(userdata & Before_silentBootupCharger_Flag)
			PowerOn_Cause = DM_POWRN_ON_CAUSE_CHARGE;
		else
			PowerOn_Cause = DM_POWRN_ON_CAUSE_KEY;
	}
	else
	{
		switch (powerCause)
		{
			case OS_POWER_CAUSE_NORMAL:
				PowerOn_Cause = DM_POWRN_ON_CAUSE_KEY;
				break;

			case OS_POWER_CAUSE_ALARM:
				PowerOn_Cause = DM_POWRN_ON_CAUSE_ALARM;
				break;

			case OS_POWER_CAUSE_CHARGER:
			case OS_POWER_CAUSE_EXTON:
				if (pmic_get_powerup_reason() & ONKEY_WAKEUP)//if (hal_KeyOnOffStateAtPowerOn())
				{
					PowerOn_Cause = DM_POWRN_ON_CAUSE_KEY;
					raw_uart_log("!!!DM_CheckPowerOnCause charger and onkey press\n");
				}
				else
				{
					if(userdata & UserData_Mask)
					{
						PowerOn_Cause = DM_POWRN_ON_CAUSE_KEY;
						raw_uart_log("!!!charger and userdata:0x%x\n",userdata);
					}
					else if(pm812_get_charger_status())
					{
						PowerOn_Cause = DM_POWRN_ON_CAUSE_CHARGE;
						raw_uart_log("!!!DM_CheckPowerOnCause Charger plugged pmic wakeup by charger\n");
					}
					else
					{
						raw_uart_log("!!!DM_CheckPowerOnCause Charger plugged out\n");
						pmic_power_down();
					}
				}
				break;

			case OS_POWER_CAUSE_FAULT:
				pmic_fault_clear();
				if((userdata & UserData_Mask) || (pmic_get_powerdown_reason() & 0x31))
				{
					PowerOn_Cause = DM_POWRN_ON_CAUSE_KEY;
					raw_uart_log("!!!FAULT start userdata:0x%x\n",userdata);
				}
				else if(pm812_get_charger_status())
				{
					PowerOn_Cause = DM_POWRN_ON_CAUSE_CHARGE;
					raw_uart_log("!!!boot up fault reason, Charger plugged pmic wakeup by charger\n");
				}
				else if(isRestartByCharger())
				{
					raw_uart_log("!!!boot up fault reason,Charger plugged out\n");
					pmic_power_down();
				}
				else
					PowerOn_Cause = DM_POWRN_ON_CAUSE_RESET;
				break;
			case OS_POWER_CAUSE_BATTERY:
				raw_uart_log("!!!DM_CheckPowerOnCause bat wakeup cause call shut down\n");
				pmic_power_down();
				break;
			default:
				raw_uart_log("!!!DM_CheckPowerOnCause NO power up cause!!!!!!!!!!!\n");
				break;
		}
	}
	if(userdata & 0xf)
	{
		pmic_userdata_reg_clear(0xf);
		raw_uart_log("!!!userdata Clear\n");
	}
}

BOOL dm_PowerOnCheck(UINT16* pCause)
{
  UI_EVENT ev;
  UINT8 POSSIBLY_UNUSED Causeflag;
  raw_uart_log("dm_PowerOnCheck++.\n");

  SUL_ZeroMemory32( (VOID *)&ev, SIZEOF(UI_EVENT) );

//  PowerOn_Cause = DM_POWRN_ON_CAUSE_KEY;
//  DM_CheckPowerOnCause();

  ev.nEventId = EV_DM_POWER_ON_IND;
  ev.nParam1 = PowerOn_Cause;

  if(pCause)
    *pCause = PowerOn_Cause;
  dm_SendPowerOnMessage(&ev);
  return TRUE; 
}

UINT32 DM_GetPowerOnCause(void)
{
  return PowerOn_Cause;
}

BOOL DM_IsPowerOnByCharger(void)
{
  return DM_POWRN_ON_CAUSE_CHARGE == PowerOn_Cause;
}

static void trigger_dev_task(void *p)
{
	// send event to dev mon task
	UI_EVENT ev;
	ev.nEventId = EV_TIM_SET_TIME_IND;
    ev.nParam1  = 0;
	ev.nParam2  = 0;
    ev.nParam3  = 0;
	UOS_SendEvent(get_dev_mon_task(), &ev, UOS_WAIT_FOREVER, UOS_EVENT_PRI_NORMAL);
}

// Device Backgroud task. 
VOID UI_DevMonitorTask (VOID* pData)
{
	UI_EVENT Rev;
    UINT16 Cause=0;
	u8 dev_timer_id = INVALID_TIMER_ID;

	dm_PowerOnCheck(&Cause);
    //
    // Init battery and charge routines.
    //
#ifdef CONFIG_ENABLE_CHARGER_SUPPORT
    // pm_BatteryInit();

	dev_timer_id = UOS_get_FunctionTimer();
	UOS_StartFunctionTimer_single(dev_timer_id, MS_TO_TICKS(1000), trigger_dev_task, NULL, "devMon");
#endif

	UOS_DelayTaskRunning();

	raw_uart_log("UI_DevMonitorTask++.\n");
    for(;;)
    {	
//        pm_BatteryMonitorProc();

		if (UOS_WaitEvent(get_dev_mon_task(), &Rev, OS_SUSPEND))
		{
            if (EV_DM_IOCTL_IND == Rev.nEventId)
            {
#ifdef CONFIG_LCD_DUAL_PANEL_SUPPORT
				if(1 == Rev.nParam2)
				{
					if(Rev.nParam1 == 0)
						mci_SetActiveLcd(MCI_LCD_ID_SUB);
					else
						mci_SetActiveLcd(MCI_LCD_ID_MAIN);		
				}
#endif
				if(0 == Rev.nParam2)
				{
					EnableSpeakerPA(Rev.nParam1);	
				}
#if 0

				if(2 == Rev.nParam2)
				{
					mci_LcdSetBrightness(g_mciLcdBrightnessLevel);
				}
#endif
			}
#if 0
            if(EV_DM_POWER_OFF_IND == Rev.nEventId)
            {
                raw_uart_log("###PowerOn_Cause Charger,Charger plugged out\n");
                pmic_power_down();
            }
			if(EV_TIM_SET_TIME_IND == Rev.nEventId)
			{
				pm_BatteryMonitorProc();
				UOS_StartFunctionTimer_single(dev_timer_id, MS_TO_TICKS(Time_To_MMI_Evt), trigger_dev_task, NULL, "devMon");
			}
#endif
#if 0
			if(EV_VIBRATOR_PLAY == Rev.nEventId)
			{
				if(Rev.nParam1 == 1)
					VibratorSwitchOn();
				else
					VibratorSwitchOff();
			}

#endif
		}
    }
}

VOID UI_UsbMonitorTask (VOID* pData)
{
	raw_uart_log("UI_UsbMonitorTask++.\n");

	UOS_DelayTaskRunning();
    for(;;)
    {
#ifndef CONFIG_RELEASE_WITH_NO_ASR_UI
#ifdef CONFIG_ENABLE_CHARGER_SUPPORT
        pm_UsbMonitorProc();
#endif
#endif
    }
}

