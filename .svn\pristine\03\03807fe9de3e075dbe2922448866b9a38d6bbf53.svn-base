
//#include "sl_def.h"
#include "asr_crane_aliota.h"
#include "asr_aliota.h"


#ifdef CONFIG_FOTA
#include "adups_typedef.h"
#include "adups_define.h"
//#include "osp_def.h"
//#include "osp_time.h"
adups_char* adups_get_device_oem(void)
{
	return ADUPS_FOTA_SERVICE_OEM;	
}

adups_char* adups_get_device_model(void)
{
	return ADUPS_FOTA_SERVICE_MODEL;		
}

adups_char* adups_get_device_product_id(void)
{
	return ADUPS_FOTA_SERVICE_PRODUCT_ID;		
}

adups_char* adups_get_device_product_sec(void)
{
	return ADUPS_FOTA_SERVICE_PRODUCT_SEC;		
}

adups_char* adups_get_device_type(void)
{
	return ADUPS_FOTA_SERVICE_DEVICE_TYPE;		
}

adups_char* adups_get_device_platform(void)
{
	return ADUPS_FOTA_SERVICE_PLATFORM;		
}

adups_char* adups_get_device_network_type(void)
{
	return ADUPS_DEVICE_NETWORKTYPE;		
}

adups_char* adups_get_device_version(void)
{
	return ADUPS_DEVICE_VERSION;
}

adups_char* adups_get_mid(void)
{
	return ADUPS_DEVICE_MID;
}

adups_int32 adups_get_flash_disksize(void)
{
#ifdef DLDATA_IN_FLASH 
	return get_fota_total_size();
#elif defined (DLDATA_IN_SD)
	return ADUPS_DEVICE_FREE_SPACE;
#endif
}

adups_uint32 adups_gettime(void)
{
    return Osp_Time_GetSysTimeStamp();
}
#endif

