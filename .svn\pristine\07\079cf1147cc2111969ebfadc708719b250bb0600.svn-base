/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_DGNSS_SatList_H_
#define	_DGNSS_SatList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct DGNSS_CorrectionsElement;

/* DGNSS-SatList */
typedef struct DGNSS_SatList {
	A_SEQUENCE_OF(struct DGNSS_CorrectionsElement) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} DGNSS_SatList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_DGNSS_SatList;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "DGNSS-CorrectionsElement.h"

#endif	/* _DGNSS_SatList_H_ */
#include <asn_internal.h>
