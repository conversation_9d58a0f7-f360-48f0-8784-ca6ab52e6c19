\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\net_sockets.c
\pcac\mbedTLS\mbedTLS_3_2_1\library\net_sockets.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\common.h
\pcac\mbedTLS\mbedTLS_3_2_1\library\common.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h
\tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/net_sockets.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/net_sockets.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ssl.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ssl.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ssl_ciphersuites.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ssl_ciphersuites.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/pk.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/pk.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/rsa.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/rsa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/cipher.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/cipher.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/x509_crt.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/x509_crt.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/x509.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/x509.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/x509_crl.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/x509_crl.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdh.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdh.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/sockets.h
/pcac/lwipv4v6/src/include/lwip/sockets.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/opt.h
/pcac/lwipv4v6/src/include/lwip/opt.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/arch/lwipopts.h
/pcac/lwipv4v6/src/include/arch/lwipopts.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/arch/cc.h
/pcac/lwipv4v6/src/include/arch/cc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hal\UART\inc\UART.h
\hal\UART\inc\UART.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\pmu\inc\pmu.h
\hop\pmu\inc\pmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\src\diag_nvm.h
\diag\diag_logic\src\diag_nvm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\inc\diag.h
\diag\diag_logic\inc\diag.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\inc\diag_API.h
\diag\diag_logic\inc\diag_API.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\inc\diag_types.h
\diag\diag_logic\inc\diag_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/osa.h
/os/osa/inc/osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\armv7r\include\alios_type.h
\os\alios\kernel\armv7r\include\alios_type.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/osa_old_api.h
/os/osa/inc/osa_old_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/osa.h
/os/osa/inc/osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/osa_utils.h
/os/osa/inc/osa_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\BSP\inc\bsp_hisr.h
\csw\BSP\inc\bsp_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\nu_xscale\inc\nucleus.h
\os\nu_xscale\inc\nucleus.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/osa_internals.h
/os/osa/inc/osa_internals.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_api.h
\os\alios\kernel\rhino\include\k_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\asr3601\config\k_config.h
\os\alios\asr3601\config\k_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_default_config.h
\os\alios\kernel\rhino\include\k_default_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\armv7r\include\k_types.h
\os\alios\kernel\armv7r\include\k_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\armv7r\include\k_compiler.h
\os\alios\kernel\armv7r\include\k_compiler.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_err.h
\os\alios\kernel\rhino\include\k_err.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_sys.h
\os\alios\kernel\rhino\include\k_sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_critical.h
\os\alios\kernel\rhino\include\k_critical.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_spin_lock.h
\os\alios\kernel\rhino\include\k_spin_lock.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_list.h
\os\alios\kernel\rhino\include\k_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_obj.h
\os\alios\kernel\rhino\include\k_obj.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_sched.h
\os\alios\kernel\rhino\include\k_sched.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_task.h
\os\alios\kernel\rhino\include\k_task.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_ringbuf.h
\os\alios\kernel\rhino\include\k_ringbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_queue.h
\os\alios\kernel\rhino\include\k_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_buf_queue.h
\os\alios\kernel\rhino\include\k_buf_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_sem.h
\os\alios\kernel\rhino\include\k_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_task_sem.h
\os\alios\kernel\rhino\include\k_task_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_mutex.h
\os\alios\kernel\rhino\include\k_mutex.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_timer.h
\os\alios\kernel\rhino\include\k_timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_time.h
\os\alios\kernel\rhino\include\k_time.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_event.h
\os\alios\kernel\rhino\include\k_event.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_stats.h
\os\alios\kernel\rhino\include\k_stats.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_mm_debug.h
\os\alios\kernel\rhino\include\k_mm_debug.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_mm.h
\os\alios\kernel\rhino\include\k_mm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_mm_blk.h
\os\alios\kernel\rhino\include\k_mm_blk.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_mm_region.h
\os\alios\kernel\rhino\include\k_mm_region.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_workqueue.h
\os\alios\kernel\rhino\include\k_workqueue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_internal.h
\os\alios\kernel\rhino\include\k_internal.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_trace.h
\os\alios\kernel\rhino\include\k_trace.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_soc.h
\os\alios\kernel\rhino\include\k_soc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_hook.h
\os\alios\kernel\rhino\include\k_hook.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\rhino\include\k_bitmap.h
\os\alios\kernel\rhino\include\k_bitmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\armv7r\include\port.h
\os\alios\kernel\armv7r\include\port.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\armv7r\include\k_vector.h
\os\alios\kernel\armv7r\include\k_vector.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\armv7r\include\k_cache.h
\os\alios\kernel\armv7r\include\k_cache.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\alios\kernel\armv7r\include\k_mmu.h
\os\alios\kernel\armv7r\include\k_mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/osa_ali.h
/os/osa/inc/osa_ali.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/alios_hisr.h
/os/osa/inc/alios_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /os/osa/inc/osa_um_extr.h
/os/osa/inc/osa_um_extr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \os\nu_xscale\inc\um_defs.h
\os\nu_xscale\inc\um_defs.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\inc\diag_config.h
\diag\diag_logic\inc\diag_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\inc\diag_osif.h
\diag\diag_logic\inc\diag_osif.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\BSP\inc\asserts.h
\csw\BSP\inc\asserts.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\timer\inc\timer.h
\hop\timer\inc\timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\SysCfg\inc\syscfg.h
\csw\SysCfg\inc\syscfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\timer\inc\timer_config.h
\hop\timer\inc\timer_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\inc\diag_pdu.h
\diag\diag_logic\inc\diag_pdu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\platform\inc\ICAT_config.h
\csw\platform\inc\ICAT_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\BSP\inc\bsp.h
\csw\BSP\inc\bsp.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\PM\inc\powerManagement.h
\csw\PM\inc\powerManagement.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\pm\inc\pm_config.h
\hop\pm\inc\pm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \softutil\TickManager\inc\tick_manager.h
\softutil\TickManager\inc\tick_manager.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\intc_list.h
\hop\intc\inc\intc_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hal\GPIO\inc\gpio_config.h
\hal\GPIO\inc\gpio_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\intc_list_xirq.h
\hop\intc\inc\intc_list_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\xirq_config.h
\hop\intc\inc\xirq_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hal\GPIO\inc\gpio.h
\hal\GPIO\inc\gpio.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hal\GPIO\inc\cgpio_HW.h
\hal\GPIO\inc\cgpio_HW.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\intc_xirq.h
\hop\intc\inc\intc_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\BSP\inc\levante_hw.h
\hop\BSP\inc\levante_hw.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \hop\BSP\inc\levante.h
\hop\BSP\inc\levante.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\src\diag_tx.h
\diag\diag_logic\src\diag_tx.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \diag\diag_logic\src\diag_API_var.h
\diag\diag_logic\src\diag_API_var.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/arch/lwipopts_platform.h
/pcac/lwipv4v6/src/include/arch/lwipopts_platform.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/arch/lwipopts_crane.h
/pcac/lwipv4v6/src/include/arch/lwipopts_crane.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/debug.h
/pcac/lwipv4v6/src/include/lwip/debug.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/arch.h
/pcac/lwipv4v6/src/include/lwip/arch.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/opt.h
/pcac/lwipv4v6/src/include/lwip/opt.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/ip_addr.h
/pcac/lwipv4v6/src/include/lwip/ip_addr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/def.h
/pcac/lwipv4v6/src/include/lwip/def.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\ip4_addr.h
\tavor\Arbel\obj_PMD2NONE\inc\ip4_addr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\ip6_addr.h
\tavor\Arbel\obj_PMD2NONE\inc\ip6_addr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\inet.h
\tavor\Arbel\obj_PMD2NONE\inc\inet.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\inet6.h
\tavor\Arbel\obj_PMD2NONE\inc\inet6.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/arch/sys_arch.h
/pcac/lwipv4v6/src/include/arch/sys_arch.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/err.h
/pcac/lwipv4v6/src/include/lwip/err.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/mem.h
/pcac/lwipv4v6/src/include/lwip/mem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\time.h
\tavor\Arbel\obj_PMD2NONE\inc\time.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\signal.h
\tavor\Arbel\obj_PMD2NONE\inc\signal.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/netdb.h
/pcac/lwipv4v6/src/include/lwip/netdb.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/api_msg.h
/pcac/lwipv4v6/src/include/lwip/api_msg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/sys.h
/pcac/lwipv4v6/src/include/lwip/sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\igmp.h
\tavor\Arbel\obj_PMD2NONE\inc\igmp.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/netif.h
/pcac/lwipv4v6/src/include/lwip/netif.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/pbuf.h
/pcac/lwipv4v6/src/include/lwip/pbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : \tavor\Arbel\obj_PMD2NONE\inc\dhcp6.h
\tavor\Arbel\obj_PMD2NONE\inc\dhcp6.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/api.h
/pcac/lwipv4v6/src/include/lwip/api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/lwip/netbuf.h
/pcac/lwipv4v6/src/include/lwip/netbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/net_sockets.o : /pcac/lwipv4v6/src/include/ipv4/inet.h
/pcac/lwipv4v6/src/include/ipv4/inet.h:
