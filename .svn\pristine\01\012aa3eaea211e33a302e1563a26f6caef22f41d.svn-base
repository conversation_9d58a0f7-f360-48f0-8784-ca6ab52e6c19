/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/**********************************************************************
*
* Filename: pl_mfunc.h
*
* Programmers: Kiril Serebnik (ska)
*
* Description: Contains declarations of mode main functions
*
* --------------------------------------------------------------------
* Revision History
*
* Date         Who        Version           Description
* --------------------------------------------------------------------
* 07-Jan-2001  ska        0.01               File Created
* 26-Mar-2001  vg              Declaration of msr mode functions added
**********************************************************************/
#ifndef PL_MFUNC_H
#define PL_MFUNC_H

#include "cmmn_dfs.h"
#include "pl_edefs.h"

/**********************************************************************
*
* Defines a general form of mode's function. All mode's functions should be
* of this type.
*
**********************************************************************/
typedef VOID (mode_function)(aplp_event, VOID_PTR);

/**********************************************************************
*
* Defines a general form of mode's init function.
* All mode's init functions should be of this type.
*
**********************************************************************/
typedef VOID (init_mode_function)(aplp_event, VOID_PTR);

/**********************************************************************
*
* Declaration of all mode functions
*
**********************************************************************/
/* PCC mode functions */
VOID plMsPccpchChannel (aplp_event, VOID_PTR);
VOID plMsSccpchChannel (aplp_event, VOID_PTR);
#ifndef  L1_WB_R99_ONLY

VOID plMsEcfHsdpaChannel (aplp_event, VOID_PTR);
VOID plMsCpcHandler (aplp_event, VOID_PTR);
#endif
VOID plMsPrachChannel (aplp_event, VOID_PTR);
VOID plMsPichChannel (aplp_event, VOID_PTR);
VOID plMsCbsChannel (aplp_event, VOID_PTR);
VOID plMsDpchChannel (aplp_event, VOID_PTR);
#ifndef  L1_WB_R99_ONLY

VOID plMsHsdpaChannel (aplp_event, VOID_PTR);
VOID plMsHsupaChannel (aplp_event, VOID_PTR);
#endif
VOID plMsDefaultHandler (aplp_event, VOID_PTR);
#if defined (L1_UPGRADE_R8)
VOID plMsTcHsscchHandler (aplp_event, VOID_PTR);
VOID TmDriverModeMatchEvent (aplp_event, VOID_PTR);
#endif
VOID TmNoSleepModeMatchEvent (aplp_event, VOID_PTR);
VOID TmSleepModeMatchEvent (aplp_event, VOID_PTR);
VOID plMsSleepMgrHandler (aplp_event, VOID_PTR);

/* TCC mode functions */
VOID tcc_init_mode(aplp_event, VOID_PTR);
VOID tcc_interrupt_mode(aplp_event, VOID_PTR);
VOID tcc_bch_mode(aplp_event, VOID_PTR);
VOID tcc_drx_mode(aplp_event, VOID_PTR);
VOID tcc_rach_mode(aplp_event, VOID_PTR);
VOID tcc_fach_mode(aplp_event, VOID_PTR);
VOID tcc_dch_mode(aplp_event, VOID_PTR);
VOID tcc_hsdsch_mode(aplp_event, VOID_PTR);
VOID tcc_default_mode(aplp_event, VOID_PTR);

/* MSR mode functions */
VOID msr_init_mode(aplp_event, VOID_PTR);
VOID msr_cell_search_mode(aplp_event, VOID_PTR);
VOID msr_oos_mode(aplp_event, VOID_PTR);
VOID msr_bcch_meas_mode(aplp_event, VOID_PTR);
VOID msr_default_mode(aplp_event, VOID_PTR);
void msr_gsm_wb_meas_mode(aplp_event , VOID_PTR);
#ifdef UPGRADE_LTE
void msr_lte_wb_meas_mode(aplp_event , VOID_PTR);
#endif

void init_msr_gsm_wb_meas_mode(aplp_event event, VOID_PTR msg);


/* Common default mode function */
VOID aplp_default_mode(aplp_event, VOID_PTR);

VOID schd_non_wb_mode(aplp_event e, VOID_PTR msg);


/* SCHD mode functionsn */
VOID schd_wb_mode(aplp_event, VOID_PTR);
#ifdef APLP_DUALSIM_SUPPORT
VOID schd_wg_mode(aplp_event, VOID_PTR);
#endif
#define	APLP_DEFAULT_MODE	aplp_default_mode


/**********************************************************************
*
* Declaration of all mode init functions
*
**********************************************************************/
/* PCC init mode functions */
VOID plMsPccpchChannelInit (aplp_event, VOID_PTR);
VOID plMsSccpchChannelInit (aplp_event, VOID_PTR);
VOID plMsEcfHsdpaChannelInit (aplp_event, VOID_PTR);
VOID plMsCpcHandlerInit (aplp_event, VOID_PTR);
VOID plMsPrachChannelInit (aplp_event, VOID_PTR);
VOID plMsPichChannelInit (aplp_event, VOID_PTR);				
VOID plMsCbsChannelInit (aplp_event, VOID_PTR);
VOID plMsDpchChannelInit (aplp_event, VOID_PTR);
VOID plMsDefaultHandlerInit (aplp_event, VOID_PTR);
VOID plMsSleepMgrHandlerInit (aplp_event, VOID_PTR);

#if defined (L1_UPGRADE_R8)
VOID plMsTcHsscchHandlerInit (aplp_event, VOID_PTR);
#endif
/* TCC mode functions */
VOID init_tcc_init_mode(aplp_event, VOID_PTR);
VOID init_tcc_interrupt_mode(aplp_event, VOID_PTR);
VOID init_tcc_bch_mode(aplp_event, VOID_PTR);
VOID init_tcc_drx_mode(aplp_event, VOID_PTR);
VOID init_tcc_rach_mode(aplp_event, VOID_PTR);
VOID init_tcc_fach(aplp_event, VOID_PTR);
VOID init_tcc_dch_mode(aplp_event, VOID_PTR);
VOID init_tcc_hsdsch_mode(aplp_event, VOID_PTR);
VOID init_tcc_default_mode(aplp_event, VOID_PTR);


/* Measurements init mode function */
VOID init_msr_init_mode(aplp_event, VOID_PTR);
VOID init_msr_cell_search_mode(aplp_event, VOID_PTR);
VOID plMsrOosInit(aplp_event, VOID_PTR);
VOID init_bcch_meas_mode(aplp_event, VOID_PTR);
VOID init_msr_default_mode(aplp_event, VOID_PTR);
#ifdef UPGRADE_LTE
void init_msr_gsm_wb_meas_mode(aplp_event event, VOID_PTR msg);
void init_msr_lte_wb_meas_mode(aplp_event event, VOID_PTR msg);					
#endif
void msr_gsm_wb_meas_mode(aplp_event event, VOID_PTR msg);					

/* Common default init mode function */
VOID init_aplp_default_mode(aplp_event, VOID_PTR);
void init_tm_no_sleep(aplp_event event, VOID_PTR pMsg);
void init_tm_sleep(aplp_event event, VOID_PTR pMsg);
#if defined (L1_UPGRADE_R8)
void init_tm_driver(aplp_event event, VOID_PTR pMsg);
#endif
#define	INIT_APLP_DEFAULT_MODE   init_aplp_default_mode

/* SCHD mode functions */
VOID init_schd_wb_mode(aplp_event, VOID_PTR);
#ifdef APLP_DUALSIM_SUPPORT
VOID init_schd_wg_mode(aplp_event, VOID_PTR);
#endif
#endif   /* PL_MFUNC_H */
