#ifndef __NFC_EFLASH_ARM_H__
#define __NFC_EFLASH_ARM_H__

#include "MyInclude.h"
#include <stdio.h>
#include <stdint.h>

#define NFC_ARM_FRAME_LEN 1024
#define NFC_ARM_SOH 0x1
#define NFC_ARM_EOT 0x4

enum NFC_ARM_CMD_SET {
    NFC_ARM_CMD_DOWNLOAD,
    NFC_ARM_CMD_DOWN_TO_RAM,
    NFC_ARM_CMD_DUMP_MEMORY,
    NFC_ARM_CMD_WRITE_FLASH,
    NFC_ARM_CMD_ERASE_PAGE,
    NFC_ARM_CMD_SCAN_TEST,
    NFC_ARM_CMD_BIST_TEST,
    NFC_ARM_CMD_WRITE_WITH_ERASE,
    NFC_ARM_CMD_ERASE_FLASH,
    NFC_ARM_CMD_ERASE_REF_CELL,
    NFC_ARM_CMD_GOTO_FLASH,
    NFC_ARM_CMD_WRITE_REG,
    NFC_ARM_CMD_READ_REG,
    NFC_ARM_CMD_CONFIG_FLASH_WRITE,
    NFC_ARM_CMD_CONFIG_FLASH_READ,
    NFC_ARM_CMD_CALIBRATE,
    NFC_ARM_CMD_CHECK_INTEGRITY,
    NFC_ARM_CMD_WRITE_INFO,
    NFC_ARM_CMD_GOTO_SLEEP,
};

enum nfc_arm_vendor_info_idx {
    NFC_ARM_FLASH_WRITE_P = 0,
    NFC_ARM_FLASH_READ_P = 0x10,
    NFC_ARM_HOST_INTF = 0x14,
    NFC_ARM_SPI_MODE = 0x17,
    NFC_ARM_OSC_48M = 0x18,
    NFC_ARM_OSC_32K = 0x19,
    NFC_ARM_CAL_FLAG = 0x1A,
    NFC_ARM_UART_SEL = 0x1B,
    NFC_ARM_UART_BAUD = 0x1C,
    NFC_ARM_GPIO_G1_DIR = 0x1D,
    NFC_ARM_GPIO_G1_DATA = 0x1E,
    NFC_ARM_GPIO_PE_CFG00 = 0x1F,
    NFC_ARM_GPIO_PE_CFG01 = 0x20,
    NFC_ARM_GPIO_PE_CFG02 = 0x21,
};

char nfc_arm_write_info(unsigned int addr, uint8_t * bytes, uint8_t len);
char nfc_arm_erase_chip(unsigned char total);
char nfc_arm_download_firmware(unsigned int addr);
void nfc_arm_write_reg(unsigned int addr, unsigned char val);
void nfc_arm_read_reg(unsigned int addr);
void nfc_arm_calibrate(unsigned char pre_ldo, unsigned int clk_freq);
void nfc_arm_adjust_clk_without_calibrate(void);
unsigned char nfc_arm_read_reg_get_val(unsigned int addr);
uint16_t nfc_arm_update_proc_from_code(void);
void  nfc_arm_write_info_init(void);
#endif
