#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include "cpu.h"
#include "qspi.h"
#include "reg.h"
#include "system.h"
//#include "uart.h"
#include "ptable.h"
#include "loadtable.h"
#include "lzop.h"
#include "sdio.h"
#include "pmic.h"
#include "ff.h"
#include "version_logo.h"
#include "mci_lcd.h"
#include "lcd_test.h"
#include "asr_property.h"
#include "guilin.h"
#include "logo_table.h"
#include "asr_lzma.h"
#include "utilities.h"
#include "bsp.h"

//unsigned int init_cpsr;

/*
*****************************************************************************************
*			some debug macros
*
*Note:  the following MACRO default state: "CLOSED".
*2. TEST_OTA_DISPLAY_INFO              	If define this MACRO, add debug code for OTA_DISPLAY
*
*****************************************************************************************
*/
//#define TEST_OTA_DISPLAY_INFO

/*
*****************************************************************************************
*			some function MACRO
*
*Note:  the following MACROs default state: "OPEN".
*1. ADD_VIBRATOR_IN_CODE				If define this MACRO, add vibrator feedback after onkeycheck during boot up.
*2. SUPPORT_MALLOC						If define this MACRO, support malloc mechanism
*****************************************************************************************
*/
#define ADD_VIBRATOR_IN_CODE
//#define SUPPORT_MALLOC

#define LZOP_COMPRESSED_MAGIC (0x4f5a4c89)
#define LZMA_COMPRESSED_MAGIC (0x8000005D)
#define LZMA_COMPRESSED_MAGIC1 (0x0800005D)
#define LZMA_COMPRESSED_MAGIC2 (0x0400005D)
#define LZMA_COMPRESSED_MAGIC3 (0x0200005D)


#define MODE32_svc          0x13
#define SPSR_T_ARM          0
#define SPSR_E_LITTLE       0
#define SPSR_FIQ_BIT        (1 << 0)
#define SPSR_IRQ_BIT        (1 << 1)
#define SPSR_ABT_BIT        (1 << 2)
#define DISABLE_ALL_EXCEPTIONS  (SPSR_FIQ_BIT | SPSR_IRQ_BIT | SPSR_ABT_BIT)

#define MODE_RW_SHIFT       0x4
#define MODE_RW_32      0x1
#define MODE32_SHIFT        0
#define MODE32_MASK     0x1f
#define SPSR_T_SHIFT        5
#define SPSR_T_MASK     0x1
#define SPSR_E_SHIFT        9
#define SPSR_E_MASK     0x1
#define SPSR_AIF_SHIFT      6
#define SPSR_AIF_MASK       0x7

#define SPSR_MODE32(mode, isa, endian, aif)     \
    (MODE_RW_32 << MODE_RW_SHIFT |          \
     ((mode) & MODE32_MASK) << MODE32_SHIFT |    \
     ((isa) & SPSR_T_MASK) << SPSR_T_SHIFT |     \
     ((endian) & SPSR_E_MASK) << SPSR_E_SHIFT |  \
     ((aif) & SPSR_AIF_MASK) << SPSR_AIF_SHIFT)

/******************************
MACRO NAME: ADD_VIBRATOR_IN_CODE
Despriction:   Add vibrator feedback after onkeycheck during boot up. 
		       Default: no vibrator feedback
		       
Owner:         Hengshan Yang
Time:          2019-10-16
******************************/


typedef struct {
#if 1 //yqian
    uint32_t  pc;
#else
    uintptr_t pc;
#endif
    uint32_t  spsr;
} transfer_parameter;

/* enum for battery operation */
typedef enum{
	BatteryLevelIsHigh = 0,
	BatteryLevelIsMedium,
	LowBatteryLevel_And_ChargerNoDetect,
	GetBatteryLevelFail,
	Calbration_flag_Not_Set,
	Dsp_adc_NotExist
}LogoBattery_t;



/* global variable for malloc mechanism  */
#ifdef SUPPORT_MALLOC
#define MALLOC_BUFFER_SIZE (0x00012000)
UINT8 g_MallocBuffer[MALLOC_BUFFER_SIZE];
#endif

/* MACRO for config cache */
// NOTE : cache start addr must br multiple of cache size and cache size must 2,4,8..
#define PSRAM_CONFIG_CACHE_START_ADDR 0x7E000000
#define PSRAM_CONFIG_CACHE_SIZE 	  0x01000000	


static int cpy_dsp_by_cp=0;
volatile unsigned long DspBackupAddress;
volatile unsigned long DspFlashAddress;
volatile unsigned long CpExecAddress;
volatile unsigned long CpCopySize;
volatile unsigned long DspExecAddress;
volatile unsigned long DspLoadSize;
volatile unsigned long CpFlashAddress;
volatile unsigned long Cp_2_FlashAddress;
volatile unsigned long rfCopySize;
volatile unsigned long rfFlashAddress;
volatile unsigned long rfLoadAddress;
volatile unsigned long DspImageSize;
volatile unsigned long DspBackupSize;
volatile unsigned long DspRamSize=0;

#define FLASH_CP_START  CpFlashAddress
#define FLASH_DSP_START DspFlashAddress
#define PSRAM_DSP_START DspExecAddress
#define DSP_LOAD_SIZE   DspLoadSize
#define DSPBACKUP_SIZE 	DspBackupSize

//#ifdef ONKEY_CHECK
/* Timer0_0 is configured to free run @1MHz from BootROM. */
#define APBTIMER0_CNT_REG   0xD4014090
#define APBTIMER0_EN_REG    0xD4014000

#define LOGO_HEAP_SIZE (0x00022000)

//void transfer_control(transfer_parameter *param) __attribute__ ((noreturn));
extern void transfer_control(transfer_parameter *param);
extern int lzop_decompress_safe(lzo_bytep src,  lzo_bytep dest, lzo_uint32p dest_len, lzo_uint32p cpz_src_len);
extern void cp_uart_init(void );

/* global variable for record "enable cache and improve core freq" for LZMA */
volatile BOOL g_LzmaOptimizeFlag = FALSE;

extern unsigned int Image$$IMG_END$$Base;

#define LOGO_IMG_END_ADDR ((unsigned int)&(Image$$IMG_END$$Base))

extern BOOL ifPsramLimit200M(void);


void Timer0_enable(BOOL enable){
	if(enable){
        *(volatile unsigned long*)0xD4015034 = 0x43;
		*(volatile unsigned long*)APBTIMER0_EN_REG = 0x1; //enable timer0_0 (in free run)
	}else{
        *(volatile unsigned long*)0xD4015034 = 0x40;
	    *(volatile unsigned long*)APBTIMER0_EN_REG = 0x0; //stop Timer0_0 
	}
}



void GetKeyPartitionInfoFetch(void)
{	
	_ptentry *cpEntry, *cp_2_Entry;

	cpEntry = ptable_find_entry("cp");
	CpFlashAddress = cpEntry->vstart;
	if(cpEntry == NULL)
		CpFlashAddress = CP_BASE_ADDR;//default address as backup one

	cp_2_Entry = ptable_find_entry("cp_2");
	if(cpEntry != NULL)
		Cp_2_FlashAddress = cp_2_Entry->vstart;	
}



int set_mpu_reg_0(void )
{
    unsigned int acc_ctrl;
    uintptr_t   base = 0x00000000;
    size_t      size = 0x80000000;

    /* Grant read, write and excution privilege to the code area */
    acc_ctrl = MPU_REGION_ACCESS_CTRL_AP(0x3)       /* read, write */
               | MPU_REGION_ACCESS_CTRL_TEX(0x1);   /* non-cacheable */

    mpu_set_region(0, base, size, acc_ctrl);
    mpu_enable_region(0, 1);
    return 0;
}

int set_mpu_reg_1(void )
{
    unsigned int acc_ctrl;
    uintptr_t   base = 0x00000000;
    size_t      size = 0x80000000;

    /* Grant read, write and excution privilege to the code area */
    acc_ctrl = MPU_REGION_ACCESS_CTRL_AP(0x3)       /* read, write */
               | MPU_REGION_ACCESS_CTRL_TEX(0x1);   /* non-cacheable */

    mpu_set_region(1, base, size, acc_ctrl);
    mpu_enable_region(1, 1);
    return 0;
}

int set_mpu_reg_2(void )
{
    unsigned int acc_ctrl;
    uintptr_t   base = 0x80000000;
    size_t      size = 0x80000000;

    /* Grant read, write and excution privilege to the code area */
    acc_ctrl = MPU_REGION_ACCESS_CTRL_AP(0x3)       /* read, write */
               | MPU_REGION_ACCESS_CTRL_TEX(0x1);   /* non-cacheable */

    mpu_set_region(2, base, size, acc_ctrl);
    mpu_enable_region(2, 1);
    return 0;
}

int set_mpu_reg_3(void )
{
    unsigned int acc_ctrl;
    uintptr_t   base = 0x80000000;
    size_t      size = 0x4000000; //64MB

    /* Grant read, write and excution privilege to the code area */
    acc_ctrl = MPU_REGION_ACCESS_CTRL_AP(0x3)       /* read, write */
               | MPU_REGION_ACCESS_CTRL_TEX(0x1)   /* non-cacheable */
               | MPU_REGION_ACCESS_CTRL_B          /* bufferable */
               | MPU_REGION_ACCESS_CTRL_C;         /* cacheable */

    mpu_set_region(3, base, size, acc_ctrl);
    mpu_enable_region(3, 1);
    return 0;
}

int set_mpu_for_DSP(void )
{
    unsigned int acc_ctrl;
    uintptr_t   base = 0xd1000000;
    size_t      size = 0x00100000;

    /* Grant read, write and excution privilege to the code area */
    acc_ctrl = MPU_REGION_ACCESS_CTRL_AP(0x3)       /* read, write */
               | MPU_REGION_ACCESS_CTRL_TEX(0x1);   /* non-cacheable */

    mpu_set_region(1, base, size, acc_ctrl);
    mpu_enable_region(1, 1);
    return 0;
}

int set_mpu_reg_4(void)
{
    unsigned int acc_ctrl;
    uintptr_t   base = 0xc0000000;
    size_t      size = 0x40000000;// 1GB
    acc_ctrl =MPU_REGION_ACCESS_CTRL_XN
            | MPU_REGION_ACCESS_CTRL_AP(0x3)       /* read, write */
            | MPU_REGION_ACCESS_CTRL_TEX(0x0)   /* non-cacheable */
            | MPU_REGION_ACCESS_CTRL_B;         /* bufferable */

    mpu_set_region(4, base, size, acc_ctrl);
    mpu_enable_region(4, 1);

    return 0;
}

static int set_mpu_reg_5(uintptr_t base, size_t size)
{
    unsigned int acc_ctrl;

    /* Grant read, write and excution privilege to the code area */
    acc_ctrl = MPU_REGION_ACCESS_CTRL_AP(0x3)       /* read, write */
               | MPU_REGION_ACCESS_CTRL_TEX(0x1);   /* non-cacheable */

    mpu_set_region(5, base, size, acc_ctrl);
    mpu_enable_region(5, 1);

    return 0;
}


extern void mpu_value_check(void);
extern void   TransferControl(unsigned int);

#ifdef LCD_USE_IN_CODE
FIL handle;
unsigned char data_buf[64];

FIL handle_lcd;

#ifdef WATCHLCDST7789_CODE_USE
UINT16 picture_buf_2[115200/2];
#else
UINT16 picture_buf_2[153600/2];
#endif

//LCD function declaration
extern int mci_Logo_LcdInit(UINT32 background);
extern void POWER_ON_LCD_ShowImage(void);
extern VOID mci_LcdSetBrightness(UINT8 level);
extern void mci_Logo_LcdClose(void);

#if 0
void lcd_test()
{
	if(!sdcard_fat_is_ok()) {
		uart_printf("sdcard is not ok\n");
	}
	unsigned int ret;
	FRESULT res;
	res = f_open(&handle, "D:/0.bin", FA_READ);
	if(res != FR_OK) {
		uart_printf("read0 err:%d\n", res);
		return;
	}
	
	res = f_read(&handle, picture_buf_0,153600, &ret);
	if(res != FR_OK) {
		uart_printf("read0 err:%d\n", res);
		return;
	}
	f_close(&handle);
	res = f_open(&handle, "D:/1.bin", FA_READ);
	if(res != FR_OK) {
		uart_printf("read1 err:%d\n", res);
		return;
	}
	res = f_read(&handle, picture_buf_1,153600, &ret);
	if(res != FR_OK) {
		uart_printf("read1 err:%d\n", res);
		return;
	}
	f_close(&handle);
	
        //extern void keypad_test(void);
	//keypad_test();
	mci_Logo_LcdInit();
	uart_printf("lcd test2\r\n");
	//void test_LcdShowAssertInfo(UINT8 *isSync);
	//test_LcdShowAssertInfo(NULL);
	//extern void test_Ass_LcdShowImage(void);
	test_Logo_LcdShowImage();
	uart_printf("lcd test3\r\n");
}
#endif
#endif




unsigned long Timer0IntervalInMilli(unsigned long Before, unsigned long After)
{
    unsigned long temp = (After - Before);
    return (temp / (1000));
}

unsigned long GetTimer0CNT(void)
{
    return *(volatile unsigned long*)APBTIMER0_CNT_REG;
}

void DelayInMilliSecond(unsigned int ms)
{
    unsigned long startTime, endTime;

    startTime = GetTimer0CNT();
    do
    {
        endTime = GetTimer0CNT();  
    }
    while(Timer0IntervalInMilli(startTime, endTime) < ms);
}

#define PMUTIMER_WDT_STATUS_REG 0xD4080070
BOOL IfWdtResetTriggered(void)
{
	return ((*(volatile unsigned long*)PMUTIMER_WDT_STATUS_REG) & 0x1);
}

//#endif

void printlog(void)
{
	CP_LOGI("LOGO END\r\n");
	
	//int delay=0x8888;
	//while(delay--);
}

#ifdef TEST_OTA_DISPLAY_INFO
extern void OTA_LCD_DisplayText(char *text, int text_length,int16_t text_height_offset,OTA_LANGUAGE_T ota_language);
extern MCI_ASR_LCD_INFO lcd_info_test;
void test_lcd_display(void)
{
	INT32 i;

	init_lcd_vars();

	/* add test code to test lcd dispaly api */
	
	mci_LcdSetBrightness(3);	//Turn on the blacklight later to fix "Blurred Screen"
	while(1);	
}
#endif

void test_float_data( void )
{
	float f_1 = 5.0;
	float f_2 = 1.0;

	CP_LOGD("f_1:%d\r\n",(int)f_1);
}

void test_fitting_calculate(       void )
{							
						//  ADC			real vol
	const INT32 array[2][2] = { { 497,		3265 },
						  		{ 521,		3519 }
							  };
	INT32 result = 0;
	float k = 0.0, b = 0.0;
	
	CP_LOGD("func:%s\r\n",__func__);

	k = ( (float)array[1][1] - array[0][1] ) / ( array[1][0] - array[0][0] );
	b = (float)array[0][1] - array[0][0] * k ;

	result = (INT32)(k * 514 + b);

	CP_LOGD("result:%d\r\n",result);
}


extern const LogoHeaderTableStruct logo_vb;

BOOL IsDisplayLogo(const LogoBattery_t BatteryRes)
{
	/* NOTE: temp patch for CraneM */
	if(PMIC_IS_PM803()){
		CP_LOGW("[PM803] LCD Driver is ready, display logo!\r\n");
		return TRUE;
	}
	/* check startup reason is WDT reset or not? */
	if(IfWdtResetTriggered()){
		CP_LOGW("Restart by WDT reset\r\n");
		return FALSE; 
	}

	/*check restart reason is "RdProduction" or not*/
    if(isSysRestartByRdProduction()){
		CP_LOGW("Restart By RdProduction\r\n");
		return FALSE; 
    }
	
	/*check battery level */
	if(BatteryRes == LowBatteryLevel_And_ChargerNoDetect){
		CP_LOGI("battery level is less than low threshold and charger not detect, not display logo\r\n");
		return FALSE;
	}

	if( BatteryRes == GetBatteryLevelFail){
		CP_LOGI("battery level get fail, not display logo\r\n");
		while(1);
//		return FALSE;
	}
	if( BatteryRes == Calbration_flag_Not_Set || BatteryRes == Dsp_adc_NotExist ){
		return FALSE;
	}

	/* display logo is valid */
	CP_LOGI("display logo!\r\n");
	return TRUE;   	
}


void CheckBatteryLevel(LogoBattery_t *pBatteryRes)
{
	BOOL IsChargerDectect;
	UINT32 battery_level;
	static BOOL PrintFlag = TRUE;

	while(1){
		//get battery voltage
		if (PMIC_IS_PM813()||PMIC_IS_PM813S())
			battery_level = pm813_get_bat_vol();
		else if(PMIC_IS_PM802())
			battery_level = pm802_get_battery_voltage();
		else if( PMIC_IS_PM803() ){
			PM803_Battery_Res_t pm803_battery_res;
			Timer0_enable( TRUE );
			pm803_get_battery_voltage( &pm803_battery_res );
			battery_level = pm803_battery_res.BatteryValue;
			Timer0_enable( FALSE );
			CP_LOGI("pm803_battery_res:%d\r\n",pm803_battery_res.pm803_status_res);
			
			if( pm803_battery_res.pm803_status_res == PM803_CalbrationFlagNotSet ){
				*pBatteryRes = Calbration_flag_Not_Set;
				return;
			}
			if( pm803_battery_res.pm803_status_res == PM803_BatteryFlagNotReady ||
				pm803_battery_res.pm803_status_res == PM803_FitFail ||
				pm803_battery_res.pm803_status_res == PM803_Load_Dsp_adc_Fail ){
				*pBatteryRes = GetBatteryLevelFail;
				return;
			}
			if( pm803_battery_res.pm803_status_res == PM803_Dsp_adc_Not_Exist ){
				*pBatteryRes = Dsp_adc_NotExist;
				return;
			}
		}else{
			CP_LOGI("CheckBatteryLevel: Not PM813(S) or PM802 or PM803.\r\n");
			return; //PM803 TBD, no GPADC func
		}
		if(battery_level > BATTERY_LOW_THRESHOLD){
			if(battery_level > BATTERY_HIGH_VALUE){
				//case: battery voltage is more than "BATTERY_HIGH_VALUE"
				*pBatteryRes = BatteryLevelIsHigh;
			}else{
				//case: battery voltage is more than "low threshold"
				CP_LOGI("battery level: %d, more than low threshold: %d\r\n",battery_level,BATTERY_LOW_THRESHOLD);			
				*pBatteryRes   = BatteryLevelIsMedium;
			}
			
			break;
		}

		//check usb is detected ?
		IsChargerDectect = PM812_CHARGER_IS_DETECTED();
		if(IsChargerDectect == FALSE){
			CP_LOGI("battery level: %d, not more than low threshold: %d\r\n",battery_level,BATTERY_LOW_THRESHOLD);
			CP_LOGW("Charger disconnect\r\n");
			*pBatteryRes   = LowBatteryLevel_And_ChargerNoDetect;
			break;
		}else{
			if(PrintFlag == TRUE){
				CP_LOGI("battery level: %d, not more than low threshold: %d\r\n",battery_level,BATTERY_LOW_THRESHOLD);
				CP_LOGI("Charger Connect\r\n");
				CP_LOGI("In Chargering...\r\n");
				PrintFlag = FALSE;
			}
		}
	}

	return;
}



void Vibrator_And_LogoFeedback(void)
{
	BOOL ret_val;
	LogoBattery_t BatteryRes;
	UINT32 BrightnessLevel;
	
	/* check battery level */
	CP_LOGD("check battery level\r\n");
	CheckBatteryLevel(&BatteryRes);
	CP_LOGD("BatteryRes:%d\r\n",BatteryRes);
	switch(BatteryRes)
	{
	   case BatteryLevelIsHigh:
		   BrightnessLevel = Normal_BackLight_Brightness_Level;
		   break;

	   case BatteryLevelIsMedium:
		   BrightnessLevel = LowerBattery_BackLight_Brightness_Level;
		   break;
	   
	   case LowBatteryLevel_And_ChargerNoDetect:
		   /* If battery level is not more than low threshold and charger not dectect, return to boot33 */
		   CP_LOGI("battery level is less than low threshold and charger not detect, return to boot33\r\n");
		   break;

	  case GetBatteryLevelFail:
			CP_LOGW("get battery level fail\r\n");
	  		break;
	  
	  case Calbration_flag_Not_Set:
	   		CP_LOGW("PM803 Calbration flag not set\r\n");
			break;
	  
  	  case Dsp_adc_NotExist:
   			CP_LOGW("dsp_adc.bin not exist in ptable\r\n");
			break;
	   
	   default:
		   CP_LOGE("[Logo] BatteryRes: %d is error\r\n",BatteryRes);
		   while(1);
	}
	   
	ret_val = IsDisplayLogo(BatteryRes);
	if(ret_val == FALSE){
	   CP_LOGW("Not display logo\r\n");
	   return;
	}

	//enable vibrator feedback.
#ifdef 	ADD_VIBRATOR_IN_CODE
	NingboVibratorEnable();
	CP_LOGI("VIBRATOR ENABLE\r\n");
#endif

	CP_LOGD("LCD INIT\r\n");
	//If need,dispaly power-on logo
#ifdef LCD_USE_IN_CODE
	mci_Logo_LcdInit(0); 		

	POWER_ON_LCD_ShowImage();
	mci_LcdSetBrightness(5);	//Turn on the blacklight later to fix "Blurred Screen"

#ifdef TEST_OTA_DISPLAY_INFO
	test_lcd_display();
#endif

#endif
#if 0	//debug
	extern UINT32 getLR_reg(void);
	extern UINT32 getSP_reg(void);

	UINT32 rg = getLR_reg();
	UINT32 sp_rg = getSP_reg();
	UINT32 *p = (UINT32 *)sp_rg;

	CP_LOGI("logo display success: lr=0x%x, sp=0x%x\r\n",rg, sp_rg);
	for (i=0; i<32; i++)
		CP_LOGI("sp: 0x%x\r\n",*(p+i));

	//int delay=0x88888;
	//while(delay--);
#endif

	//If need,disable vibrator feedback
#ifdef 	ADD_VIBRATOR_IN_CODE
	NingboVibratorDisable();
	CP_LOGI("VIBRATOR DISABLE\r\n");
#endif


}


static CompressedType_e GetCompressedType(UINT32 magic)
{
	CompressedType_e type;
	
	switch(magic)
	{
		case LZOP_COMPRESSED_MAGIC:
			CP_LOGD("lzop cpmpressed image\r\n");
			type = LZOP;
			break;		
		case LZMA_COMPRESSED_MAGIC:
		case LZMA_COMPRESSED_MAGIC1:
		case LZMA_COMPRESSED_MAGIC2:
		case LZMA_COMPRESSED_MAGIC3:
			CP_LOGD("lzma cpmpressed image\r\n");
			type = LZMA;
			break;
		default:
			CP_LOGW("not a compressed image\r\n");
			type = NO_COMPRESSED;
			break;		
	}

	return type;
}


void DecompressRegion(rw_region_item *p_region_info)
{
	UINT32 CompressedMagic;
	CompressedType_e CompressedType;
	unsigned decompress_result,outLen,inLen;
	UINT32 UncompressSrcAddr;
	
#ifdef DEBUG_LZMA	
	UINT32 t_start,t_end;
#endif
	// dump region info ( debug use )
//	Dump_RW_REGION_ITEM( p_region_info );

	UncompressSrcAddr = p_region_info->RW_REGION_COMPRESSED_ADDR;

	
	/* get compressed magic */
	CompressedMagic = *(UINT32 *)UncompressSrcAddr;
	CompressedType = GetCompressedType(CompressedMagic);
	if(LZMA == CompressedType){
		
		//Prepare for lzma
		//if(g_LzmaOptimizeFlag == FALSE)
		//	LzmaOptimizeSwitch(TRUE);
#ifdef DEBUG_LZMA	
		t_start = GetTimer0CNT();
#endif
		decompress_result = LzmaUncompress((unsigned char *)p_region_info->RW_REGION_EXEC_ADDR,(size_t *)&outLen, 
			(const unsigned char *)UncompressSrcAddr, (size_t *)&p_region_info->RW_REGION_LENGTH);
			
	    CP_LOGD("[CP_RW] decompress from [0x%.08x] to [0x%.08x] outlen[%08x]\r\n",UncompressSrcAddr,(unsigned char *)p_region_info->RW_REGION_EXEC_ADDR,outLen);
	    
#ifdef DEBUG_LZMA	
		t_end = GetTimer0CNT();
		CP_LOGD("[CP_RW][Decopress][%8s] t_start:%ld, t_end:%ld, delta:%ld\r\n",p_region_info->RW_REGION_NAME,t_start,t_end,t_end - t_start);
#endif
	}else if(LZOP == CompressedType){
		decompress_result = lzop_decompress_safe((unsigned char*)p_region_info->RW_REGION_COMPRESSED_ADDR,
		(unsigned char*)p_region_info->RW_REGION_EXEC_ADDR,
		&outLen,&inLen);
	}else{
		memcpy((unsigned char*)p_region_info->RW_REGION_EXEC_ADDR,(unsigned char*)p_region_info->RW_REGION_COMPRESSED_ADDR, p_region_info->RW_REGION_LENGTH);
		decompress_result = 0;
	}
	
	if(decompress_result != 0){
		CP_LOGE("\r\n ** ERROR: RW DECOMPRESS ERROR RESULT=[%d]",decompress_result);
		while(1);
	}
}


void region_decompress_from_flash_to_psram(void)
{
    char region_compress_mark[8];
    unsigned schedule_count = 0;
    rw_region_item region_info;
    char * rw_cpz_struct_addr=(char *)get_rw_cpz_struct_addr();

    //load compressed region struct
    memcpy( region_compress_mark,
            rw_cpz_struct_addr,
            sizeof(region_compress_mark));

    if(!strncmp(region_compress_mark, RW_REGION_MARK_PRE_STRING ,strlen(RW_REGION_MARK_PRE_STRING)-1)){
        CP_LOGD("Region CPZ struct detected from LDT\r\n");
        while(1){
            //read NEXT region cpz info struct
            memcpy(&region_info
                    ,(void*)(rw_cpz_struct_addr + (sizeof(rw_region_item)*schedule_count++))
                    ,sizeof(rw_region_item));

            if(strncmp(region_info.RW_REGION_MARK, RW_REGION_MARK_PRE_STRING ,strlen(RW_REGION_MARK_PRE_STRING)-1))
            { 
                //expend endpoint of RW decompress working flow for compressed image
                CP_LOGD("stop decompress as no further %s detected\r\n",RW_REGION_MARK_PRE_STRING);
                break;
            }
			if(region_info.RW_REGION_COMPRESSED_ADDR == RW_REGION_COMPRESSED_ADDR_NONE ){
                //expend endpoint of RW decompress working flow for uncompressed image
                //
                //no RW_REGION_COMPRESSED_ADDR detected,
                //image did not compressed with /x/tavor/Arbel/build/external_region_compress.pl script
                //the decompress would be done by /hop/BSP/src/Cinit1.c
                CP_LOGD("skip region decompress as no RW_REGION_COMPRESSED_ADDR detected\r\n");
                break;
            }
			if(!strncmp(region_info.RW_REGION_NAME,"NON_OTA",7)){
				if(asr_property_get("mini.sys.enable")!=0){
					CP_LOGD("[CP ] mini system [%8s] no need decompress\r\n",region_info.RW_REGION_NAME);
					continue;
				}
			}
			
			DecompressRegion(&region_info);
        }
    }
    else
    {
        CP_LOGE("Region CPZ struct not detected from LDT\r\n");
    }
}

int copy_cp_from_flash_to_psram(void)
{
    //COPY CP.bin
    unsigned int tmp;
    CP_LOGD("[CP ] start copy CP bin from [0x%0.8x] to [0x%0.8x] ,size=[0x%0.8x]\r\n",
            FLASH_CP_START,
            CpExecAddress,
            CpCopySize);

    memcpy( (void *)CpExecAddress,
            (void *)FLASH_CP_START,
            CpCopySize);

    tmp = memcmp((void*)CpExecAddress,
                 (void*)FLASH_CP_START,
                 CpCopySize);

    CP_LOGD("[CP ] after memcopy , do memcmp result= %s\r\n",(tmp==0)?"SUCCEED":"FAILED");
    return tmp;
}



unsigned int get_ps_ncah_address(void)
{
	char region_compress_mark[8];
	unsigned schedule_count = 0;
	rw_region_item region_info;
	char * rw_cpz_struct_addr=(char*)get_rw_cpz_struct_addr();

	//load region compressed struct
	memcpy(region_compress_mark,
			rw_cpz_struct_addr,
			sizeof(region_compress_mark));

	if(!strncmp(region_compress_mark, RW_REGION_MARK_PRE_STRING ,strlen(RW_REGION_MARK_PRE_STRING)-1)){
		while(1){
			//read NEXT region cpz info struct
			memcpy(&region_info
					,(void*)(rw_cpz_struct_addr + (sizeof(rw_region_item)*schedule_count++))
					,sizeof(rw_region_item));


			//dump region info
			//uart_printf("[CP ] decompress [%8s] from [%.08x] to [%.08x]\r\n",
			//		region_info.RW_REGION_NAME,
			//		region_info.RW_REGION_COMPRESSED_ADDR,
			//		region_info.RW_REGION_EXEC_ADDR
			//		);
			
			if(!strncmp(region_info.RW_REGION_NAME,"PS_NCAH",7)){
				return region_info.RW_REGION_EXEC_ADDR;
			}

		}
	}
	else
	{
		uart_printf("[CP ] Region CPZ struct not detected from loadtable\r\n");
	}
	return 0;

}


typedef struct {
    uint32_t  address;
    uint32_t  len;
	uint32_t  reserved[6];
} dsp_backup_info;



int copy_dsp_from_flash_to_psram(unsigned int * inLen)
{
	unsigned long magic;
    //COPY DSP.bin
    int ret = -1;
	CompressedType_e CompressedType;
    int outLen = -1;
	dsp_backup_info dspbk={0};

	magic = *(volatile unsigned long*)FLASH_DSP_START;

	CompressedType = GetCompressedType(magic);

	if (LZMA == CompressedType) //"a lzop image"
	{
	    CP_LOGD("LZMA compressed DSP image.\r\n");

        if((DspLoadSize < (LOGO_IMG_END_ADDR-PSRAM_CONFIG_CACHE_START_ADDR)) && (CHIP_IS_CRANEL || CHIP_IS_CRANELS)){

            CP_LOGD("[LzmaUncompress] src: [0x%.08x], srclen: [0x%.08x],dst: [0x%.08x], dstlen: [0x%.08x]",
                FLASH_DSP_START,*inLen, DspBackupAddress ,outLen);
            if(!LzmaUncompress((unsigned char *)DspBackupAddress,(size_t *)&outLen, (const unsigned char *)FLASH_DSP_START, (size_t *)inLen))
            {
                CP_LOGD("[DSP] released dsp outLen [0x%.08x] inLen[0x%.08x] DSPBACKUP_SIZE[%08x]\r\n", outLen,*inLen,DSPBACKUP_SIZE);
            
                if(outLen != DSP_LOAD_SIZE){
                    CP_LOGD("[DSP] WARNING: decompressed length [0x%.08x] unmatch with expected [0x%.08x].\r\n",outLen,DSP_LOAD_SIZE);
                }
            
                ret = 0;
            }else{
                CP_LOGE("\r\n ** ERROR: DSP image decompress failed .\r\n");
                while(1);
            }

        }else{
        	CP_LOGD("[LzmaUncompress] src: [0x%.08x], srclen: [0x%.08x],dst: [0x%.08x], dstlen: [0x%.08x]",
			    FLASH_DSP_START,*inLen,(unsigned char *)PSRAM_DSP_START+DSP_LOAD_SIZE,outLen);
            if(!LzmaUncompress((unsigned char *)PSRAM_DSP_START+DSP_LOAD_SIZE,(size_t *)&outLen, (const unsigned char *)FLASH_DSP_START, (size_t *)inLen))
            {
                CP_LOGD("[DSP] released dsp outLen [0x%.08x] inLen[0x%.08x]\r\n", outLen,*inLen);
            
                
                if(outLen > DSPBACKUP_SIZE){
                    memcpy((unsigned char *)PSRAM_DSP_START+DSPBACKUP_SIZE,(unsigned char *)PSRAM_DSP_START+DSP_LOAD_SIZE+DSPBACKUP_SIZE,DSP_LOAD_SIZE-DSPBACKUP_SIZE);
                    CP_LOGD("[DSP] copy dsp to address[0x%.08x] len[0x%.08x]\r\n", (unsigned char *)PSRAM_DSP_START+DSPBACKUP_SIZE,DSP_LOAD_SIZE-DSPBACKUP_SIZE);
                }else if(CHIP_IS_CRANEL || CHIP_IS_CRANELS){
                    DspRamSize = outLen;//this is 1606S 8M psram and all in dsp image
                }
            
                memcpy((unsigned char *)DspBackupAddress,(unsigned char *)PSRAM_DSP_START+DSP_LOAD_SIZE,DSPBACKUP_SIZE);
                CP_LOGD("[DSP] backup dsp size[%08x] to address[0x%.08x]\r\n",DSPBACKUP_SIZE, (unsigned char *)DspBackupAddress);
            
                if(outLen != DSP_LOAD_SIZE){
                    CP_LOGD("[DSP] WARNING: decompressed length [0x%.08x] unmatch with expected [0x%.08x].\r\n",outLen,DSP_LOAD_SIZE);
                }
            
                //dspbk.address = DspBackupAddress;
                //dspbk.len = DSPBACKUP_SIZE;
                //asr_property_set_param("dsp.backup.info",(const char *)&dspbk,sizeof(dsp_backup_info));
                ret = 0;
            }else{
                CP_LOGE("\r\n ** ERROR: DSP image decompress failed .\r\n");
                while(1);
            }

        }
			

		cpy_dsp_by_cp=0;
	}else{
		cpy_dsp_by_cp=1;
		ret = 0;	
    }
    return ret;
}

#ifdef UNCOMPRESS_DEBUG
UINT32 CalcImageChecksum( UINT32* DownloadArea, UINT32 ImageLength,UINT32 checksum)
{
	//uart_printf("CalcImageChecksum DownloadArea[%08x],ImageLength[%08x]\r\n",DownloadArea,ImageLength);
    UINT32 ImageChecksum = checksum;
    UINT32* ptr32 = DownloadArea;
    UINT32* pEnd = ptr32 + (ImageLength / sizeof(UINT32));
    UINT32 BytesSummed = 0;


    while ( ptr32 < pEnd )
    {
        // checksum format version 2 algorithm as defined by flasher
        ImageChecksum ^= (*ptr32);
        ptr32++;
        BytesSummed += sizeof(UINT32);
    }
    return ImageChecksum;
}
#endif
#define CRANEL_4MRAM_RF_ADDRESS 0x7E065000
#define CRANEL_MULTI_RF_ADDRESS 0x7E045000
#define CRANEL_SINGLE_RF_SIZE   0x00005000//20K


int copy_rf_from_flash_to_psram(unsigned int rfFlashAddress,unsigned int rfCopySize,unsigned int rfLoadAddress)
{
	
	//COPY rf.bin
	unsigned long magic;
	CompressedType_e CompressedType;
	unsigned uncompress_size=0;
	char *uncompress_buf=NULL;
	int outLen = -1;
	int dsp_non_bk_addr = -1;

	unsigned int checksum = 0;

	if(rfLoadAddress == INVALID_ADDRESS)
	{
		CP_LOGE("[RF ] rfLoadAddress = INVALID_ADDRESS\r\n");
		while(1);
	}

	CP_LOGD("[RF ] copy from [0x%0.8x] to [0x%0.8x] size[0x%0.8x]\r\n",
			rfFlashAddress,
			rfLoadAddress,
			rfCopySize);
	
	magic = *(volatile unsigned long*)rfFlashAddress;

	CompressedType = GetCompressedType(magic);
	if (LZMA == CompressedType){
		uncompress_size=get_LzmaUncompress_dst_size((const unsigned char *)rfFlashAddress);
		uncompress_buf = (char *)malloc(uncompress_size);
		if(uncompress_buf==NULL){
			CP_LOGE("\r\n ** ERROR: copy RF image failed ,malloc failed[%08x]\r\n",uncompress_size);
			while(1);

		}
		
		if(!LzmaUncompress((unsigned char *)uncompress_buf,(size_t *)&outLen, (const unsigned char *)rfFlashAddress, (size_t *)&rfCopySize))
		{
			CP_LOGD("[RF] Uncompress rf outLen [0x%.08x] inLen[0x%.08x]\r\n", outLen,rfCopySize);
			if(uncompress_size!=outLen){
				CP_LOGD("[RF] WARNING: decompressed length [0x%.08x] unmatch with expected [0x%.08x].\r\n",outLen,uncompress_size);
			}
            CP_LOGD("[RF] DspBackupAddress [0x%.08x]\r\n", DspBackupAddress);
            if((DspLoadSize < (LOGO_IMG_END_ADDR-PSRAM_CONFIG_CACHE_START_ADDR)) && (CHIP_IS_CRANEL || CHIP_IS_CRANELS)){
                if(outLen>CRANEL_SINGLE_RF_SIZE){
                    rfLoadAddress = CRANEL_MULTI_RF_ADDRESS;
                }
                
    			memcpy(DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START),uncompress_buf,outLen);
    			CP_LOGD("[RF] copy rf to [0x%.08x] outLen[0x%.08x]\r\n",DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START), outLen);

            }else if((CHIP_IS_CRANEL || CHIP_IS_CRANELS) && DspRamSize!=0){//this is 1606S 8M psram and all in dsp image
                memcpy(DspBackupAddress+(CRANEL_4MRAM_RF_ADDRESS - PSRAM_DSP_START),uncompress_buf,outLen);
                CP_LOGD("[RF] copy rf to [0x%.08x] outLen[0x%.08x]\r\n",DspBackupAddress+(CRANEL_4MRAM_RF_ADDRESS-PSRAM_DSP_START), outLen);

            }
            else{
                dsp_non_bk_addr = (PSRAM_DSP_START + DSPBACKUP_SIZE);
                if(rfLoadAddress>=dsp_non_bk_addr){
                    memcpy(rfLoadAddress,uncompress_buf,outLen);
                }else{
                    if((rfLoadAddress + outLen)>dsp_non_bk_addr){
                        memcpy(dsp_non_bk_addr,uncompress_buf + (dsp_non_bk_addr - rfLoadAddress),(rfLoadAddress + outLen) - dsp_non_bk_addr);
                        memcpy(DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START),uncompress_buf,(dsp_non_bk_addr - rfLoadAddress));
                        
                    }else{
                        memcpy(DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START),uncompress_buf,outLen);
                    }
                }

            }


			free(uncompress_buf);
			#ifdef UNCOMPRESS_DEBUG
			checksum=CalcImageChecksum(DspBackupAddress,DSPBACKUP_SIZE,0);
			checksum=CalcImageChecksum(dsp_non_bk_addr,DSP_LOAD_SIZE-DSPBACKUP_SIZE,checksum);
			CP_LOGD("[RF ]copy rf SUCCEED,checksum[%08x]\r\n",checksum);
			#else
			CP_LOGD("[RF ]copy rf SUCCEED\r\n");
			#endif
			
			
			
			return 0;
		}else{
			CP_LOGE("\r\n ** ERROR: RF image decompress failed .\r\n");
			while(1);
		}

	}

	
	if(cpy_dsp_by_cp){
		CP_LOGD("[RF ] copy rf by CP\r\n");
		return 0;
	}

	{
        if((DspLoadSize < (LOGO_IMG_END_ADDR-PSRAM_CONFIG_CACHE_START_ADDR)) && (CHIP_IS_CRANEL || CHIP_IS_CRANELS)){
			memcpy(DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START),rfFlashAddress,rfCopySize);
			CP_LOGD("[RF] copy rf to [0x%.08x] rfCopySize[0x%.08x]\r\n",DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START), rfCopySize);

        }else if((CHIP_IS_CRANEL || CHIP_IS_CRANELS) && DspRamSize != 0){//this is 1606S 8M psram and all in dsp image
                memcpy(DspBackupAddress+(CRANEL_4MRAM_RF_ADDRESS - PSRAM_DSP_START),uncompress_buf,outLen);
                CP_LOGD("[RF] copy rf to [0x%.08x] outLen[0x%.08x]\r\n",DspBackupAddress+(CRANEL_4MRAM_RF_ADDRESS-PSRAM_DSP_START), outLen);

        }else{
            dsp_non_bk_addr = (PSRAM_DSP_START + DSPBACKUP_SIZE);
            if(rfLoadAddress>=dsp_non_bk_addr){
                memcpy(rfLoadAddress,rfFlashAddress,rfCopySize);
            }else{
                if((rfLoadAddress + rfCopySize)>dsp_non_bk_addr){
                    memcpy(dsp_non_bk_addr,rfFlashAddress + (dsp_non_bk_addr - rfLoadAddress),(rfLoadAddress + rfCopySize) - dsp_non_bk_addr);
                    memcpy(DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START),rfFlashAddress,(dsp_non_bk_addr - rfLoadAddress));
                    
                }else{
                    memcpy(DspBackupAddress+(rfLoadAddress-PSRAM_DSP_START),rfFlashAddress,rfCopySize);
                }
            }

        }


		#ifdef UNCOMPRESS_DEBUG
		checksum=CalcImageChecksum(DspBackupAddress,DSPBACKUP_SIZE,0);
		checksum=CalcImageChecksum(dsp_non_bk_addr,DSP_LOAD_SIZE-DSPBACKUP_SIZE,checksum);
		CP_LOGD("[RF ]copy rf SUCCEED,checksum[%08x]\r\n",checksum);
		#else
		CP_LOGD("[RF ]copy rf SUCCEED\r\n");
		#endif

		/*
		memcpy( (void *)rfLoadAddress,
				(void *)rfFlashAddress,
				rfCopySize);
		tmp = memcmp((void*)rfLoadAddress,
					 (void*)rfFlashAddress,
					 rfCopySize);

		uart_printf("[RF ]memcmp result %s\r\n",(tmp==0)?"SUCCEED":"FAILED");
		*/
	}

	
	return 0;
}



void PrepareToExecuteCp(void)
{
	CP_LOGD("Decompress dsp and rw data start!\r\n");
	unsigned int val;
	_ptentry *cpEntry, *dspEntry,*rfEntry;

	
#ifdef DEBUG_LZMA	
	Timer0_enable(TRUE);
#endif

	/* [NOTE] move these init code into logo as pm803 use */
	/* printf the Chip ID */
	CP_LOGD("chip id: 0x%.08x\r\n",GetLongChipID());

	CpCoreFreqChangeTo624();
	if(ifPsramLimit200M()){
        PsramPhyFreqChangeTo350();
	}else{
        PsramPhyFreqChangeTo416();
	}

	dspEntry = ptable_find_entry("dsp");
	rfEntry = ptable_find_entry("rfbin");
	DspFlashAddress = dspEntry->vstart;
	DspImageSize = dspEntry->vsize;
	
	DspExecAddress = get_dsp_load_addr();
	DspLoadSize = get_dsp_copy_size();
	
	
		
	if(copy_dsp_from_flash_to_psram((unsigned int *)&DspImageSize)){
		goto FAIL;
	}

	rfLoadAddress = get_rf_load_addr();
	rfFlashAddress = rfEntry->vstart;
	rfCopySize = rfEntry->vsize;
	if(copy_rf_from_flash_to_psram(rfFlashAddress,rfCopySize,rfLoadAddress)){
		goto FAIL;
	}

    CpExecAddress = get_cp_exec_addr(); //CP Exec Addr
    CpCopySize = get_cp_copy_size();

    //DEALING WITH CP.bin
    if (get_cp_execute_mode() == XIP){
        //execute mode = XIP && RW_COMPRESSED detected
        region_decompress_from_flash_to_psram();
    }else{
        //execute mode = PSRAM
        if(copy_cp_from_flash_to_psram()){
            goto FAIL;
        }
    }

	disable_cache(0x7E000000,0x01000000);//16M
	CpCoreFreqChangeTo416();

#ifdef DEBUG_LZMA	
	Timer0_enable(FALSE);
#endif	
	CP_LOGD("Decompress dsp and load rf data END!\r\n");
	return;
FAIL:
	CP_LOGE("PrepareToExecuteCp failed!\r\n");
	while(1);

}

static char heap_a[LOGO_HEAP_SIZE];

void init_heap(void)
{
	unsigned int val;
    unsigned long DspBackupEndAddress;

    DspLoadSize = get_dsp_copy_size();

	val=get_ddr_heap_size();
	
	DspBackupSize = get_dsp_backup_size();

	if(val==0 || DspBackupSize==0){
		CP_LOGE("loadtable not latest,heap size[%08x] dsp_bk size[%08x]!\r\n",val,DspBackupSize);
		goto FAIL;

	}


	if(DspBackupSize>val){
		CP_LOGE("dsp bk size too larger[%08x], heap size[%08x]!\r\n",DspBackupSize,val);
		goto FAIL;

	}

	CP_LOGD("logo LOGO_IMG_END_ADDR[%08x]!\r\n",LOGO_IMG_END_ADDR);

	DspBackupAddress = get_dsp_backup_addr();
	if(DspBackupAddress==0){
		CP_LOGE("loadtable not latest, DspBackupAddress error!!!\r\n");
		goto FAIL;

	}


	if((DspLoadSize < (LOGO_IMG_END_ADDR-PSRAM_CONFIG_CACHE_START_ADDR)) && (CHIP_IS_CRANEL || CHIP_IS_CRANELS)){
	    DspBackupEndAddress = get_dsp_backup_end_addr();
        if(DspBackupAddress>=DspBackupEndAddress){
            CP_LOGE("error!!!DspBackupAddress[%08x] DspBackupEndAddress[%08x]\r\n",DspBackupAddress,DspBackupEndAddress);
            goto FAIL;
        }
        if((DspBackupEndAddress-DspBackupAddress + 4 )<(DspBackupSize)){
            CP_LOGE("error!!!DspBackupAddress[%08x] DspBackupEndAddress[%08x] DspBackupSize[%08x]\r\n",DspBackupAddress,DspBackupEndAddress,DspBackupSize);
            goto FAIL;
        }
	}else{
        if((LOGO_IMG_END_ADDR-PSRAM_CONFIG_CACHE_START_ADDR)>DspBackupSize){
            CP_LOGE("logo image too larger!\r\n");
            goto FAIL;
        }
	}
	
	
	enable_cache(0x7E000000,0x01000000);//16M

	
	/* support malloc mechanism */
	malloc_init(heap_a, LOGO_HEAP_SIZE);
	CP_LOGD("init_heap done!\r\n");
	return;

FAIL:
	CP_LOGE("init_heap failed!\r\n");
	while(1);

}


void logo(void)
{
    unsigned int val;
    volatile UINT8 var;
	unsigned int i;
	UINT32 battery_level;
	
    //[1] uart init
	cp_uart_init();
    CP_LOGD("LOGO START\r\n");
	usticaInit();


	/*[2] Print LOGO HEADER INFO */
	CP_LOGW("[LOGO] VERSION_DATE   :[%s]\r\n",(logo_vb.vb.logo_version_block.version_date));
	CP_LOGD("LCD_TYPE:[%s]\r\n",logo_vb.vb.logo_version_block.oem_lcd_type);
	CP_LOGD("ver:%s\r\n",logo_vb.vb.logo_version_block.build_info_string);
	CP_LOGD("LOGO_FLAG:%s\r\n",logo_vb.FeedbackBlock.feedback_block.LogoDisplayLogoFlag);


	asr_property_set("logo.version",logo_vb.vb.logo_version_block.build_info_string);
	asr_property_set("logo.lcd_type",logo_vb.vb.logo_version_block.oem_lcd_type);
	asr_property_set("logo.displayflag",logo_vb.FeedbackBlock.feedback_block.LogoDisplayLogoFlag);

	asr_property_dump();

	
    //[3] Enable MPU and using the cortex-r default memory map as the background region
    val = sctlr_get();
    val |= SCTLR_BR | SCTLR_M;
    sctlr_set(val);

	//[4] MPU config
    CP_LOGD("logo MPU config\r\n");
    set_mpu_reg_0();
    set_mpu_reg_3();
    set_mpu_reg_4();
    set_mpu_for_DSP();
//	mpu_value_check();
//    PMIC_Init();
//	sdcard_init();

#ifdef DEBUG_FLOAT_OPERATOR
	/* Note :can execute float operation but can not printf float type data with %f */
	CP_LOGD("test_float_data!\r\n");
	test_float_data();
	test_fitting_calculate();
#endif

#ifdef SUPPORT_MALLOC
   /* support malloc mechanism */
   malloc_init(g_MallocBuffer, MALLOC_BUFFER_SIZE);
   CP_LOGD("malloc init done!\r\n");
#endif

   /* [5] ptable init and dump */
   if(ptable_init()){
	   CP_LOGE("bad partition table!\r\n");
	   while(1);
   }
//  ptable_dump();

   /* 6] KEY PARTITION INFO FETCH */
   GetKeyPartitionInfoFetch();

   /* [7] flash and loadtable init */
   bbu_qspi_init();
   loadtable_init(CpFlashAddress);
   init_heap();
   logo_header_table_init();
   //dump_loadtable();


#ifdef MRD_DEBUG_CODE
	test_MRD_api();
#endif /* MRD_DEBUG_CODE */

#ifdef PM803_DEBUG_CODE
	if( PMIC_IS_PM803() ){
		LogoBattery_t BatteryRes;

		CP_LOGD("check battery level\r\n");
		CheckBatteryLevel( &BatteryRes );
		CP_LOGD("BatteryRes:%d\r\n",BatteryRes);
	}
#endif

#ifdef DMCHAGE
    DM_CHARGE_Manager();
#endif

#ifdef LCD_USE_IN_CODE
	/* check LOGO FLAG in logo.bin */
	if(0 == strncmp(logo_vb.FeedbackBlock.feedback_block.LogoDisplayLogoFlag,ENABLE_LOGO_DISPLAY_FLAG,sizeof(ENABLE_LOGO_DISPLAY_FLAG))){
		/* [8] Display logo and enable vibrator */
		Vibrator_And_LogoFeedback();
	}else{
		CP_LOGD("Read \"LOGO_FLAG\" from image is %s, not display logo\r\n",logo_vb.FeedbackBlock.feedback_block.LogoDisplayLogoFlag);
	}
#endif


	/* [9] do some prepare work before executing cp.bin (e.g. decompress dsp, load rf. decompress cp_rw if necessary) */
	PrepareToExecuteCp();

	/* [10] return to boot33 */
	//if(g_LzmaOptimizeFlag == TRUE){
	//	LzmaOptimizeSwitch(FALSE);
	//}
	CP_LOGD("LOGO END\r\n");

	return;							
	//while(1);
}

