#ifndef INTELLIGENT_DEYUFEN_H
#define INTELLIGENT_DEYUFEN_H

#ifdef __cplusplus
extern "C" {
#endif

#include <stdio.h>
#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lvgl.h"
#include "lv_watch_conf.h"
#else
#include "../../../lvgl/lvgl.h"
#include "../../../lv_watch_conf.h"
#endif

#include <stdlib.h>
#include <time.h>

#if USE_LV_WATCH_INTELLIGENT_DEYUFEN !=0
void smart_deyu_create_btn_action(lv_obj_t * btn, lv_event_t e);
	
#endif
#ifdef __cplusplus
} /* extern "C" */
#endif

#endif    
