#ifndef CRANEL_FP_8MRAM

/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2006, 2007 Marvell DSPC Ltd. All Rights Reserved.
-------------------------------------------------------------------------------------------------------------------*/

/************************************************************************/
/*  COPYRIGHT (C) 2002 Intel Corporation.                               */
/*                                                                      */
/*  This file and the software in it is furnished under                 */
/*  license and may only be used or copied in accordance with the terms */
/*  of the license. The information in this file is furnished for       */
/*  informational use only, is subject to change without notice, and    */
/*  should not be construed as a commitment by Intel Corporation.       */
/*  Intel Corporation assumes no responsibility or liability for any    */
/*  errors or inaccuracies that may appear in this document or any      */
/*  software that may be provided in association with this document.    */
/*  Except as permitted by such license, no part of this document may   */
/*  be reproduced, stored in a retrieval system, or transmitted in any  */
/*  form or by any means without the express written consent of Intel   */
/*  Corporation.                                                        */
/*                                                                      */
/* Title: Temporary file that is adaptation layer to Generic File System API */
/*                                                                      */
/* Filename: FDI2FS_API.c                                            */
/*                                                                      */
/* Author:   Miriam Yovel                                               */
/*                                                                      */
/* Project, Target, subsystem: Tavor, Tavor ,software_util    	     	*/
/*																		*/
/* Remarks: -                                                           */
/*    													                */
/* Created: 11/4/2005                                                   */
/*                                                                      */
/* Modified:                                                            */
/************************************************************************/
#ifdef NVM_INCLUDE

/*----------- External include files -----------------------------------------*/
#include <stdio.h> // needed for the 'memclr' and 'memset' functions.
#include "global_types.h"
#include "diags.h"
#include "diag_API.h"

/*----------- Local include files --------------------------------------------*/

#include "bsp.h"  /* */
#include "nvmClient.h"  /* */
#include "nvmClient_def.h"  /* */
#include "nvmClient_utils.h"  /* */
#include "FDI2FS_API.h"  /* */
#include "FDI_EXT.h"
#include "FDI_MUTX.h"
#include "FDI_FILE.h"
#include "GenericFS_API.h"

extern GenericFS_ReturnCodeE   GenericFileSysFileOpen_test(const char *fileName,const char *attMode,GENERIC_FS_FILE_ID *fileID);
extern GenericFS_ReturnCodeE   GenericFileSysFileClose_test(GENERIC_FS_FILE_ID fileID);
extern GenericFS_ReturnCodeE   GenericFileSysFileERead_test(void *file ,UINT32 elementSize ,UINT32 element2Read ,UINT32 *actualRead, GENERIC_FS_FILE_ID fileID);
extern GenericFS_ReturnCodeE   GenericFileSysFileWrite_test(void *file ,UINT32 elementSize ,UINT32 element2Write ,UINT32 *actualWrite, GENERIC_FS_FILE_ID fileID);
extern GenericFS_ReturnCodeE   GenericFileSysFileSeek_test(UINT32 offset ,UINT32 whereFrom , GENERIC_FS_FILE_ID fileID);

//#include "fdi_err.h"  /* */

/************************************************************************/
/*                      Global Parameters                           */
/************************************************************************/
static ERR_CODE  FDI2FSErrorCodeSvr;
static UINT32    FDI2FSFileIDSvr[MAX_FS_FILE_ID];
static UINT32    FS_lastBusyIdxSvr;


static ERR_CODE  FDI2FSErrorCodeTest;
static UINT32    FDI2FSFileIDTest[MAX_FS_FILE_ID];
static UINT32    FS_lastBusyIdxTest;



//rotate this index for every new file!
/****************************************************************************
                        IMPORTANT NOTES

Some file-systems (LINUX for example) return fHandler-Pointer on fopen()
If one file has been closed and another opened the handler would be the same.
We translate here  "fHandler-Pointer <-> f-Index" (GFSfID <-> FDIfID)
Let's rotate this index for every New fopen even the fHandler is the same.
******************************************************************************/

/*----------- Local function declarations ------------------------------------*/
#define FDI2FS_API_TRANSLATE_ERR(FSerr ,FDIerr)  {               \
      switch (FSerr)                                               \
      {                                                              \
        case GENERIC_FS_FILE_READ_ERROR:  FDIerr = ERR_READ;    break; \
        case GENERIC_FS_FILE_EOF:         FDIerr = ERR_EOF;     break; \
        case GENERIC_FS_MEMORY_ERROR:     FDIerr = ERR_MALLOC;  break; \
        case GENERIC_FS_COMM_ERROR:       FDIerr = ERR_SYSTEM;  break; \
        case GENERIC_FS_INVALID_FILE_ID:  FDIerr = ERR_NOTOPEN; break; \
        case GENERIC_FS_ERROR:            FDIerr = ERR_SYSTEM;  break; \
        case GENERIC_FS_FILE_WRITE_ERROR: FDIerr = ERR_WRITE;   break; \
        default:                          FDIerr = ERR_SYSTEM;  break; \
      }                                                                \
                                                  }   /*endM*/

/******************************************************************************
FILE_ID FDI_fopen(const char *, const char *);
* Function     :
*******************************************************************************
******************************************************************************/
FILE_ID FDI_fopen_2chip(const char *filename_ptr, const char *mode)
{
	UINT32                  fileID = 0;
	GenericFS_ReturnCodeE   status;
	GENERIC_FS_FILE_ID      genericFSFileID;

	status = GenericFileSysFileOpen(filename_ptr,mode,&genericFSFileID);
	if ( status != GENERIC_FS_OK)
	{

		FDI2FS_API_TRANSLATE_ERR(status ,FDI2FSErrorCodeSvr);
	}
	else
	{
		UINT32 maxIdx;
		UINT32 cpsr = disableInterrupts(); //=========MUTEX
		fileID = FS_lastBusyIdxSvr;
		maxIdx = 1;

		do
		{

			fileID++;
			if(fileID == MAX_FS_FILE_ID) fileID = 1; //Wrap to 1 but not to ZERO
			/*the assertion won't be turned off, so the side effect won't happens*/
			/*coverity[assert_side_effect]*/
			ASSERT(maxIdx++ < MAX_FS_FILE_ID);
		}
		while(FDI2FSFileIDSvr[fileID] != 0);

		FDI2FSFileIDSvr[fileID] = genericFSFileID;
		FS_lastBusyIdxSvr = fileID;
		restoreInterrupts(cpsr); //===================MUTEX
	}

	return((FILE_ID)fileID);
}




/******************************************************************************
int FDI_fclose(FILE_ID stream)
* Function     :
*******************************************************************************/
int FDI_fclose_2chip(FILE_ID stream)
{
  GenericFS_ReturnCodeE   fclose_st;
  GENERIC_FS_FILE_ID      genericFSFileID;

  ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
  genericFSFileID = FDI2FSFileIDSvr[stream];
  ASSERT(genericFSFileID!=NULL);

  fclose_st = GenericFileSysFileClose(genericFSFileID);

  //Nobody check the return status of the fclose()
  //The FDI2FSFileID[] stays dirty. Double fclose on the LINUX causes crash
  //So if ERROR, make assert here !!!
  ASSERT(fclose_st == GENERIC_FS_OK);  //genericFSError = EOF;

  FDI2FSFileIDSvr[stream] = 0;
  FDI2FSErrorCodeSvr = ERR_NONE;

  return 0; //genericFSError;
}


/******************************************************************************
* Function     :
size_t
FDI_fread(void *buffer_ptr, size_t element_size, size_t count, FILE_ID stream)
*******************************************************************************/
size_t FDI_fread_2chip(void *buffer_ptr, size_t element_size, size_t count, FILE_ID stream)
{
  UINT32                  noOfReadBytes;
  GenericFS_ReturnCodeE   status;
  GENERIC_FS_FILE_ID      genericFSFileID;

  ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
  genericFSFileID = FDI2FSFileIDSvr[stream];
  ASSERT(genericFSFileID!=NULL);

  status = GenericFileSysFileERead(buffer_ptr ,(UINT32) element_size ,(UINT32) count, &noOfReadBytes, genericFSFileID);

#if defined(NVM_DEBUG_ENABLE)
  DIAG_FILTER(NVM_remote,FDI2FS,FDI_read_debug,DIAG_INFORMATION)
  diagPrintf("fileID %d;number of element to read %d,actual %d;",stream,(UINT32)count,noOfReadBytes);
#endif

  if((status == GENERIC_FS_OK)||(status==GENERIC_FS_FILE_EOF))
  {
	FDI2FSErrorCodeSvr = ERR_NONE;
	return((size_t)noOfReadBytes);
  }
  else
  {
	FDI2FS_API_TRANSLATE_ERR(status ,FDI2FSErrorCodeSvr);
	return(0);
  }
}




/******************************************************************************
Function
size_t  FDI_fwrite(const void *buffer_ptr,
           size_t element_size,
           size_t count,
           FILE_ID stream)

*******************************************************************************/
size_t FDI_fwrite_2chip(const void *buffer_ptr,size_t element_size,size_t count,FILE_ID stream)
{
	UINT32                  noOfWriteBytes;
	GenericFS_ReturnCodeE   status;
	GENERIC_FS_FILE_ID      genericFSFileID;

	ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
	genericFSFileID = FDI2FSFileIDSvr[stream];
	ASSERT(genericFSFileID!=NULL);

	status = GenericFileSysFileWrite((void*)buffer_ptr ,(UINT32) element_size ,(UINT32) count ,&noOfWriteBytes, genericFSFileID);
	if(status == GENERIC_FS_OK )
	{
		FDI2FSErrorCodeSvr = ERR_NONE;
		return((size_t)noOfWriteBytes);
	}
	else
	{
		FDI2FS_API_TRANSLATE_ERR(status ,FDI2FSErrorCodeSvr);
		return(0);
	}
}




/******************************************************************************

* Function     :   FDI_fseek
*******************************************************************************/
int FDI_fseek_2chip(FILE_ID stream, long offset, int wherefrom)
{
	GenericFS_ReturnCodeE   status;
	int                     genericFSError = 0;
	GENERIC_FS_FILE_ID      genericFSFileID;

	ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
	genericFSFileID = FDI2FSFileIDSvr[stream];
	ASSERT(genericFSFileID!=NULL);

	status = GenericFileSysFileSeek((UINT32) offset ,(UINT32)wherefrom , genericFSFileID);

	if ( status != GENERIC_FS_OK)
		genericFSError = EOF;
	return genericFSError;
}


unsigned int FDI_fsize_2chip(FILE_ID stream)
{
	GenericFS_ReturnCodeE   status;
	int                     genericFSError = 0;
	GENERIC_FS_FILE_ID      genericFSFileID;

	ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
	genericFSFileID = FDI2FSFileIDSvr[stream];
	ASSERT(genericFSFileID!=NULL);

	return GenericFileSysFileSize(genericFSFileID);


}




/******************************************************************************
* Function     : int FDI_remove (const char *filename_ptr )
*******************************************************************************/
int FDI_remove_2chip(const char *filename_ptr)
{
	GenericFS_ReturnCodeE   status;
	int                     genericFSError = 0;

	status = GenericFileSysFileRemove(filename_ptr);
	if ( status != GENERIC_FS_OK)
	{
		genericFSError = EOF;
		FDI2FS_API_TRANSLATE_ERR(status ,FDI2FSErrorCodeSvr);
	}
	else
		FDI2FSErrorCodeSvr = ERR_NONE;
	return genericFSError;
}


/******************************************************************************
* Function     : int FDI_findfirst(const char *filename_ptr, FILE_INFO *fileinfo_ptr)
*******************************************************************************/
int FDI_findfirst_2chip(const char *filename_ptr, FILE_INFO *fileinfo_ptr)
{
  #define FOUND       0
  #define NOT_FOUND   EOF
  GenericFS_ReturnCodeE   status;
  int                     result = NOT_FOUND;
  GenericFS_FileInfo      *fileInfo = (GenericFS_FileInfo *)fileinfo_ptr;

  if ((initState<=BSP_INIT_PHASE2_BASIC_PASSED)&&(*filename_ptr=='*'))
	  return GENERIC_FS_ERROR;

  ASSERT(sizeof(fileinfo_ptr->data_id) == 2); /* elevy - make sure we are in FDI5 alike*/

  status = GenericFileSysFileFindFirst(filename_ptr , fileInfo);
  if ( status == GENERIC_FS_OK)
  {
    // Different kind of NVM-servers could treat the "findfirst" differently
    // Lets make strict checking for the information returned by server!
    UINT32 charP;
    UINT32 fileNameLen = strlen(fileinfo_ptr->file_name)+1; //with NULL
    //Check the returned fileName is null-terminated
    if( (fileNameLen > sizeof(fileinfo_ptr->file_name)) || (fileNameLen==1) )
    {
        DIAG_FILTER(NVM_remote,findfirst,LenERR,DIAG_INFORMATION)
        diagPrintf("NVM_remote: wrong file-name lenght obtained from NVM-server");
        ASSERT(fileNameLen !=0xBAD);
    }
    else
    { // Compare returned and requested fileNameS
      // Some servers are CaseSensitive, but some aren't. So do not use caseSensitive strcmp()
        for(charP=0; charP<fileNameLen; charP++)
        {
            #define DCHAR_P    ((UINT8)('a' - 'A'))
            #define DCHAR_M    ((UINT8)('A' - 'a'))
            UINT8 dChar;

            if( (filename_ptr[charP] == '*') || (filename_ptr[charP] == '?') )
            {
                charP = 0;
                break;
            }

            dChar = (UINT8)filename_ptr[charP] - (UINT8)fileinfo_ptr->file_name[charP];

            if((dChar != 0) && (dChar != DCHAR_P) && (dChar != DCHAR_M))
            {
                charP = 0xBAD;
                break;
            }
        }
        if(charP == 0xBAD)
        {
            DIAG_FILTER(NVM_remote,findfirst,NameBAD,DIAG_INFORMATION)
            diagPrintf("NVM_remote: findfirst: requested <%s>, obtained <%s>", filename_ptr, fileinfo_ptr->file_name);
            ASSERT(charP!=0xBAD);
        }
        else
        {
            result = FOUND;
        }
    }
  }
  return result;
}

/******************************************************************************
* Function     : int FDI_findnext( FILE_INFO *fileinfo_ptr)
*******************************************************************************/
int FDI_findnext_2chip( FILE_INFO *fileinfo_ptr)
{

	GenericFS_ReturnCodeE   status;
	int                     genericFSError = 0;
	GenericFS_FileInfo      *fileInfo = (GenericFS_FileInfo *) fileinfo_ptr;

	status = GenericFileSysFileFindNext(fileInfo);

	if ( status != GENERIC_FS_OK)
		genericFSError = EOF;

	return genericFSError;
}
/******************************************************************************
Function : FDI_rename(const FDI_TCHAR *oldname_ptr, const FDI_TCHAR *newname_ptr)
*******************************************************************************/
int FDI_rename_2chip(const FDI_TCHAR *oldname_ptr, const FDI_TCHAR *newname_ptr)
{
   DIAG_FILTER(NVM_remote,FDI2FS,FDI_rename,DIAG_INFORMATION)
   diagPrintf("Currently, this feature is not supported");

   return(0);
}
/******************************************************************************
Function : FDI_Format(void)
*******************************************************************************/
ERR_CODE FDI_Format_2chip(void)
{
	DIAG_FILTER(NVM_remote,FDI2FS,FDI_Format,DIAG_INFORMATION)
	diagPrintf("Currently, this feature is not supported");

	return( ERR_NONE );

}
/******************************************************************************
Function : FDI_stat
*******************************************************************************/
int FDI_stat_2chip(const char * filename_ptr, int *mode_ptr)
{
   DIAG_FILTER(NVM_remote,FDI2FS,FDI_stat,DIAG_INFORMATION)
   diagPrintf("Currently, this feature is not supported");
   return(0);
}
int FDI_chmod_2chip(const char *filname_ptr, int access_ptr)
{
   DIAG_FILTER(NVM_remote,FDI2FS,FDI_chmod,DIAG_INFORMATION)
   diagPrintf("Currently, this feature is not supported");
   return(0);

}
/******************************************************************************
Function :ERR_CODE FDI_ferror(FILE_ID stream)
*******************************************************************************/
ERR_CODE FDI_ferror_2chip(FILE_ID stream)
{
 //DIAG_FILTER(NVM_remote,FDI2FS,FDI_ferror,DIAG_INFORMATION)
 //diagPrintf("Currently, this feature is not supported");
 return(FDI2FSErrorCodeSvr);
}


int FDI_feof_2chip(FILE_ID stream)
{
	GenericFS_ReturnCodeE	status;
	int 					genericFSError = 0;
	GENERIC_FS_FILE_ID		genericFSFileID;
	
	ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
	genericFSFileID = FDI2FSFileIDSvr[stream];
	ASSERT(genericFSFileID!=NULL);
	
	return GenericFileSysFileEof(genericFSFileID);

}


void FDI2FS_Init(void)
{
   UINT32  fileIDIdx;

   //Global index required to obtain different fId for different files
   // (even if the fHandle is the same)
   //Points to the LAST-ALLOCATED
   //Take and pre-incremented for every new fopen.
   //Index ZERO is error. So the index is wrapped into 1
   FS_lastBusyIdxSvr = 0;

   for (fileIDIdx = 0;  fileIDIdx <MAX_FS_FILE_ID; fileIDIdx ++)
   		FDI2FSFileIDSvr[fileIDIdx] = 0;
}


FILE_ID FDI_fopen_2chip_test(const char *filename_ptr, const char *mode)
{
	UINT32                  fileID = 0;
	GenericFS_ReturnCodeE   status;
	GENERIC_FS_FILE_ID      genericFSFileID;

	status = GenericFileSysFileOpen_test(filename_ptr,mode,&genericFSFileID);
	if ( status != GENERIC_FS_OK)
	{

		FDI2FS_API_TRANSLATE_ERR(status ,FDI2FSErrorCodeTest);
	}
	else
	{
		UINT32 maxIdx;
		UINT32 cpsr = disableInterrupts(); //=========MUTEX
		fileID = FS_lastBusyIdxTest;
		maxIdx = 1;

		do
		{

			fileID++;
			if(fileID == MAX_FS_FILE_ID) fileID = 1; //Wrap to 1 but not to ZERO
			/*the assertion won't be turned off, so the side effect won't happens*/
			/*coverity[assert_side_effect]*/
			ASSERT(maxIdx++ < MAX_FS_FILE_ID);
		}
		while(FDI2FSFileIDTest[fileID] != 0);

		FDI2FSFileIDTest[fileID] = genericFSFileID;
		FS_lastBusyIdxTest = fileID;
		restoreInterrupts(cpsr); //===================MUTEX
	}

	return((FILE_ID)fileID);
}


int FDI_fclose_2chip_test(FILE_ID stream)
{
  GenericFS_ReturnCodeE   fclose_st;
  GENERIC_FS_FILE_ID      genericFSFileID;

  ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
  genericFSFileID = FDI2FSFileIDTest[stream];
  ASSERT(genericFSFileID!=NULL);

  fclose_st = GenericFileSysFileClose_test(genericFSFileID);

  //Nobody check the return status of the fclose()
  //The FDI2FSFileID[] stays dirty. Double fclose on the LINUX causes crash
  //So if ERROR, make assert here !!!
  ASSERT(fclose_st == GENERIC_FS_OK);  //genericFSError = EOF;

  FDI2FSFileIDTest[stream] = 0;
  FDI2FSErrorCodeTest = ERR_NONE;

  return 0; //genericFSError;
}


size_t FDI_fread_2chip_test(void *buffer_ptr, size_t element_size, size_t count, FILE_ID stream)
{
  UINT32                  noOfReadBytes;
  GenericFS_ReturnCodeE   status;
  GENERIC_FS_FILE_ID      genericFSFileID;

  ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
  genericFSFileID = FDI2FSFileIDTest[stream];
  ASSERT(genericFSFileID!=NULL);

  status = GenericFileSysFileERead_test(buffer_ptr ,(UINT32) element_size ,(UINT32) count, &noOfReadBytes, genericFSFileID);

#if defined(NVM_DEBUG_ENABLE)
  DIAG_FILTER(NVM_remote,FDI2FS,FDI_read_debug,DIAG_INFORMATION)
  diagPrintf("fileID %d;number of element to read %d,actual %d;",stream,(UINT32)count,noOfReadBytes);
#endif

  if((status == GENERIC_FS_OK)||(status==GENERIC_FS_FILE_EOF))
  {
	FDI2FSErrorCodeTest = ERR_NONE;
	return((size_t)noOfReadBytes);
  }
  else
  {
	FDI2FS_API_TRANSLATE_ERR(status ,FDI2FSErrorCodeTest);
	return(0);
  }
}


size_t FDI_fwrite_2chip_test(const void *buffer_ptr,size_t element_size,size_t count,FILE_ID stream)
{
  UINT32                  noOfWriteBytes;
  GenericFS_ReturnCodeE   status;
  GENERIC_FS_FILE_ID      genericFSFileID;

  ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
  genericFSFileID = FDI2FSFileIDTest[stream];
  ASSERT(genericFSFileID!=NULL);

 status = GenericFileSysFileWrite_test((void*)buffer_ptr ,(UINT32) element_size ,(UINT32) count ,&noOfWriteBytes, genericFSFileID);
 if(status == GENERIC_FS_OK )
 {
	FDI2FSErrorCodeTest = ERR_NONE;
	return((size_t)noOfWriteBytes);
 }
 else
 {
	FDI2FS_API_TRANSLATE_ERR(status ,FDI2FSErrorCodeTest);
	return(0);
 }
}


int FDI_fseek_2chip_test(FILE_ID stream, long offset, int wherefrom)
{
  GenericFS_ReturnCodeE   status;
  int                     genericFSError = 0;
  GENERIC_FS_FILE_ID      genericFSFileID;

  ASSERT(stream>0 && stream<MAX_FS_FILE_ID);
  genericFSFileID = FDI2FSFileIDTest[stream];
  ASSERT(genericFSFileID!=NULL);

  status = GenericFileSysFileSeek_test((UINT32) offset ,(UINT32)wherefrom , genericFSFileID);

  if ( status != GENERIC_FS_OK)
	genericFSError = EOF;
  return genericFSError;
}


#endif//NVM_INCLUDE

#endif
