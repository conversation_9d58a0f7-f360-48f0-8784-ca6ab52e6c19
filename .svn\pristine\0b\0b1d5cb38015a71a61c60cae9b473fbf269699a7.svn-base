/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/**********************************************************************
 *
 * Filename: pl_rf_dig_apc.h
 *
 * Programmer: Adi Georgy
 *
 * Functions:
 *
 * Description: TX APC for DigRf3 header file
 *
 * --------------------------------------------------------------------
 * Revision History
 *
 * Date         Who        Version      Description
 * --------------------------------------------------------------------
 * 5.8.09        AdiG          1.0
 *
 **********************************************************************/

#ifndef _PL_RF_DIG_APC_H_
#define _PL_RF_DIG_APC_H_

/*----------- External include files ----------------------------------------*/
/*----------- Local include files -------------------------------------------*/
#include "pl_rf_nvm.h"
#include "pl_rf_dig_aux_apc.h"
#include "pluginShared3G.h"

/*----------- Global defines -------------------------------------------------*/


#define DIG_TX_APC_MVOLT_2_VOLT_FACTOR_Q16 0x42 


//ICAT EXPORTED STRUCT
typedef struct
{
	INT16	pLevelMin_OL[DIG_TX_APC_PGC_TBL_NUM];  //Q8
	INT16	pLevelMax_OL[DIG_TX_APC_PGC_TBL_NUM];  //Q8
	INT16	pLevelMin_CL_LG;  //Q8
	INT16	pLevelMax_CL_LG;  //Q8
	INT16	pLevelMin_CL_HG;  //Q8
	INT16	pLevelMax_CL_HG;  //Q8
}digTxApc_PgcPowerLevel_perBand_ts;

//ICAT EXPORTED STRUCT
typedef struct
{
	digTxApc_PgcPowerLevel_perBand_ts     digTxPowerLevel[MAX_BANDS];
}digTxApc_PgcPowerLevel_ts; 


//ICAT EXPORTED STRUCT
typedef struct
{
	UINT16 deltaVctrl_LGComp;    // Q8
	UINT16 deltaVctrl_HGComp;    // Q8
	INT16 deltaOLPgc_LGComp_V;	 // Q8
	INT16 deltaOLPgc_LGLComp_F;  // Q8
	INT16 deltaOLPgc_LGLComp_T;  // Q8
	INT16 deltaOLPgc_LGHComp_F;  // Q8
	INT16 deltaOLPgc_LGHComp_T;  // Q8
	INT16 deltaOLP_LGComp_V;     // Q8
	INT16 deltaOLP_LGLComp_F;	 // Q8
	INT16 deltaOLP_LGLComp_T;	 // Q8
	INT16 deltaOLP_LGHComp_F;	 // Q8
	INT16 deltaOLP_LGHComp_T;	 // Q8
	INT16 deltaCLPd_LGLComp_F;	 // Q8
	INT16 deltaCLPd_LGLComp_T;	 // Q8
	INT16 deltaCLPd_LGHComp_F;	 // Q8
	INT16 deltaCLPd_LGHComp_T;	 // Q8
	INT16 deltaCLPd_HGLComp_F;	 // Q8
	INT16 deltaCLPd_HGLComp_T;	 // Q8
	INT16 deltaCLPd_HGHComp_F;	 // Q8
	INT16 deltaCLPd_HGHComp_T;	 // Q8
	INT16 deltaCLP_LGLComp_F;	 // Q8
	INT16 deltaCLP_LGLComp_T;	 // Q8
	INT16 deltaCLP_LGHComp_F;	 // Q8
	INT16 deltaCLP_LGHComp_T;	 // Q8
	INT16 deltaCLP_HGLComp_F;	 // Q8
	INT16 deltaCLP_HGLComp_T;	 // Q8
	INT16 deltaCLP_HGHComp_F;	 // Q8
	INT16 deltaCLP_HGHComp_T;	 // Q8	
}digTxApc_Comp_ts; 



//ICAT EXPORTED STRUCT
typedef struct
{
	UINT16		nvmVer;       
	UINT16      pOutNegativeStep;	
	UINT16      pOutPositiveStep;		
	UINT16		pgcNumOfWords;       
	INT16		pgcMinPower;       
	UINT16		pdLGNumOfWords;       
	INT16		pdLGMinPower;   	
	UINT16		pdHGNumOfWords;       
	INT16		pdHGMinPower;       
	UINT16		vCtrlLGNumOfWords;       
	INT16		vCtrlLGMinPower;   	
	UINT16		vCtrlHGNumOfWords;       
	INT16		vCtrlHGMinPower;
	UINT16		calibPgcNumOfWords;       
	INT16		calibPgcMinPower; 
	UINT16      calibPgcSlopeNumOfWords;		
	INT16		calibPgcSlopeMinPower; 
	UINT16		calibPdLGNumOfWords;       
	INT16		calibPdLGMinPower;   	
	UINT16		calibPdHGNumOfWords;       
	INT16		calibPdHGMinPower;  
	UINT16		calibVctrlLGNumOfWords;       
	INT16		calibVctrlLGMinPower;   	
	UINT16		calibVctrlHGNumOfWords;       
	INT16		calibVctrlHGMinPower;
	UINT16		frequency;
	UINT16		temperature;	
	UINT16		vBat;
} digTxApcNvmHeader_ts;


//ICAT EXPORTED STRUCT
typedef struct
{	
	digTxApc_LoopAccuracies_ts loopAccuracies;
	INT16       pMaxHGNorm;
	INT16       pMaxHGExtr;
	UINT16 		dVctrlLGComp;
	UINT16 		dVctrlHGComp;
	UINT16 		dOL_PGC_LG;
	UINT16 		dOL_P_LG;
	UINT16 		dCL_PD_LG;
	UINT16 		dCL_PD_HGL;
	UINT16 		dCL_PD_HGH;
	UINT16 		dCL_P_LG;
	UINT16 		dCL_P_HGL;
	UINT16 		dCL_P_HGH;	
	UINT16      pgcCm0arr[DIG_TX_APC_PGC_ARR_SIZE]; //44 cells
	UINT16      pgcCm0slope[DIG_TX_APC_PGC_SLOPE_ARR_SIZE];
	UINT16      pgcCm0_2arr[DIG_TX_APC_PGC_ARR_SIZE];
	UINT16      pgcCm0_2slope[DIG_TX_APC_PGC_SLOPE_ARR_SIZE];
	UINT16      pgcCm2arr[DIG_TX_APC_PGC_ARR_SIZE];
	UINT16      pgcCm2slope[DIG_TX_APC_PGC_SLOPE_ARR_SIZE];
	UINT16      pdLGarr[DIG_TX_APC_PD_LG_ARR_SIZE];	//15 cells
	UINT16      pdLGslope[DIG_TX_APC_PD_SLOPE_LG_ARR_SIZE]; // 15
	UINT16      pdHGarr[DIG_TX_APC_PD_HG_ARR_SIZE];  // 14
	UINT16      pdHGslope[DIG_TX_APC_PD_SLOPE_HG_ARR_SIZE]; //14
	UINT16      vCtrlLGarrCm0[DIG_TX_APC_VCTRL_LG_ARR_SIZE]; //25 cells
	UINT16      vCtrlLGarrCm0_2[DIG_TX_APC_VCTRL_LG_ARR_SIZE]; //25 cells
	UINT16      vCtrlLGarrCm2[DIG_TX_APC_VCTRL_LG_ARR_SIZE]; //25 cells
	UINT16      vCtrlHGarr[DIG_TX_APC_VCTRL_HG_ARR_SIZE]; // 14 cells		
	UINT16      calibPgcCm0arr[DIG_TX_APC_PGC_CALIB_ARR_SIZE]; //47
	UINT16      calibPgcCm0slope[DIG_TX_APC_PGC_SLOPE_CALIB_ARR_SIZE]; //46
	UINT16      calibPgcCm0_2arr[DIG_TX_APC_PGC_CALIB_ARR_SIZE]; // 47
	UINT16      calibPgcCm0_2slope[DIG_TX_APC_PGC_SLOPE_CALIB_ARR_SIZE]; // 46
	UINT16      calibPgcCm2arr[DIG_TX_APC_PGC_CALIB_ARR_SIZE]; //47
	UINT16      calibPgcCm2slope[DIG_TX_APC_PGC_SLOPE_CALIB_ARR_SIZE]; //46
	UINT16      calibPdLGarr[DIG_TX_APC_PD_LG_CALIB_ARR_SIZE]; //17	
	UINT16      calibPdHGarr[DIG_TX_APC_PD_HG_CALIB_ARR_SIZE];	// 19
	UINT16      analysisVctrlLGarr[DIG_TX_APC_VCTRL_LG_CALIB_ARR_SIZE]; // 27 cells
	UINT16      analysisVctrlHGarr[DIG_TX_APC_VCTRL_HG_CALIB_ARR_SIZE];	// 19 
	UINT16      digOpenLoopError;
	UINT16      digCloseLoopError;
	UINT16      digPmaxHG;
	UINT16      pad;
} digTxApcNvmBody_ts;

//ICAT EXPORTED STRUCT
typedef struct
{
	digTxApcNvmHeader_ts    apcHeader;
	digTxApcNvmBody_ts      apcTables;
} digTxApcNvm_ts;

typedef struct
{
	BOOL    frequency;
	BOOL    temperature;
	BOOL    vBat;
} digTxApc_CompFlags_ts;

//ICAT EXPORTED ENUM
typedef  enum
{
  TV_UPDATE = 0, 
  F_UPDATE
}digTxApcFTVUpdate_te;


/*----------- Global function declaration -----------------------------------*/

// aux func
UINT16 plRFD_DigRf3_ApcGetCMCalibOffset(digTxApc_Main_ts *digTxApcMain, UINT8 cmIdx);
UINT16 plRFD_DigRf3_ApcGetCalVctrlLGValByPower(INT16 powerX,  UINT16 calibOffset, digTxApc_PgcPowerLevel_ts *pgcPowerLevel, digTxApc_Main_ts *digTxApcMain);
UINT16 plRFD_DigRf3_ApcQuantVctrlUsingCeil_LG(digTxApc_Main_ts *digTxApcMain,digTxApc_PgcPowerLevel_ts *pgcPowerLevel,
																	UINT32 vCtrlInQ8, UINT16 cMCalibOffset);
INT16 plRFD_DigRf3_ApcGetPowerLevelForVctrl(digTxApc_PgcPowerLevel_ts *pgcPowerLevel, digTxApc_Main_ts *digTxApcMain, 
                                                      UINT16 vCtrlInputVal, UINT16 cMCalibOffset, INT16 xInputPower);

// declaration
VOID   plRFD_DigRf3_TxApcLoadDefaultParams(digTxApc_Main_ts *digTxApcMain);
VOID   plRFD_DigRf3_TxApcFTVUpdate(digTxApcFTVUpdate_te ftvUpdate);
VOID   plRFD_DigRf3_SendTxApcCmd(void);
VOID   plRFD_DigRf3_UpdateApcMaxPowerFromComCfg (digTxApc_Main_ts *digTxApcMain);
VOID   plRFD_DigRf3_SetApcMaxPowerFromComCfg (UINT16 bandNum, UINT16 comCfgNewMaxPowerVal);

UINT16 plRFD_DigRf3_GetApcPAdGval (void);
UINT16 plRFD_DigRf3_GetApcPgcOrPdVal (UINT16 index);
UINT16 plRFD_DigRf3_GetApcCalibPgcOrPdVal(UINT16 index);
INT16  plRFD_DigRf3_GetApcPgcOrPdMaxVal (void);
VOID   plRFD_DigRf3_SetApcCompensationFlags (BOOL compFFlag, BOOL compTFlag, BOOL compVFlag);
VOID	plRFDRefreshApcParamsFromGrfd (VOID);


#endif /* _PL_RF_DIG_APC_H_ */


