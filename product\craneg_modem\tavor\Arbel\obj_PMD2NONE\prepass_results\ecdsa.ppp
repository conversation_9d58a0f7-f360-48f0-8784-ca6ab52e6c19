# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
/*
 *  Elliptic curve DSA
 *
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

/*
 * References:
 *
 * SEC1 http://www.secg.org/index.php?action=secg,docs_secg
 */

# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"
/**
 * \file common.h
 *
 * \brief Utility macros for internal use in the library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"
/**
 * \file build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
 /*
  *  Copyright The Mbed TLS Contributors
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  */










/*
 * This set of compile-time defines can be used to determine the version number
 * of the Mbed TLS library used. Run-time variables for the same can be found in
 * version.h
 */

/**
 * The version number x.y.z is split into three parts.
 * Major, Minor, Patchlevel
 */




/**
 * The single version number has the following structure:
 *    MMNNPP00
 *    Major version | Minor version | Patch version
 */








# 1 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
/*
 * Copyright (C) 2019 Alibaba Group Holding Limited
 */




/*specially for alios*/







/* System support */




//#define MBEDTLS_PLATFORM_MEMORY

//#define MBEDTLS_CONFIG_TLS_DEBUG

/* mbed TLS feature support */
# 35 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_ALT


# 53 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 60 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 76 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"





# 103 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"







/* mbed TLS modules */
# 127 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_C
//#define MBEDTLS_TIMING_C













# 178 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 185 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 202 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 209 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//#ifdef LWM2M_WITH_MBEDTLS
# 219 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#endif /* LWM2M_WITH_MBEDTLS */







/* Module configuration options */





/**
 * \def MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION
 *
 * If set, the X509 parser will not break-off when parsing an X509 certificate
 * and encountering an unknown critical extension.
 *
 * \warning Depending on your PKI use, enabling this can be a security risk!
 *
 * Uncomment to prevent an error.
 */




//ALIPAY_SUPPORT BEGIN


typedef unsigned int time_t;
# 273 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#ifndef MBEDTLS_ECP_DP_SECP256R1_ENABLED


//#endif
# 284 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//ALIPAY_SUPPORT END

# 298 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 66 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"








/* Target and application specific configurations
 *
 * Allow user to override any previous default.
 *
 */




# 89 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
/**
 * \file check_config.h
 *
 * \brief Consistency checks for configuration options
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




/*
 * We assume CHAR_BIT is 8 in many places. In practice, this is true on our
 * target platforms, so not an issue, but let's just be extra sure.
 */
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 31 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"




# 52 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"








































//ALIPAY_SUPPORT BEGIN
# 111 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END






# 129 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"





//ALIPAY_SUPPORT BEGIN
# 152 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END


























# 196 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 206 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
































































































































# 341 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















































































































































































































































# 589 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"















# 611 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"



















































# 707 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















# 737 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











/*
 * HKDF is mandatory for TLS 1.3.
 * Otherwise support for at least one ciphersuite mandates either SHA_256 or
 * SHA_384.
 */
# 759 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

/*
 * The current implementation of TLS 1.3 requires MBEDTLS_SSL_KEEP_PEER_CERTIFICATE.
 */




# 782 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











































































# 863 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 870 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

































































/* Reject attempts to enable options that have been removed and that could
 * cause a build to succeed but with features removed. */













































/*
 * Avoid warning from -pedantic. This is a convenient place for this
 * workaround since this is included by every single file before the
 * #if defined(MBEDTLS_xxx_C) that results in empty translation units.
 */
typedef int mbedtls_iso_c_forbids_empty_translation_units;

# 91 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 27 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 29 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/** Helper to define a function as static except when building invasive tests.
 *
 * If a function is only used inside its own source file and should be
 * declared `static` to allow the compiler to optimize for code size,
 * but that function has unit tests, define it with
 * ```
 * MBEDTLS_STATIC_TESTABLE int mbedtls_foo(...) { ... }
 * ```
 * and declare it in a header in the `library/` directory with
 * ```
 * #if defined(MBEDTLS_TEST_HOOKS)
 * int mbedtls_foo(...);
 * #endif
 * ```
 */






# 63 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/** Allow library to access its structs' private members.
 *
 * Although structs defined in header files are publicly available,
 * their members are private and should not be accessed by the user.
 */


/** Byte Reading Macros
 *
 * Given a multi-byte integer \p x, MBEDTLS_BYTE_n retrieves the n-th
 * byte from x, where byte 0 is the least significant byte.
 */
# 84 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */
# 103 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 32 bits unsigned integer in big-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 122 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */
# 141 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 32 bits unsigned integer in little-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 160 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */
# 177 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 16 bits unsigned integer in little-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 194 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */
# 211 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 16 bits unsigned integer in big-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 228 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 246 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 24 bits unsigned integer in big-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 264 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 282 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 24 bits unsigned integer in little-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 300 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */
# 323 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 64 bits unsigned integer in big-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 346 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */
# 369 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 64 bits unsigned integer in little-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 392 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/* Fix MSVC C99 compatible issue
 *      MSVC support __func__ from visual studio 2015( 1900 )
 *      Use MSVC predefine macro to avoid name check fail.
 */




# 27 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"



# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"
/**
 * \file ecdsa.h
 *
 * \brief This file contains ECDSA definitions and functions.
 *
 * The Elliptic Curve Digital Signature Algorithm (ECDSA) is defined in
 * <em>Standards for Efficient Cryptography Group (SECG):
 * SEC1 Elliptic Curve Cryptography</em>.
 * The use of ECDSA for TLS is defined in <em>RFC-4492: Elliptic Curve
 * Cryptography (ECC) Cipher Suites for Transport Layer Security (TLS)</em>.
 *
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h"
 /**
 * \file private_access.h
 *
 * \brief Macro wrapper for struct's members.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




# 32 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h"

# 33 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"

# 35 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"
/**
 * \file ecp.h
 *
 * \brief This file provides an API for Elliptic Curves over GF(P) (ECP).
 *
 * The use of ECP in cryptography and TLS is defined in
 * <em>Standards for Efficient Cryptography Group (SECG): SEC1
 * Elliptic Curve Cryptography</em> and
 * <em>RFC-4492: Elliptic Curve Cryptography (ECC) Cipher Suites
 * for Transport Layer Security (TLS)</em>.
 *
 * <em>RFC-2409: The Internet Key Exchange (IKE)</em> defines ECP
 * group types.
 *
 */

/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

# 37 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

# 39 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"
/**
 * \file bignum.h
 *
 * \brief Multi-precision integer library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
# 25 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

# 27 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"
/* stddef.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.1.4 */

/* Copyright (C) ARM Ltd., 1999
 * All rights reserved
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991 ARM Limited. All rights reserved.             */
/* version 0.05 */

/*
 * The following types and macros are defined in several headers referred to in
 * the descriptions of the functions declared in that header. They are also
 * defined in this header file.
 */





# 34 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"




  typedef signed int ptrdiff_t;



 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



  /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* also in <stdlib.h> and <inttypes.h> */
# 82 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



   /* null pointer constant. */




  /* EDG uses __INTADDR__ to avoid errors when strict */




  typedef long double max_align_t;









# 114 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



/* end of stddef.h */

# 29 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"
# 30 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"





/** An error occurred while reading from or writing to a file. */

/** Bad input parameters to function. */

/** There is an invalid character in the digit string. */

/** The buffer is too small to write to. */

/** The input arguments are negative or result in illegal output. */

/** The input argument for division is zero, which is not allowed. */

/** The input arguments are not acceptable. */

/** Memory allocation failed. */


# 58 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

/*
 * Maximum size MPIs are allowed to grow to in number of limbs.
 */



/*
 * Maximum window size used for modular exponentiation. Default: 6
 * Minimum value: 1. Maximum value: 6.
 *
 * Result is an array of ( 2 ** MBEDTLS_MPI_WINDOW_SIZE ) MPIs used
 * for the sliding window calculation. (So 64 by default)
 *
 * Reduction in size, reduces speed.
 */




/*
 * Maximum size of MPIs allowed in bits and bytes for user-MPIs.
 * ( Default: 512 bytes => 4096 bits, Maximum tested: 2048 bytes => 16384 bits )
 *
 * Note: Calculations can temporarily result in larger MPIs. So the number
 * of limbs required (MBEDTLS_MPI_MAX_LIMBS) is higher.
 */





/*
 * When reading from files with mbedtls_mpi_read_file() and writing to files with
 * mbedtls_mpi_write_file() the buffer should have space
 * for a (short) label, the MPI (in the provided radix), the newline
 * characters and the '\0'.
 *
 * By default we assume at least a 10 char label, a minimum radix of 10
 * (decimal) and a maximum of 4096 bit numbers (1234 decimal chars).
 * Autosized at compile time for at least a 10 char label, a minimum radix
 * of 10 (decimal) for a number of MBEDTLS_MPI_MAX_BITS size.
 *
 * This used to be statically sized to 1250 for a maximum of 4096 bit
 * numbers (1234 decimal chars).
 *
 * Calculate using the formula:
 *  MBEDTLS_MPI_RW_BUFFER_SIZE = ceil(MBEDTLS_MPI_MAX_BITS / ln(10) * ln(2)) +
 *                                LabelSize + 6
 */




/*
 * Define the base integer type, architecture-wise.
 *
 * 32 or 64-bit integer types can be forced regardless of the underlying
 * architecture by defining MBEDTLS_HAVE_INT32 or MBEDTLS_HAVE_INT64
 * respectively and undefining MBEDTLS_HAVE_ASM.
 *
 * Double-width integers (e.g. 128-bit in 64-bit architectures) can be
 * disabled by defining MBEDTLS_NO_UDBL_DIVISION.
 */
# 168 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"


    /* Default to 32-bit compilation */



    typedef  int32_t mbedtls_mpi_sint;
    typedef uint32_t mbedtls_mpi_uint;










/**
 * \brief          MPI structure
 */
typedef struct mbedtls_mpi
{
    int s;              /*!<  Sign: -1 if the mpi is negative, 1 otherwise */
    size_t n;           /*!<  total # of limbs  */
    mbedtls_mpi_uint *p;          /*!<  pointer to limbs  */
}
mbedtls_mpi;

/**
 * \brief           Initialize an MPI context.
 *
 *                  This makes the MPI ready to be set or freed,
 *                  but does not define a value for the MPI.
 *
 * \param X         The MPI context to initialize. This must not be \c NULL.
 */
void mbedtls_mpi_init( mbedtls_mpi *X );

/**
 * \brief          This function frees the components of an MPI context.
 *
 * \param X        The MPI context to be cleared. This may be \c NULL,
 *                 in which case this function is a no-op. If it is
 *                 not \c NULL, it must point to an initialized MPI.
 */
void mbedtls_mpi_free( mbedtls_mpi *X );

/**
 * \brief          Enlarge an MPI to the specified number of limbs.
 *
 * \note           This function does nothing if the MPI is
 *                 already large enough.
 *
 * \param X        The MPI to grow. It must be initialized.
 * \param nblimbs  The target number of limbs.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_grow( mbedtls_mpi *X, size_t nblimbs );

/**
 * \brief          This function resizes an MPI downwards, keeping at least the
 *                 specified number of limbs.
 *
 *                 If \c X is smaller than \c nblimbs, it is resized up
 *                 instead.
 *
 * \param X        The MPI to shrink. This must point to an initialized MPI.
 * \param nblimbs  The minimum number of limbs to keep.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed
 *                 (this can only happen when resizing up).
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_shrink( mbedtls_mpi *X, size_t nblimbs );

/**
 * \brief          Make a copy of an MPI.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param Y        The source MPI. This must point to an initialized MPI.
 *
 * \note           The limb-buffer in the destination MPI is enlarged
 *                 if necessary to hold the value in the source MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_copy( mbedtls_mpi *X, const mbedtls_mpi *Y );

/**
 * \brief          Swap the contents of two MPIs.
 *
 * \param X        The first MPI. It must be initialized.
 * \param Y        The second MPI. It must be initialized.
 */
void mbedtls_mpi_swap( mbedtls_mpi *X, mbedtls_mpi *Y );

/**
 * \brief          Perform a safe conditional copy of MPI which doesn't
 *                 reveal whether the condition was true or not.
 *
 * \param X        The MPI to conditionally assign to. This must point
 *                 to an initialized MPI.
 * \param Y        The MPI to be assigned from. This must point to an
 *                 initialized MPI.
 * \param assign   The condition deciding whether to perform the
 *                 assignment or not. Possible values:
 *                 * \c 1: Perform the assignment `X = Y`.
 *                 * \c 0: Keep the original value of \p X.
 *
 * \note           This function is equivalent to
 *                      `if( assign ) mbedtls_mpi_copy( X, Y );`
 *                 except that it avoids leaking any information about whether
 *                 the assignment was done or not (the above code may leak
 *                 information through branch prediction and/or memory access
 *                 patterns analysis).
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_safe_cond_assign( mbedtls_mpi *X, const mbedtls_mpi *Y, unsigned char assign );

/**
 * \brief          Perform a safe conditional swap which doesn't
 *                 reveal whether the condition was true or not.
 *
 * \param X        The first MPI. This must be initialized.
 * \param Y        The second MPI. This must be initialized.
 * \param assign   The condition deciding whether to perform
 *                 the swap or not. Possible values:
 *                 * \c 1: Swap the values of \p X and \p Y.
 *                 * \c 0: Keep the original values of \p X and \p Y.
 *
 * \note           This function is equivalent to
 *                      if( assign ) mbedtls_mpi_swap( X, Y );
 *                 except that it avoids leaking any information about whether
 *                 the assignment was done or not (the above code may leak
 *                 information through branch prediction and/or memory access
 *                 patterns analysis).
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 *
 */
int mbedtls_mpi_safe_cond_swap( mbedtls_mpi *X, mbedtls_mpi *Y, unsigned char assign );

/**
 * \brief          Store integer value in MPI.
 *
 * \param X        The MPI to set. This must be initialized.
 * \param z        The value to use.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_lset( mbedtls_mpi *X, mbedtls_mpi_sint z );

/**
 * \brief          Get a specific bit from an MPI.
 *
 * \param X        The MPI to query. This must be initialized.
 * \param pos      Zero-based index of the bit to query.
 *
 * \return         \c 0 or \c 1 on success, depending on whether bit \c pos
 *                 of \c X is unset or set.
 * \return         A negative error code on failure.
 */
int mbedtls_mpi_get_bit( const mbedtls_mpi *X, size_t pos );

/**
 * \brief          Modify a specific bit in an MPI.
 *
 * \note           This function will grow the target MPI if necessary to set a
 *                 bit to \c 1 in a not yet existing limb. It will not grow if
 *                 the bit should be set to \c 0.
 *
 * \param X        The MPI to modify. This must be initialized.
 * \param pos      Zero-based index of the bit to modify.
 * \param val      The desired value of bit \c pos: \c 0 or \c 1.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_set_bit( mbedtls_mpi *X, size_t pos, unsigned char val );

/**
 * \brief          Return the number of bits of value \c 0 before the
 *                 least significant bit of value \c 1.
 *
 * \note           This is the same as the zero-based index of
 *                 the least significant bit of value \c 1.
 *
 * \param X        The MPI to query.
 *
 * \return         The number of bits of value \c 0 before the least significant
 *                 bit of value \c 1 in \p X.
 */
size_t mbedtls_mpi_lsb( const mbedtls_mpi *X );

/**
 * \brief          Return the number of bits up to and including the most
 *                 significant bit of value \c 1.
 *
 * * \note         This is same as the one-based index of the most
 *                 significant bit of value \c 1.
 *
 * \param X        The MPI to query. This must point to an initialized MPI.
 *
 * \return         The number of bits up to and including the most
 *                 significant bit of value \c 1.
 */
size_t mbedtls_mpi_bitlen( const mbedtls_mpi *X );

/**
 * \brief          Return the total size of an MPI value in bytes.
 *
 * \param X        The MPI to use. This must point to an initialized MPI.
 *
 * \note           The value returned by this function may be less than
 *                 the number of bytes used to store \p X internally.
 *                 This happens if and only if there are trailing bytes
 *                 of value zero.
 *
 * \return         The least number of bytes capable of storing
 *                 the absolute value of \p X.
 */
size_t mbedtls_mpi_size( const mbedtls_mpi *X );

/**
 * \brief          Import an MPI from an ASCII string.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param radix    The numeric base of the input string.
 * \param s        Null-terminated string buffer.
 *
 * \return         \c 0 if successful.
 * \return         A negative error code on failure.
 */
int mbedtls_mpi_read_string( mbedtls_mpi *X, int radix, const char *s );

/**
 * \brief          Export an MPI to an ASCII string.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param radix    The numeric base of the output string.
 * \param buf      The buffer to write the string to. This must be writable
 *                 buffer of length \p buflen Bytes.
 * \param buflen   The available size in Bytes of \p buf.
 * \param olen     The address at which to store the length of the string
 *                 written, including the  final \c NULL byte. This must
 *                 not be \c NULL.
 *
 * \note           You can call this function with `buflen == 0` to obtain the
 *                 minimum required buffer size in `*olen`.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if the target buffer \p buf
 *                 is too small to hold the value of \p X in the desired base.
 *                 In this case, `*olen` is nonetheless updated to contain the
 *                 size of \p buf required for a successful call.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_string( const mbedtls_mpi *X, int radix,
                              char *buf, size_t buflen, size_t *olen );

# 485 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

/**
 * \brief          Import an MPI from unsigned big endian binary data.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param buf      The input buffer. This must be a readable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The length of the input buffer \p p in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_read_binary( mbedtls_mpi *X, const unsigned char *buf,
                             size_t buflen );

/**
 * \brief          Import X from unsigned binary data, little endian
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param buf      The input buffer. This must be a readable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The length of the input buffer \p p in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_read_binary_le( mbedtls_mpi *X,
                                const unsigned char *buf, size_t buflen );

/**
 * \brief          Export X into unsigned binary data, big endian.
 *                 Always fills the whole buffer, which will start with zeros
 *                 if the number is smaller.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param buf      The output buffer. This must be a writable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The size of the output buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p buf isn't
 *                 large enough to hold the value of \p X.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_binary( const mbedtls_mpi *X, unsigned char *buf,
                              size_t buflen );

/**
 * \brief          Export X into unsigned binary data, little endian.
 *                 Always fills the whole buffer, which will end with zeros
 *                 if the number is smaller.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param buf      The output buffer. This must be a writable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The size of the output buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p buf isn't
 *                 large enough to hold the value of \p X.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_binary_le( const mbedtls_mpi *X,
                                 unsigned char *buf, size_t buflen );

/**
 * \brief          Perform a left-shift on an MPI: X <<= count
 *
 * \param X        The MPI to shift. This must point to an initialized MPI.
 * \param count    The number of bits to shift by.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_shift_l( mbedtls_mpi *X, size_t count );

/**
 * \brief          Perform a right-shift on an MPI: X >>= count
 *
 * \param X        The MPI to shift. This must point to an initialized MPI.
 * \param count    The number of bits to shift by.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_shift_r( mbedtls_mpi *X, size_t count );

/**
 * \brief          Compare the absolute values of two MPIs.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param Y        The right-hand MPI. This must point to an initialized MPI.
 *
 * \return         \c 1 if `|X|` is greater than `|Y|`.
 * \return         \c -1 if `|X|` is lesser than `|Y|`.
 * \return         \c 0 if `|X|` is equal to `|Y|`.
 */
int mbedtls_mpi_cmp_abs( const mbedtls_mpi *X, const mbedtls_mpi *Y );

/**
 * \brief          Compare two MPIs.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param Y        The right-hand MPI. This must point to an initialized MPI.
 *
 * \return         \c 1 if \p X is greater than \p Y.
 * \return         \c -1 if \p X is lesser than \p Y.
 * \return         \c 0 if \p X is equal to \p Y.
 */
int mbedtls_mpi_cmp_mpi( const mbedtls_mpi *X, const mbedtls_mpi *Y );

/**
 * \brief          Check if an MPI is less than the other in constant time.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI
 *                 with the same allocated length as Y.
 * \param Y        The right-hand MPI. This must point to an initialized MPI
 *                 with the same allocated length as X.
 * \param ret      The result of the comparison:
 *                 \c 1 if \p X is less than \p Y.
 *                 \c 0 if \p X is greater than or equal to \p Y.
 *
 * \return         0 on success.
 * \return         MBEDTLS_ERR_MPI_BAD_INPUT_DATA if the allocated length of
 *                 the two input MPIs is not the same.
 */
int mbedtls_mpi_lt_mpi_ct( const mbedtls_mpi *X, const mbedtls_mpi *Y,
        unsigned *ret );

/**
 * \brief          Compare an MPI with an integer.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param z        The integer value to compare \p X to.
 *
 * \return         \c 1 if \p X is greater than \p z.
 * \return         \c -1 if \p X is lesser than \p z.
 * \return         \c 0 if \p X is equal to \p z.
 */
int mbedtls_mpi_cmp_int( const mbedtls_mpi *X, mbedtls_mpi_sint z );

/**
 * \brief          Perform an unsigned addition of MPIs: X = |A| + |B|
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param B        The second summand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_abs( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform an unsigned subtraction of MPIs: X = |A| - |B|
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param B        The subtrahend. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p B is greater than \p A.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_sub_abs( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a signed addition of MPIs: X = A + B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param B        The second summand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_mpi( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a signed subtraction of MPIs: X = A - B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param B        The subtrahend. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_sub_mpi( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a signed addition of an MPI and an integer: X = A + b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param b        The second summand.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_int( mbedtls_mpi *X, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a signed subtraction of an MPI and an integer:
 *                 X = A - b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param b        The subtrahend.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_sub_int( mbedtls_mpi *X, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a multiplication of two MPIs: X = A * B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first factor. This must point to an initialized MPI.
 * \param B        The second factor. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mul_mpi( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a multiplication of an MPI with an unsigned integer:
 *                 X = A * b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first factor. This must point to an initialized MPI.
 * \param b        The second factor.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mul_int( mbedtls_mpi *X, const mbedtls_mpi *A,
                         mbedtls_mpi_uint b );

/**
 * \brief          Perform a division with remainder of two MPIs:
 *                 A = Q * B + R
 *
 * \param Q        The destination MPI for the quotient.
 *                 This may be \c NULL if the value of the
 *                 quotient is not needed.
 * \param R        The destination MPI for the remainder value.
 *                 This may be \c NULL if the value of the
 *                 remainder is not needed.
 * \param A        The dividend. This must point to an initialized MPi.
 * \param B        The divisor. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p B equals zero.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_div_mpi( mbedtls_mpi *Q, mbedtls_mpi *R, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a division with remainder of an MPI by an integer:
 *                 A = Q * b + R
 *
 * \param Q        The destination MPI for the quotient.
 *                 This may be \c NULL if the value of the
 *                 quotient is not needed.
 * \param R        The destination MPI for the remainder value.
 *                 This may be \c NULL if the value of the
 *                 remainder is not needed.
 * \param A        The dividend. This must point to an initialized MPi.
 * \param b        The divisor.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p b equals zero.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_div_int( mbedtls_mpi *Q, mbedtls_mpi *R, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a modular reduction. R = A mod B
 *
 * \param R        The destination MPI for the residue value.
 *                 This must point to an initialized MPI.
 * \param A        The MPI to compute the residue of.
 *                 This must point to an initialized MPI.
 * \param B        The base of the modular reduction.
 *                 This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p B equals zero.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p B is negative.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mod_mpi( mbedtls_mpi *R, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a modular reduction with respect to an integer.
 *                 r = A mod b
 *
 * \param r        The address at which to store the residue.
 *                 This must not be \c NULL.
 * \param A        The MPI to compute the residue of.
 *                 This must point to an initialized MPi.
 * \param b        The integer base of the modular reduction.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p b equals zero.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p b is negative.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_mod_int( mbedtls_mpi_uint *r, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a sliding-window exponentiation: X = A^E mod N
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The base of the exponentiation.
 *                 This must point to an initialized MPI.
 * \param E        The exponent MPI. This must point to an initialized MPI.
 * \param N        The base for the modular reduction. This must point to an
 *                 initialized MPI.
 * \param prec_RR  A helper MPI depending solely on \p N which can be used to
 *                 speed-up multiple modular exponentiations for the same value
 *                 of \p N. This may be \c NULL. If it is not \c NULL, it must
 *                 point to an initialized MPI. If it hasn't been used after
 *                 the call to mbedtls_mpi_init(), this function will compute
 *                 the helper value and store it in \p prec_RR for reuse on
 *                 subsequent calls to this function. Otherwise, the function
 *                 will assume that \p prec_RR holds the helper value set by a
 *                 previous call to mbedtls_mpi_exp_mod(), and reuse it.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \c N is negative or
 *                 even, or if \c E is negative.
 * \return         Another negative error code on different kinds of failures.
 *
 */
int mbedtls_mpi_exp_mod( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *E, const mbedtls_mpi *N,
                         mbedtls_mpi *prec_RR );

/**
 * \brief          Fill an MPI with a number of random bytes.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param size     The number of random bytes to generate.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng. This may be
 *                 \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on failure.
 *
 * \note           The bytes obtained from the RNG are interpreted
 *                 as a big-endian representation of an MPI; this can
 *                 be relevant in applications like deterministic ECDSA.
 */
int mbedtls_mpi_fill_random( mbedtls_mpi *X, size_t size,
                     int (*f_rng)(void *, unsigned char *, size_t),
                     void *p_rng );

/** Generate a random number uniformly in a range.
 *
 * This function generates a random number between \p min inclusive and
 * \p N exclusive.
 *
 * The procedure complies with RFC 6979 §3.3 (deterministic ECDSA)
 * when the RNG is a suitably parametrized instance of HMAC_DRBG
 * and \p min is \c 1.
 *
 * \note           There are `N - min` possible outputs. The lower bound
 *                 \p min can be reached, but the upper bound \p N cannot.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param min      The minimum value to return.
 *                 It must be nonnegative.
 * \param N        The upper bound of the range, exclusive.
 *                 In other words, this is one plus the maximum value to return.
 *                 \p N must be strictly larger than \p min.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p min or \p N is invalid
 *                 or if they are incompatible.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if the implementation was
 *                 unable to find a suitable value within a limited number
 *                 of attempts. This has a negligible probability if \p N
 *                 is significantly larger than \p min, which is the case
 *                 for all usual cryptographic applications.
 * \return         Another negative error code on failure.
 */
int mbedtls_mpi_random( mbedtls_mpi *X,
                        mbedtls_mpi_sint min,
                        const mbedtls_mpi *N,
                        int (*f_rng)(void *, unsigned char *, size_t),
                        void *p_rng );

/**
 * \brief          Compute the greatest common divisor: G = gcd(A, B)
 *
 * \param G        The destination MPI. This must point to an initialized MPI.
 * \param A        The first operand. This must point to an initialized MPI.
 * \param B        The second operand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_gcd( mbedtls_mpi *G, const mbedtls_mpi *A,
                     const mbedtls_mpi *B );

/**
 * \brief          Compute the modular inverse: X = A^-1 mod N
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The MPI to calculate the modular inverse of. This must point
 *                 to an initialized MPI.
 * \param N        The base of the modular inversion. This must point to an
 *                 initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p N is less than
 *                 or equal to one.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if \p has no modular inverse
 *                 with respect to \p N.
 */
int mbedtls_mpi_inv_mod( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *N );

/**
 * \brief          Miller-Rabin primality test.
 *
 * \warning        If \p X is potentially generated by an adversary, for example
 *                 when validating cryptographic parameters that you didn't
 *                 generate yourself and that are supposed to be prime, then
 *                 \p rounds should be at least the half of the security
 *                 strength of the cryptographic algorithm. On the other hand,
 *                 if \p X is chosen uniformly or non-adversarially (as is the
 *                 case when mbedtls_mpi_gen_prime calls this function), then
 *                 \p rounds can be much lower.
 *
 * \param X        The MPI to check for primality.
 *                 This must point to an initialized MPI.
 * \param rounds   The number of bases to perform the Miller-Rabin primality
 *                 test for. The probability of returning 0 on a composite is
 *                 at most 2<sup>-2*\p rounds</sup>.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *                 This may be \c NULL if \p f_rng doesn't use
 *                 a context parameter.
 *
 * \return         \c 0 if successful, i.e. \p X is probably prime.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if \p X is not prime.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_is_prime_ext( const mbedtls_mpi *X, int rounds,
                              int (*f_rng)(void *, unsigned char *, size_t),
                              void *p_rng );
/**
 * \brief Flags for mbedtls_mpi_gen_prime()
 *
 * Each of these flags is a constraint on the result X returned by
 * mbedtls_mpi_gen_prime().
 */
typedef enum {
    MBEDTLS_MPI_GEN_PRIME_FLAG_DH =      0x0001, /**< (X-1)/2 is prime too */
    MBEDTLS_MPI_GEN_PRIME_FLAG_LOW_ERR = 0x0002, /**< lower error rate from 2<sup>-80</sup> to 2<sup>-128</sup> */
} mbedtls_mpi_gen_prime_flag_t;

/**
 * \brief          Generate a prime number.
 *
 * \param X        The destination MPI to store the generated prime in.
 *                 This must point to an initialized MPi.
 * \param nbits    The required size of the destination MPI in bits.
 *                 This must be between \c 3 and #MBEDTLS_MPI_MAX_BITS.
 * \param flags    A mask of flags of type #mbedtls_mpi_gen_prime_flag_t.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *                 This may be \c NULL if \p f_rng doesn't use
 *                 a context parameter.
 *
 * \return         \c 0 if successful, in which case \p X holds a
 *                 probably prime number.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if `nbits` is not between
 *                 \c 3 and #MBEDTLS_MPI_MAX_BITS.
 */
int mbedtls_mpi_gen_prime( mbedtls_mpi *X, size_t nbits, int flags,
                   int (*f_rng)(void *, unsigned char *, size_t),
                   void *p_rng );

# 1024 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"





# 41 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

/*
 * ECP error codes
 */
/** Bad input parameters to function. */

/** The buffer is too small to write to. */

/** The requested feature is not available, for example, the requested curve is not supported. */

/** The signature is not valid. */

/** Memory allocation failed. */

/** Generation of random value, such as ephemeral key, failed. */

/** Invalid private or public key. */

/** The buffer contains a valid signature followed by more data. */

/** Operation in progress, call again with the same parameters to continue. */


/* Flags indicating whether to include code that is specific to certain
 * types of curves. These flags are for internal library use only. */
	//ALIPAY_SUPPORT BEGIN
# 81 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"
//ALIPAY_SUPPORT END









/**
 * Domain-parameter identifiers: curve, subgroup, and generator.
 *
 * \note Only curves over prime fields are supported.
 *
 * \warning This library does not support validation of arbitrary domain
 * parameters. Therefore, only standardized domain parameters from trusted
 * sources should be used. See mbedtls_ecp_group_load().
 */
/* Note: when adding a new curve:
 * - Add it at the end of this enum, otherwise you'll break the ABI by
 *   changing the numerical value for existing curves.
 * - Increment MBEDTLS_ECP_DP_MAX below if needed.
 * - Update the calculation of MBEDTLS_ECP_MAX_BITS below.
 * - Add the corresponding MBEDTLS_ECP_DP_xxx_ENABLED macro definition to
 *   mbedtls_config.h.
 * - List the curve as a dependency of MBEDTLS_ECP_C and
 *   MBEDTLS_ECDSA_C if supported in check_config.h.
 * - Add the curve to the appropriate curve type macro
 *   MBEDTLS_ECP_yyy_ENABLED above.
 * - Add the necessary definitions to ecp_curves.c.
 * - Add the curve to the ecp_supported_curves array in ecp.c.
 * - Add the curve to applicable profiles in x509_crt.c.
 * - Add the curve to applicable presets in ssl_tls.c.
 */
typedef enum
{
    MBEDTLS_ECP_DP_NONE = 0,       /*!< Curve not defined. */
    MBEDTLS_ECP_DP_SECP192R1,      /*!< Domain parameters for the 192-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP224R1,      /*!< Domain parameters for the 224-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP256R1,      /*!< Domain parameters for the 256-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP384R1,      /*!< Domain parameters for the 384-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP521R1,      /*!< Domain parameters for the 521-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_BP256R1,        /*!< Domain parameters for 256-bit Brainpool curve. */
    MBEDTLS_ECP_DP_BP384R1,        /*!< Domain parameters for 384-bit Brainpool curve. */
    MBEDTLS_ECP_DP_BP512R1,        /*!< Domain parameters for 512-bit Brainpool curve. */
    MBEDTLS_ECP_DP_CURVE25519,     /*!< Domain parameters for Curve25519. */
    MBEDTLS_ECP_DP_SECP192K1,      /*!< Domain parameters for 192-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_SECP224K1,      /*!< Domain parameters for 224-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_SECP256K1,      /*!< Domain parameters for 256-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_CURVE448,       /*!< Domain parameters for Curve448. */
} mbedtls_ecp_group_id;

/**
 * The number of supported curves, plus one for #MBEDTLS_ECP_DP_NONE.
 */


/*
 * Curve types
 */
typedef enum
{
    MBEDTLS_ECP_TYPE_NONE = 0,
    MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS,    /* y^2 = x^3 + a x + b      */
    MBEDTLS_ECP_TYPE_MONTGOMERY,           /* y^2 = x^3 + a x^2 + x    */
} mbedtls_ecp_curve_type;

/**
 * Curve information, for use by other modules.
 *
 * The fields of this structure are part of the public API and can be
 * accessed directly by applications. Future versions of the library may
 * add extra fields or reorder existing fields.
 */
typedef struct mbedtls_ecp_curve_info
{
    mbedtls_ecp_group_id grp_id;    /*!< An internal identifier. */
    uint16_t tls_id;                /*!< The TLS NamedCurve identifier. */
    uint16_t bit_size;              /*!< The curve size in bits. */
    const char *name;               /*!< A human-friendly name. */
} mbedtls_ecp_curve_info;

/**
 * \brief           The ECP point structure, in Jacobian coordinates.
 *
 * \note            All functions expect and return points satisfying
 *                  the following condition: <code>Z == 0</code> or
 *                  <code>Z == 1</code>. Other values of \p Z are
 *                  used only by internal functions.
 *                  The point is zero, or "at infinity", if <code>Z == 0</code>.
 *                  Otherwise, \p X and \p Y are its standard (affine)
 *                  coordinates.
 */
typedef struct mbedtls_ecp_point
{
    mbedtls_mpi X;          /*!< The X coordinate of the ECP point. */
    mbedtls_mpi Y;          /*!< The Y coordinate of the ECP point. */
    mbedtls_mpi Z;          /*!< The Z coordinate of the ECP point. */
}
mbedtls_ecp_point;


/*
 * default mbed TLS elliptic curve arithmetic implementation
 *
 * (in case MBEDTLS_ECP_ALT is defined then the developer has to provide an
 * alternative implementation for the whole module and it will replace this
 * one.)
 */

/**
 * \brief           The ECP group structure.
 *
 * We consider two types of curve equations:
 * <ul><li>Short Weierstrass: <code>y^2 = x^3 + A x + B mod P</code>
 * (SEC1 + RFC-4492)</li>
 * <li>Montgomery: <code>y^2 = x^3 + A x^2 + x mod P</code> (Curve25519,
 * Curve448)</li></ul>
 * In both cases, the generator (\p G) for a prime-order subgroup is fixed.
 *
 * For Short Weierstrass, this subgroup is the whole curve, and its
 * cardinality is denoted by \p N. Our code requires that \p N is an
 * odd prime as mbedtls_ecp_mul() requires an odd number, and
 * mbedtls_ecdsa_sign() requires that it is prime for blinding purposes.
 *
 * For Montgomery curves, we do not store \p A, but <code>(A + 2) / 4</code>,
 * which is the quantity used in the formulas. Additionally, \p nbits is
 * not the size of \p N but the required size for private keys.
 *
 * If \p modp is NULL, reduction modulo \p P is done using a generic algorithm.
 * Otherwise, \p modp must point to a function that takes an \p mbedtls_mpi in the
 * range of <code>0..2^(2*pbits)-1</code>, and transforms it in-place to an integer
 * which is congruent mod \p P to the given MPI, and is close enough to \p pbits
 * in size, so that it may be efficiently brought in the 0..P-1 range by a few
 * additions or subtractions. Therefore, it is only an approximative modular
 * reduction. It must return 0 on success and non-zero on failure.
 *
 * \note        Alternative implementations of the ECP module must obey the
 *              following constraints.
 *              * Group IDs must be distinct: if two group structures have
 *                the same ID, then they must be identical.
 *              * The fields \c id, \c P, \c A, \c B, \c G, \c N,
 *                \c pbits and \c nbits must have the same type and semantics
 *                as in the built-in implementation.
 *                They must be available for reading, but direct modification
 *                of these fields does not need to be supported.
 *                They do not need to be at the same offset in the structure.
 */
typedef struct mbedtls_ecp_group
{
    mbedtls_ecp_group_id id;    /*!< An internal group identifier. */
    mbedtls_mpi P;              /*!< The prime modulus of the base field. */
    mbedtls_mpi A;              /*!< For Short Weierstrass: \p A in the equation. For
                                     Montgomery curves: <code>(A + 2) / 4</code>. */
    mbedtls_mpi B;              /*!< For Short Weierstrass: \p B in the equation.
                                     For Montgomery curves: unused. */
    mbedtls_ecp_point G;        /*!< The generator of the subgroup used. */
    mbedtls_mpi N;              /*!< The order of \p G. */
    size_t pbits;               /*!< The number of bits in \p P.*/
    size_t nbits;               /*!< For Short Weierstrass: The number of bits in \p P.
                                     For Montgomery curves: the number of bits in the
                                     private keys. */
    /* End of public fields */

    unsigned int h;             /*!< \internal 1 if the constants are static. */
    int (*modp)(mbedtls_mpi *); /*!< The function for fast pseudo-reduction
                                     mod \p P (see above).*/
    int (*t_pre)(mbedtls_ecp_point *, void *);  /*!< Unused. */
    int (*t_post)(mbedtls_ecp_point *, void *); /*!< Unused. */
    void *t_data;               /*!< Unused. */
    mbedtls_ecp_point *T;       /*!< Pre-computed points for ecp_mul_comb(). */
    size_t T_size;              /*!< The number of dynamic allocated pre-computed points. */
}
mbedtls_ecp_group;

/**
 * \name SECTION: Module settings
 *
 * The configuration options you can set for this module are in this section.
 * Either change them in mbedtls_config.h, or define them using the compiler command line.
 * \{
 */


/*
 * Maximum "window" size used for point multiplication.
 * Default: a point where higher memory usage yields diminishing performance
 *          returns.
 * Minimum value: 2. Maximum value: 7.
 *
 * Result is an array of at most ( 1 << ( MBEDTLS_ECP_WINDOW_SIZE - 1 ) )
 * points used for point multiplication. This value is directly tied to EC
 * peak memory usage, so decreasing it by one should roughly cut memory usage
 * by two (if large curves are in use).
 *
 * Reduction in size may reduce speed, but larger curves are impacted first.
 * Sample performances (in ECDHE handshakes/s, with FIXED_POINT_OPTIM = 1):
 *      w-size:     6       5       4       3       2
 *      521       145     141     135     120      97
 *      384       214     209     198     177     146
 *      256       320     320     303     262     226
 *      224       475     475     453     398     342
 *      192       640     640     633     587     476
 */




/*
 * Trade code size for speed on fixed-point multiplication.
 *
 * This speeds up repeated multiplication of the generator (that is, the
 * multiplication in ECDSA signatures, and half of the multiplications in
 * ECDSA verification and ECDHE) by a factor roughly 3 to 4.
 *
 * For each n-bit Short Weierstrass curve that is enabled, this adds 4n bytes
 * of code size if n < 384 and 8n otherwise.
 *
 * Change this value to 0 to reduce code size.
 */



/** \} name SECTION: Module settings */





/**
 * The maximum size of the groups, that is, of \c N and \c P.
 */
# 322 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"
//ALIPAY_SUPPORT END
# 352 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"




# 412 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"



/* We want to declare restartable versions of existing functions anyway */
typedef void mbedtls_ecp_restart_ctx;



/**
 * \brief    The ECP key-pair structure.
 *
 * A generic key-pair that may be used for ECDSA and fixed ECDH, for example.
 *
 * \note    Members are deliberately in the same order as in the
 *          ::mbedtls_ecdsa_context structure.
 */
typedef struct mbedtls_ecp_keypair
{
    mbedtls_ecp_group grp;      /*!<  Elliptic curve and base point     */
    mbedtls_mpi d;              /*!<  our secret value                  */
    mbedtls_ecp_point Q;        /*!<  our public value                  */
}
mbedtls_ecp_keypair;

/*
 * Point formats, from RFC 4492's enum ECPointFormat
 */



/*
 * Some other constants from RFC 4492
 */


# 515 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

/*
 * Get the type of a curve
 */
mbedtls_ecp_curve_type mbedtls_ecp_get_type( const mbedtls_ecp_group *grp );

/**
 * \brief           This function retrieves the information defined in
 *                  mbedtls_ecp_curve_info() for all supported curves.
 *
 * \note            This function returns information about all curves
 *                  supported by the library. Some curves may not be
 *                  supported for all algorithms. Call mbedtls_ecdh_can_do()
 *                  or mbedtls_ecdsa_can_do() to check if a curve is
 *                  supported for ECDH or ECDSA.
 *
 * \return          A statically allocated array. The last entry is 0.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_list( void );

/**
 * \brief           This function retrieves the list of internal group
 *                  identifiers of all supported curves in the order of
 *                  preference.
 *
 * \note            This function returns information about all curves
 *                  supported by the library. Some curves may not be
 *                  supported for all algorithms. Call mbedtls_ecdh_can_do()
 *                  or mbedtls_ecdsa_can_do() to check if a curve is
 *                  supported for ECDH or ECDSA.
 *
 * \return          A statically allocated array,
 *                  terminated with MBEDTLS_ECP_DP_NONE.
 */
const mbedtls_ecp_group_id *mbedtls_ecp_grp_id_list( void );

/**
 * \brief           This function retrieves curve information from an internal
 *                  group identifier.
 *
 * \param grp_id    An \c MBEDTLS_ECP_DP_XXX value.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_grp_id( mbedtls_ecp_group_id grp_id );

/**
 * \brief           This function retrieves curve information from a TLS
 *                  NamedCurve value.
 *
 * \param tls_id    An \c MBEDTLS_ECP_DP_XXX value.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_tls_id( uint16_t tls_id );

/**
 * \brief           This function retrieves curve information from a
 *                  human-readable name.
 *
 * \param name      The human-readable name.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_name( const char *name );

/**
 * \brief           This function initializes a point as zero.
 *
 * \param pt        The point to initialize.
 */
void mbedtls_ecp_point_init( mbedtls_ecp_point *pt );

/**
 * \brief           This function initializes an ECP group context
 *                  without loading any domain parameters.
 *
 * \note            After this function is called, domain parameters
 *                  for various ECP groups can be loaded through the
 *                  mbedtls_ecp_group_load() or mbedtls_ecp_tls_read_group()
 *                  functions.
 */
void mbedtls_ecp_group_init( mbedtls_ecp_group *grp );

/**
 * \brief           This function initializes a key pair as an invalid one.
 *
 * \param key       The key pair to initialize.
 */
void mbedtls_ecp_keypair_init( mbedtls_ecp_keypair *key );

/**
 * \brief           This function frees the components of a point.
 *
 * \param pt        The point to free.
 */
void mbedtls_ecp_point_free( mbedtls_ecp_point *pt );

/**
 * \brief           This function frees the components of an ECP group.
 *
 * \param grp       The group to free. This may be \c NULL, in which
 *                  case this function returns immediately. If it is not
 *                  \c NULL, it must point to an initialized ECP group.
 */
void mbedtls_ecp_group_free( mbedtls_ecp_group *grp );

/**
 * \brief           This function frees the components of a key pair.
 *
 * \param key       The key pair to free. This may be \c NULL, in which
 *                  case this function returns immediately. If it is not
 *                  \c NULL, it must point to an initialized ECP key pair.
 */
void mbedtls_ecp_keypair_free( mbedtls_ecp_keypair *key );

# 652 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

/**
 * \brief           This function copies the contents of point \p Q into
 *                  point \p P.
 *
 * \param P         The destination point. This must be initialized.
 * \param Q         The source point. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code for other kinds of failure.
 */
int mbedtls_ecp_copy( mbedtls_ecp_point *P, const mbedtls_ecp_point *Q );

/**
 * \brief           This function copies the contents of group \p src into
 *                  group \p dst.
 *
 * \param dst       The destination group. This must be initialized.
 * \param src       The source group. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_group_copy( mbedtls_ecp_group *dst,
                            const mbedtls_ecp_group *src );

/**
 * \brief           This function sets a point to the point at infinity.
 *
 * \param pt        The point to set. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_set_zero( mbedtls_ecp_point *pt );

/**
 * \brief           This function checks if a point is the point at infinity.
 *
 * \param pt        The point to test. This must be initialized.
 *
 * \return          \c 1 if the point is zero.
 * \return          \c 0 if the point is non-zero.
 * \return          A negative error code on failure.
 */
int mbedtls_ecp_is_zero( mbedtls_ecp_point *pt );

/**
 * \brief           This function compares two points.
 *
 * \note            This assumes that the points are normalized. Otherwise,
 *                  they may compare as "not equal" even if they are.
 *
 * \param P         The first point to compare. This must be initialized.
 * \param Q         The second point to compare. This must be initialized.
 *
 * \return          \c 0 if the points are equal.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the points are not equal.
 */
int mbedtls_ecp_point_cmp( const mbedtls_ecp_point *P,
                           const mbedtls_ecp_point *Q );

/**
 * \brief           This function imports a non-zero point from two ASCII
 *                  strings.
 *
 * \param P         The destination point. This must be initialized.
 * \param radix     The numeric base of the input.
 * \param x         The first affine coordinate, as a null-terminated string.
 * \param y         The second affine coordinate, as a null-terminated string.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_MPI_XXX error code on failure.
 */
int mbedtls_ecp_point_read_string( mbedtls_ecp_point *P, int radix,
                           const char *x, const char *y );

/**
 * \brief           This function exports a point into unsigned binary data.
 *
 * \param grp       The group to which the point should belong.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param P         The point to export. This must be initialized.
 * \param format    The point format. This must be either
 *                  #MBEDTLS_ECP_PF_COMPRESSED or #MBEDTLS_ECP_PF_UNCOMPRESSED.
 *                  (For groups without these formats, this parameter is
 *                  ignored. But it still has to be either of the above
 *                  values.)
 * \param olen      The address at which to store the length of
 *                  the output in Bytes. This must not be \c NULL.
 * \param buf       The output buffer. This must be a writable buffer
 *                  of length \p buflen Bytes.
 * \param buflen    The length of the output buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the output buffer
 *                  is too small to hold the point.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the point format
 *                  or the export for the given group is not implemented.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_point_write_binary( const mbedtls_ecp_group *grp,
                                    const mbedtls_ecp_point *P,
                                    int format, size_t *olen,
                                    unsigned char *buf, size_t buflen );

/**
 * \brief           This function imports a point from unsigned binary data.
 *
 * \note            This function does not check that the point actually
 *                  belongs to the given group, see mbedtls_ecp_check_pubkey()
 *                  for that.
 *
 * \param grp       The group to which the point should belong.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param P         The destination context to import the point to.
 *                  This must be initialized.
 * \param buf       The input buffer. This must be a readable buffer
 *                  of length \p ilen Bytes.
 * \param ilen      The length of the input buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the input is invalid.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the import for the
 *                  given group is not implemented.
 */
int mbedtls_ecp_point_read_binary( const mbedtls_ecp_group *grp,
                                   mbedtls_ecp_point *P,
                                   const unsigned char *buf, size_t ilen );

/**
 * \brief           This function imports a point from a TLS ECPoint record.
 *
 * \note            On function return, \p *buf is updated to point immediately
 *                  after the ECPoint record.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The destination point.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the buffer.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_MPI_XXX error code on initialization
 *                  failure.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 */
int mbedtls_ecp_tls_read_point( const mbedtls_ecp_group *grp,
                                mbedtls_ecp_point *pt,
                                const unsigned char **buf, size_t len );

/**
 * \brief           This function exports a point as a TLS ECPoint record
 *                  defined in RFC 4492, Section 5.4.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The point to be exported. This must be initialized.
 * \param format    The point format to use. This must be either
 *                  #MBEDTLS_ECP_PF_COMPRESSED or #MBEDTLS_ECP_PF_UNCOMPRESSED.
 * \param olen      The address at which to store the length in Bytes
 *                  of the data written.
 * \param buf       The target buffer. This must be a writable buffer of
 *                  length \p blen Bytes.
 * \param blen      The length of the target buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the input is invalid.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the target buffer
 *                  is too small to hold the exported point.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_write_point( const mbedtls_ecp_group *grp,
                                 const mbedtls_ecp_point *pt,
                                 int format, size_t *olen,
                                 unsigned char *buf, size_t blen );

/**
 * \brief           This function sets up an ECP group context
 *                  from a standardized set of domain parameters.
 *
 * \note            The index should be a value of the NamedCurve enum,
 *                  as defined in <em>RFC-4492: Elliptic Curve Cryptography
 *                  (ECC) Cipher Suites for Transport Layer Security (TLS)</em>,
 *                  usually in the form of an \c MBEDTLS_ECP_DP_XXX macro.
 *
 * \param grp       The group context to setup. This must be initialized.
 * \param id        The identifier of the domain parameter set to load.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p id doesn't
 *                  correspond to a known group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_group_load( mbedtls_ecp_group *grp, mbedtls_ecp_group_id id );

/**
 * \brief           This function sets up an ECP group context from a TLS
 *                  ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \note            The read pointer \p buf is updated to point right after
 *                  the ECParameters record on exit.
 *
 * \param grp       The group context to setup. This must be initialized.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the input buffer \c *buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the group is not
 *                  recognized.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_read_group( mbedtls_ecp_group *grp,
                                const unsigned char **buf, size_t len );

/**
 * \brief           This function extracts an elliptic curve group ID from a
 *                  TLS ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \note            The read pointer \p buf is updated to point right after
 *                  the ECParameters record on exit.
 *
 * \param grp       The address at which to store the group id.
 *                  This must not be \c NULL.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the input buffer \c *buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the group is not
 *                  recognized.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_read_group_id( mbedtls_ecp_group_id *grp,
                                   const unsigned char **buf,
                                   size_t len );
/**
 * \brief           This function exports an elliptic curve as a TLS
 *                  ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \param grp       The ECP group to be exported.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param olen      The address at which to store the number of Bytes written.
 *                  This must not be \c NULL.
 * \param buf       The buffer to write to. This must be a writable buffer
 *                  of length \p blen Bytes.
 * \param blen      The length of the output buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the output
 *                  buffer is too small to hold the exported group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_write_group( const mbedtls_ecp_group *grp,
                                 size_t *olen,
                                 unsigned char *buf, size_t blen );

/**
 * \brief           This function performs a scalar multiplication of a point
 *                  by an integer: \p R = \p m * \p P.
 *
 *                  It is not thread-safe to use same group in multiple threads.
 *
 * \note            To prevent timing attacks, this function
 *                  executes the exact same sequence of base-field
 *                  operations for any valid \p m. It avoids any if-branch or
 *                  array index depending on the value of \p m. If also uses
 *                  \p f_rng to randomize some intermediate results.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply. This must be initialized.
 * \param P         The point to multiply. This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m is not a valid private
 *                  key, or \p P is not a valid public key.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_mul( mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             int (*f_rng)(void *, unsigned char *, size_t), void *p_rng );

/**
 * \brief           This function performs multiplication of a point by
 *                  an integer: \p R = \p m * \p P in a restartable way.
 *
 * \see             mbedtls_ecp_mul()
 *
 * \note            This function does the same as \c mbedtls_ecp_mul(), but
 *                  it can return early and restart according to the limit set
 *                  with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply. This must be initialized.
 * \param P         The point to multiply. This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 * \param rs_ctx    The restart context (NULL disables restart).
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m is not a valid private
 *                  key, or \p P is not a valid public key.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_mul_restartable( mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             int (*f_rng)(void *, unsigned char *, size_t), void *p_rng,
             mbedtls_ecp_restart_ctx *rs_ctx );


/**
 * \brief           This function performs multiplication and addition of two
 *                  points by integers: \p R = \p m * \p P + \p n * \p Q
 *
 *                  It is not thread-safe to use same group in multiple threads.
 *
 * \note            In contrast to mbedtls_ecp_mul(), this function does not
 *                  guarantee a constant execution flow and timing.
 *
 * \note            This function is only defined for short Weierstrass curves.
 *                  It may not be included in builds without any short
 *                  Weierstrass curve.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply \p P.
 *                  This must be initialized.
 * \param P         The point to multiply by \p m. This must be initialized.
 * \param n         The integer by which to multiply \p Q.
 *                  This must be initialized.
 * \param Q         The point to be multiplied by \p n.
 *                  This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m or \p n are not
 *                  valid private keys, or \p P or \p Q are not valid public
 *                  keys.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p grp does not
 *                  designate a short Weierstrass curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_muladd( mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             const mbedtls_mpi *n, const mbedtls_ecp_point *Q );

/**
 * \brief           This function performs multiplication and addition of two
 *                  points by integers: \p R = \p m * \p P + \p n * \p Q in a
 *                  restartable way.
 *
 * \see             \c mbedtls_ecp_muladd()
 *
 * \note            This function works the same as \c mbedtls_ecp_muladd(),
 *                  but it can return early and restart according to the limit
 *                  set with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \note            This function is only defined for short Weierstrass curves.
 *                  It may not be included in builds without any short
 *                  Weierstrass curve.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply \p P.
 *                  This must be initialized.
 * \param P         The point to multiply by \p m. This must be initialized.
 * \param n         The integer by which to multiply \p Q.
 *                  This must be initialized.
 * \param Q         The point to be multiplied by \p n.
 *                  This must be initialized.
 * \param rs_ctx    The restart context (NULL disables restart).
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m or \p n are not
 *                  valid private keys, or \p P or \p Q are not valid public
 *                  keys.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p grp does not
 *                  designate a short Weierstrass curve.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_muladd_restartable(
             mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             const mbedtls_mpi *n, const mbedtls_ecp_point *Q,
             mbedtls_ecp_restart_ctx *rs_ctx );


/**
 * \brief           This function checks that a point is a valid public key
 *                  on this curve.
 *
 *                  It only checks that the point is non-zero, has
 *                  valid coordinates and lies on the curve. It does not verify
 *                  that it is indeed a multiple of \p G. This additional
 *                  check is computationally more expensive, is not required
 *                  by standards, and should not be necessary if the group
 *                  used has a small cofactor. In particular, it is useless for
 *                  the NIST groups which all have a cofactor of 1.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure, to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group the point should belong to.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The point to check. This must be initialized.
 *
 * \return          \c 0 if the point is a valid public key.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if the point is not
 *                  a valid public key for the given curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_check_pubkey( const mbedtls_ecp_group *grp,
                              const mbedtls_ecp_point *pt );

/**
 * \brief           This function checks that an \p mbedtls_mpi is a
 *                  valid private key for this curve.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group the private key should belong to.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The integer to check. This must be initialized.
 *
 * \return          \c 0 if the point is a valid private key.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if the point is not a valid
 *                  private key for the given curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_check_privkey( const mbedtls_ecp_group *grp,
                               const mbedtls_mpi *d );

/**
 * \brief           This function generates a private key.
 *
 * \param grp       The ECP group to generate a private key for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The destination MPI (secret part). This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG parameter to be passed to \p f_rng. This may be
 *                  \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_privkey( const mbedtls_ecp_group *grp,
                     mbedtls_mpi *d,
                     int (*f_rng)(void *, unsigned char *, size_t),
                     void *p_rng );

/**
 * \brief           This function generates a keypair with a configurable base
 *                  point.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group to generate a key pair for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param G         The base point to use. This must be initialized
 *                  and belong to \p grp. It replaces the default base
 *                  point \c grp->G used by mbedtls_ecp_gen_keypair().
 * \param d         The destination MPI (secret part).
 *                  This must be initialized.
 * \param Q         The destination point (public part).
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_keypair_base( mbedtls_ecp_group *grp,
                                  const mbedtls_ecp_point *G,
                                  mbedtls_mpi *d, mbedtls_ecp_point *Q,
                                  int (*f_rng)(void *, unsigned char *, size_t),
                                  void *p_rng );

/**
 * \brief           This function generates an ECP keypair.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group to generate a key pair for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The destination MPI (secret part).
 *                  This must be initialized.
 * \param Q         The destination point (public part).
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_keypair( mbedtls_ecp_group *grp, mbedtls_mpi *d,
                             mbedtls_ecp_point *Q,
                             int (*f_rng)(void *, unsigned char *, size_t),
                             void *p_rng );

/**
 * \brief           This function generates an ECP key.
 *
 * \param grp_id    The ECP group identifier.
 * \param key       The destination key. This must be initialized.
 * \param f_rng     The RNG function to use. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_key( mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                         int (*f_rng)(void *, unsigned char *, size_t),
                         void *p_rng );

/**
 * \brief           This function reads an elliptic curve private key.
 *
 * \param grp_id    The ECP group identifier.
 * \param key       The destination key.
 * \param buf       The buffer containing the binary representation of the
 *                  key. (Big endian integer for Weierstrass curves, byte
 *                  string for Montgomery curves.)
 * \param buflen    The length of the buffer in bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY error if the key is
 *                  invalid.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the operation for
 *                  the group is not implemented.
 * \return          Another negative error code on different kinds of failure.
 */
int mbedtls_ecp_read_key( mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                          const unsigned char *buf, size_t buflen );

/**
 * \brief           This function exports an elliptic curve private key.
 *
 * \param key       The private key.
 * \param buf       The output buffer for containing the binary representation
 *                  of the key. (Big endian integer for Weierstrass curves, byte
 *                  string for Montgomery curves.)
 * \param buflen    The total length of the buffer in bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the \p key
                    representation is larger than the available space in \p buf.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the operation for
 *                  the group is not implemented.
 * \return          Another negative error code on different kinds of failure.
 */
int mbedtls_ecp_write_key( mbedtls_ecp_keypair *key,
                           unsigned char *buf, size_t buflen );

/**
 * \brief           This function checks that the keypair objects
 *                  \p pub and \p prv have the same group and the
 *                  same public point, and that the private key in
 *                  \p prv is consistent with the public key.
 *
 * \param pub       The keypair structure holding the public key. This
 *                  must be initialized. If it contains a private key, that
 *                  part is ignored.
 * \param prv       The keypair structure holding the full keypair.
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 *
 * \return          \c 0 on success, meaning that the keys are valid and match.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the keys are invalid or do not match.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or an \c MBEDTLS_ERR_MPI_XXX
 *                  error code on calculation failure.
 */
int mbedtls_ecp_check_pub_priv(
        const mbedtls_ecp_keypair *pub, const mbedtls_ecp_keypair *prv,
        int (*f_rng)(void *, unsigned char *, size_t), void *p_rng );

/**
 * \brief           This function exports generic key-pair parameters.
 *
 * \param key       The key pair to export from.
 * \param grp       Slot for exported ECP group.
 *                  It must point to an initialized ECP group.
 * \param d         Slot for the exported secret value.
 *                  It must point to an initialized mpi.
 * \param Q         Slot for the exported public value.
 *                  It must point to an initialized ECP point.
 *
 * \return          \c 0 on success,
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if key id doesn't
 *                  correspond to a known group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_export(const mbedtls_ecp_keypair *key, mbedtls_ecp_group *grp,
                       mbedtls_mpi *d, mbedtls_ecp_point *Q);

# 1319 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"





# 37 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h"
 /**
 * \file md.h
 *
 * \brief This file contains the generic message-digest wrapper.
 *
 * \author Adriaan de Jong <<EMAIL>>
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

# 28 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h"

# 30 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h"

# 32 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"
/**
 * \file platform_util.h
 *
 * \brief Common and shared functions used by multiple modules in the Mbed TLS
 *        library.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 27 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

# 29 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"









/* Internal macros meant to be called only from within the library. */



/* Internal helper macros for deprecating API constants. */
# 58 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

/* Implementation of the check-return facility.
 * See the user documentation in mbedtls_config.h.
 *
 * Do not use this macro directly to annotate function: instead,
 * use one of MBEDTLS_CHECK_RETURN_CRITICAL or MBEDTLS_CHECK_RETURN_TYPICAL
 * depending on how important it is to check the return value.
 */
# 76 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

/** Critical-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be checked in all applications.
 * Omitting the check is very likely to indicate a bug in the application
 * and will result in a compile-time warning if #MBEDTLS_CHECK_RETURN
 * is implemented for the compiler in use.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro may be added to more functions in the future.
 *        Such an extension is not considered an API break, provided that
 *        there are near-unavoidable circumstances under which the function
 *        can fail. For example, signature/MAC/AEAD verification functions,
 *        and functions that require a random generator, are considered
 *        return-check-critical.
 */


/** Ordinary-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be generally be checked in portable
 * applications. Omitting the check will result in a compile-time warning if
 * #MBEDTLS_CHECK_RETURN is implemented for the compiler in use and
 * #MBEDTLS_CHECK_RETURN_WARNING is enabled in the compile-time configuration.
 *
 * You can use #MBEDTLS_IGNORE_RETURN to explicitly ignore the return value
 * of a function that is annotated with #MBEDTLS_CHECK_RETURN.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro will be added to more functions in the future.
 *        Eventually this should appear before most functions returning
 *        an error code (as \c int in the \c mbedtls_xxx API or
 *        as ::psa_status_t in the \c psa_xxx API).
 */






/** Benign-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that it is rarely useful to check its return value.
 *
 * This macro has an empty expansion. It exists for documentation purposes:
 * a #MBEDTLS_CHECK_RETURN_OPTIONAL annotation indicates that the function
 * has been analyzed for return-check usefulness, whereas the lack of
 * an annotation indicates that the function has not been analyzed and its
 * return-check usefulness is unknown.
 */


/** \def MBEDTLS_IGNORE_RETURN
 *
 * Call this macro with one argument, a function call, to suppress a warning
 * from #MBEDTLS_CHECK_RETURN due to that function call.
 */

/* GCC doesn't silence the warning with just (void)(result).
 * (void)!(result) is known to work up at least up to GCC 10, as well
 * as with Clang and MSVC.
 *
 * https://gcc.gnu.org/onlinedocs/gcc-3.4.6/gcc/Non_002dbugs.html
 * https://stackoverflow.com/questions/40576003/ignoring-warning-wunused-result
 * https://gcc.gnu.org/bugzilla/show_bug.cgi?id=66425#c34
 */



/**
 * \brief       Securely zeroize a buffer
 *
 *              The function is meant to wipe the data contained in a buffer so
 *              that it can no longer be recovered even if the program memory
 *              is later compromised. Call this function on sensitive data
 *              stored on the stack before returning from a function, and on
 *              sensitive data stored on the heap before freeing the heap
 *              object.
 *
 *              It is extremely difficult to guarantee that calls to
 *              mbedtls_platform_zeroize() are not removed by aggressive
 *              compiler optimizations in a portable way. For this reason, Mbed
 *              TLS provides the configuration option
 *              MBEDTLS_PLATFORM_ZEROIZE_ALT, which allows users to configure
 *              mbedtls_platform_zeroize() to use a suitable implementation for
 *              their platform and needs
 *
 * \param buf   Buffer to be zeroized
 * \param len   Length of the buffer in bytes
 *
 */
void mbedtls_platform_zeroize( void *buf, size_t len );

# 202 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"





# 33 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h"

/** The selected feature is not available. */

/** Bad input parameters to function. */

/** Failed to allocate memory. */

/** Opening or reading of file failed. */






/**
 * \brief     Supported message digests.
 *
 * \warning   MD5 and SHA-1 are considered weak message digests and
 *            their use constitutes a security risk. We recommend considering
 *            stronger message digests instead.
 *
 */
typedef enum {
    MBEDTLS_MD_NONE=0,    /**< None. */
    MBEDTLS_MD_MD5,       /**< The MD5 message digest. */
    MBEDTLS_MD_SHA1,      /**< The SHA-1 message digest. */
    MBEDTLS_MD_SHA224,    /**< The SHA-224 message digest. */
    MBEDTLS_MD_SHA256,    /**< The SHA-256 message digest. */
    MBEDTLS_MD_SHA384,    /**< The SHA-384 message digest. */
    MBEDTLS_MD_SHA512,    /**< The SHA-512 message digest. */
    MBEDTLS_MD_RIPEMD160, /**< The RIPEMD-160 message digest. */
} mbedtls_md_type_t;













/**
 * Opaque struct.
 *
 * Constructed using either #mbedtls_md_info_from_string or
 * #mbedtls_md_info_from_type.
 *
 * Fields can be accessed with #mbedtls_md_get_size,
 * #mbedtls_md_get_type and #mbedtls_md_get_name.
 */
/* Defined internally in library/md_wrap.h. */
typedef struct mbedtls_md_info_t mbedtls_md_info_t;

/**
 * The generic message-digest context.
 */
typedef struct mbedtls_md_context_t
{
    /** Information about the associated message digest. */
    const mbedtls_md_info_t *md_info;

    /** The digest-specific context. */
    void *md_ctx;

    /** The HMAC part of the context. */
    void *hmac_ctx;
} mbedtls_md_context_t;

/**
 * \brief           This function returns the list of digests supported by the
 *                  generic digest module.
 *
 * \note            The list starts with the strongest available hashes.
 *
 * \return          A statically allocated array of digests. Each element
 *                  in the returned list is an integer belonging to the
 *                  message-digest enumeration #mbedtls_md_type_t.
 *                  The last entry is 0.
 */
const int *mbedtls_md_list( void );

/**
 * \brief           This function returns the message-digest information
 *                  associated with the given digest name.
 *
 * \param md_name   The name of the digest to search for.
 *
 * \return          The message-digest information associated with \p md_name.
 * \return          NULL if the associated message-digest information is not found.
 */
const mbedtls_md_info_t *mbedtls_md_info_from_string( const char *md_name );

/**
 * \brief           This function returns the message-digest information
 *                  associated with the given digest type.
 *
 * \param md_type   The type of digest to search for.
 *
 * \return          The message-digest information associated with \p md_type.
 * \return          NULL if the associated message-digest information is not found.
 */
const mbedtls_md_info_t *mbedtls_md_info_from_type( mbedtls_md_type_t md_type );

/**
 * \brief           This function returns the message-digest information
 *                  from the given context.
 *
 * \param ctx       The context from which to extract the information.
 *                  This must be initialized (or \c NULL).
 *
 * \return          The message-digest information associated with \p ctx.
 * \return          \c NULL if \p ctx is \c NULL.
 */
const mbedtls_md_info_t *mbedtls_md_info_from_ctx(
                                        const mbedtls_md_context_t *ctx );

/**
 * \brief           This function initializes a message-digest context without
 *                  binding it to a particular message-digest algorithm.
 *
 *                  This function should always be called first. It prepares the
 *                  context for mbedtls_md_setup() for binding it to a
 *                  message-digest algorithm.
 */
void mbedtls_md_init( mbedtls_md_context_t *ctx );

/**
 * \brief           This function clears the internal structure of \p ctx and
 *                  frees any embedded internal structure, but does not free
 *                  \p ctx itself.
 *
 *                  If you have called mbedtls_md_setup() on \p ctx, you must
 *                  call mbedtls_md_free() when you are no longer using the
 *                  context.
 *                  Calling this function if you have previously
 *                  called mbedtls_md_init() and nothing else is optional.
 *                  You must not call this function if you have not called
 *                  mbedtls_md_init().
 */
void mbedtls_md_free( mbedtls_md_context_t *ctx );


/**
 * \brief           This function selects the message digest algorithm to use,
 *                  and allocates internal structures.
 *
 *                  It should be called after mbedtls_md_init() or
 *                  mbedtls_md_free(). Makes it necessary to call
 *                  mbedtls_md_free() later.
 *
 * \param ctx       The context to set up.
 * \param md_info   The information structure of the message-digest algorithm
 *                  to use.
 * \param hmac      Defines if HMAC is used. 0: HMAC is not used (saves some memory),
 *                  or non-zero: HMAC is used with this context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 * \return          #MBEDTLS_ERR_MD_ALLOC_FAILED on memory-allocation failure.
 */

int mbedtls_md_setup( mbedtls_md_context_t *ctx, const mbedtls_md_info_t *md_info, int hmac );

/**
 * \brief           This function clones the state of an message-digest
 *                  context.
 *
 * \note            You must call mbedtls_md_setup() on \c dst before calling
 *                  this function.
 *
 * \note            The two contexts must have the same type,
 *                  for example, both are SHA-256.
 *
 * \warning         This function clones the message-digest state, not the
 *                  HMAC state.
 *
 * \param dst       The destination context.
 * \param src       The context to be cloned.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification failure.
 */

int mbedtls_md_clone( mbedtls_md_context_t *dst,
                      const mbedtls_md_context_t *src );

/**
 * \brief           This function extracts the message-digest size from the
 *                  message-digest information structure.
 *
 * \param md_info   The information structure of the message-digest algorithm
 *                  to use.
 *
 * \return          The size of the message-digest output in Bytes.
 */
unsigned char mbedtls_md_get_size( const mbedtls_md_info_t *md_info );

/**
 * \brief           This function extracts the message-digest type from the
 *                  message-digest information structure.
 *
 * \param md_info   The information structure of the message-digest algorithm
 *                  to use.
 *
 * \return          The type of the message digest.
 */
mbedtls_md_type_t mbedtls_md_get_type( const mbedtls_md_info_t *md_info );

/**
 * \brief           This function extracts the message-digest name from the
 *                  message-digest information structure.
 *
 * \param md_info   The information structure of the message-digest algorithm
 *                  to use.
 *
 * \return          The name of the message digest.
 */
const char *mbedtls_md_get_name( const mbedtls_md_info_t *md_info );

/**
 * \brief           This function starts a message-digest computation.
 *
 *                  You must call this function after setting up the context
 *                  with mbedtls_md_setup(), and before passing data with
 *                  mbedtls_md_update().
 *
 * \param ctx       The generic message-digest context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 */

int mbedtls_md_starts( mbedtls_md_context_t *ctx );

/**
 * \brief           This function feeds an input buffer into an ongoing
 *                  message-digest computation.
 *
 *                  You must call mbedtls_md_starts() before calling this
 *                  function. You may call this function multiple times.
 *                  Afterwards, call mbedtls_md_finish().
 *
 * \param ctx       The generic message-digest context.
 * \param input     The buffer holding the input data.
 * \param ilen      The length of the input data.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 */

int mbedtls_md_update( mbedtls_md_context_t *ctx, const unsigned char *input, size_t ilen );

/**
 * \brief           This function finishes the digest operation,
 *                  and writes the result to the output buffer.
 *
 *                  Call this function after a call to mbedtls_md_starts(),
 *                  followed by any number of calls to mbedtls_md_update().
 *                  Afterwards, you may either clear the context with
 *                  mbedtls_md_free(), or call mbedtls_md_starts() to reuse
 *                  the context for another digest operation with the same
 *                  algorithm.
 *
 * \param ctx       The generic message-digest context.
 * \param output    The buffer for the generic message-digest checksum result.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 */

int mbedtls_md_finish( mbedtls_md_context_t *ctx, unsigned char *output );

/**
 * \brief          This function calculates the message-digest of a buffer,
 *                 with respect to a configurable message-digest algorithm
 *                 in a single call.
 *
 *                 The result is calculated as
 *                 Output = message_digest(input buffer).
 *
 * \param md_info  The information structure of the message-digest algorithm
 *                 to use.
 * \param input    The buffer holding the data.
 * \param ilen     The length of the input data.
 * \param output   The generic message-digest checksum result.
 *
 * \return         \c 0 on success.
 * \return         #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                 failure.
 */

int mbedtls_md( const mbedtls_md_info_t *md_info, const unsigned char *input, size_t ilen,
        unsigned char *output );

# 357 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md.h"

/**
 * \brief           This function sets the HMAC key and prepares to
 *                  authenticate a new message.
 *
 *                  Call this function after mbedtls_md_setup(), to use
 *                  the MD context for an HMAC calculation, then call
 *                  mbedtls_md_hmac_update() to provide the input data, and
 *                  mbedtls_md_hmac_finish() to get the HMAC value.
 *
 * \param ctx       The message digest context containing an embedded HMAC
 *                  context.
 * \param key       The HMAC secret key.
 * \param keylen    The length of the HMAC key in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 */

int mbedtls_md_hmac_starts( mbedtls_md_context_t *ctx, const unsigned char *key,
                    size_t keylen );

/**
 * \brief           This function feeds an input buffer into an ongoing HMAC
 *                  computation.
 *
 *                  Call mbedtls_md_hmac_starts() or mbedtls_md_hmac_reset()
 *                  before calling this function.
 *                  You may call this function multiple times to pass the
 *                  input piecewise.
 *                  Afterwards, call mbedtls_md_hmac_finish().
 *
 * \param ctx       The message digest context containing an embedded HMAC
 *                  context.
 * \param input     The buffer holding the input data.
 * \param ilen      The length of the input data.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 */

int mbedtls_md_hmac_update( mbedtls_md_context_t *ctx, const unsigned char *input,
                    size_t ilen );

/**
 * \brief           This function finishes the HMAC operation, and writes
 *                  the result to the output buffer.
 *
 *                  Call this function after mbedtls_md_hmac_starts() and
 *                  mbedtls_md_hmac_update() to get the HMAC value. Afterwards
 *                  you may either call mbedtls_md_free() to clear the context,
 *                  or call mbedtls_md_hmac_reset() to reuse the context with
 *                  the same HMAC key.
 *
 * \param ctx       The message digest context containing an embedded HMAC
 *                  context.
 * \param output    The generic HMAC checksum result.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 */

int mbedtls_md_hmac_finish( mbedtls_md_context_t *ctx, unsigned char *output);

/**
 * \brief           This function prepares to authenticate a new message with
 *                  the same key as the previous HMAC operation.
 *
 *                  You may call this function after mbedtls_md_hmac_finish().
 *                  Afterwards call mbedtls_md_hmac_update() to pass the new
 *                  input.
 *
 * \param ctx       The message digest context containing an embedded HMAC
 *                  context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                  failure.
 */

int mbedtls_md_hmac_reset( mbedtls_md_context_t *ctx );

/**
 * \brief          This function calculates the full generic HMAC
 *                 on the input buffer with the provided key.
 *
 *                 The function allocates the context, performs the
 *                 calculation, and frees the context.
 *
 *                 The HMAC result is calculated as
 *                 output = generic HMAC(hmac key, input buffer).
 *
 * \param md_info  The information structure of the message-digest algorithm
 *                 to use.
 * \param key      The HMAC secret key.
 * \param keylen   The length of the HMAC secret key in Bytes.
 * \param input    The buffer holding the input data.
 * \param ilen     The length of the input data.
 * \param output   The generic HMAC result.
 *
 * \return         \c 0 on success.
 * \return         #MBEDTLS_ERR_MD_BAD_INPUT_DATA on parameter-verification
 *                 failure.
 */

int mbedtls_md_hmac( const mbedtls_md_info_t *md_info, const unsigned char *key, size_t keylen,
                const unsigned char *input, size_t ilen,
                unsigned char *output );

/* Internal use */

int mbedtls_md_process( mbedtls_md_context_t *ctx, const unsigned char *data );





# 38 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"

/**
 * \brief           Maximum ECDSA signature size for a given curve bit size
 *
 * \param bits      Curve size in bits
 * \return          Maximum signature size in bytes
 *
 * \note            This macro returns a compile-time constant if its argument
 *                  is one. It may evaluate its argument multiple times.
 */
/*
 *     Ecdsa-Sig-Value ::= SEQUENCE {
 *         r       INTEGER,
 *         s       INTEGER
 *     }
 *
 * For each of r and s, the value (V) may include an extra initial "0" bit.
 */





/** The maximal size of an ECDSA signature in Bytes. */






/**
 * \brief           The ECDSA context structure.
 *
 * \warning         Performing multiple operations concurrently on the same
 *                  ECDSA context is not supported; objects of this type
 *                  should not be shared between multiple threads.
 */
typedef mbedtls_ecp_keypair mbedtls_ecdsa_context;

# 117 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"

/* Now we can declare functions that take a pointer to that */
typedef void mbedtls_ecdsa_restart_ctx;



/**
 * \brief          This function checks whether a given group can be used
 *                 for ECDSA.
 *
 * \param gid      The ECP group ID to check.
 *
 * \return         \c 1 if the group can be used, \c 0 otherwise
 */
int mbedtls_ecdsa_can_do( mbedtls_ecp_group_id gid );

/**
 * \brief           This function computes the ECDSA signature of a
 *                  previously-hashed message.
 *
 * \note            The deterministic version implemented in
 *                  mbedtls_ecdsa_sign_det_ext() is usually preferred.
 *
 * \note            If the bitlength of the message hash is larger than the
 *                  bitlength of the group order, then the hash is truncated
 *                  as defined in <em>Standards for Efficient Cryptography Group
 *                  (SECG): SEC1 Elliptic Curve Cryptography</em>, section
 *                  4.1.3, step 5.
 *
 * \see             ecp.h
 *
 * \param grp       The context for the elliptic curve to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param r         The MPI context in which to store the first part
 *                  the signature. This must be initialized.
 * \param s         The MPI context in which to store the second part
 *                  the signature. This must be initialized.
 * \param d         The private signing key. This must be initialized.
 * \param buf       The content to be signed. This is usually the hash of
 *                  the original data to be signed. This must be a readable
 *                  buffer of length \p blen Bytes. It may be \c NULL if
 *                  \p blen is zero.
 * \param blen      The length of \p buf in Bytes.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be
 *                  \c NULL if \p f_rng doesn't need a context parameter.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX
 *                  or \c MBEDTLS_MPI_XXX error code on failure.
 */
int mbedtls_ecdsa_sign( mbedtls_ecp_group *grp, mbedtls_mpi *r, mbedtls_mpi *s,
                const mbedtls_mpi *d, const unsigned char *buf, size_t blen,
                int (*f_rng)(void *, unsigned char *, size_t), void *p_rng );

# 220 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"

/**
 * \brief           This function verifies the ECDSA signature of a
 *                  previously-hashed message.
 *
 * \note            If the bitlength of the message hash is larger than the
 *                  bitlength of the group order, then the hash is truncated as
 *                  defined in <em>Standards for Efficient Cryptography Group
 *                  (SECG): SEC1 Elliptic Curve Cryptography</em>, section
 *                  4.1.4, step 3.
 *
 * \see             ecp.h
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param buf       The hashed content that was signed. This must be a readable
 *                  buffer of length \p blen Bytes. It may be \c NULL if
 *                  \p blen is zero.
 * \param blen      The length of \p buf in Bytes.
 * \param Q         The public key to use for verification. This must be
 *                  initialized and setup.
 * \param r         The first integer of the signature.
 *                  This must be initialized.
 * \param s         The second integer of the signature.
 *                  This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the signature
 *                  is invalid.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX
 *                  error code on failure for any other reason.
 */
int mbedtls_ecdsa_verify( mbedtls_ecp_group *grp,
                          const unsigned char *buf, size_t blen,
                          const mbedtls_ecp_point *Q, const mbedtls_mpi *r,
                          const mbedtls_mpi *s);

/**
 * \brief           This function computes the ECDSA signature and writes it
 *                  to a buffer, serialized as defined in <em>RFC-4492:
 *                  Elliptic Curve Cryptography (ECC) Cipher Suites for
 *                  Transport Layer Security (TLS)</em>.
 *
 * \warning         It is not thread-safe to use the same context in
 *                  multiple threads.
 *
 * \note            The deterministic version is used if
 *                  #MBEDTLS_ECDSA_DETERMINISTIC is defined. For more
 *                  information, see <em>RFC-6979: Deterministic Usage
 *                  of the Digital Signature Algorithm (DSA) and Elliptic
 *                  Curve Digital Signature Algorithm (ECDSA)</em>.
 *
 * \note            If the bitlength of the message hash is larger than the
 *                  bitlength of the group order, then the hash is truncated as
 *                  defined in <em>Standards for Efficient Cryptography Group
 *                  (SECG): SEC1 Elliptic Curve Cryptography</em>, section
 *                  4.1.3, step 5.
 *
 * \see             ecp.h
 *
 * \param ctx       The ECDSA context to use. This must be initialized
 *                  and have a group and private key bound to it, for example
 *                  via mbedtls_ecdsa_genkey() or mbedtls_ecdsa_from_keypair().
 * \param md_alg    The message digest that was used to hash the message.
 * \param hash      The message hash to be signed. This must be a readable
 *                  buffer of length \p blen Bytes.
 * \param hlen      The length of the hash \p hash in Bytes.
 * \param sig       The buffer to which to write the signature. This must be a
 *                  writable buffer of length at least twice as large as the
 *                  size of the curve used, plus 9. For example, 73 Bytes if
 *                  a 256-bit curve is used. A buffer length of
 *                  #MBEDTLS_ECDSA_MAX_LEN is always safe.
 * \param sig_size  The size of the \p sig buffer in bytes.
 * \param slen      The address at which to store the actual length of
 *                  the signature written. Must not be \c NULL.
 * \param f_rng     The RNG function. This must not be \c NULL if
 *                  #MBEDTLS_ECDSA_DETERMINISTIC is unset. Otherwise,
 *                  it is used only for blinding and may be set to \c NULL, but
 *                  doing so is DEPRECATED.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be
 *                  \c NULL if \p f_rng is \c NULL or doesn't use a context.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX, \c MBEDTLS_ERR_MPI_XXX or
 *                  \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_ecdsa_write_signature( mbedtls_ecdsa_context *ctx,
                                   mbedtls_md_type_t md_alg,
                           const unsigned char *hash, size_t hlen,
                           unsigned char *sig, size_t sig_size, size_t *slen,
                           int (*f_rng)(void *, unsigned char *, size_t),
                           void *p_rng );

/**
 * \brief           This function computes the ECDSA signature and writes it
 *                  to a buffer, in a restartable way.
 *
 * \see             \c mbedtls_ecdsa_write_signature()
 *
 * \note            This function is like \c mbedtls_ecdsa_write_signature()
 *                  but it can return early and restart according to the limit
 *                  set with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \param ctx       The ECDSA context to use. This must be initialized
 *                  and have a group and private key bound to it, for example
 *                  via mbedtls_ecdsa_genkey() or mbedtls_ecdsa_from_keypair().
 * \param md_alg    The message digest that was used to hash the message.
 * \param hash      The message hash to be signed. This must be a readable
 *                  buffer of length \p blen Bytes.
 * \param hlen      The length of the hash \p hash in Bytes.
 * \param sig       The buffer to which to write the signature. This must be a
 *                  writable buffer of length at least twice as large as the
 *                  size of the curve used, plus 9. For example, 73 Bytes if
 *                  a 256-bit curve is used. A buffer length of
 *                  #MBEDTLS_ECDSA_MAX_LEN is always safe.
 * \param sig_size  The size of the \p sig buffer in bytes.
 * \param slen      The address at which to store the actual length of
 *                  the signature written. Must not be \c NULL.
 * \param f_rng     The RNG function. This must not be \c NULL if
 *                  #MBEDTLS_ECDSA_DETERMINISTIC is unset. Otherwise,
 *                  it is unused and may be set to \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be
 *                  \c NULL if \p f_rng is \c NULL or doesn't use a context.
 * \param rs_ctx    The restart context to use. This may be \c NULL to disable
 *                  restarting. If it is not \c NULL, it must point to an
 *                  initialized restart context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another \c MBEDTLS_ERR_ECP_XXX, \c MBEDTLS_ERR_MPI_XXX or
 *                  \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_ecdsa_write_signature_restartable( mbedtls_ecdsa_context *ctx,
                           mbedtls_md_type_t md_alg,
                           const unsigned char *hash, size_t hlen,
                           unsigned char *sig, size_t sig_size, size_t *slen,
                           int (*f_rng)(void *, unsigned char *, size_t),
                           void *p_rng,
                           mbedtls_ecdsa_restart_ctx *rs_ctx );

/**
 * \brief           This function reads and verifies an ECDSA signature.
 *
 * \note            If the bitlength of the message hash is larger than the
 *                  bitlength of the group order, then the hash is truncated as
 *                  defined in <em>Standards for Efficient Cryptography Group
 *                  (SECG): SEC1 Elliptic Curve Cryptography</em>, section
 *                  4.1.4, step 3.
 *
 * \see             ecp.h
 *
 * \param ctx       The ECDSA context to use. This must be initialized
 *                  and have a group and public key bound to it.
 * \param hash      The message hash that was signed. This must be a readable
 *                  buffer of length \p size Bytes.
 * \param hlen      The size of the hash \p hash.
 * \param sig       The signature to read and verify. This must be a readable
 *                  buffer of length \p slen Bytes.
 * \param slen      The size of \p sig in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if signature is invalid.
 * \return          #MBEDTLS_ERR_ECP_SIG_LEN_MISMATCH if there is a valid
 *                  signature in \p sig, but its length is less than \p siglen.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_ERR_MPI_XXX
 *                  error code on failure for any other reason.
 */
int mbedtls_ecdsa_read_signature( mbedtls_ecdsa_context *ctx,
                          const unsigned char *hash, size_t hlen,
                          const unsigned char *sig, size_t slen );

/**
 * \brief           This function reads and verifies an ECDSA signature,
 *                  in a restartable way.
 *
 * \see             \c mbedtls_ecdsa_read_signature()
 *
 * \note            This function is like \c mbedtls_ecdsa_read_signature()
 *                  but it can return early and restart according to the limit
 *                  set with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \param ctx       The ECDSA context to use. This must be initialized
 *                  and have a group and public key bound to it.
 * \param hash      The message hash that was signed. This must be a readable
 *                  buffer of length \p size Bytes.
 * \param hlen      The size of the hash \p hash.
 * \param sig       The signature to read and verify. This must be a readable
 *                  buffer of length \p slen Bytes.
 * \param slen      The size of \p sig in Bytes.
 * \param rs_ctx    The restart context to use. This may be \c NULL to disable
 *                  restarting. If it is not \c NULL, it must point to an
 *                  initialized restart context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if signature is invalid.
 * \return          #MBEDTLS_ERR_ECP_SIG_LEN_MISMATCH if there is a valid
 *                  signature in \p sig, but its length is less than \p siglen.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_ERR_MPI_XXX
 *                  error code on failure for any other reason.
 */
int mbedtls_ecdsa_read_signature_restartable( mbedtls_ecdsa_context *ctx,
                          const unsigned char *hash, size_t hlen,
                          const unsigned char *sig, size_t slen,
                          mbedtls_ecdsa_restart_ctx *rs_ctx );

/**
 * \brief          This function generates an ECDSA keypair on the given curve.
 *
 * \see            ecp.h
 *
 * \param ctx      The ECDSA context to store the keypair in.
 *                 This must be initialized.
 * \param gid      The elliptic curve to use. One of the various
 *                 \c MBEDTLS_ECP_DP_XXX macros depending on configuration.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG context to be passed to \p f_rng. This may be
 *                 \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return         \c 0 on success.
 * \return         An \c MBEDTLS_ERR_ECP_XXX code on failure.
 */
int mbedtls_ecdsa_genkey( mbedtls_ecdsa_context *ctx, mbedtls_ecp_group_id gid,
                  int (*f_rng)(void *, unsigned char *, size_t), void *p_rng );

/**
 * \brief           This function sets up an ECDSA context from an EC key pair.
 *
 * \see             ecp.h
 *
 * \param ctx       The ECDSA context to setup. This must be initialized.
 * \param key       The EC key to use. This must be initialized and hold
 *                  a private-public key pair or a public key. In the former
 *                  case, the ECDSA context may be used for signature creation
 *                  and verification after this call. In the latter case, it
 *                  may be used for signature verification.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX code on failure.
 */
int mbedtls_ecdsa_from_keypair( mbedtls_ecdsa_context *ctx,
                                const mbedtls_ecp_keypair *key );

/**
 * \brief           This function initializes an ECDSA context.
 *
 * \param ctx       The ECDSA context to initialize.
 *                  This must not be \c NULL.
 */
void mbedtls_ecdsa_init( mbedtls_ecdsa_context *ctx );

/**
 * \brief           This function frees an ECDSA context.
 *
 * \param ctx       The ECDSA context to free. This may be \c NULL,
 *                  in which case this function does nothing. If it
 *                  is not \c NULL, it must be initialized.
 */
void mbedtls_ecdsa_free( mbedtls_ecdsa_context *ctx );

# 501 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecdsa.h"





# 31 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1write.h"
/**
 * \file asn1write.h
 *
 * \brief ASN.1 buffer writing functionality
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 26 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1write.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h"
/**
 * \file asn1.h
 *
 * \brief Generic ASN.1 parsing
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
# 25 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h"

# 27 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h"

# 29 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h"

# 32 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h"


/**
 * \addtogroup asn1_module
 * \{
 */

/**
 * \name ASN1 Error codes
 * These error codes are OR'ed to X509 error codes for
 * higher error granularity.
 * ASN1 is a standard to specify data structures.
 * \{
 */
/** Out of data when parsing an ASN1 data structure. */

/** ASN1 tag was of an unexpected value. */

/** Error when trying to determine the length or invalid length. */

/** Actual length differs from expected length. */

/** Data is invalid. */

/** Memory allocation failed */

/** Buffer too small when writing ASN.1 data structure. */


/** \} name ASN1 Error codes */

/**
 * \name DER constants
 * These constants comply with the DER encoded ASN.1 type tags.
 * DER encoding uses hexadecimal representation.
 * An example DER sequence is:\n
 * - 0x02 -- tag indicating INTEGER
 * - 0x01 -- length in octets
 * - 0x05 -- value
 * Such sequences are typically read into \c ::mbedtls_x509_buf.
 * \{
 */
# 94 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h"

/* Slightly smaller way to check if tag is a string tag
 * compared to canonical implementation. */
# 106 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1.h"

/*
 * Bit masks for each of the components of an ASN.1 tag as specified in
 * ITU X.690 (08/2015), section 8.1 "General rules for encoding",
 * paragraph 8.1.2.2:
 *
 * Bit  8     7   6   5          1
 *     +-------+-----+------------+
 *     | Class | P/C | Tag number |
 *     +-------+-----+------------+
 */




/** \} name DER constants */

/** Returns the size of the binary string, without the trailing \\0 */


/**
 * Compares an mbedtls_asn1_buf structure to a reference OID.
 *
 * Only works for 'defined' oid_str values (MBEDTLS_OID_HMAC_SHA1), you cannot use a
 * 'unsigned char *oid' here!
 */












/**
 * \name Functions to parse ASN.1 data structures
 * \{
 */

/**
 * Type-length-value structure that allows for ASN1 using DER.
 */
typedef struct mbedtls_asn1_buf
{
    int tag;                /**< ASN1 type, e.g. MBEDTLS_ASN1_UTF8_STRING. */
    size_t len;             /**< ASN1 length, in octets. */
    unsigned char *p;       /**< ASN1 data, e.g. in ASCII. */
}
mbedtls_asn1_buf;

/**
 * Container for ASN1 bit strings.
 */
typedef struct mbedtls_asn1_bitstring
{
    size_t len;                 /**< ASN1 length, in octets. */
    unsigned char unused_bits;  /**< Number of unused bits at the end of the string */
    unsigned char *p;           /**< Raw ASN1 data for the bit string */
}
mbedtls_asn1_bitstring;

/**
 * Container for a sequence of ASN.1 items
 */
typedef struct mbedtls_asn1_sequence
{
    mbedtls_asn1_buf buf;                   /**< Buffer containing the given ASN.1 item. */

    /** The next entry in the sequence.
     *
     * The details of memory management for sequences are not documented and
     * may change in future versions. Set this field to \p NULL when
     * initializing a structure, and do not modify it except via Mbed TLS
     * library functions.
     */
    struct mbedtls_asn1_sequence *next;
}
mbedtls_asn1_sequence;

/**
 * Container for a sequence or list of 'named' ASN.1 data items
 */
typedef struct mbedtls_asn1_named_data
{
    mbedtls_asn1_buf oid;                   /**< The object identifier. */
    mbedtls_asn1_buf val;                   /**< The named value. */

    /** The next entry in the sequence.
     *
     * The details of memory management for named data sequences are not
     * documented and may change in future versions. Set this field to \p NULL
     * when initializing a structure, and do not modify it except via Mbed TLS
     * library functions.
     */
    struct mbedtls_asn1_named_data *next;

    /** Merge next item into the current one?
     *
     * This field exists for the sake of Mbed TLS's X.509 certificate parsing
     * code and may change in future versions of the library.
     */
    unsigned char next_merged;
}
mbedtls_asn1_named_data;

/**
 * \brief       Get the length of an ASN.1 element.
 *              Updates the pointer to immediately behind the length.
 *
 * \param p     On entry, \c *p points to the first byte of the length,
 *              i.e. immediately after the tag.
 *              On successful completion, \c *p points to the first byte
 *              after the length, i.e. the first byte of the content.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param len   On successful completion, \c *len contains the length
 *              read from the ASN.1 input.
 *
 * \return      0 if successful.
 * \return      #MBEDTLS_ERR_ASN1_OUT_OF_DATA if the ASN.1 element
 *              would end beyond \p end.
 * \return      #MBEDTLS_ERR_ASN1_INVALID_LENGTH if the length is unparsable.
 */
int mbedtls_asn1_get_len( unsigned char **p,
                          const unsigned char *end,
                          size_t *len );

/**
 * \brief       Get the tag and length of the element.
 *              Check for the requested tag.
 *              Updates the pointer to immediately behind the tag and length.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              after the length, i.e. the first byte of the content.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param len   On successful completion, \c *len contains the length
 *              read from the ASN.1 input.
 * \param tag   The expected tag.
 *
 * \return      0 if successful.
 * \return      #MBEDTLS_ERR_ASN1_UNEXPECTED_TAG if the data does not start
 *              with the requested tag.
 * \return      #MBEDTLS_ERR_ASN1_OUT_OF_DATA if the ASN.1 element
 *              would end beyond \p end.
 * \return      #MBEDTLS_ERR_ASN1_INVALID_LENGTH if the length is unparsable.
 */
int mbedtls_asn1_get_tag( unsigned char **p,
                          const unsigned char *end,
                          size_t *len, int tag );

/**
 * \brief       Retrieve a boolean ASN.1 tag and its value.
 *              Updates the pointer to immediately behind the full tag.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              beyond the ASN.1 element.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param val   On success, the parsed value (\c 0 or \c 1).
 *
 * \return      0 if successful.
 * \return      An ASN.1 error code if the input does not start with
 *              a valid ASN.1 BOOLEAN.
 */
int mbedtls_asn1_get_bool( unsigned char **p,
                           const unsigned char *end,
                           int *val );

/**
 * \brief       Retrieve an integer ASN.1 tag and its value.
 *              Updates the pointer to immediately behind the full tag.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              beyond the ASN.1 element.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param val   On success, the parsed value.
 *
 * \return      0 if successful.
 * \return      An ASN.1 error code if the input does not start with
 *              a valid ASN.1 INTEGER.
 * \return      #MBEDTLS_ERR_ASN1_INVALID_LENGTH if the parsed value does
 *              not fit in an \c int.
 */
int mbedtls_asn1_get_int( unsigned char **p,
                          const unsigned char *end,
                          int *val );

/**
 * \brief       Retrieve an enumerated ASN.1 tag and its value.
 *              Updates the pointer to immediately behind the full tag.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              beyond the ASN.1 element.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param val   On success, the parsed value.
 *
 * \return      0 if successful.
 * \return      An ASN.1 error code if the input does not start with
 *              a valid ASN.1 ENUMERATED.
 * \return      #MBEDTLS_ERR_ASN1_INVALID_LENGTH if the parsed value does
 *              not fit in an \c int.
 */
int mbedtls_asn1_get_enum( unsigned char **p,
                           const unsigned char *end,
                           int *val );

/**
 * \brief       Retrieve a bitstring ASN.1 tag and its value.
 *              Updates the pointer to immediately behind the full tag.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p is equal to \p end.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param bs    On success, ::mbedtls_asn1_bitstring information about
 *              the parsed value.
 *
 * \return      0 if successful.
 * \return      #MBEDTLS_ERR_ASN1_LENGTH_MISMATCH if the input contains
 *              extra data after a valid BIT STRING.
 * \return      An ASN.1 error code if the input does not start with
 *              a valid ASN.1 BIT STRING.
 */
int mbedtls_asn1_get_bitstring( unsigned char **p, const unsigned char *end,
                                mbedtls_asn1_bitstring *bs );

/**
 * \brief       Retrieve a bitstring ASN.1 tag without unused bits and its
 *              value.
 *              Updates the pointer to the beginning of the bit/octet string.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              of the content of the BIT STRING.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param len   On success, \c *len is the length of the content in bytes.
 *
 * \return      0 if successful.
 * \return      #MBEDTLS_ERR_ASN1_INVALID_DATA if the input starts with
 *              a valid BIT STRING with a nonzero number of unused bits.
 * \return      An ASN.1 error code if the input does not start with
 *              a valid ASN.1 BIT STRING.
 */
int mbedtls_asn1_get_bitstring_null( unsigned char **p,
                                     const unsigned char *end,
                                     size_t *len );

/**
 * \brief       Parses and splits an ASN.1 "SEQUENCE OF <tag>".
 *              Updates the pointer to immediately behind the full sequence tag.
 *
 * This function allocates memory for the sequence elements. You can free
 * the allocated memory with mbedtls_asn1_sequence_free().
 *
 * \note        On error, this function may return a partial list in \p cur.
 *              You must set `cur->next = NULL` before calling this function!
 *              Otherwise it is impossible to distinguish a previously non-null
 *              pointer from a pointer to an object allocated by this function.
 *
 * \note        If the sequence is empty, this function does not modify
 *              \c *cur. If the sequence is valid and non-empty, this
 *              function sets `cur->buf.tag` to \p tag. This allows
 *              callers to distinguish between an empty sequence and
 *              a one-element sequence.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p is equal to \p end.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param cur   A ::mbedtls_asn1_sequence which this function fills.
 *              When this function returns, \c *cur is the head of a linked
 *              list. Each node in this list is allocated with
 *              mbedtls_calloc() apart from \p cur itself, and should
 *              therefore be freed with mbedtls_free().
 *              The list describes the content of the sequence.
 *              The head of the list (i.e. \c *cur itself) describes the
 *              first element, `*cur->next` describes the second element, etc.
 *              For each element, `buf.tag == tag`, `buf.len` is the length
 *              of the content of the content of the element, and `buf.p`
 *              points to the first byte of the content (i.e. immediately
 *              past the length of the element).
 *              Note that list elements may be allocated even on error.
 * \param tag   Each element of the sequence must have this tag.
 *
 * \return      0 if successful.
 * \return      #MBEDTLS_ERR_ASN1_LENGTH_MISMATCH if the input contains
 *              extra data after a valid SEQUENCE OF \p tag.
 * \return      #MBEDTLS_ERR_ASN1_UNEXPECTED_TAG if the input starts with
 *              an ASN.1 SEQUENCE in which an element has a tag that
 *              is different from \p tag.
 * \return      #MBEDTLS_ERR_ASN1_ALLOC_FAILED if a memory allocation failed.
 * \return      An ASN.1 error code if the input does not start with
 *              a valid ASN.1 SEQUENCE.
 */
int mbedtls_asn1_get_sequence_of( unsigned char **p,
                                  const unsigned char *end,
                                  mbedtls_asn1_sequence *cur,
                                  int tag );
/**
 * \brief          Free a heap-allocated linked list presentation of
 *                 an ASN.1 sequence, including the first element.
 *
 * There are two common ways to manage the memory used for the representation
 * of a parsed ASN.1 sequence:
 * - Allocate a head node `mbedtls_asn1_sequence *head` with mbedtls_calloc().
 *   Pass this node as the `cur` argument to mbedtls_asn1_get_sequence_of().
 *   When you have finished processing the sequence,
 *   call mbedtls_asn1_sequence_free() on `head`.
 * - Allocate a head node `mbedtls_asn1_sequence *head` in any manner,
 *   for example on the stack. Make sure that `head->next == NULL`.
 *   Pass `head` as the `cur` argument to mbedtls_asn1_get_sequence_of().
 *   When you have finished processing the sequence,
 *   call mbedtls_asn1_sequence_free() on `head->cur`,
 *   then free `head` itself in the appropriate manner.
 *
 * \param seq      The address of the first sequence component. This may
 *                 be \c NULL, in which case this functions returns
 *                 immediately.
 */
void mbedtls_asn1_sequence_free( mbedtls_asn1_sequence *seq );

/**
 * \brief                Traverse an ASN.1 SEQUENCE container and
 *                       call a callback for each entry.
 *
 * This function checks that the input is a SEQUENCE of elements that
 * each have a "must" tag, and calls a callback function on the elements
 * that have a "may" tag.
 *
 * For example, to validate that the input is a SEQUENCE of `tag1` and call
 * `cb` on each element, use
 * ```
 * mbedtls_asn1_traverse_sequence_of(&p, end, 0xff, tag1, 0, 0, cb, ctx);
 * ```
 *
 * To validate that the input is a SEQUENCE of ANY and call `cb` on
 * each element, use
 * ```
 * mbedtls_asn1_traverse_sequence_of(&p, end, 0, 0, 0, 0, cb, ctx);
 * ```
 *
 * To validate that the input is a SEQUENCE of CHOICE {NULL, OCTET STRING}
 * and call `cb` on each element that is an OCTET STRING, use
 * ```
 * mbedtls_asn1_traverse_sequence_of(&p, end, 0xfe, 0x04, 0xff, 0x04, cb, ctx);
 * ```
 *
 * The callback is called on the elements with a "may" tag from left to
 * right. If the input is not a valid SEQUENCE of elements with a "must" tag,
 * the callback is called on the elements up to the leftmost point where
 * the input is invalid.
 *
 * \warning              This function is still experimental and may change
 *                       at any time.
 *
 * \param p              The address of the pointer to the beginning of
 *                       the ASN.1 SEQUENCE header. This is updated to
 *                       point to the end of the ASN.1 SEQUENCE container
 *                       on a successful invocation.
 * \param end            The end of the ASN.1 SEQUENCE container.
 * \param tag_must_mask  A mask to be applied to the ASN.1 tags found within
 *                       the SEQUENCE before comparing to \p tag_must_value.
 * \param tag_must_val   The required value of each ASN.1 tag found in the
 *                       SEQUENCE, after masking with \p tag_must_mask.
 *                       Mismatching tags lead to an error.
 *                       For example, a value of \c 0 for both \p tag_must_mask
 *                       and \p tag_must_val means that every tag is allowed,
 *                       while a value of \c 0xFF for \p tag_must_mask means
 *                       that \p tag_must_val is the only allowed tag.
 * \param tag_may_mask   A mask to be applied to the ASN.1 tags found within
 *                       the SEQUENCE before comparing to \p tag_may_value.
 * \param tag_may_val    The desired value of each ASN.1 tag found in the
 *                       SEQUENCE, after masking with \p tag_may_mask.
 *                       Mismatching tags will be silently ignored.
 *                       For example, a value of \c 0 for \p tag_may_mask and
 *                       \p tag_may_val means that any tag will be considered,
 *                       while a value of \c 0xFF for \p tag_may_mask means
 *                       that all tags with value different from \p tag_may_val
 *                       will be ignored.
 * \param cb             The callback to trigger for each component
 *                       in the ASN.1 SEQUENCE that matches \p tag_may_val.
 *                       The callback function is called with the following
 *                       parameters:
 *                       - \p ctx.
 *                       - The tag of the current element.
 *                       - A pointer to the start of the current element's
 *                         content inside the input.
 *                       - The length of the content of the current element.
 *                       If the callback returns a non-zero value,
 *                       the function stops immediately,
 *                       forwarding the callback's return value.
 * \param ctx            The context to be passed to the callback \p cb.
 *
 * \return               \c 0 if successful the entire ASN.1 SEQUENCE
 *                       was traversed without parsing or callback errors.
 * \return               #MBEDTLS_ERR_ASN1_LENGTH_MISMATCH if the input
 *                       contains extra data after a valid SEQUENCE
 *                       of elements with an accepted tag.
 * \return               #MBEDTLS_ERR_ASN1_UNEXPECTED_TAG if the input starts
 *                       with an ASN.1 SEQUENCE in which an element has a tag
 *                       that is not accepted.
 * \return               An ASN.1 error code if the input does not start with
 *                       a valid ASN.1 SEQUENCE.
 * \return               A non-zero error code forwarded from the callback
 *                       \p cb in case the latter returns a non-zero value.
 */
int mbedtls_asn1_traverse_sequence_of(
    unsigned char **p,
    const unsigned char *end,
    unsigned char tag_must_mask, unsigned char tag_must_val,
    unsigned char tag_may_mask, unsigned char tag_may_val,
    int (*cb)( void *ctx, int tag,
               unsigned char* start, size_t len ),
    void *ctx );


/**
 * \brief       Retrieve an integer ASN.1 tag and its value.
 *              Updates the pointer to immediately behind the full tag.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              beyond the ASN.1 element.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param X     On success, the parsed value.
 *
 * \return      0 if successful.
 * \return      An ASN.1 error code if the input does not start with
 *              a valid ASN.1 INTEGER.
 * \return      #MBEDTLS_ERR_ASN1_INVALID_LENGTH if the parsed value does
 *              not fit in an \c int.
 * \return      An MPI error code if the parsed value is too large.
 */
int mbedtls_asn1_get_mpi( unsigned char **p,
                          const unsigned char *end,
                          mbedtls_mpi *X );


/**
 * \brief       Retrieve an AlgorithmIdentifier ASN.1 sequence.
 *              Updates the pointer to immediately behind the full
 *              AlgorithmIdentifier.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              beyond the AlgorithmIdentifier element.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param alg   The buffer to receive the OID.
 * \param params The buffer to receive the parameters.
 *              This is zeroized if there are no parameters.
 *
 * \return      0 if successful or a specific ASN.1 or MPI error code.
 */
int mbedtls_asn1_get_alg( unsigned char **p,
                  const unsigned char *end,
                  mbedtls_asn1_buf *alg, mbedtls_asn1_buf *params );

/**
 * \brief       Retrieve an AlgorithmIdentifier ASN.1 sequence with NULL or no
 *              params.
 *              Updates the pointer to immediately behind the full
 *              AlgorithmIdentifier.
 *
 * \param p     On entry, \c *p points to the start of the ASN.1 element.
 *              On successful completion, \c *p points to the first byte
 *              beyond the AlgorithmIdentifier element.
 *              On error, the value of \c *p is undefined.
 * \param end   End of data.
 * \param alg   The buffer to receive the OID.
 *
 * \return      0 if successful or a specific ASN.1 or MPI error code.
 */
int mbedtls_asn1_get_alg_null( unsigned char **p,
                       const unsigned char *end,
                       mbedtls_asn1_buf *alg );

/**
 * \brief       Find a specific named_data entry in a sequence or list based on
 *              the OID.
 *
 * \param list  The list to seek through
 * \param oid   The OID to look for
 * \param len   Size of the OID
 *
 * \return      NULL if not found, or a pointer to the existing entry.
 */
const mbedtls_asn1_named_data *mbedtls_asn1_find_named_data( const mbedtls_asn1_named_data *list,
                                       const char *oid, size_t len );

/**
 * \brief       Free a mbedtls_asn1_named_data entry
 *
 * \param entry The named data entry to free.
 *              This function calls mbedtls_free() on
 *              `entry->oid.p` and `entry->val.p`.
 */
void mbedtls_asn1_free_named_data( mbedtls_asn1_named_data *entry );

/**
 * \brief       Free all entries in a mbedtls_asn1_named_data list.
 *
 * \param head  Pointer to the head of the list of named data entries to free.
 *              This function calls mbedtls_asn1_free_named_data() and
 *              mbedtls_free() on each list element and
 *              sets \c *head to \c NULL.
 */
void mbedtls_asn1_free_named_data_list( mbedtls_asn1_named_data **head );

/** \} name Functions to parse ASN.1 data structures */
/** \} addtogroup asn1_module */





# 28 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1write.h"

# 37 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/asn1write.h"





/**
 * \brief           Write a length field in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param len       The length value to write.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_len( unsigned char **p, const unsigned char *start,
                            size_t len );
/**
 * \brief           Write an ASN.1 tag in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param tag       The tag to write.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_tag( unsigned char **p, const unsigned char *start,
                            unsigned char tag );

/**
 * \brief           Write raw buffer data.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param buf       The data buffer to write.
 * \param size      The length of the data buffer.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_raw_buffer( unsigned char **p, const unsigned char *start,
                                   const unsigned char *buf, size_t size );


/**
 * \brief           Write a arbitrary-precision number (#MBEDTLS_ASN1_INTEGER)
 *                  in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param X         The MPI to write.
 *                  It must be non-negative.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_mpi( unsigned char **p, const unsigned char *start,
                            const mbedtls_mpi *X );


/**
 * \brief           Write a NULL tag (#MBEDTLS_ASN1_NULL) with zero data
 *                  in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_null( unsigned char **p, const unsigned char *start );

/**
 * \brief           Write an OID tag (#MBEDTLS_ASN1_OID) and data
 *                  in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param oid       The OID to write.
 * \param oid_len   The length of the OID.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_oid( unsigned char **p, const unsigned char *start,
                            const char *oid, size_t oid_len );

/**
 * \brief           Write an AlgorithmIdentifier sequence in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param oid       The OID of the algorithm to write.
 * \param oid_len   The length of the algorithm's OID.
 * \param par_len   The length of the parameters, which must be already written.
 *                  If 0, NULL parameters are added
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_algorithm_identifier( unsigned char **p,
                                             const unsigned char *start,
                                             const char *oid, size_t oid_len,
                                             size_t par_len );

/**
 * \brief           Write a boolean tag (#MBEDTLS_ASN1_BOOLEAN) and value
 *                  in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param boolean   The boolean value to write, either \c 0 or \c 1.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_bool( unsigned char **p, const unsigned char *start,
                             int boolean );

/**
 * \brief           Write an int tag (#MBEDTLS_ASN1_INTEGER) and value
 *                  in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param val       The integer value to write.
 *                  It must be non-negative.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_int( unsigned char **p, const unsigned char *start, int val );

/**
 * \brief           Write an enum tag (#MBEDTLS_ASN1_ENUMERATED) and value
 *                  in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param val       The integer value to write.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative \c MBEDTLS_ERR_ASN1_XXX error code on failure.
 */
int mbedtls_asn1_write_enum( unsigned char **p, const unsigned char *start, int val );

/**
 * \brief           Write a string in ASN.1 format using a specific
 *                  string encoding tag.

 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param tag       The string encoding tag to write, e.g.
 *                  #MBEDTLS_ASN1_UTF8_STRING.
 * \param text      The string to write.
 * \param text_len  The length of \p text in bytes (which might
 *                  be strictly larger than the number of characters).
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative error code on failure.
 */
int mbedtls_asn1_write_tagged_string( unsigned char **p, const unsigned char *start,
                                      int tag, const char *text,
                                      size_t text_len );

/**
 * \brief           Write a string in ASN.1 format using the PrintableString
 *                  string encoding tag (#MBEDTLS_ASN1_PRINTABLE_STRING).
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param text      The string to write.
 * \param text_len  The length of \p text in bytes (which might
 *                  be strictly larger than the number of characters).
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative error code on failure.
 */
int mbedtls_asn1_write_printable_string( unsigned char **p,
                                         const unsigned char *start,
                                         const char *text, size_t text_len );

/**
 * \brief           Write a UTF8 string in ASN.1 format using the UTF8String
 *                  string encoding tag (#MBEDTLS_ASN1_UTF8_STRING).
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param text      The string to write.
 * \param text_len  The length of \p text in bytes (which might
 *                  be strictly larger than the number of characters).
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative error code on failure.
 */
int mbedtls_asn1_write_utf8_string( unsigned char **p, const unsigned char *start,
                                    const char *text, size_t text_len );

/**
 * \brief           Write a string in ASN.1 format using the IA5String
 *                  string encoding tag (#MBEDTLS_ASN1_IA5_STRING).
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param text      The string to write.
 * \param text_len  The length of \p text in bytes (which might
 *                  be strictly larger than the number of characters).
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative error code on failure.
 */
int mbedtls_asn1_write_ia5_string( unsigned char **p, const unsigned char *start,
                                   const char *text, size_t text_len );

/**
 * \brief           Write a bitstring tag (#MBEDTLS_ASN1_BIT_STRING) and
 *                  value in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param buf       The bitstring to write.
 * \param bits      The total number of bits in the bitstring.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative error code on failure.
 */
int mbedtls_asn1_write_bitstring( unsigned char **p, const unsigned char *start,
                                  const unsigned char *buf, size_t bits );

/**
 * \brief           This function writes a named bitstring tag
 *                  (#MBEDTLS_ASN1_BIT_STRING) and value in ASN.1 format.
 *
 *                  As stated in RFC 5280 Appendix B, trailing zeroes are
 *                  omitted when encoding named bitstrings in DER.
 *
 * \note            This function works backwards within the data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer which is used for bounds-checking.
 * \param buf       The bitstring to write.
 * \param bits      The total number of bits in the bitstring.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative error code on failure.
 */
int mbedtls_asn1_write_named_bitstring( unsigned char **p,
                                        const unsigned char *start,
                                        const unsigned char *buf,
                                        size_t bits );

/**
 * \brief           Write an octet string tag (#MBEDTLS_ASN1_OCTET_STRING)
 *                  and value in ASN.1 format.
 *
 * \note            This function works backwards in data buffer.
 *
 * \param p         The reference to the current position pointer.
 * \param start     The start of the buffer, for bounds-checking.
 * \param buf       The buffer holding the data to write.
 * \param size      The length of the data buffer \p buf.
 *
 * \return          The number of bytes written to \p p on success.
 * \return          A negative error code on failure.
 */
int mbedtls_asn1_write_octet_string( unsigned char **p, const unsigned char *start,
                                     const unsigned char *buf, size_t size );

/**
 * \brief           Create or find a specific named_data entry for writing in a
 *                  sequence or list based on the OID. If not already in there,
 *                  a new entry is added to the head of the list.
 *                  Warning: Destructive behaviour for the val data!
 *
 * \param list      The pointer to the location of the head of the list to seek
 *                  through (will be updated in case of a new entry).
 * \param oid       The OID to look for.
 * \param oid_len   The size of the OID.
 * \param val       The associated data to store. If this is \c NULL,
 *                  no data is copied to the new or existing buffer.
 * \param val_len   The minimum length of the data buffer needed.
 *                  If this is 0, do not allocate a buffer for the associated
 *                  data.
 *                  If the OID was already present, enlarge, shrink or free
 *                  the existing buffer to fit \p val_len.
 *
 * \return          A pointer to the new / existing entry on success.
 * \return          \c NULL if if there was a memory allocation error.
 */
mbedtls_asn1_named_data *mbedtls_asn1_store_named_data( mbedtls_asn1_named_data **list,
                                        const char *oid, size_t oid_len,
                                        const unsigned char *val,
                                        size_t val_len );





# 32 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 34 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"





# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"
/**
 * \file platform.h
 *
 * \brief This file contains the definitions and functions of the
 *        Mbed TLS platform abstraction layer.
 *
 *        The platform abstraction layer removes the need for the library
 *        to directly link to standard C library functions or operating
 *        system services, making the library easier to port and embed.
 *        Application developers and users of the library can provide their own
 *        implementations of these functions, or implementations specific to
 *        their platform, which can be statically linked to the library or
 *        dynamically configured at runtime.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
# 34 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

# 36 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h"
/**
 * \file platform_time.h
 *
 * \brief mbed TLS Platform time abstraction
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 26 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h"





/*
 * The time_t datatype
 */



/* For time_t */
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"
/* time.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.12 */
/* Copyright (C) Codemist Ltd., 1988-1993.                      */
/* Copyright 1991-1993 ARM Limited. All rights reserved.        */
/* version 0.03 */

/*
 * time.h declares two macros, four types and several functions for
 * manipulating time. Many functions deal with a calendar time that
 * represents the current date (according to the Gregorian
 * calendar) and time. Some functions deal with local time, which
 * is the calendar time expressed for some specific time zone, and
 * with Daylight Savings Time, which is a temporary change in the
 * algorithm for determining local time.
 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */













# 41 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"




    /* CLOCKS_PER_SEC: the number per second of the value returned by the
     * clock function. */
# 73 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"





typedef unsigned int clock_t;    /* cpu time type */
typedef unsigned int time_t;     /* date/time in unix secs past 1-Jan-70 */

#pragma push
#pragma anon_unions

struct tm {
    int tm_sec;   /* seconds after the minute, 0 to 60
                     (0 - 60 allows for the occasional leap second) */
    int tm_min;   /* minutes after the hour, 0 to 59 */
    int tm_hour;  /* hours since midnight, 0 to 23 */
    int tm_mday;  /* day of the month, 1 to 31 */
    int tm_mon;   /* months since January, 0 to 11 */
    int tm_year;  /* years since 1900 */
    int tm_wday;  /* days since Sunday, 0 to 6 */
    int tm_yday;  /* days since January 1, 0 to 365 */
    int tm_isdst; /* Daylight Savings Time flag */
    union {       /* ABI-required extra fields, in a variety of types */
        struct {
            int __extra_1, __extra_2;
        };
        struct {
            long __extra_1_long, __extra_2_long;
        };
        struct {
            char *__extra_1_cptr, *__extra_2_cptr;
        };
        struct {
            void *__extra_1_vptr, *__extra_2_vptr;
        };
    };
};

#pragma pop

   /* struct tm holds the components of a calendar time, called the broken-down
    * time. The value of tm_isdst is positive if Daylight Savings Time is in
    * effect, zero if Daylight Savings Time is not in effect, and negative if
    * the information is not available.
    */

extern __declspec(__nothrow) clock_t clock(void);
   /* determines the processor time used.
    * Returns: the implementation's best approximation to the processor time
    *          used by the program since program invocation. The time in
    *          seconds is the value returned divided by the value of the macro
    *          CLK_TCK. The value (clock_t)-1 is returned if the processor time
    *          used is not available.
    */
extern __declspec(__nothrow) double difftime(time_t /*time1*/, time_t /*time0*/);
   /*
    * computes the difference between two calendar times: time1 - time0.
    * Returns: the difference expressed in seconds as a double.
    */
extern __declspec(__nothrow) time_t mktime(struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the broken-down time, expressed as local time, in the structure
    * pointed to by timeptr into a calendar time value with the same encoding
    * as that of the values returned by the time function. The original values
    * of the tm_wday and tm_yday components of the structure are ignored, and
    * the original values of the other components are not restricted to the
    * ranges indicated above. On successful completion, the values of the
    * tm_wday and tm_yday structure components are set appropriately, and the
    * other components are set to represent the specified calendar time, but
    * with their values forced to the ranges indicated above; the final value
    * of tm_mday is not set until tm_mon and tm_year are determined.
    * Returns: the specified calendar time encoded as a value of type time_t.
    *          If the calendar time cannot be represented, the function returns
    *          the value (time_t)-1.
    */
extern __declspec(__nothrow) time_t time(time_t * /*timer*/);
   /*
    * determines the current calendar time. The encoding of the value is
    * unspecified.
    * Returns: the implementations best approximation to the current calendar
    *          time. The value (time_t)-1 is returned if the calendar time is
    *          not available. If timer is not a null pointer, the return value
    *          is also assigned to the object it points to.
    */

extern __declspec(__nothrow) char *asctime(const struct tm * /*timeptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) char *_asctime_r(const struct tm * /*timeptr*/,
                                char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) char *asctime_r(const struct tm * /*timeptr*/,
                               char * __restrict /*buf*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the broken-down time in the structure pointed to by timeptr into
    * a string in the form "Sun Sep 16 01:03:52 1973\n\0".
    * Returns: a pointer to the string containing the date and time.
    */
extern __declspec(__nothrow) char *ctime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer to local time in the form
    * of a string. It is equivalent to asctime(localtime(timer));
    * Returns: the pointer returned by the asctime function with that
    *          broken-down time as argument.
    */
extern __declspec(__nothrow) struct tm *gmtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed as Greenwich Mean Time (GMT).
    * Returns: a pointer to that object or a null pointer if GMT not available.
    */
extern __declspec(__nothrow) struct tm *localtime(const time_t * /*timer*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) struct tm *_localtime_r(const time_t * __restrict /*timer*/,
                                       struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) struct tm *localtime_r(const time_t * __restrict /*timer*/,
                                      struct tm * __restrict /*result*/) __attribute__((__nonnull__(1,2)));

   /*
    * converts the calendar time pointed to by timer into a broken-down time,
    * expressed a local time.
    * Returns: a pointer to that object.
    */
extern __declspec(__nothrow) size_t strftime(char * __restrict /*s*/, size_t /*maxsize*/,
                       const char * __restrict /*format*/,
                       const struct tm * __restrict /*timeptr*/) __attribute__((__nonnull__(1,3,4)));
   /*
    * places characters into the array pointed to by s as controlled by the
    * string pointed to by format. The format string consists of zero or more
    * directives and ordinary characters. A directive consists of a % character
    * followed by a character that determines the directive's behaviour. All
    * ordinary characters (including the terminating null character) are copied
    * unchanged into the array. No more than maxsize characters are placed into
    * the array. Each directive is replaced by appropriate characters  as
    * described in the following list. The appropriate characters are
    * determined by the LC_TIME category of the current locale and by the
    * values contained in the structure pointed to by timeptr.
    * %a is replaced by the locale's abbreviated weekday name.
    * %A is replaced by the locale's full weekday name.
    * %b is replaced by the locale's abbreviated month name.
    * %B is replaced by the locale's full month name.
    * %c is replaced by the locale's appropriate date and time representation.
    * %d is replaced by the day of the month as a decimal number (01-31).
    * %H is replaced by the hour (24-hour clock) as a decimal number (00-23).
    * %I is replaced by the hour (12-hour clock) as a decimal number (01-12).
    * %j is replaced by the day of the year as a decimal number (001-366).
    * %m is replaced by the month as a decimal number (01-12).
    * %M is replaced by the minute as a decimal number (00-59).
    * %p is replaced by the locale's equivalent of either AM or PM designations
    *       associated with a 12-hour clock.
    * %S is replaced by the second as a decimal number (00-61).
    * %U is replaced by the week number of the year (Sunday as the first day of
    *       week 1) as a decimal number (00-53).
    * %w is replaced by the weekday as a decimal number (0(Sunday) - 6).
    * %W is replaced by the week number of the year (Monday as the first day of
    *       week 1) as a decimal number (00-53).
    * %x is replaced by the locale's appropriate date representation.
    * %X is replaced by the locale's appropriate time representation.
    * %y is replaced by the year without century as a decimal number (00-99).
    * %Y is replaced by the year with century as a decimal number.
    * %Z is replaced by the timezone name or abbreviation, or by no characters
    *       if no time zone is determinable.
    * %% is replaced by %.
    * If a directive is not one of the above, the behaviour is undefined.
    * Returns: If the total number of resulting characters including the
    *          terminating null character is not more than maxsize, the
    *          strftime function returns the number of characters placed into
    *          the array pointed to by s not including the terminating null
    *          character. otherwise, zero is returned and the contents of the
    *          array are indeterminate.
    */











# 280 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\time.h"



/* end of time.h */

# 39 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h"
typedef time_t mbedtls_time_t;


/*
 * The function pointers for time
 */

extern mbedtls_time_t (*mbedtls_time)( mbedtls_time_t* time );

/**
 * \brief   Set your own time function pointer
 *
 * \param   time_func   the time function implementation
 *
 * \return              0
 */
int mbedtls_platform_set_time( mbedtls_time_t (*time_func)( mbedtls_time_t* time ) );
# 63 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h"





# 39 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"






/**
 * \name SECTION: Module settings
 *
 * The configuration options you can set for this module are in this section.
 * Either change them in mbedtls_config.h or define them on the compiler command line.
 * \{
 */

/* The older Microsoft Windows common runtime provides non-conforming
 * implementations of some standard library functions, including snprintf
 * and vsnprintf. This affects MSVC and MinGW builds.
 */





# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"
/* stdio.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.9 */
/* Copyright (C) Codemist Ltd., 1988-1993                       */
/* Copyright 1991-1998 ARM Limited. All rights reserved.        */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: sdouglas $
 */

/*
 * stdio.h declares two types, several macros, and many functions for
 * performing input and output. For a discussion on Streams and Files
 * refer to sections 4.9.2 and 4.9.3 in the above ANSI draft, or to a
 * modern textbook on C.
 */





/*
 * Depending on compiler version __int64 or __INT64_TYPE__ should be defined.
 */




  /* On some architectures neither of these may be defined - if so, fall
     through and error out if used. */








# 47 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"


 /* always defined in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */









/* ANSI forbids va_list to be defined here */
/* keep in step with <stdarg.h> and <wchar.h> */

/* always defined in C++ and non-strict C for consistency of debug info */



    typedef struct __va_list __va_list;






   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain compliant versions of the printf
    * and scanf families of functions
    */




typedef struct __fpos_t_struct {
    unsigned __int64 __pos;
    /*
     * this structure is equivalent to an mbstate_t, but we're not
     * allowed to actually define the type name `mbstate_t' within
     * stdio.h
     */
    struct {
        unsigned int __state1, __state2;
    } __mbstate;
} fpos_t;
   /*
    * fpos_t is an object capable of recording all information needed to
    * specify uniquely every position within a file.
    */


   /* _SYS_OPEN defines a limit on the number of open files that is imposed
    * by this C library
    */

typedef struct __FILE FILE;
   /*
    * FILE is an object capable of recording all information needed to control
    * a stream, such as its file position indicator, a pointer to its
    * associated buffer, an error indicator that records whether a read/write
    * error has occurred and an end-of-file indicator that records whether the
    * end-of-file has been reached.
    * Its structure is not made known to library clients.
    */

# 136 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"


extern FILE __stdin, __stdout, __stderr;
extern FILE *__aeabi_stdin, *__aeabi_stdout, *__aeabi_stderr;

# 166 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"
   /* pointer to a FILE object associated with standard input stream */

   /* pointer to a FILE object associated with standard output stream */

   /* pointer to a FILE object associated with standard error stream */





    /* Various default file IO buffer sizes */



   /*
    * an integral constant expression that is the minimum number of files that
    * this implementation guarantees can be open simultaneously.
    */


   /*
    * an integral constant expression that is the size of an array of char
    * large enough to hold the longest filename string
    */

   /*
    * an integral constant expression that is the size of an array of char
    * large enough to hold a temporary file name string generated by the
    * tmpnam function.
    */

   /*
    * an integral constant expression that is the minimum number of unique
    * file names that shall be generated by the tmpnam function.
    */




   /*
    * negative integral constant, indicates end-of-file, that is, no more input
    * from a stream.
    */





    /*
     * _IOBIN is the flag passed to _sys_write to denote a binary
     * file.
     */






extern __declspec(__nothrow) int remove(const char * /*filename*/) __attribute__((__nonnull__(1)));
   /*
    * causes the file whose name is the string pointed to by filename to be
    * removed. Subsequent attempts to open the file will fail, unless it is
    * created anew. If the file is open, the behaviour of the remove function
    * is implementation-defined.
    * Returns: zero if the operation succeeds, nonzero if it fails.
    */
extern __declspec(__nothrow) int rename(const char * /*old*/, const char * /*new*/) __attribute__((__nonnull__(1,2)));
   /*
    * causes the file whose name is the string pointed to by old to be
    * henceforth known by the name given by the string pointed to by new. The
    * file named old is effectively removed. If a file named by the string
    * pointed to by new exists prior to the call of the rename function, the
    * behaviour is implementation-defined.
    * Returns: zero if the operation succeeds, nonzero if it fails, in which
    *          case if the file existed previously it is still known by its
    *          original name.
    */
extern __declspec(__nothrow) FILE *tmpfile(void);
   /*
    * creates a temporary binary file that will be automatically removed when
    * it is closed or at program termination. The file is opened for update.
    * Returns: a pointer to the stream of the file that it created. If the file
    *          cannot be created, a null pointer is returned.
    */
extern __declspec(__nothrow) char *tmpnam(char * /*s*/);
   /*
    * generates a string that is not the same as the name of an existing file.
    * The tmpnam function generates a different string each time it is called,
    * up to TMP_MAX times. If it is called more than TMP_MAX times, the
    * behaviour is implementation-defined.
    * Returns: If the argument is a null pointer, the tmpnam function leaves
    *          its result in an internal static object and returns a pointer to
    *          that object. Subsequent calls to the tmpnam function may modify
    *          the same object. if the argument is not a null pointer, it is
    *          assumed to point to an array of at least L_tmpnam characters;
    *          the tmpnam function writes its result in that array and returns
    *          the argument as its value.
    */

extern __declspec(__nothrow) int fclose(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * causes the stream pointed to by stream to be flushed and the associated
    * file to be closed. Any unwritten buffered data for the stream are
    * delivered to the host environment to be written to the file; any unread
    * buffered data are discarded. The stream is disassociated from the file.
    * If the associated buffer was automatically allocated, it is deallocated.
    * Returns: zero if the stream was succesfully closed, or nonzero if any
    *          errors were detected or if the stream was already closed.
    */
extern __declspec(__nothrow) int fflush(FILE * /*stream*/);
   /*
    * If the stream points to an output or update stream in which the most
    * recent operation was output, the fflush function causes any unwritten
    * data for that stream to be delivered to the host environment to be
    * written to the file. If the stream points to an input or update stream,
    * the fflush function undoes the effect of any preceding ungetc operation
    * on the stream.
    * Returns: nonzero if a write error occurs.
    */
extern __declspec(__nothrow) FILE *fopen(const char * __restrict /*filename*/,
                           const char * __restrict /*mode*/) __attribute__((__nonnull__(1,2)));
   /*
    * opens the file whose name is the string pointed to by filename, and
    * associates a stream with it.
    * The argument mode points to a string beginning with one of the following
    * sequences:
    * "r"         open text file for reading
    * "w"         create text file for writing, or truncate to zero length
    * "a"         append; open text file or create for writing at eof
    * "rb"        open binary file for reading
    * "wb"        create binary file for writing, or truncate to zero length
    * "ab"        append; open binary file or create for writing at eof
    * "r+"        open text file for update (reading and writing)
    * "w+"        create text file for update, or truncate to zero length
    * "a+"        append; open text file or create for update, writing at eof
    * "r+b"/"rb+" open binary file for update (reading and writing)
    * "w+b"/"wb+" create binary file for update, or truncate to zero length
    * "a+b"/"ab+" append; open binary file or create for update, writing at eof
    *
    * Opening a file with read mode ('r' as the first character in the mode
    * argument) fails if the file does not exist or cannot be read.
    * Opening a file with append mode ('a' as the first character in the mode
    * argument) causes all subsequent writes to be forced to the current end of
    * file, regardless of intervening calls to the fseek function. In some
    * implementations, opening a binary file with append mode ('b' as the
    * second or third character in the mode argument) may initially position
    * the file position indicator beyond the last data written, because of the
    * NUL padding.
    * When a file is opened with update mode ('+' as the second or third
    * character in the mode argument), both input and output may be performed
    * on the associated stream. However, output may not be directly followed
    * by input without an intervening call to the fflush fuction or to a file
    * positioning function (fseek, fsetpos, or rewind), and input be not be
    * directly followed by output without an intervening call to the fflush
    * fuction or to a file positioning function, unless the input operation
    * encounters end-of-file. Opening a file with update mode may open or
    * create a binary stream in some implementations. When opened, a stream
    * is fully buffered if and only if it does not refer to an interactive
    * device. The error and end-of-file indicators for the stream are
    * cleared.
    * Returns: a pointer to the object controlling the stream. If the open
    *          operation fails, fopen returns a null pointer.
    */
extern __declspec(__nothrow) FILE *freopen(const char * __restrict /*filename*/,
                    const char * __restrict /*mode*/,
                    FILE * __restrict /*stream*/) __attribute__((__nonnull__(2,3)));
   /*
    * opens the file whose name is the string pointed to by filename and
    * associates the stream pointed to by stream with it. The mode argument is
    * used just as in the fopen function.
    * The freopen function first attempts to close any file that is associated
    * with the specified stream. Failure to close the file successfully is
    * ignored. The error and end-of-file indicators for the stream are cleared.
    * Returns: a null pointer if the operation fails. Otherwise, freopen
    *          returns the value of the stream.
    */
extern __declspec(__nothrow) void setbuf(FILE * __restrict /*stream*/,
                    char * __restrict /*buf*/) __attribute__((__nonnull__(1)));
   /*
    * Except that it returns no value, the setbuf function is equivalent to the
    * setvbuf function invoked with the values _IOFBF for mode and BUFSIZ for
    * size, or (if buf is a null pointer), with the value _IONBF for mode.
    * Returns: no value.
    */
extern __declspec(__nothrow) int setvbuf(FILE * __restrict /*stream*/,
                   char * __restrict /*buf*/,
                   int /*mode*/, size_t /*size*/) __attribute__((__nonnull__(1)));
   /*
    * may be used after the stream pointed to by stream has been associated
    * with an open file but before it is read or written. The argument mode
    * determines how stream will be buffered, as follows: _IOFBF causes
    * input/output to be fully buffered; _IOLBF causes output to be line
    * buffered (the buffer will be flushed when a new-line character is
    * written, when the buffer is full, or when input is requested); _IONBF
    * causes input/output to be completely unbuffered. If buf is not the null
    * pointer, the array it points to may be used instead of an automatically
    * allocated buffer (the buffer must have a lifetime at least as great as
    * the open stream, so the stream should be closed before a buffer that has
    * automatic storage duration is deallocated upon block exit). The argument
    * size specifies the size of the array. The contents of the array at any
    * time are indeterminate.
    * Returns: zero on success, or nonzero if an invalid value is given for
    *          mode or size, or if the request cannot be honoured.
    */
#pragma __printf_args
extern __declspec(__nothrow) int fprintf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * writes output to the stream pointed to by stream, under control of the
    * string pointed to by format that specifies how subsequent arguments are
    * converted for output. If there are insufficient arguments for the format,
    * the behaviour is undefined. If the format is exhausted while arguments
    * remain, the excess arguments are evaluated but otherwise ignored. The
    * fprintf function returns when the end of the format string is reached.
    * The format shall be a multibyte character sequence, beginning and ending
    * in its initial shift state. The format is composed of zero or more
    * directives: ordinary multibyte characters (not %), which are copied
    * unchanged to the output stream; and conversion specifiers, each of which
    * results in fetching zero or more subsequent arguments. Each conversion
    * specification is introduced by the character %. For a description of the
    * available conversion specifiers refer to section ******* in the ANSI
    * draft mentioned at the start of this file or to any modern textbook on C.
    * The minimum value for the maximum number of characters producable by any
    * single conversion is at least 509.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _fprintf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, but does not support floating-point formats.
    * You can use instead of fprintf to improve code size.
    * Returns: as fprintf.
    */
#pragma __printf_args
extern __declspec(__nothrow) int printf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fprintf with the argument stdout interposed before the
    * arguments to printf.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _printf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to printf, but does not support floating-point formats.
    * You can use instead of printf to improve code size.
    * Returns: as printf.
    */
#pragma __printf_args
extern __declspec(__nothrow) int sprintf(char * __restrict /*s*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, except that the argument s specifies an array
    * into which the generated output is to be written, rather than to a
    * stream. A null character is written at the end of the characters written;
    * it is not counted as part of the returned sum.
    * Returns: the number of characters written to the array, not counting the
    *          terminating null character.
    */
#pragma __printf_args
extern __declspec(__nothrow) int _sprintf(char * __restrict /*s*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sprintf, but does not support floating-point formats.
    * You can use instead of sprintf to improve code size.
    * Returns: as sprintf.
    */

#pragma __printf_args
extern __declspec(__nothrow) int __ARM_snprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));


#pragma __printf_args
extern __declspec(__nothrow) int snprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to fprintf, except that the argument s specifies an array
    * into which the generated output is to be written, rather than to a
    * stream. The argument n specifies the size of the output array, so as to
    * avoid overflowing the buffer.
    * A null character is written at the end of the characters written, even
    * if the formatting was not completed; it is not counted as part of the
    * returned sum. At most n characters of the output buffer are used,
    * _including_ the null character.
    * Returns: the number of characters that would have been written to the
    *          array, not counting the terminating null character, if the
    *          array had been big enough. So if the return is >=0 and <n, then
    *          the entire string was successfully formatted; if the return is
    *          >=n, the string was truncated (but there is still a null char
    *          at the end of what was written); if the return is <0, there was
    *          an error.
    */

#pragma __printf_args
extern __declspec(__nothrow) int _snprintf(char * __restrict /*s*/, size_t /*n*/,
                      const char * __restrict /*format*/, ...) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to snprintf, but does not support floating-point formats.
    * You can use instead of snprintf to improve code size.
    * Returns: as snprintf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int fscanf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * reads input from the stream pointed to by stream, under control of the
    * string pointed to by format that specifies the admissible input sequences
    * and how thay are to be converted for assignment, using subsequent
    * arguments as pointers to the objects to receive the converted input. If
    * there are insufficient arguments for the format, the behaviour is
    * undefined. If the format is exhausted while arguments remain, the excess
    * arguments are evaluated but otherwise ignored.
    * The format is composed of zero or more directives: one or more
    * white-space characters; an ordinary character (not %); or a conversion
    * specification. Each conversion specification is introduced by the
    * character %. For a description of the available conversion specifiers
    * refer to section ******* in the ANSI draft mentioned at the start of this
    * file, or to any modern textbook on C.
    * If end-of-file is encountered during input, conversion is terminated. If
    * end-of-file occurs before any characters matching the current directive
    * have been read (other than leading white space, where permitted),
    * execution of the current directive terminates with an input failure;
    * otherwise, unless execution of the current directive is terminated with a
    * matching failure, execution of the following directive (if any) is
    * terminated with an input failure.
    * If conversions terminates on a conflicting input character, the offending
    * input character is left unread in the input strem. Trailing white space
    * (including new-line characters) is left unread unless matched by a
    * directive. The success of literal matches and suppressed asignments is
    * not directly determinable other than via the %n directive.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the fscanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early conflict between an input
    *          character and the format.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _fscanf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fscanf, but does not support floating-point formats.
    * You can use instead of fscanf to improve code size.
    * Returns: as fscanf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int scanf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fscanf with the argument stdin interposed before the
    * arguments to scanf.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the scanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early matching failure.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _scanf(const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to scanf, but does not support floating-point formats.
    * You can use instead of scanf to improve code size.
    * Returns: as scanf.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int sscanf(const char * __restrict /*s*/,
                    const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fscanf except that the argument s specifies a string
    * from which the input is to be obtained, rather than from a stream.
    * Reaching the end of the string is equivalent to encountering end-of-file
    * for the fscanf function.
    * Returns: the value of the macro EOF if an input failure occurs before any
    *          conversion. Otherwise, the scanf function returns the number of
    *          input items assigned, which can be fewer than provided for, or
    *          even zero, in the event of an early matching failure.
    */
#pragma __scanf_args
extern __declspec(__nothrow) int _sscanf(const char * __restrict /*s*/,
                     const char * __restrict /*format*/, ...) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sscanf, but does not support floating-point formats.
    * You can use instead of sscanf to improve code size.
    * Returns: as sscanf.
    */

/* C99 additions */
extern __declspec(__nothrow) int vfscanf(FILE * __restrict /*stream*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int vscanf(const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) int vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) int _vfscanf(FILE * __restrict /*stream*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int _vscanf(const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) int _vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) int __ARM_vsscanf(const char * __restrict /*s*/, const char * __restrict /*format*/, __va_list) __attribute__((__nonnull__(1,2)));

extern __declspec(__nothrow) int vprintf(const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to printf, with the variable argument list replaced by arg,
    * which has been initialised by the va_start macro (and possibly subsequent
    * va_arg calls). The vprintf function does not invoke the va_end function.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
extern __declspec(__nothrow) int _vprintf(const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to vprintf, but does not support floating-point formats.
    * You can use instead of vprintf to improve code size.
    * Returns: as vprintf.
    */
extern __declspec(__nothrow) int vfprintf(FILE * __restrict /*stream*/,
                    const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to fprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vfprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters transmitted, or a negative value if an
    *          output error occurred.
    */
extern __declspec(__nothrow) int vsprintf(char * __restrict /*s*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to sprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vsprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters written in the array, not counting the
    *          terminating null character.
    */
extern __declspec(__nothrow) int __ARM_vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));

extern __declspec(__nothrow) int vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to snprintf, with the variable argument list replaced by
    * arg, which has been initialised by the va_start macro (and possibly
    * subsequent va_arg calls). The vsprintf function does not invoke the
    * va_end function.
    * Returns: the number of characters that would have been written in the
    *          array, not counting the terminating null character. As
    *          snprintf.
    */

extern __declspec(__nothrow) int _vsprintf(char * __restrict /*s*/,
                      const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to vsprintf, but does not support floating-point formats.
    * You can use instead of vsprintf to improve code size.
    * Returns: as vsprintf.
    */
extern __declspec(__nothrow) int _vfprintf(FILE * __restrict /*stream*/,
                     const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(1,2)));
   /*
    * is equivalent to vfprintf, but does not support floating-point formats.
    * You can use instead of vfprintf to improve code size.
    * Returns: as vfprintf.
    */
extern __declspec(__nothrow) int _vsnprintf(char * __restrict /*s*/, size_t /*n*/,
                      const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(3)));
   /*
    * is equivalent to vsnprintf, but does not support floating-point formats.
    * You can use instead of vsnprintf to improve code size.
    * Returns: as vsnprintf.
    */

#pragma __printf_args
extern __declspec(__nothrow) int asprintf(char ** /*strp*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) int vasprintf(char ** /*strp*/, const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(2)));

#pragma __printf_args
extern __declspec(__nothrow) int __ARM_asprintf(char ** /*strp*/, const char * __restrict /*format*/, ...) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) int __ARM_vasprintf(char ** /*strp*/, const char * __restrict /*format*/, __va_list /*arg*/) __attribute__((__nonnull__(2)));
   /*
    * dynamically allocates a buffer of the right size for the
    * formatted string, and returns it in (*strp). Formal return value
    * is the same as any other printf variant, except that it returns
    * -1 if the buffer could not be allocated.
    *
    * (The functions with __ARM_ prefixed names are identical to the
    * ones without, but are available in all compilation modes without
    * violating user namespace.)
    */

extern __declspec(__nothrow) int fgetc(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * obtains the next character (if present) as an unsigned char converted to
    * an int, from the input stream pointed to by stream, and advances the
    * associated file position indicator (if defined).
    * Returns: the next character from the input stream pointed to by stream.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and fgetc returns EOF. If a read error occurs, the error
    *          indicator is set and fgetc returns EOF.
    */
extern __declspec(__nothrow) char *fgets(char * __restrict /*s*/, int /*n*/,
                    FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,3)));
   /*
    * reads at most one less than the number of characters specified by n from
    * the stream pointed to by stream into the array pointed to by s. No
    * additional characters are read after a new-line character (which is
    * retained) or after end-of-file. A null character is written immediately
    * after the last character read into the array.
    * Returns: s if successful. If end-of-file is encountered and no characters
    *          have been read into the array, the contents of the array remain
    *          unchanged and a null pointer is returned. If a read error occurs
    *          during the operation, the array contents are indeterminate and a
    *          null pointer is returned.
    */
extern __declspec(__nothrow) int fputc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * writes the character specified by c (converted to an unsigned char) to
    * the output stream pointed to by stream, at the position indicated by the
    * asociated file position indicator (if defined), and advances the
    * indicator appropriately. If the file position indicator is not defined,
    * the character is appended to the output stream.
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and fputc returns EOF.
    */
extern __declspec(__nothrow) int fputs(const char * __restrict /*s*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,2)));
   /*
    * writes the string pointed to by s to the stream pointed to by stream.
    * The terminating null character is not written.
    * Returns: EOF if a write error occurs; otherwise it returns a nonnegative
    *          value.
    */
extern __declspec(__nothrow) int getc(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * is equivalent to fgetc except that it may be implemented as an unsafe
    * macro (stream may be evaluated more than once, so the argument should
    * never be an expression with side-effects).
    * Returns: the next character from the input stream pointed to by stream.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and getc returns EOF. If a read error occurs, the error
    *          indicator is set and getc returns EOF.
    */




    extern __declspec(__nothrow) int (getchar)(void);

   /*
    * is equivalent to getc with the argument stdin.
    * Returns: the next character from the input stream pointed to by stdin.
    *          If the stream is at end-of-file, the end-of-file indicator is
    *          set and getchar returns EOF. If a read error occurs, the error
    *          indicator is set and getchar returns EOF.
    */
extern __declspec(__nothrow) char *gets(char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * reads characters from the input stream pointed to by stdin into the array
    * pointed to by s, until end-of-file is encountered or a new-line character
    * is read. Any new-line character is discarded, and a null character is
    * written immediately after the last character read into the array.
    * Returns: s if successful. If end-of-file is encountered and no characters
    *          have been read into the array, the contents of the array remain
    *          unchanged and a null pointer is returned. If a read error occurs
    *          during the operation, the array contents are indeterminate and a
    *          null pointer is returned.
    */
extern __declspec(__nothrow) int putc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * is equivalent to fputc except that it may be implemented as aan unsafe
    * macro (stream may be evaluated more than once, so the argument should
    * never be an expression with side-effects).
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and putc returns EOF.
    */




    extern __declspec(__nothrow) int (putchar)(int /*c*/);

   /*
    * is equivalent to putc with the second argument stdout.
    * Returns: the character written. If a write error occurs, the error
    *          indicator is set and putc returns EOF.
    */
extern __declspec(__nothrow) int puts(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * writes the string pointed to by s to the stream pointed to by stdout, and
    * appends a new-line character to the output. The terminating null
    * character is not written.
    * Returns: EOF if a write error occurs; otherwise it returns a nonnegative
    *          value.
    */
extern __declspec(__nothrow) int ungetc(int /*c*/, FILE * /*stream*/) __attribute__((__nonnull__(2)));
   /*
    * pushes the character specified by c (converted to an unsigned char) back
    * onto the input stream pointed to by stream. The character will be
    * returned by the next read on that stream. An intervening call to the
    * fflush function or to a file positioning function (fseek, fsetpos,
    * rewind) discards any pushed-back characters. The extern _ARMABIal storage
    * corresponding to the stream is unchanged.
    * One character pushback is guaranteed. If the unget function is called too
    * many times on the same stream without an intervening read or file
    * positioning operation on that stream, the operation may fail.
    * If the value of c equals that of the macro EOF, the operation fails and
    * the input stream is unchanged.
    * A successful call to the ungetc function clears the end-of-file
    * indicator. The value of the file position indicator after reading or
    * discarding all pushed-back characters shall be the same as it was before
    * the characters were pushed back. For a text stream, the value of the file
    * position indicator after a successful call to the ungetc function is
    * unspecified until all pushed-back characters are read or discarded. For a
    * binary stream, the file position indicator is decremented by each
    * successful call to the ungetc function; if its value was zero before a
    * call, it is indeterminate after the call.
    * Returns: the character pushed back after conversion, or EOF if the
    *          operation fails.
    */

extern __declspec(__nothrow) size_t fread(void * __restrict /*ptr*/,
                    size_t /*size*/, size_t /*nmemb*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,4)));
   /*
    * reads into the array pointed to by ptr, up to nmemb members whose size is
    * specified by size, from the stream pointed to by stream. The file
    * position indicator (if defined) is advanced by the number of characters
    * successfully read. If an error occurs, the resulting value of the file
    * position indicator is indeterminate. If a partial member is read, its
    * value is indeterminate. The ferror or feof function shall be used to
    * distinguish between a read error and end-of-file.
    * Returns: the number of members successfully read, which may be less than
    *          nmemb if a read error or end-of-file is encountered. If size or
    *          nmemb is zero, fread returns zero and the contents of the array
    *          and the state of the stream remain unchanged.
    */

extern __declspec(__nothrow) size_t __fread_bytes_avail(void * __restrict /*ptr*/,
                    size_t /*count*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,3)));
   /*
    * reads into the array pointed to by ptr, up to count characters from the
    * stream pointed to by stream. The file position indicator (if defined)
    * is advanced by the number of characters successfully read. If an error
    * occurs, the resulting value of the file position indicator is
    * indeterminate. The ferror or feof function shall be used to
    * distinguish between a read error and end-of-file.  The call will block
    * only if no characters are available.
    * Returns: the number of characters successfully read, which may be less than
    *          count. If count is zero, __fread_bytes_avail returns zero and
    *          the contents of the array and the state of the stream remain
    *          unchanged.
    */

extern __declspec(__nothrow) size_t fwrite(const void * __restrict /*ptr*/,
                    size_t /*size*/, size_t /*nmemb*/, FILE * __restrict /*stream*/) __attribute__((__nonnull__(1,4)));
   /*
    * writes, from the array pointed to by ptr up to nmemb members whose size
    * is specified by size, to the stream pointed to by stream. The file
    * position indicator (if defined) is advanced by the number of characters
    * successfully written. If an error occurs, the resulting value of the file
    * position indicator is indeterminate.
    * Returns: the number of members successfully written, which will be less
    *          than nmemb only if a write error is encountered.
    */

extern __declspec(__nothrow) int fgetpos(FILE * __restrict /*stream*/, fpos_t * __restrict /*pos*/) __attribute__((__nonnull__(1,2)));
   /*
    * stores the current value of the file position indicator for the stream
    * pointed to by stream in the object pointed to by pos. The value stored
    * contains unspecified information usable by the fsetpos function for
    * repositioning the stream to its position at the time  of the call to the
    * fgetpos function.
    * Returns: zero, if successful. Otherwise nonzero is returned and the
    *          integer expression errno is set to an implementation-defined
    *          nonzero value.
    */
extern __declspec(__nothrow) int fseek(FILE * /*stream*/, long int /*offset*/, int /*whence*/) __attribute__((__nonnull__(1)));
   /*
    * sets the file position indicator for the stream pointed to by stream.
    * For a binary stream, the new position is at the signed number of
    * characters specified by offset away from the point specified by whence.
    * The specified point is the beginning of the file for SEEK_SET, the
    * current position in the file for SEEK_CUR, or end-of-file for SEEK_END.
    * A binary stream need not meaningfully support fseek calls with a whence
    * value of SEEK_END.
    * For a text stream, either offset shall be zero, or offset shall be a
    * value returned by an earlier call to the ftell function on the same
    * stream and whence shall be SEEK_SET.
    * The fseek function clears the end-of-file indicator and undoes any
    * effects of the ungetc function on the same stream. After an fseek call,
    * the next operation on an update stream may be either input or output.
    * Returns: nonzero only for a request that cannot be satisfied.
    */
extern __declspec(__nothrow) int fsetpos(FILE * __restrict /*stream*/, const fpos_t * __restrict /*pos*/) __attribute__((__nonnull__(1,2)));
   /*
    * sets  the file position indicator for the stream pointed to by stream
    * according to the value of the object pointed to by pos, which shall be a
    * value returned by an earlier call to the fgetpos function on the same
    * stream.
    * The fsetpos function clears the end-of-file indicator and undoes any
    * effects of the ungetc function on the same stream. After an fsetpos call,
    * the next operation on an update stream may be either input or output.
    * Returns: zero, if successful. Otherwise nonzero is returned and the
    *          integer expression errno is set to an implementation-defined
    *          nonzero value.
    */
extern __declspec(__nothrow) long int ftell(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * obtains the current value of the file position indicator for the stream
    * pointed to by stream. For a binary stream, the value is the number of
    * characters from the beginning of the file. For a text stream, the file
    * position indicator contains unspecified information, usable by the fseek
    * function for returning the file position indicator to its position at the
    * time of the ftell call; the difference between two such return values is
    * not necessarily a meaningful measure of the number of characters written
    * or read.
    * Returns: if successful, the current value of the file position indicator.
    *          On failure, the ftell function returns -1L and sets the integer
    *          expression errno to an implementation-defined nonzero value.
    */
extern __declspec(__nothrow) void rewind(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * sets the file position indicator for the stream pointed to by stream to
    * the beginning of the file. It is equivalent to
    *          (void)fseek(stream, 0L, SEEK_SET)
    * except that the error indicator for the stream is also cleared.
    * Returns: no value.
    */

extern __declspec(__nothrow) void clearerr(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * clears the end-of-file and error indicators for the stream pointed to by
    * stream. These indicators are cleared only when the file is opened or by
    * an explicit call to the clearerr function or to the rewind function.
    * Returns: no value.
    */

extern __declspec(__nothrow) int feof(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * tests the end-of-file indicator for the stream pointed to by stream.
    * Returns: nonzero iff the end-of-file indicator is set for stream.
    */
extern __declspec(__nothrow) int ferror(FILE * /*stream*/) __attribute__((__nonnull__(1)));
   /*
    * tests the error indicator for the stream pointed to by stream.
    * Returns: nonzero iff the error indicator is set for stream.
    */
extern __declspec(__nothrow) void perror(const char * /*s*/);
   /*
    * maps the error number  in the integer expression errno to an error
    * message. It writes a sequence of characters to the standard error stream
    * thus: first (if s is not a null pointer and the character pointed to by
    * s is not the null character), the string pointed to by s followed by a
    * colon and a space; then an appropriate error message string followed by
    * a new-line character. The contents of the error message strings are the
    * same as those returned by the strerror function with argument errno,
    * which are implementation-defined.
    * Returns: no value.
    */

extern __declspec(__nothrow) int _fisatty(FILE * /*stream*/ ) __attribute__((__nonnull__(1)));
    /* Returns 1 if the stream is tty (stdin), 0 otherwise. Not ANSI compliant.
     */

extern __declspec(__nothrow) void __use_no_semihosting_swi(void);
extern __declspec(__nothrow) void __use_no_semihosting(void);
    /*
     * Referencing either of these symbols will cause a link-time
     * error if any library functions that use semihosting SWI
     * calls are also present in the link, i.e. you define it if
     * you want to make sure you haven't accidentally used any such
     * SWIs.
     */











# 1021 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdio.h"



/* end of stdio.h */

# 64 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
/* stdlib.h: ANSI draft (X3J11 May 88) library header, section 4.10 */
/* Copyright (C) Codemist Ltd., 1988-1993.                          */
/* Copyright 1991-1998,2014 ARM Limited. All rights reserved.       */
/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */
 
/*
 * stdlib.h declares four types, several general purpose functions,
 * and defines several macros.
 */






  /* armclang and non-strict armcc allow 'long long' in system headers */














  /*
   * Some of these declarations are new in C99.  To access them in C++
   * you can use -D__USE_C99_STDLIB (or -D__USE_C99ALL).
   */








# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 70 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"






   /* unconditional in non-strict C for consistency of debug info */



    typedef unsigned short wchar_t; /* see <stddef.h> */
# 91 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

typedef struct div_t { int quot, rem; } div_t;
   /* type of the value returned by the div function. */
typedef struct ldiv_t { long int quot, rem; } ldiv_t;
   /* type of the value returned by the ldiv function. */

typedef struct lldiv_t { long long quot, rem; } lldiv_t;
   /* type of the value returned by the lldiv function. */


# 112 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * an integral expression which may be used as an argument to the exit
    * function to return successful termination status to the host
    * environment.
    */

   /*
    * Defining __USE_ANSI_EXAMPLE_RAND at compile time switches to
    * the example implementation of rand() and srand() provided in
    * the ANSI C standard. This implementation is very poor, but is
    * provided for completeness.
    */
# 131 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * RAND_MAX: an integral constant expression, the value of which
    * is the maximum value returned by the rand function.
    */
extern __declspec(__nothrow) int __aeabi_MB_CUR_MAX(void);

   /*
    * a positive integer expression whose value is the maximum number of bytes
    * in a multibyte character for the extended character set specified by the
    * current locale (category LC_CTYPE), and whose value is never greater
    * than MB_LEN_MAX.
    */

   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain a compliant version of the strtod
    * family of functions.
    */




extern __declspec(__nothrow) double atof(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) int atoi(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to int
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) long int atol(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation.
    * Returns: the converted value.
    */

extern __declspec(__nothrow) long long atoll(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to
    * long long int representation.
    * Returns: the converted value.
    */


extern __declspec(__nothrow) double strtod(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling a
    * floating point constant; and a final string of one or more unrecognised
    * characters, including the terminating null character of the input string.
    * Then it attempts to convert the subject sequence to a floating point
    * number, and returns the result. A pointer to the final string is stored
    * in the object pointed to by endptr, provided that endptr is not a null
    * pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned. If the correct value is outside the range of
    *          representable values, plus or minus HUGE_VAL is returned
    *          (according to the sign of the value), and the value of the macro
    *          ERANGE is stored in errno. If the correct value would cause
    *          underflow, zero is returned and the value of the macro ERANGE is
    *          stored in errno.
    */

extern __declspec(__nothrow) float strtof(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) long double strtold(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * same as strtod, but return float and long double respectively.
    */

extern __declspec(__nothrow) long int strtol(const char * __restrict /*nptr*/,
                        char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling an
    * integer represented in some radix determined by the value of base, and a
    * final string of one or more unrecognised characters, including the
    * terminating null character of the input string. Then it attempts to
    * convert the subject sequence to an integer, and returns the result.
    * If the value of base is 0, the expected form of the subject sequence is
    * that of an integer constant (described in ANSI Draft, section 3.1.3.2),
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. If the value of base is between 2 and 36, the expected form of
    * the subject sequence is a sequence of letters and digits representing an
    * integer with the radix specified by base, optionally preceded by a plus
    * or minus sign, but not including an integer suffix. The letters from a
    * (or A) through z (or Z) are ascribed the values 10 to 35; only letters
    * whose ascribed values are less than that of the base are permitted. If
    * the value of base is 16, the characters 0x or 0X may optionally precede
    * the sequence of letters and digits following the sign if present.
    * A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, LONG_MAX or LONG_MIN is returned
    *          (according to the sign of the value), and the value of the
    *          macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long int strtoul(const char * __restrict /*nptr*/,
                                       char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to unsigned
    * long int representation. First it decomposes the input string into three
    * parts: an initial, possibly empty, sequence of white-space characters (as
    * determined by the isspace function), a subject sequence resembling an
    * unsigned integer represented in some radix determined by the value of
    * base, and a final string of one or more unrecognised characters,
    * including the terminating null character of the input string. Then it
    * attempts to convert the subject sequence to an unsigned integer, and
    * returns the result. If the value of base is zero, the expected form of
    * the subject sequence is that of an integer constant (described in ANSI
    * Draft, section 3.1.3.2), optionally preceded by a '+' or '-' sign, but
    * not including an integer suffix. If the value of base is between 2 and
    * 36, the expected form of the subject sequence is a sequence of letters
    * and digits representing an integer with the radix specified by base,
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. The letters from a (or A) through z (or Z) stand for the values
    * 10 to 35; only letters whose ascribed values are less than that of the
    * base are permitted. If the value of base is 16, the characters 0x or 0X
    * may optionally precede the sequence of letters and digits following the
    * sign, if present. A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, ULONG_MAX is returned, and the value of
    *          the macro ERANGE is stored in errno.
    */

/* C90 reserves all names beginning with 'str' */
extern __declspec(__nothrow) long long strtoll(const char * __restrict /*nptr*/,
                                  char ** __restrict /*endptr*/, int /*base*/)
                          __attribute__((__nonnull__(1)));
   /*
    * as strtol but returns a long long int value.  If the correct value is
    * outside the range of representable values,  LLONG_MAX or LLONG_MIN is
    * returned (according to the sign of the value), and the value of the
    * macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long long strtoull(const char * __restrict /*nptr*/,
                                            char ** __restrict /*endptr*/, int /*base*/)
                                   __attribute__((__nonnull__(1)));
   /*
    * as strtoul but returns an unsigned long long int value.  If the correct
    * value is outside the range of representable values, ULLONG_MAX is returned,
    * and the value of the macro ERANGE is stored in errno.
    */

extern __declspec(__nothrow) int rand(void);
   /*
    * Computes a sequence of pseudo-random integers in the range 0 to RAND_MAX.
    * Uses an additive generator (Mitchell & Moore) of the form:
    *   Xn = (X[n-24] + X[n-55]) MOD 2^31
    * This is described in section 3.2.2 of Knuth, vol 2. It's period is
    * in excess of 2^55 and its randomness properties, though unproven, are
    * conjectured to be good. Empirical testing since 1958 has shown no flaws.
    * Returns: a pseudo-random integer.
    */
extern __declspec(__nothrow) void srand(unsigned int /*seed*/);
   /*
    * uses its argument as a seed for a new sequence of pseudo-random numbers
    * to be returned by subsequent calls to rand. If srand is then called with
    * the same seed value, the sequence of pseudo-random numbers is repeated.
    * If rand is called before any calls to srand have been made, the same
    * sequence is generated as when srand is first called with a seed value
    * of 1.
    */

struct _rand_state { int __x[57]; };
extern __declspec(__nothrow) int _rand_r(struct _rand_state *);
extern __declspec(__nothrow) void _srand_r(struct _rand_state *, unsigned int);
struct _ANSI_rand_state { int __x[1]; };
extern __declspec(__nothrow) int _ANSI_rand_r(struct _ANSI_rand_state *);
extern __declspec(__nothrow) void _ANSI_srand_r(struct _ANSI_rand_state *, unsigned int);
   /*
    * Re-entrant variants of both flavours of rand, which operate on
    * an explicitly supplied state buffer.
    */

extern __declspec(__nothrow) void *calloc(size_t /*nmemb*/, size_t /*size*/);
   /*
    * allocates space for an array of nmemb objects, each of whose size is
    * 'size'. The space is initialised to all bits zero.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void free(void * /*ptr*/);
   /*
    * causes the space pointed to by ptr to be deallocated (i.e., made
    * available for further allocation). If ptr is a null pointer, no action
    * occurs. Otherwise, if ptr does not match a pointer earlier returned by
    * calloc, malloc or realloc or if the space has been deallocated by a call
    * to free or realloc, the behaviour is undefined.
    */
extern __declspec(__nothrow) void *malloc(size_t /*size*/);
   /*
    * allocates space for an object whose size is specified by 'size' and whose
    * value is indeterminate.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void *realloc(void * /*ptr*/, size_t /*size*/);
   /*
    * changes the size of the object pointed to by ptr to the size specified by
    * size. The contents of the object shall be unchanged up to the lesser of
    * the new and old sizes. If the new size is larger, the value of the newly
    * allocated portion of the object is indeterminate. If ptr is a null
    * pointer, the realloc function behaves like a call to malloc for the
    * specified size. Otherwise, if ptr does not match a pointer earlier
    * returned by calloc, malloc or realloc, or if the space has been
    * deallocated by a call to free or realloc, the behaviour is undefined.
    * If the space cannot be allocated, the object pointed to by ptr is
    * unchanged. If size is zero and ptr is not a null pointer, the object it
    * points to is freed.
    * Returns: either a null pointer or a pointer to the possibly moved
    *          allocated space.
    */

extern __declspec(__nothrow) int posix_memalign(void ** /*ret*/, size_t /*alignment*/, size_t /*size*/);
   /*
    * allocates space for an object of size 'size', aligned to a
    * multiple of 'alignment' (which must be a power of two and at
    * least 4).
    *
    * On success, a pointer to the allocated object is stored in
    * *ret, and zero is returned. On failure, the return value is
    * either ENOMEM (allocation failed because no suitable piece of
    * memory was available) or EINVAL (the 'alignment' parameter was
    * invalid).
    */

typedef int (*__heapprt)(void *, char const *, ...);
extern __declspec(__nothrow) void __heapstats(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                        void * /*param*/) __attribute__((__nonnull__(1)));
   /*
    * reports current heap statistics (eg. number of free blocks in
    * the free-list). Output is as implementation-defined free-form
    * text, provided via the dprint function. `param' gives an
    * extra data word to pass to dprint. You can call
    * __heapstats(fprintf,stdout) by casting fprintf to the above
    * function type; the typedef `__heapprt' is provided for this
    * purpose.
    *
    * `dprint' will not be called while the heap is being examined,
    * so it can allocate memory itself without trouble.
    */
extern __declspec(__nothrow) int __heapvalid(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                       void * /*param*/, int /*verbose*/) __attribute__((__nonnull__(1)));
   /*
    * performs a consistency check on the heap. Errors are reported
    * through dprint, like __heapstats. If `verbose' is nonzero,
    * full diagnostic information on the heap state is printed out.
    *
    * This routine probably won't work if the heap isn't a
    * contiguous chunk (for example, if __user_heap_extend has been
    * overridden).
    *
    * `dprint' may be called while the heap is being examined or
    * even in an invalid state, so it must perform no memory
    * allocation. In particular, if `dprint' calls (or is) a stdio
    * function, the stream it outputs to must already have either
    * been written to or been setvbuf'ed, or else the system will
    * allocate buffer space for it on the first call to dprint.
    */
extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
   /*
    * causes abnormal program termination to occur, unless the signal SIGABRT
    * is being caught and the signal handler does not return. Whether open
    * output streams are flushed or open streams are closed or temporary
    * files removed is implementation-defined.
    * An implementation-defined form of the status 'unsuccessful termination'
    * is returned to the host environment by means of a call to
    * raise(SIGABRT).
    */

extern __declspec(__nothrow) int atexit(void (* /*func*/)(void)) __attribute__((__nonnull__(1)));
   /*
    * registers the function pointed to by func, to be called without its
    * arguments at normal program termination. It is possible to register at
    * least 32 functions.
    * Returns: zero if the registration succeeds, nonzero if it fails.
    */
# 436 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern __declspec(__nothrow) __declspec(__noreturn) void exit(int /*status*/);
   /*
    * causes normal program termination to occur. If more than one call to the
    * exit function is executed by a program, the behaviour is undefined.
    * First, all functions registered by the atexit function are called, in the
    * reverse order of their registration.
    * Next, all open output streams are flushed, all open streams are closed,
    * and all files created by the tmpfile function are removed.
    * Finally, control is returned to the host environment. If the value of
    * status is zero or EXIT_SUCCESS, an implementation-defined form of the
    * status 'successful termination' is returned. If the value of status is
    * EXIT_FAILURE, an implementation-defined form of the status
    * 'unsuccessful termination' is returned. Otherwise the status returned
    * is implementation-defined.
    */

extern __declspec(__nothrow) __declspec(__noreturn) void _Exit(int /*status*/);
   /*
    * causes normal program termination to occur. No functions registered
    * by the atexit function are called.
    * In this implementation, all open output streams are flushed, all
    * open streams are closed, and all files created by the tmpfile function
    * are removed.
    * Control is returned to the host environment. The status returned to
    * the host environment is determined in the same way as for 'exit'.
    */     

extern __declspec(__nothrow) char *getenv(const char * /*name*/) __attribute__((__nonnull__(1)));
   /*
    * searches the environment list, provided by the host environment, for a
    * string that matches the string pointed to by name. The set of environment
    * names and the method for altering the environment list are
    * implementation-defined.
    * Returns: a pointer to a string associated with the matched list member.
    *          The array pointed to shall not be modified by the program, but
    *          may be overwritten by a subsequent call to the getenv function.
    *          If the specified name cannot be found, a null pointer is
    *          returned.
    */

extern __declspec(__nothrow) int  system(const char * /*string*/);
   /*
    * passes the string pointed to by string to the host environment to be
    * executed by a command processor in an implementation-defined manner.
    * A null pointer may be used for string, to inquire whether a command
    * processor exists.
    *
    * Returns: If the argument is a null pointer, the system function returns
    *          non-zero only if a command processor is available. If the
    *          argument is not a null pointer, the system function returns an
    *          implementation-defined value.
    */

extern  void *bsearch(const void * /*key*/, const void * /*base*/,
              size_t /*nmemb*/, size_t /*size*/,
              int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,2,5)));
   /*
    * searches an array of nmemb objects, the initial member of which is
    * pointed to by base, for a member that matches the object pointed to by
    * key. The size of each member of the array is specified by size.
    * The contents of the array shall be in ascending sorted order according to
    * a comparison function pointed to by compar, which is called with two
    * arguments that point to the key object and to an array member, in that
    * order. The function shall return an integer less than, equal to, or
    * greater than zero if the key object is considered, respectively, to be
    * less than, to match, or to be greater than the array member.
    * Returns: a pointer to a matching member of the array, or a null pointer
    *          if no match is found. If two members compare as equal, which
    *          member is matched is unspecified.
    */
# 524 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern  void qsort(void * /*base*/, size_t /*nmemb*/, size_t /*size*/,
           int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,4)));
   /*
    * sorts an array of nmemb objects, the initial member of which is pointed
    * to by base. The size of each object is specified by size.
    * The contents of the array shall be in ascending order according to a
    * comparison function pointed to by compar, which is called with two
    * arguments that point to the objects being compared. The function shall
    * return an integer less than, equal to, or greater than zero if the first
    * argument is considered to be respectively less than, equal to, or greater
    * than the second. If two members compare as equal, their order in the
    * sorted array is unspecified.
    */

# 553 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

extern __declspec(__nothrow) __attribute__((const)) int abs(int /*j*/);
   /*
    * computes the absolute value of an integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */

extern __declspec(__nothrow) __attribute__((const)) div_t div(int /*numer*/, int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the resulting
    * quotient is the integer of lesser magnitude that is the nearest to the
    * algebraic quotient. If the result cannot be represented, the behaviour is
    * undefined; otherwise, quot * denom + rem shall equal numer.
    * Returns: a structure of type div_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          int quot; int rem;
    */
extern __declspec(__nothrow) __attribute__((const)) long int labs(long int /*j*/);
   /*
    * computes the absolute value of an long integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) ldiv_t ldiv(long int /*numer*/, long int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type ldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long int quot; long int rem;
    */







extern __declspec(__nothrow) __attribute__((const)) long long llabs(long long /*j*/);
   /*
    * computes the absolute value of a long long integer j. If the
    * result cannot be represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) lldiv_t lldiv(long long /*numer*/, long long /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type lldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long long quot; long long rem;
    */
# 634 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


/*
 * ARM real-time divide functions for guaranteed performance
 */
typedef struct __sdiv32by16 { int quot, rem; } __sdiv32by16;
typedef struct __udiv32by16 { unsigned int quot, rem; } __udiv32by16;
   /* used int so that values return in separate regs, although 16-bit */
typedef struct __sdiv64by32 { int rem, quot; } __sdiv64by32;

__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv32by16 __rt_sdiv32by16(
     int /*numer*/,
     short int /*denom*/);
   /*
    * Signed divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __udiv32by16 __rt_udiv32by16(
     unsigned int /*numer*/,
     unsigned short /*denom*/);
   /*
    * Unsigned divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv64by32 __rt_sdiv64by32(
     int /*numer_h*/, unsigned int /*numer_l*/,
     int /*denom*/);
   /*
    * Signed divide: (32-bit quot), (32-bit rem) = (64-bit) / (32-bit)
    */


/*
 * ARM floating-point mask/status function (for both hardfp and softfp)
 */
extern __declspec(__nothrow) unsigned int __fp_status(unsigned int /*mask*/, unsigned int /*flags*/);
   /*
    * mask and flags are bit-fields which correspond directly to the
    * floating point status register in the FPE/FPA and fplib.  
    * __fp_status returns the current value of the status register,
    * and also sets the writable bits of the word
    * (the exception control and flag bytes) to:
    *
    *     new = (old & ~mask) ^ flags;
    */












/*
 * Multibyte Character Functions.
 * The behaviour of the multibyte character functions is affected by the
 * LC_CTYPE category of the current locale. For a state-dependent encoding,
 * each function is placed into its initial state by a call for which its
 * character pointer argument, s, is a null pointer. Subsequent calls with s
 * as other than a null pointer cause the internal state of the function to be
 * altered as necessary. A call with s as a null pointer causes these functions
 * to return a nonzero value if encodings have state dependency, and a zero
 * otherwise. After the LC_CTYPE category is changed, the shift state of these
 * functions is indeterminate.
 */
extern __declspec(__nothrow) int mblen(const char * /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mblen function determines the number of
    * bytes compromising the multibyte character pointed to by s. Except that
    * the shift state of the mbtowc function is not affected, it is equivalent
    * to   mbtowc((wchar_t *)0, s, n);
    * Returns: If s is a null pointer, the mblen function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mblen function either returns a 0 (if s points to a
    *          null character), or returns the number of bytes that compromise
    *          the multibyte character (if the next n of fewer bytes form a
    *          valid multibyte character), or returns -1 (they do not form a
    *          valid multibyte character).
    */
extern __declspec(__nothrow) int mbtowc(wchar_t * __restrict /*pwc*/,
                   const char * __restrict /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mbtowc function determines the number of
    * bytes that compromise the multibyte character pointed to by s. It then
    * determines the code for value of type wchar_t that corresponds to that
    * multibyte character. (The value of the code corresponding to the null
    * character is zero). If the multibyte character is valid and pwc is not a
    * null pointer, the mbtowc function stores the code in the object pointed
    * to by pwc. At most n bytes of the array pointed to by s will be examined.
    * Returns: If s is a null pointer, the mbtowc function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mbtowc function either returns a 0 (if s points to
    *          a null character), or returns the number of bytes that
    *          compromise the converted multibyte character (if the next n of
    *          fewer bytes form a valid multibyte character), or returns -1
    *          (they do not form a valid multibyte character).
    */
extern __declspec(__nothrow) int wctomb(char * /*s*/, wchar_t /*wchar*/);
   /*
    * determines the number of bytes need to represent the multibyte character
    * corresponding to the code whose value is wchar (including any change in
    * shift state). It stores the multibyte character representation in the
    * array object pointed to by s (if s is not a null pointer). At most
    * MB_CUR_MAX characters are stored. If the value of wchar is zero, the
    * wctomb function is left in the initial shift state).
    * Returns: If s is a null pointer, the wctomb function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the wctomb function returns a -1 if the value of wchar
    *          does not correspond to a valid multibyte character, or returns
    *          the number of bytes that compromise the multibyte character
    *          corresponding to the value of wchar.
    */

/*
 * Multibyte String Functions.
 * The behaviour of the multibyte string functions is affected by the LC_CTYPE
 * category of the current locale.
 */
extern __declspec(__nothrow) size_t mbstowcs(wchar_t * __restrict /*pwcs*/,
                      const char * __restrict /*s*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of multibyte character that begins in the initial
    * shift state from the array pointed to by s into a sequence of
    * corresponding codes and stores not more than n codes into the array
    * pointed to by pwcs. No multibyte character that follow a null character
    * (which is converted into a code with value zero) will be examined or
    * converted. Each multibyte character is converted as if by a call to
    * mbtowc function, except that the shift state of the mbtowc function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by pwcs. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If an invalid multibyte character is encountered, the mbstowcs
    *          function returns (size_t)-1. Otherwise, the mbstowcs function
    *          returns the number of array elements modified, not including
    *          a terminating zero code, if any.
    */
extern __declspec(__nothrow) size_t wcstombs(char * __restrict /*s*/,
                      const wchar_t * __restrict /*pwcs*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of codes that correspond to multibyte characters
    * from the array pointed to by pwcs into a sequence of multibyte
    * characters that begins in the initial shift state and stores these
    * multibyte characters into the array pointed to by s, stopping if a
    * multibyte character would exceed the limit of n total bytes or if a
    * null character is stored. Each code is converted as if by a call to the
    * wctomb function, except that the shift state of the wctomb function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by s. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If a code is encountered that does not correspond to a valid
    *          multibyte character, the wcstombs function returns (size_t)-1.
    *          Otherwise, the wcstombs function returns the number of bytes
    *          modified, not including a terminating null character, if any.
    */

extern __declspec(__nothrow) void __use_realtime_heap(void);
extern __declspec(__nothrow) void __use_realtime_division(void);
extern __declspec(__nothrow) void __use_two_region_memory(void);
extern __declspec(__nothrow) void __use_no_heap(void);
extern __declspec(__nothrow) void __use_no_heap_region(void);

extern __declspec(__nothrow) char const *__C_library_version_string(void);
extern __declspec(__nothrow) int __C_library_version_number(void);











# 892 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"





/* end of stdlib.h */
# 65 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"
# 67 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"
# 125 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"


/** \} name SECTION: Module settings */

/*
 * The function pointers for calloc and free.
 */
# 159 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The function pointers for fprintf
 */
# 186 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The function pointers for printf
 */
# 210 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The function pointers for snprintf
 *
 * The snprintf implementation should conform to C99:
 * - it *must* always correctly zero-terminate the buffer
 *   (except when n == 0, then it must leave the buffer untouched)
 * - however it is acceptable to return -1 instead of the required length when
 *   the destination buffer is too short.
 */





# 245 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The function pointers for vsnprintf
 *
 * The vsnprintf implementation should conform to C99:
 * - it *must* always correctly zero-terminate the buffer
 *   (except when n == 0, then it must leave the buffer untouched)
 * - however it is acceptable to return -1 instead of the required length when
 *   the destination buffer is too short.
 */






# 281 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The function pointers for setbuf
 */
# 331 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The function pointers for exit
 */
# 355 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The default exit values
 */
# 369 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"

/*
 * The function pointers for reading from and writing a seed file to
 * Non-Volatile storage (NV) in a platform-independent way
 *
 * Only enabled when the NV seed entropy source is enabled
 */
# 411 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h"



/**
 * \brief   The platform context structure.
 *
 * \note    This structure may be used to assist platform-specific
 *          setup or teardown operations.
 */
typedef struct mbedtls_platform_context
{
    char dummy; /**< A placeholder member, as empty structs are not portable. */
}
mbedtls_platform_context;





/**
 * \brief   This function performs any platform-specific initialization
 *          operations.
 *
 * \note    This function should be called before any other library functions.
 *
 *          Its implementation is platform-specific, and unless
 *          platform-specific code is provided, it does nothing.
 *
 * \note    The usage and necessity of this function is dependent on the platform.
 *
 * \param   ctx     The platform context.
 *
 * \return  \c 0 on success.
 */
int mbedtls_platform_setup( mbedtls_platform_context *ctx );
/**
 * \brief   This function performs any platform teardown operations.
 *
 * \note    This function should be called after every other Mbed TLS module
 *          has been correctly freed using the appropriate free function.
 *
 *          Its implementation is platform-specific, and unless
 *          platform-specific code is provided, it does nothing.
 *
 * \note    The usage and necessity of this function is dependent on the platform.
 *
 * \param   ctx     The platform context.
 *
 */
void mbedtls_platform_teardown( mbedtls_platform_context *ctx );





# 41 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"






# 48 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"
/**
 * \file error.h
 *
 * \brief Error to string translation
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 26 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"

# 28 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"






/**
 * Error code layout.
 *
 * Currently we try to keep all error codes within the negative space of 16
 * bits signed integers to support all platforms (-0x0001 - -0x7FFF). In
 * addition we'd like to give two layers of information on the error if
 * possible.
 *
 * For that purpose the error codes are segmented in the following manner:
 *
 * 16 bit error code bit-segmentation
 *
 * 1 bit  - Unused (sign bit)
 * 3 bits - High level module ID
 * 5 bits - Module-dependent error code
 * 7 bits - Low level module errors
 *
 * For historical reasons, low-level error codes are divided in even and odd,
 * even codes were assigned first, and -1 is reserved for other errors.
 *
 * Low-level module errors (0x0002-0x007E, 0x0001-0x007F)
 *
 * Module   Nr  Codes assigned
 * ERROR     2  0x006E          0x0001
 * MPI       7  0x0002-0x0010
 * GCM       3  0x0012-0x0016   0x0013-0x0013
 * THREADING 3  0x001A-0x001E
 * AES       5  0x0020-0x0022   0x0021-0x0025
 * CAMELLIA  3  0x0024-0x0026   0x0027-0x0027
 * BASE64    2  0x002A-0x002C
 * OID       1  0x002E-0x002E   0x000B-0x000B
 * PADLOCK   1  0x0030-0x0030
 * DES       2  0x0032-0x0032   0x0033-0x0033
 * CTR_DBRG  4  0x0034-0x003A
 * ENTROPY   3  0x003C-0x0040   0x003D-0x003F
 * NET      13  0x0042-0x0052   0x0043-0x0049
 * ARIA      4  0x0058-0x005E
 * ASN1      7  0x0060-0x006C
 * CMAC      1  0x007A-0x007A
 * PBKDF2    1  0x007C-0x007C
 * HMAC_DRBG 4                  0x0003-0x0009
 * CCM       3                  0x000D-0x0011
 * MD5       1                  0x002F-0x002F
 * RIPEMD160 1                  0x0031-0x0031
 * SHA1      1                  0x0035-0x0035 0x0073-0x0073
 * SHA256    1                  0x0037-0x0037 0x0074-0x0074
 * SHA512    1                  0x0039-0x0039 0x0075-0x0075
 * CHACHA20  3                  0x0051-0x0055
 * POLY1305  3                  0x0057-0x005B
 * CHACHAPOLY 2 0x0054-0x0056
 * PLATFORM  2  0x0070-0x0072
 *
 * High-level module nr (3 bits - 0x0...-0x7...)
 * Name      ID  Nr of Errors
 * PEM       1   9
 * PKCS#12   1   4 (Started from top)
 * X509      2   20
 * PKCS5     2   4 (Started from top)
 * DHM       3   11
 * PK        3   15 (Started from top)
 * RSA       4   11
 * ECP       4   10 (Started from top)
 * MD        5   5
 * HKDF      5   1 (Started from top)
 * SSL       5   2 (Started from 0x5F00)
 * CIPHER    6   8 (Started from 0x6080)
 * SSL       6   22 (Started from top, plus 0x6000)
 * SSL       7   20 (Started from 0x7000, gaps at
 *                   0x7380, 0x7900-0x7980, 0x7A80-0x7E80)
 *
 * Module dependent error code (5 bits 0x.00.-0x.F8.)
 */





/** Generic error */

/** This is a bug in the library */


/** Hardware accelerator failed */

/** The requested feature is not supported by the platform */


/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        Wrapper macro for mbedtls_error_add(). See that function for
 *        more details.
 */



# 137 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"

/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        This function can be called directly however it is usually
 *        called via the #MBEDTLS_ERROR_ADD macro.
 *
 *        While a value of zero is not a negative error code, it is still an
 *        error code (that denotes success) and can be combined with both a
 *        negative error code or another value of zero.
 *
 * \note  When invasive testing is enabled via #MBEDTLS_TEST_HOOKS, also try to
 *        call \link mbedtls_test_hook_error_add \endlink.
 *
 * \param high      high-level error code. See error.h for more details.
 * \param low       low-level error code. See error.h for more details.
 * \param file      file where this error code addition occurred.
 * \param line      line where this error code addition occurred.
 */
static __inline int mbedtls_error_add( int high, int low,
                                     const char *file, int line )
{




    (void)file;
    (void)line;

    return( high + low );
}

/**
 * \brief Translate a mbed TLS error code into a string representation,
 *        Result is truncated if necessary and always includes a terminating
 *        null byte.
 *
 * \param errnum    error code
 * \param buffer    buffer to place representation in
 * \param buflen    length of the buffer
 */
void mbedtls_strerror( int errnum, char *buffer, size_t buflen );

/**
 * \brief Translate the high-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char * mbedtls_high_level_strerr( int error_code );

/**
 * \brief Translate the low-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char * mbedtls_low_level_strerr( int error_code );





# 49 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"

/* Parameter validation macros based on platform_util.h */





# 210 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"













/*
 * Derive a suitable integer for group grp from a buffer of length len
 * SEC1 4.1.3 step 5 aka SEC1 4.1.4 step 3
 */
static int derive_mpi( const mbedtls_ecp_group *grp, mbedtls_mpi *x,
                       const unsigned char *buf, size_t blen )
{
    int ret = -0x006E;
    size_t n_size = ( grp->nbits + 7 ) / 8;
    size_t use_size = blen > n_size ? n_size : blen;

    do { if( ( ret = (mbedtls_mpi_read_binary( x, buf, use_size )) ) != 0 ) goto cleanup; } while( 0 );
    if( use_size * 8 > grp->nbits )
        do { if( ( ret = (mbedtls_mpi_shift_r( x, use_size * 8 - grp->nbits )) ) != 0 ) goto cleanup; } while( 0 );

    /* While at it, reduce modulo N */
    if( mbedtls_mpi_cmp_mpi( x, &grp->N ) >= 0 )
        do { if( ( ret = (mbedtls_mpi_sub_mpi( x, x, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );

cleanup:
    return( ret );
}



/*
 * Compute ECDSA signature of a hashed message (SEC1 4.1.3)
 * Obviously, compared to SEC1 4.1.3, we skip step 4 (hash message)
 */
static int ecdsa_sign_restartable( mbedtls_ecp_group *grp,
                mbedtls_mpi *r, mbedtls_mpi *s,
                const mbedtls_mpi *d, const unsigned char *buf, size_t blen,
                int (*f_rng)(void *, unsigned char *, size_t), void *p_rng,
                int (*f_rng_blind)(void *, unsigned char *, size_t),
                void *p_rng_blind,
                mbedtls_ecdsa_restart_ctx *rs_ctx )
{
    int ret, key_tries, sign_tries;
    int *p_sign_tries = &sign_tries, *p_key_tries = &key_tries;
    mbedtls_ecp_point R;
    mbedtls_mpi k, e, t;
    mbedtls_mpi *pk = &k, *pr = r;

    /* Fail cleanly on curves such as Curve25519 that can't be used for ECDSA */
    if( ! mbedtls_ecdsa_can_do( grp->id ) || grp->N.p == 0 )
        return( -0x4F80 );

    /* Make sure d is in range 1..n-1 */
    if( mbedtls_mpi_cmp_int( d, 1 ) < 0 || mbedtls_mpi_cmp_mpi( d, &grp->N ) >= 0 )
        return( -0x4C80 );

    mbedtls_ecp_point_init( &R );
    mbedtls_mpi_init( &k ); mbedtls_mpi_init( &e ); mbedtls_mpi_init( &t );

    (void) rs_ctx;

# 295 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"

    *p_sign_tries = 0;
    do
    {
        if( (*p_sign_tries)++ > 10 )
        {
            ret = -0x4D00;
            goto cleanup;
        }

        /*
         * Steps 1-3: generate a suitable ephemeral keypair
         * and set r = xR mod n
         */
        *p_key_tries = 0;
        do
        {
            if( (*p_key_tries)++ > 10 )
            {
                ret = -0x4D00;
                goto cleanup;
            }

            do { if( ( ret = (mbedtls_ecp_gen_privkey( grp, pk, f_rng, p_rng )) ) != 0 ) goto cleanup; } while( 0 );

# 326 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
            do { if( ( ret = (mbedtls_ecp_mul_restartable( grp, &R, pk, &grp->G, f_rng_blind, p_rng_blind, 0 )) ) != 0 ) goto cleanup; } while( 0 );



            do { if( ( ret = (mbedtls_mpi_mod_mpi( pr, &R . X, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );
        }
        while( mbedtls_mpi_cmp_int( pr, 0 ) == 0 );

# 340 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
        /*
         * Accounting for everything up to the end of the loop
         * (step 6, but checking now avoids saving e and t)
         */
        ;

        /*
         * Step 5: derive MPI from hashed message
         */
        do { if( ( ret = (derive_mpi( grp, &e, buf, blen )) ) != 0 ) goto cleanup; } while( 0 );

        /*
         * Generate a random value to blind inv_mod in next step,
         * avoiding a potential timing leak.
         */
        do { if( ( ret = (mbedtls_ecp_gen_privkey( grp, &t, f_rng_blind, p_rng_blind )) ) != 0 ) goto cleanup; } while( 0 );


        /*
         * Step 6: compute s = (e + r * d) / k = t (e + rd) / (kt) mod n
         */
        do { if( ( ret = (mbedtls_mpi_mul_mpi( s, pr, d )) ) != 0 ) goto cleanup; } while( 0 );
        do { if( ( ret = (mbedtls_mpi_add_mpi( &e, &e, s )) ) != 0 ) goto cleanup; } while( 0 );
        do { if( ( ret = (mbedtls_mpi_mul_mpi( &e, &e, &t )) ) != 0 ) goto cleanup; } while( 0 );
        do { if( ( ret = (mbedtls_mpi_mul_mpi( pk, pk, &t )) ) != 0 ) goto cleanup; } while( 0 );
        do { if( ( ret = (mbedtls_mpi_mod_mpi( pk, pk, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );
        do { if( ( ret = (mbedtls_mpi_inv_mod( s, pk, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );
        do { if( ( ret = (mbedtls_mpi_mul_mpi( s, s, &e )) ) != 0 ) goto cleanup; } while( 0 );
        do { if( ( ret = (mbedtls_mpi_mod_mpi( s, s, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );
    }
    while( mbedtls_mpi_cmp_int( s, 0 ) == 0 );






cleanup:
    mbedtls_ecp_point_free( &R );
    mbedtls_mpi_free( &k ); mbedtls_mpi_free( &e ); mbedtls_mpi_free( &t );

    (void) rs_ctx;

    return( ret );
}

int mbedtls_ecdsa_can_do( mbedtls_ecp_group_id gid )
{
    switch( gid )
    {
# 396 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
    default: return 1;
    }
}

/*
 * Compute ECDSA signature of a hashed message
 */
int mbedtls_ecdsa_sign( mbedtls_ecp_group *grp, mbedtls_mpi *r, mbedtls_mpi *s,
                const mbedtls_mpi *d, const unsigned char *buf, size_t blen,
                int (*f_rng)(void *, unsigned char *, size_t), void *p_rng )
{
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );

    /* Use the same RNG for both blinding and ephemeral key generation */
    return( ecdsa_sign_restartable( grp, r, s, d, buf, blen,
                                    f_rng, p_rng, f_rng, p_rng, 0 ) );
}


# 517 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"


/*
 * Verify ECDSA signature of hashed message (SEC1 4.1.4)
 * Obviously, compared to SEC1 4.1.3, we skip step 2 (hash message)
 */
static int ecdsa_verify_restartable( mbedtls_ecp_group *grp,
                                     const unsigned char *buf, size_t blen,
                                     const mbedtls_ecp_point *Q,
                                     const mbedtls_mpi *r, const mbedtls_mpi *s,
                                     mbedtls_ecdsa_restart_ctx *rs_ctx )
{
    int ret = -0x006E;
    mbedtls_mpi e, s_inv, u1, u2;
    mbedtls_ecp_point R;
    mbedtls_mpi *pu1 = &u1, *pu2 = &u2;

    mbedtls_ecp_point_init( &R );
    mbedtls_mpi_init( &e ); mbedtls_mpi_init( &s_inv );
    mbedtls_mpi_init( &u1 ); mbedtls_mpi_init( &u2 );

    /* Fail cleanly on curves such as Curve25519 that can't be used for ECDSA */
    if( ! mbedtls_ecdsa_can_do( grp->id ) || grp->N.p == 0 )
        return( -0x4F80 );

    (void) rs_ctx;

# 556 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"

    /*
     * Step 1: make sure r and s are in range 1..n-1
     */
    if( mbedtls_mpi_cmp_int( r, 1 ) < 0 || mbedtls_mpi_cmp_mpi( r, &grp->N ) >= 0 ||
        mbedtls_mpi_cmp_int( s, 1 ) < 0 || mbedtls_mpi_cmp_mpi( s, &grp->N ) >= 0 )
    {
        ret = -0x4E00;
        goto cleanup;
    }

    /*
     * Step 3: derive MPI from hashed message
     */
    do { if( ( ret = (derive_mpi( grp, &e, buf, blen )) ) != 0 ) goto cleanup; } while( 0 );

    /*
     * Step 4: u1 = e / s mod n, u2 = r / s mod n
     */
    ;

    do { if( ( ret = (mbedtls_mpi_inv_mod( &s_inv, s, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );

    do { if( ( ret = (mbedtls_mpi_mul_mpi( pu1, &e, &s_inv )) ) != 0 ) goto cleanup; } while( 0 );
    do { if( ( ret = (mbedtls_mpi_mod_mpi( pu1, pu1, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );

    do { if( ( ret = (mbedtls_mpi_mul_mpi( pu2, r, &s_inv )) ) != 0 ) goto cleanup; } while( 0 );
    do { if( ( ret = (mbedtls_mpi_mod_mpi( pu2, pu2, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );

# 591 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
    /*
     * Step 5: R = u1 G + u2 Q
     */
    do { if( ( ret = (mbedtls_ecp_muladd_restartable( grp, &R, pu1, &grp->G, pu2, Q, 0 )) ) != 0 ) goto cleanup; } while( 0 );


    if( mbedtls_ecp_is_zero( &R ) )
    {
        ret = -0x4E00;
        goto cleanup;
    }

    /*
     * Step 6: convert xR to an integer (no-op)
     * Step 7: reduce xR mod n (gives v)
     */
    do { if( ( ret = (mbedtls_mpi_mod_mpi( &R . X, &R . X, &grp->N )) ) != 0 ) goto cleanup; } while( 0 );

    /*
     * Step 8: check if v (that is, R.X) is equal to r
     */
    if( mbedtls_mpi_cmp_mpi( &R.X, r ) != 0 )
    {
        ret = -0x4E00;
        goto cleanup;
    }

cleanup:
    mbedtls_ecp_point_free( &R );
    mbedtls_mpi_free( &e ); mbedtls_mpi_free( &s_inv );
    mbedtls_mpi_free( &u1 ); mbedtls_mpi_free( &u2 );

    (void) rs_ctx;

    return( ret );
}

/*
 * Verify ECDSA signature of hashed message
 */
int mbedtls_ecdsa_verify( mbedtls_ecp_group *grp,
                          const unsigned char *buf, size_t blen,
                          const mbedtls_ecp_point *Q,
                          const mbedtls_mpi *r,
                          const mbedtls_mpi *s)
{
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );

    return( ecdsa_verify_restartable( grp, buf, blen, Q, r, s, 0 ) );
}


/*
 * Convert a signature (given by context) to ASN.1
 */
static int ecdsa_signature_to_asn1( const mbedtls_mpi *r, const mbedtls_mpi *s,
                                    unsigned char *sig, size_t sig_size,
                                    size_t *slen )
{
    int ret = -0x006E;
    unsigned char buf[( ( ( 512 ) >= 61 * 8 ? 3 : 2 ) + 2 * ( ( ( 512 ) >= 127 * 8 ? 3 : 2 ) + ( ( 512 ) + 8 ) / 8 ) )] = {0};
    unsigned char *p = buf + sizeof( buf );
    size_t len = 0;

    do { if( ( ret = (mbedtls_asn1_write_mpi( &p, buf, s )) ) < 0 ) return( ret ); else (len) += ret; } while( 0 );
    do { if( ( ret = (mbedtls_asn1_write_mpi( &p, buf, r )) ) < 0 ) return( ret ); else (len) += ret; } while( 0 );

    do { if( ( ret = (mbedtls_asn1_write_len( &p, buf, len )) ) < 0 ) return( ret ); else (len) += ret; } while( 0 );
    do { if( ( ret = (mbedtls_asn1_write_tag( &p, buf, 0x20 | 0x10 )) ) < 0 ) return( ret ); else (len) += ret; } while( 0 );


    if( len > sig_size )
        return( -0x4F00 );

    memcpy( sig, p, len );
    *slen = len;

    return( 0 );
}

/*
 * Compute and write signature
 */
int mbedtls_ecdsa_write_signature_restartable( mbedtls_ecdsa_context *ctx,
                           mbedtls_md_type_t md_alg,
                           const unsigned char *hash, size_t hlen,
                           unsigned char *sig, size_t sig_size, size_t *slen,
                           int (*f_rng)(void *, unsigned char *, size_t),
                           void *p_rng,
                           mbedtls_ecdsa_restart_ctx *rs_ctx )
{
    int ret = -0x006E;
    mbedtls_mpi r, s;
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );

    if( f_rng == 0 )
        return( -0x4F80 );

    mbedtls_mpi_init( &r );
    mbedtls_mpi_init( &s );






    (void) md_alg;

# 712 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
    /* Use the same RNG for both blinding and ephemeral key generation */
    do { if( ( ret = (ecdsa_sign_restartable( &ctx->grp, &r, &s, &ctx->d, hash, hlen, f_rng, p_rng, f_rng, p_rng, rs_ctx )) ) != 0 ) goto cleanup; } while( 0 );





    do { if( ( ret = (ecdsa_signature_to_asn1( &r, &s, sig, sig_size, slen )) ) != 0 ) goto cleanup; } while( 0 );

cleanup:
    mbedtls_mpi_free( &r );
    mbedtls_mpi_free( &s );

    return( ret );
}

/*
 * Compute and write signature
 */
int mbedtls_ecdsa_write_signature( mbedtls_ecdsa_context *ctx,
                                 mbedtls_md_type_t md_alg,
                                 const unsigned char *hash, size_t hlen,
                                 unsigned char *sig, size_t sig_size, size_t *slen,
                                 int (*f_rng)(void *, unsigned char *, size_t),
                                 void *p_rng )
{
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    return( mbedtls_ecdsa_write_signature_restartable(
                ctx, md_alg, hash, hlen, sig, sig_size, slen,
                f_rng, p_rng, 0 ) );
}

/*
 * Read and check signature
 */
int mbedtls_ecdsa_read_signature( mbedtls_ecdsa_context *ctx,
                          const unsigned char *hash, size_t hlen,
                          const unsigned char *sig, size_t slen )
{
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );
    return( mbedtls_ecdsa_read_signature_restartable(
                ctx, hash, hlen, sig, slen, 0 ) );
}

/*
 * Restartable read and check signature
 */
int mbedtls_ecdsa_read_signature_restartable( mbedtls_ecdsa_context *ctx,
                          const unsigned char *hash, size_t hlen,
                          const unsigned char *sig, size_t slen,
                          mbedtls_ecdsa_restart_ctx *rs_ctx )
{
    int ret = -0x006E;
    unsigned char *p = (unsigned char *) sig;
    const unsigned char *end = sig + slen;
    size_t len;
    mbedtls_mpi r, s;
    do { } while( 0 );
    do { } while( 0 );
    do { } while( 0 );

    mbedtls_mpi_init( &r );
    mbedtls_mpi_init( &s );

    if( ( ret = mbedtls_asn1_get_tag( &p, end, &len,
                    0x20 | 0x10 ) ) != 0 )
    {
        ret += -0x4F80;
        goto cleanup;
    }

    if( p + len != end )
    {
        ret = mbedtls_error_add( -0x4F80, -0x0066, "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c", 790 );

        goto cleanup;
    }

    if( ( ret = mbedtls_asn1_get_mpi( &p, end, &r ) ) != 0 ||
        ( ret = mbedtls_asn1_get_mpi( &p, end, &s ) ) != 0 )
    {
        ret += -0x4F80;
        goto cleanup;
    }
# 808 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"
    if( ( ret = ecdsa_verify_restartable( &ctx->grp, hash, hlen,
                              &ctx->Q, &r, &s, rs_ctx ) ) != 0 )
        goto cleanup;


    /* At this point we know that the buffer starts with a valid signature.
     * Return 0 if the buffer just contains the signature, and a specific
     * error code if the valid signature is followed by more data. */
    if( p != end )
        ret = -0x4C00;

cleanup:
    mbedtls_mpi_free( &r );
    mbedtls_mpi_free( &s );

    return( ret );
}


/*
 * Generate key pair
 */
int mbedtls_ecdsa_genkey( mbedtls_ecdsa_context *ctx, mbedtls_ecp_group_id gid,
                  int (*f_rng)(void *, unsigned char *, size_t), void *p_rng )
{
    int ret = 0;
    do { } while( 0 );
    do { } while( 0 );

    ret = mbedtls_ecp_group_load( &ctx->grp, gid );
    if( ret != 0 )
        return( ret );

   return( mbedtls_ecp_gen_keypair( &ctx->grp, &ctx->d,
                                    &ctx->Q, f_rng, p_rng ) );
}


/*
 * Set context from an mbedtls_ecp_keypair
 */
int mbedtls_ecdsa_from_keypair( mbedtls_ecdsa_context *ctx, const mbedtls_ecp_keypair *key )
{
    int ret = -0x006E;
    do { } while( 0 );
    do { } while( 0 );

    if( ( ret = mbedtls_ecp_group_copy( &ctx->grp, &key->grp ) ) != 0 ||
        ( ret = mbedtls_mpi_copy( &ctx->d, &key->d ) ) != 0 ||
        ( ret = mbedtls_ecp_copy( &ctx->Q, &key->Q ) ) != 0 )
    {
        mbedtls_ecdsa_free( ctx );
    }

    return( ret );
}

/*
 * Initialize context
 */
void mbedtls_ecdsa_init( mbedtls_ecdsa_context *ctx )
{
    do { } while( 0 );

    mbedtls_ecp_keypair_init( ctx );
}

/*
 * Free context
 */
void mbedtls_ecdsa_free( mbedtls_ecdsa_context *ctx )
{
    if( ctx == 0 )
        return;

    mbedtls_ecp_keypair_free( ctx );
}

# 928 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecdsa.c"

