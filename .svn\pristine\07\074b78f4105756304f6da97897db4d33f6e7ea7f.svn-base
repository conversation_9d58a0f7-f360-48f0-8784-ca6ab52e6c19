/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifdef __WIDL__
#pragma winrt ns_prefix
#endif

import "inspectable.idl";
import "windows.storage.streams.idl";

namespace Windows {
  namespace Storage {
    namespace Streams {
      interface IBuffer;
    }
  }
}

namespace Windows {
  namespace Security {
    namespace Cryptography {
      interface ICryptographicBufferStatics;
      typedef enum BinaryStringEncoding BinaryStringEncoding;
    }
  }
}

namespace Windows {
  namespace Security {
    namespace Cryptography {
      enum BinaryStringEncoding {
        Utf8,
        Utf16LE,
        Utf16BE
      };

      [uuid(320B7E22-3CB0-4CDF-8663-1D28910065EB)]
      interface ICryptographicBufferStatics : IInspectable {
        HRESULT Compare(Windows.Storage.Streams.IBuffer* object1, Windows.Storage.Streams.IBuffer* object2, boolean* isEqual);
        HRESULT GenerateRandom(UINT32 length, Windows.Storage.Streams.IBuffer** buffer);
        HRESULT GenerateRandomNumber(UINT32* value);
        HRESULT CreateFromByteArray(UINT32 __valueSize, BYTE* value,Windows.Storage.Streams.IBuffer** buffer);
        HRESULT CopyToByteArray(Windows.Storage.Streams.IBuffer* buffer, UINT32* __valueSize, BYTE** value);
        HRESULT DecodeFromHexString(HSTRING value, Windows.Storage.Streams.IBuffer** buffer);
        HRESULT EncodeToHexString(Windows.Storage.Streams.IBuffer* buffer, HSTRING* value);
        HRESULT DecodeFromBase64String(HSTRING value, Windows.Storage.Streams.IBuffer** buffer);
        HRESULT EncodeToBase64String(Windows.Storage.Streams.IBuffer* buffer, HSTRING* value);
        HRESULT ConvertStringToBinary(HSTRING value, BinaryStringEncoding encoding, Windows.Storage.Streams.IBuffer** buffer);
        HRESULT ConvertBinaryToString(BinaryStringEncoding encoding, Windows.Storage.Streams.IBuffer* buffer, HSTRING* value);
      }
    }
  }
}
