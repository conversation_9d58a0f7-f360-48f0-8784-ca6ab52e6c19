/******************************************************************************

*(C) Copyright 2007 Marvell International Ltd.

* All Rights Reserved

******************************************************************************
 *  Filename: telpb.c
 *
 *  Description: Process Phonebook related AT commands
 *
 *  History:
 *   Jan 25, 2008 - Creation of file 
 *   Oct 10, 2008 - Optimization	
 *
******************************************************************************/
#include "ci_api_types.h"
#include "ci_api_client.h"
#include "telatci.h"
#include "teldef.h"
#include "telconfig.h"
#include "telatparamdef.h"
#include "tel3gdef.h"
#include "telutl.h"
#include "telcc.h"
#include "pb_api.h"
#include "telpb.h"
#ifndef MIN_SYS

UINT16 PBdelindex = 0 ;//Just work round!!
UINT16 PBdelindex_1 = 0 ;//Just work round!!

#ifndef REMOVE_PB

/************************************************************************************
 *
 * PBK related global variables and structure
 *
 *************************************************************************************/
/*Fix klockwork*/
unsigned short g_pbEntryStr[CI_MAX_NAME_LENGTH + ATCI_NULL_TERMINATOR_LENGTH];
unsigned short g_pbEntryStr_1[CI_MAX_NAME_LENGTH + ATCI_NULL_TERMINATOR_LENGTH];

CHAR pbStorageStr_bak[ATCI_PB_STORAGE_STR_LENGTH + ATCI_NULL_TERMINATOR_LENGTH] = "SM";
extern AtciCharacterSet chset_type[NUM_OF_TEL_ATP];
extern AtciCharacterSet chset_type_1[NUM_OF_TEL_ATP];

extern INT16 gPbWrittenIndex[];
extern INT16 gPbWrittenIndex_1[];
extern INT16 gTempPbWrittenIndex[];
extern INT16 gTempPbWrittenIndex_1[];
extern UINT16 gPbId;
extern UINT16 gPbId_1;

extern BOOL libGetAddrInfo (const utlAtParameterValue_P2c parameter_values_p,
				int index,
				int max_address_len,
				const CHAR *default_address,
				int min_subaddrType,
				int max_subaddrType,
				int default_addrType,
				CiAddressInfo *pAddr);
CiReturnCode PB_SetTrustNum(UINT32 atHandle, unsigned int option, unsigned int index, CiAddressInfo *addrInfo);
CiReturnCode PB_CHKTrustNum(UINT32 atHandle, CiAddressInfo *addrInfo);
BOOL libGetAddrInfo_new (const utlAtParameterValue_P2c parameter_values_p,
				int index,
				int max_address_len,
				const CHAR *default_address,
				int min_subaddrType,
				int max_subaddrType,
				int default_addrType,
				CiAddressInfo *pAddr);

/************************************************************************************
 *
 * PBK related AT commands Processing Function
 *
 *************************************************************************************/

/************************************************************************************
 * F@: ciSelectPBStorage - GLOBAL API for GCF AT+CPBS command
 *
 */
RETURNCODE_T  ciSelectPBStorage(   const utlAtParameterOp_T        op,					
		const char                      *command_name_p,		
		const utlAtParameterValue_P2c   parameter_values_p,  
		const size_t                    num_parameters,		
		const char                      *info_text_p,       	
		unsigned int                    *xid_p,               		
		void                            *arg_p)
{
	RETURNCODE_T      rc = INITIAL_RETURN_CODE;
	CiReturnCode          ret = CIRC_FAIL;
        UINT32	              atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);

	*xid_p = atHandle;
	DBGMSG("ciSelectPBStorage: atHandle = %d, op = %d", atHandle, op);

	/*
	 * process operation
	 */
	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CPBS=? */
		{
            ret = PB_QueryStorage(atHandle);
			break;
		}

		case TEL_EXT_GET_CMD: /* AT+CPBS? */
		{
	        ret = PB_GetStorage(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CPBS=<storage>[,<password>] */
		{
            CHAR    pbStorageStr[ATCI_PB_STORAGE_STR_LENGTH + ATCI_NULL_TERMINATOR_LENGTH];
        	CHAR    pbPasswordStr [ TEL_AT_CPBS_1_PASSWORD_STR_MAX_LEN + ATCI_NULL_TERMINATOR_LENGTH]; 
            int       pbPasswordStrLen, pbStorageStrLen;

			/*no side effect*/
			/*coverity[incompatible_cast]*/
			if ( getExtString(parameter_values_p, 0, pbStorageStr,
					  ATCI_PB_STORAGE_STR_LENGTH, (INT16 *)&pbStorageStrLen, (CHAR *)TEL_AT_CPBS_0_STORAGE_STR_DEFAULT) == TRUE )
			{
				pbPasswordStrLen = 0;

				/* Password is required only in some cases */
				if ( (strcmp((char *)pbStorageStr, "FD") == 0) || (strcmp((char *)pbStorageStr, "ON") == 0))
				{
					/*no side effect*/
					/*coverity[incompatible_cast]*/
					if ( getExtString(parameter_values_p, 1, pbPasswordStr,
							  TEL_AT_CPBS_1_PASSWORD_STR_MAX_LEN, (INT16 *)&pbPasswordStrLen, TEL_AT_CPBS_1_PASSWORD_STR_DEFAULT) == FALSE)
					{
						ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
						break;
					}
				}   

             	ret = PB_SetStorage(atHandle, (char *)pbStorageStr, (char *)pbPasswordStr, pbPasswordStrLen);
				if(ret == CIRC_SUCCESS)
					memcpy(pbStorageStr_bak, (char *)pbStorageStr, strlen((char *)pbStorageStr));
			}
            else 
            {
                    ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);           
            }
                            
			break;
		}

		default: /* AT+CPBS */
		{
            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 * F@: ciReadPB - GLOBAL API for GCF AT+CPBR command
 *
 */
RETURNCODE_T  ciReadPB(            const utlAtParameterOp_T        op,					
		const char                      *command_name_p,		
		const utlAtParameterValue_P2c   parameter_values_p,  
		const size_t                    num_parameters,		
		const char                      *info_text_p,       	
		unsigned int                    *xid_p,               		
		void                            *arg_p)
{
	RETURNCODE_T      	rc = INITIAL_RETURN_CODE;
	CiReturnCode          	ret = CIRC_FAIL;
        UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);        
    
	*xid_p = atHandle;
	DBGMSG("ciReadPB: atHandle = %d, op = %d", atHandle, op);	

	/*
	 * process operation
	 */
	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CPBR=? */
		{
	                ret = PB_QueryRead(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD:         /* AT+CPBR= */
		{
			INT32 index1, index2;
			if ( getExtValue( parameter_values_p, 0, (int *)&index1, TEL_AT_PB_INDEX_VAL_MIN, TEL_AT_PB_INDEX_VAL_MAX, TEL_AT_PB_INDEX_VAL_DEFAULT ) == TRUE )
			{
				/* If index2 is valid, record the arrange to display. And send next +CPBR after receive CI CNF msg */
				index2 = 0;
				if ((index1 != ATCI_PB_INVALID_INDEX) && (getExtValue( parameter_values_p, 1, (int *)&index2, TEL_AT_PB_INDEX_VAL_MIN, TEL_AT_PB_INDEX_VAL_MAX, ATCI_PB_INVALID_INDEX ) == TRUE) )
				{
					if ( (index2 == ATCI_PB_INVALID_INDEX) || (index2 <= index1) )
					{
						index2 = 0;
					}
					/* Send AT Command to read entry in index1 */
					ret = PB_ReadEntryStart(atHandle, index1, index2);
				}
				else
				{
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
				}

			}
			/* If Index1 is invalid */
			else
			{
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
			}
			break;
		}

		default: /* AT+CPBR, AT+CPBR? */
		{                                     
            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);                                    
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);

}


/************************************************************************************
 * F@: ciWritePB - GLOBAL API for GCF AT+CPBW command
 *
 */
RETURNCODE_T  ciWritePB(            const utlAtParameterOp_T        op,					
		const char                      *command_name_p,		
		const utlAtParameterValue_P2c   parameter_values_p,  
		const size_t                    num_parameters,		
		const char                      *info_text_p,       	
		unsigned int                    *xid_p,               		
		void                            *arg_p)
{

    RETURNCODE_T      	rc = INITIAL_RETURN_CODE;
	CiReturnCode          	ret = CIRC_FAIL;
    UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
	UINT32 sAtpIndex = GET_ATP_INDEX( atHandle );     
	UINT16 *pPBdelindex;
	AtciCharacterSet *pchset_type;
	INT16 *pPbWrittenIndex, *pTempPbWrittenIndex;
	UINT16 PbId = 0;
	CHAR rspBuf[100] = { 0 };

	if (!GET_SIM1_FLAG(atHandle)) {
		pPBdelindex = &PBdelindex;
		pchset_type = &chset_type[sAtpIndex];
		pPbWrittenIndex = gPbWrittenIndex;
		pTempPbWrittenIndex = gTempPbWrittenIndex;
		PbId = gPbId;
	} else {
		pPBdelindex = &PBdelindex_1;
		pchset_type = &chset_type_1[sAtpIndex];
		pPbWrittenIndex = gPbWrittenIndex_1;
		pTempPbWrittenIndex = gTempPbWrittenIndex_1;
		PbId = gPbId_1;
	}

	*xid_p = atHandle;
    DBGMSG("ciWritePB: atHandle = %d, op=%d", atHandle, op);

	/*
	 * process operation
	 */
	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CPBW=? */
		{
            ret = PB_QueryWrite(atHandle);
			break;
		}

		case TEL_EXT_GET_CMD: /*AT+CPBW?*/
		{
			int i = 0;
			//ATDBGMSG(ciWritePB,1,"%s:PbId[%d],pPbWrittenIndex[%p],gPbWrittenIndex[%p],gTempPbWrittenIndex_1[%p]", __func__, PbId, pPbWrittenIndex, gPbWrittenIndex, gPbWrittenIndex_1);
			for(i = 0; i < (CI_PHONEBOOK_NEXT - 1); i++)
			{
				//ATDBGMSG(ciWritePB,2,"%s:pPbWrittenIndex[%d]=%d", __func__, i, pPbWrittenIndex[i]);
			}
			snprintf(rspBuf, sizeof(rspBuf) - 1, "+CPBW:%d", pPbWrittenIndex[PbId - 1]);
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, rspBuf);
			break;
		}

		case TEL_EXT_SET_CMD:         /* AT+CPBW=[<index>][,<number>[,<type>[,<text>[,<group>[,<adnumber>[,<adtype>[,<secondtext>[,<email>[,<sip_uri>[,<tel_uri>[,<hidden>]]]]]]]]]]] */
		{
			CiPbPrimReplacePhonebookEntryReq PbEntryReq;
			BOOL deleteEntry = FALSE;
			int index=0;
			INT16 txtLen=0;
			CHAR text[CI_MAX_NAME_LENGTH * 2 + ATCI_NULL_TERMINATOR_LENGTH];
			BOOL cmdValid = FALSE;
			BOOL valid = TRUE;
			int name_max_len = 0;
			CiAddressInfo addr;
			int CategIndex=0;
			int global_index = 0;
			int isHidden=0;
			int group_index=0, additional_index=0, email_index=0;
			CHAR group[CI_MAX_NAME_LENGTH * 2 + ATCI_NULL_TERMINATOR_LENGTH];
			CHAR emails[CI_LONG_ADR_LENGTH + ATCI_NULL_TERMINATOR_LENGTH];
			INT16 groupLen=0, emailLen=0;

			memset(&PbEntryReq, 0, sizeof(PbEntryReq));
			memset(text, 0, sizeof(text));
			memset(&addr, 0, sizeof(addr));
			memset(&PbEntryReq, 0, sizeof(PbEntryReq));
			memset(group, 0, sizeof(group));
			memset(emails, 0, sizeof(emails));
			/* Get the index to be written. If index is omitted, the record will be written to first available location  */
			index = ATCI_PB_INVALID_INDEX;
			if ( getExtValue( parameter_values_p, 0, &index, TEL_AT_PB_INDEX_VAL_MIN, TEL_AT_PB_INDEX_VAL_MAX, TEL_AT_PB_INDEX_VAL_DEFAULT ) == TRUE )
			{
				PbEntryReq.Index = index;
				
				/* Get number and type of number, if no number inputted, this entry will be removed from PBK */
				if(libGetAddrInfo(parameter_values_p, 1, TEL_AT_MAX_ADDRESS_LENGTH, NULL, ATCI_DIAL_NUMBER_UNKNOWN, ATCI_DIAL_NUMBER_INVALID, ATCI_DIAL_NUMBER_UNKNOWN, &addr) == TRUE)
				{

					if ( addr.Length == 0 )
					{
						deleteEntry = TRUE;
					}
					PbEntryReq.entry.Number = addr;
					if ( deleteEntry )
					{
						if ( index != ATCI_PB_INVALID_INDEX )
						{
							*pPBdelindex = index;
							ret = PB_DeleteEntry(atHandle, index);
						}
						else
							ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
						//if deleteEntry is true, we don't need check remaining fields.
						break;
					}
					else
					{
						if(*pchset_type == ATCI_CHSET_HEX)
						{
							name_max_len = CI_MAX_NAME_LENGTH * 2;
						}
						else
						{
							name_max_len = TEL_AT_CMDSTR_MAX_LEN;
						}


						/* Get text string  associated with the numbe */

						if ( getExtString(parameter_values_p, 3, text, name_max_len, &txtLen, NULL) == TRUE )
						{
							cmdValid = TRUE;
						
							if ( txtLen == 0 )
							{
								PbEntryReq.entry.Name.Name[0] = '\0';
								PbEntryReq.entry.Name.Length = 0;
								
								PbEntryReq.entry.Number = addr;
							
							}
							else
							{
								//since there are more parameters to check, so reinitialize cmdValid to FALSE here
								cmdValid = FALSE;
								PbEntryReq.entry.Name.Length = pb_encode_character((char *)text, txtLen, *pchset_type, (char *)PbEntryReq.entry.Name.Name, CI_MAX_NAME_LENGTH);

								if(PbEntryReq.entry.Name.Length != 0)
								{
									if ( getExtValue( parameter_values_p, 4, &isHidden, TELAT_CPBW_4_ISHIDDEN_MIN, TELAT_CPBW_4_ISHIDDEN_MAX, TELAT_CPBW_4_ISHIDDEN_DEFAULT ) == TRUE )
									{
										PbEntryReq.entry.isHidden = (CiBoolean)isHidden;

										if(*pchset_type == ATCI_CHSET_HEX)
										{
											name_max_len = CI_MAX_NAME_LENGTH * 2;
										}
										else
										{
											name_max_len = TEL_AT_CMDSTR_MAX_LEN;
										}
										for(group_index =0; group_index < CI_PHONEBOOK_GROUP_MAX; group_index++)
										{
										
											if ( getExtString(parameter_values_p, 5 + group_index, group, name_max_len, &groupLen, NULL) == TRUE )
											{
												if(groupLen)
												{
													PbEntryReq.entry.Group[group_index].Length = pb_encode_character((char *)group, groupLen, *pchset_type, (char *)PbEntryReq.entry.Group[group_index].Name, CI_MAX_NAME_LENGTH);
													if(PbEntryReq.entry.Group[group_index].Length == 0)
													{
														valid = FALSE;
														break;
													}
													PbEntryReq.entry.NumGroups++;
												}
												else
												{
													PbEntryReq.entry.Group[group_index].Name[0]='\0';
													PbEntryReq.entry.Group[group_index].Length = 0;
												}
												groupLen = 0;
												group[0] = '\0';
											}
											else
											{
												valid = FALSE;
												break;
											}
										}
										if(valid)
										{
											global_index += CI_PHONEBOOK_GROUP_MAX;
											for(additional_index = 0; additional_index < CI_PHONEBOOK_NUMBER_ADDITIONAL_NUMBERS_MAX; additional_index++)
											{
												CategIndex	=	0;
												
												if(libGetAddrInfo(parameter_values_p, 5 + global_index + 3 * additional_index, TEL_AT_MAX_ADDRESS_LENGTH, NULL, ATCI_DIAL_NUMBER_UNKNOWN, ATCI_DIAL_NUMBER_INVALID, ATCI_DIAL_NUMBER_UNKNOWN, &addr) == TRUE)
												{
													if(addr.Length)
													{
														memcpy(PbEntryReq.entry.AddNum[additional_index].number.AnrDial.Name, addr.Digits, addr.Length);
														PbEntryReq.entry.AddNum[additional_index].number.AnrDial.Length = addr.Length;
														PbEntryReq.entry.AddNum[additional_index].number.numberPlan = addr.AddrType.NumPlan;
														PbEntryReq.entry.AddNum[additional_index].number.typeOfNumber = addr.AddrType.NumType;

														if ( getExtValue( parameter_values_p, 7 + global_index + 3 * additional_index, &CategIndex, TELAT_CPBW_CATEGORY_INDEX_MIN, TELAT_CPBW_CATEGORY_INDEX_MAX, TELAT_CPBW_CATEGORY_INDEX_DEFAULT ) == TRUE )
														{
															PbEntryReq.entry.AddNum[additional_index].CategoryIndex = CategIndex;
														}
														PbEntryReq.entry.NumAddNum++;
													}
													else
													{
														PbEntryReq.entry.AddNum[additional_index].number.AnrDial.Name[0]	= 	'\0';
														PbEntryReq.entry.AddNum[additional_index].number.AnrDial.Length	=	0;
														PbEntryReq.entry.AddNum[additional_index].number.numberPlan		=	0;
														PbEntryReq.entry.AddNum[additional_index].number.typeOfNumber		=	0;
														PbEntryReq.entry.AddNum[additional_index].CategoryIndex			=	0;
													}
												}
												else
												{
													valid = FALSE;
													break;
												}
											}
											if(valid)
											{
												global_index += 3 * CI_PHONEBOOK_NUMBER_ADDITIONAL_NUMBERS_MAX;
												for(email_index = 0; email_index < CI_PHONEBOOK_NUMBER_EMAILS_MAX; email_index++)
												{

													if ( getExtString(parameter_values_p, 5 + global_index + email_index, emails, CI_LONG_ADR_LENGTH*2, &emailLen, NULL))  /*Double length to parse string data*/
													{
														if(emailLen)
														{
															PbEntryReq.entry.Email[email_index].Length = emailLen;
															/*have limited the string length*/
															/*klocwork[Buffer Overflow ]*/
															strncpy((char *)(PbEntryReq.entry.Email[email_index].Name), (char *)emails, emailLen);
															PbEntryReq.entry.NumEmails++;
														}
														else
														{
															PbEntryReq.entry.Email[email_index].Name[0] = '\0';
															PbEntryReq.entry.Email[email_index].Length = 0;
														}
													}
													else
													{
														valid = FALSE;
														break;
													}
												}
												if(valid)
													cmdValid = TRUE;
											}
										}
									}
								}
							}
						}

					}
					if(!cmdValid)
					{
						ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
						break;
					} else {
						pTempPbWrittenIndex[PbId - 1] = PbEntryReq.Index;
						ret = PBW_ProcessEntry(atHandle, (void *)&PbEntryReq);
						break;
					}
				}
			}

		}

		default:         /* AT+CPBW, AT+CPBW? */
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 * F@: ciWritePB - GLOBAL API for GCF AT^SCPBW command
 *
 */
RETURNCODE_T  ciSWritePB(            const utlAtParameterOp_T        op,					
		const char                      *command_name_p,		
		const utlAtParameterValue_P2c   parameter_values_p,  
		const size_t                    num_parameters,		
		const char                      *info_text_p,       	
		unsigned int                    *xid_p,               		
		void                            *arg_p)
{
    RETURNCODE_T      	rc = INITIAL_RETURN_CODE;
	CiReturnCode          	ret = CIRC_FAIL;
    UINT32			atHandle = MAKE_AT_HANDLE(* (TelAtParserID *) arg_p);
           
	*xid_p = atHandle;
	DBGMSG("ciSWritePB: atHandle = %d, op=%d", atHandle, op);

	/*
	 * process operation
	 */
	switch ( op )
	{
		case TEL_EXT_TEST_CMD: /* AT+CPBW=? */
		{
            ret = PB_SQueryWrite(atHandle);
			break;
		}

		case TEL_EXT_SET_CMD: /* AT+CPBW=[<index>][,<number>[,<type>[,<text>[,<group>[,<adnumber>[,<adtype>[,<secondtext>[,<email>[,<sip_uri>[,<tel_uri>[,<hidden>]]]]]]]]]]] */
		{
			BOOL    deleteEntry = FALSE;                                
			int     index=0, type=0;
			INT16   numDigits=0, txtLen=0;
			CHAR    number[CI_MAX_ADDRESS_LENGTH + ATCI_NULL_TERMINATOR_LENGTH];
			CHAR    text[CI_MAX_NAME_LENGTH + ATCI_NULL_TERMINATOR_LENGTH];
			int AnrIndex=0;

			SCPBWParameter SCPBWParameter={0};
			memset(number, 0, sizeof(number));
			memset(text, 0, sizeof(text));

			/* Get the index to be written. If index is omitted, the record will be written to first available location  */
			index = ATCI_PB_INVALID_INDEX;
			if ( getExtValue( parameter_values_p, 0, &(SCPBWParameter.index), TEL_AT_PB_INDEX_VAL_MIN, TEL_AT_PB_INDEX_VAL_MAX, TEL_AT_PB_INDEX_VAL_DEFAULT ) == TRUE )
			{				
			    /* Get number, if no number inputted, this entry will be removed from PBK */
				/*no side effect*/
				/*coverity[incompatible_cast]*/	
				if ( getExtString(parameter_values_p, 1, SCPBWParameter.SCPBWAdn.num,
							ATCI_MAX_CMDSTR_LENGTH + ATCI_NULL_TERMINATOR_LENGTH, (INT16 *)(&(SCPBWParameter.SCPBWAdn.length)), NULL) == TRUE )
				{
					if ( SCPBWParameter.SCPBWAdn.length == 0 )
					{
						deleteEntry = TRUE;
					}

                                            /* Get type of number */
					if ( getExtValue( parameter_values_p, 2, &(SCPBWParameter.SCPBWAdn.type), ATCI_DIAL_NUMBER_UNKNOWN,ATCI_DIAL_NUMBER_INVALID,ATCI_DIAL_NUMBER_UNKNOWN ) == TRUE )
					{
						if ( SCPBWParameter.SCPBWAdn.type == ATCI_DIAL_NUMBER_INVALID )
						{
							if ( SCPBWParameter.SCPBWAdn.num[0] == CI_INTERNATIONAL_PREFIX )
							{
								SCPBWParameter.SCPBWAdn.type = ATCI_DIAL_NUMBER_INTERNATIONAL;
							}
							else
							{
								SCPBWParameter.SCPBWAdn.type = ATCI_DIAL_NUMBER_UNKNOWN;
							}
						}

                        /* Get text string  associated with the numbe */
						/*no side effect*/
						/*coverity[incompatible_cast]*/							
						if ( getExtString(parameter_values_p, 9, SCPBWParameter.text,
									ATCI_MAX_CMDSTR_LENGTH + ATCI_NULL_TERMINATOR_LENGTH, (INT16 *)(&(SCPBWParameter.text_len)), NULL) == TRUE )
						{
							if ( SCPBWParameter.text_len == 0 )
							{
								deleteEntry = TRUE;
							}
						}

						if ( getExtValue( parameter_values_p, 10, &(SCPBWParameter.coding), TEL_AT_PB_CODING_MIN,TEL_AT_PB_CODING_MAX,TEL_AT_PB_CODING_DEFAULT ) != TRUE )							
						{
							SCPBWParameter.coding=TEL_AT_PB_CODING_DEFAULT;
						}							
					}
				}


				for(AnrIndex=0;AnrIndex<3;AnrIndex++)
				{
					/*no side effect*/
					/*coverity[incompatible_cast]*/
					if ( getExtString(parameter_values_p, 3+AnrIndex*2, SCPBWParameter.SCPBWAnrs[AnrIndex].num,
								ATCI_MAX_CMDSTR_LENGTH + ATCI_NULL_TERMINATOR_LENGTH, (INT16 *)(&(SCPBWParameter.SCPBWAnrs[AnrIndex].length)), NULL) == TRUE )
					{
                        /* Get type of number */
						if ( getExtValue( parameter_values_p, 4+AnrIndex*2, &(SCPBWParameter.SCPBWAnrs[AnrIndex].type), ATCI_DIAL_NUMBER_UNKNOWN,ATCI_DIAL_NUMBER_INVALID,ATCI_DIAL_NUMBER_UNKNOWN ) == TRUE )
						{
							if ( SCPBWParameter.SCPBWAnrs[AnrIndex].type == ATCI_DIAL_NUMBER_INVALID )
							{
								if ( SCPBWParameter.SCPBWAnrs[AnrIndex].num[0] == CI_INTERNATIONAL_PREFIX )
								{
									SCPBWParameter.SCPBWAnrs[AnrIndex].type = ATCI_DIAL_NUMBER_INTERNATIONAL;
								}
								else
								{
									SCPBWParameter.SCPBWAnrs[AnrIndex].type = ATCI_DIAL_NUMBER_UNKNOWN;
								}
							}

						}
					}	
				}
				/*no side effect*/
				/*coverity[incompatible_cast]*/
				if ( getExtString(parameter_values_p, 11, SCPBWParameter.email,
							CI_MAX_EMAIL_LENGTH + ATCI_NULL_TERMINATOR_LENGTH, (INT16 *)(&(SCPBWParameter.email_len)), NULL) != TRUE )
				{
					SCPBWParameter.email[0]=0;
					SCPBWParameter.email_len=0;
				}
			
				

				
			}
				
            /* If mandatory arguments are not supplied - delete entry */
			if ( deleteEntry )
			{
				if ( SCPBWParameter.index!= ATCI_PB_INVALID_INDEX )
				{
				        ret = PB_DeleteEntry(atHandle, SCPBWParameter.index);                       
				}
			}

                            /* Write record to PBK */
			else 
			{
			        ret = PB_SWriteEntry(atHandle, &SCPBWParameter);
			}
			break;
		}

		default: /* AT+CPBW, AT+CPBW? */
		{
                            ret = ATRESP( atHandle,ATCI_RESULT_CODE_CME_ERROR,CMS_OPERATION_NOT_ALLOWED,NULL);                         
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}
/************************** Added by Michal Bukai ***********************************/
/************************************************************************************
 * F@: ciFindPBEntries - GLOBAL API for GCF AT+CPBF command
 *
 */
RETURNCODE_T  ciFindPBEntries(            const utlAtParameterOp_T        op,
		const char                      *command_name_p,
		const utlAtParameterValue_P2c   parameter_values_p,
		const size_t                    num_parameters,
		const char                      *info_text_p,
		unsigned int                    *xid_p,
		void                            *arg_p)
{
	/*
	 **  Set the result code to INITIAL_RETURN_CODE.  This allows
	 **  the indications to display the correct return code after the
	 **  AT Command is issued.
	 */
	RETURNCODE_T                         rc = INITIAL_RETURN_CODE;
	CiReturnCode                         ret = CIRC_FAIL;
	INT16                                tmpLen;
	unsigned short *p_pbEntryStr;
	AtciCharacterSet *pchset_type;
	int pbEntryStrSize;

	//static CiPbPrimGetPhonebookInfoReq        GetPBInfo;
	/*
	 *  Put parser index into the variable
	 */
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	UINT32 sAtpIndex = GET_ATP_INDEX( atHandle ); 

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	if (!GET_SIM1_FLAG(atHandle)) {
		p_pbEntryStr = g_pbEntryStr;
		pchset_type = &chset_type[sAtpIndex];
		pbEntryStrSize = sizeof(g_pbEntryStr) / sizeof(g_pbEntryStr[0]);
	} else {
		p_pbEntryStr = g_pbEntryStr_1;
		pchset_type = &chset_type_1[sAtpIndex];
		pbEntryStrSize = sizeof(g_pbEntryStr_1) / sizeof(g_pbEntryStr_1[0]);
	}

	/*
	 * process operation
	 */
	switch ( op )
	{
		case TEL_EXT_TEST_CMD:         /* AT+CPBF=? */
		{
			ret = PB_QueryFind(atHandle);
			break;
		}
		case TEL_EXT_SET_CMD:         /* AT+CPBF= */
		{
			int max_len = 0;
			char text[CI_MAX_NAME_LENGTH * 2 + 1];
			if(*pchset_type == ATCI_CHSET_HEX)
			{
				max_len = CI_MAX_NAME_LENGTH * 2;
			}
			else if(*pchset_type == ATCI_CHSET_IRA)
			{
				max_len = CI_MAX_NAME_LENGTH;
			}
			else
			{
				max_len = CI_MAX_NAME_LENGTH * 4;
			}

			if ( getExtString(parameter_values_p, 0, (CHAR *)text, max_len, &tmpLen, NULL) == TRUE )
			{
				int len;
				if(*pchset_type == ATCI_CHSET_HEX)
				{
					char hex[CI_MAX_NAME_LENGTH + 1] = {0};
					len = libConvertCSCSStringToHEX(text, tmpLen, *pchset_type, hex, sizeof(hex) / sizeof(hex[0]) - 1);
					memset(g_pbEntryStr, 0, sizeof(g_pbEntryStr));
					if(len > 0)
					{
						if(pb_decode_alpha_tag((unsigned char *)hex, len, (unsigned short *)p_pbEntryStr, pbEntryStrSize - 1) == -1)
						{
							ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
							break;
						}
					}
					else
					{
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
						break;
					}
				}
				else
				{
					memset(p_pbEntryStr, 0, pbEntryStrSize);
					len = libConvertCSCSStringToUCS2(text, tmpLen, *pchset_type, p_pbEntryStr, pbEntryStrSize - 1);
					if(len <= 0)
					{
						ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
						break;
					}
				}

				/* currently last two parameters are not used, so just pass NULL and 0 */
				ret = PB_FindPBEntry(atHandle, NULL, 0);
			}
			else
				ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
			break;
		}
		case TEL_EXT_GET_CMD:           /* AT+CPBF? */
		default:                        /* AT+CPBF */
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CMS_OPERATION_NOT_ALLOWED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}

/************************************************************************************
 * F@: ciGetPhoneBookCapacity - GLOBAL API for AT*CPBC -command
 *
 */
RETURNCODE_T  ciGetPhoneBookCapacity(            const utlAtParameterOp_T op,
				    const char                      *command_name_p,
				    const utlAtParameterValue_P2c parameter_values_p,
				    const size_t num_parameters,
				    const char                      *info_text_p,
				    unsigned int                    *xid_p,
				    void                            *arg_p)
{

	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(parameter_values_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	switch (op)
	{
		case TEL_EXT_ACTION_CMD: /*AT*CPBC*/
		{
			ret = PB_GetCapc(atHandle);
			break;
		}
		case TEL_EXT_TEST_CMD: /*AT*CPBC=?*/
		case TEL_EXT_GET_CMD:  /*AT*CPBC?*/
		case TEL_EXT_SET_CMD: /*AT*CPBC=*/
		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
		}
	}
	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);

	return(rc);
}

/************************************************************************************
 * F@: ciFdnBypass - GLOBAL API for AT*FDNBYPASS -command
 *
 */
RETURNCODE_T  ciFdnBypass(            const utlAtParameterOp_T op,
					  const char                      *command_name_p,
					  const utlAtParameterValue_P2c parameter_values_p,
					  const size_t num_parameters,
					  const char                      *info_text_p,
					  unsigned int                    *xid_p,
					  void                            *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)
	UNUSEDPARAM(parameter_values_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	/*
	**  Determine the type of request.
	*/
	switch ( op )
	{
		case TEL_EXT_ACTION_CMD:          /* AT*FDNBYPASS */
		{
			ret = PB_Fdnbypass(atHandle);
			break;
		}
		default:
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

extern CiBoolean gModemPbkReady ;
extern CiBoolean gModemPbkReady_1;

/************************************************************************************
 * F@: ciCRCES - GLOBAL API for AT+MPBK	-command
 *
 */
RETURNCODE_T  ciMPBK(			 const utlAtParameterOp_T op,
					  const char					  *command_name_p,
					  const utlAtParameterValue_P2c parameter_values_p,
					  const size_t num_parameters,
					  const char					  *info_text_p,
					  unsigned int					  *xid_p,
					  void							  *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);
	CiBoolean modemPbkReady;
	
	*xid_p = atHandle;
	DBGMSG("%s: atHandle = %d.\n", __FUNCTION__, atHandle);

	if (!GET_SIM1_FLAG(atHandle)) {
		modemPbkReady = gModemPbkReady;
	} else {
		modemPbkReady = gModemPbkReady_1;
	}
	/*
	**	Check the operation type.
	*/
	/*mischecked by klocwork*/
	/*klocwork[Inconsistent Case Labels]*/
	switch ( op )
	{
	    case TEL_EXT_ACTION_CMD:  /* AT+MPBK*/
		{
			char atRspBuf[20];
			sprintf(atRspBuf, "+MPBK: %d",modemPbkReady==TRUE ? 1: 0);
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_OK, 0, (char *)atRspBuf);
			break;
		}
		case TEL_EXT_GET_CMD:			   /* AT+MPBK? */
		case TEL_EXT_SET_CMD:			   /* AT+MPBK= */
		case TEL_EXT_TEST_CMD:				/* AT+MPBK=? */
		default:
		{
			ret = ATRESP(atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_SUPPORTED, NULL);
			break;
		}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);
}

/************************************************************************************
 *
 * PBK related AT commands Processing Function
 *
 *************************************************************************************/

/************************************************************************************
 * F@: ciTRUSTNUM - GLOBAL API for GCF AT+TRUSTNUM command
 *
 */
RETURNCODE_T  ciTRUSTNUM(   const utlAtParameterOp_T op,
				   const char					   *command_name_p,
				   const utlAtParameterValue_P2c parameter_values_p,
				   const size_t num_parameters,
				   const char					   *info_text_p,
				   unsigned int 				   *xid_p,
				   void 						   *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	//ATDBGMSG(atcmdsrv,175,"ciTRUSTNUM: atHandle = %d.\n", atHandle);

	/*
	 * process operation
	 */
	switch ( op )
	{
	case TEL_EXT_TEST_CMD:		   /* AT+TRUSTNUM=? */
	{
		break;
	}

	case TEL_EXT_GET_CMD:		  /* AT+TRUSTNUM? */
	{
		unsigned int option = 3, index = 20;
		CiAddressInfo	 addrInfo;
		memset(&addrInfo, 0, sizeof(addrInfo));

		//ATDBGMSG(atcmdsrv,176,"ciTRUSTNUM_get: option = %d, index = %d.\n", option, index);
		ret = PB_SetTrustNum(atHandle, option, index, &addrInfo);

		break;
	}

	case TEL_EXT_SET_CMD:		  /* AT+TRUSTNUM=<index>[,<number>] */
	{
		unsigned int option = 0, index = 0;
		CiAddressInfo	 addrInfo;
		memset(&addrInfo, 0, sizeof(addrInfo));
		
		if ( getExtUValue(parameter_values_p, 0, &index, 0, 19, 0) == TRUE )
		{
			if (!(parameter_values_p[1].is_default)) {
				if (libGetAddrInfo_new(parameter_values_p, 1, TEL_AT_MAX_ADDRESS_LENGTH, NULL, ATCI_DIAL_NUMBER_UNKNOWN, ATCI_DIAL_NUMBER_INVALID, ATCI_DIAL_NUMBER_UNKNOWN, &addrInfo) == TRUE) {
					if (((addrInfo.AddrType.NumType == 0)&&((addrInfo.Length > 0)&&(addrInfo.Length <= 24)))
						||((addrInfo.AddrType.NumType == 1)&&((addrInfo.Length > 0)&&(addrInfo.Length <= 25)))) {		//AT+TRUSTNUM=index,"number"
						option = 0;
					} else if(addrInfo.Length == 0) {
						option = 1;						//AT+TRUSTNUM=index,""
					}
				} else {				
					ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
				}
			} else {
				option = 2;								//AT+TRUSTNUM=index,get index number
			}
			
			//ATDBGMSG(atcmdsrv,177,"ciTRUSTNUM: option = %d, index = %d, addrInfo->Length = %d, addrInfo->addrtype = %d.\n", option, index, addrInfo.Length, addrInfo.AddrType.NumType);

			ret = PB_SetTrustNum(atHandle, option, index, &addrInfo);
		}
		else
		{
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
		}

		break;
	}

	default:		 /* AT+TRUSTNUM */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}

/************************************************************************************
 *
 * PBK related AT commands Processing Function
 *
 *************************************************************************************/

/************************************************************************************
 * F@: ciCHKTRUSTNUM - GLOBAL API for GCF AT+CHKTRUSTNUM command
 *
 */
RETURNCODE_T  ciCHKTRUSTNUM(   const utlAtParameterOp_T op,
				   const char					   *command_name_p,
				   const utlAtParameterValue_P2c parameter_values_p,
				   const size_t num_parameters,
				   const char					   *info_text_p,
				   unsigned int 				   *xid_p,
				   void 						   *arg_p)
{
	UNUSEDPARAM(command_name_p)
	UNUSEDPARAM(num_parameters)
	UNUSEDPARAM(info_text_p)

	RETURNCODE_T rc = INITIAL_RETURN_CODE;
	CiReturnCode ret = CIRC_FAIL;
	UINT32 atHandle = MAKE_AT_HANDLE(*(TelAtParserID *)arg_p);

	*xid_p = atHandle;
	//ATDBGMSG(atcmdsrv,178,"ciCHKTRUSTNUM: atHandle = %d.\n", atHandle);

	/*
	 * process operation
	 */
	switch ( op )
	{
	case TEL_EXT_TEST_CMD:		   /* AT+CHKTRUSTNUM=? */
	{
		break;
	}

	case TEL_EXT_GET_CMD:		  /* AT+CHKTRUSTNUM? */
	{
		break;
	}

	case TEL_EXT_SET_CMD:		  /* AT+CHKTRUSTNUM=<number> */
	{
		CiAddressInfo	 addrInfo;
		memset(&addrInfo, 0, sizeof(addrInfo));
		
		if (libGetAddrInfo_new(parameter_values_p, 0, TEL_AT_MAX_ADDRESS_LENGTH, NULL, ATCI_DIAL_NUMBER_UNKNOWN, ATCI_DIAL_NUMBER_INVALID, ATCI_DIAL_NUMBER_UNKNOWN, &addrInfo) == TRUE) {
			if (((addrInfo.AddrType.NumType == 0)&&((addrInfo.Length > 0)&&(addrInfo.Length <= 24)))
					||((addrInfo.AddrType.NumType == 1)&&((addrInfo.Length > 0)&&(addrInfo.Length <= 25)))) {
				ret = PB_CHKTrustNum(atHandle, &addrInfo);
			} else {
				ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
			}
		} else {
			ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
		}
			
		CPUartLogPrintf("ciCHKTRUSTNUM: addrInfo->Length = %d, addrInfo->addrtype = %d.\n", addrInfo.Length, addrInfo.AddrType.NumType);

		break;
	}

	default:		 /* AT+TRUSTNUM */
	{
		ret = ATRESP( atHandle, ATCI_RESULT_CODE_CME_ERROR, CME_OPERATION_NOT_ALLOWED, NULL);
		break;
	}
	}

	/* handle the return value */
	rc = HANDLE_RETURN_VALUE(ret);
	return(rc);

}
#endif
#endif
