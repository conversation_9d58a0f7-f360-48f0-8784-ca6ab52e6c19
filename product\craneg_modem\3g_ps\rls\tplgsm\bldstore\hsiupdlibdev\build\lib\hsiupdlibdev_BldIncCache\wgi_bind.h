/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/**********************************************************************
 *
 * Filename: wgi_bind.h
 *
 * Programmers: Oren B
 *
 *
 * Description: Declarations of the WB to GSM interface bind functions
 *
 **********************************************************************/

#ifndef WGI_BIND_H
#define WGI_BIND_H

#include "global_types.h"
#include "pl_w_globs.h"
#include "pl_d_globs.h"
#include "pl_drat.h"
#include "wbBchFromGsm.h"

#if defined (UPGRADE_PLMS_L1) || defined (GPLC_LTE_RSSI_SCAN)
#include <plmsl1_sig.h>
#endif //GPLC_LTE_RSSI_SCAN


extern BOOL wgiIsGplcActive(UINT8 simID);
#if defined (INTEL_UPGRADE_DUAL_RAT)

/************************************************************************
 * define types for bind functions
 ************************************************************************/
/* DM Interface */
typedef void (*wgiResetBcchInWbIdleStateMachine_t) (UINT8 simID);
typedef GsmBcchDecodeReqInWb_ts* (*wgiGetBcchInfo_t) (void);
typedef void (*wgiResetBcchInfo_t) (void);
typedef void (*wgiBcchGsmDeactivateReq_t) (UINT8 simID);
typedef void (*wgiSetUtevStartBcchForWb_t) (GsmBcchDecodeReqDBInWb_ts *gsmBcchInfoDB_p,Int8 simID);
typedef void (*wgiSetUtevUpdateBcchForWb_t) (GsmBcchDecodeReqDBInWb_ts *gsmBcchInfoDB_P,Int8 simID,initialRat_te rat);
typedef void (*wgiSetUtevUpdateBcchForWbInDSDS_t) (plwGsmMultiBcchDecodeReq_ts  *gsmMultiBcchCellInfo,Int8 simID);
typedef void (*wgiL1BgUmphMeasRadioReq_t) (UINT8 length,Int8 simID);
typedef void (*wgiL1BgUmphRssiRadioReq_t) (Int8 simID);
typedef void (*wgiL1BgUmphDetectedCellMeasInd_t) (interFreqCellMeasInd_ts * indication,Int8 simID);
typedef void (*wgiL1BgUmphRssiPerUarfcnInd_t) (INT16 indication,Int8 simID);
typedef void (*wgiL1BgUmphRssiScanInd_t)(rssi_reply_ts * gsmRssiResult,Int8 simID);
typedef void (*wgiCellMeasIndPerUARFCN_t) (UtranCellMeasIndPerUarfcn *utranCellMeasIndPerUarfcn,Int8 simID);
typedef void (*wgiL1BgUmphUtranAbortAck_t) (Int8 simID);
typedef void (*wgiL1BgUmphUtranDetAbortAck_t) (Int8 simID);
typedef void (*wgiGSMPowerup_t) (UINT8 simID);
typedef void (*wgiGSMInitStaticSharedMemory_t) (void);
typedef void (*wgiGSMReset_t) (void);
typedef void (*wgiGSMStartup_t) (Bool value, UINT8 simID);
typedef void (*wgiGSMTerminate_t) (Bool value,UINT8 simID);
#if !defined INTEL_UPGRADE_GSM_CRL_IF
typedef void (*wgiFillGsmMeasParams_t) (UINT16 * data, UINT16 * num);
#else /* INTEL_UPGRADE_GSM_CRL_IF */
typedef void (*wgiFillGsmMeasParams_t) (INT16 SectionIndex, UINT16 * data, UINT16 * num);
#endif /* INTEL_UPGRADE_GSM_CRL_IF */
typedef UINT16 (*wgiGetGainTablesSize_t) (UINT8 num);
typedef UINT8 (*wgiGetNumOfGainTables_t) (void);
typedef void (*wgiCopyGainTables_t) (UINT16 * data, UINT16 * num);
typedef void (*wgiSaveGsmAFCValues_t) (UINT8 simID);
typedef void (*wgiAfcDacSaveValue_t) (UINT16 val,INT8 simID);
typedef INT16 (*wgiGetSignedAfcDacVal_t) (void);
typedef void (*wgiGetGsmTimingParams_t) (UINT16 * data);
typedef UINT16 (*wgiAplpGetRspRxPowerUpParamsUmtsBand_t) (UINT16 * data);
typedef UINT32 (*wgiFrGetReferenceTime_t) (void);
typedef void (*wgiSetReferenceTime_t) (UINT32 time);
typedef wbBchInfo_ts* (*wgiRetreiveWbBchParameters_t) (void);
typedef void (*wgiSendUtranBchDecodeResults_t) (utranBchDecodeInd_t * data);
typedef void (*wgiSetEventForBchDecodeTermination_t) ( Bool UtranBchDecodeIndWasSent ,Int8 simID);
typedef void (*wgiResetUtranMeas_t) (UINT8 simID);

#if defined (INTEL_UPGRADE_TCU_DIRQ_HW_BUG_WORKAROUND_VER2)
typedef void (*wgiResetXirqCounters_t) (void);
#endif
typedef void (*wgiReportWbFrameNumber_t) (void);
typedef unsigned long (*wgiDebugGetGsmFrameNumber_t) (void);
typedef void (*wgiEnableHslLogging_t) (short val);
typedef INT16 (*wgiCfgCorrectRssi_t) (INT16 iArfcn,UINT8 ucBand,INT16 iReportedRssi,INT16  iGainUsed);
typedef void (*wgiSetResumeFlag_t) (UINT16 val,UINT8 simID);
typedef UINT16 (*wgiGetResumeFlag_t) (UINT8 simID);
typedef plgMphDeactivateCnf_t (*wgiRetreiveMphDeactivateCnf_t) (void);
typedef void (*wgiSetEventForPldGsmCnf_t) (UINT8 simId);
typedef void (*wgiResetHoTo3GOnGoingFlag_t) (void);
typedef void (*wgiInitAllRFDSSPsAfterPLPIsReady_t) (void);
#endif //#if defined (INTEL_UPGRADE_DUAL_RAT)
typedef BOOL (*wgiBypassGsmRfRAMInit_t) (void);
typedef void (*wgiGsmRfRAMInit_t) (void);
typedef BOOL (*wgiIsGplcActive_t) (UINT8 simID);
#if defined (UPGRADE_PLMS_L1) || defined (GPLC_LTE_RSSI_SCAN)	
typedef void (*wgiSaveRssiScanResults_t) (INT16 numOfArfcns, const plmRssiScanReq_ts *emphRssiScanReq_p);
typedef void (*wgiSetRfSpeedChange_t)(Bool setvalue);
#endif //GPLC_LTE_RSSI_SCAN



/************************************************************************
 * define bind prototypes
 ************************************************************************/
#if defined (INTEL_UPGRADE_DUAL_RAT)
/* WB to GSM interface */
extern wgiResetBcchInWbIdleStateMachine_t 		wgiResetBcchInWbIdleStateMachine_ptr;
extern wgiGetBcchInfo_t							wgiGetBcchInfo_ptr;
extern wgiResetBcchInfo_t						wgiResetBcchInfo_ptr;
extern wgiBcchGsmDeactivateReq_t				wgiBcchGsmDeactivateReq_ptr;
extern wgiSetUtevStartBcchForWb_t				wgiSetUtevStartBcchForWb_ptr;
extern wgiSetUtevUpdateBcchForWb_t              wgiSetUtevUpdateBcchForWb_ptr;
extern wgiSetUtevUpdateBcchForWbInDSDS_t      	wgiSetUtevUpdateBcchForWbInDSDS_ptr;
extern wgiSetRfSpeedChange_t                    wgiSetRfSpeedChange_ptr;
extern wgiL1BgUmphMeasRadioReq_t				wgiL1BgUmphMeasRadioReq_ptr;
extern wgiL1BgUmphRssiRadioReq_t				wgiL1BgUmphRssiRadioReq_ptr;
extern wgiL1BgUmphDetectedCellMeasInd_t		   	wgiL1BgUmphDetectedCellMeasInd_ptr;
extern wgiL1BgUmphRssiPerUarfcnInd_t		   	wgiL1BgUmphRssiPerUarfcnInd_ptr;
extern wgiL1BgUmphRssiScanInd_t		   	        wgiL1BgUmphRssiScanInd_ptr;//shuhanl add

extern wgiCellMeasIndPerUARFCN_t				wgiCellMeasIndPerUARFCN_ptr;
extern wgiL1BgUmphUtranAbortAck_t				wgiL1BgUmphUtranAbortAck_ptr;
extern wgiL1BgUmphUtranDetAbortAck_t			wgiL1BgUmphUtranDetAbortAck_ptr;
extern wgiGSMPowerup_t							wgiGSMPowerup_ptr;
extern wgiGSMInitStaticSharedMemory_t			wgiGSMInitStaticSharedMemory_ptr;
extern wgiGSMReset_t							wgiGSMReset_ptr;
extern wgiGSMStartup_t							wgiGSMStartup_ptr;
extern wgiGSMTerminate_t						wgiGSMTerminate_ptr;
extern wgiFillGsmMeasParams_t					wgiFillGsmMeasParams_ptr;
extern wgiGetGainTablesSize_t					wgiGetGainTablesSize_ptr;
extern wgiGetNumOfGainTables_t					wgiGetNumOfGainTables_ptr;
extern wgiCopyGainTables_t						wgiCopyGainTables_ptr;
extern wgiSaveGsmAFCValues_t					wgiSaveGsmAFCValues_ptr;
extern wgiAfcDacSaveValue_t						wgiAfcDacSaveValue_ptr;
extern wgiGetSignedAfcDacVal_t					wgiGetSignedAfcDacVal_ptr;
extern wgiGetGsmTimingParams_t					wgiGetGsmTimingParams_ptr;
extern wgiAplpGetRspRxPowerUpParamsUmtsBand_t	wgiAplpGetRspRxPowerUpParamsUmtsBand_ptr;
extern wgiFrGetReferenceTime_t					wgiFrGetReferenceTime_ptr;
extern wgiSetReferenceTime_t					wgiSetReferenceTime_ptr;
extern wgiRetreiveWbBchParameters_t				wgiRetreiveWbBchParameters_ptr;
extern wgiSendUtranBchDecodeResults_t			wgiSendUtranBchDecodeResults_ptr;
extern wgiSetEventForBchDecodeTermination_t		wgiSetEventForBchDecodeTermination_ptr;
extern wgiResetUtranMeas_t					    wgiResetUtranMeas_ptr;

#if defined (INTEL_UPGRADE_TCU_DIRQ_HW_BUG_WORKAROUND_VER2)
extern wgiResetXirqCounters_t					wgiResetXirqCounters_ptr;
#endif
extern wgiReportWbFrameNumber_t					wgiReportWbFrameNumber_ptr;
extern wgiDebugGetGsmFrameNumber_t				wgiDebugGetGsmFrameNumber_ptr;
extern wgiEnableHslLogging_t					wgiEnableHslLogging_ptr;
extern wgiCfgCorrectRssi_t						wgiCfgCorrectRssi_ptr;
extern wgiSetResumeFlag_t						wgiSetResumeFlag_ptr;
extern wgiGetResumeFlag_t						wgiGetResumeFlag_ptr;
extern wgiRetreiveMphDeactivateCnf_t			wgiRetreiveMphDeactivateCnf_ptr;
extern wgiSetEventForPldGsmCnf_t				wgiSetEventForPldGsmCnf_ptr;
extern wgiResetHoTo3GOnGoingFlag_t              wgiResetHoTo3GOnGoingFlag_ptr;
#endif //#if defined (INTEL_UPGRADE_DUAL_RAT)
extern wgiBypassGsmRfRAMInit_t					wgiBypassGsmRfRAMInit_ptr;
extern wgiGsmRfRAMInit_t						wgiGsmRfRAMInit_ptr;
extern wgiIsGplcActive_t						wgiIsGplcActive_ptr;
#if defined (UPGRADE_PLMS_L1) || defined (GPLC_LTE_RSSI_SCAN)	
extern wgiSaveRssiScanResults_t                 wgiSaveRssiScanResults_ptr;
#endif //GPLC_LTE_RSSI_SCAN


/************************************************************************
 * define bind functions
 ************************************************************************/
/* WB to GSM interface */
#if defined (INTEL_UPGRADE_DUAL_RAT)
void    wgiBindResetBcchInWbIdleStateMachine (wgiResetBcchInWbIdleStateMachine_t function);
void 	wgiBindGetBcchInfo (wgiGetBcchInfo_t function);
void 	wgiBindResetBcchInfo (wgiResetBcchInfo_t function);
void 	wgiBindBcchGsmDeactivateReq (wgiBcchGsmDeactivateReq_t function);
void 	wgiBindSetUtevStartBcchForWb (wgiSetUtevStartBcchForWb_t function);
void 	wgiBindSetUtevUpdateBcchForWb (wgiSetUtevUpdateBcchForWb_t function);
void 	wgiBindSetUtevUpdateBcchForWbInDSDS (wgiSetUtevUpdateBcchForWbInDSDS_t function);
void    wgiBindSetRfSpeedChange(wgiSetRfSpeedChange_t function);

void 	wgiBindL1BgUmphMeasRadioReq (wgiL1BgUmphMeasRadioReq_t function);
void 	wgiBindL1BgUmphRssiRadioReq (wgiL1BgUmphRssiRadioReq_t function);
void 	wgiBindL1BgUmphDetectedCellMeasInd (wgiL1BgUmphDetectedCellMeasInd_t function);
void 	wgiBindL1BgUmphRssiPerUarfcnInd (wgiL1BgUmphRssiPerUarfcnInd_t function);
void wgiBindL1BgUmphRssiScanInd (wgiL1BgUmphRssiScanInd_t function);
void 	wgiBindCellMeasIndPerUARFCN (wgiCellMeasIndPerUARFCN_t function);
void 	wgiBindL1BgUmphUtranAbortAck (wgiL1BgUmphUtranAbortAck_t function);
void 	wgiBindL1BgUmphUtranDetAbortAck (wgiL1BgUmphUtranDetAbortAck_t function);
void 	wgiBindGSMPowerup (wgiGSMPowerup_t function);
void 	wgiBindGSMReset (wgiGSMReset_t function);
void 	wgiBindGSMStartup (wgiGSMStartup_t function);
void 	wgiBindGSMTerminate (wgiGSMTerminate_t function);
void 	wgiBindFillGsmMeasParams (wgiFillGsmMeasParams_t function);
void 	wgiBindGetGainTablesSize (wgiGetGainTablesSize_t function);
void 	wgiBindGetNumOfGainTables (wgiGetNumOfGainTables_t function);
void 	wgiBindCopyGainTables (wgiCopyGainTables_t function);
void 	wgiBindSaveGsmAFCValues (wgiSaveGsmAFCValues_t function);
void 	wgiBindAfcDacSaveValue (wgiAfcDacSaveValue_t function);
void 	wgiBindGetSignedAfcDacVal (wgiGetSignedAfcDacVal_t function);
void 	wgiBindGetGsmTimingParams (wgiGetGsmTimingParams_t function);
void 	wgiBindAplpGetRspRxPowerUpParamsUmtsBand (wgiAplpGetRspRxPowerUpParamsUmtsBand_t function);
void 	wgiBindFrGetReferenceTime (wgiFrGetReferenceTime_t function);
void 	wgiBindSetReferenceTime (wgiSetReferenceTime_t function);
void 	wgiBindRetreiveWbBchParameters (wgiRetreiveWbBchParameters_t function);
void 	wgiBindSendUtranBchDecodeResults (wgiSendUtranBchDecodeResults_t function);
void 	wgiBindSetEventForBchDecodeTermination (wgiSetEventForBchDecodeTermination_t function);
void 	wgiBindResetUtranMeas (wgiResetUtranMeas_t function);

#if defined (INTEL_UPGRADE_TCU_DIRQ_HW_BUG_WORKAROUND_VER2)
void 	wgiBindResetXirqCounters (wgiResetXirqCounters_t function);
#endif
void 	wgiBindReportWbFrameNumber (wgiReportWbFrameNumber_t function);
void 	wgiBindDebugGetGsmFrameNumber (wgiDebugGetGsmFrameNumber_t function);
void 	wgiBindEnableHslLogging (wgiEnableHslLogging_t function);
void 	wgiBindCfgCorrectRssi (wgiCfgCorrectRssi_t function);
#if defined (UPGRADE_PLMS) || defined (GPLC_LTE_RSSI_SCAN)
void	wgiBindSaveRssiScanResults(wgiSaveRssiScanResults_t function);
#endif //GPLC_LTE_RSSI_SCAN
void 	wgiBindSetResumeFlag (wgiSetResumeFlag_t function);
void 	wgiBindGetResumeFlag (wgiGetResumeFlag_t function);
void 	wgiBindRetreiveMphDeactivateCnf (wgiRetreiveMphDeactivateCnf_t function);
void 	wgiBindSetEventForPldGsmCnf (wgiSetEventForPldGsmCnf_t function);
void    wgiBindResetHoTo3GOnGoingFlag(wgiResetHoTo3GOnGoingFlag_t function);
UINT16 	wgiDefaultRspRxPowerUpBindFunction(void);
UINT8 	wgiDefaultGsmMeasBindFunction(void);
INT16 wgiDefaultResumeFlagtBindFunction(UINT16 simID);
UINT16  wgiDefaultAfcDacValBindFunction(void);
wbBchInfo_ts*  wgiDefaultRetreiveWbBchParamsBindFunction(void);
GsmBcchDecodeReqInWb_ts* wgiDefaultGetBcchInfoBindFunction(void);
plgMphDeactivateCnf_t *wgiDefaultRetreiveMphDeactivateCnfBindFunction(void);
INT32 wgiDefaultFrGetReferenceTimeBindFunction(void);
UINT16 wgiDefaultDebugGetGsmFrameNumberBindFunction(void);
INT16 wgiDefaultCfgCorrectRssiBindFunction(INT16 iArfcn,UINT8 ucBand,INT16 iReportedRssi,INT16  iGainUsed);
void wgiBindGSMInitStaticSharedMemory(wgiGSMInitStaticSharedMemory_t function);



#endif // (INTEL_UPGRADE_DUAL_RAT)
void 	wgiDefaultBindFunction(void);
void 	wgiDefaultBindFunctionWSimID(UINT8 simID);
void 	wgiBindBypassGsmRfRAMInit (wgiBypassGsmRfRAMInit_t function);
void	wgiBindGsmRfRAMInit (wgiGsmRfRAMInit_t function); 
BOOL wgiDefaultBypassGsmRfRAMInitBindFunction (void);

void wgiBindIsGplcActive(wgiIsGplcActive_t function);
BOOL wgiDefaultIsGplcActiveBindFunction(UINT8 simID);



#endif /* WGI_BIND_H */
