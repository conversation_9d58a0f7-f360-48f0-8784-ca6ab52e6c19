/* -*- linux-c -*- */
/*******************************************************************************
*               Copyright 2009, Marvell Technology Group Ltd.
*
* THIS CODE CONTAINS CONFIDENTIAL INFORMATION OF MARVELL. NO RIGHTS ARE GRANTED
* HEREIN UNDER ANY PATENT, MASK WORK RIGHT OR COPYRIGHT OF MARVELL OR ANY THIRD
* PARTY. MARVELL RESERVES THE RIGHT AT ITS SOLE DISCRETION TO REQUEST THAT THIS
* CODE BE IMMEDIATELY RETURNED TO MARVELL. THIS CODE IS PROVIDED "AS IS".
* MARVELL MAKES NO WARRANTIES, EXPRESS, IMPLIED OR OTHERWISE, REGARDING ITS
* ACCURACY, COMPLETENESS OR PERFORMANCE. MARVELL COMPRISES MARVELL TECHN<PERSON>OGY
* GROUP LTD. (MTGL) AND ITS SUBSIDIARIES, MARVELL INTERNATIONAL LTD. (MIL),
* MARVELL TECHNOLOGY, INC. (MTI), MARVELL SEMICONDUCTOR, INC. (MSI), MARVELL
* ASIA PTE LTD. (MAPL), MARVELL JAPAN K.K. (MJKK), GALILEO TECHNOLOGY LTD. (GTL)
* GALILEO TECHNOLOGY, INC. (GTI) AND RADLAN Computer Communications, LTD.
********************************************************************************
*/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#include <psm.h>
#include "FDI_TYPE.h"
#include "FDI_FILE.h"
#include "psm_wrapper.h"
#include "fatwk_psm.h"

#define DEFAULTPSM_DATA_LEN   PSM_BLK_SIZE

//#pragma arm section rwdata="backend_buf", zidata="backend_buf"
__align(8) static unsigned char tmp_buf1[DEFAULTPSM_DATA_LEN]; //for default psm
#if PSM_PLATFORM_NEZHA_DEFINED
static unsigned char tmp_buf2[DEFAULTPSM_DATA_LEN]; //SMS1
static unsigned char tmp_buf3[DEFAULTPSM_DATA_LEN]; //phonebook
#endif
//#pragma arm section rwdata, zidata

#ifdef SMS_ME_SUPPORT
static unsigned char tmp_buf_sms_me[DEFAULTPSM_DATA_LEN]; //ME SMS
#endif

static void *backend_read_config(char *backend_hint, char handleIndex);

struct backend_flash_info {
        unsigned char devname[16];              /* Device name */
        unsigned long devoff;                   /* Device offset */
        unsigned long actual_size;              /* environment size */
        unsigned long erase_size;               /* device erase size */
};


static int backend_erase_flash(struct backend_flash_info *bfi)
{
	PSM_Printf("enter %s",__FUNCTION__);

	if (bfi == NULL)
		return -1;

	if(FDI_remove_psm((char *)bfi->devname) < 0)
		return -1;

	PSM_Printf("leave %s",__FUNCTION__);
	return 0;

}

static int backend_erase_env(char *base, size_t len, struct backend_flash_info *bfi)
{
    int fd;
    char *p;

	PSM_Printf("enter %s",__FUNCTION__);

	if ((fd = FDI_fopen_psm((char *)bfi->devname, "rb")) <= 0)
		return -1;

	PSM_Printf("%s, base:0x%x\n",__FUNCTION__, base);
	p = base;
    *p = '\0';

	FDI_fclose_psm(fd);

	PSM_Printf("leave %s",__FUNCTION__);

	return 0;
}

static int __backend_eraseall(char *base, size_t len, struct backend_flash_info *bfi)
{
	int ret = 0;

	PSM_Printf("enter %s",__FUNCTION__);

	ret = backend_erase_flash(bfi);
	if (ret)
		goto erase_error;

	ret = backend_erase_env(base, len, bfi);

	PSM_Printf("leave %s",__FUNCTION__);

erase_error:
	return ret;
}

/*************** This could probably be common across backend ************/
static void *backend_read_config(char *backend_hint, char handleIndex)
{
	struct backend_flash_info *bfi = NULL;

	bfi = duster_malloc(sizeof(*bfi));
	if (bfi == NULL) {
		return NULL;
	}
	memset(bfi,0,sizeof(*bfi));

	sprintf((char *)bfi->devname, psmfile[handleIndex]);

	bfi->devoff = 0;
	bfi->actual_size = get_psm_len(handleIndex);
	bfi->erase_size = PSM_DATA_LEN;   //psm data file size

	PSM_Printf("backend_read_config:devname %s,actual size 0x%x, erase size 0x%x", bfi->devname,bfi->actual_size,bfi->erase_size);

	return bfi;
}

int fill_env(void *buff, struct backend_flash_info *bfi, char handleIndex)
{
	int fd, retval;
	size_t len;

	len = get_psm_len(handleIndex);
	if((fd = FDI_fopen_psm((char *)bfi->devname, "rb")) <= 0) {
		PSM_Printf("%s: Could not open mtd device",__FUNCTION__);
		return -1;
	}
	/* Read the entire environment. Signature + CRC + Variables */
#if PSM_PLATFORM_NEZHA_DEFINED
	if(handleIndex < handle_SMS5)
	{
		if ((retval = FDI_fread_defaultpsm(buff, 1, len, fd)) != len) {
	    	   PSM_Printf("%s  read call failed,env_size=%d bytes,read bytes=%d",__FUNCTION__,len,retval);
			goto close_fd;
		}
	}
	else
	{
		if ((retval = FDI_fread_psm(1, len, fd)) != len) {
	    	   PSM_Printf("%s  read call failed,env_size=%d bytes,read bytes=%d",__FUNCTION__,len,retval);
			goto close_fd;
		}
	}
#elif PSM_PLATFORM_CRANE_DEFINED
    if (handleIndex != handle_duster)
        goto close_fd;

    if ((retval = FDI_fread_defaultpsm(buff, 1, len, fd)) != len) {
        PSM_Printf("%s  read call failed,env_size=%d bytes,read bytes=%d",__FUNCTION__,len,retval);
        goto close_fd;
    }
#endif

	FDI_fclose_psm(fd);
	PSM_Printf("leave %s: return_code = 0",__FUNCTION__);
	return 0;

close_fd:
	FDI_fclose_psm(fd);
	PSM_Printf("leave %s: return_code = -1",__FUNCTION__);
	return -1;

}


int backend_read_env(qstr_t *env, void **backend_info, char *backend_hint, char handleIndex)
{
	//int file_fd;
	struct backend_flash_info *bfi;
        char *p;

	PSM_Printf("%s: handle index %d",__FUNCTION__, handleIndex);

	bfi = (void *)backend_read_config(backend_hint, handleIndex);
	if(bfi == NULL) {
		PSM_Printf("leave %s: return_code = -1",__FUNCTION__);
		return -1;
	}
	*backend_info = bfi;

#if PSM_PLATFORM_NEZHA_DEFINED
	if(handleIndex < handle_SMS5)
	{
		if(handleIndex == handle_duster)
		    p = (char *)tmp_buf1;
		else if(handleIndex == handle_SMS1)
		    p = (char *)tmp_buf2;
		else if(handleIndex == handle_phonebook)
		    p = (char *)tmp_buf3;
#ifdef SMS_ME_SUPPORT
		else if (handleIndex == handle_SMS_ME)
			p = (char *)tmp_buf_sms_me;
#endif
		else
		{
		    PSM_Printf("leave %s: no match file index",__FUNCTION__);
		    return -1;
		}
		memset(p, 0 , DEFAULTPSM_DATA_LEN);
	}
	else
	{
		/*should not be in here*/
		p = (char *)get_psm_data_addr();
		memset(p, 0 , PSM_DATA_LEN);
	}
#elif PSM_PLATFORM_CRANE_DEFINED
    if(handleIndex == handle_duster)
        p = (char *)tmp_buf1;
    else
    {
        PSM_Printf("leave %s: no match file index",__FUNCTION__);
        return -1;
    }
#endif
	//p = get_buf_addr();
	if( ! env->name ) {
        /* Bypass the PSM_HEADER and move to start of env */
		env->name = (unsigned char *)p;
		env->len = get_psm_len(handleIndex);

		/* not fill, should fill the env once*/
		fill_env(p, bfi, handleIndex);
	}

	PSM_Printf("%s: env name: 0x%x, size: 0x%x\n", __func__, env->name, env->len);
	PSM_Printf("%s: backend flash info, name: %s, size: 0x%x", __func__, bfi->devname, bfi->actual_size);

	return 0;
}

void backend_free_env(qstr_t *env, void *backend_info)
{
	struct backend_flash_info *bfi = backend_info;
	//char *base = GET_BASE_FROM_ENV(env->name);

	PSM_Printf("enter %s",__FUNCTION__);

	duster_free(bfi);

	env->name = NULL;
	env->len = 0;

	PSM_Printf("leave %s",__FUNCTION__);
}

int backend_write_env(qstr_t *env, void *backend_info, char handleIndex)
{
	int fd, ret = 0;
	unsigned long crc;
	unsigned int actual_len;

	struct backend_flash_info *bfi = backend_info;
	char *base;

	actual_len = util_get_actual_env_size((char *)env->name);

	PSM_Printf("%s: actual len:0x%x, env addr:0x%x, env len:0x%x",__FUNCTION__,actual_len, env->name, env->len);
	//PSM_Printf("2 handle->psmh_env, len:0x%x, name addr:0x%x",handle->psmh_env.len, handle->psmh_env.name);
	if(bfi == NULL)
	{
		PSM_Printf("%s: bfi is NULL!\n", __func__);
		ret = -1;
		goto end2;
	}

	if(bfi->devname == NULL)
	{
		PSM_Printf("%s: devname is NULL!\n");
		ret = -1;
		goto end2;
	}

	if((fd = FDI_fopen_psm((char *)bfi->devname, "wb+")) <= 0)
	{
		ret = -1;
		goto end2;
	}

	base = (char *)env->name;
	
#if PSM_PLATFORM_NEZHA_DEFINED
	if(handleIndex < handle_SMS5)
	{
		if(FDI_fwrite_defaultpsm(base, 1, actual_len ,fd) != actual_len) /* Write only required sectors */
		{
			PSM_Printf("%s: write default psm to flash failed", __func__);
			ret = -1;
			goto end1;
		}
	}
	else
	{
		if(FDI_fwrite_psm(1, actual_len ,fd) != actual_len) /* Write only required sectors */
		{
			PSM_Printf("%s: write psm to flash failed", __func__);
			ret = -1;
			goto end1;
		}
	}
#elif PSM_PLATFORM_CRANE_DEFINED
    if (handleIndex != handle_duster)
        goto end1;

    if(FDI_fwrite_defaultpsm(base, 1, actual_len ,fd) != actual_len) /* Write only required sectors */
	{
		PSM_Printf("%s: write default psm to flash failed", __func__);
		ret = -1;
		goto end1;
	}
#endif
	env->len = actual_len;

end1:
	FDI_fclose_psm(fd);
	ret = 0;

end2:
	PSM_Printf("%s: done with ret %d",__FUNCTION__,ret);
	return ret;
}


int backend_eraseall(qstr_t *env, void *p, void *backend_hint, char handleIndex)
{
        struct backend_flash_info *bfi = p;
        size_t len = 0;
	 int ret = -1;

	if (! bfi) {
        if ((bfi = backend_read_config(backend_hint, handleIndex)) == NULL)
			return -1;
	}
	ret = __backend_eraseall((char *)env->name, env->len, bfi);

	if (!p)
	{
		if (bfi)
			duster_free(bfi);
	}

	return ret;
}

