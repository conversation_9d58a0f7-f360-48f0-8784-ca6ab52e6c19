cmake_minimum_required(VERSION 3.10)

project(crane_modem C ASM CXX)

set(LV_I18N_ENABLE FALSE)
if(${LV_I18N_ENABLE} STREQUAL "TRUE")
  add_definitions(-DLV_I18N_ENABLE)
endif()

if (NOT CMAKE_BUILD_TYPE)
  message(STATUS "No build type selected, default to RelWithDebInfo")
  set(CMAKE_BUILD_TYPE "RelWithDebInfo")
endif()

set(USE_WATCH_LITE FALSE)
set(USE_WATCH_SPORT FALSE)
set(USE_INTERNAL_ROMFS FALSE)
if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
  set(ASR5311_GNSS TRUE)    #TRUE if enabled, FALSE if disabled
  set(USE_WATCH_LITE TRUE)
  set(USE_INTERNAL_ROMFS TRUE)
  set(BT_CHIP_ID 5801)
  if(DEFINED ENV{XF_PROJECT_NAME}) 
    if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
      set(ASR5311_GNSS FALSE)
    endif()
  endif()
elseif($ENV{PS_MODE} STREQUAL "LWG")
  set(BT_CHIP_ID 5803) #5801, 5803 or other value, one of the 5801 and 5803 chips could be supported
  set(ASR5311_GNSS FALSE)   #TRUE if enabled, FALSE if disabled
else()
  set(BT_CHIP_ID 5801) #5801 or other value, only 5801 chip could be supported
  set(ASR5311_GNSS FALSE)   #TRUE if enabled, FALSE if disabled
endif()

if(${USE_INTERNAL_ROMFS} STREQUAL "TRUE")
  add_definitions(-DUSE_INTERNAL_ROMFS)
  set(RESOURCE_BIN null_fw.bin)
else()
  set(RESOURCE_BIN resource.bin)
endif()

if(${VARIANT} STREQUAL "watch_sport")
  set(USE_WATCH_SPORT TRUE)
  add_definitions(-DUSE_WATCH_SPORT)
endif()

if(DEFINED BT_CHIP_ID)
  if(${BT_CHIP_ID} STREQUAL "5803")
    set(ASR5803_BTDM FALSE)   #TRUE if enabled, FALSE if disabled
    if(${VARIANT} STREQUAL "watch")
      set(ASR5803_WLAN FALSE) #TRUE if enabled, FALSE if disabled
    else()
      set(ASR5803_WLAN FALSE) #only FALSE
    endif()

    set(ASR5801_BTDM FALSE)   #only FALSE
  elseif(${BT_CHIP_ID} STREQUAL "5801")
    set(ASR5801_BTDM TRUE)   #TRUE if enabled, FALSE if disabled
    set(ASR5803_BTDM FALSE)   #only FALSE
    set(ASR5803_WLAN FALSE)   #only FALSE
  endif()
else()
  set(ASR5801_BTDM FALSE)   #only FALSE
  set(ASR5803_BTDM FALSE)   #only FALSE
  set(ASR5803_WLAN FALSE)   #only FALSE
endif()

set(INTERPHONE_DEMO FALSE)

if(DEFINED ENV{XF_FOTA})
if($ENV{XF_FOTA} STREQUAL "MSFOTARS") 
	add_definitions(  -D__XF_MSFOTARS_SUPPORT__) 
endif()
endif()

#lyj add for yuntian project
if(DEFINED ENV{XF_PROJECT_NAME})
if(NOT $ENV{XF_PROJECT_NAME} STREQUAL "")
add_definitions(
  -D__XF_PRO_$ENV{XF_PROJECT_NAME}__
)
message(STATUS "add_definitions += __XF_PRO_$ENV{XF_PROJECT_NAME}__")
endif()
endif()

if (DEFINED ENV{XF_PROJECT_NAME_EXT})
if (NOT $ENV{XF_PROJECT_NAME_EXT} STREQUAL "")
    string(REPLACE "+" ";" CUSTOM_PROJECTS $ENV{XF_PROJECT_NAME_EXT})
    foreach(project ${CUSTOM_PROJECTS})
      add_definitions(
        -D__XF_PRO_${project}__
      )
      message(STATUS "add_definitions += __XF_PRO_${project}__")
    endforeach()
endif()
endif()

if (DEFINED ENV{XF_WS_VENDOR})
if (NOT $ENV{XF_WS_VENDOR} STREQUAL "")
  set(XF_WS_VENDOR $ENV{XF_WS_VENDOR})
  add_definitions(-D__XF_WS_SUPPORT__)
  add_definitions(-D__XF_WS_VENDOR_$ENV{XF_WS_VENDOR}__)
  message(STATUS "add_definitions += __XF_WS_VENDOR_$ENV{XF_WS_VENDOR}__")
endif()
endif()

set(crane_modem_sources
  lv_gui_main.c
  lv_board.c
)

if("x${CMAKE_C_COMPILER_ID}" STREQUAL "xARMCC")
  add_compile_options("--diag-suppress=9931,9560")
else()
  add_compile_options(-Wshadow -Wuninitialized -Wno-missing-braces)
endif()

add_definitions(
  -DLV_CONF_INCLUDE_SIMPLE
  -DNV_USE_RAM
  -DMMI_ASR_RIL_BRINGUP
  -DRIL_SHLIB
  -DANDROID_MULTI_SIM
  -DASR_EXTENDED
  -D__STDC_LIMIT_MACROS
)

if(${ASR5803_BTDM} STREQUAL "TRUE")
  add_definitions(
    -DUSE_LV_BLUETOOTH
  )
  set(BT_BTBIN ${CMAKE_CURRENT_SOURCE_DIR}/wcn/ASR5803_BTDM/build_ram_only.bin)
  set(BT_BTLST ${CMAKE_CURRENT_SOURCE_DIR}/wcn/ASR5803_BTDM/btlst_sleep.bin)
elseif(${ASR5801_BTDM} STREQUAL "TRUE")
  add_definitions(
    -DUSE_LV_BLUETOOTH
  )
  set(BT_BTBIN ${CMAKE_CURRENT_SOURCE_DIR}/wcn/ASR5801_BTDM/build_ram_only.bin)
  set(BT_BTLST ${CMAKE_CURRENT_SOURCE_DIR}/wcn/ASR5801_BTDM/bt_update_26M.lst)
else()
  set(BT_BTBIN null_fw.bin)
  set(BT_BTLST null_fw.bin)
endif()

if(${ASR5803_WLAN} STREQUAL "TRUE")
  add_definitions(
    -DUSE_LV_WLAN
  )
  set(HERON_CALIFW ${CMAKE_CURRENT_SOURCE_DIR}/wcn/ASR5803_WLAN/HERON_CALIFW_A0.bin)
  set(HERON_FMACFW ${CMAKE_CURRENT_SOURCE_DIR}/wcn/ASR5803_WLAN/HERON_FMACFW_A0.bin)
else()
  set(HERON_CALIFW null_fw.bin)
  set(HERON_FMACFW null_fw.bin)
endif()

if(${ASR5311_GNSS} STREQUAL "TRUE")
  set(JACANA_FW ${CMAKE_CURRENT_SOURCE_DIR}/wcn/ASR5311_GNSS/jacana_fw.bin)
else()
  set(JACANA_FW null_fw.bin)
endif()



if (DEFINED ENV{XF_RF_BINS})
    if (NOT $ENV{XF_RF_BINS} STREQUAL "")
        string(REPLACE "+" ";" CUSTOM_RF_BINS $ENV{XF_RF_BINS})
        foreach(rf_bin ${CUSTOM_RF_BINS})
          set(RF_BINS ${RF_BINS} ${rf_bin})
        endforeach()
        message(STATUS "rfbins = ${RF_BINS}")
    endif()
else()
    if(DEFINED ENV{XF_LCD_SIZE}) 
        if(NOT $ENV{XF_LCD_SIZE} STREQUAL "") 
            if($ENV{XF_LCD_SIZE} STREQUAL "240X280")
              set(RF_BINS 240x280_rf)
            endif()
        endif() 
    else()
        set(RF_BINS rf)
    endif()
endif()

if("${RF_BINS}" STREQUAL "")
  set(RF_BINS rf)
  message(STATUS "rfbins = ${RF_BINS}")
endif()

if(${INTERPHONE_DEMO} STREQUAL "TRUE")
  add_definitions(
    -DUSE_LV_INTERPHONE_DEMO
  )
endif()

if(DEFINED ENV{XF_CMCC_COMPUTILITY})
if($ENV{XF_CMCC_COMPUTILITY} STREQUAL "ON")
add_definitions(
  -D__CMCC_COMPUTILITY_SUPPORT__
)
message(STATUS "add_definitions += D__CMCC_COMPUTILITY_SUPPORT__")
endif()
endif()


include_directories(
    ${LVGL_DIR}/lv_drivers
)
if(DEFINED ENV{XF_LCD_SIZE}) 
if(NOT $ENV{XF_LCD_SIZE} STREQUAL "") 
add_definitions(
  -D__XF_LCD_SIZE_$ENV{XF_LCD_SIZE}__ 
)
message(STATUS "add_definitions += __XF_LCD_SIZE_$ENV{XF_LCD_SIZE}__")

  add_definitions(
  -D__XF_2D4G_SUPPORT__
)
message(STATUS "add_definitions += __XF_2D4G_SUPPORT__")
endif()
else()
  add_definitions(
  -D__XF_2D4G_SUPPORT__
)
message(STATUS "add_definitions += __XF_2D4G_SUPPORT__")
endif()
if(NOT DEFINED VARIANT)
  set(VARIANT phone)
endif()

set(HAS_JPEG_LIB TRUE)


set(ALIPAY_VER "none")
if(DEFINED ENV{XF_ZFB}) 

if($ENV{XF_ZFB} STREQUAL "ON")
set(ALIPAY_VER "old")
add_definitions(
  -DZFB_SUPPORT 
) 
message(STATUS "add_definitions += ZFB_SUPPORT") 
set(XF_PATH_ALIPAY "alipay.a")

if (XF_LCD_SIZE_CMAKE STREQUAL "128X128")
set(XF_RES_ALIPAY "alipay_res128.a")
else()
set(XF_RES_ALIPAY "alipay_res240.a")
endif()
elseif($ENV{XF_ZFB} STREQUAL "ON_KF")
set(ALIPAY_VER "old")
add_definitions(
  -DZFB_SUPPORT
) 
message(STATUS "add_definitions += ZFB_SUPPORT_KF")
add_definitions(
  -DZFB_SUPPORT_KF
) 
set(XF_PATH_ALIPAY "alipay_airID2.a")

if (XF_LCD_SIZE_CMAKE STREQUAL "128X128")
set(XF_RES_ALIPAY "alipay_res128.a")
else()
set(XF_RES_ALIPAY "alipay_res240.a")
endif()
elseif($ENV{XF_ZFB} STREQUAL "SECURE")
set(ALIPAY_VER "secure")
add_definitions(
  -DZFB_SECURE_CHIP
)
message(STATUS "add_definitions += ZFB_SECURE_CHIP")
endif()
endif()

if(${ALIPAY_VER} STREQUAL "old")
	include_directories(
	    ${LVGL_DIR}/lv_drivers
	    alios/linkkit/include
	    alios/common/include
	)
else()
	include_directories(
	    ${LVGL_DIR}/lv_drivers
	)
endif() # if(${ALIPAY_VER} STREQUAL "old")



if(${VARIANT} STREQUAL "phone")
  if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
    set(PRODUCT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config/phone_lite)
  else()
    set(PRODUCT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config/phone)
  endif()
elseif(${VARIANT} STREQUAL "watch")
  if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
    set(PRODUCT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config/watch_lite)
  else()
    set(PRODUCT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config/watch)
  endif()
elseif(${VARIANT} STREQUAL "watch_sport")
  set(PRODUCT_CONFIG_DIR ${CMAKE_CURRENT_SOURCE_DIR}/config/watch_sport)
endif()
message("Selected product variant: ${VARIANT}")

get_filename_component(LVGL_DIR "../../gui" REALPATH)
get_filename_component(EXTERNAL_DIR "../../external" REALPATH)

add_subdirectory(${EXTERNAL_DIR}/zlib zlib)
add_subdirectory(${EXTERNAL_DIR}/libpng libpng)
if(${HAS_JPEG_LIB} STREQUAL "FALSE")
    add_subdirectory(${EXTERNAL_DIR}/libjpeg-turbo libjpeg)
endif()
add_subdirectory(${EXTERNAL_DIR}/libqrencode libqrencode)
add_subdirectory(${EXTERNAL_DIR}/code128 code128)
add_subdirectory(${EXTERNAL_DIR}/third-party-libs/juphoon juphoon EXCLUDE_FROM_ALL)
add_subdirectory(${EXTERNAL_DIR}/third-party-libs/freetype freetype EXCLUDE_FROM_ALL)
if(${ALIPAY_VER} STREQUAL "secure")
add_subdirectory(${EXTERNAL_DIR}/third-party-libs/alipay alipay EXCLUDE_FROM_ALL)
endif()
if(XF_WS_VENDOR STREQUAL "KAER_ZW")
add_subdirectory(${EXTERNAL_DIR}/third-party-libs/ynSchool lv_ynSchool EXCLUDE_FROM_ALL)
endif()
add_subdirectory(${LVGL_DIR}/lvgl lvgl)
add_subdirectory(${LVGL_DIR}/lv_drivers lv_drivers)
if(${VARIANT} STREQUAL "phone")
  add_subdirectory(${LVGL_DIR}/lv_phone lv_phone EXCLUDE_FROM_ALL)
elseif(${VARIANT} STREQUAL "watch" OR ${VARIANT} STREQUAL "watch_sport")
  add_subdirectory(${LVGL_DIR}/lv_watch lv_watch EXCLUDE_FROM_ALL)
endif()

if (DEFINED ENV{XF_HEART})
  add_subdirectory(${LVGL_DIR}/lv_drivers/kr_drv kr_drv)
endif()

add_subdirectory("ril" ril)

set(PS_MODE $ENV{PS_MODE})
string(TOLOWER ${PS_MODE} PS_MODE_LOWER_CASE)
set(TARGET_OS $ENV{TARGET_OS})
set(CHIP_ID $ENV{CHIP_ID})

if(${TARGET_OS} STREQUAL "THREADX")

if(${CHIP_ID} STREQUAL "CRANE")
set(CRANE_LIB_DIR tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_${PS_MODE})

if($ENV{PS_MODE} STREQUAL "LTEGSM")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_LTEGSM/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANE_LTEGSM_BEGIN/,/^  # MODEM_LIB_CRANE_LTEGSM_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANE_LTEGSM_LIST/a\  # MODEM_LIB_CRANE_LTEGSM_BEGIN\n  # MODEM_LIB_CRANE_LTEGSM_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANE_LTEGSM_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANE_LTEGSM_LIST
  # MODEM_LIB_CRANE_LTEGSM_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/gplc-GPLC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/os-threadx.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/sac.lib

  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANE_LTEGSM_END
)
endif() # if($ENV{PS_MODE} STREQUAL "LTEGSM")

endif() # if(${CHIP_ID} STREQUAL "CRANE")

if(${CHIP_ID} STREQUAL "CRANEL")
set(CRANE_LIB_DIR tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_${PS_MODE})

if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_LITE_LTEONLY/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEL_LITE_LTEONLY_BEGIN/,/^  # MODEM_LIB_CRANEL_LITE_LTEONLY_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEL_LITE_LTEONLY_LIST/a\  # MODEM_LIB_CRANEL_LITE_LTEONLY_BEGIN\n  # MODEM_LIB_CRANEL_LITE_LTEONLY_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEL_LITE_LTEONLY_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEL_LITE_LTEONLY_LIST
  # MODEM_LIB_CRANEL_LITE_LTEONLY_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/os-threadx.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/sac.lib

  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LITE_LTEONLY/armps.lib
  
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEL_LITE_LTEONLY_END
)
    if(DEFINED ENV{XF_PROJECT_NAME}) 
        if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
          set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
          cust/nfc_sl6568/lib/libslm_nfc_stack_asr.a
        )
        endif()
    endif()
endif() # if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")

endif() # if(${CHIP_ID} STREQUAL "CRANEL")

if(${CHIP_ID} STREQUAL "CRANEG")
set(CRANE_LIB_DIR tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_${PS_MODE})

if($ENV{PS_MODE} STREQUAL "LTEONLY")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_LTEONLY/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEG_LTEONLY_BEGIN/,/^  # MODEM_LIB_CRANEG_LTEONLY_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEG_LTEONLY_LIST/a\  # MODEM_LIB_CRANEG_LTEONLY_BEGIN\n  # MODEM_LIB_CRANEG_LTEONLY_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEG_LTEONLY_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEG_LTEONLY_LIST
  # MODEM_LIB_CRANEG_LTEONLY_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/agpstp-agpstp_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/os-threadx.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/sac.lib

  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEONLY/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEG_LTEONLY_END
)
if(DEFINED ENV{XF_PROJECT_NAME}) 
if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 cust/nfc_sl6568/lib/libslm_nfc_stack_asr.a
)
endif()
endif()
endif() # if($ENV{PS_MODE} STREQUAL "LTEONLY")

if($ENV{PS_MODE} STREQUAL "LTEGSM")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_LTEGSM/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEG_LTEGSM_BEGIN/,/^  # MODEM_LIB_CRANEG_LTEGSM_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEG_LTEGSM_LIST/a\  # MODEM_LIB_CRANEG_LTEGSM_BEGIN\n  # MODEM_LIB_CRANEG_LTEGSM_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEG_LTEGSM_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEG_LTEGSM_LIST
  # MODEM_LIB_CRANEG_LTEGSM_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/gplc-GPLC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/os-threadx.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/sac.lib

  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LTEGSM/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEG_LTEGSM_END
)
endif() # if($ENV{PS_MODE} STREQUAL "LTEGSM")

if($ENV{PS_MODE} STREQUAL "LWG")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_LWG/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEG_LWG_BEGIN/,/^  # MODEM_LIB_CRANEG_LWG_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEG_LWG_LIST/a\  # MODEM_LIB_CRANEG_LWG_BEGIN\n  # MODEM_LIB_CRANEG_LWG_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEG_LWG_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEG_LWG_LIST
  # MODEM_LIB_CRANEG_LWG_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/gplc-GPLC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/aplp-APLP.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/os-threadx.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/sac.lib

  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_LWG/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEG_LWG_END
)
endif() # if($ENV{PS_MODE} STREQUAL "LWG")

endif() # if(${CHIP_ID} STREQUAL "CRANEG")

endif() # if(${TARGET_OS} STREQUAL "THREADX")

if(${TARGET_OS} STREQUAL "ALIOS")

if(${CHIP_ID} STREQUAL "CRANE")
set(CRANE_LIB_DIR tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_${TARGET_OS}_${PS_MODE})

if($ENV{PS_MODE} STREQUAL "LTEGSM")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_ALIOS_LTEGSM/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_ALIOS_LTEGSM_BEGIN/,/^  # MODEM_LIB_ALIOS_LTEGSM_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_ALIOS_LTEGSM_LIST/a\  # MODEM_LIB_ALIOS_LTEGSM_BEGIN\n  # MODEM_LIB_ALIOS_LTEGSM_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_ALIOS_LTEGSM_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANE_ALIOS_LTEGSM_LIST
  # MODEM_LIB_CRANE_ALIOS_LTEGSM_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/gplc-GPLC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/sac.lib

  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/armps.lib
  # MODEM_LIB_CRANE_ALIOS_LTEGSM_END
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
)
if(DEFINED ENV{XF_PROJECT_NAME}) 
if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 cust/nfc_sl6568/lib/libslm_nfc_stack_asr.a
)
endif()
endif()
endif() # if($ENV{PS_MODE} STREQUAL "LTEGSM")

endif() # if(${CHIP_ID} STREQUAL "CRANE")

if(${CHIP_ID} STREQUAL "CRANEL")
set(CRANE_LIB_DIR tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_${TARGET_OS}_${PS_MODE})

if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_ALIOS_LITE_LTEONLY/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_BEGIN/,/^  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_LIST/a\  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_BEGIN\n  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_END/i\  $f" CMakeLists.txt
#   done
#
if(DEFINED ENV{XF_LCD_SIZE}) 
if(NOT $ENV{XF_LCD_SIZE} STREQUAL "") 
if($ENV{XF_LCD_SIZE} STREQUAL "240X280")
add_library(modem OBJECT IMPORTED GLOBAL)

if(${ALIPAY_VER} STREQUAL "old")
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_LIST
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/agpstp-agpstp_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-GPIO_240x280.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/sac.lib

  #alipay end
  softutil/softutil/mep_r.a
  hal/gps/aboot/lib/libaboot_tiny.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/armps.lib
  ##ws lib here
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/kws-kws.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
 #${LVGL_DIR}/../gui/lv_drivers/nfc/mifare/mifare_crack.lib
  ${LVGL_DIR}/../gui/lv_drivers/nfc/mifare/mifare_crack.a
  ${LVGL_DIR}/../gui/lv_drivers/hr_spo/Gcc_Slib_R5_3690_hrs_spo2_hrv_20231229_v02.1a.a
  ${LVGL_DIR}/../gui/lv_drivers/hr_spo/libStressEstimate.a
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_END
)
if(DEFINED ENV{XF_PROJECT_NAME}) 
if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 cust/nfc_sl6568/lib/libslm_nfc_stack_asr.a
)
endif()
endif()
  
if(DEFINED ENV{XF_WS_LIB})
if($ENV{XF_WS_LIB} STREQUAL "ws_cust")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 ws/libs/ws_ws_cust.lib
)
else()
endif()
else()
endif()

else()
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_LIST
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/agpstp-agpstp_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-GPIO_240x280.lib 
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/sac.lib

  softutil/softutil/mep_r.a
  hal/gps/aboot/lib/libaboot_tiny.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/kws-kws.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
 #${LVGL_DIR}/../gui/lv_drivers/nfc/mifare/mifare_crack.lib
  ${LVGL_DIR}/../gui/lv_drivers/nfc/mifare/mifare_crack.a
  ${LVGL_DIR}/../gui/lv_drivers/hr_spo/Gcc_Slib_R5_3690_hrs_spo2_hrv_20231229_v02.1a.a
  ${LVGL_DIR}/../gui/lv_drivers/hr_spo/libStressEstimate.a
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_END
)
if(DEFINED ENV{XF_PROJECT_NAME}) 
if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 cust/nfc_sl6568/lib/libslm_nfc_stack_asr.a
)
endif()
endif()
if(DEFINED ENV{XF_WS_LIB})
if($ENV{XF_WS_LIB} STREQUAL "ws_cust")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 ws/libs/ws_ws_cust.lib
)
else()
endif()
else()
endif()

endif() ##if(${ALIPAY_VER} STREQUAL "old")
endif() #if($ENV{XF_LCD_SIZE} STREQUAL "240X280")
endif() #if(NOT $ENV{XF_LCD_SIZE} STREQUAL "") 
else()
add_library(modem OBJECT IMPORTED GLOBAL)
if(${ALIPAY_VER} STREQUAL "old")
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_LIST
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/agpstp-agpstp_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-AES.lib  
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/sac.lib

  #alipay end
  softutil/softutil/mep_r.a
  hal/gps/aboot/lib/libaboot_tiny.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/kws-kws.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_END
)
if(DEFINED ENV{XF_PROJECT_NAME}) 
if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 cust/nfc_sl6568/lib/libslm_nfc_stack_asr.a
)
endif()
endif()
if(DEFINED ENV{XF_WS_LIB})
if($ENV{XF_WS_LIB} STREQUAL "ws_cust")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 ws/libs/ws_ws_cust.lib
)
else()
endif()
else()
endif()

else()
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_LIST
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/agpstp-agpstp_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hal-GPIO_240x280.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-AES.lib  
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/sac.lib

  softutil/softutil/mep_r.a
  hal/gps/aboot/lib/libaboot_tiny.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/kws-kws.lib

  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEL_ALIOS_LITE_LTEONLY_END
)
if(DEFINED ENV{XF_PROJECT_NAME}) 
if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 cust/nfc_sl6568/lib/libslm_nfc_stack_asr.a
)
endif()
endif()
if(DEFINED ENV{XF_WS_LIB})
if($ENV{XF_WS_LIB} STREQUAL "ws_cust")
set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
 ws/libs/ws_ws_cust.lib
)
else()
endif()
else()
endif()

endif()
endif() # if(DEFINED ENV{XF_LCD_SIZE}) 

endif() # if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")

endif() # if(${CHIP_ID} STREQUAL "CRANEL")

if(${CHIP_ID} STREQUAL "CRANEG")
set(CRANE_LIB_DIR tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_${TARGET_OS}_${PS_MODE})

if($ENV{PS_MODE} STREQUAL "LTEONLY")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_ALIOS_LTEONLY/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_BEGIN/,/^  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_LIST/a\  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_BEGIN\n  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_LIST
  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/agpstp-agpstp_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/sac.lib

  #alipay end
  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEONLY/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEG_ALIOS_LTEONLY_END
)
endif() # if($ENV{PS_MODE} STREQUAL "LTEONLY")

if($ENV{PS_MODE} STREQUAL "LTEGSM")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_ALIOS_LTEGSM/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_BEGIN/,/^  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_LIST/a\  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_BEGIN\n  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_LIST
  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/gplc-GPLC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/sac.lib

  #alipay end
  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LTEGSM/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEG_ALIOS_LTEGSM_END
)
endif() # if($ENV{PS_MODE} STREQUAL "LTEGSM")

if($ENV{PS_MODE} STREQUAL "LWG")
#
# Generate list with script
#   CP_OBJLIB_LIST=$(cat tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/Arbel_objliblist.txt | \
#     sed 's/\s*$//' | \
#     sed 's/..\\obj_PMD2NONE/\\tavor\\Arbel\\CRANE_SDK_LIB\\FP_GENERIC_ALIOS_LWG/' | \
#     sed 's|\\|/|g' | sed 's|^\/||')
#   sed -i '/^  # MODEM_LIB_CRANEG_ALIOS_LWG_BEGIN/,/^  # MODEM_LIB_CRANEG_ALIOS_LWG_END/d' CMakeLists.txt
#   sed -i '/^  # MODEM_LIB_CRANEG_ALIOS_LWG_LIST/a\  # MODEM_LIB_CRANEG_ALIOS_LWG_BEGIN\n  # MODEM_LIB_CRANEG_ALIOS_LWG_END' CMakeLists.txt
#   for f in $CP_OBJLIB_LIST; do
#     sed -i "/^  # MODEM_LIB_CRANEG_ALIOS_LWG_END/i\  $f" CMakeLists.txt
#   done
#
add_library(modem OBJECT IMPORTED GLOBAL)
set_property(TARGET modem PROPERTY IMPORTED_OBJECTS
  # MODEM_LIB_CRANEG_ALIOS_LWG_LIST
  # MODEM_LIB_CRANEG_ALIOS_LWG_BEGIN
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/ps_init.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/ps_nvm.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/GKITick.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/tavor_packages.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/dsp_filters.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/usbmgrttpal.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/root.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/diagDB.o
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/os-posix.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hal-HAL.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/CrossPlatformSW-CrossPlatformSW.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/csw-PM.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-softutil.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/diag-diag.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/cust-cust.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/nota-nota.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/framework-framework.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/pcac-pca_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/aud_sw-Audio.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/gplc-GPLC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/aplp-APLP.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-AES.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-yaffs2.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/3g_ps-dps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/os-osa.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/genlib-fsm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/ltel1a-LTEL1A.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/genlib-qmgr.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/genlib-min_max.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/CRD-CRD.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/drat-DRAT.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-rm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-aam.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-dma.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-intc.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-timer.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-RTC.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-pmu.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-commpm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-pm.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-aci.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-mrd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-mmi_mat.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/nota-sulog.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/l1wlan-l1wlan.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-csw_memory.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-lzop.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-TickManager.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-fatfs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/pcac-lwipv4v6.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/pcac-dial.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/pcac-ci_stub.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/volte-volte_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/ims-ims_components.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hal-rndis.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hal-GPIO.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/hop-core.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-fatsys.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/softutil-littlefs.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/os-alios.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/commond.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/dpd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/aslte.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/nsab_d.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/utd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/usbd.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/sac.lib

  #alipay end
  softutil/softutil/mep_r.a
  hal/BT_hoststack/lib/btstack/hal-BT_hoststack.lib
  hal/gps/aboot/lib/libaboot_tiny.a
  aud_sw/lib/libcpaudio.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/libhal.a
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LWG/armps.lib
  tavor/Arbel/CRANE_SDK_LIB/FP_GENERIC_ALIOS_LITE_LTEONLY/pcac-lwm2m.lib
  # MODEM_LIB_CRANEG_ALIOS_LWG_END
)
endif() # if($ENV{PS_MODE} STREQUAL "LWG")

endif() # if(${CHIP_ID} STREQUAL "CRANEG")

endif() # if(${TARGET_OS} STREQUAL "ALIOS")


if(${TARGET_OS} STREQUAL "ALIOS") 
if(${ALIPAY_VER} STREQUAL "old")
set_property(TARGET modem 
APPEND PROPERTY IMPORTED_OBJECTS 
# Alipay start
  alios/common/lib/osal_aos.a
  alios/common/lib/kernel_init.a
  alios/common/lib/kv.a
  alios/common/lib/debug.a
  alios/common/lib/mcu_asr3601.a
  alios/common/lib/ulog.a
  alios/common/lib/activation.a
  alios/common/lib/chip_code.a
  alios/common/lib/mbedtls.a
  alios/common/lib/cjson.a
  alios/linkkit/lib/libiot_certs.a
  alios/linkkit/lib/libiot_wrappers.a
  alios/linkkit/lib/libiot_devmodel.a
  alios/linkkit/lib/libiot_dynreg.a
  alios/linkkit/lib/libiot_infra.a
  alios/linkkit/lib/libiot_mqtt.a
  alios/linkkit/lib/libiot_sign.a
  alios/linkkit/lib/identify.a
  alios/upay/lib/AlipayInsideSESDK.lib
  alios/upay/lib/ALIPAY_IoTPay_network.lib
  alios/upay/lib/alipay_iot_device_name.lib
  alios/upay/lib/alipay_iot_netcore.lib
  alios/upay/lib/alipay_iot_rpc.lib
  alios/upay/lib/protobuf-c.lib
  alios/upay/lib/upay.a
  alios/upay/lib/alicrypto.a
  alios/upay/lib/id2.a
  alios/upay/lib/libkm.a
  alios/upay/lib/libprov.a
  alios/upay/lib/libsst.a  
  alios/upay/lib/ls_alipay.a
  alios/upay/lib/alipay_mbedtls.lib
  alios/upay/lib/alipay_qrdecoder.lib
  alios/upay/lib/cJSON.lib
  # Alipay end 
  ) 

if(NOT ${XF_PATH_ALIPAY} STREQUAL "open")
  set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS
                 alios/${XF_PATH_ALIPAY}
                 alios/${XF_RES_ALIPAY}
              )
endif()

endif() 
endif() # if(${TARGET_OS} STREQUAL "ALIOS")
add_executable(crane_modem ${crane_modem_sources} $<TARGET_OBJECTS:modem>)

target_include_directories(crane_modem PRIVATE ${PRODUCT_CONFIG_DIR} ${LVGL_DIR})

target_link_libraries(crane_modem lvgl lvgl_ram lvgl_hal lv_drivers lv_drivers_ram ril juphoon)
if("x${CMAKE_C_COMPILER_ID}" STREQUAL "xARMCC")
  target_link_options(crane_modem PRIVATE
    --map
    --symbols
    --info=sizes,totals
    --keep=init.o\(Header\)
    --keep=init.o\(Vectors\)
    --predefine="-DL1_WIFI_LOCATION"
    --predefine="-DBT_SUPPORT"
    --diag-suppress=6304,6312,6314,6319,6329,9931
  )
  if(${ASR5311_GNSS} STREQUAL "TRUE")
    target_link_options(crane_modem PRIVATE --predefine="-DAGPSTP_ENABLE")
  endif()
  if($ENV{PS_MODE} STREQUAL "LTEONLY")
    target_link_options(crane_modem PRIVATE
      --scatter=${CMAKE_CURRENT_SOURCE_DIR}/csw/platform/dev_plat/build/Crane_DS_16M_Ram_16M_Flash_XIP_Common.sct
    )
  endif()
  if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
    target_link_options(crane_modem PRIVATE
      --predefine="-DEMBED_DSP"
      --scatter=${CMAKE_CURRENT_SOURCE_DIR}/csw/platform/dev_plat/build/Crane_DS_16M_Ram_8M_Flash_XIP_Common.sct
    )
  endif()
  if($ENV{PS_MODE} STREQUAL "LTEGSM")
    target_link_options(crane_modem PRIVATE
      --predefine="-DENABLE_CAT1_LG"
      --scatter=${CMAKE_CURRENT_SOURCE_DIR}/csw/platform/dev_plat/build/Crane_DS_16M_Ram_16M_Flash_XIP_Common.sct
    )
  endif()
  if($ENV{PS_MODE} STREQUAL "LWG")
    target_link_options(crane_modem PRIVATE
      --predefine="-DENABLE_WB_R99"
      --scatter=${CMAKE_CURRENT_SOURCE_DIR}/csw/platform/dev_plat/build/CraneG_DS_XIP_Common.sct
    )
  endif()
endif()

set(CUSTOM_PRODUCT $ENV{CUSTOM_PRODUCT})
if("${CUSTOM_PRODUCT}" STREQUAL "")
  if($ENV{PS_MODE} STREQUAL "LTEONLY")
    set(CUSTOM_PRODUCT CRANEG_A0_16MB_LTEONLY)
  elseif($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
    set(CUSTOM_PRODUCT ASR3602_08MB)
  elseif($ENV{PS_MODE} STREQUAL "LTEGSM")
    if($ENV{CHIP_ID} STREQUAL "CRANE")
      set(CUSTOM_PRODUCT CRANE_A0_16MB CRANE_A0_16+8MB)
    endif()
    if($ENV{CHIP_ID} STREQUAL "CRANEG")
      set(CUSTOM_PRODUCT CRANEG_A0_16MB_LTEGSM)
    endif()
  elseif($ENV{PS_MODE} STREQUAL "LWG")
    set(CUSTOM_PRODUCT CRANEG_A0_16+8MB_LWG CRANEG_A0_16X2+8MB_LWG)
  endif()
endif()

set_target_properties(crane_modem PROPERTIES SUFFIX ".elf")
if(${VARIANT} STREQUAL "phone")
  set_target_properties(crane_modem PROPERTIES OUTPUT_NAME "craneg_modem_phone")
  target_link_libraries(crane_modem lv_phone)
elseif(${VARIANT} STREQUAL "watch")
  set_target_properties(crane_modem PROPERTIES OUTPUT_NAME "craneg_modem_watch")
  target_link_libraries(crane_modem lv_watch)
elseif(${VARIANT} STREQUAL "watch_sport")
  set_target_properties(crane_modem PROPERTIES OUTPUT_NAME "craneg_modem_watch_sport")
  target_link_libraries(crane_modem lv_watch)
endif()

#lyj add for yuntian 3602 project
if (DEFINED ENV{XF_RELEASEPACK_NAME})
if (NOT $ENV{XF_RELEASEPACK_NAME} STREQUAL "")
  set(RELCONF_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../releasepack_yt3602/$ENV{XF_RELEASEPACK_NAME}")
endif()
endif()

if (DEFINED ENV{XF_HEART})
  	set_property(TARGET modem APPEND PROPERTY IMPORTED_OBJECTS ${LVGL_DIR}/libs/libkr_drv.a)
endif()

if("${RELCONF_PATH}" STREQUAL "")
	if(DEFINED ENV{XF_LCD_SIZE}) 
	  if(NOT $ENV{XF_LCD_SIZE} STREQUAL "") 
	  if($ENV{XF_LCD_SIZE} STREQUAL "240X280")
		set(RELCONF_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../releasepack")
	  endif()
	  endif()
	else()
		set(RELCONF_PATH "${CMAKE_CURRENT_SOURCE_DIR}/../../releasepack _L6201")
	endif()
endif()

set(OUTPUT_NAME $<TARGET_PROPERTY:crane_modem,OUTPUT_NAME>)
add_custom_command(
  TARGET crane_modem
  POST_BUILD
  COMMAND ${CMAKE_OBJCOPY}
  ARGS --diag-suppress=9931 --bin --output ${OUTPUT_NAME}.bin ${OUTPUT_NAME}.elf
  COMMAND xzcat
  ARGS ${CMAKE_CURRENT_SOURCE_DIR}/${CRANE_LIB_DIR}/Arbel_MDB.txt.xz > ${OUTPUT_NAME}.mdb.txt
  COMMAND cp
  ARGS ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/specialPShandle.txt .
  COMMAND ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/platform
  ARGS -p DKB_SS -t EVB_3 -i NONE -b ${OUTPUT_NAME}.bin -o ${OUTPUT_NAME}2.bin
  COMMAND cp
  ARGS ${OUTPUT_NAME}2.bin ${OUTPUT_NAME}_unziped.bin
  COMMAND perl ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/external_rw_region_compress.pl
  ARGS ${OUTPUT_NAME}2.bin
  COMMAND perl ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/external_loadtable_update.pl
  ARGS ${OUTPUT_NAME}2.bin CRANE_DS_Z2A0_${PS_MODE}_DKB_XIP_LWIP_GENERIC_SDK
)

if($ENV{PS_MODE} STREQUAL "LITE_LTEONLY")
  set(LOGO_BIN ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/logo_no_lcd_lite.bin)
  if(DEFINED ENV{XF_LCD_SIZE}) 
    if(NOT $ENV{XF_LCD_SIZE} STREQUAL "") 
      if($ENV{XF_LCD_SIZE} STREQUAL "240X280")
        set(BOOT33_BIN ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/boot33_lite_kxs50.bin)
      endif()
    endif()
  elseif(DEFINED ENV{XF_PROJECT_NAME}) 
    if($ENV{XF_PROJECT_NAME} STREQUAL "A0376")
      set(LOGO_BIN ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/logo_no_lcd_lite_A0376.bin)
      set(BOOT33_BIN ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/boot33_lite_A0376.bin)
    endif()
  else()
    set(BOOT33_BIN ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/boot33_lite.bin)
  endif()
else ()
  set(LOGO_BIN ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/logo_no_lcd.bin)
  set(BOOT33_BIN ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/boot33.bin)
endif()
add_custom_command(
  TARGET crane_modem
  POST_BUILD
  COMMAND perl ${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/external_logo_image_handle.pl
  ARGS 'APPEND' ${OUTPUT_NAME}2.bin ${LOGO_BIN}
)
add_custom_command(
  TARGET crane_modem
  POST_BUILD
  COMMAND mv
  ARGS ${OUTPUT_NAME}2.bin ${OUTPUT_NAME}.bin
)
set(RESOURCE "resource")
if (DEFINED ENV{XF_RESOUCE_DIR})
if (NOT $ENV{XF_RESOUCE_DIR} STREQUAL "")
    message(STATUS "resource_dir = $ENV{XF_RESOUCE_DIR}")
    set(RESOURCE $ENV{XF_RESOUCE_DIR})
endif()
endif()

if (DEFINED ENV{XF_RES_AUTO_GEN})
if (NOT $ENV{XF_RES_AUTO_GEN} STREQUAL "")
    set(RESOURCE_ORG ${LVGL_DIR}/lv_watch/${RESOURCE})
    set(RESOURCE_TMP ${LVGL_DIR}/lv_watch/resource_temp)
    set(RES_BASE_LIST ${RESOURCE_ORG}/$ENV{XF_RES_AUTO_GEN}.lst)
    set(RES_FILTER_LIST ${LVGL_DIR}/lv_watch/resource.lst)
    set(FINDRES "${MISC_TOOLS_PATH}/findres")
    set(GENROMFS "${MISC_TOOLS_PATH}/genromfs")
    add_custom_command(
      TARGET crane_modem
      POST_BUILD
      COMMAND_EXPAND_LISTS
      COMMAND ${FINDRES}
      ARGS ${OUTPUT_NAME}_unziped.bin ${RES_FILTER_LIST} "R:/"
      COMMAND perl ${LVGL_DIR}/lv_watch/resappend.pl ${RES_FILTER_LIST} ${RES_BASE_LIST}
      COMMAND perl ${LVGL_DIR}/lv_watch/filecopy.pl ${RESOURCE_ORG} ${RESOURCE_TMP} ${RES_FILTER_LIST} 1 
      COMMAND ${GENROMFS}
      ARGS -V "resource" -f "resource.bin" -d ${RESOURCE_TMP}
    )


endif()
endif() 


foreach(custom_product ${CUSTOM_PRODUCT})
  string(TOLOWER ${custom_product} CUSTOM_PRODUCT_LOWER_CASE)
  set(PACKAGE_NAME $<TARGET_PROPERTY:crane_modem,OUTPUT_NAME>_${CUSTOM_PRODUCT_LOWER_CASE})
  foreach(rfbin ${RF_BINS})
  add_custom_command(
    TARGET crane_modem
    POST_BUILD
    COMMAND ${ARELEASE}
    ARGS -c ${RELCONF_PATH} -g -p ASR_CRANE_EVB -v ${custom_product}
         -i cp=${OUTPUT_NAME}.bin
         -i dsp=${CMAKE_CURRENT_SOURCE_DIR}/dsp/${CHIP_ID}/${TARGET_OS}/${PS_MODE}/dsp.bin
         -i rfbin=${CMAKE_CURRENT_SOURCE_DIR}/dsp/${CHIP_ID}/${TARGET_OS}/${PS_MODE}/${rfbin}.bin
         -i boot33_bin=${BOOT33_BIN}
         -i apn=${CMAKE_CURRENT_SOURCE_DIR}/tavor/Arbel/build/apn.bin
         -i resource=${RESOURCE_BIN}
         -i bt_btbin=${BT_BTBIN}
         -i bt_btlst=${BT_BTLST}
         -i heron_califw=${HERON_CALIFW}
         -i heron_fmacfw=${HERON_FMACFW}
         -i jacana_fw=${JACANA_FW}
         --release-pack ${PACKAGE_NAME}_source.zip
		 ${XF_BIN_PRE_NAME}_${PACKAGE_NAME}_${rfbin}.zip
  )
  endforeach()
endforeach()
