#include "sys.h"    
#include <string.h>
#include <stdlib.h>
#include <limits.h>
#include "osa.h"
#include "yt_GPS.h"
#include "yt_queue.h"
#include <stdbool.h>


extern void ASR_GPS_Init(void);
extern void ASR_GPS_UART_Set(void);
extern int GPS_InitQueue(sqQueue *Q);
extern int GPS_EnQueue(sqQueue *Q,char* e,int size);
extern int GPS_QueueLength(const sqQueue *Q);

nmea_msg nmea_msg_info_cur;

void degree_minute_to_degree(char * data, char * out, DEGREE_OF_TYPE itude_type);

static int strStartsWith(const char *line, const char *prefix);


OSATimerRef start_handle_data_timer_ref = NULL; //处理数据

OS_HISR 	uart_detect_hisr_ref;

char pGPSReader_NewLineEx[MAX_GPS_ONE_TIME_DATA_BYTE]; //pGPSReader_NewLineEx[80];

char gpsData[MAX_GPS_ONE_TIME_DATA_BYTE]; //gpsData[80];

unsigned long event = 0;

static OSAFlagRef gps_data_read_flag_ref = 0;

sqQueue GPS_Queue;
 
#define GPS_UART_Read_Flag   2


void data_timer_handler(UINT32 arg)
{
   OSAFlagSet(gps_data_read_flag_ref, GPS_DATA_EVENT_PROCESS, OSA_FLAG_OR);
}


void handle_uart_hisr()
{
    UINT32 timeout;
	
    OS_STATUS status;

    OSATimerStop(start_handle_data_timer_ref);

    int  start_time = 100;
   
    timeout = start_time / 5;

    if (timeout == 0)
        timeout = 1;

    status = OSATimerStart(start_handle_data_timer_ref,
                   timeout,
                   0,
                   data_timer_handler,
                   0);
}



void GPS_UART_recvData(UART_Port portNumber, UINT8 *data, UINT16 lenToRead)
{

    if (lenToRead < MAX_GPS_ONE_TIME_DATA_BYTE)
    {
      

        int  result =  GPS_EnQueue(&GPS_Queue, data,lenToRead);

	     if(result==0){
		 	
			    DIAG_FILTER(GPS_GPS,GPS_UART_recvData,GPS_UART_recvData_02,DIAG_INFORMATION)
					   
			    diagPrintf("quene is full");

	     	}


	     STATUS status;
		 
		status = OS_Activate_HISR(&uart_detect_hisr_ref);
		
		ASSERT(status == OS_SUCCESS);


	//	OSAFlagSet(gps_data_read_flag_ref, GPS_DATA_EVENT_PROCESS, OSA_FLAG_OR);
		
      //  handle_uart_hisr();
    }else{

		   DIAG_FILTER(GPS_GPS,GPS_UART_recvData,GPS_UART_recvData_01,DIAG_INFORMATION)
			  
		  diagPrintf("data to large");


    	}
}

struct gps_struct *  gps_struct_init()
{
	struct gps_struct * chan;

	chan = (struct gps_struct *)malloc(sizeof(struct gps_struct));
	if(!chan) {
	return NULL;
	}

	memset(chan, 0x0, sizeof(struct gps_struct));
	chan->GPSBuffer[0] = '\0';
	chan->GPSBufferCur = chan->GPSBuffer;

	return chan;
}

static char * findGpsNextEOL(char * cur)
{

	char * p_cur = NULL;
	p_cur = cur;
    while(*p_cur != '\0' && *p_cur != '\r' && *p_cur != '\n')
		p_cur++;

	if (*p_cur == '\0')
	{
	
	}
	else
	{
		
	}
	
    return *p_cur == '\0' ? NULL : p_cur;
}


static int readGpsline(char *p_read,int maxlenth)
{
	int length = 0;

	memset(pGPSReader_NewLineEx,0,MAX_GPS_ONE_TIME_DATA_BYTE);

    GPS_DeQueue(&GPS_Queue,pGPSReader_NewLineEx);  

	length = strlen(pGPSReader_NewLineEx);

	ASSERT(length < maxlenth);

	memcpy(p_read,pGPSReader_NewLineEx,length);

	int quelen = GPS_QueueLength(&GPS_Queue);

	return length;
}


static const char * getGpsLine(struct gps_struct * chan)
{

	
    char * p_eol = NULL;
    char * ret = NULL;

    if(*chan->GPSBufferCur == '\0')
	{
		return NULL;
	}
 
    while(*chan->GPSBufferCur == '\r' || *chan->GPSBufferCur == '\n')
	{
        chan->GPSBufferCur++;
    }
	
    p_eol = findGpsNextEOL(chan->GPSBufferCur);
  
    if(p_eol)
	{
        ret = chan->GPSBufferCur;
        *p_eol = '\0';
        chan->GPSBufferCur = p_eol + 1;

        while(*chan->GPSBufferCur == '\r' || *chan->GPSBufferCur == '\n')
		{
            chan->GPSBufferCur++;
        }
    }

    return ret;
}

static int strStartsWith(const char *line, const char *prefix)
{
    for (; *line != '\0' && *prefix != '\0'; line++, prefix++)
    {
        if (*line != *prefix)
        {
            return 0;
        }
    }

    return *prefix == '\0';
}

typedef void (*gps_cb_t)(nmea_msg *nmea_msg_info);

gps_cb_t gps_callback_func;

int need_callback=0;


void setNeedGPsCallback()
{

       need_callback=1;
	   
}


void cancelGPsCallback()
{

      need_callback=0;
}
char lat_degree[16]={0};
char lon_degree[16]={0};
void setLocatonCallback(void *cbdata)
{
	memset(&nmea_msg_info_cur, 0, sizeof(nmea_msg));
    memset(lat_degree,0,sizeof(lat_degree));
	memset(lon_degree,0,sizeof(lon_degree));
    gps_callback_func=cbdata;
}
int yt_latitudeStr_info(char *buf)
{  
    strcpy(buf, lat_degree);
}
int yt_longitudeStr_info(char *buf)
{  
    strcpy(buf, lon_degree);
}

void yt_set_lat_lon_Callback(void *cbdata)
{
	gps_callback_func=cbdata;
}

static int CONTENT_Comma_Pos_ex(char *buf,int  cx)
{	 		    
	char  *p=buf;

	while(cx&&*buf)
	{		 
		if(*buf==',')cx--;
		buf++;
	}
	
	if(cx==0){
		
	  return buf-p;	 	
	  
	}else{
	 return -1;
	}
	
}


uint8_t NMEA_Comma_Pos1(uint8_t *buf,uint8_t cx)
{                 
    uint8_t *p=buf;
    while(cx)
    {         
        if(*buf=='*'||*buf<' '||*buf>'z')return 0XFF;//遇到'*'或者非法字符,则不存在第cx个逗号
        if(*buf==',')cx--;
        buf++;
    }
    return buf-p;     
}



int CONTENT_Comma_Pos(char *buf,int  cx)
{	 		    
	char  *p=buf;

	while(cx&&*buf)
	{		 
		if(*buf==',')cx--;
		buf++;
	}
	
	if(cx==0){
	  return buf-p;	 	
	}else{
	 return -1;
	}
	
}


int Comma_Pos_eee(const char *buf,int cx,int *find)
{
    const char *p=buf;

    while(cx&&*buf)
    {
        if(*buf==',')cx--;
        buf++;
    }
    if(cx==0) {

        *find=1;

    }
    return buf-p;
}
uint32_t NMEA_Pow(uint8_t m,uint8_t n)
{
    uint32_t result=1;     
    while(n--)result*=m;    
    return result;
}




int NMEA_Str2num(uint8_t *buf,uint8_t*dx)
{
    uint8_t *p=buf;
    uint32_t ires=0,fres=0;
    uint8_t ilen=0,flen=0,i;
    uint8_t mask=0;
    int res;
    while(1) //得到整数和小数的长度
    {
        if(*p=='-'){mask|=0X02;p++;}//是负数
        if(*p==','||(*p=='*'))break;//遇到结束了
        if(*p=='.'){mask|=0X01;p++;}//遇到小数点了
        else if(*p>'9'||(*p<'0'))    //有非法字符
        {    
            ilen=0;
            flen=0;
            break;
        }    
        if(mask&0X01)flen++;
        else ilen++;
        p++;
    }
    if(mask&0X02)buf++;    //去掉负号
    for(i=0;i<ilen;i++)    //得到整数部分数据
    {  
        ires+=NMEA_Pow(10,ilen-1-i)*(buf[i]-'0');
    }
    if(flen>5)flen=5;    //最多取5位小数
    *dx=flen;             //小数点位数
    for(i=0;i<flen;i++)    //得到小数部分数据
    {  
        fres+=NMEA_Pow(10,flen-1-i)*(buf[ilen+1+i]-'0');
    } 
    res=ires*NMEA_Pow(10,flen)+fres;
    if(mask&0X02)res=-res;           
    return res;
}

unsigned char gps_sn=0;

void NMEA_GPGSV_Analysis(nmea_msg *gpsx,char *buf)
{
    char  *p,*p1,dx;
	
    char len,i,j,slx=0;
	
    char posx;
	
    p=buf;
	
    p1=(char*)strstr((const char *)p,"$GPGSV");
	
    len=p1[7]-'0'; //语句个数
	
    posx=NMEA_Comma_Pos1(p1,3);

	char strbuf[8]={0};

	char posx4,posx5,posx6,posx7,posx8;
	
    if(posx!=0XFF)gpsx->svnum=NMEA_Str2num(p1+posx,&dx);//
    
    for(i=0;i<len;i++)
    {
        p1=(char *)strstr((const char *)p,"$GPGSV");
		
        for(j=0;j<4;j++)
        {     
            posx4=NMEA_Comma_Pos1(p1,4+j*4);

			if(posx4==0XFF)
				break;
			
			posx5=NMEA_Comma_Pos1(p1,5+j*4);

			if(posx5==0XFF)
				break;

			posx6=NMEA_Comma_Pos1(p1,6+j*4);

			if(posx6==0XFF)
				break;

            posx7=NMEA_Comma_Pos1(p1,7+j*4);

			if(posx7==0XFF)
				break;

            posx8=NMEA_Comma_Pos1(p1,8+j*4);

			if(posx8==0XFF)
				break;

			 memset(strbuf,0,sizeof(strbuf));
			
		     if(posx5-posx4<=1){

			    gpsx->slmsg[slx].num=0;

		     }else{
		     
                  memcpy(strbuf,p1+posx4,posx5-posx4);

				  gpsx->slmsg[slx].num=atoi(strbuf);

		     }
			      memset(strbuf,0,sizeof(strbuf));
			
			       if(posx6-posx5<=1){
			
						  gpsx->slmsg[slx].eledeg=0;
			
					 }else{
					   
							memcpy(strbuf,p1+posx5,posx6-posx5);
			
							gpsx->slmsg[slx].eledeg=atoi(strbuf);
			
					   }

					 
				  memset(strbuf,0,sizeof(strbuf));

			      if(posx7-posx6<=1){
					 
						 gpsx->slmsg[slx].azideg=0;
					 
					}else{
								
						memcpy(strbuf,p1+posx6,posx7-posx6);
					 
						gpsx->slmsg[slx].azideg=atoi(strbuf);
					 
					}
					
				   memset(strbuf,0,sizeof(strbuf));

		     	   if(posx8-posx7<=1){
							  
					   gpsx->slmsg[slx].sn=0;
							  
					}else{
										 
					   memcpy(strbuf,p1+posx7,posx8-posx7);
							  
					   gpsx->slmsg[slx].sn=atoi(strbuf);		  
					  
  gps_sn=1;
				 }
			
            slx++;     
        }   
        p=p1+1;
    }

    #if 0 
	for(i=0;i<gpsx->svnum;i++){
		
	     DIAG_FILTER(GPS_GPS,NMEA_GPGSV_Analysis,NMEA_GPGSV_Analysis_032,DIAG_INFORMATION)
		  
		 diagPrintf("num=%d eledeg=%d azideg=%d sn=%d",gpsx->slmsg[i].num,gpsx->slmsg[i].eledeg,gpsx->slmsg[i].azideg,gpsx->slmsg[i].sn);


	}
	#endif
}



//检查gps固件版本,工模使用
static int need_check_gps_vresion = 0;
static int gpsVesionSucess = 0;
static int gpsAtaVesionSucess = 0;

void checkGpsVesion_init(void)
{
	need_check_gps_vresion = 1;
	gpsVesionSucess=0;
}

int checkGPSVersion(void)
{
	return gpsVesionSucess;
}

int AtacheckGPSVersion(void)
{
	return gpsAtaVesionSucess;
}
void yt_need_check_gps_vresion_clear(void)
{
	need_check_gps_vresion = 0;
}


int NMEA_GGA_Analysis(nmea_msg *gpsx,const char *buf )
{
    //处理GGA

    uint8_t *p1,dx; 
	
    uint8_t posx;    
	
    p1=(uint8_t*)buf;
	
    posx=NMEA_Comma_Pos1(p1,6);//得到GPS状态//代表逗号所在位置的偏移
    
    if(posx!=0XFF)gpsx->gpssta=NMEA_Str2num(p1+posx,&dx);  
	
	
    posx=NMEA_Comma_Pos1(p1,7);     //卫星数    

    if(posx!=0XFF)gpsx->satellites=NMEA_Str2num(p1+posx,&dx); 


	 posx=NMEA_Comma_Pos1(p1,8); 	// HDOP    
	
	 if(posx!=0XFF)
	 	{

	      gpsx->hdop=NMEA_Str2num(p1+posx,&dx);  //水平因子 只是扩大成了整数，具体几位小数是不清楚

		  if(dx<2){
		
					 gpsx->hdop*=NMEA_Pow(10,2-dx); //扩大100倍
				}

	 	}
	
    posx=NMEA_Comma_Pos1(p1,9);    //海拔

    if(posx!=0XFF)
    {
    
		gpsx->altitude=NMEA_Str2num(p1+posx,&dx);	//只是扩大成了整数，具体几位小数是不清楚

	           if(dx<2){
		   
					   gpsx->altitude*=NMEA_Pow(10,2-dx); //扩大100倍
				   }

    }


	posx=NMEA_Comma_Pos1(p1,11);    //半径
	
	if(posx!=0XFF)
	{
	
	  gpsx->radius=NMEA_Str2num(p1+posx,&dx); 

	      if(dx<2){
		   
					  gpsx->radius*=NMEA_Pow(10,2-dx); //扩大100倍
				  }

    }


    DIAG_FILTER(GPS_GPS,NMEA_GGA_Analysis,NMEA_GGA_Analysis_01,DIAG_INFORMATION)
		
    diagPrintf("gpsx->gpssta=%d gpsx->satellites=%d gpsx->hdop=%d gpsx->altitude=%d gpsx->radius=%d",gpsx->gpssta,gpsx->satellites,gpsx->hdop,gpsx->altitude,gpsx->radius);


    return 0;

}





int  NMEA_GNRMC_Analysis( nmea_msg *gpsx,const char *buf )
{
	uint8_t		*p1, dx;
	uint8_t		posx,posx2;
	uint32_t	temp;
	float		rs;

	p1	= (uint8_t *) strstr( (const char *) buf, "$GNRMC" );
	
	posx	= NMEA_Comma_Pos1( p1, 1 );

	memset(gpsx->latitudeStr,0,sizeof(gpsx->latitudeStr));

    memset(gpsx->longitudeStr,0,sizeof(gpsx->latitudeStr));

	if ( posx != 0XFF )
	{
		temp = NMEA_Str2num( p1 + posx, &dx ) / NMEA_Pow( 10, dx );

		if(temp==0){

          goto exit;
		}
		
		gpsx->utc.hour	= temp / 10000;
		gpsx->utc.min	= (temp / 100) % 100;
		gpsx->utc.sec	= temp % 100;

	}else{
		return(-1);
	}


	posx = NMEA_Comma_Pos1( p1, 2 );
	if ( posx != 0XFF )
	{
		posx2=posx;
	}


	posx = NMEA_Comma_Pos1( p1, 3 );                       /* 得到纬度 */

	if ( posx != 0XFF )
	{
        if ((posx2 != 0XFF)&&(*(p1+posx2)=='A')){
			
			gpsx->effective = 1;

        }else{

            gpsx->effective = 0;
        }
		
		temp = NMEA_Str2num( p1 + posx, &dx );

		if(temp==0){
          goto exit;
		}

	    uint8_t latitude_start = CONTENT_Comma_Pos_ex(p1,3);

	    uint8_t latitude_end = CONTENT_Comma_Pos_ex(p1,4);

		if(latitude_start>0 && latitude_end>0){

	     	memcpy(gpsx->latitudeStr,p1+latitude_start,latitude_end-(latitude_start+1));

		    DIAG_FILTER(GPS_GPS,NMEA_GNRMC_Analysis,NMEA_GNRMC_Analysis_0102,DIAG_INFORMATION)
		
            diagPrintf("gpsx->latitudeStr=%s",gpsx->latitudeStr);

		}

		gpsx->latitude = temp / NMEA_Pow( 10, dx + 2 );

		rs = temp % NMEA_Pow( 10, dx + 2 );

		gpsx->latitude = gpsx->latitude * NMEA_Pow( 10, 6 ) + (rs * NMEA_Pow( 10, 6 - dx ) ) / 60;
		
	}else{
		return(-1);
	}


	posx = NMEA_Comma_Pos1( p1, 4 );                       /* 南纬还是北纬 */

	if ( posx != 0XFF )
	{
		gpsx->nshemi = *(p1 + posx);
		
	}else{
		return(-1);
	}

	posx = NMEA_Comma_Pos1( p1, 5 );               /* 得到经度 */

	if ( posx != 0XFF )
	{
		temp= NMEA_Str2num( p1 + posx, &dx );
		
		   if(temp==0)
		   	{
				  goto exit;
			}

	    	  uint8_t longitude_start = CONTENT_Comma_Pos_ex(p1,5);
		
			  uint8_t longitude_end = CONTENT_Comma_Pos_ex(p1,6);

			  if(longitude_start>0 && longitude_end>0 ){

			      memcpy(gpsx->longitudeStr,p1+longitude_start,longitude_end-(longitude_start+1));

			  
			        DIAG_FILTER(GPS_GPS,NMEA_GNRMC_Analysis,NMEA_GNRMC_Analysis_0103,DIAG_INFORMATION)
					 
					  diagPrintf("gpsx->longitudeStr=%s",gpsx->longitudeStr);

			  }
	
		gpsx->longitude = temp / NMEA_Pow( 10, dx + 2 );                                                /* 得到° */
		rs		= temp % NMEA_Pow( 10, dx + 2 );                                                /* 得到' */
		gpsx->longitude = gpsx->longitude * NMEA_Pow( 10, 6 ) + (rs * NMEA_Pow( 10, 6 - dx ) ) / 60;    /* 转换为° */

	}else{
		return(-1);
	}
	posx = NMEA_Comma_Pos1( p1, 6 );                                                                         /* 东经还是西经 */
	if ( posx != 0XFF )
	{
		gpsx->ewhemi = *(p1 + posx);
	}else{
		return(-1);
	}

    posx=NMEA_Comma_Pos1(p1,7);								                        /*得到地面速率*/
	if(posx!=0XFF)
	{
		gpsx->speed=NMEA_Str2num(p1+posx,&dx);
		
		if(dx<3){

			gpsx->speed*=NMEA_Pow(10,3-dx);
		}
	}

	posx=NMEA_Comma_Pos1(p1,8);								                        /*对地真北航向*/
	if(posx!=0XFF)
	{
		gpsx->cotg*=NMEA_Str2num(p1+posx,&dx);
		
		if(dx<2){

			gpsx->cotg*=NMEA_Pow(10,2-dx); //夸大100
		}
	}
	
      return 0;

    exit:
    return -1;


}

#if 0
int yt_nmea_gngsa_analysis(nmea_msg * gpsx,const char * buf )
{
	uint8_t		*p1, dx;
	uint8_t		posx ;
	uint32_t	temp, degree;
	float	pecision_factor, pecision_factor2;
	
	p1	= (uint8_t *)strstr((const char *) buf, "$GNGSA");
	posx = NMEA_Comma_Pos1( p1, 2 ); //定位状态标志
	if ( posx != 0XFF ) {
		snprintf(gpsx->gsamsg.fs, 2, "%s", p1+posx);
	} else {
		return -1;
	}

	posx = NMEA_Comma_Pos1( p1, 15 ); //位置精度因子
	if ( posx != 0XFF ) {
		temp = NMEA_Str2num( p1 + posx, &dx );
		if (0 != temp) {
			pecision_factor = temp / NMEA_Pow(10, dx);
			pecision_factor2 = temp % NMEA_Pow(10, dx);
			pecision_factor2 = pecision_factor2 / NMEA_Pow(10, dx);
			pecision_factor = pecision_factor + pecision_factor2;

			snprintf(gpsx->gsamsg.pdop, 10, "%.6f", pecision_factor);
		} else {
			return -1;
		}
	} else {
		return -1;
	}

	posx = NMEA_Comma_Pos1( p1, 16); //水平精度因子
	if ( posx != 0XFF ) {
		temp = NMEA_Str2num( p1 + posx, &dx );
		if (0 != temp) {
			pecision_factor = temp / NMEA_Pow(10, dx);
			pecision_factor2 = temp % NMEA_Pow(10, dx);
			pecision_factor2 = pecision_factor2 / NMEA_Pow(10, dx);
			pecision_factor = pecision_factor + pecision_factor2;

			snprintf(gpsx->gsamsg.hdop, 10, "%.6f", pecision_factor);
		} else {
			return -1;
		}
	} else {
		return -1;
	}

	posx = NMEA_Comma_Pos1( p1, 17); //垂直精度因子
	if ( posx != 0XFF ) {
		temp = NMEA_Str2num( p1 + posx, &dx );
		if (0 != temp) {
			pecision_factor = temp / NMEA_Pow(10, dx);
			pecision_factor2 = temp % NMEA_Pow(10, dx);
			pecision_factor2 = pecision_factor2 / NMEA_Pow(10, dx);
			pecision_factor = pecision_factor + pecision_factor2;

			snprintf(gpsx->gsamsg.vdop, 10, "%.6f", pecision_factor);
		} else {
			return -1;
		}
	} else {
		return -1;
	}
	
	return 0;
}
#endif

static unsigned char CheckSum(char *databuf, unsigned char buflen)
{
    unsigned char sum;
    unsigned char c1;
    unsigned char c2;
    int i;
    sum = 0;
    for (i = 1; i < (buflen - 3); i++)
    {
        sum = sum ^ databuf[i];
    }
    printf("sum=%x\n", sum);
    c2 = sum & 0x0F;
    c1 = (sum >> 4) & 0x0F;
    if (c1 < 10)
        c1 += '0';
    else
        c1 += 'A' - 10;
    if (c2 < 10)
        c2 += '0';
    else
        c2 += 'A' - 10;
    if (c1 == databuf[buflen - 2] && c2 == databuf[buflen - 1]) 
    {
        return 1;
    }
    return 0;
}

char gpgsv_buf[480]={0};

static unsigned int nextmillis;

static int is_first=0;

static int location_first=0;

static yt_test_gps_info_t yt_test_gps_info;

int GetYtGpsData(char *local_numSV,char *numSv,char *longitude,char *latitude)
{
	strcpy(local_numSV,yt_test_gps_info.local_numSV);
	
	strcpy(numSv,yt_test_gps_info.numSv);
	
	degree_minute_to_degree(yt_test_gps_info.longitude,longitude,LONGITUDE_DATA);
	
	degree_minute_to_degree(yt_test_gps_info.latitude,latitude,LATITUDE_DATA);		
}
static void yt_gps_info_send_to_ui(nmea_msg *data)
 {
	 int i;
	 strcpy(yt_test_gps_info.fs, "");
	 
	 char svnumStr[3] = {0};
	 sprintf(svnumStr,"%d",data->svnum);
	 memset(yt_test_gps_info.numSv,0,sizeof(yt_test_gps_info.numSv));
	 strcpy(yt_test_gps_info.numSv, svnumStr);

	 memset(yt_test_gps_info.longitude,0,sizeof(yt_test_gps_info.longitude));
	 strcpy(yt_test_gps_info.longitude, data->longitudeStr);
	 memset(yt_test_gps_info.latitude,0,sizeof(yt_test_gps_info.latitude));
	 strcpy(yt_test_gps_info.latitude, data->latitudeStr);

	 char sateStr[3] = {0};
	 sprintf(sateStr,"%d",data->satellites);
	 memset(yt_test_gps_info.local_numSV,0,sizeof(yt_test_gps_info.local_numSV));
	 strcpy(yt_test_gps_info.local_numSV, sateStr);
	 
     char hdopStr[12] = {0};
	 sprintf(hdopStr,"%d.%d",data->hdop/100 ,data->hdop%100);
	 memset(yt_test_gps_info.hdop,0,sizeof(yt_test_gps_info.hdop));
	 strcpy(yt_test_gps_info.hdop, hdopStr);
	 memset(yt_test_gps_info.pdop,0,sizeof(yt_test_gps_info.pdop));
	 strcpy(yt_test_gps_info.pdop, "");
	 memset(yt_test_gps_info.vdop,0,sizeof(yt_test_gps_info.vdop));
	 strcpy(yt_test_gps_info.vdop, "");

	 char speedStr[12] = {0};
	 memset(yt_test_gps_info.spd,0,sizeof(yt_test_gps_info.spd));
	 sprintf(speedStr,"%d.%d",data->speed/1000 ,data->speed%1000);
	 strcpy(yt_test_gps_info.spd, speedStr);
 
	 for (i=0; i<MAX_GPS_SVID; i++) {
	 	 char eledegStr[3] = {0};
	     sprintf(eledegStr,"%d",data->slmsg[i].eledeg);
	     strcpy(yt_test_gps_info.numSv, svnumStr);
		 strcpy(yt_test_gps_info.local_numSV_info[i], eledegStr);
		 strcat(yt_test_gps_info.local_numSV_info[i],";");
		 char azidegStr[3] = {0};
	     sprintf(azidegStr,"%d",data->slmsg[i].azideg);
		 strcat(yt_test_gps_info.local_numSV_info[i], azidegStr);
		 strcat(yt_test_gps_info.local_numSV_info[i],";");
		 char snStr[3] = {0};
	     sprintf(snStr,"%d",data->slmsg[i].sn);
		 strcat(yt_test_gps_info.local_numSV_info[i], snStr);
	 }
	 
	// watch_modem_gps_test_info_req((void *)&yt_test_gps_info);
	 
	 for (i=0; i<MAX_GPS_SVID; i++) {
		 DIAG_FILTER(GPS_GPS,yt_gps_info_send_to_ui,yt_gps_info_send_to_ui_001,DIAG_INFORMATION)
		  
		 diagPrintf("fs=%s speed=%s pdop=%s hdop=%s  vdop=%s longitude=%s latitude=%s numSv=%s",yt_test_gps_info.fs,yt_test_gps_info.spd,yt_test_gps_info.pdop,
		 yt_test_gps_info.hdop,yt_test_gps_info.vdop,yt_test_gps_info.longitude,yt_test_gps_info.latitude,yt_test_gps_info.local_numSV_info[i]);

	 }
 }


char  gga_check = 0; //保证gga校验成功

unsigned int last_send_msg=0; 

static int is_factory_mode = 0;
void set_factory_mode(int value)
{

     is_factory_mode=value;
}

struct cmdMsg
{
    UINT32 msgID;
    void *pArgs;
    UINT32 len;
};

struct dataMsg
{
	UINT32 len; //状态
	
	void *pArgs;
};

static OSTaskRef CMDWorkerRef;

#define CMD_WORKER_STACK_SIZE 4096

static OSMsgQRef CMDMsgQ = NULL;

typedef enum
{
    LOGCMD_MSG_SEND_OK
    
} LOGCMD_MSG_SEND_ENUM;

static int gps_can_send_ok = 0;

void GPSCMD_send_msg_to_quene0(UINT32 msg_id,char *line)
{  

    struct cmdMsg pMsgArgs = {0};
	
    struct dataMsg *req = NULL;

    OSA_STATUS osaStatus;
	
    req = (struct dataMsg *)malloc(sizeof(struct dataMsg));
	
    ASSERT(req != NULL);
	
    char *p = malloc(strlen(line) + 1);

    memset(p, 0, strlen(line) + 1);
	
    memcpy(p, line, strlen(line));

    req->len = strlen(line) + 1;
	
    req->pArgs = p;

    pMsgArgs.msgID = msg_id;
	
    pMsgArgs.pArgs = (void *)req;
	
    osaStatus = OSAMsgQSend(CMDMsgQ, sizeof(struct cmdMsg), (UINT8 *)&pMsgArgs, OSA_NO_SUSPEND);
	
    ASSERT(osaStatus == OS_SUCCESS);
	
}


void gpscmd_send_ok_msg(char *content)
{
   //  GPSCMD_send_msg_to_quene0(LOGCMD_MSG_SEND_OK,content);
}

void save_open_gps_log()
{
   char str[]="open gps\r\n";
   
   gpscmd_send_ok_msg(str);

}


void save_close_gps_log()
{

	 char str[]="close gps\r\n";
	 
	 gpscmd_send_ok_msg(str);


}

extern void yt_gps_log_save(char*start)__attribute__((weak));

static void log_Main(void *argv)
{

	    struct cmdMsg apiMsg = {0};

		struct dataMsg *req;
		
		char *data;

		
		while (1)
		{
			memset(&apiMsg, 0, sizeof(struct cmdMsg));
			
			OSAMsgQRecv(CMDMsgQ, (UINT8 *)&apiMsg, sizeof(struct cmdMsg), OSA_SUSPEND);
	
			switch (apiMsg.msgID)
			{
               case LOGCMD_MSG_SEND_OK:
			   	;
			   	
			   req = (struct dataMsg *)apiMsg.pArgs;
			   
			   data = (char *)req->pArgs;

			   yt_gps_log_save(data);

			    if (data)
                {
                    free(data);
                    data = NULL;
                }

                free(req);
                req = NULL;

			   break;
			
			}
		}

}


void logcmd_init(void) /* 入口 */
{
    OS_STATUS status;
    void *CMDWorkerTaskStack = NULL;
    status = OSAMsgQCreate(&CMDMsgQ,
                           "gpslogcmd_evt",
                           sizeof(struct cmdMsg),
                           128,
                           OS_FIFO);

    CMDWorkerTaskStack = malloc(CMD_WORKER_STACK_SIZE);

    if (CMDWorkerTaskStack == NULL)
    {
        return;
    }

    if (OSATaskCreate(&CMDWorkerRef,
                      CMDWorkerTaskStack,
                      CMD_WORKER_STACK_SIZE,
                      108,
                      (char *)"LOGWorkerTask",
                      log_Main,
                      0) != 0)
    {
        return;
    }
}

static int log_control=1;


void set_conlog()
{

	log_control=1;

}

unsigned char gps_epo = 0;

unsigned char glonass_epo = 0;

unsigned char galileo_epo = 0;

unsigned char beidou_epo = 0;





void reset_gps_sn()
{
   gps_sn = 0;
}


int get_gps_sn()
{
     return gps_sn;
}

void reset_epo_status()
{

	gps_epo = 0;
	
	glonass_epo = 0;

	galileo_epo = 0;

    beidou_epo = 0;
}

static int uart_flowctrl=0;


int get_uart_flowctrl()
{

   return uart_flowctrl;

}

bool  get_inject_epo_status()
{


     if((gps_epo==1)&&(glonass_epo == 1)&&(galileo_epo==1)&&(beidou_epo==1))
     {

         return true;

	 }

     return false;
}

static void processLine(const char * line) 
{
	char new_line[256]={0};

	DIAG_FILTER(GPS_GPS,processLine,processLine_01,DIAG_INFORMATION)
	diagPrintf("line=%s",line);	
	
	if ( (strStartsWith(line+3, "GGA"))||(strStartsWith(line, "$NGGA"))) {

	   DIAG_FILTER(GPS_GPS,processLine,processLine_3208,DIAG_INFORMATION)
		diagPrintf("line=%s ",line);
		
		gga_check  = 1;
		
		memset(&nmea_msg_info_cur, 0, sizeof(nmea_msg));
	
		NMEA_GGA_Analysis(&nmea_msg_info_cur, line);
	} else if (strStartsWith(line, "$GPGSV"))  {
		if(gga_check==1) {
			
			//校验通过
			int len=line[7]-'0';
			//语句个数
			if(line[9]=='1') {
			
				memset(gpgsv_buf,0,sizeof(gpgsv_buf));
				if(strlen(line)<(sizeof(gpgsv_buf)-1)) {
					memcpy(gpgsv_buf,line,strlen(line));
				}
			} else {
				if(strlen(line)<(sizeof(gpgsv_buf)-1-strlen(gpgsv_buf))) {
					memcpy(gpgsv_buf+strlen(gpgsv_buf),line,strlen(line));
		
				}
			}
			if(line[9]==line[7]) {
				NMEA_GPGSV_Analysis(&nmea_msg_info_cur, gpgsv_buf);
			}
		}
	} else  if (strStartsWith(line+3, "RMC")) {
		if(gga_check==1) {
			int re = NMEA_GNRMC_Analysis(&nmea_msg_info_cur, line);

		      if(re>-1)
		      	{
                  if(location_first==0)
                  	{

					  location_first=1;
					  
					  int  latitude = 0;
					
					  int  longitude_0 =0;
						  
					   if(nmea_msg_info_cur.nshemi=='S') // 南纬
					    {
					
						   latitude = nmea_msg_info_cur.latitude*(-1);
					
						}else{
					
						   latitude= nmea_msg_info_cur.latitude;
					
						}
					
					
					   if(nmea_msg_info_cur.ewhemi=='W') //西经
					   {
					
						   longitude_0 = nmea_msg_info_cur.longitude*(-1);
						
					   }else{
					
						   longitude_0 = nmea_msg_info_cur.longitude;
						   
					
					   }

					   //set_last_location(latitude,longitude_0);

                  	}

		      	}
			  
			//成功可以回调
		    if((re>-1)&&(need_callback==1))
			{
		   	    if(gps_callback_func)
			   	{   	
					degree_minute_to_degree(nmea_msg_info_cur.latitudeStr,lat_degree,LATITUDE_DATA);		
					degree_minute_to_degree(nmea_msg_info_cur.longitudeStr,lon_degree,LONGITUDE_DATA);
					ws_printf("!!!!!!!!!!!!! degree_minute_to_degree=%s,%s",lat_degree,lon_degree);
					gps_callback_func(&nmea_msg_info_cur);
		   	   	}
           	}

			if(OSAGetTicks()-last_send_msg > 200) {
				
			
				if(is_factory_mode==1)
				{
				
				  yt_gps_info_send_to_ui(&nmea_msg_info_cur);

				}
				
				
				last_send_msg = OSAGetTicks();
			}
		 } else {
		
		}
		gga_check=0;
	}
}


static int readGpsData(struct gps_struct * chan)
{

    int count;
    char * p_read = NULL;
	
    if(*chan->GPSBufferCur == '\0')
	{  
        chan->GPSBufferCur = chan->GPSBuffer;
        *chan->GPSBufferCur = '\0';
        p_read = chan->GPSBuffer;

    }
	else
	{
        int CurIdx = chan->GPSBufferCur - chan->GPSBuffer;
        int len = strlen(chan->GPSBufferCur);

		if(CurIdx > 0)
		{
            memmove(chan->GPSBuffer, chan->GPSBufferCur, len + 1);
            chan->GPSBufferCur = chan->GPSBuffer;
        }
		
        p_read = chan->GPSBufferCur + len;

    }

    if(0 == MAX_GPS_DATA - (p_read - chan->GPSBuffer))
	{
        chan->GPSBufferCur = chan->GPSBuffer;
        *chan->GPSBufferCur = '\0';
        p_read = chan->GPSBuffer;

    }

    do
	{  
		count = readGpsline(p_read, MAX_GPS_DATA - (p_read - chan->GPSBuffer)); 

    } while(count < 0);

    if(count > 0)
	{
        p_read[count] = '\0';
    }
	else
	{
        if(count == 0)
		{
            ;
        }
		else
		{
            ;
        }
    }
	
    return count;
}
   

static void  waitgpsnewline()
{
	OSAFlagWait(gps_data_read_flag_ref, GPS_DATA_EVENT_PROCESS, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND);
}

int GpsReader(struct gps_struct * chan)
{	
	char * line;

	int quelen = GPS_QueueLength(&GPS_Queue);
	
	/*

           DIAG_FILTER(GPS_GPS,GpsReader,GpsReader_01,DIAG_INFORMATION)
		
		   diagPrintf("quelen=%d",quelen);*/

	if(quelen<1)
	{
		waitgpsnewline();
	}

	if(readGpsData(chan) <= 0)
		return -1;

	while(1)
	{
		line = getGpsLine(chan);

		if(line == NULL)
		{
			return 0;
		}
		else
		{
			
		  processLine(line);

		}
	}
}

static void GPSReaderTaskEntry(void * parameter)
{
 	struct    gps_struct * chan =  gps_struct_init();

	for(;;)
	{
		GpsReader(chan);
	}
}

static void * gps_ThreadStart(const char *name, void (*thread)(void*),
                                void *arg, int stacksize, int prio)
{
    OSTaskRef TaskRef = NULL;
    void* ptr_task_stack = NULL;

    ptr_task_stack = (void*)malloc(stacksize);
    ASSERT(ptr_task_stack != NULL);
    ASSERT(OS_SUCCESS == OSATaskCreate( &TaskRef,
                ptr_task_stack,
                stacksize,
                prio,
                (char *)name,
                thread,
                arg));
    return TaskRef;
}

void GpsReader_Init( void )  
{
	DIAG_FILTER(MQTT,GpsReader_Init,GpsReader_Init_01,DIAG_INFORMATION)
  	  diagPrintf("%s",__FUNCTION__);

	uart2_debug();

	extern void init_gps_lock();
	init_gps_lock();
	
	if(!gps_data_read_flag_ref)
		  OSAFlagCreate(&gps_data_read_flag_ref);   
		  
    GPS_InitQueue(&GPS_Queue);
	
	memset(&GPS_Queue,0,sizeof(sqQueue));

	OSATimerCreate(&start_handle_data_timer_ref);

	Os_Create_HISR(&uart_detect_hisr_ref, "gps_uart", handle_uart_hisr, 2);
		  
	gps_ThreadStart("GPSEcho0", &GPSReaderTaskEntry, NULL, GPS_DEFAULT_THREAD_STACKSIZE, GPS_DEFAULT_THREAD_PRIO);

	gps_uart_recv_set(GPS_UART_recvData);


	

	
}







void degree_minute_to_degree(char * data, char * out, DEGREE_OF_TYPE itude_type)
{
	char degree_result_char[11]={0};
	char degree_temp_char[4]={0};
	char minute_temp_char[9]={0};

	double degree_result_float = 0.0;
	double degree_temp_flaot = 0.0;
	double minute_temp_float = 0.0;

	int num = 0;

	if (LATITUDE_DATA==itude_type)
	{
		num = 2;
	}
	else if (LONGITUDE_DATA==itude_type)
	{
		num = 3;
	}
	else
	{
		;
	}
	
	strncpy(degree_temp_char, data, num);
	strcpy(minute_temp_char, data+num);

	degree_temp_flaot = atof(degree_temp_char);
	minute_temp_float = atof(minute_temp_char)/60;
	degree_result_float = degree_temp_flaot + minute_temp_float;

	sprintf(degree_result_char, "%.6f", degree_result_float);
	strcpy(out, degree_result_char);
}












