/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_HorizontalAccuracy_H_
#define	_HorizontalAccuracy_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* HorizontalAccuracy */
typedef struct HorizontalAccuracy {
	long	 accuracy;
	long	 confidence;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} HorizontalAccuracy_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_HorizontalAccuracy;

#ifdef __cplusplus
}
#endif

#endif	/* _HorizontalAccuracy_H_ */
#include <asn_internal.h>
