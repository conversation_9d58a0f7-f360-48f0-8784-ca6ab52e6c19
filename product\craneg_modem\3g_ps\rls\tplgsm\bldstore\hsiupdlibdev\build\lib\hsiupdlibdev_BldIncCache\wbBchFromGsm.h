/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/* ======================================================================== */
/* File        : wbBchFromGsm.h                                             */
/*                                                                          */
/* Notes       :                                                            */
/*                                                                          */
/* Copyright (c) 2005 Intel CCD. All Rights Reserved                        */
/* ======================================================================== */

#ifndef WB_BCH_FROM_GSM_H
#define WB_BCH_FROM_GSM_H

#ifndef PLG_BCH_DECODE_DATA_LEN
#define PLG_BCH_DECODE_DATA_LEN 32
#endif

#define INVALIDATED_UARFCN 			0x0000
#define INVALIDATED_SCRAMBLING_CODE 0xFFFF


typedef struct
{
	UINT16 	sfn;							/* SFN the block was received in (the last frame in the TTI)    */
	UINT16 	uarfcn;                         /* (0..16383)	UARFCN                                          */
	UINT16 	scramblingCode;                 /* The Range is from 0 to 8176 with step of 16                  */
	#if defined (ENABLE_NETWORK_SCAN_REPORT)
	// add by wpan for add EcNo and Rscp in bchRpt
	INT16  cpichEcNo;						/* add by wpan to rpt ecno of detect wb cell */
	INT16  cpichRscp;						/* add by wpan to rpt rscp of detect wb cell */
	#endif
	UINT8 	frame[PLG_BCH_DECODE_DATA_LEN]; /* BCH Block, PLG_BCH_DECODE_DATA_LEN = 31                      */
	Bool	crcCheckResult;					/* TRUE means Good CRC, FALSE means Bad CRC						*/
} utranBchDecodeInd_t;

typedef struct
{
    UINT16          uarfcn;
    UINT16          scramblingCode;
    UINT8           sttdInd;            /* STTD_OFF = 0x00 */
} wbBchInfo_ts;


//
BOOL plAMCheckOffLineMeas(void);
void  gwiStartBch(UINT8 dstSim);
void  gwiAbortBch(UINT8 dstSim);

#endif
