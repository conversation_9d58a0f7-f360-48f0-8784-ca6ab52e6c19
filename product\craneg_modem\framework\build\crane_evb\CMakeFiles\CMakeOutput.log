The system is: Windows - 10.0.22631 - AMD64
Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_098e5 
[1/2] Building C object CMakeFiles\cmTC_098e5.dir\testCCompiler.o

[2/2] Linking C executable cmTC_098e5.exe



Detecting C compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_5ee20 
[1/2] Building C object CMakeFiles\cmTC_5ee20.dir\CMakeCCompilerABI.o

[2/2] Linking C executable cmTC_5ee20.exe



Determining if the CXX compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_94823 
[1/2] Building CXX object CMakeFiles\cmTC_94823.dir\testCXXCompiler.o

[2/2] Linking CXX executable cmTC_94823.exe



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_95cda 
[1/2] Building CXX object CMakeFiles\cmTC_95cda.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX executable cmTC_95cda.exe



The system is: Windows - 10.0.22631 - AMD64
Checking whether the ASM compiler is ARMCC using "" matched "(ARM Compiler)|(ARM Assembler)":
Product: DS-5 Professional 5.26.2
Component: ARM Compiler 5.06 update 4 (build 422)
Tool: armasm [4d35cf]
For Educational purposes only
Software supplied by: ARM Limited

Usage:      armasm [options] sourcefile

Options:
--list       listingfile   Write a listing file (see manual for options)
 -o          outputfile    Name the final output file
--depend     dependfile    Save 'make' source file dependencies
--errors     errorsfile    Put stderr diagnostics to errorsfile
 -I          dir[,dir]     Add dirs to source file search path
--pd
--predefine  directive     Pre-execute a SET{L,A,S} directive
--maxcache   <n>           Maximum cache size    (default 8MB)
--no_esc                   Ignore C-style (\c) escape sequences
--no_warn                  Turn off Warning messages
 -g                        Output debugging tables
--apcs       /<quals>      Make pre-definitions to match the
                           chosen procedure-call standard
--checkreglist             Warn about out of order LDM/STM register lists
--help                     Print this information
--li                       Little-endian ARM
--bi                       Big-endian ARM
 -M                        Write source file dependency lists to stdout
--MD                       Write source file dependency lists to inputfile.d
--keep                     Keep local labels in symbol table of object file
--regnames none            Do not predefine register names
--split_ldm                Fault long LDM/STM
--unsafe                   Downgrade certain errors to warnings
--via        <file>        Read further arguments from <file>
--cpu        <target-cpu>  Set the target ARM core type
--cpu list                 Output a list of all the selectable CPUs
--fpu        <target-arch> Set target FP architecture version
--fpu list                 Output a list of all selectable FP architectures
--thumb                    Assemble Thumb instructions
--arm                      Assemble ARM instructions
Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "CMakeCCompilerId.o"

Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "__image.axf"

The C compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdC/CMakeCCompilerId.o"

Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
Compiler: C:/Program Files/DS-5 v5.26.2/sw/ARMCompiler5.06u4/bin/armcc.exe 
Build flags: 
Id flags:  

The output was:
0


Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CMakeCXXCompilerId.o"

Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "__image.axf"

The CXX compiler identification is ARMCC, found in "X:/framework/build/crane_evb/CMakeFiles/3.14.5/CompilerIdCXX/CMakeCXXCompilerId.o"

Determining if the C compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_27a60 
[1/2] Building C object CMakeFiles\cmTC_27a60.dir\testCCompiler.o

[2/2] Linking C executable cmTC_27a60.exe



Detecting C compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_e9b11 
[1/2] Building C object CMakeFiles\cmTC_e9b11.dir\CMakeCCompilerABI.o

[2/2] Linking C executable cmTC_e9b11.exe



Determining if the CXX compiler works passed with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_189b2 
[1/2] Building CXX object CMakeFiles\cmTC_189b2.dir\testCXXCompiler.o

[2/2] Linking CXX executable cmTC_189b2.exe



Detecting CXX compiler ABI info compiled with the following output:
Change Dir: X:/framework/build/crane_evb/CMakeFiles/CMakeTmp

Run Build Command(s):D:/Project_area/SVN/6_kxs50_kxs57/prebuilts/misc/windows-x86/ninja.exe cmTC_dc4df 
[1/2] Building CXX object CMakeFiles\cmTC_dc4df.dir\CMakeCXXCompilerABI.o

[2/2] Linking CXX executable cmTC_dc4df.exe



