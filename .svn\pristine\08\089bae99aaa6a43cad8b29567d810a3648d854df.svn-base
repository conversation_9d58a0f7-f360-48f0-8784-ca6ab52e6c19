#ifndef WZ_LIBRARY_CODE_H
#define WZ_LIBRARY_CODE_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
*      INCLUDES
*********************/
#include <stdio.h>

#ifdef LV_CONF_INCLUDE_SIMPLE
    #include "lvgl.h"
    #include "lv_watch_conf.h"
#else
    #include "../../../lvgl/lvgl.h"
    #include "../../../lv_watch_conf.h"
#endif

#include "lv_watch.h"
#include "ws_cJSON.h"
#if USE_LV_WATCH_QT_LIBRARY_CODE != 0
#define TICKES_IN_SECOND  (1000/5)
lv_res_t  wz_library_code_create_btn_action(lv_obj_t * btn, lv_event_t event);



#endif

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

