/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_GNSS_ReferenceTimeForOneCell_H_
#define	_GNSS_ReferenceTimeForOneCell_H_


#include <asn_application.h>

/* Including external dependencies */
#include "NetworkTime.h"
#include <NativeInteger.h>
#include <NativeEnumerated.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum GNSS_ReferenceTimeForOneCell__bsAlign {
	GNSS_ReferenceTimeForOneCell__bsAlign_true	= 0
} e_GNSS_ReferenceTimeForOneCell__bsAlign;

/* GNSS-ReferenceTimeForOneCell */
typedef struct GNSS_ReferenceTimeForOneCell {
	NetworkTime_t	 networkTime;
	long	 referenceTimeUnc;
	long	*bsAlign	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GNSS_ReferenceTimeForOneCell_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_bsAlign_4;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_GNSS_ReferenceTimeForOneCell;

#ifdef __cplusplus
}
#endif

#endif	/* _GNSS_ReferenceTimeForOneCell_H_ */
#include <asn_internal.h>
