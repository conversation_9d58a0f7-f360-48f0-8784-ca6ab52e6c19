/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utmlgsig.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description: This file defines the types relating to the signals
 *                       used by theMiniLogger.
 **************************************************************************/

#if !defined (SYSTEM_H)
#include <system.h>
#endif

#ifndef UTMLGSIG_H
#define UTMLGSIG_H

#if defined (UPGRADE_SYS_TOOLS)
# if defined (UT_ML_USE_MINILOGGER)
#  if defined (ROM_TEST_TASK)

/***************************************************************************
* Nested Include Files
***************************************************************************/

#if !defined (KITQID_H)
#include <kitqid.h>
#endif

/***************************************************************************
*   Manifest Constants
***************************************************************************/

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 *   Types
 ***************************************************************************/
/* The following data type is used for any piece of hardware, as its size is
   deemed large enough for the current functional purposed of the MiniLogger,
   i.e. capturing the start-up sequence of events.                           */

typedef volatile Int32 MlRealTimeClockTicks;

/* The following enumeration describes the event types supported by the
   MiniLogger. These events are used in the signal MiniLoggerEvent.          */

typedef enum MiniLoggerEventTypeTag
{
    ML_RTOS_TASK_STARTED,
                               /* It is meant to coincide with the start of
                                  the RTOS task data initialisation.         */

    ML_RTOS_TASK_DATA_INIT_FINISHED,
                               /* End of RTOS task data initialisation; it is
                                  meant to take place just before entering the
                                  RTOS task main loop.                       */

    ML_RTOS_TASK_MESSAGE_RECEIVED,
                               /* In an RTOS task actioned by receipt of a
                                  message. It indicates that a message has
                                  just been received.                      */

    ML_RTOS_TASK_EXIT,         /* Event signalling the end of an RTOS task. */

    ML_RTOS_ERROR,             /* Used for error signalling */

    /* The following are not events and are not part of the API. */

    ML_ALL_LOGGED_EVENTS_RETRIEVED, /* All events logged in the MiniLogger
                                       Buffer have been retrieved.          */

    ML_LOGGING_BUFFER_IS_EMPTY, /* The MiniLogger Buffer is empty. */

    ML_LOGGING_BUFFER_IS_FULL   /* The MiniLogger Buffer is full. */
}
MiniLoggerEventType;

/* RTOS task specific information. */

typedef struct MlInfoFromTaskTag
{
    TaskId              taskId;
    MiniLoggerEventType event;
    SignalId            signalId;
}
MlInfoFromTask;

/* MiniLogger specific information. */

typedef struct MiniLoggerEventTag
{
    struct MiniLoggerEventTag    *eventDataAddress;
    MlRealTimeClockTicks          realTimeClockTicks;
    MlInfoFromTask                event;
}
MiniLoggerEvent;

/***************************************************************************
*   Macro Functions
***************************************************************************/

/***************************************************************************
 *   Types
 ***************************************************************************/
/***************************************************************************
*   Global Variables
***************************************************************************/

#  endif  /* ROM_TEST_TASK */
# endif   /* UT_ML_USE_MINILOGGER */
#endif    /* UPGRADE_SYS_TOOLS */

#endif /* UTMLGSIG_H */
