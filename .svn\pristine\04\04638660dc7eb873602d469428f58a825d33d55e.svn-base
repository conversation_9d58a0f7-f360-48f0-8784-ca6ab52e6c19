/******************************************************************************
 * * call_global.h - data structure for call module
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#ifndef CALL_GLOBAL_H
#define CALL_GLOBAL_H

#ifdef __cplusplus
extern "C" {
#endif

/**********************
*      INCLUDES
**********************/
#include <stdlib.h>
#include <stdio.h>
#include "lvgl.h"
#include "ui_type.h"
#include "ui_textid.h"
#include "../../lvgl/hal/hal.h"
#include "ui_log.h"
#include "inter_common_ui_interface.h"
#include "nav.h"
#include "modem/mmi_modemadp_interface.h"
#include "ui_nvm_interface.h"
#include "inter_contacts_interface.h"
#include "inter_setting_interface.h"
#include "inter_call_setting_interface.h"
#include "inter_call_framework_interface.h"
#include "inter_framework_interface.h"
#include "inter_call_calllog.h"
#include "inter_audio_player_interface.h"
#include "utility.h"
#include "ui_ime.h"
#include "../../lvgl/hal/hal_bt.h"
#if USE_LV_WATCH_MODEM_ADAPTOR != 0
#include "modem/mmi_modem_adaptor_call.h"
#endif

#define CALL_COUNT_MAX                  2

/**********************
*      TYPEDEFS
**********************/
#define UI_CALL_SPK_VOL_LEVEL_MUTE      HAL_AUDIO_SPK_LEVEL_0
#define UI_CALL_SPK_VOL_LEVEL_1         HAL_AUDIO_SPK_LEVEL_1
#define UI_CALL_SPK_VOL_LEVEL_2         HAL_AUDIO_SPK_LEVEL_2
#define UI_CALL_SPK_VOL_LEVEL_3         HAL_AUDIO_SPK_LEVEL_3
#define UI_CALL_SPK_VOL_LEVEL_4         HAL_AUDIO_SPK_LEVEL_4
#define UI_CALL_SPK_VOL_LEVEL_5         HAL_AUDIO_SPK_LEVEL_5
#define UI_CALL_SPK_VOL_LEVEL_6         HAL_AUDIO_SPK_LEVEL_6
#define UI_CALL_SPK_VOL_LEVEL_7         HAL_AUDIO_SPK_LEVEL_7
#define UI_CALL_SPK_VOL_LEVEL_8         HAL_AUDIO_SPK_LEVEL_8
#define UI_CALL_SPK_VOL_LEVEL_9         HAL_AUDIO_SPK_LEVEL_9
#define UI_CALL_SPK_VOL_LEVEL_10        HAL_AUDIO_SPK_LEVEL_10
#define UI_CALL_SPK_VOL_LEVEL_MAX       UI_CALL_SPK_VOL_LEVEL_10
#define UI_CALL_SPK_VOL_LEVEL_NUM       10
#define UI_CALL_SPK_VOL_UP              0
#define UI_CALL_SPK_VOL_DOWN            1

#define CALL_NV_HANDSET_OFFSET          0
#define CALL_NV_SPEAKER_OFFSET          1
#define CALL_NV_EMERGENCY_NUM_OFFSET    2

#define CALL_NV_EMERGENCY_NUM_SIZE      sizeof(NV_UI_Call_ENumber_t)

#define UI_CALL_DTMF_DURATION           300

#define UI_CALL_LINE_NONE               0
#define UI_CALL_LINE_1                  1
#define UI_CALL_LINE_2                  2
#define UI_CALL_LINE_ALL                3
#define UI_CALL_LINE_MAX                2

#define UI_CALL_INVALID_PARAM_INDEX     0xFF

typedef enum {
    UI_CALL_PROCESS_NULL = 0,
    UI_CALL_PROCESS_HOLD,
    UI_CALL_PROCESS_UNHOLD,
    UI_CALL_PROCESS_SWAP,
    UI_CALL_PROCESS_MT_REJECT,
    UI_CALL_PROCESS_MT_AUTO_REJECT
} UI_CALL_PROCESS;

typedef struct {
    MMI_MODEM_SIM_ID     SimId;
    MMI_MODEM_CALL_STATE State;
    INT32                Index;                    /* Connection Index for use with, eg, AT+CHLD */
    BOOL                 IsMpty;                   /* nonzero if is mpty call */
    BOOL                 IsMT;                     /* nonzero if call is mobile terminated */
    BOOL                 IsECall;                  // TRUE means emergency call
    INT8                 *Number;                  /* Remote party number */
    INT8                 *Name;                    /* Remote party name */
    UINT32               Duration;                 // call duration for call log
} UI_Call_Params_t;

typedef struct {
    UINT8            CallsBitMap;               // refer to MACRO UI_CALL_LINE_X
    UI_Call_Params_t *Calls[CALL_COUNT_MAX];    // call info, NULL means no present
    UINT8            ActiveIndex;               // active index of Calls[] array
    BOOL             IsConnect;                 // true means connect present
    VOID             *CurrentDesc;              // pDesc of current interface
    ACTIVITY_ID      FromAct;                   // save the top ACT UI before call
    ACTIVITY_ID      StatusAct;                 // the status ACT ID
#define UI_CALL_STATUS_TIMER_LEN    2000        // call status display timer length
    TIMER_ID         DisplayTimer;              // timer for distory UI automatically
    UINT8            CallSpkStatus;             // status of speaker On/Off
    BOOL             IsMute;                    // true means mute
    UI_CALL_PROCESS  CallProcess;               // Process on going
#define UI_CALL_DURATION_TIMER_LEN    1000      // 1 seconds
    TIMER_ID         CallDurationTimer;         // timer for distory UI automatically
    INT32            CallDuration;              // call duration for display
} UI_Call_Mng_t;

typedef enum {
    CALL_NO_SIM_PRESENT = 0,
    CALL_SIM1_PRESENT,
    CALL_SIM2_PRESENT,
    CALL_BOTH_SIM_PRESENT
} CALL_SIM_STATUS;                               // bitmap

/**********************
* GLOBAL VARIABLES
**********************/
extern UI_Call_Mng_t g_MMI_Call_Mng;

/**********************
* GLOBAL PROTOTYPES
**********************/
extern UINT8 Call_Key_Proc_Calling(lv_obj_t *obj, UI_KEY_STATUS key_sta, UI_KEY_VALUE key_val);
extern UINT8 Call_Key_Proc_Main(lv_obj_t *obj, UI_KEY_STATUS key_sta, UI_KEY_VALUE key_val);
extern UINT8 Call_Key_Proc_Loudsp_Inquire(lv_obj_t *Obj, UI_KEY_STATUS Key_Sta, UI_KEY_VALUE Key_Val);
extern UINT8 Call_Key_Proc_Incoming(lv_obj_t *obj, UI_KEY_STATUS key_sta, UI_KEY_VALUE key_val);
extern UINT8 Call_Key_Proc_Opt(lv_obj_t *obj, UI_KEY_STATUS key_sta, UI_KEY_VALUE key_val);
extern UINT8 Call_Key_Proc_Emergency_Calling(lv_obj_t *Obj, UI_KEY_STATUS Key_Sta, UI_KEY_VALUE Key_Val);
extern UINT8 Call_Key_Proc_Select_Sim(lv_obj_t *Obj, UI_KEY_STATUS Key_Sta, UI_KEY_VALUE Key_Val);
extern UINT8 Call_Key_Proc_Dtmf_Input(lv_obj_t *Obj, UI_KEY_STATUS Key_Sta, UI_KEY_VALUE Key_Val);
extern VOID Call_Rel_Func_Sim_Sel_Sim1(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Sim_Sel_Sim2(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Hold(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_End_Act(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Swap(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Conference(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_End_All(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Contacts(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Menu(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Mute(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Loudsp(lv_obj_t *Obj, lv_event_t e);
extern VOID Call_Rel_Func_Call_Opt_Reject(lv_obj_t *Obj, lv_event_t e);

extern VOID Display_Call_Loudsp_Inquire(VOID);
extern VOID Display_Call_End(VOID);
extern VOID Display_Call_Opt(VOID);
extern VOID Display_Call_Main(VOID);
extern VOID Display_Call_Loudsp_On(VOID);
extern VOID Display_Call_Loudsp_Off(VOID);
extern VOID Display_Call_Emergency_Calling(VOID);
extern VOID Display_Call_Emergency_Only_Info(VOID);
extern VOID Display_Dial_Call_Select_Sim(VOID);
extern VOID Display_Call_Calling(VOID);
extern VOID Display_Call_Incoming(VOID);
extern VOID Display_Call_Mute(VOID);
extern VOID Display_Call_Unmute(VOID);
extern VOID Display_Call_Hold(VOID);
extern VOID Display_Call_Unhold(VOID);
extern VOID Display_Call_Swap(VOID);
extern VOID Display_Call_Dtmf_Input(INT8 FirstNum);
extern VOID Call_Display_Timeout(VOID* Para);

extern VOID Call_Stop_Ring(VOID);
extern VOID Call_Start_Call(VOID);
extern VOID Call_Clear_Call_Paras(UINT8 Index);
extern VOID Call_Enter_Call_Paras(UINT8 Index, INT8 *Name, INT8 *Number);
extern VOID Call_Spk_Ctrl(BOOL SpkOn);

extern VOID Call_Duration_Start(VOID);
extern VOID Call_Duration_Stop(VOID);

extern UINT8 Call_Get_Waiting_Param_Index(VOID);
extern VOID Call_Update_Call_Display(INT32 Seconds);

extern BOOL  Call_Is_Call_Waiting_Present(VOID);
extern UINT8 Call_Get_Free_Line(VOID);
extern VOID Call_Set_Line_On(UINT8 ParaIndex);
extern VOID Call_Set_Line_Off(UINT8 ParaIndex);

extern VOID Call_UI_Go_Back(VOID);
extern VOID Call_Print_Call_Info(const INT8 *func);
extern VOID Call_Proc_End(VOID);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*CALL_GLOBAL_H*/
