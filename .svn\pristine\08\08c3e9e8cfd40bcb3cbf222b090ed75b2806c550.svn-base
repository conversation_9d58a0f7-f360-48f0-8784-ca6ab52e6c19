/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/* ===========================================================================
File        : TCC_config.h
Description : Configuration parameters for the 
aplp/TCC package.

Notes       : These values can be overridden in gbl_config.h
The range checks should be updated for each
parameter.

Copyright (c) 2001 Intel CCD. All Rights Reserved
=========================================================================== */

#if !defined(_TCC_CONFIG_H_)
#define _TCC_CONFIG_H_

/* ---------------------------------------------------------------------------
Parameter   : TCC <Example> Parameter
Description : TCC parameter description 
Notes       : Why the range is what it is etc.
--------------------------------------------------------------------------- */
#define TCC_<EXAMPLE>       <value>
#define TCC_<EXAMPLE>_MIN	<min>
#define TCC_<EXAMPLE>_STEP	<step>
#define TCC_<EXAMPLE>_MAX	<max>


/* Include the global configuration file, so these values
can be overridden */
#if defined(_GBL_CONFIG_H_)
#undef _GBL_CONFIG_H_
#endif
#include "gbl_config.h"

/* Check the <Example> Parameter Range */
#if (TCC_<Example> < TCC_<Example>_MIN)|| \
(TCC_<Example> > TCC_<Example>_MAX)
#error "TCC Package <Example> parameter out of range."
#endif

#endif /* _TCC_CONFIG_H_ */


/*                      end of TCC_config.h
--------------------------------------------------------------------------- */





