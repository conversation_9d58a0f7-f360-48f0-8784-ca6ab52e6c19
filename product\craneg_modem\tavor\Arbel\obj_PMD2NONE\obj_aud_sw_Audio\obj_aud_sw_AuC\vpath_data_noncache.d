\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \aud_sw\AuC\src\vpath_data_noncache.c
\aud_sw\AuC\src\vpath_data_noncache.c:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\platform\inc\mmap.h
\csw\platform\inc\mmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hal\MMU\inc\mmu.h
\hal\MMU\inc\mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\BSP\inc\bsp.h
\csw\BSP\inc\bsp.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\PM\inc\powerManagement.h
\csw\PM\inc\powerManagement.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\pm\inc\pm_config.h
\hop\pm\inc\pm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \softutil\TickManager\inc\tick_manager.h
\softutil\TickManager\inc\tick_manager.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\timer\inc\timer.h
\hop\timer\inc\timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\SysCfg\inc\syscfg.h
\csw\SysCfg\inc\syscfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\timer\inc\timer_config.h
\hop\timer\inc\timer_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\intc_list.h
\hop\intc\inc\intc_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hal\GPIO\inc\gpio_config.h
\hal\GPIO\inc\gpio_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\intc_list_xirq.h
\hop\intc\inc\intc_list_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\xirq_config.h
\hop\intc\inc\xirq_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hal\GPIO\inc\gpio.h
\hal\GPIO\inc\gpio.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hal\GPIO\inc\cgpio_HW.h
\hal\GPIO\inc\cgpio_HW.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\intc_xirq.h
\hop\intc\inc\intc_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\BSP\inc\levante_hw.h
\hop\BSP\inc\levante_hw.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\BSP\inc\levante.h
\hop\BSP\inc\levante.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\BSP\inc\loadTable.h
\csw\BSP\inc\loadTable.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\BSP\inc\bsp_config.h
\csw\BSP\inc\bsp_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \csw\BSP\inc\ptable.h
\csw\BSP\inc\ptable.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data_noncache.o : \hop\BSP\inc\asr_property.h
\hop\BSP\inc\asr_property.h:
