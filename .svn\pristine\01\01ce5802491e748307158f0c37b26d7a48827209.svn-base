/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "RRLP-Components"
 * 	found in "rrlp12_1_0.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_Almanac_NAVKeplerianSet_H_
#define	_Almanac_NAVKeplerianSet_H_


#include <asn_application.h>

/* Including external dependencies */
#include "SVID.h"
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Almanac-NAVKeplerianSet */
typedef struct Almanac_NAVKeplerianSet {
	SVID_t	 svID;
	long	 navAlmE;
	long	 navAlmDeltaI;
	long	 navAlmOMEGADOT;
	long	 navAlmSVHealth;
	long	 navAlmSqrtA;
	long	 navAlmOMEGAo;
	long	 navAlmOmega;
	long	 navAlmMo;
	long	 navAlmaf0;
	long	 navAlmaf1;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} Almanac_NAVKeplerianSet_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_Almanac_NAVKeplerianSet;

#ifdef __cplusplus
}
#endif

#endif	/* _Almanac_NAVKeplerianSet_H_ */
#include <asn_internal.h>
