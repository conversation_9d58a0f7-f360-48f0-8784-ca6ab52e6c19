/*************************************************************************
*filename: lwip_sttest.c
*description: support sockets test
*history:
*-------2019-12-02, new file.
**************************************************************************/
#ifndef MIN_SYS

#include <stdlib.h>
#include <string.h>
#include "debug.h"
#include "lwip_api.h"
#include "opt.h"
#include "sys_arch.h"
#include "netif.h"
#include "ip4_addr.h"
#include "ip6_addr.h"
#include "dns.h"
#include "inet.h"
#include "inet6.h"
#include "sockets.h"
#include "netdb.h"
#include "libhttpclient.h"
#include "httpclient_sys.h"
#include "lwip_customer.h"

#if LWIP_ATCTL_SOCKET_TEST

#define LWIP_STTEST_MQTT    (0)
#define LWIP_STTEST_THREAD_PRIO	(90)

/*************************************************
* macro define here
*************************************************/
#define LWIP_STTEST_TASK_NAME_LEN            (8)
#define LWIP_STTEST_TASK_MAX_COUNT           (4)
#define LWIP_STTEST_MSGQ_QUEUE_SIZE          (16)
#define LWIP_STTEST_KEYTAG_SIZE              (16)
#define LWIP_STTEST_KEYVAL_SIZE              (256)
#define LWIP_STTEST_TAG_VAL_MAX_SIZE         (32)
#define LWIP_STTEST_SOCKOPT_MAX_SIZE         (64)
#define LWIP_STTEST_DATABUF_MAX_SIZE         (1500 * 2) /*max 2 mtu*/
#define LWIP_STTEST_INTERTO_MIN_SIZE         (5) /*5ms*/
#define LWIP_STTEST_SELECTTO_MIN_SIZE        (1000) /*1000ms*/
#define LWIP_STTEST_MAX_MALLOC_SIZE          (65500)

#define LWIP_STTEST_DNSAPI_KEYVAL_NUM          (2)
#define LWIP_STTEST_CREAT_KEYVAL_NUM           (2)
#define LWIP_STTEST_CONNECT_KEYVAL_NUM         (3)
#define LWIP_STTEST_BIND_KEYVAL_NUM            (3)
#define LWIP_STTEST_LISTEN_KEYVAL_NUM          (1)
#define LWIP_STTEST_ACCEPT_KEYVAL_NUM          (1)
#define LWIP_STTEST_WRITE_KEYVAL_NUM           (4)
#define LWIP_STTEST_READ_KEYVAL_NUM            (4)
#define LWIP_STTEST_ECHO_KEYVAL_NUM            (3)
#define LWIP_STTEST_SELECTECHO_KEYVAL_NUM      (4)
#define LWIP_STTEST_SELECTRW_KEYVAL_NUM        (9)

#define LWIP_STTEST_SELECTSET_NULL             (0)
#define LWIP_STTEST_SELECTSET_ME               (1)
#define LWIP_STTEST_SELECTSET_ALL              (2)

#define LWIP_STTEST_INVALID_SOCKET_ID          (-1)
#define LWIP_STTEST_INVALID_TASK_ID            (-1)
#define LWIP_STTEST_INVALID_CONN_ID            (-1)

/*hot bit flags set*/
/*wget flag, denote wget function going on, only support one wget*/
#define LWIP_STTEST_SYSCTRL_WGET_FLAG       (0x0001) /*bit 1*/

/*************************************************
* structure and enum define here
*************************************************/
enum LWIP_STTEST_TASK_MSGID_E {

    /********************************/
    /*socket test cmd msg id define*/
    /********************************/
    LWIP_STTEST_CMD_BEGIN             = 0,    
    LWIP_STTEST_CMD_SOCKET_SETSYS,
    LWIP_STTEST_CMD_SOCKET_GETSYS,
    LWIP_STTEST_CMD_SOCKET_CREAT,
    LWIP_STTEST_CMD_SOCKET_BIND,    
    LWIP_STTEST_CMD_SOCKET_CONNECT,
    LWIP_STTEST_CMD_SOCKET_LISTEN,
    LWIP_STTEST_CMD_SOCKET_ACCEPT,
    LWIP_STTEST_CMD_SOCKET_CLOSE,
    LWIP_STTEST_CMD_SOCKET_WRITE,
    LWIP_STTEST_CMD_SOCKET_READ,
    LWIP_STTEST_CMD_SOCKET_ECHO,
    LWIP_STTEST_CMD_SOCKET_SELECT_ECHO,
    LWIP_STTEST_CMD_SOCKET_SELECT_RW,
    LWIP_STTEST_CMD_SET_SOCK_OPTION,
    LWIP_STTEST_CMD_GET_SOCK_OPTION,
    LWIP_STTEST_CMD_SET_TCP_OPTION,
    LWIP_STTEST_CMD_GET_TCP_OPTION,
    LWIP_STTEST_CMD_SET_IP_OPTION,
    LWIP_STTEST_CMD_GET_IP_OPTION,
    LWIP_STTEST_CMD_SOCKET_DEBUG,
    LWIP_STTEST_CMD_SOCKET_PING,
    LWIP_STTEST_CMD_SOCKET_WGET,
    LWIP_STTEST_CMD_SOCKET_MQTT,    
    LWIP_STTEST_CMD_END

} ;

enum LWIP_STTEST_CONN_STATUS_E {
    LWIP_STTEST_CONN_STATUS_BEGIN     = 0,    
    LWIP_STTEST_CONN_ERROR,      /*in error status, not using it*/
    LWIP_STTEST_CONN_CLOSED,     /*have been closed*/
    LWIP_STTEST_CONN_CREATED,    /*have been created*/
    LWIP_STTEST_CONN_BINDED,     /*have been binded*/
    LWIP_STTEST_CONN_CONNECTED,  /*have been connected*/
    LWIP_STTEST_CONN_LISTENING,  /*have been listening for server*/
    LWIP_STTEST_CONN_ACCEPTING,  /*have been accepting for server*/
    LWIP_STTEST_CONN_WRITING,    /*have been writing*/
    LWIP_STTEST_CONN_READING,    /*have been reading*/
    LWIP_STTEST_CONN_SELECTING,  /*have been selecting*/
    LWIP_STTEST_CONN_STATUS_END
} ;

enum LWIP_STTEST_SIGNAL_E {
    LWIP_STTEST_SIG_BEGIN     = 0,    
    LWIP_STTEST_SIG_STOP,      /*stop process now.*/
    LWIP_STTEST_SIG_STOP_FINISH,    /*finish stop process now.*/
    LWIP_STTEST_SIG_PEND,      /*pend process now.*/
    LWIP_STTEST_SIG_RESUME,  /*continue process now.*/    
    LWIP_STTEST_SIG_END
} ;

struct lwip_sttest_task_table_st {
    int status;         /* LWIP_TASK_PENDING or LWIP_TASK_RUNING*/
    int priority;
    int taskid;         /* task id set. begin 1*/
    char taskname[LWIP_STTEST_TASK_NAME_LEN];     /* task name */
    OSMsgQRef taskmsgq;
    sys_thread_t taskref;
};

struct lwip_sttest_conn_table_st{
    struct lwip_sttest_conn_table_st  *next;

    int connid; /* connection id, set by user. can not reuse. */
    int taskid; /* task id, set by user, which task to run*/
    int status; /* connection status */
    int signal;/* control hisr, will interrup the task about this connection */

    /************************/
    /* application level parameters */
    /************************/
    struct http_client * httpclient;

    /************************/
    /* socket level parameters */
    /************************/
    int sockid;   /* socket id, get from create socket */
    int domain;   /* socket domain, get from socket domain, AF_INET, AF_INET6, AF_UNSPEC */
    int sttype;   /* socket type, get from socket type, tcp\udp\raw */
    int stproto;  /* socket protocol, not support now*/
    char hostname[LWIP_STTEST_KEYVAL_SIZE]; /* remote hostname for connect */

    /************************/
    /* socket listen parameters */
    /************************/
    int backlog;
    int accept_connid;

    /************************/
    /* tcp/udp level parameters */
    /************************/
    u16_t localport;
    u16_t remoteport;

    /************************/
    /* ip level parameters */
    /************************/
    ip_addr_t localipv4;
    ip6_addr_t localipv6;
    ip_addr_t remoteipv4; /*use for accept connection.*/
    ip6_addr_t remoteipv6; /*use for accept connection.*/

    /************************/
    /* raw data string */
    /************************/    
    char *rawdata;
    int rawdata_len;

    /************************/
    /* write setting */
    /************************/    
    char *builddata;     /*store build data*/
    int builddata_len; /*test data build length set*/
    int writesize;     /*set writesize after call lwip_write*/

    /************************/
    /* read setting */
    /************************/    
    int readdata_len; /*read data length pre set*/
    int readsize; /*set readsize after call lwip_read*/
    int readMbytes; /*record total readsize for Mbytes*/
    char *readdata; /*store latest read buf to this pointer*/

    /************************/
    /* select read\write\except set setting */
    /************************/    
    int interto; /*inter-time set for loop operation, unit is ms; default set to 0*/
    int loopcnt; /*loop counter set for loop operation; default set to 1;0 denote cycle*/
    int rtaskid; /*default set the same with taskid*/
    int wtaskid; /*default set the same with taskid*/

    int selectto;    /*use by set lwip_select as input, unit: ms*/ 
    int selreadset; /*0: denote null; 1: denote only me; 2: denote all; all: denote all socket low than self;*/ 
    int selwriteset; /*0: denote null; 1: denote only me; 2: denote all; me: denote only this socket;*/
    int selerrorset; /*0: denote null; 1: denote only me; 2: denote all; null: denote not detect; */ 

};

struct lwip_sttest_control_st {
    int status; /*0: not init; 1: initial finish*/
    int task_count; /*max set for 4*/
    int accept_conn_id; /*begin 128*/
    int flags; /*hot flag bit setting*/
    int ping_conn_id; /*record ping conn table id*/
    int wget_conn_id; /*record wget conn table id*/

    printCallback print;
    lwip_thread_fn task_entry [LWIP_STTEST_TASK_MAX_COUNT];

    struct lwip_sttest_conn_table_st * conn_tablelist_head; /*first table entry*/
    struct lwip_sttest_conn_table_st * conn_tablelist_last; /*last table entry*/
    struct lwip_sttest_conn_table_st * conn_tablelist_now;  /*now using*/

    struct lwip_sttest_task_table_st * task_tablelist[LWIP_STTEST_TASK_MAX_COUNT];
};

struct lwip_sttest_msgq_payload_st{
    int cmdid;
    int connid;
    int taskid;
    char* option; /*use for socket option config*/
    printCallback print;
};

/*************************************************
* global or static var define here
*************************************************/
static struct lwip_sttest_control_st  lwip_sttest_control = {0};

/*************************************************
* extern function and variable declare here
*************************************************/
extern OSMsgQRef lwip_atctl_msgq;

#if LWIP_STTEST_MQTT
void mqtt_sample(void);
#else
#define mqtt_sample()
#endif

int lwip_trigger_process(char *name, sys_thread_t threadid);
int lwip_atctl_parse_subcmd(char* usr_cmd, char *cfg_str, int max_len, char** cmd_next);
sys_thread_t lwip_atctl_thread_create(char *taskname,
                                                int priority,
                                                OSMsgQRef * msgq,
                                                lwip_thread_fn run);

/*************************************************
* function declare here
*************************************************/
int lwip_sttest_check_connid(int connid);
int lwip_sttest_new_conn_table(int connid);
int lwip_sttest_delet_conn_table(int connid);
int lwip_sttest_check_task_table(char *taskname);
int lwip_sttest_delet_task_table(char *taskname);
int lwip_sttest_env_buildup(int connid, int st_cmd, char *cfgmsg, int *taskid);
void lwip_sttest_free_conn(struct lwip_sttest_conn_table_st *conn_table);
int lwip_sttest_free_task(struct lwip_sttest_task_table_st *task_table);
int lwip_sttest_post_direct(int cmd_id, int conn_id, int task_id);
int lwip_sttest_thread_exit(int task_id);
void lwip_atctl_thread_delete(OSMsgQRef taskmsgq, sys_thread_t taskref);
int lwip_atctl_thread_post(OSMsgQRef taskmsgq, struct lwip_thread_msgq_msg_st * msg);

struct lwip_sttest_task_table_st * lwip_sttest_new_task_table(char *taskname, int priority);
struct lwip_sttest_conn_table_st * lwip_sttest_get_conn_table(int connid);

void lwip_sttest_task1_entry(void);
void lwip_sttest_task2_entry(void);
void lwip_sttest_task3_entry(void);
void lwip_sttest_task4_entry(void);
void lwip_sttest_build_testdata(void *data, int len);

err_t lwip_sttest_setopt_process(void * pArgs);
err_t lwip_sttest_getopt_process(void * pArgs);
err_t lwip_sttest_socket_process(void * pArgs);
err_t lwip_sttest_stping_process(void * pArgs);
err_t lwip_sttest_stwget_process(void * pArgs);
err_t lwip_sttest_stmqtt_process(void * pArgs);


/*************************************************
* socket test function for lwip_sttest
*************************************************/
int lwip_sttest_initial(void)
{
    if (lwip_sttest_control.status > 0) {
        return ERR_OK;
    }

    memset(&lwip_sttest_control, 0, sizeof(struct lwip_sttest_control_st));

    /*do initial process*/
    lwip_sttest_control.status = 1;    
    lwip_sttest_control.task_count = 0;
    lwip_sttest_control.accept_conn_id = 128; /*for accept connection, begin 128*/
    lwip_sttest_control.print = NULL;
    lwip_sttest_control.conn_tablelist_head = NULL;
    lwip_sttest_control.conn_tablelist_last = NULL;
    lwip_sttest_control.conn_tablelist_now = NULL;

    lwip_sttest_control.task_entry[0] = (lwip_thread_fn)lwip_sttest_task1_entry;
    lwip_sttest_control.task_entry[1] = (lwip_thread_fn)lwip_sttest_task2_entry;
    lwip_sttest_control.task_entry[2] = (lwip_thread_fn)lwip_sttest_task3_entry;
    lwip_sttest_control.task_entry[3] = (lwip_thread_fn)lwip_sttest_task4_entry;

    lwip_sttest_control.task_tablelist[0] = NULL;
    lwip_sttest_control.task_tablelist[1] = NULL;
    lwip_sttest_control.task_tablelist[2] = NULL;
    lwip_sttest_control.task_tablelist[3] = NULL;    

    return ERR_OK;
}

void lwip_sttest_build_testdata(void *data, int len)
{
	int loop;
	u8_t *pdata;
	u32_t *pdata_u32;
	u8_t fillData;

    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_330, "%s, data=%lx, len=%ld.", __FUNCTION__, data, len);

    /*fill data*/
    pdata      = (u8_t *)data;
    pdata_u32  = (u32_t *)data;
    *pdata_u32 = OSGetTicks();  

    pdata    = pdata + 4;
    fillData = 0x61; /**'a', fill a~w */
	for(loop = 0; loop < (len - 4 - 2); loop++) {
        *pdata++  = fillData;        
        fillData += 1;
        if (fillData > 0x77) {fillData = 0x61;}
	}

    /*fill NEWLINE*/
    *pdata++ = '\r';
    *pdata++ = '\n';

}

void lwip_sttest_wget_callback(char * data, int len, int num, void *cbdata)
{
    char ind_buf [128];
    int ind_size = 0;
    struct lwip_sttest_conn_table_st * conn_table;

    if (NULL == data || len <= 0) {
        return;
    }

    conn_table = lwip_sttest_get_conn_table(lwip_sttest_control.wget_conn_id);
    if (NULL == conn_table) {
        LWIP_ASSERT_NOW(0);
    }

    while (LWIP_STTEST_SIG_PEND == conn_table->signal) {
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_340, "%s, wget sleep now, len=%ld, content len=%ld!", __FUNCTION__, len, num);
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "PROCESS: wget sleep now, len=%ld, content len=%ld!", len, num);
        if (lwip_sttest_control.print && ind_size > 0) {
            ind_size = 0;
            lwip_sttest_control.print(ind_buf);
        }

        sys_msleep(1000);
    }

    conn_table->readdata_len += len;
    conn_table->readMbytes   += (conn_table->readdata_len >> 20);
    conn_table->readdata_len  = conn_table->readdata_len & 0xFFFFF;
    conn_table->readsize = num;    

    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_341, "%s, wget get data, len=%ld, content len=%ld, read=%ld(MB)+%ld(B)", 
                                            __FUNCTION__, len, num, conn_table->readMbytes, conn_table->readdata_len);
    ind_size = snprintf(ind_buf, sizeof(ind_buf), "PROCESS: wget get data, len=%ld, content len=%ld, read=%ld(MB)+%ld(B)", 
                                                        len, num, conn_table->readMbytes, conn_table->readdata_len);
    if (lwip_sttest_control.print && ind_size > 0) {
        lwip_sttest_control.print(ind_buf);
    }

}

/*
*Description: check wether exist this connection id alreay, can not create the same connid again.
*Modificaton:
*/
int lwip_sttest_check_connid(int connid)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_conn_table_st *conn_table_1st = NULL;
    int ret = -1;

    conn_table = lwip_sttest_control.conn_tablelist_head;
    conn_table_1st = conn_table;
    while (conn_table) {
        if (connid == conn_table->connid) {
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_350, "lwiperr: %s, exist this connection id.", __FUNCTION__);            
            ret = ERR_OK;
            break;
        }
        conn_table = conn_table->next;
        if (conn_table == conn_table_1st) {
            LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_351, "lwiperr: %s, pointer loop happen.", __FUNCTION__);
            ret = -2;
            break;
        }
    }

    return ret;
}

int lwip_sttest_new_conn_table(int connid)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_conn_table_st *conn_table_list = NULL;
    char *rawdata = NULL;
    char *readdata = NULL;

    conn_table = (struct lwip_sttest_conn_table_st *)MALLOC(sizeof(struct lwip_sttest_conn_table_st));
    rawdata = (char *)MALLOC(LWIP_STTEST_KEYVAL_SIZE);
    readdata = (char *)MALLOC(LWIP_STTEST_DATABUF_MAX_SIZE);

    if ((conn_table == NULL)
        || (rawdata == NULL)
        || (readdata == NULL)) {
            LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_365, "lwiperr: %s, malloc failed.", __FUNCTION__);
            if (conn_table) {free(conn_table);}
            if (rawdata) {free(rawdata);}
            if (readdata) {free(readdata);}
            return ERR_MEM;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_372, "%s(%d), conn_table=%lx,rawdata=%lx,readdata=%lx.", 
                                        __FUNCTION__, connid, conn_table, rawdata, readdata);

    memset(conn_table, 0, sizeof(struct lwip_sttest_conn_table_st));
    memcpy(rawdata, "Hello World!", sizeof("Hello World!"));

    /*initial connection table*/
    conn_table->connid   = connid;
    conn_table->taskid   = 0; /*denote lwip_atctl task*/
    conn_table->status   = LWIP_STTEST_CONN_STATUS_BEGIN;
    conn_table->signal  = LWIP_STTEST_SIG_BEGIN;

    /*socket level init*/
    conn_table->sockid   = -1; /*set invalid now*/
    conn_table->domain   = AF_INET;
    conn_table->sttype   = SOCK_DGRAM;
    conn_table->stproto  = IPPROTO_UDP;
    conn_table->rawdata  = rawdata;
    conn_table->rawdata_len = strlen(rawdata) + 1;
    conn_table->readdata = readdata;
    conn_table->builddata = NULL;
    conn_table->builddata_len = 0;    
    conn_table->interto = 1000;
    conn_table->loopcnt = 1;
    conn_table->next     = NULL;
    memcpy(conn_table->hostname, "*************", sizeof("*************"));

    /*udp\tcp level init*/
    conn_table->localport = 0;
    conn_table->remoteport = 9090;
    ip_addr_set_any(&conn_table->localipv4);
    ip6_addr_set_any(&conn_table->localipv6);
    
    /*add to table list*/
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_400, "%s(%d), before, tablelist head=%lx,last=%lx,now=%lx.", 
            __FUNCTION__, connid, lwip_sttest_control.conn_tablelist_head, lwip_sttest_control.conn_tablelist_last, lwip_sttest_control.conn_tablelist_now);

    conn_table_list = lwip_sttest_control.conn_tablelist_head;
    if (NULL == conn_table_list) {
        lwip_sttest_control.conn_tablelist_head = conn_table;
        lwip_sttest_control.conn_tablelist_last = conn_table;
        lwip_sttest_control.conn_tablelist_now = conn_table;
    } else {
        LWIP_ASSERT_NOW(lwip_sttest_control.conn_tablelist_last != NULL);
        lwip_sttest_control.conn_tablelist_last->next = conn_table;
        lwip_sttest_control.conn_tablelist_last = conn_table;
        lwip_sttest_control.conn_tablelist_now = conn_table;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_415, "%s(%d), update, tablelist head=%lx,last=%lx,now=%lx.", 
            __FUNCTION__, connid, lwip_sttest_control.conn_tablelist_head, lwip_sttest_control.conn_tablelist_last, lwip_sttest_control.conn_tablelist_now);

    return ERR_OK;
}

int lwip_sttest_new_accept_conn_table(int connid, int sockid, struct lwip_sttest_conn_table_st *listen_conn_table)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_conn_table_st *conn_table_list = NULL;
    char *rawdata = NULL;
    char *readdata = NULL;
    u16_t localport;
    u16_t remoteport;
    ipX_addr_t* ipx_addr = NULL;
    int isipv6;

    conn_table = (struct lwip_sttest_conn_table_st *)MALLOC(sizeof(struct lwip_sttest_conn_table_st));
    rawdata = (char *)MALLOC(LWIP_STTEST_KEYVAL_SIZE);
    readdata = (char *)MALLOC(LWIP_STTEST_DATABUF_MAX_SIZE);

    if ((conn_table == NULL)
        || (rawdata == NULL)
        || (readdata == NULL)) {
            LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_437, "lwiperr: %s, malloc failed.", __FUNCTION__);
            if (conn_table) {free(conn_table);}
            if (rawdata) {free(rawdata);}
            if (readdata) {free(readdata);}
            return ERR_MEM;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_444, "%s(%d,%d), conn_table=%lx,rawdata=%lx,readdata=%lx.", 
                                        __FUNCTION__, connid, sockid, conn_table, rawdata, readdata);

    memset(conn_table, 0, sizeof(struct lwip_sttest_conn_table_st));
    memcpy(rawdata, "Hello World!", sizeof("Hello World!"));

    /*initial connection table*/
    conn_table->connid   = connid;
    conn_table->taskid   = 0; /*denote lwip_atctl task*/
    conn_table->status   = LWIP_STTEST_CONN_CONNECTED;

    /*socket level init*/
    conn_table->sockid   = sockid;
    conn_table->domain   = listen_conn_table->domain;
    conn_table->sttype   = listen_conn_table->sttype;
    conn_table->stproto  = listen_conn_table->stproto;
    conn_table->rawdata  = rawdata;
    conn_table->rawdata_len = strlen(rawdata) + 1;
    conn_table->readdata = readdata;
    conn_table->next     = NULL;

    /*udp\tcp level init*/
    conn_table->localport = lwip_get_sock_localport(sockid);
    conn_table->remoteport = lwip_get_sock_remoteport(sockid);
    
    isipv6 = lwip_get_sock_isipv6(sockid);
    ipx_addr = lwip_get_sock_localipXaddr(sockid);
    if (isipv6) {
        IP6ADDR2_COPY(&conn_table->localipv6, ipX_2_ip6(ipx_addr));
    } else {
        IPADDR2_COPY(&conn_table->localipv4, ipX_2_ip(ipx_addr));
    }
    ipx_addr = lwip_get_sock_remoteipXaddr(sockid);
    if (isipv6) {
        IP6ADDR2_COPY(&conn_table->remoteipv6, ipX_2_ip6(ipx_addr));
    } else {
        IPADDR2_COPY(&conn_table->remoteipv4, ipX_2_ip(ipx_addr));
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_480, "%s(%d,%d), before, tablelist head=%lx,last=%lx,now=%lx.", 
            __FUNCTION__, connid, sockid, lwip_sttest_control.conn_tablelist_head, lwip_sttest_control.conn_tablelist_last, lwip_sttest_control.conn_tablelist_now);

    /*add to table list*/
    conn_table_list = lwip_sttest_control.conn_tablelist_head;
    if (NULL == conn_table_list) {
        lwip_sttest_control.conn_tablelist_head = conn_table;
        lwip_sttest_control.conn_tablelist_last = conn_table;
        lwip_sttest_control.conn_tablelist_now = conn_table;
    } else {
        LWIP_ASSERT_NOW(lwip_sttest_control.conn_tablelist_last != NULL);
        lwip_sttest_control.conn_tablelist_last->next = conn_table;
        lwip_sttest_control.conn_tablelist_last = conn_table;
        lwip_sttest_control.conn_tablelist_now = conn_table;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_496, "%s(%d,%d), update, tablelist head=%lx,last=%lx,now=%lx.", 
            __FUNCTION__, connid, sockid, lwip_sttest_control.conn_tablelist_head, lwip_sttest_control.conn_tablelist_last, lwip_sttest_control.conn_tablelist_now);

    return ERR_OK;
}


struct lwip_sttest_conn_table_st * lwip_sttest_get_conn_table(int connid)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_conn_table_st *conn_table_1st = NULL;

    conn_table = lwip_sttest_control.conn_tablelist_now;
    if(connid == conn_table->connid) {
        return conn_table;
    }

    conn_table_1st = lwip_sttest_control.conn_tablelist_head;
    conn_table = conn_table_1st;
    while (conn_table) {
        if (connid == conn_table->connid) {
            /*update now conn table*/
            lwip_sttest_control.conn_tablelist_now = conn_table;
            return conn_table;
        }
        conn_table = conn_table->next;
        if (conn_table == conn_table_1st) {
            LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_511, "lwiperr: %s, pointer loop happen.", __FUNCTION__);
            return NULL;
        }
    }

    return NULL;
}


int lwip_sttest_delet_conn_table(int connid)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_conn_table_st *conn_table_1st = NULL;
    struct lwip_sttest_conn_table_st *conn_table_next = NULL;
    int ret = -1;

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_540, "%s(%d), tablelist head=%lx,last=%lx,now=%lx.", 
            __FUNCTION__, connid, lwip_sttest_control.conn_tablelist_head, lwip_sttest_control.conn_tablelist_last, lwip_sttest_control.conn_tablelist_now);

    /*check conn_tablelist_now pointer*/
    conn_table = lwip_sttest_control.conn_tablelist_now;    
    if (conn_table->connid == connid) {
        lwip_sttest_control.conn_tablelist_now = NULL;
    }

    /*  is it the first table? */
    conn_table_1st = lwip_sttest_control.conn_tablelist_head;
    conn_table = conn_table_1st;
    if (conn_table->connid == connid) {
        lwip_sttest_control.conn_tablelist_head = conn_table->next;
        lwip_sttest_free_conn(conn_table);
        ret = ERR_OK;        
    } else { /* look for table further down the list */
        for (conn_table = conn_table_1st; conn_table != NULL; conn_table = conn_table->next) {
            conn_table_next = conn_table->next;
            if ((NULL != conn_table_next)
                && (conn_table_next->connid == connid)) {
                conn_table->next = conn_table_next->next;

                if (lwip_sttest_control.conn_tablelist_last == conn_table_next) {
                    lwip_sttest_control.conn_tablelist_last = conn_table;
                }

                lwip_sttest_free_conn(conn_table_next);
                ret = ERR_OK;
                break;
            }

            if (conn_table_next == conn_table_1st) {
                LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_578, "lwiperr: %s, list link loop", __FUNCTION__);
                break;
            }
        }
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_584, "%s(%d), tablelist head=%lx,last=%lx,now=%lx.", 
            __FUNCTION__, connid, lwip_sttest_control.conn_tablelist_head, lwip_sttest_control.conn_tablelist_last, lwip_sttest_control.conn_tablelist_now);

    return ret;    
}

struct lwip_sttest_task_table_st * lwip_sttest_new_task_table(char *taskname, int priority)
{
    struct lwip_sttest_task_table_st *task_table;
    lwip_thread_fn task_entry;
    int str_len;
    int task_count;

    /*check max counter*/
    task_count = lwip_sttest_control.task_count;    
    if (task_count >= LWIP_STTEST_TASK_MAX_COUNT) {
        return NULL;
    }

    str_len = strlen(taskname) + 1;
    if (str_len > LWIP_STTEST_TASK_NAME_LEN) {
        str_len = LWIP_STTEST_TASK_NAME_LEN;
    }

    task_table = (struct lwip_sttest_task_table_st *)MALLOC(sizeof(struct lwip_sttest_task_table_st));
    if (task_table) {
        /*initial taske table*/
        task_table->status = LWIP_TASK_PENDING;
        task_table->taskid = task_count + 1; /*taskid begin 1, 0 denote atctrl main task*/
        if (priority > 0) {
            task_table->priority = priority;
        } else {
            task_table->priority = LWIP_STTEST_THREAD_PRIO;
        }     
        memcpy(task_table->taskname, taskname, str_len);

        task_entry = lwip_sttest_control.task_entry[task_count];

        task_table->taskref = lwip_atctl_thread_create(task_table->taskname,
                                                         task_table->priority,
                                                         &(task_table->taskmsgq),
                                                         task_entry);
		LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_620, "%s: taskname is %s, task_count is %d.", __FUNCTION__, taskname, task_count);

        /*add to table list*/
        lwip_sttest_control.task_tablelist[task_count] = task_table;
        lwip_sttest_control.task_count = task_count + 1;
        
    } else {
        LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_630, "lwiperr: %s, malloc task table failed.", __FUNCTION__);
        return NULL;
    }

    return task_table;
}

int lwip_sttest_check_task_table(char *taskname)
{
    struct lwip_sttest_task_table_st *task_table = NULL;
    int loop_id;
    int ret = -1;

    for (loop_id = 0; loop_id < LWIP_STTEST_TASK_MAX_COUNT; loop_id++) {
        task_table = lwip_sttest_control.task_tablelist[loop_id];
        if (0 == strncmp(taskname, task_table->taskname, strlen(taskname))) {
            ret = ERR_OK;
            break;
        }        
    }

    return ret;

}

struct lwip_sttest_task_table_st * lwip_sttest_get_task_table_byID(int task_id)
{
    if (task_id > LWIP_STTEST_TASK_MAX_COUNT) {
        return NULL;
    }
    return lwip_sttest_control.task_tablelist[task_id - 1];
}

struct lwip_sttest_task_table_st * lwip_sttest_get_task_table(char *taskname)
{
    struct lwip_sttest_task_table_st *task_table = NULL;
    int loop_id;

    for (loop_id = 0; loop_id < LWIP_STTEST_TASK_MAX_COUNT; loop_id++) {
        task_table = lwip_sttest_control.task_tablelist[loop_id];
        if (0 == strncmp(taskname, task_table->taskname, strlen(taskname))) {
            return task_table;
        }        
    }

    return NULL;
}

int lwip_sttest_delet_task_table(char *taskname)
{
    struct lwip_sttest_task_table_st *task_table = NULL;
    int loop_id;
    int ret = -1;

    for (loop_id = 0; loop_id < LWIP_STTEST_TASK_MAX_COUNT; loop_id++) {
        task_table = lwip_sttest_control.task_tablelist[loop_id];
        if (0 == strncmp(taskname, task_table->taskname, strlen(taskname))) {
            ret = lwip_sttest_free_task(task_table);
            break;
        }        
    }

    return ret;    
}

int lwip_sttest_dns_query(char *netifname, char *hostname, int ipv4v6, printCallback cb)
{
    char ind_buf [512];
    int ind_size = 0;
    int size_set = 0;
    int fill_size = 0;
    int dns_index = 0;
    char *ind_buf_ptr = NULL;
	struct addrinfo *res = NULL,*cur = NULL;
	struct addrinfo hints;
    struct sockaddr_in *sin_res;
    struct sockaddr_in6 *sin6_res;
    struct netif *netif = NULL;
	int ret       = 0;

    memset(&hints,0,sizeof(hints));
    if (6 == ipv4v6) {
	    hints.ai_family   = AF_INET6;
	    hints.ai_socktype = SOCK_DGRAM;
    } else if (4 == ipv4v6) {
	    hints.ai_family   = AF_INET;
	    hints.ai_socktype = SOCK_DGRAM;
    } else {
        hints.ai_socktype = SOCK_DGRAM;
    }

    if (NULL == netifname) {
        ret =lwip_getaddrinfo_with_netif(hostname, NULL, &hints, &res, NULL);
    } else {
        ret =lwip_getaddrinfo_with_netif(hostname, NULL, &hints, &res, netifname);
    }

    if (ret) {		
		return ERR_MEM;
    }

    size_set = sizeof(ind_buf);
    ind_buf_ptr = ind_buf;
    ind_size = snprintf(ind_buf_ptr, size_set, "dns query list=>"NEWLINE);
    size_set = size_set - ind_size;
    fill_size = fill_size + ind_size;
    ind_buf_ptr = ind_buf_ptr + ind_size;

	for(cur = res; cur != NULL; cur = cur->ai_next) {
      /*reset ind_size first*/
      ind_size = 0;
      dns_index++;
      
	  if ((cur->ai_addr->sa_family) == AF_INET6) {
          sin6_res = (struct sockaddr_in6*)cur->ai_addr;
          ind_size = snprintf(ind_buf_ptr, size_set, "%d: %s"NEWLINE, dns_index, inet6_ntoa(sin6_res->sin6_addr));
	  }

      if ((cur->ai_addr->sa_family) == AF_INET) {
          sin_res = (struct sockaddr_in*)cur->ai_addr;
          ind_size = snprintf(ind_buf_ptr, size_set, "%d: %s"NEWLINE, dns_index, inet_ntoa(sin_res->sin_addr));
	  }

      if (ind_size > 0) {
        size_set = size_set - ind_size;
        fill_size = fill_size + ind_size;
        ind_buf_ptr = ind_buf_ptr + ind_size;
      } else {
        break;
      }
	}

	if(res) {
        freeaddrinfo(res);
	}

    if (cb && (fill_size > 0)) {
        cb(ind_buf);
    }
    
    return ERR_OK;
}

int lwip_sttest_set_system(int tag, char *cfgmsg, printCallback cb)
{
    struct lwip_sttest_conn_table_st * conn_table = NULL;
    struct lwip_sttest_task_table_st * task_table = NULL;
    char *msg_next = NULL;
    char *msg_now = NULL;
    char keytag [LWIP_STTEST_KEYTAG_SIZE];
    char keyval [LWIP_STTEST_KEYVAL_SIZE];
    int keylen = 0;
    char ind_buf [256] = {0};
    int ind_size = 0;
    err_t ret = ERR_OK;

    /* only one keytag | keyval format*/
    /*support all keytag */
    msg_now  = cfgmsg;
    msg_next = NULL;

    /*key-value config msg parse*/
    /*get keytag*/
    ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
    if (ret != 0) { return ERR_VAL;}

    /*get keyval*/
    msg_now = msg_next;
    ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
    if (ret != 0) { return ERR_BUF;}

    /*parse keytag | keyval*/   
    keylen = strlen(keyval) + 1; /*include '\0'*/

    if (0 == strncmp(keytag, "task", strlen("task"))) {
        if (ERR_OK != lwip_sttest_check_task_table(keyval)) {
            task_table = lwip_sttest_new_task_table(keyval, tag);
            if (task_table) {
                ind_size = snprintf(ind_buf, sizeof(ind_buf), "PROCESS: new task table, taskname=%s, status=%d, priority=%d, taskid=%d!", 
                                                                task_table->taskname, task_table->status, task_table->priority, task_table->taskid);
            } else {
                ret = ERR_MEM;
            }
        } else {
            ind_size = snprintf(ind_buf, sizeof(ind_buf), "PROCESS: exist task table already, taskname=%s, status=%d, priority=%d, taskid=%d!", 
                                                            task_table->taskname, task_table->status, task_table->priority, task_table->taskid);        
        }
    } else if (0 == strncmp(keytag, "rawdata", strlen("rawdata"))) {
        conn_table = lwip_sttest_get_conn_table(tag);
        if (conn_table) {
            if (conn_table->rawdata) {free(conn_table->rawdata);}
            conn_table->rawdata = (char *)MALLOC(keylen);
            if (conn_table->rawdata) {
                memcpy(conn_table->rawdata, keyval, keylen);
                conn_table->rawdata_len = keylen;
            } else {
                ret = ERR_MEM;
            }
        } else {
            ret = ERR_ARG;
        }
    } else {
        ret = ERR_ARG;
    }

    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return ret;
}

int lwip_sttest_get_system(int tag, char *cfgmsg, printCallback cb)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    char *msg_next = NULL;
    char *msg_now = NULL;
    char keytag [LWIP_STTEST_KEYTAG_SIZE];
    char keyval [LWIP_STTEST_KEYVAL_SIZE];
    char ind_buf [256];
    int ind_size = 0; 
    err_t ret = ERR_OK;

	memset(keytag, 0, sizeof(keytag));	
	memset(keyval, 0, sizeof(keyval));	
	memset(ind_buf, 0, sizeof(ind_buf));

    /*key-value config msg parse*/
    /*get keytag*/	
    msg_now  = cfgmsg;
    ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
    if (ret != 0) { return ERR_VAL;}

	if (0 == strncmp((const char *)keytag, "rawdata", strlen("rawdata"))) {
        conn_table = lwip_sttest_get_conn_table(tag);
        if (conn_table) {
            if (conn_table->rawdata) {
				memcpy(keyval, conn_table->rawdata, strlen(conn_table->rawdata));				
				ind_size = snprintf(ind_buf, sizeof(ind_buf), "%s: get rawdata = %s!", __FUNCTION__, keyval);
			}
        } else {
            ret = ERR_ARG;
        }
    } else {
        ret = ERR_ARG;
    }

    if (ret == ERR_OK && cb && (ind_size > 0)) {
        cb(ind_buf);
    }
    return ret;
}

int lwip_sttest_tagval_parser(int connid, char *cfgmsg, int *taskid)
{
    int loop_id = 0;
    int key_count = 0;
    int int_val = 0;
    char *msg_next = NULL;
    char *msg_now = NULL;    
    struct lwip_sttest_conn_table_st * conn_table;
    struct lwip_sttest_task_table_st * task_table;
    int ret = ERR_OK;

    /*get connection table first*/
    conn_table = lwip_sttest_get_conn_table(connid);
    if (NULL == conn_table) {
        LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_900, "%s, no conn table find for connid=%d.", __FUNCTION__, connid);
        return ERR_VAL;
    }

	LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_901, "%s, msg=%s, connid=%d.", __FUNCTION__, cfgmsg, connid);

    /*set preset first, conn_table->taskid update in env_checkup already*/
    *taskid = conn_table->taskid; 

    /* keytag | keyval format*/
    /*support ip4\ip6\port\null*/
    char keytag [LWIP_STTEST_KEYTAG_SIZE];
    char keyval [LWIP_STTEST_KEYVAL_SIZE];
    msg_now  = cfgmsg;
    msg_next = NULL;
    
    /*key-value config msg parse*/
    for (loop_id = 0; loop_id < LWIP_STTEST_TAG_VAL_MAX_SIZE; loop_id++) {
        /*get keytagk*/
        ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
        if (ret != 0) { break;}

        /*get keyval*/
        msg_now = msg_next;
        ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
        if (ret != 0) { break;}

        /*parse keytag | keyval*/
        if (0 == strncmp(keytag, "host", strlen("host"))) {
            if (strlen(keyval) > LWIP_STTEST_KEYVAL_SIZE) {
                ret = ERR_VAL;
                break;
            }
            memset(conn_table->hostname, 0, LWIP_STTEST_KEYVAL_SIZE);
            memcpy(conn_table->hostname, keyval, sizeof(keyval));
        } else if (0 == strncmp(keytag, "type", strlen("type"))) {
            int iptype;
            iptype = atol(keyval);

            if (4 == iptype) {
                 conn_table->domain = AF_INET;
             } else if (6 == iptype) {
                 conn_table->domain = AF_INET6;
             } else {
                ret = ERR_VAL;
                break;             
             }
        } else if (0 == strncmp(keytag, "proto", strlen("proto"))) {
            if (0 == strncmp(keyval, "tcp", strlen("tcp"))) {
                conn_table->sttype = SOCK_STREAM;
            } else if (0 == strncmp(keyval, "udp", strlen("udp"))) {
                conn_table->sttype = SOCK_DGRAM;
            } else if (0 == strncmp(keyval, "raw", strlen("raw"))) {
                conn_table->sttype = SOCK_RAW;
            } else {
                ret = ERR_VAL;
                break;
            }
        } else if (0 == strncmp(keytag, "port", strlen("port"))) {
            u16_t port = 0;
            port = atol(keyval);
            if (port == 0) {
                ret = ERR_VAL;
                break;
            }
            conn_table->remoteport = port;
        } else if (0 == strncmp(keytag, "lport", strlen("lport"))) {
            u16_t lport = 0;
            lport = atol(keyval);
            if (lport == 0) {
                ret = ERR_VAL;
                break;
            }
            conn_table->localport = lport;
        } else if (0 == strncmp(keytag, "ip4", strlen("ip4"))) {
            ip_addr_t ipaddr;
            if (ipaddr_aton(keyval, &ipaddr)) {
                memcpy(&conn_table->localipv4, &ipaddr, sizeof(ip_addr_t));
            } else {
                ret = ERR_VAL;
                break;
            }
        } else if (0 == strncmp(keytag, "ip6", strlen("ip6"))) {
            ip6_addr_t ip6addr;
            if (ip6addr_aton(keyval, &ip6addr)) {
                memcpy(&conn_table->localipv6, &ip6addr, sizeof(ip6_addr_t));
            } else {
                ret = ERR_VAL;
                break;
            }
        } else if (0 == strncmp(keytag, "task", strlen("task"))) {
            task_table = lwip_sttest_get_task_table(keyval);
            if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                conn_table->taskid = task_table->taskid; /*record it*/
                *taskid = task_table->taskid;
            } else {
                ret = ERR_ARG;
                break;
            }
        } else if (0 == strncmp(keytag, "rtask", strlen("rtask"))) {
           task_table = lwip_sttest_get_task_table(keyval);
           if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
               conn_table->rtaskid = task_table->taskid; /*record it*/
           } else {
               ret = ERR_ARG;
               break;
           }
        } else if (0 == strncmp(keytag, "wtask", strlen("wtask"))) {
           task_table = lwip_sttest_get_task_table(keyval);
           if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
               conn_table->wtaskid = task_table->taskid; /*record it*/
           } else {
               ret = ERR_ARG;
               break;
           }
        } else if (0 == strncmp(keytag, "rlen", strlen("rlen"))) {
           int_val = atol(keyval);
           if (int_val == 0) {int_val = LWIP_STTEST_DATABUF_MAX_SIZE;}
           conn_table->readdata_len = int_val;
        } else if ((0 == strncmp(keytag, "len", strlen("len"))) || (0 == strncmp(keytag, "wlen", strlen("wlen")))) {
           int_val = atol(keyval);
           if (int_val > 65500) {int_val = 65500;} /*set max value*/
           conn_table->builddata_len = int_val;
        } else if (0 == strncmp(keytag, "readset", strlen("readset"))) {
           if (0 == strncmp(keyval, "all", strlen("all"))) {
               conn_table->selreadset = LWIP_STTEST_SELECTSET_ALL;
           } else if (0 == strncmp(keyval, "me", strlen("me"))) {
               conn_table->selreadset = LWIP_STTEST_SELECTSET_ME;
           } else {
               conn_table->selreadset = LWIP_STTEST_SELECTSET_NULL;
           }
        } else if (0 == strncmp(keytag, "writeset", strlen("writeset"))) {
           if (0 == strncmp(keyval, "all", strlen("all"))) {
               conn_table->selwriteset = LWIP_STTEST_SELECTSET_ALL;
           } else if (0 == strncmp(keyval, "me", strlen("me"))) {
               conn_table->selwriteset = LWIP_STTEST_SELECTSET_ME;
           } else {
               conn_table->selwriteset = LWIP_STTEST_SELECTSET_NULL;
           }
        } else if (0 == strncmp(keytag, "errorset", strlen("errorset"))) {
           if (0 == strncmp(keyval, "all", strlen("all"))) {
               conn_table->selerrorset = LWIP_STTEST_SELECTSET_ALL;
           } else if (0 == strncmp(keyval, "me", strlen("me"))) {
               conn_table->selerrorset = LWIP_STTEST_SELECTSET_ME;
           } else {
               conn_table->selerrorset = LWIP_STTEST_SELECTSET_NULL;
           }
        } else if (0 == strncmp(keytag, "selectto", strlen("selectto"))) {
           int_val = atol(keyval);
           if (int_val == 0) {int_val = LWIP_STTEST_SELECTTO_MIN_SIZE;}
           conn_table->selectto = int_val;
        }  else if (0 == strncmp(keytag, "inter", strlen("inter"))) {
           int_val = atol(keyval);
           if (int_val == 0) {int_val = LWIP_STTEST_INTERTO_MIN_SIZE;}
           conn_table->interto = int_val;
        } else if (0 == strncmp(keytag, "loop", strlen("loop"))) {
           int_val = atol(keyval);
           if (int_val < 0) {int_val = 0;}
           conn_table->loopcnt = int_val;
        } else if (0 == strncmp(keytag, "backlog", strlen("backlog"))) {
            conn_table->backlog = atol(keyval);
        } else {
            break;
        }

        memset(keytag, 0, sizeof(keytag));
        memset(keyval, 0, sizeof(keyval));
        msg_now = msg_next;
        key_count++;
    }

    ret = ERR_OK;
    if (key_count == 0) {
        ret = ERR_VAL;
    }

    return ret;
}

int lwip_sttest_env_checkup(int connid, int st_cmd)
{
    struct lwip_sttest_conn_table_st * conn_table = NULL;
    struct lwip_sttest_task_table_st * task_table = NULL;
    int ret = ERR_OK;

    /*special deal for create socket*/
    if ((LWIP_STTEST_CMD_SOCKET_CREAT == st_cmd)
      || (LWIP_STTEST_CMD_SOCKET_PING == st_cmd)
      || (LWIP_STTEST_CMD_SOCKET_WGET == st_cmd)
      || (LWIP_STTEST_CMD_SOCKET_MQTT == st_cmd)){
        /* check wether exist this connection id */
        /* if exist, return err. */
        if (ERR_OK == lwip_sttest_check_connid(connid)) {
            ret = ERR_VAL;
        } else {
            /*create a new socket table */   
            ret = lwip_sttest_new_conn_table(connid);
        }
    
        return ret;
    }

    /* check wether exist this connection id */
    /* and check status */
    conn_table = lwip_sttest_get_conn_table(connid);
    if (NULL == conn_table) {
        return ERR_BUF;
    }
    if (conn_table->status == LWIP_STTEST_CONN_ERROR) {
        return ERR_VAL;
    }

    switch (st_cmd) {
        case LWIP_STTEST_CMD_SOCKET_CLOSE:
        {
            if (conn_table->status < LWIP_STTEST_CONN_CREATED) {
				ret = ERR_VAL;
                break;
            }            
            /*reset parameters for conn_table*/            
            conn_table->taskid = 0; /*default: using lwip atctrl main task*/
            break;            
        }

        case LWIP_STTEST_CMD_SOCKET_CONNECT:
        case LWIP_STTEST_CMD_SOCKET_BIND:
        case LWIP_STTEST_CMD_SOCKET_LISTEN:
        {
            if (conn_table->status > LWIP_STTEST_CONN_LISTENING) {
				ret = ERR_VAL;
                break;
            }
            
            /*reset parameters for conn_table*/            
            conn_table->taskid = 0; /*default: using lwip atctrl main task*/
            conn_table->backlog = 5; /*default set to 5;*/

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_ACCEPT:
        {
            if (conn_table->status != LWIP_STTEST_CONN_LISTENING) {
				ret = ERR_VAL;
                break;
            }
            
            /*reset parameters for conn_table*/            
            conn_table->taskid = 0; /*default: using lwip atctrl main task*/
            conn_table->accept_connid = LWIP_STTEST_INVALID_CONN_ID; /*default set invalid val;*/

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_WRITE:
        {
            if (conn_table->status < LWIP_STTEST_CONN_CREATED) {
				ret = ERR_VAL;
                break;
            }

            /*reset write parameters for conn_table*/            
            conn_table->taskid = 0; /*default: using lwip atctrl main task*/
            conn_table->builddata_len = 0;
            conn_table->interto = 0;
            conn_table->loopcnt = 1;

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_READ:        
        case LWIP_STTEST_CMD_SOCKET_ECHO:
        {
            if (conn_table->status < LWIP_STTEST_CONN_CREATED) {
				ret = ERR_VAL;
                break;
            }

            /*reset read parameters for conn_table*/
            conn_table->readdata_len = LWIP_STTEST_DATABUF_MAX_SIZE; /*max read len*/            
            conn_table->taskid = 0; /*default: using lwip atctrl main task*/
            conn_table->interto = 0;
            conn_table->loopcnt = 1;
            conn_table->readsize = 0;

            break;
        }
        
        case LWIP_STTEST_CMD_SOCKET_SELECT_ECHO:
        {       
            if (conn_table->status < LWIP_STTEST_CONN_CONNECTED) {
                ret = ERR_VAL;
                break;
            }

            /*reset select parameters for conn_table*/             
            conn_table->builddata_len = 0;
            conn_table->readdata_len = LWIP_STTEST_DATABUF_MAX_SIZE;             
            conn_table->taskid = 0; /*default: using lwip atctrl main task*/
            conn_table->interto = 0;
            conn_table->loopcnt = 1;
            conn_table->selectto = LWIP_STTEST_SELECTTO_MIN_SIZE; /*set to 1000ms*/
            conn_table->selreadset = LWIP_STTEST_SELECTSET_ME;
            conn_table->selwriteset = LWIP_STTEST_SELECTSET_ME;
            conn_table->selerrorset = LWIP_STTEST_SELECTSET_NULL;

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_SELECT_RW:
        {        
            if (conn_table->status < LWIP_STTEST_CONN_CONNECTED) {
                ret = ERR_VAL;
                break;
            }

            /*reset select parameters for conn_table*/            
            conn_table->builddata_len = 0;
            conn_table->readdata_len = LWIP_STTEST_DATABUF_MAX_SIZE;
            conn_table->taskid = 0; /*default: using lwip atctrl main task*/
            conn_table->interto = 0;
            conn_table->loopcnt = 1;
            conn_table->rtaskid = LWIP_STTEST_INVALID_TASK_ID;
            conn_table->wtaskid = LWIP_STTEST_INVALID_TASK_ID;
            conn_table->selectto = LWIP_STTEST_SELECTTO_MIN_SIZE; /*set to 1000ms*/
            conn_table->selreadset = LWIP_STTEST_SELECTSET_NULL;
            conn_table->selwriteset = LWIP_STTEST_SELECTSET_NULL;
            conn_table->selerrorset = LWIP_STTEST_SELECTSET_NULL;

            break;
        }

        default:        
        {   
            ret = ERR_CONTINUE;
            break;
        }

    }

    return ret;
}

int lwip_sttest_env_buildup(int connid, int st_cmd, char *cfgmsg, int *taskid)
{
    int loop_id = 0;
    int key_count = 0;
    int int_val = 0;
    char *msg_next = NULL;
    char *msg_now = NULL;    
    struct lwip_sttest_conn_table_st * conn_table;
    struct lwip_sttest_task_table_st * task_table;
    int ret = ERR_OK;

    /*if null config, no need to do env buildup, using default.*/
    if (0 == strncmp(cfgmsg, "null", strlen("null"))) {
        return ret;
    }

    /*get connection table first*/
    conn_table = lwip_sttest_get_conn_table(connid);

    /*set preset first, conn_table->taskid update in env_checkup already*/
    *taskid = conn_table->taskid; 
    
    switch (st_cmd) {
        case LWIP_STTEST_CMD_SOCKET_PING:
        case LWIP_STTEST_CMD_SOCKET_WGET:
        case LWIP_STTEST_CMD_SOCKET_MQTT:            
        {

            ret = lwip_sttest_tagval_parser(connid, cfgmsg, taskid);
            if (ERR_OK == ret) {

                key_count = 1; /*avoid report err at the end*/
            }
            break;
        }

        case LWIP_STTEST_CMD_SOCKET_CREAT:
        {
            /* keytag | keyval format*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_CREAT_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "type", strlen("type"))) {
                    int iptype;
                    iptype = atol(keyval);
                    if (4 == iptype) {
                         conn_table->domain = AF_INET;
                     } else if (6 == iptype) {
                         conn_table->domain = AF_INET6;
                     } else {
                        ret = ERR_VAL;
                        break;             
                     }
                } else if (0 == strncmp(keytag, "proto", strlen("proto"))) {
                    if (0 == strncmp(keyval, "tcp", strlen("tcp"))) {
                        conn_table->sttype = SOCK_STREAM;
                    } else if (0 == strncmp(keyval, "udp", strlen("udp"))) {
                        conn_table->sttype = SOCK_DGRAM;
                    } else if (0 == strncmp(keyval, "raw", strlen("raw"))) {
                        conn_table->sttype = SOCK_RAW;
                    } else {
                        ret = ERR_VAL;
                        break;
                    }
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }            

        case LWIP_STTEST_CMD_SOCKET_BIND:
        {
            /* keytag | keyval format*/
            /*support ip4\ip6\port\null*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_BIND_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "port", strlen("port"))) {
                    u16_t localport = 0;
                    localport = atol(keyval);
                    if (localport == 0) {
                        ret = ERR_VAL;
                        break;
                    }
                    conn_table->localport = localport;
                } else if (0 == strncmp(keytag, "ip4", strlen("ip4"))) {
                    ip_addr_t ipaddr;
                    if (ipaddr_aton(keyval, &ipaddr)) {
                        memcpy(&conn_table->localipv4, &ipaddr, sizeof(ip_addr_t));
                    } else {
                        ret = ERR_VAL;
                        break;
                    }
                } else if (0 == strncmp(keytag, "ip6", strlen("ip6"))) {
                    ip6_addr_t ip6addr;
                    if (ip6addr_aton(keyval, &ip6addr)) {
                        memcpy(&conn_table->localipv6, &ip6addr, sizeof(ip6_addr_t));
                    } else {
                        ret = ERR_VAL;
                        break;
                    }
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_LISTEN:
        {
            /* keytag | keyval format*/
            /*support ip4\ip6\port\null*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_LISTEN_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "backlog", strlen("backlog"))) {
                    conn_table->backlog = atol(keyval);
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_CLOSE:
        case LWIP_STTEST_CMD_SOCKET_ACCEPT:
        {
            /* keytag | keyval format*/
            /*support ip4\ip6\port\null*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_ACCEPT_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "task", strlen("task"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->taskid = task_table->taskid; /*record it*/
                        *taskid = task_table->taskid;
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_CONNECT:
        {
            /* keytag | keyval format*/
            /*support host\task\[port\rport]\null*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_CONNECT_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if ((0 == strncmp(keytag, "port", strlen("port"))) 
                    || (0 == strncmp(keytag, "rport", strlen("rport")))) {
                    u16_t remoteport = 0;
                    remoteport = atol(keyval);
                    if (remoteport == 0) {
                        ret = ERR_VAL;
                        break;
                    }
                    conn_table->remoteport = remoteport;
                } else if (0 == strncmp(keytag, "host", strlen("host"))) {
                    if (strlen(keyval) > LWIP_STTEST_KEYVAL_SIZE) {
                        ret = ERR_VAL;
                        break;
                    }
                    memset(conn_table->hostname, 0, LWIP_STTEST_KEYVAL_SIZE);
                    memcpy(conn_table->hostname, keyval, sizeof(keyval));
                } else if (0 == strncmp(keytag, "task", strlen("task"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->taskid = task_table->taskid; /*record it*/
                        *taskid = task_table->taskid;
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_WRITE:        
        {
            /* keytag | keyval format*/
            /*support task\builddata\interto\loopcnt*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_WRITE_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}
				
                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "task", strlen("task"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->taskid = task_table->taskid; /*record it*/
                        *taskid = task_table->taskid;
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else if ((0 == strncmp(keytag, "len", strlen("len"))) || (0 == strncmp(keytag, "wlen", strlen("wlen")))) {
                    int_val = atol(keyval);
                    int_val = LWIP_MIN(int_val, LWIP_STTEST_MAX_MALLOC_SIZE);
                    conn_table->builddata_len = int_val;
                } else if (0 == strncmp(keytag, "inter", strlen("inter"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_INTERTO_MIN_SIZE;}
                    conn_table->interto = int_val;
                } else if (0 == strncmp(keytag, "loop", strlen("loop"))) {
                    int_val = atol(keyval);
                    if (int_val < 0) {int_val = 0;}
                    conn_table->loopcnt = int_val;
                } else {                
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_READ:
        {
            /* keytag | keyval format*/
            /*support task\rlen\inter\loop*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_READ_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "task", strlen("task"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->taskid = task_table->taskid; /*record it*/
                        *taskid = task_table->taskid;
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else if (0 == strncmp(keytag, "rlen", strlen("rlen"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_DATABUF_MAX_SIZE;}
                    conn_table->readdata_len = int_val;
                } else if (0 == strncmp(keytag, "inter", strlen("inter"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_INTERTO_MIN_SIZE;}
                    conn_table->interto = int_val;
                } else if (0 == strncmp(keytag, "loop", strlen("loop"))) {
                    int_val = atol(keyval);
                    if (int_val < 0) {int_val = 0;}
                    conn_table->loopcnt = int_val;
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        
        case LWIP_STTEST_CMD_SOCKET_ECHO:
        {
            /* keytag | keyval format*/
            /*support task\rlen\inter\loop*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_ECHO_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "task", strlen("task"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->taskid = task_table->taskid; /*record it*/
                        *taskid = task_table->taskid;
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else if (0 == strncmp(keytag, "inter", strlen("inter"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_INTERTO_MIN_SIZE;}
                    conn_table->interto = int_val;
                } else if (0 == strncmp(keytag, "loop", strlen("loop"))) {
                    int_val = atol(keyval);
                    if (int_val < 0) {int_val = 0;}
                    conn_table->loopcnt = int_val;
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_SELECT_ECHO:
        {
            /* keytag | keyval format*/
            /*support task\readse\writeset\errorset\selectto\inter\loop*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_SELECTECHO_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "task", strlen("task"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->taskid = task_table->taskid; /*record it*/
                        *taskid = task_table->taskid;
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else if (0 == strncmp(keytag, "selectto", strlen("selectto"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_SELECTTO_MIN_SIZE;}
                    conn_table->selectto = int_val;
                }  else if (0 == strncmp(keytag, "inter", strlen("inter"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_INTERTO_MIN_SIZE;}
                    conn_table->interto = int_val;
                } else if (0 == strncmp(keytag, "loop", strlen("loop"))) {
                    int_val = atol(keyval);
                    if (int_val < 0) {int_val = 0;}
                    conn_table->loopcnt = int_val;
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_SELECT_RW:
        {
            /* keytag | keyval format*/
            /*support task\rlen\wlen\readse\writeset\errorset\selectto\inter\loop*/
            char keytag [LWIP_STTEST_KEYTAG_SIZE];
            char keyval [LWIP_STTEST_KEYVAL_SIZE];
            msg_now  = cfgmsg;
            msg_next = NULL;
            
            /*key-value config msg parse*/
            for (loop_id = 0; loop_id < LWIP_STTEST_SELECTRW_KEYVAL_NUM; loop_id++) {
                /*get keytagk*/
                ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
                if (ret != 0) { break;}

                /*get keyval*/
                msg_now = msg_next;
                ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
                if (ret != 0) { break;}

                /*parse keytag | keyval*/
                if (0 == strncmp(keytag, "task", strlen("task"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->taskid = task_table->taskid; /*record it*/
                        *taskid = task_table->taskid;
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else if (0 == strncmp(keytag, "rtask", strlen("rtask"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->rtaskid = task_table->taskid; /*record it*/
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else if (0 == strncmp(keytag, "wtask", strlen("wtask"))) {
                    task_table = lwip_sttest_get_task_table(keyval);
                    if (task_table && (LWIP_TASK_PENDING == task_table->status)) {
                        conn_table->wtaskid = task_table->taskid; /*record it*/
                    } else {
                        ret = ERR_ARG;
                        break;
                    }
                } else if (0 == strncmp(keytag, "rlen", strlen("rlen"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_DATABUF_MAX_SIZE;}
                    conn_table->readdata_len = int_val;
                } else if ((0 == strncmp(keytag, "len", strlen("len"))) || (0 == strncmp(keytag, "wlen", strlen("wlen")))) {
                    int_val = atol(keyval);
                    int_val = LWIP_MIN(int_val, LWIP_STTEST_MAX_MALLOC_SIZE);
                    conn_table->builddata_len = int_val;
                } else if (0 == strncmp(keytag, "readset", strlen("readset"))) {
                    if (0 == strncmp(keyval, "all", strlen("all"))) {
                        conn_table->selreadset = LWIP_STTEST_SELECTSET_ALL;
                    } else if (0 == strncmp(keyval, "me", strlen("me"))) {
                        conn_table->selreadset = LWIP_STTEST_SELECTSET_ME;
                    } else {
                        conn_table->selreadset = LWIP_STTEST_SELECTSET_NULL;
                    }
                } else if (0 == strncmp(keytag, "writeset", strlen("writeset"))) {
                    if (0 == strncmp(keyval, "all", strlen("all"))) {
                        conn_table->selwriteset = LWIP_STTEST_SELECTSET_ALL;
                    } else if (0 == strncmp(keyval, "me", strlen("me"))) {
                        conn_table->selwriteset = LWIP_STTEST_SELECTSET_ME;
                    } else {
                        conn_table->selwriteset = LWIP_STTEST_SELECTSET_NULL;
                    }
                } else if (0 == strncmp(keytag, "errorset", strlen("errorset"))) {
                    if (0 == strncmp(keyval, "all", strlen("all"))) {
                        conn_table->selerrorset = LWIP_STTEST_SELECTSET_ALL;
                    } else if (0 == strncmp(keyval, "me", strlen("me"))) {
                        conn_table->selerrorset = LWIP_STTEST_SELECTSET_ME;
                    } else {
                        conn_table->selerrorset = LWIP_STTEST_SELECTSET_NULL;
                    }
                } else if (0 == strncmp(keytag, "selectto", strlen("selectto"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_SELECTTO_MIN_SIZE;}
                    conn_table->selectto = int_val;
                }  else if (0 == strncmp(keytag, "inter", strlen("inter"))) {
                    int_val = atol(keyval);
                    if (int_val == 0) {int_val = LWIP_STTEST_INTERTO_MIN_SIZE;}
                    conn_table->interto = int_val;
                } else if (0 == strncmp(keytag, "loop", strlen("loop"))) {
                    int_val = atol(keyval);
                    if (int_val < 0) {int_val = 0;}
                    conn_table->loopcnt = int_val;
                } else {
                    break;
                }

                memset(keytag, 0, sizeof(keytag));
                memset(keyval, 0, sizeof(keyval));
                msg_now = msg_next;
                key_count++;
            }

            break;
        }

        default:
        {
            break;
        }
    }

	ret = ERR_OK;
    if (key_count == 0) {
        ret = ERR_VAL;
    }

    return ret;
}

int lwip_sttest_env_error(int connid, int st_cmd)
{
    struct lwip_sttest_conn_table_st * conn_table;
    struct lwip_sttest_task_table_st * task_table;
    int ret = ERR_OK;

    switch (st_cmd) {
        case LWIP_STTEST_CMD_SOCKET_CREAT:
        {
            /* check wether exist this connection id */
            /* and check status */
            conn_table = lwip_sttest_get_conn_table(connid);
            if ((NULL != conn_table)
                && (LWIP_STTEST_CONN_CREATED == conn_table->status)) {
                lwip_sttest_delet_conn_table(connid);
            }
            break;
        }

        default:        
        {     
            break;
        }
    }

    return ret;
}



void lwip_sttest_free_conn(struct lwip_sttest_conn_table_st *conn_table)
{    
    if (conn_table == NULL) {
        return;
    }

    /*begin free conn table*/
   LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_1634, "%s: conn_table=%lx, connid is %d, status is %d.",  
                            __FUNCTION__, conn_table, conn_table->connid, conn_table->status);

   /************************/
    /* application level free */
    /************************/
   if (conn_table->httpclient) {
       /*release http resources*/
       http_client_shutdown(conn_table->httpclient);
   }

   /************************/
    /* socket level free */
    /************************/
   if (conn_table->sockid >= 0) {
       lwip_close(conn_table->sockid);
   }

    /************************/
    /* tcp/udp level free */
    /************************/


    /************************/
    /* raw data level free */
    /************************/
    if (conn_table->rawdata) {
        free(conn_table->rawdata);
    }

    /************************/
    /* write setting */
    /************************/    
    if (conn_table->builddata) {
        free(conn_table->builddata);
    }

    /************************/
    /* read setting */
    /************************/  
    if (conn_table->readdata) {
        free(conn_table->readdata);
    }

    /************************/
    /* table level free */
    /************************/ 
    free(conn_table);
}

int lwip_sttest_free_task(struct lwip_sttest_task_table_st *task_table)
{
	int ret = ERR_OK;
	int trytime  = 2;
    int exitflag = 0;
	
    if (task_table == NULL) {
        return ERR_OK;
    }

	ret = lwip_sttest_thread_exit(task_table->taskid);
	if (ERR_OK != ret) {
		LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_1686, "lwiperr: %s, thread exit post failed, ret is %d.", __FUNCTION__, ret);
		return ret;
	}

    /*try to get exit status*/
	do {
		if (task_table->status == LWIP_TASK_EXITING) {
            exitflag = 1;
			break;
		}
		sys_msleep(1000);
	} while (trytime-- > 0);

    if (exitflag == 0) {
        LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_1700, "lwiperr: %s, thread can not set to exit status.", __FUNCTION__);
        return ERR_CLSD;
    }

	/************************/
	/* delete  task */
	/************************/ 
	lwip_atctl_thread_delete(task_table->taskmsgq, task_table->taskref);
	
	/************************/
	/* table level free */
	/************************/ 
	free(task_table);
	
	LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_1714, "%s: thread exit success", __FUNCTION__);
	return ERR_OK;

}

int lwip_sttest_free_source(void)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_conn_table_st *conn_table_1st = NULL;
    struct lwip_sttest_conn_table_st *conn_table_free = NULL;
    struct lwip_sttest_task_table_st *task_table = NULL;
    int loop_id = 0;
    int ret = ERR_OK;

    if (0 == lwip_sttest_control.status) {
        return ERR_OK;
    }

    /*set status*/
    lwip_sttest_control.status = 0;

    /*free conn_table*/
    conn_table = lwip_sttest_control.conn_tablelist_head;
    conn_table_1st = conn_table;
    conn_table_free = conn_table;
    while (conn_table) {
        conn_table_free = conn_table;
        conn_table = conn_table->next;

        lwip_sttest_free_conn(conn_table_free);

        if (conn_table == conn_table_1st) {
            LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_1751, "lwiperr: %s, pointer loop happen.", __FUNCTION__);
            break;
        }
    }

    /*free task_table*/
    for (loop_id = 0; loop_id < LWIP_STTEST_TASK_MAX_COUNT; loop_id++) {
        task_table = lwip_sttest_control.task_tablelist[loop_id];
		ret = lwip_sttest_free_task(task_table);
        if (ret != ERR_OK) {
            break;
        }
    }    

    lwip_sttest_control.conn_tablelist_head = NULL;
    lwip_sttest_control.conn_tablelist_last = NULL;
    lwip_sttest_control.conn_tablelist_now = NULL;

    for (loop_id = 0; loop_id < LWIP_STTEST_TASK_MAX_COUNT; loop_id++) {
        lwip_sttest_control.task_tablelist[loop_id] = NULL;
    } 

    return ret;
}

int lwip_sttest_post_direct(int cmd_id, int conn_id, int task_id)
{
    struct lwip_thread_msgq_msg_st msg_post = {0};
    struct lwip_sttest_msgq_payload_st *payload = NULL;
    struct lwip_sttest_task_table_st *tasktable = NULL;
    int ret = ERR_OK;

    payload = (struct lwip_sttest_msgq_payload_st *)MALLOC(sizeof(struct lwip_sttest_msgq_payload_st));
    if (payload == NULL) {
        return ERR_MEM;
    }

    /*post to lwip_atctrl thread to process*/
    msg_post.msgID = LWIP_ATCTL_SOCKET_TEST_CMD;
    msg_post.print = NULL;
    msg_post.pArgs = (void *)payload;
    msg_post.run_entry = lwip_sttest_socket_process;

    payload->cmdid = cmd_id;
    payload->connid = conn_id;
    payload->taskid = task_id; /*denote main task*/

    /*denote main task*/
    if (0 == task_id) {
        ret = lwip_atctl_thread_post(lwip_atctl_msgq, &msg_post);
    } else {
        tasktable = lwip_sttest_control.task_tablelist[task_id - 1];
        ret = lwip_atctl_thread_post(tasktable->taskmsgq, &msg_post);        
    }

    /*error ret, end process, if needed*/
    if (ERR_OK != ret) {
        if (payload) {free(payload);}
    }

    return ret;        
}

int lwip_sttest_thread_exit(int task_id)
{
    struct lwip_thread_msgq_msg_st msg_post = {0};
    struct lwip_sttest_msgq_payload_st *payload = NULL;
    struct lwip_sttest_task_table_st *tasktable = NULL;
    int ret = ERR_OK;

    /*post to lwip_atctrl thread to process*/
    msg_post.msgID = LWIP_ATCTL_THREAD_EXIT_CMD;
    msg_post.print = NULL;
    msg_post.pArgs = NULL;
    msg_post.run_entry = NULL;

    tasktable = lwip_sttest_control.task_tablelist[task_id - 1];
	
    ret = lwip_atctl_thread_post(tasktable->taskmsgq, &msg_post);        

	LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_1823, "%s: ret is %d, tasktable->taskname is %s, task_id is %d.", 
										__FUNCTION__, ret, tasktable->taskname, task_id);

    return ret;   
}

err_t lwip_sttest_setopt_process(void * pArgs)
{
    struct lwip_sttest_msgq_payload_st *st_msg = NULL;    
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    char option [LWIP_STTEST_SOCKOPT_MAX_SIZE];
    char keytag [32];
    char keyval [16];        
    char *msg_next = NULL;
    char *msg_now = NULL;    
    int sock_id;
    int level;
    int optname;
    int optlen;
    int optv_int;
    void *optval;
    int ret = ERR_OK;

    st_msg = (struct lwip_sttest_msgq_payload_st *)pArgs;    
    if (NULL == st_msg || st_msg->option == NULL) {
        return ERR_VAL;
    }

    optlen = strlen(st_msg->option) + 1;
    if (optlen > sizeof(option)) {
        free(st_msg->option);
        return ERR_MEM;
    } else {
        memcpy(option, st_msg->option, optlen);
        free(st_msg->option);
    }

    conn_table = lwip_sttest_get_conn_table(st_msg->connid);
    if (conn_table == NULL) {
        return ERR_BUF;
    }
        
    /*parse option, get config tag, config val*/    
    msg_now  = option;
    msg_next = NULL;
    ret = lwip_atctl_parse_subcmd(msg_now, keytag, sizeof(keytag), &msg_next);
    if (ret != 0) { return ERR_ARG;}
    msg_now = msg_next;
    ret = lwip_atctl_parse_subcmd(msg_now, keyval, sizeof(keyval), &msg_next);                
    if (ret != 0) { return ERR_ARG;}

    /*build function input*/
    switch (st_msg->cmdid) {
        case LWIP_STTEST_CMD_SET_SOCK_OPTION:
        {
            level = SOL_SOCKET;

            /*parse keytag | keyval*/
            if (0 == strncmp(keytag, "SO_KEEPALIVE", strlen("SO_KEEPALIVE"))) {
                optname = SO_KEEPALIVE;
            } else if (0 == strncmp(keytag, "SO_REUSEADDR", strlen("SO_REUSEADDR"))) {
                optname = SO_REUSEADDR;
            } else if (0 == strncmp(keytag, "SO_RCVBUF", strlen("SO_RCVBUF"))) {
                optname = SO_RCVBUF;
            } else if (0 == strncmp(keytag, "SO_BINDTODEVICE", strlen("SO_BINDTODEVICE"))) {
                optname = SO_BINDTODEVICE;
            } else if (0 == strncmp(keytag, "SO_BROADCAST", strlen("SO_BROADCAST"))) {
                optname = SO_BROADCAST;
            } else if (0 == strncmp(keytag, "SO_REUSEPORT", strlen("SO_REUSEPORT"))) {
                optname = SO_REUSEPORT;
            } else if (0 == strncmp(keytag, "SO_IPSEC", strlen("SO_IPSEC"))) {
                optname = SO_IPSEC;
            } else if (0 == strncmp(keytag, "SO_FLOWCTRL", strlen("SO_FLOWCTRL"))) {
                optname = SO_FLOWCTRL;
            } else if (0 == strncmp(keytag, "SO_SNDTIMEO", strlen("SO_SNDTIMEO"))) {
                optname = SO_SNDTIMEO;
            } else if (0 == strncmp(keytag, "SO_RCVTIMEO", strlen("SO_RCVTIMEO"))) {
                optname = SO_RCVTIMEO;
            } else {
                ret = ERR_ARG;
                break;
            }

            break;
        }

        case LWIP_STTEST_CMD_SET_TCP_OPTION:
        {
            level = IPPROTO_TCP;

			/*parse keytag | keyval*/
            if (0 == strncmp(keytag, "TCP_NODELAY", strlen("TCP_NODELAY"))) {
                optname = TCP_NODELAY;
            } else if (0 == strncmp(keytag, "TCP_KEEPALIVE", strlen("TCP_KEEPALIVE"))) {
                optname = TCP_KEEPALIVE;
            } 
#if LWIP_TCP_KEEPALIVE
			  else if (0 == strncmp(keytag, "TCP_KEEPIDLE", strlen("TCP_KEEPIDLE"))) {
                optname = TCP_KEEPIDLE;
            } else if (0 == strncmp(keytag, "TCP_KEEPINTVL", strlen("TCP_KEEPINTVL"))) {
                optname = TCP_KEEPINTVL;
            } else if (0 == strncmp(keytag, "TCP_KEEPCNT", strlen("TCP_KEEPCNT"))) {
                optname = TCP_KEEPCNT;
            } 
#endif
			  else if (0 == strncmp(keytag, "TCP_TIMEROUT", strlen("TCP_TIMEROUT"))) {
                optname = TCP_TIMEROUT;
            } else if (0 == strncmp(keytag, "TCP_CLOSE_TIMEROUT", strlen("SO_BINDTODEVICE"))) {
                optname = TCP_CLOSE_TIMEROUT;
            } else {
                ret = ERR_ARG;
                break;
            }
			
            break;
        }

		case LWIP_STTEST_CMD_SET_IP_OPTION:
        {
            level = IPPROTO_IP;

            if (0 == strncmp(keytag, "IP_TTL", strlen("IP_TTL"))) {
                optname = IP_TTL;
            } else if (0 == strncmp(keytag, "IP_TOS", strlen("IP_TOS"))) {
                optname = IP_TOS;
        	} else {
                ret = ERR_ARG;
                break;
            }
			
            break;
        }
		
        default:
        {
            ret = ERR_CONTINUE; /*denote not support*/
            break;
        }
    }

    if (ERR_OK == ret) {
        /*for int opt val*/
        if ((optname == SO_KEEPALIVE)
            || (optname == SO_REUSEADDR)
            || (optname == SO_RCVBUF)
            || (optname == SO_BROADCAST)
            || (optname == SO_REUSEPORT)
            || (optname == SO_IPSEC)
            || (optname == SO_FLOWCTRL)            
            || (optname == SO_SNDTIMEO)
            || (optname == SO_RCVTIMEO)
            || (optname == IP_TTL)
            || (optname == IP_TOS)
            || (optname == TCP_NODELAY)
            || (optname == TCP_KEEPALIVE)
#if LWIP_TCP_KEEPALIVE
            || (optname == TCP_KEEPIDLE)
            || (optname == TCP_KEEPINTVL)
            || (optname == TCP_KEEPCNT)
#endif
            || (optname == TCP_TIMEROUT)
            || (optname == TCP_CLOSE_TIMEROUT)) {
            optv_int = atol(keyval);
            optval = &optv_int;
            optlen = sizeof(int);
        } else if(optname == SO_BINDTODEVICE) {
            /*for char* opt val*/
            optval = keyval;
			optlen = strlen(keyval) + 1;
        } else if((optname == SO_NBIO)
			|| (optname == SO_BIO)) {
			//do nothing
        } else {
            ret = ERR_CONTINUE; /*denote not support*/
		}
    }

    if (ERR_OK == ret) {
        sock_id = conn_table->sockid;
        ret = lwip_setsockopt(sock_id, level, optname, optval, optlen);
    }
    
    return ret;
}

err_t lwip_sttest_getopt_process(void * pArgs)
{
    struct lwip_sttest_msgq_payload_st *st_msg = NULL;    
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    char option [64];
    char keytag [32];	
    char keyval [16];
    char ind_buf [256];
    int ind_size = 0;
    char *msg_next = NULL;
    char *msg_now = NULL;    
    int sock_id;
    int level;
    int optname;
    int optlen;
    int optv_int;
    void *optval;
    int ret = ERR_OK;

	memset(keytag, 0, sizeof(keytag));	
	memset(keyval, 0, sizeof(keyval));	
	memset(ind_buf, 0, sizeof(ind_buf));

    st_msg = (struct lwip_sttest_msgq_payload_st *)pArgs;    
    if (NULL == st_msg || st_msg->option == NULL) {
        return ERR_VAL;
    }

    optlen = strlen(st_msg->option) + 1;
    if (optlen > sizeof(option)) {
        free(st_msg->option);
        return ERR_MEM;
    } else {
        memcpy(option, st_msg->option, optlen);
        free(st_msg->option);
    }

    conn_table = lwip_sttest_get_conn_table(st_msg->connid);
    if (conn_table == NULL) {
        return ERR_BUF;
    }
        
    /*parse option, get config tag*/    
    msg_now  = option;
    msg_next = NULL;
    ret = lwip_atctl_parse_subcmd(msg_now, keytag, sizeof(keytag), &msg_next);
    if (ret != 0) { return ERR_ARG;}

    /*build function input*/
    switch (st_msg->cmdid) {
        case LWIP_STTEST_CMD_GET_SOCK_OPTION:
        {
            level = SOL_SOCKET;

            /*parse keytag | keyval*/
            if (0 == strncmp(keytag, "SO_KEEPALIVE", strlen("SO_KEEPALIVE"))) {
                optname = SO_KEEPALIVE;
            } else if (0 == strncmp(keytag, "SO_REUSEADDR", strlen("SO_REUSEADDR"))) {
                optname = SO_REUSEADDR;
            } else if (0 == strncmp(keytag, "SO_RCVBUF", strlen("SO_RCVBUF"))) {
                optname = SO_RCVBUF;
            } else if (0 == strncmp(keytag, "SO_BROADCAST", strlen("SO_BROADCAST"))) {
                optname = SO_BROADCAST;
            } else if (0 == strncmp(keytag, "SO_REUSEPORT", strlen("SO_REUSEPORT"))) {
                optname = SO_REUSEPORT;
            } else if (0 == strncmp(keytag, "SO_IPSEC", strlen("SO_IPSEC"))) {
                optname = SO_IPSEC;
            } else if (0 == strncmp(keytag, "SO_FLOWCTRL", strlen("SO_FLOWCTRL"))) {
                optname = SO_FLOWCTRL;
            } else if (0 == strncmp(keytag, "SO_TYPE", strlen("SO_TYPE"))) {
                optname = SO_TYPE;
            } else if (0 == strncmp(keytag, "SO_SNDTIMEO", strlen("SO_SNDTIMEO"))) {
                optname = SO_SNDTIMEO;
            } else if (0 == strncmp(keytag, "SO_RCVTIMEO", strlen("SO_RCVTIMEO"))) {
                optname = SO_RCVTIMEO;
            } else {
                ret = ERR_ARG;
                break;
            }

            break;
        }

        case LWIP_STTEST_CMD_GET_TCP_OPTION:
        {
            level = IPPROTO_TCP;

			/*parse keytag | keyval*/
            if (0 == strncmp(keytag, "TCP_NODELAY", strlen("TCP_NODELAY"))) {
                optname = TCP_NODELAY;
            } else if (0 == strncmp(keytag, "TCP_KEEPALIVE", strlen("TCP_KEEPALIVE"))) {
                optname = TCP_KEEPALIVE;
            } 
#if LWIP_TCP_KEEPALIVE
			  else if (0 == strncmp(keytag, "TCP_KEEPIDLE", strlen("TCP_KEEPIDLE"))) {
                optname = TCP_KEEPIDLE;
            } else if (0 == strncmp(keytag, "TCP_KEEPINTVL", strlen("TCP_KEEPINTVL"))) {
                optname = TCP_KEEPINTVL;
            } else if (0 == strncmp(keytag, "TCP_KEEPCNT", strlen("TCP_KEEPCNT"))) {
                optname = TCP_KEEPCNT;
            } 
#endif
			  else if (0 == strncmp(keytag, "TCP_TIMEROUT", strlen("TCP_TIMEROUT"))) {
                optname = TCP_TIMEROUT;
            } else if (0 == strncmp(keytag, "TCP_MSS_VALUE", strlen("TCP_MSS_VALUE"))) {
                optname = TCP_MSS_VALUE;
            } else if (0 == strncmp(keytag, "TCP_RTO_VALUE", strlen("TCP_RTO_VALUE"))) {
                optname = TCP_RTO_VALUE;
            } else if (0 == strncmp(keytag, "TCP_TXB_REST", strlen("TCP_TXB_REST"))) {
                optname = TCP_TXB_REST;
            } else if (0 == strncmp(keytag, "TCP_TXB_ACKED", strlen("TCP_TXB_ACKED"))) {
                optname = TCP_TXB_ACKED;
            } else if (0 == strncmp(keytag, "TCP_CLOSE_TIMEROUT", strlen("SO_BINDTODEVICE"))) {
                optname = TCP_CLOSE_TIMEROUT;
            } else {
                ret = ERR_ARG;
                break;
            }
			
            break;
        }
		
		case LWIP_STTEST_CMD_GET_IP_OPTION:
		{
			level = IPPROTO_IP;

			/*parse keytag | keyval*/
			if (0 == strncmp(keytag, "IP_TTL", strlen("IP_TTL"))) {
				optname = IP_TTL;
			} else if (0 == strncmp(keytag, "IP_TOS", strlen("IP_TOS"))) {
				optname = IP_TOS;
			} else {
				ret = ERR_ARG;
				break;
			}
			
			break;
		}

        default:
        {
            ret = ERR_CONTINUE; /*denote not support*/
            break;
        }
    }

    if (ERR_OK == ret) {
        /*for int opt val*/
        if ((optname == SO_KEEPALIVE)
            || (optname == SO_REUSEADDR)
            || (optname == SO_RCVBUF)
            || (optname == SO_BROADCAST)
            || (optname == SO_REUSEPORT)
            || (optname == SO_IPSEC)
            || (optname == SO_FLOWCTRL)            
            || (optname == SO_TYPE)
            || (optname == SO_SNDTIMEO)
            || (optname == SO_RCVTIMEO)
	        || (optname == IP_TTL)
            || (optname == IP_TOS)
            || (optname == TCP_NODELAY)
            || (optname == TCP_KEEPALIVE)
#if LWIP_TCP_KEEPALIVE
            || (optname == TCP_KEEPIDLE)
            || (optname == TCP_KEEPINTVL)
            || (optname == TCP_KEEPCNT)
#endif
            || (optname == TCP_TIMEROUT)
            || (optname == TCP_MSS_VALUE)
            || (optname == TCP_RTO_VALUE)
            || (optname == TCP_TXB_REST)
            || (optname == TCP_TXB_ACKED)            
            || (optname == TCP_CLOSE_TIMEROUT)) {
            optval = &optv_int;
            optlen = sizeof(int);
        } else {
            ret = ERR_CONTINUE; /*denote not support*/
        }
    }

    if (ERR_OK == ret) {
        sock_id = conn_table->sockid;
        ret = lwip_getsockopt(sock_id, level, optname, optval, (socklen_t *)&optlen);
		if (ERR_OK == ret) {
			ind_size = snprintf(ind_buf, sizeof(ind_buf), "get %s option value is 0x%lx", keytag, *(int *)optval);
		}
	}

    /*print get value*/
    if (st_msg->print) {
		if(ind_size > 0) {
			st_msg->print(ind_buf);
		}
    }
    
    return ret;
}


err_t lwip_sttest_socket_process(void * pArgs)
{
    struct lwip_sttest_msgq_payload_st *st_msg = NULL;    
    struct lwip_sttest_conn_table_st *conn_table = NULL;    
    struct lwip_sttest_conn_table_st *accept_conn_table = NULL;
    struct sockaddr_in sin_res;
    struct sockaddr_in6 sin6_res;
    struct sockaddr *skaddr;
    socklen_t sklen;
    u32_t counter = 0;    
    void *data;
    int data_len;
    int sock_id;
    int accept_sockid;
    int accept_connid;
    int sel_sockid;
    int ipv4v6;
    int loopcnt;
    int loopidx;
    char ind_buf [256] = {0};
    int ind_size = 0;
    int cont_flag = 0;
    int call_ret;    
    int ret = ERR_OK;

    if (NULL == pArgs) {
        return ERR_VAL;
    }

	memset(ind_buf, 0, sizeof(ind_buf));

    st_msg = (struct lwip_sttest_msgq_payload_st *)pArgs;
    conn_table = lwip_sttest_get_conn_table(st_msg->connid);
    if (conn_table == NULL) {
        return ERR_BUF;
    }

    memset(&sin_res, 0, sizeof(struct sockaddr_in));
    memset(&sin6_res, 0, sizeof(struct sockaddr_in6));
	LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2222, "%s: connid is %d, status is %d.",  __FUNCTION__, conn_table->connid, conn_table->status);

    switch (st_msg->cmdid) {
        case LWIP_STTEST_CMD_SOCKET_CLOSE:
        {
            call_ret = lwip_close(conn_table->sockid);
            if (call_ret < 0) {
                ret = ERR_RTE;
            } else {
                conn_table->sockid = -1; /*return socket id*/
                conn_table->status = LWIP_STTEST_CONN_CLOSED;
                lwip_sttest_delet_conn_table(conn_table->connid);
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_CREAT:
        {
            call_ret = lwip_socket(conn_table->domain, conn_table->sttype, conn_table->stproto);
            if (call_ret < 0) {
                ret = ERR_RTE;
            } else {
                conn_table->sockid = call_ret; /*return socket id*/
                conn_table->status = LWIP_STTEST_CONN_CREATED;				
				ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: create socket for connid=%d, sockid=%d!",  
																conn_table->connid, conn_table->sockid);
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_BIND:
        {
            if (AF_INET6 == conn_table->domain) {                
                sin6_res.sin6_family = AF_INET6;
                sin6_res.sin6_port   = htons(conn_table->localport);
                sin6_res.sin6_len    = sizeof(struct sockaddr_in6);
                inet6_addr_from_ip6addr(&sin6_res.sin6_addr, &conn_table->localipv6);
                skaddr =  (struct sockaddr *)&sin6_res;
                sklen = sizeof(struct sockaddr_in6);
            } else {
                sin_res.sin_family  = AF_INET;
                sin_res.sin_port    = htons(conn_table->localport);                
                sin_res.sin_len     = sizeof(struct sockaddr_in);
                inet_addr_from_ipaddr(&sin_res.sin_addr, &conn_table->localipv4);
                skaddr =  (struct sockaddr *)&sin_res;
                sklen = sizeof(struct sockaddr_in);
            }

            sock_id = conn_table->sockid;
            call_ret = lwip_bind(sock_id, skaddr, sklen);
            if (call_ret < 0) {
                ret = ERR_RTE;
            } else {
                conn_table->status = LWIP_STTEST_CONN_BINDED;
				ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: bind socket for connid=%d, sockid=%d!",  
																conn_table->connid, conn_table->sockid);
            }

			break;
        }

        case LWIP_STTEST_CMD_SOCKET_LISTEN:
        {
            sock_id = conn_table->sockid;
            call_ret = lwip_listen(sock_id, conn_table->backlog);
            if (call_ret < 0) {
                ret = ERR_RTE;
            } else {
                conn_table->status = LWIP_STTEST_CONN_LISTENING;
				ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: listen socket for connid=%d, sockid=%d!",  
																conn_table->connid, conn_table->sockid);
            }

			break;
        }

        case LWIP_STTEST_CMD_SOCKET_ACCEPT:
        {
            sock_id = conn_table->sockid;
            conn_table->status = LWIP_STTEST_CONN_ACCEPTING;

            while(1) {
                if (conn_table->domain == AF_INET) {
                    call_ret = lwip_accept(sock_id, (struct sockaddr *)&sin_res, &sklen);
                } else {
                    call_ret = lwip_accept(sock_id, (struct sockaddr *)&sin6_res, &sklen);
                }

                if (call_ret < 0) {
                    ret = ERR_RTE;
                    conn_table->status = LWIP_STTEST_CONN_ERROR;
                    break;
                } else {
                    /*return new connection*/
                    accept_sockid = call_ret;
                    accept_connid = lwip_sttest_control.accept_conn_id;
                    call_ret = lwip_sttest_new_accept_conn_table(accept_connid, accept_sockid, conn_table);
                    if (call_ret != ERR_OK) {
                        ret = ERR_INPROGRESS;
                        conn_table->status = LWIP_STTEST_CONN_ERROR;
                        break;
                    }

                    /*@TBD, need call print function to print accept conneciont id*/
                    ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: for listen connection=%d, accept_connid=%d, accept_sockid=%d!",  
                                                                    conn_table->connid, accept_connid, accept_sockid);
                    
                    /*lwip_sttest_control.accept_conn_id add it*/
                    lwip_sttest_control.accept_conn_id++;                    
                }
            }

            break;
        }

        case LWIP_STTEST_CMD_SOCKET_CONNECT:
        {
            if (AF_INET6 == conn_table->domain) {				
                skaddr   = (struct sockaddr *)&sin6_res;
                sklen    = sizeof(struct sockaddr_in6);
                ipv4v6   = 6;
				call_ret = lwip_do_dns_query(NULL, conn_table->hostname, ipv4v6, skaddr);
				sin6_res.sin6_port = htons(conn_table->remoteport);
				sin6_res.sin6_len  = sizeof(struct sockaddr_in6);
                sin6_res.sin6_family = AF_INET6;
            } else {            
				skaddr	 = (struct sockaddr *)&sin_res;
                sklen    = sizeof(struct sockaddr_in);
                ipv4v6   = 4;
				call_ret = lwip_do_dns_query(NULL, conn_table->hostname, ipv4v6, skaddr);
				sin_res.sin_port = htons(conn_table->remoteport);
				sin_res.sin_len  = sizeof(struct sockaddr_in);
                sin_res.sin_family = AF_INET;
            }
            
            if (call_ret != ERR_OK) {
                ret = ERR_RTE;
                break;
            }
			
            sock_id = conn_table->sockid;
            call_ret = lwip_connect(sock_id, skaddr, sklen);
            if (call_ret < 0) {
                ret = ERR_CONN;
            } else {
                conn_table->status = LWIP_STTEST_CONN_CONNECTED;
				ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: connect socket for connid=%d, sockid=%d!",  
																conn_table->connid, conn_table->sockid);
            }

			break;
        }

        case LWIP_STTEST_CMD_SOCKET_WRITE:            
        {
            sock_id = conn_table->sockid;
            loopcnt = conn_table->loopcnt;
            counter = 0;

            /*prepare sending data*/
            if (0 == conn_table->builddata_len) {
                data = conn_table->rawdata;
                data_len = conn_table->rawdata_len;
            } else {
                /*need build data first*/
                if (conn_table->builddata) {
                    free(conn_table->builddata);
                    conn_table->builddata = NULL;
                }

                conn_table->builddata_len = LWIP_MAX(conn_table->builddata_len, 8); /*align 8*/
                conn_table->builddata = (char*) MALLOC(conn_table->builddata_len);
                if (NULL == conn_table->builddata) {
                    ret = ERR_MEM;
                    break;
                }

                data = conn_table->builddata;
                data_len = conn_table->builddata_len;
                lwip_sttest_build_testdata(data, data_len);
            }

            if (loopcnt == 0) { cont_flag = 1; }
            while ((conn_table->signal != LWIP_STTEST_SIG_STOP) 
                && (cont_flag || (loopcnt > 0))) {

                /*update ostick, if build test data*/
                if (conn_table->builddata_len > 0) {
                    *((u32_t *)data) = counter; /*fill counter*/
                }

                /*add for record counter*/
                counter++;

                call_ret = lwip_write(sock_id, data, data_len);
                if (call_ret < 0) {
                    ret = ERR_RTE;
                    break;
                } else {
                    conn_table->status = LWIP_STTEST_CONN_WRITING;
                    conn_table->writesize += call_ret;
                }

                sys_msleep(conn_table->interto);
                if (0 == cont_flag) {loopcnt--;}
            }

			ind_size = snprintf(ind_buf, sizeof(ind_buf), "socket write result: connection_id=%d, sockid=%d, total size=%d, counter=%lu, data=%lx, len=%ld!"NEWLINE,
																				conn_table->connid, sock_id, conn_table->writesize, counter, data, data_len);
            break;
        }

        case LWIP_STTEST_CMD_SOCKET_READ:            
        {
            sock_id = conn_table->sockid;
            loopcnt = conn_table->loopcnt;
            data     = conn_table->readdata;
            data_len = conn_table->readdata_len;

            if (loopcnt == 0) { cont_flag = 1; }
            while ((conn_table->signal != LWIP_STTEST_SIG_STOP) 
                && (cont_flag || (loopcnt > 0))) {

                /*initial buf first*/
                memset(data, 0, LWIP_STTEST_DATABUF_MAX_SIZE);
                
                call_ret = lwip_read(sock_id, data, data_len);
                if (call_ret < 0) {
                    ret = ERR_RTE;
                    break;
                } else {
                    conn_table->status = LWIP_STTEST_CONN_READING;                    
                    conn_table->readsize += call_ret;
                }

                sys_msleep(conn_table->interto);
                if (0 == cont_flag) {loopcnt--;}
            }

			ind_size = snprintf(ind_buf, sizeof(ind_buf), "socket read result: connection_id=%d, sockid=%d, read data size=%d!"NEWLINE,
																				conn_table->connid, sock_id, conn_table->readsize);
            break;
        }

        case LWIP_STTEST_CMD_SOCKET_ECHO:            
        {
            sock_id = conn_table->sockid;
            loopcnt = conn_table->loopcnt;
            data     = conn_table->readdata;
            data_len = conn_table->readdata_len;

            if (loopcnt == 0) { cont_flag = 1; }
            while ((conn_table->signal != LWIP_STTEST_SIG_STOP) 
                && (cont_flag || (loopcnt > 0))) {

                /*initial buf first*/
                memset(data, 0, LWIP_STTEST_DATABUF_MAX_SIZE);
                
                call_ret = lwip_read(sock_id, data, data_len);
                if (call_ret < 0) {
                    ret = ERR_RTE;
                    break;
                } else {
                    conn_table->status = LWIP_STTEST_CONN_READING;                    
                    conn_table->readsize += call_ret;

                    call_ret = lwip_write(sock_id, conn_table->readdata, conn_table->readsize);
                    if (call_ret < 0) {
                        ret = ERR_INPROGRESS;
                        break;
                    } else {
                        conn_table->status = LWIP_STTEST_CONN_WRITING;
                        conn_table->writesize += call_ret;
                    }
                }

                sys_msleep(conn_table->interto);
                if (0 == cont_flag) {loopcnt--;}
            }

			ind_size = snprintf(ind_buf, sizeof(ind_buf), "socket echo result: connection_id=%d, sockid=%d, read data size=%d, write data size=%d!"NEWLINE,
																				conn_table->connid, sock_id, conn_table->readsize, conn_table->writesize);
            break;
        }


        case LWIP_STTEST_CMD_SOCKET_SELECT_ECHO:            
        {
            sock_id = conn_table->sockid;
            loopcnt = conn_table->loopcnt;
            fd_set readset;
            fd_set *preadset;
            struct timeval tv = {0, 0};

            /*set tv from ms config*/
            tv.tv_sec = ((conn_table->selectto) / 1000U);
            tv.tv_usec = (((conn_table->selectto) % 1000U) * 1000U); 
            data     = conn_table->readdata;
            data_len = conn_table->readdata_len;

            if (loopcnt == 0) { cont_flag = 1; }
            while ((conn_table->signal != LWIP_STTEST_SIG_STOP) 
                && (cont_flag || (loopcnt > 0))) {

                /*preset fdset*/
                FD_ZERO(&readset);
                FD_SET(sock_id, &readset);
                preadset = &readset;

                call_ret = lwip_select(sock_id + 1, preadset, NULL, NULL, &tv);
                if (call_ret < 0) {
                    ret = ERR_RTE;
                    break;
                } else if (call_ret == 0) {                
                    ret = ERR_TIMEOUT;
                } else { /*call_ret > 0*/
                    memset(data, 0, LWIP_STTEST_DATABUF_MAX_SIZE);
                    call_ret = lwip_read(sock_id, data, data_len);
                    if (call_ret < 0) {
                        ret = ERR_INPROGRESS;
                        break;
                    } else {
                        conn_table->status = LWIP_STTEST_CONN_READING;                    
                        conn_table->readsize += call_ret;
						
                        call_ret = lwip_write(sock_id, conn_table->readdata, conn_table->readsize);
                        if (call_ret < 0) {
                            ret = ERR_INPROGRESS;
                            break;
                        } else {
                            conn_table->status = LWIP_STTEST_CONN_WRITING;
                            conn_table->writesize += call_ret;
                        }
                    }
                    
                }

                sys_msleep(conn_table->interto);
                if (0 == cont_flag) {loopcnt--;}
            }

			ind_size = snprintf(ind_buf, sizeof(ind_buf), "socket select echo result: connection_id=%d, sockid=%d, read data size=%d, write data size=%d!"NEWLINE,
																				conn_table->connid, sock_id, conn_table->readsize, conn_table->writesize);
            break;
        }

        case LWIP_STTEST_CMD_SOCKET_SELECT_RW:            
        {
            sock_id = conn_table->sockid;
            loopcnt = conn_table->loopcnt;
            fd_set readset;
            fd_set writeset;
            fd_set errorset;
            fd_set *preadset;
            fd_set *pwriteset;
            fd_set *perrorset;
            struct timeval tv = {0, 0};

            /*set tv from ms config*/
            tv.tv_sec = ((conn_table->selectto) / 1000U);
            tv.tv_usec = (((conn_table->selectto) % 1000U) * 1000U); 

            if (loopcnt == 0) { cont_flag = 1; }
            while ((conn_table->signal != LWIP_STTEST_SIG_STOP) 
                && (cont_flag || (loopcnt > 0))) {

                /*preset fdset*/
                FD_ZERO(&readset);
                FD_ZERO(&writeset);
                FD_ZERO(&errorset);
                
                /*set readset*/
                if (LWIP_STTEST_SELECTSET_ME == conn_table->selreadset) {
                    FD_SET(sock_id, &readset);
                    preadset = &readset;
                } else if (LWIP_STTEST_SELECTSET_ALL == conn_table->selreadset) {
                    for (loopidx = 0; loopidx < (sock_id + 1); loopidx++) {
                        FD_SET(loopidx, &readset);
                    }
                    preadset = &readset;
                } else {
                    preadset = NULL;
                }
                
                /*set writeset*/
                if (LWIP_STTEST_SELECTSET_ME == conn_table->selwriteset) {
                    FD_SET(sock_id, &writeset);
                    pwriteset = &writeset;
                } else if (LWIP_STTEST_SELECTSET_ALL == conn_table->selwriteset) {
                    for (loopidx = 0; loopidx < (sock_id + 1); loopidx++) {
                        FD_SET(loopidx, &writeset);
                    }
                    pwriteset = &writeset;
                } else {
                    pwriteset = NULL;
                }
                
                /*set errorset*/
                if (LWIP_STTEST_SELECTSET_ME == conn_table->selerrorset) {
                    FD_SET(sock_id, &errorset);
                    perrorset = &errorset;
                } else if (LWIP_STTEST_SELECTSET_ALL == conn_table->selerrorset) {
                    for (loopidx = 0; loopidx < (sock_id + 1); loopidx++) {
                        FD_SET(loopidx, &errorset);
                    }
                    perrorset = &errorset;
                } else {
                    perrorset = NULL;
                }

                call_ret = lwip_select(sock_id + 1, preadset, pwriteset, perrorset, &tv);
                if (call_ret < 0) {
                    ret = ERR_RTE;
                    break;
                } else if (call_ret == 0) {                
                    ret = ERR_INPROGRESS;
                    break;
                } else { /*call_ret > 0*/
                    /*check readset, if needed do lwip_read*/
                    if (preadset) {
                        sel_sockid = -1;
                        if ((LWIP_STTEST_SELECTSET_ME == conn_table->selreadset)
                            && (FD_ISSET(sock_id, preadset))) {
                            sel_sockid = sock_id;
                        } else { //for (LWIP_STTEST_SELECTSET_ALL == conn_table->selreadset)
                            for (loopidx = 0; loopidx < (sock_id + 1); loopidx++) {
                                if (FD_ISSET(loopidx, preadset)) {
                                    sel_sockid = loopidx;
                                    break;
                                }
                            }
                        }

                        if (sel_sockid == -1) {
                            ret = ERR_CONN;
                            break;
                        }

                        if ((conn_table->rtaskid != LWIP_STTEST_INVALID_TASK_ID)
                            && (conn_table->rtaskid != conn_table->taskid)) {
                            lwip_sttest_post_direct(LWIP_STTEST_CMD_SOCKET_READ, conn_table->connid, conn_table->rtaskid);
                        } else { /*do read operate here*/
                            /*do lwip_read*/
                            data     = conn_table->readdata;
                            data_len = conn_table->readdata_len;
                            memset(data, 0, LWIP_STTEST_DATABUF_MAX_SIZE);
                            call_ret = lwip_read(sel_sockid, data, data_len);
							if (call_ret > 0) {
								conn_table->readsize += call_ret;
							}
                        }
                        
                    }

                    /*check readset, if needed do lwip_read*/
                    if (pwriteset) {
                        sel_sockid = -1;
                        if ((LWIP_STTEST_SELECTSET_ME == conn_table->selwriteset)
                            && (FD_ISSET(sock_id, pwriteset))) {
                            sel_sockid = sock_id;
                        } else { // (LWIP_STTEST_SELECTSET_ALL == conn_table->selwriteset)
                            for (loopidx = 0; loopidx < (sock_id + 1); loopidx++) {
                                if (FD_ISSET(loopidx, pwriteset)) {
                                    sel_sockid = loopidx;
                                    break;
                                }
                            }
                        }

                        if (sel_sockid == -1) {
                            ret = ERR_CONN;
                            break;
                        }

                        if ((conn_table->wtaskid != LWIP_STTEST_INVALID_TASK_ID)
                            && (conn_table->wtaskid != conn_table->taskid)) {
                            lwip_sttest_post_direct(LWIP_STTEST_CMD_SOCKET_WRITE, conn_table->connid, conn_table->wtaskid);
                        } else { /*do write operate here*/
                            /*prepare sending data*/
                            if (0 == conn_table->builddata_len) {
                                data = conn_table->rawdata;
                                data_len = conn_table->rawdata_len;             
                            } else {
                                /*need build data first*/
                                if (conn_table->builddata) {
                                    free(conn_table->builddata);
                                    conn_table->builddata = NULL;
                                }
                            
                                conn_table->builddata_len = LWIP_MAX(conn_table->builddata_len, 8); /*align 8*/
                                conn_table->builddata = (char*) MALLOC(conn_table->builddata_len);
                                if (NULL == conn_table->builddata) {
                                    ret = ERR_MEM;
                                    break;
                                }
                            
                                data = conn_table->builddata;
                                data_len = conn_table->builddata_len;
                                lwip_sttest_build_testdata(data, data_len);
                            }

                            call_ret = lwip_write(sel_sockid, data, data_len);
							if (call_ret > 0) {
								conn_table->writesize += call_ret;
							}							
                        }
                    }

                }

                sys_msleep(conn_table->interto);
                if (0 == cont_flag) {loopcnt--;}
            }

			ind_size = snprintf(ind_buf, sizeof(ind_buf), "socket select rw result: connection_id=%d, sockid=%d, read data size=%d, write data size=%d!"NEWLINE,
																				conn_table->connid, sock_id, conn_table->readsize, conn_table->writesize);
            break;
        }

        default:
        {
            ret = ERR_CONTINUE; /*denote not support*/
            break;
        }
    }

_stsocket_procee_end:
	if (st_msg->print && (ind_size > 0)) {
		st_msg->print(ind_buf);
	}

    if (conn_table->signal == LWIP_STTEST_SIG_STOP) {
        conn_table->signal = LWIP_STTEST_SIG_STOP_FINISH;
    }

    return ret;
}

err_t lwip_sttest_stping_process(void * pArgs)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_task_table_st *task_table = NULL;
    struct lwip_sttest_msgq_payload_st *st_msg   = NULL;
    struct lwip_sttest_msgq_payload_st *payload  = NULL;
    
    char ind_buf [256] = {0};
    char* sock_option = NULL;
    int ind_size = 0;
    int interto = 0;
    int ret = ERR_OK;

    st_msg = (struct lwip_sttest_msgq_payload_st *)pArgs;
    if (NULL == st_msg 
        || LWIP_STTEST_CMD_SOCKET_PING != st_msg->cmdid) {
        return ERR_VAL;
    }

    conn_table = lwip_sttest_get_conn_table(st_msg->connid);
    if (conn_table == NULL) {
        return ERR_BUF;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2750, "%s: connid is %d, status is %d.",  __FUNCTION__, 
                                            conn_table->connid, conn_table->status, conn_table->taskid);

	memset(ind_buf, 0, sizeof(ind_buf));
    payload = (struct lwip_sttest_msgq_payload_st *)MALLOC(sizeof(struct lwip_sttest_msgq_payload_st));
    if (payload == NULL) {
        ret = ERR_MEM;
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2751, "lwiperr: %s,enter check failed!", __FUNCTION__);
        goto _stping_process_end;
    }

    /*for tcp keepalive ping*/
    if (SOCK_STREAM == conn_table->sttype) {
        payload->connid = conn_table->connid;
        payload->taskid = conn_table->taskid;
        payload->print  = st_msg->print;

        /*create socket*/
        payload->cmdid = LWIP_STTEST_CMD_SOCKET_CREAT;
        payload->option = NULL;
        ret = lwip_sttest_socket_process(payload);
        if (ERR_OK != ret) {
            ret = ERR_MEM;
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2760, "lwiperr: %s, create socket failed!", __FUNCTION__);
            goto _stping_process_end;
        } else {
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2761, "%s, create socket ok!", __FUNCTION__);
        }

        /*config keepalive option*/
        /*config keepidle time*/
        sock_option = (char *)MALLOC(LWIP_STTEST_SOCKOPT_MAX_SIZE);        
        if (sock_option == NULL) {
            ret = ERR_MEM;
            goto _stping_process_end;
        }

        interto = conn_table->interto / 1000; /*turn to s*/
        if (interto <= 0) {
            interto = 10; /*set 10s*/
        } else if (interto < 1) {
            interto = 1;
        }

        memset(sock_option, 0, LWIP_STTEST_SOCKOPT_MAX_SIZE);
        snprintf(sock_option, LWIP_STTEST_SOCKOPT_MAX_SIZE, "TCP_KEEPIDLE|%d", interto);

        payload->cmdid  = LWIP_STTEST_CMD_SET_TCP_OPTION;
        payload->option =  sock_option;        
        lwip_sttest_setopt_process(payload);

        /*config keepprobe inter*/
        sock_option = (char *)MALLOC(LWIP_STTEST_SOCKOPT_MAX_SIZE);        
        if (sock_option == NULL) {
            ret = ERR_MEM;
            goto _stping_process_end;
        }        

        interto = interto / 10; /*calc probe inter*/
        if (interto < 1) {
            interto = 1;
        }

        memset(sock_option, 0, LWIP_STTEST_SOCKOPT_MAX_SIZE);
        snprintf(sock_option, LWIP_STTEST_SOCKOPT_MAX_SIZE, "TCP_KEEPINTVL|%d", interto);

        payload->cmdid  = LWIP_STTEST_CMD_SET_TCP_OPTION;
        payload->option =  sock_option;        
        lwip_sttest_setopt_process(payload);

        /*config socket keepalive*/
        sock_option = (char *)MALLOC(LWIP_STTEST_SOCKOPT_MAX_SIZE);        
        if (sock_option == NULL) {
            ret = ERR_MEM;
            goto _stping_process_end;
        }        

        memset(sock_option, 0, LWIP_STTEST_SOCKOPT_MAX_SIZE);
        snprintf(sock_option, LWIP_STTEST_SOCKOPT_MAX_SIZE, "SO_KEEPALIVE|1");

        payload->cmdid  = LWIP_STTEST_CMD_SET_SOCK_OPTION;
        payload->option =  sock_option;        
        lwip_sttest_setopt_process(payload);

        /*bind socket*/
        payload->cmdid = LWIP_STTEST_CMD_SOCKET_BIND;
        ret = lwip_sttest_socket_process(payload);
        if (ERR_OK != ret) {
            ret = ERR_CONN;
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2762, "lwiperr: %s, bind socket failed!", __FUNCTION__);
            goto _stping_process_end;
        } else {
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2763, "%s, bind socket ok!", __FUNCTION__);        
        }

        /*connect socket*/
        payload->cmdid = LWIP_STTEST_CMD_SOCKET_CONNECT;
        ret = lwip_sttest_socket_process(payload);
        if (ERR_OK != ret) {
            ret = ERR_CONN;
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2764, "lwiperr: %s, connect socket failed!", __FUNCTION__);
            goto _stping_process_end;
        } else {
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2765, "%s, connect socket ok, begin to write...!", __FUNCTION__);        
        }

        /*send data*/
        while (conn_table->signal != LWIP_STTEST_SIG_STOP) {
            if (conn_table->interto <= 0) {
                sys_msleep(10000); /*10s*/
            } else if (conn_table->interto < 1000) {
                sys_msleep(1000); /*1s*/
            } else {
                sys_msleep(conn_table->interto);
            }
        }

        conn_table->signal = LWIP_STTEST_SIG_STOP_FINISH;
    }

    /*for UDP ping*/
    if (SOCK_DGRAM == conn_table->sttype) {
        payload->connid = conn_table->connid;
        payload->taskid = conn_table->taskid;
        payload->print  =  st_msg->print;

        /*create socket*/
        payload->cmdid = LWIP_STTEST_CMD_SOCKET_CREAT;
        ret = lwip_sttest_socket_process(payload);
        if (ERR_OK != ret) {
            ret = ERR_MEM;
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2772, "lwiperr: %s, create socket failed!", __FUNCTION__);
            goto _stping_process_end;
        } else {
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2773, "%s, create socket ok!", __FUNCTION__);
        }

        /*bind socket*/
        payload->cmdid = LWIP_STTEST_CMD_SOCKET_BIND;
        ret = lwip_sttest_socket_process(payload);
        if (ERR_OK != ret) {
            ret = ERR_CONN;
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2774, "lwiperr: %s, bind socket failed!", __FUNCTION__);
            goto _stping_process_end;
        } else {
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2775, "%s, bind socket ok!", __FUNCTION__);        
        }

        /*connect socket*/
        payload->cmdid = LWIP_STTEST_CMD_SOCKET_CONNECT;
        ret = lwip_sttest_socket_process(payload);
        if (ERR_OK != ret) {
            ret = ERR_CONN;
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2776, "lwiperr: %s, connect socket failed!", __FUNCTION__);
            goto _stping_process_end;
        } else {
            LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2777, "%s, connect socket ok, begin to write...!", __FUNCTION__);        
        }

        /*send data*/
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2778, "%s, write socket begin!", __FUNCTION__);
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: begin to write ...!");        
        if (st_msg->print && (ind_size > 0)) {
            st_msg->print(ind_buf);
            ind_size = 0; /*reset for next*/
        }

        payload->cmdid = LWIP_STTEST_CMD_SOCKET_WRITE;
        ret = lwip_sttest_socket_process(payload);        
    }

_stping_process_end:

    /*free source when loop decrease to zero*/
    if (conn_table->signal != LWIP_STTEST_SIG_STOP_FINISH) {

        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2779, "%s, free resource begin!", __FUNCTION__);

        /*free task, 0 denote main ctrl task, no need free*/
        if (conn_table->taskid > 0) {
            task_table = lwip_sttest_control.task_tablelist[conn_table->taskid - 1];
            ret = lwip_sttest_free_task(task_table);
            if (ret != ERR_OK) {
                LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2780, "%s, lwip_sttest_free_task failed!", __FUNCTION__);
            }
        }

        /*free conn table*/
        lwip_sttest_delet_conn_table(conn_table->connid);

    } else {
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2781, "%s, stping stopped by sthisr!", __FUNCTION__);
    }

    if (ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: faile process stping!");
    } else {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: process stping finish!");
    }

    if (st_msg->print && (ind_size > 0)) {
        st_msg->print(ind_buf);
        ind_size = 0; /*reset for next*/
    }

    if (payload) {
        free(payload);
    }

    if (sock_option) {
        free(sock_option);
    }

    return ret;
}

err_t lwip_sttest_stwget_process(void * pArgs)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_task_table_st *task_table = NULL;
    struct lwip_sttest_msgq_payload_st *st_msg   = NULL;

    char private_data[]="hello world!";
    int response_code;
    struct http_client * client = NULL;
    struct http_client_list * header = NULL;

    char ind_buf [256] = {0};
    int ind_size = 0;
    int ret = ERR_OK;

    st_msg = (struct lwip_sttest_msgq_payload_st *)pArgs;
    if (NULL == st_msg 
        || LWIP_STTEST_CMD_SOCKET_WGET != st_msg->cmdid) {
        return ERR_VAL;
    }

    conn_table = lwip_sttest_get_conn_table(st_msg->connid);
    if (conn_table == NULL) {
        return ERR_BUF;
    }

    memset(ind_buf, 0, sizeof(ind_buf));
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2790, "%s: connid=%d, status=%d, taskid=%d, begin http process",  
                                __FUNCTION__, conn_table->connid, conn_table->status, conn_table->taskid);

    /*http get process*/
    client = http_client_init();
    if (NULL == client) {
        ret = ERR_MEM;
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2791, "lwiperr: %s,http_client_init failed!", __FUNCTION__);        
        goto _stwget_process_end;
    }

    /*update client to connection table*/
    conn_table->httpclient = client;

    //http_client_setopt(client, HTTPCLIENT_OPT_PDP_CID, 0);              /*set PDP cid,if not set,using default PDP*/
    http_client_setopt(client, HTTPCLIENT_OPT_BIND_PORT, conn_table->localport);            /*set tcp src port,if not set,using random port*/
    http_client_setopt(client, HTTPCLIENT_OPT_URL, conn_table->hostname);    /*set URL:support http/https and ip4/ip6*/
    http_client_setopt(client, HTTPCLIENT_OPT_HTTP1_0, 0);              /*"0" is HTTP 1.1, "1" is HTTP 1.0*/
    http_client_setopt(client, HTTPCLIENT_OPT_RESPONSECB, lwip_sttest_wget_callback);     /*response callback*/
    http_client_setopt(client, HTTPCLIENT_OPT_RESPONSECB_DATA, private_data);           /*set user private data,*/
    http_client_setopt(client, HTTPCLIENT_OPT_METHOD, HTTPCLIENT_REQUEST_GET);  /*set method,support GET/POST/PUT*/
    http_client_setopt(client, HTTPCLIENT_OPT_POSTDATA, NULL);          /*post data is http context*/
    http_client_setopt(client, HTTPCLIENT_OPT_POSTLENGTH, 0);           /*http context length*/
    //http_client_setopt(client, HTTPCLIENT_OPT_AUTH_TYPE, HTTP_AUTH_TYPE_BASE);  /*auth type support base and digest*/
    //http_client_setopt(client, HTTPCLIENT_OPT_AUTH_USERNAME, "admin");           /*username*/
    //http_client_setopt(client, HTTPCLIENT_OPT_AUTH_PASSWORD, "admin");           /*password*/
#if 0
    // Add private HTTP header
    header = http_client_list_append(header, "Content-Type: text/xml;charset=UTF-8\r\n");
    header = http_client_list_append(header, "SOAPAction:\r\n");
#endif
    /*set http private header,our http stack already support Referer/Host/Authorization/User-Agent/Connection/cookie/Content-Length,
    this header is other http header,ex: Content-Type..., we call it private header,*/
    http_client_setopt(client, HTTPCLIENT_OPT_HTTPHEADER, header);

    http_client_getinfo(client, HTTPCLIENT_GETINFO_TCP_STATE, &response_code);
    if (response_code != HTTP_TCP_ESTABLISHED) {
        ret = ERR_INPROGRESS;
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2792, "lwiperr: %s, http tcp not connecting", __FUNCTION__);
        goto _stwget_process_end;
    }

    response_code=http_client_perform(client);    /*execute http send and recv*/
    if (response_code != HTTP_CLIENT_OK) {
        ret = ERR_INPROGRESS;
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2793, "lwiperr: %s, http perform have some wrong[%d]", __FUNCTION__, response_code);
        goto _stwget_process_end;
    }

    http_client_getinfo(client, HTTPCLIENT_GETINFO_RESPONSE_CODE, &response_code);
    if (response_code != 200 && response_code != 204) {
        ret = ERR_INPROGRESS;
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2794, "lwiperr: %s, response is not 'HTTP OK'(%d)", __FUNCTION__, response_code);
    }

#if 0
    // Free private HTTP header
    if (header) {
        http_client_list_destroy(header);
        header = NULL;
    }
#endif   

_stwget_process_end:

    http_client_shutdown(client); /*release http resources*/
    conn_table->httpclient = NULL;
    LWIP_FLAGS_HOTBIT_CLEAR(lwip_sttest_control.flags, LWIP_STTEST_SYSCTRL_WGET_FLAG);

    /*if config stop signal, need set stop_finish here, avoid free source again*/
    if (conn_table->signal == LWIP_STTEST_SIG_STOP) {
        conn_table->signal = LWIP_STTEST_SIG_STOP_FINISH;
    }

    /*free source when loop decrease to zero*/
    if (conn_table->signal != LWIP_STTEST_SIG_STOP_FINISH) {

        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2795, "%s, free resource begin!", __FUNCTION__);

        /*free task, 0 denote main ctrl task, no need free*/
        if (conn_table->taskid > 0) {
            task_table = lwip_sttest_control.task_tablelist[conn_table->taskid - 1];
            ret = lwip_sttest_free_task(task_table);
            if (ret != ERR_OK) {
                LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2796, "lwiperr: %s, lwip_sttest_free_task failed", __FUNCTION__);
            }
        }

        /*free conn table*/
        lwip_sttest_delet_conn_table(conn_table->connid);
    } else {
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2797, "%s, stopped by stsignal!", __FUNCTION__);
    }

    if (ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: stwget process failed!");
    } else {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: process stwget finish!");
    }

    if (st_msg->print && (ind_size > 0)) {
        st_msg->print(ind_buf);
    }

    return ret;

}

err_t lwip_sttest_stmqtt_process(void * pArgs)
{
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    struct lwip_sttest_task_table_st *task_table = NULL;
    struct lwip_sttest_msgq_payload_st *st_msg   = NULL;

    char ind_buf [256] = {0};
    int ind_size = 0;
    int ret = ERR_OK;

    st_msg = (struct lwip_sttest_msgq_payload_st *)pArgs;
    if (NULL == st_msg 
        || LWIP_STTEST_CMD_SOCKET_MQTT != st_msg->cmdid) {
        return ERR_VAL;
    }

    conn_table = lwip_sttest_get_conn_table(st_msg->connid);
    if (conn_table == NULL) {
        return ERR_BUF;
    }

    memset(ind_buf, 0, sizeof(ind_buf));
    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2800, "%s: connid=%d, status=%d, taskid=%d, begin mqtt test",  
                                __FUNCTION__, conn_table->connid, conn_table->status, conn_table->taskid);


    mqtt_sample();

_stmqtt_process_end:

#if 0
    LWIP_FLAGS_HOTBIT_CLEAR(lwip_sttest_control.flags, LWIP_STTEST_SYSCTRL_WGET_FLAG);
#endif
    /*if config stop signal, need set stop_finish here, avoid free source again*/
    if (conn_table->signal == LWIP_STTEST_SIG_STOP) {
        conn_table->signal = LWIP_STTEST_SIG_STOP_FINISH;
    }

    /*free source when loop decrease to zero*/
    if (conn_table->signal != LWIP_STTEST_SIG_STOP_FINISH) {

        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2801, "%s, free resource begin!", __FUNCTION__);

        /*free task, 0 denote main ctrl task, no need free*/
        if (conn_table->taskid > 0) {
            task_table = lwip_sttest_control.task_tablelist[conn_table->taskid - 1];
            ret = lwip_sttest_free_task(task_table);
            if (ret != ERR_OK) {
                LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2802, "lwiperr: %s, lwip_sttest_free_task failed", __FUNCTION__);
            }
        }

        /*free conn table*/
        lwip_sttest_delet_conn_table(conn_table->connid);
    } else {
        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2803, "%s, stopped by stsignal!", __FUNCTION__);
    }

    if (ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: stmqtt process failed!");
    } else {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: process stmqtt finish!");
    }

    if (st_msg->print && (ind_size > 0)) {
        st_msg->print(ind_buf);
    }

    return ret;

}

static void lwip_sttest_task_common(int taskid)
{
    struct lwip_thread_msgq_msg_st msg = {0, NULL, NULL, NULL};
    char ind_buf [256] = {0};
    int ind_size = 0;
    int task_cnt = taskid - 1;
    int ret = ERR_OK;

    /*main execute*/
    while(1)
    {
		/*set task status to pending:*/
    	lwip_sttest_control.task_tablelist[task_cnt]->status = LWIP_TASK_PENDING;	

        /*now pend for ever to msgq*/
        OSAMsgQRecv(lwip_sttest_control.task_tablelist[task_cnt]->taskmsgq, 
                    (void *)&msg, 
                    sizeof(struct lwip_thread_msgq_msg_st), 
                    OSA_SUSPEND);

		/*set task status to runing:*/
    	lwip_sttest_control.task_tablelist[task_cnt]->status = LWIP_TASK_RUNING;

		LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2870, "%s: begin, taskid=%d, msgID=%d, pArgs=%lx, run_entry=%lx.", 
                                __FUNCTION__, taskid, msg.msgID, msg.pArgs, msg.run_entry);

		/*break while*/
		if (LWIP_ATCTL_THREAD_EXIT_CMD == msg.msgID) {
			goto _sttest_task_exit;
		}
		
        /*do other cmd process here*/
        switch (msg.msgID) {
            case LWIP_ATCTL_SOCKET_TEST_CMD:
            {
                if (NULL == msg.run_entry || NULL == msg.pArgs) {

                    ret = ERR_VAL;
                } else {
                    ret = msg.run_entry(msg.pArgs);
                }
                break;
            }
			
            default:
            {
                ret = ERR_VAL; /*denote not support*/
                break;
            }
        }
		
        if (ERR_OK == ret) {
            ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCESS: %s, taskid=%d, process end.", __FUNCTION__, taskid);
        } else {
            LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2871, "%s: FAILED, taskid=%d, ret=%d, msgID=%d, pArgs=%lx, run_entry=%lx.", 
                                    __FUNCTION__, taskid, ret, msg.msgID, msg.pArgs, msg.run_entry);

            ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: %s, taskid=%d, process end, ret=%d.", __FUNCTION__, taskid, ret);
        }

		if (msg.print && (ind_size > 0)) {
			msg.print(ind_buf);
		}

		if (msg.pArgs) {			
			free(msg.pArgs);
		}

		msg.msgID = 0;
		msg.print = NULL;
		msg.pArgs = NULL;
		ind_size = 0;
		memset(ind_buf, 0, sizeof(ind_buf));
    }

_sttest_task_exit:
	/*set task status to exiting:*/
	lwip_sttest_control.task_tablelist[task_cnt]->status = LWIP_TASK_EXITING;
	LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2872, "%s, thread exit sucess!", __FUNCTION__);  

}

void lwip_sttest_task1_entry(void)
{
    lwip_sttest_task_common(1);
}


void lwip_sttest_task2_entry(void)
{    
    lwip_sttest_task_common(2);
}



void lwip_sttest_task3_entry(void)
{    
    lwip_sttest_task_common(3);
}



void lwip_sttest_task4_entry(void)
{    
    lwip_sttest_task_common(4);
}

void lwip_sttest_help_query(printCallback cb, char * help)
{
    char ind_buf [1024] = {0};
    int ind_size = 0;
 
    if (cb == NULL) {
        return;
    }

    ind_size = snprintf(ind_buf, sizeof(ind_buf), "%s", help);
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }
}

static void lwip_atctl_stdebug_common(int cmd_id, char *msg, int tag, printCallback cb)
{
    char ind_buf [256] = {0};
    int ind_size = 0;   
    int conn_id = 0;
    int task_id = 0;
    struct lwip_sttest_task_table_st *tasktable = NULL;
    int ret = ERR_OK;

    if (msg == NULL || 0 == lwip_sttest_control.status) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: socket test not initial!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2880, "lwiperr: %s,socket test not initial!", __FUNCTION__);
        goto _atctl_stdebug_end;
    }

    lwip_sttest_control.print = cb;
	if (0 == strncmp(msg, "triggerselect", strlen("triggerselect"))) {
        task_id = tag;
        tasktable = lwip_sttest_get_task_table_byID(task_id);
        lwip_trigger_process("select", tasktable->taskref);
    } else {
        ret = ERR_ARG;
    }

    if (ERR_OK == ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: process stdebug sucess");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2881, "lwipst: %s, process end!", __FUNCTION__);        
    } else {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: process stdebug failed(ret=%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2882, "lwiperr: %s, failed(ret=%d)!", __FUNCTION__, ret);
    }

_atctl_stdebug_end:
    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;        
}

static void lwip_atctl_stsetopt_common(int cmd_id, char *msg, int tag, printCallback cb)
{
    char ind_buf [256] = {0};
    int ind_size = 0;   
    int conn_id = 0;
    int option_len = 0;
    char *option = NULL;
    struct lwip_thread_msgq_msg_st msg_post = {0};
    struct lwip_sttest_msgq_payload_st *payload = NULL;
    struct lwip_sttest_task_table_st *tasktable = NULL;
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    int ret = ERR_OK;

    if (msg == NULL || 0 == lwip_sttest_control.status) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: socket test not initial!");		
		LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2890, "lwiperr: %s,socket test not initial!", __FUNCTION__);
        goto _atctl_stsetopt_end;
    }

    lwip_sttest_control.print = cb;
    payload = (struct lwip_sttest_msgq_payload_st *)MALLOC(sizeof(struct lwip_sttest_msgq_payload_st));
    if (payload == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: malloc payload failed!");		
		LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2891, "lwiperr: %s,malloc payload failed!", __FUNCTION__);
        goto _atctl_stsetopt_end;
    }

    option_len = strlen(msg) + 1;
    option = (char *)MALLOC(option_len);
    if (option == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: malloc option string failed!");		
		LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2892, "lwiperr: %s,malloc option string failed!", __FUNCTION__);
		ret = ERR_VAL;
		goto _atctl_stsetopt_end;
    }

    conn_id = tag;
    memcpy(option, msg, option_len);
    
    /* check wether exist this connection id */
    /* and check status */
    conn_table = lwip_sttest_get_conn_table(conn_id);
    if ((NULL == conn_table)
        || (conn_table->status < LWIP_STTEST_CONN_CREATED)) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: env checkup failed(ret=%d)!", ret);		
		LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_2893, "lwiperr: %s,env checkup failed(ret=%d)!", __FUNCTION__, ret);
		ret = ERR_VAL;
		goto _atctl_stsetopt_end;
    }

    /*post to lwip_atctrl thread to process*/
    msg_post.msgID = LWIP_ATCTL_SOCKET_TEST_CMD;
    msg_post.print = cb;
    msg_post.pArgs = (void *)payload;
    msg_post.run_entry = lwip_sttest_setopt_process;

    payload->cmdid = cmd_id;
    payload->connid = conn_id;
    payload->taskid = 0; /*denote main task*/
    payload->option = option;
    payload->print = cb;

    /*denote main task*/
    ret = lwip_atctl_thread_post(lwip_atctl_msgq, &msg_post);

    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: lwip_atctl_thread_post failed(ret=%d)!", ret);		
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2894, "lwiperr: %s,lwip_atctl_thread_post failed(ret=%d)!", __FUNCTION__, ret);
        goto _atctl_stsetopt_end;          
    }    

    /*last, denote process success*/	
    ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: %s, tag=%d, ready!",  msg, tag);

_atctl_stsetopt_end:
    /*error ret, end process, if needed*/
    if (ERR_OK != ret) {
        if (option) {free(option);}
        if (payload) {free(payload);}
    }

    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;        

}

static void lwip_atctl_stgetopt_common(int cmd_id, char *msg, int tag, printCallback cb)
{
    char ind_buf [256] = {0};
    int ind_size = 0;   
    int conn_id = 0;
    int option_len = 0;
    char *option = NULL;
    struct lwip_thread_msgq_msg_st msg_post = {0};
    struct lwip_sttest_msgq_payload_st *payload = NULL;
    struct lwip_sttest_task_table_st *tasktable = NULL;
    struct lwip_sttest_conn_table_st *conn_table = NULL;
    int ret = ERR_OK;

    if (msg == NULL || 0 == lwip_sttest_control.status) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: socket test not initial!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2932, "lwiperr: %s,socket test not initial!", __FUNCTION__);
        goto _atctl_stgetopt_end;
    }

    lwip_sttest_control.print = cb;
    payload = (struct lwip_sttest_msgq_payload_st *)MALLOC(sizeof(struct lwip_sttest_msgq_payload_st));
    if (payload == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: malloc payload failed!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2939, "lwiperr: %s,malloc payload failed!", __FUNCTION__);
        goto _atctl_stgetopt_end;
    }

    option_len = strlen(msg) + 1;
    option = (char *)MALLOC(option_len);
    if (option == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: malloc option string failed!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2947, "lwiperr: %s,malloc option string failed!", __FUNCTION__);
		ret = ERR_VAL;
		goto _atctl_stgetopt_end;
    }

    conn_id = tag;
    memcpy(option, msg, option_len);
    
    /* check wether exist this connection id */
    /* and check status */
    conn_table = lwip_sttest_get_conn_table(conn_id);
    if ((NULL == conn_table)
        || (conn_table->status < LWIP_STTEST_CONN_CREATED)) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: env checkup failed(ret=%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2961, "lwiperr: %s,env checkup failed(ret=%d)!", __FUNCTION__, ret);
		ret = ERR_VAL;
		goto _atctl_stgetopt_end;
    }

    /*post to lwip_atctrl thread to process*/
    msg_post.msgID = LWIP_ATCTL_SOCKET_TEST_CMD;
    msg_post.print = cb;
    msg_post.pArgs = (void *)payload;
    msg_post.run_entry = lwip_sttest_getopt_process;

    payload->cmdid = cmd_id;
    payload->connid = conn_id;
    payload->taskid = 0; /*denote main task*/
    payload->option = option;
    payload->print = cb;

    /*denote main task*/
    ret = lwip_atctl_thread_post(lwip_atctl_msgq, &msg_post);

    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: lwip_atctl_thread_post failed(ret=%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2983, "lwiperr: %s,lwip_atctl_thread_post failed(ret=%d)!", __FUNCTION__, ret);
        goto _atctl_stgetopt_end;          
    }    

    /*last, denote process success*/
    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_2988, "SUCCESS: %s, tag=%d, ready!",  msg, tag);

_atctl_stgetopt_end:
    /*error ret, end process, if needed*/
    if (ERR_OK != ret) {
        if (option) {free(option);}
        if (payload) {free(payload);}
    }

    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;        

}


static void lwip_atctl_stsocket_common(int cmd_id, char *msg, int tag, printCallback cb)
{
    char ind_buf [256] = {0};
    int ind_size = 0;   
    int conn_id = 0;
    int task_id = 0;
    struct lwip_thread_msgq_msg_st msg_post = {0};
    struct lwip_sttest_msgq_payload_st *payload = NULL;
    struct lwip_sttest_task_table_st *tasktable = NULL;
    int ret = ERR_OK;

    if (msg == NULL || 0 == lwip_sttest_control.status) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: socket test not initial!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3020, "lwiperr: %s,socket test not initial!", __FUNCTION__);
        goto _atctl_stcommon_end;
    }

    lwip_sttest_control.print = cb;
    payload = (struct lwip_sttest_msgq_payload_st *)MALLOC(sizeof(struct lwip_sttest_msgq_payload_st));
    if (payload == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: enter check failed!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3027, "lwiperr: %s,enter check failed!", __FUNCTION__);
        goto _atctl_stcommon_end;
    }

    conn_id = tag;
    ret = lwip_sttest_env_checkup(conn_id, cmd_id);
    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: env checkup failed(ret=%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3035, "lwiperr: %s,env checkup failed(ret=%d)!", __FUNCTION__, ret);
        goto _atctl_stcommon_err;
    }

    /*socket test env buildup*/
    ret = lwip_sttest_env_buildup(conn_id, cmd_id, msg, &task_id);
    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: lwip_sttest_env_buildup failed(ret=%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3043, "lwiperr: %s,lwip_sttest_env_buildup failed(ret=%d)!", __FUNCTION__, ret);
        goto _atctl_stcommon_err;          
    }

    /*post to lwip_atctrl thread to process*/
    msg_post.msgID = LWIP_ATCTL_SOCKET_TEST_CMD;
    msg_post.print = cb;
    msg_post.pArgs = (void *)payload;
    if (LWIP_STTEST_CMD_SOCKET_PING == cmd_id) {
        msg_post.run_entry = lwip_sttest_stping_process;
        lwip_sttest_control.ping_conn_id = conn_id;
    } else if (LWIP_STTEST_CMD_SOCKET_WGET == cmd_id) {
        msg_post.run_entry = lwip_sttest_stwget_process;
        lwip_sttest_control.wget_conn_id = conn_id;
    } else if (LWIP_STTEST_CMD_SOCKET_MQTT == cmd_id) {
        msg_post.run_entry = lwip_sttest_stmqtt_process;
        //lwip_sttest_control.wget_conn_id = conn_id;
    } else {
        msg_post.run_entry = lwip_sttest_socket_process;
    }
    
    payload->cmdid = cmd_id;
    payload->connid = conn_id;
    payload->taskid = task_id; /*denote main task*/
    payload->option = NULL;
    payload->print = cb;
	LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3058, "%s: connid is %d, taskid is %d.",  __FUNCTION__, conn_id, task_id);

    /*denote main task*/
    if (0 == task_id) {
        ret = lwip_atctl_thread_post(lwip_atctl_msgq, &msg_post);
    } else {
        tasktable = lwip_sttest_control.task_tablelist[task_id - 1];
        ret = lwip_atctl_thread_post(tasktable->taskmsgq, &msg_post);        
    }

    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: lwip_atctl_thread_post failed(ret=%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3070, "lwiperr: %s,lwip_atctl_thread_post failed(ret=%d)!", __FUNCTION__, ret);
        goto _atctl_stcommon_err;          
    }    

    /*last, denote process success*/
    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3075, "SUCCESS: %s, tag=%d, ready!",  msg, tag);
    ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: %s, tag=%d, ready!",  msg, tag);

_atctl_stcommon_err:
    /*error ret, end process, if needed*/
    if (ERR_OK != ret) {
        lwip_sttest_env_error(conn_id, cmd_id);
        if (payload) {free(payload);}
    }

_atctl_stcommon_end:
    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;        
}


/*******************************************************
** socket finish at command:
**  at*lwipctrl=stfinish
********************************************************/
void lwip_atctl_stfinish_query(printCallback cb)
{
    char ind_buf [256] = {0};
    int ind_size = 0;   
    err_t ret = ERR_OK;

    ret = lwip_sttest_free_source();
    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: lwip_atctl_stfinish_process failed, ret(%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3107, "lwiperr: lwip_atctl_stfinish_process failed, ret(%d)!", ret);
    } else {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: lwip_atctl_stfinish_process success!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3110, "lwip_atctl_stfinish_process success!");
    }

    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }
}

void lwip_atctl_stfinish_process(char *msg, int tag, printCallback cb)
{
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3115, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);
    lwip_atctl_stfinish_query(cb);
}

void lwip_atctl_stinit_query(printCallback cb)
{
    char ind_buf [256] = {0};
    int ind_size = 0;   
    err_t ret = ERR_OK;
   
    ret = lwip_sttest_initial();
    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: lwip_sttest_initial failed, ret(%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3122, "lwiperr: %s,lwip_sttest_initial failed, ret(%d)!", __FUNCTION__, ret);
    } else {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: lwip_sttest_initial success!");
    }

    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

}

void lwip_atctl_stinit_process(char *msg, int tag, printCallback cb)
{
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3135, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);   
    lwip_atctl_stinit_query(cb);
}

/*******************************************************
** stsetsys at query:
**  at*lwipctrl=stsetsys
********************************************************/
void lwip_atctl_stsetsys_query(printCallback cb)
{
    char help[] =  "help for stsetsys:"NEWLINE"\
    format: "NEWLINE"\
        (1) at*lwipctrl=stsetsys,keytag|keyval,connection id"NEWLINE"\
        (2) at*lwipctrl=stsetsys,task|taskname,priority"NEWLINE"\
    support keytag, tag list as: all keytag|keyval support"NEWLINE"\
    examples:"NEWLINE"\
    at*lwipctrl=stsetsys,task|task_read,0 ---- create task named task_read, use default priority."NEWLINE"\
    at*lwipctrl=stsocket,ip4|*************,0  ---- set local ip4 addr, for connection 0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stsetsys_process(char *msg, int tag, printCallback cb)
{   
    char ind_buf [256] = {0};
    int ind_size = 0;   
    int ret = ERR_OK;

    if (msg == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: enter check failed!");
        goto _st_setsys_end;
    }

    if (0 == lwip_sttest_control.status) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: socket test not initial!");
        goto _st_setsys_end;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3185, "%s, msg=%s, conn_id=%d", __FUNCTION__, msg, tag);

    /*set socket test system*/
    ret = lwip_sttest_set_system(tag, msg, cb);
    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: set system process failed, ret(%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3191, "lwiperr: %s failed, ret(%d)!", __FUNCTION__, ret);
        goto _st_setsys_end;          
    }

    /*last, denote process success*/
    ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: %s, tag=%d, ready!",  msg, tag);

_st_setsys_end:
    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;         
}


/*******************************************************
** stsetsys at query:
**  at*lwipctrl=stgetsys
********************************************************/
void lwip_atctl_stgetsys_query(printCallback cb)
{
    char help[] =  "help for stgetsys:"NEWLINE"\
    format: at*lwipctrl=stsocket,keytag,connection id"NEWLINE"\
    support keytag, tag list as: all keytag"NEWLINE"\
    examples:"NEWLINE"\
    at*lwipctrl=stsocket,host,0  ---- get host from connection id 0 table"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);
}

void lwip_atctl_stgetsys_process(char *msg, int tag, printCallback cb)
{   
    char ind_buf [256] = {0};
    int ind_size = 0;   
    int ret = ERR_OK;

    if (msg == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: enter check failed!");
        goto _st_setsys_end;
    }

    if (0 == lwip_sttest_control.status) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: socket test not initial!");
        goto _st_setsys_end;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3239, "%s, msg=%s, conn_id=%d", __FUNCTION__, msg, tag);

    /*set socket test system*/
    ret = lwip_sttest_get_system(tag, msg, cb);
    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: lwip_sttest_get_system failed, ret(%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3245, "lwiperr: %s,lwip_sttest_get_system failed, ret(%d)!", __FUNCTION__, ret);
        goto _st_setsys_end;          
    }

    /*last, denote process success*/
    ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: %s, tag=%d, ready!",  msg, tag);

_st_setsys_end:
    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;         
}


void lwip_atctl_stdns_query(printCallback cb)
{
    char help[] =  "help for stdns:"NEWLINE"\
    format: at*lwipctrl=stdns,keytag | keyval, ipv4 or v6"NEWLINE"\
    support keytag, tag list as: host, netif"NEWLINE"\
    examples:"NEWLINE"\
    at*lwipctrl=stdns,host|www.baidu.com,4 ----do dns query for ipv4"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);
}

void lwip_atctl_stdns_process(char *msg, int tag, printCallback cb)
{   
    char ind_buf [256] = {0};
    int ind_size = 0;
    int key_count = 0;
    int loop_id = 0;
    char keytag [LWIP_STTEST_KEYTAG_SIZE];
    char keyval [LWIP_STTEST_KEYVAL_SIZE];
    char *msg_next = NULL;
    char *msg_now = NULL;
    char hostname [LWIP_STTEST_KEYVAL_SIZE];
    char netifname [NETIF_MAX_HOSTNAME_LEN];
	char *ifptr = NULL;
    int hosttag = 0;
    int ret = ERR_OK;

    if (msg == NULL) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: enter check failed!");
        goto _st_dns_end;
    }

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3294, "%s, hostname=%s, v4 or v6=%d", __FUNCTION__, msg, tag);

    /* keytag | keyval format*/
    /*support host\netif*/
    msg_now  = msg;
    msg_next = NULL;
  
    /*key-value config msg parse*/
    for (loop_id = 0; loop_id < LWIP_STTEST_DNSAPI_KEYVAL_NUM; loop_id++) {
        /*get keytagk*/
        ret = lwip_atctl_parse_subcmd(msg_now, keytag, LWIP_STTEST_KEYTAG_SIZE, &msg_next);
        if (ret != 0) { break;}
  
        /*get keyval*/
        msg_now = msg_next;
        ret = lwip_atctl_parse_subcmd(msg_now, keyval, LWIP_STTEST_KEYVAL_SIZE, &msg_next);                
        if (ret != 0) { break;}
    
        /*parse keytag | keyval*/
        if (0 == strncmp(keytag, "host", strlen("host"))) {
          if (strlen(keyval) > LWIP_STTEST_KEYVAL_SIZE) {
              ret = ERR_VAL;
              break;
          }
          hosttag = 1; /*must config host*/
          memset(hostname, 0, sizeof(hostname));
          memcpy(hostname, keyval, sizeof(keyval));
        } else if (0 == strncmp(keytag, "netif", strlen("netif"))) {
          if (strlen(keyval) > NETIF_MAX_HOSTNAME_LEN) {
              ret = ERR_VAL;
              break;
          }
          memset(netifname, 0, NETIF_MAX_HOSTNAME_LEN);
          memcpy(netifname, keyval, sizeof(keyval));
		  ifptr = netifname;
        } else {
          break;
        }
  
        memset(keytag, 0, sizeof(keytag));
        memset(keyval, 0, sizeof(keyval));

        msg_now = msg_next;
        key_count++;        
    }
  
    if ((key_count == 0) || (hosttag == 0)) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: parse msg error, or no host config!");
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3342, "lwiperr: %s, parse msg error, or no host config!", __FUNCTION__);		
        goto _st_dns_end;
    }

    /*set socket test system*/
    ret = lwip_sttest_dns_query(ifptr, hostname, tag, cb);
    if (ERR_OK != ret) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: dns query failed, ret(%d)!", ret);
		LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3350, "lwiperr: %s, failed, ret(%d)!", __FUNCTION__, ret);		
        goto _st_dns_end;          
    }

_st_dns_end:
    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;         
}


void lwip_atctl_stsocket_query(printCallback cb)
{
    char help[] =  "help for stsocket:"NEWLINE"\
    format: at*lwipctrl=stsocket,keytag,connection id"NEWLINE"\
    support keytag, tag list as: ip4, ip6, tcp, udp, raw"NEWLINE"\
    examples:"NEWLINE"\
    at*lwipctrl=stsocket,ip4|udp,0 ---- create udp socket, connection = 0."NEWLINE"\
    at*lwipctrl=stsocket,ip4|tcp,0 "NEWLINE"\
    at*lwipctrl=stsocket,ip4|raw,0 "NEWLINE"\
    at*lwipctrl=stsocket,ip6|tcp,0  ---- create tcp ip6 socket, support ipv4v6"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stsocket_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3383, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_CREAT;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}


void lwip_atctl_stconnect_query(printCallback cb)
{
    char help[] =  "help for stconnect:"NEWLINE"\
    format: at*lwipctrl=stconnect,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: task, host, port or rport"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stconnect,task|task1|host|www.baidu.com|port|80,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);
}

void lwip_atctl_stconnect_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3405, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_CONNECT;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}


void lwip_atctl_stbind_query(printCallback cb)
{
    char help[] =  "help for stbind:"NEWLINE"\
    format: at*lwipctrl=stbind,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: task, ip4, ip6, port"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stconnect,task|task1|ip4|***********|port|9090,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stbind_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3428, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_BIND;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stlisten_query(printCallback cb)
{
    char help[] =  "help for stlisten:"NEWLINE"\
    format: at*lwipctrl=stlisten,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: backlog"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stlisten,null,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stlisten_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3450, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_LISTEN;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_staccept_query(printCallback cb)
{
    char help[] =  "help for staccept:"NEWLINE"\
    format: at*lwipctrl=staccept,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: task"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=staccept,task|task_accept,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_staccept_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3472, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_ACCEPT;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stclose_query(printCallback cb)
{
    char help[] =  "help for stclose:"NEWLINE"\
    format: at*lwipctrl=stclose,keytag,connection id"NEWLINE"\
    support keytag, tag list as: task"NEWLINE"\
    examples:"NEWLINE"\
    at*lwipctrl=stclose,null,0  ---- close connection 0 on main task"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stclose_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3494, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_CLOSE;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stwrite_query(printCallback cb)
{
    char help[] =  "help for stwrite:"NEWLINE"\
    format: at*lwipctrl=stwrite,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: task, wlen, inter, loop"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stwrite,task|task_write|wlen|1500,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stwrite_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3516, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_WRITE;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stread_query(printCallback cb)
{
    char help[] =  "help for stwrite:"NEWLINE"\
    format: at*lwipctrl=stread,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: task, rlen, inter, loop"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stread,task|task_write|rlen|1500,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stread_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3538, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_READ;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stecho_query(printCallback cb)
{
    char help[] =  "help for stecho:"NEWLINE"\
    format: at*lwipctrl=stecho,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: task, inter, loop"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stecho,task|task_echo,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stecho_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3560, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_ECHO;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stselecho_query(printCallback cb)
{
    char help[] =  "help for stselecho:"NEWLINE"\
    format: at*lwipctrl=stselecho,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as: task, selectto, inter, loop"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stselecho,task|task_echo,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stselecho_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3582, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_SELECT_ECHO;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}


void lwip_atctl_stselrw_query(printCallback cb)
{
    char help[] =  "help for stselect:"NEWLINE"\
    format: at*lwipctrl=stselrw,keytag|keyval,connection id"NEWLINE"\
    support keytag|keyval, tag list as:  "NEWLINE"\
        task, rtask, wtask, rlen, wlen, readset, writeset, selectto, inter, loop"NEWLINE"\
    example:"NEWLINE"\
    at*lwipctrl=stselrw,task|task_select|selectto|1000,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stselrw_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_3606, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SOCKET_SELECT_RW;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stsetsockopt_query(printCallback cb)
{
    char help[] =  "help for stsetsockopt:"NEWLINE"\
    format: at*lwipctrl=stsetsockopt,opt_name|opt_value,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stsetsockopt,SO_RECVBUF|6400,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stsetsockopt_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3627, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SET_SOCK_OPTION;
    lwip_atctl_stsetopt_common(cmd_id, msg, tag, cb);
}
void lwip_atctl_stsettcpopt_query(printCallback cb)
{
    char help[] =  "help for stsettcpopt:"NEWLINE"\
    format: at*lwipctrl=stsettcpopt,opt_name|opt_value,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stsettcpopt,TCP_KEEPIDLE|20,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stsettcpopt_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3647, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SET_TCP_OPTION;
    lwip_atctl_stsetopt_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stsetipopt_query(printCallback cb)
{
    char help[] =  "help for stsetipopt:"NEWLINE"\
    format: at*lwipctrl=stsetipopt,opt_name|opt_value,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stsetipopt,IP_TTL|60,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stsetipopt_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3668, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_SET_IP_OPTION;
    lwip_atctl_stsetopt_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stgetsockopt_query(printCallback cb)
{
    char help[] =  "help for stgetsockopt:"NEWLINE"\
    format: at*lwipctrl=stgetsockopt,opt_name,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stgetsockopt,SO_RECVBUF,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stgetsockopt_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3689, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_GET_SOCK_OPTION;
    lwip_atctl_stgetopt_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stgettcpopt_query(printCallback cb)
{
    char help[] =  "help for stgettcpopt:"NEWLINE"\
    format: at*lwipctrl=stgettcpopt,opt_name,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stgettcpopt,TCP_KEEPIDLE,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stgettcpopt_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3710, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_GET_TCP_OPTION;
    lwip_atctl_stgetopt_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stgetipopt_query(printCallback cb)
{
    char help[] =  "help for stgetipopt:"NEWLINE"\
    format: at*lwipctrl=stgetipopt,opt_name,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stgetipopt,IP_TTL,0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stgetipopt_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3731, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    cmd_id = LWIP_STTEST_CMD_GET_IP_OPTION;
    lwip_atctl_stgetopt_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stdebug_query(printCallback cb)
{
    char help[] =  "help for stping:"NEWLINE"\
    format: at*lwipctrl=stping,tag|val, connection id"NEWLINE"\
            at*lwipctrl=stping,stop, connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stdebug,triggerselect,0 //select mantrigger for task 0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);

}

void lwip_atctl_stdebug_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3952, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);
    cmd_id = LWIP_STTEST_CMD_SOCKET_DEBUG;
    lwip_atctl_stdebug_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stping_query(printCallback cb)
{
    char help[] =  "help for stping:"NEWLINE"\
    format: at*lwipctrl=stping,tag|val,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stping,host|*************|port|9090|type|4|proto|udp,0 //use conn id 0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);
}

void lwip_atctl_stping_process(char *msg, int tag, printCallback cb)
{
    int cmd_id;
    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_3970, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    /*first call sttest initial*/
    lwip_sttest_initial();

    cmd_id = LWIP_STTEST_CMD_SOCKET_PING;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}

void lwip_atctl_stsignal_query(printCallback cb)
{
    char help[] =  "help for stsignal:"NEWLINE"\
    format: at*lwipctrl=stsignal,tag,connection id"NEWLINE"\
    support tag: stop, pend, resume"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stsignal,stop,0 //stop procoess for conn id 0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);
}

void lwip_atctl_stsignal_process(char *msg, int tag, printCallback cb)
{
    struct lwip_sttest_conn_table_st * conn_table = NULL;
    struct lwip_sttest_task_table_st *task_table = NULL;

    int connid;
    char ind_buf [256] = {0};
    int inter = 0;
    int ind_size = 0;  
    int counter = 0;
    int ret = ERR_OK;    

    LWIP_DIAG(DIAG_ON_DEBUG,lwip_sttest_4240, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    /*get connection table first*/
    connid = tag;
    conn_table = lwip_sttest_get_conn_table(connid);
    if (NULL == conn_table) {
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: check connection failed!");
        goto _sthisr_process_end;
    }

    if (0 == strncmp(msg, "stop", strlen("stop"))) {
        conn_table->signal = LWIP_STTEST_SIG_STOP;
        inter = LWIP_MAX(1000, conn_table->interto) + 500;

        /*if connection is http client*/
        if (conn_table->httpclient) {
            http_client_stop(conn_table->httpclient);
        }

        while(counter < 3) { /*wait max 3 times*/
            sys_msleep(inter); /*sleep , for stop process*/

            counter++;
            if (conn_table->signal == LWIP_STTEST_SIG_STOP_FINISH) {
                break;
            }
        }

        /*free task, 0 denote main ctrl task, no need free*/
        if (conn_table->taskid > 0) {
            task_table = lwip_sttest_control.task_tablelist[conn_table->taskid - 1];
    		ret = lwip_sttest_free_task(task_table);
            if (ret != ERR_OK) {
                ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: free task failed!");
                goto _sthisr_process_end;
            }
        }

        ret = lwip_sttest_delet_conn_table(connid);
        if (ret != ERR_OK) {
            ind_size = snprintf(ind_buf, sizeof(ind_buf), "FAILED: delete connection table failed!");
            goto _sthisr_process_end;
        }
    } else if (0 == strncmp(msg, "pend", strlen("pend"))) {
        conn_table->signal = LWIP_STTEST_SIG_PEND;
    } else if (0 == strncmp(msg, "resume", strlen("resume"))) {
        conn_table->signal = LWIP_STTEST_SIG_RESUME;
    } else {
        /*@TBD*/
    }

    ind_size = snprintf(ind_buf, sizeof(ind_buf), "SUCCESS: process %s for connection %d!", msg, tag);

_sthisr_process_end:

    /*call printcallback to report*/
    if (cb && (ind_size > 0)) {
        cb(ind_buf);
    }

    return;
}


void lwip_atctl_stwget_query(printCallback cb)
{
    char help[] =  "help for stwget:"NEWLINE"\
    format: at*lwipctrl=stwget,tag|val,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stwget,host|*************,0 //use conn id 0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);
}

void lwip_atctl_stwget_process(char *msg, int tag, printCallback cb)
{
    char ind_buf [128];
    int ind_size;
    int cmd_id;

    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_4410, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    if (LWIP_FLAGS_HOTBIT_CHECK(lwip_sttest_control.flags, LWIP_STTEST_SYSCTRL_WGET_FLAG)) {

        LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_4411, "lwiperr: wget exist, only one wget can process.");
        ind_size = snprintf(ind_buf, sizeof(ind_buf), "wget exist, only one wget can process.");
        if (cb && (ind_size > 0)) {
            cb(ind_buf);
        }

        return;
    }

    LWIP_FLAGS_HOTBIT_SET(lwip_sttest_control.flags, LWIP_STTEST_SYSCTRL_WGET_FLAG);

    /*first call sttest initial*/
    lwip_sttest_initial();

    cmd_id = LWIP_STTEST_CMD_SOCKET_WGET;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
}


void lwip_atctl_stmqtt_query(printCallback cb)
{
#if LWIP_STTEST_MQTT
    char help[] =  "help for stmqtt:"NEWLINE"\
    format: at*lwipctrl=stmqtt,tag|val,connection id"NEWLINE"\
    exampls:"NEWLINE"\
    at*lwipctrl=stmqtt,null,0 //use conn id 0"NEWLINE"";
  
    lwip_sttest_help_query(cb, help);
#endif
}

void lwip_atctl_stmqtt_process(char *msg, int tag, printCallback cb)
{
#if LWIP_STTEST_MQTT
    char ind_buf [128];
    int ind_size;
    int cmd_id;

    LWIP_DIAG(DIAG_ON_DEBUG, lwip_sttest_4420, "%s, msg=%s, tag=%d, cb=%lx", __FUNCTION__, msg, tag, cb);

    /*first call sttest initial*/
    lwip_sttest_initial();

    cmd_id = LWIP_STTEST_CMD_SOCKET_MQTT;
    lwip_atctl_stsocket_common(cmd_id, msg, tag, cb);
#endif
}


#endif
#endif
