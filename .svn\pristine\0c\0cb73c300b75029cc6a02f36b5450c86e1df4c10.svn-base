#=========================================================================
# File Name      : yaffs2.mak
# Description    : Main make file for the softutil/yaffs2 package.
#
# Usage          : make [-s] -f FDI_6.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
# Copyright (c) 2001 Intel Corporation. All Rights Reserved
#=========================================================================

# Package build options
include $(OPT_FILE)
# No PrePass needed
 PP =

# Package Makefile information
GEN_PACK_MAKEFILE = $(BUILD_ROOT)/env/$(HOST)/build/package.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = yaffs2
PACKAGE_BASE     = softutil
PACKAGE_DEP_FILE = yaffs2_dep.mak
PACKAGE_PATH     = $(BUILD_ROOT)/$(PACKAGE_BASE)/$(PACKAGE_NAME)

# The path locations of source and include file directories.
PACKAGE_SRC_PATH    = $(PACKAGE_PATH)
PACKAGE_INC_PATHS   = $(PACKAGE_PATH)/src  \
					$(PACKAGE_PATH)/src/port \
					$(PACKAGE_PATH)/src/direct   \
					$(BUILD_ROOT)/softutil/fatsys/flash	\
					$(BUILD_ROOT)/softutil/FDI/src/FM_INC


PACKAGE_SRC_FILES += \
				src/yaffs_allocator.c 	\
				src/yaffs_attribs.c		\
				src/yaffs_bitmap.c		\
				src/yaffs_checkptrw.c 	\
				src/yaffs_ecc.c    		\
				src/yaffs_endian.c    	\
				src/yaffs_guts.c    	\
				src/yaffs_nameval.c    	\
				src/yaffs_nand.c   		\
				src/yaffs_packedtags1.c    	\
				src/yaffs_packedtags2.c    	\
				src/yaffs_summary.c     	\
				src/yaffs_tagsmarshall.c    \
				src/yaffs_tagscompat.c		\
				src/yaffs_verify.c			\
				src/yaffs_yaffs1.c			\
				src/yaffs_yaffs2.c			\
				src/direct/yaffs_error.c	\
				src/direct/yaffs_hweight.c	\
				src/direct/yaffsfs.c		\
				src/port/yaffs_osglue.c		\
				src/port/yaffs_nand_drv.c		\
				src/port/yaffs_nor_drv.c		\
				src/port/yaffscfg2k.c                   \
				src/direct/yaffs_list2.c

				
# These are the tool flags specific to the FDI package only.
# The environment, target, and group also set flags.
PACKAGE_ASMFLAGS =
PACKAGE_CFLAGS   =
PACKAGE_CPPFLAGS =
PACKAGE_DFLAGS   =
PACKAGE_ARFLAGS  =

# Define Package Variants -------------------------------

# look for the variants in the VARIANT_LIST and override
# setting from the previous section. The variable
# FDI_VARIANT_1 is meant to be overwritten
# with actual variant names. More variants can be added
# as required.



# Include the Standard Package Make File ---------------
include $(GEN_PACK_MAKEFILE)

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include $(PACKAGE_DEP_FILE)









