# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"
/*
 *  RFC 1321 compliant MD5 implementation
 *
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
/*
 *  The MD5 algorithm was designed by <PERSON> in 1991.
 *
 *  http://www.ietf.org/rfc/rfc1321.txt
 */

# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"
/**
 * \file common.h
 *
 * \brief Utility macros for internal use in the library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"
/**
 * \file build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
 /*
  *  Copyright The Mbed TLS Contributors
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  */










/*
 * This set of compile-time defines can be used to determine the version number
 * of the Mbed TLS library used. Run-time variables for the same can be found in
 * version.h
 */

/**
 * The version number x.y.z is split into three parts.
 * Major, Minor, Patchlevel
 */




/**
 * The single version number has the following structure:
 *    MMNNPP00
 *    Major version | Minor version | Patch version
 */








# 1 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
/*
 * Copyright (C) 2019 Alibaba Group Holding Limited
 */




/*specially for alios*/







/* System support */




//#define MBEDTLS_PLATFORM_MEMORY

//#define MBEDTLS_CONFIG_TLS_DEBUG

/* mbed TLS feature support */
# 35 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_ALT


# 53 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 60 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 76 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"





# 103 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"







/* mbed TLS modules */
# 127 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_C
//#define MBEDTLS_TIMING_C













# 178 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 185 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 202 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 209 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//#ifdef LWM2M_WITH_MBEDTLS
# 219 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#endif /* LWM2M_WITH_MBEDTLS */







/* Module configuration options */





/**
 * \def MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION
 *
 * If set, the X509 parser will not break-off when parsing an X509 certificate
 * and encountering an unknown critical extension.
 *
 * \warning Depending on your PKI use, enabling this can be a security risk!
 *
 * Uncomment to prevent an error.
 */




//ALIPAY_SUPPORT BEGIN


typedef unsigned int time_t;
# 273 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#ifndef MBEDTLS_ECP_DP_SECP256R1_ENABLED


//#endif
# 284 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//ALIPAY_SUPPORT END

# 298 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 66 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"








/* Target and application specific configurations
 *
 * Allow user to override any previous default.
 *
 */




# 89 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
/**
 * \file check_config.h
 *
 * \brief Consistency checks for configuration options
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




/*
 * We assume CHAR_BIT is 8 in many places. In practice, this is true on our
 * target platforms, so not an issue, but let's just be extra sure.
 */
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 31 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"




# 52 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"








































//ALIPAY_SUPPORT BEGIN
# 111 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END






# 129 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"





//ALIPAY_SUPPORT BEGIN
# 152 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END


























# 196 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 206 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
































































































































# 341 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















































































































































































































































# 589 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"















# 611 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"



















































# 707 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















# 737 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











/*
 * HKDF is mandatory for TLS 1.3.
 * Otherwise support for at least one ciphersuite mandates either SHA_256 or
 * SHA_384.
 */
# 759 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

/*
 * The current implementation of TLS 1.3 requires MBEDTLS_SSL_KEEP_PEER_CERTIFICATE.
 */




# 782 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











































































# 863 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 870 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

































































/* Reject attempts to enable options that have been removed and that could
 * cause a build to succeed but with features removed. */













































/*
 * Avoid warning from -pedantic. This is a convenient place for this
 * workaround since this is included by every single file before the
 * #if defined(MBEDTLS_xxx_C) that results in empty translation units.
 */
typedef int mbedtls_iso_c_forbids_empty_translation_units;

# 91 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 27 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 29 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/** Helper to define a function as static except when building invasive tests.
 *
 * If a function is only used inside its own source file and should be
 * declared `static` to allow the compiler to optimize for code size,
 * but that function has unit tests, define it with
 * ```
 * MBEDTLS_STATIC_TESTABLE int mbedtls_foo(...) { ... }
 * ```
 * and declare it in a header in the `library/` directory with
 * ```
 * #if defined(MBEDTLS_TEST_HOOKS)
 * int mbedtls_foo(...);
 * #endif
 * ```
 */






# 63 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/** Allow library to access its structs' private members.
 *
 * Although structs defined in header files are publicly available,
 * their members are private and should not be accessed by the user.
 */


/** Byte Reading Macros
 *
 * Given a multi-byte integer \p x, MBEDTLS_BYTE_n retrieves the n-th
 * byte from x, where byte 0 is the least significant byte.
 */
# 84 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */
# 103 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 32 bits unsigned integer in big-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 122 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */
# 141 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 32 bits unsigned integer in little-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 160 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */
# 177 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 16 bits unsigned integer in little-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 194 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */
# 211 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 16 bits unsigned integer in big-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 228 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 246 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 24 bits unsigned integer in big-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 264 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 282 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 24 bits unsigned integer in little-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 300 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */
# 323 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 64 bits unsigned integer in big-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 346 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */
# 369 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 64 bits unsigned integer in little-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 392 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/* Fix MSVC C99 compatible issue
 *      MSVC support __func__ from visual studio 2015( 1900 )
 *      Use MSVC predefine macro to avoid name check fail.
 */




# 26 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"



# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md5.h"
/**
 * \file md5.h
 *
 * \brief MD5 message digest algorithm (hash function)
 *
 * \warning   MD5 is considered a weak message digest and its use constitutes a
 *            security risk. We recommend considering stronger message
 *            digests instead.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h"
 /**
 * \file private_access.h
 *
 * \brief Macro wrapper for struct's members.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




# 32 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h"

# 29 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md5.h"

# 31 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md5.h"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"
/* stddef.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.1.4 */

/* Copyright (C) ARM Ltd., 1999
 * All rights reserved
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991 ARM Limited. All rights reserved.             */
/* version 0.05 */

/*
 * The following types and macros are defined in several headers referred to in
 * the descriptions of the functions declared in that header. They are also
 * defined in this header file.
 */





# 34 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"




  typedef signed int ptrdiff_t;



 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



  /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* also in <stdlib.h> and <inttypes.h> */
# 82 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



   /* null pointer constant. */




  /* EDG uses __INTADDR__ to avoid errors when strict */




  typedef long double max_align_t;









# 114 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



/* end of stddef.h */

# 33 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md5.h"
# 34 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md5.h"






// Regular implementation
//

/**
 * \brief          MD5 context structure
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
typedef struct mbedtls_md5_context
{
    uint32_t total[2];          /*!< number of bytes processed  */
    uint32_t state[4];          /*!< intermediate digest state  */
    unsigned char buffer[64];   /*!< data block being processed */
}
mbedtls_md5_context;





/**
 * \brief          Initialize MD5 context
 *
 * \param ctx      MD5 context to be initialized
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
void mbedtls_md5_init( mbedtls_md5_context *ctx );

/**
 * \brief          Clear MD5 context
 *
 * \param ctx      MD5 context to be cleared
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
void mbedtls_md5_free( mbedtls_md5_context *ctx );

/**
 * \brief          Clone (the state of) an MD5 context
 *
 * \param dst      The destination context
 * \param src      The context to be cloned
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
void mbedtls_md5_clone( mbedtls_md5_context *dst,
                        const mbedtls_md5_context *src );

/**
 * \brief          MD5 context setup
 *
 * \param ctx      context to be initialized
 *
 * \return         0 if successful
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
int mbedtls_md5_starts( mbedtls_md5_context *ctx );

/**
 * \brief          MD5 process buffer
 *
 * \param ctx      MD5 context
 * \param input    buffer holding the data
 * \param ilen     length of the input data
 *
 * \return         0 if successful
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
int mbedtls_md5_update( mbedtls_md5_context *ctx,
                        const unsigned char *input,
                        size_t ilen );

/**
 * \brief          MD5 final digest
 *
 * \param ctx      MD5 context
 * \param output   MD5 checksum result
 *
 * \return         0 if successful
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
int mbedtls_md5_finish( mbedtls_md5_context *ctx,
                        unsigned char output[16] );

/**
 * \brief          MD5 process data block (internal use only)
 *
 * \param ctx      MD5 context
 * \param data     buffer holding one block of data
 *
 * \return         0 if successful
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
int mbedtls_internal_md5_process( mbedtls_md5_context *ctx,
                                  const unsigned char data[64] );

/**
 * \brief          Output = MD5( input buffer )
 *
 * \param input    buffer holding the data
 * \param ilen     length of the input data
 * \param output   MD5 checksum result
 *
 * \return         0 if successful
 *
 * \warning        MD5 is considered a weak message digest and its use
 *                 constitutes a security risk. We recommend considering
 *                 stronger message digests instead.
 *
 */
int mbedtls_md5( const unsigned char *input,
                 size_t ilen,
                 unsigned char output[16] );

# 198 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/md5.h"





# 30 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"
/**
 * \file platform_util.h
 *
 * \brief Common and shared functions used by multiple modules in the Mbed TLS
 *        library.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 27 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

# 29 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"









/* Internal macros meant to be called only from within the library. */



/* Internal helper macros for deprecating API constants. */
# 58 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

/* Implementation of the check-return facility.
 * See the user documentation in mbedtls_config.h.
 *
 * Do not use this macro directly to annotate function: instead,
 * use one of MBEDTLS_CHECK_RETURN_CRITICAL or MBEDTLS_CHECK_RETURN_TYPICAL
 * depending on how important it is to check the return value.
 */
# 76 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

/** Critical-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be checked in all applications.
 * Omitting the check is very likely to indicate a bug in the application
 * and will result in a compile-time warning if #MBEDTLS_CHECK_RETURN
 * is implemented for the compiler in use.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro may be added to more functions in the future.
 *        Such an extension is not considered an API break, provided that
 *        there are near-unavoidable circumstances under which the function
 *        can fail. For example, signature/MAC/AEAD verification functions,
 *        and functions that require a random generator, are considered
 *        return-check-critical.
 */


/** Ordinary-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be generally be checked in portable
 * applications. Omitting the check will result in a compile-time warning if
 * #MBEDTLS_CHECK_RETURN is implemented for the compiler in use and
 * #MBEDTLS_CHECK_RETURN_WARNING is enabled in the compile-time configuration.
 *
 * You can use #MBEDTLS_IGNORE_RETURN to explicitly ignore the return value
 * of a function that is annotated with #MBEDTLS_CHECK_RETURN.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro will be added to more functions in the future.
 *        Eventually this should appear before most functions returning
 *        an error code (as \c int in the \c mbedtls_xxx API or
 *        as ::psa_status_t in the \c psa_xxx API).
 */






/** Benign-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that it is rarely useful to check its return value.
 *
 * This macro has an empty expansion. It exists for documentation purposes:
 * a #MBEDTLS_CHECK_RETURN_OPTIONAL annotation indicates that the function
 * has been analyzed for return-check usefulness, whereas the lack of
 * an annotation indicates that the function has not been analyzed and its
 * return-check usefulness is unknown.
 */


/** \def MBEDTLS_IGNORE_RETURN
 *
 * Call this macro with one argument, a function call, to suppress a warning
 * from #MBEDTLS_CHECK_RETURN due to that function call.
 */

/* GCC doesn't silence the warning with just (void)(result).
 * (void)!(result) is known to work up at least up to GCC 10, as well
 * as with Clang and MSVC.
 *
 * https://gcc.gnu.org/onlinedocs/gcc-3.4.6/gcc/Non_002dbugs.html
 * https://stackoverflow.com/questions/40576003/ignoring-warning-wunused-result
 * https://gcc.gnu.org/bugzilla/show_bug.cgi?id=66425#c34
 */



/**
 * \brief       Securely zeroize a buffer
 *
 *              The function is meant to wipe the data contained in a buffer so
 *              that it can no longer be recovered even if the program memory
 *              is later compromised. Call this function on sensitive data
 *              stored on the stack before returning from a function, and on
 *              sensitive data stored on the heap before freeing the heap
 *              object.
 *
 *              It is extremely difficult to guarantee that calls to
 *              mbedtls_platform_zeroize() are not removed by aggressive
 *              compiler optimizations in a portable way. For this reason, Mbed
 *              TLS provides the configuration option
 *              MBEDTLS_PLATFORM_ZEROIZE_ALT, which allows users to configure
 *              mbedtls_platform_zeroize() to use a suitable implementation for
 *              their platform and needs
 *
 * \param buf   Buffer to be zeroized
 * \param len   Length of the buffer in bytes
 *
 */
void mbedtls_platform_zeroize( void *buf, size_t len );

# 202 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"





# 31 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"
/**
 * \file error.h
 *
 * \brief Error to string translation
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 26 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"

# 28 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"






/**
 * Error code layout.
 *
 * Currently we try to keep all error codes within the negative space of 16
 * bits signed integers to support all platforms (-0x0001 - -0x7FFF). In
 * addition we'd like to give two layers of information on the error if
 * possible.
 *
 * For that purpose the error codes are segmented in the following manner:
 *
 * 16 bit error code bit-segmentation
 *
 * 1 bit  - Unused (sign bit)
 * 3 bits - High level module ID
 * 5 bits - Module-dependent error code
 * 7 bits - Low level module errors
 *
 * For historical reasons, low-level error codes are divided in even and odd,
 * even codes were assigned first, and -1 is reserved for other errors.
 *
 * Low-level module errors (0x0002-0x007E, 0x0001-0x007F)
 *
 * Module   Nr  Codes assigned
 * ERROR     2  0x006E          0x0001
 * MPI       7  0x0002-0x0010
 * GCM       3  0x0012-0x0016   0x0013-0x0013
 * THREADING 3  0x001A-0x001E
 * AES       5  0x0020-0x0022   0x0021-0x0025
 * CAMELLIA  3  0x0024-0x0026   0x0027-0x0027
 * BASE64    2  0x002A-0x002C
 * OID       1  0x002E-0x002E   0x000B-0x000B
 * PADLOCK   1  0x0030-0x0030
 * DES       2  0x0032-0x0032   0x0033-0x0033
 * CTR_DBRG  4  0x0034-0x003A
 * ENTROPY   3  0x003C-0x0040   0x003D-0x003F
 * NET      13  0x0042-0x0052   0x0043-0x0049
 * ARIA      4  0x0058-0x005E
 * ASN1      7  0x0060-0x006C
 * CMAC      1  0x007A-0x007A
 * PBKDF2    1  0x007C-0x007C
 * HMAC_DRBG 4                  0x0003-0x0009
 * CCM       3                  0x000D-0x0011
 * MD5       1                  0x002F-0x002F
 * RIPEMD160 1                  0x0031-0x0031
 * SHA1      1                  0x0035-0x0035 0x0073-0x0073
 * SHA256    1                  0x0037-0x0037 0x0074-0x0074
 * SHA512    1                  0x0039-0x0039 0x0075-0x0075
 * CHACHA20  3                  0x0051-0x0055
 * POLY1305  3                  0x0057-0x005B
 * CHACHAPOLY 2 0x0054-0x0056
 * PLATFORM  2  0x0070-0x0072
 *
 * High-level module nr (3 bits - 0x0...-0x7...)
 * Name      ID  Nr of Errors
 * PEM       1   9
 * PKCS#12   1   4 (Started from top)
 * X509      2   20
 * PKCS5     2   4 (Started from top)
 * DHM       3   11
 * PK        3   15 (Started from top)
 * RSA       4   11
 * ECP       4   10 (Started from top)
 * MD        5   5
 * HKDF      5   1 (Started from top)
 * SSL       5   2 (Started from 0x5F00)
 * CIPHER    6   8 (Started from 0x6080)
 * SSL       6   22 (Started from top, plus 0x6000)
 * SSL       7   20 (Started from 0x7000, gaps at
 *                   0x7380, 0x7900-0x7980, 0x7A80-0x7E80)
 *
 * Module dependent error code (5 bits 0x.00.-0x.F8.)
 */





/** Generic error */

/** This is a bug in the library */


/** Hardware accelerator failed */

/** The requested feature is not supported by the platform */


/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        Wrapper macro for mbedtls_error_add(). See that function for
 *        more details.
 */



# 137 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"

/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        This function can be called directly however it is usually
 *        called via the #MBEDTLS_ERROR_ADD macro.
 *
 *        While a value of zero is not a negative error code, it is still an
 *        error code (that denotes success) and can be combined with both a
 *        negative error code or another value of zero.
 *
 * \note  When invasive testing is enabled via #MBEDTLS_TEST_HOOKS, also try to
 *        call \link mbedtls_test_hook_error_add \endlink.
 *
 * \param high      high-level error code. See error.h for more details.
 * \param low       low-level error code. See error.h for more details.
 * \param file      file where this error code addition occurred.
 * \param line      line where this error code addition occurred.
 */
static __inline int mbedtls_error_add( int high, int low,
                                     const char *file, int line )
{




    (void)file;
    (void)line;

    return( high + low );
}

/**
 * \brief Translate a mbed TLS error code into a string representation,
 *        Result is truncated if necessary and always includes a terminating
 *        null byte.
 *
 * \param errnum    error code
 * \param buffer    buffer to place representation in
 * \param buflen    length of the buffer
 */
void mbedtls_strerror( int errnum, char *buffer, size_t buflen );

/**
 * \brief Translate the high-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char * mbedtls_high_level_strerr( int error_code );

/**
 * \brief Translate the low-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char * mbedtls_low_level_strerr( int error_code );





# 32 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 34 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"

# 43 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"



void mbedtls_md5_init( mbedtls_md5_context *ctx )
{
    memset( ctx, 0, sizeof( mbedtls_md5_context ) );
}

void mbedtls_md5_free( mbedtls_md5_context *ctx )
{
    if( ctx == 0 )
        return;

    mbedtls_platform_zeroize( ctx, sizeof( mbedtls_md5_context ) );
}

void mbedtls_md5_clone( mbedtls_md5_context *dst,
                        const mbedtls_md5_context *src )
{
    *dst = *src;
}

/*
 * MD5 context setup
 */
int mbedtls_md5_starts( mbedtls_md5_context *ctx )
{
    ctx->total[0] = 0;
    ctx->total[1] = 0;

    ctx->state[0] = 0x67452301;
    ctx->state[1] = 0xEFCDAB89;
    ctx->state[2] = 0x98BADCFE;
    ctx->state[3] = 0x10325476;

    return( 0 );
}


int mbedtls_internal_md5_process( mbedtls_md5_context *ctx,
                                  const unsigned char data[64] )
{
    struct
    {
        uint32_t X[16], A, B, C, D;
    } local;

    local.X[ 0] = ( ( (uint32_t) ( data )[( 0 ) ] ) | ( (uint32_t) ( data )[( 0 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 0 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 0 ) + 3] << 24 ) );
    local.X[ 1] = ( ( (uint32_t) ( data )[( 4 ) ] ) | ( (uint32_t) ( data )[( 4 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 4 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 4 ) + 3] << 24 ) );
    local.X[ 2] = ( ( (uint32_t) ( data )[( 8 ) ] ) | ( (uint32_t) ( data )[( 8 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 8 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 8 ) + 3] << 24 ) );
    local.X[ 3] = ( ( (uint32_t) ( data )[( 12 ) ] ) | ( (uint32_t) ( data )[( 12 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 12 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 12 ) + 3] << 24 ) );
    local.X[ 4] = ( ( (uint32_t) ( data )[( 16 ) ] ) | ( (uint32_t) ( data )[( 16 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 16 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 16 ) + 3] << 24 ) );
    local.X[ 5] = ( ( (uint32_t) ( data )[( 20 ) ] ) | ( (uint32_t) ( data )[( 20 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 20 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 20 ) + 3] << 24 ) );
    local.X[ 6] = ( ( (uint32_t) ( data )[( 24 ) ] ) | ( (uint32_t) ( data )[( 24 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 24 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 24 ) + 3] << 24 ) );
    local.X[ 7] = ( ( (uint32_t) ( data )[( 28 ) ] ) | ( (uint32_t) ( data )[( 28 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 28 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 28 ) + 3] << 24 ) );
    local.X[ 8] = ( ( (uint32_t) ( data )[( 32 ) ] ) | ( (uint32_t) ( data )[( 32 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 32 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 32 ) + 3] << 24 ) );
    local.X[ 9] = ( ( (uint32_t) ( data )[( 36 ) ] ) | ( (uint32_t) ( data )[( 36 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 36 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 36 ) + 3] << 24 ) );
    local.X[10] = ( ( (uint32_t) ( data )[( 40 ) ] ) | ( (uint32_t) ( data )[( 40 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 40 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 40 ) + 3] << 24 ) );
    local.X[11] = ( ( (uint32_t) ( data )[( 44 ) ] ) | ( (uint32_t) ( data )[( 44 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 44 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 44 ) + 3] << 24 ) );
    local.X[12] = ( ( (uint32_t) ( data )[( 48 ) ] ) | ( (uint32_t) ( data )[( 48 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 48 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 48 ) + 3] << 24 ) );
    local.X[13] = ( ( (uint32_t) ( data )[( 52 ) ] ) | ( (uint32_t) ( data )[( 52 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 52 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 52 ) + 3] << 24 ) );
    local.X[14] = ( ( (uint32_t) ( data )[( 56 ) ] ) | ( (uint32_t) ( data )[( 56 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 56 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 56 ) + 3] << 24 ) );
    local.X[15] = ( ( (uint32_t) ( data )[( 60 ) ] ) | ( (uint32_t) ( data )[( 60 ) + 1] << 8 ) | ( (uint32_t) ( data )[( 60 ) + 2] << 16 ) | ( (uint32_t) ( data )[( 60 ) + 3] << 24 ) );




# 116 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"

    local.A = ctx->state[0];
    local.B = ctx->state[1];
    local.C = ctx->state[2];
    local.D = ctx->state[3];



    do { (local . A) += (((local . D)) ^ (((local . B)) & (((local . C)) ^ ((local . D))))) + local . X[(0)] + (0xD76AA478); (local . A) = ( ( ((local . A)) << ((7)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((7)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . C)) ^ (((local . A)) & (((local . B)) ^ ((local . C))))) + local . X[(1)] + (0xE8C7B756); (local . D) = ( ( ((local . D)) << ((12)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((12)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . B)) ^ (((local . D)) & (((local . A)) ^ ((local . B))))) + local . X[(2)] + (0x242070DB); (local . C) = ( ( ((local . C)) << ((17)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((17)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . A)) ^ (((local . C)) & (((local . D)) ^ ((local . A))))) + local . X[(3)] + (0xC1BDCEEE); (local . B) = ( ( ((local . B)) << ((22)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((22)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . D)) ^ (((local . B)) & (((local . C)) ^ ((local . D))))) + local . X[(4)] + (0xF57C0FAF); (local . A) = ( ( ((local . A)) << ((7)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((7)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . C)) ^ (((local . A)) & (((local . B)) ^ ((local . C))))) + local . X[(5)] + (0x4787C62A); (local . D) = ( ( ((local . D)) << ((12)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((12)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . B)) ^ (((local . D)) & (((local . A)) ^ ((local . B))))) + local . X[(6)] + (0xA8304613); (local . C) = ( ( ((local . C)) << ((17)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((17)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . A)) ^ (((local . C)) & (((local . D)) ^ ((local . A))))) + local . X[(7)] + (0xFD469501); (local . B) = ( ( ((local . B)) << ((22)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((22)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . D)) ^ (((local . B)) & (((local . C)) ^ ((local . D))))) + local . X[(8)] + (0x698098D8); (local . A) = ( ( ((local . A)) << ((7)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((7)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . C)) ^ (((local . A)) & (((local . B)) ^ ((local . C))))) + local . X[(9)] + (0x8B44F7AF); (local . D) = ( ( ((local . D)) << ((12)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((12)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . B)) ^ (((local . D)) & (((local . A)) ^ ((local . B))))) + local . X[(10)] + (0xFFFF5BB1); (local . C) = ( ( ((local . C)) << ((17)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((17)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . A)) ^ (((local . C)) & (((local . D)) ^ ((local . A))))) + local . X[(11)] + (0x895CD7BE); (local . B) = ( ( ((local . B)) << ((22)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((22)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . D)) ^ (((local . B)) & (((local . C)) ^ ((local . D))))) + local . X[(12)] + (0x6B901122); (local . A) = ( ( ((local . A)) << ((7)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((7)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . C)) ^ (((local . A)) & (((local . B)) ^ ((local . C))))) + local . X[(13)] + (0xFD987193); (local . D) = ( ( ((local . D)) << ((12)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((12)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . B)) ^ (((local . D)) & (((local . A)) ^ ((local . B))))) + local . X[(14)] + (0xA679438E); (local . C) = ( ( ((local . C)) << ((17)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((17)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . A)) ^ (((local . C)) & (((local . D)) ^ ((local . A))))) + local . X[(15)] + (0x49B40821); (local . B) = ( ( ((local . B)) << ((22)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((22)) ) ) ) + (local . C); } while( 0 );





    do { (local . A) += (((local . C)) ^ (((local . D)) & (((local . B)) ^ ((local . C))))) + local . X[(1)] + (0xF61E2562); (local . A) = ( ( ((local . A)) << ((5)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((5)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . C)) & (((local . A)) ^ ((local . B))))) + local . X[(6)] + (0xC040B340); (local . D) = ( ( ((local . D)) << ((9)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((9)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . B)) & (((local . D)) ^ ((local . A))))) + local . X[(11)] + (0x265E5A51); (local . C) = ( ( ((local . C)) << ((14)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((14)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . A)) & (((local . C)) ^ ((local . D))))) + local . X[(0)] + (0xE9B6C7AA); (local . B) = ( ( ((local . B)) << ((20)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((20)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . C)) ^ (((local . D)) & (((local . B)) ^ ((local . C))))) + local . X[(5)] + (0xD62F105D); (local . A) = ( ( ((local . A)) << ((5)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((5)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . C)) & (((local . A)) ^ ((local . B))))) + local . X[(10)] + (0x02441453); (local . D) = ( ( ((local . D)) << ((9)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((9)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . B)) & (((local . D)) ^ ((local . A))))) + local . X[(15)] + (0xD8A1E681); (local . C) = ( ( ((local . C)) << ((14)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((14)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . A)) & (((local . C)) ^ ((local . D))))) + local . X[(4)] + (0xE7D3FBC8); (local . B) = ( ( ((local . B)) << ((20)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((20)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . C)) ^ (((local . D)) & (((local . B)) ^ ((local . C))))) + local . X[(9)] + (0x21E1CDE6); (local . A) = ( ( ((local . A)) << ((5)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((5)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . C)) & (((local . A)) ^ ((local . B))))) + local . X[(14)] + (0xC33707D6); (local . D) = ( ( ((local . D)) << ((9)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((9)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . B)) & (((local . D)) ^ ((local . A))))) + local . X[(3)] + (0xF4D50D87); (local . C) = ( ( ((local . C)) << ((14)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((14)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . A)) & (((local . C)) ^ ((local . D))))) + local . X[(8)] + (0x455A14ED); (local . B) = ( ( ((local . B)) << ((20)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((20)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . C)) ^ (((local . D)) & (((local . B)) ^ ((local . C))))) + local . X[(13)] + (0xA9E3E905); (local . A) = ( ( ((local . A)) << ((5)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((5)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . C)) & (((local . A)) ^ ((local . B))))) + local . X[(2)] + (0xFCEFA3F8); (local . D) = ( ( ((local . D)) << ((9)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((9)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . B)) & (((local . D)) ^ ((local . A))))) + local . X[(7)] + (0x676F02D9); (local . C) = ( ( ((local . C)) << ((14)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((14)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . A)) & (((local . C)) ^ ((local . D))))) + local . X[(12)] + (0x8D2A4C8A); (local . B) = ( ( ((local . B)) << ((20)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((20)) ) ) ) + (local . C); } while( 0 );





    do { (local . A) += (((local . B)) ^ ((local . C)) ^ ((local . D))) + local . X[(5)] + (0xFFFA3942); (local . A) = ( ( ((local . A)) << ((4)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((4)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . A)) ^ ((local . B)) ^ ((local . C))) + local . X[(8)] + (0x8771F681); (local . D) = ( ( ((local . D)) << ((11)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((11)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . D)) ^ ((local . A)) ^ ((local . B))) + local . X[(11)] + (0x6D9D6122); (local . C) = ( ( ((local . C)) << ((16)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((16)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . C)) ^ ((local . D)) ^ ((local . A))) + local . X[(14)] + (0xFDE5380C); (local . B) = ( ( ((local . B)) << ((23)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((23)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . B)) ^ ((local . C)) ^ ((local . D))) + local . X[(1)] + (0xA4BEEA44); (local . A) = ( ( ((local . A)) << ((4)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((4)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . A)) ^ ((local . B)) ^ ((local . C))) + local . X[(4)] + (0x4BDECFA9); (local . D) = ( ( ((local . D)) << ((11)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((11)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . D)) ^ ((local . A)) ^ ((local . B))) + local . X[(7)] + (0xF6BB4B60); (local . C) = ( ( ((local . C)) << ((16)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((16)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . C)) ^ ((local . D)) ^ ((local . A))) + local . X[(10)] + (0xBEBFBC70); (local . B) = ( ( ((local . B)) << ((23)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((23)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . B)) ^ ((local . C)) ^ ((local . D))) + local . X[(13)] + (0x289B7EC6); (local . A) = ( ( ((local . A)) << ((4)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((4)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . A)) ^ ((local . B)) ^ ((local . C))) + local . X[(0)] + (0xEAA127FA); (local . D) = ( ( ((local . D)) << ((11)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((11)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . D)) ^ ((local . A)) ^ ((local . B))) + local . X[(3)] + (0xD4EF3085); (local . C) = ( ( ((local . C)) << ((16)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((16)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . C)) ^ ((local . D)) ^ ((local . A))) + local . X[(6)] + (0x04881D05); (local . B) = ( ( ((local . B)) << ((23)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((23)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . B)) ^ ((local . C)) ^ ((local . D))) + local . X[(9)] + (0xD9D4D039); (local . A) = ( ( ((local . A)) << ((4)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((4)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . A)) ^ ((local . B)) ^ ((local . C))) + local . X[(12)] + (0xE6DB99E5); (local . D) = ( ( ((local . D)) << ((11)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((11)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . D)) ^ ((local . A)) ^ ((local . B))) + local . X[(15)] + (0x1FA27CF8); (local . C) = ( ( ((local . C)) << ((16)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((16)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . C)) ^ ((local . D)) ^ ((local . A))) + local . X[(2)] + (0xC4AC5665); (local . B) = ( ( ((local . B)) << ((23)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((23)) ) ) ) + (local . C); } while( 0 );





    do { (local . A) += (((local . C)) ^ (((local . B)) | ~((local . D)))) + local . X[(0)] + (0xF4292244); (local . A) = ( ( ((local . A)) << ((6)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((6)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . A)) | ~((local . C)))) + local . X[(7)] + (0x432AFF97); (local . D) = ( ( ((local . D)) << ((10)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((10)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . D)) | ~((local . B)))) + local . X[(14)] + (0xAB9423A7); (local . C) = ( ( ((local . C)) << ((15)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((15)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . C)) | ~((local . A)))) + local . X[(5)] + (0xFC93A039); (local . B) = ( ( ((local . B)) << ((21)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((21)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . C)) ^ (((local . B)) | ~((local . D)))) + local . X[(12)] + (0x655B59C3); (local . A) = ( ( ((local . A)) << ((6)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((6)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . A)) | ~((local . C)))) + local . X[(3)] + (0x8F0CCC92); (local . D) = ( ( ((local . D)) << ((10)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((10)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . D)) | ~((local . B)))) + local . X[(10)] + (0xFFEFF47D); (local . C) = ( ( ((local . C)) << ((15)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((15)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . C)) | ~((local . A)))) + local . X[(1)] + (0x85845DD1); (local . B) = ( ( ((local . B)) << ((21)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((21)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . C)) ^ (((local . B)) | ~((local . D)))) + local . X[(8)] + (0x6FA87E4F); (local . A) = ( ( ((local . A)) << ((6)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((6)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . A)) | ~((local . C)))) + local . X[(15)] + (0xFE2CE6E0); (local . D) = ( ( ((local . D)) << ((10)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((10)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . D)) | ~((local . B)))) + local . X[(6)] + (0xA3014314); (local . C) = ( ( ((local . C)) << ((15)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((15)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . C)) | ~((local . A)))) + local . X[(13)] + (0x4E0811A1); (local . B) = ( ( ((local . B)) << ((21)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((21)) ) ) ) + (local . C); } while( 0 );
    do { (local . A) += (((local . C)) ^ (((local . B)) | ~((local . D)))) + local . X[(4)] + (0xF7537E82); (local . A) = ( ( ((local . A)) << ((6)) ) | ( ( ((local . A)) & 0xFFFFFFFF) >> ( 32 - ((6)) ) ) ) + (local . B); } while( 0 );
    do { (local . D) += (((local . B)) ^ (((local . A)) | ~((local . C)))) + local . X[(11)] + (0xBD3AF235); (local . D) = ( ( ((local . D)) << ((10)) ) | ( ( ((local . D)) & 0xFFFFFFFF) >> ( 32 - ((10)) ) ) ) + (local . A); } while( 0 );
    do { (local . C) += (((local . A)) ^ (((local . D)) | ~((local . B)))) + local . X[(2)] + (0x2AD7D2BB); (local . C) = ( ( ((local . C)) << ((15)) ) | ( ( ((local . C)) & 0xFFFFFFFF) >> ( 32 - ((15)) ) ) ) + (local . D); } while( 0 );
    do { (local . B) += (((local . D)) ^ (((local . C)) | ~((local . A)))) + local . X[(9)] + (0xEB86D391); (local . B) = ( ( ((local . B)) << ((21)) ) | ( ( ((local . B)) & 0xFFFFFFFF) >> ( 32 - ((21)) ) ) ) + (local . C); } while( 0 );



    ctx->state[0] += local.A;
    ctx->state[1] += local.B;
    ctx->state[2] += local.C;
    ctx->state[3] += local.D;

    /* Zeroise variables to clear sensitive data from memory. */
    mbedtls_platform_zeroize( &local, sizeof( local ) );

    return( 0 );
}



/*
 * MD5 process buffer
 */
int mbedtls_md5_update( mbedtls_md5_context *ctx,
                            const unsigned char *input,
                            size_t ilen )
{
    int ret = -0x006E;
    size_t fill;
    uint32_t left;

    if( ilen == 0 )
        return( 0 );

    left = ctx->total[0] & 0x3F;
    fill = 64 - left;

    ctx->total[0] += (uint32_t) ilen;
    ctx->total[0] &= 0xFFFFFFFF;

    if( ctx->total[0] < (uint32_t) ilen )
        ctx->total[1]++;

    if( left && ilen >= fill )
    {
        memcpy( (void *) (ctx->buffer + left), input, fill );
        if( ( ret = mbedtls_internal_md5_process( ctx, ctx->buffer ) ) != 0 )
            return( ret );

        input += fill;
        ilen  -= fill;
        left = 0;
    }

    while( ilen >= 64 )
    {
        if( ( ret = mbedtls_internal_md5_process( ctx, input ) ) != 0 )
            return( ret );

        input += 64;
        ilen  -= 64;
    }

    if( ilen > 0 )
    {
        memcpy( (void *) (ctx->buffer + left), input, ilen );
    }

    return( 0 );
}

/*
 * MD5 final digest
 */
int mbedtls_md5_finish( mbedtls_md5_context *ctx,
                            unsigned char output[16] )
{
    int ret = -0x006E;
    uint32_t used;
    uint32_t high, low;

    /*
     * Add padding: 0x80 then 0x00 until 8 bytes remain for the length
     */
    used = ctx->total[0] & 0x3F;

    ctx->buffer[used++] = 0x80;

    if( used <= 56 )
    {
        /* Enough room for padding + length in current block */
        memset( ctx->buffer + used, 0, 56 - used );
    }
    else
    {
        /* We'll need an extra block */
        memset( ctx->buffer + used, 0, 64 - used );

        if( ( ret = mbedtls_internal_md5_process( ctx, ctx->buffer ) ) != 0 )
            return( ret );

        memset( ctx->buffer, 0, 56 );
    }

    /*
     * Add message length
     */
    high = ( ctx->total[0] >> 29 )
         | ( ctx->total[1] <<  3 );
    low  = ( ctx->total[0] <<  3 );

    { ( ctx->buffer )[( 56 ) ] = ( (uint8_t) ( ( low ) & 0xff ) ); ( ctx->buffer )[( 56 ) + 1] = ( (uint8_t) ( ( ( low ) >> 8 ) & 0xff ) ); ( ctx->buffer )[( 56 ) + 2] = ( (uint8_t) ( ( ( low ) >> 16 ) & 0xff ) ); ( ctx->buffer )[( 56 ) + 3] = ( (uint8_t) ( ( ( low ) >> 24 ) & 0xff ) ); };
    { ( ctx->buffer )[( 60 ) ] = ( (uint8_t) ( ( high ) & 0xff ) ); ( ctx->buffer )[( 60 ) + 1] = ( (uint8_t) ( ( ( high ) >> 8 ) & 0xff ) ); ( ctx->buffer )[( 60 ) + 2] = ( (uint8_t) ( ( ( high ) >> 16 ) & 0xff ) ); ( ctx->buffer )[( 60 ) + 3] = ( (uint8_t) ( ( ( high ) >> 24 ) & 0xff ) ); };

    if( ( ret = mbedtls_internal_md5_process( ctx, ctx->buffer ) ) != 0 )
        return( ret );

    /*
     * Output final state
     */
    { ( output )[( 0 ) ] = ( (uint8_t) ( ( ctx->state[0] ) & 0xff ) ); ( output )[( 0 ) + 1] = ( (uint8_t) ( ( ( ctx->state[0] ) >> 8 ) & 0xff ) ); ( output )[( 0 ) + 2] = ( (uint8_t) ( ( ( ctx->state[0] ) >> 16 ) & 0xff ) ); ( output )[( 0 ) + 3] = ( (uint8_t) ( ( ( ctx->state[0] ) >> 24 ) & 0xff ) ); };
    { ( output )[( 4 ) ] = ( (uint8_t) ( ( ctx->state[1] ) & 0xff ) ); ( output )[( 4 ) + 1] = ( (uint8_t) ( ( ( ctx->state[1] ) >> 8 ) & 0xff ) ); ( output )[( 4 ) + 2] = ( (uint8_t) ( ( ( ctx->state[1] ) >> 16 ) & 0xff ) ); ( output )[( 4 ) + 3] = ( (uint8_t) ( ( ( ctx->state[1] ) >> 24 ) & 0xff ) ); };
    { ( output )[( 8 ) ] = ( (uint8_t) ( ( ctx->state[2] ) & 0xff ) ); ( output )[( 8 ) + 1] = ( (uint8_t) ( ( ( ctx->state[2] ) >> 8 ) & 0xff ) ); ( output )[( 8 ) + 2] = ( (uint8_t) ( ( ( ctx->state[2] ) >> 16 ) & 0xff ) ); ( output )[( 8 ) + 3] = ( (uint8_t) ( ( ( ctx->state[2] ) >> 24 ) & 0xff ) ); };
    { ( output )[( 12 ) ] = ( (uint8_t) ( ( ctx->state[3] ) & 0xff ) ); ( output )[( 12 ) + 1] = ( (uint8_t) ( ( ( ctx->state[3] ) >> 8 ) & 0xff ) ); ( output )[( 12 ) + 2] = ( (uint8_t) ( ( ( ctx->state[3] ) >> 16 ) & 0xff ) ); ( output )[( 12 ) + 3] = ( (uint8_t) ( ( ( ctx->state[3] ) >> 24 ) & 0xff ) ); };

    return( 0 );
}



/*
 * output = MD5( input buffer )
 */
int mbedtls_md5( const unsigned char *input,
                     size_t ilen,
                     unsigned char output[16] )
{
    int ret = -0x006E;
    mbedtls_md5_context ctx;

    mbedtls_md5_init( &ctx );

    if( ( ret = mbedtls_md5_starts( &ctx ) ) != 0 )
        goto exit;

    if( ( ret = mbedtls_md5_update( &ctx, input, ilen ) ) != 0 )
        goto exit;

    if( ( ret = mbedtls_md5_finish( &ctx, output ) ) != 0 )
        goto exit;

exit:
    mbedtls_md5_free( &ctx );

    return( ret );
}

# 434 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\md5.c"

