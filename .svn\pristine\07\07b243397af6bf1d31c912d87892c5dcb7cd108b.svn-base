/*** Autogenerated by WIDL 7.7 from include/windows.media.speechrecognition.idl - Do not edit ***/

#ifdef _WIN32
#ifndef __REQUIRED_RPCNDR_H_VERSION__
#define __REQUIRED_RPCNDR_H_VERSION__ 475
#endif
#include <rpc.h>
#include <rpcndr.h>
#endif

#ifndef COM_NO_WINDOWS_H
#include <windows.h>
#include <ole2.h>
#endif

#ifndef __windows_media_speechrecognition_h__
#define __windows_media_speechrecognition_h__

/* Forward declarations */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionCompletedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechContinuousRecognitionCompletedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionResultGeneratedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechContinuousRecognitionResultGeneratedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionSession
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechContinuousRecognitionSession;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionCompilationResult
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionCompilationResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionConstraint;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionHypothesis
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionHypothesis;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionHypothesisGeneratedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionHypothesisGeneratedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionListConstraint
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionListConstraint;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionListConstraintFactory
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionListConstraintFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionQualityDegradingEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionQualityDegradingEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionResult2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionSemanticInterpretation
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionSemanticInterpretation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizer2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerFactory
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerStateChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerStateChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerStatics2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerTimeouts
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerTimeouts;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerUIOptions
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerUIOptions;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechContinuousRecognitionCompletedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionCompletedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionCompletedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechContinuousRecognitionResultGeneratedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionResultGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionResultGeneratedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionSession_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionSession_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechContinuousRecognitionSession;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionSession __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionSession;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionSession_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionCompilationResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionCompilationResult_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognitionCompilationResult;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionCompilationResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionCompilationResult;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionCompilationResult_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesis_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesis_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognitionHypothesis;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesis __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesis;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesis_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognitionHypothesisGeneratedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesisGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesisGeneratedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionListConstraint_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionListConstraint_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognitionListConstraint;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionListConstraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionListConstraint;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionListConstraint_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognitionQualityDegradingEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionQualityDegradingEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionQualityDegradingEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResult_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognitionResult;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResult;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResult_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionSemanticInterpretation_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionSemanticInterpretation_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognitionSemanticInterpretation;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionSemanticInterpretation __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionSemanticInterpretation;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionSemanticInterpretation_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizer_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizer_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognizer;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizer __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizer;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizer_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognizerStateChangedEventArgs;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerStateChangedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerStateChangedEventArgs;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerStateChangedEventArgs_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerTimeouts_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerTimeouts_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognizerTimeouts;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerTimeouts __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerTimeouts;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerTimeouts_FWD_DEFINED__ */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerUIOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerUIOptions_FWD_DEFINED__
#ifdef __cplusplus
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                class SpeechRecognizerUIOptions;
            }
        }
    }
}
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerUIOptions __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerUIOptions;
#endif /* defined __cplusplus */
#endif /* defined ____x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerUIOptions_FWD_DEFINED__ */

#ifndef ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs* >
#endif /* __cplusplus */
#endif

#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
typedef interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs;
#ifdef __cplusplus
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs* >
#endif /* __cplusplus */
#endif

/* Headers for imported files */

#include <inspectable.h>
#include <asyncinfo.h>
#include <eventtoken.h>
#include <windowscontracts.h>
#include <windows.foundation.h>
#include <windows.globalization.h>

#ifdef __cplusplus
extern "C" {
#endif

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionMode __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionMode;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionAudioProblem __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionAudioProblem;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConfidence __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConfidence;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintType __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintType;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus;
#endif /* __cplusplus */

#ifndef __cplusplus
typedef enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState;
#endif /* __cplusplus */

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionCompletedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechContinuousRecognitionCompletedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionResultGeneratedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechContinuousRecognitionResultGeneratedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionSession
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechContinuousRecognitionSession;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionCompilationResult
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionCompilationResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionConstraint;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionHypothesis
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionHypothesis;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionHypothesisGeneratedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionHypothesisGeneratedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionListConstraint
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionListConstraint;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionListConstraintFactory
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionListConstraintFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionQualityDegradingEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionQualityDegradingEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionResult;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionResult2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionSemanticInterpretation
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognitionSemanticInterpretation;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizer;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizer2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerFactory
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerFactory;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerStateChangedEventArgs
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerStateChangedEventArgs;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerStatics
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerStatics;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerStatics2
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerStatics2;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerTimeouts
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerTimeouts;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_FWD_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_FWD_DEFINED__
typedef interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions;
#ifdef __cplusplus
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerUIOptions
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                interface ISpeechRecognizerUIOptions;
            }
        }
    }
}
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
#define ____FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_FWD_DEFINED__
typedef interface __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint;
#ifdef __cplusplus
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::Collections::IIterable<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::Collections::IIterator<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* >
#endif /* __cplusplus */
#endif

#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_FWD_DEFINED__
typedef interface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult;
#ifdef __cplusplus
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* >
#endif /* __cplusplus */
#endif

#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                enum SpeechContinuousRecognitionMode {
                    SpeechContinuousRecognitionMode_Default = 0,
                    SpeechContinuousRecognitionMode_PauseOnRecognition = 1
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionMode {
    SpeechContinuousRecognitionMode_Default = 0,
    SpeechContinuousRecognitionMode_PauseOnRecognition = 1
};
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define SpeechContinuousRecognitionMode __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionMode
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                enum SpeechRecognitionAudioProblem {
                    SpeechRecognitionAudioProblem_None = 0,
                    SpeechRecognitionAudioProblem_TooNoisy = 1,
                    SpeechRecognitionAudioProblem_NoSignal = 2,
                    SpeechRecognitionAudioProblem_TooLoud = 3,
                    SpeechRecognitionAudioProblem_TooQuiet = 4,
                    SpeechRecognitionAudioProblem_TooFast = 5,
                    SpeechRecognitionAudioProblem_TooSlow = 6
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionAudioProblem {
    SpeechRecognitionAudioProblem_None = 0,
    SpeechRecognitionAudioProblem_TooNoisy = 1,
    SpeechRecognitionAudioProblem_NoSignal = 2,
    SpeechRecognitionAudioProblem_TooLoud = 3,
    SpeechRecognitionAudioProblem_TooQuiet = 4,
    SpeechRecognitionAudioProblem_TooFast = 5,
    SpeechRecognitionAudioProblem_TooSlow = 6
};
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define SpeechRecognitionAudioProblem __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionAudioProblem
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                enum SpeechRecognitionConfidence {
                    SpeechRecognitionConfidence_High = 0,
                    SpeechRecognitionConfidence_Medium = 1,
                    SpeechRecognitionConfidence_Low = 2,
                    SpeechRecognitionConfidence_Rejected = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConfidence {
    SpeechRecognitionConfidence_High = 0,
    SpeechRecognitionConfidence_Medium = 1,
    SpeechRecognitionConfidence_Low = 2,
    SpeechRecognitionConfidence_Rejected = 3
};
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define SpeechRecognitionConfidence __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConfidence
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                enum SpeechRecognitionConstraintProbability {
                    SpeechRecognitionConstraintProbability_Default = 0,
                    SpeechRecognitionConstraintProbability_Min = 1,
                    SpeechRecognitionConstraintProbability_Max = 2
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability {
    SpeechRecognitionConstraintProbability_Default = 0,
    SpeechRecognitionConstraintProbability_Min = 1,
    SpeechRecognitionConstraintProbability_Max = 2
};
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define SpeechRecognitionConstraintProbability __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                enum SpeechRecognitionConstraintType {
                    SpeechRecognitionConstraintType_Topic = 0,
                    SpeechRecognitionConstraintType_List = 1,
                    SpeechRecognitionConstraintType_Grammar = 2,
                    SpeechRecognitionConstraintType_VoiceCommandDefinition = 3
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintType {
    SpeechRecognitionConstraintType_Topic = 0,
    SpeechRecognitionConstraintType_List = 1,
    SpeechRecognitionConstraintType_Grammar = 2,
    SpeechRecognitionConstraintType_VoiceCommandDefinition = 3
};
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define SpeechRecognitionConstraintType __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintType
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                enum SpeechRecognitionResultStatus {
                    SpeechRecognitionResultStatus_Success = 0,
                    SpeechRecognitionResultStatus_TopicLanguageNotSupported = 1,
                    SpeechRecognitionResultStatus_GrammarLanguageMismatch = 2,
                    SpeechRecognitionResultStatus_GrammarCompilationFailure = 3,
                    SpeechRecognitionResultStatus_AudioQualityFailure = 4,
                    SpeechRecognitionResultStatus_UserCanceled = 5,
                    SpeechRecognitionResultStatus_Unknown = 6,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    SpeechRecognitionResultStatus_TimeoutExceeded = 7,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    SpeechRecognitionResultStatus_PauseLimitExceeded = 8,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    SpeechRecognitionResultStatus_NetworkFailure = 9,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    SpeechRecognitionResultStatus_MicrophoneUnavailable = 10
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus {
    SpeechRecognitionResultStatus_Success = 0,
    SpeechRecognitionResultStatus_TopicLanguageNotSupported = 1,
    SpeechRecognitionResultStatus_GrammarLanguageMismatch = 2,
    SpeechRecognitionResultStatus_GrammarCompilationFailure = 3,
    SpeechRecognitionResultStatus_AudioQualityFailure = 4,
    SpeechRecognitionResultStatus_UserCanceled = 5,
    SpeechRecognitionResultStatus_Unknown = 6,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    SpeechRecognitionResultStatus_TimeoutExceeded = 7,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    SpeechRecognitionResultStatus_PauseLimitExceeded = 8,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    SpeechRecognitionResultStatus_NetworkFailure = 9,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    SpeechRecognitionResultStatus_MicrophoneUnavailable = 10
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define SpeechRecognitionResultStatus __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifdef __cplusplus
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                enum SpeechRecognizerState {
                    SpeechRecognizerState_Idle = 0,
                    SpeechRecognizerState_Capturing = 1,
                    SpeechRecognizerState_Processing = 2,
                    SpeechRecognizerState_SoundStarted = 3,
                    SpeechRecognizerState_SoundEnded = 4,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    SpeechRecognizerState_SpeechDetected = 5,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
                    SpeechRecognizerState_Paused = 6
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
                };
            }
        }
    }
}
extern "C" {
#else
enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState {
    SpeechRecognizerState_Idle = 0,
    SpeechRecognizerState_Capturing = 1,
    SpeechRecognizerState_Processing = 2,
    SpeechRecognizerState_SoundStarted = 3,
    SpeechRecognizerState_SoundEnded = 4,
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    SpeechRecognizerState_SpeechDetected = 5,
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
    SpeechRecognizerState_Paused = 6
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
};
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define SpeechRecognizerState __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */
/*****************************************************************************
 * ISpeechContinuousRecognitionCompletedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs, 0xe3d069bb, 0xe30c, 0x5e18, 0x42,0x4b, 0x7f,0xbe,0x81,0xf8,0xfb,0xd0);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("e3d069bb-e30c-5e18-424b-7fbe81f8fbd0")
                ISpeechContinuousRecognitionCompletedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Status(
                        enum SpeechRecognitionResultStatus *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs, 0xe3d069bb, 0xe30c, 0x5e18, 0x42,0x4b, 0x7f,0xbe,0x81,0xf8,0xfb,0xd0)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISpeechContinuousRecognitionCompletedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechContinuousRecognitionCompletedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechContinuousRecognitionCompletedEventArgs methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_get_Status(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus *value) {
    return This->lpVtbl->get_Status(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechContinuousRecognitionCompletedEventArgs IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs
#define ISpeechContinuousRecognitionCompletedEventArgsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgsVtbl
#define ISpeechContinuousRecognitionCompletedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs
#define ISpeechContinuousRecognitionCompletedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_QueryInterface
#define ISpeechContinuousRecognitionCompletedEventArgs_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_AddRef
#define ISpeechContinuousRecognitionCompletedEventArgs_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_Release
#define ISpeechContinuousRecognitionCompletedEventArgs_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetIids
#define ISpeechContinuousRecognitionCompletedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetRuntimeClassName
#define ISpeechContinuousRecognitionCompletedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_GetTrustLevel
#define ISpeechContinuousRecognitionCompletedEventArgs_get_Status __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_get_Status
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechContinuousRecognitionResultGeneratedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs, 0x19091e1e, 0x6e7e, 0x5a46, 0x40,0xfb, 0x76,0x59,0x4f,0x78,0x65,0x04);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("19091e1e-6e7e-5a46-40fb-76594f786504")
                ISpeechContinuousRecognitionResultGeneratedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Result(
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs, 0x19091e1e, 0x6e7e, 0x5a46, 0x40,0xfb, 0x76,0x59,0x4f,0x78,0x65,0x04)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISpeechContinuousRecognitionResultGeneratedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Result)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechContinuousRecognitionResultGeneratedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_get_Result(This,value) (This)->lpVtbl->get_Result(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechContinuousRecognitionResultGeneratedEventArgs methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_get_Result(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **value) {
    return This->lpVtbl->get_Result(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechContinuousRecognitionResultGeneratedEventArgs IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs
#define ISpeechContinuousRecognitionResultGeneratedEventArgsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgsVtbl
#define ISpeechContinuousRecognitionResultGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs
#define ISpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface
#define ISpeechContinuousRecognitionResultGeneratedEventArgs_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_AddRef
#define ISpeechContinuousRecognitionResultGeneratedEventArgs_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_Release
#define ISpeechContinuousRecognitionResultGeneratedEventArgs_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetIids
#define ISpeechContinuousRecognitionResultGeneratedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetRuntimeClassName
#define ISpeechContinuousRecognitionResultGeneratedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_GetTrustLevel
#define ISpeechContinuousRecognitionResultGeneratedEventArgs_get_Result __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_get_Result
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechContinuousRecognitionSession interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession, 0x6a213c04, 0x6614, 0x49f8, 0x99,0xa2, 0xb5,0xe9,0xb3,0xa0,0x85,0xc8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("6a213c04-6614-49f8-99a2-b5e9b3a085c8")
                ISpeechContinuousRecognitionSession : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_AutoStopSilenceTimeout(
                        struct TimeSpan *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_AutoStopSilenceTimeout(
                        struct TimeSpan value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StartAsync(
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StartWithModeAsync(
                        enum SpeechContinuousRecognitionMode mode,
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StopAsync(
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CancelAsync(
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                    virtual HRESULT STDMETHODCALLTYPE PauseAsync(
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                    virtual HRESULT STDMETHODCALLTYPE Resume(
                        ) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_Completed(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_Completed(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_ResultGenerated(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs* > *value,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_ResultGenerated(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession, 0x6a213c04, 0x6614, 0x49f8, 0x99,0xa2, 0xb5,0xe9,0xb3,0xa0,0x85,0xc8)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSessionVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        TrustLevel *trustLevel);

    /*** ISpeechContinuousRecognitionSession methods ***/
    HRESULT (STDMETHODCALLTYPE *get_AutoStopSilenceTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_AutoStopSilenceTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *StartAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    HRESULT (STDMETHODCALLTYPE *StartWithModeAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionMode mode,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    HRESULT (STDMETHODCALLTYPE *StopAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    HRESULT (STDMETHODCALLTYPE *CancelAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    HRESULT (STDMETHODCALLTYPE *PauseAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    HRESULT (STDMETHODCALLTYPE *Resume)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This);

    HRESULT (STDMETHODCALLTYPE *add_Completed)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_Completed)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_ResultGenerated)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs *value,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_ResultGenerated)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSessionVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSessionVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechContinuousRecognitionSession methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_get_AutoStopSilenceTimeout(This,value) (This)->lpVtbl->get_AutoStopSilenceTimeout(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_put_AutoStopSilenceTimeout(This,value) (This)->lpVtbl->put_AutoStopSilenceTimeout(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StartAsync(This,action) (This)->lpVtbl->StartAsync(This,action)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StartWithModeAsync(This,mode,action) (This)->lpVtbl->StartWithModeAsync(This,mode,action)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StopAsync(This,action) (This)->lpVtbl->StopAsync(This,action)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_CancelAsync(This,action) (This)->lpVtbl->CancelAsync(This,action)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_PauseAsync(This,action) (This)->lpVtbl->PauseAsync(This,action)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_Resume(This) (This)->lpVtbl->Resume(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_add_Completed(This,value,token) (This)->lpVtbl->add_Completed(This,value,token)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_remove_Completed(This,token) (This)->lpVtbl->remove_Completed(This,token)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_add_ResultGenerated(This,value,token) (This)->lpVtbl->add_ResultGenerated(This,value,token)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_remove_ResultGenerated(This,token) (This)->lpVtbl->remove_ResultGenerated(This,token)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechContinuousRecognitionSession methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_get_AutoStopSilenceTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_AutoStopSilenceTimeout(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_put_AutoStopSilenceTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_AutoStopSilenceTimeout(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StartAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->StartAsync(This,action);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StartWithModeAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechContinuousRecognitionMode mode,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->StartWithModeAsync(This,mode,action);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StopAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->StopAsync(This,action);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_CancelAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->CancelAsync(This,action);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_PauseAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->PauseAsync(This,action);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_Resume(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This) {
    return This->lpVtbl->Resume(This);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_add_Completed(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_Completed(This,value,token);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_remove_Completed(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_Completed(This,token);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_add_ResultGenerated(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs *value,EventRegistrationToken *token) {
    return This->lpVtbl->add_ResultGenerated(This,value,token);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_remove_ResultGenerated(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_ResultGenerated(This,token);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechContinuousRecognitionSession IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession
#define ISpeechContinuousRecognitionSessionVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSessionVtbl
#define ISpeechContinuousRecognitionSession __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession
#define ISpeechContinuousRecognitionSession_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_QueryInterface
#define ISpeechContinuousRecognitionSession_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_AddRef
#define ISpeechContinuousRecognitionSession_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_Release
#define ISpeechContinuousRecognitionSession_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetIids
#define ISpeechContinuousRecognitionSession_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetRuntimeClassName
#define ISpeechContinuousRecognitionSession_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_GetTrustLevel
#define ISpeechContinuousRecognitionSession_get_AutoStopSilenceTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_get_AutoStopSilenceTimeout
#define ISpeechContinuousRecognitionSession_put_AutoStopSilenceTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_put_AutoStopSilenceTimeout
#define ISpeechContinuousRecognitionSession_StartAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StartAsync
#define ISpeechContinuousRecognitionSession_StartWithModeAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StartWithModeAsync
#define ISpeechContinuousRecognitionSession_StopAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_StopAsync
#define ISpeechContinuousRecognitionSession_CancelAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_CancelAsync
#define ISpeechContinuousRecognitionSession_PauseAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_PauseAsync
#define ISpeechContinuousRecognitionSession_Resume __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_Resume
#define ISpeechContinuousRecognitionSession_add_Completed __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_add_Completed
#define ISpeechContinuousRecognitionSession_remove_Completed __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_remove_Completed
#define ISpeechContinuousRecognitionSession_add_ResultGenerated __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_add_ResultGenerated
#define ISpeechContinuousRecognitionSession_remove_ResultGenerated __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_remove_ResultGenerated
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionCompilationResult interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult, 0x407e6c5d, 0x6ac7, 0x4da4, 0x9c,0xc1, 0x2f,0xce,0x32,0xcf,0x74,0x89);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("407e6c5d-6ac7-4da4-9cc1-2fce32cf7489")
                ISpeechRecognitionCompilationResult : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Status(
                        enum SpeechRecognitionResultStatus *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult, 0x407e6c5d, 0x6ac7, 0x4da4, 0x9c,0xc1, 0x2f,0xce,0x32,0xcf,0x74,0x89)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionCompilationResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResultVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionCompilationResult methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionCompilationResult methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_get_Status(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus *value) {
    return This->lpVtbl->get_Status(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionCompilationResult IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult
#define ISpeechRecognitionCompilationResultVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResultVtbl
#define ISpeechRecognitionCompilationResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult
#define ISpeechRecognitionCompilationResult_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_QueryInterface
#define ISpeechRecognitionCompilationResult_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_AddRef
#define ISpeechRecognitionCompilationResult_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_Release
#define ISpeechRecognitionCompilationResult_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetIids
#define ISpeechRecognitionCompilationResult_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetRuntimeClassName
#define ISpeechRecognitionCompilationResult_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_GetTrustLevel
#define ISpeechRecognitionCompilationResult_get_Status __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_get_Status
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionConstraint interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint, 0x79ac1628, 0x4d68, 0x43c4, 0x89,0x11, 0x40,0xdc,0x41,0x01,0xb5,0x5b);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("79ac1628-4d68-43c4-8911-40dc4101b55b")
                ISpeechRecognitionConstraint : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_IsEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_IsEnabled(
                        boolean value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Tag(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Tag(
                        HSTRING value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Type(
                        enum SpeechRecognitionConstraintType *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Probability(
                        enum SpeechRecognitionConstraintProbability *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_Probability(
                        enum SpeechRecognitionConstraintProbability value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint, 0x79ac1628, 0x4d68, 0x43c4, 0x89,0x11, 0x40,0xdc,0x41,0x01,0xb5,0x5b)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraintVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionConstraint methods ***/
    HRESULT (STDMETHODCALLTYPE *get_IsEnabled)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsEnabled)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_Tag)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_Tag)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_Type)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintType *value);

    HRESULT (STDMETHODCALLTYPE *get_Probability)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability *value);

    HRESULT (STDMETHODCALLTYPE *put_Probability)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraintVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraintVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionConstraint methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_IsEnabled(This,value) (This)->lpVtbl->get_IsEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_IsEnabled(This,value) (This)->lpVtbl->put_IsEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Tag(This,value) (This)->lpVtbl->get_Tag(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_Tag(This,value) (This)->lpVtbl->put_Tag(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Type(This,value) (This)->lpVtbl->get_Type(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Probability(This,value) (This)->lpVtbl->get_Probability(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_Probability(This,value) (This)->lpVtbl->put_Probability(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionConstraint methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_IsEnabled(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,boolean *value) {
    return This->lpVtbl->get_IsEnabled(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_IsEnabled(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,boolean value) {
    return This->lpVtbl->put_IsEnabled(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Tag(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,HSTRING *value) {
    return This->lpVtbl->get_Tag(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_Tag(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,HSTRING value) {
    return This->lpVtbl->put_Tag(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Type(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintType *value) {
    return This->lpVtbl->get_Type(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Probability(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability *value) {
    return This->lpVtbl->get_Probability(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_Probability(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConstraintProbability value) {
    return This->lpVtbl->put_Probability(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionConstraint IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint
#define ISpeechRecognitionConstraintVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraintVtbl
#define ISpeechRecognitionConstraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint
#define ISpeechRecognitionConstraint_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_QueryInterface
#define ISpeechRecognitionConstraint_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_AddRef
#define ISpeechRecognitionConstraint_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_Release
#define ISpeechRecognitionConstraint_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetIids
#define ISpeechRecognitionConstraint_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetRuntimeClassName
#define ISpeechRecognitionConstraint_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_GetTrustLevel
#define ISpeechRecognitionConstraint_get_IsEnabled __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_IsEnabled
#define ISpeechRecognitionConstraint_put_IsEnabled __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_IsEnabled
#define ISpeechRecognitionConstraint_get_Tag __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Tag
#define ISpeechRecognitionConstraint_put_Tag __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_Tag
#define ISpeechRecognitionConstraint_get_Type __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Type
#define ISpeechRecognitionConstraint_get_Probability __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_get_Probability
#define ISpeechRecognitionConstraint_put_Probability __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_put_Probability
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionHypothesis interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis, 0x7a7b25b0, 0x99c5, 0x4f7d, 0xbf,0x84, 0x10,0xaa,0x13,0x02,0xb6,0x34);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("7a7b25b0-99c5-4f7d-bf84-10aa1302b634")
                ISpeechRecognitionHypothesis : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Text(
                        HSTRING *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis, 0x7a7b25b0, 0x99c5, 0x4f7d, 0xbf,0x84, 0x10,0xaa,0x13,0x02,0xb6,0x34)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionHypothesis methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Text)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis *This,
        HSTRING *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionHypothesis methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_get_Text(This,value) (This)->lpVtbl->get_Text(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionHypothesis methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_get_Text(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis* This,HSTRING *value) {
    return This->lpVtbl->get_Text(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionHypothesis IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis
#define ISpeechRecognitionHypothesisVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisVtbl
#define ISpeechRecognitionHypothesis __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis
#define ISpeechRecognitionHypothesis_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_QueryInterface
#define ISpeechRecognitionHypothesis_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_AddRef
#define ISpeechRecognitionHypothesis_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_Release
#define ISpeechRecognitionHypothesis_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetIids
#define ISpeechRecognitionHypothesis_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetRuntimeClassName
#define ISpeechRecognitionHypothesis_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_GetTrustLevel
#define ISpeechRecognitionHypothesis_get_Text __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_get_Text
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionHypothesisGeneratedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs, 0x55161a7a, 0x8023, 0x5866, 0x41,0x1d, 0x12,0x13,0xbb,0x27,0x14,0x76);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("55161a7a-8023-5866-411d-1213bb271476")
                ISpeechRecognitionHypothesisGeneratedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Hypothesis(
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionHypothesis **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs, 0x55161a7a, 0x8023, 0x5866, 0x41,0x1d, 0x12,0x13,0xbb,0x27,0x14,0x76)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionHypothesisGeneratedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Hypothesis)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionHypothesisGeneratedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_get_Hypothesis(This,value) (This)->lpVtbl->get_Hypothesis(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionHypothesisGeneratedEventArgs methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_get_Hypothesis(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesis **value) {
    return This->lpVtbl->get_Hypothesis(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionHypothesisGeneratedEventArgs IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs
#define ISpeechRecognitionHypothesisGeneratedEventArgsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgsVtbl
#define ISpeechRecognitionHypothesisGeneratedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs
#define ISpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface
#define ISpeechRecognitionHypothesisGeneratedEventArgs_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_AddRef
#define ISpeechRecognitionHypothesisGeneratedEventArgs_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_Release
#define ISpeechRecognitionHypothesisGeneratedEventArgs_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetIids
#define ISpeechRecognitionHypothesisGeneratedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetRuntimeClassName
#define ISpeechRecognitionHypothesisGeneratedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_GetTrustLevel
#define ISpeechRecognitionHypothesisGeneratedEventArgs_get_Hypothesis __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_get_Hypothesis
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionListConstraint interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint, 0x09c487e9, 0xe4ad, 0x4526, 0x81,0xf2, 0x49,0x46,0xfb,0x48,0x1d,0x98);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("09c487e9-e4ad-4526-81f2-4946fb481d98")
                ISpeechRecognitionListConstraint : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Commands(
                        ABI::Windows::Foundation::Collections::IVector<HSTRING > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint, 0x09c487e9, 0xe4ad, 0x4526, 0x81,0xf2, 0x49,0x46,0xfb,0x48,0x1d,0x98)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionListConstraint methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Commands)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint *This,
        __FIVector_1_HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionListConstraint methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_get_Commands(This,value) (This)->lpVtbl->get_Commands(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionListConstraint methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_get_Commands(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint* This,__FIVector_1_HSTRING **value) {
    return This->lpVtbl->get_Commands(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionListConstraint IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint
#define ISpeechRecognitionListConstraintVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintVtbl
#define ISpeechRecognitionListConstraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint
#define ISpeechRecognitionListConstraint_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_QueryInterface
#define ISpeechRecognitionListConstraint_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_AddRef
#define ISpeechRecognitionListConstraint_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_Release
#define ISpeechRecognitionListConstraint_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetIids
#define ISpeechRecognitionListConstraint_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetRuntimeClassName
#define ISpeechRecognitionListConstraint_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_GetTrustLevel
#define ISpeechRecognitionListConstraint_get_Commands __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_get_Commands
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionListConstraintFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory, 0x40f3cdc7, 0x562a, 0x426a, 0x9f,0x3b, 0x3b,0x4e,0x28,0x2b,0xe1,0xd5);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("40f3cdc7-562a-426a-9f3b-3b4e282be1d5")
                ISpeechRecognitionListConstraintFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Create(
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *commands,
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionListConstraint **listconstraint) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CreateWithTag(
                        ABI::Windows::Foundation::Collections::IIterable<HSTRING > *commands,
                        HSTRING tag,
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionListConstraint **listconstraint) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory, 0x40f3cdc7, 0x562a, 0x426a, 0x9f,0x3b, 0x3b,0x4e,0x28,0x2b,0xe1,0xd5)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionListConstraintFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This,
        __FIIterable_1_HSTRING *commands,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint **listconstraint);

    HRESULT (STDMETHODCALLTYPE *CreateWithTag)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory *This,
        __FIIterable_1_HSTRING *commands,
        HSTRING tag,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint **listconstraint);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactoryVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionListConstraintFactory methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_Create(This,commands,listconstraint) (This)->lpVtbl->Create(This,commands,listconstraint)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_CreateWithTag(This,commands,tag,listconstraint) (This)->lpVtbl->CreateWithTag(This,commands,tag,listconstraint)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionListConstraintFactory methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_Create(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This,__FIIterable_1_HSTRING *commands,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint **listconstraint) {
    return This->lpVtbl->Create(This,commands,listconstraint);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_CreateWithTag(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory* This,__FIIterable_1_HSTRING *commands,HSTRING tag,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraint **listconstraint) {
    return This->lpVtbl->CreateWithTag(This,commands,tag,listconstraint);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionListConstraintFactory IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory
#define ISpeechRecognitionListConstraintFactoryVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactoryVtbl
#define ISpeechRecognitionListConstraintFactory __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory
#define ISpeechRecognitionListConstraintFactory_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_QueryInterface
#define ISpeechRecognitionListConstraintFactory_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_AddRef
#define ISpeechRecognitionListConstraintFactory_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_Release
#define ISpeechRecognitionListConstraintFactory_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetIids
#define ISpeechRecognitionListConstraintFactory_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetRuntimeClassName
#define ISpeechRecognitionListConstraintFactory_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_GetTrustLevel
#define ISpeechRecognitionListConstraintFactory_Create __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_Create
#define ISpeechRecognitionListConstraintFactory_CreateWithTag __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_CreateWithTag
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionListConstraintFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionQualityDegradingEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs, 0x4fe24105, 0x8c3a, 0x4c7e, 0x8d,0x0a, 0x5b,0xd4,0xf5,0xb1,0x4a,0xd8);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("4fe24105-8c3a-4c7e-8d0a-5bd4f5b14ad8")
                ISpeechRecognitionQualityDegradingEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Problem(
                        enum SpeechRecognitionAudioProblem *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs, 0x4fe24105, 0x8c3a, 0x4c7e, 0x8d,0x0a, 0x5b,0xd4,0xf5,0xb1,0x4a,0xd8)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionQualityDegradingEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Problem)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionAudioProblem *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionQualityDegradingEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_get_Problem(This,value) (This)->lpVtbl->get_Problem(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionQualityDegradingEventArgs methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_get_Problem(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionAudioProblem *value) {
    return This->lpVtbl->get_Problem(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionQualityDegradingEventArgs IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs
#define ISpeechRecognitionQualityDegradingEventArgsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgsVtbl
#define ISpeechRecognitionQualityDegradingEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs
#define ISpeechRecognitionQualityDegradingEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_QueryInterface
#define ISpeechRecognitionQualityDegradingEventArgs_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_AddRef
#define ISpeechRecognitionQualityDegradingEventArgs_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_Release
#define ISpeechRecognitionQualityDegradingEventArgs_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetIids
#define ISpeechRecognitionQualityDegradingEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetRuntimeClassName
#define ISpeechRecognitionQualityDegradingEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_GetTrustLevel
#define ISpeechRecognitionQualityDegradingEventArgs_get_Problem __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_get_Problem
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionResult interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult, 0x4e303157, 0x034e, 0x4652, 0x85,0x7e, 0xd0,0x45,0x4c,0xc4,0xbe,0xec);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("4e303157-034e-4652-857e-d0454cc4beec")
                ISpeechRecognitionResult : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Status(
                        enum SpeechRecognitionResultStatus *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Text(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Confidence(
                        enum SpeechRecognitionConfidence *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SemanticInterpretation(
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionSemanticInterpretation **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE GetAlternates(
                        UINT32 max_amount,
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > **results) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Constraint(
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_RulePath(
                        ABI::Windows::Foundation::Collections::IVectorView<HSTRING > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_RawConfidence(
                        DOUBLE *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult, 0x4e303157, 0x034e, 0x4652, 0x85,0x7e, 0xd0,0x45,0x4c,0xc4,0xbe,0xec)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionResult methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Status)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus *value);

    HRESULT (STDMETHODCALLTYPE *get_Text)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *get_Confidence)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConfidence *value);

    HRESULT (STDMETHODCALLTYPE *get_SemanticInterpretation)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation **value);

    HRESULT (STDMETHODCALLTYPE *GetAlternates)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        UINT32 max_amount,
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **results);

    HRESULT (STDMETHODCALLTYPE *get_Constraint)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value);

    HRESULT (STDMETHODCALLTYPE *get_RulePath)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        __FIVectorView_1_HSTRING **value);

    HRESULT (STDMETHODCALLTYPE *get_RawConfidence)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *This,
        DOUBLE *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResultVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionResult methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Status(This,value) (This)->lpVtbl->get_Status(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Text(This,value) (This)->lpVtbl->get_Text(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Confidence(This,value) (This)->lpVtbl->get_Confidence(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_SemanticInterpretation(This,value) (This)->lpVtbl->get_SemanticInterpretation(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetAlternates(This,max_amount,results) (This)->lpVtbl->GetAlternates(This,max_amount,results)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Constraint(This,value) (This)->lpVtbl->get_Constraint(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_RulePath(This,value) (This)->lpVtbl->get_RulePath(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_RawConfidence(This,value) (This)->lpVtbl->get_RawConfidence(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionResult methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Status(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionResultStatus *value) {
    return This->lpVtbl->get_Status(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Text(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,HSTRING *value) {
    return This->lpVtbl->get_Text(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Confidence(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognitionConfidence *value) {
    return This->lpVtbl->get_Confidence(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_SemanticInterpretation(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation **value) {
    return This->lpVtbl->get_SemanticInterpretation(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetAlternates(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,UINT32 max_amount,__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **results) {
    return This->lpVtbl->GetAlternates(This,max_amount,results);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Constraint(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value) {
    return This->lpVtbl->get_Constraint(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_RulePath(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,__FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->get_RulePath(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_RawConfidence(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult* This,DOUBLE *value) {
    return This->lpVtbl->get_RawConfidence(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionResult IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult
#define ISpeechRecognitionResultVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResultVtbl
#define ISpeechRecognitionResult __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult
#define ISpeechRecognitionResult_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_QueryInterface
#define ISpeechRecognitionResult_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_AddRef
#define ISpeechRecognitionResult_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_Release
#define ISpeechRecognitionResult_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetIids
#define ISpeechRecognitionResult_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetRuntimeClassName
#define ISpeechRecognitionResult_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetTrustLevel
#define ISpeechRecognitionResult_get_Status __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Status
#define ISpeechRecognitionResult_get_Text __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Text
#define ISpeechRecognitionResult_get_Confidence __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Confidence
#define ISpeechRecognitionResult_get_SemanticInterpretation __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_SemanticInterpretation
#define ISpeechRecognitionResult_GetAlternates __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_GetAlternates
#define ISpeechRecognitionResult_get_Constraint __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_Constraint
#define ISpeechRecognitionResult_get_RulePath __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_RulePath
#define ISpeechRecognitionResult_get_RawConfidence __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_get_RawConfidence
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionResult2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2, 0xaf7ed1ba, 0x451b, 0x4166, 0xa0,0xc1, 0x1f,0xfe,0x84,0x03,0x2d,0x03);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("af7ed1ba-451b-4166-a0c1-1ffe84032d03")
                ISpeechRecognitionResult2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_PhraseStartTime(
                        struct DateTime *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_PhraseDuration(
                        struct TimeSpan *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2, 0xaf7ed1ba, 0x451b, 0x4166, 0xa0,0xc1, 0x1f,0xfe,0x84,0x03,0x2d,0x03)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionResult2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_PhraseStartTime)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This,
        struct __x_ABI_CWindows_CFoundation_CDateTime *value);

    HRESULT (STDMETHODCALLTYPE *get_PhraseDuration)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2Vtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionResult2 methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_get_PhraseStartTime(This,value) (This)->lpVtbl->get_PhraseStartTime(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_get_PhraseDuration(This,value) (This)->lpVtbl->get_PhraseDuration(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionResult2 methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_get_PhraseStartTime(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This,struct __x_ABI_CWindows_CFoundation_CDateTime *value) {
    return This->lpVtbl->get_PhraseStartTime(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_get_PhraseDuration(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_PhraseDuration(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionResult2 IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2
#define ISpeechRecognitionResult2Vtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2Vtbl
#define ISpeechRecognitionResult2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2
#define ISpeechRecognitionResult2_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_QueryInterface
#define ISpeechRecognitionResult2_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_AddRef
#define ISpeechRecognitionResult2_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_Release
#define ISpeechRecognitionResult2_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetIids
#define ISpeechRecognitionResult2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetRuntimeClassName
#define ISpeechRecognitionResult2_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_GetTrustLevel
#define ISpeechRecognitionResult2_get_PhraseStartTime __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_get_PhraseStartTime
#define ISpeechRecognitionResult2_get_PhraseDuration __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_get_PhraseDuration
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognitionSemanticInterpretation interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation, 0xaae1da9b, 0x7e32, 0x4c1f, 0x89,0xfe, 0x0c,0x65,0xf4,0x86,0xf5,0x2e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("aae1da9b-7e32-4c1f-89fe-0c65f486f52e")
                ISpeechRecognitionSemanticInterpretation : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_Properties(
                        ABI::Windows::Foundation::Collections::IMapView<HSTRING,ABI::Windows::Foundation::Collections::IVectorView<HSTRING >* > **value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation, 0xaae1da9b, 0x7e32, 0x4c1f, 0x89,0xfe, 0x0c,0x65,0xf4,0x86,0xf5,0x2e)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretationVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognitionSemanticInterpretation methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Properties)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation *This,
        __FIMapView_2_HSTRING___FIVectorView_1_HSTRING **value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretationVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretationVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognitionSemanticInterpretation methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_get_Properties(This,value) (This)->lpVtbl->get_Properties(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognitionSemanticInterpretation methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_get_Properties(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation* This,__FIMapView_2_HSTRING___FIVectorView_1_HSTRING **value) {
    return This->lpVtbl->get_Properties(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognitionSemanticInterpretation IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation
#define ISpeechRecognitionSemanticInterpretationVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretationVtbl
#define ISpeechRecognitionSemanticInterpretation __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation
#define ISpeechRecognitionSemanticInterpretation_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_QueryInterface
#define ISpeechRecognitionSemanticInterpretation_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_AddRef
#define ISpeechRecognitionSemanticInterpretation_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_Release
#define ISpeechRecognitionSemanticInterpretation_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetIids
#define ISpeechRecognitionSemanticInterpretation_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetRuntimeClassName
#define ISpeechRecognitionSemanticInterpretation_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_GetTrustLevel
#define ISpeechRecognitionSemanticInterpretation_get_Properties __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_get_Properties
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionSemanticInterpretation_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognizer interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer, 0x0bc3c9cb, 0xc26a, 0x40f2, 0xae,0xb5, 0x80,0x96,0xb2,0xe4,0x80,0x73);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("0bc3c9cb-c26a-40f2-aeb5-8096b2e48073")
                ISpeechRecognizer : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_CurrentLanguage(
                        ABI::Windows::Globalization::ILanguage **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Constraints(
                        ABI::Windows::Foundation::Collections::IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_Timeouts(
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerTimeouts **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_UIOptions(
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerUIOptions **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE CompileConstraintsAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RecognizeAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE RecognizeWithUIAsync(
                        ABI::Windows::Foundation::IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > **operation) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_RecognitionQualityDegrading(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_RecognitionQualityDegrading(
                        EventRegistrationToken token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_StateChanged(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_StateChanged(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer, 0x0bc3c9cb, 0xc26a, 0x40f2, 0xae,0xb5, 0x80,0x96,0xb2,0xe4,0x80,0x73)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizer methods ***/
    HRESULT (STDMETHODCALLTYPE *get_CurrentLanguage)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __x_ABI_CWindows_CGlobalization_CILanguage **value);

    HRESULT (STDMETHODCALLTYPE *get_Constraints)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint **value);

    HRESULT (STDMETHODCALLTYPE *get_Timeouts)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts **value);

    HRESULT (STDMETHODCALLTYPE *get_UIOptions)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions **value);

    HRESULT (STDMETHODCALLTYPE *CompileConstraintsAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult **operation);

    HRESULT (STDMETHODCALLTYPE *RecognizeAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **operation);

    HRESULT (STDMETHODCALLTYPE *RecognizeWithUIAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **operation);

    HRESULT (STDMETHODCALLTYPE *add_RecognitionQualityDegrading)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_RecognitionQualityDegrading)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        EventRegistrationToken token);

    HRESULT (STDMETHODCALLTYPE *add_StateChanged)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_StateChanged)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizer methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_CurrentLanguage(This,value) (This)->lpVtbl->get_CurrentLanguage(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_Constraints(This,value) (This)->lpVtbl->get_Constraints(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_Timeouts(This,value) (This)->lpVtbl->get_Timeouts(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_UIOptions(This,value) (This)->lpVtbl->get_UIOptions(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_CompileConstraintsAsync(This,operation) (This)->lpVtbl->CompileConstraintsAsync(This,operation)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_RecognizeAsync(This,operation) (This)->lpVtbl->RecognizeAsync(This,operation)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_RecognizeWithUIAsync(This,operation) (This)->lpVtbl->RecognizeWithUIAsync(This,operation)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_add_RecognitionQualityDegrading(This,handler,token) (This)->lpVtbl->add_RecognitionQualityDegrading(This,handler,token)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_remove_RecognitionQualityDegrading(This,token) (This)->lpVtbl->remove_RecognitionQualityDegrading(This,token)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_add_StateChanged(This,handler,token) (This)->lpVtbl->add_StateChanged(This,handler,token)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_remove_StateChanged(This,token) (This)->lpVtbl->remove_StateChanged(This,token)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizer methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_CurrentLanguage(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__x_ABI_CWindows_CGlobalization_CILanguage **value) {
    return This->lpVtbl->get_CurrentLanguage(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_Constraints(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint **value) {
    return This->lpVtbl->get_Constraints(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_Timeouts(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts **value) {
    return This->lpVtbl->get_Timeouts(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_UIOptions(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions **value) {
    return This->lpVtbl->get_UIOptions(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_CompileConstraintsAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult **operation) {
    return This->lpVtbl->CompileConstraintsAsync(This,operation);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_RecognizeAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **operation) {
    return This->lpVtbl->RecognizeAsync(This,operation);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_RecognizeWithUIAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **operation) {
    return This->lpVtbl->RecognizeWithUIAsync(This,operation);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_add_RecognitionQualityDegrading(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_RecognitionQualityDegrading(This,handler,token);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_remove_RecognitionQualityDegrading(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_RecognitionQualityDegrading(This,token);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_add_StateChanged(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_StateChanged(This,handler,token);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_remove_StateChanged(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_StateChanged(This,token);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizer IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer
#define ISpeechRecognizerVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerVtbl
#define ISpeechRecognizer __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer
#define ISpeechRecognizer_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_QueryInterface
#define ISpeechRecognizer_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_AddRef
#define ISpeechRecognizer_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_Release
#define ISpeechRecognizer_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetIids
#define ISpeechRecognizer_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetRuntimeClassName
#define ISpeechRecognizer_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_GetTrustLevel
#define ISpeechRecognizer_get_CurrentLanguage __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_CurrentLanguage
#define ISpeechRecognizer_get_Constraints __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_Constraints
#define ISpeechRecognizer_get_Timeouts __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_Timeouts
#define ISpeechRecognizer_get_UIOptions __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_get_UIOptions
#define ISpeechRecognizer_CompileConstraintsAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_CompileConstraintsAsync
#define ISpeechRecognizer_RecognizeAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_RecognizeAsync
#define ISpeechRecognizer_RecognizeWithUIAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_RecognizeWithUIAsync
#define ISpeechRecognizer_add_RecognitionQualityDegrading __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_add_RecognitionQualityDegrading
#define ISpeechRecognizer_remove_RecognitionQualityDegrading __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_remove_RecognitionQualityDegrading
#define ISpeechRecognizer_add_StateChanged __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_add_StateChanged
#define ISpeechRecognizer_remove_StateChanged __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_remove_StateChanged
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognizer2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2, 0x63c9baf1, 0x91e3, 0x4ea4, 0x86,0xa1, 0x7c,0x38,0x67,0xd0,0x84,0xa6);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("63c9baf1-91e3-4ea4-86a1-7c3867d084a6")
                ISpeechRecognizer2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ContinuousRecognitionSession(
                        ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionSession **value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_State(
                        enum SpeechRecognizerState *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE StopRecognitionAsync(
                        ABI::Windows::Foundation::IAsyncAction **action) = 0;

                    virtual HRESULT STDMETHODCALLTYPE add_HypothesisGenerated(
                        ABI::Windows::Foundation::ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs* > *handler,
                        EventRegistrationToken *token) = 0;

                    virtual HRESULT STDMETHODCALLTYPE remove_HypothesisGenerated(
                        EventRegistrationToken token) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2, 0x63c9baf1, 0x91e3, 0x4ea4, 0x86,0xa1, 0x7c,0x38,0x67,0xd0,0x84,0xa6)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizer2 methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ContinuousRecognitionSession)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession **value);

    HRESULT (STDMETHODCALLTYPE *get_State)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState *value);

    HRESULT (STDMETHODCALLTYPE *StopRecognitionAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        __x_ABI_CWindows_CFoundation_CIAsyncAction **action);

    HRESULT (STDMETHODCALLTYPE *add_HypothesisGenerated)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs *handler,
        EventRegistrationToken *token);

    HRESULT (STDMETHODCALLTYPE *remove_HypothesisGenerated)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 *This,
        EventRegistrationToken token);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2Vtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizer2 methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_get_ContinuousRecognitionSession(This,value) (This)->lpVtbl->get_ContinuousRecognitionSession(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_get_State(This,value) (This)->lpVtbl->get_State(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_StopRecognitionAsync(This,action) (This)->lpVtbl->StopRecognitionAsync(This,action)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_add_HypothesisGenerated(This,handler,token) (This)->lpVtbl->add_HypothesisGenerated(This,handler,token)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_remove_HypothesisGenerated(This,token) (This)->lpVtbl->remove_HypothesisGenerated(This,token)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizer2 methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_get_ContinuousRecognitionSession(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession **value) {
    return This->lpVtbl->get_ContinuousRecognitionSession(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_get_State(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState *value) {
    return This->lpVtbl->get_State(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_StopRecognitionAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,__x_ABI_CWindows_CFoundation_CIAsyncAction **action) {
    return This->lpVtbl->StopRecognitionAsync(This,action);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_add_HypothesisGenerated(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs *handler,EventRegistrationToken *token) {
    return This->lpVtbl->add_HypothesisGenerated(This,handler,token);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_remove_HypothesisGenerated(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2* This,EventRegistrationToken token) {
    return This->lpVtbl->remove_HypothesisGenerated(This,token);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizer2 IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2
#define ISpeechRecognizer2Vtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2Vtbl
#define ISpeechRecognizer2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2
#define ISpeechRecognizer2_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_QueryInterface
#define ISpeechRecognizer2_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_AddRef
#define ISpeechRecognizer2_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_Release
#define ISpeechRecognizer2_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetIids
#define ISpeechRecognizer2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetRuntimeClassName
#define ISpeechRecognizer2_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_GetTrustLevel
#define ISpeechRecognizer2_get_ContinuousRecognitionSession __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_get_ContinuousRecognitionSession
#define ISpeechRecognizer2_get_State __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_get_State
#define ISpeechRecognizer2_StopRecognitionAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_StopRecognitionAsync
#define ISpeechRecognizer2_add_HypothesisGenerated __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_add_HypothesisGenerated
#define ISpeechRecognizer2_remove_HypothesisGenerated __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_remove_HypothesisGenerated
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognizerFactory interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory, 0x60c488dd, 0x7fb8, 0x4033, 0xac,0x70, 0xd0,0x46,0xf6,0x48,0x18,0xe1);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("60c488dd-7fb8-4033-ac70-d046f64818e1")
                ISpeechRecognizerFactory : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE Create(
                        ABI::Windows::Globalization::ILanguage *language,
                        ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer **speechrecognizer) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory, 0x60c488dd, 0x7fb8, 0x4033, 0xac,0x70, 0xd0,0x46,0xf6,0x48,0x18,0xe1)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactoryVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizerFactory methods ***/
    HRESULT (STDMETHODCALLTYPE *Create)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory *This,
        __x_ABI_CWindows_CGlobalization_CILanguage *language,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer **speechrecognizer);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactoryVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactoryVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizerFactory methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_Create(This,language,speechrecognizer) (This)->lpVtbl->Create(This,language,speechrecognizer)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizerFactory methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_Create(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory* This,__x_ABI_CWindows_CGlobalization_CILanguage *language,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer **speechrecognizer) {
    return This->lpVtbl->Create(This,language,speechrecognizer);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizerFactory IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory
#define ISpeechRecognizerFactoryVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactoryVtbl
#define ISpeechRecognizerFactory __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory
#define ISpeechRecognizerFactory_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_QueryInterface
#define ISpeechRecognizerFactory_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_AddRef
#define ISpeechRecognizerFactory_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_Release
#define ISpeechRecognizerFactory_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetIids
#define ISpeechRecognizerFactory_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetRuntimeClassName
#define ISpeechRecognizerFactory_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_GetTrustLevel
#define ISpeechRecognizerFactory_Create __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_Create
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerFactory_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognizerStateChangedEventArgs interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs, 0x563d4f09, 0xba03, 0x4bad, 0xad,0x81, 0xdd,0xc6,0xc4,0xda,0xb0,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("563d4f09-ba03-4bad-ad81-ddc6c4dab0c3")
                ISpeechRecognizerStateChangedEventArgs : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_State(
                        enum SpeechRecognizerState *value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs, 0x563d4f09, 0xba03, 0x4bad, 0xad,0x81, 0xdd,0xc6,0xc4,0xda,0xb0,0xc3)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizerStateChangedEventArgs methods ***/
    HRESULT (STDMETHODCALLTYPE *get_State)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *This,
        enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState *value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizerStateChangedEventArgs methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_get_State(This,value) (This)->lpVtbl->get_State(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizerStateChangedEventArgs methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_get_State(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs* This,enum __x_ABI_CWindows_CMedia_CSpeechRecognition_CSpeechRecognizerState *value) {
    return This->lpVtbl->get_State(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizerStateChangedEventArgs IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs
#define ISpeechRecognizerStateChangedEventArgsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgsVtbl
#define ISpeechRecognizerStateChangedEventArgs __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs
#define ISpeechRecognizerStateChangedEventArgs_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_QueryInterface
#define ISpeechRecognizerStateChangedEventArgs_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_AddRef
#define ISpeechRecognizerStateChangedEventArgs_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_Release
#define ISpeechRecognizerStateChangedEventArgs_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetIids
#define ISpeechRecognizerStateChangedEventArgs_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetRuntimeClassName
#define ISpeechRecognizerStateChangedEventArgs_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_GetTrustLevel
#define ISpeechRecognizerStateChangedEventArgs_get_State __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_get_State
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognizerStatics interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics, 0x87a35eac, 0xa7dc, 0x4b0b, 0xbc,0xc9, 0x24,0xf4,0x7c,0x0b,0x7e,0xbf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("87a35eac-a7dc-4b0b-bcc9-24f47c0b7ebf")
                ISpeechRecognizerStatics : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_SystemSpeechLanguage(
                        ABI::Windows::Globalization::ILanguage **language) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SupportedTopicLanguages(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Globalization::Language* > **languages) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_SupportedGrammarLanguages(
                        ABI::Windows::Foundation::Collections::IVectorView<ABI::Windows::Globalization::Language* > **languages) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics, 0x87a35eac, 0xa7dc, 0x4b0b, 0xbc,0xc9, 0x24,0xf4,0x7c,0x0b,0x7e,0xbf)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStaticsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizerStatics methods ***/
    HRESULT (STDMETHODCALLTYPE *get_SystemSpeechLanguage)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This,
        __x_ABI_CWindows_CGlobalization_CILanguage **language);

    HRESULT (STDMETHODCALLTYPE *get_SupportedTopicLanguages)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This,
        __FIVectorView_1_Windows__CGlobalization__CLanguage **languages);

    HRESULT (STDMETHODCALLTYPE *get_SupportedGrammarLanguages)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics *This,
        __FIVectorView_1_Windows__CGlobalization__CLanguage **languages);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStaticsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStaticsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizerStatics methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SystemSpeechLanguage(This,language) (This)->lpVtbl->get_SystemSpeechLanguage(This,language)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SupportedTopicLanguages(This,languages) (This)->lpVtbl->get_SupportedTopicLanguages(This,languages)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SupportedGrammarLanguages(This,languages) (This)->lpVtbl->get_SupportedGrammarLanguages(This,languages)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizerStatics methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SystemSpeechLanguage(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This,__x_ABI_CWindows_CGlobalization_CILanguage **language) {
    return This->lpVtbl->get_SystemSpeechLanguage(This,language);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SupportedTopicLanguages(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This,__FIVectorView_1_Windows__CGlobalization__CLanguage **languages) {
    return This->lpVtbl->get_SupportedTopicLanguages(This,languages);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SupportedGrammarLanguages(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics* This,__FIVectorView_1_Windows__CGlobalization__CLanguage **languages) {
    return This->lpVtbl->get_SupportedGrammarLanguages(This,languages);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizerStatics IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics
#define ISpeechRecognizerStaticsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStaticsVtbl
#define ISpeechRecognizerStatics __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics
#define ISpeechRecognizerStatics_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_QueryInterface
#define ISpeechRecognizerStatics_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_AddRef
#define ISpeechRecognizerStatics_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_Release
#define ISpeechRecognizerStatics_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetIids
#define ISpeechRecognizerStatics_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetRuntimeClassName
#define ISpeechRecognizerStatics_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_GetTrustLevel
#define ISpeechRecognizerStatics_get_SystemSpeechLanguage __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SystemSpeechLanguage
#define ISpeechRecognizerStatics_get_SupportedTopicLanguages __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SupportedTopicLanguages
#define ISpeechRecognizerStatics_get_SupportedGrammarLanguages __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_get_SupportedGrammarLanguages
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognizerStatics2 interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2, 0x1d1b0d95, 0x7565, 0x4ef9, 0xa2,0xf3, 0xba,0x15,0x16,0x2a,0x96,0xcf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("1d1b0d95-7565-4ef9-a2f3-ba15162a96cf")
                ISpeechRecognizerStatics2 : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE TrySetSystemSpeechLanguageAsync(
                        ABI::Windows::Globalization::ILanguage *language,
                        ABI::Windows::Foundation::IAsyncOperation<boolean > **operation) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2, 0x1d1b0d95, 0x7565, 0x4ef9, 0xa2,0xf3, 0xba,0x15,0x16,0x2a,0x96,0xcf)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2Vtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizerStatics2 methods ***/
    HRESULT (STDMETHODCALLTYPE *TrySetSystemSpeechLanguageAsync)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 *This,
        __x_ABI_CWindows_CGlobalization_CILanguage *language,
        __FIAsyncOperation_1_boolean **operation);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2Vtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2 {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2Vtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizerStatics2 methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_TrySetSystemSpeechLanguageAsync(This,language,operation) (This)->lpVtbl->TrySetSystemSpeechLanguageAsync(This,language,operation)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizerStatics2 methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_TrySetSystemSpeechLanguageAsync(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2* This,__x_ABI_CWindows_CGlobalization_CILanguage *language,__FIAsyncOperation_1_boolean **operation) {
    return This->lpVtbl->TrySetSystemSpeechLanguageAsync(This,language,operation);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizerStatics2 IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2
#define ISpeechRecognizerStatics2Vtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2Vtbl
#define ISpeechRecognizerStatics2 __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2
#define ISpeechRecognizerStatics2_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_QueryInterface
#define ISpeechRecognizerStatics2_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_AddRef
#define ISpeechRecognizerStatics2_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_Release
#define ISpeechRecognizerStatics2_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetIids
#define ISpeechRecognizerStatics2_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetRuntimeClassName
#define ISpeechRecognizerStatics2_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_GetTrustLevel
#define ISpeechRecognizerStatics2_TrySetSystemSpeechLanguageAsync __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_TrySetSystemSpeechLanguageAsync
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStatics2_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x50000 */

/*****************************************************************************
 * ISpeechRecognizerTimeouts interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts, 0x2ef76fca, 0x6a3c, 0x4dca, 0xa1,0x53, 0xdf,0x1b,0xc8,0x8a,0x79,0xaf);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("2ef76fca-6a3c-4dca-a153-df1bc88a79af")
                ISpeechRecognizerTimeouts : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_InitialSilenceTimeout(
                        struct TimeSpan *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_InitialSilenceTimeout(
                        struct TimeSpan value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_EndSilenceTimeout(
                        struct TimeSpan *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_EndSilenceTimeout(
                        struct TimeSpan value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_BabbleTimeout(
                        struct TimeSpan *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_BabbleTimeout(
                        struct TimeSpan value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts, 0x2ef76fca, 0x6a3c, 0x4dca, 0xa1,0x53, 0xdf,0x1b,0xc8,0x8a,0x79,0xaf)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeoutsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizerTimeouts methods ***/
    HRESULT (STDMETHODCALLTYPE *get_InitialSilenceTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_InitialSilenceTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *get_EndSilenceTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_EndSilenceTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan value);

    HRESULT (STDMETHODCALLTYPE *get_BabbleTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan *value);

    HRESULT (STDMETHODCALLTYPE *put_BabbleTimeout)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts *This,
        struct __x_ABI_CWindows_CFoundation_CTimeSpan value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeoutsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeoutsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizerTimeouts methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_InitialSilenceTimeout(This,value) (This)->lpVtbl->get_InitialSilenceTimeout(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_InitialSilenceTimeout(This,value) (This)->lpVtbl->put_InitialSilenceTimeout(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_EndSilenceTimeout(This,value) (This)->lpVtbl->get_EndSilenceTimeout(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_EndSilenceTimeout(This,value) (This)->lpVtbl->put_EndSilenceTimeout(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_BabbleTimeout(This,value) (This)->lpVtbl->get_BabbleTimeout(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_BabbleTimeout(This,value) (This)->lpVtbl->put_BabbleTimeout(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizerTimeouts methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_InitialSilenceTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_InitialSilenceTimeout(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_InitialSilenceTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_InitialSilenceTimeout(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_EndSilenceTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_EndSilenceTimeout(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_EndSilenceTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_EndSilenceTimeout(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_BabbleTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan *value) {
    return This->lpVtbl->get_BabbleTimeout(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_BabbleTimeout(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts* This,struct __x_ABI_CWindows_CFoundation_CTimeSpan value) {
    return This->lpVtbl->put_BabbleTimeout(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizerTimeouts IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts
#define ISpeechRecognizerTimeoutsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeoutsVtbl
#define ISpeechRecognizerTimeouts __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts
#define ISpeechRecognizerTimeouts_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_QueryInterface
#define ISpeechRecognizerTimeouts_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_AddRef
#define ISpeechRecognizerTimeouts_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_Release
#define ISpeechRecognizerTimeouts_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetIids
#define ISpeechRecognizerTimeouts_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetRuntimeClassName
#define ISpeechRecognizerTimeouts_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_GetTrustLevel
#define ISpeechRecognizerTimeouts_get_InitialSilenceTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_InitialSilenceTimeout
#define ISpeechRecognizerTimeouts_put_InitialSilenceTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_InitialSilenceTimeout
#define ISpeechRecognizerTimeouts_get_EndSilenceTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_EndSilenceTimeout
#define ISpeechRecognizerTimeouts_put_EndSilenceTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_EndSilenceTimeout
#define ISpeechRecognizerTimeouts_get_BabbleTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_get_BabbleTimeout
#define ISpeechRecognizerTimeouts_put_BabbleTimeout __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_put_BabbleTimeout
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerTimeouts_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * ISpeechRecognizerUIOptions interface
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_INTERFACE_DEFINED__
#define ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_INTERFACE_DEFINED__

DEFINE_GUID(IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions, 0x7888d641, 0xb92b, 0x44ba, 0xa2,0x5f, 0xd1,0x86,0x46,0x30,0x64,0x1f);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Media {
            namespace SpeechRecognition {
                MIDL_INTERFACE("7888d641-b92b-44ba-a25f-d1864630641f")
                ISpeechRecognizerUIOptions : public IInspectable
                {
                    virtual HRESULT STDMETHODCALLTYPE get_ExampleText(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ExampleText(
                        HSTRING value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_AudiblePrompt(
                        HSTRING *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_AudiblePrompt(
                        HSTRING value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_IsReadBackEnabled(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_IsReadBackEnabled(
                        boolean value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE get_ShowConfirmation(
                        boolean *value) = 0;

                    virtual HRESULT STDMETHODCALLTYPE put_ShowConfirmation(
                        boolean value) = 0;

                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions, 0x7888d641, 0xb92b, 0x44ba, 0xa2,0x5f, 0xd1,0x86,0x46,0x30,0x64,0x1f)
#endif
#else
typedef struct __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptionsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        TrustLevel *trustLevel);

    /*** ISpeechRecognizerUIOptions methods ***/
    HRESULT (STDMETHODCALLTYPE *get_ExampleText)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_ExampleText)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_AudiblePrompt)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        HSTRING *value);

    HRESULT (STDMETHODCALLTYPE *put_AudiblePrompt)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        HSTRING value);

    HRESULT (STDMETHODCALLTYPE *get_IsReadBackEnabled)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_IsReadBackEnabled)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        boolean value);

    HRESULT (STDMETHODCALLTYPE *get_ShowConfirmation)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        boolean *value);

    HRESULT (STDMETHODCALLTYPE *put_ShowConfirmation)(
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions *This,
        boolean value);

    END_INTERFACE
} __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptionsVtbl;

interface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions {
    CONST_VTBL __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptionsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** ISpeechRecognizerUIOptions methods ***/
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_ExampleText(This,value) (This)->lpVtbl->get_ExampleText(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_ExampleText(This,value) (This)->lpVtbl->put_ExampleText(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_AudiblePrompt(This,value) (This)->lpVtbl->get_AudiblePrompt(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_AudiblePrompt(This,value) (This)->lpVtbl->put_AudiblePrompt(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_IsReadBackEnabled(This,value) (This)->lpVtbl->get_IsReadBackEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_IsReadBackEnabled(This,value) (This)->lpVtbl->put_IsReadBackEnabled(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_ShowConfirmation(This,value) (This)->lpVtbl->get_ShowConfirmation(This,value)
#define __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_ShowConfirmation(This,value) (This)->lpVtbl->put_ShowConfirmation(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_QueryInterface(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_AddRef(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_Release(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetIids(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetRuntimeClassName(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetTrustLevel(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** ISpeechRecognizerUIOptions methods ***/
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_ExampleText(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,HSTRING *value) {
    return This->lpVtbl->get_ExampleText(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_ExampleText(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,HSTRING value) {
    return This->lpVtbl->put_ExampleText(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_AudiblePrompt(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,HSTRING *value) {
    return This->lpVtbl->get_AudiblePrompt(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_AudiblePrompt(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,HSTRING value) {
    return This->lpVtbl->put_AudiblePrompt(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_IsReadBackEnabled(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,boolean *value) {
    return This->lpVtbl->get_IsReadBackEnabled(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_IsReadBackEnabled(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,boolean value) {
    return This->lpVtbl->put_IsReadBackEnabled(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_ShowConfirmation(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,boolean *value) {
    return This->lpVtbl->get_ShowConfirmation(This,value);
}
static FORCEINLINE HRESULT __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_ShowConfirmation(__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions* This,boolean value) {
    return This->lpVtbl->put_ShowConfirmation(This,value);
}
#endif
#ifdef WIDL_using_Windows_Media_SpeechRecognition
#define IID_ISpeechRecognizerUIOptions IID___x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions
#define ISpeechRecognizerUIOptionsVtbl __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptionsVtbl
#define ISpeechRecognizerUIOptions __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions
#define ISpeechRecognizerUIOptions_QueryInterface __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_QueryInterface
#define ISpeechRecognizerUIOptions_AddRef __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_AddRef
#define ISpeechRecognizerUIOptions_Release __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_Release
#define ISpeechRecognizerUIOptions_GetIids __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetIids
#define ISpeechRecognizerUIOptions_GetRuntimeClassName __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetRuntimeClassName
#define ISpeechRecognizerUIOptions_GetTrustLevel __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_GetTrustLevel
#define ISpeechRecognizerUIOptions_get_ExampleText __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_ExampleText
#define ISpeechRecognizerUIOptions_put_ExampleText __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_ExampleText
#define ISpeechRecognizerUIOptions_get_AudiblePrompt __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_AudiblePrompt
#define ISpeechRecognizerUIOptions_put_AudiblePrompt __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_AudiblePrompt
#define ISpeechRecognizerUIOptions_get_IsReadBackEnabled __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_IsReadBackEnabled
#define ISpeechRecognizerUIOptions_put_IsReadBackEnabled __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_IsReadBackEnabled
#define ISpeechRecognizerUIOptions_get_ShowConfirmation __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_get_ShowConfirmation
#define ISpeechRecognizerUIOptions_put_ShowConfirmation __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_put_ShowConfirmation
#endif /* WIDL_using_Windows_Media_SpeechRecognition */
#endif

#endif

#endif  /* ____x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerUIOptions_INTERFACE_DEFINED__ */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechContinuousRecognitionCompletedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionCompletedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionCompletedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionCompletedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','C','o','n','t','i','n','u','o','u','s','R','e','c','o','g','n','i','t','i','o','n','C','o','m','p','l','e','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionCompletedEventArgs[] = L"Windows.Media.SpeechRecognition.SpeechContinuousRecognitionCompletedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionCompletedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','C','o','n','t','i','n','u','o','u','s','R','e','c','o','g','n','i','t','i','o','n','C','o','m','p','l','e','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionCompletedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechContinuousRecognitionResultGeneratedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionResultGeneratedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionResultGeneratedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionResultGeneratedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','C','o','n','t','i','n','u','o','u','s','R','e','c','o','g','n','i','t','i','o','n','R','e','s','u','l','t','G','e','n','e','r','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionResultGeneratedEventArgs[] = L"Windows.Media.SpeechRecognition.SpeechContinuousRecognitionResultGeneratedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionResultGeneratedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','C','o','n','t','i','n','u','o','u','s','R','e','c','o','g','n','i','t','i','o','n','R','e','s','u','l','t','G','e','n','e','r','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionResultGeneratedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechContinuousRecognitionSession
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionSession_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionSession_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionSession[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','C','o','n','t','i','n','u','o','u','s','R','e','c','o','g','n','i','t','i','o','n','S','e','s','s','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionSession[] = L"Windows.Media.SpeechRecognition.SpeechContinuousRecognitionSession";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionSession[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','C','o','n','t','i','n','u','o','u','s','R','e','c','o','g','n','i','t','i','o','n','S','e','s','s','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechContinuousRecognitionSession_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognitionCompilationResult
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionCompilationResult_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionCompilationResult_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionCompilationResult[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','C','o','m','p','i','l','a','t','i','o','n','R','e','s','u','l','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionCompilationResult[] = L"Windows.Media.SpeechRecognition.SpeechRecognitionCompilationResult";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionCompilationResult[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','C','o','m','p','i','l','a','t','i','o','n','R','e','s','u','l','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionCompilationResult_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognitionHypothesis
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesis_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesis_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesis[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','H','y','p','o','t','h','e','s','i','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesis[] = L"Windows.Media.SpeechRecognition.SpeechRecognitionHypothesis";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesis[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','H','y','p','o','t','h','e','s','i','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesis_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognitionHypothesisGeneratedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesisGeneratedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesisGeneratedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesisGeneratedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','H','y','p','o','t','h','e','s','i','s','G','e','n','e','r','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesisGeneratedEventArgs[] = L"Windows.Media.SpeechRecognition.SpeechRecognitionHypothesisGeneratedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesisGeneratedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','H','y','p','o','t','h','e','s','i','s','G','e','n','e','r','a','t','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionHypothesisGeneratedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognitionListConstraint
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionListConstraint_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionListConstraint_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionListConstraint[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','L','i','s','t','C','o','n','s','t','r','a','i','n','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionListConstraint[] = L"Windows.Media.SpeechRecognition.SpeechRecognitionListConstraint";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionListConstraint[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','L','i','s','t','C','o','n','s','t','r','a','i','n','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionListConstraint_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognitionQualityDegradingEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionQualityDegradingEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionQualityDegradingEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionQualityDegradingEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','Q','u','a','l','i','t','y','D','e','g','r','a','d','i','n','g','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionQualityDegradingEventArgs[] = L"Windows.Media.SpeechRecognition.SpeechRecognitionQualityDegradingEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionQualityDegradingEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','Q','u','a','l','i','t','y','D','e','g','r','a','d','i','n','g','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionQualityDegradingEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognitionResult
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionResult_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionResult_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionResult[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','R','e','s','u','l','t',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionResult[] = L"Windows.Media.SpeechRecognition.SpeechRecognitionResult";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionResult[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','R','e','s','u','l','t',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionResult_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognitionSemanticInterpretation
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionSemanticInterpretation_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionSemanticInterpretation_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionSemanticInterpretation[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','S','e','m','a','n','t','i','c','I','n','t','e','r','p','r','e','t','a','t','i','o','n',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionSemanticInterpretation[] = L"Windows.Media.SpeechRecognition.SpeechRecognitionSemanticInterpretation";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognitionSemanticInterpretation[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','S','e','m','a','n','t','i','c','I','n','t','e','r','p','r','e','t','a','t','i','o','n',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognitionSemanticInterpretation_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognizer
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizer_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizer_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizer[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizer[] = L"Windows.Media.SpeechRecognition.SpeechRecognizer";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizer[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizer_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognizerStateChangedEventArgs
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerStateChangedEventArgs_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerStateChangedEventArgs_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerStateChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r','S','t','a','t','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerStateChangedEventArgs[] = L"Windows.Media.SpeechRecognition.SpeechRecognizerStateChangedEventArgs";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerStateChangedEventArgs[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r','S','t','a','t','e','C','h','a','n','g','e','d','E','v','e','n','t','A','r','g','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerStateChangedEventArgs_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognizerTimeouts
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerTimeouts_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerTimeouts_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerTimeouts[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r','T','i','m','e','o','u','t','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerTimeouts[] = L"Windows.Media.SpeechRecognition.SpeechRecognizerTimeouts";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerTimeouts[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r','T','i','m','e','o','u','t','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerTimeouts_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*
 * Class Windows.Media.SpeechRecognition.SpeechRecognizerUIOptions
 */
#if WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000
#ifndef RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerUIOptions_DEFINED
#define RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerUIOptions_DEFINED
#if !defined(_MSC_VER) && !defined(__MINGW32__)
static const WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerUIOptions[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r','U','I','O','p','t','i','o','n','s',0};
#elif defined(__GNUC__) && !defined(__cplusplus)
const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerUIOptions[] = L"Windows.Media.SpeechRecognition.SpeechRecognizerUIOptions";
#else
extern const DECLSPEC_SELECTANY WCHAR RuntimeClass_Windows_Media_SpeechRecognition_SpeechRecognizerUIOptions[] = {'W','i','n','d','o','w','s','.','M','e','d','i','a','.','S','p','e','e','c','h','R','e','c','o','g','n','i','t','i','o','n','.','S','p','e','e','c','h','R','e','c','o','g','n','i','z','e','r','U','I','O','p','t','i','o','n','s',0};
#endif
#endif /* RUNTIMECLASS_Windows_Media_SpeechRecognition_SpeechRecognizerUIOptions_DEFINED */
#endif /* WINDOWS_FOUNDATION_UNIVERSALAPICONTRACT_VERSION >= 0x10000 */

/*****************************************************************************
 * IIterable<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > interface
 */
#ifndef ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x88e6436c, 0x3253, 0x520b, 0x9e,0xd8, 0xa6,0x3b,0x17,0x8c,0x44,0xa2);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("88e6436c-3253-520b-9ed8-a63b178c44a2")
                IIterable<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > : IIterable_impl<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x88e6436c, 0x3253, 0x520b, 0x9e,0xd8, 0xa6,0x3b,0x17,0x8c,0x44,0xa2)
#endif
#else
typedef struct __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint **value);

    END_INTERFACE
} __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl;

interface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint {
    CONST_VTBL __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_First(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_ISpeechRecognitionConstraint IID___FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IIterable_ISpeechRecognitionConstraintVtbl __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl
#define IIterable_ISpeechRecognitionConstraint __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IIterable_ISpeechRecognitionConstraint_QueryInterface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface
#define IIterable_ISpeechRecognitionConstraint_AddRef __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef
#define IIterable_ISpeechRecognitionConstraint_Release __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release
#define IIterable_ISpeechRecognitionConstraint_GetIids __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids
#define IIterable_ISpeechRecognitionConstraint_GetRuntimeClassName __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName
#define IIterable_ISpeechRecognitionConstraint_GetTrustLevel __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel
#define IIterable_ISpeechRecognitionConstraint_First __FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > interface
 */
#ifndef ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x738f00b1, 0xe18c, 0x5140, 0xa5,0x3a, 0xf1,0x78,0x8d,0x10,0xc9,0x3d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("738f00b1-e18c-5140-a53a-f1788d10c93d")
                IIterator<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > : IIterator_impl<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x738f00b1, 0xe18c, 0x5140, 0xa5,0x3a, 0xf1,0x78,0x8d,0x10,0xc9,0x3d)
#endif
#else
typedef struct __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        WINBOOL *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        WINBOOL *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl;

interface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint {
    CONST_VTBL __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Current(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value) {
    return This->lpVtbl->get_Current(This,value);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_HasCurrent(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,WINBOOL *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_MoveNext(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,WINBOOL *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 items_size,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_ISpeechRecognitionConstraint IID___FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IIterator_ISpeechRecognitionConstraintVtbl __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl
#define IIterator_ISpeechRecognitionConstraint __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IIterator_ISpeechRecognitionConstraint_QueryInterface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface
#define IIterator_ISpeechRecognitionConstraint_AddRef __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef
#define IIterator_ISpeechRecognitionConstraint_Release __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release
#define IIterator_ISpeechRecognitionConstraint_GetIids __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids
#define IIterator_ISpeechRecognitionConstraint_GetRuntimeClassName __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName
#define IIterator_ISpeechRecognitionConstraint_GetTrustLevel __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel
#define IIterator_ISpeechRecognitionConstraint_get_Current __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Current
#define IIterator_ISpeechRecognitionConstraint_get_HasCurrent __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_HasCurrent
#define IIterator_ISpeechRecognitionConstraint_MoveNext __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_MoveNext
#define IIterator_ISpeechRecognitionConstraint_GetMany __FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > interface
 */
#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x341dee1d, 0x6ac2, 0x5d06, 0x90,0x26, 0xb3,0x0a,0xda,0x20,0x56,0x65);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("341dee1d-6ac2-5d06-9026-b30ada205665")
                IVectorView<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > : IVectorView_impl<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x341dee1d, 0x6ac2, 0x5d06, 0x90,0x26, 0xb3,0x0a,0xda,0x20,0x56,0x65)
#endif
#else
typedef struct __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl;

interface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint {
    CONST_VTBL __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetAt(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 index,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Size(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_IndexOf(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_ISpeechRecognitionConstraint IID___FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IVectorView_ISpeechRecognitionConstraintVtbl __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl
#define IVectorView_ISpeechRecognitionConstraint __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IVectorView_ISpeechRecognitionConstraint_QueryInterface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface
#define IVectorView_ISpeechRecognitionConstraint_AddRef __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef
#define IVectorView_ISpeechRecognitionConstraint_Release __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release
#define IVectorView_ISpeechRecognitionConstraint_GetIids __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids
#define IVectorView_ISpeechRecognitionConstraint_GetRuntimeClassName __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName
#define IVectorView_ISpeechRecognitionConstraint_GetTrustLevel __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel
#define IVectorView_ISpeechRecognitionConstraint_GetAt __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetAt
#define IVectorView_ISpeechRecognitionConstraint_get_Size __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Size
#define IVectorView_ISpeechRecognitionConstraint_IndexOf __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_IndexOf
#define IVectorView_ISpeechRecognitionConstraint_GetMany __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > interface
 */
#ifndef ____FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__
#define ____FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x2691d763, 0x561e, 0x5060, 0xbb,0xc9, 0x7b,0x07,0x36,0x1a,0xcc,0x95);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("2691d763-561e-5060-bbc9-7b07361acc95")
                IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > : IVector_impl<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint, 0x2691d763, 0x561e, 0x5060, 0xbb,0xc9, 0x7b,0x07,0x36,0x1a,0xcc,0x95)
#endif
#else
typedef struct __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        TrustLevel *trustLevel);

    /*** IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *GetView)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint **value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *SetAt)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *value);

    HRESULT (STDMETHODCALLTYPE *InsertAt)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAt)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 index);

    HRESULT (STDMETHODCALLTYPE *Append)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *value);

    HRESULT (STDMETHODCALLTYPE *RemoveAtEnd)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    HRESULT (STDMETHODCALLTYPE *Clear)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *ReplaceAll)(
        __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint *This,
        UINT32 count,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items);

    END_INTERFACE
} __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl;

interface __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint {
    CONST_VTBL __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetView(This,value) (This)->lpVtbl->GetView(This,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_SetAt(This,index,value) (This)->lpVtbl->SetAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_InsertAt(This,index,value) (This)->lpVtbl->InsertAt(This,index,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_RemoveAt(This,index) (This)->lpVtbl->RemoveAt(This,index)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Append(This,value) (This)->lpVtbl->Append(This,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_RemoveAtEnd(This) (This)->lpVtbl->RemoveAtEnd(This)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Clear(This) (This)->lpVtbl->Clear(This)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#define __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_ReplaceAll(This,count,items) (This)->lpVtbl->ReplaceAll(This,count,items)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVector<ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionConstraint* > methods ***/
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetAt(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 index,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Size(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetView(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint **value) {
    return This->lpVtbl->GetView(This,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_IndexOf(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_SetAt(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 index,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *value) {
    return This->lpVtbl->SetAt(This,index,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_InsertAt(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 index,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *value) {
    return This->lpVtbl->InsertAt(This,index,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_RemoveAt(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 index) {
    return This->lpVtbl->RemoveAt(This,index);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Append(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint *value) {
    return This->lpVtbl->Append(This,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_RemoveAtEnd(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->RemoveAtEnd(This);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Clear(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This) {
    return This->lpVtbl->Clear(This);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
static FORCEINLINE HRESULT __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_ReplaceAll(__FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint* This,UINT32 count,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionConstraint **items) {
    return This->lpVtbl->ReplaceAll(This,count,items);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVector_ISpeechRecognitionConstraint IID___FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IVector_ISpeechRecognitionConstraintVtbl __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraintVtbl
#define IVector_ISpeechRecognitionConstraint __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint
#define IVector_ISpeechRecognitionConstraint_QueryInterface __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_QueryInterface
#define IVector_ISpeechRecognitionConstraint_AddRef __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_AddRef
#define IVector_ISpeechRecognitionConstraint_Release __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Release
#define IVector_ISpeechRecognitionConstraint_GetIids __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetIids
#define IVector_ISpeechRecognitionConstraint_GetRuntimeClassName __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetRuntimeClassName
#define IVector_ISpeechRecognitionConstraint_GetTrustLevel __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetTrustLevel
#define IVector_ISpeechRecognitionConstraint_GetAt __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetAt
#define IVector_ISpeechRecognitionConstraint_get_Size __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_get_Size
#define IVector_ISpeechRecognitionConstraint_GetView __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetView
#define IVector_ISpeechRecognitionConstraint_IndexOf __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_IndexOf
#define IVector_ISpeechRecognitionConstraint_SetAt __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_SetAt
#define IVector_ISpeechRecognitionConstraint_InsertAt __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_InsertAt
#define IVector_ISpeechRecognitionConstraint_RemoveAt __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_RemoveAt
#define IVector_ISpeechRecognitionConstraint_Append __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Append
#define IVector_ISpeechRecognitionConstraint_RemoveAtEnd __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_RemoveAtEnd
#define IVector_ISpeechRecognitionConstraint_Clear __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_Clear
#define IVector_ISpeechRecognitionConstraint_GetMany __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_GetMany
#define IVector_ISpeechRecognitionConstraint_ReplaceAll __FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_ReplaceAll
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVector_1_Windows__CMedia__CSpeechRecognition__CISpeechRecognitionConstraint_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterable<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > interface
 */
#ifndef ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__
#define ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0x0d9b7b48, 0x98a1, 0x5b22, 0x9a,0x66, 0x6f,0x81,0x2f,0x59,0x47,0xaa);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0d9b7b48-98a1-5b22-9a66-6f812f5947aa")
                IIterable<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > : IIterable_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0x0d9b7b48, 0x98a1, 0x5b22, 0x9a,0x66, 0x6f,0x81,0x2f,0x59,0x47,0xaa)
#endif
#else
typedef struct __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        TrustLevel *trustLevel);

    /*** IIterable<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *First)(
        __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **value);

    END_INTERFACE
} __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl;

interface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult {
    CONST_VTBL __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterable<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
#define __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_First(This,value) (This)->lpVtbl->First(This,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterable<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
static FORCEINLINE HRESULT __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_First(__FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **value) {
    return This->lpVtbl->First(This,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterable_SpeechRecognitionResult IID___FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IIterable_SpeechRecognitionResultVtbl __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl
#define IIterable_SpeechRecognitionResult __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IIterable_SpeechRecognitionResult_QueryInterface __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface
#define IIterable_SpeechRecognitionResult_AddRef __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef
#define IIterable_SpeechRecognitionResult_Release __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release
#define IIterable_SpeechRecognitionResult_GetIids __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids
#define IIterable_SpeechRecognitionResult_GetRuntimeClassName __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName
#define IIterable_SpeechRecognitionResult_GetTrustLevel __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel
#define IIterable_SpeechRecognitionResult_First __FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_First
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterable_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IIterator<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > interface
 */
#ifndef ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__
#define ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0x20756dd2, 0x6d3f, 0x5409, 0x84,0x6a, 0x0f,0x0f,0x01,0xd7,0xbf,0x9a);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("20756dd2-6d3f-5409-846a-0f0f01d7bf9a")
                IIterator<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > : IIterator_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0x20756dd2, 0x6d3f, 0x5409, 0x84,0x6a, 0x0f,0x0f,0x01,0xd7,0xbf,0x9a)
#endif
#else
typedef struct __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        TrustLevel *trustLevel);

    /*** IIterator<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *get_Current)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **value);

    HRESULT (STDMETHODCALLTYPE *get_HasCurrent)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        WINBOOL *value);

    HRESULT (STDMETHODCALLTYPE *MoveNext)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        WINBOOL *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **items,
        UINT32 *value);

    END_INTERFACE
} __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl;

interface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult {
    CONST_VTBL __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IIterator<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Current(This,value) (This)->lpVtbl->get_Current(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_HasCurrent(This,value) (This)->lpVtbl->get_HasCurrent(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_MoveNext(This,value) (This)->lpVtbl->MoveNext(This,value)
#define __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetMany(This,items_size,items,value) (This)->lpVtbl->GetMany(This,items_size,items,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IIterator<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Current(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **value) {
    return This->lpVtbl->get_Current(This,value);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_HasCurrent(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,WINBOOL *value) {
    return This->lpVtbl->get_HasCurrent(This,value);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_MoveNext(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,WINBOOL *value) {
    return This->lpVtbl->MoveNext(This,value);
}
static FORCEINLINE HRESULT __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetMany(__FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,UINT32 items_size,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IIterator_SpeechRecognitionResult IID___FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IIterator_SpeechRecognitionResultVtbl __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl
#define IIterator_SpeechRecognitionResult __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IIterator_SpeechRecognitionResult_QueryInterface __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface
#define IIterator_SpeechRecognitionResult_AddRef __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef
#define IIterator_SpeechRecognitionResult_Release __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release
#define IIterator_SpeechRecognitionResult_GetIids __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids
#define IIterator_SpeechRecognitionResult_GetRuntimeClassName __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName
#define IIterator_SpeechRecognitionResult_GetTrustLevel __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel
#define IIterator_SpeechRecognitionResult_get_Current __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Current
#define IIterator_SpeechRecognitionResult_get_HasCurrent __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_HasCurrent
#define IIterator_SpeechRecognitionResult_MoveNext __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_MoveNext
#define IIterator_SpeechRecognitionResult_GetMany __FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIIterator_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > interface
 */
#ifndef ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__
#define ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0x0e37810f, 0x1de6, 0x5199, 0x83,0x3f, 0x5a,0x6b,0x0b,0xd9,0x1e,0x23);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            namespace Collections {
                template<>
                MIDL_INTERFACE("0e37810f-1de6-5199-833f-5a6b0bd91e23")
                IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > : IVectorView_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult* > >
                {
                };
            }
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0x0e37810f, 0x1de6, 0x5199, 0x83,0x3f, 0x5a,0x6b,0x0b,0xd9,0x1e,0x23)
#endif
#else
typedef struct __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        TrustLevel *trustLevel);

    /*** IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *GetAt)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        UINT32 index,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **value);

    HRESULT (STDMETHODCALLTYPE *get_Size)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        UINT32 *value);

    HRESULT (STDMETHODCALLTYPE *IndexOf)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *element,
        UINT32 *index,
        BOOLEAN *value);

    HRESULT (STDMETHODCALLTYPE *GetMany)(
        __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        UINT32 start_index,
        UINT32 items_size,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **items,
        UINT32 *value);

    END_INTERFACE
} __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl;

interface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult {
    CONST_VTBL __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetAt(This,index,value) (This)->lpVtbl->GetAt(This,index,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Size(This,value) (This)->lpVtbl->get_Size(This,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_IndexOf(This,element,index,value) (This)->lpVtbl->IndexOf(This,element,index,value)
#define __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetMany(This,start_index,items_size,items,value) (This)->lpVtbl->GetMany(This,start_index,items_size,items,value)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IVectorView<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetAt(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,UINT32 index,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **value) {
    return This->lpVtbl->GetAt(This,index,value);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Size(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,UINT32 *value) {
    return This->lpVtbl->get_Size(This,value);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_IndexOf(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult *element,UINT32 *index,BOOLEAN *value) {
    return This->lpVtbl->IndexOf(This,element,index,value);
}
static FORCEINLINE HRESULT __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetMany(__FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,UINT32 start_index,UINT32 items_size,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **items,UINT32 *value) {
    return This->lpVtbl->GetMany(This,start_index,items_size,items,value);
}
#endif
#ifdef WIDL_using_Windows_Foundation_Collections
#define IID_IVectorView_SpeechRecognitionResult IID___FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IVectorView_SpeechRecognitionResultVtbl __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl
#define IVectorView_SpeechRecognitionResult __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IVectorView_SpeechRecognitionResult_QueryInterface __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface
#define IVectorView_SpeechRecognitionResult_AddRef __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef
#define IVectorView_SpeechRecognitionResult_Release __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release
#define IVectorView_SpeechRecognitionResult_GetIids __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids
#define IVectorView_SpeechRecognitionResult_GetRuntimeClassName __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName
#define IVectorView_SpeechRecognitionResult_GetTrustLevel __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel
#define IVectorView_SpeechRecognitionResult_GetAt __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetAt
#define IVectorView_SpeechRecognitionResult_get_Size __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Size
#define IVectorView_SpeechRecognitionResult_IndexOf __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_IndexOf
#define IVectorView_SpeechRecognitionResult_GetMany __FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetMany
#endif /* WIDL_using_Windows_Foundation_Collections */
#endif

#endif

#endif  /* ____FIVectorView_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult, 0x78c859bd, 0x14d4, 0x5c40, 0xab,0xff, 0x49,0x06,0x16,0xd5,0xe9,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("78c859bd-14d4-5c40-abff-490616d5e92d")
            IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionCompilationResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult, 0x78c859bd, 0x14d4, 0x5c40, 0xab,0xff, 0x49,0x06,0x16,0xd5,0xe9,0x2d)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Release(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > methods ***/
static FORCEINLINE HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_SpeechRecognitionCompilationResult IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult
#define IAsyncOperationCompletedHandler_SpeechRecognitionCompilationResultVtbl __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl
#define IAsyncOperationCompletedHandler_SpeechRecognitionCompilationResult __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult
#define IAsyncOperationCompletedHandler_SpeechRecognitionCompilationResult_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_QueryInterface
#define IAsyncOperationCompletedHandler_SpeechRecognitionCompilationResult_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_AddRef
#define IAsyncOperationCompletedHandler_SpeechRecognitionCompilationResult_Release __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Release
#define IAsyncOperationCompletedHandler_SpeechRecognitionCompilationResult_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > interface
 */
#ifndef ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__
#define ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0xc2195c7d, 0xdcc2, 0x5c6d, 0x91,0x62, 0xc8,0xdf,0x66,0x52,0x87,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("c2195c7d-dcc2-5c6d-9162-c8df66528762")
            IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > : IAsyncOperationCompletedHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0xc2195c7d, 0xdcc2, 0x5c6d, 0x91,0x62, 0xc8,0xdf,0x66,0x52,0x87,0x62)
#endif
#else
typedef struct __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    /*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *info,
        AsyncStatus status);

    END_INTERFACE
} __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl;

interface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult {
    CONST_VTBL __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(This) (This)->lpVtbl->Release(This)
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
#define __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Invoke(This,info,status) (This)->lpVtbl->Invoke(This,info,status)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IAsyncOperationCompletedHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
static FORCEINLINE HRESULT __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Invoke(__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *info,AsyncStatus status) {
    return This->lpVtbl->Invoke(This,info,status);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperationCompletedHandler_SpeechRecognitionResult IID___FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IAsyncOperationCompletedHandler_SpeechRecognitionResultVtbl __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl
#define IAsyncOperationCompletedHandler_SpeechRecognitionResult __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IAsyncOperationCompletedHandler_SpeechRecognitionResult_QueryInterface __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface
#define IAsyncOperationCompletedHandler_SpeechRecognitionResult_AddRef __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef
#define IAsyncOperationCompletedHandler_SpeechRecognitionResult_Release __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release
#define IAsyncOperationCompletedHandler_SpeechRecognitionResult_Invoke __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult, 0xa392249a, 0xe28a, 0x564a, 0x9e,0x73, 0x1d,0xda,0x63,0xca,0x64,0x3c);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("a392249a-e28a-564a-9e73-1dda63ca643c")
            IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionCompilationResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult, 0xa392249a, 0xe28a, 0x564a, 0x9e,0x73, 0x1d,0xda,0x63,0xca,0x64,0x3c)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl;

interface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult {
    CONST_VTBL __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_QueryInterface(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_AddRef(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Release(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetIids(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetTrustLevel(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionCompilationResult* > methods ***/
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_put_Completed(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_get_Completed(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetResults(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionCompilationResult **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_SpeechRecognitionCompilationResult IID___FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult
#define IAsyncOperation_SpeechRecognitionCompilationResultVtbl __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResultVtbl
#define IAsyncOperation_SpeechRecognitionCompilationResult __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult
#define IAsyncOperation_SpeechRecognitionCompilationResult_QueryInterface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_QueryInterface
#define IAsyncOperation_SpeechRecognitionCompilationResult_AddRef __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_AddRef
#define IAsyncOperation_SpeechRecognitionCompilationResult_Release __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_Release
#define IAsyncOperation_SpeechRecognitionCompilationResult_GetIids __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetIids
#define IAsyncOperation_SpeechRecognitionCompilationResult_GetRuntimeClassName __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetRuntimeClassName
#define IAsyncOperation_SpeechRecognitionCompilationResult_GetTrustLevel __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetTrustLevel
#define IAsyncOperation_SpeechRecognitionCompilationResult_put_Completed __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_put_Completed
#define IAsyncOperation_SpeechRecognitionCompilationResult_get_Completed __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_get_Completed
#define IAsyncOperation_SpeechRecognitionCompilationResult_GetResults __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionCompilationResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > interface
 */
#ifndef ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__
#define ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__

DEFINE_GUID(IID___FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0xba3eebe8, 0x8d7c, 0x51f2, 0x9e,0xd4, 0xeb,0xaf,0xe3,0x67,0x4d,0xb4);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("ba3eebe8-8d7c-51f2-9ed4-ebafe3674db4")
            IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > : IAsyncOperation_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionResult* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult, 0xba3eebe8, 0x8d7c, 0x51f2, 0x9e,0xd4, 0xeb,0xaf,0xe3,0x67,0x4d,0xb4)
#endif
#else
typedef struct __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This);

    /*** IInspectable methods ***/
    HRESULT (STDMETHODCALLTYPE *GetIids)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        ULONG *iidCount,
        IID **iids);

    HRESULT (STDMETHODCALLTYPE *GetRuntimeClassName)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        HSTRING *className);

    HRESULT (STDMETHODCALLTYPE *GetTrustLevel)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        TrustLevel *trustLevel);

    /*** IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
    HRESULT (STDMETHODCALLTYPE *put_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *handler);

    HRESULT (STDMETHODCALLTYPE *get_Completed)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        __FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **handler);

    HRESULT (STDMETHODCALLTYPE *GetResults)(
        __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **results);

    END_INTERFACE
} __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl;

interface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult {
    CONST_VTBL __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(This) (This)->lpVtbl->Release(This)
/*** IInspectable methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(This,iidCount,iids) (This)->lpVtbl->GetIids(This,iidCount,iids)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(This,className) (This)->lpVtbl->GetRuntimeClassName(This,className)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(This,trustLevel) (This)->lpVtbl->GetTrustLevel(This,trustLevel)
/*** IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_put_Completed(This,handler) (This)->lpVtbl->put_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Completed(This,handler) (This)->lpVtbl->get_Completed(This,handler)
#define __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetResults(This,results) (This)->lpVtbl->GetResults(This,results)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This) {
    return This->lpVtbl->Release(This);
}
/*** IInspectable methods ***/
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,ULONG *iidCount,IID **iids) {
    return This->lpVtbl->GetIids(This,iidCount,iids);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,HSTRING *className) {
    return This->lpVtbl->GetRuntimeClassName(This,className);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,TrustLevel *trustLevel) {
    return This->lpVtbl->GetTrustLevel(This,trustLevel);
}
/*** IAsyncOperation<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionResult* > methods ***/
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_put_Completed(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult *handler) {
    return This->lpVtbl->put_Completed(This,handler);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Completed(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,__FIAsyncOperationCompletedHandler_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult **handler) {
    return This->lpVtbl->get_Completed(This,handler);
}
static FORCEINLINE HRESULT __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetResults(__FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionResult **results) {
    return This->lpVtbl->GetResults(This,results);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_IAsyncOperation_SpeechRecognitionResult IID___FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IAsyncOperation_SpeechRecognitionResultVtbl __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResultVtbl
#define IAsyncOperation_SpeechRecognitionResult __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult
#define IAsyncOperation_SpeechRecognitionResult_QueryInterface __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_QueryInterface
#define IAsyncOperation_SpeechRecognitionResult_AddRef __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_AddRef
#define IAsyncOperation_SpeechRecognitionResult_Release __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_Release
#define IAsyncOperation_SpeechRecognitionResult_GetIids __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetIids
#define IAsyncOperation_SpeechRecognitionResult_GetRuntimeClassName __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetRuntimeClassName
#define IAsyncOperation_SpeechRecognitionResult_GetTrustLevel __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetTrustLevel
#define IAsyncOperation_SpeechRecognitionResult_put_Completed __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_put_Completed
#define IAsyncOperation_SpeechRecognitionResult_get_Completed __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_get_Completed
#define IAsyncOperation_SpeechRecognitionResult_GetResults __FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_GetResults
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FIAsyncOperation_1_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionResult_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs, 0x8103c018, 0x7952, 0x59f9, 0x9f,0x41, 0x23,0xb1,0x7d,0x6e,0x45,0x2d);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("8103c018-7952-59f9-9f41-23b17d6e452d")
            ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*, ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionSession* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs*, ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionCompletedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs, 0x8103c018, 0x7952, 0x59f9, 0x9f,0x41, 0x23,0xb1,0x7d,0x6e,0x45,0x2d)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *sender,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionCompletedEventArgs* > methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *sender,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionCompletedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionCompletedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionCompletedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgsVtbl
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionCompletedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionCompletedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_QueryInterface
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionCompletedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_AddRef
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionCompletedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_Release
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionCompletedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionCompletedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs, 0x26192073, 0xa2c9, 0x527d, 0x9b,0xd3, 0x91,0x1c,0x05,0xe0,0x01,0x1e);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("26192073-a2c9-527d-9bd3-911c05e0011e")
            ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*, ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionSession* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs*, ABI::Windows::Media::SpeechRecognition::ISpeechContinuousRecognitionResultGeneratedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs, 0x26192073, 0xa2c9, 0x527d, 0x9b,0xd3, 0x91,0x1c,0x05,0xe0,0x01,0x1e)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *sender,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionSession*,ABI::Windows::Media::SpeechRecognition::SpeechContinuousRecognitionResultGeneratedEventArgs* > methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionSession *sender,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechContinuousRecognitionResultGeneratedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionResultGeneratedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionResultGeneratedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgsVtbl
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionResultGeneratedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_QueryInterface
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionResultGeneratedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_AddRef
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionResultGeneratedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_Release
#define ITypedEventHandler_SpeechContinuousRecognitionSession_SpeechContinuousRecognitionResultGeneratedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionSession_Windows__CMedia__CSpeechRecognition__CSpeechContinuousRecognitionResultGeneratedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs, 0x4cb45aba, 0x7573, 0x545a, 0xb2,0x9a, 0xe9,0xbe,0x35,0xbd,0x46,0x82);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("4cb45aba-7573-545a-b29a-e9be35bd4682")
            ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionHypothesisGeneratedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs, 0x4cb45aba, 0x7573, 0x545a, 0xb2,0x9a, 0xe9,0xbe,0x35,0xbd,0x46,0x82)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *sender,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionHypothesisGeneratedEventArgs* > methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *sender,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionHypothesisGeneratedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SpeechRecognizer_SpeechRecognitionHypothesisGeneratedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionHypothesisGeneratedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgsVtbl
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionHypothesisGeneratedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_QueryInterface
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionHypothesisGeneratedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_AddRef
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionHypothesisGeneratedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_Release
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionHypothesisGeneratedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionHypothesisGeneratedEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs, 0x15ca7918, 0x61d2, 0x57b2, 0xb9,0x33, 0x44,0x06,0x3e,0x8b,0xb6,0x62);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("15ca7918-61d2-57b2-b933-44063e8bb662")
            ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognitionQualityDegradingEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs, 0x15ca7918, 0x61d2, 0x57b2, 0xb9,0x33, 0x44,0x06,0x3e,0x8b,0xb6,0x62)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *sender,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognitionQualityDegradingEventArgs* > methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *sender,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognitionQualityDegradingEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SpeechRecognizer_SpeechRecognitionQualityDegradingEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionQualityDegradingEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgsVtbl
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionQualityDegradingEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionQualityDegradingEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_QueryInterface
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionQualityDegradingEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_AddRef
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionQualityDegradingEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_Release
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognitionQualityDegradingEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognitionQualityDegradingEventArgs_INTERFACE_DEFINED__ */

/*****************************************************************************
 * ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs* > interface
 */
#ifndef ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_INTERFACE_DEFINED__
#define ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_INTERFACE_DEFINED__

DEFINE_GUID(IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs, 0xd1185e92, 0x5c30, 0x5561, 0xb3,0xe2, 0xe8,0x2d,0xdb,0xd8,0x72,0xc3);
#if defined(__cplusplus) && !defined(CINTERFACE)
} /* extern "C" */
namespace ABI {
    namespace Windows {
        namespace Foundation {
            template<>
            MIDL_INTERFACE("d1185e92-5c30-5561-b3e2-e82ddbd872c3")
            ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs* > : ITypedEventHandler_impl<ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognizer* >, ABI::Windows::Foundation::Internal::AggregateType<ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs*, ABI::Windows::Media::SpeechRecognition::ISpeechRecognizerStateChangedEventArgs* > >
            {
            };
        }
    }
}
extern "C" {
#ifdef __CRT_UUID_DECL
__CRT_UUID_DECL(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs, 0xd1185e92, 0x5c30, 0x5561, 0xb3,0xe2, 0xe8,0x2d,0xdb,0xd8,0x72,0xc3)
#endif
#else
typedef struct __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgsVtbl {
    BEGIN_INTERFACE

    /*** IUnknown methods ***/
    HRESULT (STDMETHODCALLTYPE *QueryInterface)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs *This,
        REFIID riid,
        void **ppvObject);

    ULONG (STDMETHODCALLTYPE *AddRef)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs *This);

    ULONG (STDMETHODCALLTYPE *Release)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs *This);

    /*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs* > methods ***/
    HRESULT (STDMETHODCALLTYPE *Invoke)(
        __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs *This,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *sender,
        __x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *args);

    END_INTERFACE
} __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgsVtbl;

interface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs {
    CONST_VTBL __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgsVtbl* lpVtbl;
};

#ifdef COBJMACROS
#ifndef WIDL_C_INLINE_WRAPPERS
/*** IUnknown methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_QueryInterface(This,riid,ppvObject) (This)->lpVtbl->QueryInterface(This,riid,ppvObject)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_AddRef(This) (This)->lpVtbl->AddRef(This)
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_Release(This) (This)->lpVtbl->Release(This)
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs* > methods ***/
#define __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_Invoke(This,sender,args) (This)->lpVtbl->Invoke(This,sender,args)
#else
/*** IUnknown methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_QueryInterface(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs* This,REFIID riid,void **ppvObject) {
    return This->lpVtbl->QueryInterface(This,riid,ppvObject);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_AddRef(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs* This) {
    return This->lpVtbl->AddRef(This);
}
static FORCEINLINE ULONG __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_Release(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs* This) {
    return This->lpVtbl->Release(This);
}
/*** ITypedEventHandler<ABI::Windows::Media::SpeechRecognition::SpeechRecognizer*,ABI::Windows::Media::SpeechRecognition::SpeechRecognizerStateChangedEventArgs* > methods ***/
static FORCEINLINE HRESULT __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_Invoke(__FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs* This,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizer *sender,__x_ABI_CWindows_CMedia_CSpeechRecognition_CISpeechRecognizerStateChangedEventArgs *args) {
    return This->lpVtbl->Invoke(This,sender,args);
}
#endif
#ifdef WIDL_using_Windows_Foundation
#define IID_ITypedEventHandler_SpeechRecognizer_SpeechRecognizerStateChangedEventArgs IID___FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognizerStateChangedEventArgsVtbl __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgsVtbl
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognizerStateChangedEventArgs __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognizerStateChangedEventArgs_QueryInterface __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_QueryInterface
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognizerStateChangedEventArgs_AddRef __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_AddRef
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognizerStateChangedEventArgs_Release __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_Release
#define ITypedEventHandler_SpeechRecognizer_SpeechRecognizerStateChangedEventArgs_Invoke __FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_Invoke
#endif /* WIDL_using_Windows_Foundation */
#endif

#endif

#endif  /* ____FITypedEventHandler_2_Windows__CMedia__CSpeechRecognition__CSpeechRecognizer_Windows__CMedia__CSpeechRecognition__CSpeechRecognizerStateChangedEventArgs_INTERFACE_DEFINED__ */

/* Begin additional prototypes for all interfaces */

ULONG           __RPC_USER HSTRING_UserSize     (ULONG *, ULONG, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserMarshal  (ULONG *, unsigned char *, HSTRING *);
unsigned char * __RPC_USER HSTRING_UserUnmarshal(ULONG *, unsigned char *, HSTRING *);
void            __RPC_USER HSTRING_UserFree     (ULONG *, HSTRING *);

/* End additional prototypes */

#ifdef __cplusplus
}
#endif

#endif /* __windows_media_speechrecognition_h__ */
