/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utsig.h#20 $
 *   $Revision: #20 $
 *   $DateTime: 2007/03/02 16:51:57 $
 **************************************************************************
 * File Description: GKI signal definitions for utilities.
 **************************************************************************/
#if !defined (SIGNAL_MAPPING) //2012.1.6, copied from "jcweng for signal log"
    SIG_DEF( SIG_UT_LOG_DUMMY = UT_SIGNAL_BASE,    EmptySignal                 ut_dummy)
#endif
#if !defined (EXCLUDE_UT_LOGGER)
    SIG_DEF( SIG_UT_LOG_ERROR_REQ,                 UtLogErrorReq               utLogErrorReq)
    SIG_DEF( SIG_UT_LOG_PDU_IND,                   UtLogPduInd                 utLogPduInd)
    /* Logging signal from the Extended Block Memory Manager (EBMM) to the application layer. */
    SIG_DEF( SIG_EBMM_LOG_ACTION_IND,              EbmmLogActionInd            ebmmLogActionInd)
#else
    SIG_DEF( SIG_UT_LOG_ERROR_REQ,                 EmptySignal                 utLogErrorReqNotPresent)
    SIG_DEF( SIG_UT_LOG_PDU_IND,                   EmptySignal                 utLogPduIndNotPresent)
    SIG_DEF( SIG_EBMM_LOG_ACTION_IND,              EmptySignal                 ebmmLogActionIndNotPresent)
#endif /* !defined (EXCLUDE_UT_LOGGER) */
    SIG_DEF( SIG_UT_FLEXIBLE_TRACE_CONTROL_REQ,    UtFlexibleTraceControlReq   utFlexibleTraceControlReq)
    SIG_DEF( SIG_UT_FLEXIBLE_TRACE_OUT,            UtFlexibleTraceOut          utFlexibleTraceOut)
    SIG_DEF( SIG_UT_FLEXIBLE_TRACE_TA_CONTROL_REQ, UtFlexibleTraceTaControlReq utFlexibleTraceTaControlReq)
    SIG_DEF( SIG_UT_FLEXIBLE_TRACE_TA_OUT,         UtFlexibleTraceTaOut        utFlexibleTraceTaOut)
    SIG_DEF( SIG_UT_FLEXIBLE_TRACE_TASK_INFO,      UtFlexibleTraceTaskInfo     utFlexibleTraceTaskInfo)

#if defined (UPGRADE_SYS_TOOLS)
#if defined (UT_ML_USE_MINILOGGER)
    SIG_DEF( SIG_ML_EVENT_RETRIEVAL_IND,           MiniLoggerEvent             mlEventRetrievalInd)
#else /* UT_ML_USE_MINILOGGER */
    SIG_DEF( SIG_ML_EVENT_RETRIEVAL_IND,           EmptySignal                 mlEventRetrievalIndNotPresent)
#endif /* UT_ML_USE_MINILOGGER */
#else /* UPGRADE_SYS_TOOLS */
    SIG_DEF( SIG_ML_EVENT_RETRIEVAL_IND,           EmptySignal                 mlEventRetrievalIndNotPresent)
#endif /* UPGRADE_SYS_TOOLS */

    SIG_DEF( SIG_TEST_PERFORMANCE_LOG_REQ,         PerfLogReq                  perfLogReq)
    SIG_DEF( SIG_TEST_PERFORMANCE_LOG_CNF,         PerfLogCnf                  perfLogCnf)

#if !defined (EXCLUDE_TMM)
#if !defined (SIGNAL_MAPPING) //2012.1.6, copied from "jcweng for signal log"
    SIG_DEF( SIG_UT_TMM_DUMMY = UTTMM_SIGNAL_BASE, EmptySignal                      dummyUt)
#endif
    SIG_DEF( SIG_UT_MEM_ABOVE_VHWM_IND,	    UtMemAboveVhwmInd		utMemAboveVhwmInd)
    SIG_DEF( SIG_UT_MEM_ABOVE_HWM_IND,             UtMemAboveHwmInd                 utMemAboveHwmInd)
    SIG_DEF( SIG_UT_MEM_BELOW_LWM_IND,             UtMemBelowLwmInd                 utMemBelowLwmInd)
    SIG_DEF( SIG_UT_MEM_OP_DEBUG_IND,              UtMemOpDebugInd                  utMemOpDebugInd)
#endif

