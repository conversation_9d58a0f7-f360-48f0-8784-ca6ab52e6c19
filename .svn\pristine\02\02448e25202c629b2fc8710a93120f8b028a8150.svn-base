/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */

#ifndef _WRL_MODULE_H_
#define _WRL_MODULE_H_

#include <roapi.h>
#include <activation.h>
#include <winstring.h>
#include <intrin.h>
#include <winapifamily.h>

/* #include <wrl/def.h> */
#include <wrl/internal.h>
#include <wrl/client.h>
/* #include <wrl/implements.h> */
/* #include <wrl/ftm.h> */
#include <wrl/wrappers/corewrappers.h>

#endif
