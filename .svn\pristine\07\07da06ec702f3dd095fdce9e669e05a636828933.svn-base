#include <inttypes.h>
#include <stdarg.h>

#include "global_types.h"
#include "diag_API.h"
#include "utilities.h"
#include "osa.h"
#include <diag_API.h>
#include <log.h>

#define RTI_CURRENT_TICK	timerCountRead(TS_TIMER_ID)

// #define LOG_NUM		256
// #define LOG_SIZE	92

#ifdef MEMLOG_ENABLE
extern UINT32 Image$$DDR_NONCACHE_REGION_MEMLOG$$Base;
extern int OS_Current_Interrupt_Count(void);
extern void *malloc( unsigned int Size );


#define MEMPOOL_SZ	((16*1024) - 8)
#define LOG_SIZE	92
#define LOG_NUM		MEMPOOL_SZ/LOG_SIZE
#define LOG_ADDR	(&(Image$$DDR_NONCACHE_REGION_MEMLOG$$Base))

unsigned char * CP_MEMLOG_BASE_ADDR=(unsigned char*)LOG_ADDR;
unsigned int  CP_MEMLOG_SIZE=MEMPOOL_SZ;

static uint8_t *_logbuffer = (uint8_t*)LOG_ADDR;

static volatile uint16_t _log_put_index = 0;
static volatile uint16_t _log_get_index = 0;
static uint32_t _log_level = LOG_ERR_VALUE;
static void (*_log_output)(const char *log, uint32_t length);
static void (*_raw_log_output)(const char *log, uint32_t length);
static OSAFlagRef _log_tx_flag = NULL;

#define LOG_TASK_STACK_SIZE		1024
static OSTaskRef _log_task_ref = NULL;
static uint8_t _log_task_stack[LOG_TASK_STACK_SIZE];

struct log_module
{
	uint32_t module;
	uint32_t level;

	struct log_module *next;
};

#define MODULES_NUM 64
struct log_module *modules[MODULES_NUM] = {NULL};

#endif//MEMLOG_ENABLE

/* get module in the log sub-system*/
struct log_module *log_module_get(uint32_t module)
{
	struct log_module *lmodule = NULL;
#ifdef MEMLOG_ENABLE

	lmodule = modules[module % MODULES_NUM];
	while (lmodule != NULL && lmodule->module != module) 
	{
		lmodule = lmodule->next;
	}

	if (lmodule == NULL) /* add a module */
	{
		if (OS_Current_Interrupt_Count()) return NULL; /* ISR routine */
			
		lmodule = malloc (sizeof(struct log_module));
		if (lmodule == NULL) return NULL; /* out of memory */

		lmodule->module = module;
		lmodule->level = _log_level;
		lmodule->next = modules[module % MODULES_NUM];

		/* add to the module array */
		modules[module % MODULES_NUM] = lmodule;
	}

#endif
	return lmodule;
}

void log_printf(const char* fmt, ...)
{
#ifdef MEMLOG_ENABLE
    va_list ap;
	uint8_t* log_ptr;
	uint32_t index, next_index;
	uint32_t level, tick;
	uint32_t offset;
	uint32_t module;
return;
	/* get log level */
	if (fmt[0] == '<')
	{
		if (fmt[2] == '>')
		{
			level = fmt[1] - '0';
			fmt += 3;

			/* get module */
			if (fmt[0] == '[')
			{
				if (fmt[5] == ']')
				{
					struct log_module *lmodule;
					
					module = (fmt[1] << 24) | (fmt[2] << 16) | 
						(fmt[3] << 8) | fmt[4];
			
					/* get filter level of module */
					lmodule = log_module_get(module);
					if ((lmodule != NULL) && (level > lmodule->level)) 
						return;
				}
			}
			else if (level > _log_level)
				return;
		}
	}

	level = disableInterrupts();
	index = _log_put_index;
	next_index = _log_put_index + 1;
	if (next_index >= LOG_NUM) next_index = 0;

	if (next_index == _log_get_index) 
	{
		restoreInterrupts(level); /* full */
		return;
	}

	_log_put_index = next_index;
	log_ptr = &_logbuffer[index * LOG_SIZE];
	restoreInterrupts(level);

	/* get tick and put time stamp */
	tick = RTI_CURRENT_TICK;
	offset = sprintf((char*)log_ptr, "[%08x] ", tick);

	/* made log pdu */
    va_start (ap, fmt);
    vsnprintf((char*)(log_ptr + offset), LOG_SIZE - 1 - offset, fmt, ap);
    va_end (ap);

	/* set terminal character */
	log_ptr[LOG_SIZE - 1] = '\0';

	if (_raw_log_output != NULL)
	{
		_raw_log_output((const char *)log_ptr, strlen((const char *)log_ptr));

		if (_log_put_index == 0) _log_get_index = LOG_NUM - 1;
		else _log_get_index = _log_put_index - 1;
	}
	else
	{
		/* notify log task to send log */
		if ((OS_Current_Interrupt_Count() == 0) && !(level & 0x80))
		{
			OSAFlagSet(_log_tx_flag, 0x01, OSA_FLAG_OR);
		}
	}
	
	/*The va_end has been called after calling va_start*/
	/*coverity[missing_va_end]*/
#endif
}

void log_dump(const unsigned char* ptr, unsigned int length)
{
#ifdef MEMLOG_ENABLE
	uint8_t* line_ptr;
	uint32_t index, next_index;
	uint32_t line_size, line_index;
	uint32_t level;
	
	if ((length == 0) || ptr == NULL) return;
	line_size = LOG_SIZE/3;

	while (length)
	{
		level = disableInterrupts();
		index = _log_put_index;
		next_index = _log_put_index + 1;
		if (next_index >= LOG_NUM) next_index = 0;
		
		if (next_index == _log_get_index) 
		{
			restoreInterrupts(level); /* full */
			break;
		}
		
		_log_put_index = next_index;
		line_ptr = &_logbuffer[index * LOG_SIZE];
		restoreInterrupts(level);

		for (line_index = 0; (length > 0 && line_index < line_size); line_index ++)
		{
			sprintf((char*)line_ptr, "%02x ", *ptr);

			ptr ++; line_ptr += 3; length --;
		}
		*line_ptr = '\n'; *(line_ptr + 1) = '\0';
	}

	/* notify log task to send log */
	if (!OS_Current_Interrupt_Count())
	{
		OSAFlagSet(_log_tx_flag, 0x01, OSA_FLAG_OR);
	}
#endif
}

void log_flush(void)
{
#ifdef MEMLOG_ENABLE
	uint32_t level;
	uint8_t *log_ptr;

	while (_log_get_index != _log_put_index)
	{
		level = disableInterrupts();
		/* get log pdu */
		log_ptr = &_logbuffer[_log_get_index * LOG_SIZE];
	
		/* move forward */
		_log_get_index += 1;
		if (_log_get_index >= LOG_NUM) _log_get_index = 0;
		restoreInterrupts(level);
	
		/* send out log */
		_log_output((char*)log_ptr, strlen((char*)log_ptr));
	}
#endif
}

void _log_output_empty(const char *log, uint32_t length)
{
	/* nothing */
	return ;
}

void _log_output_ICAT(const char *log, uint32_t length)
{
#ifdef MEMLOG_ENABLE
	DIAG_FILTER(SW_PLAT, Log, ICATLog, DIAG_INFORMATION)
	diagPrintf("%s", log);
#endif
}

void _log_output_ICAT_ln(const char *log, uint32_t length)
{
#ifdef MEMLOG_ENABLE
	uint8_t line[128];
	uint8_t *line_ptr;

	while (length > 0)
	{
		line_ptr = line;
		while (*log != '\0' && *log != '\n')
		{
			*line_ptr = *log;
			log ++; line_ptr ++;

			length --;

			if (line_ptr - line >= sizeof(line) - 1) 
			{
				//[klocwork][issue id: 8958]
				line[sizeof(line)-1] = '\0';
				break;
			}
		}

		if (line_ptr != line)
		{
			*line_ptr = '\0';
			
			DIAG_FILTER(SW_PLAT, Log, ICATLogLn, DIAG_INFORMATION)
			diagPrintf("%s", line);
		}

		if (*log == '\0' || length == 0) break;

		/* skip '\n' */
		log ++;
		length --;
	}
#endif
}

void _log_output_uart(const char *log, uint32_t length)
{
#ifdef MEMLOG_ENABLE
	uint8_t *ptr;
	extern void seagull_uart_putc(const char ch);

	ptr = (uint8_t*) log;
	while (length)
	{
		seagull_uart_putc(*ptr);

		ptr ++; length --;
	}
#endif
}

static void log_task_entry(void* parameter)
{
#ifdef MEMLOG_ENABLE
	OSA_STATUS osaStatus;
	uint32_t event;
	uint8_t* log_ptr;
	uint32_t level;

	while (1)
	{
		osaStatus = OSAFlagWait(_log_tx_flag, 0x01, OSA_FLAG_OR_CLEAR, (UINT32*)&event, OSA_SUSPEND);
		if (osaStatus == OS_SUCCESS)
		{
			while (_log_get_index != _log_put_index)
			{
				level = disableInterrupts();
				/* get log pdu */
				log_ptr = &_logbuffer[_log_get_index * LOG_SIZE];

				/* move forward */
				_log_get_index += 1;
				if (_log_get_index >= LOG_NUM) _log_get_index = 0;
				restoreInterrupts(level);

				/* send out log */
				_log_output((char*)log_ptr, strlen((char*)log_ptr));
			}
		}
	}
#endif
}

void log_init(void)
{
#ifdef MEMLOG_ENABLE

	OSA_STATUS osaStatus;
#if 0	
	_log_output = _log_output_uart;
#else
	_log_output = _log_output_ICAT_ln;
#endif
	_raw_log_output = NULL;

	// _raw_log_output = _log_output_uart;

	memset(&modules[0], 0x00, sizeof(modules));
	
	/* create flag */
	osaStatus = OSAFlagCreate( &_log_tx_flag ) ;
	ASSERT( osaStatus == OS_SUCCESS ) ;

	osaStatus = OSTaskCreate(&_log_task_ref,
						  (void *)_log_task_stack, LOG_TASK_STACK_SIZE,
						  230, "log", log_task_entry, NULL);
	ASSERT(osaStatus == OS_SUCCESS);
#endif
}

void log_set_module_level(const char* module, int level)
{
#ifdef MEMLOG_ENABLE
	uint32_t module_index;
	struct log_module *lmodule;
	
	/* find module and set filter level */
	if (module[0] == '[')
		module += 1;
	module_index = (module[0] << 24) | (module[1] << 16) | (module[2] << 8) | module[3];
	lmodule = log_module_get(module_index);
	if (lmodule != NULL)
	{
		lmodule->level = level;
	}
#endif
}

int log_get_module_level(const char* module)
{
#ifdef MEMLOG_ENABLE
	uint32_t module_index;
	struct log_module *lmodule;

	if(!module)
		return (int)(_log_level);

	/* find module and set filter level */
	if (module[0] == '[' && module[5] == ']')
		module += 1;
	module_index = (module[0] << 24) | (module[1] << 16) | (module[2] << 8) | module[3];
	lmodule = log_module_get(module_index);
	if (lmodule)
		return lmodule->level;
	else
		return (int)(_log_level);
#else
return LOG_ERR_VALUE;
#endif
}

void log_set_trace_level(int level){}
#ifdef MEMLOG_ENABLE

void log_set_trace_level(int level)
{
	if (level <= LOG_DEBUG_VALUE)
	{
		_log_level = level;
	}
}


//ICAT EXPORTED FUNCTION - SW_PLAT,Log,SetTraceLevel
void log_set_trace_level_helper(void* parameter)
{
	int level;

	level = *(int*)parameter;
	if (level >= 0 && level <= LOG_DEBUG_VALUE)
	{
		log_set_trace_level(level);
	}
}

//ICAT EXPORTED FUNCTION - SW_PLAT,Log,SetOutput
void log_set_output(void *parameter)
{
	int output;

	output = *(int*)parameter;
	switch (output)
	{
	case LOG_OUTPUT_MEMLOG:
		break;
		
	case LOG_OUTPUT_NONE:
		_log_output = _log_output_empty;
		break;
	case LOG_OUTPUT_ACAT:
		_log_output = _log_output_ICAT_ln;
		break;
	case LOG_OUTPUT_UART:
		_log_output = _log_output_uart;
		break;
	case LOG_OUTPUT_NVM:
		break;
	}
}
#endif

