/*------------------------------------------------------------ 
(C) Copyright [2006-2008] Marvell International Ltd. 
All Rights Reserved 
------------------------------------------------------------*/ 
/*-------------------------------------------------------------------------------------------------------------------- 
INTEL CONFIDENTIAL 
Copyright 2006 Intel Corporation All Rights Reserved. 
The source code contained or described herein and all documents related to the source code ("Material") are owned 
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or 
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of 
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and 
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted, 
transmitted, distributed, or disclosed in any way without Intel's prior express written permission. 
No license under any patent, copyright, trade secret or other intellectual property right is granted to or 
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement, 
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by 
Intel in writing. 
-------------------------------------------------------------------------------------------------------------------*/ 
/********************************************************************** 
* 
* Filename: 
* 
* Programmers: 
* 
* Functions: 
* 
* Description: 
* 
* -------------------------------------------------------------------- 
* Revision History 
* 
* Date                Who                      Version                  Description 
* -------------------------------------------------------------------- 
*  
**********************************************************************/ 

#ifndef PL_RF_DRIVER_DUALSIM_API_H
#define PL_RF_DRIVER_DUALSIM_API_H



/*Add the H files that have defined the struct type or enum type used by the global parameters and functions below, or the definations and enum members used*/
#include "pl_w_globs.h"
#include "pl_w_api_traces.h"
#include "oss.h"
#include "pl_rf.h"
#include "drat_general_defs.h"


//The global parameters called are shown as follows: 


//The functions called are shown as follows: 
extern void	 plRFDSeqRxToIdle(void); // Defined: pl_rf_plp_dig.c	Line4429
extern UINT16	 plRFDGetDlUarfcn(void); // Defined: pl_rf_plp_dig.c	Line249
extern void	 plRFDSetDlUarfcn(UINT16 dlChannel); // Defined: pl_rf_plp_dig.c	Line231
extern RFDmode	 plRFDGetCurrentState( void ); // Defined: pl_rf_dig.c	Line4175


#endif 
