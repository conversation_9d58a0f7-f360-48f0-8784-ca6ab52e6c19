#ifndef _GSENSOR_H
#define _GSENSOR_H

#ifdef __cplusplus
extern "C" {
#endif

//#include "lv_drv_conf.h"

#include <stdbool.h>
#include "global_types.h"
//#include "lvgl/lv_hal/lv_hal_indev.h"

#include "xMotion.h"

typedef signed short                int16_t;
typedef signed short				int16;
typedef signed int					int32;
typedef unsigned int				uint32_t;
typedef unsigned char				uint8_t;
typedef unsigned short				uint16_t;
#define DA217_I2C_ADDR 0x26 /* 7-bit i2c addr */


typedef enum
{
	DA217_TYPE_UNKNOW,
	DA217_TYPE_6981,
	DA217_TYPE_7981,
	DA217_TYPE_6100,
	DA217_TYPE_MAX
}da217_chip_type;

enum
{
	DA217_AXIS_X     =     0,
	DA217_AXIS_Y     =     1,
	DA217_AXIS_Z     =     2,
	DA217_AXES_NUM   =     3
};

struct hwmsen_convert {
	short sign[4];
	short map[4];
};

struct da217_data
{
	uint8_t			chip_id;
	da217_chip_type	chip_type;	
	uint8_t			layout;
	uint16_t			lsb_1g;					
	uint32_t			step;
#if defined(DA217_USE_INT1) 
	uint8_t			int1_no;
	uint8_t			int1_level;
#endif
};


typedef struct
{
	int step;
#if FALL_ENABLE
    unsigned char fallFlag;
#endif
#if SLEEP_ENABLE
	Tsleep sleepFlag;
#endif
#if ACTION_ENABLE
	Taction actionFlag;  
#endif
#if SEDENTARY_ENABLE
	unsigned char sedentaryFlag;  
#endif
#if CALORIE_ENABLE
	int calorieValue;
#endif
#if FLIP_ENABLE
	unsigned char flipFlag;
#endif
#if RAISE_ENABLE
	Traise raiseFlag;
#endif
#if TURN_ENABLE
	Tturn turnFlag;
#endif
#if SHAKE_ENABLE
	Tshake shakeFlag;
#endif
}xMotion_Info;



#define HW_STEP_COUNT	0
#define SW_STEP_COUNT	1

/******************************************************
	Motion sensor controller register macro define
*******************************************************/
//#define DA217_HAND_LIGHT
#define DA217_STEPCOUNTER
//#define QMA7981_HAND_UP_DOWN
//#define QMA7981_ANY_MOTION
//#define QMA7981_SIGNIFICANT_MOTION
//#define QMA7981_NO_MOTION
//#define QMA7981_HAND_UP_DOWN
//#define QMA7981_INT_LATCH

#if defined(DA217_STEPCOUNTER)
//#define DA217_STEP_COUNTER_USE_INT		// for qma6981
#define DA217_CHECK_ABNORMAL_DATA
#endif

/***********QST TEAM***************************************/
#define GRAVITY_1G			9807

#define DA217_RANGE_2G        (1<<0)
#define DA217_RANGE_4G        (1<<1)
#define DA217_RANGE_8G        (1<<2)
#define DA217_RANGE_16G       (1<<3)
#define DA217_RANGE_32G       0x0f
	
#if defined(DA217_STEPCOUNTER)
#define DA217_OFFSET_X		0x60
#define DA217_OFFSET_Y		0x60
#define DA217_OFFSET_Z		0x60
#else
#define DA217_OFFSET_X		0x00
#define DA217_OFFSET_Y		0x00
#define DA217_OFFSET_Z		0x00
#endif
	

#define DA217_XOUTL			0x01	// 4-bit output value X
#define DA217_XOUTH			0x02	// 6-bit output value X
#define DA217_YOUTL			0x03	
#define DA217_YOUTH			0x04	
#define DA217_ZOUTL			0x05	
#define DA217_ZOUTH			0x06
#define DA217_STEP_CNT_L		0x07
	
#define DA217_RANGE			0x0f
#define DA217_ODR				0x10
#define DA217_MODE			0x11
	
#define DA217_INT_MAP0		0x19	// INT MAP
#define DA217_INT_STAT		0x0a    //interrupt statues
	
#define DA217_FIFO_WTMK		0x31	// FIFO water mark level
#define DA217_FIFO_CONFIG		0x3e	// fifo configure
#define DA217_FIFO_DATA		0x3f	//fifo data out 
	
#define SINGLE_TAP 1
#define DOUBLE_TAP 2

#define DA217_SLAVE_ADDR		0x26
#define DA217_CHIP_ID			0x01

/*******************************************************************************
Macro definitions - Register define for Gsensor asic
********************************************************************************/
#define NSA_REG_SPI_I2C                 0x00
#define NSA_REG_WHO_AM_I                0x01
#define NSA_REG_ACC_X_LSB               0x02
#define NSA_REG_ACC_X_MSB               0x03
#define NSA_REG_ACC_Y_LSB               0x04
#define NSA_REG_ACC_Y_MSB               0x05
#define NSA_REG_ACC_Z_LSB               0x06
#define NSA_REG_ACC_Z_MSB               0x07
#define NAS_REG_FIFO_STATUS				0x08
#define NSA_REG_MOTION_FLAG				0x09
#define NSA_REG_STEPS_MSB				0x0D
#define NSA_REG_STEPS_LSB				0x0E
#define NSA_REG_G_RANGE                 0x0f
#define NSA_REG_ODR_AXIS_DISABLE        0x10
#define NSA_REG_POWERMODE_BW            0x11
#define NSA_REG_SWAP_POLARITY           0x12
#define NSA_REG_FIFO_CTRL               0x14
#define NSA_REG_INTERRUPT_SETTINGS0     0x15
#define NSA_REG_INTERRUPT_SETTINGS1     0x16
#define NSA_REG_INTERRUPT_SETTINGS2     0x17
#define NSA_REG_INTERRUPT_MAPPING1      0x19
#define NSA_REG_INTERRUPT_MAPPING2      0x1a
#define NSA_REG_INTERRUPT_MAPPING3      0x1b
#define NSA_REG_INT_PIN_CONFIG          0x20
#define NSA_REG_INT_LATCH               0x21
#define NSA_REG_FREEFALL_DURATION       0x22
#define NSA_REG_FREEFALL_THRESHOLD      0x23
#define NSA_REG_FREEFALL_HYST           0x24
#define NSA_REG_ACTIVE_DURATION         0x27
#define NSA_REG_ACTIVE_THRESHOLD        0x28
#define NSA_REG_TAP_DURATION            0x2A
#define NSA_REG_TAP_THRESHOLD           0x2B
#define NSA_REG_RESET_STEP				0x2E
#define NSA_REG_STEP_CONGIF1			0x2F
#define NSA_REG_STEP_CONGIF2			0x30
#define NSA_REG_STEP_CONGIF3			0x31
#define NSA_REG_STEP_CONGIF4			0x32
#define NSA_REG_STEP_FILTER             0x33
#define NSA_REG_SM_THRESHOL				0x34
#define NSA_REG_CUSTOM_OFFSET_X         0x38
#define NSA_REG_CUSTOM_OFFSET_Y         0x39
#define NSA_REG_CUSTOM_OFFSET_Z         0x3a
#define NSA_REG_ENGINEERING_MODE        0x7f
#define NSA_REG_SENSITIVITY_TRIM_X      0x80
#define NSA_REG_SENSITIVITY_TRIM_Y      0x81
#define NSA_REG_SENSITIVITY_TRIM_Z      0x82
#define NSA_REG_COARSE_OFFSET_TRIM_X    0x83
#define NSA_REG_COARSE_OFFSET_TRIM_Y    0x84
#define NSA_REG_COARSE_OFFSET_TRIM_Z    0x85
#define NSA_REG_FINE_OFFSET_TRIM_X      0x86
#define NSA_REG_FINE_OFFSET_TRIM_Y      0x87
#define NSA_REG_FINE_OFFSET_TRIM_Z      0x88
#define NSA_REG_SENS_COMP               0x8c
#define NSA_REG_MEMS_OPTION             0x8f
#define NSA_REG_CHIP_INFO               0xc0




int da217_init(void);

bool gsensor_i2c_read_bytes(uint8_t reg_no, uint8_t* buffer_name, uint32_t length);

int gsensor_i2c_write_byte(uint8_t ucBufferIndex, uint8_t pucData);

uint32_t da217_pedometer_sensor_get_step(void);

int da217_check_abnormal_data(int data_in, int *data_out);

void da217_custom_reset_step(void);

int DA217_read_rawdata(int16_t *x, int16_t *y, int16_t *z);




#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* _GSENSOR_H */
