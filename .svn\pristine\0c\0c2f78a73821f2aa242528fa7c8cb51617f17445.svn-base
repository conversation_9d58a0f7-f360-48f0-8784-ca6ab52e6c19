/******************************************************************************
 * * desktop_global.h - data structure for desktop module
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#ifndef DESKTOP_GLOBAL_H
#define DESKTOP_GLOBAL_H

#ifdef __cplusplus
extern "C" {
#endif

/* include header file */
#include <stdio.h>
#include <string.h>
#include "ui_type.h"
#include "ui_log.h"
#include "ui_textid.h"
#include "modem/mmi_modemadp_interface.h"
#include "ui_nvm_interface.h"
#include "inter_common_ui_interface.h"
#include "inter_call_framework_interface.h"
#include "inter_calllog_framework_interface.h"
#include "inter_contacts_interface.h"
#include "inter_framework_alarm_interface.h"
#include "inter_framework_calculator_interface.h"
#include "inter_framework_calendar_interface.h"
#include "inter_framework_interface.h"
#include "inter_framework_sms_interface.h"
#include "inter_setting_interface.h"
#include "inter_sms_interface.h"
#include "inter_audio_player_interface.h"
#include "nav.h"
#include "../../lvgl/hal/hal.h"
#include "inter_interphone_interface.h"
#include "lv_theme_simple.h"
#include "../../lvgl/hal/hal_wlan.h"

/* define micro */
#define FRAMEWORK_TIMER_WELCOME_NOTE_LENGTH        2000
#define FRAMEWORK_TIMER_STATUS_INTERFACE_LENGTH    2000
#define FRAMEWORK_MAX_SYMBOL_NUM                   8
#define FRAMEWORK_SYMBOL_SIZE                      4

#define HAL_BAT_EMPTY 0
#define HAL_BAT_LOW 1
#define HAL_BAT_NORMAL 2
#define HAL_BAT_HIGH 3
#define HAL_BAT_FULL 4

#define FRAMEWORK_LOWER_LIMIT_FOR_BATTERY_4  75
#define FRAMEWORK_LOWER_LIMIT_FOR_BATTERY_3  50
#define FRAMEWORK_LOWER_LIMIT_FOR_BATTERY_2  25
#define FRAMEWORK_LOWER_LIMIT_FOR_BATTERY_1  0

#define FRAMEWORK_POWEROFF_BACKLIGHT_PERIOD  15 // in seconds

/* structure for global variate */
enum
{
    FRAMEWORK_TIME_HIDE = 0,                              // hide time on desktop
    FRAMEWORK_TIME_SHOW,                                  // show time on desktop
};
typedef UINT8 FRAMEWORK_TIME_DISPLAY;

enum
{
    FRAMEWORK_HOUR_FORMAT_12H = 0,                        // 12 hours format
    FRAMEWORK_HOUR_FORMAT_24H,                            // 24 hours format
};
typedef UINT8 FRAMEWORK_HOUR_FORMAT;

typedef struct
{
    FRAMEWORK_TIME_DISPLAY TimeDisplay;
    FRAMEWORK_HOUR_FORMAT  HourFormat;
} Framework_Time_Format_t;

typedef VOID (*Framework_Callback_t)(VOID);

typedef struct _Framework_App_t
{
    UINT16                  NameTextId;
    VOID                    *AppImage;
    Framework_Callback_t    AppFunc;   // function to enter APP
    UINT8                   GotoIndex; /* bit 0 - 5: APP shortcut order in Go to list, 0: invalid,
                                        * bit 6: 1 means to delete APP shortcut from Go to list
                                        * bit 7: 1 means to add APP shortcut into Go to list */
    struct _Framework_App_t *Next;
} Framework_App_t;

enum
{
    FRAMEWORK_SIM_STATUS_UNKNOWN = 0,
    FRAMEWORK_SIM_STATUS_PRESENT,
    FRAMEWORK_SIM_STATUS_PRESENT_PIN,
    FRAMEWORK_SIM_STATUS_ABSENT
};
typedef UINT8 FRAMEWORK_SIM_STATUS;

enum
{
    FRAMEWORK_REBOOT_NULL = 0,
    FRAMEWORK_REBOOT_ONGOING,
    FRAMEWORK_REBOOT_WAIT_SIM_STATUS,
};
typedef UINT8 FRAMEWORK_REBOOT_PROC;

enum
{
    FRAMEWORK_STA_BAR_UPDATE_SIGNAL = 0,
    FRAMEWORK_STA_BAR_UPDATE_OTHERS,
    FRAMEWORK_STA_BAR_UPDATE_ALL,
};
typedef UINT8 FRAMEWORK_STA_BAR_UPDATE;

typedef struct
{
    IF_FRAMEWORK_INIT_TYPE  PowerOnType;
    lv_obj_t                *ImgSimSta[2]; // SIM1 & SIM2
    lv_obj_t                *ContStatus;
    lv_obj_t                *LabelBattery;
    MMI_MODEM_SIGNAL_BAR    SignalBar[2]; // SIM1 & SIM2
    BOOL                    CallingFlg;    // TRUE: In a call
    BOOL                    LoudspeakerFlg;
    BOOL                    UnreadMissedCallFlg;
    UINT8                   UnreadMissedCallNum;  /* Display the number of unread missed calls before return to desktop
                                                   * 0: not display. Set 0 after the number displayed */
    BOOL                    UnreadSmsFlg;
    UINT8                   UnreadSmsNum;         /* Display the number of unread SMSs before return to desktop
                                                   * 0: not display. Set 0 after the number displayed */
    BOOL                    AlarmFlg;
    BOOL                    SilenceFlg;
    UINT8                   BatteryStatus;
    BOOL                    ChargeFlg;
    UINT8                   WifiLevel;      // 5 bars,[0,4]
    INT8                    *Operator[2];   // SIM1 & SIM2
    Framework_Time_Format_t TimeFormat;
    Framework_App_t         *AppList;
    UINT8                   GotoAppNum;
    BOOL                    AutoGuardOn;       // TRUE means auto guard on.
    UINT16                  AutoGuardDelay;    // delay in second for auto guard starting.
    BOOL                    KeyguardCodeOn;
    BOOL                    StandbyScreenMode; // TRUE: enter KEY_GUARD interface, FALSE: only enter DESKTOP_UNLOCK interface
    BOOL                    StartupToneOn;
    INT8                    *WelcomeNote;
    INT8                    *PhoneNumber;   // record phone number on Dial_Main interface
    FRAMEWORK_SIM_STATUS    SimStatus[2];   // SIM1 & SIM2
    BOOL                    SimStandBy[2];  // SIM1 & SIM2
    TIMER_ID                TimerId;
    BOOL                    TimerRunFlg;
    UINT8                   CnfBitmap;      /* bit 0: setting init cnf
                                             * bit 1: power up cnf for sim1
                                             * bit 2: power up cnf for sim2
                                             * bit 3: power off cnf for sim1
                                             * Bit 4: power off cnf for sim2 */
    BOOL                    LcdStatus;
    UINT32                  BacklightTimer;
    UINT32                  KeyGuardTimer;
    BOOL                    InitComplete;
    BOOL                    KeyGuardLock;
    lv_task_t               *Task;
    FRAMEWORK_REBOOT_PROC   RebootProc;
    BOOL                    FlightModeOn;
    BOOL                    SleepLcdOnly;
    BOOL                    SuspendEnable;
    BOOL                    PhoneReady;
} Framework_Mng_t;

/* extern global variate */
extern Framework_Mng_t g_FrameworkMng;

/* extern function */
extern VOID Framework_Change_Time_And_Date_To_String(hal_rtc_t *TimeAndDate, INT8 *TimeStr,
    INT8 *DateStr);
extern VOID Framework_Interface_OnSaveState(VOID *Ctrl);
extern VOID Framework_Interface_OnDestroy(VOID *Ctrl);
extern VOID Framework_Create_Dial_Main(INT8 *Number);
extern VOID Framework_Press_Direction_Key_On_List(UI_KEY_VALUE Key_Val);
extern VOID Framework_Goto_OnCreate(VOID *Ctrl);
extern VOID Framework_Goto_OnRestoreState(VOID *Ctrl);
extern INT8 *Framework_Get_Battery_Symbol(UINT8 BatSta);
extern VOID Framework_Goto_Btn_Go_to_Settings_Cb(lv_obj_t *Btn, lv_event_t Event);
extern VOID Framework_Goto_Btn_App_Cb(lv_obj_t *Btn, lv_event_t Event);
extern VOID Framework_Play_Charging_Anim(lv_obj_t *Label_Battery);
extern VOID Framework_Charging_Anim(VOID *Label, lv_anim_value_t Value);
extern VOID Framework_Output_Operator_On_Desktop(MMI_MODEM_SIM_ID SimId, lv_obj_t *Label);
extern UINT8 Framework_Get_Battery_Level(UINT8 Bat);
extern UINT32 Framework_Time_Difference(hal_rtc_t *OldTime, hal_rtc_t *NewTime);
extern VOID Framework_Check_Keyguard_Timer(VOID);
extern VOID Framework_PowerOff_Update_Status(VOID);
extern VOID Framework_Update_Charger_Status(VOID *Para);
extern VOID Framework_Power_On_Tone_End_Cb(VOID * Para);
extern VOID Framework_Reboot(VOID);
extern VOID Framework_Init_Config(BOOL Reboot);
extern VOID Framework_Switch_Off_Handle(VOID);
extern void lowbattery_handle_destroy(void);

extern VOID Framework_Create_Desktop_Unread_Sms(VOID);
extern VOID Framework_Status_Bar_Sort(FRAMEWORK_STA_BAR_UPDATE Update);
extern VOID Framework_Create_Charge_Animation(VOID);
extern VOID Framework_Create_Key_Guard(VOID);
extern VOID Framework_Create_Goto(VOID);
extern VOID Framework_Create_Switch_Off(VOID);
extern VOID Framework_Create_Desktop_Unlock(VOID);
extern VOID Framework_Keyguard_Or_Unlock(VOID);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* DESKTOP_GLOBAL_H */
