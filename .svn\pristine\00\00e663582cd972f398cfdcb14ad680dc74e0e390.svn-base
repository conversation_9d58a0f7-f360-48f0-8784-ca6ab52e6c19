#ifndef SPORT_RECORD_H
#define SPORT_RECORD_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
*      INCLUDES
*********************/
#include <stdio.h>

#ifdef LV_CONF_INCLUDE_SIMPLE
    #include "lvgl.h"
    #include "lv_watch_conf.h"
#else
    #include "../../../lvgl/lvgl.h"
    #include "../../../lv_watch_conf.h"
#endif

#include "lv_watch.h"
#include "ws_cJSON.h"
#if USE_LV_WATCH_SPORTS_RECORD != 0

typedef enum{
	JUMPTYPE = 0,	 
	RUNTYPE  = 1, 
}TYPE_SPORT;

#define  SPORT_MAX_RECORD_COUNT    20

typedef struct
{
    hal_rtc_t date;
	uint32_t time;
	uint32_t timestamp;
	float Kilometer;
}running_sport_recording;

typedef struct
{
    hal_rtc_t date;
    uint32_t count;
	uint32_t time;
	uint32_t timestamp;
}smart_jumping_recording;

#define Day_timestamp			      86400
#define	count_day 				   7
#define TopBarOptionNum		   	   2

void  sport_record_create_event_cb(lv_obj_t * btn, lv_event_t e);


#endif



#ifdef __cplusplus
} /* extern "C" */
#endif

#endif

