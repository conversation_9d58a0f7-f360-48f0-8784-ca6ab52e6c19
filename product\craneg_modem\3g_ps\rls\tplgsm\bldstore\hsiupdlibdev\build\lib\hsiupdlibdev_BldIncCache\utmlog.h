/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utmlog.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2006/03/07 12:23:53 $
 **************************************************************************
 * File Description:
 *  
 * This file defines the external interface API of the MiniLogger.
 *
 **************************************************************************/

#include <system.h>

#ifndef UTMLOG_H
#define UTMLOG_H

#if defined(UPGRADE_SYS_TOOLS)
#if defined(UT_ML_USE_MINILOGGER)
#define M_KiDbgTaskInitStart()           KiDbgTaskInitStart()
#define M_KiDbgTaskInitEnd()             KiDbgTaskInitEnd()
#define M_KiDbgTaskReceivedSignal(sigId) KiDbgTaskReceivedSignal((Int32) sigId)

extern void KiDbgTaskInitStart(void);
extern void KiDbgTaskInitEnd(void);
extern void KiDbgTaskReceivedSignal(Int32 sigId);
#endif
#endif

#endif /* UTMLOG_H */
