/******************************************************************************
*(C) Copyright 2018 ASR Microelectronics
* All Rights Reserved
******************************************************************************/
/* -------------------------------------------------------------------------------------------------------------------
 *
 *  Filename: mbim_ussd.h
 *
 *  Authors: <AUTHORS>
 *
 *  Description: all USSD mbim related functions should be added here
 *
 *  HISTORY:
 *   Apr 3, 2014 - Initial Version
 *   Mar 28,2018 - Modify for 1802 RTOS      
 *
 *  Notes:
 *
 ******************************************************************************/
#ifndef MBIM_USSD_H
#define MBIM_USSD_H

/******************************************************************************
 *   Include files
 ******************************************************************************/
#include <stdlib.h>

#include "mbim_types.h"
#include "mbim_protocol.h"
#include "mbim_ussd.h"

/******************************************************************************
 *   Macros
 ******************************************************************************/


/******************************************************************************
 *   Defines
 ******************************************************************************/
#define UUID_USSD_CID_MAX 			2
#define USSD_CID_PARAMS 			UINT32 cid, UINT32 transactionId, UINT32 commandType, UINT32 infoBufLen, char *infoBuf_p
#define USSD_CID_PARAMS_USAGE 		cid, transactionId, commandType, infoBufLen, infoBuf_p

typedef int (*ussdUuidProcessors)(USSD_CID_PARAMS);

/******************************************************************************
 *   External variables
 ******************************************************************************/


/******************************************************************************
 *	MBIM_CID_USSD (Chapter 10.5.21)
 ******************************************************************************/

typedef struct _MBIM_SET_USSD
{
	UINT32 		ussdAction;
	UINT32		ussdDataCodingScheme;
	UINT32		ussdPayloadOffset;
	UINT32		ussdPayloadLength;
	char*		dataBuffer[1];
} MBIM_SET_USSD, *P_MBIM_SET_USSD;

typedef struct _MBIM_USSD_INFO
{
	UINT32		ussdResponse;
	UINT32		ussdSessionState;
	UINT32		ussdDataCodingScheme;
	UINT32		ussdPayloadOffset;
	UINT32		ussdPayloadLength;
	char 		*dataBufer;
} MBIM_USSD_INFO, *P_MBIM_USSD_INFO;



typedef enum _MBIM_USSD_ACTION_ENUM
{
	MBIMUSSDInitiate = 0,
	MBIMUSSDContinue,
	MBIMUSSDCancel
}MBIM_USSD_ACTION_ENUM;


typedef enum _MBIM_USSD_RESPONSE_ENUM
{
	MBIMUSSDNoActionRequired = 0,
	MBIMUSSDActionRequired,
	MBIMUSSDTerminatedByNW,
	MBIMUSSDOtherLocalClient,
	MBIMUSSDOperationNotSupported,
	MBIMUSSDNetworkTimeOut
}MBIM_USSD_RESPONSE_ENUM;


typedef enum _MBIM_USSD_SESSION_STATE_ENUM
{
	MBIMUSSDNewSession = 0,
	MBIMUSSDExistingSession
}MBIM_USSD_SESSION_STATE_ENUM;


 #endif
