/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/****************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ****************************************************************************
 *   $Id: //central/main/wsd/modem/pscommon/3g_ut.mod/api/inc/utftftaevents.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 ****************************************************************************
 * File Description:
 *
 *    Contains declarations for the Timing Analysis (TA) Module
 ****************************************************************************/

#if !defined (UTFTFTAEVENTS_H)
#define       UTFTFTAEVENTS_H

/****************************************************************************
 * Profile Definitions
 *
 * Define the pofile C name with a unique bit position. Add a comment after
 * the definition, this comment is used by the pass thru task to describe
 * the profile to the end user.
 ****************************************************************************/
typedef enum
{
    FTFTA_PROFILE_NONE                  = 0x00000000, /* Do not remove */
    FTFTA_PROFILE_DL_MAC_DATA           = 0x00000001,
    FTFTA_PROFILE_DL_RLC_DATA           = 0x00000002,
    FTFTA_PROFILE_DL_L1_DATA            = 0x00000004,
    FTFTA_PROFILE_UL_MAC_DATA           = 0x00000008,
    FTFTA_PROFILE_UL_RLC_DATA           = 0x00000010,
    FTFTA_PROFILE_UL_L1_DATA            = 0x00000020,
    FTFTA_PROFILE_RLC_LP                = 0x00000040,
    FTFTA_PROFILE_UL_RLC_DETAIL         = 0x00000080,
    FTFTA_PROFILE_DL_RLC_DETAIL         = 0x00000100,
    FTFTA_PROFILE_UL_MAC_DETAIL         = 0x00000200,
    FTFTA_PROFILE_DL_MAC_DETAIL         = 0x00000400,
    FTFTA_PROFILE_MIPSRAMLOG            = 0x00000800,
    FTFTA_PROFILE_CIPHERING             = 0x00001000,
    FTFTA_PROFILE_POINT_TO_POINT_TEST   = 0x00002000
}utFtfTaProfile;

/****************************************************************************
 * Event Enumeration
 ****************************************************************************/
typedef enum
{
    FTFTA_NULL, /* Do not remove */

    /* FTFTA_PROFILE_POINT_TO_POINT_TEST - Point A to Point B Analysis */
    FTFTA_TEST_POINT_A,
    FTFTA_TEST_POINT_B,

    /* All profiles - Assert indication */
    FTFTA_ASSERT,

    /* FTFTA_PROFILE_DL_MAC_DATA - Thread Analysis */
    FTFTA_DL_MAC_DATA_RX_PHY_DATA_IND,
    FTFTA_DL_MAC_DATA_TX_MAC_DATA_IND,

    /* FTFTA_PROFILE_DL_RLC_DATA - Thread Analysis */
    FTFTA_DL_RLC_DATA_RX_MAC_PDU_LIST_INFO_IND,
    FTFTA_DL_RLC_DATA_TX_MAC_PDU_LIST_INFO_RSP,
    FTFTA_DL_RLC_DATA_RX_MAC_DATA_IND,
    FTFTA_DL_RLC_DATA_STOP_MAC_DATA_IND,
    FTFTA_DL_RLC_RX_CONTOL_PDU,
    FTFTA_DL_RLC_RX_DATA_PDU,
    FTFTA_DL_RLC_CLOSE_LOOP,
    FTFTA_DL_RLC_OPEN_LOOP,
    FTFTA_DL_SDU,

    /* FTFTA_PROFILE_DL_L1_DATA - Thread Analysis */
    FTFTA_START_MAC_RX_SIG_PHY_DATA_IND,
    FTFTA_END_MAC_RX_SIG_PHY_DATA_IND,

    /* FTFTA_PROFILE_UL_MAC_DATA - Thread Analysis */
    FTFTA_UL_MAC_DATA_RX_PHY_FRAME_IND,
    FTFTA_UL_MAC_DATA_TX_PHY_DATA_REQ,
    FTFTA_UL_MAC_DATA_TFC_STATE_CHANGE,

    /* FTFTA_PROFILE_UL_RLC_DATA - Thread Analysis */
    FTFTA_UL_RLC_DATA_RX_MAC_UPDATE_TRAFFIC_IND,
    FTFTA_UL_RLC_DATA_TX_MAC_TRAFFIC_REQ,
    FTFTA_UL_RLC_DATA_TX_MAC_NO_TRAFFIC_REQ,
    FTFTA_UL_RLC_DATA_RX_MAC_TRAFFIC_IND,
    FTFTA_UL_RLC_DATA_TX_MAC_DATA_REQ,
    FTFTA_UL_RLC_DATA_TX_RB_DATA_TO_SEND,
    FTFTA_UL_RLC_TX_CONTOL_PDU,
    FTFTA_UL_RLC_TX_DATA_PDU,
    FTFTA_UL_SDU,

    /* FTFTA_PROFILE_UL_L1_DATA - Thread Analysis */
    FTFTA_START_MAC_TX_SIG_PHY_FRAME_IND,
    FTFTA_END_MAC_RX_SIG_PHY_DATA_REQ,

    /* FTFTA_PROFILE_RLC_LOW_PRIORITY - Thread Analysis */
    /* "LP" is low priority */
    FTFTA_RLC_LP_START_LOW_PRIORITY_PROC,
    FTFTA_RLC_LP_STOP_LOW_PRIORITY_PROC,
    FTFTA_RLC_LP_START_UL_LIST_DEL_PROC,
    FTFTA_RLC_LP_STOP_UL_LIST_DEL_PROC,
    FTFTA_RLC_LP_START_UL_SEGMENT_SDU_PROC,
    FTFTA_RLC_LP_STOP_UL_SEGMENT_SDU_PROC,
    FTFTA_RLC_LP_START_DL_REASSEMBLE_PROC,
    FTFTA_RLC_LP_START_DL_REASSEMBLE_SDU,
    FTFTA_RLC_LP_STOP_DL_REASSEMBLE_PROC,
    FTFTA_RLC_LP_START_DL_DELETE_PROC,
    FTFTA_RLC_LP_STOP_DL_DELETE_PROC,
    FTFTA_RLC_LP_START_DL_NACK_PROC,
    FTFTA_RLC_LP_STOP_DL_NACK_PROC,

    /* FTFTA_PROFILE_UL_RLC_DETAIL - Thread Analysis */
    /* FTFTA_PROFILE_DL_RLC_DETAIL - Thread Analysis */
    /* FTFTA_PROFILE_UL_MAC_DETAIL - Thread Analysis */
    /* FTFTA_PROFILE_DL_MAC_DETAIL - Thread Analysis */

    /* Operating system profiling */
    FTFTA_OS_PROFILE_CONTEXT_CHANGE,

    /* FTFTA_PROFILE_CIPHERING - Ciphering */
    FTFTA_F8_CHAIN_START,
    FTFTA_F8_CHAIN_STOP

} utFtfTaEvent;

#endif /* UTFTFTAEVENTS_H */

/* END OF FILE */
