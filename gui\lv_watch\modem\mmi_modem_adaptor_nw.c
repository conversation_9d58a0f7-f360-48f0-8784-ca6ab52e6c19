/******************************************************************************
 * * mmi_modem_adaptor_nw.c - implementation of mmi-modem adaptor, network sub-module
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#include "lv_watch.h"

#if USE_LV_WATCH_MODEM_ADAPTOR
#include "mmi_modem_adaptor_nw.h"
#include "mmi_modem_adaptor_sim.h"
#include "mmi_modem_adaptor_sms.h"

Modem_NW_Mng_t   g_ModemNwMng[2];
static Modem_NW_Wifi_t g_Wifi;

/******************************************************************************
 *
 *         Functions
 *
*******************************************************************************/
static void MMI_Modem_Wifi_Timeout_Cb(uint32_t param);


/******************************************************************************
 *
 *         MMI -> Modem Adaptor
 *
*******************************************************************************/

/**
 * power up SIMx request, used only when enable SIM.
 * modem should unset SIM disable status for next power up.
 * param (in) simId: MMI_MODEM_SIM_ID
 * return  void
 */
VOID MMI_Modem_Power_Up_Req(MMI_MODEM_SIM_ID SimId)
{
    MMI_Modemadp_Power_Up_Req_t   *   pMsg;

    printf("##interface## %s: %d\n", __FUNCTION__, SimId);

    pMsg = (MMI_Modemadp_Power_Up_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Power_Up_Req_t));

    pMsg->SimId = SimId;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_POWER_UP_REQ, (UI_Inter_Msg_t *)pMsg);
}


/**
 * power off SIMx request, used when power off or SIM disable
 * modem should set the SIM disable status for next power up.
 * param (in) simId: MMI_MODEM_SIM_ID
 * param (in) PowerOffType: MMI_MODEM_POWER_OFF_TYPE
 * return  void
 */
VOID MMI_Modem_Power_Off_Req(MMI_MODEM_SIM_ID SimId, MMI_MODEM_POWER_OFF_TYPE PowerOffType)
{
    MMI_Modemadp_Power_Off_Req_t  *  pMsg;

    printf("##interface## %s: %d\n", __FUNCTION__, SimId);

    pMsg = (MMI_Modemadp_Power_Off_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Power_Off_Req_t));

    pMsg->SimId = SimId;
    pMsg->PowerOffType = PowerOffType;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_POWER_OFF_REQ, (UI_Inter_Msg_t *)pMsg);
}

/**
 * Request modem adpter to search plmn
 * param (in) PlmnSearchReq: MMI_Modem_Plmn_Search_Req_t
 * return void
 */
VOID MMI_Modem_Plmn_Search_Req(MMI_Modem_Plmn_Search_Req_t * PlmnSearchReq)
{
    printf("##interface## %s\n", __FUNCTION__);

    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_PLMN_SEARCH_REQ, (UI_Inter_Msg_t *)PlmnSearchReq);
}


/**
 * plmn search stop request
 * param void:
 * return void:
 */
VOID MMI_Modem_Plmn_Search_Stop_Req(VOID)
{
    MMI_Modemadp_Plmn_Search_Stop_Req_t   *   pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (MMI_Modemadp_Plmn_Search_Stop_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Plmn_Search_Stop_Req_t));

    pMsg->SimId = MMI_ModemAdp_Get_Sim_in_Plmn_Searching();
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_PLMN_SEARCH_STOP_REQ, (UI_Inter_Msg_t *)pMsg);
}

/**
 * get Operator(short name)
 * param (in) SimId: MMI_MODEM_SIM_ID
         (out) Operator: INT8 *
 * return  void
 */
VOID MMI_Modem_Get_Operator_Req(MMI_MODEM_SIM_ID SimId, INT8 * Operator, MMI_MODEM_PLMN_RAT * Rat)
{
    if(NULL != g_ModemNwMng[SimId].PlmnInfo.PlmnName) {
        strncpy(Operator, g_ModemNwMng[SimId].PlmnInfo.PlmnName, MMI_MAX_PLMN_NAME_LEN + 1);
    } else {
        g_ModemNwMng[SimId].PlmnInfo.PlmnName = Hal_Mem_Alloc(10);
        memset(g_ModemNwMng[SimId].PlmnInfo.PlmnName, 0, 10);
        strncpy(g_ModemNwMng[SimId].PlmnInfo.PlmnName, "Unknown", 10);
        strncpy(Operator, g_ModemNwMng[SimId].PlmnInfo.PlmnName, 10);
    }
    *Rat = g_ModemNwMng[SimId].PlmnInfo.PlmnRat;
    printf("##interface## %s, RAT %d\n", __FUNCTION__, *Rat);
}

/*
 * Request current voice registration state
 * param (in) SimId: MMI_MODEM_SIM_ID
 * return  UINT8: MMI_MODEM_VOICE_REG_STATE
 */
UINT8 MMI_Modem_Voice_Registration_State_Req(MMI_MODEM_SIM_ID SimId)
{
    printf("##interface## %s\n", __FUNCTION__);

    if((MODEM_NW_CS_NOT_REG == g_ModemNwMng[SimId].CsRegState)
            || (MODEM_NW_CS_LIMITED == g_ModemNwMng[SimId].CsRegState)) {
        return MMI_MODEM_VOICE_EMERGENCY;
    } else if((MODEM_NW_CS_REG_HOME == g_ModemNwMng[SimId].CsRegState)
              || (MODEM_NW_CS_REG_ROAMING == g_ModemNwMng[SimId].CsRegState)) {
        return MMI_MODEM_VOICE_NORMAL;
    }
    if((MODEM_NW_CS_REG_HOME_SMS_ONLY == g_ModemNwMng[SimId].CsRegState)
            || (MODEM_NW_CS_REG_ROAMING_SMS_ONLY == g_ModemNwMng[SimId].CsRegState)) {
        return MMI_MODEM_VOICE_SMS_ONLY;
    } else {
        return MMI_MODEM_VOICE_EMERGENCY;
    }
}

/*
 * Request IMS register state
 * param (in) SimId: MMI_MODEM_SIM_ID
 * return  UINT8
 */
UINT8 MMI_Modem_Get_Ims_Reg_State_Req(MMI_MODEM_SIM_ID SimId)
{
    return(g_ModemNwMng[SimId].ImsRegState);
}

/*
 * Request GSM register state
 * param (in) SimId: MMI_MODEM_SIM_ID
 * return  UINT8
 */
UINT8 MMI_Modem_Get_Gsm_Reg_State_Req(MMI_MODEM_SIM_ID SimId)
{
    return(g_ModemNwMng[SimId].GsmRegState);
}

/*
 * Request UMTS register state
 * param (in) SimId: MMI_MODEM_SIM_ID
 * return  UINT8
 */
UINT8 MMI_Modem_Get_Umts_Reg_State_Req(MMI_MODEM_SIM_ID SimId)
{
    return(g_ModemNwMng[SimId].UmtsRegState);
}

/**
 * Set GSM and LTE band information for engineer mode
 * param (in) pSetBandReq: MMI_Modem_Set_Band_Req_t
 * return  void
 */
VOID MMI_Modem_Set_Band_Req(MMI_Modem_Set_Band_Req_t * pSetBandReq)
{
    printf("##interface## %s\n", __FUNCTION__);

    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_SET_BAND_REQ, (UI_Inter_Msg_t *)pSetBandReq);
}

/**
 * Control WIFI search request
 * param (in) Option:
 *     0 - stop to wifi hotspot search,
 *     1 - start to wifi hotspot search
 * param (in) ScanRounds: range 1-3, rounds of wifi scan
 * param (in) MaxHotspotNumber: range 4-10, maximum number of wifi hotspots reported
 * param (in) TimeoutSeconds: range 1-25, timeout value to scan wifi in seconds
 * param (in) Priority: 0, data traffic has higher priority than wifi scan,
 *                         data traffic will interrupt the wifi scan;
 *                      1, data traffic has lower priority than wifi scan,
 *                         data traffic will be blocked during wifi scan
 * return  UINT8
 */
UINT8 MMI_Modem_Wifi_Ctrl_Req(UINT8 Option,
                              UINT8 ScanRounds,
                              UINT8 MaxHotspotNum,
                              UINT32 TimeoutSeconds,
                              UINT8 Priority)
{
    RIL_Errno RilResult;
    int Data[5] = {Option};
    printf("##interface## %s, Option=%d, Round=%d, MaxHotspotNum=%d, Sec=%d, Prio=%d\n",
           __FUNCTION__, Option, ScanRounds, MaxHotspotNum, TimeoutSeconds, Priority);

    if(2 == Option) {
        Data[1] = ScanRounds;
        Data[2] = MaxHotspotNum;
        Data[3] = TimeoutSeconds;
        Data[4] = Priority;
    }
    RilResult = Ril_MM_Request_Set_WifiCtrl(Data, sizeof(Data), MMI_ModemAdp_Get_Ril_SimId(modem_sim_id));
    if(RIL_E_SUCCESS != RilResult) {
        UI_Log_Out(UI_SUB_MODULE_MODEM_ADP, UI_LOG_LEVEL_ERROR,
                   "Ril set wifi ctrl failed: %d\n", RilResult);
    }

    if((1 == Option) || (2 == Option)) {
        g_Wifi.WifiStart = TRUE;

        //start timer
        if((NULL == g_Wifi.TimeoutId) && (0 != g_Wifi.TimeoutSeconds)) {
            uos_timer_create(&g_Wifi.TimeoutId);
            uos_timer_start(g_Wifi.TimeoutId, MS_TO_TICKS(g_Wifi.TimeoutSeconds * 1000), 0, MMI_Modem_Wifi_Timeout_Cb, 0);
        }
    } else {
        g_Wifi.WifiStart = FALSE;

        //stop timer
        if(NULL != g_Wifi.TimeoutId) {
            uos_timer_stop(g_Wifi.TimeoutId);
            uos_timer_delete(g_Wifi.TimeoutId);
            g_Wifi.TimeoutId = NULL;
        }
    }

    return RilResult;
}

/*
 * Request radio power status
 * param (in) SimId: MMI_MODEM_SIM_ID
 * param (in) Cb: app_adp_radio_power_cb *
 * return  VOID
 */
VOID MMI_Modem_Radio_Power_Status_Req(MMI_MODEM_SIM_ID simId, app_adp_radio_power_cb Cb)
{
    printf("##interface## %s: simId %d\n", __FUNCTION__, simId);

    g_ModemNwMng[simId].RadioPowerCb = Cb;
    Ril_MM_Request_Get_Radio_State(MMI_ModemAdp_Get_Ril_SimId(simId));
}

/**
 * get calibration information request in engineer mode
 * param (in) void:
 * return  void
*/
void MMI_ModemAdp_Get_Calibration_Info_Req(MMI_MODEM_SIM_ID SimId)
{
    MMI_Modemadp_Get_Cali_Info_Req_t   *   pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (MMI_Modemadp_Get_Cali_Info_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Get_Cali_Info_Req_t));

    pMsg->SimId = SimId;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_GET_CALI_INFO_REQ, (UI_Inter_Msg_t *)pMsg);
}

void MMI_ModemAdp_Get_onegoCali_Info_Req(MMI_MODEM_SIM_ID SimId,char* cmd,uint16 lens)
{
    MMI_Modemadp_Get_onegoCali_Info_Req_t   *   pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (MMI_Modemadp_Get_onegoCali_Info_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Get_onegoCali_Info_Req_t));

    pMsg->SimId = SimId;
    pMsg->cmd = cmd;
    pMsg->lens = lens;
	printf("%s:  len=%d\n", __FUNCTION__,pMsg->lens);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_GET_ONEGOCALI_INFO_REQ, (UI_Inter_Msg_t *)pMsg);
}

//onego-zly-20230816
void MMI_ModemAdp_Save_onegoCali_Info_Req(MMI_MODEM_SIM_ID SimId)
{
    MMI_Modemadp_Get_Sn_Info_Req_t   *   pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (MMI_Modemadp_Get_Sn_Info_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Get_Sn_Info_Req_t));

    pMsg->SimId = SimId;
	printf("%s\n", __FUNCTION__);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_SAVE_ONEGOCALI_INFO_REQ, (UI_Inter_Msg_t *)pMsg);
}

/**
 * get SN information request in engineer mode
 * param (in) void:
 * return  void
*/
void MMI_ModemAdp_Get_Sn_Info_Req(MMI_MODEM_SIM_ID SimId)
{
    MMI_Modemadp_Get_Sn_Info_Req_t   *   pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (MMI_Modemadp_Get_Sn_Info_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Get_Sn_Info_Req_t));

    pMsg->SimId = SimId;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_GET_SN_INFO_REQ, (UI_Inter_Msg_t *)pMsg);
}

/**
 * get CID information request 
 * param (in) MMI_MODEM_SIM_ID:
 * return  void
*/
void MMI_ModemAdp_Get_NETid_Info_Req(MMI_MODEM_SIM_ID SimId)
{
    MMI_Modemadp_Get_NETid_Info_Req_t   *   pMsg;

    ws_printf("##interface## %s\n", __FUNCTION__);
    pMsg = (MMI_Modemadp_Get_NETid_Info_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Get_NETid_Info_Req_t));

    pMsg->SimId = SimId;
	ws_printf("%s:MsgId=%d\n", __FUNCTION__,MMI_MODEMADP_GET_NETID_INFO_REQ);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_GET_NETID_INFO_REQ, (UI_Inter_Msg_t *)pMsg);
}

void Ril_DEV_Response_Get_NETID(RIL_SIM_ID SimId, char * psn, RIL_Errno result)
{
    RIL_Modemadp_Rsp_Get_NETid_t * pMsg = NULL;

    printf("##interface## %s SimId=%d,psn=[%s],result=%d \n", __FUNCTION__,SimId,psn,result);
   pMsg = (RIL_Modemadp_Rsp_Get_NETid_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Rsp_Get_NETid_t));
    Hal_Mem_Set(pMsg, 0, sizeof(RIL_Modemadp_Rsp_Get_NETid_t));

    pMsg->SimId = SimId;
    pMsg->result = result;
    pMsg->pCid = psn;
	printf("%s, %d, %d\n", __FUNCTION__, __LINE__,RIL_MODEMADP_MM_RSP_GET_NETID);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_GET_NETID, (UI_Inter_Msg_t *)pMsg);
}

#if USE_LV_WATCH_ALIPAY != 0
VOID MMI_Modem_Query_Sn_Req(MMI_MODEM_SIM_ID SimId, INT8 * Sn, INT8 len)
{
    INT8 tmp_sn[11] = "0000::0000";

    printf("##interface## %s\n", __FUNCTION__);

    if(NULL != g_ModemNwMng[SimId].Sn) {
        if (len < strlen(g_ModemNwMng[SimId].Sn)) {
            strncpy(Sn, g_ModemNwMng[SimId].Sn, len);
        } else {
            strncpy(Sn, g_ModemNwMng[SimId].Sn, strlen(g_ModemNwMng[SimId].Sn));
        }
    } else {
        Hal_Mem_Copy(Sn, tmp_sn, 11);
    }
}
#endif


void MMI_ModemAdp_Set_Connection_Switch(BOOL OnOff)
{
    printf("##interface## %s: %d\n", __FUNCTION__, OnOff);
    CM_SetConnectionSwitch(OnOff);
}

BOOL MMI_ModemAdp_Get_Connection_Switch(void)
{
    BOOL OnOff = CM_GetConnectionSwitch();
    printf("##interface## %s: %d\n", __FUNCTION__, OnOff);
    return OnOff;
}

void MMI_ModemAdp_Set_Data_Roaming(BOOL OnOff)
{
    printf("##interface## %s: %d\n", __FUNCTION__, OnOff);

    BOOL Conn = CM_GetConnectionSwitch();
    if(Conn) {
        CM_SetConnectionSwitch(0);
        CM_SetDataOnRoamingPersist(OnOff);
        CM_SetConnectionSwitch(1);
    } else {
        CM_SetDataOnRoamingPersist(OnOff);
    }
}

BOOL MMI_ModemAdp_Get_Data_Roaming(void)
{
    BOOL OnOff = CM_GetDataOnRoaming();
    printf("##interface## %s: %d\n", __FUNCTION__, OnOff);
    return OnOff;
}

int MMI_ModemAdp_Get_Default_Apn_Name(char *Name, int NameLen)
{
    int Ret = CM_GetDefaultApnName(Name, NameLen);
    printf("##interface## %s: Name=%s\n", __FUNCTION__, Name);
    return Ret;
}

int MMI_ModemAdp_Get_Default_Ip_Type(void)
{
    CMSimID CmSimId = (MMI_MODEM_SIM_1 == modem_sim_id) ? CM_SIM_0 : CM_SIM_1;
    int IpType = get_auto_apn_iptype(CmSimId);
    printf("##interface## %s: %d\n", __FUNCTION__, IpType);
    return IpType;
}

void MMI_ModemAdp_Set_Apn_Info(MMI_Modem_Apn_Info_t *ApnInfo)
{
    printf("##interface## %s: [0] %d, %s, %d; [1] %d, %s, %d\n",
        __FUNCTION__,
        ApnInfo[0].apnflg, ApnInfo[0].apn, ApnInfo[0].iptype,
        ApnInfo[1].apnflg, ApnInfo[1].apn, ApnInfo[1].iptype);

    memcpy(&g_ModemNwMng[0].ApnInfo, &ApnInfo[0], sizeof(MMI_Modem_Apn_Info_t));
    memcpy(&g_ModemNwMng[1].ApnInfo, &ApnInfo[1], sizeof(MMI_Modem_Apn_Info_t));
}

void MMI_ModemAdp_Set_Apn_Mode(MMI_MODEM_SIM_ID SimId)
{
    if(0 == g_ModemNwMng[SimId].ApnInfo.apnflg) {
        setAutoApn(APN_MODE_AUTO, FALSE);
    } else {
        setAutoApn(APN_MODE_MANUAL, FALSE);
    }
}

void MMI_ModemAdp_Reset_Apn(void)
{
    printf("##interface## %s\n", __FUNCTION__);

    //set auto apn mode
    setAutoApn(APN_MODE_AUTO, FALSE);

    //deactive data all
    CM_DeactiveDataCallAll();

    //setup default connection
    CM_SetupDefaultConnection();
}

void MMI_ModemAdp_Config_Apn(char *Apn, UINT8 IpType)
{
    printf("##interface## %s: %s, %d\n", __FUNCTION__, Apn, IpType);

    //set auto apn mode
    setAutoApn(APN_MODE_MANUAL, FALSE);

    //deactive data all
    CM_DeactiveDataCallAll();

    //config apn info
    //isDefault: 1: will modify LTE attach APN when call CM_SetupDataCall to active the PDP context,
    //the cid value should only set to 1, specify other value will return error
    UINT8 cid = 1;
    ApnInfo_s info;
    memset(&info, 0, sizeof(ApnInfo_s));
    strncpy(info.apn, Apn, MAX_APN_INFO_LEN);
    info.cid = cid;
    info.ipType = IpType;
    info.isDefault = 1;
    CM_ConfigApnInfo(&info);

    //setup data call
    CM_SetupDataCall(cid);
}

/******************************************************************************
 *
 *         RIL -> Modem Adaptor
 *
*******************************************************************************/
/**
 * Ril indicate that it is ready.
 * param (in) void
 * return  BOOL
 */
VOID Ril_Ril_Ready_ind(VOID)
{
    RIL_Modemadp_Ril_Ready_Ind_t   *  pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (RIL_Modemadp_Ril_Ready_Ind_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Ril_Ready_Ind_t));

    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_RIL_READY_IND, (UI_Inter_Msg_t *)pMsg);
}

/*
 * RIL_RESPONSE_SET_NETWORK_SELECTION_MANUAL
 * Response of selecting a specified network Manually
 * param   RIL_SIM_ID simId: SIM ID
 *         RIL_Errno result: RIL_E_SUCCESS, RIL_E_INTERNAL_ERR
 * return void
 */
VOID Ril_MM_Response_Set_Network_Selection_Manual(RIL_Errno Result, RIL_SIM_ID SimId)
{
    RIL_Modemadp_Set_Nw_Sel_Manual_Cnf_t   *  pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (RIL_Modemadp_Set_Nw_Sel_Manual_Cnf_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Set_Nw_Sel_Manual_Cnf_t));

    pMsg->SimId = SimId;
    pMsg->Result = Result;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_SET_NW_SEL_MANUAL, (UI_Inter_Msg_t *)pMsg);
}

/*
 * RIL_RESPONSE_QUERY_AVAILABLE_NETWORKS
 * Response of Scaning for available networks
 * param   RIL_OperInfo *pOperInfo: returned to upper layer, list of available network
 *         RIL_SIM_ID simId: SIM ID
 *         RIL_Errno result: RIL_E_SUCCESS, RIL_E_INTERNAL_ERR
 * return  void
 */
VOID Ril_MM_Response_Query_Available_Networks(RIL_OperInfo * pOperInfo, RIL_SIM_ID SimId, RIL_Errno result)
{
    RIL_Modemadp_Query_Available_Nw_Cnf_t   *  pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (RIL_Modemadp_Query_Available_Nw_Cnf_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Query_Available_Nw_Cnf_t));

    pMsg->SimId = SimId;
    pMsg->result = result;
    pMsg->OperInfo.operInfoNum = 0;
    pMsg->OperInfo.pOperInfoSub = NULL;
    if(NULL != pOperInfo) {
        pMsg->OperInfo.operInfoNum = pOperInfo->operInfoNum;
        if(0 != pOperInfo->operInfoNum) {
            pMsg->OperInfo.pOperInfoSub = pOperInfo->pOperInfoSub;
        }
        Hal_Mem_Free(pOperInfo);
    }
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_QUERY_AVAILABLE_NETWORKS, (UI_Inter_Msg_t *)pMsg);
}

/*
 * RIL_UNSOL_SIGNAL_STRENGTH(+CSQ, +CESQ)
 * Report current signal strength
 * param   RIL_SignalStrength *pSignalStrength: report to upper layer, signal strength
 *         RIL_SIM_ID SimId: SIM ID
 * return  void
 */
VOID Ril_MM_Response_Signal_Strength(RIL_SignalStrength * pSignalStrength, RIL_SIM_ID SimId)
{
    RIL_Modemadp_Sig_Strength_t   *  pMsg;
    if(gcf_test) {
        Hal_Mem_Free(pSignalStrength);
        return;
    }

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (RIL_Modemadp_Sig_Strength_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Sig_Strength_t));

    pMsg->SimId = SimId;
    Hal_Mem_Copy(&pMsg->LTE_SignalStrength, &pSignalStrength->LTE_SignalStrength, sizeof(RIL_LTE_SignalStrength_v8));
    Hal_Mem_Copy(&pMsg->GW_SignalStrength, &pSignalStrength->GW_SignalStrength, sizeof(RIL_GW_SignalStrength));

    printf("%s LTE_SignalStrength %d\n", __FUNCTION__, pMsg->LTE_SignalStrength.rsrp / 2);
    printf("%s GW_SignalStrength %d\n", __FUNCTION__, pMsg->GW_SignalStrength.signalStrength);

    Hal_Mem_Free(pSignalStrength);

    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_SIG_STRENGTH, (UI_Inter_Msg_t *)pMsg);
}

/*
 * RIL_UNSOL_VOICE_RADIO_TECH_CHANGED($CREG, +CGREG, +CEREG)
 * Report current network's radio technology
 * param   RIL_RadioTechnology *pRadioTech: report to upper layer, current radio technology
 *         RIL_SIM_ID SimId: SIM ID
 * return  void
 */
void Ril_MM_Response_Radio_Tech_Changed(RIL_MM_Reg_info * pRegInfo, RIL_SIM_ID SimId)
{
    RIL_Modemadp_Rat_Changed_t   *  pMsg;

    printf("##interface## %s RAT %d,stat:%d\n", __FUNCTION__, pRegInfo->rt, pRegInfo->stat);

    pMsg = (RIL_Modemadp_Rat_Changed_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Rat_Changed_t));

    pMsg->SimId = SimId;
    pMsg->RegInfo.rt = pRegInfo->rt;
    pMsg->RegInfo.stat = pRegInfo->stat;
    pMsg->RegInfo.lac = pRegInfo->lac;
    pMsg->RegInfo.cid = pRegInfo->cid;

    Hal_Mem_Free(pRegInfo);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_RAT_CHANGED, (UI_Inter_Msg_t *)pMsg);
}

/*
 * RIL_UNSOL_NITZ_TIME_RECEIVED(+NITZ)
 * Report current network time
 * param   RIL_NitzTimeInfo *pNitzTimeInfo: report to upper layer, current network time
 *         RIL_SIM_ID simId: SIM ID
 * return  void
 */
VOID Ril_MM_Response_Nitz_Time_Received(RIL_NitzTimeInfo * pNitzTimeInfo, RIL_SIM_ID SimId)
{
	printf("##interface## %s\n", __FUNCTION__);

#if USE_LV_WATCH_SETTING_DATETIME != 0
	static int set_time;
	if(set_time == 0)
	{
		set_time=1; 	   
		RIL_Modemadp_Nitz_Time_t   *  pMsg;
		pMsg = (RIL_Modemadp_Nitz_Time_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Nitz_Time_t));
		memset(pMsg, 0, sizeof(RIL_Modemadp_Nitz_Time_t));
		pMsg->SimId = SimId;
		memcpy(pMsg->NitzTimeData, pNitzTimeInfo->NitzTimeData, pNitzTimeInfo->NitzTimeDataSize);
		UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_NITZ_TIME_RECEIVED, (UI_Inter_Msg_t *)pMsg);
	}
#else 
	UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_NITZ_TIME_RECEIVED, NULL);
	return;
#endif
}


/*
 * RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED(*COPN, $CREG, +CGREG, +CEREG)
 * Inform network state changed
 * param   RIL_SIM_ID simId: SIM ID
 * return  void
 */
VOID Ril_MM_Response_Network_State_Changed(RIL_SIM_ID SimId)
{
    RIL_Modemadp_Nw_State_Changed_t   *  pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (RIL_Modemadp_Nw_State_Changed_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Nw_State_Changed_t));

    pMsg->SimId = SimId;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_NW_STATE_CHANGED, (UI_Inter_Msg_t *)pMsg);
}

/*
 * RIL_MODEMADP_MM_RSP_OPERATOR
 * Response of request the operators
 * param
 *         RIL_OperInfo *pOperInfo
 *         RIL_SIM_ID SimId: SIM ID
 * return void
 */
void Ril_MM_Response_Operator(RIL_OperInfo * pOperInfo, RIL_SIM_ID SimId)
{
    RIL_Modemadp_Rsp_Operator_t   *  pMsg;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (RIL_Modemadp_Rsp_Operator_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Rsp_Operator_t));

    pMsg->SimId = SimId;
    pMsg->pOperInfo = (RIL_OperInfo *)Hal_Mem_Alloc(sizeof(RIL_OperInfo));
    memset(pMsg->pOperInfo, 0, sizeof(RIL_OperInfo));
    memcpy(pMsg->pOperInfo, pOperInfo, sizeof(RIL_OperInfo));
    pMsg->pOperInfo->pOperInfoSub = (RIL_OperInfoSub *)Hal_Mem_Alloc(sizeof(RIL_OperInfoSub));
    memset(pMsg->pOperInfo->pOperInfoSub, 0, sizeof(RIL_OperInfoSub));
    memcpy(pMsg->pOperInfo->pOperInfoSub, pOperInfo->pOperInfoSub, sizeof(RIL_OperInfoSub));
    Hal_Mem_Free(pOperInfo->pOperInfoSub);
    Hal_Mem_Free(pOperInfo);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_OPERATOR, (UI_Inter_Msg_t *)pMsg);
}

/*
 * +CIREGU: 0/1
 * Inform IMS register state indication
 * param   unsigned char : ImsRegState  0: not registered 1:registered
 * param   RIL_SIM_ID simId: SIM ID
 * return   void
 */
void Ril_MM_Response_IMS_Reg_State(unsigned char ImsRegState, RIL_SIM_ID simId)
{
    RIL_Modemadp_Ims_Reg_State_t * pMsg;

    printf("##interface## %s: simId %d, ImsRegState %d\n", __FUNCTION__, simId, ImsRegState);

    pMsg = (RIL_Modemadp_Ims_Reg_State_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Ims_Reg_State_t));

    pMsg->SimId = simId;
    pMsg->ImsRegState = ImsRegState;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_IMS_REG_STATE, (UI_Inter_Msg_t *)pMsg);
}

/*
 * RIL_REQUEST_GET_VOLTE(AT*IMSRCFG="switch")
 * Query cp volte status
 * param   RIL_SIM_ID simId: SIM ID
 * param   option: int
 * return  void
 */
void Ril_MM_Response_Get_Volte_State(RIL_SIM_ID simId, int option)
{
    g_ModemNwMng[simId].VolteState = option;
    printf("##interface## %s VolteState:%d\n", __FUNCTION__, g_ModemNwMng[simId].VolteState);
    if(0 == g_ModemNwMng[simId].VolteState)
    {
        if(0 == g_ModemNwMng[simId].Ccfc_Ccwa_ReadFlg)
        {
#if USE_LV_WATCH_CCWA != 0
            MMI_Modem_Call_Wait_Query_Req(MMI_ModemAdp_Get_UI_SimId(simId));
#endif
#if USE_LV_WATCH_CCFC != 0
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(simId), MMI_MODEM_CALL_FORWARD_NO_CONDITION);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(simId), MMI_MODEM_CALL_FORWARD_BUSY);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(simId), MMI_MODEM_CALL_FORWARD_NOT_REACH);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(simId), MMI_MODEM_CALL_FORWARD_NO_ANSWER);
#endif
        }
    }
}

void Ril_MM_Response_Get_Band(RIL_SIM_ID SimId, RIL_MM_BAND_Info * pGetBandReq, RIL_Errno result)
{
    RIL_Modemadp_Rsp_Get_Band_t   *  pMsg;

    printf("##interface## %s mode:%d,res:%d\n", __FUNCTION__, pGetBandReq->ucMode, result);
    pMsg = (RIL_Modemadp_Rsp_Get_Band_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Rsp_Get_Band_t));

    pMsg->SimId = SimId;
    pMsg->ucMode = pGetBandReq->ucMode;
    pMsg->ucGsmBand = pGetBandReq->ucGsmBand;
    pMsg->usLteBandH = pGetBandReq->usLteBandH;
    pMsg->ulLteBandL = pGetBandReq->ulLteBandL;
    Hal_Mem_Free(pGetBandReq);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_GET_BAND, (UI_Inter_Msg_t *)pMsg);
}

void Ril_MM_Response_Ims_Vops(int volte, RIL_SIM_ID simId)
{
    RIL_Modemadp_Ims_Vops_Ind_t   *  pMsg;
    if(MMI_MODEM_PLMN_RAT_UNKNOW == g_ModemNwMng[simId].PlmnInfo.PlmnRat) {
        printf("volte is discarded in Ril_MM_Response_Ims_Vops\n");
        return;
    }
    printf("##interface## %s volte:%d\n", __FUNCTION__, volte);
    pMsg = (RIL_Modemadp_Ims_Vops_Ind_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Ims_Vops_Ind_t));

    pMsg->SimId = simId;
    pMsg->volte = volte;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_IMS_VOPS, (UI_Inter_Msg_t *)pMsg);
}

void Ril_MM_Response_Wifi_CellInfo(RIL_Wifi_Cell_Info * pWifiCellInfo)
{
    app_adp_wifi_result_t result;
    UINT8 count = g_Wifi.WifiApList.count;
    UINT32 mac[APP_ADP_WIFI_MAC_LEN];

    if(FALSE == g_Wifi.WifiStart) {
        printf("%s: WifiStart==FALSE, ignore this msg\n", __FUNCTION__);
        if(NULL != pWifiCellInfo) {
            Hal_Mem_Free(pWifiCellInfo);
        }
        return;
    }

    if(NULL != pWifiCellInfo) {
        printf("%s, macAddr:%s, rssi:%d\n", __FUNCTION__, pWifiCellInfo->macAddr, pWifiCellInfo->rssi);

        //save wifi info
        g_Wifi.WifiApList.count++;
        g_Wifi.WifiApList.item[count].rssi = pWifiCellInfo->rssi;

        sscanf(pWifiCellInfo->macAddr, "%2x%2x%2x%2x%2x%2x",
               mac,
               mac + 1,
               mac + 2,
               mac + 3,
               mac + 4,
               mac + 5);
        printf("%s, count=%d, mac: %02x %02x %02x %02x %02x %02x\n", __FUNCTION__, g_Wifi.WifiApList.count, mac[0], mac[1], mac[2], mac[3], mac[4], mac[5]);

        for(UINT8 i = 0; i < APP_ADP_WIFI_MAC_LEN; i++) {
            g_Wifi.WifiApList.item[count].mac[i] = (UINT8)mac[i];
        }

        Hal_Mem_Free(pWifiCellInfo);
    } else {
        printf("%s, pWifiCellInfo==NULL, count=%d\n", __FUNCTION__, g_Wifi.WifiApList.count);
    }

    if((NULL == pWifiCellInfo) ||
            (APP_ADP_WIFI_AP_MAX_NUM == g_Wifi.WifiApList.count)) {
        //stop scan
        //MMI_Modem_Wifi_Ctrl_Req(0);

        //stop timer
        if(NULL != g_Wifi.TimeoutId) {
            uos_timer_stop(g_Wifi.TimeoutId);
            uos_timer_delete(g_Wifi.TimeoutId);
            g_Wifi.TimeoutId = NULL;
        }

        //result ind
        if(NULL != g_Wifi.WifiResultInd) {
            app_adp_wifi_ap_list * ap_list = (app_adp_wifi_ap_list *)Hal_Mem_Alloc(sizeof(app_adp_wifi_ap_list));
            Hal_Mem_Copy(ap_list, &g_Wifi.WifiApList, sizeof(app_adp_wifi_ap_list));

            if(0 == g_Wifi.WifiApList.count) {
                result = APP_ADP_WIFI_RESULT_FAILURE;
            } else {
                result = APP_ADP_WIFI_RESULT_SUCCESS;
            }

            printf("%s, WifiResultInd: result=%d, count=%d\n", __FUNCTION__, result, g_Wifi.WifiApList.count);
            g_Wifi.WifiResultInd(result, ap_list);
        }

        //cleanup
        Hal_Mem_Set(&g_Wifi, 0, sizeof(Modem_NW_Wifi_t));
        return;
    }
}

void Ril_MM_Response_Get_Radio_State(RIL_SIM_ID simId, int status)
{
    RIL_Modemadp_Radio_Power_Status_t * pMsg;

    printf("##interface## %s: simId %d, status %d\n", __FUNCTION__, simId, status);

    pMsg = (RIL_Modemadp_Radio_Power_Status_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Radio_Power_Status_t));

    pMsg->SimId = simId;
    pMsg->Status = (UINT8)status;
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_RADIO_POWER_STATUS, (UI_Inter_Msg_t *)pMsg);
}

/**
 * get cell information request
 * param (in) watch_app_adp_get_cell_info_mode_t mode:refer to
 * the define of "watch_app_adp_get_cell_info_mode_t"
 * return  void
*/
void app_adaptor_get_cell_info_req(watch_app_adp_get_cell_info_mode_t mode)
{
    MMI_MODEM_SIM_ID sim_id = modem_sim_id;
    app_adaptor_lte_cell_info_ind lte_cellinfoind = g_ModemNwMng[sim_id].GetCellInfo.lte_cellinfoind;

    if(false == MMI_Modem_Sim_Present_Check_Req(sim_id)) {
        printf("sim not present and get cell info fail in app_adaptor_get_cell_info_req\n");
        if(NULL != lte_cellinfoind) {
            lte_cellinfoind(NULL);
        } else {
            printf("lte_cellinfoind is null in app_adaptor_get_cell_info_req\n");
        }
        return;
    }
    switch(mode) {
        case ENTER_CELL_INFO_AUTO_REPORT_MODE_OPEN:
            printf("open auto report cell change in app_adaptor_get_cell_info_req\n");
            Ril_MM_Request_Set_CellChange(1, MMI_ModemAdp_Get_Ril_SimId(sim_id));//1 means open report
        case GET_CELL_INFO_ONCE_MODE:
            printf("get cell info in app adaptor get cell info req\n");
            g_ModemNwMng[sim_id].GetCellInfo.srctype = MODEM_NW_APP_USE;
            Ril_EEM_Request_EEM_Info(MMI_ModemAdp_Get_Ril_SimId(sim_id));
            break;

        case EXIT_CELL_INFO_AUTO_REPORT_MODE:
            printf("close auto report cell change in app_adaptor_get_cell_info_req\n");
            Ril_MM_Request_Set_CellChange(0, MMI_ModemAdp_Get_Ril_SimId(sim_id));//0 means close report
            break;

        default:
            break;
    }
}

/**
 * get cell information request in engineer mode
 * param (in) void:
 * return  void
*/
void MMI_ModemAdp_Get_Cell_Info_Req(void)
{
    MMI_MODEM_SIM_ID sim_id = modem_sim_id;

    if(false == MMI_Modem_Sim_Present_Check_Req(sim_id)) {
        printf("sim not present and get cell info fail in MMI_ModemAdp_Get_Cell_Info_Req\n");
        return;
    }
    printf("get cell info req in engineer mode\n");
    g_ModemNwMng[sim_id].GetCellInfo.srctype = MODEM_NW_ENGINEER_MODE_USE;
    Ril_EEM_Request_EEM_Info(MMI_ModemAdp_Get_Ril_SimId(sim_id));
}

/**
 * bind lte cell information indication call back
 * param (in) func app_adaptor_shutdown
 * return  void
 */
void app_adaptor_lte_cell_info_ind_bind(app_adaptor_lte_cell_info_ind func)
{
    MMI_MODEM_SIM_ID sim_id = modem_sim_id;

    if(NULL == func) {
        printf("func is null in app_adaptor_lte_cell_info_ind_bind\n");
        return;
    }
    g_ModemNwMng[sim_id].GetCellInfo.lte_cellinfoind = func;
    printf("app adaptor lte cell info ind bind\n");
}

/**
 * bind GSM cell information indication call back
 * param (in) func app_adaptor_lte_cell_info_ind
 * return  void
 */
void app_adaptor_gsm_cell_info_ind_bind(app_adaptor_gsm_cell_info_ind func)
{
    MMI_MODEM_SIM_ID sim_id = modem_sim_id;

    if(NULL == func) {
        printf("func is null in app_adaptor_gsm_cell_info_ind_bind\n");
        return;
    }
    g_ModemNwMng[sim_id].GetCellInfo.gsm_cellinfoind = func;
    printf("app adaptor gsm cell info ind bind\n");
}

void App_Mode_Process_Lte_Scell_Info(RIL_SIM_ID sid, RIL_Lte_Scell_Info * scell)
{
    uint16_t mcc;
    uint16_t mnc;
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == scell) {
        printf("scell is null in App_Mode_Process_Lte_Scell_Info\n");
        return;
    }
    printf("Lte_Scell_Info sid is %d\n", sid);
    if(RIL_SIM_1 == sid) {
        if(NULL == g_ModemNwMng[0].GetCellInfo.ltecell_list) {
            g_ModemNwMng[0].GetCellInfo.ltecell_list
                = (app_adp_lte_cells_t *)Hal_Mem_Alloc(sizeof(app_adp_lte_cells_t));
            memset(g_ModemNwMng[0].GetCellInfo.ltecell_list, 0, sizeof(app_adp_lte_cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        if(NULL == g_ModemNwMng[1].GetCellInfo.ltecell_list) {
            g_ModemNwMng[1].GetCellInfo.ltecell_list
                = (app_adp_lte_cells_t *)Hal_Mem_Alloc(sizeof(app_adp_lte_cells_t));
            memset(g_ModemNwMng[1].GetCellInfo.ltecell_list, 0, sizeof(app_adp_lte_cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    memcpy(&getcellinfo_mng->ltecell_list->scell, scell, sizeof(app_adp_lte_scell_info_t));

    mcc = scell->mcc;
    mnc = scell->mnc;
    getcellinfo_mng->ltecell_list->scell.mcc = ((mcc >> 8) & 0x000F) * 100
            + ((mcc >> 4) & 0x000F) * 10
            + (mcc & 0x000F);
    printf("mcc is %d\n", getcellinfo_mng->ltecell_list->scell.mcc);

    getcellinfo_mng->ltecell_list->scell.mnc = ((mnc >> 8) & 0x000F) * 100
            + ((mnc >> 4) & 0x000F) * 10
            + (mnc & 0x000F);
    printf("mnc is %d\n", getcellinfo_mng->ltecell_list->scell.mnc);
    printf("Lte_Scell_Info scell->euarfcn is %d\n", scell->euarfcn);
    printf("Lte_Scell_Info scell->cellid is %d\n", scell->cellid);
}

void Engineer_Mode_Process_Lte_Scell_Info(RIL_SIM_ID sid, RIL_Lte_Scell_Info * scell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == scell) {
        printf("scell is null in Engineer_Mode_Process_Lte_Scell_Info\n");
        return;
    }
    printf("Lte_Scell_Info sid in engineer mode is %d\n", sid);
    if(RIL_SIM_1 == sid) {
        if(NULL == g_ModemNwMng[0].GetCellInfo.ltecells) {
            g_ModemNwMng[0].GetCellInfo.ltecells
                = (MMI_Modem_Lte_Cells_t *)Hal_Mem_Alloc(sizeof(MMI_Modem_Lte_Cells_t));
            memset(g_ModemNwMng[0].GetCellInfo.ltecells, 0, sizeof(MMI_Modem_Lte_Cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        if(NULL == g_ModemNwMng[1].GetCellInfo.ltecells) {
            g_ModemNwMng[1].GetCellInfo.ltecells
                = (MMI_Modem_Lte_Cells_t *)Hal_Mem_Alloc(sizeof(MMI_Modem_Lte_Cells_t));
            memset(g_ModemNwMng[1].GetCellInfo.ltecells, 0, sizeof(MMI_Modem_Lte_Cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    memcpy(&getcellinfo_mng->ltecells->scell, scell, sizeof(MMI_MODEM_LTE_SCELL_INFO));
    printf("Lte_Scell_Info scell->euarfcn in engineer mode is %d\n", scell->euarfcn);
    printf("Lte_Scell_Info scell->ulbler in engineer mode is %d\n", scell->ulbler);
}

/**
 * Indicates LTE Serving cell informaton
 * +EEMLTESVC
 * param (in) sid:  RIL_SIM_ID
 * param (in) scell:  RIL_Lte_Scell_Info *
 * return  void
 */
void Ril_EEM_Response_Lte_Scell_Info(RIL_SIM_ID sid, RIL_Lte_Scell_Info * scell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(MODEM_NW_APP_USE == getcellinfo_mng->srctype) {
        App_Mode_Process_Lte_Scell_Info(sid, scell);
    } else {
        Engineer_Mode_Process_Lte_Scell_Info(sid, scell);
    }
    Hal_Mem_Free(scell);
}

void App_Mode_Process_Lte_Intra_Ncell_Info(RIL_SIM_ID sid, RIL_Lte_Ncell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in App_Mode_Process_Lte_Intra_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(getcellinfo_mng->ltecell_list->encell_num < APP_ADP_MAX_LTE_NCELL_NUM) {
        memcpy(&getcellinfo_mng->ltecell_list->encell[getcellinfo_mng->ltecell_list->encell_num],
               ncell,
               sizeof(RIL_Lte_Ncell_Info));
        getcellinfo_mng->ltecell_list->encell_num++;
        printf("Lte_Intra_Ncell_Info ncell->euarfcn is %d\n", ncell->euarfcn);
        printf("Lte_Intra_Ncell_Info ncell->cellid is %d\n", ncell->cellid);
        printf("Lte_Intra_Ncell_Info encell_num is %d\n", getcellinfo_mng->ltecell_list->encell_num);
    }
}

void Engineer_Mode_Process_Lte_Intra_Ncell_Info(RIL_SIM_ID sid, RIL_Lte_Ncell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in Engineer_Mode_Process_Lte_Intra_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(getcellinfo_mng->ltecells->encell_num < MMI_MODEM_MAX_LTE_NCELL_NUM) {
        memcpy(&getcellinfo_mng->ltecells->encell[getcellinfo_mng->ltecells->encell_num],
               ncell,
               sizeof(RIL_Lte_Ncell_Info));
        getcellinfo_mng->ltecells->encell_num++;
        printf("Lte_Intra_Ncell_Info ncell->euarfcn in engineer mode is %d\n", ncell->euarfcn);
        printf("Lte_Intra_Ncell_Info ncell->cellid in engineer mode is %d\n", ncell->cellid);
        printf("Lte_Intra_Ncell_Info encell_num in engineer mode is %d\n", getcellinfo_mng->ltecells->encell_num);
    }
}

/**
 * Indicates LTE intra freq neibor cell informaton
 * +EEMLTEINTRA
 * param (in) sid:  RIL_SIM_ID
 * param (in) ncell:  RIL_Lte_ncell_Info *
 * return  void
 */
void Ril_EEM_Response_Lte_Intra_Ncell_Info(RIL_SIM_ID sid, RIL_Lte_Ncell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(MODEM_NW_APP_USE == getcellinfo_mng->srctype) {
        App_Mode_Process_Lte_Intra_Ncell_Info(sid, ncell);
    } else {
        Engineer_Mode_Process_Lte_Intra_Ncell_Info(sid, ncell);
    }
    Hal_Mem_Free(ncell);
}

void App_Mode_Process_Lte_Inter_Ncell_Info(RIL_SIM_ID sid, RIL_Lte_Ncell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in App_Mode_Process_Lte_Inter_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(getcellinfo_mng->ltecell_list->encell_num < APP_ADP_MAX_LTE_NCELL_NUM) {
        memcpy(&getcellinfo_mng->ltecell_list->encell[getcellinfo_mng->ltecell_list->encell_num],
               ncell,
               sizeof(RIL_Lte_Ncell_Info));
        getcellinfo_mng->ltecell_list->encell_num++;
        printf("Lte_Inter_Ncell_Info ncell->euarfcn is %d\n", ncell->euarfcn);
        printf("Lte_Inter_Ncell_Info ncell->cellid is %d\n", ncell->cellid);
        printf("Lte_Inter_Ncell_Info encell_num is %d\n", getcellinfo_mng->ltecell_list->encell_num);
    }
}

void Engineer_Mode_Process_Lte_Inter_Ncell_Info(RIL_SIM_ID sid, RIL_Lte_Ncell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in Engineer_Mode_Process_Lte_Inter_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(getcellinfo_mng->ltecells->encell_num < MMI_MODEM_MAX_LTE_NCELL_NUM) {
        memcpy(&getcellinfo_mng->ltecells->encell[getcellinfo_mng->ltecells->encell_num],
               ncell,
               sizeof(RIL_Lte_Ncell_Info));
        getcellinfo_mng->ltecells->encell_num++;
        printf("Lte_Inter_Ncell_Info ncell->euarfcn is %d\n", ncell->euarfcn);
        printf("Lte_Inter_Ncell_Info ncell->cellid is %d\n", ncell->cellid);
        printf("Lte_Inter_Ncell_Info encell_num is %d\n", getcellinfo_mng->ltecells->encell_num);
    }
}

/**
 * Indicates LTE inter freq neigbor cell informaton
 * +EEMLTEINTER
 * param (in) sid:  RIL_SIM_ID
 * param (in) ncell:  RIL_Lte_ncell_Info *
 * return  void
 * note: only gsm ncell present for crane
 */
void Ril_EEM_Response_Lte_Inter_Ncell_Info(RIL_SIM_ID sid, RIL_Lte_Ncell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(MODEM_NW_APP_USE == getcellinfo_mng->srctype) {
        App_Mode_Process_Lte_Inter_Ncell_Info(sid, ncell);
    } else {
        Engineer_Mode_Process_Lte_Inter_Ncell_Info(sid, ncell);
    }
    Hal_Mem_Free(ncell);
}

void App_Mode_Process_Lte_Irat_Ncell_Info(RIL_SIM_ID sid, RIL_Gsm_Cell_Info * ncell)
{
    uint16_t mcc;
    uint16_t mnc;
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in App_Mode_Process_Lte_Irat_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(getcellinfo_mng->ltecell_list->gncell_num < APP_ADP_MAX_GSM_NCELL_NUM) {
        memcpy(&getcellinfo_mng->ltecell_list->gncell[getcellinfo_mng->ltecell_list->gncell_num],
               ncell,
               sizeof(RIL_Gsm_Cell_Info));
        mcc = ncell->mcc;
        mnc = ncell->mnc;
        getcellinfo_mng->ltecell_list->gncell[getcellinfo_mng->ltecell_list->gncell_num].mcc = ((mcc >> 8) & 0x000F) * 100
                + ((mcc >> 4) & 0x000F) * 10
                + (mcc & 0x000F);
        printf("mcc is %d\n", getcellinfo_mng->ltecell_list->gncell[getcellinfo_mng->ltecell_list->gncell_num].mcc);

        getcellinfo_mng->ltecell_list->gncell[getcellinfo_mng->ltecell_list->gncell_num].mnc = ((mnc >> 8) & 0x000F) * 100
                + ((mnc >> 4) & 0x000F) * 10
                + (mnc & 0x000F);
        printf("mnc is %d\n", getcellinfo_mng->ltecell_list->gncell[getcellinfo_mng->ltecell_list->gncell_num].mnc);
        getcellinfo_mng->ltecell_list->gncell_num++;
        printf("Lte_Irat_Ncell_Info ncell->arfcn is %d\n", ncell->arfcn);
        printf("Lte_Irat_Ncell_Info ncell->bsic is %d\n", ncell->bsic);
        printf("Lte_Irat_Ncell_Info gncell_num is %d\n", getcellinfo_mng->ltecell_list->gncell_num);
    }
}

void Engineer_Mode_Process_Lte_Irat_Ncell_Info(RIL_SIM_ID sid, RIL_Gsm_Cell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in Engineer_Mode_Process_Lte_Irat_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(getcellinfo_mng->ltecells->gncell_num < MMI_MODEM_MAX_GSM_NCELL_NUM) {
        memcpy(&getcellinfo_mng->ltecells->gncell[getcellinfo_mng->ltecells->gncell_num],
               ncell,
               sizeof(RIL_Gsm_Cell_Info));
        getcellinfo_mng->ltecells->gncell_num++;
        printf("Lte_Irat_Ncell_Info ncell->arfcn in engineer mode is %d\n", ncell->arfcn);
        printf("Lte_Irat_Ncell_Info ncell->bsic in engineer mode is %d\n", ncell->bsic);
        printf("Lte_Irat_Ncell_Info gncell_num in engineer mode is %d\n", getcellinfo_mng->ltecells->gncell_num);
    }
}

/**
 * Indicates LTE inter freq neigbor cell informaton
 * +EEMLTEINTERRAT
 * param (in) sid:  RIL_SIM_ID
 * param (in) ncell:  RIL_Lte_Irat_ncell_Info *
 * return  void
 * note: only gsm ncell present for crane
 */
void Ril_EEM_Response_Lte_Irat_Ncell_Info(RIL_SIM_ID sid, RIL_Gsm_Cell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(MODEM_NW_APP_USE == getcellinfo_mng->srctype) {
        App_Mode_Process_Lte_Irat_Ncell_Info(sid, ncell);
    } else {
        Engineer_Mode_Process_Lte_Irat_Ncell_Info(sid, ncell);
    }
    Hal_Mem_Free(ncell);
}

void App_Mode_Process_Gsm_Scell_Info(RIL_SIM_ID sid, RIL_Gsm_Cell_Info * scell)
{
    uint16_t mcc;
    uint16_t mnc;
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == scell) {
        printf("scell is null in App_Mode_Process_Gsm_Scell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        if(NULL == g_ModemNwMng[0].GetCellInfo.gsmcell_list) {
            g_ModemNwMng[0].GetCellInfo.gsmcell_list
                = (app_adp_gsm_cells_t *)Hal_Mem_Alloc(sizeof(app_adp_gsm_cells_t));
            memset(g_ModemNwMng[0].GetCellInfo.gsmcell_list, 0, sizeof(app_adp_gsm_cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        if(NULL == g_ModemNwMng[1].GetCellInfo.gsmcell_list) {
            g_ModemNwMng[1].GetCellInfo.gsmcell_list
                = (app_adp_gsm_cells_t *)Hal_Mem_Alloc(sizeof(app_adp_gsm_cells_t));
            memset(g_ModemNwMng[1].GetCellInfo.gsmcell_list, 0, sizeof(app_adp_gsm_cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    memcpy(&getcellinfo_mng->gsmcell_list->scell, scell, sizeof(RIL_Gsm_Cell_Info));

    mcc = scell->mcc;
    mnc = scell->mnc;
    getcellinfo_mng->gsmcell_list->scell.mcc = ((mcc >> 8) & 0x000F) * 100
            + ((mcc >> 4) & 0x000F) * 10
            + (mcc & 0x000F);
    printf("mcc is %d\n", getcellinfo_mng->gsmcell_list->scell.mcc);

    getcellinfo_mng->gsmcell_list->scell.mnc = ((mnc >> 8) & 0x000F) * 100
            + ((mnc >> 4) & 0x000F) * 10
            + (mnc & 0x000F);
    printf("mnc is %d\n", getcellinfo_mng->gsmcell_list->scell.mnc);
    printf("Gsm_Scell_Info scell->arfcn is %d\n", scell->arfcn);
    printf("Gsm_Scell_Info scell->bsic is %d\n", scell->bsic);
}

void Engineer_Mode_Process_Gsm_Scell_Info(RIL_SIM_ID sid, RIL_Gsm_Cell_Info * scell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == scell) {
        printf("scell is null in Engineer_Mode_Process_Gsm_Scell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        if(NULL == g_ModemNwMng[0].GetCellInfo.gsmcells) {
            g_ModemNwMng[0].GetCellInfo.gsmcells
                = (MMI_Modem_Gsm_Cells_t *)Hal_Mem_Alloc(sizeof(MMI_Modem_Gsm_Cells_t));
            memset(g_ModemNwMng[0].GetCellInfo.gsmcells, 0, sizeof(MMI_Modem_Gsm_Cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        if(NULL == g_ModemNwMng[1].GetCellInfo.gsmcells) {
            g_ModemNwMng[1].GetCellInfo.gsmcells
                = (MMI_Modem_Gsm_Cells_t *)Hal_Mem_Alloc(sizeof(MMI_Modem_Gsm_Cells_t));
            memset(g_ModemNwMng[1].GetCellInfo.gsmcells, 0, sizeof(MMI_Modem_Gsm_Cells_t));
        }
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    memcpy(&getcellinfo_mng->gsmcells->scell, scell, sizeof(RIL_Gsm_Cell_Info));
    printf("Gsm_Scell_Info scell->arfcn in engineer mode is %d\n", scell->arfcn);
    printf("Gsm_Scell_Info scell->bsic in engineer mode is %d\n", scell->bsic);
}

/**
 * Indicates GSM Serving cell informaton
 * +EEMGINFOSVC
 * param (in) sid:  RIL_SIM_ID
 * param (in) scell:  RIL_Gsm_Cell_Info *
 * return  void
 */
void Ril_EEM_Response_Gsm_Scell_Info(RIL_SIM_ID sid, RIL_Gsm_Cell_Info * scell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(MODEM_NW_APP_USE == getcellinfo_mng->srctype) {
        App_Mode_Process_Gsm_Scell_Info(sid, scell);
    } else {
        Engineer_Mode_Process_Gsm_Scell_Info(sid, scell);
    }
    Hal_Mem_Free(scell);
}

void App_Mode_Process_Gsm_Ncell_Info(RIL_SIM_ID sid, RIL_Gsm_NCell_Info * ncell)
{
    uint16_t mcc;
    uint16_t mnc;
    UINT8 i;
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in App_Mode_Process_Gsm_Ncell_Info\n");
        return;
    }
    if(NULL == ncell->Ncell) {
        printf("ncell->Ncell is null in App_Mode_Process_Gsm_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    printf("Gsm_Ncell_Info ncell->Number is %d\n", ncell->Number);
    for(i = 0; i < ncell->Number; i++) {
        if(getcellinfo_mng->gsmcell_list->gncell_num < APP_ADP_MAX_GSM_NCELL_NUM) {
            memcpy(&getcellinfo_mng->gsmcell_list->gncell[getcellinfo_mng->gsmcell_list->gncell_num],
                   &ncell->Ncell[i],
                   sizeof(RIL_Gsm_Cell_Info));
            mcc = ncell->Ncell[i].mcc;
            mnc = ncell->Ncell[i].mnc;
            getcellinfo_mng->gsmcell_list->gncell[getcellinfo_mng->gsmcell_list->gncell_num].mcc = ((mcc >> 8) & 0x000F) * 100
                    + ((mcc >> 4) & 0x000F) * 10
                    + (mcc & 0x000F);
            printf("mcc is %d\n", getcellinfo_mng->gsmcell_list->gncell[getcellinfo_mng->gsmcell_list->gncell_num].mcc);

            getcellinfo_mng->gsmcell_list->gncell[getcellinfo_mng->gsmcell_list->gncell_num].mnc = ((mnc >> 8) & 0x000F) * 100
                    + ((mnc >> 4) & 0x000F) * 10
                    + (mnc & 0x000F);
            printf("mnc is %d\n", getcellinfo_mng->gsmcell_list->gncell[getcellinfo_mng->gsmcell_list->gncell_num].mnc);
            getcellinfo_mng->gsmcell_list->gncell_num++;
            printf("Gsm_Ncell_Info ncell->Ncell[%d].arfcn is %d\n", i, ncell->Ncell[i].arfcn);
            printf("Gsm_Ncell_Info ncell->Ncell[%d].bsic is %d\n", i, ncell->Ncell[i].bsic);
            printf("Gsm_Ncell_Info gncell_num is %d\n", getcellinfo_mng->gsmcell_list->gncell_num);
        }
    }
}

void Engineer_Mode_Process_Gsm_Ncell_Info(RIL_SIM_ID sid, RIL_Gsm_NCell_Info * ncell)
{
    UINT8 i;
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(NULL == ncell) {
        printf("ncell is null in Ril_EEM_Response_Gsm_Ncell_Info\n");
        return;
    }
    if(NULL == ncell->Ncell) {
        printf("ncell->Ncell is null in Ril_EEM_Response_Gsm_Ncell_Info\n");
        return;
    }

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    printf("Gsm_Ncell_Info ncell->Number in engineer mode is %d\n", ncell->Number);
    for(i = 0; i < ncell->Number; i++) {
        if(getcellinfo_mng->gsmcells->gncell_num < MMI_MODEM_MAX_GSM_NCELL_NUM) {
            memcpy(&getcellinfo_mng->gsmcells->gncell[getcellinfo_mng->gsmcells->gncell_num],
                   &ncell->Ncell[i],
                   sizeof(RIL_Gsm_Cell_Info));
            getcellinfo_mng->gsmcells->gncell_num++;
            printf("Gsm_Ncell_Info ncell->Ncell[%d].arfcn in engineer mode is %d\n", i, ncell->Ncell[i].arfcn);
            printf("Gsm_Ncell_Info ncell->Ncell[%d].bsic in engineer mode is %d\n", i, ncell->Ncell[i].bsic);
            printf("Gsm_Ncell_Info gncell_num in engineer mode is %d\n", getcellinfo_mng->gsmcells->gncell_num);
        }
    }
}

/**
 * Indicates GSM neighbor cell informaton
 * +EEMGINFONC
 * param (in) sid:  RIL_SIM_ID
 * param (in) ncell:  RIL_Gsm_NCell_Info *
 * return  void
 */
void Ril_EEM_Response_Gsm_Ncell_Info(RIL_SIM_ID sid, RIL_Gsm_NCell_Info * ncell)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    if(MODEM_NW_APP_USE == getcellinfo_mng->srctype) {
        App_Mode_Process_Gsm_Ncell_Info(sid, ncell);
    } else {
        Engineer_Mode_Process_Gsm_Ncell_Info(sid, ncell);
    }
    Hal_Mem_Free(ncell->Ncell);
    Hal_Mem_Free(ncell);
}

/**
 * Indicates GSM neighbor cell informaton
 * +EEMGINFO
 * param (in) sid:  RIL_SIM_ID
 * param (in) nw_type:  unsigned char
 * return  void
 */
void Ril_EEM_Response_EEM_Info(RIL_SIM_ID sid, unsigned char nw_type)
{
    MODEM_NW_GET_CELL_INFO * getcellinfo_mng;

    if(RIL_SIM_1 == sid) {
        getcellinfo_mng = &g_ModemNwMng[0].GetCellInfo;
    } else {
        getcellinfo_mng = &g_ModemNwMng[1].GetCellInfo;
    }

    printf("EEM_Info nw_type is %d\n", nw_type);
    printf("EEM_Info srctype is %d\n", getcellinfo_mng->srctype);
    app_adaptor_lte_cell_info_ind lte_cellinfoind = getcellinfo_mng->lte_cellinfoind;
    app_adaptor_gsm_cell_info_ind gsm_cellinfoind = getcellinfo_mng->gsm_cellinfoind;

    if(MODEM_NW_APP_USE == getcellinfo_mng->srctype) {
        if(2 == nw_type) {
            if(NULL != lte_cellinfoind) {
                lte_cellinfoind(getcellinfo_mng->ltecell_list);
                getcellinfo_mng->ltecell_list = NULL;
            } else {
                printf("lte_cellinfoind is null in Ril_EEM_Response_EEM_Info\n");
            }
        } else if(0 == nw_type) {
            if(NULL != gsm_cellinfoind) {
                gsm_cellinfoind(getcellinfo_mng->gsmcell_list);
                getcellinfo_mng->gsmcell_list = NULL;
            } else {
                printf("gsm_cellinfoind is null in Ril_EEM_Response_EEM_Info\n");
            }
        } else if(0xFF == nw_type) {
            printf("ril report the cme error to UI in Ril_EEM_Response_EEM_Info for user\n");
            if(NULL != lte_cellinfoind) {
                lte_cellinfoind(NULL);
            } else {
                printf("lte_cellinfoind is null in Ril_EEM_Response_EEM_Info\n");
            }
            if(NULL != getcellinfo_mng->ltecell_list) {
                lv_mem_free(getcellinfo_mng->ltecell_list);
            }
            getcellinfo_mng->ltecell_list = NULL;
            if(NULL != getcellinfo_mng->gsmcell_list) {
                lv_mem_free(getcellinfo_mng->gsmcell_list);
            }
            getcellinfo_mng->gsmcell_list = NULL;
        }
    } else {
        if(2 == nw_type) {
            MMI_Modem_Lte_Cells_Ind(getcellinfo_mng->ltecells);
            getcellinfo_mng->ltecells = NULL;
        } else if(0 == nw_type) {
            MMI_Modem_Gsm_Cells_Ind(getcellinfo_mng->gsmcells);
            getcellinfo_mng->gsmcells = NULL;
        } else if(0xFF == nw_type) {
            printf("ril report the cme error to UI in Ril_EEM_Response_EEM_Info for engineer mode\n");
            if(NULL != getcellinfo_mng->ltecells) {
                lv_mem_free(getcellinfo_mng->ltecell_list);
            }
            getcellinfo_mng->ltecells = NULL;
            if(NULL != getcellinfo_mng->gsmcells) {
                lv_mem_free(getcellinfo_mng->gsmcells);
            }
            getcellinfo_mng->gsmcells = NULL;
        }
    }


}

void Ril_EEM_Response_Cali_Info(RIL_SIM_ID SimId, RIL_Errno result)
{
    RIL_Modemadp_Rsp_Cali_Info_t * pMsg = NULL;

    printf("##interface## %s \n", __FUNCTION__);
    printf("result = %d\n", result);

    pMsg = (RIL_Modemadp_Rsp_Cali_Info_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Rsp_Cali_Info_t));
    Hal_Mem_Set(pMsg, 0, sizeof(RIL_Modemadp_Rsp_Cali_Info_t));

    pMsg->SimId = SimId;
    pMsg->result = result;

    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_CALI_INFO, (UI_Inter_Msg_t *)pMsg);
}

void Ril_DEV_Response_Get_SN(RIL_SIM_ID SimId, char * psn, RIL_Errno result)
{
    RIL_Modemadp_Rsp_Get_Sn_t * pMsg = NULL;

    printf("##interface## %s\n", __FUNCTION__);

    pMsg = (RIL_Modemadp_Rsp_Get_Sn_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Rsp_Get_Sn_t));
    Hal_Mem_Set(pMsg, 0, sizeof(RIL_Modemadp_Rsp_Get_Sn_t));

    pMsg->SimId = SimId;
    pMsg->result = result;
    pMsg->pSn = psn;

    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_GET_SN, (UI_Inter_Msg_t *)pMsg);
}

/**
 * *BANDIND:<band>,<Act>
 *
 * param (in) sid:  RIL_SIM_ID
 * param (in) s:  const INT8 *
 * return  void
 */
VOID Ril_MM_Response_Bandind(RIL_SIM_ID SimId, const INT8 * s)
{
    RIL_Modemadp_Rsp_Bandind_t * pMsg;
    UINT16                        BandindStringLen;

    pMsg =
        (RIL_Modemadp_Rsp_Bandind_t *)Hal_Mem_Alloc(sizeof(RIL_Modemadp_Rsp_Bandind_t));
    BandindStringLen = strlen(s);

    pMsg->SimId      = SimId;
    pMsg->BandindString = Hal_Mem_Alloc(BandindStringLen + 1);
    strncpy(pMsg->BandindString, s, BandindStringLen + 1);
    UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, RIL_MODEMADP_MM_RSP_BANDIND, (UI_Inter_Msg_t *)pMsg);
}

/******************************************************************************
 *
 *         Message Handler of MMI -> Modem Adaptor
 *
*******************************************************************************/

/**
 * Handler of POWER_UP_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Power_Up_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modemadp_Power_Up_Req_t * PowerUpReq;
    RIL_Errno                       RilResult;

    PowerUpReq = (MMI_Modemadp_Power_Up_Req_t *)Msg;

    if(MODEM_NW_STATE_IDLE == g_ModemNwMng[PowerUpReq->SimId].ModemState) {
        RilResult = Ril_MM_Request_Radio_Power(MMI_ModemAdp_Get_Ril_SimId(PowerUpReq->SimId), TRUE);
        if(RIL_E_SUCCESS == RilResult) {
            Ril_MM_Request_Screen_State(1, MMI_ModemAdp_Get_Ril_SimId(PowerUpReq->SimId));
            MMI_Modem_Power_Up_Cnf(PowerUpReq->SimId);
            g_ModemNwMng[PowerUpReq->SimId].ConfApnOnPowerup = 1;
        } else {
            MMI_ModemAdp_printf("%s: Turn on radio failed: %d\n", __FUNCTION__, RilResult);
        }
    } else if(MODEM_NW_STATE_NULL == g_ModemNwMng[PowerUpReq->SimId].ModemState) {
        g_ModemNwMng[PowerUpReq->SimId].PendingMsg |= MODEM_NW_POWER_UP_REQ;
        MMI_ModemAdp_printf("Sim(%d) buffer power up request since ril is not ready!\n", PowerUpReq->SimId);
    }
}

/**
 * Handler of POWER_OFF_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Power_Off_Req_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Errno                       RilResult;
    MMI_Modemadp_Power_Off_Req_t * PowerOffReq;

    printf("##interface## %s\n", __FUNCTION__);
    PowerOffReq = (MMI_Modemadp_Power_Off_Req_t *)Msg;

    if(MODEM_NW_STATE_NULL == g_ModemNwMng[PowerOffReq->SimId].ModemState) {
        // not in power on status
        return;
    }

    if(MMI_MODEM_POWER_OFF == PowerOffReq->PowerOffType) {
        RilResult = Ril_MM_Request_Radio_Power(MMI_ModemAdp_Get_Ril_SimId(PowerOffReq->SimId), FALSE);
        if(RIL_E_SUCCESS == RilResult) {
            MMI_Modem_Power_Off_Cnf(PowerOffReq->SimId);
            g_ModemNwMng[PowerOffReq->SimId].SignalBar = MMI_MODEM_SIGNAL_BAR_0;
        } else {
            MMI_ModemAdp_printf("Shutdown failed: %d\n", RilResult);
        }
    } else { //MMI_MODEM_SIM_DISABLE
        RilResult = Ril_MM_Request_Radio_Power(MMI_ModemAdp_Get_Ril_SimId(PowerOffReq->SimId), FALSE);
        if(RIL_E_SUCCESS == RilResult) {
            MMI_Modem_Power_Off_Cnf(PowerOffReq->SimId);
        } else {
            MMI_ModemAdp_printf("Turn off radio failed: %d\n", RilResult);
        }
    }

    if(NULL != g_ModemNwMng[PowerOffReq->SimId].PlmnInfo.PlmnName) {
        Hal_Mem_Free(g_ModemNwMng[PowerOffReq->SimId].PlmnInfo.PlmnName);
        g_ModemNwMng[PowerOffReq->SimId].PlmnInfo.PlmnName = NULL;
    }
}

/**
 * Handler of MMI_MODEMADP_PLMN_SEARCH_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Plmn_Search_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modem_Plmn_Search_Req_t   *  PlmnSearchReq;
    RIL_Errno                       RilResult;
    INT8                            OperStr[11] = {0};
    UINT8                           Act = 0;

    UINT8   PlmnNameLen = 0;

    printf("##interface## %s\n", __FUNCTION__);

    PlmnSearchReq = (MMI_Modem_Plmn_Search_Req_t *)Msg;

    //handled only in IDLE state
    if(MODEM_NW_STATE_IDLE != g_ModemNwMng[PlmnSearchReq->simId].ModemState) {
        return;
    }

    //search PLMN list
    if(MMI_MODEM_PLMN_SEARCH_LIST == PlmnSearchReq->SearchType) {
        RilResult = Ril_MM_Request_Query_Available_Networks(MMI_ModemAdp_Get_Ril_SimId(PlmnSearchReq->simId));
        if(RIL_E_SUCCESS != RilResult) {
            MMI_ModemAdp_printf("Request query available networks failed: %d\n", RilResult);
            return;
        }
        g_ModemNwMng[PlmnSearchReq->simId].ModemState = MODEM_NW_STATE_PLMN_SEARHING;
    }

    //search specified PLMN
    else if(MMI_MODEM_PLMN_SEARCH_SPEC == PlmnSearchReq->SearchType) {
        if(MMI_MODEM_PLMN_RAT_GSM == PlmnSearchReq->SpecPlmn.PlmnRat) {
            Act = 0;
        } else if(MMI_MODEM_PLMN_RAT_UMTS == PlmnSearchReq->SpecPlmn.PlmnRat) {
            Act = 2;
        } else { //MMI_MODEM_PLMN_RAT_LTE
            Act = 7;
        }
        snprintf(OperStr, strlen(PlmnSearchReq->SpecPlmn.PlmnId) + 4, "\"%s\",%d", PlmnSearchReq->SpecPlmn.PlmnId, Act);
        RilResult = Ril_MM_Request_Set_Network_Selection_Manual(OperStr, MMI_ModemAdp_Get_Ril_SimId(PlmnSearchReq->simId));
        if(RIL_E_SUCCESS != RilResult) {
            MMI_ModemAdp_printf("Request manual NW selection failed: %d\n", RilResult);
            return;
        }

        g_ModemNwMng[PlmnSearchReq->simId].ModemState = MODEM_NW_STATE_PLMN_SEARHING;

        //store the specified PLMN info to global management variable, can be used for PLMN_SEARCH_CNF
        g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnState = PlmnSearchReq->SpecPlmn.PlmnStat;
        g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnRat = PlmnSearchReq->SpecPlmn.PlmnRat;
        if(NULL != g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName) {
            Hal_Mem_Free(g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName);
        }
        PlmnNameLen = strlen(PlmnSearchReq->SpecPlmn.PlmnName);
        if(0 != PlmnNameLen) {
            g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName = Hal_Mem_Alloc(PlmnNameLen + 1);
            memset(g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName, 0, PlmnNameLen + 1);
            memcpy(g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName, PlmnSearchReq->SpecPlmn.PlmnName, PlmnNameLen);
        } else {
            g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName = Hal_Mem_Alloc(10);
            memset(g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName, 0, 10);
            strncpy(g_ModemNwMng[PlmnSearchReq->simId].PlmnInfo.PlmnName, "Unknown", 10);
        }
    }

    //auto search
    else {
        RilResult = Ril_MM_Request_Set_Network_Selection_Automatic(MMI_ModemAdp_Get_Ril_SimId(PlmnSearchReq->simId));
        if(RIL_E_SUCCESS != RilResult) {
            MMI_ModemAdp_printf("Request auto NW selection failed: %d\n", RilResult);
            return;
        }
    }

}

/**
 * Handler of PLMN_SEARCH_STOP_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Plmn_Search_Stop_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modemadp_Plmn_Search_Stop_Req_t * PlmnSearchStopReq;
    RIL_Errno                               RilResult;

    printf("##interface## %s\n", __FUNCTION__);

    PlmnSearchStopReq = (MMI_Modemadp_Plmn_Search_Stop_Req_t *)Msg;

    //handled only in IDLE state
    if(MODEM_NW_STATE_PLMN_SEARHING != g_ModemNwMng[PlmnSearchStopReq->SimId].ModemState) {
        return;
    }

    RilResult = Ril_MM_Request_Cancel_Plmn_Search(MMI_ModemAdp_Get_Ril_SimId(PlmnSearchStopReq->SimId));
    if(RIL_E_SUCCESS != RilResult) {
        MMI_ModemAdp_printf("Plmn search stop request failed: %d\n", RilResult);
    }

    g_ModemNwMng[PlmnSearchStopReq->SimId].ModemState = MODEM_NW_STATE_IDLE;
}



/******************************************************************************
 *
 *         Message Handler of RIL -> Modem Adaptor
 *
*******************************************************************************/
/**
 * Handler of RIL_READY_IND
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Ril_Ready_Ind_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Errno    RilResult;

    if(MODEM_NW_STATE_NULL == g_ModemNwMng[0].ModemState) {
        //update state
        g_ModemNwMng[0].ModemState = MODEM_NW_STATE_IDLE;
        g_ModemNwMng[1].ModemState = MODEM_NW_STATE_IDLE;

        Ril_MM_Request_Screen_State(1, RIL_SIM_1);
        Ril_MM_Request_Screen_State(1, RIL_SIM_2);

        if(MODEM_NW_POWER_UP_REQ & g_ModemNwMng[0].PendingMsg) {
            g_ModemNwMng[0].PendingMsg &= ~(MODEM_NW_POWER_UP_REQ);
            RilResult = Ril_MM_Request_Radio_Power(MMI_ModemAdp_Get_Ril_SimId(MMI_MODEM_SIM_1), TRUE);
            if(RIL_E_SUCCESS == RilResult) {
                MMI_Modem_Power_Up_Cnf(MMI_MODEM_SIM_1);
                g_ModemNwMng[0].ConfApnOnPowerup = 1;
            } else {
                MMI_ModemAdp_printf("%s: Turn on radio failed: %d\n", __FUNCTION__, RilResult);
            }
        }
        if(MODEM_NW_POWER_UP_REQ & g_ModemNwMng[1].PendingMsg) {
            g_ModemNwMng[1].PendingMsg &= ~(MODEM_NW_POWER_UP_REQ);
            RilResult = Ril_MM_Request_Radio_Power(MMI_ModemAdp_Get_Ril_SimId(MMI_MODEM_SIM_2), TRUE);
            if(RIL_E_SUCCESS == RilResult) {
                MMI_Modem_Power_Up_Cnf(MMI_MODEM_SIM_2);
                g_ModemNwMng[1].ConfApnOnPowerup = 1;
            } else {
                MMI_ModemAdp_printf("%s: Turn on radio failed: %d\n", __FUNCTION__, RilResult);
            }
        }

        MMI_Modemadp_Sim_Present_Chk_Req_t * SimPresentChkReq;

        SimPresentChkReq        = (MMI_Modemadp_Sim_Present_Chk_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Sim_Present_Chk_Req_t));
        SimPresentChkReq->SimId = MMI_MODEM_SIM_1;
        UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_SIM_PRESENT_CHK_REQ, (UI_Inter_Msg_t *)SimPresentChkReq);

        SimPresentChkReq        = (MMI_Modemadp_Sim_Present_Chk_Req_t *)Hal_Mem_Alloc(sizeof(MMI_Modemadp_Sim_Present_Chk_Req_t));
        SimPresentChkReq->SimId = MMI_MODEM_SIM_2;
        UI_Send_Inter_Msg(UI_SUB_MODULE_MODEM_ADP, MMI_MODEMADP_SIM_PRESENT_CHK_REQ, (UI_Inter_Msg_t *)SimPresentChkReq);

        // for read & write SIM phonebook
        Ril_SIM_Request_Set_Cscs(RIL_SIM_1);
        Ril_SIM_Request_Set_Cscs(RIL_SIM_2);
    } else {
        MMI_ModemAdp_printf("Unexpected RIL_READY_IND!\n");
    }
}
#ifdef FEATURE_HERO_ENGINE_APP
#include <stdio.h>
#include "stdarg.h"
#define HERO_ENGINE_LOG

void hr_printf(const char *format,...)
{
	#ifdef HERO_ENGINE_LOG
	char out_buf[256] = {0};

	va_list params;
	va_start(params, format);
	vsnprintf(out_buf,250,format,params);
	va_end (params);

	printf("T_TRACE:%s\n", out_buf);
	//uart_printf("T_TRACE:%s\n", out_buf);
	#endif
}
#endif

/**
 * Handler of RIL_MODEMADP_MM_RSP_NW_STATE_CHANGED
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Nw_State_Changed_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Nw_State_Changed_t   *  NwStateChanged;

    RIL_Errno       RilResult;
    RIL_RegState    RegState;

    NwStateChanged = (RIL_Modemadp_Nw_State_Changed_t *)Msg;

    printf("%s simId %d\n", __FUNCTION__, NwStateChanged->SimId);
    //handled only in IDLE state
    if(MODEM_NW_STATE_IDLE != g_ModemNwMng[NwStateChanged->SimId].ModemState) {
        return;
    }

    //Hal_Mem_Free(g_ModemNwMng[NwStateChanged->SimId].PlmnInfo.PlmnName);

    //get CS registration state
    RilResult = Ril_MM_Request_Voice_Registration_state(&RegState, NwStateChanged->SimId);

    if(RIL_E_SUCCESS != RilResult) {
        MMI_ModemAdp_printf("Get CS reg state failed: %d\n", RilResult);
        return;
    }

    /* <stat>: integer type; circuit mode registration status
        * 0: not registered, MT is not currently searching a new operator to register to
        * 1: registered, home network
        * 2: not registered, but MT is currently searching a new operator to register to
        * 3: registration denied
        * 4: unknown
        * 5: registered, roaming
        * 6: registered for "SMS only", home network (applicable only when <AcT> indicates E-UTRAN)
        * 7: registered for "SMS only", roaming (applicable only when <AcT> indicates E-UTRAN)
        * 8: attached for emergency bearer services only (see NOTE 2) (not applicable)
        * 9: registered for "CSFB not preferred", home network (applicable only when <AcT> indicates E-UTRAN)
        * 10: registered for "CSFB not preferred", roaming (applicable only when <AcT> indicates E-UTRAN)
        * 11: only emergency services are available.
        * 12: registeration denied in roaming
        * 13: sync done in LTE roaming network
        * 14: ecall inactive */
    if((1 == RegState.stat) || (9 == RegState.stat)) {
        g_ModemNwMng[NwStateChanged->SimId].CsRegState = MODEM_NW_CS_REG_HOME;
    }
    if(6 == RegState.stat) {
        g_ModemNwMng[NwStateChanged->SimId].CsRegState = MODEM_NW_CS_REG_HOME_SMS_ONLY;
    } else if((5 == RegState.stat) || (10 == RegState.stat)) {
        g_ModemNwMng[NwStateChanged->SimId].CsRegState = MODEM_NW_CS_REG_ROAMING;
    } else if(7 == RegState.stat) {
        g_ModemNwMng[NwStateChanged->SimId].CsRegState = MODEM_NW_CS_REG_ROAMING_SMS_ONLY;
    } else if((8 == RegState.stat) || (11 == RegState.stat)) {
        g_ModemNwMng[NwStateChanged->SimId].CsRegState = MODEM_NW_CS_LIMITED;
    } else {
        g_ModemNwMng[NwStateChanged->SimId].CsRegState = MODEM_NW_CS_NOT_REG;
    }
}

/**
 * Handler of RIL_MODEMADP_MM_RSP_SIG_STRENGTH
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Sig_Strength_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Sig_Strength_t   *  SigStrength;
    MMI_MODEM_SIGNAL_BAR               SignalBar;

    SigStrength = (RIL_Modemadp_Sig_Strength_t *)Msg;

    //handled only in IDLE state
    if(MODEM_NW_STATE_IDLE != g_ModemNwMng[SigStrength->SimId].ModemState) {
        printf("$$$$$%s, Modemstate %d\n", __FUNCTION__, g_ModemNwMng[SigStrength->SimId].ModemState);
        return;
    }

    if(MMI_MODEM_PLMN_RAT_UNKNOW == g_ModemNwMng[SigStrength->SimId].PlmnInfo.PlmnRat) {
        SigStrength->SignalStrength = 99;
    }

    if(MMI_MODEM_PLMN_RAT_LTE == g_ModemNwMng[SigStrength->SimId].PlmnInfo.PlmnRat) {
        SigStrength->SignalStrength = SigStrength->LTE_SignalStrength.rsrp / 2;
    } else {
        SigStrength->SignalStrength = SigStrength->GW_SignalStrength.signalStrength;
    }

    if((99 == SigStrength->SignalStrength) || (SigStrength->SignalStrength < MODEM_NW_SIGNAL_LEVEL_0)) {  //3: -107dBm
        SignalBar = MMI_MODEM_SIGNAL_BAR_0;
    } else if(SigStrength->SignalStrength >= MODEM_NW_SIGNAL_LEVEL_FULL) {
        SignalBar = MMI_MODEM_SIGNAL_BAR_5;
    } else if(SigStrength->SignalStrength >= MODEM_NW_SIGNAL_LEVEL_4) {
        SignalBar = MMI_MODEM_SIGNAL_BAR_4;
    } else if(SigStrength->SignalStrength >= MODEM_NW_SIGNAL_LEVEL_3) {
        SignalBar = MMI_MODEM_SIGNAL_BAR_3;
    } else if(SigStrength->SignalStrength >= MODEM_NW_SIGNAL_LEVEL_2) {
        SignalBar = MMI_MODEM_SIGNAL_BAR_2;
    } else {
        SignalBar = MMI_MODEM_SIGNAL_BAR_1;
    }

    //if not CS registered state, notify EMPTY BAR IND
    /* if((MODEM_NW_CS_REG_HOME != g_ModemNwMng[SigStrength->SimId].CsRegState) */
    /*         && (MODEM_NW_CS_REG_ROAMING != g_ModemNwMng[SigStrength->SimId].CsRegState)) { */
    /*     SignalBar = MMI_MODEM_SIGNAL_BAR_0; */
    /* } */

    printf("%s signal %d bar %d PlmnRat %d\n", __FUNCTION__, SigStrength->SignalStrength, SignalBar,g_ModemNwMng[SigStrength->SimId].PlmnInfo.PlmnRat);
    g_ModemNwMng[SigStrength->SimId].SignalBar = SignalBar;
    if(99 == SigStrength->SignalStrength) {
        g_ModemSimMng.RegHomePlmn[SigStrength->SimId].signalstrength = 0;
    } else {
        g_ModemSimMng.RegHomePlmn[SigStrength->SimId].signalstrength = SigStrength->SignalStrength;
    }
    MMI_Modem_Signal_Bar_Ind(MMI_ModemAdp_Get_UI_SimId(SigStrength->SimId), SignalBar);
}

/**
 * Handler of RIL_MODEMADP_MM_RSP_NITZ_TIME_RECEIVED
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Nitz_Time_Received_Handler(UI_Inter_Msg_t * Msg)
{
#if USE_LV_WATCH_SETTING_DATETIME != 0
	RIL_Modemadp_Nitz_Time_t * NitzTimeReceived;
    MMI_Modem_Time_Info_t * TimeInfo;
    UINT8   i = 0;

    NitzTimeReceived = (RIL_Modemadp_Nitz_Time_t *)Msg;

    //handled only in IDLE state
    if(MODEM_NW_STATE_IDLE != g_ModemNwMng[NitzTimeReceived->SimId].ModemState) {
        return;
    }

    //get date & time info from NITZ string
    //+NITZ: 0,+32,11/08/02,09:27:39
    /*
    +NITZ:<DST>[,<sign><timezone>[,<year>/<month>/<day>,<hour>:<minute>:<second>]]
        Defined Values
        <year>: The integer value is in range [00...99].
        <month>: The integer value is in range [1...12].
        <day>: The integer value is in range [1...31].
        <hour>: The integer value is in range [0...59]
        <minute>: The integer value is in range [0...59]
        <second>: The integer value is in range [0...59]
        <sign>: a char value
            C local time zone is passive.
            C local time zone is negative.
        <time zone>: time zone
        <DST>:Daylight Saving Time (DST),also summer time.
            0 no adjustment
            1 +1 hour
            2 +2 hour
    */
    // NitzStringLen = strlen(NitzTimeReceived->NitzTimeData);

    //find the start postion of "year"
    // ril change the position of para for android to ""yy/mm/dd,hh:mm:ss(+/-)tz,dt" in UTC"
    /* for(i = 0; i < NitzStringLen; i++) { */
    /*     if(NitzTimeReceived->NitzTimeData[i] == ',') { */
    /*         CommaCnt++; */
    /*         if(2 == CommaCnt) { */
    /*             i++; */
    /*             break; */
    /*         } */
    /*     } */
    /* } */

    //year/month/day
    TimeInfo = (MMI_Modem_Time_Info_t *)Hal_Mem_Alloc(sizeof(MMI_Modem_Time_Info_t));
    Hal_Mem_Set(TimeInfo, 0, sizeof(MMI_Modem_Time_Info_t));

    if(NitzTimeReceived->NitzTimeData[i] == '"') i++;   // "
    TimeInfo->tm_year = 2000 + (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
    TimeInfo->tm_year += NitzTimeReceived->NitzTimeData[i++] - 0x30;
    i++;
    TimeInfo->tm_mon = (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
    TimeInfo->tm_mon += NitzTimeReceived->NitzTimeData[i++] - 0x30;
    i++;
    TimeInfo->tm_mday = (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
    TimeInfo->tm_mday += NitzTimeReceived->NitzTimeData[i++] - 0x30;
    if(NitzTimeReceived->NitzTimeData[i] == '"') i++;   // "
    //hour:min:sec
    i++; // ,
    if(NitzTimeReceived->NitzTimeData[i] == '"') i++;   // "
    TimeInfo->tm_hour = (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
    TimeInfo->tm_hour += NitzTimeReceived->NitzTimeData[i++] - 0x30;
    i++;
    TimeInfo->tm_min = (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
    TimeInfo->tm_min += NitzTimeReceived->NitzTimeData[i++] - 0x30;
    i++;
    TimeInfo->tm_sec = (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
    TimeInfo->tm_sec += NitzTimeReceived->NitzTimeData[i++] - 0x30;
    if(NitzTimeReceived->NitzTimeData[i] == '"') i++;   // "

    if(NitzTimeReceived->NitzTimeData[i++] == '+') {
        if(NitzTimeReceived->NitzTimeData[i + 1] != ',') {
            TimeInfo->timezone = (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
        }
        TimeInfo->timezone += NitzTimeReceived->NitzTimeData[i++] - 0x30;
    } else {
        if(NitzTimeReceived->NitzTimeData[i + 1] != ',') {
            TimeInfo->timezone -= (NitzTimeReceived->NitzTimeData[i++] - 0x30) * 10;
        }
        TimeInfo->timezone -= NitzTimeReceived->NitzTimeData[i++] - 0x30;
    }

    MMI_Modem_Time_Update_Ind(TimeInfo);
#else
    MMI_Modem_Time_Update_Ind(NULL);
#endif
}

/**
 * Handler of RIL_MODEMADP_MM_RSP_QUERY_AVAILABLE_NETWORKS
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Query_Available_Nw_Cnf_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Query_Available_Nw_Cnf_t  *  QueryAvailNwCnf;
    MMI_Modem_Plmn_Search_Cnf_t         *        PlmnSearchCnf;

    UINT8   i;
    UINT8   PlmnNameLen = 0;

    QueryAvailNwCnf = (RIL_Modemadp_Query_Available_Nw_Cnf_t *)Msg;

    //handled only in PLMN_SEARCHING state
    if(MODEM_NW_STATE_PLMN_SEARHING != g_ModemNwMng[QueryAvailNwCnf->SimId].ModemState) {
        if(NULL != QueryAvailNwCnf->OperInfo.pOperInfoSub) {
            Hal_Mem_Free(QueryAvailNwCnf->OperInfo.pOperInfoSub);
        }
        return;
    }

    PlmnSearchCnf = (MMI_Modem_Plmn_Search_Cnf_t *)Hal_Mem_Alloc(sizeof(MMI_Modem_Plmn_Search_Cnf_t));
    Hal_Mem_Set(PlmnSearchCnf, 0, sizeof(MMI_Modem_Plmn_Search_Cnf_t));
    PlmnSearchCnf->simId = MMI_ModemAdp_Get_UI_SimId(QueryAvailNwCnf->SimId);
    if(RIL_E_SUCCESS != QueryAvailNwCnf->result) {
        PlmnSearchCnf->PlmnCnt = 0;
    } else {
        PlmnSearchCnf->PlmnCnt = QueryAvailNwCnf->OperInfo.operInfoNum;
    }
    if(0 != PlmnSearchCnf->PlmnCnt) {
        PlmnSearchCnf->PlmnList = (MMI_Modem_PLMN_Info *)Hal_Mem_Alloc(sizeof(MMI_Modem_PLMN_Info) * PlmnSearchCnf->PlmnCnt);
        Hal_Mem_Set(PlmnSearchCnf->PlmnList, 0, sizeof(MMI_Modem_PLMN_Info) * PlmnSearchCnf->PlmnCnt);
        for(i = 0; i < PlmnSearchCnf->PlmnCnt; i++) {
            PlmnNameLen = strlen(QueryAvailNwCnf->OperInfo.pOperInfoSub[i].operShortStr);
            if(0 != PlmnNameLen) {
                Hal_Mem_Copy(&(PlmnSearchCnf->PlmnList[i].PlmnName),
                             QueryAvailNwCnf->OperInfo.pOperInfoSub[i].operShortStr,
                             MMI_MAX_PLMN_NAME_LEN);
            } else {
                strncpy(PlmnSearchCnf->PlmnList[i].PlmnName, "Unknown", 8);
            }
            strncpy(PlmnSearchCnf->PlmnList[i].PlmnId, QueryAvailNwCnf->OperInfo.pOperInfoSub[i].operNumStr, strlen(QueryAvailNwCnf->OperInfo.pOperInfoSub[i].operNumStr) + 1);

            /* <AcT> integer type; access technology selected:
                * 0: GSM
                * 1: GSM Compact
                * 2: UTRAN
                * 3: GSM EGPRS
                * 4: UTRAN HSDPA
                * 5: UTRAN HSUPA
                * 6: UTRAN HSPA
                * 7: EUTRAN */
            if((0 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act) || (1 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act)
                    || (3 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act)) {
                PlmnSearchCnf->PlmnList[i].PlmnRat = MMI_MODEM_PLMN_RAT_GSM;
            } else if((2 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act) || (4 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act)
                      || (5 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act) || (6 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act)) {
                PlmnSearchCnf->PlmnList[i].PlmnRat = MMI_MODEM_PLMN_RAT_UMTS;
            } else if(7 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].act) {
                PlmnSearchCnf->PlmnList[i].PlmnRat = MMI_MODEM_PLMN_RAT_LTE;
            }

            /* <stat>: integer type
                 * 0 unknown
                 * 1 available
                 * 2 current
                 * 3 forbidden */
            if(1 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].state) {
                PlmnSearchCnf->PlmnList[i].PlmnStat = MMI_MODEM_PLMN_AVAILABLE;
            } else if(2 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].state) {
                PlmnSearchCnf->PlmnList[i].PlmnStat = MMI_MODEM_PLMN_CURRENT;
            } else if(3 == QueryAvailNwCnf->OperInfo.pOperInfoSub[i].state) {
                PlmnSearchCnf->PlmnList[i].PlmnStat = MMI_MODEM_PLMN_FORBIDDEN;
            } else { //unknown
                UI_Log_Out(UI_SUB_MODULE_MODEM_ADP, UI_LOG_LEVEL_ERROR, "Query get unknown state PLMN!%d\n", PlmnSearchCnf->PlmnList[i].PlmnStat);
            }
        }
    }

    MMI_Modem_Plmn_Search_Cnf(PlmnSearchCnf);
    g_ModemNwMng[QueryAvailNwCnf->SimId].ModemState = MODEM_NW_STATE_IDLE;

    if(NULL != QueryAvailNwCnf->OperInfo.pOperInfoSub) {
        Hal_Mem_Free(QueryAvailNwCnf->OperInfo.pOperInfoSub);
    }
}




/**
 * Handler of RIL_MODEMADP_MM_RSP_SET_NW_SEL_MANUAL
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Set_Nw_Sel_Manual_Cnf_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Set_Nw_Sel_Manual_Cnf_t   *  SetNwSelManualCnf;
    MMI_Modem_Plmn_Search_Cnf_t         *        PlmnSearchCnf;

    SetNwSelManualCnf = (RIL_Modemadp_Set_Nw_Sel_Manual_Cnf_t *)Msg;

    //handled only in IDLE state
    if(MODEM_NW_STATE_PLMN_SEARHING != g_ModemNwMng[SetNwSelManualCnf->SimId].ModemState) {
        return;
    }

    PlmnSearchCnf = (MMI_Modem_Plmn_Search_Cnf_t *)Hal_Mem_Alloc(sizeof(MMI_Modem_Plmn_Search_Cnf_t));
    Hal_Mem_Set(PlmnSearchCnf, 0, sizeof(MMI_Modem_Plmn_Search_Cnf_t));
    PlmnSearchCnf->simId = MMI_ModemAdp_Get_UI_SimId(SetNwSelManualCnf->SimId);
    if(RIL_E_SUCCESS == SetNwSelManualCnf->Result) {
        PlmnSearchCnf->PlmnCnt = 1;
        PlmnSearchCnf->PlmnList = (MMI_Modem_PLMN_Info *)Hal_Mem_Alloc(sizeof(MMI_Modem_PLMN_Info));
        memset(PlmnSearchCnf->PlmnList, 0, sizeof(MMI_Modem_PLMN_Info));
        strncpy(PlmnSearchCnf->PlmnList[0].PlmnName, g_ModemNwMng[SetNwSelManualCnf->SimId].PlmnInfo.PlmnName, strlen(g_ModemNwMng[SetNwSelManualCnf->SimId].PlmnInfo.PlmnName));
        PlmnSearchCnf->PlmnList[0].PlmnRat = g_ModemNwMng[SetNwSelManualCnf->SimId].PlmnInfo.PlmnRat;
        PlmnSearchCnf->PlmnList[0].PlmnStat = g_ModemNwMng[SetNwSelManualCnf->SimId].PlmnInfo.PlmnState;
    } else {
        PlmnSearchCnf->PlmnCnt = 0;
    }

    MMI_Modem_Plmn_Search_Cnf(PlmnSearchCnf);
    g_ModemNwMng[SetNwSelManualCnf->SimId].ModemState = MODEM_NW_STATE_IDLE;

}

/**
 * Handler of RIL_MODEMADP_MM_RSP_RAT_CHANGED
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Rat_Changed_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Rat_Changed_t  * RatChanged;

    RatChanged = (RIL_Modemadp_Rat_Changed_t *)Msg;

    if((0 == RatChanged->RegInfo.stat)
        || (2 == RatChanged->RegInfo.stat)
        || (3 == RatChanged->RegInfo.stat)
        || (4 == RatChanged->RegInfo.stat)
        || (11 == RatChanged->RegInfo.stat)
        || (13 == RatChanged->RegInfo.stat))
    {
        MMI_ModemAdp_printf("Unknown RAT: %d,Stat: %d\n", RatChanged->RegInfo.rt,RatChanged->RegInfo.stat);
        g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_UNKNOW;
        return;
    }

    switch(RatChanged->RegInfo.rt) {
        case RADIO_TECH_EDGE:
        case RADIO_TECH_GPRS:
            g_ModemNwMng[RatChanged->SimId].GsmRegState = 1;
            g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_GSM;
            break;
        case RADIO_TECH_GSM:
            g_ModemNwMng[RatChanged->SimId].GsmRegState = 0;
            g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_GSM;
            break;

        case RADIO_TECH_UMTS:
        case RADIO_TECH_HSDPA:
        case RADIO_TECH_HSUPA:
        case RADIO_TECH_HSPA:
        case RADIO_TECH_HSPAP:
        case RADIO_TECH_TD_SCDMA:
            if(RADIO_TECH_UMTS == RatChanged->RegInfo.rt) {
                g_ModemNwMng[RatChanged->SimId].UmtsRegState = 0;
            } else if(RADIO_TECH_TD_SCDMA == RatChanged->RegInfo.rt) {
                g_ModemNwMng[RatChanged->SimId].UmtsRegState = 1;
            } else {
                g_ModemNwMng[RatChanged->SimId].UmtsRegState = 2;
            }
            g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_UMTS;
            break;

        case RADIO_TECH_LTE:
        case RADIO_TECH_LTE_CA:
            g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_LTE;
            break;

        case RADIO_TECH_IS95A:
        case RADIO_TECH_IS95B:
        case RADIO_TECH_1xRTT:
        case RADIO_TECH_EVDO_0:
        case RADIO_TECH_EVDO_A:
        case RADIO_TECH_EVDO_B:
        case RADIO_TECH_EHRPD:
        case RADIO_TECH_IWLAN:
            UI_Log_Out(UI_SUB_MODULE_MODEM_ADP, UI_LOG_LEVEL_ERROR, "Unsupported RAT: %d\n", RatChanged->RegInfo.rt);
            return;

        default:
            MMI_ModemAdp_printf("Unknown RAT: %d\n", RatChanged->RegInfo.rt);
            g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_UNKNOW;
            return;
    }
    g_ModemSimMng.RegHomePlmn[RatChanged->SimId].lac = RatChanged->RegInfo.lac;
    g_ModemSimMng.RegHomePlmn[RatChanged->SimId].cid = RatChanged->RegInfo.cid;

    // Get SMS center number
    if((MMI_MODEM_PLMN_RAT_GSM == g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat)
            || (MMI_MODEM_PLMN_RAT_UMTS == g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat)
            || (MMI_MODEM_PLMN_RAT_LTE  == g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat)) {
        if(NULL == g_ModemSmsMng.pSendSms_Smsc[MMI_ModemAdp_Get_UI_SimId(RatChanged->SimId)]) {
            Ril_MSG_Request_Get_Smsc_Addr(RatChanged->SimId);
        }

        MMI_Modem_Get_Plmn_Req(MMI_ModemAdp_Get_UI_SimId(RatChanged->SimId));

        if(NULL == g_ModemSimMng.Iccid[MMI_ModemAdp_Get_UI_SimId(RatChanged->SimId)]) {
            MMI_Modem_Get_Iccid_Req(MMI_ModemAdp_Get_UI_SimId(RatChanged->SimId));
        }

        if(NULL == g_ModemSimMng.Msisdn[MMI_ModemAdp_Get_UI_SimId(RatChanged->SimId)]) {
            MMI_Modem_Get_Msisdn_Req(MMI_ModemAdp_Get_UI_SimId(RatChanged->SimId));
        }
#if 0
        if((MODEM_NW_LTE_MODE_UNKOWN == g_ModemNwMng[RatChanged->SimId].LteOnlyFlg)
           && (MMI_MODEM_PLMN_RAT_LTE == g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat)
           && ((1 == RatChanged->RegInfo.stat)
               || (5 == RatChanged->RegInfo.stat)
               || (9 == RatChanged->RegInfo.stat)
               || (10 == RatChanged->RegInfo.stat))) {
            Ril_MM_Request_Get_Band(NULL, RatChanged->SimId);
        }
#endif
        Ril_MM_Request_Get_Volte_State(RatChanged->SimId);
    }

    // set roaming statues
    if((RatChanged->RegInfo.stat == 5) || (RatChanged->RegInfo.stat == 7) ||
       (RatChanged->RegInfo.stat == 10)) {
        g_ModemNwMng[RatChanged->SimId].RoamingState = 1;
    } else {
        g_ModemNwMng[RatChanged->SimId].RoamingState = 0;
    }

    printf("~~~~~%s, Rat %d, stat %d, roaming %d\n", __FUNCTION__,
           g_ModemNwMng[RatChanged->SimId].PlmnInfo.PlmnRat,
           RatChanged->RegInfo.stat,
           g_ModemNwMng[RatChanged->SimId].RoamingState);
    //notify UI: current not needed due to RAT info not displayed on status bar
}
/**
 * Handler of RIL_MODEMADP_MM_RSP_OPERATOR
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Rsp_Operator_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Rsp_Operator_t   *  pMsg;

    UINT8           PlmnNameLen = 0;
    INT8      *      Operator;

    pMsg = (RIL_Modemadp_Rsp_Operator_t *)Msg;
    //only one operator will be reported in this message
    g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnState = (MMI_MODEM_PLMN_STAT)pMsg->pOperInfo->pOperInfoSub->state;

    printf("PlmnState is %d in MMI_ModemAdp_Rsp_Operator_Handler\n", pMsg->pOperInfo->pOperInfoSub->state);

    /* <AcT> integer type; access technology selected:
        * 0: GSM
        * 1: GSM Compact
        * 2: UTRAN
        * 3: GSM EGPRS
        * 4: UTRAN HSDPA
        * 5: UTRAN HSUPA
        * 6: UTRAN HSPA
        * 7: EUTRAN */
    printf("act is %d in MMI_ModemAdp_Rsp_Operator_Handler\n", pMsg->pOperInfo->pOperInfoSub->act);
    if((0 == pMsg->pOperInfo->pOperInfoSub->act) || (1 == pMsg->pOperInfo->pOperInfoSub->act)
            || (3 == pMsg->pOperInfo->pOperInfoSub->act)) {
        g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_GSM;
    } else if((2 == pMsg->pOperInfo->pOperInfoSub->act) || (4 == pMsg->pOperInfo->pOperInfoSub->act)
              || (5 == pMsg->pOperInfo->pOperInfoSub->act) || (6 == pMsg->pOperInfo->pOperInfoSub->act)) {
        g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_UMTS;
    } else if(7 == pMsg->pOperInfo->pOperInfoSub->act) {
        g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnRat = MMI_MODEM_PLMN_RAT_LTE;
    }

    //UI only needs short name
    if(NULL != g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName) {
        Hal_Mem_Free(g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName);
    }
    PlmnNameLen = strlen(pMsg->pOperInfo->pOperInfoSub->operShortStr);
    if(0 != PlmnNameLen) {
        g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName = Hal_Mem_Alloc(PlmnNameLen + 1);
        memset(g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName, 0, PlmnNameLen + 1);
        memcpy(g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName, pMsg->pOperInfo->pOperInfoSub->operShortStr, PlmnNameLen);
    } else {
        g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName = Hal_Mem_Alloc(10);
        memset(g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName, 0, 10);
        strncpy(g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName, "Unknown", 10);
    }
    printf("operShortStr is %d in MMI_ModemAdp_Rsp_Operator_Handler\n", pMsg->pOperInfo->pOperInfoSub->operShortStr);

    //if not current PLMN, notify to UI
    if(MMI_MODEM_PLMN_CURRENT != pMsg->pOperInfo->pOperInfoSub->state) {
        PlmnNameLen = strlen(g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName);
        Operator = Hal_Mem_Alloc(PlmnNameLen + 1);
        strncpy(Operator, g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnName, PlmnNameLen + 1);
        MMI_Modem_Operator_Ind(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), Operator,
                               g_ModemNwMng[pMsg->SimId].PlmnInfo.PlmnRat);
    }
    Hal_Mem_Free(pMsg->pOperInfo->pOperInfoSub);
    Hal_Mem_Free(pMsg->pOperInfo);
}

/**
 * Handler of RIL_MODEMADP_MM_RSP_IMS_REG_STATE
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Rsp_Ims_Reg_State_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Ims_Reg_State_t   *  pMsg;
    pMsg = (RIL_Modemadp_Ims_Reg_State_t *)Msg;

    g_ModemNwMng[pMsg->SimId].ImsRegState = pMsg->ImsRegState;

    //config manual APN after IMS registered for LTE
    if(pMsg->ImsRegState) {
        if(1 == g_ModemNwMng[pMsg->SimId].ConfApnOnPowerup) {
            g_ModemNwMng[pMsg->SimId].ConfApnOnPowerup = 0;

            MMI_Modem_Apn_Info_t *ApnInfo = &g_ModemNwMng[pMsg->SimId].ApnInfo;
            if(ApnInfo->apnflg) {
                MMI_ModemAdp_Config_Apn(ApnInfo->apn, ApnInfo->iptype);
            }
        }
        if(0 == g_ModemNwMng[pMsg->SimId].Ccfc_Ccwa_ReadFlg)
        {
#if USE_LV_WATCH_CCWA != 0
            MMI_Modem_Call_Wait_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId));
#endif
#if USE_LV_WATCH_CCFC != 0
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_NO_CONDITION);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_BUSY);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_NOT_REACH);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_NO_ANSWER);
#endif
        }
    }
}

/**
 * Handler of RIL_MODEMADP_MM_RSP_GET_BAND
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Mm_Rsp_Get_Band_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Rsp_Get_Band_t   *  pMsg;
    pMsg = (RIL_Modemadp_Rsp_Get_Band_t *)Msg;
    printf("LteOnlyFlg is %d in MMI_ModemAdp_Mm_Rsp_Get_Band_Handler\n", g_ModemNwMng[pMsg->SimId].LteOnlyFlg);
    printf("IMSVopsState is %d in MMI_ModemAdp_Mm_Rsp_Get_Band_Handler\n", g_ModemNwMng[pMsg->SimId].IMSVopsState);
    printf("ImsVopsRepFlg is %d in MMI_ModemAdp_Mm_Rsp_Get_Band_Handler\n", g_ModemNwMng[pMsg->SimId].ImsVopsRepFlg);
    printf("ucMode is %d in MMI_ModemAdp_Mm_Rsp_Get_Band_Handler\n", pMsg->ucMode);
    printf("ucGsmBand=%d,usLteBandH=%d,ulLteBandL=%ld \n", pMsg->ucGsmBand,pMsg->usLteBandH,pMsg->ulLteBandL);

    g_ModemNwMng[pMsg->SimId].GetBandInfo.ucGsmBand = pMsg->ucGsmBand;
    g_ModemNwMng[pMsg->SimId].GetBandInfo.usLteBandH = pMsg->usLteBandH;
    g_ModemNwMng[pMsg->SimId].GetBandInfo.ulLteBandL = pMsg->ulLteBandL;

    if(MODEM_NW_LTE_MODE_UNKOWN == g_ModemNwMng[pMsg->SimId].LteOnlyFlg) {
        if((MODEM_NW_IMS_VOPS_OFF == g_ModemNwMng[pMsg->SimId].IMSVopsState)
                && (MODEM_NW_IMS_VOPS_NOT_REPORTED == g_ModemNwMng[pMsg->SimId].ImsVopsRepFlg)
                && (5 == pMsg->ucMode)) {
            //send msg to ui
            MMI_ModemAdp_Check_China_Operator_t *Operator = (MMI_ModemAdp_Check_China_Operator_t *)Hal_Mem_Alloc(sizeof(MMI_ModemAdp_Check_China_Operator_t));
            Hal_Mem_Set(Operator,0,sizeof(MMI_ModemAdp_Check_China_Operator_t));
            Operator->SimId = MMI_ModemAdp_Get_UI_SimId(pMsg->SimId);
            
            MMI_ModemAdp_Check_China_Operator(Operator);
            if(NULL != Operator->data) {
                MMI_Modem_Ims_Vops_Ind(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId),1);//display send sms
            } else {
                MMI_Modem_Ims_Vops_Ind(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId),0);//display not send sms
            }
            Hal_Mem_Free(Operator);
            g_ModemNwMng[pMsg->SimId].ImsVopsRepFlg = MODEM_NW_IMS_VOPS_HAS_BEEN_REPORTED;
        }
    }
    if(5 != pMsg->ucMode) {
        g_ModemNwMng[pMsg->SimId].LteOnlyFlg = MODEM_NW_NOT_LTE_ONLY;
    } else {
        g_ModemNwMng[pMsg->SimId].LteOnlyFlg = MODEM_NW_LTE_ONLY;
    }
    Ril_MM_Request_Get_ImsVops(pMsg->SimId);
}

/**
 * Handler of RIL_MODEMADP_MM_IMS_VOPS
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Mm_Ims_Vops_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Ims_Vops_Ind_t   *  pMsg;
    pMsg = (RIL_Modemadp_Ims_Vops_Ind_t *)Msg;
    printf("LteOnlyFlg is %d in MMI_ModemAdp_Mm_Ims_Vops_Handler\n", g_ModemNwMng[pMsg->SimId].LteOnlyFlg);
    printf("IMSVopsState is %d in MMI_ModemAdp_Mm_Ims_Vops_Handler\n", g_ModemNwMng[pMsg->SimId].IMSVopsState);
    printf("ImsVopsRepFlg is %d in MMI_ModemAdp_Mm_Ims_Vops_Handler\n", g_ModemNwMng[pMsg->SimId].ImsVopsRepFlg);
    printf("volte is %d in MMI_ModemAdp_Mm_Ims_Vops_Handler\n", pMsg->volte);
    if(MODEM_NW_IMS_VOPS_NULL == g_ModemNwMng[pMsg->SimId].IMSVopsState) {
        if((MODEM_NW_LTE_ONLY == g_ModemNwMng[pMsg->SimId].LteOnlyFlg)
                && (MODEM_NW_IMS_VOPS_NOT_REPORTED == g_ModemNwMng[pMsg->SimId].ImsVopsRepFlg)
                && (0 == pMsg->volte)) {
            //send msg to ui
            MMI_ModemAdp_Check_China_Operator_t *Operator = (MMI_ModemAdp_Check_China_Operator_t *)Hal_Mem_Alloc(sizeof(MMI_ModemAdp_Check_China_Operator_t));
            Hal_Mem_Set(Operator,0,sizeof(MMI_ModemAdp_Check_China_Operator_t));
            Operator->SimId = MMI_ModemAdp_Get_UI_SimId(pMsg->SimId);
            
            MMI_ModemAdp_Check_China_Operator(Operator);
            if(NULL != Operator->data) {
                MMI_Modem_Ims_Vops_Ind(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId),1);//display send sms
            } else {
                MMI_Modem_Ims_Vops_Ind(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId),0);//display not send sms
            }
            Hal_Mem_Free(Operator);
            g_ModemNwMng[pMsg->SimId].ImsVopsRepFlg = MODEM_NW_IMS_VOPS_HAS_BEEN_REPORTED;
        }
    }
    if(0 == pMsg->volte) {
        g_ModemNwMng[pMsg->SimId].IMSVopsState = MODEM_NW_IMS_VOPS_OFF;
        if(0 == g_ModemNwMng[pMsg->SimId].Ccfc_Ccwa_ReadFlg)
        {
#if USE_LV_WATCH_CCWA != 0
            MMI_Modem_Call_Wait_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId));
#endif
#if USE_LV_WATCH_CCFC != 0
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_NO_CONDITION);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_BUSY);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_NOT_REACH);
            MMI_Modem_Call_Forward_Query_Req(MMI_ModemAdp_Get_UI_SimId(pMsg->SimId), MMI_MODEM_CALL_FORWARD_NO_ANSWER);
#endif
        }
    } else {
        g_ModemNwMng[pMsg->SimId].IMSVopsState = MODEM_NW_IMS_VOPS_ON;
    }
}

/**
 * Handler of MMI_MODEMADP_SET_BAND_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Set_Band_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modem_Set_Band_Req_t * SetBandReq = NULL;
    RIL_MM_BAND_Info     *    pSetBandReq = NULL;
    RIL_Errno                RilResult;

    SetBandReq = (MMI_Modem_Set_Band_Req_t *)Msg;
    pSetBandReq = (RIL_MM_BAND_Info *)Hal_Mem_Alloc(sizeof(RIL_MM_BAND_Info));
    Hal_Mem_Set(pSetBandReq, 0, sizeof(RIL_MM_BAND_Info));

    pSetBandReq->ucMode = SetBandReq->mode;
    pSetBandReq->ucGsmBand = SetBandReq->gsmBand;
    pSetBandReq->usLteBandH = SetBandReq->lteBandH;
    pSetBandReq->ulLteBandL = SetBandReq->lteBandL;

    printf("modemada,bandmode:%d, gsmband: 0x%x, ltebandh: 0x%x, ltebandl: 0x%x\n", SetBandReq->mode, SetBandReq->gsmBand, SetBandReq->lteBandH, SetBandReq->lteBandL);

    RilResult   = Ril_MM_Request_Set_Band(pSetBandReq, MMI_ModemAdp_Get_Ril_SimId(modem_sim_id));
    Hal_Mem_Free(pSetBandReq);
    if(RIL_E_SUCCESS == RilResult) {
        // nothing to do
    } else {
        UI_Log_Out(UI_SUB_MODULE_MODEM_ADP, UI_LOG_LEVEL_ERROR,
                   "Ril set band failed: %d\n", RilResult);
    }
}

/**
 * get SimId in plmn searching
 * param (in) void:
 * return  MMI_MODEM_SIM_ID
 */
MMI_MODEM_SIM_ID  MMI_ModemAdp_Get_Sim_in_Plmn_Searching(VOID)
{
    if(MODEM_NW_STATE_PLMN_SEARHING == g_ModemNwMng[MMI_MODEM_SIM_1].ModemState) {
        return MMI_MODEM_SIM_1;
    } else {
        return MMI_MODEM_SIM_2;
    }
}

VOID MMI_ModemAdp_Radio_Power_Status_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Radio_Power_Status_t * pMsg = (RIL_Modemadp_Radio_Power_Status_t *)Msg;

    MMI_MODEM_SIM_ID SimId = MMI_ModemAdp_Get_UI_SimId(pMsg->SimId);
    if(NULL == g_ModemNwMng[SimId].RadioPowerCb) {
        UI_Log_Out(UI_SUB_MODULE_MODEM_ADP, UI_LOG_LEVEL_ERROR,
                   "%s: Callback function is NULL for SIM%d!\n", __FUNCTION__, SimId);
        return;
    }
    g_ModemNwMng[SimId].RadioPowerCb(pMsg->Status);
    g_ModemNwMng[SimId].RadioPowerCb = NULL;
}

/**
 * Handler of MMI_MODEMADP_GET_CALI_INFO_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Get_Cali_Info_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modemadp_Get_Cali_Info_Req_t * pGetCaliInfoReq = NULL;
    RIL_SIM_ID SimId = RIL_SIM_1;
    INT8 GsmCal[8] = "R,GsmCal";
    INT8 GsmTest[9] = "R,GsmTest";
    INT8 LteCal[8] = "R,LteCal";
    INT8 LteTest[9] = "R,LteTest";
    INT8 * pGsmCal = NULL;
    INT8 * pGsmTest = NULL;
    INT8 * pLteCal = NULL;
    INT8 * pLteTest = NULL;

    pGetCaliInfoReq = (MMI_Modemadp_Get_Cali_Info_Req_t *)Msg;
    g_ModemNwMng[pGetCaliInfoReq->SimId].CaliRspCnt = 0;
    g_ModemNwMng[pGetCaliInfoReq->SimId].CaliRslt   = 1;
    g_ModemNwMng[pGetCaliInfoReq->SimId].CaliRptFlg = 0;
    SimId = MMI_ModemAdp_Get_Ril_SimId(pGetCaliInfoReq->SimId);

    if(0){//MODEM_NW_LTE_ONLY != g_ModemNwMng[pGetCaliInfoReq->SimId].LteOnlyFlg) {
        // AT*CALINFO=R,GsmCal
        pGsmCal = (INT8 *)Hal_Mem_Alloc(9);
        Hal_Mem_Set(pGsmCal, 0, 9);
        Hal_Mem_Copy(pGsmCal, GsmCal, 8);
        Ril_EEM_Request_Cali_Info(pGsmCal, 9, SimId);
        Hal_Mem_Free(pGsmCal);
        //AT*CALINFO=R,GsmTest
        pGsmTest = (INT8 *)Hal_Mem_Alloc(10);
        Hal_Mem_Set(pGsmTest, 0, 10);
        Hal_Mem_Copy(pGsmTest, GsmTest, 9);
        Ril_EEM_Request_Cali_Info(pGsmTest, 10, SimId);
        Hal_Mem_Free(pGsmTest);
    }
    //AT*CALINFO=R,LteCal
    pLteCal = (INT8 *)Hal_Mem_Alloc(9);
    Hal_Mem_Set(pLteCal, 0, 9);
    Hal_Mem_Copy(pLteCal, LteCal, 8);
    Ril_EEM_Request_Cali_Info(pLteCal, 9, SimId);
    Hal_Mem_Free(pLteCal);
	if(0){
    //AT*CALINFO=R,LteTest
    pLteTest = (INT8 *)Hal_Mem_Alloc(10);
    Hal_Mem_Set(pLteTest, 0, 10);
    Hal_Mem_Copy(pLteTest, LteTest, 9);
    Ril_EEM_Request_Cali_Info(pLteTest, 10, SimId);
    Hal_Mem_Free(pLteTest);
	}
}

/**
 * Handler of MMI_MODEMADP_GET_ONEGOCALI_INFO_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Get_onegoCali_Info_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modemadp_Get_onegoCali_Info_Req_t * pGetCaliInfoReq = NULL;
    RIL_SIM_ID SimId = RIL_SIM_1;
    INT8 * pLteCal = NULL;


    pGetCaliInfoReq = (MMI_Modemadp_Get_onegoCali_Info_Req_t *)Msg;
    g_ModemNwMng[pGetCaliInfoReq->SimId].CaliRspCnt = 0;
    g_ModemNwMng[pGetCaliInfoReq->SimId].CaliRslt   = 1;
    g_ModemNwMng[pGetCaliInfoReq->SimId].CaliRptFlg = 0;
    SimId = MMI_ModemAdp_Get_Ril_SimId(pGetCaliInfoReq->SimId);


    pLteCal = (INT8 *)Hal_Mem_Alloc(pGetCaliInfoReq->lens+1);
    Hal_Mem_Set(pLteCal, 0, pGetCaliInfoReq->lens+1);
    Hal_Mem_Copy(pLteCal, pGetCaliInfoReq->cmd, pGetCaliInfoReq->lens);
	printf("%s:  cmd=[%s] len=%d\n", __FUNCTION__,pLteCal,pGetCaliInfoReq->lens);
    Ril_EEM_Request_Cali_Info(pLteCal, pGetCaliInfoReq->lens+1, SimId);
    Hal_Mem_Free(pLteCal);

}

//onego-zly-20230816
VOID MMI_ModemAdp_Save_onegoCali_Info_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modemadp_Get_onegoCali_Info_Req_t * pGetCaliInfoReq = NULL;
    RIL_SIM_ID SimId = RIL_SIM_1;
    
	printf("%s\n", __FUNCTION__);
    Ril_MM_Request_Save_Cali_Info(SimId);
}

/**
 * Handler of MMI_MODEMADP_GET_SN_INFO_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Get_Sn_Info_Req_Handler(UI_Inter_Msg_t * Msg)
{
    MMI_Modemadp_Get_Sn_Info_Req_t * pGetSnInfoReq = NULL;

    pGetSnInfoReq = (MMI_Modemadp_Get_Sn_Info_Req_t *)Msg;
    Ril_DEV_Request_Get_SN(MMI_ModemAdp_Get_Ril_SimId(pGetSnInfoReq->SimId));
}

 /**
 * Handler of MMI_MODEMADP_GET_NETID_INFO_REQ
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Get_NETid_Info_Req_Handler(UI_Inter_Msg_t * Msg)
{
	MMI_Modemadp_Get_NETid_Info_Req_t * pGetCidInfoReq = NULL;

	printf("%s\n", __FUNCTION__);
    pGetCidInfoReq = (MMI_Modemadp_Get_NETid_Info_Req_t *)Msg;
    Ril_DEV_Request_Get_NETID(MMI_ModemAdp_Get_Ril_SimId(pGetCidInfoReq->SimId));
}


/**
 * Handler of RIL_MODEMADP_MM_RSP_CALI_INFO
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Rsp_Cali_Info_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Rsp_Cali_Info_t * pMsg = NULL;
    pMsg = (RIL_Modemadp_Rsp_Cali_Info_t *)Msg;

    printf("%s: resutl = %d, CaliRspCnt = %d\n", __FUNCTION__, pMsg->result, g_ModemNwMng[pMsg->SimId].CaliRspCnt);

    g_ModemNwMng[pMsg->SimId].CaliRspCnt++;

    if(RIL_E_SUCCESS != pMsg->result) {
        g_ModemNwMng[pMsg->SimId].CaliRslt = 0;
    }

    if(MODEM_NW_LTE_ONLY == g_ModemNwMng[pMsg->SimId].LteOnlyFlg) {
        if(1 == g_ModemNwMng[pMsg->SimId].CaliRspCnt) {
            g_ModemNwMng[pMsg->SimId].CaliRptFlg = 1;
        }
    } else {
        if(1 == g_ModemNwMng[pMsg->SimId].CaliRspCnt) {
            g_ModemNwMng[pMsg->SimId].CaliRptFlg = 1;
        }
    }

    if(1 == g_ModemNwMng[pMsg->SimId].CaliRptFlg) {
		g_ModemNwMng[pMsg->SimId].CaliRspCnt = 0;
        MMI_Modem_Cali_Rslt_Ind(g_ModemNwMng[pMsg->SimId].CaliRslt);
    }
}

/**
 * Handler of RIL_MODEMADP_MM_RSP_GET_SN
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Rsp_Get_Sn_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Rsp_Get_Sn_t * pMsg = NULL;
    INT8 * pSn = NULL;

    pMsg = (RIL_Modemadp_Rsp_Get_Sn_t *)Msg;

    if(RIL_E_SUCCESS == pMsg->result) {
        if(NULL != pMsg->pSn) {
            pSn = (INT8 *)Hal_Mem_Alloc(strlen(pMsg->pSn) + 1);
            Hal_Mem_Set(pSn, 0, strlen(pMsg->pSn) + 1);
            Hal_Mem_Copy(pSn, pMsg->pSn, strlen(pMsg->pSn));
        }
    }

    if(NULL != pMsg->pSn) {
        Hal_Mem_Free(pMsg->pSn);
        pMsg->pSn = NULL;
    }

    MMI_Modem_Sn_Info_Ind(pSn);
}

//onego-zly-20231012
/**
 * Handler of RIL_MODEMADP_MM_RSP_GET_NETID
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Rsp_Get_NETid_Handler(UI_Inter_Msg_t * Msg)
{
    RIL_Modemadp_Rsp_Get_NETid_t * pMsg = NULL;
    INT8 * pCid = NULL;

    pMsg = (RIL_Modemadp_Rsp_Get_NETid_t *)Msg;

    if(RIL_E_SUCCESS == pMsg->result) {
        if(NULL != pMsg->pCid) {
            pCid = (INT8 *)Hal_Mem_Alloc(strlen(pMsg->pCid) + 1);
            Hal_Mem_Set(pCid, 0, strlen(pMsg->pCid) + 1);
            Hal_Mem_Copy(pCid, pMsg->pCid, strlen(pMsg->pCid));
        }
    }

    if(NULL != pMsg->pCid) {
        Hal_Mem_Free(pMsg->pCid);
        pMsg->pCid = NULL;
    }
    printf("##interface## %s,pCid=[%s]\n", __FUNCTION__,pCid);
    MMI_Modem_NETid_Info_Ind(pCid);
}


/**
 * Handler of RIL_MODEMADP_MM_RSP_BANDIND
 * param (in) Msg: UI_Inter_Msg_t*
 * return  void
 */
VOID MMI_ModemAdp_Rsp_Bandind_Handler(UI_Inter_Msg_t * Msg)
{
    printf("##interface## %s\n", __FUNCTION__);
    RIL_Modemadp_Rsp_Bandind_t * pMsg = (RIL_Modemadp_Rsp_Bandind_t *)Msg;

    if(NULL != pMsg->BandindString) {
        if(strlen(pMsg->BandindString) > strlen("*BANDIND:")) {
            printf("##interface## %s:BandindString is %s,LteOnlyFlg is %d\n", __FUNCTION__,pMsg->BandindString,g_ModemNwMng[pMsg->SimId].LteOnlyFlg);
            if(MODEM_NW_LTE_MODE_UNKOWN == g_ModemNwMng[pMsg->SimId].LteOnlyFlg) {
                Ril_MM_Request_Get_Band(NULL, pMsg->SimId);
            }
        }
        Hal_Mem_Free(pMsg->BandindString);
        pMsg->BandindString = NULL;
    }
}

/**
 * initiate mmi-modem adaptor NW module
 * param (in) void:
 * return  void
 */
VOID MMI_ModemAdp_NW_Init_Req(VOID)
{
    printf("##interface## %s\n", __FUNCTION__);

    Hal_Mem_Set(g_ModemNwMng, 0, sizeof(Modem_NW_Mng_t) * 2);

    g_ModemNwMng[0].ModemState         = MODEM_NW_STATE_NULL;
    g_ModemNwMng[0].PlmnInfo.PlmnState = MMI_MODEM_PLMN_UNKNOW;
    g_ModemNwMng[0].PlmnInfo.PlmnRat   = MMI_MODEM_PLMN_RAT_UNKNOW;
    g_ModemNwMng[0].CsRegState         = MODEM_NW_CS_NOT_REG;
    g_ModemNwMng[0].SignalBar          = MMI_MODEM_SIGNAL_BAR_0;   // init to invalid level
    g_ModemNwMng[0].PendingMsg = MODEM_NW_MSG_NULL;
    g_ModemNwMng[0].GetCellInfo.lte_cellinfoind = NULL;
    g_ModemNwMng[0].GetCellInfo.gsm_cellinfoind = NULL;
    g_ModemNwMng[0].GetCellInfo.ltecell_list = NULL;
    g_ModemNwMng[0].GetCellInfo.gsmcell_list = NULL;
    g_ModemNwMng[0].ImsRegState = 0;
    g_ModemNwMng[0].GsmRegState = 0;
    g_ModemNwMng[0].UmtsRegState = 0;
    g_ModemNwMng[0].VolteState = 0;
    printf("get cell info init\n");
    g_ModemNwMng[0].RadioPowerCb = NULL;
    g_ModemNwMng[0].CaliRslt   = 1;
    g_ModemNwMng[0].CaliRspCnt = 0;
    g_ModemNwMng[0].CaliRptFlg = 0;
    g_ModemNwMng[0].ConfApnOnPowerup = 0;
    g_ModemNwMng[0].CcwaOnOffFlg = 0;
    g_ModemNwMng[0].NoConditionDivert.CallForwardOn = 0;
    g_ModemNwMng[0].BusyDivert.CallForwardOn = 0;
    g_ModemNwMng[0].NotReachDivert.CallForwardOn = 0;
    g_ModemNwMng[0].NoAnswerDivert.CallForwardOn = 0;
    g_ModemNwMng[0].NoAnswerDivert.NoAnswerDelay = 20;

    g_ModemNwMng[1].ModemState         = MODEM_NW_STATE_NULL;
    g_ModemNwMng[1].PlmnInfo.PlmnState = MMI_MODEM_PLMN_UNKNOW;
    g_ModemNwMng[1].PlmnInfo.PlmnRat   = MMI_MODEM_PLMN_RAT_UNKNOW;
    g_ModemNwMng[1].CsRegState         = MODEM_NW_CS_NOT_REG;
    g_ModemNwMng[1].SignalBar          = MMI_MODEM_SIGNAL_BAR_0;   // init to invalid level
    g_ModemNwMng[1].PendingMsg = MODEM_NW_MSG_NULL;
    g_ModemNwMng[1].GetCellInfo.lte_cellinfoind = NULL;
    g_ModemNwMng[1].GetCellInfo.gsm_cellinfoind = NULL;
    g_ModemNwMng[1].GetCellInfo.ltecell_list = NULL;
    g_ModemNwMng[1].GetCellInfo.gsmcell_list = NULL;
    g_ModemNwMng[1].ImsRegState = 0;
    g_ModemNwMng[1].GsmRegState = 0;
    g_ModemNwMng[1].VolteState = 0;
    g_ModemNwMng[1].RadioPowerCb = NULL;
    g_ModemNwMng[1].CaliRslt   = 1;
    g_ModemNwMng[1].CaliRspCnt = 0;
    g_ModemNwMng[1].CaliRptFlg = 0;
    g_ModemNwMng[1].ConfApnOnPowerup = 0;
    g_ModemNwMng[1].CcwaOnOffFlg = 0;
    g_ModemNwMng[1].NoConditionDivert.CallForwardOn = 0;
    g_ModemNwMng[1].BusyDivert.CallForwardOn = 0;
    g_ModemNwMng[1].NotReachDivert.CallForwardOn = 0;
    g_ModemNwMng[1].NoAnswerDivert.CallForwardOn = 0;
    g_ModemNwMng[1].NoAnswerDivert.NoAnswerDelay = 20;

    Hal_Mem_Set(&g_Wifi, 0, sizeof(Modem_NW_Wifi_t));

    app_adaptor_enable_plmn_search(TRUE);
}
/**
 * NW module Msg handler
 * param (in) void
 * return  void
 */
VOID MMI_ModemAdp_NW_Msg_handle(UI_INTER_MSG_ID MsgId, UI_Inter_Msg_t * pMsg)
{
    switch(MsgId) {
        // network
        case MMI_MODEMADP_POWER_UP_REQ:
            MMI_ModemAdp_Power_Up_Req_Handler(pMsg);
            break;

        case MMI_MODEMADP_POWER_OFF_REQ:
            MMI_ModemAdp_Power_Off_Req_Handler(pMsg);
            break;

        case RIL_MODEMADP_RIL_READY_IND:
            MMI_ModemAdp_Ril_Ready_Ind_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_NW_STATE_CHANGED:
            MMI_ModemAdp_Nw_State_Changed_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_SIG_STRENGTH:
            MMI_ModemAdp_Sig_Strength_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_NITZ_TIME_RECEIVED:
            MMI_ModemAdp_Nitz_Time_Received_Handler(pMsg);
            break;

        case MMI_MODEMADP_PLMN_SEARCH_REQ:
            MMI_ModemAdp_Plmn_Search_Req_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_QUERY_AVAILABLE_NETWORKS:
            MMI_ModemAdp_Query_Available_Nw_Cnf_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_SET_NW_SEL_MANUAL:
            MMI_ModemAdp_Set_Nw_Sel_Manual_Cnf_Handler(pMsg);
            break;

        case MMI_MODEMADP_PLMN_SEARCH_STOP_REQ:
            MMI_ModemAdp_Plmn_Search_Stop_Req_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_RAT_CHANGED:
            MMI_ModemAdp_Rat_Changed_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_OPERATOR:
            MMI_ModemAdp_Rsp_Operator_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_IMS_REG_STATE:
            MMI_ModemAdp_Rsp_Ims_Reg_State_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_GET_BAND:
            MMI_ModemAdp_Mm_Rsp_Get_Band_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_IMS_VOPS:
            MMI_ModemAdp_Mm_Ims_Vops_Handler(pMsg);
            break;

        case MMI_MODEMADP_SET_BAND_REQ:
            MMI_ModemAdp_Set_Band_Req_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_RADIO_POWER_STATUS:
            MMI_ModemAdp_Radio_Power_Status_Handler(pMsg);
            break;

        case MMI_MODEMADP_GET_CALI_INFO_REQ:
            MMI_ModemAdp_Get_Cali_Info_Req_Handler(pMsg);
            break;
			
		case MMI_MODEMADP_GET_ONEGOCALI_INFO_REQ:
			MMI_ModemAdp_Get_onegoCali_Info_Req_Handler(pMsg);
			break;
			
		case MMI_MODEMADP_SAVE_ONEGOCALI_INFO_REQ:	 //onego-zly-20230816
			MMI_ModemAdp_Save_onegoCali_Info_Req_Handler(pMsg);
			break;

        case MMI_MODEMADP_GET_SN_INFO_REQ:
            MMI_ModemAdp_Get_Sn_Info_Req_Handler(pMsg);
            break;
        //onego-zly-20231012
        case MMI_MODEMADP_GET_NETID_INFO_REQ:
			printf("%s:MMI_MODEMADP_GET_NETID_INFO_REQ\n", __FUNCTION__);
            MMI_ModemAdp_Get_NETid_Info_Req_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_CALI_INFO:
            MMI_ModemAdp_Rsp_Cali_Info_Handler(pMsg);
            break;

        case RIL_MODEMADP_MM_RSP_GET_SN:
            MMI_ModemAdp_Rsp_Get_Sn_Handler(pMsg);
            break;

//onego-zly-20231012
        case RIL_MODEMADP_MM_RSP_GET_NETID:
			printf("%s:RIL_MODEMADP_MM_RSP_GET_NETID\n", __FUNCTION__);
			 MMI_ModemAdp_Rsp_Get_NETid_Handler(pMsg);
			break;

        case RIL_MODEMADP_MM_RSP_BANDIND:
            MMI_ModemAdp_Rsp_Bandind_Handler(pMsg);
            break;

        default:
            UI_Log_Out(UI_SUB_MODULE_MODEM_ADP, UI_LOG_LEVEL_ERROR, "Unknown MsgId %d!\n", MsgId & 0xFF);
            break;
    } /* switch */
}

MMI_MODEM_SIGNAL_BAR MMI_Modem_Get_Signal_Bar_Req(MMI_MODEM_SIM_ID SimId)
{
    printf("##interface## %s bar %d\n", __FUNCTION__, g_ModemNwMng[SimId].SignalBar);
    return(g_ModemNwMng[SimId].SignalBar);
}

VOID MMI_ModemAdp_Screen_State_Req(INT32 state)
{
    if(MODEM_NW_STATE_NULL != g_ModemNwMng[0].ModemState)
    {
        Ril_MM_Request_Screen_State(state, RIL_SIM_1);
    }
    if(MODEM_NW_STATE_NULL != g_ModemNwMng[1].ModemState)
    {
        Ril_MM_Request_Screen_State(state, RIL_SIM_2);
    }
}

UINT8 MMI_ModemAdp_Get_Ril_Ready_state(VOID)
{
    return(g_ModemNwMng[0].ModemState);
}

uint8_t MMI_Modem_Get_Wifi_State(VOID)
{
    uint8_t state = (TRUE == g_Wifi.WifiStart) ? 1 : 0;

    return state;
}

/**
 * Set WIFI scan parameters
 * param (in) TimeoutSeconds: UINT32
 * param (in) Cb: app_adp_wifi_scan_cb
 * return  void
*/
void MMI_Modem_Set_Wifi_Scan_Para(UINT32 TimeoutSeconds, app_adp_wifi_scan_cb Cb)
{
    g_Wifi.TimeoutSeconds = TimeoutSeconds + 2;
    g_Wifi.WifiResultInd = Cb;
}

/**
 * WIFI timeout call back function
 * param (in) para: uint32_t
 * return  void
*/
static void MMI_Modem_Wifi_Timeout_Cb(uint32_t param)
{
    app_adp_wifi_result_t result = APP_ADP_WIFI_RESULT_TIMEOUT;

    if(NULL != g_Wifi.TimeoutId) {
        uos_timer_delete(g_Wifi.TimeoutId);
        g_Wifi.TimeoutId = NULL;
    }

    //stop scan
    //MMI_Modem_Wifi_Ctrl_Req(0);

    //result ind
    if(NULL != g_Wifi.WifiResultInd) {
        app_adp_wifi_ap_list * ap_list = (app_adp_wifi_ap_list *)Hal_Mem_Alloc(sizeof(app_adp_wifi_ap_list));
        Hal_Mem_Copy(ap_list, &g_Wifi.WifiApList, sizeof(app_adp_wifi_ap_list));

        printf("%s, WifiResultInd: result=%d, count=%d\n", __FUNCTION__, result, g_Wifi.WifiApList.count);
        g_Wifi.WifiResultInd(result, ap_list);
    }

    //cleanup
    Hal_Mem_Set(&g_Wifi, 0, sizeof(Modem_NW_Wifi_t));
}

/**
 * wifi scan request
 * param (in) scan_rounds: range 1-3, rounds of wifi scan
 * param (in) max_hotspot_number: range 4-10, maximum number of wifi hotspots reported
 * param (in) timeout_seconds: range 1-25, timeout value to scan wifi in seconds
 * param (in) priority: 0, data traffic has higher priority than wifi scan,
 *                         data traffic will interrupt the wifi scan;
 *                      1, data traffic has lower priority than wifi scan,
 *                         data traffic will be blocked during wifi scan
 * param (in) cb: wifi scan call back function
 * return  uint8_t
 */
uint8_t app_adaptor_wifi_scan(uint8_t scan_rounds,
                              uint8_t max_hotspot_number,
                              uint32_t timeout_seconds,
                              uint8_t priority,
                              app_adp_wifi_scan_cb cb)
{
    uint8_t ret = 0;

#if USE_LV_WATCH_MODEM_ADAPTOR
    MMI_Modem_Set_Wifi_Scan_Para(timeout_seconds, cb);
    ret = MMI_Modem_Wifi_Ctrl_Req(2, scan_rounds, max_hotspot_number, timeout_seconds, priority);
#endif

    return ret;
}

                              
void app_adaptor_enable_plmn_search( BOOL enable)
{
  const int disable_params[] = {1, 3600*4, 3600*4, 3600*4};
  const int enable_params[] = {0};

  printf("##interface## %s, enable=%d\n", __FUNCTION__, enable);

  if(enable)
      Ril_MM_Request_Set_OSS_phase_period((RIL_SIM_ID)modem_sim_id, enable_params,sizeof(enable_params));
  else
      Ril_MM_Request_Set_OSS_phase_period((RIL_SIM_ID)modem_sim_id, disable_params,sizeof(disable_params));
}                              

/*
 * Request Volte om/off state
 * param (in) SimId: MMI_MODEM_SIM_ID;state: UINT8
 * return  VOID
 */
VOID MMI_Modem_Volte_State_Req(MMI_MODEM_SIM_ID SimId, UINT8 state)
{
    Ril_MM_Request_Set_Volte_State((RIL_SIM_ID)SimId, state);
    g_ModemNwMng[SimId].VolteState = state;
}

/*
 * Request Volte state
 * param (in) SimId: VOID
 * return  UINT8
 */
UINT8 MMI_Modem_Get_Volte_State_Req(MMI_MODEM_SIM_ID SimId)
{
    return  g_ModemNwMng[SimId].VolteState;
}

/*
 * set Neighbour Bch query option
 * param (in) SimId: MMI_MODEM_SIM_ID
 * param (in) option: 0/1
 * return  VOID
 */
VOID MMI_Modem_Set_Ncell_Bch_Req(MMI_MODEM_SIM_ID SimId, UINT8 option)
{
    printf("##interface## %s, option=%d\n", __FUNCTION__, option);
    Ril_MM_Request_Set_NCellBch(MMI_ModemAdp_Get_Ril_SimId(SimId), option);
}

/**
 * get roaming status
 * param (in) SimId: MMI_MODEM_SIM_ID
 * return  INT8: 0 not raoming, 1 roaming
 */
UINT8 MMI_Modem_Get_Roaming_Stat_Req(MMI_MODEM_SIM_ID SimId)
{
    printf("##interface## %s, roaming = %d\n", __FUNCTION__, g_ModemNwMng[SimId].RoamingState);
    return(g_ModemNwMng[SimId].RoamingState);
}

/*
 * set Neighbour Bch query option
 * param   option: 0/1
 * return  void
 */
void app_adaptor_set_ncell_bch_req(UINT8 option)
{
    MMI_Modem_Set_Ncell_Bch_Req(modem_sim_id, option);
}

/**
 * fast dormancy request
 * param (in) void:
 * return void
*/
void app_adaptor_fast_dormancy_req(void)
{
    printf("##interface## %s\n", __FUNCTION__);

    Ril_PS_Request_Fast_Dormancy(MMI_ModemAdp_Get_Ril_SimId(modem_sim_id));
}

/**
 * drx pch request
 * param (in) position: app_adp_drx_pch_position_t
 * param (in) config_val: 1, 2, 4, 8, 16, 32
 * return void
*/
void app_adaptor_drx_pch_req(app_adp_medcr_position_t position, uint8_t config_val)
{
    printf("##interface## %s: position=%d, config_val=%d\n", __FUNCTION__, position, config_val);

    int data[3];
    data[0] = 0; //set operation
    data[1] = position;
    data[2] = config_val;

    Ril_PS_Request_Set_Medcr(MMI_ModemAdp_Get_Ril_SimId(modem_sim_id), data, sizeof(int) * 3);
}

/**
 * enable/disable LTE fast dormancy
 * param (in) time_value: (unit: second), 0 for disable.
 * return void
*/
void app_adaptor_enable_lte_fast_dormancy_req(uint8_t time_value)
{
    printf("##interface## %s: time_value=%d\n", __FUNCTION__, time_value);

    int data[3];
    data[0] = 0; //set operation
    data[1] = 34;
    data[2] = time_value;

    Ril_PS_Request_Set_Medcr(MMI_ModemAdp_Get_Ril_SimId(modem_sim_id), data, sizeof(int) * 3);
}


/**
 * LTE power request
 * param (in) position: app_adaptor_lte_power_tx_req
 * param (in) option, channel, power
 * return void
*/
void app_adaptor_lte_power_tx_req(MMI_MODEM_SIM_ID sid, uint8_t option, uint8_t channel, uint8_t power)
{
    int data[3];
    data[0] = option;
    data[1] = channel;
    data[2] = power;
    printf("##interface## %s: sid=%d, option=%d, channel=%d, power=%d\n", __FUNCTION__, sid, option, channel, power);
    Ril_PS_Request_Set_Ltepower((RIL_SIM_ID)sid, data, sizeof(int) * 3);
}

/**
 * app_adaptor_get_lte_band_req
 * param (in)
 * return void
*/
void app_adaptor_get_lte_band_req(MMI_MODEM_SIM_ID sid, void * band_info)
{
    MODEM_NW_GET_BAND_INFO * getltebandinfo = NULL;

    if(RIL_SIM_1 == (RIL_SIM_ID)sid) {
        getltebandinfo = &g_ModemNwMng[0].GetBandInfo;
    } else {
        getltebandinfo = &g_ModemNwMng[1].GetBandInfo;
    }
    if(NULL != getltebandinfo) {
        Hal_Mem_Copy((MODEM_NW_GET_BAND_INFO *)band_info, getltebandinfo, sizeof(MODEM_NW_GET_BAND_INFO));
    }
}

#endif // USE_LV_WATCH_MODEM_ADAPTOR
