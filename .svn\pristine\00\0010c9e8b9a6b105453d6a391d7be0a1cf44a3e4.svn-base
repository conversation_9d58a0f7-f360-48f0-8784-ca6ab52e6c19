#ifdef CRANEL_FP_8MRAM
/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2006, 2007 Marvell DSPC Ltd. All Rights Reserved.
-------------------------------------------------------------------------------------------------------------------*/
#include "hal_cfg.h"
#include "FDI_EXT.h"

#include "FDI_FILE.h"
#include "FDI_TYPE.h"

#include <time.h>
#include "pmic_rtc.h"//mq

// Default permissions a file that just created
#define FDI5_FILE_DEFAULT_PERMISSIONS       (S_IRWXU | S_IRWXG | S_IRWXW)


#include "diag.h"



#include "fattypes.h"
#include "fat.h"
#include "fatdir.h"
#include "fatfile.h"
#include "fatio.h"
#include "sdsys.h"

#include "osa.h"

#include "osa_old_api.h"

#ifdef LFS_FILE_SYS
#include "lfs.h"
#include "lfs_api.h"
#endif
#include "utils.h"
#include "crossPlatformSW.h"

#define OS_FILE_NAME_SIZE	129


// Local Function Declarations
static int FileNameValid(const char *);

// Global Declarations
ERR_CODE FDI_errno = ERR_NOTEXISTS; // track file system errors not associated

		    // with an open file identifier.

// Local Declarations
#define FILE_NULL  0x00   // File Manager NULL value

//#define FDI6_TRACE_ENABLE  // print to viewer all FDI actions
#undef FFS_TCHAR_SIZE
#define FFS_TCHAR_SIZE 1

int FDI5to6_cTime2Time(const size_t clock);
int FDI5to6_cTime2Date(const time_t clock);
int lfs_io_findfirst(const char *wildcard ,char *Filename,fatTYPE_sStat *Stat);
int lfs_io_findnext(char *Filename,fatTYPE_sStat *Stat);

extern void Fat_lock_Init(void);
extern S32 NAND_Init(void);
extern BOOL sdSYS_Initialise(fatFAT_eAreas area);
extern S32 NAND_EraseAll(void);
extern void sdSYS_Format(fatFAT_eAreas Area, FAT_Type FatType);
extern int bbu_qspi_init(void);
extern void qspi_sim_buf_init(void);
extern VOID lfs_op_flush_task_init(VOID *argv);
extern int pmic_rtc_to_tm(int tim, t_rtc *tm);
extern int lfs_io_stat(const char* path, fatTYPE_sStat *st);
extern int lfs_io_removeDir(const char* path);
extern void lfs_nvm_size_check(void);
#undef ASSERT
#define ASSERT(cOND)     { if (!(cOND))  { utilsAssertFail(#cOND, __FILE__,   __LINE__, 1);    }  }


#ifndef LFS_FILE_SYS
#define FATSYS_TRACE(fmt, ...)
#endif

volatile int isDir=0;

volatile int isFSbusy = 0;

/*****************************************************************************
* Module Name: 'Name'. or TCHAR manipulation functions.
*
* Description: Starting from FDI7.1, there is define FFS_TCHAR_SIZE.
* If FFS_TCHAR_SIZE defined to 2, TCHAR defined to UIN16 instead of char.
* All next functions and defines came to hide this story.
*
* Functions: NameConvert(); NameInitPrefix(); NameConcat();
*	expandStrcpy(); compressStrcpy(); NameLength();
* Parameters:	TCHAR dst[MAX_PATH_SIZE+1].
******************************************************************************/
//---------------------------------------------------
#define VOLUME_FILENAME_DELIMITER_COLON     ':'
#define VOLUME_FILENAME_DELIMITER_SLASH     '\\'

/* find the last occurrence of '/' or '\' in a string.*/
/*used to separate the file name from the path*/
char *strrchr_sl(const char *str)
{
	char *rp=NULL;
	char *itr=(char*)str;
	while(*itr!='\0')
	{
		if((*itr=='\\')||(*itr=='/'))
			rp=itr;
		itr++;
	}
	return rp;
}

/*****************************************************************************/

#define FAT_SYS_OFFSET    1
#define FAT_SYS_INC(a)	a = (a+FAT_SYS_OFFSET)
#define FAT_SYS_DEC(a)	a = (a-FAT_SYS_OFFSET)

OSSemaRef FatSysRef;
/* littlefs find */
UINT32 FatSys_Lock_Type=0;

extern UINT32 assertFlag;

#define FS_NVM_VERSION      "1.02.000"


#ifdef FS_TIME_TEST
typedef struct {
	FILE_ID 		handle;
	char 			filename[80];
	unsigned long 	timestamp;
	int				isused;
}fs_time_t;

fs_time_t FS_time[8];

void FS_fileopentime(char* filename, FILE_ID handle, unsigned long timestamp )
{
	int index;
	index = handle-1;
	FS_time[index].handle = handle;
	strcpy(FS_time[index].filename,filename);
	FS_time[index].timestamp = timestamp;
	FS_time[index].isused = 0xff;
	return;
}

void FS_fileclosetime(FILE_ID handle)
{
	unsigned long timestamp;
	int index;
	index = handle-1;
	timestamp = timerCountRead(TS_TIMER_ID);

	if(FS_time[index].handle == handle)
	{
		FS_time[index].isused = 0;
		FS_time[index].handle = 0;
		if(timestamp >= FS_time[index].timestamp)
			timestamp = timestamp - FS_time[index].timestamp;
		else
			timestamp = 0xffffffff- FS_time[index].timestamp;

		FATSYS_TRACE("file: %s, cose 32ktick %ld", FS_time[index].filename, timestamp);
//		memset(FS_time[index].filename,0,80);
	}
	else
		FATSYS_TRACE("the handle is not matched");

	return;
}

#endif

unsigned long FDI_timercount(void)
{
	return timerCountRead(TS_TIMER_ID);
}

#ifdef ENABLE_FDI_FS

extern long fatSYS_PhysicalFlushEx(unsigned long Area);

void fat_flush2flash(void)
{
	OSA_STATUS status;

	nvm_op_lock(1);

	fatSYS_PhysicalFlushEx(0);

	nvm_op_unlock(1);

}

typedef enum
{
	DATA_FLASH_ONE_NAND = 0,
	DATA_FLASH_RAW_NAND,
	DATA_FLASH_eMMC
}DataFlash_Type;

extern DataFlash_Type g_DataFlash_type;
static void*            FatSysEventRef;
#define FATSYS_CLOSE_FLAG		0x2

unsigned char FatTaskStack[1024];
OSTaskRef		FatTaskRef;

void FatTask_entry(void *argv)
{
	UINT32		flags;

	while(1)
	{
	  fat_flush2flash();
	  OSATaskSleep(600);
	}
}
#endif


void nvm_lfs_init(void) 
{
	OSA_STATUS status;

	fatal_printf("nvm_lfs_init\r\n");

	status = OSASemaphoreCreate (&FatSysRef,1,OSA_FIFO);
	ASSERT(status == OS_SUCCESS);

#ifdef LFS_FILE_SYS
#if ((defined RUN_XIP_MODE) && (!defined LTEONLY_8M8M))
	lfs_op_flush_task_init(NULL);
#endif

	lfs_sys_init();
	lfs_nvm_size_check();

#endif

}


#ifdef ENABLE_FDI_FS
int FDI_ChDir(CONST char *Dirname)
{
	OSA_STATUS status=0;

	nvm_op_lock(2);

	fatDIR_ChDir(fatFAT_USER_AREA, Dirname);

	nvm_op_unlock(2);

	return status;
}
#endif

extern UINT32 assertFlag;
/****************************************************************************
* Functions: FDI_fopen(), FDI_fclose(),
*	     FDI_fread(),FDI_fwrite(),FDI_fseek()
*
* DESCRIPTION:
*    The function FDI_fopen takes as arguments a file name and a mode;
*    each is specified as a character string.  The file name is used in
*    an implementation-specified matter to open or create a file and
*    associate it with a stream.  Returns the StreamInfoTable index used
*    for the opened file.
*
* USAGE:
*    file_identifier = FDI_fopen(filename_ptr, wb);
*
* PARAMETERS:
*
* INPUTS:
*  filename_ptr         const character string for file name
*  mode                 const character string for type specification;
*                       modes supported: rb, wb, ab, rb+, wb+, ab+
* OUTPUTS:
*
* RETURNS:
*  Returns the StreamInfoTable index used for the opened file.
*  If an error is detected, FDI_fopen returns 0.
*
* History:
* 20.05.05  Dmitry P. Initial version. Originaly described in FDI_FILE.c:260
*
****************************************************************************/


UINT32 nvm_op_lock_label=0;

void nvm_op_lock(UINT32 label)
{
	OSA_STATUS status;
	UINT32 cpsr;

	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	nvm_op_lock_label=label;
}

void nvm_op_unlock(UINT32 label)
{
	OSA_STATUS status;

	if(label!=nvm_op_lock_label)
	{

		DIAG_FILTER(FDI,nvm_op_unlock,Lab001, DIAG_INFORMATION)
	    diagPrintf("nvm_op_unlock %d %d", label, nvm_op_lock_label);

		ASSERT(0);
	}

	nvm_op_lock_label=0;

	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

}




#ifndef LTEONLY_8M8M
extern UINT32 total_read_cnt;
extern UINT32 total_write_cnt;

extern UINT32 link_list_len;

extern UINT32 read_in_cache_cnt;
extern UINT32 read_out_cache_cnt;
extern UINT32 write_in_cache_cnt;
extern UINT32 write_out_cache_cnt;
#endif

FILE_ID  FDI_fopen_fatsys(const char *filename_ptr, const char *mode)
{
	UINT32 oflag;
	WORD32 handle=0;
	FILE_ID hFileID;
	OSA_STATUS status;
#ifdef FS_TIME_TEST
	unsigned long timestamp;
	timestamp = timerCountRead(TS_TIMER_ID);
#endif

	nvm_op_lock(3);

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_Open_start, DIAG_INFORMATION)
		diagPrintf("Fdi open start: %s: mode: %s, [%d,%d,%d][%d,%d,%d] %d", filename_ptr, mode
#ifndef LTEONLY_8M8M
		, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt, link_list_len
#endif
		);
	}
   // make sure filename_ptr is valid
   if (FileNameValid(filename_ptr) == FALSE)
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   //NameConvert(path, filename_ptr);

   // testing mode
   if(strlen(mode)<2 || strlen(mode)>3)
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   // Next flags set according to first case of table 9 of FDI_6.0_UG.pdf
   switch (mode[0])
   {
	  case 'r': case 'R':
		 oflag = fatIO_RDONLY;
		 break;
	  case 'w': case 'W':
		 oflag = fatIO_RDWR | fatIO_CREATE | fatIO_TRUNCATE;
		 break;
	  case 'a': case 'A':
		 oflag = fatIO_RDWR | fatIO_APPEND | fatIO_CREATE;
		 break;
	  default:
	 FDI_errno = ERR_PARAM; goto toNULL;
   }

   if(mode[1]!='b' && mode[1]!='B' )
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   if(mode[2]=='+')
   switch (mode[0])
   {
	  case 'r': case 'R':
		 oflag = fatIO_RDWR;
		 break;
	  case 'w': case 'W':
		 oflag = fatIO_RDWR | fatIO_CREATE | fatIO_TRUNCATE;
		 break;
	  case 'a': case 'A':
		 oflag = fatIO_RDWR | fatIO_APPEND | fatIO_CREATE;
		 break;
   }
   else if(mode[2]!=0)
   {  FDI_errno = ERR_PARAM; goto toNULL; }
   isFSbusy = 0xf;

#ifdef LFS_FILE_SYS
   handle = lfs_io_open(filename_ptr, oflag, fatTYPE_NORMAL);
#else
   handle = fatIO_Open(fatFAT_USER_AREA, filename_ptr, oflag, fatTYPE_NORMAL);
#endif
   if(handle <0 || handle>=8 )
		goto toNULL;

#ifdef FS_TIME_TEST
	FS_fileopentime(filename_ptr,(FILE_ID)(handle+1),timestamp);
#endif
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_Open_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi open succeed: %s: handle: %d", filename_ptr, handle);
	}
	FATSYS_TRACE("flash file open succeed: %s", filename_ptr);

	nvm_op_unlock(3);

	FDI_errno=ERR_NONE;
	isFSbusy = 0x0;
	hFileID = (FILE_ID)handle;

   return FAT_SYS_INC(hFileID);
toNULL:
   FDI_errno = ERR_ACCESS;
   DIAG_FILTER(FDI,fatsys,F_Open_error, DIAG_INFORMATION)
   diagPrintf("Fdi open error: %s, status: %d", filename_ptr, handle);
   FATSYS_TRACE("flash file open error: %s, status: %d", filename_ptr, handle);

   nvm_op_unlock(3);

   return FILE_NULL;
}


extern UINT32 initState;

int FDI_fclose_fatsys(FILE_ID stream)
{
	int res;
	OSA_STATUS status;
	FAT_SYS_DEC(stream);
	isFSbusy = 0xf;

	nvm_op_lock(4);

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_Close_start, DIAG_INFORMATION)
		diagPrintf("Fdi close start,  handle: %d, [%d,%d,%d][%d,%d,%d]", stream
#ifndef LTEONLY_8M8M
		, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt
#endif
		);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_close(stream);
#else
	res = fatIO_Close(stream);
#endif

#ifdef FS_TIME_TEST
	FS_fileclosetime(stream);
#endif
	if(res < 0)
	{
		FDI_errno=ERR_SYSTEM;
	    isFSbusy = 0x0;

		DIAG_FILTER(FDI,fatsys,F_Close_error, DIAG_INFORMATION)
        diagPrintf("Fdi close error, handle: %d, errCode: %d", stream, res);

		FATSYS_TRACE("flash file close error, res = %d", res);
		nvm_op_unlock(4);

		return EOF;
	}

	isFSbusy = 0x0;
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_Close_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi close succeed, handle: %d", stream);
	}
	FATSYS_TRACE("flash file close succeed, res = %d", res);
	nvm_op_unlock(4);




	FDI_errno=ERR_NONE; return 0;
}

size_t FDI_fread_fatsys(void *buff, size_t element_size, size_t count, FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	nvm_op_lock(5);


	DIAG_FILTER(FDI,fatsys,F_read_start, DIAG_INFORMATION)
	diagPrintf("Fdi read start, handle: %d, size: %ld, [%d,%d,%d][%d,%d,%d]", stream, element_size*count
#ifndef LTEONLY_8M8M
	, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt
#endif
	);
#ifdef LFS_FILE_SYS
    res = lfs_io_read( stream, buff, element_size * count );
#else
	res = fatIO_Read( stream, buff, element_size*count);
#endif

	if(res < 0)
	{
		FDI_errno = ERR_READ;
		DIAG_FILTER(FDI,fatsys,F_read_error, DIAG_INFORMATION)
    	diagPrintf("Fdi read error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file read error, res = %d", res);
		nvm_op_unlock(5);

		return 0;
	}


	nvm_op_unlock(5);

	DIAG_FILTER(FDI,fatsys,F_read_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi read succeed, handle: %d, actual size: %ld", stream, res);
	FDI_errno=ERR_NONE;
	return (res/element_size);
}

#ifdef LFS_FILE_SYS
size_t FDI_freadEx_fatsys(void *buff, size_t element_size, size_t count, FILE_ID stream, UINT32 filepostoread)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	nvm_op_lock(6);


    res = lfs_io_readEx( stream, buff, element_size * count, filepostoread);

	if(res < 0)
	{
		FDI_errno = ERR_READ;

		DIAG_FILTER(FDI,fatsys,F_readEx_error, DIAG_INFORMATION)
    	diagPrintf("Fdi read error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file readEx error, res = %d", res);

		nvm_op_unlock(6);

		return 0;
	}


	nvm_op_unlock(6);


	FDI_errno=ERR_NONE;
	return (res/element_size);
}
#endif

unsigned int FDI_fsize_fatsys(FILE_ID stream)
{
	int ori,cur;

	unsigned int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	nvm_op_lock(7);


#ifdef LFS_FILE_SYS

#if 0
	ori = lfs_io_ftell(stream);
	lfs_io_lseek(stream, 0, 2);
	res = lfs_io_ftell(stream);
	lfs_io_lseek(stream, ori, 0);
#endif

    res= lfs_io_size(stream);
#else
	res= fatIO_Size( stream);
#endif

	nvm_op_unlock(7);

	FDI_errno=ERR_NONE;
	return res;
}



size_t FDI_fwrite_fatsys(const void *buff, size_t element_size,
		  size_t count, FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);

	isFSbusy = 0xf;
	nvm_op_lock(8);


	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_write_start, DIAG_INFORMATION)
		diagPrintf("Fdi write start, handle: %d, size: %ld, [%d,%d,%d][%d,%d,%d]", stream, element_size*count
#ifndef LTEONLY_8M8M
		, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt
#endif
		);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_write(stream, buff, element_size * count );
#else
    res = fatIO_Write(stream, buff, element_size*count );
#endif
    if(res < 0)
	{
	    isFSbusy = 0x0;
		FDI_errno = ERR_WRITE;
		DIAG_FILTER(FDI,fatsys,F_write_error, DIAG_INFORMATION)
    	diagPrintf("Fdi write error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file write error,res = %d", res);
		nvm_op_unlock(8);

		return 0;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_write_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi write succeed, handle: %d, actual size: %ld", stream, res);
	}
    isFSbusy = 0x0;
	nvm_op_unlock(8);

	FDI_errno=ERR_NONE;
    return (res/element_size);
}

#ifdef LFS_FILE_SYS
size_t FDI_fwriteEx_fatsys(const void *buff, size_t element_size,
		  size_t count, FILE_ID stream, UINT32 filePostowrite)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);

	isFSbusy = 0xf;
	nvm_op_lock(9);


	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_writeEx_start, DIAG_INFORMATION)
	    diagPrintf("Fdi write start, handle: %d, size: %ld", stream, element_size*count);
	}

    res = lfs_io_writeEx(stream, buff, element_size * count,filePostowrite);

    if(res < 0)
	{
	    isFSbusy = 0x0;
		FDI_errno = ERR_WRITE;
		DIAG_FILTER(FDI,fatsys,F_writeEx_error, DIAG_INFORMATION)
    	diagPrintf("Fdi write error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file write error,res = %d", res);
		nvm_op_unlock(9);

		return 0;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_writeEx_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi write succeed, handle: %d, actual size: %ld", stream, res);
	}
    isFSbusy = 0x0;
	nvm_op_unlock(9);

	FDI_errno=ERR_NONE;
    return (res/element_size);
}
#endif

int FDI_fseek_fatsys(FILE_ID stream, long offset, int wherefrom)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	nvm_op_lock(10);

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_seek_start, DIAG_INFORMATION)
		diagPrintf("Fdi seek start, handle: %d, offset:%d, wherefrom: %d, [%d,%d,%d][%d,%d,%d]", stream,offset,wherefrom 
#ifndef LTEONLY_8M8M
		, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt
#endif
		);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_lseek(stream, offset, wherefrom);
//uart_printf("lfs_io_lseek,res=%d\r\n",res);
#else
	res = fatIO_Seek(stream,offset,(wherefrom+1));
#endif

	if(res < 0)
	{
		FDI_errno=ERR_SYSTEM;
		DIAG_FILTER(FDI,fatsys,F_seek_error, DIAG_INFORMATION)
    	diagPrintf("Fdi seek error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file seek error, res = %d", res);
		nvm_op_unlock(10);

		return EOF;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_seek_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi seek succeed, handle: %d,res: %d", stream,res);
	}
	nvm_op_unlock(10);


	FDI_errno=ERR_NONE;
	return 0;
}


int FDI_ftell_fatsys(FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	nvm_op_lock(11);
	
	DIAG_FILTER(FDI,fatsys,F_tell_start, DIAG_INFORMATION)
    diagPrintf("Fdi tell start, handle: %d", stream);


#ifdef LFS_FILE_SYS
    res = lfs_io_ftell(stream);
#else
	res=fatIO_Tell(stream);
#endif


	DIAG_FILTER(FDI,fatsys,F_tell_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi tell succeed, handle: %d", stream);

	nvm_op_unlock(11);


	FDI_errno=ERR_NONE;
	return res;
}




/****************************************************************************
* Functions: FDI_findfirst(), FDI_findnext();
*	 FDI5to6_cTime2Time(),FDI5_fileinfo_time2str(),
*	 FDI5to6_cTime2Date(),FDI5_fileinfo_date2str().
*
* DESCRIPTI
*    The function FDI_findfirst begins a search for files specified
*    wildcards.  The parameter filename_ptr is a string specifying the
*    file name.  Wildcard match characters (* and ?) are supported.  The
*    parameter fileinfo_ptr is a pointer to the type FILE_INFO which is
*    filled with the file information.
*
* USAGE:
*    status = FDI_findfirst("*.ext", &the_file_info);
*
* PARAMETERS:
*
* INPUTS:
*  filename_ptr      const character string for file name specifier
*
* OUTPUTS:
*  fileinfo_ptr      pointer to type FILE_INFO structure filled with
*                    located file information
*
* RETURNS:
*  Returns zero if successful in finding a file matching the search of
*  filename_ptr, otherwise it returns EOF.
*
* KNOWN BUGS:	In simulation, only one folder can be opened at a time.
* Openning of new folder cause to close previouse one.
*
* History:
* 23.05.05  dmp Initial version.
*
*****************************************************************************/

static char gFullFileName[OS_FILE_NAME_SIZE] = {0};
int FDI_findfirst_fatsys(const char *filename_ptr, FILE_INFO *fileinfo_ptr)
{
	int lastIndex;
	char fullFileName[OS_FILE_NAME_SIZE];
	char *fileNameP=NULL;
	char *slP=NULL;

	long rec;
	fatTYPE_sStat Stat;
	OSA_STATUS status;
#ifdef LFS_FILE_SYS
	lfs_io_findall();
#endif
	nvm_op_lock(12);


	DIAG_FILTER(FDI,fatsys,F_findfirst_start, DIAG_INFORMATION)
    diagPrintf("Fdi findfirst start, filename_ptr: %s", filename_ptr);

	memset(gFullFileName, 0, sizeof(gFullFileName));
	memset(fullFileName, 0, sizeof(fullFileName));

	// Make sure filename_ptr is valid length.
	// Can't call FileNameValid since filename_ptr can contain wild cards
	if ((filename_ptr == NULL) ||(strlen(filename_ptr) > FILE_NAME_SIZE))
	{
		FDI_errno = ERR_PARAM;
		DIAG_FILTER(FDI,fatsys,F_findfirst_error_0, DIAG_INFORMATION)
    	diagPrintf("Fdi findfirst error, ERR_PARAM");
		nvm_op_unlock(12);

		return EOF;
	}


    rec = lfs_io_findfirst(filename_ptr, fullFileName,&Stat);
//	rec = lfs_io_stat(filename_ptr, &Stat);
    if(rec != 0)
    {
    	DIAG_FILTER(FDI,fatsys,F_findfirst_error_1, DIAG_INFORMATION)
    	diagPrintf("Fdi findfirst error 1, errCode: %d", rec);
		
		nvm_op_unlock(12);

        return EOF;
    }
    isDir = 0;

    if(Stat.st_mode & fatTYPE_DIR)
        isDir = 0xf;



	strcpy(gFullFileName,fullFileName);


	/* we need to return the file name without the directory prefix.*/
	if ((slP = strrchr_sl(filename_ptr))!=NULL)
		filename_ptr = slP+1;


	strncpy(fileinfo_ptr->file_name,fullFileName,FILE_NAME_SIZE);
	fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

	fileinfo_ptr->plr_time=0;
	Stat.st_time=Fatsys_time(NULL);//

	fileinfo_ptr->time=FDI5to6_cTime2Time(Stat.st_time);
	fileinfo_ptr->date=FDI5to6_cTime2Date(Stat.st_time);
	fileinfo_ptr->size=Stat.st_size;

	fileinfo_ptr->owner_id=0x101;//FileGetUID();
	fileinfo_ptr->group_id=0x100;//FileGetGID();
	if(isDir)
		fileinfo_ptr->permissions=0x200;
	else
		fileinfo_ptr->permissions=FDI5_FILE_DEFAULT_PERMISSIONS;//my_dirent->mode;
	// next fields not in use
	fileinfo_ptr->data_id=0;
	fileinfo_ptr->plr_id=0;
	//fileinfo_ptr->plr_time=0; // is in use for dir
	//fileinfo_ptr->plr_date=0; // is in use for dirID
	fileinfo_ptr->plr_size=0;

	FDI_errno=ERR_NONE;

	DIAG_FILTER(FDI,fatsys,F_findfirst_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi findfirst succeed, the found file: %s", gFullFileName);
	nvm_op_unlock(12);

	return 0;
}//FDI_findfirst()


int FDI5to6_cTime2Time(const size_t clock)
{  int time;
   //struct tm *my_tm;
   //my_tm=localtime(&clock);

   t_rtc my_tm;
   pmic_rtc_to_tm(clock,&my_tm);
   time = my_tm.tm_hour<<(6+6);
   time+= my_tm.tm_min<<6;
   time+= my_tm.tm_sec;
   return time;
}//FDI5to6_cTime2Time

int FDI5to6_cTime2Date(const time_t clock)
{  int date;
   //struct tm *my_tm;
   //my_tm=localtime(&clock);

   t_rtc my_tm;
   pmic_rtc_to_tm(clock,&my_tm);
   date = my_tm.tm_mday<<(12+4);
   date+= my_tm.tm_mon<<12;
   date+= my_tm.tm_year;
   return date;
}//FDI5to6_cTime2Date


char * FDI5_fileinfo_time2str(char * str,int time)
{
  int hour = time >> (6+6);
  int min = (time >> 6) & 0x3f;
  int sec = time & 0x3f;
  if(!str) return NULL;
  sprintf(str,"%d:%02d:%02d",hour,min,sec);
  return str;
} // FDI5_fileinfo_time2str()

char * FDI5_fileinfo_date2str(char * str,int date)
{
  int day = date >> (12+4);
  int mon = (date >> 12) & 0xf;
  int year = date & 0xfff;
  if(!str) return NULL;
  sprintf(str,"%d.%02d.%02d",day+1,mon+1,year);
  return str;
} // FDI5_fileinfo_date2str()



// Original in FDI_FILE.c line 1906
int FDI_findnext_fatsys(FILE_INFO *fileinfo_ptr)
{
	char fullFileName[OS_FILE_NAME_SIZE];
	char *fileNameP=NULL;

	long rec;
	fatTYPE_sStat Stat;
	OSA_STATUS status;
	nvm_op_lock(13);


	DIAG_FILTER(FDI,fatsys,F_findnext_start, DIAG_INFORMATION)
    diagPrintf("Fdi findnext start");

	memset(fullFileName, 0, sizeof(fullFileName));

	isDir = 0;

#ifdef LFS_FILE_SYS
	rec=lfs_io_findnext(fullFileName,&Stat);
#else
	rec = fatFILE_FindNext(fatFAT_USER_AREA, fullFileName, &Stat);
#endif
	if(rec != 0)
	{
		FDI_errno = ERR_NOTEXISTS;
		if(rec == -7)
		{
			DIAG_FILTER(FDI,fatsys,F_findnext_end, DIAG_INFORMATION)
	    	diagPrintf("Fdi findnext there is not matching files");
		}
		else
		{
			DIAG_FILTER(FDI,fatsys,F_findnext_error, DIAG_INFORMATION)
    		diagPrintf("Fdi findnext error errCode: %d", rec);
		}
		nvm_op_unlock(13);

		return EOF;
	}

	if(Stat.st_mode&fatTYPE_DIR)
		isDir = 0xf;

	if(strcmp(gFullFileName,fullFileName) == 0)
	{
		FDI_errno = ERR_NOTEXISTS;

		DIAG_FILTER(FDI,fatsys,F_findnext_finished, DIAG_INFORMATION)
    	diagPrintf("Fdi find process finished: the final file: %s", gFullFileName);
		nvm_op_unlock(13);

		return EOF;
	}

	strcpy(gFullFileName,fullFileName);



	strncpy(fileinfo_ptr->file_name,fullFileName,FILE_NAME_SIZE);
	fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

	fileinfo_ptr->plr_time=0;
	Stat.st_time=Fatsys_time(NULL);//

	fileinfo_ptr->time=FDI5to6_cTime2Time(Stat.st_time);
	fileinfo_ptr->date=FDI5to6_cTime2Date(Stat.st_time);
	fileinfo_ptr->size=Stat.st_size;

	fileinfo_ptr->owner_id=0x101;//FileGetUID();
	fileinfo_ptr->group_id=0x100;//FileGetGID();
	if(isDir)
		fileinfo_ptr->permissions=0x200;
	else
		fileinfo_ptr->permissions=FDI5_FILE_DEFAULT_PERMISSIONS;//my_dirent->mode;
	// next fields not in use
	fileinfo_ptr->data_id=0;
	fileinfo_ptr->plr_id=0;
	//fileinfo_ptr->plr_time=0; // is in use for dir
	//fileinfo_ptr->plr_date=0; // is in use for dirID
	fileinfo_ptr->plr_size=0;
	FDI_errno=ERR_NONE;

	DIAG_FILTER(FDI,fatsys,F_findnext_succeed, DIAG_INFORMATION)
    diagPrintf("Fdi findnext succeed: the found file: %s", gFullFileName);
	nvm_op_unlock(13);

	return 0;

}//FDI_findnext()

/***************************************************
* Original in file ...  line 2004
* History:
* 18.05.05 DmitryP Intitial stab.
* 19.09.05 bamos   Initial verion.
*  5.02.06 DmitryP First workable verion.
***************************************************/
int FDI_remove_fatsys(const char *filename_ptr)
{
	int res;
	OSA_STATUS status;
	nvm_op_lock(14);


	DIAG_FILTER(FDI,fatsys,F_remove_start, DIAG_INFORMATION)
    diagPrintf("Fdi remove start, file: %s", filename_ptr);

#ifdef LFS_FILE_SYS
    res = lfs_io_remove(filename_ptr);
#else
	//NameConvert(path, filename_ptr);
	res = fatFILE_Remove(fatFAT_USER_AREA,filename_ptr);
#endif

	if(res < 0)
	{
		FDI_errno=ERR_NOTEXISTS;
		DIAG_FILTER(FDI,fatsys,F_remove_error, DIAG_INFORMATION)
        diagPrintf("Fdi remove error, file: %s, errCode: %d", filename_ptr, res);

		FATSYS_TRACE("flash file remove error");
		nvm_op_unlock(14);

		return EOF;
	}

	DIAG_FILTER(FDI,fatsys,F_remove_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi remove succeed, file: %s", filename_ptr);
	nvm_op_unlock(14);


	FDI_errno=ERR_NONE;
	return ERR_NONE;
}//FDI_remove()

/*-----------------12/13/2005 11:19AM---------------
 * added for the fdi_ram in order to allow rename in the icat - Smadar
 * --------------------------------------------------*/
int FDI_rename_fatsys(const FDI_TCHAR *name, const FDI_TCHAR *new_name)
{
	int res = 0;
	OSA_STATUS status;
	nvm_op_lock(15);


#ifdef LFS_FILE_SYS
    res = lfs_io_rename(name, new_name);
#else
	res = fatFILE_Rename(fatFAT_USER_AREA, name, new_name);
#endif

	if(res < 0)
	{
		FATSYS_TRACE("flash file rename error");
		nvm_op_unlock(15);

		return EOF;
	}
	nvm_op_unlock(15);

	FDI_errno=ERR_NONE;
	return ERR_NONE;
}
/*-----------------12/13/2005 11:19AM---------------
 * added for the fdi_ram in order to allow fdi_stat in the icat - Smadar
 * --------------------------------------------------*/

int FDI_stat_fatsys(const FDI_TCHAR *file_name, int *mode)
{
	return ERR_NONE;
}
/*-----------------12/13/2005 11:19AM---------------
 * added for the fdi_ram in order to allow fdi_chmod in the icat - Smadar
 * --------------------------------------------------*/

int FDI_chmod_fatsys(const FDI_TCHAR *file_name, int mode)
{
	int res = 0;
	return res;
}

ERR_CODE FDI_Format_fatsys(void)
{
#ifdef ENABLE_FDI_FS
    int ret_err = 0;
	OSA_STATUS status;
	nvm_op_lock(16);


	NAND_EraseAll();
	sdSYS_Format(fatFAT_USER_AREA,FAT);

	ret_err = sdSYS_Initialise(fatFAT_USER_AREA);
    if(ret_err != 0x0)
	{
		DIAG_FILTER(FDI,fatsys,Format_Success, DIAG_INFORMATION)
		diagPrintf("The volume was successfully formated!");
	}
	else
	{
		DIAG_FILTER(FDI,fatsys,Format_Error, DIAG_ERROR)
		diagPrintf("An error occurred while formating volume, errCode; %d", ret_err);
	}
	nvm_op_unlock(16);


//	OSAFlagSet( (OSFlagRef)FatSysEventRef, FATSYS_CLOSE_FLAG, OSA_FLAG_OR );
#endif
	return ERR_NONE;
}


int FDI_feof(FILE_ID stream)
{
    DIAG_FILTER(FDI,FDI5to6,FDI_feof,DIAG_ERROR)
    diagPrintf("FDI_feof() not implemented in FDI5 API to FDI7 converter");
    return ERR_NONE;

}//FDI_feof()



int FDI_feof_fatsys(FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	nvm_op_lock(17);


	DIAG_FILTER(FDI,fatsys,F_eof_start, DIAG_INFORMATION)
	    diagPrintf("Fdi eof start,  handle: %d", stream);

#ifdef LFS_FILE_SYS
	res=lfs_io_eof(stream);
#else
	res=fatIO_EOF(stream);
#endif
	uart_printf("FDI_eof: %d\r\n",res);

	nvm_op_unlock(17);

	DIAG_FILTER(FDI,fatsys,F_eof_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi eof succeed, handle: %d,res: %d", stream,res);

	FDI_errno=ERR_NONE;
	return res;


}

#if defined(CRANE_MCU_DONGLE) && defined(LFS_FILE_SYS)
/*****************************************************************************
* Function Name: FDI_Access()
*
* DESCRIPTION:
*    Determine file or directory access permission.
*
* USAGE:
*    int FDI_Access(const char *path, UINT32 mode)
*
* PARAMETERS:
*
* INPUTS:
*  filename_ptr  const character null terminated string of the filename
*                to check
*
* OUTPUTS:
*
* RETURNS:
*  Returns TRUE if filename is valid; otherwise, it returns FALSE.
*
* HISTORY:
* 20.05.05 dmp Took from FDI5 from FDI_FILE.c:3875; and simplifyed.
*
***************************************************************************/
int FDI_Access(const char *path, UINT32 mode)
{
    int ret = ERR_NONE;
    OSA_STATUS status;

    FATSYS_TRACE("%s %s", __FUNCTION__, path);

	nvm_op_lock(18);


    ret = lfs_io_Access(path, mode);

	nvm_op_unlock(18);


    FATSYS_TRACE("%s %s end", __FUNCTION__, path);

    return ret;
}
#endif

#ifdef LFS_FILE_SYS
/*****************************************************************************
* Function Name: FDI_Stat()
*
* DESCRIPTION:
*    None.
*
* USAGE:
*    int FDI_Stat(const char *file_name, fatTYPE_sStat *stat)
*
* PARAMETERS:
*
* INPUTS:
*  filename_ptr  const character null terminated string of the filename
*                to check
*
* OUTPUTS:
*
* RETURNS:
*  Returns TRUE if filename is valid; otherwise, it returns FALSE.
*
* HISTORY:
* 20.05.05 dmp Took from FDI5 from FDI_FILE.c:3875; and simplifyed.
*
***************************************************************************/
int FDI_Stat(const char *file_name, fatTYPE_sStat *stat)
{
    int ret = ERR_NONE;
    OSA_STATUS status;

    FATSYS_TRACE("%s %s", __FUNCTION__, file_name);

	nvm_op_lock(19);


    ret = lfs_io_stat(file_name, stat);

	nvm_op_unlock(19);


    FATSYS_TRACE("%s %s end", __FUNCTION__, file_name);

    return ret;
}

extern int NVMFileNameMatch(const char *src, const char *dest);
#define MAX_NUM_OF_FILES 240
#define MAX_FILE_NAME_LENTGH 128
FILE_INFO FILE_LIST_LFS[MAX_NUM_OF_FILES];
static UINT32 LastFound = 0;										//used for the find find first\next functions.
static char WILDCARDS[MAX_FILE_NAME_LENTGH];		//used for the find find first\next functions

int FILE_NUM_LFS=0;
void lfs_io_findall(void)
{
//	char *filename_all[100];
	FILE_ID  hFileID;
	FILE_INFO	file_info;				/* Holds the file information				*/
	int i=0;

	FATSYS_TRACE("lfs_io_findall\r\n");

	hFileID = FDI_OpenDir("/");
	if (hFileID == 0)
	{
		uart_printf("lfs_io_findall: fail to open Current DIR");
		return;
	}
//	uart_printf("lab01,fname size=%d\r\n",FILE_NAME_SIZE);	//128
	while((FDI_DirRead(hFileID, &file_info)== 1) && (file_info.file_name[0] != 0))
	{
#if 0
		FILE_LIST_LFS[i]=(FILE_INFO *)malloc(sizeof(FILE_INFO));
		strcpy(FILE_LIST_LFS[i]->file_name,file_info.file_name);
		FILE_LIST_LFS[i]->size=file_info.size;
		FILE_LIST_LFS[i]->time=file_info.time;
		FILE_LIST_LFS[i]->date=file_info.date;
		uart_printf("FILE_LIST_LFS[%d]----name:%s,size: %d,time:%d,date:%d\r\n",i,FILE_LIST_LFS[i]->file_name,FILE_LIST_LFS[i]->size,FILE_LIST_LFS[i]->time,FILE_LIST_LFS[i]->date);
#else
		strcpy(FILE_LIST_LFS[i].file_name,file_info.file_name);
		FILE_LIST_LFS[i].size=file_info.size;
		FILE_LIST_LFS[i].permissions=file_info.permissions;
//		FILE_LIST_LFS[i].time=file_info.time;
//		FILE_LIST_LFS[i].date=file_info.date;
//		uart_printf("FILE_LIST_LFS[%d]----name:%s,size: %d,time:%d,date:%d\r\n",i,FILE_LIST_LFS[i].file_name,FILE_LIST_LFS[i].size,FILE_LIST_LFS[i].time,FILE_LIST_LFS[i].date);
#endif
		i++;
	}
	FDI_CloseDir(hFileID);
	FILE_NUM_LFS=i;
	FATSYS_TRACE("find total files:%d\r\n",FILE_NUM_LFS);
}

void lfsSetFileInfo(UINT32 index, fatTYPE_sStat *file_information)
{
	if(!PLATFORM_IS_FPGA)//Z2 HAPS has no PMIC
	{
		unsigned long clock;
		clock=Fatsys_time(NULL);

		FILE_LIST_LFS[index].time=FDI5to6_cTime2Time(clock);
		FILE_LIST_LFS[index].date=FDI5to6_cTime2Date(clock);
	}

//	strcpy (file_information->, FILE_LIST_LFS[index]->file_name);
	file_information->st_size=FILE_LIST_LFS[index].size;
	file_information->st_time=FILE_LIST_LFS[index].time;
	if(FILE_LIST_LFS[index].permissions==FDI5_FILE_DEFAULT_PERMISSIONS)
		file_information->st_mode=fatTYPE_NORMAL;
	else
		file_information->st_mode=fatTYPE_DIR;

} /* end of SetFileInfo */

int lfs_io_findfirst(const char *wildcard ,char *Filename,fatTYPE_sStat *Stat)
{
	UINT32 index;

	FATSYS_TRACE("lfs_io_findfirst %s\r\n",wildcard);

	if(FILE_NUM_LFS==0)
		return (-2);

	for (index = 0; index < FILE_NUM_LFS; index ++)
	{
//		uart_printf("index:%d,",index);
		if (lfs_fname_match(FILE_LIST_LFS[index].file_name, wildcard) /*&& FILE_LIST_LFS[index].flag_exist*/)
		{
//			uart_printf("name match,");
			lfsSetFileInfo(index, Stat);
//			Stat->st_size=FILE_LIST_LFS[index]->size;
			/*the index of the file which was last found and the wildcard should be saved in order to use
			 * it later in the find next function */
			LastFound = index;
			strcpy (Filename, FILE_LIST_LFS[index].file_name);
			strcpy(WILDCARDS,wildcard);
//			uart_printf("st_mode,%d\r\n",Stat->st_mode);
			return 0;
		}

	}

	FATSYS_TRACE("lfs_io_findfirst end, -1\r\n");
	return (-1);
}
int lfs_io_findnext(char *Filename,fatTYPE_sStat *Stat)
{
	UINT32 index;
	FATSYS_TRACE("lfs_io_findnext\r\n");
	for (index = (LastFound+1); index < FILE_NUM_LFS; index ++)
	{
		if (lfs_fname_match(FILE_LIST_LFS[index].file_name, WILDCARDS) /*&& FILE_LIST_LFS[index].flag_exist*/)
		{
			lfsSetFileInfo(index, Stat);
			strcpy (Filename, FILE_LIST_LFS[index].file_name);
			LastFound = index;
			return 0;
		}

	}

	return (-1);
} /* end of FDI_RAM_findnext */
#endif






//2787
ERR_CODE FDI_ferror_fatsys(FILE_ID stream)
{
	return FDI_errno;

}//FDI_ferror()

static int
FileNameValid(const FDI_TCHAR *filename_ptr)
{  int i;
   // Invalid filename characters array.
   // Must end with end of string character as array end marker.
   const FDI_TCHAR InvalidFilenameChars[] =
	       { FILE_SCWILDCARD, FILE_MCWILDCARD, FILE_EOS};
   if (filename_ptr == NULL) return FALSE;
   if( filename_ptr[0]==0 )  return FALSE;
   if(strlen(filename_ptr)>FILE_NAME_SIZE ) return FALSE;
   // check that filename_ptr contains no invalid characters
   for(i=0; InvalidFilenameChars[i] != FILE_EOS;i++)
      if(strchr(filename_ptr, InvalidFilenameChars[i]))
	 return FALSE;
   return TRUE;
}// FileNameValid

#else

/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2006, 2007 Marvell DSPC Ltd. All Rights Reserved.
-------------------------------------------------------------------------------------------------------------------*/
#include "hal_cfg.h"
#include "FDI_EXT.h"

#include "FDI_FILE.h"
#include "FDI_TYPE.h"

#include <time.h>
#include "pmic_rtc.h"
#include "diag.h"
#include "fattypes.h"
#include "fat.h"
#include "fatdir.h"
#include "fatfile.h"
#include "fatio.h"
#include "sdsys.h"
#include "osa.h"
#include "osa_old_api.h"

#ifdef LFS_FILE_SYS
#include "lfs.h"
#include "lfs_api.h"
#endif
#include "utils.h"

#include "yaffsfs.h"
#include "crossPlatformSW.h"

/*===========================================================================

            LOCAL MACRO FOR MODULE

This section contains local macros needed by this module.

===========================================================================*/

#define OS_FILE_NAME_SIZE	129

// Default permissions a file that just created
#define FDI5_FILE_DEFAULT_PERMISSIONS       (S_IRWXU | S_IRWXG | S_IRWXW)

#define FILE_NULL  0x00   // File Manager NULL value

//#define FDI6_TRACE_ENABLE  // print to viewer all FDI actions
#undef FFS_TCHAR_SIZE
#define FFS_TCHAR_SIZE                      1

#define MAX_NUM_OF_FILES                    240

#define MAX_FILE_NAME_LENTGH                128

#define FAT_SYS_OFFSET                      1
#define FAT_SYS_INC(a)	                    a = (a+FAT_SYS_OFFSET)
#define FAT_SYS_DEC(a)	                    a = (a-FAT_SYS_OFFSET)

#undef ASSERT
#define ASSERT(cOND)     { if (!(cOND))  { utilsAssertFail(#cOND, __FILE__,   __LINE__, 1);    }  }

#ifndef LFS_FILE_SYS
#define FATSYS_TRACE(fmt, ...)
#endif

/*****************************************************************************
* Module Name: 'Name'. or TCHAR manipulation functions.
*
* Description: Starting from FDI7.1, there is define FFS_TCHAR_SIZE.
* If FFS_TCHAR_SIZE defined to 2, TCHAR defined to UIN16 instead of char.
* All next functions and defines came to hide this story.
*
* Functions: NameConvert(); NameInitPrefix(); NameConcat();
*	expandStrcpy(); compressStrcpy(); NameLength();
* Parameters:	TCHAR dst[MAX_PATH_SIZE+1].
******************************************************************************/
//---------------------------------------------------
#define VOLUME_FILENAME_DELIMITER_COLON     ':'
#define VOLUME_FILENAME_DELIMITER_SLASH     '\\'

#define LFS_GET_FREE_OPTIMIZE
#define GET_VIRTUAL_FREE_CNT_MAX	50

/*===========================================================================

            LOCAL DEFINITIONS AND DECLARATIONS FOR MODULE

This section contains local definitions for constants, macros, types,
variables and other items needed by this module.

===========================================================================*/

// track file system errors not associated
ERR_CODE FDI_errno = ERR_NOTEXISTS;

volatile int isDir=0;

/* FS busy flag*/
volatile int isFSbusy = 0;

/* LFS file numer. */
int FILE_NUM_LFS=0;

/* FAT system Semaphore*/
OSSemaRef FatSysRef;

/* used for the find find first\next functions. */
FILE_INFO FILE_LIST_LFS[MAX_NUM_OF_FILES];

/* used for the find find first\next functions. */
static UINT32 LastFound = 0;

/* full name for the find find first\next functions */
static char fullFileName[OS_FILE_NAME_SIZE];

/* used for the find find first\next functions */
static char WILDCARDS[MAX_FILE_NAME_LENTGH];

/* full name for the find find first\next functions */
static char gFullFileName[OS_FILE_NAME_SIZE] = {0};

/* littlefs find */
UINT32 FatSys_Lock_Type=0;
UINT8 rollback;
UINT8 rollback_ori;

UINT8 lfs_free_space_init_flag=0;
UINT32 lfs_free_space[PARTITION_LFS_MAX];
UINT8 lfs_free_space_valid[PARTITION_LFS_MAX];
UINT8 lfs_get_free_cnt[PARTITION_LFS_MAX];

/*===========================================================================

            EXTERN DEFINITIONS AND DECLARATIONS FOR MODULE

===========================================================================*/

extern UINT32 assertFlag;
extern UINT32 SPIFlashUsed;

extern UINT32 total_read_cnt;
extern UINT32 total_write_cnt;

extern UINT32 link_list_len;

extern UINT32 read_in_cache_cnt;
extern UINT32 read_out_cache_cnt;
extern UINT32 write_in_cache_cnt;
extern UINT32 write_out_cache_cnt;

extern UINT8 factory_reset_flag;

/*===========================================================================

                        EXTERN FUNCTION DECLARATIONS

===========================================================================*/

int FDI5to6_cTime2Time(const size_t clock);
int FDI5to6_cTime2Date(const time_t clock);
int lfs_io_findfirst(const char *wildcard ,char *Filename,fatTYPE_sStat *Stat);
int lfs_io_findnext(char *Filename,fatTYPE_sStat *Stat);
static int FileNameValid(const char *);

extern void Fat_lock_Init(void);
extern S32 NAND_Init(void);
extern BOOL sdSYS_Initialise(fatFAT_eAreas area);
extern S32 NAND_EraseAll(void);
extern void sdSYS_Format(fatFAT_eAreas Area, FAT_Type FatType);
extern int bbu_qspi_init(void);
extern void qspi_sim_buf_init(void);
extern VOID lfs_op_flush_task_init(VOID *argv);
extern int pmic_rtc_to_tm(int tim, t_rtc *tm);
extern int lfs_io_stat(const char* path, fatTYPE_sStat *st);
extern int lfs_io_removeDir(const char* path);
extern void lfs_nvm_size_check(void);
void lfs_free_space_update(FILE_ID Handle);

/*===========================================================================

                          INTERNAL FUNCTION DEFINITIONS

===========================================================================*/
/*===========================================================================

FUNCTION strrchr_sl

DESCRIPTION
  find the last occurrence of '/' or '\' in a string.
  used to separate the file name from the path

DEPENDENCIES
  none

RETURN VALUE
  return staus

SIDE EFFECTS
  none

===========================================================================*/

char *strrchr_sl(const char *str)
{
	char *rp=NULL;
	char *itr=(char*)str;
	while(*itr!='\0')
	{
		if((*itr=='\\')||(*itr=='/'))
			rp=itr;
		itr++;
	}
	return rp;
}

/*****************************************************************************/


#ifdef FS_TIME_TEST
typedef struct {
	FILE_ID 		handle;
	char 			filename[80];
	unsigned long 	timestamp;
	int				isused;
}fs_time_t;

fs_time_t FS_time[8];

void FS_fileopentime(char* filename, FILE_ID handle, unsigned long timestamp )
{
	int index;
	index = handle-1;
	FS_time[index].handle = handle;
	strcpy(FS_time[index].filename,filename);
	FS_time[index].timestamp = timestamp;
	FS_time[index].isused = 0xff;
	return;
}

void FS_fileclosetime(FILE_ID handle)
{
	unsigned long timestamp;
	int index;
	index = handle-1;
	timestamp = timerCountRead(TS_TIMER_ID);

	if(FS_time[index].handle == handle)
	{
		FS_time[index].isused = 0;
		FS_time[index].handle = 0;
		if(timestamp >= FS_time[index].timestamp)
			timestamp = timestamp - FS_time[index].timestamp;
		else
			timestamp = 0xffffffff- FS_time[index].timestamp;

		FATSYS_TRACE("file: %s, cose 32ktick %ld", FS_time[index].filename, timestamp);
//		memset(FS_time[index].filename,0,80);
	}
	else
		FATSYS_TRACE("the handle is not matched");

	return;
}

#endif

unsigned long FDI_timercount(void)
{
	return timerCountRead(TS_TIMER_ID);
}

#ifndef CRANE_MCU_DONGLE
extern long fatSYS_PhysicalFlushEx(unsigned long Area);

void fat_flush2flash(void)
{
	OSA_STATUS status;
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}


	fatSYS_PhysicalFlushEx(0);


	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}
}

typedef enum
{
	DATA_FLASH_ONE_NAND = 0,
	DATA_FLASH_RAW_NAND,
	DATA_FLASH_eMMC
}DataFlash_Type;

extern DataFlash_Type g_DataFlash_type;
static void*            FatSysEventRef;
#define FATSYS_CLOSE_FLAG		0x2

unsigned char FatTaskStack[1024];
OSTaskRef		FatTaskRef;

void FatTask_entry(void *argv)
{
	UINT32		flags;

	while(1)
	{
	  fat_flush2flash();
	  OSATaskSleep(600);
	}
}
#endif

OSA_STATUS FDI2FatSysInit(void)
{
	OSA_STATUS status;
	extern unsigned int use_qspi_sim;
	UINT32 cpsr;

	FATSYS_TRACE("start file system init");

#ifdef ENABLE_FDI_FS
	Fat_lock_Init();
#endif
	status = OSASemaphoreCreate (&FatSysRef,1,OSA_FIFO);
	ASSERT(status == OS_SUCCESS);

if(SPIFlashUsed==1)
{
	cpsr=disableInterrupts();
	fatal_printf("bbu_qspi_init +\r\n");
	bbu_qspi_init();
	fatal_printf("bbu_qspi_init -\r\n");
	restoreInterrupts(cpsr);
}
else
{
#ifndef CRANE_MCU_DONGLE
	FatSysEventRef = NULL;
	ASSERT (OS_SUCCESS == OSAFlagCreate((OSFlagRef*)&FatSysEventRef));
	ASSERT ( FatSysEventRef != NULL );

	qspi_sim_buf_init();
#else
	cpsr=disableInterrupts();
	fatal_printf("bbu_qspi_init +\r\n");
	bbu_qspi_init();
	fatal_printf("bbu_qspi_init -\r\n");
	restoreInterrupts(cpsr);
#endif
}

	fatal_printf("bbu_qspi_init done\r\n");

#if 0
 	if(NAND_Init() != 0)
	{
		FATSYS_TRACE("not NAND or OneNAND, init eMMC");
		return OS_FAIL;
 	}
#endif

#ifdef LFS_FILE_SYS
#ifdef RUN_XIP_MODE
	lfs_op_flush_task_init(NULL);
#endif
    lfs_sys_init();
#else
    if(sdSYS_Initialise(fatFAT_USER_AREA) != TRUE)
	{
		FATSYS_TRACE("format ...");
		uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
		fatFAT_EnterUserArea();

//		NAND_EraseAll();
		qspi_sim_buf_erase_all();
		uart_printf("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);


		sdSYS_Format(fatFAT_USER_AREA,FAT);

		sdSYS_Initialise(fatFAT_USER_AREA);
	}
#endif

	return OS_SUCCESS;
}

void nvm_lfs_init(void) 
{
	OSA_STATUS status;
	static int init_flag=0;

	if(init_flag==0)
	{
		fatal_printf("nvm_lfs_init\r\n");

		status = OSASemaphoreCreate (&FatSysRef,1,OSA_FIFO);
		ASSERT(status == OS_SUCCESS);

#ifdef LFS_FILE_SYS
#ifdef RUN_XIP_MODE
		lfs_op_flush_task_init(NULL);
#endif
		lfs_sys_init();
		lfs_nvm_size_check();
#endif

#if defined (SSP1_USED) && (defined DUAL_NVM_SUPPORT)
		lfs_partition_init(PARTITION_EXTRA_SPI);
#endif
		init_flag=1;
	}

}


#ifdef ENABLE_FDI_FS
int FDI_ChDir(CONST char *Dirname)
{
	OSA_STATUS status=0;
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}
	fatDIR_ChDir(fatFAT_USER_AREA, Dirname);
	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	return status;
}
#endif


void fdi_sem_lock(void)
{
	OSA_STATUS status;

	status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);


}

void fdi_sem_unlock(void)
{
	OSA_STATUS status;

	status = OSASemaphoreRelease(FatSysRef);
	ASSERT(status == OS_SUCCESS);

}


unsigned int fdi_sem_lock_check(void)
{
	OSA_STATUS status;

	status = OSASemaphoreAcquire(FatSysRef,OS_NO_SUSPEND);
	if(status == OS_SUCCESS)
		return 1;
	else
		return 0;

}


FILE_ID  FDI_fopen_fatsys(const char *filename_ptr, const char *mode)
{
	UINT32 oflag;
	WORD32 handle=0;
	FILE_ID hFileID;
	OSA_STATUS status;
#ifdef FS_TIME_TEST
	unsigned long timestamp;
	timestamp = timerCountRead(TS_TIMER_ID);
#endif
	if(assertFlag==0)
	{
	status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);
	}
	if(assertFlag != 1)
	{
	DIAG_FILTER(FDI,fatsys,F_Open_start, DIAG_INFORMATION)
    diagPrintf("Fdi open start: %s: mode: %s, [%lu,%lu,%lu][%lu,%lu,%lu] %lu", filename_ptr, mode, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt, link_list_len);
	}
   // make sure filename_ptr is valid
   if (FileNameValid(filename_ptr) == FALSE)
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   //NameConvert(path, filename_ptr);

   // testing mode
   if(strlen(mode)<2 || strlen(mode)>3)
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   // Next flags set according to first case of table 9 of FDI_6.0_UG.pdf
   switch (mode[0])
   {
	  case 'r': case 'R':
		 oflag = fatIO_RDONLY;
		 break;
	  case 'w': case 'W':
		 oflag = fatIO_RDWR | fatIO_CREATE | fatIO_TRUNCATE;
		 break;
	  case 'a': case 'A':
		 oflag = fatIO_RDWR | fatIO_APPEND | fatIO_CREATE;
		 break;
	  default:
	 FDI_errno = ERR_PARAM; goto toNULL;
   }

   if(mode[1]!='b' && mode[1]!='B' )
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   if(mode[2]=='+')
   switch (mode[0])
   {
	  case 'r': case 'R':
		 oflag = fatIO_RDWR;
		 break;
	  case 'w': case 'W':
		 oflag = fatIO_RDWR | fatIO_CREATE | fatIO_TRUNCATE;
		 break;
	  case 'a': case 'A':
		 oflag = fatIO_RDWR | fatIO_APPEND | fatIO_CREATE;
		 break;
   }
   else if(mode[2]!=0)
   {  FDI_errno = ERR_PARAM; goto toNULL; }
   isFSbusy = 0xf;

#ifdef LFS_FILE_SYS
   handle = lfs_io_open(filename_ptr, oflag, fatTYPE_NORMAL);
#else
   handle = fatIO_Open(fatFAT_USER_AREA, filename_ptr, oflag, fatTYPE_NORMAL);
#endif
    if(handle < 0)
		goto toNULL;

#ifdef FS_TIME_TEST
	FS_fileopentime(filename_ptr,(FILE_ID)(handle+1),timestamp);
#endif
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_Open_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi open succeed: %s: handle: %d", filename_ptr, handle);
	}
	FATSYS_TRACE("flash file open succeed: %s", filename_ptr);
	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	isFSbusy = 0x0;
	hFileID = (FILE_ID)handle;

   return FAT_SYS_INC(hFileID);
toNULL:
   FDI_errno = ERR_ACCESS;
   DIAG_FILTER(FDI,fatsys,F_Open_error, DIAG_INFORMATION)
   diagPrintf("Fdi open error: %s, status: %d", filename_ptr, handle);
   FATSYS_TRACE("flash file open error: %s, status: %d", filename_ptr, handle);
   if(assertFlag==0)
   {
   status = OSASemaphoreRelease(FatSysRef);
   ASSERT(status == OS_SUCCESS);
   }
   return FILE_NULL;
}


extern UINT32 initState;

int FDI_fclose_fatsys(FILE_ID stream)
{
	int res;
	OSA_STATUS status;
	FAT_SYS_DEC(stream);
	isFSbusy = 0xf;
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_Close_start, DIAG_INFORMATION)
		diagPrintf("Fdi close start,  handle: %d, [%lu,%lu,%lu][%lu,%lu,%lu] %lu", stream, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt, link_list_len);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_close(stream);
#else
	res = fatIO_Close(stream);
#endif

#ifdef FS_TIME_TEST
	FS_fileclosetime(stream);
#endif
	if(res < 0)
	{
		FDI_errno=ERR_SYSTEM;
	    isFSbusy = 0x0;

		DIAG_FILTER(FDI,fatsys,F_Close_error, DIAG_INFORMATION)
        diagPrintf("Fdi close error, handle: %d, errCode: %d", stream, res);

		FATSYS_TRACE("flash file close error, res = %d", res);
		if(assertFlag==0)
		{
			status = OSASemaphoreRelease(FatSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

#ifdef LFS_GET_FREE_OPTIMIZE
	lfs_free_space_update(stream);
#endif

	isFSbusy = 0x0;
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_Close_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi close succeed, handle: %d", stream);
	}
	FATSYS_TRACE("flash file close succeed, res = %d", res);
	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}



	FDI_errno=ERR_NONE; return 0;
}

size_t FDI_fread_fatsys(void *buff, size_t element_size, size_t count, FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,fatsys,F_read_start, DIAG_INFORMATION)
	diagPrintf("Fdi read start, handle: %d, size: %ld, [%lu,%lu,%lu][%lu,%lu,%lu]", stream, element_size*count, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt);
#ifdef LFS_FILE_SYS
    res = lfs_io_read( stream, buff, element_size * count );
#else
	res = fatIO_Read( stream, buff, element_size*count);
#endif

	if(res < 0)
	{
		FDI_errno = ERR_READ;
		DIAG_FILTER(FDI,fatsys,F_read_error, DIAG_INFORMATION)
    	diagPrintf("Fdi read error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file read error, res = %d", res);
		if(assertFlag==0)
		{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return 0;
	}


	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	DIAG_FILTER(FDI,fatsys,F_read_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi read succeed, handle: %d, actual size: %ld", stream, res);
	FDI_errno=ERR_NONE;
	return (res/element_size);
}

#ifdef LFS_FILE_SYS
size_t FDI_freadEx_fatsys(void *buff, size_t element_size, size_t count, FILE_ID stream, UINT32 filepostoread)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

    res = lfs_io_readEx( stream, buff, element_size * count, filepostoread);

	if(res < 0)
	{
		FDI_errno = ERR_READ;

		DIAG_FILTER(FDI,fatsys,F_readEx_error, DIAG_INFORMATION)
    	diagPrintf("Fdi read error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file readEx error, res = %d", res);

		if(assertFlag == 0)
		{
    		status = OSASemaphoreRelease(FatSysRef);
    		ASSERT(status == OS_SUCCESS);
		}
		return 0;
	}


	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return (res/element_size);
}
#endif

unsigned int FDI_fsize_fatsys(FILE_ID stream)
{
	int ori,cur;

	unsigned int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

#ifdef LFS_FILE_SYS

#if 0
	ori = lfs_io_ftell(stream);
	lfs_io_lseek(stream, 0, 2);
	res = lfs_io_ftell(stream);
	lfs_io_lseek(stream, ori, 0);
#endif

    res= lfs_io_size(stream);
#else
	res= fatIO_Size( stream);
#endif

	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	FDI_errno=ERR_NONE;
	return res;
}



size_t FDI_fwrite_fatsys(const void *buff, size_t element_size,
		  size_t count, FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);

	isFSbusy = 0xf;
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_write_start, DIAG_INFORMATION)
		diagPrintf("Fdi write start, handle: %d, size: %ld, [%lu,%lu,%lu][%lu,%lu,%lu]", stream, element_size*count, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_write(stream, buff, element_size * count );
#else
    res = fatIO_Write(stream, buff, element_size*count );
#endif
    if(res < 0)
	{
	    isFSbusy = 0x0;
		FDI_errno = ERR_WRITE;
		DIAG_FILTER(FDI,fatsys,F_write_error, DIAG_INFORMATION)
    	diagPrintf("Fdi write error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file write error,res = %d", res);
		if(assertFlag==0)
		{
			status = OSASemaphoreRelease(FatSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return 0;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_write_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi write succeed, handle: %d, actual size: %ld", stream, res);
	}
    isFSbusy = 0x0;
	if(assertFlag==0)
	{
	    status = OSASemaphoreRelease(FatSysRef);
	    ASSERT(status == OS_SUCCESS);
	}
	FDI_errno=ERR_NONE;
    return (res/element_size);
}

#ifdef LFS_FILE_SYS
size_t FDI_fwriteEx_fatsys(const void *buff, size_t element_size,
		  size_t count, FILE_ID stream, UINT32 filePostowrite)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);

	isFSbusy = 0xf;
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_writeEx_start, DIAG_INFORMATION)
	    diagPrintf("Fdi write start, handle: %d, size: %ld", stream, element_size*count);
	}

    res = lfs_io_writeEx(stream, buff, element_size * count,filePostowrite);

    if(res < 0)
	{
	    isFSbusy = 0x0;
		FDI_errno = ERR_WRITE;
		DIAG_FILTER(FDI,fatsys,F_writeEx_error, DIAG_INFORMATION)
    	diagPrintf("Fdi write error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file write error,res = %d", res);
		if(assertFlag==0)
		{
			status = OSASemaphoreRelease(FatSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return 0;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_writeEx_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi write succeed, handle: %d, actual size: %ld", stream, res);
	}
    isFSbusy = 0x0;
	if(assertFlag==0)
	{
	    status = OSASemaphoreRelease(FatSysRef);
	    ASSERT(status == OS_SUCCESS);
	}
	FDI_errno=ERR_NONE;
    return (res/element_size);
}
#endif

int FDI_fseek_fatsys(FILE_ID stream, long offset, int wherefrom)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_seek_start, DIAG_INFORMATION)
		diagPrintf("Fdi seek start, handle: %d, offset:%d, wherefrom: %d, [%lu,%lu,%lu][%lu,%lu,%lu]", stream,offset,wherefrom, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_lseek(stream, offset, wherefrom);
//uart_printf("lfs_io_lseek,res=%d\r\n",res);
#else
	res = fatIO_Seek(stream,offset,(wherefrom+1));
#endif

	if(res < 0)
	{
		FDI_errno=ERR_SYSTEM;
		DIAG_FILTER(FDI,fatsys,F_seek_error, DIAG_INFORMATION)
    	diagPrintf("Fdi seek error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file seek error, res = %d", res);
		if(assertFlag==0)
		{
			status = OSASemaphoreRelease(FatSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_seek_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi seek succeed, handle: %d,res: %d", stream,res);
	}
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return 0;
}


int FDI_ftell_fatsys(FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);

		DIAG_FILTER(FDI,fatsys,F_tell_start, DIAG_INFORMATION)
	    diagPrintf("Fdi tell start, handle: %d", stream);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_ftell(stream);
#else
	res=fatIO_Tell(stream);
#endif


	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,fatsys,F_tell_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi tell succeed, handle: %d", stream);

		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return res;
}




/****************************************************************************
* Functions: FDI_findfirst(), FDI_findnext();
*	 FDI5to6_cTime2Time(),FDI5_fileinfo_time2str(),
*	 FDI5to6_cTime2Date(),FDI5_fileinfo_date2str().
*
* DESCRIPTI
*    The function FDI_findfirst begins a search for files specified
*    wildcards.  The parameter filename_ptr is a string specifying the
*    file name.  Wildcard match characters (* and ?) are supported.  The
*    parameter fileinfo_ptr is a pointer to the type FILE_INFO which is
*    filled with the file information.
*
* USAGE:
*    status = FDI_findfirst("*.ext", &the_file_info);
*
* PARAMETERS:
*
* INPUTS:
*  filename_ptr      const character string for file name specifier
*
* OUTPUTS:
*  fileinfo_ptr      pointer to type FILE_INFO structure filled with
*                    located file information
*
* RETURNS:
*  Returns zero if successful in finding a file matching the search of
*  filename_ptr, otherwise it returns EOF.
*
* KNOWN BUGS:	In simulation, only one folder can be opened at a time.
* Openning of new folder cause to close previouse one.
*
* History:
* 23.05.05  dmp Initial version.
*
*****************************************************************************/

int FDI_findfirst_fatsys(const char *filename_ptr, FILE_INFO *fileinfo_ptr)
{
	int lastIndex;
	char fullFileName[OS_FILE_NAME_SIZE];
	char *slP=NULL;

	long rec;
	fatTYPE_sStat Stat;
	OSA_STATUS status;
	rollback=0;
	rollback_ori=0;

#ifdef LFS_FILE_SYS
	strcpy(WILDCARDS,filename_ptr);
	lfs_io_findall(filename_ptr);
	filename_ptr += lfs_get_prefixlen(filename_ptr);
#endif
	if(assertFlag != 1)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,fatsys,F_findfirst_start, DIAG_INFORMATION)
    diagPrintf("Fdi findfirst start, filename_ptr: %s", filename_ptr);

	memset(gFullFileName, 0, sizeof(gFullFileName));
	memset(fullFileName, 0, sizeof(fullFileName));

	// Make sure filename_ptr is valid length.
	// Can't call FileNameValid since filename_ptr can contain wild cards
	if ((filename_ptr == NULL) ||(strlen(filename_ptr) > FILE_NAME_SIZE))
	{
		FDI_errno = ERR_PARAM;
		DIAG_FILTER(FDI,fatsys,F_findfirst_error_0, DIAG_INFORMATION)
    	diagPrintf("Fdi findfirst error, ERR_PARAM");
		if(assertFlag != 1)
		{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

#ifdef LFS_FILE_SYS
    rec = lfs_io_findfirst(filename_ptr, fullFileName,&Stat);
//	rec = lfs_io_stat(filename_ptr, &Stat);
    if(rec != 0)
    {
    DIAG_FILTER(FDI,fatsys,F_findfirst_error_1, DIAG_INFORMATION)
    	diagPrintf("Fdi findfirst error 1, errCode: %d", rec);
        status = OSASemaphoreRelease(FatSysRef);
        ASSERT(status == OS_SUCCESS);
        return EOF;
    }
    isDir = 0;

    if(Stat.st_mode & fatTYPE_DIR)
        isDir = 0xf;

//    strcpy(fullFileName, filename_ptr);
#else

	//NameConvert(path, filename_ptr);

	rec = fatFILE_Find(fatFAT_USER_AREA, filename_ptr, fatTYPE_NORMAL);
	if(rec != 0)
	{
		FDI_errno = ERR_NOTEXISTS;
		DIAG_FILTER(FDI,fatsys,F_findfirst_error_1, DIAG_INFORMATION)
    	diagPrintf("Fdi findfirst error 1, errCode: %d", rec);
				if(assertFlag != 1){
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}


	do
	{

		isDir = 0;
		rec = fatFILE_FindNext(fatFAT_USER_AREA, fullFileName, &Stat);
		if(rec != 0)
		{
			if(rec == -7)
			{
				DIAG_FILTER(FDI,fatsys,F_findfirst_end, DIAG_INFORMATION)
	    		diagPrintf("Fdi findfirst there is not matching files");
			}
			else
			{
				DIAG_FILTER(FDI,fatsys,F_findfirst_error_2, DIAG_INFORMATION)
	    		diagPrintf("Fdi findfirst error 2, errCode: %d", rec);
			}
			FATSYS_TRACE("flash findfirst: there is not matching files!");
			FDI_errno = ERR_NOTEXISTS;
			if(assertFlag != 1)
			{
			status = OSASemaphoreRelease(FatSysRef);
			ASSERT(status == OS_SUCCESS);
			}
			return EOF;
		}
		if(Stat.st_mode&fatTYPE_DIR)
			isDir = 0xf;
	}while(!(strcmp(fullFileName,".") && strcmp(fullFileName,"..")) );
#endif

	strcpy(gFullFileName,fullFileName);


	/* we need to return the file name without the directory prefix.*/
	if ((slP = strrchr_sl(filename_ptr))!=NULL)
		filename_ptr = slP+1;


	strncpy(fileinfo_ptr->file_name,fullFileName,FILE_NAME_SIZE);
	fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

	fileinfo_ptr->plr_time=0;
	Stat.st_time=Fatsys_time(NULL);//

	fileinfo_ptr->time=FDI5to6_cTime2Time(Stat.st_time);
	fileinfo_ptr->date=FDI5to6_cTime2Date(Stat.st_time);
	fileinfo_ptr->size=Stat.st_size;

	fileinfo_ptr->owner_id=0x101;//FileGetUID();
	fileinfo_ptr->group_id=0x100;//FileGetGID();
	if(isDir)
		fileinfo_ptr->permissions=0x200;
	else
		fileinfo_ptr->permissions=FDI5_FILE_DEFAULT_PERMISSIONS;//my_dirent->mode;
	// next fields not in use
	fileinfo_ptr->data_id=0;
	fileinfo_ptr->plr_id=0;
	//fileinfo_ptr->plr_time=0; // is in use for dir
	//fileinfo_ptr->plr_date=0; // is in use for dirID
	fileinfo_ptr->plr_size=0;

	FDI_errno=ERR_NONE;

	DIAG_FILTER(FDI,fatsys,F_findfirst_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi findfirst succeed, the found file: %s", gFullFileName);
	if(assertFlag != 1)
	{
	status = OSASemaphoreRelease(FatSysRef);
	ASSERT(status == OS_SUCCESS);
	}
	return 0;
}//FDI_findfirst()


int FDI5to6_cTime2Time(const size_t clock)
{  int time;
   //struct tm *my_tm;
   //my_tm=localtime(&clock);

   t_rtc my_tm;
   pmic_rtc_to_tm(clock,&my_tm);
   time = my_tm.tm_hour<<(6+6);
   time+= my_tm.tm_min<<6;
   time+= my_tm.tm_sec;
   return time;
}//FDI5to6_cTime2Time

int FDI5to6_cTime2Date(const time_t clock)
{  int date;
   //struct tm *my_tm;
   //my_tm=localtime(&clock);

   t_rtc my_tm;
   pmic_rtc_to_tm(clock,&my_tm);
   date = my_tm.tm_mday<<(12+4);
   date+= my_tm.tm_mon<<12;
   date+= my_tm.tm_year;
   return date;
}//FDI5to6_cTime2Date


char * FDI5_fileinfo_time2str(char * str,int time)
{
  int hour = time >> (6+6);
  int min = (time >> 6) & 0x3f;
  int sec = time & 0x3f;
  if(!str) return NULL;
  sprintf(str,"%d:%02d:%02d",hour,min,sec);
  return str;
} // FDI5_fileinfo_time2str()

char * FDI5_fileinfo_date2str(char * str,int date)
{
  int day = date >> (12+4);
  int mon = (date >> 12) & 0xf;
  int year = date & 0xfff;
  if(!str) return NULL;
  sprintf(str,"%d.%02d.%02d",day+1,mon+1,year);
  return str;
} // FDI5_fileinfo_date2str()



// Original in FDI_FILE.c line 1906
int FDI_findnext_fatsys(FILE_INFO *fileinfo_ptr)
{
	char fullFileName[OS_FILE_NAME_SIZE];
	char *fileNameP=NULL;

	long rec;
	fatTYPE_sStat Stat;
	OSA_STATUS status;
	if(assertFlag != 1)
	{
	status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);
	}

//	DIAG_FILTER(FDI,fatsys,F_findnext_start, DIAG_INFORMATION)
//    diagPrintf("Fdi findnext start");

	memset(fullFileName, 0, sizeof(fullFileName));

	isDir = 0;

#ifdef LFS_FILE_SYS
	rec=lfs_io_findnext(fullFileName,&Stat);
#else
	rec = fatFILE_FindNext(fatFAT_USER_AREA, fullFileName, &Stat);
#endif
	if(rec != 0)
	{
		FDI_errno = ERR_NOTEXISTS;
		if(rec == -7)
		{
			DIAG_FILTER(FDI,fatsys,F_findnext_end, DIAG_INFORMATION)
	    	diagPrintf("Fdi findnext there is not matching files");
		}
		else
		{
			DIAG_FILTER(FDI,fatsys,F_findnext_error, DIAG_INFORMATION)
    		diagPrintf("Fdi findnext error errCode: %d", rec);
		}
		if(assertFlag != 1)
		{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	if(Stat.st_mode&fatTYPE_DIR)
		isDir = 0xf;

	if(strcmp(gFullFileName,fullFileName) == 0)
	{
		FDI_errno = ERR_NOTEXISTS;

		DIAG_FILTER(FDI,fatsys,F_findnext_finished, DIAG_INFORMATION)
    	diagPrintf("Fdi find process finished: the final file: %s", gFullFileName);
		if(assertFlag != 1)
		{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	strcpy(gFullFileName,fullFileName);



	strncpy(fileinfo_ptr->file_name,fullFileName,FILE_NAME_SIZE);
	fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

	fileinfo_ptr->plr_time=0;
	Stat.st_time=Fatsys_time(NULL);//

	fileinfo_ptr->time=FDI5to6_cTime2Time(Stat.st_time);
	fileinfo_ptr->date=FDI5to6_cTime2Date(Stat.st_time);
	fileinfo_ptr->size=Stat.st_size;

	fileinfo_ptr->owner_id=0x101;//FileGetUID();
	fileinfo_ptr->group_id=0x100;//FileGetGID();
	if(isDir)
		fileinfo_ptr->permissions=0x200;
	else
		fileinfo_ptr->permissions=FDI5_FILE_DEFAULT_PERMISSIONS;//my_dirent->mode;
	// next fields not in use
	fileinfo_ptr->data_id=0;
	fileinfo_ptr->plr_id=0;
	//fileinfo_ptr->plr_time=0; // is in use for dir
	//fileinfo_ptr->plr_date=0; // is in use for dirID
	fileinfo_ptr->plr_size=0;
	FDI_errno=ERR_NONE;

	DIAG_FILTER(FDI,fatsys,F_findnext_succeed, DIAG_INFORMATION)
    diagPrintf("Fdi findnext succeed: the found file: %s", gFullFileName);
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	return 0;

}//FDI_findnext()

/***************************************************
* Original in file ...  line 2004
* History:
* 18.05.05 DmitryP Intitial stab.
* 19.09.05 bamos   Initial verion.
*  5.02.06 DmitryP First workable verion.
***************************************************/
int FDI_remove_fatsys(const char *filename_ptr)
{
	int res;
	OSA_STATUS status;
	int partition = 0;

	if(assertFlag != 1)
	{
	status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,fatsys,F_remove_start, DIAG_INFORMATION)
    diagPrintf("Fdi remove start, file: %s", filename_ptr);

#ifdef LFS_FILE_SYS
    res = lfs_io_remove(filename_ptr);
#else
	//NameConvert(path, filename_ptr);
	res = fatFILE_Remove(fatFAT_USER_AREA,filename_ptr);
#endif

	if(res < 0)
	{
		FDI_errno=ERR_NOTEXISTS;
		DIAG_FILTER(FDI,fatsys,F_remove_error, DIAG_INFORMATION)
        diagPrintf("Fdi remove error, file: %s, errCode: %d", filename_ptr, res);

		FATSYS_TRACE("flash file remove error");
		if(assertFlag != 1)
		{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}
	partition = lfs_get_partition(filename_ptr);
	lfs_free_space_valid[partition]=0;

	DIAG_FILTER(FDI,fatsys,F_remove_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi remove succeed, file: %s", filename_ptr);
	if(assertFlag != 1)
	{
	status = OSASemaphoreRelease(FatSysRef);
	ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return ERR_NONE;
}//FDI_remove()

/*-----------------12/13/2005 11:19AM---------------
 * added for the fdi_ram in order to allow rename in the icat - Smadar
 * --------------------------------------------------*/
int FDI_rename_fatsys(const FDI_TCHAR *name, const FDI_TCHAR *new_name)
{
	int res = 0;
	OSA_STATUS status;
	if(assertFlag != 1)
	{
	status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);
	}

#ifdef LFS_FILE_SYS
    res = lfs_io_rename(name, new_name);
#else
	res = fatFILE_Rename(fatFAT_USER_AREA, name, new_name);
#endif

	if(res < 0)
	{
		FATSYS_TRACE("flash file rename error");
		if(assertFlag != 1)
		{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}
	if(assertFlag != 1)
	{
	status = OSASemaphoreRelease(FatSysRef);
	ASSERT(status == OS_SUCCESS);
	}
	FDI_errno=ERR_NONE;
	return ERR_NONE;
}
/*-----------------12/13/2005 11:19AM---------------
 * added for the fdi_ram in order to allow fdi_stat in the icat - Smadar
 * --------------------------------------------------*/

int FDI_stat_fatsys(const FDI_TCHAR *file_name, int *mode)
{
	return ERR_NONE;
}
/*-----------------12/13/2005 11:19AM---------------
 * added for the fdi_ram in order to allow fdi_chmod in the icat - Smadar
 * --------------------------------------------------*/

int FDI_chmod_fatsys(const FDI_TCHAR *file_name, int mode)
{
	int res = 0;
	return res;
}

ERR_CODE FDI_Format_fatsys(void)
{
#ifndef CRANE_MCU_DONGLE
    int ret_err = 0;
	OSA_STATUS status;
	if(assertFlag != 1)
	{
	status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
    ASSERT(status == OS_SUCCESS);
	}

	NAND_EraseAll();
	sdSYS_Format(fatFAT_USER_AREA,FAT);

	ret_err = sdSYS_Initialise(fatFAT_USER_AREA);
    if(ret_err != 0x0)
	{
		DIAG_FILTER(FDI,fatsys,Format_Success, DIAG_INFORMATION)
		diagPrintf("The volume was successfully formated!");
	}
	else
	{
		DIAG_FILTER(FDI,fatsys,Format_Error, DIAG_ERROR)
		diagPrintf("An error occurred while formating volume, errCode; %d", ret_err);
	}
	if(assertFlag != 1)
	{
	status = OSASemaphoreRelease(FatSysRef);
	ASSERT(status == OS_SUCCESS);
	}

//	OSAFlagSet( (OSFlagRef)FatSysEventRef, FATSYS_CLOSE_FLAG, OSA_FLAG_OR );
#endif
	return ERR_NONE;
}


int FDI_feof(FILE_ID stream)
{
    DIAG_FILTER(FDI,FDI5to6,FDI_feof,DIAG_ERROR)
    diagPrintf("FDI_feof() not implemented in FDI5 API to FDI7 converter");
    return ERR_NONE;

}//FDI_feof()



int FDI_feof_fatsys(FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,fatsys,F_eof_start, DIAG_INFORMATION)
	    diagPrintf("Fdi eof start,  handle: %d", stream);

#ifdef LFS_FILE_SYS
	res=lfs_io_eof(stream);
#else
	res=fatIO_EOF(stream);
#endif
	uart_printf("FDI_eof: %d\r\n",res);

	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	DIAG_FILTER(FDI,fatsys,F_eof_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi eof succeed, handle: %d,res: %d", stream,res);

	FDI_errno=ERR_NONE;
	return res;


}

#ifdef LFS_GET_FREE_OPTIMIZE
void lfs_free_space_update(FILE_ID Handle)
{
	int sec0, sec1;
	int partition = 0;

	if(lfs_free_space_init_flag==0)
		return;

	partition = GET_PARTITION_FROM_HANDLE(Handle);
	Handle -= HANDLE_BASE[partition];

//	uart_printf("lfs_free_space_update 01:[%d][%d]=%d, %d->%d\r\n", partition,Handle, lfs_free_space[partition], lfs_open_close_fsize_ary[partition][Handle][0], lfs_open_close_fsize_ary[partition][Handle][1]);
	sec0=lfs_open_close_fsize_ary[partition][Handle][0]/LFS_BLOCK_SIZE+1;
	sec1=lfs_open_close_fsize_ary[partition][Handle][1]/LFS_BLOCK_SIZE+1;

	if(sec0>=sec1)//open>=close
		lfs_free_space[partition] += (sec0-sec1)*LFS_BLOCK_SIZE;
	else
		lfs_free_space[partition] -= (sec1-sec0)*LFS_BLOCK_SIZE;

	if((lfs_open_close_fsize_ary[partition][Handle][0]==0)&&(lfs_open_close_fsize_ary[partition][Handle][1]!=0))
		lfs_free_space[partition] -= LFS_BLOCK_SIZE;
	else if((lfs_open_close_fsize_ary[partition][Handle][0]!=0)&&(lfs_open_close_fsize_ary[partition][Handle][1]==0))
		lfs_free_space[partition] += LFS_BLOCK_SIZE;
//	uart_printf("lfs_free_space_update 02:%d, cnt:%d\r\n", lfs_free_space[partition],lfs_get_free_cnt[partition]);
}
#endif

unsigned int lfs_free_space_get(const char *path_prefix)
{
	unsigned int size=0;
	int partition = 0;

#ifdef LFS_GET_FREE_OPTIMIZE
	if(lfs_free_space_init_flag==0)
	{
		lfs_io_statfs(path_prefix,(UINT32 *)&size);
		return size;
	}
	
	partition = lfs_get_partition(path_prefix);
    
	if((lfs_free_space_valid[partition]==0) ||(lfs_get_free_cnt[partition]>=GET_VIRTUAL_FREE_CNT_MAX))
	{
		lfs_io_statfs(path_prefix,(UINT32 *)&size);
		lfs_free_space[partition]=size;
		lfs_free_space_valid[partition]=1;
		lfs_get_free_cnt[partition]=0;
	}
	else
		lfs_get_free_cnt[partition]++;
	
	return lfs_free_space[partition];
#else
	lfs_io_statfs(path_prefix,(UINT32 *)&size);
	return size;
#endif
}

unsigned int FDI_get_free_fatsys(const char *path_prefix)
{
	int res;
	unsigned int size=0;
	OSA_STATUS status;

	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,fatsys,FDI_get_free_start, DIAG_INFORMATION)
	    diagPrintf("Fdi get free space start %s", path_prefix);

	size = lfs_free_space_get(path_prefix);

	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	DIAG_FILTER(FDI,fatsys,FDI_get_free_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi get free space %s succeed,size: %lu", path_prefix, size);

	FDI_errno=ERR_NONE;
	return size;
}

void lfs_free_space_init(void)
{
	int i=0;
	int index=0;

	if(fs_mode_is_fat()==1)
	{		
		lfs_free_space[0] = FDI_get_free_fatsys("");
		lfs_free_space_valid[0]=1;
		lfs_get_free_cnt[0]=0;
		index++;
		
#ifdef DUAL_NVM_SUPPORT
		i=PARTITION_EXTRA_SPI;
		lfs_free_space[i] = FDI_get_free_fatsys(PARTITION_PREFIX[i]);
		lfs_free_space_valid[i]=1;
		lfs_get_free_cnt[i]=0;
		index++;
#endif
#ifdef EMMC_MULTI_PARTITION
		while(index<NUM_OF_PARTITION)
		{
			i=PARTITION_EMMC_0;
			lfs_free_space[i] = FDI_get_free_fatsys(PARTITION_PREFIX[i]);
			lfs_free_space_valid[i]=1;
			lfs_get_free_cnt[i]=0;
			index++;
			i++;
		}
#endif	

		lfs_free_space_init_flag=1;
		uart_printf("lfs_free_space_init [%d], %d Bytes\r\n", lfs_free_space[0]);
	}
}

#if defined(CRANE_MCU_DONGLE) && defined(LFS_FILE_SYS)
/*****************************************************************************
* Function Name: FDI_Access()
*
* DESCRIPTION:
*    Determine file or directory access permission.
*
* USAGE:
*    int FDI_Access(const char *path, UINT32 mode)
*
* PARAMETERS:
*
* INPUTS:
*  filename_ptr  const character null terminated string of the filename
*                to check
*
* OUTPUTS:
*
* RETURNS:
*  Returns TRUE if filename is valid; otherwise, it returns FALSE.
*
* HISTORY:
* 20.05.05 dmp Took from FDI5 from FDI_FILE.c:3875; and simplifyed.
*
***************************************************************************/
int FDI_Access(const char *path, UINT32 mode)
{
    int ret = ERR_NONE;
    OSA_STATUS status;

    FATSYS_TRACE("%s %s", __FUNCTION__, path);

    if(assertFlag == 0)
    {
        status = OSASemaphoreAcquire(FatSysRef, OS_SUSPEND);
        ASSERT(status == OS_SUCCESS);
    }

    ret = lfs_io_Access(path, mode);

    if(assertFlag == 0)
    {
        status = OSASemaphoreRelease(FatSysRef);
        ASSERT(status == OS_SUCCESS);
    }

    FATSYS_TRACE("%s %s end", __FUNCTION__, path);

    return ret;
}
#endif

#ifdef LFS_FILE_SYS
/*****************************************************************************
* Function Name: FDI_Stat()
*
* DESCRIPTION:
*    None.
*
* USAGE:
*    int FDI_Stat(const char *file_name, fatTYPE_sStat *stat)
*
* PARAMETERS:
*
* INPUTS:
*  filename_ptr  const character null terminated string of the filename
*                to check
*
* OUTPUTS:
*
* RETURNS:
*  Returns TRUE if filename is valid; otherwise, it returns FALSE.
*
* HISTORY:
* 20.05.05 dmp Took from FDI5 from FDI_FILE.c:3875; and simplifyed.
*
***************************************************************************/
int FDI_Stat(const char *file_name, fatTYPE_sStat *stat)
{
    int ret = ERR_NONE;
    OSA_STATUS status;

    FATSYS_TRACE("%s %s", __FUNCTION__, file_name);
    if(fs_mode_is_fat()==0)
        ASSERT(0);

    if(assertFlag == 0)
	{
		status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

    ret = lfs_io_stat(file_name, stat);

    if(assertFlag == 0)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

    FATSYS_TRACE("%s %s end", __FUNCTION__, file_name);

    return ret;
}

extern int NVMFileNameMatch(const char *src, const char *dest);


/***********************************************************************
 *
 * Name:		lfs_io_findall
 *
 * Description: traversed root dir and save info into FILE_LIST_LFS[MAX_NUM_OF_FILES]
 * Parameters:
 *  char                 filename_ptr      [IN]    filename_ptr to find/match.
 * Returns:
 *		  0:		dir traverse  done
 *		  1:		dir traverse not  done
 *		-1:		opendDir / failed
 *
 * Notes:
 *
 ***********************************************************************/
int lfs_io_findall(const char *filename_ptr)
{
	FILE_ID  hFileID;
	FILE_INFO	file_info;				/* Holds the file information				*/
	int i=0,j=0;
	int fmatch=0;
	int ret=0;
	
	FATSYS_TRACE("lfs_io_findall: %s %d\r\n",filename_ptr,rollback);

	hFileID = FDI_OpenDir(lfs_get_prefix(filename_ptr));
	if (hFileID == 0)
	{
		FATSYS_TRACE("lfs_io_findall: fail to open Current DIR");
		return -1;
	}

	filename_ptr += lfs_get_prefixlen(filename_ptr);
	while((FDI_DirRead(hFileID, &file_info)== 1) && (file_info.file_name[0] != 0))//exit when 1. dir traverse done; 2. not done but up to max and matched
	{
		j++;
		if(factory_reset_flag==0)
		{
			if(j<=rollback*MAX_NUM_OF_FILES)
				continue;
		}

		strcpy(FILE_LIST_LFS[i].file_name,file_info.file_name);
		FILE_LIST_LFS[i].size=file_info.size;
		FILE_LIST_LFS[i].permissions=file_info.permissions;
//		FILE_LIST_LFS[i].time=file_info.time;
//		FILE_LIST_LFS[i].date=file_info.date;
		FATSYS_TRACE("[%d %d]:name:%s,size: %d,0x%x\r\n",j,i,FILE_LIST_LFS[i].file_name,FILE_LIST_LFS[i].size,FILE_LIST_LFS[i].permissions);
		
		if (lfs_fname_match(FILE_LIST_LFS[i].file_name, filename_ptr) /*&& FILE_LIST_LFS[index].flag_exist*/)
			fmatch=1;
		
		i++;
		if(i>=MAX_NUM_OF_FILES)
		{
			if(fmatch==0)
			{
				i=0;
				rollback++;
			}
			else
			{
				ret=1;	//dir traverse not  done
				break;
			}
		}
		
	}
	FDI_CloseDir(hFileID);
	FILE_NUM_LFS=i;
	FATSYS_TRACE("find total files:%d,ret:%d,fmatch:%d, rollback: %d\r\n",FILE_NUM_LFS, ret, fmatch, rollback);

	return ret;
}

void lfsSetFileInfo(UINT32 index, fatTYPE_sStat *file_information)
{
	if(!PLATFORM_IS_FPGA)//Z2 HAPS has no PMIC
	{
		unsigned long clock;
		clock=Fatsys_time(NULL);

		FILE_LIST_LFS[index].time=FDI5to6_cTime2Time(clock);
		FILE_LIST_LFS[index].date=FDI5to6_cTime2Date(clock);
	}

	file_information->st_size=FILE_LIST_LFS[index].size;
	file_information->st_time=FILE_LIST_LFS[index].time;
	if(FILE_LIST_LFS[index].permissions==FDI5_FILE_DEFAULT_PERMISSIONS)
		file_information->st_mode=fatTYPE_NORMAL;
	else
		file_information->st_mode=fatTYPE_DIR;

} /* end of SetFileInfo */

int lfs_io_findfirst(const char *wildcard ,char *Filename,fatTYPE_sStat *Stat)
{
	UINT32 index;

	FATSYS_TRACE("lfs_io_findfirst %s, %d/%d\r\n",wildcard,rollback_ori,rollback);

	if(FILE_NUM_LFS==0)
		return (-2);

	for (index = 0; index < FILE_NUM_LFS; index ++)
	{
		if (lfs_fname_match(FILE_LIST_LFS[index].file_name, wildcard) /*&& FILE_LIST_LFS[index].flag_exist*/)
		{
			lfsSetFileInfo(index, Stat);

			/*the index of the file which was last found and the wildcard should be saved in order to use
			 * it later in the find next function */
			LastFound = index;
			rollback_ori=rollback;

			strcpy (Filename, FILE_LIST_LFS[index].file_name);
//			strcpy(WILDCARDS,wildcard);
			return 0;
		}

	}

	FATSYS_TRACE("lfs_io_findfirst end, -1\r\n");
	return (-1);
}
int lfs_io_findnext(char *Filename,fatTYPE_sStat *Stat)
{
	UINT32 index;
	int ret=1;
	char *wildcard_no_prefix;
	
	wildcard_no_prefix = WILDCARDS + lfs_get_prefixlen(WILDCARDS);
	FATSYS_TRACE("lfs_io_findnext, %s,%s,%d%d,%d\r\n",WILDCARDS,wildcard_no_prefix,rollback_ori,rollback,FILE_NUM_LFS);
	
	while((ret== 1)||((ret== 0)&&(FILE_NUM_LFS>0)))
	{
		if(rollback_ori!=rollback)
			index=0;
		else
			index=LastFound+1;
	//	for (index/* = (LastFound+1)*/; index < FILE_NUM_LFS; index ++)
		while(index < FILE_NUM_LFS)
		{
			if (lfs_fname_match(FILE_LIST_LFS[index].file_name, wildcard_no_prefix) /*&& FILE_LIST_LFS[index].flag_exist*/)
			{
//				FATSYS_TRACE("lfs_io_findnext %d,[%d] %s,%d%d\r\n",FILE_NUM_LFS,index,FILE_LIST_LFS[index].file_name,rollback_ori,rollback);
				lfsSetFileInfo(index, Stat);
				strcpy (Filename, FILE_LIST_LFS[index].file_name);
				LastFound = index;
				rollback_ori = rollback;
				return 0;
			}
			index ++;
		}
		if(FILE_NUM_LFS<MAX_NUM_OF_FILES)
		{
			FATSYS_TRACE("lfs_io_findnext none1, rollback_ori/rollback:%d/%d, %d\r\n",rollback_ori,rollback,FILE_NUM_LFS);
			return (-1);
		}
		
		rollback++;
		FatSys_Lock_Type=1;
		ret= lfs_io_findall(WILDCARDS);
		FatSys_Lock_Type=0;
	}
	FATSYS_TRACE("lfs_io_findnext none2, rollback_ori/rollback:%d/%d, %d\r\n",rollback_ori,rollback,FILE_NUM_LFS);

	return (-1);
}
#endif

ERR_CODE FDI_ferror_fatsys(FILE_ID stream)
{
	return FDI_errno;

}//FDI_ferror()

static int FileNameValid(const FDI_TCHAR *filename_ptr)
{
    int i = 0;

    // Invalid filename characters array.
    // Must end with end of string character as array end marker.
    const FDI_TCHAR InvalidFilenameChars[] = { FILE_SCWILDCARD, FILE_MCWILDCARD, FILE_EOS};

    if (filename_ptr == NULL)
    {
        return FALSE;
    }

    if( filename_ptr[0] == 0 )
    {
        return FALSE;
    }

    if(strlen(filename_ptr) > FILE_NAME_SIZE )
    {
        return FALSE;
    }

    // check that filename_ptr contains no invalid characters
    for(i = 0; InvalidFilenameChars[i] != FILE_EOS; i++)
    {
        if(strchr(filename_ptr, InvalidFilenameChars[i]))
        {
            return FALSE;
        }
    }
    return TRUE;
}

#endif


FILE_ID FDI_opendir_fatsys(const char *dir_name)
{
    int handle = 0;
    FILE_ID hDirID;
    OSA_STATUS status;

    FATSYS_TRACE("%s %s", __FUNCTION__, dir_name);
    if(fs_mode_is_fat()==0)
        ASSERT(0);

    if(FatSys_Lock_Type==0)
    {
        status = OSASemaphoreAcquire(FatSysRef, OS_SUSPEND);
        ASSERT(status == OS_SUCCESS);
    }

    handle = lfs_io_dir_open(dir_name);

    if(handle < 0)
    {
        DIAG_FILTER(FDI,fatsys,F_Opendir, DIAG_INFORMATION)
        diagPrintf("Fdi open dir error: %s, status: %d", dir_name, handle);
        FATSYS_TRACE("hDirID = %d", handle);

        goto toNULL;
    }
    if(FatSys_Lock_Type==0)
    {
        status = OSASemaphoreRelease(FatSysRef);
        ASSERT(status == OS_SUCCESS);
    }
	DIAG_FILTER(FDI,fatsys,F_Opendir_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi open dir succeed: %s: handle: %d", dir_name, handle);

    FATSYS_TRACE("%s %s end", __FUNCTION__, dir_name);

    hDirID = (FILE_ID)handle;

    return FAT_SYS_INC(hDirID);

toNULL:
    FATSYS_TRACE("DIR open error: %s", dir_name);
    if(FatSys_Lock_Type==0)
    {
        status = OSASemaphoreRelease(FatSysRef);
        ASSERT(status == OS_SUCCESS);
    }

    return FILE_NULL;
}

int FDI_dirread_fatsys(FILE_ID hDirID, FILE_INFO *fileinfo_ptr)
{
    int rec = 0;
    OSA_STATUS status;
    struct lfs_info info;

	extern UINT32 LfsAcatDirEnableFlag;

    FAT_SYS_DEC(hDirID);

    FATSYS_TRACE("%s %d", __FUNCTION__, hDirID);
    if(fs_mode_is_fat()==0)
        ASSERT(0);

    if(FatSys_Lock_Type==0)
    {
        status = OSASemaphoreAcquire(FatSysRef, OS_SUSPEND);
        ASSERT(status == OS_SUCCESS);
    }
	
    memset(&info, 0x00, sizeof(struct lfs_info));
    rec = lfs_io_dir_read(hDirID, &info);
    if(rec != 1)
    {
        if(FatSys_Lock_Type==0)
        {
            status = OSASemaphoreRelease(FatSysRef);
            ASSERT(status == OS_SUCCESS);
        }
        return rec;
    }

    strncpy(fileinfo_ptr->file_name, info.name, FILE_NAME_SIZE + 1);
    fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

    fileinfo_ptr->plr_time = 0;

#if 0
    fileinfo_ptr->time = FDI5to6_cTime2Time(Stat.st_time);
    fileinfo_ptr->date = FDI5to6_cTime2Date(Stat.st_time);
#else
    fileinfo_ptr->time = 0;
    fileinfo_ptr->date = 0;
#endif
    
    fileinfo_ptr->owner_id = 0x101; //FileGetUID();
    fileinfo_ptr->group_id = 0x100; //FileGetGID();

    if(LfsAcatDirEnableFlag==1)
    {
        if(info.type == LFS_TYPE_DIR)
        {
            fileinfo_ptr->permissions = 0x3FF;
            fileinfo_ptr->size = 160;
        }
        else
        {
            fileinfo_ptr->permissions = FDI5_FILE_DEFAULT_PERMISSIONS; //my_dirent->mode;
            fileinfo_ptr->size = info.size;
        }
    }
    else
    {
        fileinfo_ptr->size = info.size;
        
        if(info.type == LFS_TYPE_DIR)
            fileinfo_ptr->permissions = 0x200;
        else
            fileinfo_ptr->permissions = FDI5_FILE_DEFAULT_PERMISSIONS; //my_dirent->mode;
    }

    // next fields not in use
    fileinfo_ptr->data_id = 0;
    fileinfo_ptr->plr_id = 0;
    //fileinfo_ptr->plr_time=0; // is in use for dir
    //fileinfo_ptr->plr_date=0; // is in use for dirID
    fileinfo_ptr->plr_size = 0;

    if(FatSys_Lock_Type==0)
    {
        status = OSASemaphoreRelease(FatSysRef);
        ASSERT(status == OS_SUCCESS);
    }

//    FATSYS_TRACE("%s %d end", __FUNCTION__, hDirID);
    return 1;
}

int FDI_closedir_fatsys(FILE_ID stream)
{
    int res;
    OSA_STATUS status;
	FILE_ID fdiID = stream;

    FAT_SYS_DEC(stream);
    isFSbusy = 0xf;

    FATSYS_TRACE("%s %d", __FUNCTION__, stream);
    if(fs_mode_is_fat()==0)
        ASSERT(0);

    if(FatSys_Lock_Type==0)
    {
        status = OSASemaphoreAcquire(FatSysRef, OS_SUSPEND);
        ASSERT(status == OS_SUCCESS);
    }

    res = lfs_io_dir_close(stream);
    if(res < 0)
    {
        isFSbusy = 0x0;

        FDI_errno = ERR_ACCESS;

        DIAG_FILTER(FDI,fatsys,F_Closedir, DIAG_INFORMATION)
        diagPrintf("Fdi close dir error: handle:%d, status: %d", stream, res);
        FATSYS_TRACE("file close error, res = %d", res);
        if(FatSys_Lock_Type==0)
	{
            status = OSASemaphoreRelease(FatSysRef);
            ASSERT(status == OS_SUCCESS);
        }
		
        return EOF;
    }

    isFSbusy = 0x0;

    FDI_errno = ERR_NONE;

    DIAG_FILTER(FDI,fatsys,F_Closedir_succeed, DIAG_INFORMATION)
    diagPrintf("Fdi close dir succeed:handle: %d,res: %d", stream, res);
    FATSYS_TRACE("dir close succeed, res = %d", res);
    if(FatSys_Lock_Type==0)
    {
        status = OSASemaphoreRelease(FatSysRef);
        ASSERT(status == OS_SUCCESS);
    }

    FATSYS_TRACE("%s %d end", __FUNCTION__, stream);

    return 0;
}

int  FDI_mkdir_fatsys(const char *dir_name)
{
    OSA_STATUS status;
    int res;
    int partition = 0;
    FATSYS_TRACE("%s %s", __FUNCTION__, dir_name);
    if(fs_mode_is_fat()==0)
        ASSERT(0);

    status = OSASemaphoreAcquire(FatSysRef, OS_SUSPEND);
    ASSERT(status == OS_SUCCESS);

    res = lfs_io_dir_make(dir_name);

    if(res<0)
    {
	DIAG_FILTER(FDI,fatsys,F_Makedir, DIAG_INFORMATION)
	diagPrintf("Fdi make dir error: %s, status: %d", dir_name, res);
	FATSYS_TRACE("DIR make error: res=%d", res);
        goto toNULL;
    }

#ifndef CRANEL_FP_8MRAM
    partition = lfs_get_partition(dir_name);
    lfs_free_space_valid[partition]=0;
#endif

    status = OSASemaphoreRelease(FatSysRef);
    ASSERT(status == OS_SUCCESS);
    DIAG_FILTER(FDI,fatsys,F_Makedir_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi make dir succeed: %s: status: %d", dir_name, res);
	FATSYS_TRACE("%s %s end", __FUNCTION__, dir_name);
    return 0;
toNULL:
    status = OSASemaphoreRelease(FatSysRef);
    ASSERT(status == OS_SUCCESS);
    return EOF;
}

int FDI_rmdir_fatsys(const char *dir_name)	//mq, if remove file, return -1
{
	int res;
	OSA_STATUS status;
	int partition = 0;
	if(fs_mode_is_fat()==0)
		ASSERT(0);

	if(assertFlag != 1)
	{
	status = OSASemaphoreAcquire(FatSysRef,OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,fatsys,F_removeDir_start, DIAG_INFORMATION)
	diagPrintf("Fdi removeDir start, dir: %s", dir_name);

	res = lfs_io_removeDir(dir_name);
	
	if(res < 0)
	{
		FDI_errno=ERR_NOTEXISTS;
		DIAG_FILTER(FDI,fatsys,F_removeDir_error, DIAG_INFORMATION)
	        diagPrintf("Fdi removeDir error, dir: %s, errCode: %d", dir_name, res);

		FATSYS_TRACE("flash file remove error");
		if(assertFlag != 1)
		{
			status = OSASemaphoreRelease(FatSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

#ifndef CRANEL_FP_8MRAM
	partition = lfs_get_partition(dir_name);
	lfs_free_space_valid[partition]=0;
#endif

	DIAG_FILTER(FDI,fatsys,F_removeDir_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi removeDir succeed, dir: %s", dir_name);
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(FatSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return ERR_NONE;
}//FDI_removeDir()

#ifdef SPINAND_SUPPORT

#include "yaffsfs.h"

OSSemaRef YaffsSysRef;
yaffs_list2_t  yaffs_file_list;
UINT32 YaffsAcatDirEnableFlag=0;

extern unsigned int FILE_NUM_YAFFS;

void yaffs_nvm_lock_init(void)
{
	OSA_STATUS status;

	status = OSASemaphoreCreate (&YaffsSysRef,1,OSA_FIFO);
	ASSERT(status == OS_SUCCESS);
}

char yaffs_full_name[128]; 
FILE_ID  FDI_fopen_yaffs(const char *filename_ptr, const char *mode)
{
	UINT32 oflag;
	WORD32 handle=0;
	FILE_ID hFileID;
	OSA_STATUS status;

//	char full_name[128]; 

//	uart_printf("FDI_fopen_yaffs %s %s\r\n", filename_ptr, mode);

	
#ifdef FS_TIME_TEST
	unsigned long timestamp;
	timestamp = timerCountRead(TS_TIMER_ID);
#endif
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_Open_start, DIAG_INFORMATION)
		diagPrintf("Fdi open start: %s: mode: %s, [%lu,%lu,%lu][%lu,%lu,%lu] %lu", filename_ptr, mode, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt, link_list_len);
	}
   // make sure filename_ptr is valid
   if (FileNameValid(filename_ptr) == FALSE)
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   //NameConvert(path, filename_ptr);

   // testing mode
   if(strlen(mode)<2 || strlen(mode)>3)
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   // Next flags set according to first case of table 9 of FDI_6.0_UG.pdf
   switch (mode[0])
   {
	  case 'r': case 'R':
		 oflag = fatIO_RDONLY;
		 break;
	  case 'w': case 'W':
		 oflag = fatIO_RDWR | fatIO_CREATE | fatIO_TRUNCATE;
		 break;
	  case 'a': case 'A':
		 oflag = fatIO_RDWR | fatIO_APPEND | fatIO_CREATE;
		 break;
	  default:
	 FDI_errno = ERR_PARAM; goto toNULL;
   }

   if(mode[1]!='b' && mode[1]!='B' )
   {  FDI_errno = ERR_PARAM; goto toNULL; }

   if(mode[2]=='+')
   switch (mode[0])
   {
	  case 'r': case 'R':
		 oflag = fatIO_RDWR;
		 break;
	  case 'w': case 'W':
		 oflag = fatIO_RDWR | fatIO_CREATE | fatIO_TRUNCATE;
		 break;
	  case 'a': case 'A':
		 oflag = fatIO_RDWR | fatIO_APPEND | fatIO_CREATE;
		 break;
   }
   else if(mode[2]!=0)
   {  FDI_errno = ERR_PARAM; goto toNULL; }
   isFSbusy = 0xf;


	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		sprintf(yaffs_full_name, "/spinand/%s",filename_ptr);
		handle = yaffs_open(yaffs_full_name, oflag,  fatTYPE_NORMAL);
	}

    if(handle < 0)
		goto toNULL;

#ifdef FS_TIME_TEST
	FS_fileopentime(filename_ptr,(FILE_ID)(handle+1),timestamp);
#endif
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_Open_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi open succeed: %s: handle: %d", filename_ptr, handle);
	}
	FATSYS_TRACE("flash file open succeed: %s", filename_ptr);
	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	isFSbusy = 0x0;
	hFileID = (FILE_ID)handle;

//	uart_printf("open ok\r\n", filename_ptr, mode);

   return FAT_SYS_INC(hFileID);
toNULL:
   FDI_errno = ERR_ACCESS;
   DIAG_FILTER(FDI,yaffs,F_Open_error, DIAG_INFORMATION)
   diagPrintf("Fdi open error: %s, status: %d", filename_ptr, handle);
   FATSYS_TRACE("flash file open error: %s, status: %d", filename_ptr, handle);
   if(assertFlag==0)
   {
	   status = OSASemaphoreRelease(YaffsSysRef);
	   ASSERT(status == OS_SUCCESS);
   }
//   uart_printf("open fail\r\n", filename_ptr, mode);
   return FILE_NULL;
}

int FDI_fclose_yaffs(FILE_ID stream)
{
	int res;
	OSA_STATUS status;

//	uart_printf("FDI_fclose_yaffs %d\r\n", stream);
	
	FAT_SYS_DEC(stream);
	isFSbusy = 0xf;
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_Close_start, DIAG_INFORMATION)
		diagPrintf("Fdi close start,  handle: %d, [%lu,%lu,%lu][%lu,%lu,%lu] %lu", stream, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt, link_list_len);
	}

	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		res = yaffs_close(stream);
	}

#ifdef FS_TIME_TEST
	FS_fileclosetime(stream);
#endif
	if(res < 0)
	{
		FDI_errno=ERR_SYSTEM;
		isFSbusy = 0x0;

		DIAG_FILTER(FDI,yaffs,F_Close_error, DIAG_INFORMATION)
		diagPrintf("Fdi close error, handle: %d, errCode: %d", stream, res);

		FATSYS_TRACE("flash file close error, res = %d", res);
		if(assertFlag==0)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	isFSbusy = 0x0;
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_Close_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi close succeed, handle: %d", stream);
	}
	FATSYS_TRACE("flash file close succeed, res = %d", res);
	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE; return 0;
}

size_t FDI_fread_yaffs(void *buff, size_t element_size, size_t count, FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,yaffs,F_read_start, DIAG_INFORMATION)
	diagPrintf("Fdi read start, handle: %d, size: %ld, [%lu,%lu,%lu][%lu,%lu,%lu]", stream, element_size*count, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt);


	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		res = yaffs_read(stream, buff, element_size*count);
	}

	if(res < 0)
	{
		FDI_errno = ERR_READ;
		DIAG_FILTER(FDI,yaffs,F_read_error, DIAG_INFORMATION)
    	diagPrintf("Fdi read error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file read error, res = %d", res);
		if(assertFlag==0)
		{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return 0;
	}


	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	DIAG_FILTER(FDI,yaffs,F_read_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi read succeed, handle: %d, actual size: %ld", stream, res);
	FDI_errno=ERR_NONE;
	return (res/element_size);
}


size_t FDI_fwrite_yaffs(const void *buff, size_t element_size,
		  size_t count, FILE_ID stream)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);

	isFSbusy = 0xf;
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_write_start, DIAG_INFORMATION)
		diagPrintf("Fdi write start, handle: %d, size: %ld, [%lu,%lu,%lu][%lu,%lu,%lu]", stream, element_size*count, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt);
	}

	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		res = yaffs_write(stream, buff, element_size * count);
	}


    if(res < 0)
	{
	    isFSbusy = 0x0;
		FDI_errno = ERR_WRITE;
		DIAG_FILTER(FDI,yaffs,F_write_error, DIAG_INFORMATION)
    	diagPrintf("Fdi write error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file write error,res = %d", res);
		if(assertFlag==0)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return 0;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_write_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi write succeed, handle: %d, actual size: %ld", stream, res);
	}
    isFSbusy = 0x0;
	if(assertFlag==0)
	{
	    status = OSASemaphoreRelease(YaffsSysRef);
	    ASSERT(status == OS_SUCCESS);
	}
	FDI_errno=ERR_NONE;
    return (res/element_size);
}

int FDI_fseek_yaffs(FILE_ID stream, long offset, int wherefrom)
{
	int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}
	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_seek_start, DIAG_INFORMATION)
		diagPrintf("Fdi seek start, handle: %d, offset:%d, wherefrom: %d, [%lu,%lu,%lu][%lu,%lu,%lu]", stream,offset,wherefrom, total_read_cnt, read_in_cache_cnt, read_out_cache_cnt, total_write_cnt, write_in_cache_cnt, write_out_cache_cnt);
	}


	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		res = yaffs_lseek(stream, offset, wherefrom);
	}

	if(res < 0)
	{
		FDI_errno=ERR_SYSTEM;
		DIAG_FILTER(FDI,yaffs,F_seek_error, DIAG_INFORMATION)
    	diagPrintf("Fdi seek error, handle: %d, errCode: %d", stream, res);
		FATSYS_TRACE("flash file seek error, res = %d", res);
		if(assertFlag==0)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_seek_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi seek succeed, handle: %d,res: %d", stream,res);
	}
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return 0;
}

int FDI_ftell_yaffs(FILE_ID stream)
{
	int res;
	OSA_STATUS status;
	extern int yaffs_ftell(int handle);
	
	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);

		DIAG_FILTER(FDI,yaffs,F_tell_start, DIAG_INFORMATION)
	    diagPrintf("Fdi tell start, handle: %d", stream);
	}

	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		res=yaffs_ftell(stream);
	}

	if(assertFlag != 1)
	{
		DIAG_FILTER(FDI,yaffs,F_tell_succeed, DIAG_INFORMATION)
		diagPrintf("Fdi tell succeed, handle: %d", stream);

		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return res;
}

unsigned int FDI_fsize_yaffs(FILE_ID stream)
{
	int ori,cur;
	extern int yaffs_fsize(int handle);
	unsigned int res;
	OSA_STATUS status;

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		res=yaffs_fsize(stream);
	}

	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	FDI_errno=ERR_NONE;
	return res;
}

int FDI_remove_yaffs(const char *filename_ptr)
{
	int res;
	OSA_STATUS status;

	char full_name[128];
	
	if(assertFlag != 1)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,yaffs,F_remove_start, DIAG_INFORMATION)
    diagPrintf("Fdi remove start, file: %s", filename_ptr);

	if(get_fs_mode()==YAFFS_FS_MODE)
	{
	    sprintf(full_name, "/spinand/%s",filename_ptr);
	    res = yaffs_unlink(full_name);
	}


	if(res < 0)
	{
		FDI_errno=ERR_NOTEXISTS;
		DIAG_FILTER(FDI,yaffs,F_remove_error, DIAG_INFORMATION)
        diagPrintf("Fdi remove error, file: %s, errCode: %d", filename_ptr, res);

		FATSYS_TRACE("flash file remove error");
		if(assertFlag != 1)
		{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	DIAG_FILTER(FDI,yaffs,F_remove_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi remove succeed, file: %s", filename_ptr);
	if(assertFlag != 1)
	{
	status = OSASemaphoreRelease(YaffsSysRef);
	ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return ERR_NONE;
}//FDI_remove()

ERR_CODE FDI_ferror_yaffs(FILE_ID stream)
{
	return FDI_errno;

}

int FDI_rename_yaffs(const FDI_TCHAR *name, const FDI_TCHAR *new_name)
{
	int res = 0;
	OSA_STATUS status;

	char full_name_old[128]; 
	char full_name_new[128]; 

	if(assertFlag != 1)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		sprintf(full_name_old, "/spinand/%s",name);
		sprintf(full_name_new, "/spinand/%s",new_name);
		res = yaffs_rename(full_name_old, full_name_new);
	}

	if(res < 0)
	{
		FATSYS_TRACE("flash file rename error");
		if(assertFlag != 1)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	FDI_errno=ERR_NONE;
	return ERR_NONE;
}

int FDI_stat_yaffs(const FDI_TCHAR *file_name, int *mode)
{
	return ERR_NONE;
}

int FDI_chmod_yaffs(const FDI_TCHAR *file_name, int mode)
{
	int res = 0;
	return res;
}

ERR_CODE FDI_Format_yaffs(void)
{
	extern int yaffs_format(const char *path,
			int unmount_flag,
			int force_unmount_flag,
			int remount_flag);

	int ret_err = 0;
	OSA_STATUS status;
	if(assertFlag != 1)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		yaffs_format("/spinand", 1, 1, 1);
	}

	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}

//	OSAFlagSet( (OSFlagRef)FatSysEventRef, FATSYS_CLOSE_FLAG, OSA_FLAG_OR );
	return ERR_NONE;
}

int FDI_feof_yaffs(FILE_ID stream)
{
	int res;
	OSA_STATUS status;
	extern int yaffs_eof(int handle);

	FAT_SYS_DEC(stream);
	if(assertFlag==0)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,yaffs,F_eof_start, DIAG_INFORMATION)
	    diagPrintf("Fdi eof start,  handle: %d", stream);


	if(get_fs_mode()==YAFFS_FS_MODE)
	{
		res = yaffs_eof(stream);
	}

//	uart_printf("FDI_eof: %d\r\n",res);

	if(assertFlag==0)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	DIAG_FILTER(FDI,yaffs,F_eof_succeed, DIAG_INFORMATION)
	    diagPrintf("Fdi eof succeed, handle: %d,res: %d", stream,res);

	FDI_errno=ERR_NONE;
	return res;
}

void fill_file_list_lfs(int idx, char *fname, int len, int type)
{

	strcpy(FILE_LIST_LFS[idx].file_name,fname);

	if(YaffsAcatDirEnableFlag==1)
	{
		if(type==0x3)
		{
			FILE_LIST_LFS[idx].permissions=0x3FF;
			FILE_LIST_LFS[idx].size=160;
		}
		else
		{
			FILE_LIST_LFS[idx].permissions=FDI5_FILE_DEFAULT_PERMISSIONS;
			FILE_LIST_LFS[idx].size=len;
		}

	}
	else
	{

		if(type==0x3)
		{
			FILE_LIST_LFS[idx].permissions=0x200;
			FILE_LIST_LFS[idx].size=0;
		}
		else
		{
			FILE_LIST_LFS[idx].permissions=FDI5_FILE_DEFAULT_PERMISSIONS;
			FILE_LIST_LFS[idx].size=len;
		}
	}

}
FILE_ID FDI_opendir_yaffs(const char *dir_name)
{
    int handle = 0;
    FILE_ID hDirID;
    OSA_STATUS status;
	char full_name[128];
//	extern int yaffs_io_dir_open(const char *path);

    FATSYS_TRACE("%s %s", __FUNCTION__, dir_name);
	
    if(fs_mode_is_yaffs()==0)
        ASSERT(0);

	status = OSASemaphoreAcquire(YaffsSysRef, OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);

	if(strcmp("/",dir_name)==0)
		sprintf(full_name, "/spinand");
	else
		sprintf(full_name, "/spinand/%s",dir_name);
	handle = yaffs_io_dir_open(full_name);

    if(handle < 0)
    {
        DIAG_FILTER(FDI,yaffs,F_Opendir, DIAG_INFORMATION)
        diagPrintf("Fdi open dir error: %s, status: %d", dir_name, handle);
        FATSYS_TRACE("hDirID = %d", handle);

        goto toNULL;
    }
	status = OSASemaphoreRelease(YaffsSysRef);
	ASSERT(status == OS_SUCCESS);

	DIAG_FILTER(FDI,yaffs,F_Opendir_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi open dir succeed: %s: handle: %d", dir_name, handle);

    FATSYS_TRACE("%s %s end", __FUNCTION__, dir_name);

    hDirID = (FILE_ID)handle;

    return FAT_SYS_INC(hDirID);

toNULL:
    FATSYS_TRACE("DIR open error: %s", dir_name);

	status = OSASemaphoreRelease(YaffsSysRef);
	ASSERT(status == OS_SUCCESS);

    return FILE_NULL;
}


int FDI_dirread_yaffs(FILE_ID hDirID, FILE_INFO *fileinfo_ptr)
{
	int rec = 0;
	OSA_STATUS status;
	struct yaffs_info yaffs_info;

	extern UINT32 YaffsAcatDirEnableFlag;

	FAT_SYS_DEC(hDirID);

	FATSYS_TRACE("%s %d", __FUNCTION__, hDirID);

	if(fs_mode_is_yaffs()==0)
		ASSERT(0);

	status = OSASemaphoreAcquire(YaffsSysRef, OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);

	memset(&yaffs_info, 0x00, sizeof(struct yaffs_info));
	rec = yaffs_io_dir_read(hDirID, &yaffs_info);
	if(rec != 1)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);

		return rec;
	}

	if(fs_mode_is_yaffs()==1)
		strncpy(fileinfo_ptr->file_name, yaffs_info.name, FILE_NAME_SIZE + 1);

	fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

	fileinfo_ptr->plr_time = 0;

#if 0
	fileinfo_ptr->time = FDI5to6_cTime2Time(Stat.st_time);
	fileinfo_ptr->date = FDI5to6_cTime2Date(Stat.st_time);
#else
	fileinfo_ptr->time = 0;
	fileinfo_ptr->date = 0;
#endif


	fileinfo_ptr->owner_id = 0x101; //FileGetUID();
	fileinfo_ptr->group_id = 0x100; //FileGetGID();

	if(YaffsAcatDirEnableFlag==1)
	{
		if(yaffs_info.type == 0x3)
		{
			fileinfo_ptr->permissions = 0x3FF;
			fileinfo_ptr->size = 160;
		}
		else
		{
			fileinfo_ptr->permissions = FDI5_FILE_DEFAULT_PERMISSIONS; //my_dirent->mode;
			fileinfo_ptr->size = yaffs_info.size;
		}
	}
	else
	{
		fileinfo_ptr->size = yaffs_info.size;

		if(yaffs_info.type == 0x3)
			fileinfo_ptr->permissions = 0x200;
		else
			fileinfo_ptr->permissions = FDI5_FILE_DEFAULT_PERMISSIONS; //my_dirent->mode;
	}

	// next fields not in use
	fileinfo_ptr->data_id = 0;
	fileinfo_ptr->plr_id = 0;
	//fileinfo_ptr->plr_time=0; // is in use for dir
	//fileinfo_ptr->plr_date=0; // is in use for dirID
	fileinfo_ptr->plr_size = 0;

	status = OSASemaphoreRelease(YaffsSysRef);
	ASSERT(status == OS_SUCCESS);

	return 1;
}

int FDI_closedir_yaffs(FILE_ID stream)
{
	int res;
	OSA_STATUS status;
	FILE_ID fdiID = stream;
	extern int yaffs_io_dir_close(int Handle);

	FAT_SYS_DEC(stream);
	isFSbusy = 0xf;

	FATSYS_TRACE("%s %d", __FUNCTION__, stream);

	if(fs_mode_is_yaffs()==0)
		ASSERT(0);

	status = OSASemaphoreAcquire(YaffsSysRef, OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);

	res = yaffs_io_dir_close(stream);

	if(res < 0)
	{
		isFSbusy = 0x0;

		FDI_errno = ERR_ACCESS;

		DIAG_FILTER(FDI,yaffs,F_Closedir, DIAG_INFORMATION)
		diagPrintf("Fdi close dir error: handle:%d, status: %d", stream, res);
		FATSYS_TRACE("file close error, res = %d", res);

		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);

		return EOF;
	}

	isFSbusy = 0x0;

	FDI_errno = ERR_NONE;

	DIAG_FILTER(FDI,yaffs,F_Closedir_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi close dir succeed:handle: %d,res: %d", stream, res);
	FATSYS_TRACE("dir close succeed, res = %d", res);

	status = OSASemaphoreRelease(YaffsSysRef);
	ASSERT(status == OS_SUCCESS);

	FATSYS_TRACE("%s %d end", __FUNCTION__, stream);

	return 0;
}

int  FDI_mkdir_yaffs(const char *dir_name)
{
    OSA_STATUS status;
	char full_name[128]; 
	extern int yaffs_mkdir(const char *path, long mode);
	
    int res;
    FATSYS_TRACE("%s %s", __FUNCTION__, dir_name);
	
    if(fs_mode_is_yaffs()==0)
        ASSERT(0);

	status = OSASemaphoreAcquire(YaffsSysRef, OS_SUSPEND);
	ASSERT(status == OS_SUCCESS);

	sprintf(full_name, "/spinand/%s",dir_name);
	res=yaffs_mkdir(full_name, fatTYPE_NORMAL);

	if(res<0)
	{
		DIAG_FILTER(FDI,yaffs,F_Makedir, DIAG_INFORMATION)
		diagPrintf("Fdi make dir error: %s, status: %d", dir_name, res);
		FATSYS_TRACE("DIR make error: res=%d", res);
		goto toNULL;
	}

	status = OSASemaphoreRelease(YaffsSysRef);
	ASSERT(status == OS_SUCCESS);
	
	DIAG_FILTER(FDI,yaffs,F_Makedir_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi make dir succeed: %s: status: %d", dir_name, res);
	FATSYS_TRACE("%s %s end", __FUNCTION__, dir_name);

	return 0;
	
toNULL:

	if(fs_mode_is_fat()==1)
	{
	    status = OSASemaphoreRelease(FatSysRef);
	    ASSERT(status == OS_SUCCESS);
	}

	status = OSASemaphoreRelease(YaffsSysRef);
	ASSERT(status == OS_SUCCESS);

	return EOF;
}

int FDI_rmdir_yaffs(const char *dir_name)	//mq, if remove file, return -1
{
	int res;
	OSA_STATUS status;
	char full_name[128];
	extern int yaffs_rmdir(const char *path);
	
    if(fs_mode_is_yaffs()==0)
        ASSERT(0);

	if(assertFlag != 1)
	{
		status = OSASemaphoreAcquire(YaffsSysRef, OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,yaffs,F_removeDir_start, DIAG_INFORMATION)
	diagPrintf("Fdi removeDir start, dir: %s", dir_name);

	sprintf(full_name, "/spinand/%s",dir_name);
	res=yaffs_rmdir(full_name);
	
	if(res < 0)
	{
		FDI_errno=ERR_NOTEXISTS;
		DIAG_FILTER(FDI,yaffs,F_removeDir_error, DIAG_INFORMATION)
	    diagPrintf("Fdi removeDir error, dir: %s, errCode: %d", dir_name, res);

		FATSYS_TRACE("flash file remove error");
		if(assertFlag != 1)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	DIAG_FILTER(FDI,yaffs,F_removeDir_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi removeDir succeed, dir: %s", dir_name);
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}

	FDI_errno=ERR_NONE;
	return ERR_NONE;
}//FDI_removeDir()

int yaffs_io_findall(const char *filename_ptr)
{
	yaffs_DIR *p_dir=NULL;
	struct yaffs_dirent *p_dirent;
	unsigned int len;

	int i=0,j=0;
	int fmatch=0;
	int ret=0;
	
	FATSYS_TRACE("yaffs_io_findall: %s %d\r\n",filename_ptr,rollback);

	p_dir=yaffs_opendir("/spinand");
	if (p_dir == NULL)
	{
		FATSYS_TRACE("yaffs_io_findall: fail to open Current DIR");
		return -1;
	}

	while((p_dirent=yaffs_readdir_len(p_dir, &len))!=NULL)//exit when 1. dir traverse done; 2. not done but up to max and matched
	{
		j++;
		if(factory_reset_flag==0)
		{
			if(j<=rollback*MAX_NUM_OF_FILES)
				continue;
		}

		fill_file_list_lfs(i, p_dirent->d_name, len, p_dirent->d_type);

		FATSYS_TRACE("[%d %d]:name:%s,size:%d,0x%x\r\n",j,i,FILE_LIST_LFS[i].file_name,FILE_LIST_LFS[i].size,FILE_LIST_LFS[i].permissions);
		
		if (lfs_fname_match(FILE_LIST_LFS[i].file_name, filename_ptr) /*&& FILE_LIST_LFS[index].flag_exist*/)
			fmatch=1;
		
		i++;
		if(i>=MAX_NUM_OF_FILES)
		{
			if(fmatch==0)
			{
				i=0;
				rollback++;
			}
			else
			{
				ret=1;	//dir traverse not  done
				break;
			}
		}
		
	}
	ret=yaffs_closedir(p_dir);
	FILE_NUM_LFS=i;
	FATSYS_TRACE("find total files:%d,ret:%d,fmatch:%d, rollback: %d\r\n",FILE_NUM_LFS, ret, fmatch, rollback);

	return ret;
}

int yaffs_io_findfirst(const char *wildcard ,char *Filename,fatTYPE_sStat *Stat)
{
	UINT32 index;

	FATSYS_TRACE("yaffs_io_findfirst %s, %d/%d\r\n",wildcard,rollback_ori,rollback);

	if(FILE_NUM_LFS==0)
		return (-2);

	for (index = 0; index < FILE_NUM_LFS; index ++)
	{
		if (lfs_fname_match(FILE_LIST_LFS[index].file_name, wildcard) /*&& FILE_LIST_LFS[index].flag_exist*/)
		{
			lfsSetFileInfo(index, Stat);

			/*the index of the file which was last found and the wildcard should be saved in order to use
			 * it later in the find next function */
			LastFound = index;
			rollback_ori=rollback;

			strcpy (Filename, FILE_LIST_LFS[index].file_name);
//			strcpy(WILDCARDS,wildcard);
			return 0;
		}

	}

	FATSYS_TRACE("yaffs_io_findfirst end, -1\r\n");
	return (-1);
}
int yaffs_io_findnext(char *Filename,fatTYPE_sStat *Stat)
{
	UINT32 index;
	int ret=1;
	
	FATSYS_TRACE("yaffs_io_findnext,%s,%d,%d,%d\r\n",WILDCARDS,rollback_ori,rollback,FILE_NUM_LFS);
	
	while((ret== 1)||((ret== 0)&&(FILE_NUM_LFS>0)))
	{
		if(rollback_ori!=rollback)
			index=0;
		else
			index=LastFound+1;
	//	for (index/* = (LastFound+1)*/; index < FILE_NUM_LFS; index ++)
		while(index < FILE_NUM_LFS)
		{
			if (lfs_fname_match(FILE_LIST_LFS[index].file_name, WILDCARDS) /*&& FILE_LIST_LFS[index].flag_exist*/)
			{
//				FATSYS_TRACE("lfs_io_findnext %d,[%d] %s,%d%d\r\n",FILE_NUM_LFS,index,FILE_LIST_LFS[index].file_name,rollback_ori,rollback);
				lfsSetFileInfo(index, Stat);
				strcpy (Filename, FILE_LIST_LFS[index].file_name);
				LastFound = index;
				rollback_ori = rollback;
				return 0;
			}
			index ++;
		}
		if(FILE_NUM_LFS<MAX_NUM_OF_FILES)
		{
			FATSYS_TRACE("yaffs_io_findnext none1, rollback_ori/rollback:%d/%d, %d\r\n",rollback_ori,rollback,FILE_NUM_LFS);
			return (-1);
		}
		
		rollback++;
		FatSys_Lock_Type=1;
		ret= yaffs_io_findall(WILDCARDS);
		FatSys_Lock_Type=0;
	}
	FATSYS_TRACE("yaffs_io_findnext none2, rollback_ori/rollback:%d/%d, %d\r\n",rollback_ori,rollback,FILE_NUM_LFS);

	return (-1);
}

int FDI_findfirst_yaffs(const char *filename_ptr, FILE_INFO *fileinfo_ptr)
{
	int lastIndex;
	char *slP=NULL;
//	extern int yaffs_io_findfirst(const char *wildcard ,char *Filename,fatTYPE_sStat *Stat);

	long rec;
	fatTYPE_sStat Stat;
	OSA_STATUS status;
	rollback=0;
	rollback_ori=0;

//	uart_printf("FDI_findfirst_yaffs %s\r\n", filename_ptr);
	strcpy(WILDCARDS,filename_ptr);
	yaffs_io_findall(filename_ptr);

	if(assertFlag != 1)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

	DIAG_FILTER(FDI,yaffs,F_findfirst_start, DIAG_INFORMATION)
    diagPrintf("Fdi findfirst start, filename_ptr: %s", filename_ptr);

	memset(gFullFileName, 0, sizeof(gFullFileName));
	memset(fullFileName, 0, sizeof(fullFileName));

	// Make sure filename_ptr is valid length.
	// Can't call FileNameValid since filename_ptr can contain wild cards
	if ((filename_ptr == NULL) ||(strlen(filename_ptr) > FILE_NAME_SIZE))
	{
		FDI_errno = ERR_PARAM;
		DIAG_FILTER(FDI,fatsys,F_findfirst_error_0, DIAG_INFORMATION)
    	diagPrintf("Fdi findfirst error, ERR_PARAM");
		if(assertFlag != 1)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	rec = yaffs_io_findfirst(filename_ptr, fullFileName,&Stat);
	if(rec != 0)
	{
		DIAG_FILTER(FDI,yaffs,F_findfirst_error_1, DIAG_INFORMATION)
		diagPrintf("Fdi findfirst error 1, errCode: %d", rec);
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
		return EOF;
	}
	isDir = 0;

	if(Stat.st_mode & fatTYPE_DIR)
		isDir = 0xf;

	strcpy(gFullFileName,fullFileName);

	/* we need to return the file name without the directory prefix.*/
	if ((slP = strrchr_sl(filename_ptr))!=NULL)
		filename_ptr = slP+1;


	strncpy(fileinfo_ptr->file_name,fullFileName,FILE_NAME_SIZE);
	fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

	fileinfo_ptr->plr_time=0;
	Stat.st_time=Fatsys_time(NULL);//

	fileinfo_ptr->time=FDI5to6_cTime2Time(Stat.st_time);
	fileinfo_ptr->date=FDI5to6_cTime2Date(Stat.st_time);
	fileinfo_ptr->size=Stat.st_size;

	fileinfo_ptr->owner_id=0x101;//FileGetUID();
	fileinfo_ptr->group_id=0x100;//FileGetGID();
	if(isDir)
		fileinfo_ptr->permissions=0x200;
	else
		fileinfo_ptr->permissions=FDI5_FILE_DEFAULT_PERMISSIONS;//my_dirent->mode;
	// next fields not in use
	fileinfo_ptr->data_id=0;
	fileinfo_ptr->plr_id=0;
	//fileinfo_ptr->plr_time=0; // is in use for dir
	//fileinfo_ptr->plr_date=0; // is in use for dirID
	fileinfo_ptr->plr_size=0;

	FDI_errno=ERR_NONE;

	DIAG_FILTER(FDI,yaffs,F_findfirst_succeed, DIAG_INFORMATION)
	diagPrintf("Fdi findfirst succeed, the found file: %s", gFullFileName);
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	return 0;
}//FDI_findfirst()

int FDI_findnext_yaffs(FILE_INFO *fileinfo_ptr)
{
	char fullFileName[OS_FILE_NAME_SIZE];

	long rec;
	fatTYPE_sStat Stat;
	OSA_STATUS status;
	if(assertFlag != 1)
	{
		status = OSASemaphoreAcquire(YaffsSysRef,OS_SUSPEND);
		ASSERT(status == OS_SUCCESS);
	}

//	DIAG_FILTER(FDI,fatsys,F_findnext_start, DIAG_INFORMATION)
//    diagPrintf("Fdi findnext start");

	memset(fullFileName, 0, sizeof(fullFileName));

	isDir = 0;

	rec=yaffs_io_findnext(fullFileName,&Stat);

	if(rec != 0)
	{
		FDI_errno = ERR_NOTEXISTS;
		if(rec == -7)
		{
			DIAG_FILTER(FDI,yaffs,F_findnext_end, DIAG_INFORMATION)
	    	diagPrintf("Fdi findnext there is not matching files");
		}
		else
		{
			DIAG_FILTER(FDI,yaffs,F_findnext_error, DIAG_INFORMATION)
    		diagPrintf("Fdi findnext error errCode: %d", rec);
		}
		if(assertFlag != 1)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	if(Stat.st_mode&fatTYPE_DIR)
		isDir = 0xf;

	if(strcmp(gFullFileName,fullFileName) == 0)
	{
		FDI_errno = ERR_NOTEXISTS;

		DIAG_FILTER(FDI,yaffs,F_findnext_finished, DIAG_INFORMATION)
    	diagPrintf("Fdi find process finished: the final file: %s", gFullFileName);
		if(assertFlag != 1)
		{
			status = OSASemaphoreRelease(YaffsSysRef);
			ASSERT(status == OS_SUCCESS);
		}
		return EOF;
	}

	strcpy(gFullFileName,fullFileName);

	strncpy(fileinfo_ptr->file_name,fullFileName,FILE_NAME_SIZE);
	fileinfo_ptr->file_name[FILE_NAME_SIZE] = '\0';

	fileinfo_ptr->plr_time=0;
	Stat.st_time=Fatsys_time(NULL);//

	fileinfo_ptr->time=FDI5to6_cTime2Time(Stat.st_time);
	fileinfo_ptr->date=FDI5to6_cTime2Date(Stat.st_time);
	fileinfo_ptr->size=Stat.st_size;

	fileinfo_ptr->owner_id=0x101;//FileGetUID();
	fileinfo_ptr->group_id=0x100;//FileGetGID();
	if(isDir)
		fileinfo_ptr->permissions=0x200;
	else
		fileinfo_ptr->permissions=FDI5_FILE_DEFAULT_PERMISSIONS;//my_dirent->mode;
	// next fields not in use
	fileinfo_ptr->data_id=0;
	fileinfo_ptr->plr_id=0;
	//fileinfo_ptr->plr_time=0; // is in use for dir
	//fileinfo_ptr->plr_date=0; // is in use for dirID
	fileinfo_ptr->plr_size=0;
	FDI_errno=ERR_NONE;

	DIAG_FILTER(FDI,yaffs,F_findnext_succeed, DIAG_INFORMATION)
    diagPrintf("Fdi findnext succeed: the found file: %s", gFullFileName);
	if(assertFlag != 1)
	{
		status = OSASemaphoreRelease(YaffsSysRef);
		ASSERT(status == OS_SUCCESS);
	}
	return 0;

}//FDI_findnext()
#ifdef yaffs_test
//ICAT EXPORTED FUNCTION - AAA,DDD,yaffs_wr_test
void yaffs_wr_test(void)
{
	FILE_ID fp;
	UINT8 *w_buf, *r_buf;
	UINT32 i;

	w_buf=(UINT8*)malloc(8*1024);
	r_buf=(UINT8*)malloc(8*1024);
	
	for(i=0;i<8*1024;i++)
		w_buf[i]=i;

	memset(r_buf,0,8*1024);

	fp=FDI_fopen_yaffs("001.txt","wb");

	if(fp!=FILE_NULL)
	{
		FDI_fwrite_yaffs(w_buf, 1, 8*1024, fp);
		FDI_fclose_yaffs(fp);
	}
	else
		uart_printf("FDI_fopen_yaffs wb error\r\n");

	fp=FDI_fopen_yaffs("001.txt","rb");

	if(fp!=FILE_NULL)
	{
		FDI_fread_yaffs(r_buf, 1, 8*1024, fp);
		FDI_fclose_yaffs(fp);

		uart_printf("Start Data:\r\n");
		for(i=0;i<128;i++)
			uart_printf("%02X ", r_buf[i]);
		uart_printf("\r\n");


		uart_printf("End Data:\r\n");
		for(i=0;i<128;i++)
			uart_printf("%02X ", r_buf[8*1024-128+i]);
		uart_printf("\r\n");
	}
	else
		uart_printf("FDI_fopen_yaffs rb error\r\n");

	free(w_buf);
	free(r_buf);
}

//ICAT EXPORTED FUNCTION - AAA,DDD,yaffs_format_test
void yaffs_format_test(void)
{
#if 0
	extern void yaffs_ll_test(void);
	yaffs_ll_test();
#endif

	FDI_Format();
}

//ICAT EXPORTED FUNCTION - AAA,DDD,yaffs_fdi_dir_test
void yaffs_fdi_dir_test(void)
{
	int oflag;
	int handle;

	FILE_ID fd;

	unsigned int buf_len=128;
	int ret;
	unsigned int len;
	unsigned int i;
	unsigned char *w_buf, *r_buf;

	yaffs_DIR *p_dir=NULL;
	struct yaffs_dirent *p_dirent;

	FILE_INFO fileinfo;

	w_buf=(UINT8*)malloc(buf_len);
	r_buf=(UINT8*)malloc(buf_len);

	
	for(i=0;i<buf_len;i++)
		w_buf[i]=i;

	memset(r_buf,0,buf_len);

#if 1

	FDI_removeDir("005");

	FDI_MakeDir("005");
	
	fd=FDI_fopen("005/001.txt","wb");
	if(fd!=NULL)
	{
		FDI_fwrite(w_buf, 1, buf_len,fd);
		FDI_fclose(fd);
	}
	else
		uart_printf("FDI_fopen Fail\r\n");


	fd=FDI_fopen("005/002.txt","wb");
	if(fd!=NULL)
	{
		FDI_fwrite(w_buf, 1, buf_len,fd);
		FDI_fclose(fd);
	}
	else
		uart_printf("FDI_fopen Fail\r\n");
	

	fd=FDI_fopen("005/003.txt","wb");
	if(fd!=NULL)
	{
		FDI_fwrite(w_buf, 1, buf_len,fd);
		FDI_fclose(fd);
	}
	else
		uart_printf("FDI_fopen Fail\r\n");


#endif

	fd=FDI_OpenDir("005");

	if(fd!=NULL)
	{

		do
		{
			ret=FDI_DirRead(fd,&fileinfo);
		
			if(ret==1)
			{
				uart_printf("%s, %d\r\n", fileinfo.file_name, fileinfo.size);
			}
			else
				uart_printf("FDI_DirRead %d\r\n", ret);
			
		}
		while(ret==1);

		FDI_CloseDir(fd);
	
	}

	free(w_buf);
	free(r_buf);
}

#endif

#else
void yaffs_nvm_lock_init(void)
{
}

#endif

