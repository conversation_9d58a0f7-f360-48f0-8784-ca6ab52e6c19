/*------------------------------------------------------------
(C) Copyright [2018-2020] ASR Ltd.
All Rights Reserved
------------------------------------------------------------*/

#ifndef _SDK_AUDIOHAL_H_
    #define _SDK_AUDIOHAL_H_
#include <stdint.h>

#ifdef __cplusplus
extern "C"{
#endif

//craneg A1 VAD, voice wakeup
#define USE_VAD 0

//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_ERR_NO = 0,  //No error
    AUDIOHAL_ERR_RESOURCE_RESET,
    AUDIOHAL_ERR_RESOURCE_BUSY,
    AUDIOHAL_ERR_RESOURCE_TIMEOUT,
    AUDIOHAL_ERR_RESOURCE_NOT_ENABLED,
    AUDIOHAL_ERR_BAD_PARAMETER,

    AUDIOHAL_ERR_UART_RX_OVERFLOW,
    AUDIOHAL_ERR_UART_TX_OVERFLOW,
    AUDIOHAL_ERR_UART_PARITY,
    AUDIOHAL_ERR_UART_FRAMING,
    AUDIOHAL_ERR_UART_BREAK_INT,

    AUDIOHAL_ERR_TIM_RTC_NOT_VALID,
    AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_ENABLED,
    AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_DISABLED,

    AUDIOHAL_ERR_COMMUNICATION_FAILED,

    /* Must be at the end */
    AUDIOHAL_ERR_QTY,

    AUDIOHAL_ERR_ENUM_32_BIT = 0x7FFFFFFF //32bit enum compiling enforcement
} AUDIOHAL_ERR_T;

//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_ITF_RECEIVER         = 0,
    AUDIOHAL_ITF_EARPIECE,
    AUDIOHAL_ITF_HEADPHONE = AUDIOHAL_ITF_EARPIECE,
    AUDIOHAL_ITF_LOUDSPEAKER,
    AUDIOHAL_ITF_LOUDSPEAKER_AND_HEADPHONE,
    //AUDIOHAL_ITF_LOUDSPEAKER_AND_HEADPHONE = AUDIOHAL_ITF_LOUDSPEAKER_AND_EARPIECE,
    AUDIOHAL_ITF_BLUETOOTH,
    AUDIOHAL_ITF_FM,
    AUDIOHAL_ITF_FM2SPK,
    AUDIOHAL_ITF_TV,

    AUDIOHAL_ITF_QTY,
    AUDIOHAL_ITF_NONE = 255,
} AUDIOHAL_ITF_T;

typedef enum
{
    AUDIOHAL_SPK_RECEIVER         = 0,
    AUDIOHAL_SPK_EARPIECE,
    AUDIOHAL_SPK_LOUDSPEAKER,
    AUDIOHAL_SPK_LOUDSPEAKER_EARPIECE,  //Output on both hands-free loud speaker and earpiece

    AUDIOHAL_SPK_QTY,
    AUDIOHAL_SPK_DISABLE = 255,
} AUDIOHAL_SPK_T;

//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_SPEAKER_STEREO         = 0,
    AUDIOHAL_SPEAKER_MONO_RIGHT,
    AUDIOHAL_SPEAKER_MONO_LEFT,
    AUDIOHAL_SPEAKER_STEREO_NA,  //Output is mono only

    AUDIOHAL_SPEAKER_QTY,
    AUDIOHAL_SPEAKER_DISABLE = 255,
} AUDIOHAL_SPEAKER_TYPE_T;

//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_MIC_RECEIVER         = 0,
    AUDIOHAL_MIC_EARPIECE,
    AUDIOHAL_MIC_LOUDSPEAKER,

    AUDIOHAL_MIC_QTY,
    AUDIOHAL_MIC_DISABLE = 255,
} AUDIOHAL_MIC_T;

typedef struct {
    uint8_t spkLevel;
    uint8_t  micLevel;
    uint8_t sideLevel;
    uint8_t toneLevel;
} AUDIOHAL_AIF_LEVEL_T;

typedef struct {
    AUDIOHAL_SPK_T          spkSel;
    AUDIOHAL_SPEAKER_TYPE_T spkType;    //Kind of speaker(stereo/mono/etc)
    AUDIOHAL_MIC_T          micSel;
    AUDIOHAL_AIF_LEVEL_T  * level;
} AUDIOHAL_AIF_DEVICE_CFG_T;

AUDIOHAL_ERR_T AudioHAL_AifOpen(AUDIOHAL_ITF_T itf, AUDIOHAL_AIF_DEVICE_CFG_T *config);

//craneg A1 support voice wakeup
#if USE_VAD
int AudioHAL_AifSet_VAD(int on);
int AudioHAL_AifGet_VAD(void);
typedef void (*AUDIOHAL_KWS_CB_T) (unsigned int key);
int AudioHAL_AifBindKWS_CB(AUDIOHAL_KWS_CB_T);
#endif

#ifdef __cplusplus
}
#endif

#endif // _SDK_AUDIOHAL_H_
