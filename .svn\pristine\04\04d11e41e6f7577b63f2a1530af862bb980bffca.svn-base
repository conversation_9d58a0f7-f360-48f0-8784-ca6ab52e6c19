/**
 * @file alarm.h
 *
 */
#ifndef ALARM_H
#define ALARM_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lvgl.h"
#include "lv_watch_conf.h"
#else
#include "../../../lvgl/lvgl.h"
#include "../../../lv_watch_conf.h"
#endif

#if USE_LV_WATCH_ALARM

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/
typedef struct {
    lv_watch_obj_ext_t lv_watch_obj;
    /*New data for this type */
    gif_info_t * gif_info;
    TIMER_ID timer_id;
    bool timer_running;
} lv_alarm_obj_ext_t;

#if USE_LV_WATCH_REPEAT_PLAY_ALARM_TEN_MIN != 0
typedef struct {
	uint8 	Alarm_Repeat_Cycle;
	uint32_t Alarm_Repeat_Seconds;
} alarm_repeat_t;
#endif

/**********************
 * GLOBAL PROTOTYPES
 **********************/
lv_obj_t * alarm_create(lv_obj_t * activity_obj);
void alarm_init(void);
void alarm_cleanup(void);
void alarm_shutdown(hal_rtc_t * poweron_time);
void alarm_set_alarm_by_ui(app_adaptor_alarm_t * alarm, uint8_t count);
app_adaptor_alarm_t * alarm_get_alarm(void);
void alarm_set_rtc_alarm(void);
void poweroff_alarm_ring(int alarm_type);
int set_alarm_on_status(uint8_t onoff);
int get_alarm_on_status(void);
static void power_off_alarm_off(void);
bool is_power_off_alarm(void);
void stop_alarm_rering_timer(void);
bool is_power_off_alarm_wating(void);
void set_power_off_alarm_wating(bool onoff);

/**********************
 *      MACROS
 **********************/

#endif /*USE_LV_WATCH_ALARM*/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*ALARM_H*/
