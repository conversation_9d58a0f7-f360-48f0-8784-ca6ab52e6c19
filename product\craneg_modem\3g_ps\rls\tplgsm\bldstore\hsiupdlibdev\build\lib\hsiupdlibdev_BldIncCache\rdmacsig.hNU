/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 *
 *                       TTPCom [product name]
 *
 *           Copyright (c) 2000 TTP Communications Ltd.
 *
 ***************************************************************************
 *
 *   $Id: //central/releases/Branch_release_9/tplgsm/gpinc/rdmacsig.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2004/01/14 14:10:56 $
 *
 ***************************************************************************
 *
 * File Description
 * ----------------
 *
 *               RD >>> MAC signal definitions
 * These definitions are for the new multislot GPRS system.
 *
 ***************************************************************************
 *
 *
 ***************************************************************************
 *
 ***************************************************************************/

#if !defined (RDMACSIG_H)
#define       RDMACSIG_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <gpgentyp.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/
#define NUM_SPARE_BYTES 1

/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef struct DataRxBlockTag
{
    CodingScheme        csUsed;  /* coding scheme used */
#if defined (UPGRADE_EDGE)
    Int8                data[SIZE_OF_MCS_9];
    Int8                controlBits;     /* Octet containing split block and SDS pointer indicator bits */
    Int16               bsn;             /*Explicitly include the BSN as the header is not stored*/
#else
    Int8                data[SIZE_OF_CS_4];
#endif
}
DataRxBlock;

typedef struct DataTxBlockTag
{
    CodingScheme        csUsed;  /* coding scheme used */
    Int8                numLlcPduInBlock;
#if defined (UPGRADE_EDGE)
    CodingScheme        lastCsUsed;  /* the last coding scheme used */
    Int8                data[SIZE_OF_MCS_9];
    PuncturingScheme    ps;
    Int8                spb;
#else
    Int8                data[SIZE_OF_CS_4 + NUM_SPARE_BYTES];
#endif
}
DataTxBlock;

typedef DataTxBlock * DataTxBlockPtr;
typedef DataRxBlock * DataRxBlockPtr;
typedef DataRxBlock * DataBlockPtr;

typedef struct RdMacUlDataTag
{
    Int8            numBlocksRequired;

#if defined (UPGRADE_EDGE)
    DataTxBlockPtr  ulBlock_p[MAX_SUPPORTED_TIMESLOTS * 2];
#else
    DataTxBlockPtr  ulBlock_p[MAX_SUPPORTED_TIMESLOTS];
#endif
    Boolean         encodeTlli;
    CodingScheme    csToUse;

    /* Parameters used in fixed allocation to calculate the
     * correct countdown for the remaining allocation */
    Boolean         fixedFinalAllocation;
    Int8            fixedBlocksRemaining;

    /* If resegment is true, then Resegment previously
     * segmented data blocks */
    Boolean         resegment;

    /* This field is the BSN that the segmentation should
     * restart from */
#if defined (UPGRADE_EDGE)
    Int16           resegBsnStart;
#else
     Int8           resegBsnStart;
#endif

    /* This is the number of uplink timeslots assigned in the
     * current uplink TBF */
    Int8            numTimeslotsAssigned;
}
RdMacUlData;

/***************************************************************************
 * Signal Definitions
 **************************************************************************/
typedef struct RdMacLlcPduAckIndTag
{
    Boolean         allLlcPduAcked;
    Int8            numLlcPduAcked;
}
RdMacLlcPduAckInd;

typedef struct RdMacDataReqTag
{
    Int8            numBlocksUsed;
    Boolean         uplinkTbfActive;    /* True whiles RD has not sent CV = 0    */
#if defined (UPGRADE_EDGE)
    Int16           lastBlockSeqNumber;
#else
    Int8            lastBlockSeqNumber;
#endif
#if defined (DEVELOPMENT_VERSION)
    Int16           outstandingBlocks;
    Int16           currentPduLength;
    Int16           currentPduOctetSent;
    CodingScheme    csUsed;
#endif
}
RdMacDataReq;

typedef struct RdMacDataIndTag
{

	Int8            numBlocksReceived;
    Boolean         rlcUnAckMode;
#if defined (UPGRADE_EDGE)
    Int16              bsn[MAX_SUPPORTED_TIMESLOTS * 2];            /*Explicitly include the BSN as the header is not stored*/            
    CodingScheme  csUsed[MAX_SUPPORTED_TIMESLOTS * 2];       /* coding scheme used */
    Int8                controlBits[MAX_SUPPORTED_TIMESLOTS * 2];  /* Octet containing split block and SDS pointer indicator bits */
    DataRxBlockPtr  dlBlock_p[MAX_SUPPORTED_TIMESLOTS * 2];
#else
    Int16              bsn[MAX_SUPPORTED_TIMESLOTS];            /*Explicitly include the BSN as the header is not stored*/            
    CodingScheme  csUsed[MAX_SUPPORTED_TIMESLOTS];       /* coding scheme used */
    Int8                controlBits[MAX_SUPPORTED_TIMESLOTS];  /* Octet containing split block and SDS pointer indicator bits */
    DataRxBlockPtr  dlBlock_p[MAX_SUPPORTED_TIMESLOTS];
#endif

    RdMacUlData     rdMacUlData;
#if defined (DEVELOPMENT_VERSION)
#if defined (UPGRADE_EDGE)
    Int16           rxLastBsn;           /* The BSN of the last rx block        */
    Int16           vr;                  /* Receive State Variable V(R)         */
    Int16           vq;                  /* Receive Window State Variable V(Q)  */
    Int16           nextSendToRd;        /* The next block to be sent to Rd     */
    Int16           vs;                  /* Send State Variable V(S)            */
    Int16           va;                  /* Acknowledge State Varibel V(A)      */
    Int16           lastRdBlockAcquired; /* last data block written by RD       */
#else
    Int8            rxLastBsn;           /* The BSN of the last rx block        */
    Int8            vr;                  /* Receive State Variable V(R)         */
    Int8            vq;                  /* Receive Window State Variable V(Q)  */
    Int8            nextSendToRd;        /* The next block to be sent to Rd     */
    Int8            vs;                  /* Send State Variable V(S)            */
    Int8            va;                  /* Acknowledge State Varibel V(A)      */
    Int8            lastRdBlockAcquired; /* last data block written by RD       */
#endif
#endif
}
RdMacDataInd;

typedef struct RdMacReadyToSendIndTag
{
    RdMacUlData     rdMacUlData;

#if defined (DEVELOPMENT_VERSION)
#if defined (UPGRADE_EDGE)
    Int16           vr;                 /* Receive State Variable V(R)        */
    Int16           vq;                 /* Receive Window State Variable V(Q) */
    Int16           nextSendToRd;       /* The next block to be sent to Rd    */
    Int16           vs;                 /* Send State Variable V(S)           */
    Int16           va;                 /* Acknowledge State Varibel V(A)     */
    Int16           lastRdBlockAcquired;/* last data block written by RD      */
#else
    Int8            vr;                 /* Receive State Variable V(R)        */
    Int8            vq;                 /* Receive Window State Variable V(Q) */
    Int8            nextSendToRd;       /* The next block to be sent to Rd    */
    Int8            vs;                 /* Send State Variable V(S)           */
    Int8            va;                 /* Acknowledge State Varibel V(A)     */
    Int8            lastRdBlockAcquired;/* last data block written by RD      */
#endif
#endif
}
RdMacReadyToSendInd;


/***************************************************************************
 *  Macros
 **************************************************************************/


/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

#endif

/* END OF FILE */
