/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "RRLP-Components"
 * 	found in "rrlp12_1_0.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_GANSSEphemerisExtensionCheck_H_
#define	_GANSSEphemerisExtensionCheck_H_


#include <asn_application.h>

/* Including external dependencies */
#include "GANSSEphemerisExtensionTime.h"
#include "GANSSSatEventsInfo.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* GANSSEphemerisExtensionCheck */
typedef struct GANSSEphemerisExtensionCheck {
	GANSSEphemerisExtensionTime_t	 ganssBeginTime;
	GANSSEphemerisExtensionTime_t	 ganssEndTime;
	GANSSSatEventsInfo_t	 ganssSatEventsInfo;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GANSSEphemerisExtensionCheck_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GANSSEphemerisExtensionCheck;

#ifdef __cplusplus
}
#endif

#endif	/* _GANSSEphemerisExtensionCheck_H_ */
#include <asn_internal.h>
