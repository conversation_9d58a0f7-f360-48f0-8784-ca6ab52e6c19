#!armcc -E -DREGION_END=0x7E420000

#include "./inc/updater_scatter_ota_spi.h"
#include "../common/inc/updater_table_scatter.h"

LOAD UPDATER_OTA_SPI_PSRAM_BASE_ADDR
{
    INIT INIT_BASE_ADDR INIT_SIZE
    {
		version_updater.o (IMG_HEADER_INFO,+First)
        StartUp.o (Init)         			; Startup code
        main.o (+RO)                        ; Place main() in a root region for the benefit of software breakpoints
    }

    ITCM 0 0x10000							; Second Exec region is ITCM; size:64K
    {
		LzmaDec.o (+RO)						; LzmaDec code on ITCM
    }
    
    CODE  CODE_BASE_ADDR	CODE_SIZE
    {
        * (+RO)                             ; Application code, including C library
    }

    DATA  DATA_BASE_ADDR	DATA_SIZE
    {
        * (+RW,+ZI)                         ; All RW and ZI Data
    }
	NONCACHE_DATA  NONCACHE_DATA_BASE_ADDR	NONCACHE_DATA_SIZE
    {
		main.o (TEST_NONCACHE_DATA)
    }
	
	ASR_SPI_OTA_BUFFER ASR_SPI_OTA_BUFFER_BASE_ADDR ASR_SPI_OTA_BUFFER_SIZE
	{
	
	}	

	IMG_END +0 EMPTY 0x04 {}
}


