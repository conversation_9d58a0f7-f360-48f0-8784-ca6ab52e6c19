/* zconf.h -- configuration of the zlib compression library
 * Copyright (C) 1995-2004 <PERSON><PERSON><PERSON><PERSON>.
 * For conditions of distribution and use, see copyright notice in zlib.h
 */

/* @(#) $Id: //central/main/tplgsm/zlib/zconf.in.h#7 $ */

#if !defined (ZCONF_H)
#define ZCONF_H

/*
 * If you *really* need a unique prefix for all types and library functions,
 * compile with -DZ_PREFIX. The "standard" zlib should be compiled without it.
 */
#if defined (Z_PREFIX)
/* API */
#  define deflateInit_                  z_deflateInit_
#  define deflate                       z_deflate
#  define deflateEnd                    z_deflateEnd
#  define inflateInit_                  z_inflateInit_
#  define inflate                       z_inflate
#  define inflateEnd                    z_inflateEnd
#  define deflateInit2_                 z_deflateInit2_
#  define deflateSetDictionary          z_deflateSetDictionary
#  define deflateCopy                   z_deflateCopy
#  define deflateReset                  z_deflateReset
#  define deflateParams                 z_deflateParams
#  define deflateBound                  z_deflateBound
#  define deflatePrime                  z_deflatePrime
#  define inflateInit2_                 z_inflateInit2_
#  define inflateSetDictionary          z_inflateSetDictionary
#  define inflateSync                   z_inflateSync
#  define inflateSyncPoint              z_inflateSyncPoint
#  define inflateCopy                   z_inflateCopy
#  define inflateReset                  z_inflateReset
#  define inflateBack                   z_inflateBack
#  define inflateBackEnd                z_inflateBackEnd
#  define compress                      z_compress
#  define compress2                     z_compress2
#  define compressBound                 z_compressBound
#  define uncompress                    z_uncompress
#  define adler32                       z_adler32
#  define crc32                         z_crc32
#  define get_crc_table                 z_get_crc_table
#  define zError                        z_zError

/* Types */
#  define Byte                          z_Byte
#  define uInt                          z_uInt
#  define uLong                         z_uLong
#  define Bytef                         z_Bytef
#  define charf                         z_charf
#  define intf                          z_intf
#  define uIntf                         z_uIntf
#  define uLongf                        z_uLongf
#  define voidpf                        z_voidpf
#  define voidp                         z_voidp


/* Other exported symbols */
#  define _dist_code                    z__dist_code
#  define _length_code                  z__length_code
#  define _tr_align                     z__tr_align
#  define _tr_flush_block               z__tr_flush_block
#  define _tr_init                      z__tr_init
#  define _tr_stored_block              z__tr_stored_block
#  define _tr_tally                     z__tr_tally
#  define deflate_copyright             z_deflate_copyright
#  define fclose_file_func              z_fclose_file_func
#  define ferror_file_func              z_ferror_file_func
#  define fill_fopen_filefunc           z_fill_fopen_filefunc
#  define fopen_file_func               z_fopen_file_func
#  define fread_file_func               z_fread_file_func
#  define fseek_file_func               z_fseek_file_func
#  define ftell_file_func               z_ftell_file_func
#  define fwrite_file_func              z_fwrite_file_func
#  define gen_trees_header              z_gen_trees_header
#  define get_crc_table                 z_get_crc_table
#  define gzclearerr                    z_gzclearerr
#  define gzclose                       z_gzclose
#  define gzdopen                       z_gzdopen
#  define gzeof                         z_gzeof
#  define gzerror                       z_gzerror
#  define gzflush                       z_gzflush
#  define gzgetc                        z_gzgetc
#  define gzgets                        z_gzgets
#  define gzopen                        z_gzopen
#  define gzprintf                      z_gzprintf
#  define gzprintf                      z_gzprintf
#  define gzputc                        z_gzputc
#  define gzputs                        z_gzputs
#  define gzread                        z_gzread
#  define gzrewind                      z_gzrewind
#  define gzseek                        z_gzseek
#  define gzsetparams                   z_gzsetparams
#  define gztell                        z_gztell
#  define gzungetc                      z_gzungetc
#  define gzwrite                       z_gzwrite
#  define inflateBackInit_              z_inflateBackInit_
#  define inflate_fast                  z_inflate_fast
#  define inflate_table                 z_inflate_table
#  define makefixed                     z_makefixed
#  define uncompress                    z_uncompress
#  define unzClose                      z_unzClose
#  define unzCloseCurrentFile           z_unzCloseCurrentFile
#  define unzGetCurrentFileInfo         z_unzGetCurrentFileInfo
#  define unzGetFilePos                 z_unzGetFilePos
#  define unzGetGlobalComment           z_unzGetGlobalComment
#  define unzGetGlobalInfo              z_unzGetGlobalInfo
#  define unzGetLocalExtrafield         z_unzGetLocalExtrafield
#  define unzGetOffset                  z_unzGetOffset
#  define unzGoToFilePos                z_unzGoToFilePos
#  define unzGoToFirstFile              z_unzGoToFirstFile
#  define unzGoToNextFile               z_unzGoToNextFile
#  define unzLocateFile                 z_unzLocateFile
#  define unzOpen                       z_unzOpen
#  define unzOpen2                      z_unzOpen2
#  define unzOpenCurrentFile            z_unzOpenCurrentFile
#  define unzOpenCurrentFile2           z_unzOpenCurrentFile2
#  define unzOpenCurrentFile3           z_unzOpenCurrentFile3
#  define unzOpenCurrentFilePassword    z_unzOpenCurrentFilePassword
#  define unzReadCurrentFile            z_unzReadCurrentFile
#  define unzSetOffset                  z_unzSetOffset
#  define unzStringFileNameCompare      z_unzStringFileNameCompare
#  define unz_copyright                 z_unz_copyright
#  define unzeof                        z_unzeof
#  define unztell                       z_unztell
#  define z_errmsg                      z_z_errmsg
#  define z_error                       z_z_error
#  define z_verbose                     z_z_verbose
#  define zcalloc                       z_zcalloc
#  define zcalloc                       z_zcalloc
#  define zcfree                        z_zcfree
#  define zcfree                        z_zcfree
#  define zlibCompileFlags              z_zlibCompileFlags
#  define zlibVersion                   z_zlibVersion
#  define zlib_inflate_copyright        z_zlib_inflate_copyright
#  define zipOpen                       z_zipOpen
#  define zipOpen2                      z_zipOpen2
#  define zipOpenNewFileInZip           z_zipOpenNewFileInZip
#  define zipOpenNewFileInZip2          z_zipOpenNewFileInZip2
#  define zipOpenNewFileInZip3          z_zipOpenNewFileInZip3
#  define zipWriteInFileInZip           z_zipWriteInFileInZip
#  define zipCloseFileInZip             z_zipCloseFileInZip
#  define zipCloseFileInZipRaw          z_zipCloseFileInZipRaw
#  define zipClose                      z_zipClose
#endif

#if defined(__MSDOS__) && !defined(MSDOS)
#  define MSDOS
#endif
#if (defined(OS_2) || defined(__OS2__)) && !defined(OS2)
#  define OS2
#endif
#if defined(_WINDOWS) && !defined(WINDOWS)
#  define WINDOWS
#endif
#if (defined(_WIN32) || defined(__WIN32__)) && !defined(WIN32)
#  define WIN32
#endif
#if (defined(MSDOS) || defined(OS2) || defined(WINDOWS)) && !defined(WIN32)
#  if !defined(__GNUC__) && !defined(__FLAT__) && !defined(__386__)
#    if !defined (SYS16BIT)
#      define SYS16BIT
#    endif
#  endif
#endif

/*
 * Compile with -DMAXSEG_64K if the alloc function cannot allocate more
 * than 64k bytes at a time (needed on systems with 16-bit int).
 */
#if defined (SYS16BIT)
#  define MAXSEG_64K
#endif
#if defined (MSDOS)
#  define UNALIGNED_OK
#endif

#if defined (__STDC_VERSION__)
#  if !defined (STDC)
#    define STDC
#  endif
#  if __STDC_VERSION__ >= 199901L
#    if !defined (STDC99)
#      define STDC99
#    endif
#  endif
#endif
#if !defined(STDC) && (defined(__STDC__) || defined(__cplusplus))
#  define STDC
#endif
#if !defined(STDC) && (defined(__GNUC__) || defined(__BORLANDC__))
#  define STDC
#endif
#if !defined(STDC) && (defined(MSDOS) || defined(WINDOWS) || defined(WIN32))
#  define STDC
#endif
#if !defined(STDC) && (defined(OS2) || defined(__HOS_AIX__))
#  define STDC
#endif

#if defined(__OS400__) && !defined(STDC)    /* iSeries (formerly AS/400). */
#  define STDC
#endif

#if !defined (STDC)
#  if !defined (const ) /* cannot use !defined(STDC) && !defined(const) on Mac */
#    define const       /* note: need a more gentle solution here */
#  endif
#endif

/* Some Mac compilers merge all .h files incorrectly: */
#if defined(__MWERKS__)||defined(applec)||defined(THINK_C)||defined(__SC__)
#  define NO_DUMMY_DECL
#endif

/* Maximum value for memLevel in deflateInit2 */
#if !defined (MAX_MEM_LEVEL)
#  if defined (MAXSEG_64K)
#    define MAX_MEM_LEVEL 7
#  else
#    define MAX_MEM_LEVEL 9
#  endif
#endif

/* Maximum value for windowBits in deflateInit2 and inflateInit2.
 * WARNING: reducing MAX_WBITS makes minigzip unable to extract .gz files
 * created by gzip. (Files created by minigzip can still be extracted by
 * gzip.)
 *
 * DWFV87: Require different inflate and deflate sizes. Deflate requires
 * lots of RAM, so a smaller deflate size prevents out-of-memory errors
 * on compression.  But PNG support requires a 15-bit window for decompression,
 * otherwise PNG files simply can't be opened.  A larger decompression window
 * can open zipped data which was compressed with a smaller compression window,
 * but not the other way round.
 */
#if !defined (MAX_WBITS)
#  define MAX_WBITS   14 /* 16K LZ77 window for deflate */
#endif

#if !defined (MAX_WBITS_INFLATE)
#  define MAX_WBITS_INFLATE   15 /* 32K LZ77 window for inflate */
#endif

/* The memory requirements for deflate are (in bytes):
            (1 << (windowBits+2)) +  (1 << (memLevel+9))
 that is: 128K for windowBits=15  +  128K for memLevel = 8  (default values)
 plus a few kilobytes for small objects. For example, if you want to reduce
 the default memory requirements from 256K to 128K, compile with
     make CFLAGS="-O -DMAX_WBITS=14 -DMAX_MEM_LEVEL=7"
 Of course this will generally degrade compression (there's no free lunch).

   The memory requirements for inflate are (in bytes) 1 << windowBits
 that is, 32K for windowBits=15 (default value) plus a few kilobytes
 for small objects.
*/

                        /* Type declarations */

#if !defined (OF ) /* function prototypes */
#  if defined (STDC)
#    define OF(args)  args
#  else
#    define OF(args)  ()
#  endif
#endif

/* The following definitions for FAR are needed only for MSDOS mixed
 * model programming (small or medium model with some far allocations).
 * This was tested only with MSC; for other MSDOS compilers you may have
 * to define NO_MEMCPY in zutil.h.  If you don't need the mixed model,
 * just define FAR to be empty.
 */
#if defined (SYS16BIT)
#  if defined(M_I86SM) || defined(M_I86MM)
     /* MSC small or medium model */
#    define SMALL_MEDIUM
#    if defined (_MSC_VER)
#      define FAR _far
#    else
#      define FAR far
#    endif
#  endif
#  if (defined(__SMALL__) || defined(__MEDIUM__))
     /* Turbo C small or medium model */
#    define SMALL_MEDIUM
#    if defined (__BORLANDC__)
#      define FAR _far
#    else
#      define FAR far
#    endif
#  endif
#endif

#if defined(WINDOWS) || defined(WIN32)
   /* If building or using zlib as a DLL, define ZLIB_DLL.
    * This is not mandatory, but it offers a little performance increase.
    */
#  if defined (ZLIB_DLL)
#    if defined(WIN32) && (!defined(__BORLANDC__) || (__BORLANDC__ >= 0x500))
#      if defined (ZLIB_INTERNAL)
#        define ZEXTERN extern __declspec(dllexport)
#      else
#        define ZEXTERN extern __declspec(dllimport)
#      endif
#    endif
#  endif  /* ZLIB_DLL */
   /* If building or using zlib with the WINAPI/WINAPIV calling convention,
    * define ZLIB_WINAPI.
    * Caution: the standard ZLIB1.DLL is NOT compiled using ZLIB_WINAPI.
    */
#  if defined (ZLIB_WINAPI)
#    if defined (FAR)
#      undef FAR
#    endif
#    include <windows.h>
     /* No need for _export, use ZLIB.DEF instead. */
     /* For complete Windows compatibility, use WINAPI, not __stdcall. */
#    define ZEXPORT WINAPI
#    if defined (WIN32)
#      define ZEXPORTVA WINAPIV
#    else
#      define ZEXPORTVA FAR CDECL
#    endif
#  endif
#endif

#if defined (__BEOS__)
#  if defined (ZLIB_DLL)
#    if defined (ZLIB_INTERNAL)
#      define ZEXPORT   __declspec(dllexport)
#      define ZEXPORTVA __declspec(dllexport)
#    else
#      define ZEXPORT   __declspec(dllimport)
#      define ZEXPORTVA __declspec(dllimport)
#    endif
#  endif
#endif

#if !defined (ZEXTERN)
#  define ZEXTERN extern
#endif
#if !defined (ZEXPORT)
#  define ZEXPORT
#endif
#if !defined (ZEXPORTVA)
#  define ZEXPORTVA
#endif

#if !defined (FAR)
#  define FAR
#endif

#if !defined(__MACTYPES__)
typedef unsigned char  Byte;  /* 8 bits */
#endif
typedef unsigned int   uInt;  /* 16 bits or more */
typedef unsigned long  uLong; /* 32 bits or more */

#if defined (SMALL_MEDIUM)
   /* Borland C/C++ and some old MSC versions ignore FAR inside typedef */
#  define Bytef Byte FAR
#else
   typedef Byte  FAR Bytef;
#endif
typedef char  FAR charf;
typedef int   FAR intf;
typedef uInt  FAR uIntf;
typedef uLong FAR uLongf;

#if defined (STDC)
   typedef void const *voidpc;
   typedef void FAR   *voidpf;
   typedef void       *voidp;
#else
   typedef Byte const *voidpc;
   typedef Byte FAR   *voidpf;
   typedef Byte       *voidp;
#endif

#if 0           /* HAVE_UNISTD_H -- this line is updated by ./configure */
#  include <sys/types.h> /* for off_t */
#  include <unistd.h>    /* for SEEK_* and off_t */
#  if defined (VMS)
#    include <unixio.h>   /* for off_t */
#  endif
#  define z_off_t off_t
#endif
#if !defined (SEEK_SET)
#  define SEEK_SET        0       /* Seek from beginning of file.  */
#  define SEEK_CUR        1       /* Seek from current position.  */
#  define SEEK_END        2       /* Set file pointer to EOF plus "offset" */
#endif
#if !defined (z_off_t)
#  define z_off_t long
#endif

#if defined(__OS400__)
#  define NO_vsnprintf
#endif

#if defined(__MVS__)
#  define NO_vsnprintf
#  if defined (FAR)
#    undef FAR
#  endif
#endif

/* MVS linker does not support external names larger than 8 bytes */
#if defined(__MVS__)
#   pragma map(deflateInit_,"DEIN")
#   pragma map(deflateInit2_,"DEIN2")
#   pragma map(deflateEnd,"DEEND")
#   pragma map(deflateBound,"DEBND")
#   pragma map(inflateInit_,"ININ")
#   pragma map(inflateInit2_,"ININ2")
#   pragma map(inflateEnd,"INEND")
#   pragma map(inflateSync,"INSY")
#   pragma map(inflateSetDictionary,"INSEDI")
#   pragma map(compressBound,"CMBND")
#   pragma map(inflate_table,"INTABL")
#   pragma map(inflate_fast,"INFA")
#   pragma map(zlib_inflate_copyright,"INCOPY")
#endif

#endif /* ZCONF_H */
