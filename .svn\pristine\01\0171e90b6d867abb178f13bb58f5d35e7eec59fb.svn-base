#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#=========================================================================
# File Name      : BSP.mak
# Description    : Main make file for the aplp/BSP package.
#
# Usage          : make [-s] -f BSP.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
# Copyright (c) 2002 Intel Corporation. All Rights Reserved
#=========================================================================

# Package build options

include $(OPT_FILE)

# Package Makefile information

GEN_PACK_MAKEFILE = $(BUILD_ROOT)/env/$(HOST)/build/package.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = BSP
PACKAGE_BASE     = hop
PACKAGE_PATH     = $(BUILD_ROOT)/$(PACKAGE_BASE)/$(PACKAGE_NAME)

# The path locations of source and include file directories.
PACKAGE_SRC_PATH    = $(PACKAGE_PATH)/src

PACKAGE_INC_PATHS   = $(PACKAGE_PATH)/src $(PACKAGE_PATH)/inc $(PACKAGE_PATH)/src/include \
		  $(PACKAGE_PATH)/templates \
		  $(BUILD_ROOT)/hop/core/inc \
		  $(BUILD_ROOT)/hop/core/src \
		  $(BUILD_ROOT)/hop/MMU/inc \
		  $(BUILD_ROOT)/hop/MEMC/inc \
		  $(BUILD_ROOT)/hop/timer/inc \
		  $(BUILD_ROOT)/hop/wdt/inc \
		  $(BUILD_ROOT)/hop/dma/inc   \
		  $(BUILD_ROOT)/hop/ccu/inc   \
		  $(BUILD_ROOT)/hop/rm/inc   \
		  $(BUILD_ROOT)/hop/pm/inc   \
		  $(BUILD_ROOT)/hop/ssp/inc   \
		  $(BUILD_ROOT)/hop/KEYPAD/inc \
		  $(BUILD_ROOT)/hop/RTC/inc    \
		  $(BUILD_ROOT)/hop/USB/inc    \
		  $(BUILD_ROOT)/hal/I2C/inc    \
		  $(BUILD_ROOT)/hop/OneWire/inc \
		  $(BUILD_ROOT)/hal/UART/inc    \
		  $(BUILD_ROOT)/hop/LCDIF/inc    \
		  $(BUILD_ROOT)/hop/CLCD/inc    \
		  $(BUILD_ROOT)/hop/CAMIF/inc    \
		  $(BUILD_ROOT)/hop/csw_memory/inc\
		  $(BUILD_ROOT)/os/nu_xscale/inc    \
		  $(BUILD_ROOT)/hop/apbccu/inc    \
		  $(BUILD_ROOT)/os/osa/inc    \
		  $(BUILD_ROOT)/os/osx/inc    \
		  $(BUILD_ROOT)/hop/intc/inc \
		  $(BUILD_ROOT)/hop/commpm/inc   \
		  $(BUILD_ROOT)/softutil/datacollector/inc \
		  $(BUILD_ROOT)/hal/core/src \
		  ${BUILD_ROOT}/softutil/datacollector/src \
		  $(BUILD_ROOT)/hop/cpmu/inc \
		  ${BUILD_ROOT}/pcac/duster/inc \
		  ${BUILD_ROOT}/pcac/lwipv4v6/src/include/arch \
		  ${BUILD_ROOT}/pcac/lwipv4v6/src/include/ipv4 \
		  ${BUILD_ROOT}/pcac/lwipv4v6/src/include/ipv6 \
		  ${BUILD_ROOT}/pcac/lwipv4v6/src/include/lwip \
		  $(BUILD_ROOT)/CRD/DTC/inc \
		  $(BUILD_ROOT)/softutil/fatsys/flash/\
		  $(BUILD_ROOT)/diag/diag_comm/inc

ifneq (,$(findstring BT_STACK_SUPPORT,${VARIANT_LIST}))
PACKAGE_INC_PATHS     += $(BUILD_ROOT)/hal/BT_device/btstack/inc
else
PACKAGE_INC_PATHS     += $(BUILD_ROOT)/hal/BT_device/bthoststack/inc
endif

# Platform-independent files
BSP_PATH=${BUILD_ROOT}/csw/BSP/src
# Define Package Variants -------------------------------
# handle the DVT_TEST_ENABLE variant ------------
ifneq ($(DVT_TEST_ENABLE),)
PACKAGE_SRC_PATH   += $(PACKAGE_PATH)/test
PACKAGE_SRC_FILES  += pmchip_test_dvt.c
endif

ifneq  (,$(findstring PHS_SW_DEMO,${VARIANT_LIST}))
#gwl add for ripc
    PACKAGE_SRC_FILES += \
    		  ripc.c
#
# remove INIT_DATA region, seems nolonger used
#
#    		  init_reserved_data.c
endif

PACKAGE_SRC_FILES += \
              cinit1.c       \
              asr_property.c \
              main.c         \
              platform.c     \
              platform_pmu.c \
              platform_shim_api.c \
              init.s

ifeq (,$(findstring NOAUDIO,${VARIANT_LIST}))
PACKAGE_SRC_FILES +=   AudioExtern.c
endif
	      
ifeq  (,$(findstring NOIMS,${VARIANT_LIST}))
PACKAGE_SRC_FILES += ims_api_psram.c
endif

ifneq  (,$(findstring PHS_SW_DEMO,${VARIANT_LIST}))    
#    PACKAGE_SRC_FILES += pin_mux.s
endif

NONATIVE_SRC_FILES += \
	      $(BSP_PATH)/bsp_hisr.c   \
	      $(BSP_PATH)/cinit2.c     \
	      $(BSP_PATH)/loadTable.c  \
	      $(BSP_PATH)/updater_table.c  \
	      $(BSP_PATH)/logo_table.c  \
	      $(BSP_PATH)/ptable.c  \
	      $(BSP_PATH)/firmware.c  \
	      $(BSP_PATH)/exceptionHandlers.s  \
	      $(BSP_PATH)/vectors.s  \
              $(BSP_PATH)/nucleus_dep.s \
	      $(BSP_PATH)/bspUartManager.c \
	      $(BSP_PATH)/idletask.c  \
	      $(BSP_PATH)/idleasm.s  \
	      $(BSP_PATH)/lowtasks.c    \
	      $(BSP_PATH)/dsp_boot.c    \
	      $(BSP_PATH)/initTaskUtil.c \
	      $(BSP_PATH)/version.c \
	      $(BSP_PATH)/led.c

ifneq (,$(findstring ALIOS,${TARGET_OS}))
NONATIVE_SRC_FILES += $(BSP_PATH)/irq_alios.s
endif

ifneq (,$(findstring THREADX,${TARGET_OS}))
NONATIVE_SRC_FILES += $(BSP_PATH)/irq.s
endif

# Platform-dependent files: either included or excluded for some of the platforms
#ifneq  (,$(findstring SILICON_TTC,${VARIANT_LIST}))
# TTC specific
#PACKAGE_INC_PATHS += $(BUILD_ROOT)/csw/BSP/src/include $(BUILD_ROOT)/csw/PMTTC_PRM/inc
#NONATIVE_SRC_FILES += \
#	      $(BSP_PATH)/os_tick.c  \
#	      $(BSP_PATH)/bsp_ttc.c

#else
# Harbell specific
PACKAGE_INC_PATHS += $(BSP_PATH)/include
PACKAGE_SRC_FILES += \
	      bsp_tavor.c    \
	      miccoConfig.c  \
	      err_data_dump.c
#	      levante.c\

# only build in LWG
 ifneq (,$(findstring BUILD_LWG,${VARIANT_LIST}))
#   PACKAGE_SRC_FILES += levante.c
 endif

NONATIVE_SRC_FILES += \
	      $(BSP_PATH)/Results_Buffer.c \
	      $(BSP_PATH)/diag_Mem_Test.c \
	      $(BSP_PATH)/xscale_stubs.c
#endif

# These are the tool flags specific to the BSP package only.
# The environment, target, and group also set flags.

PACKAGE_ASMFLAGS =
PACKAGE_CFLAGS   =
PACKAGE_CPPFLAGS =
PACKAGE_DFLAGS   =
PACKAGE_ARFLAGS  =
ifneq (, $(findstring EDEN_1928, $(VARIANT_LIST)))
PACKAGE_ASMFLAGS += --predefine "_EDEN_1928_ SETL {TRUE}"
endif
ifneq  (,$(findstring CPONLY,${VARIANT_LIST}))
PACKAGE_ASMFLAGS += --predefine "PHS_SW_TTC_CPONLY SETL {TRUE}"
endif

ifneq  (,$(findstring DKB,${VARIANT_LIST}))
PACKAGE_ASMFLAGS += --predefine "PHS_SW_TTC_DKB SETL {TRUE}"
endif

ifneq  (,$(findstring SILICON_TTC,${VARIANT_LIST}))
#PACKAGE_ASMFLAGS += --predefine "TTC_BSP_ARM946_CP15 SETL {TRUE}"
#PACKAGE_CFLAGS   +=-DTTC_BSP_ARM946_CP15
endif
ifneq  (,$(findstring DL,${VARIANT_LIST}))
PACKAGE_CFLAGS   +=-DLOCAL_NVM
endif

ifneq  (,$(findstring PHS_SW_DEMO_PM,${VARIANT_LIST}))
PACKAGE_ASMFLAGS += --predefine "GPIO_TRACE SETL {TRUE}"
PACKAGE_ASMFLAGS += --predefine "PHS_SW_DEMO_TTC_PM SETL {TRUE}"
PACKAGE_CFLAGS   +=-DGPIO_TRACE
endif

#
# Allow this definition to enable CCCR configurations
# followed by an infinite loop. (Raviv 20/12/05)
#
#PACKAGE_ASMFLAGS += -predefine "ENABLE_L1_LOOP SETL {TRUE}"
#

ifneq  (,$(findstring DATACARD,${VARIANT_LIST}))
PACKAGE_CFLAGS   +=-DTD_DATACARD
endif


# The package dependency file
PACKAGE_DEP_FILE = BSP_dep.mak


# Include the Standard Package Make File ---------------
include $(GEN_PACK_MAKEFILE)

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include $(PACKAGE_DEP_FILE)

