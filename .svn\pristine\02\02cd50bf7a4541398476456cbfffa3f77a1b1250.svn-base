#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#=========================================================================
# File Name      : usb_device.mak
# Description    : Main make file for the hal/USB package.
#
# Usage          : make [-s] -f USB.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
# Varients:
#               NOCICODE - disconnect all dependiency in CI (ChipIdea) USB2.0 code.
#               USB2ONLY - make sure only USB2 code participate in compilation.
#
# Compilation Flags:
#               NOCICODE - disconnect all dependiency in CI (ChipIdea) USB2.0 code.
#               USB2ONLY - make sure only USB2 code participate in compilation.
#               MV_USB2_FULL_SPEED_MODE - work with USB2.0 in FS mode (defualt HS).
#               MV_USB_TRACE_LOG - debug info collected into global variable.
#               MV_USB_TRACE_PRINT - debug info printed to UART.
#                       not fully supported currently, need to correlate appropriate
#                       descriptors to FS in USB2.
#
# Copyright (c) 2001 Intel Corporation. All Rights Reserved
#=========================================================================

# Package build options
include ${OPT_FILE}

# Package Makefile information
GEN_PACK_MAKEFILE = ${BUILD_ROOT}/env/${HOST}/build/package.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = usb_device
PACKAGE_BASE     = hal
PACKAGE_DEP_FILE = usb_device_dep.mak
PACKAGE_PATH     = $(BUILD_ROOT)/$(PACKAGE_BASE)/$(PACKAGE_NAME)

# The relative path locations of source and include file directories.
PACKAGE_SRC_PATH    = $(PACKAGE_PATH)/src

PACKAGE_INC_PATHS   = $(PACKAGE_PATH)/src $(PACKAGE_PATH)/inc  \
                        ${BUILD_ROOT}\hal\usb_device\test\inc    \
                        ${BUILD_ROOT}\hal\usb_cable\inc    \
                        ${BUILD_ROOT}\hal\usb_standart\inc    \
                        ${BUILD_ROOT}\hal\intc\inc     \
                        ${BUILD_ROOT}\hal\dma\inc     \
                        ${BUILD_ROOT}\hal\core\inc    \
                        ${BUILD_ROOT}\hal\keypad\inc     \
			$(BUILD_ROOT)\diag\diag_comm\inc \
                        ${BUILD_ROOT}\softutil\mmi\inc   \
                        ${BUILD_ROOT}\pcac\td_telephony\modem\inc


# Package source files, paths not required
PACKAGE_SRC_FILES = usb_device.c usb_modem.c usb_netcard.c

# Varient that enable excluding of USB1.1 code from SW (e.g. only USB2 should exist).
#ifeq (,$(findstring USB2ONLY ,${VARIANT_LIST}))
PACKAGE_SRC_FILES += \
                usb1_device.c  \
                udc_driver.c
#else
        PACKAGE_DFLAGS  = -DUSB2ONLY
#endif

# Varient that enable excluding of CI (chipidea) code from SW.
#ifeq (,$(findstring NOCICODE ,${VARIANT_LIST}))

PACKAGE_SRC_PATH  += $(PACKAGE_PATH)/src/cidriver
PACKAGE_INC_PATHS += $(PACKAGE_PATH)/src/cidriver
PACKAGE_SRC_FILES += \
                usb2_device.c \
                mvUsbDevCh9.c   \
                mvUsbDevMain.c \
                mvUsbDevRecv.c \
                mvUsbDevSend.c \
                mvUsbDevUtl.c \
                mvUsbHsDevCncl.c \
                mvUsbHsDevUtl.c \
                mvUsbHsDevMain.c \
                mvUsbStorage.c
#		HSIC.c
#else
	PACKAGE_DFLAGS  = -DNOCICODE
#endif


# These are the tool flags specific to the USB package only.
# The environment, target, and group also set flags.
PACKAGE_CFLAGS  =
#PACKAGE_DFLAGS  += -DMV_USB_TRACE_LOG
#PACKAGE_DFLAGS  += -DMV_USB_TRACE_PRINT

#PACKAGE_DFLAGS  += -DMV_USB2_FULL_SPEED_MODE
PACKAGE_ARFLAGS =

# Define Package Variants -------------------------------

# look for the variants in the VARIANT_LIST and override
# setting from the previous section. The variables
# USB_VARIANT_1 and USB_VARIANT_2
# are meant to be overwritten with actual variant names.

# handle the USB_VARIANT_1 variant ------------
ifneq (,$(findstring USB_TEST ,${VARIANT_LIST}))

# Package source files, paths not required
PACKAGE_SRC_FILES += usb_tests.c
PACKAGE_SRC_PATH  += $(PACKAGE_PATH)/test/src

# These are the tool flags specific to the USB package only.
# The environment, target, and group also set flags.
PACKAGE_CFLAGS  =
PACKAGE_DFLAGS  += -D_DO_TESTS_
PACKAGE_ARFLAGS =

endif

# handle the USB_VARIANT_2 variant ------------
ifneq (,$(findstring USB_VARIANT_2 ,${VARIANT_LIST}))

PACKAGE_VARIANT = USB_VARIANT_2

# Package source files, paths not required
PACKAGE_SRC_FILES = ^SRC_FILE_LIST

# These are the tool flags specific to the USB package only.
# The environment, target, and group also set flags.
PACKAGE_CFLAGS  =
PACKAGE_DFLAGS  =
PACKAGE_ARFLAGS =

endif

# Include the Standard Package Make File ---------------
include ${GEN_PACK_MAKEFILE}

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include ${PACKAGE_DEP_FILE}









