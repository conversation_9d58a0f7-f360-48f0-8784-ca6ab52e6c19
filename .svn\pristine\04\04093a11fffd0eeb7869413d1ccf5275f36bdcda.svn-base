/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_GNSS_BadSignalList_H_
#define	_GNSS_BadSignalList_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct BadSignalElement;

/* GNSS-BadSignalList */
typedef struct GNSS_BadSignalList {
	A_SEQUENCE_OF(struct BadSignalElement) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GNSS_BadSignalList_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GNSS_BadSignalList;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "LPPBadSignalElement.h"

#endif	/* _GNSS_BadSignalList_H_ */
#include <asn_internal.h>
