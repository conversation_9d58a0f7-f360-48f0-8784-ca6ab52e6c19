/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

#include <stdio.h>
#include <string.h>

//#define SERIAL_NOT_ON_BOARD

#if !defined (HEARTBEAT_ON_SERIAL)
#if !defined (SERIAL_NOT_ON_BOARD) && defined (PLATFORM_ONLY)
#define HEARTBEAT_ON_SERIAL
#endif
#endif

#if !defined (SERIAL_NOT_ON_BOARD)

#include "watchdog.h"
#include "EEHandler.h"
#include "UART.h"

static const UARTConfiguration uartConfig=
	{
    /*configuration[port].opMode           */    UART_FIFO_CTL,
    /*configuration[port].triggerLevel     */    UART_INT_TRIGGER_L32,
    /*configuration[port].baudRate         */    UART_BAUD_115200,
    /*configuration[port].numDataBits      */    UART_WORD_LEN_8,
    /*configuration[port].stopBits         */    UART_ONE_STOP_BIT,
    /*configuration[port].parityBitType    */    UART_NO_PARITY_BITS,
    /*configuration[port].interfaceType    */    UART_BASIC_INTERFACE,
    /*configuration[port].modemSignal      */    FALSE,
	/*					  flowControl	   */	 FALSE,
	/*					  sleepMode        */	 FALSE,
	/*configuration[port].auto_baud        */    FALSE,
	/*                    IrDA             */    {FALSE,FALSE,FALSE,FALSE,FALSE}
	};


//static FatalEventAction_fn* pfnOldAction;

/*---------------------------------------------------------------------------------
 Absolut Index :         0                  1                 2
     UART_PORT : UART_PORT_FFUART   UART_PORT_BTUART   UART_PORT_STUART
                      Boerne             Hermon        Harbell, Bulverde,TTC
---------------------------------------------------------------------------------*/
//#if (defined(INTEL_2CHIP_PLAT_BVD) && !defined(_TAVOR_BOERNE_)) && (defined(SILICON_PV2)|| defined (_TAVOR_HARBELL_)) || defined (SILICON_TTC)
#if (defined(INTEL_2CHIP_PLAT_BVD) && !defined(_TAVOR_BOERNE_)) && (defined(SILICON_PV2)) || defined (SILICON_TTC)
  #define UART_PORT_EE_FATAL     UART_PORT_STUART
#else
#if defined(_TAVOR_BOERNE_)
  #define UART_PORT_EE_FATAL     UART_PORT_FFUART
#else
  #define UART_PORT_EE_FATAL     UART_PORT_BTUART
#endif//_TAVOR_BOERNE_
#endif

volatile const  UART_Port  eeFatalComPortConst = UART_PORT_EE_FATAL;
volatile        UART_Port  eeFatalComPort;

extern void RTI_LOG(const char* fmt, ...);

static void uartInit(UART_Port   port)
{
	UARTClose(port); //just in case it was in use
	UARTConfigure(port, &uartConfig);
    UARTHwInterruptDisable(port);
	UARTOpen(port);
    UARTHwInterruptDisable(port);
}

static void printString(const char* msg)
{
  int len=strlen(msg);
  while(len)
  {
  	/*unused code*/
	/*coverity[incompatible_cast]*/
    UARTFIFOWrite(eeFatalComPort,(UINT8*)msg,(UINT16*)&len);
  }
}


// Blocking loop for delay (call it in LOW priority). 0x20000000 = ~13 seconds
#define DELAY_LOOP(COUNTER)    { volatile UINT32 delay = COUNTER; while(delay) delay--;}

void finalActionSerialLoop(const char* errorString,               //mandatory, event description string
						   const char* file,                      //optional, may appear as NULL
						   int line,                              //optional, may appear as 0
		                   EE_entry_t type,                       //mandatory, event type (enum)
		                   const EE_RegInfo_Data_t* regContext,   //register context
						   RTC_CalendarTime* calendarTime)       //RTC time
{
#ifndef CRANE_MCU_DONGLE
  #define PROMPT_DELAY  0x04000000 /* ~4sec */
  #define KICK_DELAY    0x00400000 /* ~0.25sec */
  static int entered = 0;
  UINT8 i = 0;

  if(entered++) //Prevent nesting of ASSERTs from different tasks:
        while(1); // only first entered pass through, others are whiled forever
  else
  {
  /*static*/ char eeAssertfailMsg [256];  // We could safely use Stack-RAM instead static
    volatile UINT32 promptLoop = 0;

    eeFatalComPort = eeFatalComPortConst;
	uartInit(eeFatalComPort);

    while(1)
    {
        if (promptLoop == 0)
        {
            // The printString() uses UART FIFO, which has size 64bytes only !!!
            // Give delay between 64byte chunks.
            //    Split to chunk TBD
            if (errorString != NULL)
                printString (errorString);
            DELAY_LOOP(0x00003000);
            sprintf (eeAssertfailMsg, "\n\r%d EE@FATAL %s #%d\n\r", i++, file, line);
            printString (eeAssertfailMsg);
            promptLoop = PROMPT_DELAY;
        }
        if (promptLoop%KICK_DELAY == 0)
            watchdogKick();

        promptLoop--;
    }
  }
#else
    //volatile UINT32 *reg = NULL;

    if (file != NULL)
    {
        RTI_LOG("EE@FATAL F:%s, L:%d", file, line);
    }
    else
    {
        if(regContext != NULL)
        {
            RTI_LOG("EE@Exception PC:0x%x, LR:0x%x, SP:0x%x, R0:0x%x, R1:0x%x, R2:0x%x",
                                regContext->PC,
                                regContext->LR,
                                regContext->SP,
                                regContext->r0,
                                regContext->r1,
                                regContext->r2);
        }
    }

    DELAY_LOOP(0x00003000);

#if 0
	reg = (volatile UINT32 *)0xD4050200;
    *reg = 4;
	reg = (volatile UINT32 *)0xD4050200;
	*reg = 3;

    *((volatile UINT32 *)0xd4050020) |= 0x10;
    PMIC_Reset();
#endif
#endif
}

/***********************************************************************************************/

//ICAT EXPORTED FUNCTION - EE_HANDLER,EE,eeSerialOutputBind
void eeSerialOutputBind(void)
{
  /*pfnOldAction = */EEHandlerExternalFinalActionBind(finalActionSerialLoop);
}

#endif//if not SERIAL_NOT_ON_BOARD


#if !defined (HEARTBEAT_ON_SERIAL)
void DBG_Heartbeat(int isActive)    {}
void DBG_HeartbeatMsgSet(char* msg) {}
void DBG_HeartbeatActivate(int isActive){}
void SerialHeartbeatOff(void)       {}
#else
#include "osa.h"
#include "csw_mem.h"
#define   HEARTBEAT_TASK_PRIORITY   249  /* check vs INIT_TASK */
#define   HEARTBEAT_STACK_SIZE      600
OSTaskRef HeartbeatTaskRef;
char     *HeartbeatTaskStack;
char      HeartbeatMsgNum[4] = {'9',' ',0,0};
char     *HeartbeatMsgExternal = NULL;
BOOL      HeartbeatIsRinning;

VOID HeartbeatTask(VOID *argv)
{
    HeartbeatMsgNum[0] = '0';
    eeFatalComPort = eeFatalComPortConst;
	uartInit(eeFatalComPort);

    printString("\n\rSTART Heartbeat TASK \n\r");

    while(HeartbeatIsRinning)
    {
        HeartbeatMsgNum[0]++;
        if(HeartbeatMsgNum[0] > '9')
        {
           HeartbeatMsgNum[0] = '0';
           printString("\n\rHeart-bit ");
           OSATaskSleep(400);
        }
        if(HeartbeatMsgExternal)
            printString(HeartbeatMsgExternal);
        else
            printString(HeartbeatMsgNum);

        OSATaskSleep(400);
    }
    UARTClose(eeFatalComPort);
}

void DBG_HeartbeatActivate(int isActive)
{
    OS_STATUS      status;

  if(!isActive)
  {
      HeartbeatIsRinning = FALSE;
      return;
  }
  if(HeartbeatIsRinning)  return; //is already running

  if(HeartbeatTaskStack == NULL)
  {
     HeartbeatTaskStack = extMemDynMalloc( HEARTBEAT_STACK_SIZE );
     ASSERT(HeartbeatTaskStack != NULL);
  }
  HeartbeatIsRinning = TRUE;

  if(HeartbeatTaskRef != NULL)
  {
     status = OSATaskDelete(HeartbeatTaskRef);
     ASSERT(status == OS_SUCCESS);
     HeartbeatTaskRef = NULL;
     OSATaskSleep(200);
  }
  status = OSATaskCreate(&HeartbeatTaskRef, HeartbeatTaskStack, HEARTBEAT_STACK_SIZE, HEARTBEAT_TASK_PRIORITY, "Heartbeat", HeartbeatTask, NULL);
  ASSERT(status == OS_SUCCESS);
}

void DBG_HeartbeatMsgSet(char* msg)
{
    UINT32   cpsr = disableInterrupts();
    HeartbeatMsgExternal = msg;
    restoreInterrupts(cpsr);
}

//ICAT EXPORTED FUNCTION - Validation,Heartbeat,SerialHeartbeatOn
void SerialHeartbeatOn(void)
{ DBG_HeartbeatActivate(TRUE);
}

//ICAT EXPORTED FUNCTION - Validation,Heartbeat,SerialHeartbeatOff
void SerialHeartbeatOff(void)
{ DBG_HeartbeatActivate(FALSE);
}

#endif//HEARTBEAT_ON_SERIAL

