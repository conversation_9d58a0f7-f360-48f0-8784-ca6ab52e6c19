/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utlg_sig.h#9 $
 *   $Revision: #9 $
 *   $DateTime: 2006/04/11 12:18:38 $
 **************************************************************************
 * File Description:
 *
 * definition of signals sent to/from UT_LOG task.
 *
 * Taken from abgp_sig.h v1.11
 **************************************************************************/

#ifndef UTLG_SIG_H
#define UTLG_SIG_H


#include <system.h>
#if !defined (EXCLUDE_UT_LOGGER)
#include <utlg_typ.h>
#include <uterr.h>
#include <l3_typ.h> /* needs l3_typ.h for definition of messageheader */

#if defined (UPGRADE_GPRS)
#include <llc_sig.h>
#include <sm_sig.h>
#endif /* UPGRADE_GPRS */


/*****************************************************************************
 * Constants
 *****************************************************************************/

#define UT_PDU_LOGGER_MAX_DATA_SIZE 2000

/*****************************************************************************
 * Typedefs
 *****************************************************************************/

/*****************************************************************************
 * Error logging signals
 *****************************************************************************/
#define ERROR_VAR ERROR_STRUCT
#include <uterrdef.h>

union UtErrorUnion
{
#   include <uterrors.h>
};

typedef struct UtLogErrorReqTag
{
    FrameTicks          relativeTime;
    UtErrorType         id;
    union UtErrorUnion  error;
}
UtLogErrorReq;

typedef struct UtLogPduIndTag
{
    Boolean sending;
    TaskId sourceTask;
    Int32 pduCounter;
    FrameTicks relativeTime;
    Int16 pduLen;
    Int8 pdu[UT_PDU_LOGGER_MAX_DATA_SIZE];
}
UtLogPduInd;

#endif /* !defined (EXCLUDE_UT_LOGGER) */

#endif

/* END OF FILE */
