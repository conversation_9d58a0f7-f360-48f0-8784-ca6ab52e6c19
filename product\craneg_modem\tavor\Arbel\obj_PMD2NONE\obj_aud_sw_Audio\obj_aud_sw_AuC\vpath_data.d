\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \aud_sw\AuC\src\vpath_data.c
\aud_sw\AuC\src\vpath_data.c:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \aud_sw\Audio\inc\audio_def.h
\aud_sw\Audio\inc\audio_def.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \aud_sw\AuC\inc\vpath_data.h
\aud_sw\AuC\inc\vpath_data.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\platform\inc\mmap.h
\csw\platform\inc\mmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hal\MMU\inc\mmu.h
\hal\MMU\inc\mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\BSP\inc\bsp.h
\csw\BSP\inc\bsp.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\PM\inc\powerManagement.h
\csw\PM\inc\powerManagement.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\pm\inc\pm_config.h
\hop\pm\inc\pm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \softutil\TickManager\inc\tick_manager.h
\softutil\TickManager\inc\tick_manager.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\timer\inc\timer.h
\hop\timer\inc\timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\SysCfg\inc\syscfg.h
\csw\SysCfg\inc\syscfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\timer\inc\timer_config.h
\hop\timer\inc\timer_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\intc_list.h
\hop\intc\inc\intc_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hal\GPIO\inc\gpio_config.h
\hal\GPIO\inc\gpio_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\intc_list_xirq.h
\hop\intc\inc\intc_list_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\xirq_config.h
\hop\intc\inc\xirq_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hal\GPIO\inc\gpio.h
\hal\GPIO\inc\gpio.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hal\GPIO\inc\cgpio_HW.h
\hal\GPIO\inc\cgpio_HW.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\intc_xirq.h
\hop\intc\inc\intc_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\BSP\inc\levante_hw.h
\hop\BSP\inc\levante_hw.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\BSP\inc\levante.h
\hop\BSP\inc\levante.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\BSP\inc\loadTable.h
\csw\BSP\inc\loadTable.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\BSP\inc\bsp_config.h
\csw\BSP\inc\bsp_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\BSP\inc\ptable.h
\csw\BSP\inc\ptable.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\BSP\inc\asr_property.h
\hop\BSP\inc\asr_property.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \aud_sw\AuC\inc\PCA_api.h
\aud_sw\AuC\inc\PCA_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\acmTypes.h
\tavor\Arbel\obj_PMD2NONE\inc\acmTypes.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \aud_sw\AuC\inc\vpath_ctrl.h
\aud_sw\AuC\inc\vpath_ctrl.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \CRD\IPC\inc\IPCComm.h
\CRD\IPC\inc\IPCComm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \CRD\IPC\inc\WS_IPCComm.h
\CRD\IPC\inc\WS_IPCComm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \CRD\IPC\inc\WS_IPCCommConfig.h
\CRD\IPC\inc\WS_IPCCommConfig.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\platform\inc\IPC_gbl_config.h
\csw\platform\inc\IPC_gbl_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\PM\inc\prm.h
\csw\PM\inc\prm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hal\UART\inc\UART.h
\hal\UART\inc\UART.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \hop\pmu\inc\pmu.h
\hop\pmu\inc\pmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \diag\diag_logic\src\diag_nvm.h
\diag\diag_logic\src\diag_nvm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \aud_sw\Audio\inc\audio_bind.h
\aud_sw\Audio\inc\audio_bind.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\acm_comm.h
\tavor\Arbel\obj_PMD2NONE\inc\acm_comm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\audio_atc.h
\tavor\Arbel\obj_PMD2NONE\inc\audio_atc.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\acm_audio_primitive.h
\tavor\Arbel\obj_PMD2NONE\inc\acm_audio_primitive.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\aam.h
\tavor\Arbel\obj_PMD2NONE\inc\aam.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \csw\PM\inc\pm_dbg_types.h
\csw\PM\inc\pm_dbg_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\aam_config.h
\tavor\Arbel\obj_PMD2NONE\inc\aam_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\commpm.h
\tavor\Arbel\obj_PMD2NONE\inc\commpm.h:
\tavor\Arbel\obj_PMD2NONE\obj_aud_sw_Audio\obj_aud_sw_AuC/vpath_data.o : \tavor\Arbel\obj_PMD2NONE\inc\singleToneDect.h
\tavor\Arbel\obj_PMD2NONE\inc\singleToneDect.h:
