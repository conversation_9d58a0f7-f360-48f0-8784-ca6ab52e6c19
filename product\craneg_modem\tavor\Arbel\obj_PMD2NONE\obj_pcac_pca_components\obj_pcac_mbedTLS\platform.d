\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\platform.c
\pcac\mbedTLS\mbedTLS_3_2_1\library\platform.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\common.h
\pcac\mbedTLS\mbedTLS_3_2_1\library\common.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : \tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h
\tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_time.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h:
