/*************************************************************************/
/*                                                                       */
/* FILE NAME                                               VERSION       */
/*                                                                       */
/*      atnet.c                                     Nucleus PLUS 1.15 */
/*                                                                       */
/* COMPONENT                                                             */
/*                                                                       */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This file contains the TCP/UDP/FTP task for AT Command control */
/*                                                                       */
/* DATA STRUCTURES                                                       */
/*                                                                       */
/*      None                                                             */
/*                                                                       */
/*                                                                       */
/*                                                                       */
/*************************************************************************/
#ifndef NO_EXTEND_MY_Q_AT
#include <time.h>
#include <stdlib.h>
#include <stdarg.h>
#include <string.h>
#include <ctype.h>
#include <stddef.h>
#include <stdio.h>
//#include "common_print.h"
#include "sockets.h"
#include "ip_addr.h"
#include "def.h"
#include <stdint.h>
#include <inttypes.h>
#include "netdb.h"
#include "osa.h"
#include "connect_management.h"
#include "atnet.h"
#include "sgled.h"
#include "cmux_api.h"
#include "mi_api.h"
#include "sys_arch.h"
#include "teldef.h"
#include "atnetssl.h"
#include "duster.h"
#include "telcontroller.h"
#include "dialer_task.h"
#include "mat_response.h"

#define atnet_printf(fmt, args...) do { CPUartLogPrintf("[atnet]"fmt, ##args); } while(0)
//#define atnet_printf(fmt, args...) do { uart_printf("[atnet]"fmt, ##args); } while(0)
//#define atnet_printf(fmt,args...)     uart_printf("[atnet]"fmt"\r\n", ##args)

#define sleep(x) OSATaskSleep((x) * 200)//second
#define msleep(x) OSATaskSleep((x) * 20)//100*msecond

//#define ATNETCI_TO_PROXY_MESSAGE_Q_SIZE sizeof(struct miRequestArgs)
#define ATNETCI_TO_PROXY_MESSAGE_Q_SIZE sizeof(struct miApiMsg)

#define  ATNETCI_TO_PROXY_MESSAGE_Q_MAX 1600

#define ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE sizeof(struct miRequestArgs)
#define  ATNETPROXY_TO_TCPDUP_MESSAGE_Q_MAX 10

#define ATNET_ROTA_FLAG 0xA5BC
#define RECV_MAX_BUFF 3072
#define SEND_MAX_BUFF 2048
#define ATNET_FD_MAX 64
#define ATNETERRNO errno
#define ATNET_TASK_PRIORITY 70
#define ATNET_TRANS_TASK_PRIORITY 70
#define ATNET_SEND_TIMER_PRIORITY 200

#define ATNET_TASK_STACK_SIZE 4096



#define SG_NET
#ifdef SG_NET

#ifndef toupper
#define toupper(x) ((((x) >= 'A') && ((x) <= 'Z')) ? (x) : ((x) - 'a' + 'A'))
#endif

#define ATNET_MAX_READ_LEN 3072

unsigned char isAtNetSupported = 1;
static OSTaskRef AtNetProxyWorkerRef[6]={0};
static OSTaskRef AtNetTcpUdpWorkerRef[6]={0};
static OSSemaRef AtNetSemaRef[6] = {0};
OSMsgQRef AtNetCiToProxyMsgQ[6]={0};
OSMsgQRef AtNetProxyToTCPUdpMsgQ[6]={0};
OSMsgQRef AtNetProxyToTCPServerMsgQ[6]={0};


struct netUdpDataBackup udpdataBackup[6];


static AtNet_Context atnet_con[6];
struct netSetIpfilterReq ipFilter[MAX_IPFILER_NUM];
static OSATimerRef udp_send_timer[6]={0};

extern UINT8 gSimCardMode;

extern void PM812_SW_RESET(void);

int send_connect_apdu(int sATPInd, int type, int ip, int port);

AtNet_Context* atnet_get_context_by_netId(int netId)
{
	return &atnet_con[netId];
}

int atnet_context_is_transport_channel_internal(int channel)
{
	int i = 0;
	for (i = 0; i < 6; i++)
	{
		if (atnet_con[i].channel==channel
			&& atnet_con[i].netType!=1
			&& atnet_con[i].transportMode==1)
			break;
	}
	if(i>=6){
		return 0;
	}
	return 1;


}


int cmuxGetSvcIdByConnectIdInternal(UINT8   ConnectID)
{

	int i = 0;
	for (i = 0; i < 6; i++)
	{
		if ((atnet_con[i].socket_map[0].fd > 0) && (atnet_con[i].socket_map[0].SocketId==ConnectID))
			break;
	}
	return i;
}

INT32 get_free_socketID()
{
	int i = 0;
	for (i = 0; i < 6; i++)
	{
		if (atnet_con[i].socket_map[0].fd <= 0)
			break;
	}
	return i;

}


int atnet_ip_filter2(UINT32 client_addr, int netId)
{
	int i;
	UINT32 ip;
	UINT32 ip_mask;
	int flag=0;

	for(i=0;i<MAX_IPFILER_NUM;i++){
		if(ipFilter[i].action ==1){
			flag = 1;
			inet_aton(ipFilter[i].ipStr,&ip);
			inet_aton(ipFilter[i].netMask,&ip_mask);
			atnet_printf("%s[%d], ipStr %s[%08x],netMask %s[%08x], client_addr[%08x]",__FUNCTION__, netId, ipFilter[i].ipStr,ip,ipFilter[i].netMask,ip_mask,client_addr);
			if((client_addr & ip_mask) == (ip & ip_mask)){
				flag = 0;
				break;
			}

		}
	}
	return flag;

}

int atnet_save_data(char *buf, int  bytes, int  netId)
{
	AtNet_Context *con;
	int rest_bytes;

	con = atnet_get_context_by_netId(netId);

	AtNetMutexLock(netId);
	if(con->recv.rota_flag != ATNET_ROTA_FLAG){
		if (con->recv.end < con->recv.begin)
		    return 0;
		if((con->recv.buffer + RECV_MAX_BUFF - con->recv.end)<bytes){
			memcpy(con->recv.end, buf, (con->recv.buffer + RECV_MAX_BUFF - con->recv.end));
			rest_bytes = bytes - (con->recv.buffer + RECV_MAX_BUFF - con->recv.end);
			con->recv.rota_flag = ATNET_ROTA_FLAG;
			if(rest_bytes > (con->recv.begin - con->recv.buffer)){
				memcpy(con->recv.buffer, buf + (con->recv.buffer + RECV_MAX_BUFF - con->recv.end), (con->recv.begin - con->recv.buffer));
				con->recv.end = con->recv.begin;
				atnet_printf("%s[%d], %d, recv buffer full, drop %d bytes",__FUNCTION__, netId, __LINE__,rest_bytes - (con->recv.begin - con->recv.buffer));
				AtNetMutexUnlock(netId);
				return (con->recv.buffer + RECV_MAX_BUFF - con->recv.end) + (con->recv.begin - con->recv.buffer);
			}else{
				memcpy(con->recv.buffer, buf + (con->recv.buffer + RECV_MAX_BUFF - con->recv.end), rest_bytes);
				con->recv.end = con->recv.buffer + rest_bytes;
				AtNetMutexUnlock(netId);
				return bytes;
			}

		}else{
			memcpy(con->recv.end, buf, bytes);
			con->recv.end =con->recv.end + bytes;
			AtNetMutexUnlock(netId);
			return bytes;
		}
	}else{
		if (con->recv.end > con->recv.begin)
		 return 0;
		if((con->recv.begin - con->recv.end)<bytes){
			memcpy(con->recv.end, buf, (con->recv.begin - con->recv.end));
			con->recv.end = con->recv.begin;
			atnet_printf("%s[%d], %d, recv buffer full, drop %d bytes",__FUNCTION__, netId, __LINE__, bytes - (con->recv.begin - con->recv.end));
			AtNetMutexUnlock(netId);
			return (con->recv.begin - con->recv.end);
		}else{
			memcpy(con->recv.end, buf, bytes);
			con->recv.end =con->recv.end + bytes;
			AtNetMutexUnlock(netId);
			return bytes;
		}
	}
}

int atnet_connect(int fd, struct sockaddr *server)
{
    int res=-1;
	unsigned long on = 1;
	fd_set write_fds, read_fds;
	struct timeval tv;
	tv.tv_sec = 8;
	tv.tv_usec = 0;
	/*
	int bytes=0;
	char buf;
	struct sockaddr_in ser;
	socklen_t addrlen;
	*/
	int value;
	int len;

	struct sockaddr_in * dst =(struct sockaddr_in *)server;
	#if 0
    if(!is_E20_module_type()){
        if(send_connect_apdu(TEL_AT_CMD_ATP_3,1,ntohl(dst->sin_addr.s_addr),ntohs(dst->sin_port))!=0){
            atnet_printf("%s, apdu failed sin_port[%d] ip[%s]",__FUNCTION__,ntohs(dst->sin_port),inet_ntoa(dst->sin_addr.s_addr));
               return MIRC_FAIL;
        }

    }
	#endif

	FD_ZERO(&write_fds);
	FD_ZERO(&read_fds);

	FD_SET(fd, &write_fds);
	FD_SET(fd, &read_fds);



	ioctlsocket(fd, FIONBIO, &on);//set non-blocking i/o

	res = connect(fd, (struct sockaddr *)server, sizeof(struct sockaddr));
	if(res == 0){
	    atnet_printf("%s,connect success",__FUNCTION__);
        res = MIRC_SUCCESS;
	}else{
	    if(ATNETERRNO != EINPROGRESS){
            res = MIRC_TCP_CONN_REJECT;
            atnet_printf("%s,connect error %d",__FUNCTION__,ATNETERRNO);
            goto out;
	    }

	    atnet_printf("%s,connect EINPROGRESS",__FUNCTION__);
        res = select(fd+1, &read_fds, &write_fds, NULL, &tv);
        switch(res)
        {
            case -1:
                atnet_printf("%s,select error %d",__FUNCTION__,ATNETERRNO);
                res = MIRC_TCP_CONN_REJECT;
                break;
            case 0:
                atnet_printf("%s,select time out",__FUNCTION__);
                res = MIRC_TCP_CONN_TIMEOUT;
                break;
            case 2:
                atnet_printf("%s,connect may be reset",__FUNCTION__);
                len = sizeof(value);
                //get so_error to check connect be RST or not
                getsockopt(fd, SOL_SOCKET, SO_ERROR, &value, (socklen_t *)&len);
                if(value == ECONNRESET){
                    res = MIRC_TCP_CONN_REJECT;
                }else{
                    res = MIRC_SUCCESS;
                }
                atnet_printf("%s,get SO_ERROR= %d",__FUNCTION__,value);
                /*
                //using recv to check connect be RST or not
                bytes = recvfrom(fd, buf, 0, 0, &ser, &addrlen);
                if(bytes < 0){
                    res = MIRC_TCP_CONN_REJECT;
                }else{
                    res = MIRC_SUCCESS;
                }
                atnet_printf("%s,select recvfrom %d",__FUNCTION__,bytes);
                */
                break;
            default:
            {

				len = sizeof(value);
                //get so_error to check connect be RST or not
                getsockopt(fd, SOL_SOCKET, SO_ERROR, &value, (socklen_t *)&len);
				if(value == ECONNRESET){
					res = MIRC_TCP_CONN_REJECT;
				}else{
					 res = MIRC_SUCCESS;
				}

				atnet_printf("%s,select return %d,value[%d]",__FUNCTION__,res,value);
                  /*
		                if(!FD_ISSET(fd,&read_fds)&&!FD_ISSET(fd,&write_fds)){
		                    res = MIRC_TCP_CONN_REJECT;
		                    atnet_printf("%s,select not read and write",__FUNCTION__);
		                    break;
		                }
		                if(ATNETERRNO!=0){
		                    res = MIRC_TCP_CONN_REJECT;
		                    atnet_printf("%s,select get error %d",__FUNCTION__,ATNETERRNO);
		                    break;
		                }
		                */
                break;
             }
        }

	}
out:
    on = 0;
	ioctlsocket(fd, FIONBIO, &on);//clear non-blocking i/o
    return res;


}

static INT32 atnet_send_data(int fd, char *data, int len, int netId)
{
	int bytes = 0;
	int index = 0;
	AtNet_Context *con = atnet_get_context_by_netId(netId);

	while(len){
#ifdef ATNET_SSL
	    if(con->ssl_enable && con->sslclient){
            bytes = atnet_ssl_write(con->sslclient, (const unsigned char *)(data + index), len);
	    }
	    else
#endif
	    {
            bytes = send(fd, data + index, len, 0);
	    }

		if(bytes < 0){
			atnet_printf("%s[%d], send error:%s",__FUNCTION__, netId, ATNETERRNO);
			set_E20_error_num(E20_SOCKET_WRITE_FAILED);
			return MIRC_FAIL;
		}else{
			len = len -bytes;
			index = index + bytes;
		}
	}
	return MIRC_SUCCESS;
}

static INT32 atnet_send_data_remote(int fd, char *data, int len, int netId, struct sockaddr_in *remote)
{
	int bytes = 0;
	int index = 0;
	socklen_t dstlen = sizeof(struct sockaddr_in);

	while(len){
	    bytes = sendto(fd, data + index, len, 0, (struct sockaddr*)remote, dstlen);
		if(bytes < 0){
			atnet_printf("%s[%d], send error:%s",__FUNCTION__, netId, ATNETERRNO);
			set_E20_error_num(E20_SOCKET_WRITE_FAILED);
			return MIRC_FAIL;
		}else{
			len = len -bytes;
			index = index + bytes;
		}
	}
	return MIRC_SUCCESS;
}


int atnet_send_apdu_to_sim(UINT32 client_addr, int netId)
{
	int ret=-1;
	char *buf=NULL;
	char *at_cmd=NULL;
	char at_res[10];
	buf = (char *)malloc(256);
	if(buf== NULL){
        return 0;
	}
	
	memset(buf, 0, 256);
	if(gSimCardMode != 0){
		if(gSimCardMode == 1){
			strcpy(buf,"A0");
		}else if(gSimCardMode == 2){
			strcpy(buf,"80");
		}
		atnet_printf("%s[%d], client_addr: %08X",__FUNCTION__, netId,client_addr);
		atnet_printf("%s[%d], client_addr: %08X",__FUNCTION__, netId,ntohl(client_addr));
		strcpy(buf+strlen(buf),"C2000046D144820283810607910000000000008B35400C910000000000007FF67190729075050022027000001D0D00000000B0002000000000010080EC00400A3608A104");
		sprintf(buf+strlen(buf), "%08X",client_addr);
		strcpy(buf+strlen(buf),"A200");
		at_cmd = (char *)malloc(256);
		if(at_cmd){
            memset(at_cmd, 0, 256);
            sprintf(at_cmd, "AT+CSIM=%d,%s\r\n",strlen(buf), buf);
            ret = SendATCMDWaitResp(TEL_ATNET_AT_CMD_ATP,at_cmd, 150, "+CSIM", 1, NULL, at_res, sizeof(at_res));
            if(ret!=0)
            {
                atnet_printf("%s[%d], error: send AT+CSIM failed",__FUNCTION__, netId);
            }
            atnet_printf("%s[%d],send AT+CSIM response %s",__FUNCTION__, netId, at_res);
            free(at_cmd);
		}


	}else{
		atnet_printf("%s[%d], error: unknow the sim card mode",__FUNCTION__, netId);
	}
	free(buf);

	return 0;
}
static int atnet_tcp_server_recv(int fd,int netId, UINT32 requestHandle)
{
	int skfd_new = -1;
	int fdmax, j, bytes;
	fd_set master, read_fds;
	int len;
	int res;
	//char *buf=NULL;
	socklen_t addrlen;
	AtNet_Context* con=NULL;
	struct sockaddr_in  cliaddr;
	struct miRequestArgs *req=NULL;
	struct miApiMsg msg;
	struct  urcDataInd * data_ind;
	struct  urcDataPushInd * data_push_ind;
	struct  urcCloseInd * close_ind;
	struct  urcIncomingClientInd * incoming_ind;
	struct netSocketAcceptReq *accept_req;
	struct netSocketAcceptCnf *accept_cnf;
	OSA_STATUS osa_status;
	struct netSocketReadCnf *read_cnf;

	UINT32 svcID = netId + SVCID_NET0;
    int errorno=0;

	fdmax = ATNET_FD_MAX;
	/* Clear and set the FD set */
	FD_ZERO(&master);
	FD_ZERO(&read_fds);
	FD_SET(fd, &master);

	con = atnet_get_context_by_netId(netId);
	while (1) {
		atnet_printf("%s[%d], start select",__FUNCTION__,netId);

		read_fds = master;
		if (select(fdmax, &read_fds, NULL, NULL, NULL) == -1) {
		    errorno=lwip_getsockerrno(fd);
		    atnet_printf("%s[%d], select error %d", __FUNCTION__,netId,errorno);
		    if(errorno==ENETRESET || errorno==ECONNABORTED || errorno==ENOTCONN){
				if(con->socket_map[0].fd == 0){
					atnet_printf("%s[%d], select is ERROR, the fd be closed!",__FUNCTION__,netId);
					return 0;
				}
				msg.msgID = MI_REQUEST_MSG;
				req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
				if(req){
                    req->primID = AT_NET_TCP_SERVER_ACCEPT_ERROR;
                    close_ind = (struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
                    if(close_ind){
                        close_ind->socketID=con->socket_map[0].SocketId;
                        req->param=(void *)close_ind;
                        msg.pArgs = (void*)req;
                        OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);
                        atnet_printf("%s[%d], close server socket at line %d", __FUNCTION__, netId, __LINE__);
                    
                    }else{
                        free(req);
                    }
				}
				return 0;

		    }
			
			sleep(1);
			continue;
		}

		for (j = 0; j < fdmax; j++) {
			if (!FD_ISSET(j, &read_fds)) {
				continue;
			}

			if (j == fd) {

				if(con->socket_map[0].fd == 0){
					atnet_printf("%s[%d], select is Accept,but the fd be closed!",__FUNCTION__,netId);

					return 0;
				}
				len = sizeof(cliaddr);
				skfd_new = lwip_accept(fd,(struct sockaddr *)&cliaddr, (socklen_t *)&len);
				if (skfd_new < 0) {
					atnet_printf("%s[%d], Accept socket failed, error %d", __FUNCTION__, netId,ATNETERRNO);
					if (ATNETERRNO == EWOULDBLOCK) {
						atnet_printf("%s[%d], EWOULDBLOCK",__FUNCTION__, netId);
						sleep(1);
					} else {
						msg.msgID = MI_REQUEST_MSG;
						req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
						if(req){
                            req->primID = AT_NET_TCP_SERVER_ACCEPT_ERROR;
                            close_ind = (struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
                            if(close_ind){
                                close_ind->socketID=con->socket_map[0].SocketId;
                                req->param=(void *)close_ind;
                                msg.pArgs = (void*)req;
                                OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);
                                atnet_printf("%s[%d], close server socket at line %d", __FUNCTION__, netId, __LINE__);
                            
                            }else{
                                free(req);
                            }
						}
						return 0;
					}
				} else {
				    con->remote_port = cliaddr.sin_port;
				    con->remote_ip = cliaddr.sin_addr.s_addr;
					atnet_printf("%s[%d], Accept client socket %d,%s ", __FUNCTION__,netId, skfd_new,inet_ntoa(cliaddr.sin_addr.s_addr));
					#if 0
                    if(!is_E20_module_type()){
                        if(send_connect_apdu(TEL_AT_CMD_ATP_3,2,ntohl(cliaddr.sin_addr.s_addr),ntohs(cliaddr.sin_port))!=0){
                            atnet_printf("%s, apdu failed sin_port[%d] ip[%s]",__FUNCTION__,ntohs(cliaddr.sin_port),inet_ntoa(cliaddr.sin_addr.s_addr));
                            close(skfd_new);
                            continue;
                        }

                    }
					#endif

					if(atnet_ip_filter2(cliaddr.sin_addr.s_addr,netId)){
						//atnet_send_apdu_to_sim(cliaddr.sin_addr.s_addr,netId);
						close(skfd_new);
						atnet_printf("%s[%d], ip filter return ture",__FUNCTION__, netId);
						continue;
					}
					req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
					if(req== NULL){
						close(skfd_new);
					    req=NULL;
						continue;
					}

					struct urcClientInd *client_ind;
					client_ind = (struct urcClientInd *)malloc(sizeof(struct urcClientInd));
					if(client_ind== NULL){
						close(skfd_new);
						free(req);
					    req=NULL;
						continue;

					}
					memset(client_ind,0,sizeof(struct urcClientInd));

					client_ind->socketID=get_free_socketID();
					if(client_ind->socketID < 6 ){

						atnet_printf("%s[%d], get incoming client socket ID %d", __FUNCTION__, netId, client_ind->socketID);
						msg.msgID = MI_REQUEST_MSG;

						req->primID = AT_NET_TCP_SERVER_INCOMING_CLIENT;
						incoming_ind = (struct urcIncomingClientInd *)malloc(sizeof(struct urcIncomingClientInd));
						if(incoming_ind){
                            incoming_ind->channel = con->channel;
                            incoming_ind->socketID=client_ind->socketID;
                            incoming_ind->skfd = skfd_new;
                            incoming_ind->viewMode = con->viewMode;
                            req->param=(void *)incoming_ind;
                            msg.pArgs = (void*)req;
                            OSAMsgQSend(AtNetCiToProxyMsgQ[incoming_ind->socketID], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);

						}else{
                            close(skfd_new);
                            free(client_ind);
                            free(req);
                            req=NULL;
                            continue;

						}


					}else{
						atnet_printf("%s[%d], socket_map full",__FUNCTION__,netId);
						close(skfd_new);
						free(client_ind);
						free(req);
					    req=NULL;
						continue;
					}

					client_ind->port = ntohs(cliaddr.sin_port);
					sprintf(client_ind->ipStr,"%s",inet_ntoa(cliaddr.sin_addr.s_addr));

					//call API to send the ind message
					if(con->accept_transportMode == 0){
						miInd(svcID, svcID, MI_UCR_CLIENT_IND, con->channel, (void *)client_ind);
					}
					free(client_ind);
				}
				continue;
			}

		}

	}
	//return 0;
}


static int atnet_tcp_client_recv(int fd,int netId,UINT32 requestHandle)
{
	AtNet_Context* con=NULL;
	char *buf=NULL;
	int fdmax, j, bytes;
	int res;
	fd_set master, read_fds;
	struct sockaddr_in server;
	socklen_t addrlen;
	struct  urcDataInd * data_ind;
	struct  urcDataPushInd * data_push_ind;
	struct netSocketReadCnf *read_cnf;
	struct  urcCloseInd * close_ind;
	struct miRequestArgs *req;
	struct miApiMsg msg;
	UINT32 svcID = netId + SVCID_NET0;
	fdmax = ATNET_FD_MAX;
	int errorno=0;

	FD_ZERO(&master);
	FD_ZERO(&read_fds);
	FD_SET(fd, &master);


	buf = (char *)malloc(RECV_MAX_BUFF);
	if(buf == NULL){
		FD_CLR(fd, &master);
		free(buf);
		return 0;   
	}
	con = atnet_get_context_by_netId(netId);
	while (1) {

		read_fds = master;
		if (select(fdmax, &read_fds, NULL, NULL, NULL) == -1) {

		    errorno=lwip_getsockerrno(fd);
		    atnet_printf("%s[%d], select error %d", __FUNCTION__,netId,errorno);
		    if(errorno==ENETRESET || errorno==ECONNABORTED || errorno==ENOTCONN){
				if(con->socket_map[0].fd == 0){
					atnet_printf("%s[%d], select error! found fd alredy be closed", __FUNCTION__,netId);
					FD_CLR(fd, &master);
					free(buf);
					return 0;

				}
                FD_CLR(fd, &master);
                atnet_printf("%s[%d], select error!", __FUNCTION__,netId);
                msg.msgID = MI_REQUEST_MSG;
                req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
                if(req){
                    req->primID = AT_NET_RECV_ERROR;
                    close_ind = (struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
                    if(close_ind){
                        close_ind->socketID=con->socket_map[0].SocketId;
                        req->param=(void *)close_ind;
                        msg.pArgs =(void *)req;
                        OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);
                    }else{
                        free(req);
                    }
                }
 
                free(buf);
                return 0;

		    }

			sleep(1);
			continue;
		}

		for (j = 0; j < fdmax; j++) {
			if (!FD_ISSET(j, &read_fds)) {
				continue;
			}

			if(j == fd){
				if(con->socket_map[0].fd==0){
					atnet_printf("%s[%d], select found fd alredy be closed", __FUNCTION__,netId);
					FD_CLR(j, &master);
					free(buf);
					return 0;
				}
				memset(buf,0,RECV_MAX_BUFF);
#ifdef ATNET_SSL
				if(con->ssl_enable && con->sslclient){
                    bytes = atnet_ssl_read(con->sslclient, (const unsigned char *)buf, RECV_MAX_BUFF);
				}
				else
#endif
				{
                    bytes = recvfrom(fd, buf, RECV_MAX_BUFF, 0, (struct sockaddr *)&server, &addrlen);
				}

				if (bytes <= 0) {

					FD_CLR(j, &master);
					atnet_printf("%s[%d], Recv data failed,bytes=%d", __FUNCTION__,netId,bytes);
					msg.msgID = MI_REQUEST_MSG;
					req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
					if(req){
                        req->primID = AT_NET_RECV_ERROR;
                        close_ind = (struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
                        if(close_ind){
                            close_ind->socketID=con->socket_map[0].SocketId;
                            req->param=(void *)close_ind;
                            msg.pArgs =(void *)req;
                            OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);
                            free(buf);

                        }else{
                            free(req);
                        }
					}

					return 0;
				}else{
				    con->Total_read += bytes;
					if(con->transportMode==0){
						atnet_printf("%s[%d], recv data length %d, %s",__FUNCTION__,netId, bytes,buf);
						res = atnet_save_data(buf,bytes,netId);
						if(res){
							req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
							if(req){
                                if(con->ssl_enable && con->sslclient){
                                    req->primID = MI_UCR_SSL_DATA_IND;
                                }else{
                                    req->primID = MI_UCR_DATA_IND;
                                }
                                
                                data_ind = (struct urcDataInd *)malloc(sizeof(struct urcDataInd));
                                if(data_ind){
                                    data_ind->socketID = con->socket_map[0].SocketId;
                                    req->param=(void *)data_ind;
                                    //call indication API
                                    miInd(svcID, svcID,req->primID, con->channel, req->param);
                                    free(data_ind);
                                }
                                free(req);
							}

						}else{
							atnet_printf("%s[%d], look like the recv buffer full",__FUNCTION__,netId);
						}
					}else if(con->transportMode==2){
					    data_push_ind = (struct urcDataPushInd *)malloc(sizeof(struct urcDataPushInd));
					    memset(data_push_ind,0,sizeof(struct urcDataPushInd));
					    data_push_ind->buf = buf;
					    data_push_ind->len = bytes;
					    data_push_ind->socketID = con->socket_map[0].SocketId;
					    if(con->ssl_enable && con->sslclient){
                            miInd(svcID, svcID,MI_UCR_SSL_DATA_PUSH_IND, con->channel, data_push_ind);
						}else{
                            miInd(svcID, svcID,MI_UCR_DATA_PUSH_IND, con->channel, data_push_ind);
						}

                        free(data_push_ind);
					}else{
						atnet_printf("%s[%d], transport mode,recv data length %d,%s",__FUNCTION__,netId, bytes,buf);
						read_cnf = (struct netSocketReadCnf *)malloc(sizeof(struct netSocketReadCnf));
						if(read_cnf){
                            read_cnf->result = MIRC_SUCCESS;
                            read_cnf->dataLen = bytes;
                            read_cnf->data = buf;
                            read_cnf->socketID = con->socket_map[0].SocketId;
                            read_cnf->viewMode = con->viewMode;
                            read_cnf->contextID = con->channel;
                            miResponse(svcID, svcID, MI_NET_SET_SOCKET_TRANS_READ_DATA_CNF, requestHandle, (void *) read_cnf);
                            free(read_cnf);

						}

					}
				}
			}
		}
	}

	//free(buf);

	//return 0;

}

static void save_udp_data_to_backup(char *data,int dataLen,int netId)
{
    int len =0;
    AtNet_Context *con;
    int result=0;

    if(dataLen<=0)
        return;

    if(udpdataBackup[netId].buf==NULL)
        return;


    con = atnet_get_context_by_netId(netId);

    len = udpdataBackup[netId].len;

    if(dataLen>=1500){
        result=atnet_send_data(con->socket_map[0].fd , udpdataBackup[netId].buf, len, netId);
        atnet_printf("%s[%d], result %d,len %d", __FUNCTION__,netId,result,len);


        result=atnet_send_data(con->socket_map[0].fd , data, dataLen, netId);
        atnet_printf("%s[%d], result %d,dataLen %d", __FUNCTION__,netId,result,dataLen);
        udpdataBackup[netId].len = 0;


    }else if((len + dataLen)>= 1500){
        result=atnet_send_data(con->socket_map[0].fd , udpdataBackup[netId].buf, len, netId);
        atnet_printf("%s[%d], result %d,len %d", __FUNCTION__,netId,result,len);

        result=atnet_send_data(con->socket_map[0].fd , data, dataLen, netId);
        atnet_printf("%s[%d], result %d,dataLen %d", __FUNCTION__,netId,result,dataLen);

        udpdataBackup[netId].len = 0;
    }else{
        memcpy(udpdataBackup[netId].buf+len,data,dataLen);
        udpdataBackup[netId].len += dataLen;

    }

    return;
}

static void clear_udp_data_backup_buf(int netId)
{
    udpdataBackup[netId].len = 0;
    if(udpdataBackup[netId].buf)
        memset(udpdataBackup[netId].buf,0,2048);
    return;
}

static void send_udp_data_backup_buf(int fd, int netId)
{
    if(udpdataBackup[netId].len){
        atnet_send_data(fd , udpdataBackup[netId].buf, udpdataBackup[netId].len, netId);
        udpdataBackup[netId].len = 0;
        if(udpdataBackup[netId].buf)
            memset(udpdataBackup[netId].buf,0,2048);
    }
    return;
}


static void _send_udp_data_delay(UINT32 ctx)
{
	struct netTimerCon *nettimercon= (struct netTimerCon *)ctx;
    struct miApiMsg msg = { 0 };
    struct miRequestArgs * req;
    atnet_printf("%s[%d] data len=%d", __FUNCTION__,nettimercon->netId,nettimercon->udpdata->len);
    if(nettimercon->udpdata->len >0 ){
        msg.msgID = MI_REQUEST_MSG;
        req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
        if(req){
            req->primID = MI_NET_TRANSPORT_UDP_BACKUP_SEND_REQ;
            msg.pArgs =(void *)req;
            OSAMsgQSend(AtNetCiToProxyMsgQ[nettimercon->netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_NO_SUSPEND);
        }

    }
    free(nettimercon);
}


static void start_atnet_timer(int netId)
{
    OSA_STATUS status = 0;
    struct netTimerCon *nettimercon;
    nettimercon = (struct netTimerCon *)malloc(sizeof(struct netTimerCon));
    nettimercon->netId = netId;
    nettimercon->udpdata = &udpdataBackup[netId];
    atnet_printf("%s[%d]", __FUNCTION__,netId);
    //delay 50ms
    if(udp_send_timer[netId]){
	    status = OSATimerStart(udp_send_timer[netId], 10, 0, _send_udp_data_delay, (unsigned int)nettimercon);
	}else{
        free(nettimercon);
	}
    /* coverity[leaked_storage] */
}


static int atnet_udp_recv(int fd,int netId, UINT32 requestHandle)
{
	AtNet_Context* con=NULL;
	char *buf=NULL;
	int fdmax, j, bytes;
	int res;
	fd_set master, read_fds;
	struct sockaddr_in server;
	socklen_t addrlen;
	struct  urcDataInd * data_ind;
	struct  urcDataPushInd * data_push_ind;
	struct  urcCloseInd * close_ind;
	struct miRequestArgs *req;
	struct netSocketReadCnf *read_cnf;
	struct miApiMsg msg;
	UINT32 svcID = netId + SVCID_NET0;
	fdmax = ATNET_FD_MAX;
    int errorno = 0;

	FD_ZERO(&master);
	FD_ZERO(&read_fds);
	FD_SET(fd, &master);


	buf = (char *)malloc(RECV_MAX_BUFF);
	if(buf == NULL){
        FD_CLR(fd, &master);
        free(buf);
        return 0;
	}
	con = atnet_get_context_by_netId(netId);
	while (1) {

		read_fds = master;
		if (select(fdmax, &read_fds, NULL, NULL, NULL) == -1) {
			errorno=lwip_getsockerrno(fd);
		    atnet_printf("%s[%d], select error %d", __FUNCTION__,netId,errorno);
		    if(errorno==ENETRESET || errorno==ECONNABORTED || errorno==ENOTCONN){
				if(con->socket_map[0].fd == 0){
					atnet_printf("%s[%d], select error! found fd alredy be closed", __FUNCTION__,netId);
					FD_CLR(fd, &master);
					free(buf);
					return 0;

				}
                FD_CLR(fd, &master);
                atnet_printf("%s[%d], select error!", __FUNCTION__,netId);
                msg.msgID = MI_REQUEST_MSG;
                req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
                if(req){
                    req->primID = AT_NET_RECV_ERROR;
                    close_ind = (struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
                    if(close_ind){
                        close_ind->socketID=con->socket_map[0].SocketId;
                        req->param=(void *)close_ind;
                        msg.pArgs =(void *)req;
                        OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);
                    }
                }
                free(buf);
                return 0;

		    }
			sleep(1);
			continue;
		}

		for (j = 0; j < fdmax; j++) {
			if (!FD_ISSET(j, &read_fds)) {
				continue;
			}

			if(j == fd){
				atnet_printf("%s[%d], select found fd", __FUNCTION__,netId);
				if(con->socket_map[0].fd==0){
					FD_CLR(j, &master);

					free(buf);
					return 0;
				}
				memset(buf,0,RECV_MAX_BUFF);

				bytes = recvfrom(fd, buf, RECV_MAX_BUFF, 0, (struct sockaddr *)&server, &addrlen);
				if (bytes <= 0) {
					FD_CLR(j, &master);
					atnet_printf("%s[%d], Recv data failed", __FUNCTION__,netId);
					msg.msgID = MI_REQUEST_MSG;
					req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
					if(req){
    					req->primID = AT_NET_RECV_ERROR;
    					close_ind = (struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
    					if(close_ind){
                            close_ind->socketID=con->socket_map[0].SocketId;
                            req->param=(void *)close_ind;
                            msg.pArgs = (void *)req;
                            OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);
                            
    					}
                    }
                    free(buf);

					return 0;
				}else{
				    con->Total_read += bytes;
					if(con->transportMode==0){
					    con->remote_port = server.sin_port;
				        con->remote_ip = server.sin_addr.s_addr;
						atnet_printf("%s[%d], recv data %s %d",__FUNCTION__,netId,
							inet_ntoa(server.sin_addr.s_addr),
							ntohs(server.sin_port));
					    atnet_printf("%s[%d], recv data length %d, %s",__FUNCTION__,netId, bytes,buf);
						res = atnet_save_data(buf,bytes,netId);
						if(res){
							req = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
							if(req){
    							req->primID = MI_UCR_DATA_IND;
    							data_ind = (struct urcDataInd *)malloc(sizeof(struct urcDataInd));
    							if(data_ind) {
                                    data_ind->socketID = con->socket_map[0].SocketId;
                                    req->param=(void *)data_ind;
                                    //call indication API
                                    miInd(svcID, svcID,req->primID, con->channel, req->param);
                                    
                                    free(data_ind);

    							}
                                free(req);
                            }

						}else{
							atnet_printf("%s[%d], look like the recv buffer full",__FUNCTION__,netId);
						}
					}else if(con->transportMode==2){
					    data_push_ind = (struct urcDataPushInd *)malloc(sizeof(struct urcDataPushInd));
					    memset(data_push_ind,0,sizeof(struct urcDataPushInd));
					    data_push_ind->buf = buf;
					    data_push_ind->len = bytes;
					    data_push_ind->socketID = con->socket_map[0].SocketId;
					    if(con->netType == 3){
                            data_push_ind->remote_port = ntohs(server.sin_port);
                            sprintf(data_push_ind->remote_ipStr,"%s",inet_ntoa(server.sin_addr.s_addr));
					    }
                        miInd(svcID, svcID,MI_UCR_DATA_PUSH_IND, con->channel, data_push_ind);
                        free(data_push_ind);
					}else{
						atnet_printf("%s[%d], transport mode,recv data length %d, %s",__FUNCTION__,netId, bytes,buf);
						read_cnf = (struct netSocketReadCnf *)malloc(sizeof(struct netSocketReadCnf));
						if(read_cnf){
                            read_cnf->result = MIRC_SUCCESS;
                            read_cnf->dataLen = bytes;
                            read_cnf->data = buf;
                            read_cnf->socketID = con->socket_map[0].SocketId;
                            read_cnf->viewMode = con->viewMode;
                            read_cnf->contextID = con->channel;
                            miResponse(svcID, svcID, MI_NET_SET_SOCKET_TRANS_READ_DATA_CNF, requestHandle, (void *) read_cnf);
                            free(read_cnf);


						}

					}
				}
			}
		}
	}

	//free(buf);
	//return 0;

}
int atnet_asr_socket_service_open(struct asrOpenReq *asropen,struct miRequestArgs *req,int netId)
{
    struct hostent* host_entry;
    int fd;
    struct sockaddr_in sa;
    struct sockaddr_in server;
    AtNet_Context *con;
    con = atnet_get_context_by_netId(netId);
    int result;
    char ipstr[128];
    int reuseport = 1;

    if(con->socket_map[0].fd>0){
        set_E20_error_num(E20_SOCKET_IDENTITY_HAS_BEEN_USED);
        atnet_printf("%s[%d], service_req, DNS gethostbyname failed: %s\n",__FUNCTION__,netId,asropen->ip_address);
        return MIRC_FAIL;
    }

    host_entry = gethostbyname(asropen->ip_address);
    if (host_entry == NULL) {
        set_E20_error_num(E20_DNS_PARSE_FAILED);
        atnet_printf("%s[%d], service_req, DNS gethostbyname failed: %s\n",__FUNCTION__,netId,asropen->ip_address);
        return MIRC_FAIL;
    }
    atnet_printf("%s[%d],DNS gethostbyname,Get %s ip %d.%d.%d.%d\n", __FUNCTION__,netId,asropen->ip_address, host_entry->h_addr_list[0][0] & 0xff,
        host_entry->h_addr_list[0][1] & 0xff, host_entry->h_addr_list[0][2] & 0xff, host_entry->h_addr_list[0][3] & 0xff);

    if(asropen->service_type==ASR_SERVICE_TCP || asropen->service_type==ASR_SERVICE_TCP_LISTENER){
        fd = socket(AF_INET, SOCK_STREAM, 0);
    }else{
        atnet_printf("%s[%d], UDP",__FUNCTION__,netId);
        fd = socket(AF_INET, SOCK_DGRAM, 0);
    }
    if(fd <=0 ){
        set_E20_error_num(E20_CREATE_SOCKET_FAILED);
        return MIRC_FAIL;
    }
    memset(ipstr,0,128);
    cmuxGetPdpIpStr(con->channel,ipstr);
    atnet_printf("%s[%d], cmuxGetPdpIpStr %s",__FUNCTION__, netId, ipstr);

    if(setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &reuseport,  sizeof(reuseport))<0){
        atnet_printf("%s[%d], setsockopt SO_REUSEPORT failed",__FUNCTION__,netId);
    }
    sa.sin_family       = AF_INET;
    sa.sin_port         = htons(asropen->local_port);
    //sa.sin_addr.s_addr  = 0;
    inet_aton(ipstr,&sa.sin_addr);

    if (bind(fd, (const struct sockaddr *)&sa, sizeof(sa)) < 0) {
        set_E20_error_num(E20_SOCKET_BIND_FAILED);
        close(fd);
        atnet_printf("%s[%d], bind error",__FUNCTION__, netId);
        return MIRC_FAIL;
    }

    server.sin_family = AF_INET;
    server.sin_port = htons(asropen->remote_port);
    server.sin_addr.s_addr= * (UINT32 *) host_entry->h_addr_list[0];

    if(asropen->service_type==ASR_SERVICE_TCP){
        con->remote_port = asropen->remote_port;
        con->remote_ip = server.sin_addr.s_addr;
        con->netType = 0;

        result = atnet_connect(fd,(struct sockaddr *)&server);
        if(result != MIRC_SUCCESS){
            set_E20_error_num(E20_SOCKET_CONNECT_FAILED);
            close(fd);
            atnet_printf("%s,  connect error",__FUNCTION__);
            return MIRC_FAIL;
        }else{
            con->recv.buffer = (char *)malloc(RECV_MAX_BUFF);
            if(con->recv.buffer == NULL){
                set_E20_error_num(E20_MEMORY_NOT_ENOUGH);
                close(fd);
                return MIRC_FAIL;
            }
            con->recv.begin = con->recv.buffer;
            con->recv.end = con->recv.buffer;
            con->recv.rota_flag = 0;

            con->type = 1;
#ifdef ATNET_SSL
            if (asropen->ssl_enable) {
        		con->sslclient = atnet_ssl_client_init(fd,asropen->ssl_ctxid);
        		if (con->sslclient == NULL) {
        		    set_E20_error_num(E20_SOCKET_CONNECT_FAILED);
                    close(fd);
        			atnet_printf("Try build SSL client failed");
					free(con->recv.buffer); //chenjian
        			return MIRC_FAIL;
        		}
        		con->ssl_enable = asropen->ssl_enable;
                con->ssl_ctx_id = asropen->ssl_ctxid;
        	}
#endif
        }
    }else if(asropen->service_type==ASR_SERVICE_TCP_LISTENER){
        con->netType = 1;
        con->action = 1;


        if (listen(fd, 5) < 0) {
            set_E20_error_num(E20_SOCKET_LISTEN_FAILED);
            close(fd);
            atnet_printf("%s[%d], listen error",__FUNCTION__,netId);
            return MIRC_FAIL;
        }
        con->listen = 1;
    }else {

        con->recv.buffer = (char *)malloc(RECV_MAX_BUFF);
        if(con->recv.buffer == NULL) {
            set_E20_error_num(E20_MEMORY_NOT_ENOUGH);
            close(fd);
            return MIRC_FAIL;

        }
        con->recv.begin = con->recv.buffer;
        con->recv.end = con->recv.buffer;
        con->recv.rota_flag = 0;

       if(asropen->service_type==ASR_SERVICE_UDP){
            con->remote_port = asropen->remote_port;
            con->remote_ip = server.sin_addr.s_addr;
            result = connect(fd, (struct sockaddr *)&server, sizeof(server));
            if(result<0){
                atnet_printf("%s[%d], udp connect error",__FUNCTION__,netId);
            }
            con->netType = 2;
       }else{
            con->netType = 3;
       }
       con->type = 1;
    }
    con->channel = asropen->ContextID;
    con->viewMode = 0;
    con->socket_map[0].SocketId = asropen->ConnectID;
    con->socket_map[0].fd = fd;
    con->net_socket.ip_port = asropen->remote_port;
    if(asropen->access_mode == 2){
        con->transportMode = 1;
    }else if(asropen->access_mode == 1){
        con->transportMode = 2;
    }else{
        con->transportMode = 0;
    }
    req->primID = AT_NET_ASR_OPEN;


    OSAMsgQSend(AtNetProxyToTCPUdpMsgQ[netId], ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,(UINT8 *)req, OSA_SUSPEND);

    return MIRC_SUCCESS;


}

int atnet_asr_socket_query_state(struct asrStateReq *asrstate,struct miRequestArgs *req,int netId)
{
    int i,j;
    struct asrStateCnf *statecnf;
    LinkStatus_Context * cm_link_status = NULL;
    Ipv4Info * ipv4 = NULL;
    unsigned int ipaddr;
    statecnf=(struct asrStateCnf *)malloc(sizeof(struct asrStateCnf ));
    memset(statecnf,0,sizeof(struct asrStateCnf ));
    UINT32 svcID = netId + SVCID_NET0;

    cm_link_status = CM_Get_LinkStatus(0);
    if (!cm_link_status){
        atnet_printf("%s[%d], CM_Get_LinkStatus failed !!",__FUNCTION__,netId);
        free(statecnf);
        return -1;
    }

    j=0;
    if(asrstate->QueryType == 2){
        for(i=0;i<AT_NET_MAX;i++){
            if(atnet_con[i].socket_map[0].fd>0){
                statecnf->info[j].ContextID = atnet_con[i].channel;
                statecnf->info[j].ConnectID = atnet_con[i].socket_map[0].SocketId;
                statecnf->info[j].local_port = atnet_con[i].net_socket.local_port;
                if(atnet_con[i].transportMode==2){
                    statecnf->info[j].access_mode = 1;
                }else if(atnet_con[i].transportMode==1){
                    statecnf->info[j].access_mode = 2;
                }else{
                    statecnf->info[j].access_mode = 0;
                }

                statecnf->info[j].service_type =atnet_con[i].netType;
                if(atnet_con[i].netType==0 || atnet_con[i].netType==2){
                    statecnf->info[j].remote_port = ntohs(atnet_con[i].remote_port);
                    sprintf(statecnf->info[j].ip_address,"%s",inet_ntoa(atnet_con[i].remote_ip));
                }else{
                    ipv4 = cm_link_status->ip4info;
                    if (ipv4) {
                        ipaddr = lwip_htonl(ipv4->IPAddr);
                        sprintf(statecnf->info[j].ip_address,"%s",inet_ntoa(ipaddr));
                        statecnf->info[j].remote_port = 0;
                    }
                }
                if(atnet_con[i].socket_map[0].fd<=0){
                    statecnf->info[j].socket_state =4;
                }else if((atnet_con[i].netType==0 || atnet_con[i].netType==2)&& atnet_con[i].type == 1){
                    statecnf->info[j].socket_state =2;
                }else if((atnet_con[i].netType==0 || atnet_con[i].netType==2) && atnet_con[i].type == 0){
                    statecnf->info[j].socket_state =0;
                }else if((atnet_con[i].netType==1 || atnet_con[i].netType==3) && atnet_con[i].action == 0){
                    statecnf->info[j].socket_state =2;
                }else if((atnet_con[i].netType==1 || atnet_con[i].netType==3) && atnet_con[i].action == 1){
                    statecnf->info[j].socket_state =3;
                }
                statecnf->info[j].serverID =0;
                statecnf->info[j].ssl_enable = atnet_con[i].ssl_enable;
                statecnf->info[j].ssl_ctx_id = atnet_con[i].ssl_ctx_id;
            }
            j++;
            statecnf->count = j;
        }
    }else if(asrstate->QueryType == 1){
         for(i=0;i<AT_NET_MAX;i++){
            if(atnet_con[i].socket_map[0].fd>0 && atnet_con[i].socket_map[0].SocketId == asrstate->ConID){
                statecnf->info[j].ContextID = atnet_con[i].channel;
                statecnf->info[j].ConnectID = atnet_con[i].socket_map[0].SocketId;
                statecnf->info[j].local_port = atnet_con[i].net_socket.local_port;
                if(atnet_con[i].transportMode==2){
                    statecnf->info[j].access_mode = 1;
                }else if(atnet_con[i].transportMode==1){
                    statecnf->info[j].access_mode = 2;
                }else{
                    statecnf->info[j].access_mode = 0;
                }

                statecnf->info[j].service_type =atnet_con[i].netType;
                if(atnet_con[i].netType==0 || atnet_con[i].netType==2){
                    statecnf->info[j].remote_port = ntohs(atnet_con[i].remote_port);
                    sprintf(statecnf->info[j].ip_address,"%s",inet_ntoa(atnet_con[i].remote_ip));
                }else{
                    ipv4 = cm_link_status->ip4info;
                    if (ipv4) {
                        ipaddr = lwip_htonl(ipv4->IPAddr);
                        sprintf(statecnf->info[j].ip_address,"%s",inet_ntoa(ipaddr));
                        statecnf->info[j].remote_port = 0;
                    }
                }
                if(atnet_con[i].socket_map[0].fd<=0){
                    statecnf->info[j].socket_state =4;
                }else if((atnet_con[i].netType==0 || atnet_con[i].netType==2)&& atnet_con[i].type == 1){
                    statecnf->info[j].socket_state =2;
                }else if((atnet_con[i].netType==0 || atnet_con[i].netType==2) && atnet_con[i].type == 0){
                    statecnf->info[j].socket_state =0;
                }else if((atnet_con[i].netType==1 || atnet_con[i].netType==3) && atnet_con[i].action == 0){
                    statecnf->info[j].socket_state =2;
                }else if((atnet_con[i].netType==1 || atnet_con[i].netType==3) && atnet_con[i].action == 1){
                    statecnf->info[j].socket_state =3;
                }
                statecnf->info[j].serverID =0;
                statecnf->info[j].ssl_enable = atnet_con[i].ssl_enable;
                statecnf->info[j].ssl_ctx_id = atnet_con[i].ssl_ctx_id;
                j++;
                statecnf->count = j;
            }

        }

    }else if(asrstate->QueryType == 0){

         for(i=0;i<AT_NET_MAX;i++){
            if(atnet_con[i].socket_map[0].fd>0 && atnet_con[i].channel == asrstate->ConID){
                statecnf->info[j].ContextID = atnet_con[i].channel;
                statecnf->info[j].ConnectID = atnet_con[i].socket_map[0].SocketId;
                statecnf->info[j].local_port = atnet_con[i].net_socket.local_port;
                if(atnet_con[i].transportMode==2){
                    statecnf->info[j].access_mode = 1;
                }else if(atnet_con[i].transportMode==1){
                    statecnf->info[j].access_mode = 2;
                }else{
                    statecnf->info[j].access_mode = 0;
                }

                statecnf->info[j].service_type =atnet_con[i].netType;
                if(atnet_con[i].netType==0 || atnet_con[i].netType==2){
                    statecnf->info[j].remote_port = ntohs(atnet_con[i].remote_port);
                    sprintf(statecnf->info[j].ip_address,"%s",inet_ntoa(atnet_con[i].remote_ip));
                }else{
                    ipv4 = cm_link_status->ip4info;
                    if (ipv4) {
                        ipaddr = lwip_htonl(ipv4->IPAddr);
                        sprintf(statecnf->info[j].ip_address,"%s",inet_ntoa(ipaddr));
                        statecnf->info[j].remote_port = 0;
                    }
                }
                if(atnet_con[i].socket_map[0].fd<=0){
                    statecnf->info[j].socket_state =4;
                }else if((atnet_con[i].netType==0 || atnet_con[i].netType==2)&& atnet_con[i].type == 1){
                    statecnf->info[j].socket_state =2;
                }else if((atnet_con[i].netType==0 || atnet_con[i].netType==2) && atnet_con[i].type == 0){
                    statecnf->info[j].socket_state =0;
                }else if((atnet_con[i].netType==1 || atnet_con[i].netType==3) && atnet_con[i].action == 0){
                    statecnf->info[j].socket_state =2;
                }else if((atnet_con[i].netType==1 || atnet_con[i].netType==3) && atnet_con[i].action == 1){
                    statecnf->info[j].socket_state =3;
                }
                statecnf->info[j].serverID =0;
                j++;
                statecnf->count = j;
            }

        }
    }

    miResponse(svcID,svcID, MI_NET_SET_ASR_SOCKET_STATE_CNF, req->requestHandle, statecnf);

    free_link_status(cm_link_status);
    free(statecnf);

    return 0;
}

static INT32 e20_close_socket(AtNet_Context *con, struct netSocketCloseReq * close_req, int netId)
{
	INT32 result=MIRC_SUCCESS;
	int fd = 0;
	if((con->netType == 0)
		&& (close_req->socketID == con->socket_map[0].SocketId)
		&& (con->socket_map[0].fd >0) ){
		fd = con->socket_map[0].fd;
		//shutdown(con->socket_map[0].fd, SHUT_RDWR);
		con->socket_map[0].fd = 0;
		con->socket_map[0].SocketId = 0;
		con->net_socket.ip_addr= 0;
		con->net_socket.ip_port = 0;
		con->net_socket.local_port = 0;
		con->service_port = 0;
		con->viewMode = 0;
		con->transportMode = 0;
#ifdef ATNET_SSL
		if(con->ssl_enable){
			atnet_ssl_client_shutdown(netId);
			con->ssl_enable = 0;
			con->ssl_ctx_id = 0;
		}
#endif
		close(fd);
		memset(con->service_addr,0,MAX_STRING_LEN);
		if(con->type == 1){
			AtNetMutexLock(netId);
			con->recv.begin = 0;
			con->recv.end = 0;
			con->recv.rota_flag = 0;
			if(con->recv.buffer){
				free(con->recv.buffer);
				con->recv.buffer =NULL;
			}
			AtNetMutexUnlock(netId);
			con->type = 0;
		}
		//atnet_printf("%s, close success!!",__FUNCTION__);
		result = MIRC_SUCCESS;

	}else if(con->netType == 1){
		if((close_req->socketID == con->socket_map[1].SocketId)
			&& (con->socket_map[1].fd > 0)){
			//shutdown(con->socket_map[1].fd, SHUT_RDWR);

			fd = con->socket_map[1].fd;
			con->socket_map[1].fd = 0;
			con->socket_map[1].SocketId = 0;
			con->net_socket.ip_addr= 0;
			con->net_socket.ip_port = 0;
			con->net_socket.local_port = 0;
			con->accept_transportMode = 0;
			close(fd);
			if(con->action== 0){
				AtNetMutexLock(netId);
				con->recv.begin = 0;
				con->recv.end = 0;
				con->recv.rota_flag = 0;
				if(con->recv.buffer){
					free(con->recv.buffer);
					con->recv.buffer =NULL;
				}
				AtNetMutexUnlock(netId);
				con->action = 1;
			}
			result = MIRC_SUCCESS;
		}else if((close_req->socketID == con->socket_map[0].SocketId)
			&& (con->socket_map[0].fd > 0) ){
			if(con->action== 0){
				if(con->socket_map[1].fd>0){
					fd = con->socket_map[1].fd;
					con->socket_map[1].fd = 0;
					con->socket_map[1].SocketId = 0;
					con->net_socket.ip_addr= 0;
					con->net_socket.ip_port = 0;
					con->net_socket.local_port = 0;
					con->accept_transportMode = 0;
					close(fd);
					if(con->action== 0){
						AtNetMutexLock(netId);
						con->recv.begin = 0;
						con->recv.end = 0;
						con->recv.rota_flag = 0;
						if(con->recv.buffer){
							free(con->recv.buffer);
							con->recv.buffer =NULL;
						}
						AtNetMutexUnlock(netId);
						con->action = 1;
					}
				}
				fd = con->socket_map[0].fd;
				con->socket_map[0].fd = 0;
				con->socket_map[0].SocketId = 0;
				con->service_port = 0;
				con->listen = 0;
				con->transportMode = 0;
				con->viewMode = 0;
				close(fd);
				memset(con->service_addr,0,MAX_STRING_LEN);
				result = MIRC_SUCCESS;
			}else{
				fd = con->socket_map[0].fd;

				//shutdown(con->socket_map[0].fd, SHUT_RDWR);
				con->socket_map[0].fd = 0;
				con->socket_map[0].SocketId = 0;
				con->service_port = 0;
				con->listen = 0;
				con->accept_transportMode = 0;
				con->transportMode = 0;
				con->viewMode = 0;
				close(fd);
				memset(con->service_addr,0,MAX_STRING_LEN);
				result = MIRC_SUCCESS;
			}
		}
	}else if((con->netType == 2 || con->netType == 3)
	&& (close_req->socketID == con->socket_map[0].SocketId)
	&& (con->socket_map[0].fd > 0)){

		//shutdown(con->socket_map[0].fd, SHUT_RDWR);
		fd = con->socket_map[0].fd;
		con->socket_map[0].fd = 0;
		con->socket_map[0].SocketId = 0;
		con->net_socket.ip_addr= 0;
		con->net_socket.ip_port = 0;
		con->net_socket.local_port = 0;
		con->service_port = 0;
		con->transportMode = 0;
		con->viewMode = 0;
		close(fd);
		memset(con->service_addr,0,MAX_STRING_LEN);

		AtNetMutexLock(netId);
		con->recv.begin = 0;
		con->recv.end = 0;
		con->recv.rota_flag = 0;
		if(con->recv.buffer){
			free(con->recv.buffer);
			con->recv.buffer =NULL;
		}
		AtNetMutexUnlock(netId);

		con->type = 0;
		result = MIRC_SUCCESS;
	}else{
		result = MIRC_SOCKET_NOT_EXIST;
		set_E20_error_num(E20_SOCKET_HAS_BEEN_CLOSED);
		atnet_printf("%s[%d], close_req error! not found normal Socket ID,netType[%d], type[%d], action[%d]!!",__FUNCTION__,netId,con->netType,con->type,con->action);
	}
	return result;

}

void atnet_process_tcp_udp(int netId)
{
    AtNet_Context *con;
    Atnet_Socket *cre;
    int fd;
    int result;
    int len;
    int value =0;
    struct sockaddr_in server;
    struct sockaddr_in sa;
    struct miRequestArgs req;
    struct miRequestArgs *req_t;
    struct setGenericCnf *gen;
    struct netSetSocketOpenReq *open_req;
    struct netSetSocketOpenCnf *open_cnf;
    struct netSetSocketOpenReq_t *open_req_t;
    struct netCreateCnf *cre_cnf;
    LinkStatus_Context * cm_link_status = NULL;
    Ipv4Info * ipv4 = NULL;
    unsigned int ipaddr;
    char * string;
    struct miApiMsg msg;
    UINT32 svcID = netId + SVCID_NET0;
    con = atnet_get_context_by_netId(netId);
    int reuseport = 1;
    char ipstr[128];

    while(1)
    {
        memset(&req,0,sizeof(struct miRequestArgs));
        OSAMsgQRecv(AtNetProxyToTCPUdpMsgQ[netId], (UINT8 *)&req, ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE, OSA_SUSPEND);
        switch(req.primID){
            case AT_NET_TCP_SERVER_SERVICE:
				result = MIRC_SUCCESS;
                cre = (Atnet_Socket *)req.param;
				memcpy(&con->net_socket,cre,sizeof(Atnet_Socket));

                req.primID = MI_NET_SET_SERVICE_PARAM_CNF;
                gen = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                if(gen == NULL){
                   free(cre); 
                   break;
                }
                memset(gen, 0, sizeof(struct setGenericCnf));
                gen ->result = result;
                req.param = (void *)gen;
                free(cre);

                //callback API
                miResponse(svcID,svcID, req.primID, req.requestHandle, req.param);
                free(gen);

                break;
            case AT_NET_TCP_CLIENT_SERVICE:
                result = MIRC_SUCCESS;
                cre = (Atnet_Socket *)req.param;

                memcpy(&con->net_socket,cre,sizeof(Atnet_Socket));
                server.sin_family = AF_INET;
                server.sin_port = htons(cre->ip_port);
                server.sin_addr.s_addr= cre->ip_addr;

                req.primID = MI_NET_SET_SERVICE_PARAM_CNF;
                gen = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                if(gen == NULL){
                    free(cre);
                    break;
                }
                gen ->result = result;
                req.param = (void *)gen;
                free(cre);

                //callback API
                miResponse(svcID, svcID,req.primID, req.requestHandle, req.param);
                free(gen);
                break;
            case AT_NET_UDP_SERVICE:
                result = MIRC_SUCCESS;
                cre = (Atnet_Socket *)req.param;
                memcpy(&con->net_socket,cre,sizeof(Atnet_Socket));
                server.sin_family = AF_INET;
                server.sin_port = htons(cre->ip_port);
                server.sin_addr.s_addr= cre->ip_addr;

                req.primID = MI_NET_SET_SERVICE_PARAM_CNF;
                gen = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                if(gen == NULL){
                    free(cre);
                    break;
                }
                gen ->result = result;
                req.param = (void *)gen;
                free(cre);

                //callback API
                miResponse(svcID,svcID, req.primID, req.requestHandle, req.param);
                free(gen);
                break;
            case AT_NET_TCP_SERVER_CREATE:
                result = MIRC_FAIL;
                cre = (Atnet_Socket *)req.param;
                fd = socket(AF_INET, SOCK_STREAM, 0);
                if(fd >0 ){

            		memset(ipstr,0,128);
					cmuxGetPdpIpStr(con->channel,ipstr);
					atnet_printf("%s[%d], cmuxGetPdpIpStr %s",__FUNCTION__, netId, ipstr);

					if(setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &reuseport,  sizeof(reuseport))<0){
	                    atnet_printf("%s[%d], AT_NET_TCP_SERVER_CREATE, setsockopt SO_REUSEPORT failed",__FUNCTION__,netId);
	                }
                    sa.sin_family       = AF_INET;
                    sa.sin_port         = htons(cre->local_port);
                    //sa.sin_addr.s_addr  = 0;
                    inet_aton(ipstr,&sa.sin_addr);
                    if (bind(fd, (const struct sockaddr *)&sa, sizeof(sa)) < 0) {
                        atnet_printf("%s[%d], AT_NET_TCP_SERVER_CREATE, bind error",__FUNCTION__,netId);
                        close(fd);
                    }else{
                        con->socket_map[0].fd = fd;
                        //con->net_socket.ip_addr.addr = cre->ip_addr.addr;
                        //con->net_socket.ip_port = cre->ip_port;
                        result = MIRC_SUCCESS;
                    }

                }else{
                    atnet_printf("%s[%d], AT_NET_TCP_SERVER_CREATE, socket error",__FUNCTION__, netId);
                }
                free(cre);

                req_t = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
                if(req_t== NULL){
                    break;
                }
                req_t->primID = MI_NET_TRANSPORT_OPEN_REQ;
                open_req_t = (struct netSetSocketOpenReq_t *)malloc(sizeof(struct netSetSocketOpenReq_t));
                if(open_req_t == NULL){
                    free(req_t);
                    break;
                }
                open_req_t ->result = result;
                open_req_t->socketID = con->socket_map[0].SocketId;
                req_t->param = (void *)open_req_t;
                req_t->requestHandle = req.requestHandle;

                msg.msgID = MI_REQUEST_MSG;
                msg.pArgs =(void *)req_t;
                OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);

                break;
            case AT_NET_TCP_SERVER_LISTEN:
                open_req = (struct netSetSocketOpenReq *)req.param;

                if (listen(con->socket_map[0].fd, 5) < 0) {
                    result = MIRC_FAIL;
                    atnet_printf("%s[%d], AT_NET_TCP_SERVER_LISTEN, listen error",__FUNCTION__,netId);
                }else{
                    result = MIRC_SUCCESS;
                }
                if(open_req) free(open_req);
#if 0
                if(con->transportMode){
                    cre_cnf =  (struct netCreateCnf *)malloc(sizeof(struct netCreateCnf));
                    assert(cre_cnf!= NULL);
                    cre_cnf->result = result;
                    cre_cnf->mode = con->netType;
                    req.primID = MI_NET_SET_CREATE_CNF;
                    req.param = (void *)cre_cnf;
                    miResponse(svcID, svcID,req.primID, req.requestHandle, req.param);
                    free(cre_cnf);
                    if(result != MIRC_SUCCESS){
                        con->transportMode = 0;
                        con->socket_map[0].SocketId = 0;
                        close(con->socket_map[0].fd);
                        con->socket_map[0].fd = 0;
                    }
                }else
#endif
                {
                    open_cnf = (struct netSetSocketOpenCnf *)malloc(sizeof(struct netSetSocketOpenCnf));
                    if(open_cnf ){
                        memset(open_cnf, 0, sizeof(struct netSetSocketOpenCnf));
                        if(con->transportMode==1){
                            open_cnf->transportMode = 1;
                        }
                        open_cnf->socketID = con->socket_map[0].SocketId;
                        open_cnf->result = result;
                        req.primID = MI_NET_SET_SOCKET_OPEN_CNF;
                        req.param = (void *)open_cnf;
                        
                        //callback to send the create socket result
                        //callback API
                        miResponse(svcID, svcID,req.primID, req.requestHandle, req.param);
                        free(open_cnf);

                    }else{
                        result = MIRC_FAIL;
                    }


                }


                if(result == MIRC_SUCCESS){
                    con->listen = 1;
                    atnet_tcp_server_recv(con->socket_map[0].fd, netId, req.requestHandle);
                }
                break;
            case AT_NET_TCP_CLIENT_CREATE:
                result = MIRC_FAIL;
                cre = (Atnet_Socket *)req.param;
                fd = socket(AF_INET, SOCK_STREAM, 0);
                if(fd >0 ){

                    memset(ipstr,0,128);
                    cmuxGetPdpIpStr(con->channel,ipstr);
                    atnet_printf("%s[%d], cmuxGetPdpIpStr %s",__FUNCTION__, netId, ipstr);

                    if(setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &reuseport,  sizeof(reuseport))<0){
                        atnet_printf("%s[%d], AT_NET_TCP_CLIENT_CREATE, setsockopt SO_REUSEPORT failed",__FUNCTION__,netId);
                    }
                    sa.sin_family       = AF_INET;
                    sa.sin_port         = htons(cre->local_port);
                    inet_aton(ipstr,&sa.sin_addr);

                    if (bind(fd, (const struct sockaddr *)&sa, sizeof(sa)) < 0) {
                        atnet_printf("%s[%d], AT_NET_TCP_CLIENT_CREATE, bind error,local_port=%d",__FUNCTION__,netId ,cre->local_port);
                        close(fd);
                    }else{
                        con->socket_map[0].fd = fd;
                        con->net_socket.ip_addr = cre->ip_addr;
                        con->net_socket.ip_port = cre->ip_port;
                        con->net_socket.local_port = cre->local_port;
                        server.sin_family = AF_INET;
                        server.sin_port = htons(cre->ip_port);
                        server.sin_addr.s_addr= cre->ip_addr;
                        result = MIRC_SUCCESS;
                    }

                }
                else{
                    atnet_printf("%s[%d], AT_NET_TCP_CLIENT_CREATE, socket error",__FUNCTION__,netId);
                }
                free(cre);
                req_t = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
                if(req_t== NULL){
                    break;
                }

                req_t->primID = MI_NET_TRANSPORT_OPEN_REQ;
                open_req_t = (struct netSetSocketOpenReq_t *)malloc(sizeof(struct netSetSocketOpenReq_t));
                if(open_req_t == NULL){
                    free(req_t);
                    break;
                }
                open_req_t ->result = result;
                open_req_t->socketID = con->socket_map[0].SocketId;
                req_t->param = (void *)open_req_t;
                req_t->requestHandle = req.requestHandle;

                msg.msgID = MI_REQUEST_MSG;
                msg.pArgs =(void *)req_t;
                OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);

                break;
            case AT_NET_TCP_CLIENT_CONNECT:
                open_req = (struct netSetSocketOpenReq *)req.param;
                open_cnf = (struct netSetSocketOpenCnf *)malloc(sizeof(struct netSetSocketOpenCnf));
                if(open_cnf == NULL){
                    if(open_req) free(open_req);
                    break;
                }
                memset(open_cnf, 0, sizeof(struct netSetSocketOpenCnf));

                atnet_printf("%s[%d], connect fd[%d],sin_family[%d],sin_port[%d] ip[%s]",__FUNCTION__,netId,
                        con->socket_map[0].fd,server.sin_family,ntohs(server.sin_port),inet_ntoa(server.sin_addr.s_addr));

                //result = connect(con->socket_map[0].fd, (struct sockaddr *)&server, sizeof(server));
                //if(result < 0){
                result = atnet_connect(con->socket_map[0].fd,(struct sockaddr *)&server);
                if(result != MIRC_SUCCESS){
                    //result = MIRC_TCP_CONN_REJECT;
                    atnet_printf("%s, AT_NET_TCP_CLIENT_CONNECT, connect error",__FUNCTION__);
                }else{
                    result = MIRC_SUCCESS;
                    con->recv.buffer = (char *)malloc(RECV_MAX_BUFF);
                    if(con->recv.buffer){
                        con->recv.begin = con->recv.buffer;
                        con->recv.end = con->recv.buffer;
                        con->recv.rota_flag = 0;
                        len = sizeof(value);
                        getsockopt(con->socket_map[0].fd, IPPROTO_TCP, TCP_MSS_VALUE, &value, (socklen_t *)&len);
                        atnet_printf("%s[%d], AT_NET_TCP_CLIENT_CONNECT, getsockopt TCP_MSS_VALUE=%d",__FUNCTION__,netId, value);
                        open_cnf->remoteMSS = value;

                    }else{
                        result = MIRC_FAIL;
                    }

                }


                if(open_req) free(open_req);
                //callback to send the create socket result
                //callback API
                if(con->transportMode){
                    cre_cnf =  (struct netCreateCnf *)malloc(sizeof(struct netCreateCnf));
                    if(cre_cnf){
                        cre_cnf->socketID = con->socket_map[0].SocketId;
                        cre_cnf->result = result;
                        cre_cnf->mode = con->netType;
                        req.primID = MI_NET_SET_CREATE_CNF;
                        req.param = (void *)cre_cnf;
                        miResponse(svcID, svcID,req.primID, req.requestHandle, req.param);
                        free(cre_cnf);

                    }else{
                        result = MIRC_FAIL;
                    }

                    if(result != MIRC_SUCCESS){
                        con->transportMode = 0;
                        con->socket_map[0].SocketId = 0;
                        close(con->socket_map[0].fd);
                        con->socket_map[0].fd = 0;
                    }
                }else{
                    if(result != MIRC_SUCCESS){
                        close(con->socket_map[0].fd);
                        con->socket_map[0].fd = 0;
                    }
                    open_cnf->socketID = con->socket_map[0].SocketId;
                    open_cnf->result = result;
                    req.primID = MI_NET_SET_SOCKET_OPEN_CNF;
                    req.param = (void *)open_cnf;
                    miResponse(svcID, svcID,req.primID, req.requestHandle, req.param);
                }

                free(open_cnf);

                if(result == MIRC_SUCCESS){
                    con->type = 1;
                    atnet_tcp_client_recv(con->socket_map[0].fd, netId, req.requestHandle);
                }

                break;
		    case AT_NET_TCP_SERVER_INCOMING_CLIENT_RECV:
			    con->recv.buffer = (char *)malloc(RECV_MAX_BUFF);
                if(con->recv.buffer == NULL){
                    break;
                }
                con->recv.begin = con->recv.buffer;
                con->recv.end = con->recv.buffer;
                con->recv.rota_flag = 0;
				atnet_tcp_client_recv(con->socket_map[0].fd, netId, req.requestHandle);
				break;
            case AT_NET_UDP_CREATE:
                result = MIRC_FAIL;
                cre = (Atnet_Socket *)req.param;
                fd = socket(AF_INET, SOCK_DGRAM, 0);
                if(fd >0 ){

                    memset(ipstr,0,128);
                    cmuxGetPdpIpStr(con->channel,ipstr);
                    atnet_printf("%s[%d], cmuxGetPdpIpStr %s",__FUNCTION__, netId, ipstr);
                    if(setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &reuseport,  sizeof(reuseport))<0){
                        atnet_printf("%s[%d], AT_NET_UDP_CREATE, setsockopt SO_REUSEPORT failed",__FUNCTION__,netId);
                    }

                    sa.sin_family       = AF_INET;
                    sa.sin_port         = htons(cre->local_port);
                    inet_aton(ipstr,&sa.sin_addr);
                    if (bind(fd, (const struct sockaddr *)&sa, sizeof(sa)) < 0) {
                        close(fd);
                        atnet_printf("%s[%d], AT_NET_UDP_CREATE, bind error",__FUNCTION__,netId);
                    }else{
                        con->socket_map[0].fd = fd;
                        con->net_socket.ip_addr = cre->ip_addr;
                        con->net_socket.ip_port = cre->ip_port;
                        con->net_socket.local_port = cre->local_port;
                        server.sin_family = AF_INET;
                        server.sin_port = htons(cre->ip_port);
                        server.sin_addr.s_addr= cre->ip_addr;
                        result = MIRC_SUCCESS;
                    }

                }
                else{
                    atnet_printf("%s[%d], AT_NET_UDP_CREATE, socket error",__FUNCTION__,netId);
                }

                free(cre);

                req_t = (struct miRequestArgs *)malloc(sizeof(struct miRequestArgs));
                if(req_t== NULL){
                    close(fd);
                    con->socket_map[0].fd = 0;
                    break;

                }

                req_t->primID = MI_NET_TRANSPORT_OPEN_REQ;
                open_req_t = (struct netSetSocketOpenReq_t *)malloc(sizeof(struct netSetSocketOpenReq_t));
                if(open_req_t == NULL){
                    close(fd);
                    con->socket_map[0].fd = 0;
                    break;
                }
                open_req_t ->result = result;
                open_req_t->socketID = con->socket_map[0].SocketId;
                req_t->param = (void *)open_req_t;
                req_t->requestHandle = req.requestHandle;

                msg.msgID = MI_REQUEST_MSG;
                msg.pArgs =(void *)req_t;
                OSAMsgQSend(AtNetCiToProxyMsgQ[netId], ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,(UINT8 *)&msg, OSA_SUSPEND);
                break;
            case AT_NET_UDP_CONNECT:
                open_req = (struct netSetSocketOpenReq *)req.param;

                result = connect(con->socket_map[0].fd, (struct sockaddr *)&server, sizeof(server));
                if(result<0){
                    result = MIRC_FAIL;
                    atnet_printf("%s[%d], AT_NET_UDP_CONNECT, connect error",__FUNCTION__,netId);
                }else{
                    result = MIRC_SUCCESS;
                }
                if(open_req) free(open_req);

                if(con->transportMode==0){
                    open_cnf = (struct netSetSocketOpenCnf *)malloc(sizeof(struct netSetSocketOpenCnf));
                    if(open_cnf == NULL){
                        close(con->socket_map[0].fd);
                        con->socket_map[0].fd = 0;
                        break;
                    }
                    memset(open_cnf, 0, sizeof(struct netSetSocketOpenCnf));
                    open_cnf->socketID = con->socket_map[0].SocketId;
                    open_cnf->result = result;
                    req.primID = MI_NET_SET_SOCKET_OPEN_CNF;
                    req.param = (void *)open_cnf;

                    if(result != MIRC_SUCCESS){
                        close(con->socket_map[0].fd);
                        con->socket_map[0].fd = 0;
                    }else{
						con->recv.buffer = (char *)malloc(RECV_MAX_BUFF);
						if(con->recv.buffer) {
                          	con->recv.begin = con->recv.buffer;
						    con->recv.end = con->recv.buffer;
						    con->recv.rota_flag = 0;  
						}else{
                            close(con->socket_map[0].fd);
                            con->socket_map[0].fd = 0;
                            result = MIRC_FAIL;
						}

					}

                    //callback to send the create socket result
                    //callback API
                    miResponse(svcID, svcID,req.primID, req.requestHandle, req.param);
                    free(open_cnf);
                }else{
                    cre_cnf =  (struct netCreateCnf *)malloc(sizeof(struct netCreateCnf));
                    if(cre_cnf== NULL){
                        result = MIRC_FAIL;
                    }else{
                        cre_cnf->socketID = con->socket_map[0].SocketId;
                        cre_cnf->result = result;
                        cre_cnf->mode = con->netType;
                        req.primID = MI_NET_SET_CREATE_CNF;
                        req.param = (void *)cre_cnf;
                        miResponse(svcID, svcID,req.primID, req.requestHandle, req.param);
                        free(cre_cnf);

                    }

                    if(result != MIRC_SUCCESS){
                        con->transportMode = 0;
                        con->socket_map[0].SocketId = 0;
                        close(con->socket_map[0].fd);
                        con->socket_map[0].fd = 0;
                    }
                }

                if(result == MIRC_SUCCESS){
                    con->type = 1;
                    atnet_udp_recv(con->socket_map[0].fd, netId, req.requestHandle);
                }
                break;
            case AT_NET_ASR_OPEN:
                if(con->netType == 2 || con->netType == 3){
                    atnet_udp_recv(con->socket_map[0].fd, netId, req.requestHandle);
                }else if(con->netType == 1){
                    atnet_tcp_server_recv(con->socket_map[0].fd, netId, req.requestHandle);
                }else if(con->netType == 0){
                    atnet_tcp_client_recv(con->socket_map[0].fd, netId, req.requestHandle);
                }
                break;
            default:
                atnet_printf("%s[%d], unknow primID from proxy: %d",__FUNCTION__, netId, req.primID);
                break;

        }


    }
}


void atnet0_tcp_udp_worker_thread(void * argv)
{
    atnet_process_tcp_udp(AT_NET0);
}

void atnet1_tcp_udp_worker_thread(void * argv)
{
    atnet_process_tcp_udp(AT_NET1);
}


void atnet2_tcp_udp_worker_thread(void * argv)
{
    atnet_process_tcp_udp(AT_NET2);
}



void atnet3_tcp_udp_worker_thread(void * argv)
{
    atnet_process_tcp_udp(AT_NET3);
}




void atnet4_tcp_udp_worker_thread(void * argv)
{
    atnet_process_tcp_udp(AT_NET4);
}




void atnet5_tcp_udp_worker_thread(void * argv)
{
     atnet_process_tcp_udp(AT_NET5);
}



/*
int atnet_ip_filter(int netId)
{
	int i;
	int index=0;
	int mask_ip;
	ip_filter_param_list *ip_filter_list = NULL;
	ip_filter_param_list *ip_filter_temp = NULL;
	ip_filter_param_list *ip_filter_next = NULL;
	ip_filter_list = (ip_filter_param_list *)malloc( sizeof(ip_filter_param_list));
	memset(ip_filter_list, 0 , sizeof(ip_filter_param_list));

	ip_filter_temp = ip_filter_list;
	for(i=0; i<5; i++){
		if(ipFilter[i].action == 1){
			index ++;
			if(index == 1){
				ip_filter_temp->des_ip = 0;
				ip_filter_temp->des_port_end = 0;
				ip_filter_temp->des_port_start = 0;
				ip_filter_temp->net_mask2 = 0;
				ip_filter_temp->enable_disable = 1;
				ip_filter_temp->protocol = 3;
				//inet_aton(service_req->ipPortStr,&(new_socket->ip_addr.addr));
				inet_aton(ipFilter[i].ipStr, &(ip_filter_temp->src_ip));
				inet_aton(ipFilter[i].netMask, &mask_ip);
				ip_filter_temp->net_mask1 = (mask_ip & 0xFF000000)>>24;
				atnet_printf("%s[%d], mask_ip:%08X, net_mask1:%02X",__FUNCTION__,netId,mask_ip,ip_filter_temp->net_mask1);
				ip_filter_temp->src_port_start = 0;
				ip_filter_temp->src_port_end = 0;

			}else{
				ip_filter_next = (ip_filter_param_list *)malloc( sizeof(ip_filter_param_list));
				memset(ip_filter_next, 0 , sizeof(ip_filter_param_list));
				ip_filter_temp->next = ip_filter_next;
				ip_filter_temp = ip_filter_next;

				ip_filter_temp->des_ip = 0;
				ip_filter_temp->des_port_end = 0;
				ip_filter_temp->des_port_start = 0;
				ip_filter_temp->net_mask2 = 0;
				ip_filter_temp->enable_disable = 1;
				ip_filter_temp->protocol = 3;
				//inet_aton(service_req->ipPortStr,&(new_socket->ip_addr.addr));
				inet_aton(ipFilter[i].ipStr, &(ip_filter_temp->src_ip));
				inet_aton(ipFilter[i].netMask, &mask_ip);
				ip_filter_temp->net_mask1 = (mask_ip & 0xFF000000)>>24;
				atnet_printf("%s[%d], mask_ip:%08X, net_mask1:%02X",__FUNCTION__,netId,mask_ip,ip_filter_temp->net_mask1);
				ip_filter_temp->src_port_start = 0;
				ip_filter_temp->src_port_end = 0;
			}
		}
	}
	// API
	atnet_ip_filter_post_set(index, ip_filter_list);

	ip_filter_temp = ip_filter_list;
	for(i=0; i<5; i++){
		if(ip_filter_temp->next != NULL){
			ip_filter_next= ip_filter_temp->next;
			free(ip_filter_temp);
			ip_filter_temp = ip_filter_next;
		}else{
			free(ip_filter_temp);
			ip_filter_temp = NULL;
			break;
		}
	}
	return 0;
}
*/

void atnet_process_proxy_worker(int netId)
{
    AtNet_Context *con;
	AtNet_Context *conTemp;
    Atnet_Socket *new_socket;
    struct miRequestArgs *req;
    struct netSetServiceReq  *service_req;
    struct netCreateReq *create_req;
    struct netCreateCnf *create_cnf;
    struct netCreateCnf *create_cnf2;
    struct netSocketAcceptReq *accept_req;
    struct netSocketAcceptCnf *accept_cnf;
    struct netSetSocketOpenReq *open_req;
    struct netSetSocketOpenReq_t *open_req_t;
    struct netSetSocketOpenCnf *open_cnf;
    struct netGetSocketOpenCnf *query_cnf;
    struct netSocketReadReq * read_req;
    struct netSocketReadCnf *read_cnf;
    struct netSocketWriteReq *write_req;
    struct netSocketWriteCnf *write_cnf;
    struct netSocketWriteDataReq *writedata_req;
    struct netSocketCloseReq * close_req;
    struct netSocketCloseCnf * close_cnf;
    struct netSocketAckReq *ack_req;
    struct netSocketAckCnf *ack_cnf;
    struct netGetServiceCnf *query_ser_cnf;
    struct urcCloseInd * close_ind;
	struct urcIncomingClientInd * incoming_client;
    struct netSetIpfilterReq *ipfilter_req;
    struct setGenericCnf *gen_cnf;
    struct netGetIpfilterCnf *ipfilter_cnf;
    struct asrOpenReq *asropen;
    struct asrOpenCnf *asropencnf;
    struct asrStateReq *asrstate;
    struct asrSocketSendReq *asrsend;
    struct asrSocketQuerySendDataCnf *asrquerysenddata;
    struct asrSocketQueryReadDataCnf *asrqueryreaddata;
    struct asrSendHexReq *sendHex;
    struct asrSwTmdReq *swtmd;
    struct asrSwTmdCnf *swtmdcnf;
    struct asrSetCfg *cfg;
    struct miSSLCfgReq *sslcfgreq;
    //Atnet_SSL_Ctx *sslctx;
    int i,j;
    int fd=0;
    int len;
    INT32 result;
    LinkStatus_Context * cm_link_status = NULL;
    Ipv4Info * ipv4 = NULL;
    unsigned int ipaddr;
    char * string=NULL;
    struct hostent* host_entry;
    struct sockaddr_in name;
    struct miApiMsg apiMsg = { 0 };
    union pdpSetParamReq *pdpReq = NULL;
    char *buf=NULL;
    int value =0;
    int rest_value = 0;
    UINT32 svcID = netId + SVCID_NET0;
	int reuseport = 1;
	UINT8 status;
	struct cmuxChannelController * cmux_channelCtrl = get_cmux_channelCtrl();
	char ipstr[128];
    struct netGetSocketOpenCnf *temp=NULL;
    struct pdpIpInfo * pdpinfo=NULL;
    //AtNet_Context *conTemp=NULL;

    con = atnet_get_context_by_netId(netId);
    while(1)
    {
        memset(&apiMsg,0,sizeof(struct miApiMsg));
        OSAMsgQRecv(AtNetCiToProxyMsgQ[netId], (UINT8 *)&apiMsg, ATNETCI_TO_PROXY_MESSAGE_Q_SIZE, OSA_SUSPEND);
        if(apiMsg.msgID == MI_REQUEST_MSG){
            req = (struct miRequestArgs *)apiMsg.pArgs;
                switch(req->primID){
                case MI_NET_SET_CFGT_REQ:
                    pdpReq = (union pdpSetParamReq *)req->param;
                    gen_cnf = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                    if(gen_cnf== NULL){
                        free(req);
                        free(pdpReq);
                        break;
                    }
                    gen_cnf->result = MIRC_SUCCESS;

                    miResponse(svcID, svcID,MI_NET_SET_CFGT_CNF, req->requestHandle, gen_cnf);
                    free(req);
                    free(pdpReq);
                    free(gen_cnf);
                    break;

                case MI_NET_SET_CFGP_REQ:
                    pdpReq = (union pdpSetParamReq *)req->param;
                    gen_cnf = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                    if(gen_cnf== NULL){
                        free(req);
                        free(pdpReq);
                        break;
                    }
                    gen_cnf->result = MIRC_SUCCESS;

                    miResponse(svcID, svcID,MI_NET_SET_CFGP_CNF, req->requestHandle, gen_cnf);
                    free(req);
                    free(pdpReq);
                    free(gen_cnf);
                    break;

                case MI_NET_SET_IP_FILTER_REQ:
                    ipfilter_req = (struct netSetIpfilterReq *)req->param;
                    if(ipfilter_req->id > 5){
                        //need response error!
                        result = MIRC_PARAM_INVALID;
                        atnet_printf("%s[%d], ipfilter_req id[%d] is error!",__FUNCTION__,netId, ipfilter_req->id);
                    }else{
                        result = MIRC_SUCCESS;
                        if(ipfilter_req->action == 1){
                            ipFilter[ipfilter_req->id].id = ipfilter_req->id;
                            ipFilter[ipfilter_req->id].action = ipfilter_req->action;
                            memset(ipFilter[ipfilter_req->id].ipStr,0,MAX_STRING_LEN);
                            memcpy(ipFilter[ipfilter_req->id].ipStr, ipfilter_req->ipStr, strlen(ipfilter_req->ipStr));
                            memset(ipFilter[ipfilter_req->id].netMask,0,MAX_STRING_LEN);
                            memcpy(ipFilter[ipfilter_req->id].netMask, ipfilter_req->netMask, strlen(ipfilter_req->netMask));
                            //atnet_ip_filter(AT_NET0);
                        }else if(ipfilter_req->action == 0){
                            ipFilter[ipfilter_req->id].id = 0;
                            ipFilter[ipfilter_req->id].action = 0;
                            memset(ipFilter[ipfilter_req->id].ipStr,0,MAX_STRING_LEN);
                            memset(ipFilter[ipfilter_req->id].netMask,0,MAX_STRING_LEN);
                            //atnet_ip_filter(AT_NET0);
                        }else if(ipfilter_req->action == 2){
                            memset(ipFilter, 0, 5*sizeof(struct netSetIpfilterReq));
                            //atnet_ip_filter(AT_NET0);
                        }else{
                            result = MIRC_PARAM_INVALID;
                            atnet_printf("%s[%d], ipfilter_req actionp[%d] is error!",__FUNCTION__, netId ,ipfilter_req->action);
                        }

                    }
                    if(ipfilter_req)
                        free(ipfilter_req);
                    gen_cnf = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf ));
                    if(gen_cnf == NULL){
                        free(req);
                        break;
                    }
                    gen_cnf->result = result;
                    req->primID = MI_NET_SET_IP_FILTER_CNF;
                    req->param = (void *)gen_cnf;
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(req);
                    free(gen_cnf);
                    //callback API
                    break;
                case MI_NET_GET_IP_FILTER_REQ:
                    ipfilter_cnf = (struct netGetIpfilterCnf *)malloc(sizeof(struct netGetIpfilterCnf ));
                    if(ipfilter_cnf== NULL){
                        free(req);
                        break;
                    }
                    memset(ipfilter_cnf, 0, sizeof(struct netGetIpfilterCnf ));
                    for(i=0; i<MAX_IPFILER_NUM; i++){
                        if(ipFilter[i].action == 1){
                            ipfilter_cnf->ipFilter[i].id = ipFilter[i].id;
                            memcpy(ipfilter_cnf->ipFilter[i].ipStr ,ipFilter[i].ipStr,MAX_STRING_LEN);
                            memcpy(ipfilter_cnf->ipFilter[i].netMask ,ipFilter[i].netMask,MAX_STRING_LEN);
                        }
                    }
                    ipfilter_cnf->result = MIRC_SUCCESS;
                    req->primID = MI_NET_GET_IP_FILTER_CNF;
                    req->param = (void *)ipfilter_cnf;
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(req);
                    free(ipfilter_cnf);
                    //callback API
                    break;
                case MI_NET_SET_SERVICE_PARAM_REQ:
                    service_req = (struct netSetServiceReq *)req->param;
                    if(con->socket_map[0].fd>0){
                        atnet_printf("%s[%d], service_req, previous socket not closed!",__FUNCTION__,netId);
                        if(service_req)
                            free(service_req);
                        gen_cnf = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                        if(gen_cnf == NULL) {
                            free(req);
                            break;
                        }
                        gen_cnf->result = MIRC_SOCKET_EXIST;
                        req->primID = MI_NET_SET_SERVICE_PARAM_CNF;
                        req->param = (void *)gen_cnf;
                        miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                        free(req);
                        free(gen_cnf);
                        //callback API
                        //need response error!because previous socket not closed
                    }else{
                        string =strstr(service_req->ipPortStr,":");
                        *string=0;

                        host_entry = gethostbyname(service_req->ipPortStr);
                        if (host_entry == NULL) {
                            atnet_printf("%s[%d], service_req, DNS gethostbyname failed: %s\n",__FUNCTION__,netId,service_req->ipPortStr);
                            //callback API
                            //need response error!because DNS failed
                            if(service_req)
                                free(service_req);
                            gen_cnf = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                            if(gen_cnf == NULL){
                                free(req);
                                break;
                            }
                            gen_cnf->result = MIRC_DOMAIN_NOT_EXIST;
                            req->primID = MI_NET_SET_SERVICE_PARAM_CNF;
                            req->param = (void *)gen_cnf;
                            miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                            free(req);
                            free(gen_cnf);
                        }else{
                            atnet_printf("%s[%d],DNS gethostbyname,Get %s ip %d.%d.%d.%d\n", __FUNCTION__,netId,service_req->ipPortStr, host_entry->h_addr_list[0][0] & 0xff,
                                host_entry->h_addr_list[0][1] & 0xff, host_entry->h_addr_list[0][2] & 0xff, host_entry->h_addr_list[0][3] & 0xff);

                            memcpy(con->service_addr,service_req->ipPortStr,strlen(service_req->ipPortStr));
                            con->service_port = atoi(string+1);

                            new_socket = (Atnet_Socket *)malloc(sizeof(Atnet_Socket));
                            if(new_socket == NULL) {
                                if(service_req)
                                    free(service_req);
                                free(req);
                                break;
                            }
                            new_socket->local_port = 0;
                            new_socket->ip_port = atoi(string+1);
                            new_socket->ip_addr = * (UINT32 *) host_entry->h_addr_list[0];
                            //inet_aton(service_req->ipPortStr,&(new_socket->ip_addr.addr));
                            con->socket_map[0].SocketId = service_req->socketID;
                            req->param = (void *)new_socket;
                            if(service_req->netType==0){
                                req->primID = AT_NET_TCP_CLIENT_SERVICE;
                            }else if(service_req->netType==1){
                                req->primID = AT_NET_TCP_SERVER_SERVICE;
                            }else if(service_req->netType==2){
                                req->primID = AT_NET_UDP_SERVICE;
                            }
							con->channel = service_req->channel;
							con->netType = service_req->netType;
							con->viewMode = service_req->viewMode;
							con->transportMode = 0;
                            if(service_req)
                                free(service_req);
                            //atnet_printf("OSAMsgQSend %d AtNetProxyToTCPUdpMsgQ!primID[%d]", __LINE__, req->primID);
                            OSAMsgQSend(AtNetProxyToTCPUdpMsgQ[netId], ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,(UINT8 *)req, OSA_SUSPEND);
                            free(req);
                        }

                    }
                    break;
                case MI_NET_GET_SERVICE_PARAM_REQ:
                    req->primID = MI_NET_GET_SERVICE_PARAM_CNF;
                    query_ser_cnf = (struct netGetServiceCnf *)malloc(sizeof(struct netGetServiceCnf));
                    if(query_ser_cnf== NULL){
                        free(req);
                        break;
                    }

                    memset(query_ser_cnf,0,sizeof(struct netGetServiceCnf));
                    query_ser_cnf->result = MIRC_SUCCESS;
					query_ser_cnf->num=0;
					j=0;

					for(i=0;i<6;i++)
					{
						conTemp = atnet_get_context_by_netId(i);
						if(strlen(conTemp->service_addr)){
							query_ser_cnf->serviceinfo[j].channel = conTemp->channel;
							query_ser_cnf->serviceinfo[j].socketID = conTemp->socket_map[0].SocketId;
							query_ser_cnf->serviceinfo[j].viewMode = conTemp->viewMode;
							query_ser_cnf->serviceinfo[j].netType = conTemp->netType;
							sprintf(query_ser_cnf->serviceinfo[j].ipPortStr,"\"%s:%d\"",conTemp->service_addr,conTemp->service_port);
							j++;
							query_ser_cnf->num++;
						}
					}

                    req->param = (void *)query_ser_cnf;
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(req);
                    free(query_ser_cnf);
                    //callback API
                    break;
                case MI_NET_SET_CREATE_REQ:
                    create_req = (struct netCreateReq *)req->param;
                    if(con->socket_map[0].fd>0){
                        atnet_printf(" %s[%d], create_req,  previous socket not closed!\n",__FUNCTION__,netId);
                        create_cnf = (struct netCreateCnf *)malloc(sizeof(struct netCreateCnf));
                        if(create_cnf == NULL){
                            if(create_req)
                                free(create_req);
                            free(req);
                            break;

                        }
                        create_cnf->result = MIRC_SOCKET_EXIST;
                        create_cnf->mode = create_req->mode;
                        if(create_req)
                            free(create_req);
                        req->primID = MI_NET_SET_CREATE_CNF;
                        req->param = (void *)create_cnf;
                        miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                        free(req);
                        free(create_cnf);
                        //need response error!because previous socket not closed
                        //callback API
                    }else{

                        host_entry = gethostbyname(create_req->dstIP);
                        if (host_entry == NULL) {
                            atnet_printf("%s[%d], create_req, DNS gethostbyname failed: %s\n",__FUNCTION__,netId,create_req->dstIP);
                            //callback API
                            //need response error!because DNS failed

                            create_cnf = (struct netCreateCnf *)malloc(sizeof(struct netCreateCnf));
                            if(create_cnf == NULL){
                                if(create_req)
                                    free(create_req);
                                free(req);
                                break;

                            }
                            create_cnf->result = MIRC_DOMAIN_NOT_EXIST;
                            create_cnf->mode = create_req->mode;
                            if(create_req)
                                free(create_req);
                            req->primID = MI_NET_SET_CREATE_CNF;
                            req->param = (void *)create_cnf;
                            miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                            free(req);
                            free(create_cnf);
                        }else{
                            atnet_printf("%s[%d],DNS gethostbyname,Get %s ip %d.%d.%d.%d\n", __FUNCTION__,netId,create_req->dstIP, host_entry->h_addr_list[0][0] & 0xff,
                                host_entry->h_addr_list[0][1] & 0xff, host_entry->h_addr_list[0][2] & 0xff, host_entry->h_addr_list[0][3] & 0xff);

                            new_socket = (Atnet_Socket *)malloc(sizeof(Atnet_Socket));
                            if(new_socket == NULL){
                                if(create_req)
                                    free(create_req);
                                free(req);
                                break;
                            }
                            atnet_printf("%s[%d],local_port=%d",__FUNCTION__,netId,create_req->srcPort);
                            new_socket->local_port = create_req->srcPort;
                            new_socket->ip_port = create_req->dstPort;
                            new_socket->ip_addr = * (UINT32 *) host_entry->h_addr_list[0];
                            //inet_aton(create_req->dstIP,&(new_socket->ip_addr ));
                            con->socket_map[0].SocketId = create_req->socketID;
                            req->param = (void *)new_socket;
                            if(create_req->mode==0){
                                req->primID = AT_NET_TCP_CLIENT_CREATE;
                                clear_udp_data_backup_buf(netId);
                            }else if(create_req->mode==1){
                                req->primID = AT_NET_TCP_SERVER_CREATE;
                            }else if(create_req->mode==2){
                                req->primID = AT_NET_UDP_CREATE;
                                clear_udp_data_backup_buf(netId);
                            }
							con->channel = create_req->channel;
                            con->netType = create_req->mode;
                            con->transportMode = 1;
                            con->viewMode = 0;
                            memcpy(con->service_addr,create_req->dstIP,strlen(create_req->dstIP));
                            con->service_port = create_req->dstPort;


                            if(create_req)
                                free(create_req);
                            OSAMsgQSend(AtNetProxyToTCPUdpMsgQ[netId], ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,(UINT8 *)req, OSA_SUSPEND);
                            free(req);
                        }

                    }
                    break;
                case MI_NET_TRANSPORT_OPEN_REQ:
                    open_req_t = (struct netSetSocketOpenReq_t *)req->param;
                    if(open_req_t->result == MIRC_SUCCESS){
                        if(con->netType == 0){
                            req->primID = AT_NET_TCP_CLIENT_CONNECT;
                        }else if(con->netType == 1){
                            req->primID=AT_NET_TCP_SERVER_LISTEN;
                        }else if(con->netType == 2){
                            req->primID = AT_NET_UDP_CONNECT;
                        }
                        open_req = (struct netSetSocketOpenReq *)malloc(sizeof(struct netSetSocketOpenReq));
                        if(open_req== NULL){
                            free(open_req_t);
                            free(req);
                            break;
                        }
                        open_req->socketID = open_req_t->socketID;
                        req->param = open_req;
                        OSAMsgQSend(AtNetProxyToTCPUdpMsgQ[netId], ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,(UINT8 *)req, OSA_SUSPEND);
                        free(open_req_t);
                        free(req);


                    }else{
                        atnet_printf("%s[%d], MI_NET_TRANSPORT_OPEN_REQ, net create error",__FUNCTION__,netId);
                        create_cnf2 = (struct netCreateCnf *)malloc(sizeof(struct netCreateCnf));
                        if(create_cnf2== NULL){
                            free(open_req_t);
                            free(req);
                            break;
                        }
                        create_cnf2->result = open_req_t->result;
                        create_cnf2->mode = con->netType;
                        miResponse(svcID, svcID,MI_NET_SET_CREATE_CNF, req->requestHandle, create_cnf2);
                        con->netType = 0;
                        con->transportMode = 0;
                        con->socket_map[0].SocketId = 0;
                        free(open_req_t);
                        free(create_cnf2);
                        free(req);
                    }
                    break;
                case MI_NET_SET_SOCKET_ACCEPT_REQ:
                    accept_req= (struct netSocketAcceptReq *)req->param;
                    if(con->netType ==4
						&& accept_req->socketID==con->socket_map[0].SocketId
						&& con->socket_map[0].fd>0){
						result = MIRC_SUCCESS;
						if(accept_req->action==0){
							atnet_printf("%s[%d],accept!SocketID[%d],current netType[%d]\n",__FUNCTION__,netId, accept_req->socketID, con->netType);
							con->netType = 0;
							con->type = 1;
							con->transportMode = accept_req->transMode;
						}else{
							atnet_printf("%s[%d], don't accept!SocketID[%d],current netType[%d]\n",__FUNCTION__,netId, accept_req->socketID, con->netType);
							con->netType = 0;
							con->channel = 0;
							con->type = 0;
							con->socket_map[0].SocketId = 0;
							close(con->socket_map[0].fd);
							con->socket_map[0].fd = 0;
						}

                    }else{
                    	result = MIRC_PARAM_INVALID;
                        //need response error!because previous socket not TCP server!
                        //callback API
                        atnet_printf("%s[%d], accept_req error!SocketID[%d],current netType[%d] socketID1[%d] socketID2[%d]\n",__FUNCTION__,netId, accept_req->socketID, con->netType,
                        con->socket_map[1].SocketId, con->socket_map[2].SocketId);

                    }
					accept_cnf= (struct netSocketAcceptCnf *)malloc(sizeof(struct netSocketAcceptCnf));
                    if(accept_cnf == NULL){
                        if(accept_req)
                            free(accept_req);
                        free(req);
                        break;

                    }
                    accept_cnf->result = result;
                    accept_cnf->transMode = accept_req->transMode;
                    accept_cnf->action = accept_req->action;
					accept_cnf->socketId = con->socket_map[0].SocketId;
                    if(accept_req)
                        free(accept_req);
                    req->primID = MI_NET_SET_SOCKET_ACCEPT_CNF;
                    req->param = (void *)accept_cnf;
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(accept_cnf);
					if(result==MIRC_SUCCESS && con->socket_map[0].fd>0){
						req->primID=AT_NET_TCP_SERVER_INCOMING_CLIENT_RECV;
						req->param= NULL;
						OSAMsgQSend(AtNetProxyToTCPUdpMsgQ[netId], ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,(UINT8 *)req, OSA_SUSPEND);
					}
					free(req);
                    break;
                case MI_NET_SET_SOCKET_OPEN_REQ:
                    open_req = (struct netSetSocketOpenReq *)req->param;

					cmuxGetPdpStatus(con->channel,&status);

                    if(con->socket_map[0].SocketId == open_req->socketID
						&& status == PDP_STATUS_ACTIVED){
						memset(ipstr,0,128);
						cmuxGetPdpIpStr(con->channel,ipstr);
						atnet_printf("%s[%d], cmuxGetPdpIpStr %s",__FUNCTION__, netId, ipstr);
                        result = MIRC_SUCCESS;
                        if(con->netType == 0){
                            if(con->type == 0){
                                req->primID = AT_NET_TCP_CLIENT_CONNECT;
                                fd = socket(AF_INET, SOCK_STREAM, 0);
                                if(fd >0 ){

                                    if(setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &reuseport,  sizeof(reuseport))<0){
    									atnet_printf("%s[%d], set open_req error! setsockopt SO_REUSEPORT failed",__FUNCTION__,netId);
    								}
                                    name.sin_family     = AF_INET;
                                    name.sin_port       = 0;
                                    inet_aton(ipstr,&name.sin_addr);
                                    //name.sin_addr.s_addr    = 0;
                                    if (bind(fd, (const struct sockaddr *)&name, sizeof(name)) < 0) {
                                        close(fd);
                                        atnet_printf("%s[%d], set open_req error! bind error",__FUNCTION__, netId);
                                        result = MIRC_FAIL;
                                    }else{
                                        con->socket_map[0].fd = fd;
                                        result = MIRC_SUCCESS;
                                    }

                                }else{
                                    atnet_printf("%s[%d], set open_req error!socket failed!!",__FUNCTION__,netId);
                                    result = MIRC_FAIL;
                                }
                            }else{
                                result = MIRC_SOCKET_EXIST;
                                atnet_printf("%s[%d], set open_req error!TCP Client already connected!!",__FUNCTION__,netId);
                            }
                        }else if(con->netType == 1){
							fd = socket(AF_INET, SOCK_STREAM, 0);
							if(fd >0 ){
								if(setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &reuseport,  sizeof(reuseport))<0){
									atnet_printf("%s[%d], set open_req error! setsockopt SO_REUSEPORT failed",__FUNCTION__,netId);
								}
								name.sin_family		= AF_INET;
								name.sin_port 		= htons(con->net_socket.ip_port);
								inet_aton(ipstr,&name.sin_addr);
								//name.sin_addr.s_addr	= 0;
								if (bind(fd, (const struct sockaddr *)&name, sizeof(name)) < 0) {
									close(fd);
									atnet_printf("%s[%d], set open_req error! bind error",__FUNCTION__, netId);
									result = MIRC_FAIL;
								}else{

									if(con->listen == 0){
										con->socket_map[0].fd = fd;
										result = MIRC_SUCCESS;
		                                req->primID=AT_NET_TCP_SERVER_LISTEN;
		                            }else{
		                            	close(fd);
		                                result = MIRC_SOCKET_EXIST;
		                                atnet_printf("%s[%d], set open_req error!TCP Server already listened!!",__FUNCTION__,netId);
		                            }
								}

							}else{
								result = MIRC_FAIL;
								atnet_printf("%s[%d], set open_req error! socket error",__FUNCTION__, netId);
							}



                        }else if(con->netType == 2){
                            if(con->type == 0){
                                req->primID = AT_NET_UDP_CONNECT;
                                fd = socket(AF_INET, SOCK_DGRAM, 0);
                                if(fd >0 ){
                                    if(setsockopt(fd, SOL_SOCKET, SO_REUSEADDR, &reuseport,  sizeof(reuseport))<0){
    									atnet_printf("%s[%d], set open_req error! setsockopt SO_REUSEPORT failed",__FUNCTION__,netId);
    								}

                                    name.sin_family     = AF_INET;
                                    name.sin_port       = 0;
                                    inet_aton(ipstr,&name.sin_addr);
                                    //name.sin_addr.s_addr    = 0;
                                    if (bind(fd, (const struct sockaddr *)&name, sizeof(name)) < 0) {
                                        close(fd);
                                        atnet_printf("%s[%d], set open_req error! bind error",__FUNCTION__, netId);
                                        result = MIRC_FAIL;
                                    }else{
                                        result = MIRC_SUCCESS;
                                        con->socket_map[0].fd = fd;
                                    }
                                }
								else{
                                    atnet_printf("%s[%d], set open_req error!socket failed!!",__FUNCTION__,netId);
                                    result = MIRC_FAIL;
                                }


                            }else{
                                result = MIRC_SOCKET_EXIST;
                                atnet_printf("%s[%d], set open_req error!UDP already connected!!",__FUNCTION__,netId);
                            }
                        }else{
                            atnet_printf("%s[%d], set open_req param invalid!netType[%d] !!",__FUNCTION__,netId,con->netType);
                            result = MIRC_PARAM_INVALID;
                        }

                        if(con->transportMode==1){
                            atnet_printf("%s[%d], MI_NET_SET_SOCKET_OPEN_REQ, transportMode error",__FUNCTION__,netId);
                            result = MIRC_FAIL;
                        }

                    }else{
                        atnet_printf("%s[%d], set open_req param invalid!socketID[%d] !!",__FUNCTION__,netId,open_req->socketID);
						if(status!=PDP_STATUS_ACTIVED){
							result = MIRC_PDP_UNACTIVE;
						}else{
							result = MIRC_SOCKET_NOT_EXIST;
						}
                    }
                    if(result == MIRC_SUCCESS){
                        //atnet_printf("OSAMsgQSend AtNetProxyToTCPUdpMsgQ!primID[%d]", req->primID);
                        OSAMsgQSend(AtNetProxyToTCPUdpMsgQ[netId], ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,(UINT8 *)req, OSA_SUSPEND);
                        free(req);
                    }else{
                        //need response error!becausenot found the Socket ID
                        //callback API
                        atnet_printf("%s[%d], open_req error!SocketID[%d],current netType[%d] socketID0[%d] socketID1[%d]\n",__FUNCTION__, netId,open_req->socketID, con->netType,
                        con->socket_map[0].SocketId, con->socket_map[1].SocketId);
                        open_cnf= (struct netSetSocketOpenCnf *)malloc(sizeof(struct netSetSocketOpenCnf));
                        if(open_cnf == NULL){
                            if(open_req) free(open_req);
                            free(req);
                            break;
                        }
                        open_cnf->result = result;
                        open_cnf->socketID = open_req->socketID;
                        if(open_req)
                            free(open_req);
                        req->primID = MI_NET_SET_SOCKET_OPEN_CNF;
                        req->param = (void *)open_cnf;
                        miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                        free(req);
                        free(open_cnf);
                    }

                    break;
                case MI_NET_GET_SOCKET_OPEN_REQ:
                    result = MIRC_SUCCESS;
                    //open_req = (struct netSetSocketOpenReq *)req->param;
                    query_cnf = (struct netGetSocketOpenCnf *)malloc(sizeof(struct netGetSocketOpenCnf));
                    if(query_cnf == NULL){
                        free(req);
                        break;
                    }
					j=0;
					for(i=0;i<MAX_CMUX_CHANNEL;i++)
					{
						cmuxGetPdpStatus(i,&status);
						if(PDP_STATUS_ACTIVED == status){
							j++;
						}
					}
					if(j==0){
						result = MIRC_PDP_UNACTIVE;
					}else{
						query_cnf->num=0;
						j=0;
						for(i=0;i<MAX_CMUX_CHANNEL;i++)
						{
							conTemp=atnet_get_context_by_netId(i);
							if(conTemp->socket_map[0].fd>0){
								pdpinfo = (struct pdpIpInfo *)cmuxGetPdpInfo(conTemp->channel);
								sprintf(query_cnf->info[j].localIPStr,"%s",pdpinfo->ipStr);
								sprintf(query_cnf->info[j].gateStr,"%s",pdpinfo->gateStr);
								sprintf(query_cnf->info[j].DNS1,"%s",pdpinfo->DNS1);
								sprintf(query_cnf->info[j].DNS2,"%s",pdpinfo->DNS2);
								if(conTemp->netType==1){
									query_cnf->info[j].localPort = conTemp->service_port;
									memset(query_cnf->info[j].dstIPStr,0,MAX_STRING_LEN);
									strcat(query_cnf->info[j].dstIPStr,"0:0:0:0");
									query_cnf->info[j].dstPort = 0;

									/*
									if(conTemp->action == 1){
										strcat(query_cnf->info[j].dstIPStr,"0:0:0:0");
										query_cnf->info[j].dstPort = 0;
									}else{
										string = ipaddr_ntoa((ip_addr_t*)&(conTemp->net_socket.ip_addr));
										sprintf(query_cnf->info[j].dstIPStr,"%s",string);
										query_cnf->info[j].dstPort = (conTemp->net_socket.ip_port);

									}
									*/
								}else{
									if(conTemp->net_socket.local_port!=0){
										query_cnf->info[j].localPort = conTemp->net_socket.local_port;
									}else{
										len = sizeof(struct sockaddr_in);
										getsockname(conTemp->socket_map[0].fd, (struct sockaddr *)&name, (socklen_t *)&len);
										query_cnf->info[j].localPort = ntohs(name.sin_port);
									}
									memset(query_cnf->info[j].dstIPStr,0,MAX_STRING_LEN);
									if(conTemp->type == 0){
										strcat(query_cnf->info[j].dstIPStr,"0:0:0:0");
										query_cnf->info[j].dstPort = 0;
									}else{
										string = ipaddr_ntoa((ip_addr_t*)&(conTemp->net_socket.ip_addr));
										sprintf(query_cnf->info[j].dstIPStr,"%s",string);
										query_cnf->info[j].dstPort = (conTemp->net_socket.ip_port);

									}
								}
								if(conTemp->netType==0){
									query_cnf->info[j].socketID = conTemp->socket_map[0].SocketId;
								}else if(conTemp->netType==1){
									query_cnf->info[j].socketID = conTemp->socket_map[0].SocketId;
								}else if(conTemp->netType==2){
									query_cnf->info[j].socketID = conTemp->socket_map[0].SocketId;
								}

								query_cnf->info[j].netType = conTemp->netType;
								j++;
								query_cnf->num++;
							}
						}

					}
					/*
					                    cm_link_status = CM_Get_LinkStatus(0);
					                    if (!cm_link_status){
					                        atnet_printf("%s[%d], get socket open req error!CM_Get_LinkStatus failed !!",__FUNCTION__,netId);
					                        result = MIRC_FAIL;
					                    }
					                    if(result == MIRC_SUCCESS){
					                        ipv4 = cm_link_status->ip4info;
					                        if (ipv4) {
					                            ipaddr = lwip_htonl(ipv4->IPAddr);
					                            string = ipaddr_ntoa((ip_addr_t*)&ipaddr);
					                            sprintf(query_cnf->localIPStr,"%s",string);

					                            ipaddr = lwip_htonl(ipv4->PrimaryDNS);
					                            string = ipaddr_ntoa((ip_addr_t*)&ipaddr);
					                            sprintf(query_cnf->DNS1,"%s",string);

					                            ipaddr = lwip_htonl(ipv4->SecondaryDNS);
					                            string = ipaddr_ntoa((ip_addr_t*)&ipaddr);
					                            sprintf(query_cnf->DNS2,"%s",string);

					                            ipaddr = lwip_htonl(ipv4->GateWay);
					                            string = ipaddr_ntoa((ip_addr_t*)&ipaddr);
					                            sprintf(query_cnf->gateStr,"%s",string);

					                        }
					                        free_link_status(cm_link_status);

					                        if(con->netType==1){
					                            query_cnf->localPort = con->service_port;
					                            memset(query_cnf->dstIPStr,0,MAX_STRING_LEN);
					                            if(con->action == 1){
					                                strcat(query_cnf->dstIPStr,"0:0:0:0");
					                                query_cnf->dstPort = 0;
					                            }else{
					                                string = ipaddr_ntoa((ip_addr_t*)&(con->net_socket.ip_addr));
					                                sprintf(query_cnf->dstIPStr,"%s",string);
					                                query_cnf->dstPort = (con->net_socket.ip_port);

					                            }
					                        }else{
					                            if(con->net_socket.local_port!=0){
					                                query_cnf->localPort = con->net_socket.local_port;
					                            }else{
					                                len = sizeof(struct sockaddr_in);
					                                getsockname(con->socket_map[0].fd, &name, &len);
					                                query_cnf->localPort = ntohs(name.sin_port);
					                            }
					                            memset(query_cnf->dstIPStr,0,MAX_STRING_LEN);
					                            if(con->type == 0){
					                                strcat(query_cnf->dstIPStr,"0:0:0:0");
					                                query_cnf->dstPort = 0;
					                            }else{
					                                string = ipaddr_ntoa((ip_addr_t*)&(con->net_socket.ip_addr));
					                                sprintf(query_cnf->dstIPStr,"%s",string);
					                                query_cnf->dstPort = (con->net_socket.ip_port);

					                            }
					                        }
					                        if(con->netType==0){
					                            query_cnf->socketID = con->socket_map[0].SocketId;
					                        }else if(con->netType==1){
					                            query_cnf->socketID = con->socket_map[0].SocketId;
					                        }else if(con->netType==2){
					                            query_cnf->socketID = con->socket_map[0].SocketId;
					                        }

					                        query_cnf->netType = con->netType;
					                    }
					                    */
                    //if(open_req) free(open_req);
                    query_cnf->result = result;
                    req->param = (void *)query_cnf;
                    req->primID = MI_NET_GET_SOCKET_OPEN_CNF;
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(req);
                    free(query_cnf);
                    //call back to response
                    break;
                case MI_NET_SET_SOCKET_READ_REQ:
                    read_req = (struct netSocketReadReq *)req->param;
                    if((con->netType == 0) && (read_req->socketID == con->socket_map[0].SocketId) && (con->socket_map[0].fd>0) ){
                        result = MIRC_SUCCESS;
                    }else if((con->netType == 1) && (read_req->socketID == con->socket_map[1].SocketId) && (con->socket_map[1].fd>0)){
                        result = MIRC_SUCCESS;
                    }else if((con->netType == 2) && (read_req->socketID == con->socket_map[0].SocketId) && (con->socket_map[0].fd>0)){
                        result = MIRC_SUCCESS;
                    }else if((con->netType == 3) && (read_req->socketID == con->socket_map[0].SocketId) && (con->socket_map[0].fd>0)){
                        result = MIRC_SUCCESS;
                    }else{
                        result = MIRC_PARAM_INVALID;
                        atnet_printf("%s[%d],read_req error!SocketID[%d],current netType[%d] socketID0[%d] socketID1[%d]\n",__FUNCTION__,netId,read_req->socketID, con->netType,
                            con->socket_map[0].SocketId, con->socket_map[1].SocketId);
                    }


                    if(result==MIRC_SUCCESS){
                        if(read_req->dataLen > RECV_MAX_BUFF){
                            result = MIRC_PARAM_INVALID;
                            atnet_printf("%s[%d], read_req error!dataLen too large!!",__FUNCTION__,netId);
                        }else if(read_req->dataLen==0){
                            atnet_printf("%s[%d], read_req error!dataLen = 0!!",__FUNCTION__,netId);
                            result = MIRC_PARAM_INVALID;
                        }

                        if(con->transportMode==1){
                            atnet_printf("%s[%d], MI_NET_SET_SOCKET_READ_REQ, transportMode error",__FUNCTION__,netId);
                            result = MIRC_FAIL;
                        }
                    }

                    atnet_printf("%s[%d], read_len=%d,  buffer =%x",__FUNCTION__,netId,read_req->dataLen,con->recv.buffer);
                    read_cnf = (struct netSocketReadCnf *)malloc(sizeof(struct netSocketReadCnf));
                    if(read_cnf == NULL){
                        free(req);
                        if(read_req) free(read_req);
                        //call back to response
                        break;

                    }
                    memset(read_cnf,0,sizeof(struct netSocketReadCnf));
                    read_cnf->socketID = read_req->socketID;
                    if(result == MIRC_SUCCESS){
                        AtNetMutexLock(netId);
                        if(con->recv.rota_flag!=ATNET_ROTA_FLAG){
                            len=con->recv.end-con->recv.begin;
                            atnet_printf("%s[%d], rota_flag!=ATNET_ROTA_FLAG,saved data len =%d,begin=%x, end=%x",__FUNCTION__,netId,len,con->recv.begin,con->recv.end);
                            if(len){
                                if(read_req->dataLen >= len){
                                    read_cnf->data = (char *)malloc(len+1);
                                    if(read_cnf->data){
                                        memset(read_cnf->data, 0, len+1);
                                        memcpy(read_cnf->data, con->recv.begin, len);
                                        con->recv.begin = con->recv.begin+len;
                                        read_cnf->dataLen = len;
                                    }else{
                                        read_cnf->dataLen = 0;
                                    }
                                }else{
                                    read_cnf->data = (char *)malloc(read_req->dataLen+1);
                                    if(read_cnf->data){
                                        memcpy(read_cnf->data, con->recv.begin, read_req->dataLen);
                                        con->recv.begin = con->recv.begin+read_req->dataLen;
                                        read_cnf->dataLen = read_req->dataLen;
                                    }else{
                                        read_cnf->dataLen = 0;
                                    }
                                }
                            }
                            //read_cnf->dataLen = len;
                        }else{
                            len=(con->recv.buffer + RECV_MAX_BUFF - con->recv.begin) + (con->recv.end - con->recv.buffer);
                            atnet_printf("%s[%d], rota_flag = ATNET_ROTA_FLAG,saved data len =%d,begin=%x, end=%x",__FUNCTION__,netId,len,con->recv.begin,con->recv.end);
                            if(read_req->dataLen >= len){
                                read_cnf->data = (char *)malloc(len+1);
                                if(read_cnf->data){
                                    memset(read_cnf->data, 0, len+1);
                                    atnet_printf("%s[%d], dataLen >= len,copy 1 len=%d, copy 2 len=%d",__FUNCTION__,netId,(con->recv.buffer + RECV_MAX_BUFF - con->recv.begin),(con->recv.end - con->recv.buffer));
                                    memcpy(read_cnf->data, con->recv.begin, (con->recv.buffer + RECV_MAX_BUFF - con->recv.begin));

                                    memcpy(read_cnf->data + (con->recv.buffer + RECV_MAX_BUFF - con->recv.begin), con->recv.buffer, (con->recv.end - con->recv.buffer));
                                    read_cnf->dataLen = len;
                                    con->recv.begin = con->recv.end;
                                    con->recv.rota_flag = 0;
                                }else{
                                    read_cnf->dataLen = 0;
                                }
                            }else{
                                read_cnf->data = (char *)malloc(read_req->dataLen+1);
                                if(read_cnf->data){
                                    memset(read_cnf->data, 0, read_req->dataLen+1);


                                    if((con->recv.buffer + RECV_MAX_BUFF - con->recv.begin)<read_req->dataLen){
                                        atnet_printf("%s[%d], dataLen < len,copy 1 len=%d, copy 2 len=%d",__FUNCTION__,netId,(con->recv.buffer + RECV_MAX_BUFF - con->recv.begin),(read_req->dataLen-(con->recv.buffer + RECV_MAX_BUFF - con->recv.begin)));
                                        memcpy(read_cnf->data, con->recv.begin, (con->recv.buffer + RECV_MAX_BUFF - con->recv.begin));
                                        memcpy(read_cnf->data+(con->recv.buffer + RECV_MAX_BUFF - con->recv.begin),
                                            con->recv.buffer,
                                            read_req->dataLen-(con->recv.buffer + RECV_MAX_BUFF - con->recv.begin));
                                        con->recv.begin = con->recv.buffer + read_req->dataLen-(con->recv.buffer + RECV_MAX_BUFF - con->recv.begin);
                                        con->recv.rota_flag = 0;

                                    }else{
                                        atnet_printf("%s[%d], dataLen < len,copy 1 len=%d",__FUNCTION__,netId,read_req->dataLen);
                                        memcpy(read_cnf->data, con->recv.begin, read_req->dataLen);
                                        con->recv.begin = con->recv.begin+read_req->dataLen;
                                    }
                                    read_cnf->dataLen = read_req->dataLen;
                                }
                                else{
                                    read_cnf->dataLen = 0;
                                }
                            }
                        }
                        AtNetMutexUnlock(netId);
                    }
                    read_cnf->result = result;
                    if(is_E20_module_type()){
                        cfg=getAsrCfgReq();
                        if(cfg->recv_data_format==1){
                            read_cnf->viewMode = HEX_MODE;
                        }else{
                            read_cnf->viewMode = TEXT_MODE;
                        }
                    }else{
                        read_cnf->viewMode = con->viewMode;
                    }

                    if(con->netType == 3){
                        read_cnf->remote_port = ntohs(con->remote_port);
                        sprintf(read_cnf->remote_IPStr,"%s",inet_ntoa(con->remote_ip));
                    }
                    if(con->ssl_enable && con->sslclient){
                        read_cnf->ssl_enable = 1;
                    }else{
                        read_cnf->ssl_enable = 0;
                    }
                    if(read_req) free(read_req);
                    req->primID = MI_NET_SET_SOCKET_READ_CNF;
                    req->param =(void *) read_cnf;
                    atnet_printf("%s[%d], read_cnf->dataLen=%d,viewMode=%d,",__FUNCTION__,netId,read_cnf->dataLen, read_cnf->viewMode);
                    //atnet_printf("%s[%d], %s,",__FUNCTION__,netId, read_cnf->data);
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(req);
                    if(result == MIRC_SUCCESS){
                        if(read_cnf->data) free(read_cnf->data);
                    }
                    free(read_cnf);
                    //call back to response
                    break;
                case MI_NET_SET_SOCKET_WRITE_REQ:
                    write_req = (struct netSocketWriteReq *)req->param;
                    atnet_printf("%s[%d], write_req dataLen=%d",__FUNCTION__,netId,write_req->dataLen);
                    if((con->netType == 0) && (write_req->socketID == con->socket_map[0].SocketId)
						&& con->socket_map[0].fd>0){
						result=MIRC_SUCCESS;
						/*
		                        if(con->type!=1){
		                            result=MIRC_SOCKET_NOT_EXIST;
		                            atnet_printf("%s[%d], write_req error!netType TCP Client, disconnect!!",__FUNCTION__,netId);
		                        }else{
		                            result=MIRC_SUCCESS;
		                        }
		                        */

                    }else if((con->netType == 1)&& (write_req->socketID == con->socket_map[0].SocketId)
                    	&& con->socket_map[0].fd>0){
                    	result=MIRC_FAIL;
                    	/*
		                        if(con->action!=0){
		                            result=MIRC_SOCKET_NOT_EXIST;
		                            atnet_printf("%s[%d], write_req error!netType TCP Server, don't accept any client!!",__FUNCTION__,netId);
		                        }else{
		                            result=MIRC_SUCCESS;
		                        }
		                        */
                    }else if((con->netType == 2)&& (write_req->socketID == con->socket_map[0].SocketId)
                    	&& con->socket_map[0].fd>0){
                    	result=MIRC_SUCCESS;
                    	/*
		                        if(con->type!=1){
		                            result=MIRC_SOCKET_NOT_EXIST;
		                            atnet_printf("%s[%d], write_req error!netType UDP, disconnect!!",__FUNCTION__,netId);
		                        }else{
		                            result=MIRC_SUCCESS;
		                        }
		                        */
                    }else{
                        result = MIRC_SOCKET_NOT_EXIST;
                        atnet_printf("%s[%d], write_req error! not found normal Socket ID,netType[%d], type[%d], action[%d]!!",__FUNCTION__,netId,con->netType,con->type,con->action);
                    }
                    if(result==MIRC_SUCCESS){
                        if(write_req->dataLen>1460){
                            result = MIRC_PARAM_INVALID;
                            atnet_printf("%s[%d], write_req error!dataLen too large!!",__FUNCTION__,netId);
                        }else if(write_req->dataLen==0){
                            atnet_printf("%s[%d], write_req error!dataLen = 0!!",__FUNCTION__,netId);
                            result = MIRC_PARAM_INVALID;
                        }
                        if(con->transportMode==1){
                            atnet_printf("%s[%d], MI_NET_SET_SOCKET_WRITE_REQ, transportMode error",__FUNCTION__,netId);
                            result = MIRC_FAIL;
                        }
                    }
                    write_cnf = (struct netSocketWriteCnf *)malloc(sizeof(struct netSocketWriteCnf));
                    if(write_cnf==NULL){
                        free(req);
                        if(write_req) free(write_req);
                        break;

                    }
                    write_cnf->result = result;
                    write_cnf->socketID = write_req->socketID;
                    write_cnf->dataLen = write_req->dataLen;

                    if(is_E20_module_type()){
                        cfg=getAsrCfgReq();
                        if(cfg->send_data_format==1){
                            write_cnf->viewMode = HEX_MODE;
                        }else{
                            write_cnf->viewMode = TEXT_MODE;
                        }
                    }else{
                        write_cnf->viewMode = con->viewMode;
                    }

                    if(write_req) free(write_req);
                    req->primID = MI_NET_SET_SOCKET_WRITE_CNF;
                    req->param =(void *) write_cnf;
                    //call back to response
                    miResponse(svcID,svcID, req->primID, req->requestHandle, req->param);
                    free(req);
                    free(write_cnf);
                    break;
                case MI_NET_SET_SOCKET_WRITE_DATA_REQ:
                    writedata_req = (struct netSocketWriteDataReq *)req->param;
                    atnet_printf("%s[%d], writedata_req dataLen=%d,transportMode=%d,viewMode=%d",__FUNCTION__,netId,writedata_req->dataLen,con->transportMode,con->viewMode);
                    //atnet_printf("%s[%d]",writedata_req->data,);
                    result = MIRC_FAIL;
                    if(is_E20_module_type()){
                        cfg=getAsrCfgReq();
                        if(con->transportMode != 1 && cfg->send_data_format== 0){
                            result = MIRC_SUCCESS;
                        }
                    }else{
                        if(con->transportMode == 0 && con->viewMode == 1){
                            result = MIRC_SUCCESS;
                        }
                    }
                    if(result == MIRC_SUCCESS){
                        len = writedata_req->dataLen/2;
                        if(len>0){
                            buf=(char *)malloc(len+1);
                            if(buf != NULL){
                                memset(buf,0,len+1);
                                atnet_printf("%s[%d], get writedata=%s",__FUNCTION__,netId,writedata_req->data);
                                HexStrToByte(writedata_req->data,buf,writedata_req->dataLen);
                                atnet_printf("%s[%d], HexStrToByte=%s",__FUNCTION__,netId,buf);
                                free(writedata_req->data);
                                writedata_req->data = buf;
                                writedata_req->dataLen = len;
                            }else{
                                result = MIRC_FAIL;
                                //writedata_req->data = NULL;
                                writedata_req->dataLen = 0;

                            }


                        }else{
                            result = MIRC_PARAM_INVALID;
                            //writedata_req->data = NULL;
                            writedata_req->dataLen = 0;
                        }

                    }

                    if((con->netType == 0)
                        && (writedata_req->socketID == con->socket_map[0].SocketId)
                        && (con->type==1)
                        &&(writedata_req->dataLen!=0)){
                        result=atnet_send_data(con->socket_map[0].fd , writedata_req->data, writedata_req->dataLen ,netId);
                    }else if((con->netType == 1)
                    && (writedata_req->socketID == con->socket_map[1].SocketId)
                    && (con->action==0)
                    &&(writedata_req->dataLen!=0)){
                        result=atnet_send_data(con->socket_map[1].fd , writedata_req->data, writedata_req->dataLen, netId);
                    }else if((con->netType == 2)
                    && (writedata_req->socketID == con->socket_map[0].SocketId)
                    && (con->type==1)
                    &&(writedata_req->dataLen!=0)){
                        result=atnet_send_data(con->socket_map[0].fd , writedata_req->data, writedata_req->dataLen, netId);
                    }else if((con->netType == 3)
                    && (writedata_req->socketID == con->socket_map[0].SocketId)
                    &&(writedata_req->dataLen!=0)){
                        result=atnet_send_data_remote(con->socket_map[0].fd , writedata_req->data, writedata_req->dataLen, netId, &name);
                    }else{
                        set_E20_error_num(E20_SOCKET_HAS_BEEN_CLOSED);
                        result = MIRC_PARAM_INVALID;
                        atnet_printf("%s[%d], write_req error! not found normal Socket ID,netType[%d], type[%d], action[%d]!!",__FUNCTION__,netId,con->netType,con->type,con->action);
                    }
                    write_cnf = (struct netSocketWriteCnf *)malloc(sizeof(struct netSocketWriteCnf));
                    if(write_cnf == NULL){
                        if(writedata_req) free(writedata_req);
                        free(req);
                        break;

                    }
                    write_cnf->result = result;
                    write_cnf->dataLen = writedata_req->dataLen;
                    write_cnf->socketID = writedata_req->socketID;
                    if(result == MIRC_SUCCESS){
                        con->Total_send += writedata_req->dataLen;
                    }

                    req->primID = MI_NET_SET_SOCKET_WRITE_DATA_CNF;
                    req->param =(void *) write_cnf;
                    //call back to response
                    if(con->transportMode==0||con->transportMode==2){
                        if(is_E20_module_type() && getAsrEchoSendData()){
                            miResponse(svcID,svcID, MI_NET_SET_ASR_ECHO_SEND_DATA_CNF, req->requestHandle, writedata_req);
                        }
                        if(writedata_req->data){
                            free(writedata_req->data);
                        }
                        miResponse(svcID,svcID, req->primID, req->requestHandle, req->param);
                    }else{
                        if(writedata_req->data) free(writedata_req->data);
                    }
                    if(writedata_req) free(writedata_req);
                    free(req);
                    free(write_cnf);
                    break;
                case MI_NET_SET_SOCKET_TRANS_WRITE_DATA_REQ:
                    writedata_req = (struct netSocketWriteDataReq *)req->param;
                    atnet_printf("%s[%d], writedata_req dataLen=%d,transportMode=%d,viewMode=%d",__FUNCTION__,netId,writedata_req->dataLen,con->transportMode,con->viewMode);
                    //atnet_printf("%s",writedata_req->data);


                    if((con->netType == 0)
                        && (writedata_req->socketID == con->socket_map[0].SocketId)
                        && (con->type==1)
                        &&(writedata_req->dataLen!=0)){
                        if(udpdataBackup[netId].len==0){
                            start_atnet_timer(netId);
                        }
                        save_udp_data_to_backup(writedata_req->data,writedata_req->dataLen,netId);
                        //result=atnet_send_data(con->socket_map[0].fd , writedata_req->data, writedata_req->dataLen ,netId);
                    }else if((con->netType == 1)
                    && (writedata_req->socketID == con->socket_map[1].SocketId)
                    && (con->action==0)
                    &&(writedata_req->dataLen!=0)){
                        result=atnet_send_data(con->socket_map[1].fd , writedata_req->data, writedata_req->dataLen, netId);
                    }else if((con->netType == 2)
                    && (writedata_req->socketID == con->socket_map[0].SocketId)
                    && (con->type==1)
                    &&(writedata_req->dataLen!=0)){
                        //result=atnet_send_data(con->socket_map[0].fd , writedata_req->data, writedata_req->dataLen, AT_NET0);
                        if(udpdataBackup[netId].len==0){
                            start_atnet_timer(netId);
                        }
                        save_udp_data_to_backup(writedata_req->data,writedata_req->dataLen,netId);
                    }else{
                        set_E20_error_num(E20_SOCKET_HAS_BEEN_CLOSED);
                        result = MIRC_PARAM_INVALID;
                        atnet_printf("%s[%d], write_req error! not found normal Socket ID,netType[%d], type[%d], action[%d]!!",__FUNCTION__,netId,con->netType,con->type,con->action);
                    }
                    write_cnf = (struct netSocketWriteCnf *)malloc(sizeof(struct netSocketWriteCnf));
                    if(write_cnf == NULL) {
                        if(writedata_req) free(writedata_req);
                        free(req);
                        break;

                    }
                    write_cnf->result = result;
                    write_cnf->dataLen = writedata_req->dataLen;
                    write_cnf->socketID = writedata_req->socketID;

                    if(result == MIRC_SUCCESS){
                        con->Total_send += writedata_req->dataLen;
                    }
                    req->primID = MI_NET_SET_SOCKET_WRITE_DATA_CNF;
                    req->param =(void *) write_cnf;
                    //call back to response

                    if(writedata_req->data) free(writedata_req->data);

                    if(writedata_req) free(writedata_req);
                    free(req);
                    free(write_cnf);
                    break;

                case MI_NET_TRANSPORT_UDP_BACKUP_SEND_REQ:
                    atnet_printf("%s[%d], get request to send UDP transport data backup!!",__FUNCTION__,netId);
                    send_udp_data_backup_buf(con->socket_map[0].fd, netId);

                    free(req);
                    break;
                case MI_NET_SET_SOCKET_ACK_REQ:
                    ack_req = (struct netSocketAckReq *)req->param;
                    len = sizeof(value);
                    if((con->netType == 0) && (ack_req->socketID == con->socket_map[0].SocketId)  && (con->type==1)){
                        getsockopt(con->socket_map[0].fd, IPPROTO_TCP, TCP_TXB_UNACK, &value, (socklen_t *)&len);
                        getsockopt(con->socket_map[0].fd, IPPROTO_TCP, TCP_TXB_REST, &rest_value, (socklen_t *)&len);
                        result = MIRC_SUCCESS;
                    }else if((con->netType == 1) && (ack_req->socketID == con->socket_map[1].SocketId) && (con->action==0)){
                        getsockopt(con->socket_map[1].fd, IPPROTO_TCP, TCP_TXB_UNACK, &value, (socklen_t *)&len);
                        getsockopt(con->socket_map[1].fd, IPPROTO_TCP, TCP_TXB_REST, &rest_value, (socklen_t *)&len);
                        result = MIRC_SUCCESS;
                    }else if((con->netType == 2) && (ack_req->socketID == con->socket_map[0].SocketId) && (con->type==1)){
                        value = 0;
                        //rest_value = SEND_MAX_BUFF;
                        result = MIRC_SOCKET_NOT_EXIST;
                    }else{
                        result = MIRC_SOCKET_NOT_EXIST;
                        value = 0;
                        atnet_printf("%s[%d], ack_req error!not found normal Socket ID,netType[%d], type[%d], action[%d]!!",__FUNCTION__,netId,con->netType,con->type,con->action);
                    }
                    ack_cnf = (struct netSocketAckCnf *)malloc(sizeof(struct netSocketAckCnf));
                    if(ack_cnf == NULL) {
                        free(req);
                        if(ack_req) free(ack_req);
                        break;

                    }
                    ack_cnf->socketID = ack_req->socketID;
                    ack_cnf->result = result;

                    ack_cnf->unAckedDataLen = value;
                    if(result == MIRC_SUCCESS){
                        ack_cnf->restBufferLen = rest_value;
                    }
					//else{
                    //    ack_cnf->restBufferLen = SEND_MAX_BUFF;
                    //}

                    if(ack_req) free(ack_req);
                    req->primID = MI_NET_SET_SOCKET_ACK_CNF;
                    req->param =(void *) ack_cnf;
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(req);
                    free(ack_cnf);
                    //call back to response
                    break;

                case MI_NET_SET_SOCKET_CLOSE_REQ:
					atnet_printf("%s[%d], %d: MI_NET_SET_SOCKET_CLOSE_REQ!!!",__FUNCTION__,netId,req->primID);
					close_req = (struct netSocketCloseReq *)req->param;
					if(!is_E20_module_type()){
						if((close_req->socketID == con->socket_map[0].SocketId)
							&& (con->socket_map[0].fd >0) ){
							fd = con->socket_map[0].fd;
							con->socket_map[0].fd=0;
							con->action = 1;
							con->listen = 0;
							con->type=0;
							AtNetMutexLock(netId);
                            con->recv.begin = 0;
                            con->recv.end = 0;
                            con->recv.rota_flag = 0;
                            if(con->recv.buffer){
                                free(con->recv.buffer);
                                con->recv.buffer =NULL;
                            }
                            AtNetMutexUnlock(netId);
							close(fd);
							result = MIRC_SUCCESS;

						}
						else
						{
							result = MIRC_SOCKET_NOT_EXIST;
						}

					}
					else{
						result=e20_close_socket(con,close_req,netId);
					}

                    close_cnf = (struct netSocketCloseCnf *)malloc(sizeof(struct netSocketCloseCnf));
                    if(close_cnf == NULL){
                        free(req);
                        if(close_req) free(close_req);
                        break;

                    }
                    close_cnf->result = result;
                    close_cnf->socketID = close_req->socketID;
                    if(close_req) free(close_req);
                    req->primID = MI_NET_SET_SOCKET_CLOSE_CNF;
                    req->param =(void *) close_cnf;
                    //atnet_printf("%s, close response!!",__FUNCTION__);
                    miResponse(svcID, svcID,req->primID, req->requestHandle, req->param);
                    free(req);
                    free(close_cnf);
                    //call back to response
                    break;

                case AT_NET_RECV_ERROR:
				case AT_NET_TCP_SERVER_ACCEPT_ERROR:
					atnet_printf("%s[%d], %d: AT_NET_RECV_ERROR!!!channel[%d]",__FUNCTION__,netId,req->primID,con->channel);
                    close_ind = (struct urcCloseInd *)req->param;
                    if(con->socket_map[0].fd>0
                        && con->socket_map[0].SocketId == close_ind->socketID)
                    {
							fd = con->socket_map[0].fd;
							con->socket_map[0].fd=0;
							con->action = 1;
							con->listen = 0;
							con->type=0;
							AtNetMutexLock(netId);
                            con->recv.begin = 0;
                            con->recv.end = 0;
                            con->recv.rota_flag = 0;
                            if(con->recv.buffer){
                                free(con->recv.buffer);
                                con->recv.buffer =NULL;
                            }
                            AtNetMutexUnlock(netId);
							close(fd);

							if(cmux_channelCtrl->channel[con->channel].dataCtrl.waitForWrite
								&& cmux_channelCtrl->channel[con->channel].dataCtrl.ftpPutMode==FALSE
								&& cmux_channelCtrl->channel[con->channel].transMode== NON_TRANSPARENT_MDOE){
								atnet_printf("%s: channel[%d],svcID:%d,atHandle:%d", __FUNCTION__,con->channel,svcID,cmux_channelCtrl->channel[con->channel].dataCtrl.atHandle);
								atnet_printf("%s: req->param:%d,atp:%d,socketID:%d", __FUNCTION__,req->param,cmux_channelCtrl->channel[con->channel].atp,cmux_channelCtrl->channel[con->channel].dataCtrl.socketID);
								miInd(svcID, svcID, MI_UCR_SEND_DATA_INTER_IND,
									con->channel, req->param);
								cmuxClearDataCtrl(con->channel);
								cmuxSetMode(con->channel, AT_COMMAND_MODE);
							}else{
								miInd(svcID, svcID,MI_UCR_CLOSE_IND, con->channel, req->param);
							}
							free(close_ind);
							free(req);

							if(req->primID == AT_NET_RECV_ERROR){
								set_E20_error_num(E20_SOCKET_READ_FAILED);
							}else{
								set_E20_error_num(E20_SOCKET_ACCEPT_FAILED);
							}


					}
					else{
                        free(req);
                        free(close_ind);
                        atnet_printf("%s[%d], AT_NET_RECV_ERROR may be the socket is already be closed!!",__FUNCTION__,netId);
                    }
					break;
/*
                    if(con->socket_map[1].fd>0
                        && con->socket_map[1].SocketId == close_ind->socketID
                        && con->action == 0
                        && con->netType == 1){
                        fd = con->socket_map[1].fd;

                        con->socket_map[1].fd = 0;
                        con->socket_map[1].SocketId = 0;
                        con->action = 1;
                        con->accept_transportMode = 0;
                        close(fd);
                        memset(&(con->net_socket),0,sizeof(Atnet_Socket));
                        if(con->recv.buffer){
                            free(con->recv.buffer);
                            con->recv.buffer =NULL;
                        }
                        memset(&(con->recv),0,sizeof(Atnet_Recv_Buf));

                        req->primID = MI_UCR_CLOSE_IND;
                        miInd(svcID, svcID,req->primID, con->channel, req->param);
                        free(req);
                        free(close_ind);
                        set_E20_error_num(E20_SOCKET_READ_FAILED);
                        // call indication API


                    }else if(con->socket_map[0].fd>0
                        && con->socket_map[0].SocketId == close_ind->socketID
                        && con->type== 1
                        && con->netType != 1){

                        fd = con->socket_map[0].fd;

                        con->socket_map[0].fd = 0;
                        con->socket_map[0].SocketId = 0;
                        con->action = 1;
                        con->netType = 0;
                        con->viewMode = 0;
                        con->transportMode = 0;
                        con->unAck = 0;
                        con->type = 0;
                        con->service_port =0;
                        con->accept_transportMode = 0;
#ifdef ATNET_SSL
                        if(con->ssl_enable){
                            atnet_ssl_client_shutdown(netId);
                            con->ssl_enable = 0;
                            con->ssl_ctx_id = 0;
                            req->primID = MI_UCR_SSL_CLOSE_IND;
                        }else
#endif
                        {
                            req->primID = MI_UCR_CLOSE_IND;
                        }
                        close(fd);
                        memset(con->service_addr,0,MAX_STRING_LEN);
                        memset(&(con->net_socket),0,sizeof(Atnet_Socket));
                        if(con->recv.buffer){
                            free(con->recv.buffer);
                            con->recv.buffer =NULL;
                        }
                        memset(&(con->recv),0,sizeof(Atnet_Recv_Buf));

                        miInd(svcID, svcID,req->primID, con->channel, req->param);
                        free(req);
                        free(close_ind);
                        set_E20_error_num(E20_SOCKET_READ_FAILED);
                        // call indication API
                    }else{
                        free(req);
                        free(close_ind);
                        atnet_printf("%s[%d], AT_NET_RECV_ERROR may be the socket is already be closed!!",__FUNCTION__,netId);
                    }

                case AT_NET_TCP_SERVER_ACCEPT_ERROR:
                    close_ind = (struct urcCloseInd *)req->param;
                    if(con->socket_map[0].fd>0
                        && con->socket_map[0].SocketId == close_ind->socketID
                        && con->netType == 1){
                        fd = con->socket_map[0].fd;
                        con->socket_map[0].fd = 0;
                        con->socket_map[0].SocketId = 0;
                        con->action = 1;
                        con->netType = 0;
                        con->viewMode = 0;
                        con->transportMode = 0;
                        con->unAck = 0;
                        con->type = 0;
                        con->listen = 0;
                        con->service_port =0;
                        con->accept_transportMode = 0;
                        set_E20_error_num(E20_SOCKET_ACCEPT_FAILED);
                        close(fd);
                        memset(con->service_addr,0,MAX_STRING_LEN);
                        req->primID = MI_UCR_CLOSE_IND;
                        miInd(svcID, svcID,req->primID, con->channel, req->param);
                        free(req);
                        free(close_ind);
                        // call indication API
                    }else{
                        free(req);
                        free(close_ind);
                        atnet_printf("%s[%d], AT_NET_TCP_SERVER_ACCEPT_ERROR may be the socket is already be closed!!",__FUNCTION__,netId);
                    }
                    break;
                    */
				case AT_NET_TCP_SERVER_INCOMING_CLIENT:
					incoming_client = (struct urcIncomingClientInd *)req->param;
					con->channel = incoming_client->channel;
					con->netType = 4;
					con->action = 1;
					con->socket_map[0].SocketId = incoming_client->socketID;
					con->socket_map[0].fd = incoming_client->skfd;
					con->viewMode = incoming_client->viewMode;
                    if(is_E20_module_type()){
                        con->action = 0;
                        con->netType = 0;
						con->type = 1;
                        req->primID=AT_NET_TCP_SERVER_INCOMING_CLIENT_RECV;
                        req->param= NULL;
                        OSAMsgQSend(AtNetProxyToTCPUdpMsgQ[netId], ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,(UINT8 *)req, OSA_SUSPEND);
                    }

				    free(req);
                    free(incoming_client);
					break;
                case MI_NET_SYS_RESET_REQ:
                    atnet_printf("%s[%d], %d: SYS RESET!!!",__FUNCTION__,netId,req->primID);
                    set_sg_led_status(SGLED_RESET);
                    sleep(2);
                    free(req);
                    PM812_SW_RESET();
                    //reset_router(0, NULL);
                    break;
                case MI_NET_DEACTIVE_CLOSE_SOCKET_REQ:
                    atnet_printf("%s[%d], %d: DEACTIVE close socket!!!",__FUNCTION__,netId,req->primID);
                    close_req = (struct netSocketCloseReq *)req->param;
					close_ind=(struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
					for(i=0;i<6;i++)
					{
						conTemp = atnet_get_context_by_netId(i);
						if(conTemp->channel == close_req->socketID && conTemp->socket_map[0].fd>0){
							fd = conTemp->socket_map[0].fd;
							conTemp->socket_map[0].fd = 0;
							req->primID = MI_UCR_CLOSE_IND;
							close_ind->socketID = conTemp->socket_map[0].SocketId;
                        	miInd(svcID, svcID, MI_UCR_CLOSE_IND, conTemp->channel, (void *)close_ind);
							conTemp->action = 1;
							conTemp->listen = 0;
							conTemp->type=0;
							AtNetMutexLock(i);
                            conTemp->recv.begin = 0;
                            conTemp->recv.end = 0;
                            conTemp->recv.rota_flag = 0;
                            if(conTemp->recv.buffer){
                                free(conTemp->recv.buffer);
                                conTemp->recv.buffer =NULL;
                            }
                            AtNetMutexUnlock(i);
							close(fd);
						}
					}
					free(close_ind);
					close_ind=NULL;
					if(close_req) free(close_req);
					close_req=NULL;
                    free(req);
                    break;
				case MI_NET_DETACH_CLOSE_SOCKET_REQ:
                    atnet_printf("%s[%d], %d: DETACH close socket!!!",__FUNCTION__,netId,req->primID);
					close_ind=(struct urcCloseInd *)malloc(sizeof(struct urcCloseInd));
					for(i=0;i<6;i++)
					{
						conTemp = atnet_get_context_by_netId(i);
						if(conTemp->socket_map[0].fd>0){
							fd = conTemp->socket_map[0].fd;
							conTemp->socket_map[0].fd = 0;
							close_ind->socketID = conTemp->socket_map[0].SocketId;
							miInd(svcID, svcID, MI_UCR_CLOSE_IND, conTemp->channel, (void *)close_ind);

							conTemp->action = 1;
							conTemp->listen = 0;
							conTemp->type=0;
							AtNetMutexLock(i);
                            conTemp->recv.begin = 0;
                            conTemp->recv.end = 0;
                            conTemp->recv.rota_flag = 0;
                            if(conTemp->recv.buffer){
                                free(conTemp->recv.buffer);
                                conTemp->recv.buffer =NULL;
                            }
                            AtNetMutexUnlock(i);
							close(fd);
						}
					}
					free(close_ind);
					close_ind=NULL;
                    free(req);
                    break;
					/*
                    if((con->netType == 0)
                        && (con->socket_map[0].fd >0) ){
                        fd = con->socket_map[0].fd;
                        //shutdown(con->socket_map[0].fd, SHUT_RDWR);
                        con->socket_map[0].fd = 0;
                        con->socket_map[0].SocketId = 0;
                        con->net_socket.ip_addr= 0;
                        con->net_socket.ip_port = 0;
                        con->net_socket.local_port = 0;
                        con->service_port = 0;
                        con->viewMode = 0;
                        con->transportMode = 0;
#ifdef ATNET_SSL

                        if(con->ssl_enable){
                            atnet_ssl_client_shutdown(netId);
                            con->ssl_enable = 0;
                            con->ssl_ctx_id = 0;
                        }
#endif
                        close(fd);
                        memset(con->service_addr,0,MAX_STRING_LEN);
                        if(con->type == 1){
                            AtNetMutexLock(netId);
                            con->recv.begin = 0;
                            con->recv.end = 0;
                            con->recv.rota_flag = 0;
                            if(con->recv.buffer){
                                free(con->recv.buffer);
                                con->recv.buffer =NULL;
                            }
                            AtNetMutexUnlock(netId);
                            con->type = 0;
                        }
                        //atnet_printf("%s, close success!!",__FUNCTION__);
                        result = MIRC_SUCCESS;

                    }else if(con->netType == 1){
                        if(con->socket_map[1].fd > 0){
                            //shutdown(con->socket_map[1].fd, SHUT_RDWR);

                            fd = con->socket_map[1].fd;
                            con->socket_map[1].fd = 0;
                            con->socket_map[1].SocketId = 0;
                            con->net_socket.ip_addr= 0;
                            con->net_socket.ip_port = 0;
                            con->net_socket.local_port = 0;
                            con->accept_transportMode = 0;
                            close(fd);
                            if(con->action== 0){
                                AtNetMutexLock(netId);
                                con->recv.begin = 0;
                                con->recv.end = 0;
                                con->recv.rota_flag = 0;
                                if(con->recv.buffer){
                                    free(con->recv.buffer);
                                    con->recv.buffer =NULL;
                                }
                                AtNetMutexUnlock(netId);
                                con->action = 1;
                            }
                            result = MIRC_SUCCESS;
                        }
                        if(con->socket_map[0].fd > 0){

                            fd = con->socket_map[0].fd;

                            //shutdown(con->socket_map[0].fd, SHUT_RDWR);
                            con->socket_map[0].fd = 0;
                            con->socket_map[0].SocketId = 0;
                            con->service_port = 0;
                            con->listen = 0;
                            con->accept_transportMode = 0;
                            con->transportMode = 0;
                            con->viewMode = 0;
                            close(fd);
                            memset(con->service_addr,0,MAX_STRING_LEN);
                            result = MIRC_SUCCESS;
                        }
                    }else if((con->netType == 2)
                    && (con->socket_map[0].fd > 0)){

                        //shutdown(con->socket_map[0].fd, SHUT_RDWR);
                        fd = con->socket_map[0].fd;
                        con->socket_map[0].fd = 0;
                        con->socket_map[0].SocketId = 0;
                        con->net_socket.ip_addr= 0;
                        con->net_socket.ip_port = 0;
                        con->net_socket.local_port = 0;
                        con->service_port = 0;
                        con->transportMode = 0;
                        con->viewMode = 0;
                        close(fd);
                        memset(con->service_addr,0,MAX_STRING_LEN);

                        AtNetMutexLock(netId);
                        con->recv.begin = 0;
                        con->recv.end = 0;
                        con->recv.rota_flag = 0;
                        if(con->recv.buffer){
                            free(con->recv.buffer);
                            con->recv.buffer =NULL;
                        }
                        AtNetMutexUnlock(netId);

                        con->type = 0;
                        result = MIRC_SUCCESS;
                    }else{
                        result = MIRC_SOCKET_NOT_EXIST;
                        set_E20_error_num(E20_SOCKET_HAS_BEEN_CLOSED);
                        atnet_printf("%s[%d], ACTIVE close_req error! not found normal Socket ID,netType[%d], type[%d], action[%d]!!",__FUNCTION__,netId,con->netType,con->type,con->action);
                    }
			*/

                case MI_NET_SET_ASR_SOCKET_OPEN_REQ:
                    asropen = (struct asrOpenReq *)req->param;
                    result=atnet_asr_socket_service_open(asropen,req,netId);

                    asropencnf = (struct asrOpenCnf *)malloc(sizeof(struct asrOpenCnf));
                    if(asropencnf == NULL){
                        free(req);
                        free(asropen);
                        break;

                    }
                    asropencnf->result = result;
                    asropencnf->ConnectID = asropen->ConnectID;
                    asropencnf->access_mode = asropen->access_mode;
                    asropencnf->ssl_enable = asropen->ssl_enable;
                    miResponse(svcID, svcID,MI_NET_SET_ASR_SOCKET_OPEN_CNF, req->requestHandle, asropencnf);

                    free(req);
                    free(asropen);
                    free(asropencnf);
                    break;
                case MI_NET_SET_ASR_SOCKET_STATE_REQ:
                    asrstate = (struct asrStateReq *)req->param;
                    result=atnet_asr_socket_query_state(asrstate,req,netId);
                    free(req);
                    free(asrstate);

                    break;
                case MI_NET_SET_ASR_SOCKET_SEND_REQ:
                    result = MIRC_SUCCESS;
                    asrsend = (struct asrSocketSendReq *)req->param;
                    if(asrsend->dataLen == 0){
                        asrquerysenddata = (struct asrSocketQuerySendDataCnf *)malloc(sizeof(struct asrSocketQuerySendDataCnf));
                        if(asrquerysenddata== NULL){
                            free(req);
                            free(asrsend);
                            break;

                        }
                        if(con->netType == 0 || con->netType == 1){
                            getsockopt(con->socket_map[0].fd, IPPROTO_TCP, TCP_TXB_UNACK, &value, (socklen_t*)&len);
                            asrquerysenddata->total_send_len = con->Total_send;
                            asrquerysenddata->unacked_bytes = value;
                            asrquerysenddata->acked_bytes = con->Total_send - value;

                        }else{
                            asrquerysenddata->total_send_len = con->Total_send;
                            asrquerysenddata->unacked_bytes = 0;
                            asrquerysenddata->acked_bytes = 0;
                        }

                        miResponse(svcID, svcID,MI_NET_SET_ASR_QUERY_SEND_DATA_CNF, req->requestHandle, asrquerysenddata);
                        free(req);
                        free(asrsend);
                        free(asrquerysenddata);
                        break;

                    }else if(asrsend->remote_port!=0 && con->netType==3 ){
                        host_entry = gethostbyname(asrsend->remoteIP);
                        if (host_entry == NULL) {
                            set_E20_error_num(E20_DNS_PARSE_FAILED);
                            atnet_printf("%s[%d], MI_NET_SET_ASR_SOCKET_SEND_REQ, DNS gethostbyname failed: %s\n",__FUNCTION__,netId,asropen->ip_address);
                            result =  MIRC_FAIL;
                        }

                        name.sin_family = AF_INET;
                        name.sin_port = htons(asrsend->remote_port);
                        name.sin_addr.s_addr= * (UINT32 *) host_entry->h_addr_list[0];

                    }

                    write_cnf = (struct netSocketWriteCnf *)malloc(sizeof(struct netSocketWriteCnf));
                    if(write_cnf == NULL){
                        free(req);
                        free(asrsend);
                        break;
                    }
                    write_cnf->result = result;
                    write_cnf->socketID = asrsend->socketID;
                    if(asrsend->dataLen == 0xFFFFFFFF){
                        write_cnf->dataLen = 0;
                    }else{
                        write_cnf->dataLen = asrsend->dataLen;
                    }

                    write_cnf->viewMode = 0;
                    req->primID = MI_NET_SET_SOCKET_WRITE_CNF;
                    req->param =(void *) write_cnf;
                    miResponse(svcID,svcID, req->primID, req->requestHandle, req->param);
                    free(req);
                    free(asrsend);
                    free(write_cnf);
                    break;
                case MI_NET_SET_ASR_QUERY_READ_DATA_REQ:
                    read_req = (struct netSocketReadReq *)req->param;
                    asrqueryreaddata = (struct asrSocketQueryReadDataCnf *)malloc(sizeof(struct asrSocketQueryReadDataCnf));
                    if(asrqueryreaddata== NULL){
                        free(req);
                        free(read_req);
                        break;
                    }

                    asrqueryreaddata->total_read_len = con->Total_read;
                    asrqueryreaddata->have_read_len = con->Total_read;
                    asrqueryreaddata->unread_len = 0;


                    miResponse(svcID, svcID,MI_NET_SET_ASR_QUERY_READ_DATA_CNF, req->requestHandle, asrqueryreaddata);
                    free(req);
                    free(read_req);
                    free(asrqueryreaddata);
                    break;
                 case MI_NET_SET_ASR_SOCKET_SEND_HEX_REQ:
                    sendHex = (struct asrSendHexReq *)req->param;

                    len = sendHex->data_len/2;

                   buf=(char *)malloc(len+1);
                   if(buf == NULL){
                       free(req);
                       free(sendHex);
                       break;

                   }
                   
                   memset(buf,0,len+1);
                   atnet_printf("%s[%d], get writedata=%s",__FUNCTION__,netId,sendHex->data);
                   HexStrToByte(sendHex->data,buf,sendHex->data_len);

                    if(con->netType == 1){
                        fd = con->socket_map[1].fd;

                    }else{
                        fd = con->socket_map[0].fd;
                    }
                    if(fd > 0){
                        result=atnet_send_data(fd , buf, len ,netId);
                    }else{
                        result = MIRC_FAIL;
                        set_E20_error_num(E20_OPERATION_NOT_ALLOWED);
                    }

                    if(result == MIRC_SUCCESS){
                        con->Total_send += len;
                    }
                    gen_cnf = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                    if(gen_cnf== NULL){
                        free(req);
                        free(buf);
                        free(sendHex);
                        break;

                    }
                    gen_cnf->result = result;


                    miResponse(svcID, svcID,MI_NET_SET_ASR_SOCKET_SEND_HEX_CNF, req->requestHandle, gen_cnf);
                    free(req);
                    free(buf);
                    free(sendHex);
                    free(gen_cnf);
                    break;
                case MI_NET_SET_ASR_SWT_MODE_REQ:
                    swtmd = (struct asrSwTmdReq *)req->param;
                    swtmdcnf = (struct asrSwTmdCnf *)malloc(sizeof(struct asrSwTmdCnf));
                    if(swtmdcnf==NULL){
                        free(req);
                        free(swtmd);
                        break;
                    }

                    if((con->socket_map[0].fd>0) && (con->socket_map[0].SocketId ==swtmd->socketID)){
                        if(swtmd->access_mode == 2){
                            con->transportMode =1;
                        }else if(swtmd->access_mode == 1){
                            con->transportMode =2;
                        }else{
                            con->transportMode =0;
                        }
                        swtmdcnf->result= MIRC_SUCCESS;

                    }else{
                        swtmdcnf->result = MIRC_FAIL;
                        set_E20_error_num(E20_OPERATION_NOT_ALLOWED);
                    }


                    miResponse(svcID, svcID,MI_NET_SET_ASR_SWT_MODE_CNF, req->requestHandle, swtmdcnf);
                    free(req);
                    free(swtmd);
                    free(swtmdcnf);
                    break;
#ifdef ATNET_SSL
                case MI_NET_SET_SSL_CFG_REQ:
                    sslcfgreq =(struct miSSLCfgReq *)req->param;
                    gen_cnf = (struct setGenericCnf *)malloc(sizeof(struct setGenericCnf));
                    gen_cnf->result = atnet_set_ssl_ctx_cfg(sslcfgreq);
                    miResponse(svcID, svcID, MI_NET_SET_SSL_CFG_CNF, req->requestHandle, gen_cnf);
                    free(req);
                    free(sslcfgreq);
                    free(gen_cnf);
                    break;
                case MI_NET_GET_SSL_CFG_REQ:
                    sslcfgreq =(struct miSSLCfgReq *)req->param;
                    atnet_get_ssl_ctx_cfg(sslcfgreq);
                    miResponse(svcID, svcID, MI_NET_GET_SSL_CFG_CNF, req->requestHandle, sslcfgreq);
                    free(req);
                    free(sslcfgreq);
                    break;
                case MI_NET_GET_ALL_SSL_CFG_REQ:
                    //sslctx = atnet_get_all_ssl_ctx_cfg();
                    //miResponse(svcID, svcID, MI_NET_GET_ALL_SSL_CFG_CNF, req->requestHandle, sslctx);
                    //free(req);
                    break;
#endif
                default:
                    atnet_printf("%s[%d], %d: unknow req.primID!!",__FUNCTION__,netId,req->primID);
                    break;
            }
        }
        else if(apiMsg.msgID == MI_TRANSPORT_REQUEST_MSG){

            //writedata_req = (struct netSocketWriteDataReq *)apiMsg.pArgs;
            if (cmuxSwitchToCommandMode((char *)apiMsg.pArgs, apiMsg.len) == TRUE)
            {
                atnet_printf("%s: from transparent data mode to command mode,netId %d,channel %d", __func__,netId,con->channel);
                cmuxSetCommandMode(con->channel);
            }else{
                atnet_printf("%s[%d], MI_TRANSPORT_REQUEST_MSG dataLen=%d,transportMode=%d,viewMode=%d",__FUNCTION__,netId,apiMsg.len,con->transportMode,con->viewMode);
                if(udpdataBackup[netId].len==0){
                   start_atnet_timer(netId);
                }
                save_udp_data_to_backup((char *)apiMsg.pArgs,apiMsg.len,netId);
                con->Total_send += apiMsg.len;

                if(apiMsg.pArgs) free(apiMsg.pArgs);
                //if(writedata_req) free(writedata_req);

            }

        }
        else{
            atnet_printf("%s[%d], %d: apiMsg.msgID =! MI_REQUEST_MSG!!",__FUNCTION__,netId,req->primID);
        }



    }
}


void atnet0_proxy_worker_thread(void * argv)
{
    atnet_process_proxy_worker(AT_NET0);
}

void atnet1_proxy_worker_thread(void * argv)
{
    atnet_process_proxy_worker(AT_NET1);
}

void atnet2_proxy_worker_thread(void * argv)
{
    atnet_process_proxy_worker(AT_NET2);
}


void atnet3_proxy_worker_thread(void * argv)
{
    atnet_process_proxy_worker(AT_NET3);
}


void atnet4_proxy_worker_thread(void * argv)
{
    atnet_process_proxy_worker(AT_NET4);
}

void atnet5_proxy_worker_thread(void * argv)
{
    atnet_process_proxy_worker(AT_NET5);
}



void AtNetMutexLock(int netId)
{
    OSA_STATUS status;

    if(AtNetSemaRef[netId])
        status = OSASemaphoreAcquire(AtNetSemaRef[netId], OS_SUSPEND);
}

void AtNetMutexUnlock(int netId)
{
    OSA_STATUS status;

    if(AtNetSemaRef[netId])
        status = OSASemaphoreRelease(AtNetSemaRef[netId]);
}

static void (*atnet_tcp_udp_worker_thread[])(void * argv) =
{
	atnet0_tcp_udp_worker_thread,
	atnet1_tcp_udp_worker_thread,
	atnet2_tcp_udp_worker_thread,
	atnet3_tcp_udp_worker_thread,
	atnet4_tcp_udp_worker_thread,
	atnet5_tcp_udp_worker_thread,
};

static void (*atnet_proxy_worker_thread[])(void * argv) =
{
	atnet0_proxy_worker_thread,
	atnet1_proxy_worker_thread,
	atnet2_proxy_worker_thread,
	atnet3_proxy_worker_thread,
	atnet4_proxy_worker_thread,
	atnet5_proxy_worker_thread,
};

int atnet_init_by_netid(int atnet_id)
{
    OSA_STATUS status = OS_SUCCESS;
    char name[128];
	void *atnetProxyWorkerTaskStack=NULL;
	void *atnetTcpUdpWorkerTaskStack=NULL;

    status = OSASemaphoreCreate (&AtNetSemaRef[atnet_id], 1, OSA_FIFO);
	if(status != OS_SUCCESS)
	    return 1;

    /***********create timer to send taransport udp backup data*************/
    status = OSATimerCreate(&udp_send_timer[atnet_id]);
	if(status != OS_SUCCESS)
	    return 1;

    
    memset(name,0,128);
    sprintf(name,"AtNet%dCiToProxyMsgQ",atnet_id);
	status = OSAMsgQCreate(&AtNetCiToProxyMsgQ[atnet_id],
	                       name,
	                       ATNETCI_TO_PROXY_MESSAGE_Q_SIZE,
	                       ATNETCI_TO_PROXY_MESSAGE_Q_MAX,
	                       OS_FIFO);
	if(status != OS_SUCCESS)
	    return 1;
	
    initMsgQRef(SVCID_NET0+atnet_id, AtNetCiToProxyMsgQ[atnet_id]);


    memset(name,0,128);
    sprintf(name,"AtNet%dProxyToTCPUDPMsgQ",atnet_id);

	status = OSAMsgQCreate(&AtNetProxyToTCPUdpMsgQ[atnet_id],
	                       name,
	                       ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,
	                       ATNETPROXY_TO_TCPDUP_MESSAGE_Q_MAX,OS_FIFO);
	if(status != OS_SUCCESS)
	    return 1;

    memset(name,0,128);
    sprintf(name,"AtNet%dProxyToTCPServerMsgQ",atnet_id);

	status = OSAMsgQCreate(&AtNetProxyToTCPServerMsgQ[atnet_id],
	                       name,
	                       ATNET_PROXY_TO_TCPDUP_MESSAGE_Q_SIZE,
	                       ATNETPROXY_TO_TCPDUP_MESSAGE_Q_MAX,OS_FIFO);

	if(status != OS_SUCCESS)
	    return 1;



    atnetProxyWorkerTaskStack=malloc(ATNET_TASK_STACK_SIZE);
    if(atnetProxyWorkerTaskStack == NULL)
    {
        atnet_printf("Out of memory in atnet0ProxyWorkerTaskStack!");
        return 1;
    }

    memset(name,0,128);
    sprintf(name,"AtNet%dProxyWorkerTaskNet",atnet_id);

    
    if(OSATaskCreate(&AtNetProxyWorkerRef[atnet_id],
                     atnetProxyWorkerTaskStack,
                     ATNET_TASK_STACK_SIZE,ATNET_TRANS_TASK_PRIORITY,(char*)name,
                     atnet_proxy_worker_thread[atnet_id], NULL) != 0)
    {
        atnet_printf("Cannot start AtNet0ProxyWorkerTask!");
        return 1;
    }


    atnetTcpUdpWorkerTaskStack=malloc(ATNET_TASK_STACK_SIZE);
    if(atnetTcpUdpWorkerTaskStack == NULL)
    {
        atnet_printf("Out of memory in atnet0TcpUdpWorkerTaskStack!");
        return 1;
    }
    memset(name,0,128);
    sprintf(name,"AtNet%dTcpUdpWorkerTaskNet",atnet_id);

    
    if(OSATaskCreate(&AtNetTcpUdpWorkerRef[atnet_id],
                     atnetTcpUdpWorkerTaskStack,
                     ATNET_TASK_STACK_SIZE,ATNET_TASK_PRIORITY,(char*)name,
                     atnet_tcp_udp_worker_thread[atnet_id], NULL) != 0)
    {
        atnet_printf("Cannot start AtNet0TcpUdpWorkerTaskNet!");
        return 1;
    }
    
    //initMsgQRef(SVCID_DIALER+atnet_id, AtNetCiToProxyMsgQ[atnet_id]);

    
    return 0;


}

int atnet_start()
{

    int i=0,res=0;
    for(i=AT_NET0;i<(AT_NET5+1);i++){
        res=atnet_init_by_netid(i);
        if(res!=0){
            return res;
        }
    }

	return 0;
}

int init_atnet_context()
{
    int i=0;
    memset(atnet_con,0,6*sizeof(AtNet_Context));

	for(i=0;i<AT_NET_MAX;i++)
	{
	    atnet_con[i].action = 1;


        udpdataBackup[i].len = 0;
        udpdataBackup[i].buf= (char *)malloc(2048);
        if(udpdataBackup[i].buf==NULL){
            return 1;
        }
	}
	return 0;
}

static OSTaskRef e20_send_timer_task=NULL;
OSFlagRef	e20_send_timer_flag=NULL;
#define E20_SEND_FLAG 1


void e20_set_timer_flag_internal(void)
{
    OS_STATUS os_status;
    atnet_printf("%s",__func__);
    if(e20_send_timer_flag)
        os_status = OSAFlagSet(e20_send_timer_flag,E20_SEND_FLAG,OSA_FLAG_OR);

}

void e20_send_timer_thread(void * argv)
{
    unsigned long flag_value;
	OSA_STATUS status;
	struct asrSetCfg *cfg;
	struct writeDataCtrl *dataCtrl;
	unsigned int current_tick;
	unsigned int value=0;
	unsigned int min_value=0;
	int i,ret;
	int count;
	UINT32 atHandle;
	char * writedataBuf;
	UINT32 svcID = SVCID_NET0;
	struct netSocketWriteDataReq reqParas = { 0 };
	struct cmuxChannelController * cmux_channelCtrl = get_cmux_channelCtrl();
	while(1){

        status = OSAFlagWait(e20_send_timer_flag, E20_SEND_FLAG,OSA_FLAG_OR_CLEAR,&flag_value,OSA_SUSPEND);

        atnet_printf("%s: e20_get_timer_falg,status[%d]",__func__,status);

        if(status != OS_SUCCESS){
            continue;
        }
        
		if(is_E20_module_type()){
			cfg = getAsrCfgReq();
			if(cfg->timeout==0){
				atnet_printf("get e20 send timer flag,but time is 0!");
				continue;
			}
			min_value = cfg->timeout;
		}else{
			cfg = getAsrCfgReq();
			min_value = 45*1000;//TIGX default 45sec
			cfg->timeout=45*1000;
		}




        while(1){
            count =0;
            for(i=0;i<MAX_CMUX_CHANNEL;i++){
                dataCtrl = &cmux_channelCtrl->channel[i].dataCtrl;
                if(dataCtrl->waitForWrite && dataCtrl->ftpPutMode==FALSE && cmux_channelCtrl->channel[i].transMode== NON_TRANSPARENT_MDOE){
                    atnet_printf("%s: channel[%d] sending data to socket %d,cfg time:%d", __func__,i, dataCtrl->socketID,cfg->timeout);
                    count ++;
                    current_tick=OSAGetTicks();
                    atnet_printf("%s: current_tick[%d],lastDataTick[%d]", __func__,current_tick,dataCtrl->lastDataTick);
                    if(((current_tick-dataCtrl->lastDataTick)*5) >cfg->timeout){
                        count --;
						atHandle = dataCtrl->atHandle;
						if(is_E20_module_type()){
							{
								atnet_printf("%s: channel[%d] send data to socket %d, writeLen=%d", __func__, i, dataCtrl->socketID,dataCtrl->writeLen);
								reqParas.socketID = dataCtrl->socketID;
								reqParas.dataLen = dataCtrl->writeLen;
								writedataBuf = (char *)malloc(reqParas.dataLen+1);
								if(writedataBuf){
                                    memcpy(writedataBuf,cmux_channelCtrl->channel[i].writeBuf,reqParas.dataLen);
                                    reqParas.data = writedataBuf;
                                    ret=setNetWriteDataReq(atHandle, &reqParas);
								}else{
                                    ret = MIRC_FAIL;
								}

								if(ret != MIRC_SUCCESS){
									atnet_printf("%s: send data fail", __func__);
								}
							}

						}else{
							svcID=svcID+i;
                        	miInd(svcID, svcID, MI_UCR_SEND_DATA_TIMEOUT_IND, atHandle, NULL);
						}


                        dataCtrl->writeLen = 0;
                        dataCtrl->totalWriteLen = 0;
                        dataCtrl->waitForWrite = FALSE;
                        dataCtrl->lastDataTick = 0;
                        cmux_channelCtrl->channel[i].transMode = AT_COMMAND_MODE;

                    }else{
                        value = cfg->timeout -(current_tick-dataCtrl->lastDataTick)*5;
                        if(value<min_value){
                           min_value = value;
                        }
                    }
                }
            }
            if(count==0){
                atnet_printf("%s: not found any channel sending data", __func__);
                break;
            }
            atnet_printf("%s: need sleep value %d", __func__,min_value);
            if(min_value>5){
                OSATaskSleep(min_value/5);
            }else{
                OSATaskSleep(1);
            }

        }

	}

}


int init_e20_send_timer(void)
{
    void *e20TimerWorkerTaskStack=NULL;

    //if(!is_E20_module_type()){
    //    return;
    //}

    if(OSAFlagCreate(&e20_send_timer_flag) != 0)
    {
		atnet_printf("OSAFlagCreate e20_send_timer_flag failed!");
		goto failed;

    }


    e20TimerWorkerTaskStack = malloc(2048);
	if(e20TimerWorkerTaskStack == NULL)
	{
		atnet_printf("Out of memory in e20TimerWorkerTaskStack!");
		goto failed;
	}

	if(OSATaskCreate(&e20_send_timer_task,
	                 e20TimerWorkerTaskStack,
	                 2048,ATNET_SEND_TIMER_PRIORITY,(char*)"e20_send_timer_task",
	                 e20_send_timer_thread, NULL) != 0)
	{
		atnet_printf("Cannot start e20_send_timer_task!");
		goto failed;
	}
    return 0 ;
failed:
    if(e20_send_timer_flag) OSAFlagDelete(e20_send_timer_flag);
    e20_send_timer_flag = NULL;
    if(e20TimerWorkerTaskStack) free(e20TimerWorkerTaskStack);
    
    return 1;

}


static OSMsgQRef gATnetCommMsgQ = NULL;
#define ATNET_COMMAND_MSG_Q_MAX	256
extern OSMsgQRef 	gsATPMsgQ[];
extern UINT8 getChannelNum(UINT8 simID, AT_CHANNEL_TYPE type);
extern void ci_modem_clear_ind_apt_index(TelAtParserID index);

static int atnet_task_resp_cb(TelAtParserID sATPInd, const char *in_str, int size)
{
	OSA_STATUS osa_status;
	DialRespMessage resp_msg = {0};
	OSMsgQRef tRespMsgQ = NULL;
	TelAtParserID respInd = sATPInd;
	char logBuf[UART_LOG_MAX_LENTH] = {'\0'};
	char *pBuf = NULL;
	const char *pData = NULL;
	INT32 index, headSkipLen = 0, skipLen = 0;
	UINT8 simID, channelNum;

	if(in_str == NULL){
        return 0;
	}

	if (sATPInd == TEL_AT_CMD_ATP_0 || sATPInd == TEL_AT_CMD_ATP_36)
	{
		return 0;
	}

	simID = getSimIdFromChnlId(respInd, CHANNEL_IMS);
	index = converChannelIdToIndex(respInd, CHANNEL_IMS);
	channelNum = getChannelNum(SIM_0, CHANNEL_IMS);

	atnet_printf("%s: sATP%d simID %d channel num %d channel index %d", __func__, respInd, simID, channelNum, index);
	if (simID == SIM_0)
		tRespMsgQ = gsATPMsgQ[index];
	else
		tRespMsgQ = gsATPMsgQ[index - channelNum];

	if(tRespMsgQ != NULL)
	{
		if (strlen(in_str) <= 0)
			return 0;

		/*skip head \r\n*/
		pData = in_str;
		while ((headSkipLen < strlen(in_str)) && (*pData == '\r' || *pData == '\n'))
		{
			headSkipLen++;
			pData++;
		}

		/*skip tail \r\n*/
		pData = in_str + strlen(in_str) - 1;
		skipLen = headSkipLen;
		while ((skipLen < strlen(in_str)) && (*pData == '\r' || *pData == '\n'))
		{
			skipLen++;
			pData--;
		}

		//snprintf(logBuf,UART_LOG_MAX_LENTH - 1, "%s: resp(len %d, skiplen %d, %d) %s",__func__, size, headSkipLen, skipLen, in_str + headSkipLen);
		//dialmsg(logBuf);

		if (strlen(in_str) <= skipLen)
		{
			atnet_printf("%s: size wrong, discard", __func__);
			return 0;
		}

		pBuf = (char *)malloc(strlen(in_str)+1);
		if(pBuf == NULL) return 0;
		memset(pBuf, 0, strlen(in_str)+1);
		memcpy(pBuf, in_str + headSkipLen, strlen(in_str) - skipLen);

		resp_msg.MsgData = (void *)pBuf;
		osa_status = OSAMsgQSend(tRespMsgQ, sizeof(resp_msg), (UINT8 *)&resp_msg, OSA_SUSPEND);
	}

	return 0;
}



int init_tigx_command_cb(void)
{
    int i=0;
    OSA_STATUS	status;
    status = OSAMsgQCreate(&gATnetCommMsgQ,
#ifdef  OSA_QUEUE_NAMES
                              "atnetComQ",
#endif
                              sizeof(DialRespMessage),
                              ATNET_COMMAND_MSG_Q_MAX,
                              OSA_FIFO);
    if(status != OS_SUCCESS)
        return 1;


    i = converChannelIdToIndex(TEL_ATNET_AT_CMD_ATP, CHANNEL_IMS);
    
    gsATPMsgQ[i] = gATnetCommMsgQ;


    MATSetConfIndCB(TEL_ATNET_AT_CMD_ATP, atnet_task_resp_cb);
    ci_modem_clear_ind_apt_index(TEL_ATNET_AT_CMD_ATP);
    
    init_cmux_channelCtrl();
    return 0;

}

#endif



void init_atnet_internal(void)
{

    if(bspGetBoardType() == TIGX_MIFI){
        init_cmux_channelCtrl();
#ifdef SG_NET
        int res;
        if (!isAtNetSupported)
        {
            atnet_printf("AtNet not support return");
            return;
        }
        init_e20_config();

        res = init_e20_send_timer();
        if(res!=0)
        {
            atnet_printf("init_e20_send_timer failed");
            return;
        }

        res = init_tigx_command_cb();
        if(res!=0)
        {
            atnet_printf("init_tigx_command_cb failed");
            return;
        }

        init_tigx_info();

        res = init_atnet_context();
        if(res!=0)
        {
            atnet_printf("init_atnet_context failed");
            return;
        }

        
#ifdef ATNET_SSL
        init_atnet_ssl_context();
#endif

        atnet_start();
#endif

    }

}

#endif

