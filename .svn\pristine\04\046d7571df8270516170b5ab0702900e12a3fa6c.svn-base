#ifndef __HERO_MEDIA_H__
#define __HERO_MEDIA_H__

#include "heroapp.h"

int32 hero_audio_initialize(void);

int32 hr_media_audio_get_vol(void);

int32 hr_media_audio_set_vol(int32 vol);

int32 hr_audio_init();

int32 hr_audio_load_sound(T_DSM_MEDIA_LOAD *media, BOOL isFile, int sound_type);

int32 hr_audio_stop_sound();

int32 hr_audio_play_sound_req(T_DSM_MEDIA_PLAY *playReq);

void hr_audio_set_play_finish_status();

int32 hr_media_audio_pause_req(void);

int32 hr_media_audio_resume_req(void);

int32 hr_audio_close_handle();

int32 hr_audio_get_status();

int32 hr_audio_get_total_time(uint8* input, int32 input_len, uint8** output, int32* output_len, HR_PLAT_EX_CB *cb);

int32 hr_media_audio_get_play_seconds(uint8* input, int32 input_len, uint8** output, int32* output_len, HR_PLAT_EX_CB *cb);

int32 hr_media_audio_get_play_milliseconds(uint8* input, int32 input_len, uint8** output, int32* output_len, HR_PLAT_EX_CB *cb);

int32 hr_media_audio_set_progress_time(uint8* input, int32 input_len, uint8** output, int32* output_len, HR_PLAT_EX_CB *cb);

int32 hr_voice_record_resume(int32 param);

int32 hr_voice_record_stop(int32 param);

int32 hr_voice_record_pause(int32 param);

int32 hr_voice_record_start(uint8* input, int32 input_len, uint8** output, int32* output_len, HR_PLAT_EX_CB *cb);

int32 hr_voice_record_get_buffer(uint8* input, int32 input_len, uint8** output, int32* output_len, HR_PLAT_EX_CB *cb);

#endif

