/******************************************************************************
 * * inter_message_interface.h - internal interface between message and others
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/

typedef enum {
    MESSAGE_IME_DICTIONARY_ENABLE = 0,
    MESSAGE_IME_DICTIONARY_OFF
} IF_IME_CONFIG_REQ;

/**
 * Inform Message init
 * param (in) void
 * return  void
 */
extern VOID Message_Init_Req(VOID);

/**
 * IME config req
 * param (in) IMEConfigReq:IF_IME_CONFIG_REQ
 * return  void
 */
extern VOID IME_Config_Req(IF_IME_CONFIG_REQ IMEConfigReq);

/**
 * Call app send SMS or send the number to others
 * param (in) char* cNumber,char* Message
 * return  void
 */
extern VOID Call_Send_Sms_Req(char *Number, char *Message);

/**
 * Call app inform message app the MT call Incoming
 * param (in)void
 * return  void
 */

extern VOID Call_Inform_Sms_Mt_Call_Incoming(VOID);
