# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"
/*
 *  Elliptic curves over GF(p): curve-specific data and functions
 *
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"
/**
 * \file common.h
 *
 * \brief Utility macros for internal use in the library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"
/**
 * \file build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
 /*
  *  Copyright The Mbed TLS Contributors
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  */










/*
 * This set of compile-time defines can be used to determine the version number
 * of the Mbed TLS library used. Run-time variables for the same can be found in
 * version.h
 */

/**
 * The version number x.y.z is split into three parts.
 * Major, Minor, Patchlevel
 */




/**
 * The single version number has the following structure:
 *    MMNNPP00
 *    Major version | Minor version | Patch version
 */








# 1 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
/*
 * Copyright (C) 2019 Alibaba Group Holding Limited
 */




/*specially for alios*/







/* System support */




//#define MBEDTLS_PLATFORM_MEMORY

//#define MBEDTLS_CONFIG_TLS_DEBUG

/* mbed TLS feature support */
# 35 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_ALT


# 53 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 60 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 76 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"





# 103 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"







/* mbed TLS modules */
# 127 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_C
//#define MBEDTLS_TIMING_C













# 178 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 185 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 202 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 209 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//#ifdef LWM2M_WITH_MBEDTLS
# 219 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#endif /* LWM2M_WITH_MBEDTLS */







/* Module configuration options */





/**
 * \def MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION
 *
 * If set, the X509 parser will not break-off when parsing an X509 certificate
 * and encountering an unknown critical extension.
 *
 * \warning Depending on your PKI use, enabling this can be a security risk!
 *
 * Uncomment to prevent an error.
 */




//ALIPAY_SUPPORT BEGIN


typedef unsigned int time_t;
# 273 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#ifndef MBEDTLS_ECP_DP_SECP256R1_ENABLED


//#endif
# 284 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//ALIPAY_SUPPORT END

# 298 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 66 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"








/* Target and application specific configurations
 *
 * Allow user to override any previous default.
 *
 */




# 89 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
/**
 * \file check_config.h
 *
 * \brief Consistency checks for configuration options
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




/*
 * We assume CHAR_BIT is 8 in many places. In practice, this is true on our
 * target platforms, so not an issue, but let's just be extra sure.
 */
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 31 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"




# 52 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"








































//ALIPAY_SUPPORT BEGIN
# 111 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END






# 129 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"





//ALIPAY_SUPPORT BEGIN
# 152 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END


























# 196 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 206 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
































































































































# 341 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















































































































































































































































# 589 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"















# 611 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"



















































# 707 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















# 737 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











/*
 * HKDF is mandatory for TLS 1.3.
 * Otherwise support for at least one ciphersuite mandates either SHA_256 or
 * SHA_384.
 */
# 759 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

/*
 * The current implementation of TLS 1.3 requires MBEDTLS_SSL_KEEP_PEER_CERTIFICATE.
 */




# 782 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











































































# 863 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 870 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

































































/* Reject attempts to enable options that have been removed and that could
 * cause a build to succeed but with features removed. */













































/*
 * Avoid warning from -pedantic. This is a convenient place for this
 * workaround since this is included by every single file before the
 * #if defined(MBEDTLS_xxx_C) that results in empty translation units.
 */
typedef int mbedtls_iso_c_forbids_empty_translation_units;

# 91 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 27 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 29 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/** Helper to define a function as static except when building invasive tests.
 *
 * If a function is only used inside its own source file and should be
 * declared `static` to allow the compiler to optimize for code size,
 * but that function has unit tests, define it with
 * ```
 * MBEDTLS_STATIC_TESTABLE int mbedtls_foo(...) { ... }
 * ```
 * and declare it in a header in the `library/` directory with
 * ```
 * #if defined(MBEDTLS_TEST_HOOKS)
 * int mbedtls_foo(...);
 * #endif
 * ```
 */






# 63 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/** Allow library to access its structs' private members.
 *
 * Although structs defined in header files are publicly available,
 * their members are private and should not be accessed by the user.
 */


/** Byte Reading Macros
 *
 * Given a multi-byte integer \p x, MBEDTLS_BYTE_n retrieves the n-th
 * byte from x, where byte 0 is the least significant byte.
 */
# 84 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */
# 103 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 32 bits unsigned integer in big-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 122 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 32 bits integer corresponding to four bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the four bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the four bytes to build the 32 bits unsigned
 *                  integer from.
 */
# 141 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 32 bits unsigned integer in little-endian order.
 *
 * \param   n       32 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 32
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 32 bits unsigned integer \p n.
 */
# 160 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */
# 177 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 16 bits unsigned integer in little-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 194 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 16 bits integer corresponding to two bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the two bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the two bytes to build the 16 bits unsigned
 *                  integer from.
 */
# 211 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 16 bits unsigned integer in big-endian order.
 *
 * \param   n       16 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 16
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 16 bits unsigned integer \p n.
 */
# 228 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 246 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 24 bits unsigned integer in big-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 264 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 24 bits integer corresponding to three bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the three bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the three bytes to build the 24 bits unsigned
 *                  integer from.
 */
# 282 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 24 bits unsigned integer in little-endian order.
 *
 * \param   n       24 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 24
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 24 bits unsigned integer \p n.
 */
# 300 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * big-endian order (MSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and most significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */
# 323 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 64 bits unsigned integer in big-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the most significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 346 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Get the unsigned 64 bits integer corresponding to eight bytes in
 * little-endian order (LSB first).
 *
 * \param   data    Base address of the memory to get the eight bytes from.
 * \param   offset  Offset from \p data of the first and least significant
 *                  byte of the eight bytes to build the 64 bits unsigned
 *                  integer from.
 */
# 369 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/**
 * Put in memory a 64 bits unsigned integer in little-endian order.
 *
 * \param   n       64 bits unsigned integer to put in memory.
 * \param   data    Base address of the memory where to put the 64
 *                  bits unsigned integer in.
 * \param   offset  Offset from \p data where to put the least significant
 *                  byte of the 64 bits unsigned integer \p n.
 */
# 392 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\common.h"

/* Fix MSVC C99 compatible issue
 *      MSVC support __func__ from visual studio 2015( 1900 )
 *      Use MSVC predefine macro to avoid name check fail.
 */




# 21 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"



# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"
/**
 * \file ecp.h
 *
 * \brief This file provides an API for Elliptic Curves over GF(P) (ECP).
 *
 * The use of ECP in cryptography and TLS is defined in
 * <em>Standards for Efficient Cryptography Group (SECG): SEC1
 * Elliptic Curve Cryptography</em> and
 * <em>RFC-4492: Elliptic Curve Cryptography (ECC) Cipher Suites
 * for Transport Layer Security (TLS)</em>.
 *
 * <em>RFC-2409: The Internet Key Exchange (IKE)</em> defines ECP
 * group types.
 *
 */

/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h"
 /**
 * \file private_access.h
 *
 * \brief Macro wrapper for struct's members.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




# 32 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/private_access.h"

# 37 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

# 39 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"
/**
 * \file bignum.h
 *
 * \brief Multi-precision integer library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
# 25 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

# 27 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"
/* stddef.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.1.4 */

/* Copyright (C) ARM Ltd., 1999
 * All rights reserved
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991 ARM Limited. All rights reserved.             */
/* version 0.05 */

/*
 * The following types and macros are defined in several headers referred to in
 * the descriptions of the functions declared in that header. They are also
 * defined in this header file.
 */





# 34 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"




  typedef signed int ptrdiff_t;



 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



  /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* also in <stdlib.h> and <inttypes.h> */
# 82 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



   /* null pointer constant. */




  /* EDG uses __INTADDR__ to avoid errors when strict */




  typedef long double max_align_t;









# 114 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



/* end of stddef.h */

# 29 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"
# 30 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"





/** An error occurred while reading from or writing to a file. */

/** Bad input parameters to function. */

/** There is an invalid character in the digit string. */

/** The buffer is too small to write to. */

/** The input arguments are negative or result in illegal output. */

/** The input argument for division is zero, which is not allowed. */

/** The input arguments are not acceptable. */

/** Memory allocation failed. */


# 58 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

/*
 * Maximum size MPIs are allowed to grow to in number of limbs.
 */



/*
 * Maximum window size used for modular exponentiation. Default: 6
 * Minimum value: 1. Maximum value: 6.
 *
 * Result is an array of ( 2 ** MBEDTLS_MPI_WINDOW_SIZE ) MPIs used
 * for the sliding window calculation. (So 64 by default)
 *
 * Reduction in size, reduces speed.
 */




/*
 * Maximum size of MPIs allowed in bits and bytes for user-MPIs.
 * ( Default: 512 bytes => 4096 bits, Maximum tested: 2048 bytes => 16384 bits )
 *
 * Note: Calculations can temporarily result in larger MPIs. So the number
 * of limbs required (MBEDTLS_MPI_MAX_LIMBS) is higher.
 */





/*
 * When reading from files with mbedtls_mpi_read_file() and writing to files with
 * mbedtls_mpi_write_file() the buffer should have space
 * for a (short) label, the MPI (in the provided radix), the newline
 * characters and the '\0'.
 *
 * By default we assume at least a 10 char label, a minimum radix of 10
 * (decimal) and a maximum of 4096 bit numbers (1234 decimal chars).
 * Autosized at compile time for at least a 10 char label, a minimum radix
 * of 10 (decimal) for a number of MBEDTLS_MPI_MAX_BITS size.
 *
 * This used to be statically sized to 1250 for a maximum of 4096 bit
 * numbers (1234 decimal chars).
 *
 * Calculate using the formula:
 *  MBEDTLS_MPI_RW_BUFFER_SIZE = ceil(MBEDTLS_MPI_MAX_BITS / ln(10) * ln(2)) +
 *                                LabelSize + 6
 */




/*
 * Define the base integer type, architecture-wise.
 *
 * 32 or 64-bit integer types can be forced regardless of the underlying
 * architecture by defining MBEDTLS_HAVE_INT32 or MBEDTLS_HAVE_INT64
 * respectively and undefining MBEDTLS_HAVE_ASM.
 *
 * Double-width integers (e.g. 128-bit in 64-bit architectures) can be
 * disabled by defining MBEDTLS_NO_UDBL_DIVISION.
 */
# 168 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"


    /* Default to 32-bit compilation */



    typedef  int32_t mbedtls_mpi_sint;
    typedef uint32_t mbedtls_mpi_uint;










/**
 * \brief          MPI structure
 */
typedef struct mbedtls_mpi
{
    int s;              /*!<  Sign: -1 if the mpi is negative, 1 otherwise */
    size_t n;           /*!<  total # of limbs  */
    mbedtls_mpi_uint *p;          /*!<  pointer to limbs  */
}
mbedtls_mpi;

/**
 * \brief           Initialize an MPI context.
 *
 *                  This makes the MPI ready to be set or freed,
 *                  but does not define a value for the MPI.
 *
 * \param X         The MPI context to initialize. This must not be \c NULL.
 */
void mbedtls_mpi_init( mbedtls_mpi *X );

/**
 * \brief          This function frees the components of an MPI context.
 *
 * \param X        The MPI context to be cleared. This may be \c NULL,
 *                 in which case this function is a no-op. If it is
 *                 not \c NULL, it must point to an initialized MPI.
 */
void mbedtls_mpi_free( mbedtls_mpi *X );

/**
 * \brief          Enlarge an MPI to the specified number of limbs.
 *
 * \note           This function does nothing if the MPI is
 *                 already large enough.
 *
 * \param X        The MPI to grow. It must be initialized.
 * \param nblimbs  The target number of limbs.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_grow( mbedtls_mpi *X, size_t nblimbs );

/**
 * \brief          This function resizes an MPI downwards, keeping at least the
 *                 specified number of limbs.
 *
 *                 If \c X is smaller than \c nblimbs, it is resized up
 *                 instead.
 *
 * \param X        The MPI to shrink. This must point to an initialized MPI.
 * \param nblimbs  The minimum number of limbs to keep.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed
 *                 (this can only happen when resizing up).
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_shrink( mbedtls_mpi *X, size_t nblimbs );

/**
 * \brief          Make a copy of an MPI.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param Y        The source MPI. This must point to an initialized MPI.
 *
 * \note           The limb-buffer in the destination MPI is enlarged
 *                 if necessary to hold the value in the source MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_copy( mbedtls_mpi *X, const mbedtls_mpi *Y );

/**
 * \brief          Swap the contents of two MPIs.
 *
 * \param X        The first MPI. It must be initialized.
 * \param Y        The second MPI. It must be initialized.
 */
void mbedtls_mpi_swap( mbedtls_mpi *X, mbedtls_mpi *Y );

/**
 * \brief          Perform a safe conditional copy of MPI which doesn't
 *                 reveal whether the condition was true or not.
 *
 * \param X        The MPI to conditionally assign to. This must point
 *                 to an initialized MPI.
 * \param Y        The MPI to be assigned from. This must point to an
 *                 initialized MPI.
 * \param assign   The condition deciding whether to perform the
 *                 assignment or not. Possible values:
 *                 * \c 1: Perform the assignment `X = Y`.
 *                 * \c 0: Keep the original value of \p X.
 *
 * \note           This function is equivalent to
 *                      `if( assign ) mbedtls_mpi_copy( X, Y );`
 *                 except that it avoids leaking any information about whether
 *                 the assignment was done or not (the above code may leak
 *                 information through branch prediction and/or memory access
 *                 patterns analysis).
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_safe_cond_assign( mbedtls_mpi *X, const mbedtls_mpi *Y, unsigned char assign );

/**
 * \brief          Perform a safe conditional swap which doesn't
 *                 reveal whether the condition was true or not.
 *
 * \param X        The first MPI. This must be initialized.
 * \param Y        The second MPI. This must be initialized.
 * \param assign   The condition deciding whether to perform
 *                 the swap or not. Possible values:
 *                 * \c 1: Swap the values of \p X and \p Y.
 *                 * \c 0: Keep the original values of \p X and \p Y.
 *
 * \note           This function is equivalent to
 *                      if( assign ) mbedtls_mpi_swap( X, Y );
 *                 except that it avoids leaking any information about whether
 *                 the assignment was done or not (the above code may leak
 *                 information through branch prediction and/or memory access
 *                 patterns analysis).
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 *
 */
int mbedtls_mpi_safe_cond_swap( mbedtls_mpi *X, mbedtls_mpi *Y, unsigned char assign );

/**
 * \brief          Store integer value in MPI.
 *
 * \param X        The MPI to set. This must be initialized.
 * \param z        The value to use.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_lset( mbedtls_mpi *X, mbedtls_mpi_sint z );

/**
 * \brief          Get a specific bit from an MPI.
 *
 * \param X        The MPI to query. This must be initialized.
 * \param pos      Zero-based index of the bit to query.
 *
 * \return         \c 0 or \c 1 on success, depending on whether bit \c pos
 *                 of \c X is unset or set.
 * \return         A negative error code on failure.
 */
int mbedtls_mpi_get_bit( const mbedtls_mpi *X, size_t pos );

/**
 * \brief          Modify a specific bit in an MPI.
 *
 * \note           This function will grow the target MPI if necessary to set a
 *                 bit to \c 1 in a not yet existing limb. It will not grow if
 *                 the bit should be set to \c 0.
 *
 * \param X        The MPI to modify. This must be initialized.
 * \param pos      Zero-based index of the bit to modify.
 * \param val      The desired value of bit \c pos: \c 0 or \c 1.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_set_bit( mbedtls_mpi *X, size_t pos, unsigned char val );

/**
 * \brief          Return the number of bits of value \c 0 before the
 *                 least significant bit of value \c 1.
 *
 * \note           This is the same as the zero-based index of
 *                 the least significant bit of value \c 1.
 *
 * \param X        The MPI to query.
 *
 * \return         The number of bits of value \c 0 before the least significant
 *                 bit of value \c 1 in \p X.
 */
size_t mbedtls_mpi_lsb( const mbedtls_mpi *X );

/**
 * \brief          Return the number of bits up to and including the most
 *                 significant bit of value \c 1.
 *
 * * \note         This is same as the one-based index of the most
 *                 significant bit of value \c 1.
 *
 * \param X        The MPI to query. This must point to an initialized MPI.
 *
 * \return         The number of bits up to and including the most
 *                 significant bit of value \c 1.
 */
size_t mbedtls_mpi_bitlen( const mbedtls_mpi *X );

/**
 * \brief          Return the total size of an MPI value in bytes.
 *
 * \param X        The MPI to use. This must point to an initialized MPI.
 *
 * \note           The value returned by this function may be less than
 *                 the number of bytes used to store \p X internally.
 *                 This happens if and only if there are trailing bytes
 *                 of value zero.
 *
 * \return         The least number of bytes capable of storing
 *                 the absolute value of \p X.
 */
size_t mbedtls_mpi_size( const mbedtls_mpi *X );

/**
 * \brief          Import an MPI from an ASCII string.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param radix    The numeric base of the input string.
 * \param s        Null-terminated string buffer.
 *
 * \return         \c 0 if successful.
 * \return         A negative error code on failure.
 */
int mbedtls_mpi_read_string( mbedtls_mpi *X, int radix, const char *s );

/**
 * \brief          Export an MPI to an ASCII string.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param radix    The numeric base of the output string.
 * \param buf      The buffer to write the string to. This must be writable
 *                 buffer of length \p buflen Bytes.
 * \param buflen   The available size in Bytes of \p buf.
 * \param olen     The address at which to store the length of the string
 *                 written, including the  final \c NULL byte. This must
 *                 not be \c NULL.
 *
 * \note           You can call this function with `buflen == 0` to obtain the
 *                 minimum required buffer size in `*olen`.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if the target buffer \p buf
 *                 is too small to hold the value of \p X in the desired base.
 *                 In this case, `*olen` is nonetheless updated to contain the
 *                 size of \p buf required for a successful call.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_string( const mbedtls_mpi *X, int radix,
                              char *buf, size_t buflen, size_t *olen );

# 485 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"

/**
 * \brief          Import an MPI from unsigned big endian binary data.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param buf      The input buffer. This must be a readable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The length of the input buffer \p p in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_read_binary( mbedtls_mpi *X, const unsigned char *buf,
                             size_t buflen );

/**
 * \brief          Import X from unsigned binary data, little endian
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param buf      The input buffer. This must be a readable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The length of the input buffer \p p in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_read_binary_le( mbedtls_mpi *X,
                                const unsigned char *buf, size_t buflen );

/**
 * \brief          Export X into unsigned binary data, big endian.
 *                 Always fills the whole buffer, which will start with zeros
 *                 if the number is smaller.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param buf      The output buffer. This must be a writable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The size of the output buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p buf isn't
 *                 large enough to hold the value of \p X.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_binary( const mbedtls_mpi *X, unsigned char *buf,
                              size_t buflen );

/**
 * \brief          Export X into unsigned binary data, little endian.
 *                 Always fills the whole buffer, which will end with zeros
 *                 if the number is smaller.
 *
 * \param X        The source MPI. This must point to an initialized MPI.
 * \param buf      The output buffer. This must be a writable buffer of length
 *                 \p buflen Bytes.
 * \param buflen   The size of the output buffer \p buf in Bytes.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_BUFFER_TOO_SMALL if \p buf isn't
 *                 large enough to hold the value of \p X.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_write_binary_le( const mbedtls_mpi *X,
                                 unsigned char *buf, size_t buflen );

/**
 * \brief          Perform a left-shift on an MPI: X <<= count
 *
 * \param X        The MPI to shift. This must point to an initialized MPI.
 * \param count    The number of bits to shift by.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_shift_l( mbedtls_mpi *X, size_t count );

/**
 * \brief          Perform a right-shift on an MPI: X >>= count
 *
 * \param X        The MPI to shift. This must point to an initialized MPI.
 * \param count    The number of bits to shift by.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_shift_r( mbedtls_mpi *X, size_t count );

/**
 * \brief          Compare the absolute values of two MPIs.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param Y        The right-hand MPI. This must point to an initialized MPI.
 *
 * \return         \c 1 if `|X|` is greater than `|Y|`.
 * \return         \c -1 if `|X|` is lesser than `|Y|`.
 * \return         \c 0 if `|X|` is equal to `|Y|`.
 */
int mbedtls_mpi_cmp_abs( const mbedtls_mpi *X, const mbedtls_mpi *Y );

/**
 * \brief          Compare two MPIs.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param Y        The right-hand MPI. This must point to an initialized MPI.
 *
 * \return         \c 1 if \p X is greater than \p Y.
 * \return         \c -1 if \p X is lesser than \p Y.
 * \return         \c 0 if \p X is equal to \p Y.
 */
int mbedtls_mpi_cmp_mpi( const mbedtls_mpi *X, const mbedtls_mpi *Y );

/**
 * \brief          Check if an MPI is less than the other in constant time.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI
 *                 with the same allocated length as Y.
 * \param Y        The right-hand MPI. This must point to an initialized MPI
 *                 with the same allocated length as X.
 * \param ret      The result of the comparison:
 *                 \c 1 if \p X is less than \p Y.
 *                 \c 0 if \p X is greater than or equal to \p Y.
 *
 * \return         0 on success.
 * \return         MBEDTLS_ERR_MPI_BAD_INPUT_DATA if the allocated length of
 *                 the two input MPIs is not the same.
 */
int mbedtls_mpi_lt_mpi_ct( const mbedtls_mpi *X, const mbedtls_mpi *Y,
        unsigned *ret );

/**
 * \brief          Compare an MPI with an integer.
 *
 * \param X        The left-hand MPI. This must point to an initialized MPI.
 * \param z        The integer value to compare \p X to.
 *
 * \return         \c 1 if \p X is greater than \p z.
 * \return         \c -1 if \p X is lesser than \p z.
 * \return         \c 0 if \p X is equal to \p z.
 */
int mbedtls_mpi_cmp_int( const mbedtls_mpi *X, mbedtls_mpi_sint z );

/**
 * \brief          Perform an unsigned addition of MPIs: X = |A| + |B|
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param B        The second summand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_abs( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform an unsigned subtraction of MPIs: X = |A| - |B|
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param B        The subtrahend. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p B is greater than \p A.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_sub_abs( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a signed addition of MPIs: X = A + B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param B        The second summand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_mpi( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a signed subtraction of MPIs: X = A - B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param B        The subtrahend. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_sub_mpi( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a signed addition of an MPI and an integer: X = A + b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first summand. This must point to an initialized MPI.
 * \param b        The second summand.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_add_int( mbedtls_mpi *X, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a signed subtraction of an MPI and an integer:
 *                 X = A - b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The minuend. This must point to an initialized MPI.
 * \param b        The subtrahend.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_sub_int( mbedtls_mpi *X, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a multiplication of two MPIs: X = A * B
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first factor. This must point to an initialized MPI.
 * \param B        The second factor. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mul_mpi( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a multiplication of an MPI with an unsigned integer:
 *                 X = A * b
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The first factor. This must point to an initialized MPI.
 * \param b        The second factor.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mul_int( mbedtls_mpi *X, const mbedtls_mpi *A,
                         mbedtls_mpi_uint b );

/**
 * \brief          Perform a division with remainder of two MPIs:
 *                 A = Q * B + R
 *
 * \param Q        The destination MPI for the quotient.
 *                 This may be \c NULL if the value of the
 *                 quotient is not needed.
 * \param R        The destination MPI for the remainder value.
 *                 This may be \c NULL if the value of the
 *                 remainder is not needed.
 * \param A        The dividend. This must point to an initialized MPi.
 * \param B        The divisor. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p B equals zero.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_div_mpi( mbedtls_mpi *Q, mbedtls_mpi *R, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a division with remainder of an MPI by an integer:
 *                 A = Q * b + R
 *
 * \param Q        The destination MPI for the quotient.
 *                 This may be \c NULL if the value of the
 *                 quotient is not needed.
 * \param R        The destination MPI for the remainder value.
 *                 This may be \c NULL if the value of the
 *                 remainder is not needed.
 * \param A        The dividend. This must point to an initialized MPi.
 * \param b        The divisor.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p b equals zero.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_div_int( mbedtls_mpi *Q, mbedtls_mpi *R, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a modular reduction. R = A mod B
 *
 * \param R        The destination MPI for the residue value.
 *                 This must point to an initialized MPI.
 * \param A        The MPI to compute the residue of.
 *                 This must point to an initialized MPI.
 * \param B        The base of the modular reduction.
 *                 This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p B equals zero.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p B is negative.
 * \return         Another negative error code on different kinds of failure.
 *
 */
int mbedtls_mpi_mod_mpi( mbedtls_mpi *R, const mbedtls_mpi *A,
                         const mbedtls_mpi *B );

/**
 * \brief          Perform a modular reduction with respect to an integer.
 *                 r = A mod b
 *
 * \param r        The address at which to store the residue.
 *                 This must not be \c NULL.
 * \param A        The MPI to compute the residue of.
 *                 This must point to an initialized MPi.
 * \param b        The integer base of the modular reduction.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_DIVISION_BY_ZERO if \p b equals zero.
 * \return         #MBEDTLS_ERR_MPI_NEGATIVE_VALUE if \p b is negative.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_mod_int( mbedtls_mpi_uint *r, const mbedtls_mpi *A,
                         mbedtls_mpi_sint b );

/**
 * \brief          Perform a sliding-window exponentiation: X = A^E mod N
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The base of the exponentiation.
 *                 This must point to an initialized MPI.
 * \param E        The exponent MPI. This must point to an initialized MPI.
 * \param N        The base for the modular reduction. This must point to an
 *                 initialized MPI.
 * \param prec_RR  A helper MPI depending solely on \p N which can be used to
 *                 speed-up multiple modular exponentiations for the same value
 *                 of \p N. This may be \c NULL. If it is not \c NULL, it must
 *                 point to an initialized MPI. If it hasn't been used after
 *                 the call to mbedtls_mpi_init(), this function will compute
 *                 the helper value and store it in \p prec_RR for reuse on
 *                 subsequent calls to this function. Otherwise, the function
 *                 will assume that \p prec_RR holds the helper value set by a
 *                 previous call to mbedtls_mpi_exp_mod(), and reuse it.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \c N is negative or
 *                 even, or if \c E is negative.
 * \return         Another negative error code on different kinds of failures.
 *
 */
int mbedtls_mpi_exp_mod( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *E, const mbedtls_mpi *N,
                         mbedtls_mpi *prec_RR );

/**
 * \brief          Fill an MPI with a number of random bytes.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param size     The number of random bytes to generate.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng. This may be
 *                 \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on failure.
 *
 * \note           The bytes obtained from the RNG are interpreted
 *                 as a big-endian representation of an MPI; this can
 *                 be relevant in applications like deterministic ECDSA.
 */
int mbedtls_mpi_fill_random( mbedtls_mpi *X, size_t size,
                     int (*f_rng)(void *, unsigned char *, size_t),
                     void *p_rng );

/** Generate a random number uniformly in a range.
 *
 * This function generates a random number between \p min inclusive and
 * \p N exclusive.
 *
 * The procedure complies with RFC 6979 §3.3 (deterministic ECDSA)
 * when the RNG is a suitably parametrized instance of HMAC_DRBG
 * and \p min is \c 1.
 *
 * \note           There are `N - min` possible outputs. The lower bound
 *                 \p min can be reached, but the upper bound \p N cannot.
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param min      The minimum value to return.
 *                 It must be nonnegative.
 * \param N        The upper bound of the range, exclusive.
 *                 In other words, this is one plus the maximum value to return.
 *                 \p N must be strictly larger than \p min.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p min or \p N is invalid
 *                 or if they are incompatible.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if the implementation was
 *                 unable to find a suitable value within a limited number
 *                 of attempts. This has a negligible probability if \p N
 *                 is significantly larger than \p min, which is the case
 *                 for all usual cryptographic applications.
 * \return         Another negative error code on failure.
 */
int mbedtls_mpi_random( mbedtls_mpi *X,
                        mbedtls_mpi_sint min,
                        const mbedtls_mpi *N,
                        int (*f_rng)(void *, unsigned char *, size_t),
                        void *p_rng );

/**
 * \brief          Compute the greatest common divisor: G = gcd(A, B)
 *
 * \param G        The destination MPI. This must point to an initialized MPI.
 * \param A        The first operand. This must point to an initialized MPI.
 * \param B        The second operand. This must point to an initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         Another negative error code on different kinds of failure.
 */
int mbedtls_mpi_gcd( mbedtls_mpi *G, const mbedtls_mpi *A,
                     const mbedtls_mpi *B );

/**
 * \brief          Compute the modular inverse: X = A^-1 mod N
 *
 * \param X        The destination MPI. This must point to an initialized MPI.
 * \param A        The MPI to calculate the modular inverse of. This must point
 *                 to an initialized MPI.
 * \param N        The base of the modular inversion. This must point to an
 *                 initialized MPI.
 *
 * \return         \c 0 if successful.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if \p N is less than
 *                 or equal to one.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if \p has no modular inverse
 *                 with respect to \p N.
 */
int mbedtls_mpi_inv_mod( mbedtls_mpi *X, const mbedtls_mpi *A,
                         const mbedtls_mpi *N );

/**
 * \brief          Miller-Rabin primality test.
 *
 * \warning        If \p X is potentially generated by an adversary, for example
 *                 when validating cryptographic parameters that you didn't
 *                 generate yourself and that are supposed to be prime, then
 *                 \p rounds should be at least the half of the security
 *                 strength of the cryptographic algorithm. On the other hand,
 *                 if \p X is chosen uniformly or non-adversarially (as is the
 *                 case when mbedtls_mpi_gen_prime calls this function), then
 *                 \p rounds can be much lower.
 *
 * \param X        The MPI to check for primality.
 *                 This must point to an initialized MPI.
 * \param rounds   The number of bases to perform the Miller-Rabin primality
 *                 test for. The probability of returning 0 on a composite is
 *                 at most 2<sup>-2*\p rounds</sup>.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *                 This may be \c NULL if \p f_rng doesn't use
 *                 a context parameter.
 *
 * \return         \c 0 if successful, i.e. \p X is probably prime.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_NOT_ACCEPTABLE if \p X is not prime.
 * \return         Another negative error code on other kinds of failure.
 */
int mbedtls_mpi_is_prime_ext( const mbedtls_mpi *X, int rounds,
                              int (*f_rng)(void *, unsigned char *, size_t),
                              void *p_rng );
/**
 * \brief Flags for mbedtls_mpi_gen_prime()
 *
 * Each of these flags is a constraint on the result X returned by
 * mbedtls_mpi_gen_prime().
 */
typedef enum {
    MBEDTLS_MPI_GEN_PRIME_FLAG_DH =      0x0001, /**< (X-1)/2 is prime too */
    MBEDTLS_MPI_GEN_PRIME_FLAG_LOW_ERR = 0x0002, /**< lower error rate from 2<sup>-80</sup> to 2<sup>-128</sup> */
} mbedtls_mpi_gen_prime_flag_t;

/**
 * \brief          Generate a prime number.
 *
 * \param X        The destination MPI to store the generated prime in.
 *                 This must point to an initialized MPi.
 * \param nbits    The required size of the destination MPI in bits.
 *                 This must be between \c 3 and #MBEDTLS_MPI_MAX_BITS.
 * \param flags    A mask of flags of type #mbedtls_mpi_gen_prime_flag_t.
 * \param f_rng    The RNG function to use. This must not be \c NULL.
 * \param p_rng    The RNG parameter to be passed to \p f_rng.
 *                 This may be \c NULL if \p f_rng doesn't use
 *                 a context parameter.
 *
 * \return         \c 0 if successful, in which case \p X holds a
 *                 probably prime number.
 * \return         #MBEDTLS_ERR_MPI_ALLOC_FAILED if a memory allocation failed.
 * \return         #MBEDTLS_ERR_MPI_BAD_INPUT_DATA if `nbits` is not between
 *                 \c 3 and #MBEDTLS_MPI_MAX_BITS.
 */
int mbedtls_mpi_gen_prime( mbedtls_mpi *X, size_t nbits, int flags,
                   int (*f_rng)(void *, unsigned char *, size_t),
                   void *p_rng );

# 1024 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/bignum.h"





# 41 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

/*
 * ECP error codes
 */
/** Bad input parameters to function. */

/** The buffer is too small to write to. */

/** The requested feature is not available, for example, the requested curve is not supported. */

/** The signature is not valid. */

/** Memory allocation failed. */

/** Generation of random value, such as ephemeral key, failed. */

/** Invalid private or public key. */

/** The buffer contains a valid signature followed by more data. */

/** Operation in progress, call again with the same parameters to continue. */


/* Flags indicating whether to include code that is specific to certain
 * types of curves. These flags are for internal library use only. */
	//ALIPAY_SUPPORT BEGIN
# 81 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"
//ALIPAY_SUPPORT END









/**
 * Domain-parameter identifiers: curve, subgroup, and generator.
 *
 * \note Only curves over prime fields are supported.
 *
 * \warning This library does not support validation of arbitrary domain
 * parameters. Therefore, only standardized domain parameters from trusted
 * sources should be used. See mbedtls_ecp_group_load().
 */
/* Note: when adding a new curve:
 * - Add it at the end of this enum, otherwise you'll break the ABI by
 *   changing the numerical value for existing curves.
 * - Increment MBEDTLS_ECP_DP_MAX below if needed.
 * - Update the calculation of MBEDTLS_ECP_MAX_BITS below.
 * - Add the corresponding MBEDTLS_ECP_DP_xxx_ENABLED macro definition to
 *   mbedtls_config.h.
 * - List the curve as a dependency of MBEDTLS_ECP_C and
 *   MBEDTLS_ECDSA_C if supported in check_config.h.
 * - Add the curve to the appropriate curve type macro
 *   MBEDTLS_ECP_yyy_ENABLED above.
 * - Add the necessary definitions to ecp_curves.c.
 * - Add the curve to the ecp_supported_curves array in ecp.c.
 * - Add the curve to applicable profiles in x509_crt.c.
 * - Add the curve to applicable presets in ssl_tls.c.
 */
typedef enum
{
    MBEDTLS_ECP_DP_NONE = 0,       /*!< Curve not defined. */
    MBEDTLS_ECP_DP_SECP192R1,      /*!< Domain parameters for the 192-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP224R1,      /*!< Domain parameters for the 224-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP256R1,      /*!< Domain parameters for the 256-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP384R1,      /*!< Domain parameters for the 384-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_SECP521R1,      /*!< Domain parameters for the 521-bit curve defined by FIPS 186-4 and SEC1. */
    MBEDTLS_ECP_DP_BP256R1,        /*!< Domain parameters for 256-bit Brainpool curve. */
    MBEDTLS_ECP_DP_BP384R1,        /*!< Domain parameters for 384-bit Brainpool curve. */
    MBEDTLS_ECP_DP_BP512R1,        /*!< Domain parameters for 512-bit Brainpool curve. */
    MBEDTLS_ECP_DP_CURVE25519,     /*!< Domain parameters for Curve25519. */
    MBEDTLS_ECP_DP_SECP192K1,      /*!< Domain parameters for 192-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_SECP224K1,      /*!< Domain parameters for 224-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_SECP256K1,      /*!< Domain parameters for 256-bit "Koblitz" curve. */
    MBEDTLS_ECP_DP_CURVE448,       /*!< Domain parameters for Curve448. */
} mbedtls_ecp_group_id;

/**
 * The number of supported curves, plus one for #MBEDTLS_ECP_DP_NONE.
 */


/*
 * Curve types
 */
typedef enum
{
    MBEDTLS_ECP_TYPE_NONE = 0,
    MBEDTLS_ECP_TYPE_SHORT_WEIERSTRASS,    /* y^2 = x^3 + a x + b      */
    MBEDTLS_ECP_TYPE_MONTGOMERY,           /* y^2 = x^3 + a x^2 + x    */
} mbedtls_ecp_curve_type;

/**
 * Curve information, for use by other modules.
 *
 * The fields of this structure are part of the public API and can be
 * accessed directly by applications. Future versions of the library may
 * add extra fields or reorder existing fields.
 */
typedef struct mbedtls_ecp_curve_info
{
    mbedtls_ecp_group_id grp_id;    /*!< An internal identifier. */
    uint16_t tls_id;                /*!< The TLS NamedCurve identifier. */
    uint16_t bit_size;              /*!< The curve size in bits. */
    const char *name;               /*!< A human-friendly name. */
} mbedtls_ecp_curve_info;

/**
 * \brief           The ECP point structure, in Jacobian coordinates.
 *
 * \note            All functions expect and return points satisfying
 *                  the following condition: <code>Z == 0</code> or
 *                  <code>Z == 1</code>. Other values of \p Z are
 *                  used only by internal functions.
 *                  The point is zero, or "at infinity", if <code>Z == 0</code>.
 *                  Otherwise, \p X and \p Y are its standard (affine)
 *                  coordinates.
 */
typedef struct mbedtls_ecp_point
{
    mbedtls_mpi X;          /*!< The X coordinate of the ECP point. */
    mbedtls_mpi Y;          /*!< The Y coordinate of the ECP point. */
    mbedtls_mpi Z;          /*!< The Z coordinate of the ECP point. */
}
mbedtls_ecp_point;


/*
 * default mbed TLS elliptic curve arithmetic implementation
 *
 * (in case MBEDTLS_ECP_ALT is defined then the developer has to provide an
 * alternative implementation for the whole module and it will replace this
 * one.)
 */

/**
 * \brief           The ECP group structure.
 *
 * We consider two types of curve equations:
 * <ul><li>Short Weierstrass: <code>y^2 = x^3 + A x + B mod P</code>
 * (SEC1 + RFC-4492)</li>
 * <li>Montgomery: <code>y^2 = x^3 + A x^2 + x mod P</code> (Curve25519,
 * Curve448)</li></ul>
 * In both cases, the generator (\p G) for a prime-order subgroup is fixed.
 *
 * For Short Weierstrass, this subgroup is the whole curve, and its
 * cardinality is denoted by \p N. Our code requires that \p N is an
 * odd prime as mbedtls_ecp_mul() requires an odd number, and
 * mbedtls_ecdsa_sign() requires that it is prime for blinding purposes.
 *
 * For Montgomery curves, we do not store \p A, but <code>(A + 2) / 4</code>,
 * which is the quantity used in the formulas. Additionally, \p nbits is
 * not the size of \p N but the required size for private keys.
 *
 * If \p modp is NULL, reduction modulo \p P is done using a generic algorithm.
 * Otherwise, \p modp must point to a function that takes an \p mbedtls_mpi in the
 * range of <code>0..2^(2*pbits)-1</code>, and transforms it in-place to an integer
 * which is congruent mod \p P to the given MPI, and is close enough to \p pbits
 * in size, so that it may be efficiently brought in the 0..P-1 range by a few
 * additions or subtractions. Therefore, it is only an approximative modular
 * reduction. It must return 0 on success and non-zero on failure.
 *
 * \note        Alternative implementations of the ECP module must obey the
 *              following constraints.
 *              * Group IDs must be distinct: if two group structures have
 *                the same ID, then they must be identical.
 *              * The fields \c id, \c P, \c A, \c B, \c G, \c N,
 *                \c pbits and \c nbits must have the same type and semantics
 *                as in the built-in implementation.
 *                They must be available for reading, but direct modification
 *                of these fields does not need to be supported.
 *                They do not need to be at the same offset in the structure.
 */
typedef struct mbedtls_ecp_group
{
    mbedtls_ecp_group_id id;    /*!< An internal group identifier. */
    mbedtls_mpi P;              /*!< The prime modulus of the base field. */
    mbedtls_mpi A;              /*!< For Short Weierstrass: \p A in the equation. For
                                     Montgomery curves: <code>(A + 2) / 4</code>. */
    mbedtls_mpi B;              /*!< For Short Weierstrass: \p B in the equation.
                                     For Montgomery curves: unused. */
    mbedtls_ecp_point G;        /*!< The generator of the subgroup used. */
    mbedtls_mpi N;              /*!< The order of \p G. */
    size_t pbits;               /*!< The number of bits in \p P.*/
    size_t nbits;               /*!< For Short Weierstrass: The number of bits in \p P.
                                     For Montgomery curves: the number of bits in the
                                     private keys. */
    /* End of public fields */

    unsigned int h;             /*!< \internal 1 if the constants are static. */
    int (*modp)(mbedtls_mpi *); /*!< The function for fast pseudo-reduction
                                     mod \p P (see above).*/
    int (*t_pre)(mbedtls_ecp_point *, void *);  /*!< Unused. */
    int (*t_post)(mbedtls_ecp_point *, void *); /*!< Unused. */
    void *t_data;               /*!< Unused. */
    mbedtls_ecp_point *T;       /*!< Pre-computed points for ecp_mul_comb(). */
    size_t T_size;              /*!< The number of dynamic allocated pre-computed points. */
}
mbedtls_ecp_group;

/**
 * \name SECTION: Module settings
 *
 * The configuration options you can set for this module are in this section.
 * Either change them in mbedtls_config.h, or define them using the compiler command line.
 * \{
 */


/*
 * Maximum "window" size used for point multiplication.
 * Default: a point where higher memory usage yields diminishing performance
 *          returns.
 * Minimum value: 2. Maximum value: 7.
 *
 * Result is an array of at most ( 1 << ( MBEDTLS_ECP_WINDOW_SIZE - 1 ) )
 * points used for point multiplication. This value is directly tied to EC
 * peak memory usage, so decreasing it by one should roughly cut memory usage
 * by two (if large curves are in use).
 *
 * Reduction in size may reduce speed, but larger curves are impacted first.
 * Sample performances (in ECDHE handshakes/s, with FIXED_POINT_OPTIM = 1):
 *      w-size:     6       5       4       3       2
 *      521       145     141     135     120      97
 *      384       214     209     198     177     146
 *      256       320     320     303     262     226
 *      224       475     475     453     398     342
 *      192       640     640     633     587     476
 */




/*
 * Trade code size for speed on fixed-point multiplication.
 *
 * This speeds up repeated multiplication of the generator (that is, the
 * multiplication in ECDSA signatures, and half of the multiplications in
 * ECDSA verification and ECDHE) by a factor roughly 3 to 4.
 *
 * For each n-bit Short Weierstrass curve that is enabled, this adds 4n bytes
 * of code size if n < 384 and 8n otherwise.
 *
 * Change this value to 0 to reduce code size.
 */



/** \} name SECTION: Module settings */





/**
 * The maximum size of the groups, that is, of \c N and \c P.
 */
# 322 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"
//ALIPAY_SUPPORT END
# 352 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"




# 412 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"



/* We want to declare restartable versions of existing functions anyway */
typedef void mbedtls_ecp_restart_ctx;



/**
 * \brief    The ECP key-pair structure.
 *
 * A generic key-pair that may be used for ECDSA and fixed ECDH, for example.
 *
 * \note    Members are deliberately in the same order as in the
 *          ::mbedtls_ecdsa_context structure.
 */
typedef struct mbedtls_ecp_keypair
{
    mbedtls_ecp_group grp;      /*!<  Elliptic curve and base point     */
    mbedtls_mpi d;              /*!<  our secret value                  */
    mbedtls_ecp_point Q;        /*!<  our public value                  */
}
mbedtls_ecp_keypair;

/*
 * Point formats, from RFC 4492's enum ECPointFormat
 */



/*
 * Some other constants from RFC 4492
 */


# 515 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

/*
 * Get the type of a curve
 */
mbedtls_ecp_curve_type mbedtls_ecp_get_type( const mbedtls_ecp_group *grp );

/**
 * \brief           This function retrieves the information defined in
 *                  mbedtls_ecp_curve_info() for all supported curves.
 *
 * \note            This function returns information about all curves
 *                  supported by the library. Some curves may not be
 *                  supported for all algorithms. Call mbedtls_ecdh_can_do()
 *                  or mbedtls_ecdsa_can_do() to check if a curve is
 *                  supported for ECDH or ECDSA.
 *
 * \return          A statically allocated array. The last entry is 0.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_list( void );

/**
 * \brief           This function retrieves the list of internal group
 *                  identifiers of all supported curves in the order of
 *                  preference.
 *
 * \note            This function returns information about all curves
 *                  supported by the library. Some curves may not be
 *                  supported for all algorithms. Call mbedtls_ecdh_can_do()
 *                  or mbedtls_ecdsa_can_do() to check if a curve is
 *                  supported for ECDH or ECDSA.
 *
 * \return          A statically allocated array,
 *                  terminated with MBEDTLS_ECP_DP_NONE.
 */
const mbedtls_ecp_group_id *mbedtls_ecp_grp_id_list( void );

/**
 * \brief           This function retrieves curve information from an internal
 *                  group identifier.
 *
 * \param grp_id    An \c MBEDTLS_ECP_DP_XXX value.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_grp_id( mbedtls_ecp_group_id grp_id );

/**
 * \brief           This function retrieves curve information from a TLS
 *                  NamedCurve value.
 *
 * \param tls_id    An \c MBEDTLS_ECP_DP_XXX value.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_tls_id( uint16_t tls_id );

/**
 * \brief           This function retrieves curve information from a
 *                  human-readable name.
 *
 * \param name      The human-readable name.
 *
 * \return          The associated curve information on success.
 * \return          NULL on failure.
 */
const mbedtls_ecp_curve_info *mbedtls_ecp_curve_info_from_name( const char *name );

/**
 * \brief           This function initializes a point as zero.
 *
 * \param pt        The point to initialize.
 */
void mbedtls_ecp_point_init( mbedtls_ecp_point *pt );

/**
 * \brief           This function initializes an ECP group context
 *                  without loading any domain parameters.
 *
 * \note            After this function is called, domain parameters
 *                  for various ECP groups can be loaded through the
 *                  mbedtls_ecp_group_load() or mbedtls_ecp_tls_read_group()
 *                  functions.
 */
void mbedtls_ecp_group_init( mbedtls_ecp_group *grp );

/**
 * \brief           This function initializes a key pair as an invalid one.
 *
 * \param key       The key pair to initialize.
 */
void mbedtls_ecp_keypair_init( mbedtls_ecp_keypair *key );

/**
 * \brief           This function frees the components of a point.
 *
 * \param pt        The point to free.
 */
void mbedtls_ecp_point_free( mbedtls_ecp_point *pt );

/**
 * \brief           This function frees the components of an ECP group.
 *
 * \param grp       The group to free. This may be \c NULL, in which
 *                  case this function returns immediately. If it is not
 *                  \c NULL, it must point to an initialized ECP group.
 */
void mbedtls_ecp_group_free( mbedtls_ecp_group *grp );

/**
 * \brief           This function frees the components of a key pair.
 *
 * \param key       The key pair to free. This may be \c NULL, in which
 *                  case this function returns immediately. If it is not
 *                  \c NULL, it must point to an initialized ECP key pair.
 */
void mbedtls_ecp_keypair_free( mbedtls_ecp_keypair *key );

# 652 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"

/**
 * \brief           This function copies the contents of point \p Q into
 *                  point \p P.
 *
 * \param P         The destination point. This must be initialized.
 * \param Q         The source point. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code for other kinds of failure.
 */
int mbedtls_ecp_copy( mbedtls_ecp_point *P, const mbedtls_ecp_point *Q );

/**
 * \brief           This function copies the contents of group \p src into
 *                  group \p dst.
 *
 * \param dst       The destination group. This must be initialized.
 * \param src       The source group. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_group_copy( mbedtls_ecp_group *dst,
                            const mbedtls_ecp_group *src );

/**
 * \brief           This function sets a point to the point at infinity.
 *
 * \param pt        The point to set. This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_set_zero( mbedtls_ecp_point *pt );

/**
 * \brief           This function checks if a point is the point at infinity.
 *
 * \param pt        The point to test. This must be initialized.
 *
 * \return          \c 1 if the point is zero.
 * \return          \c 0 if the point is non-zero.
 * \return          A negative error code on failure.
 */
int mbedtls_ecp_is_zero( mbedtls_ecp_point *pt );

/**
 * \brief           This function compares two points.
 *
 * \note            This assumes that the points are normalized. Otherwise,
 *                  they may compare as "not equal" even if they are.
 *
 * \param P         The first point to compare. This must be initialized.
 * \param Q         The second point to compare. This must be initialized.
 *
 * \return          \c 0 if the points are equal.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the points are not equal.
 */
int mbedtls_ecp_point_cmp( const mbedtls_ecp_point *P,
                           const mbedtls_ecp_point *Q );

/**
 * \brief           This function imports a non-zero point from two ASCII
 *                  strings.
 *
 * \param P         The destination point. This must be initialized.
 * \param radix     The numeric base of the input.
 * \param x         The first affine coordinate, as a null-terminated string.
 * \param y         The second affine coordinate, as a null-terminated string.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_MPI_XXX error code on failure.
 */
int mbedtls_ecp_point_read_string( mbedtls_ecp_point *P, int radix,
                           const char *x, const char *y );

/**
 * \brief           This function exports a point into unsigned binary data.
 *
 * \param grp       The group to which the point should belong.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param P         The point to export. This must be initialized.
 * \param format    The point format. This must be either
 *                  #MBEDTLS_ECP_PF_COMPRESSED or #MBEDTLS_ECP_PF_UNCOMPRESSED.
 *                  (For groups without these formats, this parameter is
 *                  ignored. But it still has to be either of the above
 *                  values.)
 * \param olen      The address at which to store the length of
 *                  the output in Bytes. This must not be \c NULL.
 * \param buf       The output buffer. This must be a writable buffer
 *                  of length \p buflen Bytes.
 * \param buflen    The length of the output buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the output buffer
 *                  is too small to hold the point.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the point format
 *                  or the export for the given group is not implemented.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_point_write_binary( const mbedtls_ecp_group *grp,
                                    const mbedtls_ecp_point *P,
                                    int format, size_t *olen,
                                    unsigned char *buf, size_t buflen );

/**
 * \brief           This function imports a point from unsigned binary data.
 *
 * \note            This function does not check that the point actually
 *                  belongs to the given group, see mbedtls_ecp_check_pubkey()
 *                  for that.
 *
 * \param grp       The group to which the point should belong.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param P         The destination context to import the point to.
 *                  This must be initialized.
 * \param buf       The input buffer. This must be a readable buffer
 *                  of length \p ilen Bytes.
 * \param ilen      The length of the input buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the input is invalid.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the import for the
 *                  given group is not implemented.
 */
int mbedtls_ecp_point_read_binary( const mbedtls_ecp_group *grp,
                                   mbedtls_ecp_point *P,
                                   const unsigned char *buf, size_t ilen );

/**
 * \brief           This function imports a point from a TLS ECPoint record.
 *
 * \note            On function return, \p *buf is updated to point immediately
 *                  after the ECPoint record.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The destination point.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the buffer.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_MPI_XXX error code on initialization
 *                  failure.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 */
int mbedtls_ecp_tls_read_point( const mbedtls_ecp_group *grp,
                                mbedtls_ecp_point *pt,
                                const unsigned char **buf, size_t len );

/**
 * \brief           This function exports a point as a TLS ECPoint record
 *                  defined in RFC 4492, Section 5.4.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The point to be exported. This must be initialized.
 * \param format    The point format to use. This must be either
 *                  #MBEDTLS_ECP_PF_COMPRESSED or #MBEDTLS_ECP_PF_UNCOMPRESSED.
 * \param olen      The address at which to store the length in Bytes
 *                  of the data written.
 * \param buf       The target buffer. This must be a writable buffer of
 *                  length \p blen Bytes.
 * \param blen      The length of the target buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the input is invalid.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the target buffer
 *                  is too small to hold the exported point.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_write_point( const mbedtls_ecp_group *grp,
                                 const mbedtls_ecp_point *pt,
                                 int format, size_t *olen,
                                 unsigned char *buf, size_t blen );

/**
 * \brief           This function sets up an ECP group context
 *                  from a standardized set of domain parameters.
 *
 * \note            The index should be a value of the NamedCurve enum,
 *                  as defined in <em>RFC-4492: Elliptic Curve Cryptography
 *                  (ECC) Cipher Suites for Transport Layer Security (TLS)</em>,
 *                  usually in the form of an \c MBEDTLS_ECP_DP_XXX macro.
 *
 * \param grp       The group context to setup. This must be initialized.
 * \param id        The identifier of the domain parameter set to load.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p id doesn't
 *                  correspond to a known group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_group_load( mbedtls_ecp_group *grp, mbedtls_ecp_group_id id );

/**
 * \brief           This function sets up an ECP group context from a TLS
 *                  ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \note            The read pointer \p buf is updated to point right after
 *                  the ECParameters record on exit.
 *
 * \param grp       The group context to setup. This must be initialized.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the input buffer \c *buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the group is not
 *                  recognized.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_read_group( mbedtls_ecp_group *grp,
                                const unsigned char **buf, size_t len );

/**
 * \brief           This function extracts an elliptic curve group ID from a
 *                  TLS ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \note            The read pointer \p buf is updated to point right after
 *                  the ECParameters record on exit.
 *
 * \param grp       The address at which to store the group id.
 *                  This must not be \c NULL.
 * \param buf       The address of the pointer to the start of the input buffer.
 * \param len       The length of the input buffer \c *buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if input is invalid.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the group is not
 *                  recognized.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_read_group_id( mbedtls_ecp_group_id *grp,
                                   const unsigned char **buf,
                                   size_t len );
/**
 * \brief           This function exports an elliptic curve as a TLS
 *                  ECParameters record as defined in RFC 4492, Section 5.4.
 *
 * \param grp       The ECP group to be exported.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param olen      The address at which to store the number of Bytes written.
 *                  This must not be \c NULL.
 * \param buf       The buffer to write to. This must be a writable buffer
 *                  of length \p blen Bytes.
 * \param blen      The length of the output buffer \p buf in Bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the output
 *                  buffer is too small to hold the exported group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_tls_write_group( const mbedtls_ecp_group *grp,
                                 size_t *olen,
                                 unsigned char *buf, size_t blen );

/**
 * \brief           This function performs a scalar multiplication of a point
 *                  by an integer: \p R = \p m * \p P.
 *
 *                  It is not thread-safe to use same group in multiple threads.
 *
 * \note            To prevent timing attacks, this function
 *                  executes the exact same sequence of base-field
 *                  operations for any valid \p m. It avoids any if-branch or
 *                  array index depending on the value of \p m. If also uses
 *                  \p f_rng to randomize some intermediate results.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply. This must be initialized.
 * \param P         The point to multiply. This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m is not a valid private
 *                  key, or \p P is not a valid public key.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_mul( mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             int (*f_rng)(void *, unsigned char *, size_t), void *p_rng );

/**
 * \brief           This function performs multiplication of a point by
 *                  an integer: \p R = \p m * \p P in a restartable way.
 *
 * \see             mbedtls_ecp_mul()
 *
 * \note            This function does the same as \c mbedtls_ecp_mul(), but
 *                  it can return early and restart according to the limit set
 *                  with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply. This must be initialized.
 * \param P         The point to multiply. This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 * \param rs_ctx    The restart context (NULL disables restart).
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m is not a valid private
 *                  key, or \p P is not a valid public key.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_mul_restartable( mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             int (*f_rng)(void *, unsigned char *, size_t), void *p_rng,
             mbedtls_ecp_restart_ctx *rs_ctx );


/**
 * \brief           This function performs multiplication and addition of two
 *                  points by integers: \p R = \p m * \p P + \p n * \p Q
 *
 *                  It is not thread-safe to use same group in multiple threads.
 *
 * \note            In contrast to mbedtls_ecp_mul(), this function does not
 *                  guarantee a constant execution flow and timing.
 *
 * \note            This function is only defined for short Weierstrass curves.
 *                  It may not be included in builds without any short
 *                  Weierstrass curve.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply \p P.
 *                  This must be initialized.
 * \param P         The point to multiply by \p m. This must be initialized.
 * \param n         The integer by which to multiply \p Q.
 *                  This must be initialized.
 * \param Q         The point to be multiplied by \p n.
 *                  This must be initialized.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m or \p n are not
 *                  valid private keys, or \p P or \p Q are not valid public
 *                  keys.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p grp does not
 *                  designate a short Weierstrass curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_muladd( mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             const mbedtls_mpi *n, const mbedtls_ecp_point *Q );

/**
 * \brief           This function performs multiplication and addition of two
 *                  points by integers: \p R = \p m * \p P + \p n * \p Q in a
 *                  restartable way.
 *
 * \see             \c mbedtls_ecp_muladd()
 *
 * \note            This function works the same as \c mbedtls_ecp_muladd(),
 *                  but it can return early and restart according to the limit
 *                  set with \c mbedtls_ecp_set_max_ops() to reduce blocking.
 *
 * \note            This function is only defined for short Weierstrass curves.
 *                  It may not be included in builds without any short
 *                  Weierstrass curve.
 *
 * \param grp       The ECP group to use.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param R         The point in which to store the result of the calculation.
 *                  This must be initialized.
 * \param m         The integer by which to multiply \p P.
 *                  This must be initialized.
 * \param P         The point to multiply by \p m. This must be initialized.
 * \param n         The integer by which to multiply \p Q.
 *                  This must be initialized.
 * \param Q         The point to be multiplied by \p n.
 *                  This must be initialized.
 * \param rs_ctx    The restart context (NULL disables restart).
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if \p m or \p n are not
 *                  valid private keys, or \p P or \p Q are not valid public
 *                  keys.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if \p grp does not
 *                  designate a short Weierstrass curve.
 * \return          #MBEDTLS_ERR_ECP_IN_PROGRESS if maximum number of
 *                  operations was reached: see \c mbedtls_ecp_set_max_ops().
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_muladd_restartable(
             mbedtls_ecp_group *grp, mbedtls_ecp_point *R,
             const mbedtls_mpi *m, const mbedtls_ecp_point *P,
             const mbedtls_mpi *n, const mbedtls_ecp_point *Q,
             mbedtls_ecp_restart_ctx *rs_ctx );


/**
 * \brief           This function checks that a point is a valid public key
 *                  on this curve.
 *
 *                  It only checks that the point is non-zero, has
 *                  valid coordinates and lies on the curve. It does not verify
 *                  that it is indeed a multiple of \p G. This additional
 *                  check is computationally more expensive, is not required
 *                  by standards, and should not be necessary if the group
 *                  used has a small cofactor. In particular, it is useless for
 *                  the NIST groups which all have a cofactor of 1.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure, to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group the point should belong to.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param pt        The point to check. This must be initialized.
 *
 * \return          \c 0 if the point is a valid public key.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if the point is not
 *                  a valid public key for the given curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_check_pubkey( const mbedtls_ecp_group *grp,
                              const mbedtls_ecp_point *pt );

/**
 * \brief           This function checks that an \p mbedtls_mpi is a
 *                  valid private key for this curve.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group the private key should belong to.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The integer to check. This must be initialized.
 *
 * \return          \c 0 if the point is a valid private key.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY if the point is not a valid
 *                  private key for the given curve.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_check_privkey( const mbedtls_ecp_group *grp,
                               const mbedtls_mpi *d );

/**
 * \brief           This function generates a private key.
 *
 * \param grp       The ECP group to generate a private key for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The destination MPI (secret part). This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG parameter to be passed to \p f_rng. This may be
 *                  \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_privkey( const mbedtls_ecp_group *grp,
                     mbedtls_mpi *d,
                     int (*f_rng)(void *, unsigned char *, size_t),
                     void *p_rng );

/**
 * \brief           This function generates a keypair with a configurable base
 *                  point.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group to generate a key pair for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param G         The base point to use. This must be initialized
 *                  and belong to \p grp. It replaces the default base
 *                  point \c grp->G used by mbedtls_ecp_gen_keypair().
 * \param d         The destination MPI (secret part).
 *                  This must be initialized.
 * \param Q         The destination point (public part).
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_keypair_base( mbedtls_ecp_group *grp,
                                  const mbedtls_ecp_point *G,
                                  mbedtls_mpi *d, mbedtls_ecp_point *Q,
                                  int (*f_rng)(void *, unsigned char *, size_t),
                                  void *p_rng );

/**
 * \brief           This function generates an ECP keypair.
 *
 * \note            This function uses bare components rather than an
 *                  ::mbedtls_ecp_keypair structure to ease use with other
 *                  structures, such as ::mbedtls_ecdh_context or
 *                  ::mbedtls_ecdsa_context.
 *
 * \param grp       The ECP group to generate a key pair for.
 *                  This must be initialized and have group parameters
 *                  set, for example through mbedtls_ecp_group_load().
 * \param d         The destination MPI (secret part).
 *                  This must be initialized.
 * \param Q         The destination point (public part).
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_keypair( mbedtls_ecp_group *grp, mbedtls_mpi *d,
                             mbedtls_ecp_point *Q,
                             int (*f_rng)(void *, unsigned char *, size_t),
                             void *p_rng );

/**
 * \brief           This function generates an ECP key.
 *
 * \param grp_id    The ECP group identifier.
 * \param key       The destination key. This must be initialized.
 * \param f_rng     The RNG function to use. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may
 *                  be \c NULL if \p f_rng doesn't need a context argument.
 *
 * \return          \c 0 on success.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or \c MBEDTLS_MPI_XXX error code
 *                  on failure.
 */
int mbedtls_ecp_gen_key( mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                         int (*f_rng)(void *, unsigned char *, size_t),
                         void *p_rng );

/**
 * \brief           This function reads an elliptic curve private key.
 *
 * \param grp_id    The ECP group identifier.
 * \param key       The destination key.
 * \param buf       The buffer containing the binary representation of the
 *                  key. (Big endian integer for Weierstrass curves, byte
 *                  string for Montgomery curves.)
 * \param buflen    The length of the buffer in bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_INVALID_KEY error if the key is
 *                  invalid.
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED if memory allocation failed.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the operation for
 *                  the group is not implemented.
 * \return          Another negative error code on different kinds of failure.
 */
int mbedtls_ecp_read_key( mbedtls_ecp_group_id grp_id, mbedtls_ecp_keypair *key,
                          const unsigned char *buf, size_t buflen );

/**
 * \brief           This function exports an elliptic curve private key.
 *
 * \param key       The private key.
 * \param buf       The output buffer for containing the binary representation
 *                  of the key. (Big endian integer for Weierstrass curves, byte
 *                  string for Montgomery curves.)
 * \param buflen    The total length of the buffer in bytes.
 *
 * \return          \c 0 on success.
 * \return          #MBEDTLS_ERR_ECP_BUFFER_TOO_SMALL if the \p key
                    representation is larger than the available space in \p buf.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if the operation for
 *                  the group is not implemented.
 * \return          Another negative error code on different kinds of failure.
 */
int mbedtls_ecp_write_key( mbedtls_ecp_keypair *key,
                           unsigned char *buf, size_t buflen );

/**
 * \brief           This function checks that the keypair objects
 *                  \p pub and \p prv have the same group and the
 *                  same public point, and that the private key in
 *                  \p prv is consistent with the public key.
 *
 * \param pub       The keypair structure holding the public key. This
 *                  must be initialized. If it contains a private key, that
 *                  part is ignored.
 * \param prv       The keypair structure holding the full keypair.
 *                  This must be initialized.
 * \param f_rng     The RNG function. This must not be \c NULL.
 * \param p_rng     The RNG context to be passed to \p f_rng. This may be \c
 *                  NULL if \p f_rng doesn't need a context.
 *
 * \return          \c 0 on success, meaning that the keys are valid and match.
 * \return          #MBEDTLS_ERR_ECP_BAD_INPUT_DATA if the keys are invalid or do not match.
 * \return          An \c MBEDTLS_ERR_ECP_XXX or an \c MBEDTLS_ERR_MPI_XXX
 *                  error code on calculation failure.
 */
int mbedtls_ecp_check_pub_priv(
        const mbedtls_ecp_keypair *pub, const mbedtls_ecp_keypair *prv,
        int (*f_rng)(void *, unsigned char *, size_t), void *p_rng );

/**
 * \brief           This function exports generic key-pair parameters.
 *
 * \param key       The key pair to export from.
 * \param grp       Slot for exported ECP group.
 *                  It must point to an initialized ECP group.
 * \param d         Slot for the exported secret value.
 *                  It must point to an initialized mpi.
 * \param Q         Slot for the exported public value.
 *                  It must point to an initialized ECP point.
 *
 * \return          \c 0 on success,
 * \return          #MBEDTLS_ERR_MPI_ALLOC_FAILED on memory-allocation failure.
 * \return          #MBEDTLS_ERR_ECP_FEATURE_UNAVAILABLE if key id doesn't
 *                  correspond to a known group.
 * \return          Another negative error code on other kinds of failure.
 */
int mbedtls_ecp_export(const mbedtls_ecp_keypair *key, mbedtls_ecp_group *grp,
                       mbedtls_mpi *d, mbedtls_ecp_point *Q);

# 1319 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/ecp.h"





# 25 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"
/**
 * \file platform_util.h
 *
 * \brief Common and shared functions used by multiple modules in the Mbed TLS
 *        library.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 27 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

# 29 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"









/* Internal macros meant to be called only from within the library. */



/* Internal helper macros for deprecating API constants. */
# 58 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

/* Implementation of the check-return facility.
 * See the user documentation in mbedtls_config.h.
 *
 * Do not use this macro directly to annotate function: instead,
 * use one of MBEDTLS_CHECK_RETURN_CRITICAL or MBEDTLS_CHECK_RETURN_TYPICAL
 * depending on how important it is to check the return value.
 */
# 76 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"

/** Critical-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be checked in all applications.
 * Omitting the check is very likely to indicate a bug in the application
 * and will result in a compile-time warning if #MBEDTLS_CHECK_RETURN
 * is implemented for the compiler in use.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro may be added to more functions in the future.
 *        Such an extension is not considered an API break, provided that
 *        there are near-unavoidable circumstances under which the function
 *        can fail. For example, signature/MAC/AEAD verification functions,
 *        and functions that require a random generator, are considered
 *        return-check-critical.
 */


/** Ordinary-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that its return value should be generally be checked in portable
 * applications. Omitting the check will result in a compile-time warning if
 * #MBEDTLS_CHECK_RETURN is implemented for the compiler in use and
 * #MBEDTLS_CHECK_RETURN_WARNING is enabled in the compile-time configuration.
 *
 * You can use #MBEDTLS_IGNORE_RETURN to explicitly ignore the return value
 * of a function that is annotated with #MBEDTLS_CHECK_RETURN.
 *
 * \note  The use of this macro is a work in progress.
 *        This macro will be added to more functions in the future.
 *        Eventually this should appear before most functions returning
 *        an error code (as \c int in the \c mbedtls_xxx API or
 *        as ::psa_status_t in the \c psa_xxx API).
 */






/** Benign-failure function
 *
 * This macro appearing at the beginning of the declaration of a function
 * indicates that it is rarely useful to check its return value.
 *
 * This macro has an empty expansion. It exists for documentation purposes:
 * a #MBEDTLS_CHECK_RETURN_OPTIONAL annotation indicates that the function
 * has been analyzed for return-check usefulness, whereas the lack of
 * an annotation indicates that the function has not been analyzed and its
 * return-check usefulness is unknown.
 */


/** \def MBEDTLS_IGNORE_RETURN
 *
 * Call this macro with one argument, a function call, to suppress a warning
 * from #MBEDTLS_CHECK_RETURN due to that function call.
 */

/* GCC doesn't silence the warning with just (void)(result).
 * (void)!(result) is known to work up at least up to GCC 10, as well
 * as with Clang and MSVC.
 *
 * https://gcc.gnu.org/onlinedocs/gcc-3.4.6/gcc/Non_002dbugs.html
 * https://stackoverflow.com/questions/40576003/ignoring-warning-wunused-result
 * https://gcc.gnu.org/bugzilla/show_bug.cgi?id=66425#c34
 */



/**
 * \brief       Securely zeroize a buffer
 *
 *              The function is meant to wipe the data contained in a buffer so
 *              that it can no longer be recovered even if the program memory
 *              is later compromised. Call this function on sensitive data
 *              stored on the stack before returning from a function, and on
 *              sensitive data stored on the heap before freeing the heap
 *              object.
 *
 *              It is extremely difficult to guarantee that calls to
 *              mbedtls_platform_zeroize() are not removed by aggressive
 *              compiler optimizations in a portable way. For this reason, Mbed
 *              TLS provides the configuration option
 *              MBEDTLS_PLATFORM_ZEROIZE_ALT, which allows users to configure
 *              mbedtls_platform_zeroize() to use a suitable implementation for
 *              their platform and needs
 *
 * \param buf   Buffer to be zeroized
 * \param len   Length of the buffer in bytes
 *
 */
void mbedtls_platform_zeroize( void *buf, size_t len );

# 202 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/platform_util.h"





# 26 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"
# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"
/**
 * \file error.h
 *
 * \brief Error to string translation
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 26 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"

# 28 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"






/**
 * Error code layout.
 *
 * Currently we try to keep all error codes within the negative space of 16
 * bits signed integers to support all platforms (-0x0001 - -0x7FFF). In
 * addition we'd like to give two layers of information on the error if
 * possible.
 *
 * For that purpose the error codes are segmented in the following manner:
 *
 * 16 bit error code bit-segmentation
 *
 * 1 bit  - Unused (sign bit)
 * 3 bits - High level module ID
 * 5 bits - Module-dependent error code
 * 7 bits - Low level module errors
 *
 * For historical reasons, low-level error codes are divided in even and odd,
 * even codes were assigned first, and -1 is reserved for other errors.
 *
 * Low-level module errors (0x0002-0x007E, 0x0001-0x007F)
 *
 * Module   Nr  Codes assigned
 * ERROR     2  0x006E          0x0001
 * MPI       7  0x0002-0x0010
 * GCM       3  0x0012-0x0016   0x0013-0x0013
 * THREADING 3  0x001A-0x001E
 * AES       5  0x0020-0x0022   0x0021-0x0025
 * CAMELLIA  3  0x0024-0x0026   0x0027-0x0027
 * BASE64    2  0x002A-0x002C
 * OID       1  0x002E-0x002E   0x000B-0x000B
 * PADLOCK   1  0x0030-0x0030
 * DES       2  0x0032-0x0032   0x0033-0x0033
 * CTR_DBRG  4  0x0034-0x003A
 * ENTROPY   3  0x003C-0x0040   0x003D-0x003F
 * NET      13  0x0042-0x0052   0x0043-0x0049
 * ARIA      4  0x0058-0x005E
 * ASN1      7  0x0060-0x006C
 * CMAC      1  0x007A-0x007A
 * PBKDF2    1  0x007C-0x007C
 * HMAC_DRBG 4                  0x0003-0x0009
 * CCM       3                  0x000D-0x0011
 * MD5       1                  0x002F-0x002F
 * RIPEMD160 1                  0x0031-0x0031
 * SHA1      1                  0x0035-0x0035 0x0073-0x0073
 * SHA256    1                  0x0037-0x0037 0x0074-0x0074
 * SHA512    1                  0x0039-0x0039 0x0075-0x0075
 * CHACHA20  3                  0x0051-0x0055
 * POLY1305  3                  0x0057-0x005B
 * CHACHAPOLY 2 0x0054-0x0056
 * PLATFORM  2  0x0070-0x0072
 *
 * High-level module nr (3 bits - 0x0...-0x7...)
 * Name      ID  Nr of Errors
 * PEM       1   9
 * PKCS#12   1   4 (Started from top)
 * X509      2   20
 * PKCS5     2   4 (Started from top)
 * DHM       3   11
 * PK        3   15 (Started from top)
 * RSA       4   11
 * ECP       4   10 (Started from top)
 * MD        5   5
 * HKDF      5   1 (Started from top)
 * SSL       5   2 (Started from 0x5F00)
 * CIPHER    6   8 (Started from 0x6080)
 * SSL       6   22 (Started from top, plus 0x6000)
 * SSL       7   20 (Started from 0x7000, gaps at
 *                   0x7380, 0x7900-0x7980, 0x7A80-0x7E80)
 *
 * Module dependent error code (5 bits 0x.00.-0x.F8.)
 */





/** Generic error */

/** This is a bug in the library */


/** Hardware accelerator failed */

/** The requested feature is not supported by the platform */


/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        Wrapper macro for mbedtls_error_add(). See that function for
 *        more details.
 */



# 137 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/error.h"

/**
 * \brief Combines a high-level and low-level error code together.
 *
 *        This function can be called directly however it is usually
 *        called via the #MBEDTLS_ERROR_ADD macro.
 *
 *        While a value of zero is not a negative error code, it is still an
 *        error code (that denotes success) and can be combined with both a
 *        negative error code or another value of zero.
 *
 * \note  When invasive testing is enabled via #MBEDTLS_TEST_HOOKS, also try to
 *        call \link mbedtls_test_hook_error_add \endlink.
 *
 * \param high      high-level error code. See error.h for more details.
 * \param low       low-level error code. See error.h for more details.
 * \param file      file where this error code addition occurred.
 * \param line      line where this error code addition occurred.
 */
static __inline int mbedtls_error_add( int high, int low,
                                     const char *file, int line )
{




    (void)file;
    (void)line;

    return( high + low );
}

/**
 * \brief Translate a mbed TLS error code into a string representation,
 *        Result is truncated if necessary and always includes a terminating
 *        null byte.
 *
 * \param errnum    error code
 * \param buffer    buffer to place representation in
 * \param buflen    length of the buffer
 */
void mbedtls_strerror( int errnum, char *buffer, size_t buflen );

/**
 * \brief Translate the high-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char * mbedtls_high_level_strerr( int error_code );

/**
 * \brief Translate the low-level part of an Mbed TLS error code into a string
 *        representation.
 *
 * This function returns a const pointer to an un-modifiable string. The caller
 * must not try to modify the string. It is intended to be used mostly for
 * logging purposes.
 *
 * \param error_code    error code
 *
 * \return The string representation of the error code, or \c NULL if the error
 *         code is unknown.
 */
const char * mbedtls_low_level_strerr( int error_code );





# 27 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"
/**
 * \file bn_mul.h
 *
 * \brief Multi-precision integer library
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */
/*
 *      Multiply source vector [s] with b, add result
 *       to destination vector [d] and set carry c.
 *
 *      Currently supports:
 *
 *         . IA-32 (386+)         . AMD64 / EM64T
 *         . IA-32 (SSE2)         . Motorola 68000
 *         . PowerPC, 32-bit      . MicroBlaze
 *         . PowerPC, 64-bit      . TriCore
 *         . SPARC v8             . ARM v3+
 *         . Alpha                . MIPS32
 *         . C, longlong          . C, generic
 */



# 40 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"

# 42 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"


/*
 * Conversion macros for embedded constants:
 * build lists of mbedtls_mpi_uint's from lists of unsigned char's grouped by 8, 4 or 2
 */















# 82 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"







/* armcc5 --gnu defines __GNUC__ but doesn't support GNU's extended asm */
# 878 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"

# 979 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"



# 1002 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"

# 1009 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"

# 1023 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bn_mul.h"

























# 29 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"
# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bignum_internal.h"
/**
 *  Internal bignum functions
 *
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




# 24 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bignum_internal.h"

# 27 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\bignum_internal.h"


/** Perform a known-size multiply accumulate operation
 *
 * Add \p b * \p s to \p d.
 *
 * \param[in,out] d     The pointer to the (little-endian) array
 *                      representing the bignum to accumulate onto.
 * \param d_len         The number of limbs of \p d. This must be
 *                      at least \p s_len.
 * \param[in] s         The pointer to the (little-endian) array
 *                      representing the bignum to multiply with.
 *                      This may be the same as \p d. Otherwise,
 *                      it must be disjoint from \p d.
 * \param s_len         The number of limbs of \p s.
 * \param b             A scalar to multiply with.
 *
 * \return c            The carry at the end of the operation.
 */
mbedtls_mpi_uint mbedtls_mpi_core_mla( mbedtls_mpi_uint *d, size_t d_len ,
                                       const mbedtls_mpi_uint *s, size_t s_len,
                                       mbedtls_mpi_uint b );

# 30 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"
# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_invasive.h"
/**
 * \file ecp_invasive.h
 *
 * \brief ECP module: interfaces for invasive testing only.
 *
 * The interfaces in this file are intended for testing purposes only.
 * They SHOULD NOT be made available in library integrations except when
 * building the library for testing.
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */



# 30 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_invasive.h"
# 31 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_invasive.h"
# 32 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_invasive.h"

# 80 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_invasive.h"

# 31 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 33 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"



/* Parameter validation macros based on platform_util.h */




















# 68 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"
/* For these curves, we build the group parameters dynamically. */

static mbedtls_mpi_uint mpi_one[] = {1};


/*
 * Note: the constants are in little-endian order
 * to be directly usable in MPIs
 */

/*
 * Domain parameters for secp192r1
 */
# 290 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

/*
 * Domain parameters for secp224r1
 */
# 540 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

/*
 * Domain parameters for secp256r1
 */

static const mbedtls_mpi_uint secp256r1_p[] = {
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ),
    ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ), ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ),
    ( (mbedtls_mpi_uint) (0x01) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_b[] = {
    ( (mbedtls_mpi_uint) (0x4B) << 0 ) | ( (mbedtls_mpi_uint) (0x60) << 8 ) | ( (mbedtls_mpi_uint) (0xD2) << 16 ) | ( (mbedtls_mpi_uint) (0x27) << 24 ), ( (mbedtls_mpi_uint) (0x3E) << 0 ) | ( (mbedtls_mpi_uint) (0x3C) << 8 ) | ( (mbedtls_mpi_uint) (0xCE) << 16 ) | ( (mbedtls_mpi_uint) (0x3B) << 24 ),
    ( (mbedtls_mpi_uint) (0xF6) << 0 ) | ( (mbedtls_mpi_uint) (0xB0) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0xCC) << 24 ), ( (mbedtls_mpi_uint) (0xB0) << 0 ) | ( (mbedtls_mpi_uint) (0x06) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x65) << 24 ),
    ( (mbedtls_mpi_uint) (0xBC) << 0 ) | ( (mbedtls_mpi_uint) (0x86) << 8 ) | ( (mbedtls_mpi_uint) (0x98) << 16 ) | ( (mbedtls_mpi_uint) (0x76) << 24 ), ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0xBD) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0xB3) << 24 ),
    ( (mbedtls_mpi_uint) (0xE7) << 0 ) | ( (mbedtls_mpi_uint) (0x93) << 8 ) | ( (mbedtls_mpi_uint) (0x3A) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ), ( (mbedtls_mpi_uint) (0xD8) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0xC6) << 16 ) | ( (mbedtls_mpi_uint) (0x5A) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_gx[] = {
    ( (mbedtls_mpi_uint) (0x96) << 0 ) | ( (mbedtls_mpi_uint) (0xC2) << 8 ) | ( (mbedtls_mpi_uint) (0x98) << 16 ) | ( (mbedtls_mpi_uint) (0xD8) << 24 ), ( (mbedtls_mpi_uint) (0x45) << 0 ) | ( (mbedtls_mpi_uint) (0x39) << 8 ) | ( (mbedtls_mpi_uint) (0xA1) << 16 ) | ( (mbedtls_mpi_uint) (0xF4) << 24 ),
    ( (mbedtls_mpi_uint) (0xA0) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ), ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x03) << 16 ) | ( (mbedtls_mpi_uint) (0x77) << 24 ),
    ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0x40) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0x63) << 24 ), ( (mbedtls_mpi_uint) (0xE5) << 0 ) | ( (mbedtls_mpi_uint) (0xE6) << 8 ) | ( (mbedtls_mpi_uint) (0xBC) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ),
    ( (mbedtls_mpi_uint) (0x47) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0x2C) << 16 ) | ( (mbedtls_mpi_uint) (0xE1) << 24 ), ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0xD1) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_gy[] = {
    ( (mbedtls_mpi_uint) (0xF5) << 0 ) | ( (mbedtls_mpi_uint) (0x51) << 8 ) | ( (mbedtls_mpi_uint) (0xBF) << 16 ) | ( (mbedtls_mpi_uint) (0x37) << 24 ), ( (mbedtls_mpi_uint) (0x68) << 0 ) | ( (mbedtls_mpi_uint) (0x40) << 8 ) | ( (mbedtls_mpi_uint) (0xB6) << 16 ) | ( (mbedtls_mpi_uint) (0xCB) << 24 ),
    ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0x5E) << 8 ) | ( (mbedtls_mpi_uint) (0x31) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ), ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0xCE) << 16 ) | ( (mbedtls_mpi_uint) (0x2B) << 24 ),
    ( (mbedtls_mpi_uint) (0x16) << 0 ) | ( (mbedtls_mpi_uint) (0x9E) << 8 ) | ( (mbedtls_mpi_uint) (0x0F) << 16 ) | ( (mbedtls_mpi_uint) (0x7C) << 24 ), ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0xEB) << 8 ) | ( (mbedtls_mpi_uint) (0xE7) << 16 ) | ( (mbedtls_mpi_uint) (0x8E) << 24 ),
    ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0x7F) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ), ( (mbedtls_mpi_uint) (0xE2) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_n[] = {
    ( (mbedtls_mpi_uint) (0x51) << 0 ) | ( (mbedtls_mpi_uint) (0x25) << 8 ) | ( (mbedtls_mpi_uint) (0x63) << 16 ) | ( (mbedtls_mpi_uint) (0xFC) << 24 ), ( (mbedtls_mpi_uint) (0xC2) << 0 ) | ( (mbedtls_mpi_uint) (0xCA) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0xF3) << 24 ),
    ( (mbedtls_mpi_uint) (0x84) << 0 ) | ( (mbedtls_mpi_uint) (0x9E) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0xA7) << 24 ), ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0xFA) << 8 ) | ( (mbedtls_mpi_uint) (0xE6) << 16 ) | ( (mbedtls_mpi_uint) (0xBC) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
};

static const mbedtls_mpi_uint secp256r1_T_0_X[] = {
    ( (mbedtls_mpi_uint) (0x96) << 0 ) | ( (mbedtls_mpi_uint) (0xC2) << 8 ) | ( (mbedtls_mpi_uint) (0x98) << 16 ) | ( (mbedtls_mpi_uint) (0xD8) << 24 ), ( (mbedtls_mpi_uint) (0x45) << 0 ) | ( (mbedtls_mpi_uint) (0x39) << 8 ) | ( (mbedtls_mpi_uint) (0xA1) << 16 ) | ( (mbedtls_mpi_uint) (0xF4) << 24 ),
    ( (mbedtls_mpi_uint) (0xA0) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ), ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x03) << 16 ) | ( (mbedtls_mpi_uint) (0x77) << 24 ),
    ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0x40) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0x63) << 24 ), ( (mbedtls_mpi_uint) (0xE5) << 0 ) | ( (mbedtls_mpi_uint) (0xE6) << 8 ) | ( (mbedtls_mpi_uint) (0xBC) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ),
    ( (mbedtls_mpi_uint) (0x47) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0x2C) << 16 ) | ( (mbedtls_mpi_uint) (0xE1) << 24 ), ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0xD1) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_0_Y[] = {
    ( (mbedtls_mpi_uint) (0xF5) << 0 ) | ( (mbedtls_mpi_uint) (0x51) << 8 ) | ( (mbedtls_mpi_uint) (0xBF) << 16 ) | ( (mbedtls_mpi_uint) (0x37) << 24 ), ( (mbedtls_mpi_uint) (0x68) << 0 ) | ( (mbedtls_mpi_uint) (0x40) << 8 ) | ( (mbedtls_mpi_uint) (0xB6) << 16 ) | ( (mbedtls_mpi_uint) (0xCB) << 24 ),
    ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0x5E) << 8 ) | ( (mbedtls_mpi_uint) (0x31) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ), ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0xCE) << 16 ) | ( (mbedtls_mpi_uint) (0x2B) << 24 ),
    ( (mbedtls_mpi_uint) (0x16) << 0 ) | ( (mbedtls_mpi_uint) (0x9E) << 8 ) | ( (mbedtls_mpi_uint) (0x0F) << 16 ) | ( (mbedtls_mpi_uint) (0x7C) << 24 ), ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0xEB) << 8 ) | ( (mbedtls_mpi_uint) (0xE7) << 16 ) | ( (mbedtls_mpi_uint) (0x8E) << 24 ),
    ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0x7F) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ), ( (mbedtls_mpi_uint) (0xE2) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_1_X[] = {
    ( (mbedtls_mpi_uint) (0x70) << 0 ) | ( (mbedtls_mpi_uint) (0xC8) << 8 ) | ( (mbedtls_mpi_uint) (0xBA) << 16 ) | ( (mbedtls_mpi_uint) (0x04) << 24 ), ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0x4B) << 8 ) | ( (mbedtls_mpi_uint) (0xD2) << 16 ) | ( (mbedtls_mpi_uint) (0xF7) << 24 ),
    ( (mbedtls_mpi_uint) (0xAB) << 0 ) | ( (mbedtls_mpi_uint) (0xC6) << 8 ) | ( (mbedtls_mpi_uint) (0x23) << 16 ) | ( (mbedtls_mpi_uint) (0x3A) << 24 ), ( (mbedtls_mpi_uint) (0xA0) << 0 ) | ( (mbedtls_mpi_uint) (0x09) << 8 ) | ( (mbedtls_mpi_uint) (0x3A) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ),
    ( (mbedtls_mpi_uint) (0x1D) << 0 ) | ( (mbedtls_mpi_uint) (0x9D) << 8 ) | ( (mbedtls_mpi_uint) (0x4C) << 16 ) | ( (mbedtls_mpi_uint) (0xF9) << 24 ), ( (mbedtls_mpi_uint) (0x58) << 0 ) | ( (mbedtls_mpi_uint) (0x23) << 8 ) | ( (mbedtls_mpi_uint) (0xCC) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ),
    ( (mbedtls_mpi_uint) (0x02) << 0 ) | ( (mbedtls_mpi_uint) (0xED) << 8 ) | ( (mbedtls_mpi_uint) (0x7B) << 16 ) | ( (mbedtls_mpi_uint) (0x29) << 24 ), ( (mbedtls_mpi_uint) (0x87) << 0 ) | ( (mbedtls_mpi_uint) (0x0F) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0x3C) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_1_Y[] = {
    ( (mbedtls_mpi_uint) (0x40) << 0 ) | ( (mbedtls_mpi_uint) (0x69) << 8 ) | ( (mbedtls_mpi_uint) (0xF2) << 16 ) | ( (mbedtls_mpi_uint) (0x40) << 24 ), ( (mbedtls_mpi_uint) (0x0B) << 0 ) | ( (mbedtls_mpi_uint) (0xA3) << 8 ) | ( (mbedtls_mpi_uint) (0x98) << 16 ) | ( (mbedtls_mpi_uint) (0xCE) << 24 ),
    ( (mbedtls_mpi_uint) (0xAF) << 0 ) | ( (mbedtls_mpi_uint) (0xA8) << 8 ) | ( (mbedtls_mpi_uint) (0x48) << 16 ) | ( (mbedtls_mpi_uint) (0x02) << 24 ), ( (mbedtls_mpi_uint) (0x0D) << 0 ) | ( (mbedtls_mpi_uint) (0x1C) << 8 ) | ( (mbedtls_mpi_uint) (0x12) << 16 ) | ( (mbedtls_mpi_uint) (0x62) << 24 ),
    ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0xAF) << 8 ) | ( (mbedtls_mpi_uint) (0x09) << 16 ) | ( (mbedtls_mpi_uint) (0x83) << 24 ), ( (mbedtls_mpi_uint) (0x80) << 0 ) | ( (mbedtls_mpi_uint) (0xAA) << 8 ) | ( (mbedtls_mpi_uint) (0x58) << 16 ) | ( (mbedtls_mpi_uint) (0xA7) << 24 ),
    ( (mbedtls_mpi_uint) (0xC6) << 0 ) | ( (mbedtls_mpi_uint) (0x12) << 8 ) | ( (mbedtls_mpi_uint) (0xBE) << 16 ) | ( (mbedtls_mpi_uint) (0x70) << 24 ), ( (mbedtls_mpi_uint) (0x94) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0xE4) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_2_X[] = {
    ( (mbedtls_mpi_uint) (0x7D) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0xEF) << 16 ) | ( (mbedtls_mpi_uint) (0x86) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xE3) << 8 ) | ( (mbedtls_mpi_uint) (0x37) << 16 ) | ( (mbedtls_mpi_uint) (0xDD) << 24 ),
    ( (mbedtls_mpi_uint) (0xDB) << 0 ) | ( (mbedtls_mpi_uint) (0x86) << 8 ) | ( (mbedtls_mpi_uint) (0x8B) << 16 ) | ( (mbedtls_mpi_uint) (0x08) << 24 ), ( (mbedtls_mpi_uint) (0x27) << 0 ) | ( (mbedtls_mpi_uint) (0x7C) << 8 ) | ( (mbedtls_mpi_uint) (0xD7) << 16 ) | ( (mbedtls_mpi_uint) (0xF6) << 24 ),
    ( (mbedtls_mpi_uint) (0x91) << 0 ) | ( (mbedtls_mpi_uint) (0x54) << 8 ) | ( (mbedtls_mpi_uint) (0x4C) << 16 ) | ( (mbedtls_mpi_uint) (0x25) << 24 ), ( (mbedtls_mpi_uint) (0x4F) << 0 ) | ( (mbedtls_mpi_uint) (0x9A) << 8 ) | ( (mbedtls_mpi_uint) (0xFE) << 16 ) | ( (mbedtls_mpi_uint) (0x28) << 24 ),
    ( (mbedtls_mpi_uint) (0x5E) << 0 ) | ( (mbedtls_mpi_uint) (0xFD) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ), ( (mbedtls_mpi_uint) (0x37) << 0 ) | ( (mbedtls_mpi_uint) (0x03) << 8 ) | ( (mbedtls_mpi_uint) (0x69) << 16 ) | ( (mbedtls_mpi_uint) (0xD6) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_2_Y[] = {
    ( (mbedtls_mpi_uint) (0x96) << 0 ) | ( (mbedtls_mpi_uint) (0xD5) << 8 ) | ( (mbedtls_mpi_uint) (0xDA) << 16 ) | ( (mbedtls_mpi_uint) (0xAD) << 24 ), ( (mbedtls_mpi_uint) (0x92) << 0 ) | ( (mbedtls_mpi_uint) (0x49) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0x9F) << 24 ),
    ( (mbedtls_mpi_uint) (0xF9) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0x43) << 16 ) | ( (mbedtls_mpi_uint) (0x9E) << 24 ), ( (mbedtls_mpi_uint) (0xAF) << 0 ) | ( (mbedtls_mpi_uint) (0xA7) << 8 ) | ( (mbedtls_mpi_uint) (0xD1) << 16 ) | ( (mbedtls_mpi_uint) (0xF3) << 24 ),
    ( (mbedtls_mpi_uint) (0x67) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x07) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ), ( (mbedtls_mpi_uint) (0x78) << 0 ) | ( (mbedtls_mpi_uint) (0x95) << 8 ) | ( (mbedtls_mpi_uint) (0x3E) << 16 ) | ( (mbedtls_mpi_uint) (0xA1) << 24 ),
    ( (mbedtls_mpi_uint) (0x22) << 0 ) | ( (mbedtls_mpi_uint) (0x3D) << 8 ) | ( (mbedtls_mpi_uint) (0xD1) << 16 ) | ( (mbedtls_mpi_uint) (0xE6) << 24 ), ( (mbedtls_mpi_uint) (0x3C) << 0 ) | ( (mbedtls_mpi_uint) (0xA5) << 8 ) | ( (mbedtls_mpi_uint) (0xE2) << 16 ) | ( (mbedtls_mpi_uint) (0x20) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_3_X[] = {
    ( (mbedtls_mpi_uint) (0xBF) << 0 ) | ( (mbedtls_mpi_uint) (0x6A) << 8 ) | ( (mbedtls_mpi_uint) (0x5D) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ), ( (mbedtls_mpi_uint) (0x35) << 0 ) | ( (mbedtls_mpi_uint) (0xD7) << 8 ) | ( (mbedtls_mpi_uint) (0xBF) << 16 ) | ( (mbedtls_mpi_uint) (0xAE) << 24 ),
    ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0xA2) << 8 ) | ( (mbedtls_mpi_uint) (0xBE) << 16 ) | ( (mbedtls_mpi_uint) (0x96) << 24 ), ( (mbedtls_mpi_uint) (0xF4) << 0 ) | ( (mbedtls_mpi_uint) (0xF8) << 8 ) | ( (mbedtls_mpi_uint) (0x02) << 16 ) | ( (mbedtls_mpi_uint) (0xC3) << 24 ),
    ( (mbedtls_mpi_uint) (0xA4) << 0 ) | ( (mbedtls_mpi_uint) (0x20) << 8 ) | ( (mbedtls_mpi_uint) (0x49) << 16 ) | ( (mbedtls_mpi_uint) (0x54) << 24 ), ( (mbedtls_mpi_uint) (0xEA) << 0 ) | ( (mbedtls_mpi_uint) (0xB3) << 8 ) | ( (mbedtls_mpi_uint) (0x82) << 16 ) | ( (mbedtls_mpi_uint) (0xDB) << 24 ),
    ( (mbedtls_mpi_uint) (0x2E) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0xEA) << 16 ) | ( (mbedtls_mpi_uint) (0x02) << 24 ), ( (mbedtls_mpi_uint) (0xD1) << 0 ) | ( (mbedtls_mpi_uint) (0x75) << 8 ) | ( (mbedtls_mpi_uint) (0x1C) << 16 ) | ( (mbedtls_mpi_uint) (0x62) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_3_Y[] = {
    ( (mbedtls_mpi_uint) (0xF0) << 0 ) | ( (mbedtls_mpi_uint) (0x85) << 8 ) | ( (mbedtls_mpi_uint) (0xF4) << 16 ) | ( (mbedtls_mpi_uint) (0x9E) << 24 ), ( (mbedtls_mpi_uint) (0x4C) << 0 ) | ( (mbedtls_mpi_uint) (0xDC) << 8 ) | ( (mbedtls_mpi_uint) (0x39) << 16 ) | ( (mbedtls_mpi_uint) (0x89) << 24 ),
    ( (mbedtls_mpi_uint) (0x63) << 0 ) | ( (mbedtls_mpi_uint) (0x6D) << 8 ) | ( (mbedtls_mpi_uint) (0xC4) << 16 ) | ( (mbedtls_mpi_uint) (0x57) << 24 ), ( (mbedtls_mpi_uint) (0xD8) << 0 ) | ( (mbedtls_mpi_uint) (0x03) << 8 ) | ( (mbedtls_mpi_uint) (0x5D) << 16 ) | ( (mbedtls_mpi_uint) (0x22) << 24 ),
    ( (mbedtls_mpi_uint) (0x70) << 0 ) | ( (mbedtls_mpi_uint) (0x7F) << 8 ) | ( (mbedtls_mpi_uint) (0x2D) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ), ( (mbedtls_mpi_uint) (0x6F) << 0 ) | ( (mbedtls_mpi_uint) (0xC9) << 8 ) | ( (mbedtls_mpi_uint) (0xDA) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ),
    ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0x64) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0xB4) << 24 ), ( (mbedtls_mpi_uint) (0xFE) << 0 ) | ( (mbedtls_mpi_uint) (0xA4) << 8 ) | ( (mbedtls_mpi_uint) (0xC4) << 16 ) | ( (mbedtls_mpi_uint) (0xD7) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_4_X[] = {
    ( (mbedtls_mpi_uint) (0x2A) << 0 ) | ( (mbedtls_mpi_uint) (0x37) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0xC0) << 24 ), ( (mbedtls_mpi_uint) (0xAA) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0xC6) << 16 ) | ( (mbedtls_mpi_uint) (0x8B) << 24 ),
    ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0x58) << 8 ) | ( (mbedtls_mpi_uint) (0xD9) << 16 ) | ( (mbedtls_mpi_uint) (0xED) << 24 ), ( (mbedtls_mpi_uint) (0x58) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0x65) << 16 ) | ( (mbedtls_mpi_uint) (0xF7) << 24 ),
    ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x26) << 16 ) | ( (mbedtls_mpi_uint) (0x8C) << 24 ), ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0xF9) << 8 ) | ( (mbedtls_mpi_uint) (0x05) << 16 ) | ( (mbedtls_mpi_uint) (0x9F) << 24 ),
    ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0x9A) << 16 ) | ( (mbedtls_mpi_uint) (0xC9) << 24 ), ( (mbedtls_mpi_uint) (0xE7) << 0 ) | ( (mbedtls_mpi_uint) (0x46) << 8 ) | ( (mbedtls_mpi_uint) (0xDC) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_4_Y[] = {
    ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0xD0) << 8 ) | ( (mbedtls_mpi_uint) (0x55) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ), ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x0A) << 8 ) | ( (mbedtls_mpi_uint) (0xF5) << 16 ) | ( (mbedtls_mpi_uint) (0x4A) << 24 ),
    ( (mbedtls_mpi_uint) (0x6A) << 0 ) | ( (mbedtls_mpi_uint) (0xBF) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0x81) << 24 ), ( (mbedtls_mpi_uint) (0x2D) << 0 ) | ( (mbedtls_mpi_uint) (0x20) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0xB5) << 24 ),
    ( (mbedtls_mpi_uint) (0x11) << 0 ) | ( (mbedtls_mpi_uint) (0xC1) << 8 ) | ( (mbedtls_mpi_uint) (0x28) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ), ( (mbedtls_mpi_uint) (0xAB) << 0 ) | ( (mbedtls_mpi_uint) (0xE3) << 8 ) | ( (mbedtls_mpi_uint) (0xD1) << 16 ) | ( (mbedtls_mpi_uint) (0x40) << 24 ),
    ( (mbedtls_mpi_uint) (0x24) << 0 ) | ( (mbedtls_mpi_uint) (0x34) << 8 ) | ( (mbedtls_mpi_uint) (0x79) << 16 ) | ( (mbedtls_mpi_uint) (0x45) << 24 ), ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0xA5) << 8 ) | ( (mbedtls_mpi_uint) (0x12) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_5_X[] = {
    ( (mbedtls_mpi_uint) (0xEE) << 0 ) | ( (mbedtls_mpi_uint) (0xCF) << 8 ) | ( (mbedtls_mpi_uint) (0xB8) << 16 ) | ( (mbedtls_mpi_uint) (0x7E) << 24 ), ( (mbedtls_mpi_uint) (0xF7) << 0 ) | ( (mbedtls_mpi_uint) (0x92) << 8 ) | ( (mbedtls_mpi_uint) (0x96) << 16 ) | ( (mbedtls_mpi_uint) (0x8D) << 24 ),
    ( (mbedtls_mpi_uint) (0x3D) << 0 ) | ( (mbedtls_mpi_uint) (0x01) << 8 ) | ( (mbedtls_mpi_uint) (0x8C) << 16 ) | ( (mbedtls_mpi_uint) (0x0D) << 24 ), ( (mbedtls_mpi_uint) (0x23) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0x05) << 24 ),
    ( (mbedtls_mpi_uint) (0x59) << 0 ) | ( (mbedtls_mpi_uint) (0x2E) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0x84) << 24 ), ( (mbedtls_mpi_uint) (0x52) << 0 ) | ( (mbedtls_mpi_uint) (0x7A) << 8 ) | ( (mbedtls_mpi_uint) (0x34) << 16 ) | ( (mbedtls_mpi_uint) (0x76) << 24 ),
    ( (mbedtls_mpi_uint) (0xE5) << 0 ) | ( (mbedtls_mpi_uint) (0xA1) << 8 ) | ( (mbedtls_mpi_uint) (0xB0) << 16 ) | ( (mbedtls_mpi_uint) (0x15) << 24 ), ( (mbedtls_mpi_uint) (0x90) << 0 ) | ( (mbedtls_mpi_uint) (0xE2) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0x3C) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_5_Y[] = {
    ( (mbedtls_mpi_uint) (0xD4) << 0 ) | ( (mbedtls_mpi_uint) (0x98) << 8 ) | ( (mbedtls_mpi_uint) (0xE7) << 16 ) | ( (mbedtls_mpi_uint) (0xFA) << 24 ), ( (mbedtls_mpi_uint) (0xA5) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x8B) << 16 ) | ( (mbedtls_mpi_uint) (0x53) << 24 ),
    ( (mbedtls_mpi_uint) (0x91) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0xD2) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ), ( (mbedtls_mpi_uint) (0xD1) << 0 ) | ( (mbedtls_mpi_uint) (0x1B) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0x1B) << 24 ),
    ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0x69) << 8 ) | ( (mbedtls_mpi_uint) (0x08) << 16 ) | ( (mbedtls_mpi_uint) (0x9A) << 24 ), ( (mbedtls_mpi_uint) (0x72) << 0 ) | ( (mbedtls_mpi_uint) (0xF0) << 8 ) | ( (mbedtls_mpi_uint) (0xA9) << 16 ) | ( (mbedtls_mpi_uint) (0x11) << 24 ),
    ( (mbedtls_mpi_uint) (0xB3) << 0 ) | ( (mbedtls_mpi_uint) (0xFE) << 8 ) | ( (mbedtls_mpi_uint) (0x0E) << 16 ) | ( (mbedtls_mpi_uint) (0x14) << 24 ), ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0x7C) << 8 ) | ( (mbedtls_mpi_uint) (0x0E) << 16 ) | ( (mbedtls_mpi_uint) (0xD3) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_6_X[] = {
    ( (mbedtls_mpi_uint) (0x83) << 0 ) | ( (mbedtls_mpi_uint) (0xF6) << 8 ) | ( (mbedtls_mpi_uint) (0xE8) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ), ( (mbedtls_mpi_uint) (0x87) << 0 ) | ( (mbedtls_mpi_uint) (0xF7) << 8 ) | ( (mbedtls_mpi_uint) (0xFC) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ),
    ( (mbedtls_mpi_uint) (0x90) << 0 ) | ( (mbedtls_mpi_uint) (0xBE) << 8 ) | ( (mbedtls_mpi_uint) (0x7F) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ), ( (mbedtls_mpi_uint) (0x7A) << 0 ) | ( (mbedtls_mpi_uint) (0x2B) << 8 ) | ( (mbedtls_mpi_uint) (0xD7) << 16 ) | ( (mbedtls_mpi_uint) (0x13) << 24 ),
    ( (mbedtls_mpi_uint) (0xCF) << 0 ) | ( (mbedtls_mpi_uint) (0x32) << 8 ) | ( (mbedtls_mpi_uint) (0xF2) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ), ( (mbedtls_mpi_uint) (0x94) << 0 ) | ( (mbedtls_mpi_uint) (0x6D) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0xFD) << 24 ),
    ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0x9A) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ), ( (mbedtls_mpi_uint) (0x42) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0x84) << 16 ) | ( (mbedtls_mpi_uint) (0xED) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_6_Y[] = {
    ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x95) << 8 ) | ( (mbedtls_mpi_uint) (0x29) << 16 ) | ( (mbedtls_mpi_uint) (0x73) << 24 ), ( (mbedtls_mpi_uint) (0xA1) << 0 ) | ( (mbedtls_mpi_uint) (0x67) << 8 ) | ( (mbedtls_mpi_uint) (0x3E) << 16 ) | ( (mbedtls_mpi_uint) (0x02) << 24 ),
    ( (mbedtls_mpi_uint) (0xE3) << 0 ) | ( (mbedtls_mpi_uint) (0x30) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x35) << 24 ), ( (mbedtls_mpi_uint) (0x8E) << 0 ) | ( (mbedtls_mpi_uint) (0x0A) << 8 ) | ( (mbedtls_mpi_uint) (0xDD) << 16 ) | ( (mbedtls_mpi_uint) (0x67) << 24 ),
    ( (mbedtls_mpi_uint) (0x03) << 0 ) | ( (mbedtls_mpi_uint) (0xD7) << 8 ) | ( (mbedtls_mpi_uint) (0xA1) << 16 ) | ( (mbedtls_mpi_uint) (0x97) << 24 ), ( (mbedtls_mpi_uint) (0x61) << 0 ) | ( (mbedtls_mpi_uint) (0x3B) << 8 ) | ( (mbedtls_mpi_uint) (0xF8) << 16 ) | ( (mbedtls_mpi_uint) (0x0C) << 24 ),
    ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0x3C) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ), ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0x34) << 8 ) | ( (mbedtls_mpi_uint) (0x23) << 16 ) | ( (mbedtls_mpi_uint) (0xA3) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_7_X[] = {
    ( (mbedtls_mpi_uint) (0x99) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0x16) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ), ( (mbedtls_mpi_uint) (0x7B) << 0 ) | ( (mbedtls_mpi_uint) (0xBC) << 8 ) | ( (mbedtls_mpi_uint) (0xBB) << 16 ) | ( (mbedtls_mpi_uint) (0xCE) << 24 ),
    ( (mbedtls_mpi_uint) (0x61) << 0 ) | ( (mbedtls_mpi_uint) (0xEE) << 8 ) | ( (mbedtls_mpi_uint) (0x4E) << 16 ) | ( (mbedtls_mpi_uint) (0x8A) << 24 ), ( (mbedtls_mpi_uint) (0xC1) << 0 ) | ( (mbedtls_mpi_uint) (0x51) << 8 ) | ( (mbedtls_mpi_uint) (0xCC) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ),
    ( (mbedtls_mpi_uint) (0x1F) << 0 ) | ( (mbedtls_mpi_uint) (0x0D) << 8 ) | ( (mbedtls_mpi_uint) (0x4D) << 16 ) | ( (mbedtls_mpi_uint) (0x1B) << 24 ), ( (mbedtls_mpi_uint) (0x53) << 0 ) | ( (mbedtls_mpi_uint) (0x23) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0xB3) << 24 ),
    ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0x2A) << 8 ) | ( (mbedtls_mpi_uint) (0x38) << 16 ) | ( (mbedtls_mpi_uint) (0x66) << 24 ), ( (mbedtls_mpi_uint) (0x52) << 0 ) | ( (mbedtls_mpi_uint) (0x84) << 8 ) | ( (mbedtls_mpi_uint) (0xE1) << 16 ) | ( (mbedtls_mpi_uint) (0x95) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_7_Y[] = {
    ( (mbedtls_mpi_uint) (0x5B) << 0 ) | ( (mbedtls_mpi_uint) (0x9B) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0x0A) << 24 ), ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0x4F) << 8 ) | ( (mbedtls_mpi_uint) (0xAD) << 16 ) | ( (mbedtls_mpi_uint) (0xAC) << 24 ),
    ( (mbedtls_mpi_uint) (0x0F) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0x41) << 24 ), ( (mbedtls_mpi_uint) (0x6E) << 0 ) | ( (mbedtls_mpi_uint) (0xA9) << 8 ) | ( (mbedtls_mpi_uint) (0xA2) << 16 ) | ( (mbedtls_mpi_uint) (0xA0) << 24 ),
    ( (mbedtls_mpi_uint) (0x2F) << 0 ) | ( (mbedtls_mpi_uint) (0xA1) << 8 ) | ( (mbedtls_mpi_uint) (0x4F) << 16 ) | ( (mbedtls_mpi_uint) (0x1F) << 24 ), ( (mbedtls_mpi_uint) (0x89) << 0 ) | ( (mbedtls_mpi_uint) (0x82) << 8 ) | ( (mbedtls_mpi_uint) (0xAA) << 16 ) | ( (mbedtls_mpi_uint) (0x3E) << 24 ),
    ( (mbedtls_mpi_uint) (0xF3) << 0 ) | ( (mbedtls_mpi_uint) (0xB8) << 8 ) | ( (mbedtls_mpi_uint) (0x0F) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ), ( (mbedtls_mpi_uint) (0x8F) << 0 ) | ( (mbedtls_mpi_uint) (0x8C) << 8 ) | ( (mbedtls_mpi_uint) (0xD6) << 16 ) | ( (mbedtls_mpi_uint) (0x68) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_8_X[] = {
    ( (mbedtls_mpi_uint) (0xF1) << 0 ) | ( (mbedtls_mpi_uint) (0xB3) << 8 ) | ( (mbedtls_mpi_uint) (0xBB) << 16 ) | ( (mbedtls_mpi_uint) (0x51) << 24 ), ( (mbedtls_mpi_uint) (0x69) << 0 ) | ( (mbedtls_mpi_uint) (0xA2) << 8 ) | ( (mbedtls_mpi_uint) (0x11) << 16 ) | ( (mbedtls_mpi_uint) (0x93) << 24 ),
    ( (mbedtls_mpi_uint) (0x65) << 0 ) | ( (mbedtls_mpi_uint) (0x4F) << 8 ) | ( (mbedtls_mpi_uint) (0x0F) << 16 ) | ( (mbedtls_mpi_uint) (0x8D) << 24 ), ( (mbedtls_mpi_uint) (0xBD) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0x0F) << 16 ) | ( (mbedtls_mpi_uint) (0xE8) << 24 ),
    ( (mbedtls_mpi_uint) (0xB9) << 0 ) | ( (mbedtls_mpi_uint) (0xCB) << 8 ) | ( (mbedtls_mpi_uint) (0xEC) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ), ( (mbedtls_mpi_uint) (0x34) << 0 ) | ( (mbedtls_mpi_uint) (0xC3) << 8 ) | ( (mbedtls_mpi_uint) (0x3D) << 16 ) | ( (mbedtls_mpi_uint) (0x9D) << 24 ),
    ( (mbedtls_mpi_uint) (0xE4) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0x1E) << 16 ) | ( (mbedtls_mpi_uint) (0x10) << 24 ), ( (mbedtls_mpi_uint) (0xD5) << 0 ) | ( (mbedtls_mpi_uint) (0x44) << 8 ) | ( (mbedtls_mpi_uint) (0xE2) << 16 ) | ( (mbedtls_mpi_uint) (0x54) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_8_Y[] = {
    ( (mbedtls_mpi_uint) (0x28) << 0 ) | ( (mbedtls_mpi_uint) (0x9E) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0xF1) << 24 ), ( (mbedtls_mpi_uint) (0x6E) << 0 ) | ( (mbedtls_mpi_uint) (0x4C) << 8 ) | ( (mbedtls_mpi_uint) (0xAD) << 16 ) | ( (mbedtls_mpi_uint) (0xB3) << 24 ),
    ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0xE3) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ), ( (mbedtls_mpi_uint) (0xC0) << 0 ) | ( (mbedtls_mpi_uint) (0xFB) << 8 ) | ( (mbedtls_mpi_uint) (0x34) << 16 ) | ( (mbedtls_mpi_uint) (0x43) << 24 ),
    ( (mbedtls_mpi_uint) (0x25) << 0 ) | ( (mbedtls_mpi_uint) (0x9C) << 8 ) | ( (mbedtls_mpi_uint) (0xDF) << 16 ) | ( (mbedtls_mpi_uint) (0x35) << 24 ), ( (mbedtls_mpi_uint) (0x07) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0xBD) << 16 ) | ( (mbedtls_mpi_uint) (0x19) << 24 ),
    ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x6E) << 8 ) | ( (mbedtls_mpi_uint) (0x10) << 16 ) | ( (mbedtls_mpi_uint) (0xEC) << 24 ), ( (mbedtls_mpi_uint) (0x0E) << 0 ) | ( (mbedtls_mpi_uint) (0xEC) << 8 ) | ( (mbedtls_mpi_uint) (0xBB) << 16 ) | ( (mbedtls_mpi_uint) (0xD6) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_9_X[] = {
    ( (mbedtls_mpi_uint) (0xC8) << 0 ) | ( (mbedtls_mpi_uint) (0xCF) << 8 ) | ( (mbedtls_mpi_uint) (0xEF) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ), ( (mbedtls_mpi_uint) (0x83) << 0 ) | ( (mbedtls_mpi_uint) (0x1A) << 8 ) | ( (mbedtls_mpi_uint) (0x88) << 16 ) | ( (mbedtls_mpi_uint) (0xE8) << 24 ),
    ( (mbedtls_mpi_uint) (0x0B) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0xB5) << 16 ) | ( (mbedtls_mpi_uint) (0xB9) << 24 ), ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0xC9) << 8 ) | ( (mbedtls_mpi_uint) (0xA3) << 16 ) | ( (mbedtls_mpi_uint) (0xAE) << 24 ),
    ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0x46) << 8 ) | ( (mbedtls_mpi_uint) (0x1E) << 16 ) | ( (mbedtls_mpi_uint) (0x77) << 24 ), ( (mbedtls_mpi_uint) (0xCD) << 0 ) | ( (mbedtls_mpi_uint) (0x7E) << 8 ) | ( (mbedtls_mpi_uint) (0xB3) << 16 ) | ( (mbedtls_mpi_uint) (0x10) << 24 ),
    ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0xD4) << 24 ), ( (mbedtls_mpi_uint) (0xA3) << 0 ) | ( (mbedtls_mpi_uint) (0x16) << 8 ) | ( (mbedtls_mpi_uint) (0x08) << 16 ) | ( (mbedtls_mpi_uint) (0xEE) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_9_Y[] = {
    ( (mbedtls_mpi_uint) (0xA1) << 0 ) | ( (mbedtls_mpi_uint) (0xCA) << 8 ) | ( (mbedtls_mpi_uint) (0xA8) << 16 ) | ( (mbedtls_mpi_uint) (0xB3) << 24 ), ( (mbedtls_mpi_uint) (0xBF) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x8E) << 24 ),
    ( (mbedtls_mpi_uint) (0xD1) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0x05) << 16 ) | ( (mbedtls_mpi_uint) (0xC1) << 24 ), ( (mbedtls_mpi_uint) (0xCF) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0x91) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ),
    ( (mbedtls_mpi_uint) (0x9F) << 0 ) | ( (mbedtls_mpi_uint) (0x01) << 8 ) | ( (mbedtls_mpi_uint) (0x49) << 16 ) | ( (mbedtls_mpi_uint) (0xDB) << 24 ), ( (mbedtls_mpi_uint) (0x82) << 0 ) | ( (mbedtls_mpi_uint) (0xDF) << 8 ) | ( (mbedtls_mpi_uint) (0x5F) << 16 ) | ( (mbedtls_mpi_uint) (0x3A) << 24 ),
    ( (mbedtls_mpi_uint) (0xE1) << 0 ) | ( (mbedtls_mpi_uint) (0x06) << 8 ) | ( (mbedtls_mpi_uint) (0x90) << 16 ) | ( (mbedtls_mpi_uint) (0xAD) << 24 ), ( (mbedtls_mpi_uint) (0xE3) << 0 ) | ( (mbedtls_mpi_uint) (0x38) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0xC4) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_10_X[] = {
    ( (mbedtls_mpi_uint) (0xC9) << 0 ) | ( (mbedtls_mpi_uint) (0xD2) << 8 ) | ( (mbedtls_mpi_uint) (0x3A) << 16 ) | ( (mbedtls_mpi_uint) (0xE8) << 24 ), ( (mbedtls_mpi_uint) (0x03) << 0 ) | ( (mbedtls_mpi_uint) (0xC5) << 8 ) | ( (mbedtls_mpi_uint) (0x6D) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ),
    ( (mbedtls_mpi_uint) (0xBE) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0xAE) << 24 ), ( (mbedtls_mpi_uint) (0x1D) << 0 ) | ( (mbedtls_mpi_uint) (0x7A) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0xCA) << 24 ),
    ( (mbedtls_mpi_uint) (0x33) << 0 ) | ( (mbedtls_mpi_uint) (0x1E) << 8 ) | ( (mbedtls_mpi_uint) (0xD2) << 16 ) | ( (mbedtls_mpi_uint) (0xCB) << 24 ), ( (mbedtls_mpi_uint) (0xAC) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0x27) << 16 ) | ( (mbedtls_mpi_uint) (0x55) << 24 ),
    ( (mbedtls_mpi_uint) (0xF0) << 0 ) | ( (mbedtls_mpi_uint) (0xB9) << 8 ) | ( (mbedtls_mpi_uint) (0x9C) << 16 ) | ( (mbedtls_mpi_uint) (0xE0) << 24 ), ( (mbedtls_mpi_uint) (0x31) << 0 ) | ( (mbedtls_mpi_uint) (0xDD) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x86) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_10_Y[] = {
    ( (mbedtls_mpi_uint) (0x61) << 0 ) | ( (mbedtls_mpi_uint) (0xF9) << 8 ) | ( (mbedtls_mpi_uint) (0x9B) << 16 ) | ( (mbedtls_mpi_uint) (0x32) << 24 ), ( (mbedtls_mpi_uint) (0x96) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x58) << 16 ) | ( (mbedtls_mpi_uint) (0x38) << 24 ),
    ( (mbedtls_mpi_uint) (0xF9) << 0 ) | ( (mbedtls_mpi_uint) (0x5A) << 8 ) | ( (mbedtls_mpi_uint) (0x2A) << 16 ) | ( (mbedtls_mpi_uint) (0xB8) << 24 ), ( (mbedtls_mpi_uint) (0x96) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0xB2) << 16 ) | ( (mbedtls_mpi_uint) (0x4C) << 24 ),
    ( (mbedtls_mpi_uint) (0xC1) << 0 ) | ( (mbedtls_mpi_uint) (0x78) << 8 ) | ( (mbedtls_mpi_uint) (0x2C) << 16 ) | ( (mbedtls_mpi_uint) (0xC7) << 24 ), ( (mbedtls_mpi_uint) (0x08) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0x19) << 16 ) | ( (mbedtls_mpi_uint) (0x24) << 24 ),
    ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0x28) << 16 ) | ( (mbedtls_mpi_uint) (0xE9) << 24 ), ( (mbedtls_mpi_uint) (0x84) << 0 ) | ( (mbedtls_mpi_uint) (0x54) << 8 ) | ( (mbedtls_mpi_uint) (0xE6) << 16 ) | ( (mbedtls_mpi_uint) (0x16) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_11_X[] = {
    ( (mbedtls_mpi_uint) (0xDD) << 0 ) | ( (mbedtls_mpi_uint) (0x38) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0xDB) << 24 ), ( (mbedtls_mpi_uint) (0x70) << 0 ) | ( (mbedtls_mpi_uint) (0x2C) << 8 ) | ( (mbedtls_mpi_uint) (0x0A) << 16 ) | ( (mbedtls_mpi_uint) (0xA2) << 24 ),
    ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0x5C) << 8 ) | ( (mbedtls_mpi_uint) (0x9D) << 16 ) | ( (mbedtls_mpi_uint) (0xE9) << 24 ), ( (mbedtls_mpi_uint) (0xD5) << 0 ) | ( (mbedtls_mpi_uint) (0x46) << 8 ) | ( (mbedtls_mpi_uint) (0x0B) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ),
    ( (mbedtls_mpi_uint) (0x83) << 0 ) | ( (mbedtls_mpi_uint) (0x0B) << 8 ) | ( (mbedtls_mpi_uint) (0x60) << 16 ) | ( (mbedtls_mpi_uint) (0x4B) << 24 ), ( (mbedtls_mpi_uint) (0x37) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0xC9) << 24 ),
    ( (mbedtls_mpi_uint) (0x5E) << 0 ) | ( (mbedtls_mpi_uint) (0x24) << 8 ) | ( (mbedtls_mpi_uint) (0xF3) << 16 ) | ( (mbedtls_mpi_uint) (0x3D) << 24 ), ( (mbedtls_mpi_uint) (0x79) << 0 ) | ( (mbedtls_mpi_uint) (0x7F) << 8 ) | ( (mbedtls_mpi_uint) (0x6C) << 16 ) | ( (mbedtls_mpi_uint) (0x18) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_11_Y[] = {
    ( (mbedtls_mpi_uint) (0x7F) << 0 ) | ( (mbedtls_mpi_uint) (0xE5) << 8 ) | ( (mbedtls_mpi_uint) (0x1C) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ), ( (mbedtls_mpi_uint) (0x60) << 0 ) | ( (mbedtls_mpi_uint) (0x24) << 8 ) | ( (mbedtls_mpi_uint) (0xF7) << 16 ) | ( (mbedtls_mpi_uint) (0x2A) << 24 ),
    ( (mbedtls_mpi_uint) (0xED) << 0 ) | ( (mbedtls_mpi_uint) (0xD8) << 8 ) | ( (mbedtls_mpi_uint) (0xE2) << 16 ) | ( (mbedtls_mpi_uint) (0x91) << 24 ), ( (mbedtls_mpi_uint) (0x7F) << 0 ) | ( (mbedtls_mpi_uint) (0x89) << 8 ) | ( (mbedtls_mpi_uint) (0x49) << 16 ) | ( (mbedtls_mpi_uint) (0x92) << 24 ),
    ( (mbedtls_mpi_uint) (0x97) << 0 ) | ( (mbedtls_mpi_uint) (0xA7) << 8 ) | ( (mbedtls_mpi_uint) (0x2E) << 16 ) | ( (mbedtls_mpi_uint) (0x8D) << 24 ), ( (mbedtls_mpi_uint) (0x6A) << 0 ) | ( (mbedtls_mpi_uint) (0xB3) << 8 ) | ( (mbedtls_mpi_uint) (0x39) << 16 ) | ( (mbedtls_mpi_uint) (0x81) << 24 ),
    ( (mbedtls_mpi_uint) (0x13) << 0 ) | ( (mbedtls_mpi_uint) (0x89) << 8 ) | ( (mbedtls_mpi_uint) (0xB5) << 16 ) | ( (mbedtls_mpi_uint) (0x9A) << 24 ), ( (mbedtls_mpi_uint) (0xB8) << 0 ) | ( (mbedtls_mpi_uint) (0x8D) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0x9C) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_12_X[] = {
    ( (mbedtls_mpi_uint) (0x8D) << 0 ) | ( (mbedtls_mpi_uint) (0x45) << 8 ) | ( (mbedtls_mpi_uint) (0xE6) << 16 ) | ( (mbedtls_mpi_uint) (0x4B) << 24 ), ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0x4F) << 8 ) | ( (mbedtls_mpi_uint) (0x1E) << 16 ) | ( (mbedtls_mpi_uint) (0x1F) << 24 ),
    ( (mbedtls_mpi_uint) (0x47) << 0 ) | ( (mbedtls_mpi_uint) (0x65) << 8 ) | ( (mbedtls_mpi_uint) (0x5E) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ), ( (mbedtls_mpi_uint) (0x22) << 0 ) | ( (mbedtls_mpi_uint) (0xCC) << 8 ) | ( (mbedtls_mpi_uint) (0x72) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ),
    ( (mbedtls_mpi_uint) (0xF1) << 0 ) | ( (mbedtls_mpi_uint) (0x93) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0x27) << 24 ), ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0x34) << 8 ) | ( (mbedtls_mpi_uint) (0xC5) << 16 ) | ( (mbedtls_mpi_uint) (0x5B) << 24 ),
    ( (mbedtls_mpi_uint) (0x63) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0xA5) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ), ( (mbedtls_mpi_uint) (0x5C) << 0 ) | ( (mbedtls_mpi_uint) (0x15) << 8 ) | ( (mbedtls_mpi_uint) (0x2E) << 16 ) | ( (mbedtls_mpi_uint) (0xC6) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_12_Y[] = {
    ( (mbedtls_mpi_uint) (0xF4) << 0 ) | ( (mbedtls_mpi_uint) (0x7F) << 8 ) | ( (mbedtls_mpi_uint) (0xBA) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ), ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0x84) << 8 ) | ( (mbedtls_mpi_uint) (0x6F) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ),
    ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0xA6) << 8 ) | ( (mbedtls_mpi_uint) (0x36) << 16 ) | ( (mbedtls_mpi_uint) (0x7E) << 24 ), ( (mbedtls_mpi_uint) (0xDC) << 0 ) | ( (mbedtls_mpi_uint) (0xF7) << 8 ) | ( (mbedtls_mpi_uint) (0xE1) << 16 ) | ( (mbedtls_mpi_uint) (0x67) << 24 ),
    ( (mbedtls_mpi_uint) (0x04) << 0 ) | ( (mbedtls_mpi_uint) (0x4D) << 8 ) | ( (mbedtls_mpi_uint) (0xAA) << 16 ) | ( (mbedtls_mpi_uint) (0xEE) << 24 ), ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0x3A) << 16 ) | ( (mbedtls_mpi_uint) (0xD3) << 24 ),
    ( (mbedtls_mpi_uint) (0x4E) << 0 ) | ( (mbedtls_mpi_uint) (0x7E) << 8 ) | ( (mbedtls_mpi_uint) (0x26) << 16 ) | ( (mbedtls_mpi_uint) (0x18) << 24 ), ( (mbedtls_mpi_uint) (0x22) << 0 ) | ( (mbedtls_mpi_uint) (0x23) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_13_X[] = {
    ( (mbedtls_mpi_uint) (0x1D) << 0 ) | ( (mbedtls_mpi_uint) (0x4C) << 8 ) | ( (mbedtls_mpi_uint) (0x64) << 16 ) | ( (mbedtls_mpi_uint) (0xC7) << 24 ), ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0x02) << 8 ) | ( (mbedtls_mpi_uint) (0x3F) << 16 ) | ( (mbedtls_mpi_uint) (0xE3) << 24 ),
    ( (mbedtls_mpi_uint) (0xD8) << 0 ) | ( (mbedtls_mpi_uint) (0x02) << 8 ) | ( (mbedtls_mpi_uint) (0x90) << 16 ) | ( (mbedtls_mpi_uint) (0xBB) << 24 ), ( (mbedtls_mpi_uint) (0xC3) << 0 ) | ( (mbedtls_mpi_uint) (0xEC) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0x40) << 24 ),
    ( (mbedtls_mpi_uint) (0x9F) << 0 ) | ( (mbedtls_mpi_uint) (0x6F) << 8 ) | ( (mbedtls_mpi_uint) (0x64) << 16 ) | ( (mbedtls_mpi_uint) (0xF4) << 24 ), ( (mbedtls_mpi_uint) (0x16) << 0 ) | ( (mbedtls_mpi_uint) (0x69) << 8 ) | ( (mbedtls_mpi_uint) (0x48) << 16 ) | ( (mbedtls_mpi_uint) (0xA4) << 24 ),
    ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x44) << 8 ) | ( (mbedtls_mpi_uint) (0x9C) << 16 ) | ( (mbedtls_mpi_uint) (0x95) << 24 ), ( (mbedtls_mpi_uint) (0x0C) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x67) << 16 ) | ( (mbedtls_mpi_uint) (0x5E) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_13_Y[] = {
    ( (mbedtls_mpi_uint) (0x44) << 0 ) | ( (mbedtls_mpi_uint) (0x91) << 8 ) | ( (mbedtls_mpi_uint) (0x8B) << 16 ) | ( (mbedtls_mpi_uint) (0xD8) << 24 ), ( (mbedtls_mpi_uint) (0xD0) << 0 ) | ( (mbedtls_mpi_uint) (0xD7) << 8 ) | ( (mbedtls_mpi_uint) (0xE7) << 16 ) | ( (mbedtls_mpi_uint) (0xE2) << 24 ),
    ( (mbedtls_mpi_uint) (0x1F) << 0 ) | ( (mbedtls_mpi_uint) (0xF9) << 8 ) | ( (mbedtls_mpi_uint) (0x48) << 16 ) | ( (mbedtls_mpi_uint) (0x62) << 24 ), ( (mbedtls_mpi_uint) (0x6F) << 0 ) | ( (mbedtls_mpi_uint) (0xA8) << 8 ) | ( (mbedtls_mpi_uint) (0x93) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ),
    ( (mbedtls_mpi_uint) (0xEA) << 0 ) | ( (mbedtls_mpi_uint) (0x3A) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x02) << 24 ), ( (mbedtls_mpi_uint) (0xD5) << 0 ) | ( (mbedtls_mpi_uint) (0x0B) << 8 ) | ( (mbedtls_mpi_uint) (0x3D) << 16 ) | ( (mbedtls_mpi_uint) (0xE3) << 24 ),
    ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0xD3) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x31) << 24 ), ( (mbedtls_mpi_uint) (0xE6) << 0 ) | ( (mbedtls_mpi_uint) (0x0C) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0x44) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_14_X[] = {
    ( (mbedtls_mpi_uint) (0x56) << 0 ) | ( (mbedtls_mpi_uint) (0xB2) << 8 ) | ( (mbedtls_mpi_uint) (0xAA) << 16 ) | ( (mbedtls_mpi_uint) (0xFD) << 24 ), ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0x15) << 8 ) | ( (mbedtls_mpi_uint) (0xDF) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ),
    ( (mbedtls_mpi_uint) (0x4C) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0x27) << 16 ) | ( (mbedtls_mpi_uint) (0x31) << 24 ), ( (mbedtls_mpi_uint) (0x44) << 0 ) | ( (mbedtls_mpi_uint) (0xCD) << 8 ) | ( (mbedtls_mpi_uint) (0xC0) << 16 ) | ( (mbedtls_mpi_uint) (0x68) << 24 ),
    ( (mbedtls_mpi_uint) (0x53) << 0 ) | ( (mbedtls_mpi_uint) (0xF8) << 8 ) | ( (mbedtls_mpi_uint) (0x91) << 16 ) | ( (mbedtls_mpi_uint) (0xA5) << 24 ), ( (mbedtls_mpi_uint) (0x71) << 0 ) | ( (mbedtls_mpi_uint) (0x94) << 8 ) | ( (mbedtls_mpi_uint) (0x84) << 16 ) | ( (mbedtls_mpi_uint) (0x2A) << 24 ),
    ( (mbedtls_mpi_uint) (0x92) << 0 ) | ( (mbedtls_mpi_uint) (0xCB) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0x93) << 24 ), ( (mbedtls_mpi_uint) (0xE9) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0xDA) << 16 ) | ( (mbedtls_mpi_uint) (0xE4) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_14_Y[] = {
    ( (mbedtls_mpi_uint) (0x24) << 0 ) | ( (mbedtls_mpi_uint) (0xC6) << 8 ) | ( (mbedtls_mpi_uint) (0x39) << 16 ) | ( (mbedtls_mpi_uint) (0x16) << 24 ), ( (mbedtls_mpi_uint) (0x5D) << 0 ) | ( (mbedtls_mpi_uint) (0xA3) << 8 ) | ( (mbedtls_mpi_uint) (0x1E) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ),
    ( (mbedtls_mpi_uint) (0xBA) << 0 ) | ( (mbedtls_mpi_uint) (0x07) << 8 ) | ( (mbedtls_mpi_uint) (0x37) << 16 ) | ( (mbedtls_mpi_uint) (0x26) << 24 ), ( (mbedtls_mpi_uint) (0x36) << 0 ) | ( (mbedtls_mpi_uint) (0x2A) << 8 ) | ( (mbedtls_mpi_uint) (0xFE) << 16 ) | ( (mbedtls_mpi_uint) (0x60) << 24 ),
    ( (mbedtls_mpi_uint) (0x51) << 0 ) | ( (mbedtls_mpi_uint) (0xBC) << 8 ) | ( (mbedtls_mpi_uint) (0xF3) << 16 ) | ( (mbedtls_mpi_uint) (0xD0) << 24 ), ( (mbedtls_mpi_uint) (0xDE) << 0 ) | ( (mbedtls_mpi_uint) (0x50) << 8 ) | ( (mbedtls_mpi_uint) (0xFC) << 16 ) | ( (mbedtls_mpi_uint) (0x97) << 24 ),
    ( (mbedtls_mpi_uint) (0x80) << 0 ) | ( (mbedtls_mpi_uint) (0x2E) << 8 ) | ( (mbedtls_mpi_uint) (0x06) << 16 ) | ( (mbedtls_mpi_uint) (0x10) << 24 ), ( (mbedtls_mpi_uint) (0x15) << 0 ) | ( (mbedtls_mpi_uint) (0x4D) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0xF7) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_15_X[] = {
    ( (mbedtls_mpi_uint) (0x27) << 0 ) | ( (mbedtls_mpi_uint) (0x65) << 8 ) | ( (mbedtls_mpi_uint) (0x69) << 16 ) | ( (mbedtls_mpi_uint) (0x5B) << 24 ), ( (mbedtls_mpi_uint) (0x66) << 0 ) | ( (mbedtls_mpi_uint) (0xA2) << 8 ) | ( (mbedtls_mpi_uint) (0x75) << 16 ) | ( (mbedtls_mpi_uint) (0x2E) << 24 ),
    ( (mbedtls_mpi_uint) (0x9C) << 0 ) | ( (mbedtls_mpi_uint) (0x16) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x5A) << 24 ), ( (mbedtls_mpi_uint) (0xB0) << 0 ) | ( (mbedtls_mpi_uint) (0x30) << 8 ) | ( (mbedtls_mpi_uint) (0x25) << 16 ) | ( (mbedtls_mpi_uint) (0x1A) << 24 ),
    ( (mbedtls_mpi_uint) (0x42) << 0 ) | ( (mbedtls_mpi_uint) (0xFB) << 8 ) | ( (mbedtls_mpi_uint) (0x86) << 16 ) | ( (mbedtls_mpi_uint) (0x42) << 24 ), ( (mbedtls_mpi_uint) (0x80) << 0 ) | ( (mbedtls_mpi_uint) (0xC1) << 8 ) | ( (mbedtls_mpi_uint) (0xC4) << 16 ) | ( (mbedtls_mpi_uint) (0x76) << 24 ),
    ( (mbedtls_mpi_uint) (0x5B) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0x8E) << 24 ), ( (mbedtls_mpi_uint) (0x94) << 0 ) | ( (mbedtls_mpi_uint) (0x01) << 8 ) | ( (mbedtls_mpi_uint) (0x5F) << 16 ) | ( (mbedtls_mpi_uint) (0x82) << 24 ),
};
static const mbedtls_mpi_uint secp256r1_T_15_Y[] = {
    ( (mbedtls_mpi_uint) (0x39) << 0 ) | ( (mbedtls_mpi_uint) (0x37) << 8 ) | ( (mbedtls_mpi_uint) (0x70) << 16 ) | ( (mbedtls_mpi_uint) (0xEF) << 24 ), ( (mbedtls_mpi_uint) (0x1F) << 0 ) | ( (mbedtls_mpi_uint) (0xA1) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0xDB) << 24 ),
    ( (mbedtls_mpi_uint) (0x6A) << 0 ) | ( (mbedtls_mpi_uint) (0x10) << 8 ) | ( (mbedtls_mpi_uint) (0x5B) << 16 ) | ( (mbedtls_mpi_uint) (0xCE) << 24 ), ( (mbedtls_mpi_uint) (0xC4) << 0 ) | ( (mbedtls_mpi_uint) (0x9B) << 8 ) | ( (mbedtls_mpi_uint) (0x6F) << 16 ) | ( (mbedtls_mpi_uint) (0x10) << 24 ),
    ( (mbedtls_mpi_uint) (0x50) << 0 ) | ( (mbedtls_mpi_uint) (0x11) << 8 ) | ( (mbedtls_mpi_uint) (0x11) << 16 ) | ( (mbedtls_mpi_uint) (0x24) << 24 ), ( (mbedtls_mpi_uint) (0x4F) << 0 ) | ( (mbedtls_mpi_uint) (0x4C) << 8 ) | ( (mbedtls_mpi_uint) (0x79) << 16 ) | ( (mbedtls_mpi_uint) (0x61) << 24 ),
    ( (mbedtls_mpi_uint) (0x17) << 0 ) | ( (mbedtls_mpi_uint) (0x3A) << 8 ) | ( (mbedtls_mpi_uint) (0x72) << 16 ) | ( (mbedtls_mpi_uint) (0xBC) << 24 ), ( (mbedtls_mpi_uint) (0xFE) << 0 ) | ( (mbedtls_mpi_uint) (0x72) << 8 ) | ( (mbedtls_mpi_uint) (0x58) << 16 ) | ( (mbedtls_mpi_uint) (0x43) << 24 ),
};
static const mbedtls_ecp_point secp256r1_T[16] = {
    { {1, (sizeof(secp256r1_T_0_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_0_X)}, {1, (sizeof(secp256r1_T_0_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_0_Y)}, {1, (1), (mbedtls_mpi_uint *)(mpi_one)} },
    { {1, (sizeof(secp256r1_T_1_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_1_X)}, {1, (sizeof(secp256r1_T_1_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_1_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_2_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_2_X)}, {1, (sizeof(secp256r1_T_2_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_2_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_3_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_3_X)}, {1, (sizeof(secp256r1_T_3_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_3_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_4_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_4_X)}, {1, (sizeof(secp256r1_T_4_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_4_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_5_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_5_X)}, {1, (sizeof(secp256r1_T_5_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_5_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_6_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_6_X)}, {1, (sizeof(secp256r1_T_6_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_6_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_7_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_7_X)}, {1, (sizeof(secp256r1_T_7_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_7_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_8_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_8_X)}, {1, (sizeof(secp256r1_T_8_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_8_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_9_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_9_X)}, {1, (sizeof(secp256r1_T_9_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_9_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_10_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_10_X)}, {1, (sizeof(secp256r1_T_10_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_10_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_11_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_11_X)}, {1, (sizeof(secp256r1_T_11_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_11_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_12_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_12_X)}, {1, (sizeof(secp256r1_T_12_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_12_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_13_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_13_X)}, {1, (sizeof(secp256r1_T_13_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_13_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_14_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_14_X)}, {1, (sizeof(secp256r1_T_14_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_14_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp256r1_T_15_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_15_X)}, {1, (sizeof(secp256r1_T_15_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp256r1_T_15_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
};






/*
 * Domain parameters for secp384r1
 */

static const mbedtls_mpi_uint secp384r1_p[] = {
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ),
    ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0xFE) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_b[] = {
    ( (mbedtls_mpi_uint) (0xEF) << 0 ) | ( (mbedtls_mpi_uint) (0x2A) << 8 ) | ( (mbedtls_mpi_uint) (0xEC) << 16 ) | ( (mbedtls_mpi_uint) (0xD3) << 24 ), ( (mbedtls_mpi_uint) (0xED) << 0 ) | ( (mbedtls_mpi_uint) (0xC8) << 8 ) | ( (mbedtls_mpi_uint) (0x85) << 16 ) | ( (mbedtls_mpi_uint) (0x2A) << 24 ),
    ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0xD1) << 8 ) | ( (mbedtls_mpi_uint) (0x2E) << 16 ) | ( (mbedtls_mpi_uint) (0x8A) << 24 ), ( (mbedtls_mpi_uint) (0x8D) << 0 ) | ( (mbedtls_mpi_uint) (0x39) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0xC6) << 24 ),
    ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0x87) << 8 ) | ( (mbedtls_mpi_uint) (0x13) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ), ( (mbedtls_mpi_uint) (0x8F) << 0 ) | ( (mbedtls_mpi_uint) (0x08) << 8 ) | ( (mbedtls_mpi_uint) (0x14) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ),
    ( (mbedtls_mpi_uint) (0x12) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x81) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ), ( (mbedtls_mpi_uint) (0x6E) << 0 ) | ( (mbedtls_mpi_uint) (0x9C) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x18) << 24 ),
    ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0x2D) << 8 ) | ( (mbedtls_mpi_uint) (0xF8) << 16 ) | ( (mbedtls_mpi_uint) (0xE3) << 24 ), ( (mbedtls_mpi_uint) (0x6B) << 0 ) | ( (mbedtls_mpi_uint) (0x05) << 8 ) | ( (mbedtls_mpi_uint) (0x8E) << 16 ) | ( (mbedtls_mpi_uint) (0x98) << 24 ),
    ( (mbedtls_mpi_uint) (0xE4) << 0 ) | ( (mbedtls_mpi_uint) (0xE7) << 8 ) | ( (mbedtls_mpi_uint) (0x3E) << 16 ) | ( (mbedtls_mpi_uint) (0xE2) << 24 ), ( (mbedtls_mpi_uint) (0xA7) << 0 ) | ( (mbedtls_mpi_uint) (0x2F) << 8 ) | ( (mbedtls_mpi_uint) (0x31) << 16 ) | ( (mbedtls_mpi_uint) (0xB3) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_gx[] = {
    ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0x0A) << 8 ) | ( (mbedtls_mpi_uint) (0x76) << 16 ) | ( (mbedtls_mpi_uint) (0x72) << 24 ), ( (mbedtls_mpi_uint) (0x38) << 0 ) | ( (mbedtls_mpi_uint) (0x5E) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x3A) << 24 ),
    ( (mbedtls_mpi_uint) (0x6C) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0x55) << 16 ) | ( (mbedtls_mpi_uint) (0xBF) << 24 ), ( (mbedtls_mpi_uint) (0x5D) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0x02) << 16 ) | ( (mbedtls_mpi_uint) (0x55) << 24 ),
    ( (mbedtls_mpi_uint) (0x38) << 0 ) | ( (mbedtls_mpi_uint) (0x2A) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x82) << 24 ), ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0xF7) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ),
    ( (mbedtls_mpi_uint) (0x98) << 0 ) | ( (mbedtls_mpi_uint) (0x9B) << 8 ) | ( (mbedtls_mpi_uint) (0xA7) << 16 ) | ( (mbedtls_mpi_uint) (0x8B) << 24 ), ( (mbedtls_mpi_uint) (0x62) << 0 ) | ( (mbedtls_mpi_uint) (0x3B) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x6E) << 24 ),
    ( (mbedtls_mpi_uint) (0x74) << 0 ) | ( (mbedtls_mpi_uint) (0xAD) << 8 ) | ( (mbedtls_mpi_uint) (0x20) << 16 ) | ( (mbedtls_mpi_uint) (0xF3) << 24 ), ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0xC7) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0x8E) << 24 ),
    ( (mbedtls_mpi_uint) (0x37) << 0 ) | ( (mbedtls_mpi_uint) (0x05) << 8 ) | ( (mbedtls_mpi_uint) (0x8B) << 16 ) | ( (mbedtls_mpi_uint) (0xBE) << 24 ), ( (mbedtls_mpi_uint) (0x22) << 0 ) | ( (mbedtls_mpi_uint) (0xCA) << 8 ) | ( (mbedtls_mpi_uint) (0x87) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_gy[] = {
    ( (mbedtls_mpi_uint) (0x5F) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0xEA) << 16 ) | ( (mbedtls_mpi_uint) (0x90) << 24 ), ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0x43) << 16 ) | ( (mbedtls_mpi_uint) (0x7A) << 24 ),
    ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0x81) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0x1D) << 24 ), ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0xB1) << 8 ) | ( (mbedtls_mpi_uint) (0x60) << 16 ) | ( (mbedtls_mpi_uint) (0x0A) << 24 ),
    ( (mbedtls_mpi_uint) (0xC0) << 0 ) | ( (mbedtls_mpi_uint) (0xB8) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0xB5) << 24 ), ( (mbedtls_mpi_uint) (0x13) << 0 ) | ( (mbedtls_mpi_uint) (0x31) << 8 ) | ( (mbedtls_mpi_uint) (0xDA) << 16 ) | ( (mbedtls_mpi_uint) (0xE9) << 24 ),
    ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0x14) << 8 ) | ( (mbedtls_mpi_uint) (0x9A) << 16 ) | ( (mbedtls_mpi_uint) (0x28) << 24 ), ( (mbedtls_mpi_uint) (0xBD) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0xF4) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ),
    ( (mbedtls_mpi_uint) (0x29) << 0 ) | ( (mbedtls_mpi_uint) (0xDC) << 8 ) | ( (mbedtls_mpi_uint) (0x92) << 16 ) | ( (mbedtls_mpi_uint) (0x92) << 24 ), ( (mbedtls_mpi_uint) (0xBF) << 0 ) | ( (mbedtls_mpi_uint) (0x98) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ),
    ( (mbedtls_mpi_uint) (0x6F) << 0 ) | ( (mbedtls_mpi_uint) (0x2C) << 8 ) | ( (mbedtls_mpi_uint) (0x26) << 16 ) | ( (mbedtls_mpi_uint) (0x96) << 24 ), ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0xDE) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0x36) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_n[] = {
    ( (mbedtls_mpi_uint) (0x73) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0xC5) << 16 ) | ( (mbedtls_mpi_uint) (0xCC) << 24 ), ( (mbedtls_mpi_uint) (0x6A) << 0 ) | ( (mbedtls_mpi_uint) (0x19) << 8 ) | ( (mbedtls_mpi_uint) (0xEC) << 16 ) | ( (mbedtls_mpi_uint) (0xEC) << 24 ),
    ( (mbedtls_mpi_uint) (0x7A) << 0 ) | ( (mbedtls_mpi_uint) (0xA7) << 8 ) | ( (mbedtls_mpi_uint) (0xB0) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ), ( (mbedtls_mpi_uint) (0xB2) << 0 ) | ( (mbedtls_mpi_uint) (0x0D) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ),
    ( (mbedtls_mpi_uint) (0xDF) << 0 ) | ( (mbedtls_mpi_uint) (0x2D) << 8 ) | ( (mbedtls_mpi_uint) (0x37) << 16 ) | ( (mbedtls_mpi_uint) (0xF4) << 24 ), ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0x4D) << 8 ) | ( (mbedtls_mpi_uint) (0x63) << 16 ) | ( (mbedtls_mpi_uint) (0xC7) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ), ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
};

static const mbedtls_mpi_uint secp384r1_T_0_X[] = {
    ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0x0A) << 8 ) | ( (mbedtls_mpi_uint) (0x76) << 16 ) | ( (mbedtls_mpi_uint) (0x72) << 24 ), ( (mbedtls_mpi_uint) (0x38) << 0 ) | ( (mbedtls_mpi_uint) (0x5E) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x3A) << 24 ),
    ( (mbedtls_mpi_uint) (0x6C) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0x55) << 16 ) | ( (mbedtls_mpi_uint) (0xBF) << 24 ), ( (mbedtls_mpi_uint) (0x5D) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0x02) << 16 ) | ( (mbedtls_mpi_uint) (0x55) << 24 ),
    ( (mbedtls_mpi_uint) (0x38) << 0 ) | ( (mbedtls_mpi_uint) (0x2A) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x82) << 24 ), ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0xF7) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ),
    ( (mbedtls_mpi_uint) (0x98) << 0 ) | ( (mbedtls_mpi_uint) (0x9B) << 8 ) | ( (mbedtls_mpi_uint) (0xA7) << 16 ) | ( (mbedtls_mpi_uint) (0x8B) << 24 ), ( (mbedtls_mpi_uint) (0x62) << 0 ) | ( (mbedtls_mpi_uint) (0x3B) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x6E) << 24 ),
    ( (mbedtls_mpi_uint) (0x74) << 0 ) | ( (mbedtls_mpi_uint) (0xAD) << 8 ) | ( (mbedtls_mpi_uint) (0x20) << 16 ) | ( (mbedtls_mpi_uint) (0xF3) << 24 ), ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0xC7) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0x8E) << 24 ),
    ( (mbedtls_mpi_uint) (0x37) << 0 ) | ( (mbedtls_mpi_uint) (0x05) << 8 ) | ( (mbedtls_mpi_uint) (0x8B) << 16 ) | ( (mbedtls_mpi_uint) (0xBE) << 24 ), ( (mbedtls_mpi_uint) (0x22) << 0 ) | ( (mbedtls_mpi_uint) (0xCA) << 8 ) | ( (mbedtls_mpi_uint) (0x87) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_0_Y[] = {
    ( (mbedtls_mpi_uint) (0x5F) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0xEA) << 16 ) | ( (mbedtls_mpi_uint) (0x90) << 24 ), ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0x43) << 16 ) | ( (mbedtls_mpi_uint) (0x7A) << 24 ),
    ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0x81) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0x1D) << 24 ), ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0xB1) << 8 ) | ( (mbedtls_mpi_uint) (0x60) << 16 ) | ( (mbedtls_mpi_uint) (0x0A) << 24 ),
    ( (mbedtls_mpi_uint) (0xC0) << 0 ) | ( (mbedtls_mpi_uint) (0xB8) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0xB5) << 24 ), ( (mbedtls_mpi_uint) (0x13) << 0 ) | ( (mbedtls_mpi_uint) (0x31) << 8 ) | ( (mbedtls_mpi_uint) (0xDA) << 16 ) | ( (mbedtls_mpi_uint) (0xE9) << 24 ),
    ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0x14) << 8 ) | ( (mbedtls_mpi_uint) (0x9A) << 16 ) | ( (mbedtls_mpi_uint) (0x28) << 24 ), ( (mbedtls_mpi_uint) (0xBD) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0xF4) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ),
    ( (mbedtls_mpi_uint) (0x29) << 0 ) | ( (mbedtls_mpi_uint) (0xDC) << 8 ) | ( (mbedtls_mpi_uint) (0x92) << 16 ) | ( (mbedtls_mpi_uint) (0x92) << 24 ), ( (mbedtls_mpi_uint) (0xBF) << 0 ) | ( (mbedtls_mpi_uint) (0x98) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ),
    ( (mbedtls_mpi_uint) (0x6F) << 0 ) | ( (mbedtls_mpi_uint) (0x2C) << 8 ) | ( (mbedtls_mpi_uint) (0x26) << 16 ) | ( (mbedtls_mpi_uint) (0x96) << 24 ), ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0xDE) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0x36) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_1_X[] = {
    ( (mbedtls_mpi_uint) (0x46) << 0 ) | ( (mbedtls_mpi_uint) (0x92) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ), ( (mbedtls_mpi_uint) (0x78) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0x1F) << 16 ) | ( (mbedtls_mpi_uint) (0x37) << 24 ),
    ( (mbedtls_mpi_uint) (0x17) << 0 ) | ( (mbedtls_mpi_uint) (0xF3) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0xB7) << 24 ), ( (mbedtls_mpi_uint) (0x06) << 0 ) | ( (mbedtls_mpi_uint) (0xF7) << 8 ) | ( (mbedtls_mpi_uint) (0xB6) << 16 ) | ( (mbedtls_mpi_uint) (0xBC) << 24 ),
    ( (mbedtls_mpi_uint) (0x3D) << 0 ) | ( (mbedtls_mpi_uint) (0xBC) << 8 ) | ( (mbedtls_mpi_uint) (0x2C) << 16 ) | ( (mbedtls_mpi_uint) (0xCF) << 24 ), ( (mbedtls_mpi_uint) (0xD8) << 0 ) | ( (mbedtls_mpi_uint) (0xED) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0xE7) << 24 ),
    ( (mbedtls_mpi_uint) (0x52) << 0 ) | ( (mbedtls_mpi_uint) (0x75) << 8 ) | ( (mbedtls_mpi_uint) (0x7B) << 16 ) | ( (mbedtls_mpi_uint) (0xA3) << 24 ), ( (mbedtls_mpi_uint) (0xAB) << 0 ) | ( (mbedtls_mpi_uint) (0xC3) << 8 ) | ( (mbedtls_mpi_uint) (0x2C) << 16 ) | ( (mbedtls_mpi_uint) (0x85) << 24 ),
    ( (mbedtls_mpi_uint) (0xE5) << 0 ) | ( (mbedtls_mpi_uint) (0x9D) << 8 ) | ( (mbedtls_mpi_uint) (0x78) << 16 ) | ( (mbedtls_mpi_uint) (0x41) << 24 ), ( (mbedtls_mpi_uint) (0xF6) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0x84) << 16 ) | ( (mbedtls_mpi_uint) (0xAC) << 24 ),
    ( (mbedtls_mpi_uint) (0x54) << 0 ) | ( (mbedtls_mpi_uint) (0x56) << 8 ) | ( (mbedtls_mpi_uint) (0xE8) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ), ( (mbedtls_mpi_uint) (0xB3) << 0 ) | ( (mbedtls_mpi_uint) (0xCB) << 8 ) | ( (mbedtls_mpi_uint) (0xA8) << 16 ) | ( (mbedtls_mpi_uint) (0xBD) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_1_Y[] = {
    ( (mbedtls_mpi_uint) (0x6D) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0xAE) << 16 ) | ( (mbedtls_mpi_uint) (0xA4) << 24 ), ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x89) << 8 ) | ( (mbedtls_mpi_uint) (0x1B) << 16 ) | ( (mbedtls_mpi_uint) (0xDA) << 24 ),
    ( (mbedtls_mpi_uint) (0x01) << 0 ) | ( (mbedtls_mpi_uint) (0x0F) << 8 ) | ( (mbedtls_mpi_uint) (0xCE) << 16 ) | ( (mbedtls_mpi_uint) (0x1C) << 24 ), ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0xF6) << 8 ) | ( (mbedtls_mpi_uint) (0x50) << 16 ) | ( (mbedtls_mpi_uint) (0x4C) << 24 ),
    ( (mbedtls_mpi_uint) (0x4C) << 0 ) | ( (mbedtls_mpi_uint) (0xEB) << 8 ) | ( (mbedtls_mpi_uint) (0x90) << 16 ) | ( (mbedtls_mpi_uint) (0xE6) << 24 ), ( (mbedtls_mpi_uint) (0x4D) << 0 ) | ( (mbedtls_mpi_uint) (0xC7) << 8 ) | ( (mbedtls_mpi_uint) (0xD4) << 16 ) | ( (mbedtls_mpi_uint) (0x7A) << 24 ),
    ( (mbedtls_mpi_uint) (0xD1) << 0 ) | ( (mbedtls_mpi_uint) (0x49) << 8 ) | ( (mbedtls_mpi_uint) (0x2D) << 16 ) | ( (mbedtls_mpi_uint) (0x8A) << 24 ), ( (mbedtls_mpi_uint) (0x01) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0x60) << 16 ) | ( (mbedtls_mpi_uint) (0x94) << 24 ),
    ( (mbedtls_mpi_uint) (0x5F) << 0 ) | ( (mbedtls_mpi_uint) (0x80) << 8 ) | ( (mbedtls_mpi_uint) (0x9B) << 16 ) | ( (mbedtls_mpi_uint) (0x9B) << 24 ), ( (mbedtls_mpi_uint) (0x6A) << 0 ) | ( (mbedtls_mpi_uint) (0xB0) << 8 ) | ( (mbedtls_mpi_uint) (0x07) << 16 ) | ( (mbedtls_mpi_uint) (0xD9) << 24 ),
    ( (mbedtls_mpi_uint) (0xC2) << 0 ) | ( (mbedtls_mpi_uint) (0xA2) << 8 ) | ( (mbedtls_mpi_uint) (0xEE) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ), ( (mbedtls_mpi_uint) (0xBE) << 0 ) | ( (mbedtls_mpi_uint) (0x95) << 8 ) | ( (mbedtls_mpi_uint) (0xBC) << 16 ) | ( (mbedtls_mpi_uint) (0x23) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_2_X[] = {
    ( (mbedtls_mpi_uint) (0xE6) << 0 ) | ( (mbedtls_mpi_uint) (0x9D) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0xAE) << 24 ), ( (mbedtls_mpi_uint) (0x59) << 0 ) | ( (mbedtls_mpi_uint) (0xFB) << 8 ) | ( (mbedtls_mpi_uint) (0x1F) << 16 ) | ( (mbedtls_mpi_uint) (0x98) << 24 ),
    ( (mbedtls_mpi_uint) (0xCF) << 0 ) | ( (mbedtls_mpi_uint) (0xAC) << 8 ) | ( (mbedtls_mpi_uint) (0x91) << 16 ) | ( (mbedtls_mpi_uint) (0x80) << 24 ), ( (mbedtls_mpi_uint) (0x87) << 0 ) | ( (mbedtls_mpi_uint) (0xA8) << 8 ) | ( (mbedtls_mpi_uint) (0x6E) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ),
    ( (mbedtls_mpi_uint) (0x30) << 0 ) | ( (mbedtls_mpi_uint) (0x08) << 8 ) | ( (mbedtls_mpi_uint) (0xA7) << 16 ) | ( (mbedtls_mpi_uint) (0x08) << 24 ), ( (mbedtls_mpi_uint) (0x94) << 0 ) | ( (mbedtls_mpi_uint) (0x32) << 8 ) | ( (mbedtls_mpi_uint) (0xFC) << 16 ) | ( (mbedtls_mpi_uint) (0x67) << 24 ),
    ( (mbedtls_mpi_uint) (0x9F) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0x84) << 24 ), ( (mbedtls_mpi_uint) (0xF4) << 0 ) | ( (mbedtls_mpi_uint) (0xE5) << 8 ) | ( (mbedtls_mpi_uint) (0x6E) << 16 ) | ( (mbedtls_mpi_uint) (0x7E) << 24 ),
    ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ), ( (mbedtls_mpi_uint) (0x24) << 0 ) | ( (mbedtls_mpi_uint) (0xF8) << 8 ) | ( (mbedtls_mpi_uint) (0x9C) << 16 ) | ( (mbedtls_mpi_uint) (0xC7) << 24 ),
    ( (mbedtls_mpi_uint) (0x34) << 0 ) | ( (mbedtls_mpi_uint) (0x04) << 8 ) | ( (mbedtls_mpi_uint) (0x01) << 16 ) | ( (mbedtls_mpi_uint) (0xC2) << 24 ), ( (mbedtls_mpi_uint) (0xFB) << 0 ) | ( (mbedtls_mpi_uint) (0x77) << 8 ) | ( (mbedtls_mpi_uint) (0x3E) << 16 ) | ( (mbedtls_mpi_uint) (0xDE) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_2_Y[] = {
    ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x38) << 8 ) | ( (mbedtls_mpi_uint) (0xEE) << 16 ) | ( (mbedtls_mpi_uint) (0xE3) << 24 ), ( (mbedtls_mpi_uint) (0xC7) << 0 ) | ( (mbedtls_mpi_uint) (0x9D) << 8 ) | ( (mbedtls_mpi_uint) (0xEC) << 16 ) | ( (mbedtls_mpi_uint) (0xA6) << 24 ),
    ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0xCF) << 16 ) | ( (mbedtls_mpi_uint) (0x43) << 24 ), ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x92) << 8 ) | ( (mbedtls_mpi_uint) (0x5E) << 16 ) | ( (mbedtls_mpi_uint) (0x8E) << 24 ),
    ( (mbedtls_mpi_uint) (0xE9) << 0 ) | ( (mbedtls_mpi_uint) (0xCA) << 8 ) | ( (mbedtls_mpi_uint) (0x43) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ), ( (mbedtls_mpi_uint) (0x3B) << 0 ) | ( (mbedtls_mpi_uint) (0x49) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0x75) << 24 ),
    ( (mbedtls_mpi_uint) (0x1C) << 0 ) | ( (mbedtls_mpi_uint) (0xE7) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0x17) << 24 ), ( (mbedtls_mpi_uint) (0x45) << 0 ) | ( (mbedtls_mpi_uint) (0x86) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0xE1) << 24 ),
    ( (mbedtls_mpi_uint) (0x92) << 0 ) | ( (mbedtls_mpi_uint) (0x69) << 8 ) | ( (mbedtls_mpi_uint) (0x57) << 16 ) | ( (mbedtls_mpi_uint) (0x32) << 24 ), ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0x9C) << 8 ) | ( (mbedtls_mpi_uint) (0xD1) << 16 ) | ( (mbedtls_mpi_uint) (0x00) << 24 ),
    ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x10) << 8 ) | ( (mbedtls_mpi_uint) (0xB8) << 16 ) | ( (mbedtls_mpi_uint) (0x4D) << 24 ), ( (mbedtls_mpi_uint) (0xB8) << 0 ) | ( (mbedtls_mpi_uint) (0xF4) << 8 ) | ( (mbedtls_mpi_uint) (0x0D) << 16 ) | ( (mbedtls_mpi_uint) (0xE3) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_3_X[] = {
    ( (mbedtls_mpi_uint) (0x60) << 0 ) | ( (mbedtls_mpi_uint) (0xDC) << 8 ) | ( (mbedtls_mpi_uint) (0x9A) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ), ( (mbedtls_mpi_uint) (0x79) << 0 ) | ( (mbedtls_mpi_uint) (0x39) << 8 ) | ( (mbedtls_mpi_uint) (0x27) << 16 ) | ( (mbedtls_mpi_uint) (0x16) << 24 ),
    ( (mbedtls_mpi_uint) (0x4F) << 0 ) | ( (mbedtls_mpi_uint) (0x71) << 8 ) | ( (mbedtls_mpi_uint) (0xE4) << 16 ) | ( (mbedtls_mpi_uint) (0x3B) << 24 ), ( (mbedtls_mpi_uint) (0x4D) << 0 ) | ( (mbedtls_mpi_uint) (0x60) << 8 ) | ( (mbedtls_mpi_uint) (0x0C) << 16 ) | ( (mbedtls_mpi_uint) (0xA3) << 24 ),
    ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0xBD) << 8 ) | ( (mbedtls_mpi_uint) (0x19) << 16 ) | ( (mbedtls_mpi_uint) (0x40) << 24 ), ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x19) << 8 ) | ( (mbedtls_mpi_uint) (0x2A) << 16 ) | ( (mbedtls_mpi_uint) (0x5A) << 24 ),
    ( (mbedtls_mpi_uint) (0x4D) << 0 ) | ( (mbedtls_mpi_uint) (0xF8) << 8 ) | ( (mbedtls_mpi_uint) (0x1E) << 16 ) | ( (mbedtls_mpi_uint) (0x43) << 24 ), ( (mbedtls_mpi_uint) (0xA1) << 0 ) | ( (mbedtls_mpi_uint) (0x50) << 8 ) | ( (mbedtls_mpi_uint) (0x8D) << 16 ) | ( (mbedtls_mpi_uint) (0xEF) << 24 ),
    ( (mbedtls_mpi_uint) (0xA3) << 0 ) | ( (mbedtls_mpi_uint) (0x18) << 8 ) | ( (mbedtls_mpi_uint) (0x7C) << 16 ) | ( (mbedtls_mpi_uint) (0x41) << 24 ), ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x7C) << 8 ) | ( (mbedtls_mpi_uint) (0x1B) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ),
    ( (mbedtls_mpi_uint) (0x00) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0x24) << 16 ) | ( (mbedtls_mpi_uint) (0xC4) << 24 ), ( (mbedtls_mpi_uint) (0xE9) << 0 ) | ( (mbedtls_mpi_uint) (0xB7) << 8 ) | ( (mbedtls_mpi_uint) (0xD3) << 16 ) | ( (mbedtls_mpi_uint) (0xAD) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_3_Y[] = {
    ( (mbedtls_mpi_uint) (0xBB) << 0 ) | ( (mbedtls_mpi_uint) (0x01) << 8 ) | ( (mbedtls_mpi_uint) (0x3D) << 16 ) | ( (mbedtls_mpi_uint) (0x63) << 24 ), ( (mbedtls_mpi_uint) (0x54) << 0 ) | ( (mbedtls_mpi_uint) (0x45) << 8 ) | ( (mbedtls_mpi_uint) (0x6F) << 16 ) | ( (mbedtls_mpi_uint) (0xB7) << 24 ),
    ( (mbedtls_mpi_uint) (0x7B) << 0 ) | ( (mbedtls_mpi_uint) (0xB2) << 8 ) | ( (mbedtls_mpi_uint) (0x19) << 16 ) | ( (mbedtls_mpi_uint) (0xA3) << 24 ), ( (mbedtls_mpi_uint) (0x86) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0x34) << 24 ),
    ( (mbedtls_mpi_uint) (0x84) << 0 ) | ( (mbedtls_mpi_uint) (0x02) << 8 ) | ( (mbedtls_mpi_uint) (0x87) << 16 ) | ( (mbedtls_mpi_uint) (0x18) << 24 ), ( (mbedtls_mpi_uint) (0x92) << 0 ) | ( (mbedtls_mpi_uint) (0x52) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0x71) << 24 ),
    ( (mbedtls_mpi_uint) (0x6C) << 0 ) | ( (mbedtls_mpi_uint) (0x18) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ), ( (mbedtls_mpi_uint) (0x18) << 0 ) | ( (mbedtls_mpi_uint) (0x1B) << 8 ) | ( (mbedtls_mpi_uint) (0x37) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ),
    ( (mbedtls_mpi_uint) (0xF4) << 0 ) | ( (mbedtls_mpi_uint) (0x74) << 8 ) | ( (mbedtls_mpi_uint) (0x61) << 16 ) | ( (mbedtls_mpi_uint) (0xBA) << 24 ), ( (mbedtls_mpi_uint) (0x18) << 0 ) | ( (mbedtls_mpi_uint) (0xAF) << 8 ) | ( (mbedtls_mpi_uint) (0x40) << 16 ) | ( (mbedtls_mpi_uint) (0x30) << 24 ),
    ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x3C) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ), ( (mbedtls_mpi_uint) (0x0F) << 0 ) | ( (mbedtls_mpi_uint) (0x07) << 8 ) | ( (mbedtls_mpi_uint) (0xB0) << 16 ) | ( (mbedtls_mpi_uint) (0x6F) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_4_X[] = {
    ( (mbedtls_mpi_uint) (0x09) << 0 ) | ( (mbedtls_mpi_uint) (0x39) << 8 ) | ( (mbedtls_mpi_uint) (0x13) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ), ( (mbedtls_mpi_uint) (0x60) << 0 ) | ( (mbedtls_mpi_uint) (0x15) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x30) << 24 ),
    ( (mbedtls_mpi_uint) (0x17) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0xCB) << 16 ) | ( (mbedtls_mpi_uint) (0xC6) << 24 ), ( (mbedtls_mpi_uint) (0xB1) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0x97) << 16 ) | ( (mbedtls_mpi_uint) (0x90) << 24 ),
    ( (mbedtls_mpi_uint) (0xE6) << 0 ) | ( (mbedtls_mpi_uint) (0xFA) << 8 ) | ( (mbedtls_mpi_uint) (0x60) << 16 ) | ( (mbedtls_mpi_uint) (0xB8) << 24 ), ( (mbedtls_mpi_uint) (0x24) << 0 ) | ( (mbedtls_mpi_uint) (0xE4) << 8 ) | ( (mbedtls_mpi_uint) (0x7D) << 16 ) | ( (mbedtls_mpi_uint) (0xD3) << 24 ),
    ( (mbedtls_mpi_uint) (0xDD) << 0 ) | ( (mbedtls_mpi_uint) (0x75) << 8 ) | ( (mbedtls_mpi_uint) (0xB3) << 16 ) | ( (mbedtls_mpi_uint) (0x70) << 24 ), ( (mbedtls_mpi_uint) (0xB2) << 0 ) | ( (mbedtls_mpi_uint) (0x83) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0x9B) << 24 ),
    ( (mbedtls_mpi_uint) (0xA3) << 0 ) | ( (mbedtls_mpi_uint) (0xE3) << 8 ) | ( (mbedtls_mpi_uint) (0x6C) << 16 ) | ( (mbedtls_mpi_uint) (0xCD) << 24 ), ( (mbedtls_mpi_uint) (0x33) << 0 ) | ( (mbedtls_mpi_uint) (0x62) << 8 ) | ( (mbedtls_mpi_uint) (0x7A) << 16 ) | ( (mbedtls_mpi_uint) (0x56) << 24 ),
    ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0x30) << 8 ) | ( (mbedtls_mpi_uint) (0xDC) << 16 ) | ( (mbedtls_mpi_uint) (0x0F) << 24 ), ( (mbedtls_mpi_uint) (0x9F) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0xB8) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_4_Y[] = {
    ( (mbedtls_mpi_uint) (0xA6) << 0 ) | ( (mbedtls_mpi_uint) (0xD5) << 8 ) | ( (mbedtls_mpi_uint) (0x0A) << 16 ) | ( (mbedtls_mpi_uint) (0x60) << 24 ), ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0xB9) << 8 ) | ( (mbedtls_mpi_uint) (0xC5) << 16 ) | ( (mbedtls_mpi_uint) (0x16) << 24 ),
    ( (mbedtls_mpi_uint) (0x44) << 0 ) | ( (mbedtls_mpi_uint) (0xAA) << 8 ) | ( (mbedtls_mpi_uint) (0x2F) << 16 ) | ( (mbedtls_mpi_uint) (0xD6) << 24 ), ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0xDF) << 16 ) | ( (mbedtls_mpi_uint) (0xEB) << 24 ),
    ( (mbedtls_mpi_uint) (0xF3) << 0 ) | ( (mbedtls_mpi_uint) (0x7B) << 8 ) | ( (mbedtls_mpi_uint) (0x74) << 16 ) | ( (mbedtls_mpi_uint) (0xC9) << 24 ), ( (mbedtls_mpi_uint) (0xB3) << 0 ) | ( (mbedtls_mpi_uint) (0x5B) << 8 ) | ( (mbedtls_mpi_uint) (0x95) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ),
    ( (mbedtls_mpi_uint) (0xAC) << 0 ) | ( (mbedtls_mpi_uint) (0x04) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0x15) << 24 ), ( (mbedtls_mpi_uint) (0xC8) << 0 ) | ( (mbedtls_mpi_uint) (0x5F) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0xF6) << 24 ),
    ( (mbedtls_mpi_uint) (0xB5) << 0 ) | ( (mbedtls_mpi_uint) (0x50) << 8 ) | ( (mbedtls_mpi_uint) (0x20) << 16 ) | ( (mbedtls_mpi_uint) (0x28) << 24 ), ( (mbedtls_mpi_uint) (0xD1) << 0 ) | ( (mbedtls_mpi_uint) (0x01) << 8 ) | ( (mbedtls_mpi_uint) (0xAF) << 16 ) | ( (mbedtls_mpi_uint) (0xF0) << 24 ),
    ( (mbedtls_mpi_uint) (0x28) << 0 ) | ( (mbedtls_mpi_uint) (0x6D) << 8 ) | ( (mbedtls_mpi_uint) (0x4F) << 16 ) | ( (mbedtls_mpi_uint) (0x31) << 24 ), ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0x2F) << 8 ) | ( (mbedtls_mpi_uint) (0x94) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_5_X[] = {
    ( (mbedtls_mpi_uint) (0x46) << 0 ) | ( (mbedtls_mpi_uint) (0x2F) << 8 ) | ( (mbedtls_mpi_uint) (0xD8) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ), ( (mbedtls_mpi_uint) (0x63) << 0 ) | ( (mbedtls_mpi_uint) (0x7C) << 8 ) | ( (mbedtls_mpi_uint) (0xE9) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ),
    ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x8C) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0x14) << 24 ), ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x37) << 8 ) | ( (mbedtls_mpi_uint) (0x63) << 16 ) | ( (mbedtls_mpi_uint) (0xDE) << 24 ),
    ( (mbedtls_mpi_uint) (0x10) << 0 ) | ( (mbedtls_mpi_uint) (0x02) << 8 ) | ( (mbedtls_mpi_uint) (0xB8) << 16 ) | ( (mbedtls_mpi_uint) (0x46) << 24 ), ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0xCE) << 8 ) | ( (mbedtls_mpi_uint) (0x7B) << 16 ) | ( (mbedtls_mpi_uint) (0x38) << 24 ),
    ( (mbedtls_mpi_uint) (0x82) << 0 ) | ( (mbedtls_mpi_uint) (0x47) << 8 ) | ( (mbedtls_mpi_uint) (0x2D) << 16 ) | ( (mbedtls_mpi_uint) (0x66) << 24 ), ( (mbedtls_mpi_uint) (0xA7) << 0 ) | ( (mbedtls_mpi_uint) (0xE9) << 8 ) | ( (mbedtls_mpi_uint) (0x33) << 16 ) | ( (mbedtls_mpi_uint) (0x23) << 24 ),
    ( (mbedtls_mpi_uint) (0x92) << 0 ) | ( (mbedtls_mpi_uint) (0xF9) << 8 ) | ( (mbedtls_mpi_uint) (0x93) << 16 ) | ( (mbedtls_mpi_uint) (0x94) << 24 ), ( (mbedtls_mpi_uint) (0xA8) << 0 ) | ( (mbedtls_mpi_uint) (0x48) << 8 ) | ( (mbedtls_mpi_uint) (0xB3) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ),
    ( (mbedtls_mpi_uint) (0xE9) << 0 ) | ( (mbedtls_mpi_uint) (0x4A) << 8 ) | ( (mbedtls_mpi_uint) (0xAC) << 16 ) | ( (mbedtls_mpi_uint) (0x51) << 24 ), ( (mbedtls_mpi_uint) (0x08) << 0 ) | ( (mbedtls_mpi_uint) (0x72) << 8 ) | ( (mbedtls_mpi_uint) (0x2F) << 16 ) | ( (mbedtls_mpi_uint) (0x1A) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_5_Y[] = {
    ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0xAD) << 8 ) | ( (mbedtls_mpi_uint) (0xA0) << 16 ) | ( (mbedtls_mpi_uint) (0xF9) << 24 ), ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0xE1) << 8 ) | ( (mbedtls_mpi_uint) (0x78) << 16 ) | ( (mbedtls_mpi_uint) (0x97) << 24 ),
    ( (mbedtls_mpi_uint) (0x3A) << 0 ) | ( (mbedtls_mpi_uint) (0x9A) << 8 ) | ( (mbedtls_mpi_uint) (0x63) << 16 ) | ( (mbedtls_mpi_uint) (0xD8) << 24 ), ( (mbedtls_mpi_uint) (0xBA) << 0 ) | ( (mbedtls_mpi_uint) (0x79) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0x17) << 24 ),
    ( (mbedtls_mpi_uint) (0x34) << 0 ) | ( (mbedtls_mpi_uint) (0x31) << 8 ) | ( (mbedtls_mpi_uint) (0x7B) << 16 ) | ( (mbedtls_mpi_uint) (0x7A) << 24 ), ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0x7D) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ),
    ( (mbedtls_mpi_uint) (0x83) << 0 ) | ( (mbedtls_mpi_uint) (0x96) << 8 ) | ( (mbedtls_mpi_uint) (0x12) << 16 ) | ( (mbedtls_mpi_uint) (0x4B) << 24 ), ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0x09) << 8 ) | ( (mbedtls_mpi_uint) (0xE0) << 16 ) | ( (mbedtls_mpi_uint) (0xB7) << 24 ),
    ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0x8A) << 8 ) | ( (mbedtls_mpi_uint) (0x57) << 16 ) | ( (mbedtls_mpi_uint) (0xEE) << 24 ), ( (mbedtls_mpi_uint) (0x4E) << 0 ) | ( (mbedtls_mpi_uint) (0x6E) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0xEC) << 24 ),
    ( (mbedtls_mpi_uint) (0x11) << 0 ) | ( (mbedtls_mpi_uint) (0x9D) << 8 ) | ( (mbedtls_mpi_uint) (0x69) << 16 ) | ( (mbedtls_mpi_uint) (0xDC) << 24 ), ( (mbedtls_mpi_uint) (0xB3) << 0 ) | ( (mbedtls_mpi_uint) (0xDA) << 8 ) | ( (mbedtls_mpi_uint) (0xD8) << 16 ) | ( (mbedtls_mpi_uint) (0x08) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_6_X[] = {
    ( (mbedtls_mpi_uint) (0x68) << 0 ) | ( (mbedtls_mpi_uint) (0x49) << 8 ) | ( (mbedtls_mpi_uint) (0x03) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ), ( (mbedtls_mpi_uint) (0x33) << 0 ) | ( (mbedtls_mpi_uint) (0x6F) << 8 ) | ( (mbedtls_mpi_uint) (0x28) << 16 ) | ( (mbedtls_mpi_uint) (0x4A) << 24 ),
    ( (mbedtls_mpi_uint) (0x5D) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0xA7) << 16 ) | ( (mbedtls_mpi_uint) (0x05) << 24 ), ( (mbedtls_mpi_uint) (0x8C) << 0 ) | ( (mbedtls_mpi_uint) (0xF3) << 8 ) | ( (mbedtls_mpi_uint) (0x4D) << 16 ) | ( (mbedtls_mpi_uint) (0xFB) << 24 ),
    ( (mbedtls_mpi_uint) (0x8E) << 0 ) | ( (mbedtls_mpi_uint) (0x92) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0xA8) << 24 ), ( (mbedtls_mpi_uint) (0xEC) << 0 ) | ( (mbedtls_mpi_uint) (0x0D) << 8 ) | ( (mbedtls_mpi_uint) (0x64) << 16 ) | ( (mbedtls_mpi_uint) (0x3B) << 24 ),
    ( (mbedtls_mpi_uint) (0x4E) << 0 ) | ( (mbedtls_mpi_uint) (0xFC) << 8 ) | ( (mbedtls_mpi_uint) (0xFD) << 16 ) | ( (mbedtls_mpi_uint) (0xD0) << 24 ), ( (mbedtls_mpi_uint) (0x4B) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0x1B) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ),
    ( (mbedtls_mpi_uint) (0x83) << 0 ) | ( (mbedtls_mpi_uint) (0x9C) << 8 ) | ( (mbedtls_mpi_uint) (0x51) << 16 ) | ( (mbedtls_mpi_uint) (0x69) << 24 ), ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0x71) << 8 ) | ( (mbedtls_mpi_uint) (0x73) << 16 ) | ( (mbedtls_mpi_uint) (0xF5) << 24 ),
    ( (mbedtls_mpi_uint) (0xB8) << 0 ) | ( (mbedtls_mpi_uint) (0x5A) << 8 ) | ( (mbedtls_mpi_uint) (0x14) << 16 ) | ( (mbedtls_mpi_uint) (0x23) << 24 ), ( (mbedtls_mpi_uint) (0x1A) << 0 ) | ( (mbedtls_mpi_uint) (0x46) << 8 ) | ( (mbedtls_mpi_uint) (0x63) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_6_Y[] = {
    ( (mbedtls_mpi_uint) (0xBC) << 0 ) | ( (mbedtls_mpi_uint) (0x4C) << 8 ) | ( (mbedtls_mpi_uint) (0x70) << 16 ) | ( (mbedtls_mpi_uint) (0x44) << 24 ), ( (mbedtls_mpi_uint) (0x18) << 0 ) | ( (mbedtls_mpi_uint) (0xCD) << 8 ) | ( (mbedtls_mpi_uint) (0xEF) << 16 ) | ( (mbedtls_mpi_uint) (0xED) << 24 ),
    ( (mbedtls_mpi_uint) (0xC2) << 0 ) | ( (mbedtls_mpi_uint) (0x49) << 8 ) | ( (mbedtls_mpi_uint) (0xDD) << 16 ) | ( (mbedtls_mpi_uint) (0x64) << 24 ), ( (mbedtls_mpi_uint) (0x7E) << 0 ) | ( (mbedtls_mpi_uint) (0x7E) << 8 ) | ( (mbedtls_mpi_uint) (0x4D) << 16 ) | ( (mbedtls_mpi_uint) (0x92) << 24 ),
    ( (mbedtls_mpi_uint) (0xA2) << 0 ) | ( (mbedtls_mpi_uint) (0x32) << 8 ) | ( (mbedtls_mpi_uint) (0x7C) << 16 ) | ( (mbedtls_mpi_uint) (0x09) << 24 ), ( (mbedtls_mpi_uint) (0xD0) << 0 ) | ( (mbedtls_mpi_uint) (0x3F) << 8 ) | ( (mbedtls_mpi_uint) (0xD6) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ),
    ( (mbedtls_mpi_uint) (0x6D) << 0 ) | ( (mbedtls_mpi_uint) (0xE0) << 8 ) | ( (mbedtls_mpi_uint) (0x4F) << 16 ) | ( (mbedtls_mpi_uint) (0x65) << 24 ), ( (mbedtls_mpi_uint) (0x0C) << 0 ) | ( (mbedtls_mpi_uint) (0x7A) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x3E) << 24 ),
    ( (mbedtls_mpi_uint) (0x16) << 0 ) | ( (mbedtls_mpi_uint) (0xFA) << 8 ) | ( (mbedtls_mpi_uint) (0xFB) << 16 ) | ( (mbedtls_mpi_uint) (0x4A) << 24 ), ( (mbedtls_mpi_uint) (0xB4) << 0 ) | ( (mbedtls_mpi_uint) (0x79) << 8 ) | ( (mbedtls_mpi_uint) (0x5A) << 16 ) | ( (mbedtls_mpi_uint) (0x8C) << 24 ),
    ( (mbedtls_mpi_uint) (0x04) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0x1B) << 16 ) | ( (mbedtls_mpi_uint) (0x2B) << 24 ), ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0xBC) << 8 ) | ( (mbedtls_mpi_uint) (0x9A) << 16 ) | ( (mbedtls_mpi_uint) (0x74) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_7_X[] = {
    ( (mbedtls_mpi_uint) (0x51) << 0 ) | ( (mbedtls_mpi_uint) (0xAC) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0xF7) << 24 ), ( (mbedtls_mpi_uint) (0x5F) << 0 ) | ( (mbedtls_mpi_uint) (0x51) << 8 ) | ( (mbedtls_mpi_uint) (0x68) << 16 ) | ( (mbedtls_mpi_uint) (0x0B) << 24 ),
    ( (mbedtls_mpi_uint) (0xC6) << 0 ) | ( (mbedtls_mpi_uint) (0xE0) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0xBC) << 24 ), ( (mbedtls_mpi_uint) (0x13) << 0 ) | ( (mbedtls_mpi_uint) (0x4E) << 8 ) | ( (mbedtls_mpi_uint) (0xAC) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ),
    ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0xF5) << 8 ) | ( (mbedtls_mpi_uint) (0xC5) << 16 ) | ( (mbedtls_mpi_uint) (0xE6) << 24 ), ( (mbedtls_mpi_uint) (0xD2) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0xBA) << 16 ) | ( (mbedtls_mpi_uint) (0xCB) << 24 ),
    ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0x28) << 16 ) | ( (mbedtls_mpi_uint) (0x23) << 24 ), ( (mbedtls_mpi_uint) (0x58) << 0 ) | ( (mbedtls_mpi_uint) (0x67) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0xEE) << 24 ),
    ( (mbedtls_mpi_uint) (0x9E) << 0 ) | ( (mbedtls_mpi_uint) (0x80) << 8 ) | ( (mbedtls_mpi_uint) (0x4B) << 16 ) | ( (mbedtls_mpi_uint) (0xD8) << 24 ), ( (mbedtls_mpi_uint) (0xC4) << 0 ) | ( (mbedtls_mpi_uint) (0xDF) << 8 ) | ( (mbedtls_mpi_uint) (0x15) << 16 ) | ( (mbedtls_mpi_uint) (0xE4) << 24 ),
    ( (mbedtls_mpi_uint) (0xF1) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0x58) << 16 ) | ( (mbedtls_mpi_uint) (0xE6) << 24 ), ( (mbedtls_mpi_uint) (0x2C) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_7_Y[] = {
    ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0x27) << 16 ) | ( (mbedtls_mpi_uint) (0x99) << 24 ), ( (mbedtls_mpi_uint) (0x16) << 0 ) | ( (mbedtls_mpi_uint) (0x2B) << 8 ) | ( (mbedtls_mpi_uint) (0x22) << 16 ) | ( (mbedtls_mpi_uint) (0x0B) << 24 ),
    ( (mbedtls_mpi_uint) (0xBA) << 0 ) | ( (mbedtls_mpi_uint) (0xF3) << 8 ) | ( (mbedtls_mpi_uint) (0x8F) << 16 ) | ( (mbedtls_mpi_uint) (0xC3) << 24 ), ( (mbedtls_mpi_uint) (0x2A) << 0 ) | ( (mbedtls_mpi_uint) (0x9B) << 8 ) | ( (mbedtls_mpi_uint) (0xFC) << 16 ) | ( (mbedtls_mpi_uint) (0x38) << 24 ),
    ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x2E) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0x3D) << 24 ), ( (mbedtls_mpi_uint) (0xFE) << 0 ) | ( (mbedtls_mpi_uint) (0x9E) << 8 ) | ( (mbedtls_mpi_uint) (0x3C) << 16 ) | ( (mbedtls_mpi_uint) (0x1B) << 24 ),
    ( (mbedtls_mpi_uint) (0x08) << 0 ) | ( (mbedtls_mpi_uint) (0x57) << 8 ) | ( (mbedtls_mpi_uint) (0xCD) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ), ( (mbedtls_mpi_uint) (0xC1) << 0 ) | ( (mbedtls_mpi_uint) (0x49) << 8 ) | ( (mbedtls_mpi_uint) (0x38) << 16 ) | ( (mbedtls_mpi_uint) (0xB5) << 24 ),
    ( (mbedtls_mpi_uint) (0x95) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0x8B) << 16 ) | ( (mbedtls_mpi_uint) (0x33) << 24 ), ( (mbedtls_mpi_uint) (0x89) << 0 ) | ( (mbedtls_mpi_uint) (0x1F) << 8 ) | ( (mbedtls_mpi_uint) (0xEA) << 16 ) | ( (mbedtls_mpi_uint) (0x01) << 24 ),
    ( (mbedtls_mpi_uint) (0xAA) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0x13) << 16 ) | ( (mbedtls_mpi_uint) (0xD7) << 24 ), ( (mbedtls_mpi_uint) (0x50) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0x3E) << 16 ) | ( (mbedtls_mpi_uint) (0xEB) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_8_X[] = {
    ( (mbedtls_mpi_uint) (0xD2) << 0 ) | ( (mbedtls_mpi_uint) (0x9A) << 8 ) | ( (mbedtls_mpi_uint) (0x52) << 16 ) | ( (mbedtls_mpi_uint) (0xD2) << 24 ), ( (mbedtls_mpi_uint) (0x54) << 0 ) | ( (mbedtls_mpi_uint) (0x7C) << 8 ) | ( (mbedtls_mpi_uint) (0x97) << 16 ) | ( (mbedtls_mpi_uint) (0xF2) << 24 ),
    ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0x6E) << 16 ) | ( (mbedtls_mpi_uint) (0xED) << 24 ), ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x87) << 8 ) | ( (mbedtls_mpi_uint) (0x50) << 16 ) | ( (mbedtls_mpi_uint) (0xC5) << 24 ),
    ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0x16) << 24 ), ( (mbedtls_mpi_uint) (0x40) << 0 ) | ( (mbedtls_mpi_uint) (0x15) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0xB8) << 24 ),
    ( (mbedtls_mpi_uint) (0x33) << 0 ) | ( (mbedtls_mpi_uint) (0x2B) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0xAB) << 24 ), ( (mbedtls_mpi_uint) (0x03) << 0 ) | ( (mbedtls_mpi_uint) (0x91) << 8 ) | ( (mbedtls_mpi_uint) (0xEA) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ),
    ( (mbedtls_mpi_uint) (0xC1) << 0 ) | ( (mbedtls_mpi_uint) (0x47) << 8 ) | ( (mbedtls_mpi_uint) (0x39) << 16 ) | ( (mbedtls_mpi_uint) (0xEF) << 24 ), ( (mbedtls_mpi_uint) (0x05) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0x90) << 24 ),
    ( (mbedtls_mpi_uint) (0xBF) << 0 ) | ( (mbedtls_mpi_uint) (0x24) << 8 ) | ( (mbedtls_mpi_uint) (0x0D) << 16 ) | ( (mbedtls_mpi_uint) (0x76) << 24 ), ( (mbedtls_mpi_uint) (0x11) << 0 ) | ( (mbedtls_mpi_uint) (0x53) << 8 ) | ( (mbedtls_mpi_uint) (0x08) << 16 ) | ( (mbedtls_mpi_uint) (0xAF) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_8_Y[] = {
    ( (mbedtls_mpi_uint) (0x1F) << 0 ) | ( (mbedtls_mpi_uint) (0x2F) << 8 ) | ( (mbedtls_mpi_uint) (0xDD) << 16 ) | ( (mbedtls_mpi_uint) (0xBD) << 24 ), ( (mbedtls_mpi_uint) (0x50) << 0 ) | ( (mbedtls_mpi_uint) (0x48) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0xE5) << 24 ),
    ( (mbedtls_mpi_uint) (0x80) << 0 ) | ( (mbedtls_mpi_uint) (0x1C) << 8 ) | ( (mbedtls_mpi_uint) (0x84) << 16 ) | ( (mbedtls_mpi_uint) (0x55) << 24 ), ( (mbedtls_mpi_uint) (0x78) << 0 ) | ( (mbedtls_mpi_uint) (0x14) << 8 ) | ( (mbedtls_mpi_uint) (0xEB) << 16 ) | ( (mbedtls_mpi_uint) (0xF6) << 24 ),
    ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x5E) << 8 ) | ( (mbedtls_mpi_uint) (0x3E) << 16 ) | ( (mbedtls_mpi_uint) (0xA6) << 24 ), ( (mbedtls_mpi_uint) (0xAF) << 0 ) | ( (mbedtls_mpi_uint) (0xF6) << 8 ) | ( (mbedtls_mpi_uint) (0xC7) << 16 ) | ( (mbedtls_mpi_uint) (0x04) << 24 ),
    ( (mbedtls_mpi_uint) (0xE7) << 0 ) | ( (mbedtls_mpi_uint) (0x11) << 8 ) | ( (mbedtls_mpi_uint) (0xE2) << 16 ) | ( (mbedtls_mpi_uint) (0x65) << 24 ), ( (mbedtls_mpi_uint) (0xCA) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x95) << 16 ) | ( (mbedtls_mpi_uint) (0x3B) << 24 ),
    ( (mbedtls_mpi_uint) (0xAE) << 0 ) | ( (mbedtls_mpi_uint) (0x83) << 8 ) | ( (mbedtls_mpi_uint) (0xD8) << 16 ) | ( (mbedtls_mpi_uint) (0xE6) << 24 ), ( (mbedtls_mpi_uint) (0x4D) << 0 ) | ( (mbedtls_mpi_uint) (0x22) << 8 ) | ( (mbedtls_mpi_uint) (0x06) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ),
    ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x7F) << 8 ) | ( (mbedtls_mpi_uint) (0x25) << 16 ) | ( (mbedtls_mpi_uint) (0x2A) << 24 ), ( (mbedtls_mpi_uint) (0xAA) << 0 ) | ( (mbedtls_mpi_uint) (0x28) << 8 ) | ( (mbedtls_mpi_uint) (0x46) << 16 ) | ( (mbedtls_mpi_uint) (0x97) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_9_X[] = {
    ( (mbedtls_mpi_uint) (0x79) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0x15) << 16 ) | ( (mbedtls_mpi_uint) (0x56) << 24 ), ( (mbedtls_mpi_uint) (0x84) << 0 ) | ( (mbedtls_mpi_uint) (0xCB) << 8 ) | ( (mbedtls_mpi_uint) (0xC0) << 16 ) | ( (mbedtls_mpi_uint) (0x56) << 24 ),
    ( (mbedtls_mpi_uint) (0x56) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0x0E) << 16 ) | ( (mbedtls_mpi_uint) (0x08) << 24 ), ( (mbedtls_mpi_uint) (0xC9) << 0 ) | ( (mbedtls_mpi_uint) (0xF5) << 8 ) | ( (mbedtls_mpi_uint) (0xD4) << 16 ) | ( (mbedtls_mpi_uint) (0x9E) << 24 ),
    ( (mbedtls_mpi_uint) (0xE6) << 0 ) | ( (mbedtls_mpi_uint) (0x62) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0x1A) << 24 ), ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0x13) << 8 ) | ( (mbedtls_mpi_uint) (0xD5) << 16 ) | ( (mbedtls_mpi_uint) (0x07) << 24 ),
    ( (mbedtls_mpi_uint) (0x7D) << 0 ) | ( (mbedtls_mpi_uint) (0xAD) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0xE0) << 24 ), ( (mbedtls_mpi_uint) (0x32) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0xA0) << 16 ) | ( (mbedtls_mpi_uint) (0xC0) << 24 ),
    ( (mbedtls_mpi_uint) (0xC5) << 0 ) | ( (mbedtls_mpi_uint) (0x38) << 8 ) | ( (mbedtls_mpi_uint) (0x81) << 16 ) | ( (mbedtls_mpi_uint) (0x21) << 24 ), ( (mbedtls_mpi_uint) (0x23) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0xD2) << 16 ) | ( (mbedtls_mpi_uint) (0xBB) << 24 ),
    ( (mbedtls_mpi_uint) (0x1C) << 0 ) | ( (mbedtls_mpi_uint) (0x51) << 8 ) | ( (mbedtls_mpi_uint) (0x05) << 16 ) | ( (mbedtls_mpi_uint) (0xD0) << 24 ), ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0x82) << 8 ) | ( (mbedtls_mpi_uint) (0xA9) << 16 ) | ( (mbedtls_mpi_uint) (0x71) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_9_Y[] = {
    ( (mbedtls_mpi_uint) (0xA7) << 0 ) | ( (mbedtls_mpi_uint) (0xC3) << 8 ) | ( (mbedtls_mpi_uint) (0x27) << 16 ) | ( (mbedtls_mpi_uint) (0xBF) << 24 ), ( (mbedtls_mpi_uint) (0xC6) << 0 ) | ( (mbedtls_mpi_uint) (0xAA) << 8 ) | ( (mbedtls_mpi_uint) (0xB7) << 16 ) | ( (mbedtls_mpi_uint) (0xB9) << 24 ),
    ( (mbedtls_mpi_uint) (0xCB) << 0 ) | ( (mbedtls_mpi_uint) (0x65) << 8 ) | ( (mbedtls_mpi_uint) (0x45) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ), ( (mbedtls_mpi_uint) (0xB9) << 0 ) | ( (mbedtls_mpi_uint) (0x46) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0x46) << 24 ),
    ( (mbedtls_mpi_uint) (0xF5) << 0 ) | ( (mbedtls_mpi_uint) (0x38) << 8 ) | ( (mbedtls_mpi_uint) (0x3F) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ), ( (mbedtls_mpi_uint) (0xB1) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0xCA) << 16 ) | ( (mbedtls_mpi_uint) (0x1C) << 24 ),
    ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0x6C) << 16 ) | ( (mbedtls_mpi_uint) (0x63) << 24 ), ( (mbedtls_mpi_uint) (0xE9) << 0 ) | ( (mbedtls_mpi_uint) (0xD7) << 8 ) | ( (mbedtls_mpi_uint) (0x48) << 16 ) | ( (mbedtls_mpi_uint) (0xB8) << 24 ),
    ( (mbedtls_mpi_uint) (0xBC) << 0 ) | ( (mbedtls_mpi_uint) (0xF1) << 8 ) | ( (mbedtls_mpi_uint) (0xD7) << 16 ) | ( (mbedtls_mpi_uint) (0x99) << 24 ), ( (mbedtls_mpi_uint) (0x8C) << 0 ) | ( (mbedtls_mpi_uint) (0xC2) << 8 ) | ( (mbedtls_mpi_uint) (0x05) << 16 ) | ( (mbedtls_mpi_uint) (0x99) << 24 ),
    ( (mbedtls_mpi_uint) (0x6D) << 0 ) | ( (mbedtls_mpi_uint) (0xE6) << 8 ) | ( (mbedtls_mpi_uint) (0x5E) << 16 ) | ( (mbedtls_mpi_uint) (0x82) << 24 ), ( (mbedtls_mpi_uint) (0x6D) << 0 ) | ( (mbedtls_mpi_uint) (0xE5) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0xD5) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_10_X[] = {
    ( (mbedtls_mpi_uint) (0x7B) << 0 ) | ( (mbedtls_mpi_uint) (0x61) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0x7D) << 24 ), ( (mbedtls_mpi_uint) (0x01) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0xB6) << 16 ) | ( (mbedtls_mpi_uint) (0x63) << 24 ),
    ( (mbedtls_mpi_uint) (0x11) << 0 ) | ( (mbedtls_mpi_uint) (0xC6) << 8 ) | ( (mbedtls_mpi_uint) (0x58) << 16 ) | ( (mbedtls_mpi_uint) (0x39) << 24 ), ( (mbedtls_mpi_uint) (0xF4) << 0 ) | ( (mbedtls_mpi_uint) (0xC6) << 8 ) | ( (mbedtls_mpi_uint) (0x82) << 16 ) | ( (mbedtls_mpi_uint) (0x23) << 24 ),
    ( (mbedtls_mpi_uint) (0x47) << 0 ) | ( (mbedtls_mpi_uint) (0x5A) << 8 ) | ( (mbedtls_mpi_uint) (0x7A) << 16 ) | ( (mbedtls_mpi_uint) (0x80) << 24 ), ( (mbedtls_mpi_uint) (0x08) << 0 ) | ( (mbedtls_mpi_uint) (0xCD) << 8 ) | ( (mbedtls_mpi_uint) (0xAA) << 16 ) | ( (mbedtls_mpi_uint) (0xD8) << 24 ),
    ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0x8C) << 8 ) | ( (mbedtls_mpi_uint) (0xC6) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ), ( (mbedtls_mpi_uint) (0x3C) << 0 ) | ( (mbedtls_mpi_uint) (0xA5) << 8 ) | ( (mbedtls_mpi_uint) (0x68) << 16 ) | ( (mbedtls_mpi_uint) (0xF4) << 24 ),
    ( (mbedtls_mpi_uint) (0xBB) << 0 ) | ( (mbedtls_mpi_uint) (0xF5) << 8 ) | ( (mbedtls_mpi_uint) (0xD5) << 16 ) | ( (mbedtls_mpi_uint) (0x17) << 24 ), ( (mbedtls_mpi_uint) (0xAE) << 0 ) | ( (mbedtls_mpi_uint) (0x36) << 8 ) | ( (mbedtls_mpi_uint) (0xD8) << 16 ) | ( (mbedtls_mpi_uint) (0x8A) << 24 ),
    ( (mbedtls_mpi_uint) (0xC7) << 0 ) | ( (mbedtls_mpi_uint) (0xAD) << 8 ) | ( (mbedtls_mpi_uint) (0x92) << 16 ) | ( (mbedtls_mpi_uint) (0xC5) << 24 ), ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0x6C) << 8 ) | ( (mbedtls_mpi_uint) (0xDA) << 16 ) | ( (mbedtls_mpi_uint) (0x91) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_10_Y[] = {
    ( (mbedtls_mpi_uint) (0xE8) << 0 ) | ( (mbedtls_mpi_uint) (0x67) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0xC0) << 24 ), ( (mbedtls_mpi_uint) (0x40) << 0 ) | ( (mbedtls_mpi_uint) (0x78) << 8 ) | ( (mbedtls_mpi_uint) (0x8C) << 16 ) | ( (mbedtls_mpi_uint) (0x84) << 24 ),
    ( (mbedtls_mpi_uint) (0x7E) << 0 ) | ( (mbedtls_mpi_uint) (0x9F) << 8 ) | ( (mbedtls_mpi_uint) (0xF4) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ), ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0x5C) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ),
    ( (mbedtls_mpi_uint) (0x96) << 0 ) | ( (mbedtls_mpi_uint) (0xDB) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0x3E) << 24 ), ( (mbedtls_mpi_uint) (0x72) << 0 ) | ( (mbedtls_mpi_uint) (0x64) << 8 ) | ( (mbedtls_mpi_uint) (0xA0) << 16 ) | ( (mbedtls_mpi_uint) (0x67) << 24 ),
    ( (mbedtls_mpi_uint) (0x27) << 0 ) | ( (mbedtls_mpi_uint) (0xF9) << 8 ) | ( (mbedtls_mpi_uint) (0x41) << 16 ) | ( (mbedtls_mpi_uint) (0x17) << 24 ), ( (mbedtls_mpi_uint) (0x43) << 0 ) | ( (mbedtls_mpi_uint) (0xE3) << 8 ) | ( (mbedtls_mpi_uint) (0xE8) << 16 ) | ( (mbedtls_mpi_uint) (0xA8) << 24 ),
    ( (mbedtls_mpi_uint) (0x66) << 0 ) | ( (mbedtls_mpi_uint) (0xDD) << 8 ) | ( (mbedtls_mpi_uint) (0xCC) << 16 ) | ( (mbedtls_mpi_uint) (0x43) << 24 ), ( (mbedtls_mpi_uint) (0x7E) << 0 ) | ( (mbedtls_mpi_uint) (0x16) << 8 ) | ( (mbedtls_mpi_uint) (0x05) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ),
    ( (mbedtls_mpi_uint) (0x36) << 0 ) | ( (mbedtls_mpi_uint) (0x4B) << 8 ) | ( (mbedtls_mpi_uint) (0xCF) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ), ( (mbedtls_mpi_uint) (0x8F) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x90) << 16 ) | ( (mbedtls_mpi_uint) (0xE5) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_11_X[] = {
    ( (mbedtls_mpi_uint) (0x98) << 0 ) | ( (mbedtls_mpi_uint) (0x0C) << 8 ) | ( (mbedtls_mpi_uint) (0x6B) << 16 ) | ( (mbedtls_mpi_uint) (0x9D) << 24 ), ( (mbedtls_mpi_uint) (0x22) << 0 ) | ( (mbedtls_mpi_uint) (0x04) << 8 ) | ( (mbedtls_mpi_uint) (0xBC) << 16 ) | ( (mbedtls_mpi_uint) (0x5C) << 24 ),
    ( (mbedtls_mpi_uint) (0x86) << 0 ) | ( (mbedtls_mpi_uint) (0x63) << 8 ) | ( (mbedtls_mpi_uint) (0x79) << 16 ) | ( (mbedtls_mpi_uint) (0x2F) << 24 ), ( (mbedtls_mpi_uint) (0x6A) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0x8A) << 16 ) | ( (mbedtls_mpi_uint) (0xDE) << 24 ),
    ( (mbedtls_mpi_uint) (0x29) << 0 ) | ( (mbedtls_mpi_uint) (0x67) << 8 ) | ( (mbedtls_mpi_uint) (0x3F) << 16 ) | ( (mbedtls_mpi_uint) (0x02) << 24 ), ( (mbedtls_mpi_uint) (0xB8) << 0 ) | ( (mbedtls_mpi_uint) (0x91) << 8 ) | ( (mbedtls_mpi_uint) (0x7F) << 16 ) | ( (mbedtls_mpi_uint) (0x74) << 24 ),
    ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x14) << 8 ) | ( (mbedtls_mpi_uint) (0x64) << 16 ) | ( (mbedtls_mpi_uint) (0xA0) << 24 ), ( (mbedtls_mpi_uint) (0x33) << 0 ) | ( (mbedtls_mpi_uint) (0xF4) << 8 ) | ( (mbedtls_mpi_uint) (0x6B) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ),
    ( (mbedtls_mpi_uint) (0x1C) << 0 ) | ( (mbedtls_mpi_uint) (0x44) << 8 ) | ( (mbedtls_mpi_uint) (0x71) << 16 ) | ( (mbedtls_mpi_uint) (0x87) << 24 ), ( (mbedtls_mpi_uint) (0xB8) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0x3F) << 16 ) | ( (mbedtls_mpi_uint) (0x45) << 24 ),
    ( (mbedtls_mpi_uint) (0x1B) << 0 ) | ( (mbedtls_mpi_uint) (0x2B) << 8 ) | ( (mbedtls_mpi_uint) (0x85) << 16 ) | ( (mbedtls_mpi_uint) (0x05) << 24 ), ( (mbedtls_mpi_uint) (0xC5) << 0 ) | ( (mbedtls_mpi_uint) (0x44) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0x15) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_11_Y[] = {
    ( (mbedtls_mpi_uint) (0x3E) << 0 ) | ( (mbedtls_mpi_uint) (0x2B) << 8 ) | ( (mbedtls_mpi_uint) (0xFE) << 16 ) | ( (mbedtls_mpi_uint) (0xD1) << 24 ), ( (mbedtls_mpi_uint) (0x1C) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0x2E) << 24 ),
    ( (mbedtls_mpi_uint) (0x66) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0xA1) << 16 ) | ( (mbedtls_mpi_uint) (0xD3) << 24 ), ( (mbedtls_mpi_uint) (0x69) << 0 ) | ( (mbedtls_mpi_uint) (0x1C) << 8 ) | ( (mbedtls_mpi_uint) (0x9D) << 16 ) | ( (mbedtls_mpi_uint) (0xD2) << 24 ),
    ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0x5A) << 8 ) | ( (mbedtls_mpi_uint) (0xBA) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ), ( (mbedtls_mpi_uint) (0xAE) << 0 ) | ( (mbedtls_mpi_uint) (0x1B) << 8 ) | ( (mbedtls_mpi_uint) (0x94) << 16 ) | ( (mbedtls_mpi_uint) (0x04) << 24 ),
    ( (mbedtls_mpi_uint) (0xAF) << 0 ) | ( (mbedtls_mpi_uint) (0x74) << 8 ) | ( (mbedtls_mpi_uint) (0x90) << 16 ) | ( (mbedtls_mpi_uint) (0x5C) << 24 ), ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0xB0) << 8 ) | ( (mbedtls_mpi_uint) (0x3A) << 16 ) | ( (mbedtls_mpi_uint) (0x45) << 24 ),
    ( (mbedtls_mpi_uint) (0xDD) << 0 ) | ( (mbedtls_mpi_uint) (0x2F) << 8 ) | ( (mbedtls_mpi_uint) (0x93) << 16 ) | ( (mbedtls_mpi_uint) (0x20) << 24 ), ( (mbedtls_mpi_uint) (0x24) << 0 ) | ( (mbedtls_mpi_uint) (0x54) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x8D) << 24 ),
    ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x78) << 8 ) | ( (mbedtls_mpi_uint) (0x9D) << 16 ) | ( (mbedtls_mpi_uint) (0x71) << 24 ), ( (mbedtls_mpi_uint) (0x67) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0x49) << 16 ) | ( (mbedtls_mpi_uint) (0x98) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_12_X[] = {
    ( (mbedtls_mpi_uint) (0x12) << 0 ) | ( (mbedtls_mpi_uint) (0xC8) << 8 ) | ( (mbedtls_mpi_uint) (0x0E) << 16 ) | ( (mbedtls_mpi_uint) (0x11) << 24 ), ( (mbedtls_mpi_uint) (0x8D) << 0 ) | ( (mbedtls_mpi_uint) (0xE0) << 8 ) | ( (mbedtls_mpi_uint) (0x8F) << 16 ) | ( (mbedtls_mpi_uint) (0x69) << 24 ),
    ( (mbedtls_mpi_uint) (0x59) << 0 ) | ( (mbedtls_mpi_uint) (0x7F) << 8 ) | ( (mbedtls_mpi_uint) (0x79) << 16 ) | ( (mbedtls_mpi_uint) (0x6C) << 24 ), ( (mbedtls_mpi_uint) (0x5F) << 0 ) | ( (mbedtls_mpi_uint) (0xB7) << 8 ) | ( (mbedtls_mpi_uint) (0xBC) << 16 ) | ( (mbedtls_mpi_uint) (0xB1) << 24 ),
    ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0xE1) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0x3C) << 24 ), ( (mbedtls_mpi_uint) (0x12) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0xEE) << 16 ) | ( (mbedtls_mpi_uint) (0x96) << 24 ),
    ( (mbedtls_mpi_uint) (0x2A) << 0 ) | ( (mbedtls_mpi_uint) (0xC2) << 8 ) | ( (mbedtls_mpi_uint) (0xC4) << 16 ) | ( (mbedtls_mpi_uint) (0x1B) << 24 ), ( (mbedtls_mpi_uint) (0x41) << 0 ) | ( (mbedtls_mpi_uint) (0x71) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0x17) << 24 ),
    ( (mbedtls_mpi_uint) (0xB0) << 0 ) | ( (mbedtls_mpi_uint) (0xEE) << 8 ) | ( (mbedtls_mpi_uint) (0xBB) << 16 ) | ( (mbedtls_mpi_uint) (0x1D) << 24 ), ( (mbedtls_mpi_uint) (0x89) << 0 ) | ( (mbedtls_mpi_uint) (0x50) << 8 ) | ( (mbedtls_mpi_uint) (0x88) << 16 ) | ( (mbedtls_mpi_uint) (0xF2) << 24 ),
    ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x1C) << 8 ) | ( (mbedtls_mpi_uint) (0x55) << 16 ) | ( (mbedtls_mpi_uint) (0x74) << 24 ), ( (mbedtls_mpi_uint) (0xEB) << 0 ) | ( (mbedtls_mpi_uint) (0xDE) << 8 ) | ( (mbedtls_mpi_uint) (0x92) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_12_Y[] = {
    ( (mbedtls_mpi_uint) (0x9C) << 0 ) | ( (mbedtls_mpi_uint) (0x38) << 8 ) | ( (mbedtls_mpi_uint) (0x92) << 16 ) | ( (mbedtls_mpi_uint) (0x06) << 24 ), ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0xD0) << 8 ) | ( (mbedtls_mpi_uint) (0xB3) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ),
    ( (mbedtls_mpi_uint) (0x2A) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0x26) << 16 ) | ( (mbedtls_mpi_uint) (0xA3) << 24 ), ( (mbedtls_mpi_uint) (0x5F) << 0 ) | ( (mbedtls_mpi_uint) (0xE2) << 8 ) | ( (mbedtls_mpi_uint) (0xC1) << 16 ) | ( (mbedtls_mpi_uint) (0x81) << 24 ),
    ( (mbedtls_mpi_uint) (0x75) << 0 ) | ( (mbedtls_mpi_uint) (0xFC) << 8 ) | ( (mbedtls_mpi_uint) (0xFD) << 16 ) | ( (mbedtls_mpi_uint) (0xC3) << 24 ), ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0x24) << 16 ) | ( (mbedtls_mpi_uint) (0x8F) << 24 ),
    ( (mbedtls_mpi_uint) (0xAF) << 0 ) | ( (mbedtls_mpi_uint) (0xAD) << 8 ) | ( (mbedtls_mpi_uint) (0xE7) << 16 ) | ( (mbedtls_mpi_uint) (0x49) << 24 ), ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0x64) << 8 ) | ( (mbedtls_mpi_uint) (0x4B) << 16 ) | ( (mbedtls_mpi_uint) (0x96) << 24 ),
    ( (mbedtls_mpi_uint) (0x6C) << 0 ) | ( (mbedtls_mpi_uint) (0x4E) << 8 ) | ( (mbedtls_mpi_uint) (0x95) << 16 ) | ( (mbedtls_mpi_uint) (0xAD) << 24 ), ( (mbedtls_mpi_uint) (0x07) << 0 ) | ( (mbedtls_mpi_uint) (0xFE) << 8 ) | ( (mbedtls_mpi_uint) (0xB6) << 16 ) | ( (mbedtls_mpi_uint) (0x30) << 24 ),
    ( (mbedtls_mpi_uint) (0x4F) << 0 ) | ( (mbedtls_mpi_uint) (0x15) << 8 ) | ( (mbedtls_mpi_uint) (0xE7) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ), ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0xA9) << 8 ) | ( (mbedtls_mpi_uint) (0x08) << 16 ) | ( (mbedtls_mpi_uint) (0x10) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_13_X[] = {
    ( (mbedtls_mpi_uint) (0xBE) << 0 ) | ( (mbedtls_mpi_uint) (0xBD) << 8 ) | ( (mbedtls_mpi_uint) (0xAC) << 16 ) | ( (mbedtls_mpi_uint) (0x0A) << 24 ), ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0x6B) << 8 ) | ( (mbedtls_mpi_uint) (0xFF) << 16 ) | ( (mbedtls_mpi_uint) (0xFA) << 24 ),
    ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0xE4) << 8 ) | ( (mbedtls_mpi_uint) (0x74) << 16 ) | ( (mbedtls_mpi_uint) (0x14) << 24 ), ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x70) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x71) << 24 ),
    ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0xB0) << 8 ) | ( (mbedtls_mpi_uint) (0x71) << 16 ) | ( (mbedtls_mpi_uint) (0xBB) << 24 ), ( (mbedtls_mpi_uint) (0xD8) << 0 ) | ( (mbedtls_mpi_uint) (0x18) << 8 ) | ( (mbedtls_mpi_uint) (0x96) << 16 ) | ( (mbedtls_mpi_uint) (0x2B) << 24 ),
    ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0xB8) << 8 ) | ( (mbedtls_mpi_uint) (0x19) << 16 ) | ( (mbedtls_mpi_uint) (0x90) << 24 ), ( (mbedtls_mpi_uint) (0x80) << 0 ) | ( (mbedtls_mpi_uint) (0xB5) << 8 ) | ( (mbedtls_mpi_uint) (0xEE) << 16 ) | ( (mbedtls_mpi_uint) (0x01) << 24 ),
    ( (mbedtls_mpi_uint) (0x91) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0x20) << 16 ) | ( (mbedtls_mpi_uint) (0xA6) << 24 ), ( (mbedtls_mpi_uint) (0x17) << 0 ) | ( (mbedtls_mpi_uint) (0x48) << 8 ) | ( (mbedtls_mpi_uint) (0x03) << 16 ) | ( (mbedtls_mpi_uint) (0x6F) << 24 ),
    ( (mbedtls_mpi_uint) (0xE3) << 0 ) | ( (mbedtls_mpi_uint) (0x1D) << 8 ) | ( (mbedtls_mpi_uint) (0xBB) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ), ( (mbedtls_mpi_uint) (0x94) << 0 ) | ( (mbedtls_mpi_uint) (0x20) << 8 ) | ( (mbedtls_mpi_uint) (0x34) << 16 ) | ( (mbedtls_mpi_uint) (0xF1) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_13_Y[] = {
    ( (mbedtls_mpi_uint) (0x59) << 0 ) | ( (mbedtls_mpi_uint) (0x82) << 8 ) | ( (mbedtls_mpi_uint) (0x67) << 16 ) | ( (mbedtls_mpi_uint) (0x4B) << 24 ), ( (mbedtls_mpi_uint) (0x8E) << 0 ) | ( (mbedtls_mpi_uint) (0x4E) << 8 ) | ( (mbedtls_mpi_uint) (0xBE) << 16 ) | ( (mbedtls_mpi_uint) (0xE2) << 24 ),
    ( (mbedtls_mpi_uint) (0xBE) << 0 ) | ( (mbedtls_mpi_uint) (0xDA) << 8 ) | ( (mbedtls_mpi_uint) (0x77) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ), ( (mbedtls_mpi_uint) (0x23) << 0 ) | ( (mbedtls_mpi_uint) (0x55) << 8 ) | ( (mbedtls_mpi_uint) (0x2B) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ),
    ( (mbedtls_mpi_uint) (0x5C) << 0 ) | ( (mbedtls_mpi_uint) (0x02) << 8 ) | ( (mbedtls_mpi_uint) (0xDE) << 16 ) | ( (mbedtls_mpi_uint) (0x25) << 24 ), ( (mbedtls_mpi_uint) (0x35) << 0 ) | ( (mbedtls_mpi_uint) (0x2D) << 8 ) | ( (mbedtls_mpi_uint) (0x74) << 16 ) | ( (mbedtls_mpi_uint) (0x51) << 24 ),
    ( (mbedtls_mpi_uint) (0xD0) << 0 ) | ( (mbedtls_mpi_uint) (0x0C) << 8 ) | ( (mbedtls_mpi_uint) (0xB8) << 16 ) | ( (mbedtls_mpi_uint) (0x0B) << 24 ), ( (mbedtls_mpi_uint) (0x39) << 0 ) | ( (mbedtls_mpi_uint) (0xBA) << 8 ) | ( (mbedtls_mpi_uint) (0xAD) << 16 ) | ( (mbedtls_mpi_uint) (0x04) << 24 ),
    ( (mbedtls_mpi_uint) (0xA6) << 0 ) | ( (mbedtls_mpi_uint) (0x0E) << 8 ) | ( (mbedtls_mpi_uint) (0x28) << 16 ) | ( (mbedtls_mpi_uint) (0x4D) << 24 ), ( (mbedtls_mpi_uint) (0xE1) << 0 ) | ( (mbedtls_mpi_uint) (0x3D) << 8 ) | ( (mbedtls_mpi_uint) (0xE4) << 16 ) | ( (mbedtls_mpi_uint) (0x1B) << 24 ),
    ( (mbedtls_mpi_uint) (0x5D) << 0 ) | ( (mbedtls_mpi_uint) (0xEC) << 8 ) | ( (mbedtls_mpi_uint) (0x0A) << 16 ) | ( (mbedtls_mpi_uint) (0xD4) << 24 ), ( (mbedtls_mpi_uint) (0xB8) << 0 ) | ( (mbedtls_mpi_uint) (0xC4) << 8 ) | ( (mbedtls_mpi_uint) (0x8D) << 16 ) | ( (mbedtls_mpi_uint) (0xB0) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_14_X[] = {
    ( (mbedtls_mpi_uint) (0x3E) << 0 ) | ( (mbedtls_mpi_uint) (0x68) << 8 ) | ( (mbedtls_mpi_uint) (0xCE) << 16 ) | ( (mbedtls_mpi_uint) (0xC2) << 24 ), ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0x4D) << 8 ) | ( (mbedtls_mpi_uint) (0x0C) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ),
    ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0x20) << 8 ) | ( (mbedtls_mpi_uint) (0x93) << 16 ) | ( (mbedtls_mpi_uint) (0x32) << 24 ), ( (mbedtls_mpi_uint) (0x90) << 0 ) | ( (mbedtls_mpi_uint) (0xD6) << 8 ) | ( (mbedtls_mpi_uint) (0xAE) << 16 ) | ( (mbedtls_mpi_uint) (0x47) << 24 ),
    ( (mbedtls_mpi_uint) (0xDD) << 0 ) | ( (mbedtls_mpi_uint) (0x78) << 8 ) | ( (mbedtls_mpi_uint) (0xAB) << 16 ) | ( (mbedtls_mpi_uint) (0x43) << 24 ), ( (mbedtls_mpi_uint) (0x9E) << 0 ) | ( (mbedtls_mpi_uint) (0xEB) << 8 ) | ( (mbedtls_mpi_uint) (0x73) << 16 ) | ( (mbedtls_mpi_uint) (0xAE) << 24 ),
    ( (mbedtls_mpi_uint) (0xED) << 0 ) | ( (mbedtls_mpi_uint) (0x97) << 8 ) | ( (mbedtls_mpi_uint) (0xC3) << 16 ) | ( (mbedtls_mpi_uint) (0x83) << 24 ), ( (mbedtls_mpi_uint) (0xA6) << 0 ) | ( (mbedtls_mpi_uint) (0x3C) << 8 ) | ( (mbedtls_mpi_uint) (0xF1) << 16 ) | ( (mbedtls_mpi_uint) (0xBF) << 24 ),
    ( (mbedtls_mpi_uint) (0x0F) << 0 ) | ( (mbedtls_mpi_uint) (0x25) << 8 ) | ( (mbedtls_mpi_uint) (0x25) << 16 ) | ( (mbedtls_mpi_uint) (0x66) << 24 ), ( (mbedtls_mpi_uint) (0x08) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0x4B) << 24 ),
    ( (mbedtls_mpi_uint) (0x41) << 0 ) | ( (mbedtls_mpi_uint) (0xFB) << 8 ) | ( (mbedtls_mpi_uint) (0x44) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ), ( (mbedtls_mpi_uint) (0x82) << 0 ) | ( (mbedtls_mpi_uint) (0xEC) << 8 ) | ( (mbedtls_mpi_uint) (0x3B) << 16 ) | ( (mbedtls_mpi_uint) (0xAC) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_14_Y[] = {
    ( (mbedtls_mpi_uint) (0x58) << 0 ) | ( (mbedtls_mpi_uint) (0x90) << 8 ) | ( (mbedtls_mpi_uint) (0xEA) << 16 ) | ( (mbedtls_mpi_uint) (0xB5) << 24 ), ( (mbedtls_mpi_uint) (0x04) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0x69) << 24 ),
    ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0x22) << 16 ) | ( (mbedtls_mpi_uint) (0xA0) << 24 ), ( (mbedtls_mpi_uint) (0xEB) << 0 ) | ( (mbedtls_mpi_uint) (0xFD) << 8 ) | ( (mbedtls_mpi_uint) (0x45) << 16 ) | ( (mbedtls_mpi_uint) (0x87) << 24 ),
    ( (mbedtls_mpi_uint) (0x5D) << 0 ) | ( (mbedtls_mpi_uint) (0xA4) << 8 ) | ( (mbedtls_mpi_uint) (0x81) << 16 ) | ( (mbedtls_mpi_uint) (0x32) << 24 ), ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0xFA) << 8 ) | ( (mbedtls_mpi_uint) (0xEE) << 16 ) | ( (mbedtls_mpi_uint) (0x5B) << 24 ),
    ( (mbedtls_mpi_uint) (0x27) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0x6A) << 24 ), ( (mbedtls_mpi_uint) (0x77) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x5C) << 16 ) | ( (mbedtls_mpi_uint) (0x1D) << 24 ),
    ( (mbedtls_mpi_uint) (0xA1) << 0 ) | ( (mbedtls_mpi_uint) (0x1E) << 8 ) | ( (mbedtls_mpi_uint) (0xAA) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ), ( (mbedtls_mpi_uint) (0xF0) << 0 ) | ( (mbedtls_mpi_uint) (0x10) << 8 ) | ( (mbedtls_mpi_uint) (0xB3) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ),
    ( (mbedtls_mpi_uint) (0x09) << 0 ) | ( (mbedtls_mpi_uint) (0x74) << 8 ) | ( (mbedtls_mpi_uint) (0x13) << 16 ) | ( (mbedtls_mpi_uint) (0x14) << 24 ), ( (mbedtls_mpi_uint) (0x9E) << 0 ) | ( (mbedtls_mpi_uint) (0x90) << 8 ) | ( (mbedtls_mpi_uint) (0xD7) << 16 ) | ( (mbedtls_mpi_uint) (0xE6) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_15_X[] = {
    ( (mbedtls_mpi_uint) (0xDB) << 0 ) | ( (mbedtls_mpi_uint) (0xBD) << 8 ) | ( (mbedtls_mpi_uint) (0x70) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ), ( (mbedtls_mpi_uint) (0xA8) << 0 ) | ( (mbedtls_mpi_uint) (0xD1) << 8 ) | ( (mbedtls_mpi_uint) (0x06) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ),
    ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0x4E) << 8 ) | ( (mbedtls_mpi_uint) (0x2E) << 16 ) | ( (mbedtls_mpi_uint) (0x68) << 24 ), ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ),
    ( (mbedtls_mpi_uint) (0x60) << 0 ) | ( (mbedtls_mpi_uint) (0x53) << 8 ) | ( (mbedtls_mpi_uint) (0x75) << 16 ) | ( (mbedtls_mpi_uint) (0xED) << 24 ), ( (mbedtls_mpi_uint) (0xF2) << 0 ) | ( (mbedtls_mpi_uint) (0x5F) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0xEB) << 24 ),
    ( (mbedtls_mpi_uint) (0x39) << 0 ) | ( (mbedtls_mpi_uint) (0x87) << 8 ) | ( (mbedtls_mpi_uint) (0x6B) << 16 ) | ( (mbedtls_mpi_uint) (0x9F) << 24 ), ( (mbedtls_mpi_uint) (0x05) << 0 ) | ( (mbedtls_mpi_uint) (0xE2) << 8 ) | ( (mbedtls_mpi_uint) (0x22) << 16 ) | ( (mbedtls_mpi_uint) (0x93) << 24 ),
    ( (mbedtls_mpi_uint) (0x4F) << 0 ) | ( (mbedtls_mpi_uint) (0x1A) << 8 ) | ( (mbedtls_mpi_uint) (0xA8) << 16 ) | ( (mbedtls_mpi_uint) (0xB7) << 24 ), ( (mbedtls_mpi_uint) (0x03) << 0 ) | ( (mbedtls_mpi_uint) (0x9E) << 8 ) | ( (mbedtls_mpi_uint) (0x6D) << 16 ) | ( (mbedtls_mpi_uint) (0x7C) << 24 ),
    ( (mbedtls_mpi_uint) (0xCB) << 0 ) | ( (mbedtls_mpi_uint) (0xD0) << 8 ) | ( (mbedtls_mpi_uint) (0x69) << 16 ) | ( (mbedtls_mpi_uint) (0x88) << 24 ), ( (mbedtls_mpi_uint) (0xA8) << 0 ) | ( (mbedtls_mpi_uint) (0x39) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0x3A) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_15_Y[] = {
    ( (mbedtls_mpi_uint) (0xF8) << 0 ) | ( (mbedtls_mpi_uint) (0xEF) << 8 ) | ( (mbedtls_mpi_uint) (0x68) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ), ( (mbedtls_mpi_uint) (0xEC) << 0 ) | ( (mbedtls_mpi_uint) (0x24) << 8 ) | ( (mbedtls_mpi_uint) (0x08) << 16 ) | ( (mbedtls_mpi_uint) (0x15) << 24 ),
    ( (mbedtls_mpi_uint) (0xA1) << 0 ) | ( (mbedtls_mpi_uint) (0x06) << 8 ) | ( (mbedtls_mpi_uint) (0x4B) << 16 ) | ( (mbedtls_mpi_uint) (0x92) << 24 ), ( (mbedtls_mpi_uint) (0x0D) << 0 ) | ( (mbedtls_mpi_uint) (0xB7) << 8 ) | ( (mbedtls_mpi_uint) (0x34) << 16 ) | ( (mbedtls_mpi_uint) (0x74) << 24 ),
    ( (mbedtls_mpi_uint) (0x3E) << 0 ) | ( (mbedtls_mpi_uint) (0xF4) << 8 ) | ( (mbedtls_mpi_uint) (0xDD) << 16 ) | ( (mbedtls_mpi_uint) (0x1A) << 24 ), ( (mbedtls_mpi_uint) (0xA0) << 0 ) | ( (mbedtls_mpi_uint) (0x4A) << 8 ) | ( (mbedtls_mpi_uint) (0xE4) << 16 ) | ( (mbedtls_mpi_uint) (0x45) << 24 ),
    ( (mbedtls_mpi_uint) (0xC3) << 0 ) | ( (mbedtls_mpi_uint) (0x63) << 8 ) | ( (mbedtls_mpi_uint) (0x4F) << 16 ) | ( (mbedtls_mpi_uint) (0x4F) << 24 ), ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0xD6) << 16 ) | ( (mbedtls_mpi_uint) (0xD3) << 24 ),
    ( (mbedtls_mpi_uint) (0xCD) << 0 ) | ( (mbedtls_mpi_uint) (0xEE) << 8 ) | ( (mbedtls_mpi_uint) (0x8D) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ), ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0xB7) << 16 ) | ( (mbedtls_mpi_uint) (0xAC) << 24 ),
    ( (mbedtls_mpi_uint) (0xDF) << 0 ) | ( (mbedtls_mpi_uint) (0x06) << 8 ) | ( (mbedtls_mpi_uint) (0xB6) << 16 ) | ( (mbedtls_mpi_uint) (0x80) << 24 ), ( (mbedtls_mpi_uint) (0x4D) << 0 ) | ( (mbedtls_mpi_uint) (0x81) << 8 ) | ( (mbedtls_mpi_uint) (0xD9) << 16 ) | ( (mbedtls_mpi_uint) (0x53) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_16_X[] = {
    ( (mbedtls_mpi_uint) (0x15) << 0 ) | ( (mbedtls_mpi_uint) (0xF5) << 8 ) | ( (mbedtls_mpi_uint) (0x13) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ), ( (mbedtls_mpi_uint) (0x13) << 0 ) | ( (mbedtls_mpi_uint) (0x19) << 8 ) | ( (mbedtls_mpi_uint) (0x97) << 16 ) | ( (mbedtls_mpi_uint) (0x94) << 24 ),
    ( (mbedtls_mpi_uint) (0x08) << 0 ) | ( (mbedtls_mpi_uint) (0xF9) << 8 ) | ( (mbedtls_mpi_uint) (0xB3) << 16 ) | ( (mbedtls_mpi_uint) (0x33) << 24 ), ( (mbedtls_mpi_uint) (0x66) << 0 ) | ( (mbedtls_mpi_uint) (0x82) << 8 ) | ( (mbedtls_mpi_uint) (0x21) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ),
    ( (mbedtls_mpi_uint) (0xF5) << 0 ) | ( (mbedtls_mpi_uint) (0xFC) << 8 ) | ( (mbedtls_mpi_uint) (0x39) << 16 ) | ( (mbedtls_mpi_uint) (0x16) << 24 ), ( (mbedtls_mpi_uint) (0x23) << 0 ) | ( (mbedtls_mpi_uint) (0x43) << 8 ) | ( (mbedtls_mpi_uint) (0x76) << 16 ) | ( (mbedtls_mpi_uint) (0x0E) << 24 ),
    ( (mbedtls_mpi_uint) (0x09) << 0 ) | ( (mbedtls_mpi_uint) (0x48) << 8 ) | ( (mbedtls_mpi_uint) (0x25) << 16 ) | ( (mbedtls_mpi_uint) (0xA1) << 24 ), ( (mbedtls_mpi_uint) (0x64) << 0 ) | ( (mbedtls_mpi_uint) (0x95) << 8 ) | ( (mbedtls_mpi_uint) (0x1C) << 16 ) | ( (mbedtls_mpi_uint) (0x2F) << 24 ),
    ( (mbedtls_mpi_uint) (0x43) << 0 ) | ( (mbedtls_mpi_uint) (0xAC) << 8 ) | ( (mbedtls_mpi_uint) (0x15) << 16 ) | ( (mbedtls_mpi_uint) (0x57) << 24 ), ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0xDE) << 8 ) | ( (mbedtls_mpi_uint) (0xA0) << 16 ) | ( (mbedtls_mpi_uint) (0x28) << 24 ),
    ( (mbedtls_mpi_uint) (0x16) << 0 ) | ( (mbedtls_mpi_uint) (0x5F) << 8 ) | ( (mbedtls_mpi_uint) (0xB8) << 16 ) | ( (mbedtls_mpi_uint) (0x3D) << 24 ), ( (mbedtls_mpi_uint) (0x48) << 0 ) | ( (mbedtls_mpi_uint) (0x91) << 8 ) | ( (mbedtls_mpi_uint) (0x24) << 16 ) | ( (mbedtls_mpi_uint) (0xCC) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_16_Y[] = {
    ( (mbedtls_mpi_uint) (0x2D) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0xC8) << 16 ) | ( (mbedtls_mpi_uint) (0x54) << 24 ), ( (mbedtls_mpi_uint) (0xD1) << 0 ) | ( (mbedtls_mpi_uint) (0x32) << 8 ) | ( (mbedtls_mpi_uint) (0xBD) << 16 ) | ( (mbedtls_mpi_uint) (0xC4) << 24 ),
    ( (mbedtls_mpi_uint) (0x8A) << 0 ) | ( (mbedtls_mpi_uint) (0x3B) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ), ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0xD8) << 8 ) | ( (mbedtls_mpi_uint) (0xF4) << 16 ) | ( (mbedtls_mpi_uint) (0x20) << 24 ),
    ( (mbedtls_mpi_uint) (0x4F) << 0 ) | ( (mbedtls_mpi_uint) (0xC3) << 8 ) | ( (mbedtls_mpi_uint) (0xBB) << 16 ) | ( (mbedtls_mpi_uint) (0x6C) << 24 ), ( (mbedtls_mpi_uint) (0x66) << 0 ) | ( (mbedtls_mpi_uint) (0xAC) << 8 ) | ( (mbedtls_mpi_uint) (0x25) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ),
    ( (mbedtls_mpi_uint) (0x6F) << 0 ) | ( (mbedtls_mpi_uint) (0x25) << 8 ) | ( (mbedtls_mpi_uint) (0x10) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ), ( (mbedtls_mpi_uint) (0xE1) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0xDE) << 16 ) | ( (mbedtls_mpi_uint) (0x1D) << 24 ),
    ( (mbedtls_mpi_uint) (0x3C) << 0 ) | ( (mbedtls_mpi_uint) (0xE8) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0xB8) << 24 ), ( (mbedtls_mpi_uint) (0x37) << 0 ) | ( (mbedtls_mpi_uint) (0xBC) << 8 ) | ( (mbedtls_mpi_uint) (0x2A) << 16 ) | ( (mbedtls_mpi_uint) (0x98) << 24 ),
    ( (mbedtls_mpi_uint) (0xBA) << 0 ) | ( (mbedtls_mpi_uint) (0x57) << 8 ) | ( (mbedtls_mpi_uint) (0x01) << 16 ) | ( (mbedtls_mpi_uint) (0x4A) << 24 ), ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0x78) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0x85) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_17_X[] = {
    ( (mbedtls_mpi_uint) (0xBD) << 0 ) | ( (mbedtls_mpi_uint) (0x19) << 8 ) | ( (mbedtls_mpi_uint) (0xCD) << 16 ) | ( (mbedtls_mpi_uint) (0x12) << 24 ), ( (mbedtls_mpi_uint) (0x0B) << 0 ) | ( (mbedtls_mpi_uint) (0x51) << 8 ) | ( (mbedtls_mpi_uint) (0x4F) << 16 ) | ( (mbedtls_mpi_uint) (0x56) << 24 ),
    ( (mbedtls_mpi_uint) (0x30) << 0 ) | ( (mbedtls_mpi_uint) (0x4B) << 8 ) | ( (mbedtls_mpi_uint) (0x3D) << 16 ) | ( (mbedtls_mpi_uint) (0x24) << 24 ), ( (mbedtls_mpi_uint) (0xA4) << 0 ) | ( (mbedtls_mpi_uint) (0x16) << 8 ) | ( (mbedtls_mpi_uint) (0x59) << 16 ) | ( (mbedtls_mpi_uint) (0x05) << 24 ),
    ( (mbedtls_mpi_uint) (0xAC) << 0 ) | ( (mbedtls_mpi_uint) (0xEB) << 8 ) | ( (mbedtls_mpi_uint) (0xD3) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ), ( (mbedtls_mpi_uint) (0x2E) << 0 ) | ( (mbedtls_mpi_uint) (0x75) << 8 ) | ( (mbedtls_mpi_uint) (0x7C) << 16 ) | ( (mbedtls_mpi_uint) (0x01) << 24 ),
    ( (mbedtls_mpi_uint) (0x8C) << 0 ) | ( (mbedtls_mpi_uint) (0xB9) << 8 ) | ( (mbedtls_mpi_uint) (0xB4) << 16 ) | ( (mbedtls_mpi_uint) (0xA5) << 24 ), ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x2E) << 8 ) | ( (mbedtls_mpi_uint) (0x29) << 16 ) | ( (mbedtls_mpi_uint) (0x4C) << 24 ),
    ( (mbedtls_mpi_uint) (0x86) << 0 ) | ( (mbedtls_mpi_uint) (0x16) << 8 ) | ( (mbedtls_mpi_uint) (0x05) << 16 ) | ( (mbedtls_mpi_uint) (0x75) << 24 ), ( (mbedtls_mpi_uint) (0x02) << 0 ) | ( (mbedtls_mpi_uint) (0xB3) << 8 ) | ( (mbedtls_mpi_uint) (0x06) << 16 ) | ( (mbedtls_mpi_uint) (0xEE) << 24 ),
    ( (mbedtls_mpi_uint) (0xAB) << 0 ) | ( (mbedtls_mpi_uint) (0x7C) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0x79) << 24 ), ( (mbedtls_mpi_uint) (0x91) << 0 ) | ( (mbedtls_mpi_uint) (0xF1) << 8 ) | ( (mbedtls_mpi_uint) (0x4F) << 16 ) | ( (mbedtls_mpi_uint) (0x23) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_17_Y[] = {
    ( (mbedtls_mpi_uint) (0x65) << 0 ) | ( (mbedtls_mpi_uint) (0x98) << 8 ) | ( (mbedtls_mpi_uint) (0x7C) << 16 ) | ( (mbedtls_mpi_uint) (0x84) << 24 ), ( (mbedtls_mpi_uint) (0xE1) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0x77) << 24 ),
    ( (mbedtls_mpi_uint) (0x71) << 0 ) | ( (mbedtls_mpi_uint) (0xE2) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ), ( (mbedtls_mpi_uint) (0x55) << 0 ) | ( (mbedtls_mpi_uint) (0x40) << 8 ) | ( (mbedtls_mpi_uint) (0xBD) << 16 ) | ( (mbedtls_mpi_uint) (0xCD) << 24 ),
    ( (mbedtls_mpi_uint) (0x69) << 0 ) | ( (mbedtls_mpi_uint) (0x65) << 8 ) | ( (mbedtls_mpi_uint) (0x87) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ), ( (mbedtls_mpi_uint) (0xC4) << 0 ) | ( (mbedtls_mpi_uint) (0xC2) << 8 ) | ( (mbedtls_mpi_uint) (0x24) << 16 ) | ( (mbedtls_mpi_uint) (0x57) << 24 ),
    ( (mbedtls_mpi_uint) (0x0E) << 0 ) | ( (mbedtls_mpi_uint) (0x30) << 8 ) | ( (mbedtls_mpi_uint) (0x0A) << 16 ) | ( (mbedtls_mpi_uint) (0x60) << 24 ), ( (mbedtls_mpi_uint) (0x15) << 0 ) | ( (mbedtls_mpi_uint) (0xD1) << 8 ) | ( (mbedtls_mpi_uint) (0x24) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ),
    ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0xD9) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ), ( (mbedtls_mpi_uint) (0xAE) << 0 ) | ( (mbedtls_mpi_uint) (0xB1) << 8 ) | ( (mbedtls_mpi_uint) (0xAF) << 16 ) | ( (mbedtls_mpi_uint) (0x1D) << 24 ),
    ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0x80) << 8 ) | ( (mbedtls_mpi_uint) (0xEE) << 16 ) | ( (mbedtls_mpi_uint) (0xA2) << 24 ), ( (mbedtls_mpi_uint) (0x0F) << 0 ) | ( (mbedtls_mpi_uint) (0x74) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0xF3) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_18_X[] = {
    ( (mbedtls_mpi_uint) (0x03) << 0 ) | ( (mbedtls_mpi_uint) (0xE6) << 8 ) | ( (mbedtls_mpi_uint) (0x0F) << 16 ) | ( (mbedtls_mpi_uint) (0x37) << 24 ), ( (mbedtls_mpi_uint) (0xC1) << 0 ) | ( (mbedtls_mpi_uint) (0x10) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x1E) << 24 ),
    ( (mbedtls_mpi_uint) (0x61) << 0 ) | ( (mbedtls_mpi_uint) (0xAD) << 8 ) | ( (mbedtls_mpi_uint) (0x9D) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ), ( (mbedtls_mpi_uint) (0x80) << 0 ) | ( (mbedtls_mpi_uint) (0x01) << 8 ) | ( (mbedtls_mpi_uint) (0xA6) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ),
    ( (mbedtls_mpi_uint) (0xB0) << 0 ) | ( (mbedtls_mpi_uint) (0x0F) << 8 ) | ( (mbedtls_mpi_uint) (0x10) << 16 ) | ( (mbedtls_mpi_uint) (0x2A) << 24 ), ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0x20) << 8 ) | ( (mbedtls_mpi_uint) (0x38) << 16 ) | ( (mbedtls_mpi_uint) (0xEB) << 24 ),
    ( (mbedtls_mpi_uint) (0x6C) << 0 ) | ( (mbedtls_mpi_uint) (0x60) << 8 ) | ( (mbedtls_mpi_uint) (0xCB) << 16 ) | ( (mbedtls_mpi_uint) (0xCE) << 24 ), ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0xA0) << 8 ) | ( (mbedtls_mpi_uint) (0xA7) << 16 ) | ( (mbedtls_mpi_uint) (0x32) << 24 ),
    ( (mbedtls_mpi_uint) (0xBA) << 0 ) | ( (mbedtls_mpi_uint) (0xCF) << 8 ) | ( (mbedtls_mpi_uint) (0x14) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ), ( (mbedtls_mpi_uint) (0xBF) << 0 ) | ( (mbedtls_mpi_uint) (0xE5) << 8 ) | ( (mbedtls_mpi_uint) (0x74) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ),
    ( (mbedtls_mpi_uint) (0xB5) << 0 ) | ( (mbedtls_mpi_uint) (0x12) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0xDD) << 24 ), ( (mbedtls_mpi_uint) (0x59) << 0 ) | ( (mbedtls_mpi_uint) (0x02) << 8 ) | ( (mbedtls_mpi_uint) (0x5D) << 16 ) | ( (mbedtls_mpi_uint) (0xC6) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_18_Y[] = {
    ( (mbedtls_mpi_uint) (0xC8) << 0 ) | ( (mbedtls_mpi_uint) (0xC9) << 8 ) | ( (mbedtls_mpi_uint) (0xF8) << 16 ) | ( (mbedtls_mpi_uint) (0xF5) << 24 ), ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x13) << 8 ) | ( (mbedtls_mpi_uint) (0x4D) << 16 ) | ( (mbedtls_mpi_uint) (0x7B) << 24 ),
    ( (mbedtls_mpi_uint) (0xED) << 0 ) | ( (mbedtls_mpi_uint) (0x45) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0x93) << 24 ), ( (mbedtls_mpi_uint) (0xB3) << 0 ) | ( (mbedtls_mpi_uint) (0xA2) << 8 ) | ( (mbedtls_mpi_uint) (0x79) << 16 ) | ( (mbedtls_mpi_uint) (0xDC) << 24 ),
    ( (mbedtls_mpi_uint) (0x74) << 0 ) | ( (mbedtls_mpi_uint) (0xF6) << 8 ) | ( (mbedtls_mpi_uint) (0xCF) << 16 ) | ( (mbedtls_mpi_uint) (0xF7) << 24 ), ( (mbedtls_mpi_uint) (0xE6) << 0 ) | ( (mbedtls_mpi_uint) (0x29) << 8 ) | ( (mbedtls_mpi_uint) (0x9C) << 16 ) | ( (mbedtls_mpi_uint) (0xCC) << 24 ),
    ( (mbedtls_mpi_uint) (0x87) << 0 ) | ( (mbedtls_mpi_uint) (0x50) << 8 ) | ( (mbedtls_mpi_uint) (0x65) << 16 ) | ( (mbedtls_mpi_uint) (0x80) << 24 ), ( (mbedtls_mpi_uint) (0xBC) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0x0A) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ),
    ( (mbedtls_mpi_uint) (0x0E) << 0 ) | ( (mbedtls_mpi_uint) (0xF0) << 8 ) | ( (mbedtls_mpi_uint) (0x24) << 16 ) | ( (mbedtls_mpi_uint) (0x35) << 24 ), ( (mbedtls_mpi_uint) (0xA2) << 0 ) | ( (mbedtls_mpi_uint) (0x46) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0x0C) << 24 ),
    ( (mbedtls_mpi_uint) (0xBD) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0xC0) << 16 ) | ( (mbedtls_mpi_uint) (0x9D) << 24 ), ( (mbedtls_mpi_uint) (0x61) << 0 ) | ( (mbedtls_mpi_uint) (0x56) << 8 ) | ( (mbedtls_mpi_uint) (0x62) << 16 ) | ( (mbedtls_mpi_uint) (0x67) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_19_X[] = {
    ( (mbedtls_mpi_uint) (0x10) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0x24) << 24 ), ( (mbedtls_mpi_uint) (0x43) << 0 ) | ( (mbedtls_mpi_uint) (0x2E) << 8 ) | ( (mbedtls_mpi_uint) (0x37) << 16 ) | ( (mbedtls_mpi_uint) (0x54) << 24 ),
    ( (mbedtls_mpi_uint) (0x8A) << 0 ) | ( (mbedtls_mpi_uint) (0xF7) << 8 ) | ( (mbedtls_mpi_uint) (0xCE) << 16 ) | ( (mbedtls_mpi_uint) (0x35) << 24 ), ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x77) << 8 ) | ( (mbedtls_mpi_uint) (0xF3) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ),
    ( (mbedtls_mpi_uint) (0x75) << 0 ) | ( (mbedtls_mpi_uint) (0x34) << 8 ) | ( (mbedtls_mpi_uint) (0x96) << 16 ) | ( (mbedtls_mpi_uint) (0xD5) << 24 ), ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0x9D) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ),
    ( (mbedtls_mpi_uint) (0xB8) << 0 ) | ( (mbedtls_mpi_uint) (0x3B) << 8 ) | ( (mbedtls_mpi_uint) (0x0F) << 16 ) | ( (mbedtls_mpi_uint) (0xEA) << 24 ), ( (mbedtls_mpi_uint) (0xA8) << 0 ) | ( (mbedtls_mpi_uint) (0x12) << 8 ) | ( (mbedtls_mpi_uint) (0x0B) << 16 ) | ( (mbedtls_mpi_uint) (0x22) << 24 ),
    ( (mbedtls_mpi_uint) (0x66) << 0 ) | ( (mbedtls_mpi_uint) (0x3F) << 8 ) | ( (mbedtls_mpi_uint) (0x5D) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ), ( (mbedtls_mpi_uint) (0x1C) << 0 ) | ( (mbedtls_mpi_uint) (0xD4) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0xFB) << 24 ),
    ( (mbedtls_mpi_uint) (0x7D) << 0 ) | ( (mbedtls_mpi_uint) (0x2E) << 8 ) | ( (mbedtls_mpi_uint) (0xDD) << 16 ) | ( (mbedtls_mpi_uint) (0xC7) << 24 ), ( (mbedtls_mpi_uint) (0x6E) << 0 ) | ( (mbedtls_mpi_uint) (0xAB) << 8 ) | ( (mbedtls_mpi_uint) (0xAF) << 16 ) | ( (mbedtls_mpi_uint) (0xDC) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_19_Y[] = {
    ( (mbedtls_mpi_uint) (0x8C) << 0 ) | ( (mbedtls_mpi_uint) (0xB2) << 8 ) | ( (mbedtls_mpi_uint) (0x7B) << 16 ) | ( (mbedtls_mpi_uint) (0x0C) << 24 ), ( (mbedtls_mpi_uint) (0x9A) << 0 ) | ( (mbedtls_mpi_uint) (0x83) << 8 ) | ( (mbedtls_mpi_uint) (0x8E) << 16 ) | ( (mbedtls_mpi_uint) (0x59) << 24 ),
    ( (mbedtls_mpi_uint) (0x30) << 0 ) | ( (mbedtls_mpi_uint) (0x51) << 8 ) | ( (mbedtls_mpi_uint) (0x90) << 16 ) | ( (mbedtls_mpi_uint) (0x92) << 24 ), ( (mbedtls_mpi_uint) (0x79) << 0 ) | ( (mbedtls_mpi_uint) (0x32) << 8 ) | ( (mbedtls_mpi_uint) (0x19) << 16 ) | ( (mbedtls_mpi_uint) (0xC3) << 24 ),
    ( (mbedtls_mpi_uint) (0xEE) << 0 ) | ( (mbedtls_mpi_uint) (0x89) << 8 ) | ( (mbedtls_mpi_uint) (0xF9) << 16 ) | ( (mbedtls_mpi_uint) (0xD0) << 24 ), ( (mbedtls_mpi_uint) (0xCF) << 0 ) | ( (mbedtls_mpi_uint) (0x2C) << 8 ) | ( (mbedtls_mpi_uint) (0xA5) << 16 ) | ( (mbedtls_mpi_uint) (0x8F) << 24 ),
    ( (mbedtls_mpi_uint) (0x7B) << 0 ) | ( (mbedtls_mpi_uint) (0x50) << 8 ) | ( (mbedtls_mpi_uint) (0x21) << 16 ) | ( (mbedtls_mpi_uint) (0xDE) << 24 ), ( (mbedtls_mpi_uint) (0x50) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x9D) << 16 ) | ( (mbedtls_mpi_uint) (0x81) << 24 ),
    ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x2B) << 16 ) | ( (mbedtls_mpi_uint) (0x9E) << 24 ), ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0x95) << 8 ) | ( (mbedtls_mpi_uint) (0xA8) << 16 ) | ( (mbedtls_mpi_uint) (0xE3) << 24 ),
    ( (mbedtls_mpi_uint) (0xD8) << 0 ) | ( (mbedtls_mpi_uint) (0xA5) << 8 ) | ( (mbedtls_mpi_uint) (0x20) << 16 ) | ( (mbedtls_mpi_uint) (0x87) << 24 ), ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0x97) << 8 ) | ( (mbedtls_mpi_uint) (0x5F) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_20_X[] = {
    ( (mbedtls_mpi_uint) (0x64) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0xB4) << 16 ) | ( (mbedtls_mpi_uint) (0x66) << 24 ), ( (mbedtls_mpi_uint) (0x7E) << 0 ) | ( (mbedtls_mpi_uint) (0xE8) << 8 ) | ( (mbedtls_mpi_uint) (0x5A) << 16 ) | ( (mbedtls_mpi_uint) (0x60) << 24 ),
    ( (mbedtls_mpi_uint) (0xA5) << 0 ) | ( (mbedtls_mpi_uint) (0x5C) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ), ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0xD9) << 8 ) | ( (mbedtls_mpi_uint) (0xC9) << 16 ) | ( (mbedtls_mpi_uint) (0xDA) << 24 ),
    ( (mbedtls_mpi_uint) (0x82) << 0 ) | ( (mbedtls_mpi_uint) (0x97) << 8 ) | ( (mbedtls_mpi_uint) (0x49) << 16 ) | ( (mbedtls_mpi_uint) (0xA3) << 24 ), ( (mbedtls_mpi_uint) (0x13) << 0 ) | ( (mbedtls_mpi_uint) (0x83) << 8 ) | ( (mbedtls_mpi_uint) (0x07) << 16 ) | ( (mbedtls_mpi_uint) (0x2E) << 24 ),
    ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0xC7) << 16 ) | ( (mbedtls_mpi_uint) (0x13) << 24 ), ( (mbedtls_mpi_uint) (0x35) << 0 ) | ( (mbedtls_mpi_uint) (0x0D) << 8 ) | ( (mbedtls_mpi_uint) (0xB0) << 16 ) | ( (mbedtls_mpi_uint) (0x6B) << 24 ),
    ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0x60) << 8 ) | ( (mbedtls_mpi_uint) (0xAB) << 16 ) | ( (mbedtls_mpi_uint) (0xFA) << 24 ), ( (mbedtls_mpi_uint) (0x4B) << 0 ) | ( (mbedtls_mpi_uint) (0x93) << 8 ) | ( (mbedtls_mpi_uint) (0x18) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ),
    ( (mbedtls_mpi_uint) (0x54) << 0 ) | ( (mbedtls_mpi_uint) (0x2D) << 8 ) | ( (mbedtls_mpi_uint) (0x1C) << 16 ) | ( (mbedtls_mpi_uint) (0x31) << 24 ), ( (mbedtls_mpi_uint) (0x4C) << 0 ) | ( (mbedtls_mpi_uint) (0xE4) << 8 ) | ( (mbedtls_mpi_uint) (0x61) << 16 ) | ( (mbedtls_mpi_uint) (0xAE) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_20_Y[] = {
    ( (mbedtls_mpi_uint) (0xDE) << 0 ) | ( (mbedtls_mpi_uint) (0x4D) << 8 ) | ( (mbedtls_mpi_uint) (0x1E) << 16 ) | ( (mbedtls_mpi_uint) (0x51) << 24 ), ( (mbedtls_mpi_uint) (0x59) << 0 ) | ( (mbedtls_mpi_uint) (0x6E) << 8 ) | ( (mbedtls_mpi_uint) (0x91) << 16 ) | ( (mbedtls_mpi_uint) (0xC5) << 24 ),
    ( (mbedtls_mpi_uint) (0x38) << 0 ) | ( (mbedtls_mpi_uint) (0x54) << 8 ) | ( (mbedtls_mpi_uint) (0x4D) << 16 ) | ( (mbedtls_mpi_uint) (0x51) << 24 ), ( (mbedtls_mpi_uint) (0xED) << 0 ) | ( (mbedtls_mpi_uint) (0x36) << 8 ) | ( (mbedtls_mpi_uint) (0xCC) << 16 ) | ( (mbedtls_mpi_uint) (0x60) << 24 ),
    ( (mbedtls_mpi_uint) (0x18) << 0 ) | ( (mbedtls_mpi_uint) (0xA8) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0xC7) << 24 ), ( (mbedtls_mpi_uint) (0x78) << 0 ) | ( (mbedtls_mpi_uint) (0x27) << 8 ) | ( (mbedtls_mpi_uint) (0x33) << 16 ) | ( (mbedtls_mpi_uint) (0xC5) << 24 ),
    ( (mbedtls_mpi_uint) (0x42) << 0 ) | ( (mbedtls_mpi_uint) (0xB7) << 8 ) | ( (mbedtls_mpi_uint) (0x95) << 16 ) | ( (mbedtls_mpi_uint) (0xC9) << 24 ), ( (mbedtls_mpi_uint) (0x8B) << 0 ) | ( (mbedtls_mpi_uint) (0xC8) << 8 ) | ( (mbedtls_mpi_uint) (0x6A) << 16 ) | ( (mbedtls_mpi_uint) (0xBC) << 24 ),
    ( (mbedtls_mpi_uint) (0x5E) << 0 ) | ( (mbedtls_mpi_uint) (0xE9) << 8 ) | ( (mbedtls_mpi_uint) (0x13) << 16 ) | ( (mbedtls_mpi_uint) (0x96) << 24 ), ( (mbedtls_mpi_uint) (0xB3) << 0 ) | ( (mbedtls_mpi_uint) (0xE1) << 8 ) | ( (mbedtls_mpi_uint) (0xF9) << 16 ) | ( (mbedtls_mpi_uint) (0xEE) << 24 ),
    ( (mbedtls_mpi_uint) (0xF5) << 0 ) | ( (mbedtls_mpi_uint) (0x46) << 8 ) | ( (mbedtls_mpi_uint) (0xB0) << 16 ) | ( (mbedtls_mpi_uint) (0x5E) << 24 ), ( (mbedtls_mpi_uint) (0xC3) << 0 ) | ( (mbedtls_mpi_uint) (0x94) << 8 ) | ( (mbedtls_mpi_uint) (0x03) << 16 ) | ( (mbedtls_mpi_uint) (0x05) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_21_X[] = {
    ( (mbedtls_mpi_uint) (0x6D) << 0 ) | ( (mbedtls_mpi_uint) (0x5B) << 8 ) | ( (mbedtls_mpi_uint) (0x29) << 16 ) | ( (mbedtls_mpi_uint) (0x30) << 24 ), ( (mbedtls_mpi_uint) (0x41) << 0 ) | ( (mbedtls_mpi_uint) (0x1A) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ),
    ( (mbedtls_mpi_uint) (0x76) << 0 ) | ( (mbedtls_mpi_uint) (0xCA) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0x31) << 24 ), ( (mbedtls_mpi_uint) (0x5B) << 0 ) | ( (mbedtls_mpi_uint) (0xA7) << 8 ) | ( (mbedtls_mpi_uint) (0xCB) << 16 ) | ( (mbedtls_mpi_uint) (0x42) << 24 ),
    ( (mbedtls_mpi_uint) (0x21) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0x50) << 16 ) | ( (mbedtls_mpi_uint) (0x44) << 24 ), ( (mbedtls_mpi_uint) (0x4D) << 0 ) | ( (mbedtls_mpi_uint) (0x64) << 8 ) | ( (mbedtls_mpi_uint) (0x31) << 16 ) | ( (mbedtls_mpi_uint) (0x89) << 24 ),
    ( (mbedtls_mpi_uint) (0xCF) << 0 ) | ( (mbedtls_mpi_uint) (0x84) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ), ( (mbedtls_mpi_uint) (0x97) << 0 ) | ( (mbedtls_mpi_uint) (0xA5) << 8 ) | ( (mbedtls_mpi_uint) (0x3C) << 16 ) | ( (mbedtls_mpi_uint) (0x18) << 24 ),
    ( (mbedtls_mpi_uint) (0xF0) << 0 ) | ( (mbedtls_mpi_uint) (0x0F) << 8 ) | ( (mbedtls_mpi_uint) (0xA5) << 16 ) | ( (mbedtls_mpi_uint) (0xFD) << 24 ), ( (mbedtls_mpi_uint) (0x8E) << 0 ) | ( (mbedtls_mpi_uint) (0x5A) << 8 ) | ( (mbedtls_mpi_uint) (0x47) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ),
    ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0x58) << 8 ) | ( (mbedtls_mpi_uint) (0x02) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ), ( (mbedtls_mpi_uint) (0x40) << 0 ) | ( (mbedtls_mpi_uint) (0xB1) << 8 ) | ( (mbedtls_mpi_uint) (0x0B) << 16 ) | ( (mbedtls_mpi_uint) (0xBA) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_21_Y[] = {
    ( (mbedtls_mpi_uint) (0xDA) << 0 ) | ( (mbedtls_mpi_uint) (0x33) << 8 ) | ( (mbedtls_mpi_uint) (0x8C) << 16 ) | ( (mbedtls_mpi_uint) (0x67) << 24 ), ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0x23) << 8 ) | ( (mbedtls_mpi_uint) (0x43) << 16 ) | ( (mbedtls_mpi_uint) (0x99) << 24 ),
    ( (mbedtls_mpi_uint) (0x84) << 0 ) | ( (mbedtls_mpi_uint) (0x53) << 8 ) | ( (mbedtls_mpi_uint) (0x47) << 16 ) | ( (mbedtls_mpi_uint) (0x72) << 24 ), ( (mbedtls_mpi_uint) (0x44) << 0 ) | ( (mbedtls_mpi_uint) (0x1F) << 8 ) | ( (mbedtls_mpi_uint) (0x5B) << 16 ) | ( (mbedtls_mpi_uint) (0x2A) << 24 ),
    ( (mbedtls_mpi_uint) (0xAE) << 0 ) | ( (mbedtls_mpi_uint) (0xC1) << 8 ) | ( (mbedtls_mpi_uint) (0xD9) << 16 ) | ( (mbedtls_mpi_uint) (0xA4) << 24 ), ( (mbedtls_mpi_uint) (0x50) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0x63) << 16 ) | ( (mbedtls_mpi_uint) (0x18) << 24 ),
    ( (mbedtls_mpi_uint) (0x7C) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0x75) << 16 ) | ( (mbedtls_mpi_uint) (0x69) << 24 ), ( (mbedtls_mpi_uint) (0x73) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0xC4) << 16 ) | ( (mbedtls_mpi_uint) (0x31) << 24 ),
    ( (mbedtls_mpi_uint) (0x4B) << 0 ) | ( (mbedtls_mpi_uint) (0x90) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ), ( (mbedtls_mpi_uint) (0x1A) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0xD8) << 16 ) | ( (mbedtls_mpi_uint) (0x69) << 24 ),
    ( (mbedtls_mpi_uint) (0x05) << 0 ) | ( (mbedtls_mpi_uint) (0xB1) << 8 ) | ( (mbedtls_mpi_uint) (0x89) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ), ( (mbedtls_mpi_uint) (0xA8) << 0 ) | ( (mbedtls_mpi_uint) (0x70) << 8 ) | ( (mbedtls_mpi_uint) (0x62) << 16 ) | ( (mbedtls_mpi_uint) (0xEF) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_22_X[] = {
    ( (mbedtls_mpi_uint) (0x7E) << 0 ) | ( (mbedtls_mpi_uint) (0x8A) << 8 ) | ( (mbedtls_mpi_uint) (0x55) << 16 ) | ( (mbedtls_mpi_uint) (0x50) << 24 ), ( (mbedtls_mpi_uint) (0x7B) << 0 ) | ( (mbedtls_mpi_uint) (0xEF) << 8 ) | ( (mbedtls_mpi_uint) (0x8A) << 16 ) | ( (mbedtls_mpi_uint) (0x3C) << 24 ),
    ( (mbedtls_mpi_uint) (0xFE) << 0 ) | ( (mbedtls_mpi_uint) (0x1B) << 8 ) | ( (mbedtls_mpi_uint) (0x23) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ), ( (mbedtls_mpi_uint) (0x23) << 0 ) | ( (mbedtls_mpi_uint) (0x63) << 8 ) | ( (mbedtls_mpi_uint) (0x91) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ),
    ( (mbedtls_mpi_uint) (0x0D) << 0 ) | ( (mbedtls_mpi_uint) (0x04) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x3C) << 24 ), ( (mbedtls_mpi_uint) (0x24) << 0 ) | ( (mbedtls_mpi_uint) (0x9B) << 8 ) | ( (mbedtls_mpi_uint) (0xC7) << 16 ) | ( (mbedtls_mpi_uint) (0x9A) << 24 ),
    ( (mbedtls_mpi_uint) (0x25) << 0 ) | ( (mbedtls_mpi_uint) (0x38) << 8 ) | ( (mbedtls_mpi_uint) (0xC3) << 16 ) | ( (mbedtls_mpi_uint) (0x84) << 24 ), ( (mbedtls_mpi_uint) (0xFB) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0x49) << 24 ),
    ( (mbedtls_mpi_uint) (0x66) << 0 ) | ( (mbedtls_mpi_uint) (0x2A) << 8 ) | ( (mbedtls_mpi_uint) (0xE0) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ), ( (mbedtls_mpi_uint) (0x68) << 0 ) | ( (mbedtls_mpi_uint) (0x8A) << 8 ) | ( (mbedtls_mpi_uint) (0x5C) << 16 ) | ( (mbedtls_mpi_uint) (0xCB) << 24 ),
    ( (mbedtls_mpi_uint) (0xC4) << 0 ) | ( (mbedtls_mpi_uint) (0x93) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0x85) << 24 ), ( (mbedtls_mpi_uint) (0xA1) << 0 ) | ( (mbedtls_mpi_uint) (0x0D) << 8 ) | ( (mbedtls_mpi_uint) (0xAF) << 16 ) | ( (mbedtls_mpi_uint) (0x63) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_22_Y[] = {
    ( (mbedtls_mpi_uint) (0x1B) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0x95) << 16 ) | ( (mbedtls_mpi_uint) (0x4C) << 24 ), ( (mbedtls_mpi_uint) (0x0B) << 0 ) | ( (mbedtls_mpi_uint) (0xD0) << 8 ) | ( (mbedtls_mpi_uint) (0x06) << 16 ) | ( (mbedtls_mpi_uint) (0x51) << 24 ),
    ( (mbedtls_mpi_uint) (0x92) << 0 ) | ( (mbedtls_mpi_uint) (0xAF) << 8 ) | ( (mbedtls_mpi_uint) (0x8D) << 16 ) | ( (mbedtls_mpi_uint) (0x49) << 24 ), ( (mbedtls_mpi_uint) (0xA2) << 0 ) | ( (mbedtls_mpi_uint) (0xC8) << 8 ) | ( (mbedtls_mpi_uint) (0xB4) << 16 ) | ( (mbedtls_mpi_uint) (0xE0) << 24 ),
    ( (mbedtls_mpi_uint) (0x75) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0x09) << 24 ), ( (mbedtls_mpi_uint) (0x88) << 0 ) | ( (mbedtls_mpi_uint) (0x43) << 8 ) | ( (mbedtls_mpi_uint) (0x87) << 16 ) | ( (mbedtls_mpi_uint) (0xCA) << 24 ),
    ( (mbedtls_mpi_uint) (0x90) << 0 ) | ( (mbedtls_mpi_uint) (0xA4) << 8 ) | ( (mbedtls_mpi_uint) (0x77) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ), ( (mbedtls_mpi_uint) (0x5E) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0xB4) << 16 ) | ( (mbedtls_mpi_uint) (0x0A) << 24 ),
    ( (mbedtls_mpi_uint) (0x35) << 0 ) | ( (mbedtls_mpi_uint) (0x9E) << 8 ) | ( (mbedtls_mpi_uint) (0x86) << 16 ) | ( (mbedtls_mpi_uint) (0x64) << 24 ), ( (mbedtls_mpi_uint) (0xCC) << 0 ) | ( (mbedtls_mpi_uint) (0x91) << 8 ) | ( (mbedtls_mpi_uint) (0xC1) << 16 ) | ( (mbedtls_mpi_uint) (0x77) << 24 ),
    ( (mbedtls_mpi_uint) (0xC1) << 0 ) | ( (mbedtls_mpi_uint) (0x17) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0xCB) << 24 ), ( (mbedtls_mpi_uint) (0xC3) << 0 ) | ( (mbedtls_mpi_uint) (0x7D) << 8 ) | ( (mbedtls_mpi_uint) (0x5B) << 16 ) | ( (mbedtls_mpi_uint) (0xB1) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_23_X[] = {
    ( (mbedtls_mpi_uint) (0x64) << 0 ) | ( (mbedtls_mpi_uint) (0x74) << 8 ) | ( (mbedtls_mpi_uint) (0x9F) << 16 ) | ( (mbedtls_mpi_uint) (0xB5) << 24 ), ( (mbedtls_mpi_uint) (0x91) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0x1C) << 24 ),
    ( (mbedtls_mpi_uint) (0x1E) << 0 ) | ( (mbedtls_mpi_uint) (0xED) << 8 ) | ( (mbedtls_mpi_uint) (0xE1) << 16 ) | ( (mbedtls_mpi_uint) (0x11) << 24 ), ( (mbedtls_mpi_uint) (0xEF) << 0 ) | ( (mbedtls_mpi_uint) (0x45) << 8 ) | ( (mbedtls_mpi_uint) (0xAF) << 16 ) | ( (mbedtls_mpi_uint) (0xC1) << 24 ),
    ( (mbedtls_mpi_uint) (0xE0) << 0 ) | ( (mbedtls_mpi_uint) (0x31) << 8 ) | ( (mbedtls_mpi_uint) (0xBE) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ), ( (mbedtls_mpi_uint) (0xBC) << 0 ) | ( (mbedtls_mpi_uint) (0x72) << 8 ) | ( (mbedtls_mpi_uint) (0x65) << 16 ) | ( (mbedtls_mpi_uint) (0x1F) << 24 ),
    ( (mbedtls_mpi_uint) (0xB1) << 0 ) | ( (mbedtls_mpi_uint) (0x4B) << 8 ) | ( (mbedtls_mpi_uint) (0x8C) << 16 ) | ( (mbedtls_mpi_uint) (0x77) << 24 ), ( (mbedtls_mpi_uint) (0xCE) << 0 ) | ( (mbedtls_mpi_uint) (0x1E) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0xB5) << 24 ),
    ( (mbedtls_mpi_uint) (0xFF) << 0 ) | ( (mbedtls_mpi_uint) (0xC9) << 8 ) | ( (mbedtls_mpi_uint) (0xAA) << 16 ) | ( (mbedtls_mpi_uint) (0xB9) << 24 ), ( (mbedtls_mpi_uint) (0xD9) << 0 ) | ( (mbedtls_mpi_uint) (0x86) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x55) << 24 ),
    ( (mbedtls_mpi_uint) (0x65) << 0 ) | ( (mbedtls_mpi_uint) (0x23) << 8 ) | ( (mbedtls_mpi_uint) (0x80) << 16 ) | ( (mbedtls_mpi_uint) (0xC6) << 24 ), ( (mbedtls_mpi_uint) (0x4E) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0x0B) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_23_Y[] = {
    ( (mbedtls_mpi_uint) (0x47) << 0 ) | ( (mbedtls_mpi_uint) (0xD8) << 8 ) | ( (mbedtls_mpi_uint) (0xA2) << 16 ) | ( (mbedtls_mpi_uint) (0x0A) << 24 ), ( (mbedtls_mpi_uint) (0x39) << 0 ) | ( (mbedtls_mpi_uint) (0x32) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x23) << 24 ),
    ( (mbedtls_mpi_uint) (0x61) << 0 ) | ( (mbedtls_mpi_uint) (0xC8) << 8 ) | ( (mbedtls_mpi_uint) (0x86) << 16 ) | ( (mbedtls_mpi_uint) (0xF1) << 24 ), ( (mbedtls_mpi_uint) (0x12) << 0 ) | ( (mbedtls_mpi_uint) (0x9A) << 8 ) | ( (mbedtls_mpi_uint) (0x4A) << 16 ) | ( (mbedtls_mpi_uint) (0x05) << 24 ),
    ( (mbedtls_mpi_uint) (0x8D) << 0 ) | ( (mbedtls_mpi_uint) (0xF1) << 8 ) | ( (mbedtls_mpi_uint) (0x7C) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ), ( (mbedtls_mpi_uint) (0x70) << 0 ) | ( (mbedtls_mpi_uint) (0x8E) << 8 ) | ( (mbedtls_mpi_uint) (0xBC) << 16 ) | ( (mbedtls_mpi_uint) (0x01) << 24 ),
    ( (mbedtls_mpi_uint) (0x62) << 0 ) | ( (mbedtls_mpi_uint) (0x01) << 8 ) | ( (mbedtls_mpi_uint) (0x47) << 16 ) | ( (mbedtls_mpi_uint) (0x8F) << 24 ), ( (mbedtls_mpi_uint) (0xDD) << 0 ) | ( (mbedtls_mpi_uint) (0x8B) << 8 ) | ( (mbedtls_mpi_uint) (0xA5) << 16 ) | ( (mbedtls_mpi_uint) (0xC8) << 24 ),
    ( (mbedtls_mpi_uint) (0xDB) << 0 ) | ( (mbedtls_mpi_uint) (0x08) << 8 ) | ( (mbedtls_mpi_uint) (0x21) << 16 ) | ( (mbedtls_mpi_uint) (0xF4) << 24 ), ( (mbedtls_mpi_uint) (0xAB) << 0 ) | ( (mbedtls_mpi_uint) (0xC7) << 8 ) | ( (mbedtls_mpi_uint) (0xF5) << 16 ) | ( (mbedtls_mpi_uint) (0x96) << 24 ),
    ( (mbedtls_mpi_uint) (0x0A) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0xA5) << 16 ) | ( (mbedtls_mpi_uint) (0x95) << 24 ), ( (mbedtls_mpi_uint) (0xC4) << 0 ) | ( (mbedtls_mpi_uint) (0x0F) << 8 ) | ( (mbedtls_mpi_uint) (0x88) << 16 ) | ( (mbedtls_mpi_uint) (0x1D) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_24_X[] = {
    ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0x2A) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ), ( (mbedtls_mpi_uint) (0xCD) << 0 ) | ( (mbedtls_mpi_uint) (0x75) << 8 ) | ( (mbedtls_mpi_uint) (0x51) << 16 ) | ( (mbedtls_mpi_uint) (0x49) << 24 ),
    ( (mbedtls_mpi_uint) (0x90) << 0 ) | ( (mbedtls_mpi_uint) (0x36) << 8 ) | ( (mbedtls_mpi_uint) (0xE5) << 16 ) | ( (mbedtls_mpi_uint) (0x04) << 24 ), ( (mbedtls_mpi_uint) (0x2B) << 0 ) | ( (mbedtls_mpi_uint) (0x44) << 8 ) | ( (mbedtls_mpi_uint) (0xC6) << 16 ) | ( (mbedtls_mpi_uint) (0xEF) << 24 ),
    ( (mbedtls_mpi_uint) (0x5C) << 0 ) | ( (mbedtls_mpi_uint) (0xEE) << 8 ) | ( (mbedtls_mpi_uint) (0x16) << 16 ) | ( (mbedtls_mpi_uint) (0x13) << 24 ), ( (mbedtls_mpi_uint) (0x07) << 0 ) | ( (mbedtls_mpi_uint) (0x83) << 8 ) | ( (mbedtls_mpi_uint) (0xB5) << 16 ) | ( (mbedtls_mpi_uint) (0x30) << 24 ),
    ( (mbedtls_mpi_uint) (0x76) << 0 ) | ( (mbedtls_mpi_uint) (0x59) << 8 ) | ( (mbedtls_mpi_uint) (0xC6) << 16 ) | ( (mbedtls_mpi_uint) (0xA2) << 24 ), ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0x05) << 8 ) | ( (mbedtls_mpi_uint) (0xD3) << 16 ) | ( (mbedtls_mpi_uint) (0xC6) << 24 ),
    ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x8B) << 8 ) | ( (mbedtls_mpi_uint) (0xA8) << 16 ) | ( (mbedtls_mpi_uint) (0x16) << 24 ), ( (mbedtls_mpi_uint) (0x09) << 0 ) | ( (mbedtls_mpi_uint) (0xB7) << 8 ) | ( (mbedtls_mpi_uint) (0xEA) << 16 ) | ( (mbedtls_mpi_uint) (0xD6) << 24 ),
    ( (mbedtls_mpi_uint) (0x70) << 0 ) | ( (mbedtls_mpi_uint) (0xEE) << 8 ) | ( (mbedtls_mpi_uint) (0x14) << 16 ) | ( (mbedtls_mpi_uint) (0xAF) << 24 ), ( (mbedtls_mpi_uint) (0xB5) << 0 ) | ( (mbedtls_mpi_uint) (0xFD) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0xEF) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_24_Y[] = {
    ( (mbedtls_mpi_uint) (0x18) << 0 ) | ( (mbedtls_mpi_uint) (0x7C) << 8 ) | ( (mbedtls_mpi_uint) (0xCA) << 16 ) | ( (mbedtls_mpi_uint) (0x71) << 24 ), ( (mbedtls_mpi_uint) (0x3E) << 0 ) | ( (mbedtls_mpi_uint) (0x6E) << 8 ) | ( (mbedtls_mpi_uint) (0x66) << 16 ) | ( (mbedtls_mpi_uint) (0x75) << 24 ),
    ( (mbedtls_mpi_uint) (0xBE) << 0 ) | ( (mbedtls_mpi_uint) (0x31) << 8 ) | ( (mbedtls_mpi_uint) (0x0E) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ), ( (mbedtls_mpi_uint) (0xE5) << 0 ) | ( (mbedtls_mpi_uint) (0x91) << 8 ) | ( (mbedtls_mpi_uint) (0xC4) << 16 ) | ( (mbedtls_mpi_uint) (0x7F) << 24 ),
    ( (mbedtls_mpi_uint) (0x8E) << 0 ) | ( (mbedtls_mpi_uint) (0x3D) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0x3E) << 24 ), ( (mbedtls_mpi_uint) (0x95) << 0 ) | ( (mbedtls_mpi_uint) (0x37) << 8 ) | ( (mbedtls_mpi_uint) (0x58) << 16 ) | ( (mbedtls_mpi_uint) (0x2B) << 24 ),
    ( (mbedtls_mpi_uint) (0x01) << 0 ) | ( (mbedtls_mpi_uint) (0x1F) << 8 ) | ( (mbedtls_mpi_uint) (0x02) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ), ( (mbedtls_mpi_uint) (0xF3) << 0 ) | ( (mbedtls_mpi_uint) (0xEF) << 8 ) | ( (mbedtls_mpi_uint) (0xEE) << 16 ) | ( (mbedtls_mpi_uint) (0x66) << 24 ),
    ( (mbedtls_mpi_uint) (0x28) << 0 ) | ( (mbedtls_mpi_uint) (0x5B) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0xFC) << 24 ), ( (mbedtls_mpi_uint) (0x38) << 0 ) | ( (mbedtls_mpi_uint) (0xCD) << 8 ) | ( (mbedtls_mpi_uint) (0xE8) << 16 ) | ( (mbedtls_mpi_uint) (0x24) << 24 ),
    ( (mbedtls_mpi_uint) (0x12) << 0 ) | ( (mbedtls_mpi_uint) (0x57) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0x85) << 24 ), ( (mbedtls_mpi_uint) (0xC6) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0x68) << 16 ) | ( (mbedtls_mpi_uint) (0x71) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_25_X[] = {
    ( (mbedtls_mpi_uint) (0x8D) << 0 ) | ( (mbedtls_mpi_uint) (0xA2) << 8 ) | ( (mbedtls_mpi_uint) (0x4A) << 16 ) | ( (mbedtls_mpi_uint) (0x66) << 24 ), ( (mbedtls_mpi_uint) (0xB1) << 0 ) | ( (mbedtls_mpi_uint) (0x0A) << 8 ) | ( (mbedtls_mpi_uint) (0xE6) << 16 ) | ( (mbedtls_mpi_uint) (0xC0) << 24 ),
    ( (mbedtls_mpi_uint) (0x86) << 0 ) | ( (mbedtls_mpi_uint) (0x0C) << 8 ) | ( (mbedtls_mpi_uint) (0x94) << 16 ) | ( (mbedtls_mpi_uint) (0x9D) << 24 ), ( (mbedtls_mpi_uint) (0x5E) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0xB2) << 16 ) | ( (mbedtls_mpi_uint) (0xCE) << 24 ),
    ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0x03) << 8 ) | ( (mbedtls_mpi_uint) (0x40) << 16 ) | ( (mbedtls_mpi_uint) (0xCA) << 24 ), ( (mbedtls_mpi_uint) (0xB2) << 0 ) | ( (mbedtls_mpi_uint) (0xB3) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0x55) << 24 ),
    ( (mbedtls_mpi_uint) (0x74) << 0 ) | ( (mbedtls_mpi_uint) (0x78) << 8 ) | ( (mbedtls_mpi_uint) (0x48) << 16 ) | ( (mbedtls_mpi_uint) (0x27) << 24 ), ( (mbedtls_mpi_uint) (0x34) << 0 ) | ( (mbedtls_mpi_uint) (0x1E) << 8 ) | ( (mbedtls_mpi_uint) (0xE2) << 16 ) | ( (mbedtls_mpi_uint) (0x42) << 24 ),
    ( (mbedtls_mpi_uint) (0xAE) << 0 ) | ( (mbedtls_mpi_uint) (0x72) << 8 ) | ( (mbedtls_mpi_uint) (0x5B) << 16 ) | ( (mbedtls_mpi_uint) (0xAC) << 24 ), ( (mbedtls_mpi_uint) (0xC1) << 0 ) | ( (mbedtls_mpi_uint) (0x6D) << 8 ) | ( (mbedtls_mpi_uint) (0xE3) << 16 ) | ( (mbedtls_mpi_uint) (0x82) << 24 ),
    ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0xAB) << 8 ) | ( (mbedtls_mpi_uint) (0x46) << 16 ) | ( (mbedtls_mpi_uint) (0xCB) << 24 ), ( (mbedtls_mpi_uint) (0xEA) << 0 ) | ( (mbedtls_mpi_uint) (0x5E) << 8 ) | ( (mbedtls_mpi_uint) (0x4B) << 16 ) | ( (mbedtls_mpi_uint) (0x0B) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_25_Y[] = {
    ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x08) << 8 ) | ( (mbedtls_mpi_uint) (0xAD) << 16 ) | ( (mbedtls_mpi_uint) (0x4E) << 24 ), ( (mbedtls_mpi_uint) (0x51) << 0 ) | ( (mbedtls_mpi_uint) (0x9F) << 8 ) | ( (mbedtls_mpi_uint) (0x2A) << 16 ) | ( (mbedtls_mpi_uint) (0x52) << 24 ),
    ( (mbedtls_mpi_uint) (0x68) << 0 ) | ( (mbedtls_mpi_uint) (0x5C) << 8 ) | ( (mbedtls_mpi_uint) (0x7D) << 16 ) | ( (mbedtls_mpi_uint) (0x4C) << 24 ), ( (mbedtls_mpi_uint) (0xD6) << 0 ) | ( (mbedtls_mpi_uint) (0xCF) << 8 ) | ( (mbedtls_mpi_uint) (0xDD) << 16 ) | ( (mbedtls_mpi_uint) (0x02) << 24 ),
    ( (mbedtls_mpi_uint) (0xD8) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0x26) << 16 ) | ( (mbedtls_mpi_uint) (0xE0) << 24 ), ( (mbedtls_mpi_uint) (0x8B) << 0 ) | ( (mbedtls_mpi_uint) (0x10) << 8 ) | ( (mbedtls_mpi_uint) (0xD9) << 16 ) | ( (mbedtls_mpi_uint) (0x7C) << 24 ),
    ( (mbedtls_mpi_uint) (0x30) << 0 ) | ( (mbedtls_mpi_uint) (0xA7) << 8 ) | ( (mbedtls_mpi_uint) (0x23) << 16 ) | ( (mbedtls_mpi_uint) (0x4E) << 24 ), ( (mbedtls_mpi_uint) (0x5F) << 0 ) | ( (mbedtls_mpi_uint) (0xD2) << 8 ) | ( (mbedtls_mpi_uint) (0x42) << 16 ) | ( (mbedtls_mpi_uint) (0x17) << 24 ),
    ( (mbedtls_mpi_uint) (0xD1) << 0 ) | ( (mbedtls_mpi_uint) (0xE5) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0xEC) << 24 ), ( (mbedtls_mpi_uint) (0x77) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0x34) << 16 ) | ( (mbedtls_mpi_uint) (0x28) << 24 ),
    ( (mbedtls_mpi_uint) (0x5C) << 0 ) | ( (mbedtls_mpi_uint) (0x14) << 8 ) | ( (mbedtls_mpi_uint) (0x65) << 16 ) | ( (mbedtls_mpi_uint) (0xEA) << 24 ), ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0x85) << 8 ) | ( (mbedtls_mpi_uint) (0xC3) << 16 ) | ( (mbedtls_mpi_uint) (0x2F) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_26_X[] = {
    ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0xD8) << 8 ) | ( (mbedtls_mpi_uint) (0x40) << 16 ) | ( (mbedtls_mpi_uint) (0x27) << 24 ), ( (mbedtls_mpi_uint) (0x73) << 0 ) | ( (mbedtls_mpi_uint) (0x15) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0x65) << 24 ),
    ( (mbedtls_mpi_uint) (0xF6) << 0 ) | ( (mbedtls_mpi_uint) (0xBB) << 8 ) | ( (mbedtls_mpi_uint) (0x53) << 16 ) | ( (mbedtls_mpi_uint) (0x7E) << 24 ), ( (mbedtls_mpi_uint) (0x0F) << 0 ) | ( (mbedtls_mpi_uint) (0x40) << 8 ) | ( (mbedtls_mpi_uint) (0xC8) << 16 ) | ( (mbedtls_mpi_uint) (0xD4) << 24 ),
    ( (mbedtls_mpi_uint) (0xEA) << 0 ) | ( (mbedtls_mpi_uint) (0x37) << 8 ) | ( (mbedtls_mpi_uint) (0x19) << 16 ) | ( (mbedtls_mpi_uint) (0x73) << 24 ), ( (mbedtls_mpi_uint) (0xEF) << 0 ) | ( (mbedtls_mpi_uint) (0x5A) << 8 ) | ( (mbedtls_mpi_uint) (0x5E) << 16 ) | ( (mbedtls_mpi_uint) (0x04) << 24 ),
    ( (mbedtls_mpi_uint) (0x9C) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0x2B) << 16 ) | ( (mbedtls_mpi_uint) (0x49) << 24 ), ( (mbedtls_mpi_uint) (0x7E) << 0 ) | ( (mbedtls_mpi_uint) (0xAC) << 8 ) | ( (mbedtls_mpi_uint) (0x97) << 16 ) | ( (mbedtls_mpi_uint) (0x5C) << 24 ),
    ( (mbedtls_mpi_uint) (0x15) << 0 ) | ( (mbedtls_mpi_uint) (0xB2) << 8 ) | ( (mbedtls_mpi_uint) (0xC3) << 16 ) | ( (mbedtls_mpi_uint) (0x1E) << 24 ), ( (mbedtls_mpi_uint) (0x0E) << 0 ) | ( (mbedtls_mpi_uint) (0xE7) << 8 ) | ( (mbedtls_mpi_uint) (0xD2) << 16 ) | ( (mbedtls_mpi_uint) (0x21) << 24 ),
    ( (mbedtls_mpi_uint) (0x8A) << 0 ) | ( (mbedtls_mpi_uint) (0x08) << 8 ) | ( (mbedtls_mpi_uint) (0xD6) << 16 ) | ( (mbedtls_mpi_uint) (0xDD) << 24 ), ( (mbedtls_mpi_uint) (0xAC) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0xD6) << 16 ) | ( (mbedtls_mpi_uint) (0x3E) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_26_Y[] = {
    ( (mbedtls_mpi_uint) (0xA9) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0xBE) << 16 ) | ( (mbedtls_mpi_uint) (0x6D) << 24 ), ( (mbedtls_mpi_uint) (0x6D) << 0 ) | ( (mbedtls_mpi_uint) (0xF2) << 8 ) | ( (mbedtls_mpi_uint) (0x38) << 16 ) | ( (mbedtls_mpi_uint) (0x3F) << 24 ),
    ( (mbedtls_mpi_uint) (0x08) << 0 ) | ( (mbedtls_mpi_uint) (0x6C) << 8 ) | ( (mbedtls_mpi_uint) (0x31) << 16 ) | ( (mbedtls_mpi_uint) (0xA7) << 24 ), ( (mbedtls_mpi_uint) (0x49) << 0 ) | ( (mbedtls_mpi_uint) (0x50) << 8 ) | ( (mbedtls_mpi_uint) (0x3A) << 16 ) | ( (mbedtls_mpi_uint) (0x89) << 24 ),
    ( (mbedtls_mpi_uint) (0xC3) << 0 ) | ( (mbedtls_mpi_uint) (0x99) << 8 ) | ( (mbedtls_mpi_uint) (0xC6) << 16 ) | ( (mbedtls_mpi_uint) (0xF5) << 24 ), ( (mbedtls_mpi_uint) (0xD2) << 0 ) | ( (mbedtls_mpi_uint) (0xC2) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0x5A) << 24 ),
    ( (mbedtls_mpi_uint) (0x2A) << 0 ) | ( (mbedtls_mpi_uint) (0xE4) << 8 ) | ( (mbedtls_mpi_uint) (0xF6) << 16 ) | ( (mbedtls_mpi_uint) (0x8B) << 24 ), ( (mbedtls_mpi_uint) (0x8B) << 0 ) | ( (mbedtls_mpi_uint) (0x97) << 8 ) | ( (mbedtls_mpi_uint) (0xE9) << 16 ) | ( (mbedtls_mpi_uint) (0xB2) << 24 ),
    ( (mbedtls_mpi_uint) (0xDD) << 0 ) | ( (mbedtls_mpi_uint) (0x21) << 8 ) | ( (mbedtls_mpi_uint) (0xB7) << 16 ) | ( (mbedtls_mpi_uint) (0x0D) << 24 ), ( (mbedtls_mpi_uint) (0xFC) << 0 ) | ( (mbedtls_mpi_uint) (0x15) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x0B) << 24 ),
    ( (mbedtls_mpi_uint) (0x65) << 0 ) | ( (mbedtls_mpi_uint) (0x83) << 8 ) | ( (mbedtls_mpi_uint) (0x1C) << 16 ) | ( (mbedtls_mpi_uint) (0xA4) << 24 ), ( (mbedtls_mpi_uint) (0xCD) << 0 ) | ( (mbedtls_mpi_uint) (0x6B) << 8 ) | ( (mbedtls_mpi_uint) (0x9D) << 16 ) | ( (mbedtls_mpi_uint) (0xF2) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_27_X[] = {
    ( (mbedtls_mpi_uint) (0xD6) << 0 ) | ( (mbedtls_mpi_uint) (0xE8) << 8 ) | ( (mbedtls_mpi_uint) (0x4C) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ), ( (mbedtls_mpi_uint) (0xE4) << 0 ) | ( (mbedtls_mpi_uint) (0xAA) << 8 ) | ( (mbedtls_mpi_uint) (0x69) << 16 ) | ( (mbedtls_mpi_uint) (0x93) << 24 ),
    ( (mbedtls_mpi_uint) (0x27) << 0 ) | ( (mbedtls_mpi_uint) (0x7A) << 8 ) | ( (mbedtls_mpi_uint) (0x27) << 16 ) | ( (mbedtls_mpi_uint) (0xFC) << 24 ), ( (mbedtls_mpi_uint) (0x37) << 0 ) | ( (mbedtls_mpi_uint) (0x96) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0x7B) << 24 ),
    ( (mbedtls_mpi_uint) (0x6F) << 0 ) | ( (mbedtls_mpi_uint) (0xE7) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0xA5) << 24 ), ( (mbedtls_mpi_uint) (0xCF) << 0 ) | ( (mbedtls_mpi_uint) (0x13) << 8 ) | ( (mbedtls_mpi_uint) (0x46) << 16 ) | ( (mbedtls_mpi_uint) (0x5C) << 24 ),
    ( (mbedtls_mpi_uint) (0x8C) << 0 ) | ( (mbedtls_mpi_uint) (0xD8) << 8 ) | ( (mbedtls_mpi_uint) (0xAF) << 16 ) | ( (mbedtls_mpi_uint) (0x74) << 24 ), ( (mbedtls_mpi_uint) (0x23) << 0 ) | ( (mbedtls_mpi_uint) (0x4D) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0x84) << 24 ),
    ( (mbedtls_mpi_uint) (0x32) << 0 ) | ( (mbedtls_mpi_uint) (0x3D) << 8 ) | ( (mbedtls_mpi_uint) (0x44) << 16 ) | ( (mbedtls_mpi_uint) (0x14) << 24 ), ( (mbedtls_mpi_uint) (0x1B) << 0 ) | ( (mbedtls_mpi_uint) (0x97) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0xF0) << 24 ),
    ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x47) << 8 ) | ( (mbedtls_mpi_uint) (0xD7) << 16 ) | ( (mbedtls_mpi_uint) (0x5F) << 24 ), ( (mbedtls_mpi_uint) (0xFD) << 0 ) | ( (mbedtls_mpi_uint) (0x98) << 8 ) | ( (mbedtls_mpi_uint) (0x38) << 16 ) | ( (mbedtls_mpi_uint) (0xF7) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_27_Y[] = {
    ( (mbedtls_mpi_uint) (0xA3) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0x64) << 16 ) | ( (mbedtls_mpi_uint) (0x36) << 24 ), ( (mbedtls_mpi_uint) (0xFD) << 0 ) | ( (mbedtls_mpi_uint) (0x7B) << 8 ) | ( (mbedtls_mpi_uint) (0xC1) << 16 ) | ( (mbedtls_mpi_uint) (0x15) << 24 ),
    ( (mbedtls_mpi_uint) (0xEA) << 0 ) | ( (mbedtls_mpi_uint) (0x5D) << 8 ) | ( (mbedtls_mpi_uint) (0x32) << 16 ) | ( (mbedtls_mpi_uint) (0xD2) << 24 ), ( (mbedtls_mpi_uint) (0x47) << 0 ) | ( (mbedtls_mpi_uint) (0x94) << 8 ) | ( (mbedtls_mpi_uint) (0x89) << 16 ) | ( (mbedtls_mpi_uint) (0x2D) << 24 ),
    ( (mbedtls_mpi_uint) (0x51) << 0 ) | ( (mbedtls_mpi_uint) (0xE9) << 8 ) | ( (mbedtls_mpi_uint) (0x30) << 16 ) | ( (mbedtls_mpi_uint) (0xAC) << 24 ), ( (mbedtls_mpi_uint) (0x06) << 0 ) | ( (mbedtls_mpi_uint) (0xC8) << 8 ) | ( (mbedtls_mpi_uint) (0x65) << 16 ) | ( (mbedtls_mpi_uint) (0x04) << 24 ),
    ( (mbedtls_mpi_uint) (0xFA) << 0 ) | ( (mbedtls_mpi_uint) (0x6C) << 8 ) | ( (mbedtls_mpi_uint) (0xB9) << 16 ) | ( (mbedtls_mpi_uint) (0x1B) << 24 ), ( (mbedtls_mpi_uint) (0xF7) << 0 ) | ( (mbedtls_mpi_uint) (0x61) << 8 ) | ( (mbedtls_mpi_uint) (0x49) << 16 ) | ( (mbedtls_mpi_uint) (0x53) << 24 ),
    ( (mbedtls_mpi_uint) (0xD7) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0x32) << 16 ) | ( (mbedtls_mpi_uint) (0x43) << 24 ), ( (mbedtls_mpi_uint) (0x80) << 0 ) | ( (mbedtls_mpi_uint) (0xDA) << 8 ) | ( (mbedtls_mpi_uint) (0xA6) << 16 ) | ( (mbedtls_mpi_uint) (0xB1) << 24 ),
    ( (mbedtls_mpi_uint) (0xAC) << 0 ) | ( (mbedtls_mpi_uint) (0xF8) << 8 ) | ( (mbedtls_mpi_uint) (0x04) << 16 ) | ( (mbedtls_mpi_uint) (0x01) << 24 ), ( (mbedtls_mpi_uint) (0x95) << 0 ) | ( (mbedtls_mpi_uint) (0x35) << 8 ) | ( (mbedtls_mpi_uint) (0xCE) << 16 ) | ( (mbedtls_mpi_uint) (0x21) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_28_X[] = {
    ( (mbedtls_mpi_uint) (0x6D) << 0 ) | ( (mbedtls_mpi_uint) (0x06) << 8 ) | ( (mbedtls_mpi_uint) (0x46) << 16 ) | ( (mbedtls_mpi_uint) (0x0D) << 24 ), ( (mbedtls_mpi_uint) (0x51) << 0 ) | ( (mbedtls_mpi_uint) (0xE2) << 8 ) | ( (mbedtls_mpi_uint) (0xD8) << 16 ) | ( (mbedtls_mpi_uint) (0xAC) << 24 ),
    ( (mbedtls_mpi_uint) (0x14) << 0 ) | ( (mbedtls_mpi_uint) (0x57) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x6F) << 24 ), ( (mbedtls_mpi_uint) (0x79) << 0 ) | ( (mbedtls_mpi_uint) (0xA0) << 8 ) | ( (mbedtls_mpi_uint) (0xCD) << 16 ) | ( (mbedtls_mpi_uint) (0xA6) << 24 ),
    ( (mbedtls_mpi_uint) (0xDF) << 0 ) | ( (mbedtls_mpi_uint) (0xFB) << 8 ) | ( (mbedtls_mpi_uint) (0x36) << 16 ) | ( (mbedtls_mpi_uint) (0xCA) << 24 ), ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0xF5) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0x41) << 24 ),
    ( (mbedtls_mpi_uint) (0x6F) << 0 ) | ( (mbedtls_mpi_uint) (0x7A) << 8 ) | ( (mbedtls_mpi_uint) (0x1D) << 16 ) | ( (mbedtls_mpi_uint) (0x9E) << 24 ), ( (mbedtls_mpi_uint) (0x1D) << 0 ) | ( (mbedtls_mpi_uint) (0x95) << 8 ) | ( (mbedtls_mpi_uint) (0x48) << 16 ) | ( (mbedtls_mpi_uint) (0xDC) << 24 ),
    ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0x26) << 8 ) | ( (mbedtls_mpi_uint) (0xA5) << 16 ) | ( (mbedtls_mpi_uint) (0xB7) << 24 ), ( (mbedtls_mpi_uint) (0x15) << 0 ) | ( (mbedtls_mpi_uint) (0x2C) << 8 ) | ( (mbedtls_mpi_uint) (0xC2) << 16 ) | ( (mbedtls_mpi_uint) (0xC6) << 24 ),
    ( (mbedtls_mpi_uint) (0x86) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0x72) << 16 ) | ( (mbedtls_mpi_uint) (0xAA) << 24 ), ( (mbedtls_mpi_uint) (0x11) << 0 ) | ( (mbedtls_mpi_uint) (0xDC) << 8 ) | ( (mbedtls_mpi_uint) (0xC9) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_28_Y[] = {
    ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0x6C) << 8 ) | ( (mbedtls_mpi_uint) (0x64) << 16 ) | ( (mbedtls_mpi_uint) (0xA7) << 24 ), ( (mbedtls_mpi_uint) (0x62) << 0 ) | ( (mbedtls_mpi_uint) (0x3C) << 8 ) | ( (mbedtls_mpi_uint) (0xAB) << 16 ) | ( (mbedtls_mpi_uint) (0xD4) << 24 ),
    ( (mbedtls_mpi_uint) (0x48) << 0 ) | ( (mbedtls_mpi_uint) (0x6A) << 8 ) | ( (mbedtls_mpi_uint) (0x44) << 16 ) | ( (mbedtls_mpi_uint) (0xD8) << 24 ), ( (mbedtls_mpi_uint) (0x60) << 0 ) | ( (mbedtls_mpi_uint) (0xC0) << 8 ) | ( (mbedtls_mpi_uint) (0xA8) << 16 ) | ( (mbedtls_mpi_uint) (0x80) << 24 ),
    ( (mbedtls_mpi_uint) (0x82) << 0 ) | ( (mbedtls_mpi_uint) (0x76) << 8 ) | ( (mbedtls_mpi_uint) (0x58) << 16 ) | ( (mbedtls_mpi_uint) (0x12) << 24 ), ( (mbedtls_mpi_uint) (0x57) << 0 ) | ( (mbedtls_mpi_uint) (0x3C) << 8 ) | ( (mbedtls_mpi_uint) (0x89) << 16 ) | ( (mbedtls_mpi_uint) (0x46) << 24 ),
    ( (mbedtls_mpi_uint) (0x82) << 0 ) | ( (mbedtls_mpi_uint) (0x4F) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0xCE) << 24 ), ( (mbedtls_mpi_uint) (0xCB) << 0 ) | ( (mbedtls_mpi_uint) (0xB8) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ),
    ( (mbedtls_mpi_uint) (0x9A) << 0 ) | ( (mbedtls_mpi_uint) (0x84) << 8 ) | ( (mbedtls_mpi_uint) (0x04) << 16 ) | ( (mbedtls_mpi_uint) (0xB0) << 24 ), ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0xEB) << 8 ) | ( (mbedtls_mpi_uint) (0xFA) << 16 ) | ( (mbedtls_mpi_uint) (0xDF) << 24 ),
    ( (mbedtls_mpi_uint) (0x34) << 0 ) | ( (mbedtls_mpi_uint) (0xA4) << 8 ) | ( (mbedtls_mpi_uint) (0xC3) << 16 ) | ( (mbedtls_mpi_uint) (0x41) << 24 ), ( (mbedtls_mpi_uint) (0x44) << 0 ) | ( (mbedtls_mpi_uint) (0x4E) << 8 ) | ( (mbedtls_mpi_uint) (0x65) << 16 ) | ( (mbedtls_mpi_uint) (0x3E) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_29_X[] = {
    ( (mbedtls_mpi_uint) (0xB6) << 0 ) | ( (mbedtls_mpi_uint) (0x16) << 8 ) | ( (mbedtls_mpi_uint) (0xA9) << 16 ) | ( (mbedtls_mpi_uint) (0x1C) << 24 ), ( (mbedtls_mpi_uint) (0xE7) << 0 ) | ( (mbedtls_mpi_uint) (0x65) << 8 ) | ( (mbedtls_mpi_uint) (0x20) << 16 ) | ( (mbedtls_mpi_uint) (0xC1) << 24 ),
    ( (mbedtls_mpi_uint) (0x58) << 0 ) | ( (mbedtls_mpi_uint) (0x53) << 8 ) | ( (mbedtls_mpi_uint) (0x32) << 16 ) | ( (mbedtls_mpi_uint) (0xF8) << 24 ), ( (mbedtls_mpi_uint) (0xC0) << 0 ) | ( (mbedtls_mpi_uint) (0xA6) << 8 ) | ( (mbedtls_mpi_uint) (0xBD) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ),
    ( (mbedtls_mpi_uint) (0xB7) << 0 ) | ( (mbedtls_mpi_uint) (0xF0) << 8 ) | ( (mbedtls_mpi_uint) (0xE6) << 16 ) | ( (mbedtls_mpi_uint) (0x57) << 24 ), ( (mbedtls_mpi_uint) (0x31) << 0 ) | ( (mbedtls_mpi_uint) (0xCC) << 8 ) | ( (mbedtls_mpi_uint) (0x26) << 16 ) | ( (mbedtls_mpi_uint) (0x6F) << 24 ),
    ( (mbedtls_mpi_uint) (0x27) << 0 ) | ( (mbedtls_mpi_uint) (0xE3) << 8 ) | ( (mbedtls_mpi_uint) (0x54) << 16 ) | ( (mbedtls_mpi_uint) (0x1C) << 24 ), ( (mbedtls_mpi_uint) (0x34) << 0 ) | ( (mbedtls_mpi_uint) (0xD3) << 8 ) | ( (mbedtls_mpi_uint) (0x17) << 16 ) | ( (mbedtls_mpi_uint) (0xBC) << 24 ),
    ( (mbedtls_mpi_uint) (0xF5) << 0 ) | ( (mbedtls_mpi_uint) (0xAE) << 8 ) | ( (mbedtls_mpi_uint) (0xED) << 16 ) | ( (mbedtls_mpi_uint) (0xFB) << 24 ), ( (mbedtls_mpi_uint) (0xCD) << 0 ) | ( (mbedtls_mpi_uint) (0xE7) << 8 ) | ( (mbedtls_mpi_uint) (0x1E) << 16 ) | ( (mbedtls_mpi_uint) (0x9F) << 24 ),
    ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0x16) << 8 ) | ( (mbedtls_mpi_uint) (0x1C) << 16 ) | ( (mbedtls_mpi_uint) (0x34) << 24 ), ( (mbedtls_mpi_uint) (0x40) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0x1F) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_29_Y[] = {
    ( (mbedtls_mpi_uint) (0x6A) << 0 ) | ( (mbedtls_mpi_uint) (0x32) << 8 ) | ( (mbedtls_mpi_uint) (0x00) << 16 ) | ( (mbedtls_mpi_uint) (0xC2) << 24 ), ( (mbedtls_mpi_uint) (0xD4) << 0 ) | ( (mbedtls_mpi_uint) (0x3B) << 8 ) | ( (mbedtls_mpi_uint) (0x1A) << 16 ) | ( (mbedtls_mpi_uint) (0x09) << 24 ),
    ( (mbedtls_mpi_uint) (0x34) << 0 ) | ( (mbedtls_mpi_uint) (0xE0) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x8F) << 24 ), ( (mbedtls_mpi_uint) (0x0C) << 0 ) | ( (mbedtls_mpi_uint) (0x4A) << 8 ) | ( (mbedtls_mpi_uint) (0x16) << 16 ) | ( (mbedtls_mpi_uint) (0x44) << 24 ),
    ( (mbedtls_mpi_uint) (0x83) << 0 ) | ( (mbedtls_mpi_uint) (0x73) << 8 ) | ( (mbedtls_mpi_uint) (0x18) << 16 ) | ( (mbedtls_mpi_uint) (0x1B) << 24 ), ( (mbedtls_mpi_uint) (0xD4) << 0 ) | ( (mbedtls_mpi_uint) (0x94) << 8 ) | ( (mbedtls_mpi_uint) (0x29) << 16 ) | ( (mbedtls_mpi_uint) (0x62) << 24 ),
    ( (mbedtls_mpi_uint) (0x29) << 0 ) | ( (mbedtls_mpi_uint) (0xA4) << 8 ) | ( (mbedtls_mpi_uint) (0x2D) << 16 ) | ( (mbedtls_mpi_uint) (0xB1) << 24 ), ( (mbedtls_mpi_uint) (0x9D) << 0 ) | ( (mbedtls_mpi_uint) (0x74) << 8 ) | ( (mbedtls_mpi_uint) (0x32) << 16 ) | ( (mbedtls_mpi_uint) (0x67) << 24 ),
    ( (mbedtls_mpi_uint) (0xBF) << 0 ) | ( (mbedtls_mpi_uint) (0xF4) << 8 ) | ( (mbedtls_mpi_uint) (0xB1) << 16 ) | ( (mbedtls_mpi_uint) (0x0C) << 24 ), ( (mbedtls_mpi_uint) (0x37) << 0 ) | ( (mbedtls_mpi_uint) (0x62) << 8 ) | ( (mbedtls_mpi_uint) (0x8B) << 16 ) | ( (mbedtls_mpi_uint) (0x66) << 24 ),
    ( (mbedtls_mpi_uint) (0xC9) << 0 ) | ( (mbedtls_mpi_uint) (0xFF) << 8 ) | ( (mbedtls_mpi_uint) (0xDA) << 16 ) | ( (mbedtls_mpi_uint) (0xE2) << 24 ), ( (mbedtls_mpi_uint) (0x35) << 0 ) | ( (mbedtls_mpi_uint) (0xA3) << 8 ) | ( (mbedtls_mpi_uint) (0xB6) << 16 ) | ( (mbedtls_mpi_uint) (0x42) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_30_X[] = {
    ( (mbedtls_mpi_uint) (0x91) << 0 ) | ( (mbedtls_mpi_uint) (0x49) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x65) << 24 ), ( (mbedtls_mpi_uint) (0xC5) << 0 ) | ( (mbedtls_mpi_uint) (0xED) << 8 ) | ( (mbedtls_mpi_uint) (0x16) << 16 ) | ( (mbedtls_mpi_uint) (0xEF) << 24 ),
    ( (mbedtls_mpi_uint) (0x79) << 0 ) | ( (mbedtls_mpi_uint) (0x42) << 8 ) | ( (mbedtls_mpi_uint) (0x9A) << 16 ) | ( (mbedtls_mpi_uint) (0xF3) << 24 ), ( (mbedtls_mpi_uint) (0xA7) << 0 ) | ( (mbedtls_mpi_uint) (0x4E) << 8 ) | ( (mbedtls_mpi_uint) (0x6F) << 16 ) | ( (mbedtls_mpi_uint) (0x2B) << 24 ),
    ( (mbedtls_mpi_uint) (0x7B) << 0 ) | ( (mbedtls_mpi_uint) (0x0A) << 8 ) | ( (mbedtls_mpi_uint) (0x7E) << 16 ) | ( (mbedtls_mpi_uint) (0xC0) << 24 ), ( (mbedtls_mpi_uint) (0xD7) << 0 ) | ( (mbedtls_mpi_uint) (0x4E) << 8 ) | ( (mbedtls_mpi_uint) (0x07) << 16 ) | ( (mbedtls_mpi_uint) (0x55) << 24 ),
    ( (mbedtls_mpi_uint) (0xD6) << 0 ) | ( (mbedtls_mpi_uint) (0x7A) << 8 ) | ( (mbedtls_mpi_uint) (0x31) << 16 ) | ( (mbedtls_mpi_uint) (0x69) << 24 ), ( (mbedtls_mpi_uint) (0xA6) << 0 ) | ( (mbedtls_mpi_uint) (0xB9) << 8 ) | ( (mbedtls_mpi_uint) (0x15) << 16 ) | ( (mbedtls_mpi_uint) (0x34) << 24 ),
    ( (mbedtls_mpi_uint) (0xA8) << 0 ) | ( (mbedtls_mpi_uint) (0xE0) << 8 ) | ( (mbedtls_mpi_uint) (0x72) << 16 ) | ( (mbedtls_mpi_uint) (0xA4) << 24 ), ( (mbedtls_mpi_uint) (0x3F) << 0 ) | ( (mbedtls_mpi_uint) (0xB9) << 8 ) | ( (mbedtls_mpi_uint) (0xF8) << 16 ) | ( (mbedtls_mpi_uint) (0x0C) << 24 ),
    ( (mbedtls_mpi_uint) (0x2B) << 0 ) | ( (mbedtls_mpi_uint) (0x75) << 8 ) | ( (mbedtls_mpi_uint) (0x32) << 16 ) | ( (mbedtls_mpi_uint) (0x85) << 24 ), ( (mbedtls_mpi_uint) (0xA2) << 0 ) | ( (mbedtls_mpi_uint) (0xDE) << 8 ) | ( (mbedtls_mpi_uint) (0x37) << 16 ) | ( (mbedtls_mpi_uint) (0x12) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_30_Y[] = {
    ( (mbedtls_mpi_uint) (0xBC) << 0 ) | ( (mbedtls_mpi_uint) (0xC0) << 8 ) | ( (mbedtls_mpi_uint) (0x0D) << 16 ) | ( (mbedtls_mpi_uint) (0xCF) << 24 ), ( (mbedtls_mpi_uint) (0x25) << 0 ) | ( (mbedtls_mpi_uint) (0x41) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0xF4) << 24 ),
    ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0xFC) << 8 ) | ( (mbedtls_mpi_uint) (0xB2) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ), ( (mbedtls_mpi_uint) (0xC3) << 0 ) | ( (mbedtls_mpi_uint) (0x85) << 8 ) | ( (mbedtls_mpi_uint) (0x83) << 16 ) | ( (mbedtls_mpi_uint) (0x4B) << 24 ),
    ( (mbedtls_mpi_uint) (0x2B) << 0 ) | ( (mbedtls_mpi_uint) (0xBE) << 8 ) | ( (mbedtls_mpi_uint) (0x0B) << 16 ) | ( (mbedtls_mpi_uint) (0x58) << 24 ), ( (mbedtls_mpi_uint) (0x2D) << 0 ) | ( (mbedtls_mpi_uint) (0x7A) << 8 ) | ( (mbedtls_mpi_uint) (0x9A) << 16 ) | ( (mbedtls_mpi_uint) (0x62) << 24 ),
    ( (mbedtls_mpi_uint) (0xC5) << 0 ) | ( (mbedtls_mpi_uint) (0xF3) << 8 ) | ( (mbedtls_mpi_uint) (0x81) << 16 ) | ( (mbedtls_mpi_uint) (0x18) << 24 ), ( (mbedtls_mpi_uint) (0x1B) << 0 ) | ( (mbedtls_mpi_uint) (0x74) << 8 ) | ( (mbedtls_mpi_uint) (0x4F) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ),
    ( (mbedtls_mpi_uint) (0xE2) << 0 ) | ( (mbedtls_mpi_uint) (0x43) << 8 ) | ( (mbedtls_mpi_uint) (0xA3) << 16 ) | ( (mbedtls_mpi_uint) (0x0A) << 24 ), ( (mbedtls_mpi_uint) (0x16) << 0 ) | ( (mbedtls_mpi_uint) (0x8B) << 8 ) | ( (mbedtls_mpi_uint) (0xA3) << 16 ) | ( (mbedtls_mpi_uint) (0x1E) << 24 ),
    ( (mbedtls_mpi_uint) (0x4A) << 0 ) | ( (mbedtls_mpi_uint) (0x18) << 8 ) | ( (mbedtls_mpi_uint) (0x81) << 16 ) | ( (mbedtls_mpi_uint) (0x7B) << 24 ), ( (mbedtls_mpi_uint) (0x8D) << 0 ) | ( (mbedtls_mpi_uint) (0xA2) << 8 ) | ( (mbedtls_mpi_uint) (0x35) << 16 ) | ( (mbedtls_mpi_uint) (0x77) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_31_X[] = {
    ( (mbedtls_mpi_uint) (0x86) << 0 ) | ( (mbedtls_mpi_uint) (0xC4) << 8 ) | ( (mbedtls_mpi_uint) (0x3F) << 16 ) | ( (mbedtls_mpi_uint) (0x2C) << 24 ), ( (mbedtls_mpi_uint) (0xE7) << 0 ) | ( (mbedtls_mpi_uint) (0x5F) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x03) << 24 ),
    ( (mbedtls_mpi_uint) (0xF0) << 0 ) | ( (mbedtls_mpi_uint) (0x2B) << 8 ) | ( (mbedtls_mpi_uint) (0xB7) << 16 ) | ( (mbedtls_mpi_uint) (0xB6) << 24 ), ( (mbedtls_mpi_uint) (0xAD) << 0 ) | ( (mbedtls_mpi_uint) (0x5A) << 8 ) | ( (mbedtls_mpi_uint) (0x56) << 16 ) | ( (mbedtls_mpi_uint) (0xFF) << 24 ),
    ( (mbedtls_mpi_uint) (0x04) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0xA4) << 16 ) | ( (mbedtls_mpi_uint) (0x48) << 24 ), ( (mbedtls_mpi_uint) (0xC8) << 0 ) | ( (mbedtls_mpi_uint) (0xE8) << 8 ) | ( (mbedtls_mpi_uint) (0xBA) << 16 ) | ( (mbedtls_mpi_uint) (0xBF) << 24 ),
    ( (mbedtls_mpi_uint) (0xE8) << 0 ) | ( (mbedtls_mpi_uint) (0xA1) << 8 ) | ( (mbedtls_mpi_uint) (0xB5) << 16 ) | ( (mbedtls_mpi_uint) (0x13) << 24 ), ( (mbedtls_mpi_uint) (0x5A) << 0 ) | ( (mbedtls_mpi_uint) (0xCD) << 8 ) | ( (mbedtls_mpi_uint) (0x99) << 16 ) | ( (mbedtls_mpi_uint) (0x9C) << 24 ),
    ( (mbedtls_mpi_uint) (0xB0) << 0 ) | ( (mbedtls_mpi_uint) (0x95) << 8 ) | ( (mbedtls_mpi_uint) (0xAD) << 16 ) | ( (mbedtls_mpi_uint) (0xFC) << 24 ), ( (mbedtls_mpi_uint) (0xE2) << 0 ) | ( (mbedtls_mpi_uint) (0x7E) << 8 ) | ( (mbedtls_mpi_uint) (0xE7) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ),
    ( (mbedtls_mpi_uint) (0x96) << 0 ) | ( (mbedtls_mpi_uint) (0x6B) << 8 ) | ( (mbedtls_mpi_uint) (0xD1) << 16 ) | ( (mbedtls_mpi_uint) (0x34) << 24 ), ( (mbedtls_mpi_uint) (0x99) << 0 ) | ( (mbedtls_mpi_uint) (0x53) << 8 ) | ( (mbedtls_mpi_uint) (0x63) << 16 ) | ( (mbedtls_mpi_uint) (0x0B) << 24 ),
};
static const mbedtls_mpi_uint secp384r1_T_31_Y[] = {
    ( (mbedtls_mpi_uint) (0x19) << 0 ) | ( (mbedtls_mpi_uint) (0x8A) << 8 ) | ( (mbedtls_mpi_uint) (0x77) << 16 ) | ( (mbedtls_mpi_uint) (0x5D) << 24 ), ( (mbedtls_mpi_uint) (0x2B) << 0 ) | ( (mbedtls_mpi_uint) (0xAB) << 8 ) | ( (mbedtls_mpi_uint) (0x01) << 16 ) | ( (mbedtls_mpi_uint) (0x28) << 24 ),
    ( (mbedtls_mpi_uint) (0x4E) << 0 ) | ( (mbedtls_mpi_uint) (0x85) << 8 ) | ( (mbedtls_mpi_uint) (0xD0) << 16 ) | ( (mbedtls_mpi_uint) (0xD5) << 24 ), ( (mbedtls_mpi_uint) (0x49) << 0 ) | ( (mbedtls_mpi_uint) (0x83) << 8 ) | ( (mbedtls_mpi_uint) (0x4D) << 16 ) | ( (mbedtls_mpi_uint) (0x60) << 24 ),
    ( (mbedtls_mpi_uint) (0x81) << 0 ) | ( (mbedtls_mpi_uint) (0xC6) << 8 ) | ( (mbedtls_mpi_uint) (0x91) << 16 ) | ( (mbedtls_mpi_uint) (0x30) << 24 ), ( (mbedtls_mpi_uint) (0x3B) << 0 ) | ( (mbedtls_mpi_uint) (0x00) << 8 ) | ( (mbedtls_mpi_uint) (0xAF) << 16 ) | ( (mbedtls_mpi_uint) (0x7A) << 24 ),
    ( (mbedtls_mpi_uint) (0x3A) << 0 ) | ( (mbedtls_mpi_uint) (0xAE) << 8 ) | ( (mbedtls_mpi_uint) (0x61) << 16 ) | ( (mbedtls_mpi_uint) (0x07) << 24 ), ( (mbedtls_mpi_uint) (0xE1) << 0 ) | ( (mbedtls_mpi_uint) (0xB6) << 8 ) | ( (mbedtls_mpi_uint) (0xE2) << 16 ) | ( (mbedtls_mpi_uint) (0xC9) << 24 ),
    ( (mbedtls_mpi_uint) (0x95) << 0 ) | ( (mbedtls_mpi_uint) (0x43) << 8 ) | ( (mbedtls_mpi_uint) (0x41) << 16 ) | ( (mbedtls_mpi_uint) (0xFE) << 24 ), ( (mbedtls_mpi_uint) (0x9B) << 0 ) | ( (mbedtls_mpi_uint) (0xB6) << 8 ) | ( (mbedtls_mpi_uint) (0xF0) << 16 ) | ( (mbedtls_mpi_uint) (0xA5) << 24 ),
    ( (mbedtls_mpi_uint) (0xB4) << 0 ) | ( (mbedtls_mpi_uint) (0x97) << 8 ) | ( (mbedtls_mpi_uint) (0xAE) << 16 ) | ( (mbedtls_mpi_uint) (0xAD) << 24 ), ( (mbedtls_mpi_uint) (0x89) << 0 ) | ( (mbedtls_mpi_uint) (0x88) << 8 ) | ( (mbedtls_mpi_uint) (0x9E) << 16 ) | ( (mbedtls_mpi_uint) (0x41) << 24 ),
};
static const mbedtls_ecp_point secp384r1_T[32] = {
    { {1, (sizeof(secp384r1_T_0_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_0_X)}, {1, (sizeof(secp384r1_T_0_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_0_Y)}, {1, (1), (mbedtls_mpi_uint *)(mpi_one)} },
    { {1, (sizeof(secp384r1_T_1_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_1_X)}, {1, (sizeof(secp384r1_T_1_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_1_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_2_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_2_X)}, {1, (sizeof(secp384r1_T_2_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_2_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_3_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_3_X)}, {1, (sizeof(secp384r1_T_3_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_3_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_4_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_4_X)}, {1, (sizeof(secp384r1_T_4_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_4_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_5_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_5_X)}, {1, (sizeof(secp384r1_T_5_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_5_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_6_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_6_X)}, {1, (sizeof(secp384r1_T_6_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_6_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_7_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_7_X)}, {1, (sizeof(secp384r1_T_7_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_7_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_8_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_8_X)}, {1, (sizeof(secp384r1_T_8_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_8_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_9_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_9_X)}, {1, (sizeof(secp384r1_T_9_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_9_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_10_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_10_X)}, {1, (sizeof(secp384r1_T_10_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_10_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_11_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_11_X)}, {1, (sizeof(secp384r1_T_11_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_11_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_12_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_12_X)}, {1, (sizeof(secp384r1_T_12_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_12_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_13_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_13_X)}, {1, (sizeof(secp384r1_T_13_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_13_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_14_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_14_X)}, {1, (sizeof(secp384r1_T_14_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_14_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_15_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_15_X)}, {1, (sizeof(secp384r1_T_15_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_15_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_16_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_16_X)}, {1, (sizeof(secp384r1_T_16_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_16_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_17_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_17_X)}, {1, (sizeof(secp384r1_T_17_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_17_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_18_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_18_X)}, {1, (sizeof(secp384r1_T_18_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_18_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_19_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_19_X)}, {1, (sizeof(secp384r1_T_19_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_19_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_20_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_20_X)}, {1, (sizeof(secp384r1_T_20_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_20_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_21_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_21_X)}, {1, (sizeof(secp384r1_T_21_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_21_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_22_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_22_X)}, {1, (sizeof(secp384r1_T_22_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_22_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_23_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_23_X)}, {1, (sizeof(secp384r1_T_23_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_23_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_24_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_24_X)}, {1, (sizeof(secp384r1_T_24_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_24_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_25_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_25_X)}, {1, (sizeof(secp384r1_T_25_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_25_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_26_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_26_X)}, {1, (sizeof(secp384r1_T_26_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_26_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_27_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_27_X)}, {1, (sizeof(secp384r1_T_27_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_27_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_28_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_28_X)}, {1, (sizeof(secp384r1_T_28_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_28_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_29_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_29_X)}, {1, (sizeof(secp384r1_T_29_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_29_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_30_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_30_X)}, {1, (sizeof(secp384r1_T_30_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_30_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
    { {1, (sizeof(secp384r1_T_31_X) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_31_X)}, {1, (sizeof(secp384r1_T_31_Y) / sizeof(mbedtls_mpi_uint)), (mbedtls_mpi_uint *)(secp384r1_T_31_Y)}, {1, (0), (mbedtls_mpi_uint *)(0)} },
};






/*
 * Domain parameters for secp521r1
 */
# 2191 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 2404 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 2652 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 2900 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

/*
 * Domain parameters for brainpoolP256r1 (RFC 5639 3.4)
 */
# 3158 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

/*
 * Domain parameters for brainpoolP384r1 (RFC 5639 3.6)
 */
# 3764 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

/*
 * Domain parameters for brainpoolP512r1 (RFC 5639 3.7)
 */
# 4509 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"


/*
 * Create an MPI from embedded constants
 * (assumes len is an exact multiple of sizeof mbedtls_mpi_uint)
 */
static __inline void ecp_mpi_load( mbedtls_mpi *X, const mbedtls_mpi_uint *p, size_t len )
{
    X->s = 1;
    X->n = len / sizeof( mbedtls_mpi_uint );
    X->p = (mbedtls_mpi_uint *) p;
}

/*
 * Set an MPI to static value 1
 */
static __inline void ecp_mpi_set1( mbedtls_mpi *X )
{
    X->s = 1;
    X->n = 1;
    X->p = mpi_one;
}

/*
 * Make group available from embedded constants
 */
static int ecp_group_load( mbedtls_ecp_group *grp,
                           const mbedtls_mpi_uint *p,  size_t plen,
                           const mbedtls_mpi_uint *a,  size_t alen,
                           const mbedtls_mpi_uint *b,  size_t blen,
                           const mbedtls_mpi_uint *gx, size_t gxlen,
                           const mbedtls_mpi_uint *gy, size_t gylen,
                           const mbedtls_mpi_uint *n,  size_t nlen,
                           const mbedtls_ecp_point *T)
{
    ecp_mpi_load( &grp->P, p, plen );
    if( a != 0 )
        ecp_mpi_load( &grp->A, a, alen );
    ecp_mpi_load( &grp->B, b, blen );
    ecp_mpi_load( &grp->N, n, nlen );

    ecp_mpi_load( &grp->G.X, gx, gxlen );
    ecp_mpi_load( &grp->G.Y, gy, gylen );
    ecp_mpi_set1( &grp->G.Z );

    grp->pbits = mbedtls_mpi_bitlen( &grp->P );
    grp->nbits = mbedtls_mpi_bitlen( &grp->N );

    grp->h = 1;

    grp->T = (mbedtls_ecp_point *) T;
    /*
     * Set T_size to 0 to prevent T free by mbedtls_ecp_group_free.
     */
    grp->T_size = 0;

    return( 0 );
}


# 4591 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

/* Additional forward declarations */
# 4608 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 4619 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 4630 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 4676 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 4731 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

/*
 * Set a group using well-known domain parameters
 */
int mbedtls_ecp_group_load( mbedtls_ecp_group *grp, mbedtls_ecp_group_id id )
{
    do { } while( 0 );
    mbedtls_ecp_group_free( grp );

    mbedtls_ecp_group_init( grp );

    grp->id = id;

    switch( id )
    {













        case MBEDTLS_ECP_DP_SECP256R1:
            ;
            return( ecp_group_load( grp, secp256r1_p, sizeof( secp256r1_p ), 0, 0, secp256r1_b, sizeof( secp256r1_b ), secp256r1_gx, sizeof( secp256r1_gx ), secp256r1_gy, sizeof( secp256r1_gy ), secp256r1_n, sizeof( secp256r1_n ), secp256r1_T ) );



        case MBEDTLS_ECP_DP_SECP384R1:
            ;
            return( ecp_group_load( grp, secp384r1_p, sizeof( secp384r1_p ), 0, 0, secp384r1_b, sizeof( secp384r1_b ), secp384r1_gx, sizeof( secp384r1_gx ), secp384r1_gy, sizeof( secp384r1_gy ), secp384r1_n, sizeof( secp384r1_n ), secp384r1_T ) );





















































        default:
            grp->id = MBEDTLS_ECP_DP_NONE;
            return( -0x4E80 );
    }
}

# 5211 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 5245 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 5316 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 5397 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 5413 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 5434 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"

# 5449 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\library\\ecp_curves.c"



