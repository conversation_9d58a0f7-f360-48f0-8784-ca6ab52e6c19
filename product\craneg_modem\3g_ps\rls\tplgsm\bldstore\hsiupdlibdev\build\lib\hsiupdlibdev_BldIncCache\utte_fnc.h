/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utte_fnc.h#7 $
 *   $Revision: #7 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description:
 *
 * Task Execution (TE) supporting function for
 * - Sequential task execution by Te signal function
 * - Persistent memory by Te persistent memory function (system 2 only)
 *
 * Note. Prefix "Te" is for public use.
 *       Prefix "Utte" is for function internal use
 **************************************************************************/

#ifndef UTTE_FNC_H
#define UTTE_FNC_H

/**** NESTED INCLUDE FILES *************************************************/

#include <kernel.h>
#include <system.h>

/**** #defines  ************************************************************/

/* Configuration macro TE_DATA_ITEM type defines */
#define UTTE_DATA_ID_ENUM           1 /* For data id enum */
#define UTTE_DATA_SIZE_FUNC         2 /* For data size function */
#define UTTE_DATA_SIZE_FUNC_PROTO   3 /* For data size function prototype */
#define UTTE_DATA_SIZE_FUNC_HND     4 /* For a parameter of data size function */

#define TE_DATA_ITEM_ID_BITS    14
#define TE_DATA_ID_BEGIN        1000
#define TE_DATA_ID_BIT          (1<<30)
                                /* = 0x40000000 */

/* Maximum number of data item.
 * When TE_MAX_NUM_DATA_ITEMS = 0x4000 (16K), the maximum subitem will
 * be 0x10000 (64K)
 * TE_MAX_NUM_DATA_ITEMS * MaxNumSubItems = TE_DATA_ID_BIT */
#define TE_MAX_NUM_DATA_ITEMS   (1<<TE_DATA_ITEM_ID_BITS)
                                /* =16384  */
#define TE_DATA_ITEM_MASK       (TE_MAX_NUM_DATA_ITEMS - 1)
                                /* =0x00003fff */
#define TE_SUB_DATA_ITEM_FACTOR TE_MAX_NUM_DATA_ITEMS
#define TE_SUB_DATA_ITEM_MASK   ((TE_DATA_ID_BIT - 1) & (~TE_DATA_ITEM_MASK))
                                /* =0x3fffc000 */


/**** TYPES  ************************************************************/


/* Unique data id
 * Unique data id is used to generate NVRAM NvDataType
 * The first NvDataType will bigger than TE_DATA_ID_BIT
 * All NvDataType beteen TE_DATA_ID_BIT and (TE_DATA_ID_BIT*2-1) are used by TE persistent memory */
#define UTTE_DATA_ITEM_TYPE UTTE_DATA_ID_ENUM
typedef enum UtteDataIdTag
{
  TE_DATA_ID_BEGIN_DUMMY  = TE_DATA_ID_BEGIN,
#include <utte_cfg.h>
  TE_DATA_ID_END
}
UtteTeDataId;

/* Note: UtteDataId will be casted to TeDataID, as enum may not support 32 bits */
typedef Int32 TeDataId;

typedef enum TeDataFlagTag
{
  TE_MEMORY,  /* No NVRAM operation. Data will be lost after TeClose */
  TE_READ,    /* Try to read from NVRAM. Data will be lost after TeClose */
  TE_CREATE,  /* Erase content in NVRAM. Save data after TeFlash or TeClose */
  TE_WRITE    /* Try to read from NVRAM. Save data after TeFlash or TeClose */
}
TeDataFlag;

typedef enum TeResultTag
{
  TE_UNINIT,            /* Data is not read from NVRAM */
  TE_NVRAM_OK,          /* NVRAM operation OK */
  TE_NVRAM_FAILURE      /* NVRAM operation failure */
} TeResult;

/* data item size function param */
typedef Int32 TeHnd;

/* data item size function type */
typedef Int32 (*TeDataSizeFunc)(
    TeHnd hnd
    );

typedef struct UtteDataHeaderTag
{
  TeDataFlag  flag;
  TeDataId    dataId;
  Int32       size;
}
UtteDataHeader;

/**** FUNCTION PROTOTYPES **************************************************/

void TeReceiveSignal (SignalBuffer      *signal_p);

void TeWaitForSignal (SignalBuffer      *signal_p,
                      SignalId          signalId);

void TeDestroySignal (SignalBuffer      *signal_p);

void TeReceiveSignalPoll (SignalBuffer      *signal_p);

/* Task init if variable zero initialise is not supported
 * This function init TE signal function for a task */
void TeInitTask (void);


TeResult TeOpen      (void              **buffer_p,
                      const Int32       size,
                      const TeDataId    id,
                      const TeDataFlag  flag);

TeResult TeClose     (void              **buffer_p);

TeResult TeFlush     (void              **buffer_p);

/* TE memory init function if variable zero initialise is not supported.
 * This function init TE memory function for system */
void TeInitSystem (void);

#if defined(DEVELOPMENT_VERSION)
Int32 TeGetDataSizeSum(void);
#endif

Int16 TeGetDataSubId(TeDataId dataId);
TeDataId TeGetDataId(TeDataId enumId, Int16 subId);

#ifdef PC_ANITE_TEST
extern
	KiUnitQueue * utteGetLocalQueue (TaskId taskId);
#endif

#endif /* UTTE_FNC_H */



/* END OF FILE */
