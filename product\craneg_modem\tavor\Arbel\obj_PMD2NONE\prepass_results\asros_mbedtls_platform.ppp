# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.c"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
/* stdlib.h: ANSI draft (X3J11 May 88) library header, section 4.10 */
/* Copyright (C) Codemist Ltd., 1988-1993.                          */
/* Copyright 1991-1998,2014 ARM Limited. All rights reserved.       */
/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */
 
/*
 * stdlib.h declares four types, several general purpose functions,
 * and defines several macros.
 */






  /* armclang and non-strict armcc allow 'long long' in system headers */














  /*
   * Some of these declarations are new in C99.  To access them in C++
   * you can use -D__USE_C99_STDLIB (or -D__USE_C99ALL).
   */








# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 70 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"






   /* unconditional in non-strict C for consistency of debug info */



    typedef unsigned short wchar_t; /* see <stddef.h> */
# 91 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

typedef struct div_t { int quot, rem; } div_t;
   /* type of the value returned by the div function. */
typedef struct ldiv_t { long int quot, rem; } ldiv_t;
   /* type of the value returned by the ldiv function. */

typedef struct lldiv_t { long long quot, rem; } lldiv_t;
   /* type of the value returned by the lldiv function. */


# 112 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * an integral expression which may be used as an argument to the exit
    * function to return successful termination status to the host
    * environment.
    */

   /*
    * Defining __USE_ANSI_EXAMPLE_RAND at compile time switches to
    * the example implementation of rand() and srand() provided in
    * the ANSI C standard. This implementation is very poor, but is
    * provided for completeness.
    */
# 131 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * RAND_MAX: an integral constant expression, the value of which
    * is the maximum value returned by the rand function.
    */
extern __declspec(__nothrow) int __aeabi_MB_CUR_MAX(void);

   /*
    * a positive integer expression whose value is the maximum number of bytes
    * in a multibyte character for the extended character set specified by the
    * current locale (category LC_CTYPE), and whose value is never greater
    * than MB_LEN_MAX.
    */

   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain a compliant version of the strtod
    * family of functions.
    */




extern __declspec(__nothrow) double atof(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) int atoi(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to int
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) long int atol(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation.
    * Returns: the converted value.
    */

extern __declspec(__nothrow) long long atoll(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to
    * long long int representation.
    * Returns: the converted value.
    */


extern __declspec(__nothrow) double strtod(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling a
    * floating point constant; and a final string of one or more unrecognised
    * characters, including the terminating null character of the input string.
    * Then it attempts to convert the subject sequence to a floating point
    * number, and returns the result. A pointer to the final string is stored
    * in the object pointed to by endptr, provided that endptr is not a null
    * pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned. If the correct value is outside the range of
    *          representable values, plus or minus HUGE_VAL is returned
    *          (according to the sign of the value), and the value of the macro
    *          ERANGE is stored in errno. If the correct value would cause
    *          underflow, zero is returned and the value of the macro ERANGE is
    *          stored in errno.
    */

extern __declspec(__nothrow) float strtof(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) long double strtold(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * same as strtod, but return float and long double respectively.
    */

extern __declspec(__nothrow) long int strtol(const char * __restrict /*nptr*/,
                        char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling an
    * integer represented in some radix determined by the value of base, and a
    * final string of one or more unrecognised characters, including the
    * terminating null character of the input string. Then it attempts to
    * convert the subject sequence to an integer, and returns the result.
    * If the value of base is 0, the expected form of the subject sequence is
    * that of an integer constant (described in ANSI Draft, section 3.1.3.2),
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. If the value of base is between 2 and 36, the expected form of
    * the subject sequence is a sequence of letters and digits representing an
    * integer with the radix specified by base, optionally preceded by a plus
    * or minus sign, but not including an integer suffix. The letters from a
    * (or A) through z (or Z) are ascribed the values 10 to 35; only letters
    * whose ascribed values are less than that of the base are permitted. If
    * the value of base is 16, the characters 0x or 0X may optionally precede
    * the sequence of letters and digits following the sign if present.
    * A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, LONG_MAX or LONG_MIN is returned
    *          (according to the sign of the value), and the value of the
    *          macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long int strtoul(const char * __restrict /*nptr*/,
                                       char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to unsigned
    * long int representation. First it decomposes the input string into three
    * parts: an initial, possibly empty, sequence of white-space characters (as
    * determined by the isspace function), a subject sequence resembling an
    * unsigned integer represented in some radix determined by the value of
    * base, and a final string of one or more unrecognised characters,
    * including the terminating null character of the input string. Then it
    * attempts to convert the subject sequence to an unsigned integer, and
    * returns the result. If the value of base is zero, the expected form of
    * the subject sequence is that of an integer constant (described in ANSI
    * Draft, section 3.1.3.2), optionally preceded by a '+' or '-' sign, but
    * not including an integer suffix. If the value of base is between 2 and
    * 36, the expected form of the subject sequence is a sequence of letters
    * and digits representing an integer with the radix specified by base,
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. The letters from a (or A) through z (or Z) stand for the values
    * 10 to 35; only letters whose ascribed values are less than that of the
    * base are permitted. If the value of base is 16, the characters 0x or 0X
    * may optionally precede the sequence of letters and digits following the
    * sign, if present. A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, ULONG_MAX is returned, and the value of
    *          the macro ERANGE is stored in errno.
    */

/* C90 reserves all names beginning with 'str' */
extern __declspec(__nothrow) long long strtoll(const char * __restrict /*nptr*/,
                                  char ** __restrict /*endptr*/, int /*base*/)
                          __attribute__((__nonnull__(1)));
   /*
    * as strtol but returns a long long int value.  If the correct value is
    * outside the range of representable values,  LLONG_MAX or LLONG_MIN is
    * returned (according to the sign of the value), and the value of the
    * macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long long strtoull(const char * __restrict /*nptr*/,
                                            char ** __restrict /*endptr*/, int /*base*/)
                                   __attribute__((__nonnull__(1)));
   /*
    * as strtoul but returns an unsigned long long int value.  If the correct
    * value is outside the range of representable values, ULLONG_MAX is returned,
    * and the value of the macro ERANGE is stored in errno.
    */

extern __declspec(__nothrow) int rand(void);
   /*
    * Computes a sequence of pseudo-random integers in the range 0 to RAND_MAX.
    * Uses an additive generator (Mitchell & Moore) of the form:
    *   Xn = (X[n-24] + X[n-55]) MOD 2^31
    * This is described in section 3.2.2 of Knuth, vol 2. It's period is
    * in excess of 2^55 and its randomness properties, though unproven, are
    * conjectured to be good. Empirical testing since 1958 has shown no flaws.
    * Returns: a pseudo-random integer.
    */
extern __declspec(__nothrow) void srand(unsigned int /*seed*/);
   /*
    * uses its argument as a seed for a new sequence of pseudo-random numbers
    * to be returned by subsequent calls to rand. If srand is then called with
    * the same seed value, the sequence of pseudo-random numbers is repeated.
    * If rand is called before any calls to srand have been made, the same
    * sequence is generated as when srand is first called with a seed value
    * of 1.
    */

struct _rand_state { int __x[57]; };
extern __declspec(__nothrow) int _rand_r(struct _rand_state *);
extern __declspec(__nothrow) void _srand_r(struct _rand_state *, unsigned int);
struct _ANSI_rand_state { int __x[1]; };
extern __declspec(__nothrow) int _ANSI_rand_r(struct _ANSI_rand_state *);
extern __declspec(__nothrow) void _ANSI_srand_r(struct _ANSI_rand_state *, unsigned int);
   /*
    * Re-entrant variants of both flavours of rand, which operate on
    * an explicitly supplied state buffer.
    */

extern __declspec(__nothrow) void *calloc(size_t /*nmemb*/, size_t /*size*/);
   /*
    * allocates space for an array of nmemb objects, each of whose size is
    * 'size'. The space is initialised to all bits zero.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void free(void * /*ptr*/);
   /*
    * causes the space pointed to by ptr to be deallocated (i.e., made
    * available for further allocation). If ptr is a null pointer, no action
    * occurs. Otherwise, if ptr does not match a pointer earlier returned by
    * calloc, malloc or realloc or if the space has been deallocated by a call
    * to free or realloc, the behaviour is undefined.
    */
extern __declspec(__nothrow) void *malloc(size_t /*size*/);
   /*
    * allocates space for an object whose size is specified by 'size' and whose
    * value is indeterminate.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void *realloc(void * /*ptr*/, size_t /*size*/);
   /*
    * changes the size of the object pointed to by ptr to the size specified by
    * size. The contents of the object shall be unchanged up to the lesser of
    * the new and old sizes. If the new size is larger, the value of the newly
    * allocated portion of the object is indeterminate. If ptr is a null
    * pointer, the realloc function behaves like a call to malloc for the
    * specified size. Otherwise, if ptr does not match a pointer earlier
    * returned by calloc, malloc or realloc, or if the space has been
    * deallocated by a call to free or realloc, the behaviour is undefined.
    * If the space cannot be allocated, the object pointed to by ptr is
    * unchanged. If size is zero and ptr is not a null pointer, the object it
    * points to is freed.
    * Returns: either a null pointer or a pointer to the possibly moved
    *          allocated space.
    */

extern __declspec(__nothrow) int posix_memalign(void ** /*ret*/, size_t /*alignment*/, size_t /*size*/);
   /*
    * allocates space for an object of size 'size', aligned to a
    * multiple of 'alignment' (which must be a power of two and at
    * least 4).
    *
    * On success, a pointer to the allocated object is stored in
    * *ret, and zero is returned. On failure, the return value is
    * either ENOMEM (allocation failed because no suitable piece of
    * memory was available) or EINVAL (the 'alignment' parameter was
    * invalid).
    */

typedef int (*__heapprt)(void *, char const *, ...);
extern __declspec(__nothrow) void __heapstats(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                        void * /*param*/) __attribute__((__nonnull__(1)));
   /*
    * reports current heap statistics (eg. number of free blocks in
    * the free-list). Output is as implementation-defined free-form
    * text, provided via the dprint function. `param' gives an
    * extra data word to pass to dprint. You can call
    * __heapstats(fprintf,stdout) by casting fprintf to the above
    * function type; the typedef `__heapprt' is provided for this
    * purpose.
    *
    * `dprint' will not be called while the heap is being examined,
    * so it can allocate memory itself without trouble.
    */
extern __declspec(__nothrow) int __heapvalid(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                       void * /*param*/, int /*verbose*/) __attribute__((__nonnull__(1)));
   /*
    * performs a consistency check on the heap. Errors are reported
    * through dprint, like __heapstats. If `verbose' is nonzero,
    * full diagnostic information on the heap state is printed out.
    *
    * This routine probably won't work if the heap isn't a
    * contiguous chunk (for example, if __user_heap_extend has been
    * overridden).
    *
    * `dprint' may be called while the heap is being examined or
    * even in an invalid state, so it must perform no memory
    * allocation. In particular, if `dprint' calls (or is) a stdio
    * function, the stream it outputs to must already have either
    * been written to or been setvbuf'ed, or else the system will
    * allocate buffer space for it on the first call to dprint.
    */
extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
   /*
    * causes abnormal program termination to occur, unless the signal SIGABRT
    * is being caught and the signal handler does not return. Whether open
    * output streams are flushed or open streams are closed or temporary
    * files removed is implementation-defined.
    * An implementation-defined form of the status 'unsuccessful termination'
    * is returned to the host environment by means of a call to
    * raise(SIGABRT).
    */

extern __declspec(__nothrow) int atexit(void (* /*func*/)(void)) __attribute__((__nonnull__(1)));
   /*
    * registers the function pointed to by func, to be called without its
    * arguments at normal program termination. It is possible to register at
    * least 32 functions.
    * Returns: zero if the registration succeeds, nonzero if it fails.
    */
# 436 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern __declspec(__nothrow) __declspec(__noreturn) void exit(int /*status*/);
   /*
    * causes normal program termination to occur. If more than one call to the
    * exit function is executed by a program, the behaviour is undefined.
    * First, all functions registered by the atexit function are called, in the
    * reverse order of their registration.
    * Next, all open output streams are flushed, all open streams are closed,
    * and all files created by the tmpfile function are removed.
    * Finally, control is returned to the host environment. If the value of
    * status is zero or EXIT_SUCCESS, an implementation-defined form of the
    * status 'successful termination' is returned. If the value of status is
    * EXIT_FAILURE, an implementation-defined form of the status
    * 'unsuccessful termination' is returned. Otherwise the status returned
    * is implementation-defined.
    */

extern __declspec(__nothrow) __declspec(__noreturn) void _Exit(int /*status*/);
   /*
    * causes normal program termination to occur. No functions registered
    * by the atexit function are called.
    * In this implementation, all open output streams are flushed, all
    * open streams are closed, and all files created by the tmpfile function
    * are removed.
    * Control is returned to the host environment. The status returned to
    * the host environment is determined in the same way as for 'exit'.
    */     

extern __declspec(__nothrow) char *getenv(const char * /*name*/) __attribute__((__nonnull__(1)));
   /*
    * searches the environment list, provided by the host environment, for a
    * string that matches the string pointed to by name. The set of environment
    * names and the method for altering the environment list are
    * implementation-defined.
    * Returns: a pointer to a string associated with the matched list member.
    *          The array pointed to shall not be modified by the program, but
    *          may be overwritten by a subsequent call to the getenv function.
    *          If the specified name cannot be found, a null pointer is
    *          returned.
    */

extern __declspec(__nothrow) int  system(const char * /*string*/);
   /*
    * passes the string pointed to by string to the host environment to be
    * executed by a command processor in an implementation-defined manner.
    * A null pointer may be used for string, to inquire whether a command
    * processor exists.
    *
    * Returns: If the argument is a null pointer, the system function returns
    *          non-zero only if a command processor is available. If the
    *          argument is not a null pointer, the system function returns an
    *          implementation-defined value.
    */

extern  void *bsearch(const void * /*key*/, const void * /*base*/,
              size_t /*nmemb*/, size_t /*size*/,
              int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,2,5)));
   /*
    * searches an array of nmemb objects, the initial member of which is
    * pointed to by base, for a member that matches the object pointed to by
    * key. The size of each member of the array is specified by size.
    * The contents of the array shall be in ascending sorted order according to
    * a comparison function pointed to by compar, which is called with two
    * arguments that point to the key object and to an array member, in that
    * order. The function shall return an integer less than, equal to, or
    * greater than zero if the key object is considered, respectively, to be
    * less than, to match, or to be greater than the array member.
    * Returns: a pointer to a matching member of the array, or a null pointer
    *          if no match is found. If two members compare as equal, which
    *          member is matched is unspecified.
    */
# 524 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern  void qsort(void * /*base*/, size_t /*nmemb*/, size_t /*size*/,
           int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,4)));
   /*
    * sorts an array of nmemb objects, the initial member of which is pointed
    * to by base. The size of each object is specified by size.
    * The contents of the array shall be in ascending order according to a
    * comparison function pointed to by compar, which is called with two
    * arguments that point to the objects being compared. The function shall
    * return an integer less than, equal to, or greater than zero if the first
    * argument is considered to be respectively less than, equal to, or greater
    * than the second. If two members compare as equal, their order in the
    * sorted array is unspecified.
    */

# 553 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

extern __declspec(__nothrow) __attribute__((const)) int abs(int /*j*/);
   /*
    * computes the absolute value of an integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */

extern __declspec(__nothrow) __attribute__((const)) div_t div(int /*numer*/, int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the resulting
    * quotient is the integer of lesser magnitude that is the nearest to the
    * algebraic quotient. If the result cannot be represented, the behaviour is
    * undefined; otherwise, quot * denom + rem shall equal numer.
    * Returns: a structure of type div_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          int quot; int rem;
    */
extern __declspec(__nothrow) __attribute__((const)) long int labs(long int /*j*/);
   /*
    * computes the absolute value of an long integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) ldiv_t ldiv(long int /*numer*/, long int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type ldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long int quot; long int rem;
    */







extern __declspec(__nothrow) __attribute__((const)) long long llabs(long long /*j*/);
   /*
    * computes the absolute value of a long long integer j. If the
    * result cannot be represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) lldiv_t lldiv(long long /*numer*/, long long /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type lldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long long quot; long long rem;
    */
# 634 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


/*
 * ARM real-time divide functions for guaranteed performance
 */
typedef struct __sdiv32by16 { int quot, rem; } __sdiv32by16;
typedef struct __udiv32by16 { unsigned int quot, rem; } __udiv32by16;
   /* used int so that values return in separate regs, although 16-bit */
typedef struct __sdiv64by32 { int rem, quot; } __sdiv64by32;

__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv32by16 __rt_sdiv32by16(
     int /*numer*/,
     short int /*denom*/);
   /*
    * Signed divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __udiv32by16 __rt_udiv32by16(
     unsigned int /*numer*/,
     unsigned short /*denom*/);
   /*
    * Unsigned divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv64by32 __rt_sdiv64by32(
     int /*numer_h*/, unsigned int /*numer_l*/,
     int /*denom*/);
   /*
    * Signed divide: (32-bit quot), (32-bit rem) = (64-bit) / (32-bit)
    */


/*
 * ARM floating-point mask/status function (for both hardfp and softfp)
 */
extern __declspec(__nothrow) unsigned int __fp_status(unsigned int /*mask*/, unsigned int /*flags*/);
   /*
    * mask and flags are bit-fields which correspond directly to the
    * floating point status register in the FPE/FPA and fplib.  
    * __fp_status returns the current value of the status register,
    * and also sets the writable bits of the word
    * (the exception control and flag bytes) to:
    *
    *     new = (old & ~mask) ^ flags;
    */












/*
 * Multibyte Character Functions.
 * The behaviour of the multibyte character functions is affected by the
 * LC_CTYPE category of the current locale. For a state-dependent encoding,
 * each function is placed into its initial state by a call for which its
 * character pointer argument, s, is a null pointer. Subsequent calls with s
 * as other than a null pointer cause the internal state of the function to be
 * altered as necessary. A call with s as a null pointer causes these functions
 * to return a nonzero value if encodings have state dependency, and a zero
 * otherwise. After the LC_CTYPE category is changed, the shift state of these
 * functions is indeterminate.
 */
extern __declspec(__nothrow) int mblen(const char * /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mblen function determines the number of
    * bytes compromising the multibyte character pointed to by s. Except that
    * the shift state of the mbtowc function is not affected, it is equivalent
    * to   mbtowc((wchar_t *)0, s, n);
    * Returns: If s is a null pointer, the mblen function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mblen function either returns a 0 (if s points to a
    *          null character), or returns the number of bytes that compromise
    *          the multibyte character (if the next n of fewer bytes form a
    *          valid multibyte character), or returns -1 (they do not form a
    *          valid multibyte character).
    */
extern __declspec(__nothrow) int mbtowc(wchar_t * __restrict /*pwc*/,
                   const char * __restrict /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mbtowc function determines the number of
    * bytes that compromise the multibyte character pointed to by s. It then
    * determines the code for value of type wchar_t that corresponds to that
    * multibyte character. (The value of the code corresponding to the null
    * character is zero). If the multibyte character is valid and pwc is not a
    * null pointer, the mbtowc function stores the code in the object pointed
    * to by pwc. At most n bytes of the array pointed to by s will be examined.
    * Returns: If s is a null pointer, the mbtowc function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mbtowc function either returns a 0 (if s points to
    *          a null character), or returns the number of bytes that
    *          compromise the converted multibyte character (if the next n of
    *          fewer bytes form a valid multibyte character), or returns -1
    *          (they do not form a valid multibyte character).
    */
extern __declspec(__nothrow) int wctomb(char * /*s*/, wchar_t /*wchar*/);
   /*
    * determines the number of bytes need to represent the multibyte character
    * corresponding to the code whose value is wchar (including any change in
    * shift state). It stores the multibyte character representation in the
    * array object pointed to by s (if s is not a null pointer). At most
    * MB_CUR_MAX characters are stored. If the value of wchar is zero, the
    * wctomb function is left in the initial shift state).
    * Returns: If s is a null pointer, the wctomb function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the wctomb function returns a -1 if the value of wchar
    *          does not correspond to a valid multibyte character, or returns
    *          the number of bytes that compromise the multibyte character
    *          corresponding to the value of wchar.
    */

/*
 * Multibyte String Functions.
 * The behaviour of the multibyte string functions is affected by the LC_CTYPE
 * category of the current locale.
 */
extern __declspec(__nothrow) size_t mbstowcs(wchar_t * __restrict /*pwcs*/,
                      const char * __restrict /*s*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of multibyte character that begins in the initial
    * shift state from the array pointed to by s into a sequence of
    * corresponding codes and stores not more than n codes into the array
    * pointed to by pwcs. No multibyte character that follow a null character
    * (which is converted into a code with value zero) will be examined or
    * converted. Each multibyte character is converted as if by a call to
    * mbtowc function, except that the shift state of the mbtowc function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by pwcs. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If an invalid multibyte character is encountered, the mbstowcs
    *          function returns (size_t)-1. Otherwise, the mbstowcs function
    *          returns the number of array elements modified, not including
    *          a terminating zero code, if any.
    */
extern __declspec(__nothrow) size_t wcstombs(char * __restrict /*s*/,
                      const wchar_t * __restrict /*pwcs*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of codes that correspond to multibyte characters
    * from the array pointed to by pwcs into a sequence of multibyte
    * characters that begins in the initial shift state and stores these
    * multibyte characters into the array pointed to by s, stopping if a
    * multibyte character would exceed the limit of n total bytes or if a
    * null character is stored. Each code is converted as if by a call to the
    * wctomb function, except that the shift state of the wctomb function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by s. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If a code is encountered that does not correspond to a valid
    *          multibyte character, the wcstombs function returns (size_t)-1.
    *          Otherwise, the wcstombs function returns the number of bytes
    *          modified, not including a terminating null character, if any.
    */

extern __declspec(__nothrow) void __use_realtime_heap(void);
extern __declspec(__nothrow) void __use_realtime_division(void);
extern __declspec(__nothrow) void __use_two_region_memory(void);
extern __declspec(__nothrow) void __use_no_heap(void);
extern __declspec(__nothrow) void __use_no_heap_region(void);

extern __declspec(__nothrow) char const *__C_library_version_string(void);
extern __declspec(__nothrow) int __C_library_version_number(void);











# 892 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"





/* end of stdlib.h */
# 2 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.c"
# 1 "/os/osa/inc/osa.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa.h
Description : This file defines the OS wrapper API and definitions for external application use.

              The OSA API allows applications to be developed independent of
              the target microkernel/hardware environment. It provides the
              facility to add support for multiple independent operating
              systems from different vendors.

Copyright (c) 2001 Intel CCD. All Rights Reserved
=========================================================================== */







# 1 "\\os\\alios\\kernel\\armv7r\\include\\alios_type.h"








typedef char                        CHAR;


typedef unsigned char               UCHAR;

typedef int                                     INT;

typedef unsigned int                UINT;

typedef long                                    LONG;
typedef unsigned long                           ULONG;
typedef short                                   SHORT;
typedef unsigned short                          USHORT;


# 26 "/os/osa/inc/osa.h"







# 41 "/os/osa/inc/osa.h"

# 1 "\\csw\\platform\\inc\\gbl_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ============================================================================
File        : gbl_types.h
Description : Global types file for testing the
              os/kal package.

Notes       : This file is only used to test the compilation and
              archiving for the os/kal package.

Copyright 2001, Intel Corporation, All rights reserved.
============================================================================ */




/* Use the Xscale environment types */
# 1 "\\env\\win32\\inc\\xscale_types.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : xscale_types.h
Description : Global types file for the Xscale environment.

Notes       : This file is designed for use in the arm environment
              and is referenced from the gbl_types.h file. Use of
			  this file requires ENV_XSCALE to be defined in xscale_env.mak.
              
Copyright 2001, Intel Corporation, All rights reserved.
=========================================================================== */




typedef unsigned char	BOOL;
typedef unsigned char   UINT8;
typedef unsigned short  UINT16;
typedef unsigned long   UINT32;

typedef char            CHAR;
typedef signed char     INT8;
typedef signed short    INT16;
typedef signed long     INT32;













/*                         end of xscale_types.h
--------------------------------------------------------------------------- */



# 23 "\\csw\\platform\\inc\\gbl_types.h"


/* Use the NordHeim environment types */




/* Use the Arm environment types */




/* Use the Gnu environment types */




/* Use the Microsoft Visual C environment types */




  /* Standard typedefs */
  typedef unsigned char   Bool;         /* Boolean                        */

  /* Standard typedefs - to retain compatibility with TDMA */
  typedef UINT8           		 BYTE;         			/* Unsigned 8-bit quantity        */
  typedef UINT8            		 UBYTE;        			/* Unsigned 8-bit quantity        */
  typedef UINT16          		 UWORD;        			/* Unsigned 16-bit quantity       */
  typedef UINT16          		 WORD;         			/* Unsigned 16-bit quantity       */
  typedef INT16           		 SWORD;        			/* Signed 16-bit quantity         */
  typedef UINT32                 DWORD;        			/* Unsigned 32-bit quantity       */
  // hero start //FEATURE_HERO_ENGINE_APP

  typedef unsigned long long     UINT64;                /* Unsigned 64-bit quantity       */

  // hero end
  typedef void* 		         VOID_PTR;























  /* A NULL value is required such that it is not mistaken for a valid */
  /* value which includes values in the range of modulo 64. */


  /* Definition of NULL is required */















/*                      end of gbl_types.h
--------------------------------------------------------------------------- */



# 43 "/os/osa/inc/osa.h"


/*****************************************************************************
 * OSA Constants
 ****************************************************************************/
//ICAT EXPORTED ENUM
typedef enum 
{
	OSA_TASK_READY,
	OSA_TASK_COMPLETED,
	OSA_TASK_TERMINATED,
	OSA_TASK_SUSPENDED,
	OSA_TASK_SLEEP,
	OSA_TASK_QUEUE_SUSP,
	OSA_TASK_SEMAPHORE_SUSP,
	OSA_TASK_EVENT_FLAG,
	OSA_TASK_BLOCK_MEMORY,
	OSA_TASK_MUTEX_SUSP,
	OSA_TASK_STATE_UNKNOWN,
}OSA_TASK_STATE;

//ICAT EXPORTED STRUCT
typedef struct OSA_TASK_STRUCT
{
    char                *task_name;                /* Pointer to thread's name     */		
    unsigned int        task_priority;             /* Priority of thread (0-255)  */	
    unsigned long        task_stack_def_val;             /* default vaule of  thread  */
    OSA_TASK_STATE      task_state;                /* Thread's execution state     */
    unsigned long       task_stack_ptr;           /* Thread's stack pointer   */
    unsigned long       task_stack_start;         /* Stack starting address   */
    unsigned long       task_stack_end;           /* Stack ending address     */
    unsigned long       task_stack_size;           /* Stack size               */	
    unsigned long       task_run_count;            /* Thread's run counter     */

} OSA_TASK;

# 87 "/os/osa/inc/osa.h"

# 98 "/os/osa/inc/osa.h"

# 114 "/os/osa/inc/osa.h"






 /*========================================================================
  *  OSA Return Error Codes
  *========================================================================*/

  enum
  {
    OS_SUCCESS = 0,        /* 0x0 -no errors                                        */
    OS_FAIL,               /* 0x1 -operation failed code                            */
    OS_TIMEOUT,            /* 0x2 -Timed out waiting for a resource                 */
    OS_NO_RESOURCES,       /* 0x3 -Internal OS resources expired                    */
    OS_INVALID_POINTER,    /* 0x4 -0 or out of range pointer value                  */
    OS_INVALID_REF,        /* 0x5 -invalid reference                                */
    OS_INVALID_DELETE,     /* 0x6 -deleting an unterminated task                    */
    OS_INVALID_PTR,        /* 0x7 -invalid memory pointer                           */
    OS_INVALID_MEMORY,     /* 0x8 -invalid memory pointer                           */
    OS_INVALID_SIZE,       /* 0x9 -out of range size argument                       */
    OS_INVALID_MODE,       /* 0xA, 10 -invalid mode                                 */
    OS_INVALID_PRIORITY,   /* 0xB, 11 -out of range task priority                   */
    OS_UNAVAILABLE,        /* 0xC, 12 -Service requested was unavailable or in use  */
    OS_POOL_EMPTY,         /* 0xD, 13 -no resources in resource pool                */
    OS_QUEUE_FULL,         /* 0xE, 14 -attempt to send to full messaging queue      */
    OS_QUEUE_EMPTY,        /* 0xF, 15 -no messages on the queue                     */
    OS_NO_MEMORY,          /* 0x10, 16 -no memory left                              */
    OS_DELETED,            /* 0x11, 17 -service was deleted                         */
    OS_SEM_DELETED,        /* 0x12, 18 -semaphore was deleted                       */
    OS_MUTEX_DELETED,      /* 0x13, 19 -mutex was deleted                           */
    OS_MSGQ_DELETED,       /* 0x14, 20 -msg Q was deleted                           */
    OS_MBOX_DELETED,       /* 0x15, 21 -mailbox Q was deleted                       */
    OS_FLAG_DELETED,       /* 0x16, 22 -flag was deleted                            */
    OS_INVALID_VECTOR,     /* 0x17, 23 -interrupt vector is invalid                 */
    OS_NO_TASKS,           /* 0x18, 24 -exceeded max # of tasks in the system       */
    OS_NO_FLAGS,           /* 0x19, 25 -exceeded max # of flags in the system       */
    OS_NO_SEMAPHORES,      /* 0x1A, 26 -exceeded max # of semaphores in the system  */
    OS_NO_MUTEXES,         /* 0x1B, 27 -exceeded max # of mutexes in the system     */
    OS_NO_QUEUES,          /* 0x1C, 28 -exceeded max # of msg queues in the system  */
    OS_NO_MBOXES,          /* 0x1D, 29 -exceeded max # of mbox queues in the system */
    OS_NO_TIMERS,          /* 0x1E, 30 -exceeded max # of timers in the system      */
    OS_NO_MEM_POOLS,       /* 0x1F, 31 -exceeded max # of mem pools in the system   */
    OS_NO_INTERRUPTS,      /* 0x20, 32 -exceeded max # of isr's in the system       */
    OS_FLAG_NOT_PRESENT,   /* 0x21, 33 -requested flag combination not present      */
    OS_UNSUPPORTED,        /* 0x22, 34 -service is not supported by the OS          */
    OS_NO_MEM_CELLS,       /* 0x23, 35 -no global memory cells                      */
    OS_DUPLICATE_NAME,     /* 0x24, 36 -duplicate global memory cell name           */
    OS_INVALID_PARM        /* 0x25, 37 -invalid parameter                           */
  };


/*****************************************************************************
 * OSA Data Types
 ****************************************************************************/


typedef void    *OsaRefT ;
typedef UINT8   OSA_STATUS;

/*========================================================================
 *  OSA Initialization:
 *
 *  Initializes OSA internal structures, tables, and OS specific services.
 *
 *========================================================================*/
typedef enum
{
    OSA_SISR_HIGH_PRIORITY = 0,
    OSA_SISR_MED_PRIORITY  = 1,
    OSA_SISR_LOW_PRIORITY  = 2,
    OSA_SISR_MAX_PRIORITY           //  Keep Last.
}
OsaSisrPriorityT ;

typedef struct
{
    UINT32      *SisrStackPtr[(int)OSA_SISR_MAX_PRIORITY] ;     //  [OP,IN] SISR Stack Pointers.
    UINT32      SisrStackSize[(int)OSA_SISR_MAX_PRIORITY] ;     //  [IN]    SISR Stack Size.
}
OsaInitParamsT ;


 extern OSA_STATUS   OsaInit(
    OsaInitParamsT          *pParams            //  [OP,IN] Initialization parameters.
    ) ;

 extern void        OsaRun( void *pForFutureUse );

 extern char        *OsaGetVersion( void );

/*========================================================================
 *  OSA Task Management:
 *========================================================================*/
typedef struct
{
    void                (*entry)(void *) ;      //  Task entry function.
    UINT32              stackSize ;             //  Size of stack.
    UINT32              *stackPtr ;             //  [OP]    Memory area for the stack.
    void                *argv ;                 //  [OP]    A pointer that is passed to the task.
    char                *name ;                 //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;         //  [OP]    TRUE - The object can be accessed from all processes.
    UINT8               priority ;              //  Task priority.
}
OsaTaskCreateParamsT ;

 extern OSA_STATUS  OsaTaskCreateEx(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    OsaTaskCreateParamsT    *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTaskCreate(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    OsaTaskCreateParamsT    *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTaskDelete(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTaskSuspend(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS OsaTaskResume(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    void                    *pForFutureUse
    ) ;

 extern void        OsaTaskSleep(
    UINT32                  ticks,              //  [IN]    Time to sleep in ticks.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTaskChangePriority(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    UINT8                   newPriority,        //  [IN]    New task priority.
    UINT8                   *oldPriority,       //  [OT]    Old task priority.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTaskGetPriority(
    OsaRefT                 OsaRef,             //  [IN]    Reference.
    UINT8                   *pPriority,         //  [OT]    Task priority.
    void                    *pForFutureUse
    ) ;
// heroengine start // FEATURE_HERO_ENGINE_APP



// heroengine end // FEATURE_HERO_ENGINE_APP
 extern OSA_STATUS OsaHisrCreate(
    OsaRefT* OsaRef, 
    CHAR* name, 
    void (*hisr_entry)(void), 
    unsigned char priority
    );
 extern OSA_STATUS OsaHisrActivate(OsaRefT *hisr);
 extern OSA_STATUS OsaHisrDel(OsaRefT *hisr);
 extern OSA_STATUS OsaHISRGetPriority( 
	OsaRefT 				OsaRef, 			//  [IN]    Reference.
	UINT8 					*pPriority, 		//  [OT]    Task priority.
	void 					*pForFutureUse 
	) ;

 extern void        OsaTaskYield(  void *pForFutureUse  ) ;

 extern OSA_STATUS  OsaTaskGetCurrentRef(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    void                    *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaHISRGetCurrentRef(
    OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    void                    *pForFutureUse
    ) ;
 extern OSA_STATUS OsaTaskTerminate( OsaRefT OsaRef, void *pForFutureUse );
 extern OSA_STATUS OsaGetTaskInfo(OsaRefT OsaRef, OSA_TASK *task_info);

 extern OSA_STATUS OsaGetCurrentThreadRef(
	OsaRefT                 *pOsaRef,           //  [OT]    Reference.
    void                    *pForFutureUse
  );
 extern OSA_STATUS OsaGetThreadListHead(
	OsaRefT *pListHead, //  [OT]    Reference.
	void *pForFutureUse 
);

 extern OSA_STATUS OsaGetCreatedThreadCount(
	unsigned long *count, //  [OT]    count
	void *pForFutureUse 
);

 extern OSA_STATUS OsaGetMaxThreadCount(
	unsigned long *maxCnt, //  [OT]	  count
	void *pForFutureUse 
);
 extern unsigned long OsaThreadList(OsaRefT *ptrList, unsigned long maximum_pointers,void *pForFutureUse);

/*========================================================================
 *  OSA Semaphore Management
 *========================================================================*/
typedef struct
{
    UINT32              initialCount ;              //  [OP]    Initial count of the semaphore (0 = Lock), (Default = 1).
    UINT32              maximumCount ;              //  [OP]    Maximum tasks that can "pass" through the semaphore, (Default = max(1,initialCount)).
    UINT8               waitingMode ;               //  [OP]    OS_FIFO, OS_PRIORITY, (Default = OS_PRIORITY).
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaSemaphoreCreateParamsT ;

 extern OSA_STATUS  OsaSemaphoreCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaSemaphoreCreateParamsT   *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    , UINT32 callerAddress) ;

 extern OSA_STATUS  OsaSemaphoreAcquire(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaSemaphorePoll(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *pCount,            //  [OT]    Current semaphore count.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaSemaphoreRelease(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaSemaphoreDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Mutex Management
 *========================================================================*/
typedef struct
{
    UINT8               waitingMode ;               //  [OP]    OS_FIFO, OS_PRIORITY, (Default = OS_PRIORITY).
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
    BOOL				inherit;
}
OsaMutexCreateParamsT ;

 extern OSA_STATUS  OsaMutexCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMutexCreateParamsT       *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    , UINT32 callerAddress) ;

 extern OSA_STATUS  OsaMutexLock(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMutexUnlock(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMutexDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMutexCreateEx(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMutexCreateParamsT       *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    , UINT32 callerAddress) ;

 extern OSA_STATUS  OsaMutexLockEx(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMutexUnlockEx(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMutexDeleteEx(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;


/*========================================================================
 *  OSA Interrupt Control
 *========================================================================*/
typedef struct
{
    UINT32              intSource ;                 //          Interrupt number (need to set to OSA_NULL_INT_SOURCE if not used).
    void                (*fisrRoutine)(UINT32) ;    //  [OP]    First level ISR to be called.
    void                (*sisrRoutine)(void) ;      //  [OP]    Second level ISR routine to be called.
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    UINT32              stackSize ;                 //  [OP]    Size of stack.
    OsaSisrPriorityT    priority ;                  //          SISR priority.
}
OsaIsrCreateParamsT ;



 extern OSA_STATUS  OsaIsrCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaIsrCreateParamsT         *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaIsrNotify(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaIsrDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*===========================================================================
 *  OSA Real-Time Access:
 *=========================================================================*/

 extern UINT32  OsaGetTicks( void *pForFutureUse ) ;

 extern UINT32  OsaGetClockRate( void *pForFutureUse ) ;

 extern void    OsaTick( void *pForFutureUse ) ;

/*========================================================================
 *  OSA Sys Context info
 *========================================================================*/
typedef struct
{
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaCriticalSectionCreateParamsT ;

 extern OsaRefT     OsaCriticalSectionCreate(
    OsaCriticalSectionCreateParamsT     *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OsaRefT     OsaCriticalSectionEnter(
    OsaRefT                             OsaRef,             //  [IN]    Reference from the Create Function.
    void                                *pForFutureUse
    ) ;

 extern void        OsaCriticalSectionExit(
    OsaRefT                             OsaRef,             //  [IN]    Reference from the Enter Function.
    void                                *pForFutureUse
    ) ;

 extern void        OsaCriticalSectionDelete(
    OsaRefT                             OsaRef,             //  [IN]    Reference from the Create Function.
    void                                *pForFutureUse
    ) ;

 extern UINT32      OsaControlInterrupts(
    UINT32                              mask,               //  [IN]    New interrupt mask.
    void                                *pForFutureUse
    ) ;

 extern UINT32      OsaContextLock( void *pForFutureUse ) ;

 extern UINT32      OsaContextRestore( void *pForFutureUse ) ;

 extern OSA_STATUS OsaContextLockExt( void *pForFutureUse );
 
 extern OSA_STATUS OsaContextRestoreExt( void *pForFutureUse );

 /*========================================================================
  *  OSA Message Passing
  *========================================================================*/
typedef struct
{
    UINT32              maxSize ;                   //  Max message size the queue supports.
    UINT32              maxMsg ;                    //  Max # of messages in the queue.
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
    UINT8               waitingMode ;               //  OS_FIFO, OS_PRIORITY.
}
OsaMsgQCreateParamsT ;

typedef struct
{
    void                *msgPtr ;                   //  [IN]    Start address of the data.
    UINT32              size ;                      //  [IN]    Size of the message.
    UINT32              timeout ;                   //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
}
OsaMsgQSendParamsT ;

typedef struct
{
    void                *msgPtr ;                   //  [IN]    Start address of the data.
    UINT32              size ;                      //  [IN]    Size of the message.
    UINT32              timeout ;                   //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    UINT32              actualSize ;                //  [OT]    Actual bytes read from queue.
}
OsaMsgQRecvParamsT ;

 extern OSA_STATUS  OsaMsgQCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMsgQCreateParamsT        *pParams            //  [IN]    Input Parameters (see datatype for details).
    ,UINT32 callerAddress) ;

 extern OSA_STATUS  OsaMsgQSend(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaMsgQSendParamsT          *pParams            //  [IN]    See datatype.
    ) ;

 extern OSA_STATUS  OsaMsgQRecv(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaMsgQRecvParamsT          *pParams            //  [IN]    See datatype.
    ) ;

 extern OSA_STATUS  OsaMsgQPoll(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *pCount,            //  [OT]    Number of messages in Q.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMsgQDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 /*========================================================================
  *  OSA Mailboxes
  *========================================================================*/
typedef struct
{
    UINT32              maxMsg ;                    //  Max # of messages in the queue.
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
    UINT8               waitingMode ;               //  OS_FIFO, OS_PRIORITY.
}
OsaMailboxQCreateParamsT ;

 extern OSA_STATUS  OsaMailboxQCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMailboxQCreateParamsT    *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaMailboxQSend(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      mboxData,           //  [IN]    Data to put in mailbox.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMailboxQRecv(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *mboxData,          //  [OT]    Data read from mailbox.
    UINT32                      timeout,            //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMailboxQPoll(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      *pCount,            //  [OT]    Number of messages in Q.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaMailboxQDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Event Management:
 *========================================================================*/
typedef struct
{
    char                *name ;                     //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;             //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaFlagCreateParamsT ;

typedef struct
{
    UINT32              mask ;                      //  [IN]    Flag mask.
    UINT32              operation ;                 //  [IN]    OSA_FLAG_AND, OSA_FLAG_AND_CLEAR, OSA_FLAG_OR, OSA_FLAG_OR_CLEAR.
    UINT32              timeout ;                   //  [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout in OS ticks.
    UINT32              *flags ;                    //  [OT]    Current value of the flag.
}
OsaFlagWaitParamsT ;

 extern OSA_STATUS  OsaFlagCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaFlagCreateParamsT        *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaFlagSet(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    UINT32                      mask,               //  [IN]    Flag mask.
    UINT32                      operation,          //  [IN]    OSA_FLAG_AND, OSA_FLAG_OR.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaFlagWait(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaFlagWaitParamsT          *pParams            //  [IN]    Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaFlagDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*========================================================================
 *  OSA Timer Management:
 *========================================================================*/
typedef struct
{
    UINT32              initialTime ;                   //  [IN]    Initial expiration time in clock ticks.
    UINT32              rescheduleTime ;                //  [IN]    Periodic expiration time in clock ticks. 0=One shot timer.
    void                (*callBackRoutine)(UINT32) ;    //  [IN]    Routine to call when timer expiers.
    UINT32              timerArgc ;                     //  [IN]    Argument to be passed to the callBackRoutine
    char                *name ;                         //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;                 //  [OP]    TRUE - The object can be accessed from all processes.
}
OsaTimerParamsT ;

typedef struct
{
    UINT32              status  ;                       //  [O]     Timer status OS_ENABLED, OS_DISABLED.
}
OsaTimerStatusParamsT ;

 extern OSA_STATUS  OsaTimerCreate(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaTimerParamsT             *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTimerStart(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaTimerParamsT             *pParams            //  [OP,IN] Input Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTimerStop(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

 extern OSA_STATUS  OsaTimerStatus(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    OsaTimerStatusParamsT       *pParams            //  [OT]    Output Parameters (see datatype for details).
    ) ;

 extern OSA_STATUS  OsaTimerDelete(
    OsaRefT                     OsaRef,             //  [IN]    Reference.
    void                        *pForFutureUse
    ) ;

/*============================================================================
 * Special memory handling that doesn't use the OS memory services.
 *============================================================================*/
typedef struct
{
    void                *poolBase ;                     //          Pointer to start of pool memory.
    UINT32              poolSize ;                      //          size of the pool.
    char                *name ;                         //  [OP]    Pointer to a NULL terminated string.
    BOOL                bSharedForIpc ;                 //  [OP]    TRUE - The object can be accessed from all processes.
    UINT32              LowWaterMark ;                  //  [OP]    Number of bytes left in pool tp trigger the LowWaterMarkCbFunc.
    void                (*LowWaterMarkCbFunc)(UINT32) ; //  [OP]    Routine to call when the LowWaterMark is triggered.
}
OsaMemCreateParamsT ;

 extern OSA_STATUS  OsaMemAddMemoryToPool(
    OsaRefT                     OsaRef,             //  [OP,IN] Pool Reference, if NULL, the default pool is used.
    void                        *memBase,           //  [IN]    Pointer to the memory to be added.
    UINT32                      memSize,            //  [IN]    Size of the memory to be added.
    void                        *pForFutureUse
    ) ;

 extern void *      OsaMemAlloc(
    OsaRefT                     OsaRef,             //  [OP,IN] Pool Reference, if NULL, the default pool is used.
    UINT32                      Size                //  [IN]    Number of bytes to be allocated.
    ) ;

 extern BOOL        OsaMemAllocAgain(
    void                        *pMem               //  [IN]    Pointer to the memory to be allocated again.
    ) ;

 extern OSA_STATUS  OsaMemCreatePool(
    OsaRefT                     *pOsaRef,           //  [OT]    Reference.
    OsaMemCreateParamsT         *pParams            //  [IN] Input Parameters (see datatype for details).
    ) ;

 extern void        OsaMemFree(
    void                        *pMem               //  [IN]    Pointer to the memory to be freed.
    ) ;

 extern UINT32      OsaMemGetAllocSize(
    void                        *pMem               //  [IN]    Pointer to the memory.
    ) ;

 extern OsaRefT     OsaMemGetDefaultPoolRef( void ) ;

 extern OsaRefT     OsaMemGetPoolRef(
    char                        *poolName,          //  [OP,IN] Pool's name - can be NULL.
    void                        *pMem,              //  [OP,IN] A memory address we need the poolRef for it - can be NULL.
    void                        *pForFutureUse
    ) ;

 extern UINT32      OsaMemGetUserParam(
    void                        *pMem               //  [IN]    Pointer to the allocated memory.
    ) ;

 extern BOOL        OsaMemResizeAlloc(
    void                        *pMem,              //  [IN]    Pointer to the memory to be re-sized.
    UINT32                      NewSize             //  [IN]    New Size.
    ) ;

 extern void        OsaMemSetDefaultPool(
    OsaRefT                     OsaRef              //  [IN]    Pool Reference.
     ) ;

 extern OSA_STATUS  OsaMemSetUserParam(
    void                        *pMem,              //  [IN]    Pointer to the allocated memory.
    UINT32                      Param               //  [IN]    User's parameter.
     ) ;

 extern OSA_STATUS	OsaPartitionAllocate(
	OsaRefT * 					pOsaRef, 
	void **						return_pointer, 
	UINT32 						suspend
	);

 extern OSA_STATUS	OsaPartitionFree(
	OsaRefT 					OsaRef
	);

 extern OSA_STATUS OsaPartitionPoolCreate(
	OsaRefT *					pOsaRef, 
	char *						pool_name, 
	void *						start_address, 
	UINT32 						pool_size, 
	UINT32 						partition_size, 
	UINT32 						suspend_type
	);

 extern UINT32 Osa_TimeoutValue( 
	UINT32 						timeout 
	);

 extern OSA_STATUS OsaSystemProtectInit(void);

 extern OSA_STATUS OsaSystemProtect(void);

 extern OSA_STATUS OsaSystemUnProtect(void);

 extern UINT32 OsaGetInterruptCount(void);

 extern OSA_STATUS OsaListAllCreatedTasks( void );

 extern OSA_STATUS OsaListAllCreatedTimers( void );

 extern OSA_STATUS OsaListAllCreatedEventFlags( void );

 extern OSA_STATUS OsaListAllCreatedMemBlockPools( void );

 extern OSA_STATUS OsaListAllCreatedMsgQs( void );

 extern OSA_STATUS OsaListAllCreatedSemaphores( void );

 extern OSA_STATUS OsaListAllCreatedMutexs( void );

 extern OSA_STATUS OsaListAllCreatedMemBytesPoolsStatus(void);

/*=============================================================================
 * Remap old names to new ones to remain backwards compatibility with old names.
 *=============================================================================*/
# 1 "/os/osa/inc/osa_old_api.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/







/*****************************************************************************
 * OSA Data Types
 ****************************************************************************/

# 1 "/os/osa/inc/osa.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa.h
Description : This file defines the OS wrapper API and definitions for external application use.

              The OSA API allows applications to be developed independent of
              the target microkernel/hardware environment. It provides the
              facility to add support for multiple independent operating
              systems from different vendors.

Copyright (c) 2001 Intel CCD. All Rights Reserved
=========================================================================== */
# 859 "/os/osa/inc/osa.h"

# 17 "/os/osa/inc/osa_old_api.h"

# 24 "/os/osa/inc/osa_old_api.h"
typedef void*   OSATaskRef;
typedef void*   OSAHISRRef;
typedef void*   OSASemaRef;
typedef void*   OSAMutexRef;
typedef void*   OSAMsgQRef;
typedef void*   OSAMailboxQRef;
typedef void*   OSAPoolRef;
typedef void*   OSATimerRef;
typedef void*   OSAFlagRef;
typedef void*	OSAPartitionPoolRef;
/* Remain for backwards compatibility */
typedef void*   OSTaskRef;
typedef void*   OSSemaRef;
typedef void*   OSMutexRef;
typedef void*   OSMsgQRef;
typedef void*   OSMailboxQRef;
typedef void*   OSPoolRef;
typedef void*   OSTimerRef;
typedef void*   OSFlagRef;
typedef UINT8   OS_STATUS ;


/*****************************************************************************
 * OSA Function Prototypes
 ****************************************************************************/


/*========================================================================
 *  OSA Initialization:
 *
 *  Initializes OSA internal structures, tables, and OS specific services.
 *
 *========================================================================*/

/***********************************************************************
 *
 * Name:        OSAInitialize()
 *
 * Description: Initialize OS specific stuff.
 *
 * Parameters:  None
 *
 * Returns:     
 *	OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSARun()
 *
 * Description: Activates the OS.
 *
 * Parameters:  None
 *
 * Returns:     None
 *
 * Notes:
 *
 ***********************************************************************/



/*========================================================================
 *  OSA Task Management:
 *========================================================================*/

/***********************************************************************
 *
 * Name:        OSATaskCreate
 *
 * Description: Create Task.
 *
 * Parameters:
 *  OSATaskRef                 	*taskRef      [OUT]    OS task reference 
 *  void		                  	*stackPtr      [IN]    pointer to start of task stack area   
 *  UINT32 	                  	stackSize      [IN]    number of bytes in task stack area 
 *  UINT8                	    	priority     [IN]    task priority 0 - 252  
 *  CHAR                 		*taskName      [IN]    task name
 *  void                 			*taskStart(void*)      [IN]   pointer to task entry point 
 *  void                 			*argv      [IN]    task entry argument pointer  
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSATaskCreate(
    OSATaskRef* taskRef,     
    void*       stackPtr,  
    UINT32      stackSize,      
    UINT8       priority,       
    CHAR        *taskName,      
    void        (*taskStart)(void*), 
    void*       argv            
 );


/***********************************************************************
 *
 * Name:        OSATaskCreateEx
 *
 * Description: Create Task.
 *
 * Parameters:
 *  OSATaskRef                 	*taskRef      [OUT]    OS task reference 
 *  void		                  	*stackPtr      [IN]    pointer to start of task stack area   
 *  UINT32 	                  	stackSize      [IN]    number of bytes in task stack area 
 *  UINT8                	    	priority     [IN]    task priority 0 - 252  
 *  CHAR                 		*taskName      [IN]    task name
 *  void                 			*taskStart(void*)      [IN]   pointer to task entry point 
 *  void                 			*argv      [IN]    task entry argument pointer  
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 * The task created doesn't start automatically.
 *
 ***********************************************************************/
 extern OSA_STATUS OSATaskCreateEx(
    OSATaskRef* taskRef,     
    void*       stackPtr,  
    UINT32      stackSize,      
    UINT8       priority,       
    CHAR        *taskName,      
    void        (*taskStart)(void*), 
    void*       argv            
 );

/***********************************************************************
 *
 * Name:        OSATaskDelete
 *
 * Description: Delete Task.
 *
 * Parameters:
 *  OSATaskRef                 oSArEF      [IN]    Task Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSATaskTerminate
 *
 * Description: Terminate Task.
 *
 * Parameters:
 *  OSATaskRef                 oSArEF      [IN]    Task Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/




/***********************************************************************
 *
 * Name:        OSATaskSuspend
 *
 * Description: Suspend Task.
 *
 * Parameters:
 *  OSATaskRef                 oSArEF      [IN]    Task Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/




/***********************************************************************
 *
 * Name:        OSATaskResume
 *
 * Description: Resume Task.
 *
 * Parameters:
 *  OSATaskRef                 oSArEF      [IN]    Task Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSATaskSleep
 *
 * Description: Task sleep.
 *
 * Parameters:
 *  UINT32              tICKS           [IN]    Ticks to sleep.(1s=200ticks)
 *
 * Returns: None
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSATaskChangePriority
 *
 * Description: Change task priority.
 *
 * Parameters:
 *  OSATaskRef          oSArEF          [IN]    Task Reference.
 *  UINT8                   nEW     [IN]    New Priority.
 *  UINT8                  *oLD    [OUT]    Old Priority.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSATaskGetPriority
 *
 * Description: Get task priority.
 *
 * Parameters:
 *  OSATaskRef                 oSArEF          [IN]    Task Reference.
 *  UINT8                   *pRIORITY      [OUT]    Priority.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAHISRGetPriority
 *
 * Description: Get HISR priority.
 *
 * Parameters:
 *  OSATaskRef                 oSArEF          [IN]    HISR Reference.
 *  UINT8                   *pRIORITY      [OUT]    Priority.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSATaskYield
 *
 * Description: Allow other task to run.
 *
 * Parameters: None
 *
 * Returns:  None
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSATaskGetCurrentRef
 *
 * Description: Get Task Ref.
 *
 * Parameters:
 *  OSATaskRef                 *PoSArEF    [OUT]    Task Reference.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAHISRGetCurrentRef
 *
 * Description: Get HISR Ref.
 *
 * Parameters:
 *  OSAHISRRef                 *PoSArEF    [OUT]    HISR Reference.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAGetCurrentThreadRef
 *
 * Description: Get Current thread Ref.
 *
 * Parameters:
 *  OSATaskRef                 *PoSArEF    [OUT]    thread Reference.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAGetTaskInfo
 *
 * Description: Get the detailed information of specific thread
 *
 * Parameters:
 *  OSATaskRef                 oSArEF          [IN]    thread Reference.
 *  OSA_TASK                   *pTaskInfo      [OUT]    structure of thread info.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAGetThreadListHead
 *
 * Description: Get the head of created thread reference list
 *
 * Parameters:
 *  OSATaskRef                 *pListHead    [OUT]   list head.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAGetCreatedThreadCount
 *
 * Description: Get the count of created thread
 * Parameters:
 *  unsigned long                 *pCount    [OUT]   out.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAGetMaxThreadCount
 *
 * Description: Get the max number of thread that system can be created 
 * Parameters:
 *  unsigned long                 *pCount    [OUT]   out.
 *
 * Returns:
 *      OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAThreadList
 *
 * Description: Get the reference list of created thread 
 * Parameters:
 *  OSATaskRef                 *ptrList          [OUT]    thread Reference list.
 *  unsigned long               maximum_pointerst    [IN]   length of array
 *
 * Returns:
 *      unsigned long        Number of threads placed in list
 *
 * Notes:
 *
 ***********************************************************************/



/*========================================================================
 *  OSA Semaphore Management
 *========================================================================*/

/***********************************************************************
 *
 * Name:        OSASemaphoreCreate
 *
 * Description: Create Semaphore.
 *
 * Parameters:
 *  OSASemaRef                 		*semaRef      [OUT]    OS semaphore reference  
 *  UINT32		                  	initialCount      [IN]     initial count of the semaphore   
 *  UINT8 	                  		waitingMode      [IN]    mode of tasks waiting (OS_FIFO, OS_PRIORITY)
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSASemaphoreCreate (
    OSASemaRef   *semaRef,   
    UINT32      initialCount,   
    UINT8       waitingMode    
 );

/***********************************************************************
 *
 * Name:        OSASemaphoreAcquire
 *
 * Description: Acquire semaphore.
 *
 * Parameters:
 *  OSASemaRef                 	oSArEF          [IN]    Reference.
 *  UINT32                  		tIMEoUT         [IN]    Timeout in OS Ticks.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSASemaphorePoll
 *
 * Description: Poll semaphore.
 *
 * Parameters:
 *  OSASemaRef                 	oSArEF          [IN]    Reference.
 *  UINT32                  		*PcOUNT         [OUT]    Current semaphore count.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSASemaphoreRelease
 *
 * Description: Release semaphore.
 *
 * Parameters:
 *  OSASemaRef                 oSArEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSASemaphoreDelete
 *
 * Description: Delete semaphore.
 *
 * Parameters:
 *  OSASemaRef                 oSArEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OsaSemaphoreCheck
 *
 * Description: Check whether semaphore is valid or not.
 *
 * Parameters:
 *  OsaRefT                 OsaRef          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/*========================================================================
 *  OSA Mutex Management
 *========================================================================*/
 extern OSA_STATUS OSAMutexCreate (
    OSMutexRef  *mutexRef,       /* OS mutex reference                         */
    UINT8       waitingMode      /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );

 extern OSA_STATUS OSAMutexCreateEx (
    OSMutexRef  *mutexRef,       /* OS mutex reference                         */
    UINT8       waitingMode      /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );

/***********************************************************************
 *
 * Name:        OSAMutexLock
 *
 * Description: 
 *
 * Parameters:
 *  OSAMutexRef                 oSArEF          [IN]    Reference.
 *  UINT32                  tIMEoUT         [IN]    Timeout in OS Ticks.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/




/***********************************************************************
 *
 * Name:        OSAMutexUnlock
 *
 * Description: 
 *
 * Parameters:
 *  OSAMutexRef                 oSArEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAMutexDelete
 *
 * Description: 
 *
 * Parameters:
 *  OSAMutexRef                 oSArEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/*========================================================================
 *  OSA Interrupt Control
 *========================================================================*/


/***********************************************************************
 *
 * Name:        OSACreateHISR
 *
 * Description: Create ISR.
 *
 *  Parameters:
 *  OSAHISRRef                 	*oSArEF      [OUT]  oOS HISR  reference 
 *  CHAR		                  	*name      [IN]    HISR name
  *  void                 		*hisr_entry(void)      [IN]   pointer to HISR entry point 
 *  UINT8                	    	priority     [IN]    HISR priority 0 - 2  
 *
 * Returns:
 *  None
 *
 * Notes:
 *
 ***********************************************************************/




/***********************************************************************
 *
 * Name:        OSADeleteHISR
 *
 * Description: Delete ISR.
 *
 *  Parameters:
 *  OSAHISRRef                 *oSArEF      [IN]    reference 
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAActivateHISR
 *
 * Description: Activate ISR.
 *
 *  Parameters:
 *  OSAHISRRef                 *oSArEF      [IN]    reference 
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/*===========================================================================
 *  OSA Real-Time Access:
 *=========================================================================*/
 /***********************************************************************
 *
 * Name:        OSAGetTicks
 *
 * Description: Number of ticks that passed since last reset.
 *
 * Parameters: None
 *
 * Returns:  UINT32   Number of ticks that passed since last reset
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAGetClockRate
 *
 * Description: Get the current system clock rate.
 *
 * Parameters: None
 *
 * Returns:     UNIT32  - current clock rate (ms / tick)
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSATick
 *
 * Description: Tick the OS.
 *
 * Parameters: None
 *
 * Returns: None
 *
 * Notes:
 *
 ***********************************************************************/



/*========================================================================
 *  OSA Sys Context info
 *========================================================================*/




/***********************************************************************
 *
 * Name:        OSAContextLock
 *
 * Description: Lock context - No interrupts and no preemptions.
 *
 * Parameters:  None
 *
 * Returns:		OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:       OSAContextUnlock
 *
 * Description: Restore the context.
 *
 * Parameters: None
 *
 * Returns:    OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/


 /***********************************************************************
  *
  * Name:		 OSAContextLockExt
  *
  * Description: Lock specific context-only interrtup and HISR preemptions allowed.
  *
  * Parameters:  None
  *
  * Returns:	 OS_SUCCESS/OS_FAIL
  *
  * Notes:
  *
  ***********************************************************************/

 
 
 /***********************************************************************
  *
  * Name:		OSAContextUnlockExt
  *
  * Description: Restore the specific context.
  *
  * Parameters: None
  *
  * Returns:	OS_SUCCESS/OS_FAIL
  *
  * Notes:
  *
  ***********************************************************************/

 

 /*========================================================================
  *  OSA Message Passing
  *========================================================================*/

/***********************************************************************
 *
 * Name:        OSAMsgQCreate
 *
 * Description: create message queue
 *
 * Parameters:
 *  OSMsgQRef                 	*msgQRef          [OUT]    OS message queue reference .
 *  char                  		*queueName       	 [IN]   name of message queue.
 *  UINT32                  		maxSize         [IN]    max message size the queue supports .
 *  UINT32                  		maxNumber         [IN]    max # of messages in the queue  . 
 *  UINT32                  		waitingMode         [IN]    mode of tasks waiting OS_FIFO, OS_PRIORITY.
  
 * Returns:    OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMsgQCreate(
    OSMsgQRef   *msgQRef,       

    char        *queueName,     

    UINT32      maxSize,       
    UINT32      maxNumber,      
    UINT8       waitingMode    
 );


/***********************************************************************
 *
 * Name:		OSAMsgQSend
 *
 * Description: Send to message Q.
 *
 * Parameters:
 *  OSMsgQRef                 	msgQRef          [IN]    OS message queue reference .
 *  UINT32                  		size         [IN]    size of the message 
 *  UINT8                  		*msgPtr         [IN]    start address of the data to be sent
 *  UINT32                  		timeout         [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout

 *
 * Returns:
 *	OSA_STATUS	OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMsgQSend (
    OSMsgQRef   msgQRef,       
    UINT32      size,           
    UINT8       *msgPtr,        
    UINT32      timeout       
 );


/***********************************************************************
 *
 * Name:		OSAMsgQRecv
 *
 * Description: Recieve from message Q..
 *
 * Parameters:
 *  OSMsgQRef                 	msgQRef          [IN]    OS message queue reference .
 *  UINT8                  		*recvMsg         OUT]     pointer to the message received
 *  UINT32                  		size         [IN]    size of the message 
 *  UINT32                  		timeout         [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout

 *
 * Returns:
 *	OSA_STATUS	OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMsgQRecv (
    OSMsgQRef   msgQRef,       
    UINT8       *recvMsg,       
    UINT32      size,          
    UINT32      timeout         
 );

/***********************************************************************
 *
 * Name:        OSAMsgQPoll
 *
 * Description: Get the number of messages in queue.
 *
 * Parameters:
 *  OSAMsgQRef          oSArEF          [IN]    Reference.
 *  UINY32                  *cOUNT         [OT]    Number of messages in queue.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAMsgQDelete
 *
 * Description: Delete message Q.
 *
 * Parameters:
 *  OSAMsgQRef                 oSArEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



 /*========================================================================
  *  OSA Mailboxes
  *========================================================================*/

/***********************************************************************
 *
 * Name:        OSAMailboxQCreate
 *
 * Description: Create a mailbox Q.
 *              Each mailbox entry is 32 bit.
 *
 * Parameters:
 *  OSMailboxQRef                     *mboxQRef    [OUT]    OS mailbox queue reference.
 *  char                  			*queueName       	 [IN]   name of mailbox queue 
 *  UINT32                  			maxNumber         [IN]    max # of messages in the queue  . 
 *  UINT8                  			waitingMode         [IN]    mode of tasks waiting OS_FIFO, OS_PRIORITY.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMailboxQCreate (
    OSMailboxQRef   *mboxQRef,      /* OS mailbox queue reference              */

    char            *queueName,     /* name of mailbox queue                   */

    UINT32          maxNumber,      /* max # of messages in the queue          */
    UINT8           waitingMode     /* mode of tasks waiting OS_FIFO, OS_PRIORITY */
 );


/***********************************************************************
 *
 * Name:        OSAMailboxQSend
 *
 * Description: Write data to mailbox Q.
 *
 * Parameters:
 *  OSAMailboxQRef                 	oSArEF          [IN]    Reference.
 *  UINT32                  			pTR       	 [IN]    Data to put in mailbox.
 *  UINT32                  			tIMEOUT         [IN]    timeout.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAMailboxQRecv
 *
 * Description: Read from mailbox Q.
 *
 * Parameters:
 *  OSAMailboxQRef                 	oSArEF          [IN]    Reference.
 *  UINT32                 			*pTR        [OUT]    Data to receive from mailbox.
 *  UINT32                  			tIMEOUT         [IN]    timeout.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAMailboxQPoll
 *
 * Description: Gen the number of messages in queue.
 *
 * Parameters:
 *  OSAMailboxQRef                 oSArEF          [IN]    Reference.
 *  UINY32                  		   *cOUNT         [OUT]    Number of messages in queue.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAMailboxQDelete
 *
 * Description: Delete mailbox Q.
 *
 * Parameters:
 *  OSAMailboxQRef                 oSArEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/*========================================================================
 *  OSA Event Management:
 *========================================================================*/

/***********************************************************************
 *
 * Name:        OsaFlagWait
 *
 * Description: Wait for event.
 *
 * Parameters:
 *  OSFlagRef                 flagRef          [IN]    OS reference to the flag.
 *  UINT32                  mask       	 	[IN]    flag mask to wait for .
 *  UINT32                  operation         [IN]    OSA_FLAG_AND, OSA_FLAG_AND_CLEAR,  OSA_FLAG_OR, OSA_FLAG_OR_CLEAR.
 *  UINT32                  *flags       	 	[OUT]    Current value of all flags 
 *  UINT32                  timeout       	[IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout 
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAFlagWait(
    OSFlagRef   flagRef,        
    UINT32      mask,          
    UINT32      operation,                         
    UINT32      *flags,       
    UINT32      timeout        
 );
/***********************************************************************
 *
 * Name:        OSAFlagCreate
 *
 * Description: Create a flag event group.
 *
 * Parameters:
 *  OSFlagRef                     *rEF    [OT]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAFlagSet
 *
 * Description: Set an event.
 *
 * Parameters:
 *  OSFlagRef                 rEF          [IN]    Reference.
 *  UINT32                  mASK            [IN]    Flag mask.
 *  UINT32                  oP       [IN]    OSA_FLAG_AND, OSA_FLAG_OR.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAFlagDelete
 *
 * Description: Delete event group.
 *
 * Parameters:
 *  OSFlagRef                 rEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/*========================================================================
 *  OSA Timer Management:
 *========================================================================*/

/***********************************************************************
 *
 * Name:        OSATimerStart
 *
 * Description: Create a Timer if needed and activate it.
 *
 * Parameters:
 *  OSTimerRef                 	timerRef          [IN]    OS supplied timer reference.
 *  UINT32                  		initialTime       	 	[IN]     initial expiration time in OS tick (5ms each)
 *  UINT32                 	 	rescheduleTime         [IN]    timer period after initial expiration
 *  void                  		(*callBackRoutine)(UINT32)       	 	[IN]    timer call-back routine 
 *  UINT32                  		timerArgc       	[IN]    argument to be passed to call-back on expiration 
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSATimerStart(
    OSTimerRef  timerRef,   
    UINT32      initialTime,    
    UINT32      rescheduleTime,
    void        (*callBackRoutine)(UINT32),
    UINT32      timerArgc 
 );

typedef OsaTimerStatusParamsT OSATimerStatus ;
/***********************************************************************
 *
 * Name:        OSATimerCreate
 *
 * Description: Create a Timer, if no input params the timer will be created on OsaTimerStart.
 *
 * Parameters:
 *  OSTimerRef                     *rEF    [OUT]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSATimerDelete
 *
 * Description: Delete timer.
 *
 * Parameters:
 *  OSTimerRef                 rEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSATimerStop
 *
 * Description: Stop timer.
 *
 * Parameters:
 *  OSTimerRef                 rEF          [IN]    Reference.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSATimerGetStatus
 *
 * Description: Get timer status.
 *
 * Parameters:
 *  OSTimerRef                 rEF          [IN]    Reference.
 *  OSATimerStatus                *sTATUS          [OT]    timer status.
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/*========================================================================
 *  OSA Memory Heap Access
 *
 *  Allocating Memory -
 *
 *  Deallocating Memory -
 *
 *========================================================================*/


/***********************************************************************
 *
 * Name:        OSAMemPoolCreate
 *
 * Description: Create a memory pool
 *
 * Parameters:
 *  OSPoolRef                 	*poolRef          [OUT]    OS assigned reference to the pool.
 *  UINT32                  		poolType       	 	[IN]    OSA_FIXED or OS_VARIABLE
 *  UINT8                 	 	*poolBase         [IN]    pointer to start of pool memory
 *  UINT32                  		poolSize      	 	[IN]    number of bytes in the memory pool  
 *  UINT32                  		partitionSize       	[IN]     size of partitions in fixed pools 
 *  UINT8                 	 	waitingMode         [IN]    mode of tasks waiting OS_FIFO, OS_PRIORITY
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMemPoolCreate (
    OSPoolRef *poolRef,         
    UINT32    poolType,         
    UINT8*    poolBase,         
    UINT32    poolSize,         
    UINT32    partitionSize,    
    UINT8     waitingMode     
 );

/***********************************************************************
 *
 * Name:        OSAMemPoolCreateExt
 *
 * Description: Create a independent memory pool
 *
 * Parameters:
 *  OSPoolRef                 	*poolRef          [OUT]    OS assigned reference to the pool.
 *  UINT32                  		poolType       	 	[IN]    OSA_FIXED or OS_VARIABLE
 *  UINT8                 	 	*poolBase         [IN]    pointer to start of pool memory
 *  UINT32                  		poolSize      	 	[IN]    number of bytes in the memory pool  
 *  UINT32                  		partitionSize       	[IN]     size of partitions in fixed pools 
 *  UINT8                 	 	waitingMode         [IN]    mode of tasks waiting OS_FIFO, OS_PRIORITY
 *  char                 		*poolName      [IN]    pool name
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMemPoolCreateExt (
    OSPoolRef *poolRef,         
    UINT32    poolType,         
    UINT8*    poolBase,         
    UINT32    poolSize,         
    UINT32    partitionSize,    
    UINT8     waitingMode,
    char *poolName
 );

/***********************************************************************
 *
 * Name:        OSAMemPoolAlloc
 *
 * Description:  allocation from memory pool
 *
 * Parameters:
 *  OSPoolRef                 	poolRef          [IN]    OS assigned reference to the pool.
 *  UINT32                  		size       	 	[IN]    number of bytes to be allocated   
 *  void                 	 		**mem         [OUT]    pointer to start of allocated memory
 *  UINT32                  		timeout      	 	[IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout 
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMemPoolAlloc (
    OSPoolRef poolRef,        
    UINT32    size,           
    void**    mem,           
    UINT32    timeout           
 );


/***********************************************************************
 *
 * Name:        OSAMemPoolFree
 *
 * Description:  free memory
 *
 * Parameters:
 *  OSPoolRef                 	poolRef          [IN]    OS assigned reference to the pool. Unused, can be NULL
 *  void                 	 		*mem           [IN]     pointer to start of memory to be freed 
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/
 extern OSA_STATUS OSAMemPoolFree (
    OSPoolRef poolRef,       
    void*     mem               
 );

/***********************************************************************
 *
 * Name:        OSAMemPoolDelete
 *
 * Description:  delete memory pool
 *
 * Parameters:
 *  OSPoolRef                 	rEF          [IN]    OS assigned reference to the pool. Unused, can be NULL
 *
 * Returns:
 *  OS_SUCCESS
 *
 * Notes:
 *
 ***********************************************************************/






/***********************************************************************
 *
 * Name:        OsaPartitionPoolCreate
 *
 * Description: Create Partition Pool.
 *
 * Parameters:
 *  OSAPartitionPoolRef         *rEF       	 	[OUT]    OS assigned reference to the pool
 *  char                 	 		*nAME         [IN]    pool name
 *  void                  		*sTARTaDDR      	 	[IN]     pointer to start of pool memory 
 *  UINT32                  		pOOLsIZE       	[IN]      number of bytes in the memory pool 
 *  UINT32                  		pARTsIZE       	[IN]     size of partitions in fixed pools 
 *  UINT8                 	 	sUSPEND         [IN]    mode of tasks waiting OS_FIFO, OS_PRIORITY
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAPartitionAllocate
 *
 * Description: allocation a block from Partition Pool.
 *
 * Parameters:
 *  OSAPartitionPoolRef         *rEF       	 	[IN]    OS assigned reference to the pool
 *  void                  		**pBLOCK       	[OUT]      pointer to start of allocated block 
 *  UINT8                 	 	sUSPEND         [IN]    OS_SUSPEND, OS_NO_SUSPEND, or timeout
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAPartitionFree
 *
 * Description: free the block
 *
 * Parameters:
 *  void                  		*pBLOCK       	[IN]      the block to be freedl 
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


		
/***********************************************************************
 *
 * Name:        OSAListAllCreatedTasks
 *
 * Description: list all created tasks and HISRs
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAListAllCreatedTimers
 *
 * Description: list all created timers
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAListAllCreatedEventFlags
 *
 * Description: list all created event flags
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAListAllCreatedMemBlockPools
 *
 * Description: list all created memory block pools
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAListAllCreatedMsgQs
 *
 * Description: list all created message queues
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAListAllCreatedSemaphores
 *
 * Description: list all created semaphores
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAListAllCreatedMemBytesPoolsStatus
 *
 * Description: list all created memory bytes pools status
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  OSA_STATUS  OSA Complition Code.
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAGetDefaultMemPoolFreeSize
 *
 * Description: get the avaliable bytes of default heap pool
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  UINT32 the unused bytes of default heap pool
 *
 * Notes:
 *
 ***********************************************************************/



/***********************************************************************
 *
 * Name:        OSAGetMemPoolFreeSize
 *
 * Description: get the avaliable bytes of specificed memory pool
 *
 * Parameters:
 *  OSPoolRef      rEF       	 	[IN]    memory pool reference
 *
 * Returns:
 *  UINT32 the unused bytes of specificed memory pool
 *
 * Notes:
 *
 ***********************************************************************/


/***********************************************************************
 *
 * Name:        OSAGetVersion
 *
 * Description: get the current OS verison
 *
 * Parameters:
 *  None
 *
 * Returns:
 *  UINT8 *   OS verison string
 *
 * Notes:
 *
 ***********************************************************************/


/* Remap old names to new ones to remain backwards compatibility with old names.
 */
# 1634 "/os/osa/inc/osa_old_api.h"






# 1693 "/os/osa/inc/osa_old_api.h"

# 798 "/os/osa/inc/osa.h"
# 1 "/os/osa/inc/osa_utils.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa_utils.h
Description : 

Copyright (c) 2001 Intel CCD. All Rights Reserved
=========================================================================== */




typedef void*   OSATaskRef;
typedef void*   OSAHISRRef;

typedef void*   OSAMsgQRef;
typedef void*   OSAMailboxQRef;
typedef void*	OSAPartitionPoolRef;
typedef UINT8   OS_STATUS ;
 extern UINT32 OSATaskList(OSATaskRef *ptrList,UINT32 maximum_pointers);
 extern UINT32 OSAHISRList(OSAHISRRef  *ptrList,UINT32 maximum_pointers);
 extern void* 	OSATaskGetStackStart(OSATaskRef taskRef);
 extern UINT32		OSATaskGetStackSize(OSATaskRef taskRef);
 extern void 		(*OSATaskGetEntry(OSATaskRef taskRef))(void *);
 extern UINT32		OSATaskGetEntryParam(OSATaskRef hisrRef);
 extern char*		OSATaskGetName(OSATaskRef taskRef);
		

 extern UINT32 		OSATaskGetSysParam1(OSATaskRef taskRef);
 extern void 		OSATaskSetSysParam1(OSATaskRef taskRef, UINT32 value);
 extern UINT32 		OSATaskGetSysParam3(OSATaskRef taskRef);
 extern void 		OSATaskSetSysParam3(OSATaskRef taskRef, UINT32 value);


 extern void*		OSAHISRGetStackStart(OSATaskRef hisrRef);
 extern UINT32		OSAHISRGetStackSize(OSATaskRef hisrRef);
 extern void 		(*OSAHISRGetEntry(OSATaskRef hisrRef))(void);
 extern char*		OSAHISRGetName(OSATaskRef hisrRef);

 extern UINT32 		OSAHISRGetSysParam1(OSATaskRef hisrRef);
 extern void 		OSAHISRSetSysParam1(OSATaskRef hisrRef, UINT32 value);
 extern UINT32 		OSAHISRGetAppParam1(OSATaskRef hisrRef);
 extern void 		OSAHISRSetAppParam1(OSATaskRef hisrRef, UINT32 value);

 extern UINT32			OSAPartitionPoolGetAllocated(OSAPartitionPoolRef pool_ptr);
 extern char*			OSAPartitionPoolGetName(OSAPartitionPoolRef PartitionPoolRef);
 extern UINT32			OSAPartitionPoolGetAvailble(OSAPartitionPoolRef PartitionPoolRef);
 extern UINT32			OSAPartitionPoolGetPartitionSize(OSAPartitionPoolRef PartitionPoolRef);
 extern BOOL			OSAPartitionInUse( void* BlockPtr, void* PoolPtr );
 extern OSAPartitionPoolRef 	OSAPartitionGetPoolPtr( void* BlockPtr );
 extern OSA_STATUS 		OSAMsgQFrontSend(OSAMsgQRef queue_ptr, void *message,
                                        UINT32 size, UINT32 suspend);

 extern UINT64 Osa_TimeoutValueEx( UINT32 timeout );
 extern UINT32 OsaGetThreadEntry(OsaRefT osaRef);
# 799 "/os/osa/inc/osa.h"
// heroengine start //FEATURE_HERO_ENGINE_APP
# 1 "\\csw\\BSP\\inc\\bsp_hisr.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

//
// bsp_hisr.h
//




# 30 "\\csw\\BSP\\inc\\bsp_hisr.h"



/*************************************************************
             CREATE  HISR
             ------------
The using of "Create" is very ambigutive:
- with macro OS_Create_HISR
- with procedure Os_Create_HISR
- with procedure Manitoba_Create_HISR
Go to implement Os_Create_HISR() instead of Manitoba_Create_HISR()
"Manitoba_Create_HISR" to be obsolete. Use it as macro only (may be also macroed in the global_types.h)
**/
typedef UINT8   		OS_STATUS ;
	



typedef		void*				OS_HISR;


/*************************************************************
             CREATE  HISR
             ------------
The using of "Create" is very ambigutive:
- with macro OS_Create_HISR
- with procedure Os_Create_HISR
- with procedure Manitoba_Create_HISR
Go to implement Os_Create_HISR() instead of Manitoba_Create_HISR()
"Manitoba_Create_HISR" to be obsolete. Use it as macro only (Refer bsp_hisr.h)
*

*/

void Os_Create_HISR(void** hisr, char* name, void (*hisr_entry)(void), unsigned char priority);
INT32 Os_Activate_HISR(void** hisr);
INT32 Os_Delete_HISR(void** hisr);








int OS_Current_Interrupt_Count(void);








# 802 "/os/osa/inc/osa.h"
# 1 "\\os\\nu_xscale\\inc\\nucleus.h"
/*************************************************************************/
/*                                                                       */
/*               Copyright Mentor Graphics Corporation 2004              */
/*                         All Rights Reserved.                          */
/*                                                                       */
/* THIS WORK CONTAINS TRADE SECRET AND PROPRIETARY INFORMATION WHICH IS  */
/* THE PROPERTY OF MENTOR GRAPHICS CORPORATION OR ITS LICENSORS AND IS   */
/* SUBJECT TO LICENSE TERMS.                                             */
/*                                                                       */
/*************************************************************************/

/*************************************************************************/
/*                                                                       */
/* FILE NAME                                           VERSION           */
/*                                                                       */
/*      nucleus.h                                   Nucleus PLUS 1.15    */
/*                                                                       */
/* COMPONENT                                                             */
/*                                                                       */
/*      System Constants                                                 */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This file contains system constants common to both the           */
/*      application and the actual Nucleus PLUS components.  This file   */
/*      also contains data structure definitions that hide internal      */
/*      information from the application.                                */
/*                                                                       */
/*                                                                       */
/* DATA STRUCTURES                                                       */
/*                                                                       */
/*      NU_DRIVER                           I/O Driver control block     */
/*      NU_EVENT_GROUP                      Event group control block    */
/*      NU_HISR                             HISR control block           */
/*      NU_MAILBOX                          Mailbox control block        */
/*      NU_MEMORY_POOL                      Memory Pool control block    */
/*      NU_PARTITION_POOL                   Partition Pool control block */
/*      NU_PIPE                             Pipe control block           */
/*      NU_QUEUE                            Queue control block          */
/*      NU_SEMAPHORE                        Semaphore control block      */
/*      NU_TASK                             Task control block           */
/*      NU_TIMER                            Timer control block          */
/*      NU_PROTECT                          Protection structure         */
/*                                                                       */
/* FUNCTIONS                                                             */
/*                                                                       */
/*      None                                                             */
/*                                                                       */
/* DEPENDENCIES                                                          */
/*                                                                       */
/*      None                                                             */
/*                                                                       */
/*************************************************************************/

/* Check to see if this file has been included already.  */







# 65 "\\os\\nu_xscale\\inc\\nucleus.h"












/* Define standard data types.  These definitions allow Nucleus PLUS to
   perform in the same manner on different target platforms.  */

typedef unsigned long           UNSIGNED;
typedef long                    SIGNED;
typedef unsigned char           DATA_ELEMENT;
typedef DATA_ELEMENT            OPTION;
typedef DATA_ELEMENT            BOOLEAN;
typedef int                     STATUS;
typedef unsigned char           UNSIGNED_CHAR;
typedef unsigned int            UNSIGNED_INT;
typedef int                     INT;
typedef unsigned long *         UNSIGNED_PTR;
typedef unsigned char *         BYTE_PTR;


# 106 "\\os\\nu_xscale\\inc\\nucleus.h"


/* Define register defines.  R1, R2, R3, and R4 are used in the Nucleus PLUS
   source code in front of variables that are referenced often.  In some
   ports, defining them as "register" will improve performance.  */






# 1018 "\\os\\nu_xscale\\inc\\nucleus.h"
/* Define Supervisor and User mode functions */


# 1027 "\\os\\nu_xscale\\inc\\nucleus.h"







/* Nucleus PLUS Profiling Support */



















# 803 "/os/osa/inc/osa.h"

// heroengine end // FEATURE_HERO_ENGINE_APP
# 1 "/os/osa/inc/osa_internals.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa_internals.h
Description : Definition of OSA Software Layer data types that are internal in OSA
                and are OS independent.

Notes       :

=========================================================================== */



//extern int fatal_printf(const char * fmt,...);
/*
 * Special OS Definitions for common files.
 */
# 29 "/os/osa/inc/osa_internals.h"

# 36 "/os/osa/inc/osa_internals.h"

/*
 * ASSERT Macro.
 */
# 55 "/os/osa/inc/osa_internals.h"

/*
 * Defines.
 */

/*
 * Macros.
 */
# 75 "/os/osa/inc/osa_internals.h"

# 82 "/os/osa/inc/osa_internals.h"

/*
 * Functions.
 */
void        Osa_Init( void ) ;
OSA_STATUS  OsaMem_InitPools( void ) ;

# 806 "/os/osa/inc/osa.h"





# 827 "/os/osa/inc/osa.h"





# 1 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */








# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"
/* stddef.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.1.4 */

/* Copyright (C) ARM Ltd., 1999
 * All rights reserved
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */

/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991 ARM Limited. All rights reserved.             */
/* version 0.05 */

/*
 * The following types and macros are defined in several headers referred to in
 * the descriptions of the functions declared in that header. They are also
 * defined in this header file.
 */





# 34 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"




  typedef signed int ptrdiff_t;



 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 57 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



  /* unconditional in non-strict C for consistency of debug info */



      typedef unsigned short wchar_t; /* also in <stdlib.h> and <inttypes.h> */
# 82 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



   /* null pointer constant. */




  /* EDG uses __INTADDR__ to avoid errors when strict */




  typedef long double max_align_t;









# 114 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stddef.h"



/* end of stddef.h */

# 13 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 14 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 15 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
/* limits.h: ANSI 'C' (X3J11 Oct 88) library header, section 2.2.4.2 */
/* Copyright (C) Codemist Ltd., 1988                            */
/* Copyright 1991-1997 ARM Limited. All rights reserved         */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */






    /* max number of bits for smallest object that is not a bit-field (byte) */

    /* mimimum value for an object of type signed char */

    /* maximum value for an object of type signed char */

    /* maximum value for an object of type unsigned char */
# 30 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
      /* minimum value for an object of type char */

      /* maximum value for an object of type char */






# 45 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\limits.h"
    /* maximum number of bytes in a multibyte character, */
    /* for any supported locale */


    /* minimum value for an object of type short int */

    /* maximum value for an object of type short int */

    /* maximum value for an object of type unsigned short int */

    /* minimum value for an object of type int */

    /* maximum value for an object of type int */

    /* maximum value for an object of type unsigned int */





    /* minimum value for an object of type long int */





    /* maximum value for an object of type long int */





    /* maximum value for an object of type unsigned long int */


      /* minimum value for an object of type long long int */

      /* maximum value for an object of type long long int */

      /* maximum value for an object of type unsigned long int */




/* end of limits.h */

# 16 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"

# 1 "\\os\\alios\\asr3601\\config\\k_config.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




/* kernel feature conf */
# 36 "\\os\\alios\\asr3601\\config\\k_config.h"
/* kernel task conf */
# 58 "\\os\\alios\\asr3601\\config\\k_config.h"

/* kernel workqueue conf */




/* kernel mm_region conf */




/* kernel timer&tick conf */




/*must reserve enough stack size for timer cb will consume*/
# 81 "\\os\\alios\\asr3601\\config\\k_config.h"




/* kernel dyn alloc conf */




# 98 "\\os\\alios\\asr3601\\config\\k_config.h"

/* kernel idle conf */




/* kernel hook conf */




/* kernel stats conf */





















/* kernel timer&tick conf */




























# 18 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_default_config.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




















/* kernel feature conf */
























# 60 "\\os\\alios\\kernel\\rhino\\include\\k_default_config.h"

























/* heap conf */












































/* kernel task conf */




































/* kernel timer&tick conf */








/* kernel intrpt conf */
/* kernel stack ovf check */








/* kernel dyn alloc conf */
















/* kernel idle conf */




/* kernel hook conf */




/* kernel stats conf */












/* System stack size (for ISR, Fault) */






























# 19 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"

# 1 "\\os\\alios\\kernel\\armv7r\\include\\k_types.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




# 1 "\\os\\alios\\kernel\\armv7r\\include\\k_compiler.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */






/* get the return address of the current function
   unsigned int __return_address(void) */

/* get the  the value of the stack pointer
   unsigned int __current_sp(void) */

/* get the number of leading 0-bits in x
   unsigned char __clz(unsigned int val) */






# 69 "\\os\\alios\\kernel\\armv7r\\include\\k_compiler.h"



# 9 "\\os\\alios\\kernel\\armv7r\\include\\k_types.h"






typedef uint32_t cpu_stack_t;
typedef uint64_t hr_timer_t;
typedef uint64_t lr_timer_t;
typedef uint32_t cpu_cpsr_t;



# 21 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_err.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef enum
{
    RHINO_SUCCESS = 0u,
    RHINO_SYS_FATAL_ERR,
    RHINO_SYS_SP_ERR,
    RHINO_RUNNING,
    RHINO_STOPPED,
    RHINO_INV_PARAM,
    RHINO_NULL_PTR,
    RHINO_INV_ALIGN,
    RHINO_KOBJ_TYPE_ERR,
    RHINO_KOBJ_DEL_ERR,
    RHINO_KOBJ_DOCKER_EXIST,
    RHINO_KOBJ_BLK,
    RHINO_KOBJ_SET_FULL,
    RHINO_NOTIFY_FUNC_EXIST,

    RHINO_MM_POOL_SIZE_ERR = 100u,
    RHINO_MM_ALLOC_SIZE_ERR,
    RHINO_MM_FREE_ADDR_ERR,
    RHINO_MM_CORRUPT_ERR,
    RHINO_DYN_MEM_PROC_ERR,
    RHINO_NO_MEM,
    RHINO_RINGBUF_FULL,
    RHINO_RINGBUF_EMPTY,

    RHINO_SCHED_DISABLE = 200u,
    RHINO_SCHED_ALREADY_ENABLED,
    RHINO_SCHED_LOCK_COUNT_OVF,
    RHINO_INV_SCHED_WAY,

    RHINO_TASK_INV_STACK_SIZE = 300u,
    RHINO_TASK_NOT_SUSPENDED,
    RHINO_TASK_DEL_NOT_ALLOWED,
    RHINO_TASK_SUSPEND_NOT_ALLOWED,
    RHINO_TASK_CANCELED,
    RHINO_SUSPENDED_COUNT_OVF,
    RHINO_BEYOND_MAX_PRI,
    RHINO_PRI_CHG_NOT_ALLOWED,
    RHINO_INV_TASK_STATE,
    RHINO_IDLE_TASK_EXIST,

    RHINO_NO_PEND_WAIT = 400u,
    RHINO_BLK_ABORT,
    RHINO_BLK_TIMEOUT,
    RHINO_BLK_DEL,
    RHINO_BLK_INV_STATE,
    RHINO_BLK_POOL_SIZE_ERR,

    RHINO_TIMER_STATE_INV = 500u,

    RHINO_NO_THIS_EVENT_OPT = 600u,

    RHINO_BUF_QUEUE_INV_SIZE = 700u,
    RHINO_BUF_QUEUE_SIZE_ZERO,
    RHINO_BUF_QUEUE_FULL,
    RHINO_BUF_QUEUE_MSG_SIZE_OVERFLOW,
    RHINO_QUEUE_FULL,
    RHINO_QUEUE_NOT_FULL,

    RHINO_SEM_OVF = 800u,
    RHINO_SEM_TASK_WAITING,

    RHINO_MUTEX_NOT_RELEASED_BY_OWNER = 900u,
    RHINO_MUTEX_OWNER_NESTED,
    RHINO_MUTEX_NESTED_OVF,

    RHINO_NOT_CALLED_BY_INTRPT = 1000u,
    RHINO_TRY_AGAIN,

    RHINO_WORKQUEUE_EXIST = 1100u,
    RHINO_WORKQUEUE_NOT_EXIST,
    RHINO_WORKQUEUE_WORK_EXIST,
    RHINO_WORKQUEUE_BUSY,
    RHINO_WORKQUEUE_WORK_RUNNING,

    RHINO_TASK_STACK_OVF = 1200u,
    RHINO_INTRPT_STACK_OVF,

    RHINO_STATE_ALIGN = 0x7fffffff /* keep enum 4 bytes at 32bit machine */
} kstat_t;

typedef void (*krhino_err_proc_t)(kstat_t err);

extern krhino_err_proc_t g_err_proc;

extern void k_err_proc_debug(kstat_t err, char *file, int line);





# 22 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_sys.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */










typedef enum
{
    RHINO_FALSE = 0u,
    RHINO_TRUE  = 1u
} RHINO_BOOL;

typedef char     name_t;
typedef uint8_t  suspend_nested_t;
typedef uint32_t sem_count_t;
typedef uint32_t mutex_nested_t;
typedef uint64_t sys_time_t;
typedef int64_t  sys_time_i_t;
typedef uint64_t tick_t;
typedef int64_t  tick_i_t;
typedef uint64_t idle_count_t;
typedef uint64_t ctx_switch_t;

# 38 "\\os\\alios\\kernel\\rhino\\include\\k_sys.h"

/**
 * This function will init AliOS
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_init(void);

/**
 * This function will start AliOS
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_start(void);

/**
 * This function will enter interrupt
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_intrpt_enter(void);

/**
 * This function will exit interrupt
 */
void krhino_intrpt_exit(void);

/**
 * This function will check intrpt-stack overflow
 */
void krhino_intrpt_stack_ovf_check(void);

/**
 * This function get the system next sleep ticks
 */
tick_t krhino_next_sleep_ticks_get(void);

/**
 * This function will get the whole ram space used by kernel
 * @return  the whole ram space used by kernel
 */
size_t krhino_global_space_get(void);

/**
 * This function will get kernel version
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
uint32_t krhino_version_get(void);



# 23 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_critical.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




# 15 "\\os\\alios\\kernel\\rhino\\include\\k_critical.h"

# 41 "\\os\\alios\\kernel\\rhino\\include\\k_critical.h"



# 24 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_spin_lock.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef struct {



    int dummy;

} kspinlock_t;

/* Be careful nested spin lock is not supported */
# 48 "\\os\\alios\\kernel\\rhino\\include\\k_spin_lock.h"
/* UP spin lock */


















# 25 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_list.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef struct klist_s {
    struct klist_s *next;
    struct klist_s *prev;
} klist_t;



static __inline void klist_init(klist_t *list_head)
{
    list_head->next = list_head;
    list_head->prev = list_head;
}

static __inline uint8_t is_klist_empty(klist_t *list)
{
    return (list->next == list);
}

static __inline void klist_insert(klist_t *head, klist_t *element)
{
    element->prev = head->prev;
    element->next = head;

    head->prev->next = element;
    head->prev       = element;
}

static __inline void klist_add(klist_t *head, klist_t *element)
{
    element->prev = head;
    element->next = head->next;

    head->next->prev = element;
    head->next = element;
}

static __inline void klist_rm(klist_t *element)
{
    element->prev->next = element->next;
    element->next->prev = element->prev;
}

static __inline void klist_rm_init(klist_t *element)
{
    element->prev->next = element->next;
    element->next->prev = element->prev;

    element->next = element->prev = element;
}



# 26 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_obj.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef enum {
    BLK_POLICY_PRI = 0u,
    BLK_POLICY_FIFO
} blk_policy_t;

typedef enum {
    BLK_FINISH = 0,
    BLK_ABORT,
    BLK_TIMEOUT,
    BLK_DEL,
    BLK_INVALID
} blk_state_t;

typedef enum {
    RHINO_OBJ_TYPE_NONE = 0,
    RHINO_SEM_OBJ_TYPE,
    RHINO_MUTEX_OBJ_TYPE,
    RHINO_QUEUE_OBJ_TYPE,
    RHINO_BUF_QUEUE_OBJ_TYPE,
    RHINO_TIMER_OBJ_TYPE,
    RHINO_EVENT_OBJ_TYPE,
    RHINO_MM_OBJ_TYPE
} kobj_type_t;

typedef struct blk_obj {
    klist_t       blk_list;
    const name_t *name;
    blk_policy_t  blk_policy;
    kobj_type_t   obj_type;




    uint8_t       cancel;


} blk_obj_t;

typedef struct {
    klist_t task_head;
    klist_t mutex_head;


    klist_t sem_head;



    klist_t queue_head;



    klist_t event_head;



    klist_t buf_queue_head;

} kobj_list_t;



# 27 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_sched.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */









typedef struct {
    klist_t  *cur_list_item[256];
    uint32_t  task_bit_map[((256 + 31) / 32)];
    uint16_t  highest_pri;
} runqueue_t;

/**
 * This function will disable schedule
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sched_disable(void);

/**
 * This function will enable schedule
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sched_enable(void);



# 28 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_task.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef enum {
    K_SEED,
    K_RDY,
    K_PEND,
    K_SUSPENDED,
    K_PEND_SUSPENDED,
    K_SLEEP,
    K_SLEEP_SUSPENDED,
    K_DELETED,
} task_stat_t;

typedef void (*task_entry_t)(void *arg);

/* task control information */
typedef struct {
    /* update while task switching
       access by assemble code, so do not change position */
    void            *task_stack;
    /* access by activation, so do not change position */
    const name_t    *task_name;

    /* access by assemble code, so do not change position */
    void            *user_info[4];


# 43 "\\os\\alios\\kernel\\rhino\\include\\k_task.h"

    cpu_stack_t     *task_stack_base;
    uint32_t         stack_size;
    klist_t          task_list;

    suspend_nested_t suspend_count;

    struct mutex_s  *mutex_list;


    klist_t          task_stats_item;


    klist_t          tick_list;
    tick_t           tick_match;
    tick_t           tick_remain;
    klist_t         *tick_head;

    void            *msg;


    size_t           bq_msg_size;


    task_stat_t      task_state;
    blk_state_t      blk_state;

    /* Task block on mutex, queue, semphore, event */
    blk_obj_t       *blk_obj;






    size_t           task_free_stack_size;
    ctx_switch_t     task_ctx_switch_times;
    sys_time_t       task_time_total_run;
    sys_time_t       task_time_total_run_prev;
    lr_timer_t       task_exec_time;
    lr_timer_t       task_time_start;
    hr_timer_t       task_intrpt_disable_time_max;
    hr_timer_t       task_sched_disable_time_max;









    uint32_t         pend_flags;
    void            *pend_info;
    uint8_t          pend_option;






    uint8_t          cpu_num;








    uint8_t          cancel;


    /* current prio */
    uint8_t          prio;
    /* base prio */
    uint8_t          b_prio;
    uint8_t          mm_alloc_flag;


    void            *sys_info[4];

} ktask_t;

/**
 * This function will initialize a task
 * @param[in]  task       the task to be created
 * @param[in]  name       the name of task, which shall be unique
 * @param[in]  arg        the parameter of task enter function
 * @param[in]  pri        the prio of task
 * @param[in]  ticks      the time slice if there are same prio task
 * @param[in]  stack_buf  the start address of task stack
 * @param[in]  stack      the size of thread stack
 * @param[in]  entry      the entry function of task
 * @param[in]  autorun    the autorunning flag of task
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_create(ktask_t *task, const name_t *name, void *arg,
                           uint8_t prio, tick_t ticks, cpu_stack_t *stack_buf,
                           size_t stack_size, task_entry_t entry, uint8_t autorun);

# 153 "\\os\\alios\\kernel\\rhino\\include\\k_task.h"



/**
 * This function will initialize a task
 * @param[in]  task     the task to be created
 * @param[in]  name     the name of task, which shall be unique
 * @param[in]  arg      the parameter of task enter function
 * @param[in]  pri      the prio of task
 * @param[in]  ticks    the time slice if there are same prio task
 * @param[in]  stack    the size of thread stack
 * @param[in]  entry    the entry function of task
 * @param[in]  autorun  the autorunning flag of task
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_dyn_create(ktask_t **task, const name_t *name, void *arg,
                               uint8_t pri, tick_t ticks, size_t stack,
                               task_entry_t entry, uint8_t autorun);

# 178 "\\os\\alios\\kernel\\rhino\\include\\k_task.h"


/**
 * This function will delete a task
 * @param[in]  task  the task to be deleted.
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_del(ktask_t *task);

/**
 * This function will delete a dyn-task
 * @param[in]  task  the task to be deleted.
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_dyn_del(ktask_t *task);


/**
 * This function will cancel a task
 * @param[in]  task  the task to be killed.
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_cancel(ktask_t *task);

/**
 * This function will check the task whether is canceled or not.
 * @param[in]  task  the task to be checked.
 * @return  the operation status, RHINO_TRUE is TRUE, RHINO_FALSE is FALSE
 */
RHINO_BOOL krhino_task_cancel_chk(void);


/**
 * This function will cause a task to sleep for some ticks
 * @param[in]  ticks  the ticks to sleep
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */

kstat_t krhino_task_sleep(tick_t dly);

/**
 * This function will yield a task
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_yield(void);

/**
 * This function will get the current task for this cpu
 * @return the current task
 */
ktask_t *krhino_cur_task_get(void);

/**
 * This function will suspend a task
 * @param[in]  task  the task to be suspended
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_suspend(ktask_t *task);

/**
 * This function will resume a task
 * @param[in]  task  the task to be resumed
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_resume(ktask_t *task);

/**
 * This function will get min free stack size in the total runtime
 * @param[in]  task  the task where get free stack size.
 * @param[in]  free  the free task stack size to be filled with.
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_stack_min_free(ktask_t *task, size_t *free);

/**
 * This function will change the prio of task
 * @param[in]   task     the task to be changed prio
 * @param[in]   pri      the prio to be changed.
 * @param[out]  old_pri  the old task prio to be filled with
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_pri_change(ktask_t *task, uint8_t pri, uint8_t *old_pri);


/**
 * This function will abort a task and wakup the task
 * @param[in]  task  the task to be aborted
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_wait_abort(ktask_t *task);


# 295 "\\os\\alios\\kernel\\rhino\\include\\k_task.h"


/**
 * This function will set task private infomation
 * @param[in]   task  the task to be set private infomation
 * @param[out]  info  the private information
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_info_set(ktask_t *task, size_t idx, void *info);

/**
 * This function will get task private infomation
 * @param[in]   task  the task to be get private infomation
 * @param[out]  info  to save private infomation
 * @return  the task private information
 */
kstat_t krhino_task_info_get(ktask_t *task, size_t idx, void **info);


/**
 * This function will be set in cpu_task_stack_init,set LR reg with
 * this funtion pointer
 */
void  krhino_task_deathbed(void);



# 29 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_ringbuf.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */







typedef struct {
    uint8_t *buf;
    uint8_t *end;
    uint8_t *head;
    uint8_t *tail;
    size_t   freesize;
    size_t   type;
    size_t   blk_size;
} k_ringbuf_t;

static __inline kstat_t ringbuf_queue_push(k_ringbuf_t *p_ringbuf, void *data, size_t len)
{
    if (p_ringbuf->tail == p_ringbuf->end) {
        p_ringbuf->tail = p_ringbuf->buf;
    }

    memcpy(p_ringbuf->tail, data, p_ringbuf->blk_size);
    p_ringbuf->tail += p_ringbuf->blk_size;

    return RHINO_SUCCESS;
}

static __inline kstat_t ringbuf_queue_pop(k_ringbuf_t *p_ringbuf, void *pdata, size_t *plen)
{
    if (p_ringbuf->head == p_ringbuf->end) {
        p_ringbuf->head = p_ringbuf->buf;
    }

    memcpy(pdata, p_ringbuf->head, p_ringbuf->blk_size);
    p_ringbuf->head += p_ringbuf->blk_size;

    if (plen != 0) {
        *plen = p_ringbuf->blk_size;
    }

    return RHINO_SUCCESS;
}



# 30 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_queue.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */







typedef struct {
    void   **queue_start;
    size_t   size;
    size_t   cur_num;
    size_t   peak_num;
} msg_q_t;

typedef struct {
    msg_q_t  msg_q;
    klist_t *pend_entry;
} msg_info_t;

typedef struct queue_s {
    blk_obj_t   blk_obj;
    k_ringbuf_t ringbuf;
    msg_q_t     msg_q;

    klist_t     queue_item;

    uint8_t     mm_alloc_flag;
} kqueue_t;

/**
 * This function will create a queue
 * @param[in]  queue    pointer to the queue(the space is provided by user)
 * @param[in]  name     name of the queue
 * @param[in]  start    start address of the queue internal space
 * @param[in]  msg_num  num of the msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_create(kqueue_t *queue, const name_t *name, void **start, size_t msg_num);

/**
 * This function will delete a queue
 * @param[in]  queue  pointer to the queue
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_del(kqueue_t *queue);


/**
 * This function will create a dyn queue
 * @param[in]  queue    pointer to the queue
 * @param[in]  name     name of the queue
 * @param[in]  msg_num  num of the msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_dyn_create(kqueue_t **queue, const name_t *name, size_t msg_num);

/**
 * This function will delete a dyn created queue
 * @param[in]  queue  pointer to the queue
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_dyn_del(kqueue_t *queue);


/**
 * This function will send a msg to the back of a queue
 * @param[in]  queue  pointer to the queue
 * @param[in]  msg    msg to send
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_back_send(kqueue_t *queue, void *msg);

/**
 * This function will send a msg to a queue and wake all tasks
 * @param[in]  queue  pointer to the queue
 * @param[in]  msg    msg to send
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_all_send(kqueue_t *queue, void *msg);

/**
 * This function will receive msg from a queue
 * @param[in]   queue  pointer to the queue
 * @param[in]   ticks  ticks to wait before receive
 * @param[out]  msg    buf to save msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_recv(kqueue_t *queue, tick_t ticks, void **msg);

/**
 * This function will detect a queue full or not
 * @param[in]  queue  pointer to the queue
 * @return  the operation status, RHINO_QUEUE_FULL/RHINO_QUEUE_NOT_FULL
 */
kstat_t krhino_queue_is_full(kqueue_t *queue);

/**
 * This function will reset a queue
 * @param[in]  queue  pointer to the queue
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_flush(kqueue_t *queue);

/**
 * This function will get information of a queue
 * @param[in]   queue  pointer to the queue
 * @param[out]  info   buf to save msg-info
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_queue_info_get(kqueue_t *queue, msg_info_t *info);



# 31 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_buf_queue.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef struct {
    blk_obj_t    blk_obj;
    void        *buf;
    k_ringbuf_t  ringbuf;
    size_t       max_msg_size;
    size_t       cur_num;
    size_t       peak_num;
    size_t       min_free_buf_size;

    klist_t      buf_queue_item;




    uint8_t      mm_alloc_flag;
} kbuf_queue_t;

typedef struct {
    size_t buf_size;
    size_t max_msg_size;
    size_t cur_num;
    size_t peak_num;
    size_t free_buf_size;
    size_t min_free_buf_size;
} kbuf_queue_info_t;

/**
 * This function will create a buf-queue
 * @param[in]  queue    pointer to the queue(the space is provided by user)
 * @param[in]  name     name of the queue
 * @param[in]  buf      pointer to the buf
 * @param[in]  size     size of the buf
 * @param[in]  max_msg  max size of one msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_create(kbuf_queue_t *queue, const name_t *name,
                                void *buf, size_t size, size_t max_msg);

/**
 * This function will create a fix buf-queue
 * @param[in]  queue     pointer to the queue(the space is provided by user)
 * @param[in]  name      name of the queue
 * @param[in]  buf       pointer to the buf
 * @param[in]  msg_size  size of the msg
 * @param[in]  msg_num   number of msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_fix_buf_queue_create(kbuf_queue_t *queue, const name_t *name,
                                    void *buf, size_t msg_size, size_t msg_num);

/**
 * This function will delete a queue
 * @param[in]  queue  pointer to the queue
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_del(kbuf_queue_t *queue);


/**
 * This function will create a dyn-queue
 * @param[out]  queue    pointer to the queue(The space is provided by kernel)
 * @param[in]   name     pointer to the nam
 * @param[in]   size     size of the buf
 * @param[in]   max_msg  max size of one msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_dyn_create(kbuf_queue_t **queue, const name_t *name,
                                    size_t size, size_t max_msg);

/**
 * This function will create a dyn fix buf-queue
 * @param[in]  queue     pointer to the queue(the space is provided by user)
 * @param[in]  name      name of the queue
 * @param[in]  buf       pointer to the buf
 * @param[in]  msg_size  size of the msg
 * @param[in]  msg_num   number of msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_fix_buf_queue_dyn_create(kbuf_queue_t **queue, const name_t *name,
                                        size_t msg_size, size_t msg_num);

/**
 * This function will delete a dyn-queue
 * @param[in]  queue  pointer to the queue
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_dyn_del(kbuf_queue_t *queue);


/**
 * This function will send a msg at the end of queue
 * @param[in]  queue  pointer to the queue
 * @param[in]  msg    pointer to msg to be send
 * @param[in]  size   size of the msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_send(kbuf_queue_t *queue, void *msg, size_t size);


/**
 * This function will receive msg form aqueue
 * @param[in]   queue  pointer to the queue
 * @param[in]   ticks  ticks to wait before receiving msg
 * @param[out]  msg    pointer to the buf to save msg
 * @param[out]  size   size of received msg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_recv(kbuf_queue_t *queue, tick_t ticks, void *msg, size_t *size);

/**
 * This function will reset queue
 * @param[in]  queue  pointer to the queue
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_flush(kbuf_queue_t *queue);

/**
 * This function will get information of a queue
 * @param[in]   queue  pointer to the queue
 * @param[out]  free   free size of the queue buf
 * @param[out]  total  total size of the queue buf
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_buf_queue_info_get(kbuf_queue_t *queue, kbuf_queue_info_t *info);



# 32 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_sem.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */







typedef struct sem_s {
    blk_obj_t   blk_obj;
    sem_count_t count;
    sem_count_t peak_count;

    klist_t     sem_item;

    uint8_t mm_alloc_flag;
} ksem_t;

/**
 * This function will create a semaphore
 * @param[in]  sem    pointer to the semaphore(the space is provided by user)
 * @param[in]  name   name of the semaphore
 * @param[in]  count  the init count of the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_create(ksem_t *sem, const name_t *name, sem_count_t count);

/**
 * This function will delete a semaphore
 * @param[in]  sem  pointer to the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_del(ksem_t *sem);


/**
 * This function will create a dyn-semaphore
 * @param[out]  sem    pointer to the semaphore(the space is provided by kernel)
 * @param[in]   name   name of the semaphore
 * @param[in]   count  the init count of the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_dyn_create(ksem_t **sem, const name_t *name, sem_count_t count);

/**
 * This function will delete a dyn-semaphore
 * @param[in]  sem  pointer to the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_dyn_del(ksem_t *sem);


/**
 * This function will give a semaphore
 * @param[in]  sem  pointer to the semphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_give(ksem_t *sem);

/**
 * This function will give a semaphore and wakeup all thee waiting task
 * @param[in]  sem  pointer to the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_give_all(ksem_t *sem);

/**
 * This function will take a semaphore
 * @param[in]  sem    pointer to the semaphore
 * @param[in]  ticks  ticks to wait before take
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_take(ksem_t *sem, tick_t ticks);

/**
 * This function will set the count of a semaphore
 * @param[in]  sem        pointer to the semaphore
 * @param[in]  sem_count  count of the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_count_set(ksem_t *sem, sem_count_t  count);

/**
 * This function will get count of a semaphore
 * @param[in]   sem    pointer to the semaphore
 * @param[out]  count  count of the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_sem_count_get(ksem_t *sem, sem_count_t *count);



# 33 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_task_sem.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




/**
 * This function will create a task-semaphore
 * @param[in]  task   pointer to the task
 * @param[in]  sem    pointer to the semaphore
 * @param[in]  name   name of the task-semaphore
 * @param[in]  count  count of the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_sem_create(ktask_t *task, ksem_t *sem, const name_t *name, size_t count);

/**
 * This function will delete a task-semaphore
 * @param[in]  task  pointer to the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_sem_del(ktask_t *task);

/**
 * This function will give up a task-semaphore
 * @param[in]  task  pointer to the task
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_sem_give(ktask_t *task);

/**
 * This function will take a task-semaphore
 * @param[in]  ticks  ticks to wait before take the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_sem_take(tick_t ticks);

/**
 * This function will set the count of a task-semaphore
 * @param[in]  task   pointer to the task
 * @param[in]  count  count of the semaphre to set
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_sem_count_set(ktask_t *task, sem_count_t count);

/**
 * This function will get task-semaphore count
 * @param[in]   task   pointer to the semphore
 * @param[out]  count  count of the semaphore
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_task_sem_count_get(ktask_t *task, sem_count_t *count);



# 34 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_mutex.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef struct mutex_s {
    blk_obj_t       blk_obj;
    ktask_t        *mutex_task; /* mutex owner task */
    struct mutex_s *mutex_list; /* task mutex list */
    mutex_nested_t  owner_nested;


    klist_t         mutex_item;


    uint8_t         mm_alloc_flag;
} kmutex_t;

/**
 * This function will create a mutex
 * @param[in] mutex  pointer to the mutex(the space is provided by user)
 * @param[in] name   name of the mutex
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mutex_create(kmutex_t *mutex, const name_t *name);

/**
 * This function will delete a mutex
 * @param[in] mutex pointer to the mutex
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mutex_del(kmutex_t *mutex);


/**
 * This function will create a dyn mutex
 * @param[in]  mutex  pointer to the mutex(the space is provided by user)
 * @param[in]  name   name of the mutex
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mutex_dyn_create(kmutex_t **mutex, const name_t *name);

/**
 * This function will delete a dyn mutex
 * @param[in] mutex  pointer to the mutex
 * @return the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mutex_dyn_del(kmutex_t *mutex);


/**
 * This function will lock mutex
 * @param[in]  mutex  pointer to the mutex
 * @param[in]  ticks  ticks to be wait for before lock
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mutex_lock(kmutex_t *mutex, tick_t ticks);

/**
 * This function will unlock a mutex
 * @param[in]  mutex  pointer to the mutex
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mutex_unlock(kmutex_t *mutex);



# 35 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_timer.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




enum {
    TIMER_CMD_CB = 0u,
    TIMER_CMD_START,
    TIMER_CMD_STOP,
    TIMER_CMD_CHG,
    TIMER_ARG_CHG,
    TIMER_ARG_CHG_AUTO,
    TIMER_CMD_DEL,
    TIMER_CMD_DYN_DEL
};

typedef void (*timer_cb_t)(void *timer, void *arg);

typedef struct {
    klist_t       timer_list;
    klist_t      *to_head;
    const name_t *name;
    timer_cb_t    cb;
    void         *timer_cb_arg;
    sys_time_t    match;
    sys_time_t    remain;
    sys_time_t    init_count;
    sys_time_t    round_ticks;
    void         *priv;
    kobj_type_t   obj_type;
    uint8_t       timer_state;
    uint8_t       mm_alloc_flag;
} ktimer_t;

typedef struct {
    ktimer_t   *timer;
    uint8_t     cb_num;
    sys_time_t  first;

    union {
        sys_time_t  round;
        void       *arg;
    } u;
} k_timer_queue_cb;

typedef enum {
    TIMER_DEACTIVE = 0u,
    TIMER_ACTIVE
} k_timer_state_t;

/**
 * This function will create a timer
 * @param[in]  timer     pointer to the timer(the space is provided by user)
 * @param[in]  name      name of the timer
 * @param[in]  cb        callbak of the timer
 * @param[in]  first     ticks of the first timer triger
 * @param[in]  round     ticks of the normal timer triger
 * @param[in]  arg       the argument of the callback
 * @param[in]  auto_run  auto run or not when the timer is created
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_create(ktimer_t *timer, const name_t *name, timer_cb_t cb,
                            sys_time_t first, sys_time_t round, void *arg, uint8_t auto_run);

/**
 * This function will delete a timer
 * @param[in]  timer  pointer to a timer
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_del(ktimer_t *timer);


/**
 * This function will create a dyn-timer
 * @param[in]  timer     pointer to the timer
 * @param[in]  name      name of the timer
 * @param[in]  cb        callbak of the timer
 * @param[in]  first     ticks of the first timer triger
 * @param[in]  round     ticks of the normal timer triger
 * @param[in]  arg       the argument of the callback
 * @param[in]  auto_run  auto run or not when the timer is created
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_dyn_create(ktimer_t **timer, const name_t *name, timer_cb_t cb,
                                sys_time_t first, sys_time_t round, void *arg, uint8_t auto_run);
/**
 * This function will delete a dyn-timer
 * @param[in]  timer  pointer to a timer
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_dyn_del(ktimer_t *timer);


/**
 * This function will start a timer
 * @param[in]  timer  pointer to the timer
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_start(ktimer_t *timer);

/**
 * This function will stop a timer
 * @param[in]  timer  pointer to the timer
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_stop(ktimer_t *timer);

/**
 * This function will change attributes of a timer
 * @param[in]  timer  pointer to the timer
 * @param[in]  first  ticks of the first timer triger
 * @param[in]  round  ticks of the normal timer triger
 *
 * @note change the timer attributes should follow
 *       the sequence as timer_stop->timer_change->timer_start
 *
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_change(ktimer_t *timer, sys_time_t first, sys_time_t round);

/**
 * This function will change attributes of a timer without stop and start
 * @param[in]  timer  pointer to the timer
 * @param[in]  first  ticks of the first timer triger
 * @param[in]  round  ticks of the normal timer triger
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_arg_change_auto(ktimer_t *timer, void *arg);

/**
 * This function will change callback arg attributes of a timer
 * @param[in]  timer  pointer to the timer
 * @param[in]  arg timer callback arg
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_timer_arg_change(ktimer_t *timer, void *arg);



# 36 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_time.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




/**
 * This function will handle systick routine
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
void krhino_tick_proc(void);

/**
 * This function will get time of the system in ms
 * @return  system time
 */
sys_time_t krhino_sys_time_get(void);

/**
 * This function will get ticks of the system
 * @return  the system ticks
 */
sys_time_t krhino_sys_tick_get(void);

/**
 * This function will convert ms to ticks
 * @param[in]  ms  ms which will be converted to ticks
 * @return  the ticks of the ms
 */
tick_t krhino_ms_to_ticks(sys_time_t ms);

/**
 * This function will convert ticks to ms
 * @param[in]  ticks  ticks which will be converted to ms
 * @return  the ms of the ticks
 */
sys_time_t krhino_ticks_to_ms(tick_t ticks);



# 37 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_event.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef struct {
    blk_obj_t blk_obj;
    uint32_t  flags;


    klist_t   event_item;


    uint8_t   mm_alloc_flag;
} kevent_t;









/**
 * This function will create a event
 * @param[in]  event  pointer to the event
 * @param[in]  name   name of the event
 * @param[in]  flags  flags to be init
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_event_create(kevent_t *event, const name_t *name, uint32_t flags);

/**
 * This function will delete a event
 * @param[in]  event  pointer to a event
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_event_del(kevent_t *event);


/**
 * This function will create a dyn-event
 * @param[out]  event  pointer to the event
 * @param[in]   name   name of the semaphore
 * @param[in]   flags  flags to be init
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_event_dyn_create(kevent_t **event, const name_t *name, uint32_t flags);

/**
 * This function will delete a dyn created event
 * @param[in]  event  pointer to a event
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_event_dyn_del(kevent_t *event);


/**
 * This function will get event
 * @param[in]   event       pointer to the event
 * @param[in]   flags       which is provided by users
 * @param[in]   opt         could be RHINO_AND, RHINO_AND_CLEAR, RHINO_OR, RHINO_OR_CLEAR
 * @param[out]  actl_flags  the actually flag where flags is satisfied
 * @param[in]   ticks       ticks to wait
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_event_get(kevent_t *event, uint32_t flags, uint8_t opt,
                         uint32_t *actl_flags, tick_t ticks);

/**
 * This function will set a event
 * @param[in]  event  pointer to a event
 * @param[in]  flags  which users want to be set
 * @param[in]  opt    could be RHINO_AND, RHINO_OR
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_event_set(kevent_t *event, uint32_t flags, uint8_t opt);



# 38 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_stats.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */





void kobj_list_init(void);



/**
 * This function will check task stack overflow
 */
void krhino_stack_ovf_check(void);



/**
 * This function will reset task schedule stats
 */
void krhino_task_sched_stats_reset(void);

/**
 * This function will get task statistic data
 */
void krhino_task_sched_stats_get(void);



void krhino_overhead_measure(void);




# 39 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_mm_debug.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




# 1 "\\os\\alios\\kernel\\rhino\\include\\k_mm.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




/* use two level bit map to find free memory block */











/* mm bitmask freelist: */












/* bit 0 */



/* bit 1 */










# 69 "\\os\\alios\\kernel\\rhino\\include\\k_mm.h"

/* struct of memory list ,every memory block include this information */
typedef struct free_ptr_struct {
    struct k_mm_list_struct *prev;
    struct k_mm_list_struct *next;
} free_ptr_t;

typedef struct k_mm_list_struct {




    struct k_mm_list_struct *prev;
    /* bit 0 indicates whether the block is used and */
    /* bit 1 allows to know whether the previous block is free */
    size_t buf_size;
    union {
        struct free_ptr_struct free_ptr;
        uint8_t                buffer[1];
    } mbinfo;
} k_mm_list_t;

typedef struct k_mm_region_info_struct {
    k_mm_list_t                    *end;
    struct k_mm_region_info_struct *next;
} k_mm_region_info_t;

typedef struct {



    kspinlock_t mm_lock;


    k_mm_region_info_t *regioninfo;


    void *fix_pool;



    size_t used_size;
    size_t maxused_size;
    size_t free_size;
    size_t alloc_times[(20 - 6 + 2)]; /* number of times for each TLF level */

    /* msb (MM_BIT_LEVEL-1) <-> lsb 0, one bit match one freelist */
    uint32_t free_bitmap;
    /* freelist[N]: contain free blks at level N,
     * 2^(N + MM_MIN_BIT) <= level N buffer size < 2^(1 + N + MM_MIN_BIT)
     */
    k_mm_list_t *freelist[(20 - 6 + 2)];
} k_mm_head;

kstat_t krhino_init_mm_head(k_mm_head **ppmmhead, void *addr, size_t len);
kstat_t krhino_deinit_mm_head(k_mm_head *mmhead);
kstat_t krhino_add_mm_region(k_mm_head *mmhead, void *addr, size_t len);

void *k_mm_alloc(k_mm_head *mmhead, size_t size);
void  k_mm_free(k_mm_head *mmhead, void *ptr);
void *k_mm_realloc(k_mm_head *mmhead, void *oldmem, size_t new_size);

/*
 * This function is wrapper of mm allocation
 * @param[in]  size  size of the mem to malloc
 * @return  the operation status, NULL is error, others is memory address
 */
void *krhino_mm_alloc(size_t size);

/*
 * This function is wrapper of mm free
 * @param[in]  ptr  address point of the mem
 */
void krhino_mm_free(void *ptr);

/*
 * This function is wrapper of mm rallocation
 * @param[in]  oldmem  oldmem address
 * @param[in]  size    size of the mem to malloc
 * @return  the operation status, NULL is error, others is realloced memory address
 */
void *krhino_mm_realloc(void *oldmem, size_t newsize);

/*
 * This function return max free block size
 * @param[in]  NULL
 * @return  the max free block size
 */
size_t krhino_mm_max_free_size_get(void);

# 168 "\\os\\alios\\kernel\\rhino\\include\\k_mm.h"



# 9 "\\os\\alios\\kernel\\rhino\\include\\k_mm_debug.h"

# 31 "\\os\\alios\\kernel\\rhino\\include\\k_mm_debug.h"



# 40 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_mm_blk.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef struct {
    const name_t *pool_name;
    void         *pool_end;   /* end address */
    void         *pool_start; /* start address */
    size_t        blk_size;
    size_t        blk_avail;  /* num of available(free) blk */
    size_t        blk_whole;  /* num of all blk */
    uint8_t      *avail_list;
    kspinlock_t   blk_lock;

    klist_t       mblkpool_stats_item;

} mblk_pool_t;

/**
 * This function will init a blk-pool
 * @param[in]  pool        pointer to the pool
 * @param[in]  name        name of the pool
 * @param[in]  pool_start  start addr of the pool
 * @param[in]  blk_size    size of the blk
 * @param[in]  pool_size   size of the pool
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mblk_pool_init(mblk_pool_t *pool, const name_t *name,
                              void *pool_start, size_t blk_size, size_t pool_size);

/**
 * This function will alloc a blk-pool
 * @param[in]  pool  pointer to a pool
 * @param[in]  blk   pointer to a blk
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mblk_alloc(mblk_pool_t *pool, void **blk);

/**
 * This function will free a blk-pool
 * @param[in]  pool  pointer to the pool
 * @param[in]  blk   pointer to the blk
 * @return  the operation status, RHINO_SUCCESS is OK, others is error
 */
kstat_t krhino_mblk_free(mblk_pool_t *pool, void *blk);

/**
 * is blk in pool?
 * @param[in]  pool  pointer to the pool
 * @param[in]  blk   pointer to the blk
 * @return  yes return 1, no reture 0
 */







# 41 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_mm_region.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




typedef struct {
    uint8_t *start;
    size_t   len;
} k_mm_region_t;



# 42 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 43 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_workqueue.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




# 76 "\\os\\alios\\kernel\\rhino\\include\\k_workqueue.h"



# 44 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_internal.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




extern kstat_t g_sys_stat;
extern uint8_t g_idle_task_spawned[1];

extern runqueue_t g_ready_queue;

/* System lock */
extern uint8_t g_sched_lock[1];
extern uint8_t g_intrpt_nested_level[1];

/* highest pri ready task object */
extern ktask_t *g_preferred_ready_task[1];

/* current active task */
extern ktask_t *g_active_task[1];

/* idle attribute */
extern ktask_t      g_idle_task[1];
extern idle_count_t g_idle_count[1];
extern cpu_stack_t  g_idle_task_stack[1][512];

/* tick attribute */
extern tick_t  g_tick_count;
extern klist_t g_tick_head;


extern kobj_list_t g_kobj_list;



extern klist_t          g_timer_head;
extern sys_time_t       g_timer_count;
extern ktask_t          g_timer_task;
extern cpu_stack_t      g_timer_task_stack[512];
extern kbuf_queue_t     g_timer_queue;
extern k_timer_queue_cb timer_queue_cb[20];



extern hr_timer_t g_sched_disable_time_start;
extern hr_timer_t g_sched_disable_max_time;
extern hr_timer_t g_cur_sched_disable_max_time;
extern uint16_t   g_intrpt_disable_times;
extern hr_timer_t g_intrpt_disable_time_start;
extern hr_timer_t g_intrpt_disable_max_time;
extern hr_timer_t g_cur_intrpt_disable_max_time;
extern ctx_switch_t g_sys_ctx_switch_times;



extern hr_timer_t g_sys_measure_waste;



extern ksem_t      g_res_sem;
extern klist_t     g_res_list;
extern ktask_t     g_dyn_task;
extern cpu_stack_t g_dyn_task_stack[256];


# 73 "\\os\\alios\\kernel\\rhino\\include\\k_internal.h"


extern k_mm_head *g_kmm_head;










# 92 "\\os\\alios\\kernel\\rhino\\include\\k_internal.h"

# 100 "\\os\\alios\\kernel\\rhino\\include\\k_internal.h"


# 113 "\\os\\alios\\kernel\\rhino\\include\\k_internal.h"



typedef struct
{
    size_t   cnt;
    void    *res[4];
    klist_t  res_list;
} res_free_t;

ktask_t *preferred_cpu_ready_task_get(runqueue_t *rq, uint8_t cpu_num);

void core_sched(void);
void runqueue_init(runqueue_t *rq);

void ready_list_add(runqueue_t *rq, ktask_t *task);
void ready_list_add_head(runqueue_t *rq, ktask_t *task);
void ready_list_add_tail(runqueue_t *rq, ktask_t *task);
void ready_list_rm(runqueue_t *rq, ktask_t *task);
void ready_list_head_to_tail(runqueue_t *rq, ktask_t *task);

void time_slice_update(void);
void timer_task_sched(void);

void pend_list_reorder(ktask_t *task);
void pend_task_wakeup(ktask_t *task);
void pend_to_blk_obj(blk_obj_t *blk_obj, ktask_t *task, tick_t timeout);
void pend_task_rm(ktask_t *task);

kstat_t pend_state_end_proc(ktask_t *task, blk_obj_t *blk_obj);

void idle_task(void *p_arg);

void tick_list_init(void);
void tick_task_start(void);
void tick_list_rm(ktask_t *task);
void tick_list_insert(ktask_t *task, tick_t time);
void tick_list_update(tick_i_t ticks);

uint8_t mutex_pri_limit(ktask_t *tcb, uint8_t pri);
void    mutex_task_pri_reset(ktask_t *tcb);
uint8_t mutex_pri_look(ktask_t *tcb, kmutex_t *mutex_rel);

kstat_t task_pri_change(ktask_t *task, uint8_t new_pri);

void ktimer_init(void);

void intrpt_disable_measure_start(void);
void intrpt_disable_measure_stop(void);
void dyn_mem_proc_task_start(void);

kstat_t ringbuf_init(k_ringbuf_t *p_ringbuf, void *buf, size_t len, size_t type,size_t block_size);
kstat_t ringbuf_reset(k_ringbuf_t *p_ringbuf);
kstat_t ringbuf_push(k_ringbuf_t *p_ringbuf, void *data, size_t len);
kstat_t ringbuf_head_push(k_ringbuf_t *p_ringbuf, void *data, size_t len);
kstat_t ringbuf_pop(k_ringbuf_t *p_ringbuf, void *pdata, size_t *plen);
uint8_t ringbuf_is_full(k_ringbuf_t *p_ringbuf);
uint8_t ringbuf_is_empty(k_ringbuf_t *p_ringbuf);
void    workqueue_init(void);
void    k_mm_init(void);







void debug_fatal_error(kstat_t err, char *file, int line);




# 45 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_trace.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */





/* task trace */
# 20 "\\os\\alios\\kernel\\rhino\\include\\k_trace.h"

/* semaphore trace */
# 29 "\\os\\alios\\kernel\\rhino\\include\\k_trace.h"

/* mutex trace */
# 39 "\\os\\alios\\kernel\\rhino\\include\\k_trace.h"

/* event trace */






/* buf_queue trace */






/* timer trace */



/* MBLK trace */


/* MM trace */


/* MM region trace*/


/* work queue trace */






# 46 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_soc.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */





void       soc_hw_timer_init(void);
hr_timer_t soc_hr_hw_cnt_get(void);
lr_timer_t soc_lr_hw_cnt_get(void);
# 18 "\\os\\alios\\kernel\\rhino\\include\\k_soc.h"





void soc_err_proc(kstat_t err);

size_t soc_get_cur_sp(void);



# 47 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_hook.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */





/**
 * This function will provide init hook
 */
void krhino_init_hook(void);

/**
 * This function will provide system start hook
 */
void krhino_start_hook(void);

/**
 * This function will provide task create hook
 * @param[in]  task  pointer to the task
 */
void krhino_task_create_hook(ktask_t *task);

/**
 * This function will provide task delete hook
 * @param[in]  task  pointer to the task
 */
void krhino_task_del_hook(ktask_t *task, res_free_t *arg);

/**
 * This function will provide task abort hook
 * @param[in]  task  pointer to the task
 */
void krhino_task_abort_hook(ktask_t *task);

/**
 * This function will provide task switch hook
 */
void krhino_task_switch_hook(ktask_t *orgin, ktask_t *dest);

/**
 * This function will provide system tick hook
 */
void krhino_tick_hook(void);

/**
 * This function will provide idle hook
 */
void krhino_idle_hook(void);

/**
 * This function will provide idle pre hook
 */
void krhino_idle_pre_hook(void);

/**
 * This function will provide  krhino_mm_alloc hook
 */
void krhino_mm_alloc_hook(void *mem, size_t size);




# 48 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"
# 1 "\\os\\alios\\kernel\\rhino\\include\\k_bitmap.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */











/**
 ** This MACRO will declare a bitmap
 ** @param[in]  name  the name of the bitmap to declare
 ** @param[in]  bits  the bits of the bitmap
 ** @return  no return
 **/


/**
 ** This function will set a bit of the bitmap
 ** @param[in]  bitmap  pointer to the bitmap
 ** @param[in]  nr      position of the bitmap to set
 ** @return  no return
 **/
static __inline void krhino_bitmap_set(uint32_t *bitmap, int32_t nr)
{
    bitmap[((nr) >> 5U)] |= (1UL << (32U - 1U - ((nr) & 0X0000001F)));
}

/**
 ** This function will clear a bit of the bitmap
 ** @param[in]  bitmap  pointer to the bitmap
 ** @param[in]  nr      position of the bitmap to clear
 ** @return  no return
 **/
static __inline void krhino_bitmap_clear(uint32_t *bitmap, int32_t nr)
{
    bitmap[((nr) >> 5U)] &= ~(1UL << (32U - 1U - ((nr) & 0X0000001F)));
}

/* Count Leading Zeros (clz)
   counts the number of zero bits preceding the most significant one bit. */
static __inline uint8_t krhino_clz32(uint32_t x)
{
    uint8_t n = 0;

    if (x == 0) {
        return 32;
    }


    n += __clz(x);
# 78 "\\os\\alios\\kernel\\rhino\\include\\k_bitmap.h"

    return n;
}

/* Count Trailing Zeros (ctz)
   counts the number of zero bits succeeding the least significant one bit. */
static __inline uint8_t krhino_ctz32(uint32_t x)
{
    uint8_t n = 0;

    if (x == 0) {
        return 32;
    }

    if ((x & 0X0000FFFF) == 0) {
        x >>= 16;
        n += 16;
    }
    if ((x & 0X000000FF) == 0) {
        x >>= 8;
        n += 8;
    }
    if ((x & 0X0000000F) == 0) {
        x >>= 4;
        n += 4;
    }
    if ((x & 0X00000003) == 0) {
        x >>= 2;
        n += 2;
    }
    if ((x & 0X00000001) == 0) {
        n += 1;
    }

    return n;
}


/**
 ** This function will find the first bit(1) of the bitmap
 ** @param[in]  bitmap  pointer to the bitmap
 ** @return  the first bit position
 **/
static __inline int32_t krhino_find_first_bit(uint32_t *bitmap)
{
    int32_t  nr  = 0;
    uint32_t tmp = 0;

    while (*bitmap == 0UL) {
        nr += 32U;
        bitmap++;
    }

    tmp = *bitmap;

    nr += __clz(tmp);
# 159 "\\os\\alios\\kernel\\rhino\\include\\k_bitmap.h"

    return nr;
}



# 49 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"

# 1 "\\os\\alios\\kernel\\armv7r\\include\\port.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */




# 9 "\\os\\alios\\kernel\\armv7r\\include\\port.h"
# 1 "\\os\\alios\\kernel\\armv7r\\include\\k_vector.h"



void *k_vectable_get(void);
void k_vectable_set(void);

# 10 "\\os\\alios\\kernel\\armv7r\\include\\port.h"
# 1 "\\os\\alios\\kernel\\armv7r\\include\\k_cache.h"



void k_icache_enable(void);
void k_icache_disable(void);
void k_dcache_invalidate(uintptr_t text, uintptr_t size);
void k_icache_invalidate_all(void);

void k_dcache_enable(void);
void k_dcache_disable(void);
void k_dcache_clean(uintptr_t buffer, uintptr_t size);
void k_dcache_invalidate(uintptr_t buffer, uintptr_t size);
void k_dcache_clean_invalidate(uintptr_t buffer, uintptr_t size);

void k_dcache_clean_all(void);
void k_dcache_invalidate_all(void);
void k_dcache_clean_invalidate_all(void);

# 11 "\\os\\alios\\kernel\\armv7r\\include\\port.h"
# 1 "\\os\\alios\\kernel\\armv7r\\include\\k_mmu.h"
/*
 * Copyright (C) 2015-2017 Alibaba Group Holding Limited
 */



/************************* MMU configurations *************************/
/* Where to put mmu page tables
   It is recommended to place "mmu_tbl" in the place as the vaddr same with paddr */


/* Whether the secondary page table is supported */





/* number of level2 page tables, 1 table takes 1KB ram, cover 1MB memory */


/*********************************************************************/







/******************** mmu_mem_attr_t for app: ***********************/
typedef enum {
    /********** mem_attr for MMIO (e.g. regs, dma ...) **********/
    /* Strongly-ordered MMIO
       Non-Executable + Full access + Non-cacheable */
    MMU_ATTR_SO,
    /* Shareable Device MMIO
       Non-Executable + Full access + Non-cacheable */
    MMU_ATTR_DEV,
    /* Non-Shareable Device MMIO
       Non-Executable + Full access + Non-cacheable */
    MMU_ATTR_DEV_NSH,
    /* Non-cacheable Memory (DMA)
       Non-Executable + Full access + Non-cacheable */
    MMU_ATTR_NON_CACHE,

    /********** mem_attr for Normal memory (e.g. ddr, sram ...) **********/
    /* Normal memory for text / rodata
       Executable + Read-only + Cache write back */
    MMU_ATTR_EXEC,
    /* Normal memory for data / bss
       Non-Executable + Read-Write + Cache write back */
    MMU_ATTR_DATA,
    /* Normal memory for read-only data
       Non-Executable + Read-only + Cache write back */
    MMU_ATTR_DATA_RO,
    /* Normal memory for all (mixing text and data)
       Executable + Read-Write + Cache write back */
    MMU_ATTR_EXEC_DATA,

    /********** mem_attr for Normal memory in multicore system  **********/
    /* in multicore system, when ACTLR.SMP == 1,
       mem_attr should be with "shareable", for Cache coherency */
    /* Normal memory for text / rodata, shareable
       Executable + Read-only + Cache write back */
    MMU_ATTR_EXEC_SH,
    /* Normal memory for data / bss, shareable
       Non-Executable + Read-Write + Cache write back */
    MMU_ATTR_DATA_SH,
    /* Normal memory for read-only data, shareable
       Non-Executable + Read-only + Cache write back */
    MMU_ATTR_DATA_RO_SH,
    /* Normal memory for all (mixing text and data), shareable
       Executable + Read-Write + Cache write back */
    MMU_ATTR_EXEC_DATA_SH,

    /* memory for More combinations, add below */

    MMU_ATTR_BUTT
} mmu_mem_attr_t;

typedef void os_mmu_func_t(uintptr_t vaddr, uintptr_t paddr, size_t len, int32_t isKenrel);

uintptr_t k_mmu_vir2phy(uintptr_t vaddr);

void k_mmu_disable(void);
void k_mmu_enable(void);

/* MMU Map the "vaddr" to "paddr" with mem_attr,

when K_CONFIG_MMU_LVL2 switch off:
   Supersection / Section format,
   vaddr" "paddr" "len" should be aligned with 1M

when K_CONFIG_MMU_LVL2 switch on:
   Supersection / Section / Large Page / Small Page format,
   "vaddr" "paddr" "len" should be aligned with 4K */
int32_t k_mmap(uintptr_t vaddr,
               uintptr_t paddr,
               size_t    len,
               mmu_mem_attr_t  mem_attr);

void k_mmu_show(void);

# 12 "\\os\\alios\\kernel\\armv7r\\include\\port.h"





cpu_cpsr_t cpu_intrpt_save(void);
void   cpu_intrpt_restore(cpu_cpsr_t cpsr);
void   cpu_intrpt_switch(void);
void   cpu_task_switch(void);
void   cpu_first_task_start(void);
void  *cpu_task_stack_init(cpu_stack_t *base, size_t size, void *arg, task_entry_t entry);



# 90 "\\os\\alios\\kernel\\armv7r\\include\\port.h"

static __inline uint8_t cpu_cur_get(void)
{
    return 0;
}







# 51 "\\os\\alios\\kernel\\rhino\\include\\k_api.h"

enum
{
	RHINO_CONFIG_DEFAULT_VALUE = 0,
	RHINO_CONFIG_SYS_STATS_DISABLE = 0x1,
	RHINO_CONFIG_TASK_STACK_OVF_CHECK_DISABLE = 0x2,
};

extern uint32_t rhino_dis_dbg_opt;
char* get_os_current_verison(void);

void krhino_set_dbg_dis_opt(uint32_t val);

uint32_t krhino_get_dbg_dis_opt(void);







# 833 "/os/osa/inc/osa.h"
# 1 "/os/osa/inc/osa_ali.h"
/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/* ===========================================================================
File        : osa_ali.h
Description : Definition of OSA Software Layer data types specific to the
              AliOS.

Notes       :

=========================================================================== */





# 20 "/os/osa/inc/osa_ali.h"
# 21 "/os/osa/inc/osa_ali.h"
# 1 "/os/osa/inc/alios_hisr.h"






typedef struct abstract_task_tag
{
	ktask_t 	hisr_task;  /* task MUST be the first member */
    UINT32 		osa_system_reserved_1;  /* System reserved word   */
    UINT32 		osa_system_reserved_2;  /* System reserved word   */
    UINT32 		osa_system_reserved_3;  /* System reserved word   */
    UINT32 		osa_app_reserved_1;     /* App reserved word      */
    UINT32 		aos_thread_id;          /* Control block ID       */
}abstract_task;

typedef struct RHINO_HISR_STRUCT{

	abstract_task 		abs_task;  /* task MUST be the first member */
	ksem_t				hisr_semaphore;		//note: should not be a pointer
	void                (*hisr_entry)(UINT32);
	UINT32              hisr_entry_parameter;
	CHAR				*hisr_stack_start; 
	CHAR				*hisr_name;
}RHINO_HISR;


UINT32 _ali_hisr_create(RHINO_HISR *hisr_ptr, CHAR * name,void(* entry_function)(UINT32),
				UINT32 entry_input, UINT8 priority);
UINT32 _ali_hisr_delete(RHINO_HISR *hisr_ptr);
UINT32 _ali_hisr_activate(RHINO_HISR *hisr_ptr);






# 22 "/os/osa/inc/osa_ali.h"












/*
 * Data Types.
 */
typedef struct
{
    ksem_t    sem;
}
OsaSemaphoreT ;

typedef struct
{
	abstract_task abs_task;  /* task MUST be the first member */
	void   *entry;
	UINT32  entryParam;
	UINT8  *allocatedStack;
}OsaTaskT;


typedef struct
{
    RHINO_HISR     hisrRef ;
    UINT32      intSource ;
    void        (*fisrRoutine)(UINT32) ;
    void        (*sisrRoutine)(void) ;
}
OsaIsrT ;

typedef struct
{
	kbuf_queue_t 	queue;
	void           *buf;
	CHAR           *name;
	UINT32          blockSize;
}
OsaMsgQT ;


typedef struct
{
	kqueue_t 	queue;
	CHAR       *name;
	CHAR       *buf;
}
OsaMbQT ;

typedef struct
{
    kevent_t   event;
}
OsaFlagT;


typedef struct
{
    kmutex_t    mutex;
}
OsaMutexT ;


typedef struct
{
	ktimer_t *timer;
	CHAR     *name;
	void     (*callBackRoutine)(UINT32) ;
    UINT32   timerArgc ;
    UINT32   initialTime ;
    UINT32   rescheduleTime ;
	CHAR     started;
	CHAR     excluded;
}
OsaTimerT;

/*
 * Defines.
 */




/*
 * Macros.
 */

/*
 * Data.
 */

/*
 * Functions.
 */
BOOL Osa_TranslateErrorCode( char *callerFuncName, UINT32 ErrorCode, OSA_STATUS *pOsaStatus ) ;
OsaRefT OsaCurrentThreadRef(void);



# 834 "/os/osa/inc/osa.h"
# 1 "/os/osa/inc/osa_um_extr.h"
/*************************************************************************/
/*                                                                       */
/*               Copyright Mentor Graphics Corporation 2012              */
/*                         All Rights Reserved.                          */
/*                                                                       */
/* THIS WORK CONTAINS TRADE SECRET AND PROPRIETARY INFORMATION WHICH IS  */
/* THE PROPERTY OF MENTOR GRAPHICS CORPORATION OR ITS LICENSORS AND IS   */
/* SUBJECT TO LICENSE TERMS.                                             */
/*                                                                       */
/*************************************************************************/

/*************************************************************************/
/*                                                                       */
/* FILE NAME                                               VERSION       */
/*                                                                       */
/*      dm_extr.h                                      Nucleus PLUS 1.15 */
/*                                                                       */
/* COMPONENT                                                             */
/*                                                                       */
/*      UM - USB Ring Memory Management                                   */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This file contains function prototypes of all functions          */
/*      accessible to other components.                                  */
/*                                                                       */
/* DATA STRUCTURES                                                       */
/*                                                                       */
/*      None                                                             */
/*                                                                       */
/* DEPENDENCIES                                                          */
/*                                                                       */
/*      um_defs.h                          USB Ring Management constants */
/*                                                                       */
/*************************************************************************/
# 1 "\\os\\nu_xscale\\inc\\um_defs.h"
/*************************************************************************/
/*                                                                       */
/*               Copyright Mentor Graphics Corporation 2012             */
/*                         All Rights Reserved.                          */
/*                                                                       */
/* THIS WORK CONTAINS TRADE SECRET AND PROPRIETARY INFORMATION WHICH IS  */
/* THE PROPERTY OF MENTOR GRAPHICS CORPORATION OR ITS LICENSORS AND IS   */
/* SUBJECT TO LICENSE TERMS.                                             */
/*                                                                       */
/*************************************************************************/

/*************************************************************************/
/*                                                                       */
/* FILE NAME                                               VERSION       */
/*                                                                       */
/*      um_defs.h                                      Nucleus PLUS 1.15 */
/*                                                                       */
/* COMPONENT                                                             */
/*                                                                       */
/*      UM - USB Ring Memory Management                                   */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      This file contains data structure definitions and constants for  */
/*      the USB Ring Memory component.                                    */
/*                                                                       */
/* DATA STRUCTURES                                                       */
/*                                                                       */
/*      DM_PCB                              Dynamic Pool control block   */
/*      DM_HEADER                           Header of each memory block  */
/*      DM_SUSPEND                          Memory suspension block      */
/*                                                                       */
/* DEPENDENCIES                                                          */
/*                                                                       */
/*      cs_defs.h                           Common service definitions   */
/*      tc_defs.h                           Thread Control definitions   */
/*                                                                       */
/*************************************************************************/





/* Check to see if the file has been included already.  */






/* Define the Dynamic Pool Control Block data type.  */
typedef struct UM_PCB_STRUCT
{
    volatile struct UM_BLOCK_HDR_STRUCT* um_hdr;
    UINT16 um_block_size;
    UINT16 um_block_num;

    UINT16 um_waiting_lwm;
    UINT16 um_lwm;

    DATA_ELEMENT *um_data_ptr;

    void (*um_tx_notify)(struct UM_PCB_STRUCT* um_buffer);
    void (*um_resume_notify)(struct UM_PCB_STRUCT* um_buffer);
	void (*um_event_handler)(struct UM_PCB_STRUCT* um_buffer, int evt);
} UM_PCB;

/* Define the header structure that is in front of each memory block.  */
typedef struct UM_BLOCK_HDR_STRUCT
{
    volatile UINT16 um_put_index;
    volatile UINT16 um_get_index;

    DATA_ELEMENT um_padding[32 - 4];
}UM_BLOCK_HDR;

/* Define the block structure that describes each memory block.  */
typedef struct UM_BLOCK_STRUCT
{
    volatile UINT16 um_length;
    volatile UINT16 um_ref;
}UM_BLOCK;

/* Define the header structure that is in front of each memory block.  */
typedef struct UMUP_BLOCK_HDR_STRUCT
{
    volatile UINT16 um_block_put_index;
    volatile UINT16 um_block_get_index;
	volatile UINT16 um_buffer_put_index;
	volatile UINT16 um_buffer_get_index;
    DATA_ELEMENT um_padding[32 - 8];
}UMUP_BLOCK_HDR;
/* Define the block structure that describes each memory block.  */
typedef struct UMUP_BLOCK_STRUCT
{
    UINT16 um_length;
    UINT16 um_ref;
	UINT16 umup_ref;

    DATA_ELEMENT um_padding[32 - 6];
}UMUP_BLOCK;

/* Define the Dynamic Pool Control Block data type for UP direction.  */
typedef struct UMUP_PCB_STRUCT
{
    volatile UMUP_BLOCK_HDR* um_hdr;
    UINT16 um_block_size;
    UINT16 um_block_num;
	

    UINT16 um_waiting_lwm;
    UINT16 um_lwm;

	UINT16 um_packet_headersize;
	UINT16 um_block_by_application;
	
    DATA_ELEMENT *um_data_ptr;

    void (*um_rx_notify)(struct UMUP_PCB_STRUCT* um_buffer);
    void (*um_resume_notify)(struct UMUP_PCB_STRUCT* um_buffer);
} UMUP_PCB;




# 40 "/os/osa/inc/osa_um_extr.h"


/* Check to see if the file has been included already.  */




# 56 "/os/osa/inc/osa_um_extr.h"













/*  Initialization functions.  */
STATUS UMC_Data_Buffer_Init(UM_PCB *buf, DATA_ELEMENT *hdr, DATA_ELEMENT *ptr, 
    					  UINT16 block_size, UINT16 block_num);

/* Core processing functions.  */
DATA_ELEMENT* UMC_Data_Buffer_Get(UM_PCB *buf, UINT16 size, OPTION newblock);
void UMC_Data_Buffer_Put(UM_PCB *buf, DATA_ELEMENT *ptr);

UM_BLOCK* UMC_Data_Buffer_Get_Block(UM_PCB *buf);
void UMC_Data_Buffer_Put_Block(UM_PCB *buf, UM_BLOCK *block);

/* Supplemental service routines */

/* Information retrieval functions.  */
UNSIGNED UMC_Data_Buffer_Get_Size(UM_PCB *buf);

STATUS UMUPC_Data_Buffer_Init(UMUP_PCB *buf, DATA_ELEMENT *hdr, DATA_ELEMENT *ptr, 
					UINT16 block_size, UINT16 block_num);

UMUP_BLOCK* UMUPC_Data_Buffer_Get_Block(UMUP_PCB *buf);

STATUS UMUPC_Data_Buffer_Put_Block(UMUP_PCB *buf, UMUP_BLOCK *block, UINT16 size);
DATA_ELEMENT *UMUPC_Data_Buffer_Get(UMUP_PCB *buf, UINT16* size);

void UMUPC_Data_Buffer_Put(UMUP_PCB *buf, DATA_ELEMENT *ptr);

BOOL Umc_Is_Reach_Hwm(UM_PCB *buf);








# 835 "/os/osa/inc/osa.h"

# 853 "/os/osa/inc/osa.h"







# 3 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.c"
# 1 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.h"
/**
 *  Copyright (C) 2006-2016, ARM Limited, All Rights Reserved
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 *
 *  This file is part of mbed TLS (https://tls.mbed.org)
 */





# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"
/**
 * \file build_info.h
 *
 * \brief Build-time configuration info
 *
 *  Include this file if you need to depend on the
 *  configuration options defined in mbedtls_config.h or MBEDTLS_CONFIG_FILE
 */
 /*
  *  Copyright The Mbed TLS Contributors
  *  SPDX-License-Identifier: Apache-2.0
  *
  *  Licensed under the Apache License, Version 2.0 (the "License"); you may
  *  not use this file except in compliance with the License.
  *  You may obtain a copy of the License at
  *
  *  http://www.apache.org/licenses/LICENSE-2.0
  *
  *  Unless required by applicable law or agreed to in writing, software
  *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
  *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  *  See the License for the specific language governing permissions and
  *  limitations under the License.
  */










/*
 * This set of compile-time defines can be used to determine the version number
 * of the Mbed TLS library used. Run-time variables for the same can be found in
 * version.h
 */

/**
 * The version number x.y.z is split into three parts.
 * Major, Minor, Patchlevel
 */




/**
 * The single version number has the following structure:
 *    MMNNPP00
 *    Major version | Minor version | Patch version
 */








# 1 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
/*
 * Copyright (C) 2019 Alibaba Group Holding Limited
 */




/*specially for alios*/







/* System support */




//#define MBEDTLS_PLATFORM_MEMORY

//#define MBEDTLS_CONFIG_TLS_DEBUG

/* mbed TLS feature support */
# 35 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_ALT


# 53 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 60 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 76 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"





# 103 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"







/* mbed TLS modules */
# 127 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#define MBEDTLS_THREADING_C
//#define MBEDTLS_TIMING_C













# 178 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 185 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 202 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 209 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//#ifdef LWM2M_WITH_MBEDTLS
# 219 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#endif /* LWM2M_WITH_MBEDTLS */







/* Module configuration options */





/**
 * \def MBEDTLS_X509_ALLOW_UNSUPPORTED_CRITICAL_EXTENSION
 *
 * If set, the X509 parser will not break-off when parsing an X509 certificate
 * and encountering an unknown critical extension.
 *
 * \warning Depending on your PKI use, enabling this can be a security risk!
 *
 * Uncomment to prevent an error.
 */




//ALIPAY_SUPPORT BEGIN


typedef unsigned int time_t;
# 273 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"
//#ifndef MBEDTLS_ECP_DP_SECP256R1_ENABLED


//#endif
# 284 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

//ALIPAY_SUPPORT END

# 298 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\alios_mbedtls_config.h"

# 66 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"








/* Target and application specific configurations
 *
 * Allow user to override any previous default.
 *
 */




# 89 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 1 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
/**
 * \file check_config.h
 *
 * \brief Consistency checks for configuration options
 */
/*
 *  Copyright The Mbed TLS Contributors
 *  SPDX-License-Identifier: Apache-2.0
 *
 *  Licensed under the Apache License, Version 2.0 (the "License"); you may
 *  not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *  http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS, WITHOUT
 *  WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 */




/*
 * We assume CHAR_BIT is 8 in many places. In practice, this is true on our
 * target platforms, so not an issue, but let's just be extra sure.
 */
# 31 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"




# 52 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"








































//ALIPAY_SUPPORT BEGIN
# 111 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END






# 129 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"





//ALIPAY_SUPPORT BEGIN
# 152 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
//ALIPAY_SUPPORT END


























# 196 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 206 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"
































































































































# 341 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















































































































































































































































# 589 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"















# 611 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"



















































# 707 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"


















# 737 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











/*
 * HKDF is mandatory for TLS 1.3.
 * Otherwise support for at least one ciphersuite mandates either SHA_256 or
 * SHA_384.
 */
# 759 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

/*
 * The current implementation of TLS 1.3 requires MBEDTLS_SSL_KEEP_PEER_CERTIFICATE.
 */




# 782 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"











































































# 863 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

# 870 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h"

































































/* Reject attempts to enable options that have been removed and that could
 * cause a build to succeed but with features removed. */













































/*
 * Avoid warning from -pedantic. This is a convenient place for this
 * workaround since this is included by every single file before the
 * #if defined(MBEDTLS_xxx_C) that results in empty translation units.
 */
typedef int mbedtls_iso_c_forbids_empty_translation_units;

# 91 "/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h"

# 25 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.h"

# 43 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.h"









# 60 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.h"

/*
 * MBEDTLS_ERR_PLATFORM_HW_FAILED is deprecated and should not be used.
 */



# 78 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.h"



time_t GetGMTTimeSecond(void);
static time_t mbedtls_gmt_time ( time_t* time )
{
    return GetGMTTimeSecond();
}




# 4 "\\pcac\\mbedTLS\\mbedTLS_3_2_1\\asros\\asros_mbedtls_platform.c"

unsigned long rti_get_current_tick(void);

int mbedtls_hardware_poll(void *data, unsigned char *output, 
                                   unsigned int len, unsigned int *olen)
{
    unsigned int seed = (unsigned int)(rti_get_current_tick() & 0xFFFFFFFFUL);
    unsigned int r;
    int i = 0;

    srand(seed);

    for(i = 0; i < len; i++)
    {
        output[i] = (unsigned char)rand();
    }
    *olen = len;

    return 0;
}

