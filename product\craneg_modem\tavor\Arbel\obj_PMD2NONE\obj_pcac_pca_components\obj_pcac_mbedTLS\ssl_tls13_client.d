\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ssl_tls13_client.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\ssl_tls13_client.c
\pcac\mbedTLS\mbedTLS_3_2_1\library\ssl_tls13_client.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ssl_tls13_client.o : \pcac\mbedTLS\mbedTLS_3_2_1\library\common.h
\pcac\mbedTLS\mbedTLS_3_2_1\library\common.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ssl_tls13_client.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ssl_tls13_client.o : \tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h
\tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/ssl_tls13_client.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h:
