/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

#ifndef _PL_MS_CBS_H_
#define _PL_MS_CBS_H_

#include "pl_api.h"
#include "pl_edefs.h"
#include "pl_mode.h"
#include "pl_event.h"
#include "pl_chain.h"
/**********************************************************************
*
* CBS states enum
*
**********************************************************************/
#define STOP_L2_CONFIG	0x1
#define PAUSE_L2_CONFIG	0x2
#define RESUME_L2_CONFIG	0x3
#define MAX_L2_TO_RESTORE	0x2
#define CBS_L2_MASK_OPERATION 0x3
#define INVALID_PERIOD_ID 0xFFFF
#define DIV_IN_NUM_BITS_IN_UINT16(val)  ((val) >> 4)
#define MODULU_NUM_BITS_IN_UINT16(val)  ((val) & 0xF)

#define UINT16_ARR_SET_LSB_IDX(mask,idx) ((*((UINT16 *)mask+(DIV_IN_NUM_BITS_IN_UINT16(idx))))|=(1<<(MODULU_NUM_BITS_IN_UINT16(idx))))


extern state_descriptor *cbs_states[];

//ICAT EXPORTED ENUM
typedef enum
{
    CBS_SET_UP_STATE,
    CBS_L2_CONFIGURATION_STATE,
    CBS_L2_MODIFY_CONFIGURATION_STATE,
    CBS_NORMAL_STATE,
    CBS_TERMINATE_STATE,
    CBS_SELF_TERMINATION_STATE,
    CBS_PENDING_FOR_RESTORATION_STATE,
    CBS_RESTORING_OLD_CONFIGURATION_STATE,
    MAX_CBS_STATES
}CBS_States;

Bool plMclCbsCanSelfReleaseContinue (void);
Bool plMsCbsDrxNeedsToBeReleased (void);

VOID plMsCbsReleaseReqDuringBcch (aplp_event event, VOID_PTR msg);
void plMsCbsSetCbsInTransition (UINT8 mask);
void plMsCbsSetDrxReleasePending (Bool flag);
void plMsCbsTrchConfigDone (aplp_event event, VOID_PTR msg);
VOID plMsCbsL2ConfigReqCantHandle (aplp_event event, VOID_PTR msg);
void plMsCbsInitFlags(void);
UINT8 plMsCbsGetMaxTti (VOID);
void plMsSSmClearCBSClient(void);

#endif
