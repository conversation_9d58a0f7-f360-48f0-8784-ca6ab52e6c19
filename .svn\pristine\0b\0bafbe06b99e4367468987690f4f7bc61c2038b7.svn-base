/*
 * asr_agps_api.h
 *
 *  Created on: 2021
 *      Author: huizhang
 */

#ifndef INCLUDE_ASR_AGPS_API_H_
#define INCLUDE_ASR_AGPS_API_H_
#include "RXN_MSL_Ephemeris.h"

#define SUPL_RRLP_ASSIST_REFTIME (1)
#define SUPL_RRLP_ASSIST_REFLOC (2)
#define SUPL_RRLP_ASSIST_IONO (4)
#define SUPL_RRLP_ASSIST_EPHEMERIS (8)
#define SUPL_RRLP_ASSIST_UTC (16)
#define SUPL_RRLP_BDS_UTC (32)

struct supl_ephemeris_s {
  unsigned char prn;
  unsigned char fill1;
  short delta_n;
  int M0;
  unsigned int e;
  unsigned int A_sqrt;
  int OMEGA_0;
  int i0;
  int w;
  int OMEGA_dot;
  short i_dot;
  short Cuc;
  short Cus;
  short Crc;
  short Crs;
  short Cic;
  short Cis;
  unsigned short toe;
  unsigned short IODC;
  unsigned int toc;
  int AF0;
  short AF1;
  char AF2;
  unsigned char nav_model;

  /* nav model */
  unsigned char bits;
  unsigned char ura;
  unsigned char health;
  char reserved[11];
  char tgd;
  unsigned char AODA;
  unsigned short IODE;
};

struct supl_almanac_s {
  unsigned char prn;
  unsigned short e;
  unsigned char toa;
  short Ksii;
  short OMEGA_dot;
  unsigned char health;
//  unsigned char :8;

  unsigned int A_sqrt;
  int OMEGA_0;
  int w;
  int M0;
  short AF0;
  short AF1;
};

struct supl_ephemeris_glo_s
{
    unsigned char  prn;
    unsigned char  fill[3];

    /* Navigation Model */
    unsigned char  health;
    unsigned char  Ft;
    unsigned int tb;

    /* orbit */
    unsigned int En;
    unsigned int P1;
    unsigned int P2;
    unsigned int M;
    int  X;
    int  Xdot;
    int  Xdotdot;
    int  Y;
    int  Ydot;
    int  Ydotdot;
    int  Z;
    int  Zdot;
    int  Zdotdot;

    /* Clock */
    int  Tau;
    int  Gamma;
    int  DeltaTau;
    int  NT;

    /* from Aux in RT*/
    unsigned char FCN; //Frequency Channel Number
};
struct supl_almanac_qzs_s {
    unsigned char prn;
    unsigned short e;
    short Deltal;
    short OmegaDot;
    unsigned char health;
    unsigned int SqrtA;
    int Omeag0;
    int w;
    int M0;
    short AF0;
    short AF1;
};
struct supl_almanac_glo_s {
    unsigned short    NA;
    unsigned char    nA;
    unsigned char    HA;
    int    LambdaA;
    unsigned int    tlambdaA;
    int    DeltaIa;
    int    DeltaTA;
    char    DeltaTdotA;
    unsigned short    EpsilonA;
    short    OmegaA;
    short    TauA;
    char    CA;
    char    MA;
};
struct supl_ephemeris_bds_s {
    unsigned char  prn;
    unsigned char  fill[3];

    /* Orbit Model */
    unsigned int AODE;
    unsigned short ura;
    unsigned char  fill1[2];
    unsigned int toe;
    unsigned int A_sqrt;
    unsigned int e;
    int  w;
    short  delta_n;
    unsigned char  fill2[2];
    int  M0;
    int  OMEGA_0;
    int OMEGA_dot;
    int i0;
    short i_dot;
    unsigned char rsv[2];

    int Cuc;
    int Cus;
    int Crc;
    int Crs;
    int Cic;
    int Cis;

    /* Clock Model */
    unsigned char  rsv1[2];
    int  toc;
    int  AF0;
    int  AF1;
    int  AF2;
    short  tgd;

    /* nav model */
    unsigned short IODC;
    unsigned char  health;
};

struct supl_ephemeris_qzs_s {
    unsigned char  prn;
    unsigned int  Tot;
    unsigned char CodeOnL2;
    unsigned char ura;
    unsigned char  health;
    unsigned short Iodc;
    unsigned short Iode;
    unsigned char L2pFlag;
    unsigned int  Resv[4];
    char  tgd;
    unsigned int toc;
    char AF2;
    short AF1;
    int AF0;
    short Crs;
    short delta_n;
    int M0;
    short Cuc;
    unsigned int e;
    short Cus;
    unsigned int A_sqrt;
    unsigned int toe;
    char fitflag;
    char AODO;
    short Cic;
    int OMEGA_0;
    short Cis;
    int i0;
    short Crc;
    int w;
    int OMEGA_dot;
    short i_dot;
};
struct supl_almanac_bds_s {
  int  prn;
  int  toa;
  int  A_sqrt;
  int  e;
  int  w;
  int  M0;
  int  OMEGA_0;
  int  OMEGA_dot;
  int  DeltaI;
  short  a0;
  short  a1;
  int  Health;
};
struct agps_utc{
      int tm_sec;    //seconds [0,59]
      int tm_min;    //minutes [0,59]
      int tm_hour;  //hour [0,23]
      int tm_mday;  //day of month [1,31]
      int tm_mon;   //month of year [1,12]
      int tm_year; // since 1970
      int tm_wday; // sunday = 0
};
struct supl_ionospheric_s {
    char a0, a1, a2, a3, b0, b1, b2, b3;
    //belows for beidou
    int toe;
    char svid;
};
struct supl_utc_s {
  int a0;
  int a1;
  char delta_tls;
  unsigned char tot;
  unsigned char wnt;
  unsigned char wnlsf;
  unsigned char dn;
  unsigned char delta_tlsf;
  unsigned char  bValid;
  unsigned char fill[1];
};
struct reftime{
  long gps_tow;
  long gps_week;
  struct agps_utc stamp;
  char gps_week_cycle;
};

typedef struct supl_rrlp_ctx_s {
    int predmode;// 0 means realtime mode , 1 means predmode
    int set;
    long smlc_code;
    long transactionId;

    long waitFix;

    struct reftime time;

    int cnt_eph;
    struct supl_ephemeris_s *eph;

    int cnt_alm;
    int alm_week;
    struct supl_almanac_s *alm;
    struct supl_ionospheric_s iono;
    struct supl_utc_s utc;
    /* Glonass */
    int cnt_eph_glo;
    struct supl_ephemeris_glo_s *eph_glo;

    int cnt_alm_glo;
    int alm_wn_glo;   // week number for glonass
    int wn_glo;   // week number for glonass
    struct supl_almanac_glo_s *alm_glo;

    /* BDS */
    int cnt_eph_bds;
    unsigned short wn_bds;   // Week Number for BDS
    struct supl_ephemeris_bds_s *eph_bds;

    int cnt_alm_bds;
    int alm_wn_bds;   // Week Number for BDS
    struct supl_almanac_bds_s *alm_bds;
    int bds_iono_cnt;
    struct supl_ionospheric_s *bds_iono;

    struct supl_ionospheric_s *glo_iono;
    //struct supl_utc_s           utc_bds;
    //struct SUPLGANSSAddUTCModel utc_add_bds;
    /* QZS */
    int cnt_eph_qzs;
    struct supl_ephemeris_qzs_s *eph_qzs;

    unsigned char cnt_alm_qzs;
    unsigned char alm_wn_qzs;   // alm Week Number for QZS
    unsigned short wn_qzs;   // Week Number for QZS
    unsigned char toa_qzs;
    struct supl_almanac_qzs_s *alm_qzs;
    struct supl_ionospheric_s *qzs_iono;

} supl_assist_t;

int GetGPSEphInfo(RXN_MSL_GPS_Ephem_t *gpsEphemeris,int size);
int GetBDSEphInfo(RXN_MSL_Beidou_Ephem_t *bdsEphemeris,int size);
int tp_agps_init(void);
int tp_get_rt_assist(supl_assist_t *assist,int alm,int lat,int lon,int accuracy);
int tp_get_pred_assist(int day,supl_assist_t *assist);
/*
 * -1xx: init error
 * -2xx: invalid parameters
 * -30x: get eph file error
 * -31x: decode eph file error
 * -4xx: decode pred eph file error
 * -5xx: get utc error
 * */
int tp_get_assist(supl_assist_t *assist,int alm);
int tp_get_assist_bycondition(supl_assist_t *assist,int alm,int lat,int lon,int accuracy);
int tp_consume_1(supl_assist_t *ctx) ;
void tp_free_assist(supl_assist_t *ctx) ;
int tp_set_server(char *srvname);
int tp_set_mode(int day,int flag);
int getTime( struct reftime *ref);
int getRefTime(supl_assist_t *assist);
int convertUtcToRefTime(unsigned int utc,supl_assist_t *assist);
int getGPSTime(unsigned int *GPSTime);
int getUTCTime(unsigned int *utc);
int tp_decode_data(int mode,char *data,int len,supl_assist_t *assist);
int encodeAccessKey(unsigned int utc,char passwd[10],char output[64]);
void tp_decode_status_set(int state);
int tp_decode_status_get(void);
int SetAGPSConfig(char *host,unsigned short port, char *username, char *passwd ,int day, int force, int version);
#endif /* INCLUDE_ASR_AGPS_API_H_ */
