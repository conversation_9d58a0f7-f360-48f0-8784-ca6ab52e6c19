#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#=========================================================================
# File Name      : HAL.mak
# Description    : Main make file for the hal/HAL group.
#
# Usage          : make [-s] -f HAL.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper group building.
#
# Copyright (c) 2001 Intel Corporation. All Rights Reserved
# Yaeli <PERSON>i Feb 06	For TAVOR_RTOS some libs (as IPC and more) of HAL will not be built
#						TAVOR_RTOS platform is basic infrastructure - currently BSP and drivers.
#						Also, not all include files are needed..
# Yaeli Karni 25 Apr 06	Remove BBU lib from BoerneRTOS.
#						specific init.s (brn_init.s) is provided in csw/BSP/src .
#=========================================================================

# Group build options
include ${OPT_FILE}

# Group Makefile information
GEN_GROUP_MAKEFILE = ${BUILD_ROOT}/env/${HOST}/build/group.mak

# Define Group ---------------------------------------

GROUP_NAME     = HAL
GROUP_BASE     = hal
GROUP_DEP_FILE = HAL_dep.mak

GROUP_PATH = ${BUILD_ROOT}/hal/HAL

# The relative path locations of local source and include file directories.
LOCAL_SRC_PATH    = $(GROUP_PATH)/src
LOCAL_INC_PATHS   = $(GROUP_PATH)/src $(GROUP_PATH)/inc

# Group source files, paths not required
LOCAL_SRC_FILES =

# local group build flags for source files
# contained in this group directory
LOCAL_CFLAGS  =
LOCAL_DFLAGS  =

# These are the tool flags specific to the HAL group only.
# The environment, target, and group also set flags.
GROUP_CFLAGS  =
GROUP_DFLAGS  =
GROUP_ARFLAGS =

GROUP_PACKAGE_LIST =
GROUP_GROUP_LIST   =


GROUP_INC_PATHS =     \
                ${BUILD_ROOT}/softutil/csw_memory/inc \
                ${BUILD_ROOT}/softutil/transport/inc \
                ${BUILD_ROOT}/os/osa/inc        \
                ${BUILD_ROOT}/os/nu_xscale/inc  \
                ${BUILD_ROOT}/hal/dvt/inc\
                ${BUILD_ROOT}/softutil/TickManager/inc\
                ${BUILD_ROOT}/softutil/datacollector/src \
                ${BUILD_ROOT}/softutil/datacollector/inc \
                ${BUILD_ROOT}/csw/BSP/inc \
                ${BUILD_ROOT}/CrossPlatformSW/usbmgrtunnel/inc \
                ${BUILD_ROOT}/hal/jtag/inc \
                ${BUILD_ROOT}/hop/core/inc       \
                ${BUILD_ROOT}/hop/MUX27010/inc \
                ${BUILD_ROOT}/hop/timer/inc \
                ${BUILD_ROOT}/hop/ssp/inc \
                ${BUILD_ROOT}/softutil/csw_memory/inc\
               	${BUILD_ROOT}/hop/aci/inc	\
               	${BUILD_ROOT}/hop/mrd/inc \
				$(BUILD_ROOT)/hal/MMU/inc	\
				$(BUILD_ROOT)/hal/SDIO/inc	\
               	$(BUILD_ROOT)/hop/telephony/atcmdsrv/inc \
               	$(BUILD_ROOT)/hal/GPIO/inc \
               	$(BUILD_ROOT)/pcac/duster/inc

ifneq (,$(findstring LWIP,${VARIANT_LIST}))
GROUP_INC_PATHS +=  ${BUILD_ROOT}/hal/musb_device/inc
else
GROUP_INC_PATHS +=  ${BUILD_ROOT}/hal/usb_device/inc
endif

ifneq (,$(findstring BT_STACK_SUPPORT,${VARIANT_LIST}))
GROUP_INC_PATHS += $(BUILD_ROOT)/hal/BT_device/btstack/inc
else
GROUP_INC_PATHS += $(BUILD_ROOT)/hal/BT_device/bthoststack/inc
endif

ifneq (,$(findstring mbedTLS_2_1_8,${VARIANT_LIST}))
GROUP_DFLAGS  += -I$(BUILD_ROOT)/pcac/mbedTLS/mbedTLS_2_1_8 \
                 -I$(BUILD_ROOT)/pcac/mbedTLS/mbedTLS_2_1_8/include
endif

ifneq (,$(findstring mbedTLS_3_2_1,${VARIANT_LIST}))
GROUP_DFLAGS  += -I$(BUILD_ROOT)/pcac/mbedTLS/mbedTLS_3_2_1 \
                 -I$(BUILD_ROOT)/pcac/mbedTLS/mbedTLS_3_2_1/include
endif

ifneq (,$(findstring SILICON_SEAGULL,${VARIANT_LIST}))
GROUP_INC_PATHS +=  ${BUILD_ROOT}/hop/intc/inc

GROUP_PACKAGE_LIST +=	csw/SysCfg \
			hop/pm     \
			hop/rm     \
			hop/aam    \
			hop/commpm \
			hop/timer \
			hal/I2C  \
			hop/pmu \
			hop/dma	\
			hop/RTC \
			hop/aci \
			hop/telephony   \
			hop/mrd		\
			hop/mmi_mat	\
			hal/GPIO	\
			hal/gps_device \
			hal/YT_device
			
ifeq (,$(findstring CRANEL_FP_8MRAM,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += hal/ecm
endif

ifneq (,$(findstring SPIMUX_SUPPORT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += hop/ssp \
			hop/AMUX
endif

ifneq (,$(findstring AES_SUPPORT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += hop/AES 
GROUP_DFLAGS += -DAES_SUPPORT
endif

ifneq (,$(findstring BT_SUPPORT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += \
		hal/BT_device

ifneq (,$(findstring BT_BD_SRC,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += \
		hal/BT_hoststack
endif
endif

ifneq (,$(findstring CMUX_SUPPORT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += \
			hal/cmux
endif

ifneq (,$(findstring LCD_SUPPORT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += \
			hal/lcd
endif

ifneq (,$(findstring CAMERA_SUPPORT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += \
			hal/camera \
			hal/camera_isp
endif

ifneq (,$(findstring SILICON_TTC_CORE_MOHAWK,${VARIANT_LIST}))
	GROUP_PACKAGE_LIST += hal\MMU hal\CORE
else
ifneq (,$(findstring LWIP,${VARIANT_LIST}))
	GROUP_PACKAGE_LIST += hop\core hop\BSP hop\intc hop\utilities  hal\watchdog hal\USIM hal\musb_device hal\USBMgr hal\musb_standart
else
	GROUP_PACKAGE_LIST += hop\core hop\BSP hop\intc hop\utilities  hal\watchdog hal\USIM hal\usb_device hal\USBMgr
endif

ifneq (,$(findstring CRANEL_FP_8MRAM,${VARIANT_LIST}))
ifneq (,$(findstring ENABLE_RNDIS,${VARIANT_LIST}))
	GROUP_PACKAGE_LIST +=hal\rndis hal\ecm
endif
else
	GROUP_PACKAGE_LIST +=hal\rndis
endif


endif

###FOR MINIPLAT SPECIFIC IMPLEMENTATIONS
ifneq (,$(findstring FLAVOR_MINIPLAT,${VARIANT_LIST}))
###DIAGUART ENABLE UART
	ifneq  (,$(findstring DIAGUART,${VARIANT_LIST}))
 		GROUP_PACKAGE_LIST += hal\UART
    endif
endif

#ADD in here verything else except MINIPLAT
ifeq (,$(findstring FLAVOR_MINIPLAT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += hal\UART
endif
## add by tanshi for dbgshell test

ifneq (,$(findstring UART_DBGSHELL,${VARIANT_LIST})) ##enable dbgshell by tanshili 2019/05/24
GROUP_PACKAGE_LIST += hal\dbgshell
endif
endif # SILICON_SEAGULL (PV2 and TTC COMM)

#-------------------- COMMON ------------------------------
ifeq (,$(findstring NOACIPC,${VARIANT_LIST}))
ifneq (,$(findstring ACIPC,${VARIANT_LIST}))
#GROUP_PACKAGE_LIST += CrossPlatformSW/ACIPC
endif
endif

# GROUP_PACKAGE_LIST += CrossPlatformSW/HSI

# Define Group Variants -------------------------------
# Define the members of this group

# Tests for Drivers Validation Team
ifneq ($(DVT_TEST_ENABLE),)
 GROUP_PACKAGE_LIST += hal/DVT
endif

ifneq (,$(findstring WIFI_SUPPORT,${VARIANT_LIST}))
GROUP_DFLAGS += -DWIFI_FUNCTION_SUPPORT
ifeq  (,$(findstring HERON_SUPPORT,${VARIANT_LIST}))
GROUP_DFLAGS += -DASRDKB_550X
endif
endif

ifneq (,$(findstring BT_CHIP_5803,${VARIANT_LIST}))
GROUP_DFLAGS += -DBT_CHIP_5803
endif

ifneq (,$(findstring JACANA_GPS_SUPPORT,${VARIANT_LIST}))
GROUP_PACKAGE_LIST += hal/gps
endif

# GROUP_TRACE_LIST contains package and groups in which diagnostics/tracing
# will be compiled. Not compiling diagnostics/tracing in some modules
# will reduce code size and increase code performance.
GROUP_TRACE_LIST   =  $(GROUP_PACKAGE_LIST)
GROUP_TRACE_TYPE   = STRING_TRACING

# Include the Standard Group Make File ---------------
include ${GEN_GROUP_MAKEFILE}

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include ${GROUP_DEP_FILE}

