/**
 * @file voice_msg.c
 *
 */

/*********************
*      INCLUDES
*********************/
#include "lv_watch.h"

#if USE_LV_WATCH_VOICE_MSG_YNYD != 0 && USE_LV_WATCH_KR_WEILIAO != 1

/*********************
*      DEFINES
*********************/
#define VOICE_MSG_SEND_TIMEOUT_SECONDS (15/3)
#define RES_MENBER_LEN 64
#define VOICE_MSG_MAX_RECORD_DURATION 16 /*in seconds*/
#define VOICE_MSG_MAX_RECORD_SIZE VOICE_MSG_MAX_RECORD_DURATION * HAL_AMR_BUFSIZE_PER_SECOND + VOICE_MSG_AMR_HEAD_SIZE
#define VOICE_MSG_MAX_CHAT_MSG_NUM 0xff  //APP_ADAPTOR_VOICE_MSG_MAX_CHAT_MSG_NUM
#define VOICE_MSG_FAMILY_GROUP_CHAT_WITH_ADMIN_ONLY 1 /*1:add family group when admin is added,
                                                       *0:add family group when the first standby admin is added,

                                                       *and add friends group when the second others is added*/


voice_msg_contact_t gYnContacts[APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM];
voice_msg_contact_t gYnSessions[APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM];

#define YN_CMD_VALUE_MAX_LEN      (60+1)
#define YN_CMD_CHOICE_NUM      (10)
typedef struct
{
    char name[YN_CMD_VALUE_MAX_LEN];
    char value[YN_CMD_VALUE_MAX_LEN];
    char msg_id[YN_CMD_VALUE_MAX_LEN];
    char sesdid[YN_CMD_VALUE_MAX_LEN];
} voice_cmd_cfm_data_t;
voice_cmd_cfm_data_t gYnCmds[YN_CMD_CHOICE_NUM];
#if 0
char *yn_voicedw_buff;
uint32_t yn_voicedw_buff_len;
static char voiceurl_bak[256+1];
#endif
char *yn_photodw_buff;
uint32_t yn_photodw_buff_len;
uint32_t yn_photodw_isgif=0;

static char photourl_bak[256+1];
#if 0
void yn_voice_dw_buff_zero()
{
	if(yn_voicedw_buff)
	{
		lv_mem_free(yn_voicedw_buff);
		yn_voicedw_buff=NULL;
	}
	yn_voicedw_buff_len=0;
	
	WS_PRINTF("%s: \n", __FUNCTION__);
}
#endif

#define DIAL_PAD_TA_X    0
#define DIAL_PAD_TA_Y 	 34
#define DIAL_PAD_TA_H    46
#define DIAL_PAD_TA_W    240
#define DIAL_PAD_HIGHTLIGHT_WIDTH    80
#define DIAL_PAD_HIGHTLIGHT_HEIGHT    40
#define DIAL_PAD_HIGHTLIGHT_YOFFSET   80
#define DIAL_PAD_CONT_W  DIAL_PAD_TA_W
#define DIAL_PAD_CONT_H  (LV_VER_RES - DIAL_PAD_TA_H - DIAL_PAD_TA_Y)

#define DIAL_PAD_X_NUM  3
#define DIAL_PAD_Y_NUM  4
#define DIAL_PAD_NUM  (DIAL_PAD_X_NUM * DIAL_PAD_Y_NUM)


static lv_ll_t * voice_session_msg_read_nvm(void);


#if USE_LV_WATCH_WS_LEEFINE != 0
#define VOICE_MSG_NO_FAMILY_GROUP 1
#endif 

#if USE_LV_WATCH_SETTING_PASSWORD != 0
extern lv_img_dsc_t password_btn;
extern lv_img_dsc_t password_bg;
#else
const LV_ATTRIBUTE_MEM_ALIGN uint8_t password_bg_map[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 
  0x00, 0x00, 0x00, 0xf0, 0x00, 0x00, 0x00, 0xf0, 0x08, 0x03, 0x00, 0x00, 0x00, 0x09, 0x8b, 0x19, 
  0xa0, 0x00, 0x00, 0x00, 0x60, 0x50, 0x4c, 0x54, 0x45, 0x00, 0x00, 0x00, 0x32, 0x32, 0x32, 0xf9, 
  0xf9, 0xf9, 0xff, 0xff, 0xff, 0x3e, 0x3e, 0x3e, 0x80, 0x80, 0x80, 0xbf, 0xbf, 0xbf, 0xeb, 0xeb, 
  0xeb, 0xb5, 0xb5, 0xb5, 0x07, 0x07, 0x07, 0x79, 0x79, 0x79, 0xd8, 0xd8, 0xd8, 0xf4, 0xf4, 0xf4, 
  0x26, 0x26, 0x26, 0x10, 0x10, 0x10, 0xf0, 0xf0, 0xf0, 0x14, 0x14, 0x14, 0xc8, 0xc8, 0xc8, 0x58, 
  0x58, 0x58, 0xe3, 0xe3, 0xe3, 0x1f, 0x1f, 0x1f, 0xef, 0xef, 0xef, 0xcf, 0xcf, 0xcf, 0x9a, 0x9a, 
  0x9a, 0x60, 0x60, 0x60, 0xdf, 0xdf, 0xdf, 0x9f, 0x9f, 0x9f, 0x8f, 0x8f, 0x8f, 0x70, 0x70, 0x70, 
  0x2b, 0x2b, 0x2b, 0xaf, 0xaf, 0xaf, 0x50, 0x50, 0x50, 0xd2, 0x49, 0xb1, 0x31, 0x00, 0x00, 0x08, 
  0x42, 0x49, 0x44, 0x41, 0x54, 0x78, 0xda, 0xec, 0xda, 0xdd, 0xce, 0xab, 0x20, 0x10, 0x85, 0xe1, 
  0xe9, 0x0a, 0x09, 0x03, 0x6a, 0xa9, 0x3f, 0x89, 0x47, 0xde, 0xff, 0x6d, 0x6e, 0xb4, 0xb1, 0xbb, 
  0xf6, 0xd4, 0xf9, 0x12, 0x02, 0xeb, 0xb9, 0x80, 0x49, 0xdf, 0x80, 0xc1, 0x54, 0x84, 0x88, 0x88, 
  0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0x88, 0xda, 0xf2, 0x5c, 0xb6, 
  0x47, 0xb5, 0xb6, 0xe5, 0xf9, 0x9b, 0x5b, 0x71, 0xed, 0xdb, 0x76, 0x49, 0x5e, 0x1e, 0x0d, 0x58, 
  0x1a, 0xeb, 0xfd, 0x2a, 0x7e, 0x3e, 0x1a, 0x71, 0xee, 0xea, 0xea, 0x9f, 0xdf, 0xd3, 0xd6, 0xd8, 
  0x02, 0xe7, 0x25, 0x6e, 0xe9, 0x09, 0xde, 0x2d, 0x6d, 0xed, 0xe8, 0x73, 0x4f, 0x3f, 0x1a, 0xc2, 
  0xe0, 0xda, 0x31, 0xb8, 0x76, 0x0c, 0xae, 0x1d, 0x83, 0x6b, 0xc7, 0xe0, 0xda, 0x31, 0xb8, 0x76, 
  0x0c, 0xae, 0x1d, 0x83, 0x6b, 0xc7, 0xe0, 0xda, 0x31, 0xb8, 0x76, 0x0c, 0xae, 0x1d, 0x83, 0x6b, 
  0xd7, 0xe6, 0x97, 0x87, 0xe6, 0xbe, 0x2d, 0x35, 0xf7, 0xf5, 0xb0, 0x9d, 0x3d, 0xbd, 0xb5, 0x7a, 
  0x03, 0xa0, 0x95, 0xa7, 0xb8, 0xb5, 0x5b, 0x2d, 0x4b, 0xcb, 0xf7, 0xb4, 0xda, 0xbb, 0x89, 0x47, 
  0x44, 0x44, 0x44, 0x44, 0x44, 0x7f, 0xe6, 0xe5, 0xdc, 0x20, 0x46, 0x26, 0x1d, 0xfd, 0xa8, 0x66, 
  0xe3, 0x5e, 0x51, 0xc7, 0x59, 0x75, 0x30, 0xcd, 0xed, 0x00, 0x78, 0xbb, 0x51, 0x87, 0x7e, 0x12, 
  0x03, 0x31, 0x7d, 0xe6, 0x39, 0x31, 0x12, 0x46, 0xc0, 0x2a, 0x78, 0xc5, 0x97, 0x28, 0xb7, 0xa9, 
  0xed, 0xbc, 0x73, 0xa6, 0x5d, 0xb0, 0x47, 0x36, 0xeb, 0x98, 0xb0, 0x73, 0x72, 0xd7, 0x00, 0xa0, 
  0x5b, 0x5d, 0xec, 0xb0, 0x0b, 0x62, 0x01, 0x59, 0x6f, 0x15, 0x1c, 0x81, 0x18, 0x24, 0x1b, 0xd2, 
  0x3e, 0x56, 0x6e, 0xeb, 0x35, 0x7c, 0xca, 0xb1, 0x8a, 0x85, 0x19, 0xd0, 0x60, 0x15, 0x2c, 0x5d, 
  0xf8, 0x5a, 0x1b, 0x0c, 0x62, 0x46, 0x01, 0xcc, 0x62, 0x21, 0x6a, 0x10, 0x39, 0x82, 0x6d, 0xcd, 
  0xc7, 0x92, 0x98, 0x71, 0xb6, 0x3f, 0xd1, 0x3e, 0xf8, 0x58, 0x12, 0x65, 0xf0, 0xad, 0x71, 0x6b, 
  0xf1, 0xc1, 0x51, 0xac, 0x0c, 0xc8, 0x5e, 0x45, 0x07, 0x27, 0xc3, 0x5f, 0x18, 0x14, 0x99, 0x4a, 
  0xc9, 0xc1, 0xee, 0x38, 0x96, 0x0c, 0x8c, 0xde, 0x7b, 0xbc, 0x7b, 0x8b, 0x0e, 0xf6, 0x56, 0x3b, 
  0xda, 0xe3, 0x2d, 0x4a, 0xd1, 0xc1, 0xeb, 0xb1, 0xc0, 0x96, 0xc1, 0xe8, 0x42, 0xc1, 0xc1, 0x03, 
  0x32, 0x67, 0x33, 0xca, 0x39, 0xa7, 0x09, 0x59, 0x0a, 0xc5, 0x06, 0x07, 0xf3, 0x67, 0xce, 0xa1, 
  0xe4, 0x73, 0x38, 0xa4, 0xf7, 0xbc, 0x42, 0xf7, 0x8c, 0x79, 0xf0, 0xd1, 0x9b, 0x82, 0xd8, 0x9f, 
  0xeb, 0x63, 0x91, 0xc1, 0xd7, 0xde, 0x42, 0xdf, 0x2d, 0xf7, 0x61, 0x65, 0xf7, 0x16, 0x1b, 0x7c, 
  0xf6, 0x9a, 0x9b, 0x00, 0xcc, 0xe5, 0x05, 0x5b, 0xf7, 0x5e, 0xcf, 0xe3, 0xb5, 0xb8, 0x60, 0xeb, 
  0xde, 0xc9, 0x5d, 0xfe, 0x89, 0x7a, 0x95, 0x16, 0x7c, 0xf4, 0x22, 0xf9, 0x0f, 0xb9, 0xa9, 0x47, 
  0x5a, 0x73, 0x73, 0x98, 0x3c, 0x32, 0x2d, 0xee, 0x1c, 0x56, 0xfc, 0x90, 0x7b, 0x1c, 0xbe, 0x75, 
  0xe5, 0xbd, 0x5a, 0x9a, 0x07, 0xf7, 0xf8, 0x6f, 0x15, 0x43, 0xaa, 0x1a, 0xe5, 0x3e, 0xa7, 0x3f, 
  0xe4, 0xae, 0x41, 0xe7, 0xe3, 0xb5, 0xad, 0x8b, 0x41, 0x88, 0x88, 0x88, 0xfe, 0xb1, 0x6b, 0x2e, 
  0xab, 0x11, 0xc3, 0x30, 0x14, 0x15, 0xc6, 0xe0, 0x37, 0x63, 0x3b, 0x8b, 0xfc, 0xff, 0x9f, 0x16, 
  0x4d, 0x4a, 0x95, 0x74, 0xdb, 0xc3, 0x60, 0xa8, 0xcf, 0x62, 0xc8, 0x6c, 0x84, 0x6f, 0xa4, 0x60, 
  0xe3, 0x7b, 0x37, 0x9b, 0xcd, 0x66, 0xb3, 0xf9, 0x3c, 0xc9, 0x7b, 0xdf, 0xa8, 0x4a, 0x46, 0x02, 
  0x17, 0x98, 0xa3, 0x87, 0xcd, 0xcd, 0x20, 0x78, 0x72, 0x09, 0x5a, 0x62, 0x1f, 0xd5, 0xd1, 0xfe, 
  0x3a, 0x26, 0x38, 0xe2, 0x82, 0x7b, 0x70, 0x0a, 0x29, 0xb8, 0x39, 0x4e, 0xf0, 0x41, 0x0b, 0xce, 
  0xee, 0xa2, 0x86, 0x2c, 0x10, 0xa9, 0x82, 0x82, 0x83, 0x73, 0x85, 0xfc, 0x86, 0x8b, 0x53, 0x8a, 
  0x17, 0x90, 0xa1, 0xef, 0x8f, 0x12, 0x6c, 0xde, 0x35, 0xf7, 0x85, 0x8c, 0x84, 0xdf, 0xe9, 0x9f, 
  0xd8, 0xcd, 0xb4, 0x0d, 0x32, 0x81, 0x47, 0xcd, 0x7f, 0x5b, 0x62, 0x15, 0x15, 0x4c, 0xad, 0x10, 
  0xec, 0x47, 0x35, 0xbd, 0x68, 0x64, 0xce, 0x9b, 0x60, 0x60, 0x5c, 0x04, 0x23, 0xa3, 0x1e, 0xa9, 
  0xed, 0x9b, 0x51, 0x30, 0xc1, 0x51, 0xeb, 0xa0, 0x0d, 0x4e, 0x32, 0x63, 0x08, 0x83, 0xba, 0xa7, 
  0xed, 0xea, 0x8d, 0x08, 0x27, 0xb8, 0xb8, 0x37, 0xa1, 0x64, 0x68, 0xbb, 0x2c, 0xd9, 0x7d, 0x13, 
  0x31, 0x53, 0xae, 0x83, 0x82, 0x03, 0x64, 0x15, 0x58, 0x16, 0xc8, 0x0a, 0xbe, 0xb0, 0x80, 0x20, 
  0x28, 0x98, 0x33, 0x83, 0x6c, 0x5c, 0x86, 0xf7, 0x67, 0x7d, 0x3f, 0x20, 0x33, 0x73, 0x08, 0x29, 
  0xd8, 0x37, 0x11, 0xe9, 0xb3, 0x20, 0x3d, 0x0e, 0x57, 0x5a, 0xc9, 0xc4, 0x77, 0xc0, 0xde, 0xac, 
  0xc9, 0x04, 0x83, 0xb4, 0x17, 0x10, 0xda, 0xd7, 0x1a, 0x35, 0xdd, 0xd4, 0x0f, 0xe0, 0x88, 0xe5, 
  0xc5, 0x04, 0x93, 0x74, 0xe0, 0xc8, 0xf0, 0x28, 0x31, 0xff, 0xbe, 0x44, 0xaf, 0x03, 0x13, 0x2f, 
  0xf4, 0x5d, 0xc6, 0x18, 0xbb, 0x08, 0x79, 0x60, 0x2d, 0x7f, 0x17, 0xfc, 0x4c, 0xbb, 0x01, 0x9f, 
  0xc8, 0x13, 0x2f, 0xca, 0x2a, 0xb1, 0x9b, 0xf0, 0x90, 0xf8, 0x4f, 0x04, 0x37, 0x50, 0x70, 0x8e, 
  0x86, 0x8d, 0x34, 0x29, 0xf8, 0x20, 0x53, 0xf5, 0x57, 0x40, 0x77, 0xb1, 0xd0, 0xc3, 0xaf, 0x6f, 
  0xf8, 0x64, 0xb2, 0x59, 0xf6, 0x67, 0x61, 0xc1, 0x89, 0xd8, 0x37, 0xeb, 0x35, 0xd3, 0x16, 0x39, 
  0x5f, 0x4e, 0xf0, 0xec, 0xb6, 0xc5, 0x03, 0x0d, 0xc9, 0x76, 0xf0, 0xd0, 0xc7, 0x2a, 0xcb, 0x09, 
  0xae, 0xae, 0xe4, 0x2e, 0xd2, 0xa2, 0x53, 0x3a, 0x72, 0x9b, 0x5a, 0x73, 0x92, 0x56, 0xb4, 0xde, 
  0x5c, 0x4e, 0x70, 0x73, 0x77, 0x1a, 0x12, 0x75, 0x33, 0xb2, 0x2c, 0x27, 0x78, 0x3a, 0xa3, 0x24, 
  0x01, 0x48, 0xc5, 0xbd, 0xb1, 0xfe, 0xae, 0x95, 0xd4, 0x92, 0x39, 0x42, 0xd5, 0x2b, 0xd5, 0xb3, 
  0x0b, 0x44, 0x1b, 0xc1, 0xb9, 0x7a, 0x9c, 0x3b, 0xa8, 0xb5, 0xd9, 0x6c, 0x36, 0x9b, 0xcd, 0x66, 
  0xb3, 0xd9, 0x6c, 0x3e, 0x46, 0xf3, 0x4f, 0xba, 0x00, 0x24, 0x75, 0x37, 0x8f, 0xd8, 0xb8, 0x55, 
  0xc6, 0x71, 0xc4, 0xb3, 0x73, 0xce, 0x97, 0x11, 0xc9, 0xd8, 0x52, 0xe8, 0x42, 0x30, 0xab, 0xbb, 
  0x28, 0x69, 0x49, 0xc1, 0xc5, 0x19, 0x4d, 0xd0, 0x7a, 0xb5, 0x41, 0x82, 0x49, 0x47, 0x77, 0xdc, 
  0xed, 0xcd, 0x0a, 0xe9, 0x2d, 0x73, 0x0e, 0xf3, 0x11, 0xd1, 0x74, 0x01, 0xe2, 0xa0, 0xf5, 0x9f, 
  0xcb, 0xa8, 0x8c, 0x84, 0x78, 0xda, 0xbb, 0xb2, 0xd6, 0x8b, 0xc2, 0x91, 0x91, 0x7a, 0xa7, 0xa9, 
  0xf4, 0xda, 0x1a, 0xa2, 0x0b, 0xf3, 0x76, 0x3f, 0x98, 0x16, 0x6b, 0xb0, 0x1c, 0x5a, 0x85, 0xb8, 
  0x16, 0x34, 0x91, 0xaf, 0xfb, 0x74, 0xe7, 0xc5, 0x1a, 0x6c, 0x76, 0x1f, 0x23, 0x38, 0xde, 0x57, 
  0x35, 0x75, 0x64, 0xd0, 0x06, 0x77, 0xc2, 0x4f, 0xb2, 0x11, 0xe4, 0x0c, 0x7b, 0xf3, 0x6e, 0x02, 
  0xd9, 0xe0, 0x02, 0xc5, 0xd2, 0x6a, 0x32, 0xab, 0x13, 0x18, 0x18, 0xff, 0xd8, 0x53, 0x16, 0x6b, 
  0xf0, 0xb5, 0xc4, 0xea, 0x45, 0x52, 0xa1, 0x52, 0x3c, 0xf9, 0x21, 0x78, 0xb1, 0x06, 0xeb, 0xbe, 
  0xa4, 0xd4, 0xa2, 0xbf, 0x47, 0x22, 0x4e, 0x6d, 0xe5, 0x61, 0x47, 0xae, 0xd6, 0x60, 0x91, 0x14, 
  0xdc, 0x37, 0x03, 0xb2, 0xaa, 0xda, 0xfd, 0x0c, 0xb2, 0x5a, 0x83, 0xbf, 0xda, 0x3b, 0x1b, 0xde, 
  0xb4, 0x61, 0x20, 0x0c, 0x5f, 0xf6, 0xde, 0x19, 0x46, 0x87, 0xe9, 0x47, 0xec, 0xc6, 0x49, 0x04, 
  0xff, 0xff, 0x5f, 0x6e, 0x67, 0xe2, 0x1a, 0x18, 0x93, 0x88, 0x71, 0xa5, 0x68, 0xca, 0x23, 0x21, 
  0x4a, 0x69, 0xad, 0x3c, 0xdc, 0x25, 0x04, 0xfb, 0x72, 0xc4, 0xe6, 0xb4, 0x89, 0xcf, 0x3a, 0x7b, 
  0xc8, 0x61, 0x7f, 0xb1, 0xc6, 0x54, 0x2f, 0xc0, 0xfb, 0x6a, 0xb5, 0xd7, 0x5a, 0x3b, 0x71, 0x48, 
  0x95, 0x78, 0x55, 0x56, 0x23, 0x3f, 0xb6, 0xdb, 0x54, 0xc2, 0xb9, 0x94, 0xea, 0x93, 0x89, 0xdc, 
  0x51, 0x75, 0xbf, 0xa9, 0x92, 0xd5, 0x9a, 0x7c, 0x89, 0x5f, 0x75, 0xf2, 0x30, 0x1d, 0xfc, 0x6b, 
  0xd6, 0x73, 0x57, 0x2b, 0x15, 0x4c, 0xaf, 0x9c, 0xea, 0x7e, 0xaa, 0xfb, 0x6e, 0x61, 0x01, 0xbe, 
  0x2a, 0x96, 0xd3, 0x2d, 0xdd, 0xd6, 0xe9, 0xa8, 0xbd, 0xfd, 0xe4, 0xa9, 0xee, 0x77, 0x61, 0x01, 
  0xde, 0xde, 0x2b, 0x15, 0x5c, 0x4e, 0x15, 0x54, 0xd5, 0x00, 0xe7, 0xab, 0x09, 0xaa, 0x0f, 0x9c, 
  0x2f, 0x35, 0x62, 0x5a, 0x52, 0x03, 0xca, 0x49, 0xf8, 0xbd, 0x76, 0x48, 0xf2, 0xdb, 0xfb, 0x66, 
  0x51, 0x1d, 0x46, 0x73, 0x13, 0xe8, 0x6a, 0x85, 0x69, 0x99, 0xd7, 0x43, 0x3c, 0x04, 0x2e, 0x2d, 
  0xc0, 0xa9, 0x64, 0xb8, 0x76, 0xfb, 0xdc, 0xd7, 0xed, 0x0f, 0x85, 0x17, 0x17, 0xe0, 0x34, 0x05, 
  0xb5, 0x4f, 0xdb, 0xf8, 0x56, 0xa3, 0x9d, 0xd1, 0xcb, 0x61, 0x2a, 0xe2, 0x59, 0x56, 0x0b, 0xd9, 
  0x5c, 0x56, 0x95, 0x79, 0x59, 0xde, 0xa4, 0x6f, 0xdd, 0x00, 0xab, 0xf1, 0xdb, 0xb7, 0xcc, 0x72, 
  0x7f, 0x30, 0x29, 0x75, 0x1a, 0x60, 0xbd, 0x53, 0x55, 0x5e, 0xb7, 0x9b, 0x18, 0x91, 0x3a, 0x75, 
  0x55, 0x3b, 0x5d, 0xc4, 0x78, 0x5f, 0x4b, 0xb4, 0x56, 0x56, 0x56, 0x56, 0x56, 0x56, 0x56, 0x56, 
  0x56, 0x56, 0x56, 0xfe, 0x03, 0x62, 0x59, 0xd5, 0x22, 0x9b, 0x05, 0x37, 0xf4, 0x0d, 0xec, 0x73, 
  0x2f, 0xa8, 0xa5, 0xd1, 0x41, 0xe8, 0x2f, 0x6c, 0xdf, 0xd7, 0x5a, 0x1a, 0xf9, 0xa0, 0x1a, 0x30, 
  0x84, 0xae, 0x11, 0xb1, 0x54, 0x82, 0x0d, 0xce, 0xde, 0x89, 0xba, 0xc3, 0xf8, 0xec, 0xda, 0xd2, 
  0x66, 0xf7, 0xfe, 0x52, 0x38, 0xa3, 0x65, 0x00, 0x40, 0x48, 0xa0, 0x10, 0x35, 0x1c, 0x85, 0x1b, 
  0x13, 0x69, 0x48, 0x19, 0x81, 0x66, 0x46, 0x16, 0x9b, 0x09, 0xa2, 0x01, 0xce, 0x44, 0x3a, 0xba, 
  0x64, 0x84, 0xb7, 0x54, 0xcc, 0xd7, 0x82, 0x17, 0x97, 0x4d, 0x33, 0x9a, 0x20, 0x12, 0x85, 0x45, 
  0x0c, 0xd4, 0x3f, 0x0a, 0x33, 0x22, 0x71, 0xb8, 0xd6, 0x39, 0xf8, 0x59, 0xc2, 0x1e, 0x41, 0x85, 
  0x19, 0xc1, 0x28, 0xee, 0x9c, 0x32, 0xb8, 0x83, 0x29, 0x4a, 0xe8, 0xcd, 0x33, 0xdf, 0x53, 0xa8, 
  0x91, 0x88, 0xc2, 0x44, 0x92, 0x85, 0xf5, 0x46, 0x01, 0x1a, 0x08, 0xeb, 0xd1, 0x0d, 0xe8, 0xe7, 
  0xed, 0xba, 0x1a, 0x52, 0xeb, 0x5c, 0x1b, 0x5f, 0x81, 0x29, 0xa0, 0x80, 0xb9, 0xc1, 0x17, 0x08, 
  0x1f, 0x72, 0x58, 0x7f, 0xc6, 0x3a, 0xb2, 0x7a, 0xc2, 0x0d, 0x8e, 0xf4, 0x87, 0x23, 0x7a, 0x95, 
  0x9e, 0x63, 0xdc, 0xa3, 0x8d, 0x2f, 0x14, 0x53, 0xf6, 0x56, 0x61, 0xba, 0x81, 0x61, 0x4a, 0xe6, 
  0xe1, 0x7f, 0x5d, 0x15, 0xa4, 0xd4, 0x13, 0x1e, 0x63, 0x9c, 0xfa, 0xa8, 0x6a, 0xdd, 0x1c, 0xe3, 
  0x10, 0xe2, 0x3f, 0x06, 0xa3, 0x84, 0x78, 0x3f, 0x54, 0x11, 0xce, 0xdd, 0xaf, 0xf2, 0x83, 0x7a, 
  0xc2, 0x06, 0x96, 0xac, 0x99, 0x44, 0x1b, 0x07, 0xdf, 0xd0, 0x63, 0x34, 0x18, 0x34, 0xc0, 0x83, 
  0x41, 0xf4, 0xf5, 0xc6, 0x18, 0x35, 0x13, 0xa1, 0x1b, 0x5a, 0xe9, 0x4a, 0xcb, 0xd2, 0xf2, 0xe2, 
  0x61, 0x35, 0x61, 0x8b, 0x23, 0x35, 0x39, 0x95, 0xad, 0x07, 0x06, 0x4b, 0x8f, 0x30, 0x40, 0x98, 
  0xb9, 0xd5, 0xb1, 0x88, 0x24, 0x26, 0x36, 0x0c, 0x75, 0x72, 0x0f, 0x7e, 0xb6, 0x8e, 0xec, 0x50, 
  0x4d, 0xf8, 0x84, 0x8e, 0x1d, 0x02, 0x12, 0xbe, 0x47, 0xa0, 0x87, 0x08, 0x50, 0xf8, 0x46, 0xd8, 
  0xe0, 0x1e, 0x52, 0xbb, 0xb1, 0x4a, 0xb9, 0xf0, 0x08, 0xa1, 0xbe, 0xeb, 0xcc, 0x1f, 0xe0, 0xe2, 
  0x5e, 0x78, 0x3a, 0xd1, 0x23, 0x9c, 0x80, 0x91, 0xc7, 0xbf, 0x84, 0x1b, 0x4e, 0x8c, 0xf0, 0x9c, 
  0x68, 0x69, 0x1e, 0xbf, 0x9e, 0x14, 0x16, 0x5c, 0x63, 0xb2, 0x30, 0x79, 0xa4, 0xad, 0x81, 0x99, 
  0x79, 0x2a, 0xc3, 0xc4, 0x51, 0x38, 0x63, 0x48, 0x69, 0x4a, 0x8f, 0x55, 0x59, 0xf1, 0x49, 0x61, 
  0x96, 0x10, 0x44, 0x60, 0xc4, 0x40, 0xf4, 0xd6, 0x5d, 0x08, 0x33, 0xfa, 0x12, 0x61, 0x46, 0x48, 
  0xc2, 0x4e, 0x74, 0xd0, 0x5e, 0x44, 0xa6, 0x11, 0x3c, 0x9a, 0xea, 0xc2, 0xf5, 0x8e, 0xd2, 0x1a, 
  0x62, 0x8e, 0xa4, 0x14, 0xa4, 0x47, 0xf0, 0xe8, 0x92, 0xb0, 0xb9, 0x4c, 0x69, 0x05, 0xa0, 0x5b, 
  0xe1, 0xe5, 0xec, 0xc3, 0xfa, 0x58, 0x70, 0x05, 0x3d, 0x42, 0x27, 0xfc, 0x2f, 0x61, 0x4e, 0x77, 
  0x5f, 0x14, 0x09, 0xbf, 0xd6, 0x3e, 0x4a, 0x67, 0xe1, 0x4e, 0x22, 0x08, 0x12, 0xa1, 0xc7, 0x48, 
  0xc2, 0x41, 0x13, 0xa3, 0xc7, 0xc8, 0x3c, 0x99, 0x0a, 0xc6, 0xf3, 0xf3, 0xce, 0x4c, 0x14, 0xad, 
  0xff, 0xf3, 0xe5, 0x07, 0xa7, 0x4d, 0x55, 0x61, 0x26, 0x9b, 0xe2, 0x63, 0x49, 0x99, 0x25, 0x8c, 
  0xcc, 0x59, 0x38, 0x40, 0x52, 0xa0, 0x2b, 0x7c, 0x0b, 0x74, 0xe1, 0x99, 0x96, 0x3b, 0x46, 0xe1, 
  0x3e, 0xdc, 0x08, 0x7b, 0x11, 0x87, 0x96, 0x04, 0xa7, 0x28, 0xdc, 0x87, 0x66, 0xae, 0x30, 0xa3, 
  0xbf, 0x8e, 0x30, 0x03, 0xe9, 0xa0, 0x55, 0xa7, 0x76, 0xec, 0xa5, 0xa4, 0x2c, 0x03, 0x12, 0x6f, 
  0xc6, 0x9c, 0x85, 0x3b, 0x99, 0x84, 0x8f, 0x00, 0x84, 0x5a, 0xe7, 0xa7, 0xf3, 0x24, 0xb8, 0xe6, 
  0x71, 0x61, 0x85, 0x07, 0x9c, 0xae, 0xf7, 0x61, 0x8f, 0x01, 0xa1, 0x7d, 0x4e, 0x98, 0xd2, 0x27, 
  0xa4, 0xd2, 0xcb, 0x64, 0x18, 0x5d, 0x14, 0x76, 0x43, 0x14, 0x56, 0xa6, 0x94, 0x8e, 0x27, 0x05, 
  0x26, 0x6d, 0xae, 0x1a, 0xf3, 0xc3, 0x63, 0xf6, 0x22, 0xd2, 0x06, 0xd8, 0x2b, 0x61, 0x81, 0xd7, 
  0x51, 0xba, 0xe7, 0x84, 0x5f, 0xf2, 0x5c, 0xd6, 0xae, 0xe4, 0xf3, 0xb0, 0xa0, 0x55, 0xe1, 0x06, 
  0xdd, 0xad, 0xf0, 0xf9, 0xd9, 0x81, 0x52, 0x7c, 0x3a, 0xa0, 0x7b, 0x3c, 0xa5, 0xf5, 0xef, 0x7b, 
  0x4a, 0xc2, 0x8d, 0xe6, 0x48, 0xcc, 0xe7, 0xce, 0xc1, 0x79, 0x04, 0x49, 0x8c, 0x45, 0x75, 0x5a, 
  0xfb, 0xaf, 0x9f, 0x98, 0x66, 0x12, 0x7c, 0xcb, 0x27, 0xc8, 0xa0, 0xd1, 0x18, 0x40, 0xc4, 0x44, 
  0xe3, 0x97, 0x30, 0xc3, 0xdb, 0xe6, 0xbc, 0xb9, 0x6a, 0xf0, 0x68, 0x52, 0x5b, 0xb6, 0x44, 0xd6, 
  0x81, 0x27, 0xe1, 0x00, 0x40, 0x18, 0x88, 0x72, 0x56, 0x02, 0x2e, 0x18, 0xca, 0x2e, 0x3d, 0xdc, 
  0xfd, 0xa4, 0x5d, 0x51, 0xd5, 0x52, 0x87, 0xd1, 0x02, 0x8e, 0xdd, 0xb1, 0xf1, 0xc6, 0x05, 0x22, 
  0x0f, 0x00, 0xa7, 0x14, 0xe1, 0xb1, 0xa1, 0x3e, 0x9f, 0xe2, 0x5b, 0x9a, 0x43, 0x7f, 0x24, 0x85, 
  0xa5, 0x1d, 0x8c, 0xe9, 0x2d, 0x99, 0x9e, 0x12, 0x9c, 0xb1, 0x45, 0x0d, 0xbf, 0x94, 0xb2, 0x4b, 
  0x23, 0xad, 0xb1, 0xa4, 0x3b, 0x6b, 0xcf, 0x64, 0xcc, 0xb1, 0x21, 0x1a, 0x8c, 0x31, 0x72, 0x39, 
  0x73, 0xd9, 0xc5, 0x5f, 0x94, 0x60, 0x6f, 0x1f, 0x56, 0xe2, 0xe7, 0x47, 0xf2, 0xfd, 0x58, 0xe2, 
  0x54, 0xfc, 0x77, 0xc0, 0x5a, 0x2a, 0xb8, 0x79, 0xdb, 0xd3, 0xca, 0xca, 0xca, 0xca, 0xca, 0xca, 
  0x15, 0xbf, 0x01, 0x6e, 0x51, 0x83, 0xf1, 0x3b, 0x45, 0x1b, 0xdd, 0x00, 0x00, 0x00, 0x00, 0x49, 
  0x45, 0x4e, 0x44, 0xae, 0x42, 0x60, 0x82, 
};

lv_img_dsc_t password_bg = {
  .header.always_zero = 0,
  .header.w = 240,
  .header.h = 240,
  .data_size = 2279,
  .header.cf = LV_IMG_CF_RAW,
  .data = password_bg_map,
};

const LV_ATTRIBUTE_MEM_ALIGN uint8_t password_btn_map[] = {
  0x89, 0x50, 0x4e, 0x47, 0x0d, 0x0a, 0x1a, 0x0a, 0x00, 0x00, 0x00, 0x0d, 0x49, 0x48, 0x44, 0x52, 
  0x00, 0x00, 0x00, 0x44, 0x00, 0x00, 0x00, 0x28, 0x01, 0x03, 0x00, 0x00, 0x00, 0x7d, 0xeb, 0xce, 
  0xd0, 0x00, 0x00, 0x00, 0x03, 0x50, 0x4c, 0x54, 0x45, 0x00, 0x00, 0x00, 0xa7, 0x7a, 0x3d, 0xda, 
  0x00, 0x00, 0x00, 0x01, 0x74, 0x52, 0x4e, 0x53, 0x00, 0x40, 0xe6, 0xd8, 0x66, 0x00, 0x00, 0x00, 
  0x0d, 0x49, 0x44, 0x41, 0x54, 0x18, 0xd3, 0x63, 0x18, 0x05, 0x83, 0x0a, 0x00, 0x00, 0x01, 0x90, 
  0x00, 0x01, 0x7b, 0x65, 0x9a, 0x4f, 0x00, 0x00, 0x00, 0x00, 0x49, 0x45, 0x4e, 0x44, 0xae, 0x42, 
  0x60, 0x82, 
};

lv_img_dsc_t password_btn = {
  .header.always_zero = 0,
  .header.w = 68,
  .header.h = 40,
  .data_size = 98,
  .header.cf = LV_IMG_CF_RAW_ALPHA,
  .data = password_btn_map,
};
#endif


#undef printf
extern int ws_printf(const char * fmt, ...);
#define printf(fmt, ...)   ws_printf(fmt, ##__VA_ARGS__)

/*for reading file*/
typedef lv_fs_file_t ui_file_t;
typedef lv_fs_dir_t  ui_dir_t;
#define UI_FILE_READ_ONLY     LV_FS_MODE_RD
#define UI_FILE_OPEN(p, m)   \
    ({                       \
        ui_file_t *f;        \
        f = lv_mem_alloc(sizeof(ui_file_t)); \
        memset(f,0,sizeof(ui_file_t)); \
        lv_fs_open(f, p, m); \
        (f);                 \
    }                        \
    )

#define UI_FILE_CLOSE(f)      \
    ({                        \
        lv_fs_close(f);       \
        lv_mem_free(f);       \
    }                         \
    )
#define UI_FILE_SEEK          lv_fs_seek
#define UI_FILE_READ(f, b, l)          \
    ({                                 \
        uint32_t ret_Len;                \
        lv_fs_read(f, b, l, &ret_Len); \
        (ret_Len);                     \
    }                                  \
    )
#define UI_FILE_SIZE          lv_fs_size
#define UI_EMPTY_FILE         ".EMPTFILE"

/**********************
*      TYPEDEFS
**********************/

/**********************
*  STATIC PROTOTYPES
**********************/
static void voice_msg_set_slide_enable(lv_watch_obj_ext_t * ext, bool enable);
static void voice_msg_lcd_wakeup(void);
static bool voice_msg_is_higher_prio_task_present(lv_watch_Activity_Id_t exception);
//static void voice_msg_del_interface(lv_watch_Activity_Id_t act_id);
static bool voice_msg_compare_contacts(app_adaptor_voice_msg_t * msg, voice_msg_contact_t * contact);
static char * voice_msg_copy_text(uint16_t text_id);
static void voice_msg_write_nvm(lv_ll_t * contact_list);
static void voice_msg_high_priority_task_end(void);
#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
static void voice_msg_create_one_unread_msg(app_adaptor_voice_msg_t * info,
        lv_ll_t * contact_list,
        voice_msg_contact_t * contact);
#endif
static void voice_msg_create_multi_unread_msgs(lv_obj_t * obj,
        uint8_t unread_count,
        lv_ll_t * contact_list,
        voice_msg_contact_t * contact);
static void voice_msg_update_multi_unread_msgs(lv_obj_t * obj, uint8_t unread_count);
static nv_watch_voice_msg_t * voice_msg_read_index_nvm(void);
static nv_watch_friends_t * voice_msg_read_pb_nvm(void);
static lv_ll_t * voice_msg_read_nvm(void);
static bool voice_msg_update_nvm(app_adaptor_voice_msg_t * msg);
static void voice_msg_update_contact_list(lv_voice_msg_ext_t * ext,bool is_sesslist);
static bool voice_msg_send_voice(lv_voice_msg_chat_ext_t * ext);
lv_obj_t * voice_msg_create_send(lv_obj_t * activity_obj);
static void voice_msg_no_network_or_sim_create(lv_obj_t * obj);
static void voice_msg_no_online_create(lv_obj_t * obj);
static void voice_msg_free_msgs(app_adaptor_voice_msg_t * msgs);
static void voice_msg_contacts_create(lv_obj_t * obj,uint8_t type);
static lv_obj_t * voice_msg_get_obj(lv_watch_Activity_Id_t actId);
static void voice_msg_chat_stop_voice(lv_voice_msg_chat_ext_t * ext);
static void voice_msg_add_new_msg_to_chat(app_adaptor_voice_msg_t * msgs,bool playtone);
static void voice_msg_chat_record_cb(MCI_EVNET_T event, MCI_INFO_T info_type, int32_t value);
static void voice_msg_create_speak(void);
static void voice_msg_prepare_destory(lv_obj_t * activity_obj);
static lv_obj_t * voice_msg_no_contacts_create(lv_obj_t * obj,uint32_t textid);
static void voice_msg_contact_list_create(lv_voice_msg_ext_t * ext, lv_coord_t pos);
static void voice_msg_send_end_anim(lv_anim_t * a);
static lv_obj_t  * voice_msg_create_expression(lv_obj_t * activity_obj);
static void voice_msg_create_enlarged_photo(lv_img_dsc_t * photo);
static uint32_t voice_msg_record_buffer_stop_req(void);
static void voice_msg_note_create(lv_obj_t * obj, uint16_t text_id);


#if VOICE_MSG_XPHONE_TEST != 0
static void voice_msg_xphone_init(void);
#endif 
#if VOICE_MSG_TEST != 0
static void voice_msg_test_init(void);
#if VOICE_MSG_APP_SPECIFIC_TEST == 0
static void voice_msg_test_clear(void);
static void voice_msg_clear_nvm_for_test(void);
static void voice_msg_msg_incoming_over_cb(void * para);
#endif
/*for pc test*/
/* static void Hal_Record_Buffer_Start_Req_1(uint8_t * buffer, uint32_t bufsize, uint32_t maxDurationMs, AUDIO_RECORD_CB_FUNC func); */
/* static uint32_t Hal_Record_Buffer_Stop_Req_1(uint32_t * recSize, uint32_t * durationMs); */
#endif
void voice_msgincome_audio_ctrl_callback(AUDIO_CTRL_PRIORITY priority);

#if USE_LV_WATCH_MEDIA_KEY != 0
static bool lv_watch_voice_msg_chat_key_handle(lv_obj_t *act_obj, uint8_t key, uint8_t event, void *param);
static bool lv_watch_voice_msg_speak_key_handle(lv_obj_t *act_obj, uint8_t key, uint8_t event, void *param);
#endif

static lv_obj_t * voice_msg_create_title(lv_obj_t * cont, char * text, uint16_t actId);

/**********************
*  STATIC VARIABLES
**********************/

static app_adaptor_voice_msg_t * msg_buf = NULL;
/**********************
*      MACROS
**********************/
#if VOICE_MSG_XPHONE_TEST != 0
/*test content:
 *open voice msg app, start chat with one contact*/
#include "ws_cJSON.h"

void voice_msg_list_dump_to_fs(uint8_t index);
void voice_msg_delete_old_info(uint8_t index, void *node);

#define GROUP_VOICE_MSG_MAX_NUM (APP_ADAPTOR_VOICE_MSG_MAX_CHAT_MSG_NUM)
#define FRIEND_VOICE_MSG_MAX_NUM (APP_ADAPTOR_VOICE_MSG_MAX_CHAT_MSG_NUM)

typedef struct _voice_msg_list_s
{
    lv_ll_t list;
    uint16_t count;
    uint16_t index;
    uint16_t max;
    
}voice_msg_list_s;

static voice_msg_list_s voice_msg_list[NV_WATCH_MAX_VOICE_MSG_CHAT_NUM];

#define VOICE_MSG_LIST(i) (&(voice_msg_list[i].list))
#define VOICE_MSG_CNT(i)  (voice_msg_list[i].count)
#define VOICE_MSG_IDX(i)  (voice_msg_list[i].index)
#define VOICE_MSG_MAX(i)  (voice_msg_list[i].max)

#define VOICE_MSG_INIT(i, a)  { }


#define VOICE_MSG_REMOVE(i, msg) { }


#define VOICE_MSG_ADD(i, msg) { }

#define VOICE_MSG_SAVE(i, msg) { }


#define VOICE_MSG_NODE_TAIL(i) _lv_ll_get_tail(VOICE_MSG_LIST(i))

	
#define UI_FILE_WRITE_ONLY    LV_FS_MODE_WR
#define UI_FILE_WRITE(f, b, l)          \
    ({                                  \
        uint32_t ret_Len;                 \
        lv_fs_write(f, b, l, &ret_Len); \
        (ret_Len);                      \
    }                                   \
    )
#define UI_FILE_REMOVE        lv_fs_remove
#define VOICE_MSG_TEST_FILE_DIR          "C:/"
#define VOICE_MSG_LIST_DB                "C:/voice_msg_list.json"
#if USE_LV_WATCH_VOICE_MSG_YNYD!=0
#define VOICE_MSG_LIST_DB2                "C:/voice_msg_list2.json"
#endif
#endif

/**********************
 *   GLOBAL FUNCTIONS
 ***********************/
lv_obj_t * voice_msg_create(lv_obj_t * activity_obj, voice_msg_from_app_t other_app, lv_ll_t * contact_list)
{
    printf("%s\n", __FUNCTION__);

    if(activity_obj == NULL) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_MSG;
        activity_ext.create = NULL; /*voice_msg_create;*/
        activity_ext.prepare_destory = voice_msg_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);
    }

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    /*Allocate the tab type specific extended data*/
	lv_voice_msg_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_ext_t));
	LV_ASSERT_MEM(ext);
	ext->pressing = false;
	ext->other_app = other_app;
	ext->contact_list = NULL;
	ext->page=NULL;
	ext->label_tip=NULL;

	lv_style_init(&ext->style_cont_mark);
	lv_style_set_bg_color(&ext->style_cont_mark, LV_STATE_DEFAULT, LV_COLOR_RED);
	lv_style_set_radius(&ext->style_cont_mark, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
	lv_style_set_border_opa(&ext->style_cont_mark, LV_STATE_DEFAULT, LV_OPA_0);

	lv_style_init(&ext->style_label_mark);
    lv_style_set_text_color(&ext->style_label_mark, LV_STATE_DEFAULT, LV_COLOR_WHITE);
	lv_style_set_text_font(&ext->style_label_mark, LV_STATE_DEFAULT, LV_THEME_WATCH_NIGHT_FONT_20);
	lv_style_set_text_color(&ext->style_label_mark, LV_STATE_DEFAULT, LV_COLOR_BLACK);

	if(setting_is_flying_mode()) {
		voice_msg_note_create(obj, WATCH_TEXT_ID_PLEASE_TURN_OFF_FLYING_MODE);
		return obj;
	}

	bool is_no_connetion = false;
	if((false == watch_modem_sim_present_check_req())
	   || (MMI_MODEM_SIGNAL_BAR_0 == watch_modem_get_signal_bar_req())) {
		is_no_connetion = true;
	}
#if USE_LV_WLAN != 0
	if(is_no_connetion) {
		if(hal_wlan_is_connected_state()) {
			is_no_connetion = false;
		}
	}
#endif

	if(is_no_connetion) {
		voice_msg_no_network_or_sim_create(obj);
		return obj;
	}


	if(!MMI_ModemAdp_WS_Is_Online())
	{
		voice_msg_no_online_create(obj);
		return obj;
	}

    if(contact_list)
        ext->contact_list = contact_list;
    else
        ext->contact_list = voice_msg_read_nvm();


    if(NULL == ext->contact_list->head) {
        /*no contacts*/
		voice_msg_create_title(obj,NULL, ACT_ID_VOICE_MSG);
        ext->label_tip=voice_msg_no_contacts_create(obj,WATCH_TEXT_ID_WAITING);
        return obj;
    }

    voice_msg_contacts_create(obj,0);

	//MMI_ModemAdp_WS_Yn_Chat_Get_ContactList();
	
    return(obj);
}

static void voice_session_msg_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    if(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT)) {
        printf("%s: error, delete ACT_ID_VOICE_MSG_SESSION before ACT_ID_VOICE_MSG_CHAT\n", __FUNCTION__);
        return;
    }

    lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_SESSION);
    if(NULL == ext) return;
    if(ext->other_app) {
#if USE_LV_WATCH_ALBUM != 0
        album_photo_share_flag_reset();
#endif /* USE_LV_WATCH_ALBUM */
    }
    if(ext->contact_list) {
#if VOICE_MSG_TEST == 0
        voice_msg_write_nvm(ext->contact_list);
#elif VOICE_MSG_APP_SPECIFIC_TEST != 0
        voice_msg_write_nvm(ext->contact_list);
#endif
        _lv_ll_clear(ext->contact_list);
        lv_mem_free(ext->contact_list);
    }
    lv_style_reset(&(ext->style_cont_mark));
    lv_style_reset(&(ext->style_label_mark));
	
    lv_watch_png_cache_all_free();
	
    printf("%s--end\n", __FUNCTION__);

#if VOICE_MSG_TEST != 0
#if VOICE_MSG_APP_SPECIFIC_TEST == 0
    voice_msg_clear_nvm_for_test();
    voice_msg_test_clear();
#endif
#endif
}

lv_obj_t * voice_session_msg_create(lv_obj_t * activity_obj, voice_msg_from_app_t other_app, lv_ll_t * contact_list)
{
    ws_printf("%s\n", __FUNCTION__);

    if(activity_obj == NULL) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_MSG_SESSION;
        activity_ext.create = NULL; /*voice_msg_create;*/
        activity_ext.prepare_destory = voice_session_msg_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);
    }

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    /*Allocate the tab type specific extended data*/
	lv_voice_msg_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_ext_t));
	LV_ASSERT_MEM(ext);
	ext->pressing = false;
	ext->other_app = other_app;
	ext->contact_list = NULL;
	ext->page=NULL;
	ext->label_tip=NULL;

	lv_style_init(&ext->style_cont_mark);
	lv_style_set_bg_color(&ext->style_cont_mark, LV_STATE_DEFAULT, LV_COLOR_RED);
	lv_style_set_radius(&ext->style_cont_mark, LV_STATE_DEFAULT, LV_RADIUS_CIRCLE);
	lv_style_set_border_opa(&ext->style_cont_mark, LV_STATE_DEFAULT, LV_OPA_0);

	lv_style_init(&ext->style_label_mark);
    lv_style_set_text_color(&ext->style_label_mark, LV_STATE_DEFAULT, LV_COLOR_WHITE);
	lv_style_set_text_font(&ext->style_label_mark, LV_STATE_DEFAULT, LV_THEME_WATCH_NIGHT_FONT_20);
	lv_style_set_text_color(&ext->style_label_mark, LV_STATE_DEFAULT, LV_COLOR_BLACK);

	if(setting_is_flying_mode()) {
		voice_msg_note_create(obj, WATCH_TEXT_ID_PLEASE_TURN_OFF_FLYING_MODE);
		return obj;
	}

	bool is_no_connetion = false;
	if((false == watch_modem_sim_present_check_req())
	   || (MMI_MODEM_SIGNAL_BAR_0 == watch_modem_get_signal_bar_req())) {
		is_no_connetion = true;
	}
#if USE_LV_WLAN != 0
	if(is_no_connetion) {
		if(hal_wlan_is_connected_state()) {
			is_no_connetion = false;
		}
	}
#endif

	if(is_no_connetion) {
		voice_msg_no_network_or_sim_create(obj);
		return obj;
	}


	if(!MMI_ModemAdp_WS_Is_Online())
	{
		voice_msg_no_online_create(obj);
		return obj;
	}

    if(contact_list)
        ext->contact_list = contact_list;
    else
        ext->contact_list = voice_session_msg_read_nvm();
	

	MMI_ModemAdp_WS_Yn_Chat_Get_SessionList();

    if(NULL == ext->contact_list->head) {
        /*no contacts*/
		printf("%s--no session record--\n", __FUNCTION__);
		voice_msg_create_title(obj,NULL, ACT_ID_VOICE_MSG_SESSION);
        //ext->label_tip=voice_msg_no_contacts_create(obj,WATCH_TEXT_ID_HEALTH_NO_RECORD);
        ext->label_tip=voice_msg_no_contacts_create(obj,WATCH_TEXT_ID_GETTING_INFO);
        return obj;
    }

    voice_msg_contacts_create(obj,1);

	
    return(obj);
}


static lv_res_t voice_cmd_cfm_btn_action(lv_obj_t * btn,lv_event_t e)
{
	printf("--voice_cmd_cfm_btn_action start..\n");
	if(e==LV_EVENT_CLICKED){
	    uint8_t index = voice_msg_get_user_num(btn);
		if(index<0 ||index>YN_CMD_CHOICE_NUM)
			index=0;
		
		char sid_msgid[256]={0};
		sprintf(sid_msgid,"%s#%s#%s",gYnCmds[index].sesdid,gYnCmds[index].msg_id,gYnCmds[index].value);
		
		printf("-sid_msgid:%s\n",sid_msgid);
		MMI_ModemAdp_WS_Yn_Report_CmdClick(sid_msgid);
		
		lv_watch_go_back();
	}
	
    return LV_RES_OK;
}

static void voice_cmd_cfm_msg_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    lv_watch_png_cache_all_free();
	
    printf("%s--end\n", __FUNCTION__);

}

lv_obj_t * voice_cmd_cfm_msg_create(lv_obj_t * activity_obj, char*sessid, char * txt, char * msg_id)
{
    printf("%s,msg_id:%p,txt:%p\n", __FUNCTION__,msg_id,txt);

    if(activity_obj == NULL) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_CMD_CFM;
        activity_ext.create = NULL; /*voice_msg_create;*/
        activity_ext.prepare_destory = voice_cmd_cfm_msg_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);
    }

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    /*Allocate the tab type specific extended data*/
	
	if(msg_id&&txt)
	{
	
		uint16_t Length=strlen(txt);
		char * showtxt=(char *)malloc(Length+1);
		char * msgId=(char *)malloc(strlen(msg_id)+1);
		memset(msgId,0,strlen(msg_id)+1);
		memset(showtxt,0,Length+1);
		strcpy(showtxt,txt);
		strcpy(msgId,msg_id);
		printf("sessid:%s,msg_id:%s,txt:%s\n",sessid,msgId,showtxt);

		char *pdata=(char *)showtxt;
		char * index=strstr(pdata,"CMD");

		lv_obj_t * cont= lv_cont_create(obj, NULL);
		LV_ASSERT_MEM(cont);
		if(cont == NULL) return NULL;
		lv_obj_add_style(cont,LV_CONT_PART_MAIN, &lv_watch_style_transp);
		lv_cont_set_fit2(cont, LV_FIT_NONE, LV_FIT_TIGHT);
		lv_cont_set_layout(cont, LV_LAYOUT_OFF);
		lv_obj_set_width(cont, LV_HOR_RES);
		
		/*title */
		lv_obj_t * cont1 = lv_cont_create(cont, NULL);
		LV_ASSERT_MEM(cont1);
		if(cont1 == NULL) return NULL;
		lv_obj_add_style(cont1, LV_CONT_PART_MAIN,&lv_watch_style_transp);
		lv_cont_set_layout(cont1, LV_LAYOUT_OFF);
		lv_obj_set_width(cont1, LV_HOR_RES);
		lv_cont_set_fit2(cont1, LV_FIT_NONE, LV_FIT_TIGHT);
		lv_obj_align(cont1, NULL, LV_ALIGN_IN_TOP_LEFT, 0, 10);
		lv_obj_t * label = lv_label_create(cont1, NULL);
	    lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font20);
	    lv_label_set_long_mode(label, LV_LABEL_LONG_BREAK);
		lv_obj_set_width(label, LV_HOR_RES);
	    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);

		 pdata=(char *)showtxt;
		 index=strstr(pdata,"CMD");
		if(index){
			 uint16_t len =strlen(pdata);
			 char* tmp_str=(char*)malloc(len+1);
			 memset(tmp_str,0,len+1);
			 strncpy(tmp_str,showtxt,index-pdata);	
			 lv_label_set_text(label, tmp_str);
			 pdata=index+3;
		}
		else
	    	lv_label_set_text(label, showtxt);
		
		lv_obj_align(label, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, 2);


		//disp choice button
		/*T...C...T...C...T...C...*/
		uint8_t idx=0;
		memset(gYnCmds,0,sizeof(voice_cmd_cfm_data_t)*YN_CMD_CHOICE_NUM);
		pdata+=1;
		//gYnCmds[idx]
		char* pCursor=pdata;
		index=NULL;
		
		
		char res_part[3][RES_MENBER_LEN] = "";
		while(pCursor){
		
			index = strstr(pCursor, "T");
			if(index!=NULL){
				*index = 0;
				index++;
			}
			//WS_PRINTF("sos pre-idx:%d,data:%s\n",id_x,pCursor);
            memset(res_part, 0, 3*RES_MENBER_LEN);
			str_get_params_ex(pCursor, 0, strlen(pCursor)-1, '\0', "C", res_part);  //support number+name
			if(strlen(res_part[0])&&strlen(res_part[1]))
			{
	            strcpy(gYnCmds[idx].name,res_part[0]);
	            strcpy(gYnCmds[idx].value,res_part[1]);
	            strcpy(gYnCmds[idx].msg_id,msgId);
	            strcpy(gYnCmds[idx].sesdid,sessid);
				idx++;
			}
			pCursor=index;
			
		}	

		lv_obj_t * cont2 = lv_cont_create(cont, NULL);
		lv_obj_add_style(cont2,LV_CONT_PART_MAIN, &lv_watch_style_transp);
		lv_obj_align(cont2, cont1, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 4);
		lv_cont_set_layout(cont2, LV_LAYOUT_COLUMN_MID);
		lv_obj_set_width(cont2, LV_HOR_RES);
		lv_cont_set_fit2(cont2, LV_FIT_NONE, LV_FIT_TIGHT);
		
		printf("idx:%d\n",idx);
		for(int i=0;i<idx;i++)
		{
			lv_obj_t * btn=lv_btn_create(cont2,NULL);
			lv_obj_set_size(btn,200,40);
			lv_obj_t * Label = lv_label_create(btn,NULL);
			lv_label_set_text(Label,gYnCmds[i].name); 
			lv_obj_add_style(label,LV_LABEL_PART_MAIN, &lv_watch_font20);
			lv_obj_align(Label, btn, LV_ALIGN_IN_TOP_LEFT, 0, 0);
			voice_msg_set_user_num(btn,i);
			lv_obj_set_event_cb(btn, voice_cmd_cfm_btn_action);
			lv_obj_set_style_local_text_color(btn,LV_BTN_PART_MAIN,LV_STATE_DEFAULT,LV_COLOR_MAKE(40,41,49));
			lv_obj_set_style_local_text_color(btn,LV_BTN_PART_MAIN,LV_STATE_PRESSED,LV_COLOR_MAKE(255,199,8));
			
		}

		free(showtxt);
		free(msgId);
	}

	
    return(obj);
}

static void voice_friend_add_choice_pos_btn_cb(lv_obj_t * btn,lv_event_t e)
{
	printf("--voice_friend_add_choice_pos_btn_action start..\n");
	if(LV_EVENT_CLICKED==e){
		lv_watch_go_back();
		make_friends_create_event_cb(NULL,LV_EVENT_CLICKED);
	}
	
}

static void voice_friend_add_choice_number_btn_cb(lv_obj_t * btn,lv_event_t e)
{
	printf("--voice_friend_add_choice_number_btn_action start..\n");
	if(LV_EVENT_CLICKED==e){
		lv_watch_go_back();
		voice_friend_add_choice_phone_number_create(NULL);
	}
	
}

static void voice_friend_add_choice_way_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    lv_watch_png_cache_all_free();

}

lv_obj_t * voice_friend_add_choice_way_create(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    if(activity_obj == NULL) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_FRIEND_ADD_CHOICE;
        activity_ext.create = NULL; /*voice_msg_create;*/
        activity_ext.prepare_destory = voice_friend_add_choice_way_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);
    }

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    /*Allocate the tab type specific extended data*/

	/*title */
	lv_obj_t * cont1 = lv_cont_create(obj, NULL);
	LV_ASSERT_MEM(cont1);
	if(cont1 == NULL) return NULL;
	lv_obj_add_style(cont1,LV_CONT_PART_MAIN, &lv_watch_style_transp);
	lv_cont_set_layout(cont1, LV_LAYOUT_OFF);
	lv_obj_set_width(cont1, LV_HOR_RES);
	lv_cont_set_fit2(cont1, LV_FIT_NONE,LV_FIT_TIGHT);
	lv_obj_align(cont1, NULL, LV_ALIGN_IN_TOP_LEFT, 0, 10);
	lv_obj_t * label = lv_label_create(cont1, NULL);
    lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font30);
    lv_label_set_long_mode(label, LV_LABEL_LONG_BREAK);
	lv_obj_set_width(label, LV_HOR_RES);
    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
    lv_label_set_text_id(label, WATCH_TEXT_ID_FRIEND_ADD);
	
	lv_obj_align(label, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, 2);


	lv_obj_t * cont2 = lv_cont_create(obj, NULL);
	lv_obj_add_style(cont2, LV_CONT_PART_MAIN,&lv_watch_style_transp);
	lv_obj_align(cont2, cont1, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 10);
	lv_cont_set_layout(cont2, LV_LAYOUT_COLUMN_MID);
	lv_obj_set_width(cont2, LV_HOR_RES);
	lv_cont_set_fit2(cont2, LV_FIT_NONE,LV_FIT_TIGHT);

	lv_obj_t * btn1=lv_btn_create(cont2,NULL);
	lv_obj_set_size(btn1,200,50);
	lv_obj_t * Label1 = lv_label_create(btn1,NULL);
	lv_label_set_text_id(Label1,WATCH_TEXT_ID_FRIEND_POSTION_TO_POS); 
	lv_obj_add_style(Label1,LV_LABEL_PART_MAIN, &lv_watch_font30);
	lv_obj_align(Label1, btn1, LV_ALIGN_IN_TOP_LEFT, 0, 0);
	lv_obj_set_event_cb(btn1, voice_friend_add_choice_pos_btn_cb);
	lv_obj_set_style_local_text_color(btn1,LV_BTN_PART_MAIN,LV_STATE_DEFAULT,LV_COLOR_MAKE(40,41,49));
	lv_obj_set_style_local_text_color(btn1,LV_BTN_PART_MAIN,LV_STATE_PRESSED,LV_COLOR_MAKE(255,199,8));

#if 1
	
	//shou ji hao ma cha zhao
	lv_obj_t * btn2=lv_btn_create(cont2,NULL);
	lv_obj_set_size(btn2,200,50);
	lv_obj_t * Label2 = lv_label_create(btn2,NULL);
	lv_label_set_text_id(Label2,WATCH_TEXT_ID_FRIEND_BY_PHONE_NUMBER); 
	lv_obj_add_style(Label2,LV_LABEL_PART_MAIN, &lv_watch_font30);
	lv_obj_align(Label2, btn2, LV_ALIGN_IN_TOP_LEFT, 0, 0);
	lv_obj_set_event_cb(btn2, voice_friend_add_choice_number_btn_cb);
	lv_obj_set_style_local_text_color(btn2,LV_BTN_PART_MAIN,LV_STATE_DEFAULT,LV_COLOR_MAKE(40,41,49));
	lv_obj_set_style_local_text_color(btn2,LV_BTN_PART_MAIN,LV_STATE_PRESSED,LV_COLOR_MAKE(255,199,8));
	
#endif

	
    return(obj);
}

lv_input_number_obj_ext_t * voice_friend_input_number_get_ext(void)
{
    lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_FRIEND_ADD_BY_PHONE);
    if(activity_obj == NULL) return NULL;

    lv_obj_t * watch_obj;
    lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &watch_obj);
    if(watch_obj == NULL) return NULL;

    if(watch_obj) {
        lv_input_number_obj_ext_t  * ext = lv_obj_get_ext_attr(watch_obj);
        return ext;

    }
    return NULL;
}

static lv_res_t voice_msg_input_number_btn_press_action(lv_obj_t * btn)
{
    lv_input_number_obj_ext_t * ext = voice_friend_input_number_get_ext();
	if (ext){
	    lv_obj_set_hidden(ext->cont_circle, false);
	    lv_obj_set_pos(ext->cont_circle, lv_obj_get_x(btn)+(LV_HOR_RES-DIAL_PAD_CONT_W)/2, lv_obj_get_y(btn) + DIAL_PAD_HIGHTLIGHT_YOFFSET);
	}

    return LV_RES_OK;
}

static lv_res_t voice_msg_input_number_btn_click_action(lv_obj_t * btn)
{
    lv_obj_t * img = NULL;
    char num[2];
    uint32_t free_num = lv_obj_get_user_data(btn).user_num;
    lv_input_number_obj_ext_t * ext = voice_friend_input_number_get_ext();
	if (ext == NULL) return;

    switch(free_num){
        case 10: //0+
            lv_textarea_add_text(ext->ta, "0");
            break;

        case 11:  //back
            lv_textarea_del_char(ext->ta);
            break;

        case 9:  //confirm
            const char * number = lv_textarea_get_text(ext->ta);
			printf("%s, number=%s\n", __FUNCTION__, number);
            if(strlen(number) < 4) {
				lv_textarea_set_text(ext->ta, "");
				tip_content_create(lv_watch_get_activity_obj(ACT_ID_VOICE_FRIEND_ADD_BY_PHONE), WATCH_TEXT_ID_FRIEND_PHONE_NUMBER_INVALID);
			}else{
				char *find=(char *)malloc(12);
				memset(find,0,12);
				strcpy(find,number);
				MMI_ModemAdp_WS_Yn_Friend_Find_By_PhbNumber(find);
				lv_textarea_set_text(ext->ta, "");
				tip_content_create(lv_watch_get_activity_obj(ACT_ID_VOICE_FRIEND_ADD_BY_PHONE), WATCH_TEXT_ID_FRIEND_ADD_SEND);
				return LV_RES_OK;

			}

            break;

        default:
            snprintf(num, 2, "%d", free_num + 1);
            lv_textarea_add_text(ext->ta, num);
            break;
    }

    //hide circle
    lv_obj_set_hidden(ext->cont_circle, true);

    return LV_RES_OK;
}

static void voice_friend_add_phone_number_input_btn_event_cb(lv_obj_t * btn, lv_event_t e)
{
    switch(e) {
        case LV_EVENT_PRESSED:			
           // voice_msg_input_number_btn_press_action(btn);
            break;

        case LV_EVENT_SHORT_CLICKED:
            voice_msg_input_number_btn_click_action(btn);
            break;

        default:
            break;
    }
}


void voice_friend_number_rsp_cb(uint32_t pa, int32_t pb, void *pc)
{
    lv_input_number_obj_ext_t * ext = voice_friend_input_number_get_ext();
    //hide circle
	if (ext){
	 	lv_watch_go_back();
	}
}

lv_obj_t * voice_friend_add_choice_phone_number_create(lv_obj_t * activity_obj)
{
    lv_input_number_obj_ext_t * ext = NULL;
    lv_obj_t * btn = NULL;
    lv_obj_t * img = NULL;
    static lv_style_t style_circle;
    static lv_style_t style_btn;
	uint32_t title_id;
	
    //activity obj
    if(NULL == activity_obj) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_FRIEND_ADD_BY_PHONE;
        activity_ext.create = NULL;
        activity_ext.prepare_destory = voice_friend_add_choice_way_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    }
	if(activity_obj == NULL) return LV_RES_OK;
	
    //watch obj
    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    if(obj == NULL) return NULL;

#if WATCH_HAS_WALLPAPER == 0
	img = lv_img_create(activity_obj, NULL);
	LV_ASSERT_MEM(img);
	if(img == NULL) return NULL;
	lv_obj_set_size(img, LV_HOR_RES, LV_VER_RES);

	lv_obj_t * par = lv_obj_get_parent(obj);
	lv_obj_set_parent(obj, img);
	lv_obj_del(par);
#endif
	
#if defined(__XF_LCD_SIZE_128X128__)
	lv_img_set_src(lv_obj_get_parent(obj), &password_bg128);
#else
    lv_img_set_src(lv_obj_get_parent(obj), &password_bg);
#endif

	lv_watch_obj_set_anim_mode(obj, LV_WATCH_ANIM_HOR_RIGHT_HIDE);

    ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_input_number_obj_ext_t));

	title_id = WATCH_TEXT_ID_FRIEND_BY_PHONE_NUMBER;

	// title
    ext->title = lv_label_create(obj, NULL);
    lv_obj_set_size(ext->title, LV_HOR_RES, 30);
	lv_obj_add_style(ext->title, LV_LABEL_PART_MAIN, &lv_watch_font20);
    lv_obj_set_click(ext->title, false);
    lv_label_set_align(ext->title, LV_LABEL_ALIGN_CENTER);
    lv_label_set_text_id(ext->title, title_id);
#if defined(__XF_LCD_SIZE_128X128__)
    lv_obj_align(ext->title, obj, LV_ALIGN_IN_TOP_MID, 0, 3);
#elif defined(__XF_LCD_STYLE_ROUND__)
	lv_obj_align(ext->title, obj, LV_ALIGN_IN_TOP_MID, 0, 10);
#else
    lv_obj_align(ext->title, obj, LV_ALIGN_IN_TOP_MID, 0, 5);
#endif
	ws_printf("voice_friend_add_choice_phone_number_create~~~~~~~~");
    //phone number text area
		//phone number text area
		ext->ta = lv_textarea_create(obj, NULL);
	//文本框样式？
		lv_textarea_set_cursor_hidden(ext->ta, true);
		lv_textarea_set_one_line(ext->ta, true);
		lv_textarea_set_max_length(ext->ta, 11);   //phone number
		lv_obj_add_style(ext->ta, LV_TEXTAREA_PART_BG, &lv_style_transp);
		lv_obj_set_style_local_bg_opa(ext->ta, LV_TEXTAREA_PART_BG, LV_STATE_DEFAULT, LV_OPA_TRANSP);
		lv_obj_set_style_local_pad_ver(ext->ta, LV_TEXTAREA_PART_BG, LV_STATE_DEFAULT, 2);
		lv_obj_set_style_local_text_font(lv_textarea_get_label(ext->ta),LV_LABEL_PART_MAIN,LV_STATE_DEFAULT, LV_THEME_WATCH_NIGHT_FONT_20);
		lv_obj_set_style_local_text_letter_space(lv_textarea_get_label(ext->ta), LV_LABEL_PART_MAIN, LV_STATE_DEFAULT, 2);
	
		lv_textarea_set_text(ext->ta, "");
		lv_textarea_set_text_align(ext->ta, LV_LABEL_ALIGN_CENTER);
		lv_textarea_set_pwd_mode(ext->ta, false);  //true
		lv_obj_set_pos(ext->ta, DIAL_PAD_TA_X, DIAL_PAD_TA_Y);
		lv_obj_set_size(ext->ta, DIAL_PAD_TA_W, DIAL_PAD_TA_H);
	
		lv_watch_obj_add_element(ext->ta);
	
		// hightlight circle
		ext->cont_circle = lv_cont_create(obj, NULL);
		lv_obj_set_size(ext->cont_circle, DIAL_PAD_HIGHTLIGHT_WIDTH, DIAL_PAD_HIGHTLIGHT_HEIGHT);
		lv_obj_set_hidden(ext->cont_circle, true);
		lv_obj_set_click(ext->cont_circle, false);
		lv_obj_set_style_local_bg_color(ext->cont_circle, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_COLOR_SILVER);
		lv_obj_set_style_local_bg_opa(ext->cont_circle, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_50);
		lv_obj_set_style_local_radius(ext->cont_circle, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 100);
		lv_obj_set_style_local_border_width(ext->cont_circle, LV_OBJ_PART_MAIN, LV_STATE_DEFAULT, 0);
		
		//button container
		lv_obj_t * cont_btn = lv_cont_create(obj, NULL);
		lv_obj_set_size(cont_btn, DIAL_PAD_CONT_W, DIAL_PAD_CONT_H);
		lv_cont_set_layout(cont_btn, LV_LAYOUT_GRID);
#if defined(__XF_LCD_STYLE_ROUND__)
		lv_obj_align(cont_btn, obj, LV_ALIGN_IN_TOP_LEFT, (LV_HOR_RES-DIAL_PAD_CONT_W)/2, DIAL_PAD_TA_Y+DIAL_PAD_TA_H);
#else
		lv_obj_align(cont_btn, ext->ta, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 0);
#endif
		lv_obj_set_click(cont_btn, false);
		lv_watch_obj_add_element(cont_btn);
		lv_obj_set_style_local_bg_opa(cont_btn, LV_GAUGE_PART_MAIN, LV_STATE_DEFAULT, LV_OPA_0);
		lv_obj_set_style_local_pad_top(cont_btn, LV_GAUGE_PART_MAIN, LV_STATE_DEFAULT, 0);
		lv_obj_set_style_local_pad_bottom(cont_btn, LV_GAUGE_PART_MAIN, LV_STATE_DEFAULT, 0);
		lv_obj_set_style_local_pad_left(cont_btn, LV_GAUGE_PART_MAIN, LV_STATE_DEFAULT, 0);
		lv_obj_set_style_local_pad_right(cont_btn, LV_GAUGE_PART_MAIN, LV_STATE_DEFAULT, 0);
		lv_obj_set_style_local_pad_inner(cont_btn, LV_GAUGE_PART_MAIN, LV_STATE_DEFAULT, 0);
		lv_obj_set_style_local_border_width(cont_btn, LV_GAUGE_PART_MAIN, LV_STATE_DEFAULT, 0);
	
		//button
		for(int8_t i = 0; i < DIAL_PAD_NUM; i++) {
			btn = lv_btn_create(cont_btn, NULL);
			lv_obj_set_size(btn, (DIAL_PAD_CONT_W) / DIAL_PAD_X_NUM, (DIAL_PAD_CONT_H) / DIAL_PAD_Y_NUM);
			lv_obj_add_style(btn, LV_OBJ_PART_MAIN, &lv_watch_style_transp);
			lv_obj_set_event_cb(btn, voice_friend_add_phone_number_input_btn_event_cb);
			
			img = lv_img_create(btn, NULL);
#if defined(__XF_LCD_SIZE_128X128__)
			lv_img_set_src(img, &password_btn128);
#elif defined(__XF_LCD_STYLE_ROUND__)
			lv_img_set_src(img, &password_round_btn);
#else
			lv_img_set_src(img, &password_btn);
#endif
			lv_obj_set_click(img, false);
			lv_watch_set_free_num(btn, i);
			lv_watch_obj_add_element(btn);
		}

    return obj;
}



void voice_msg_create_event_cb(lv_obj_t * btn, lv_event_t e)
{
    (void)btn;
    if(LV_EVENT_CLICKED == e) {
        lv_watch_png_cache_all_free();

		if(!MMI_ModemAdp_WS_Is_Online())
		{
			tip_content_create(NULL,WATCH_TEXT_ID_BINDAPP_FIRST);
			return ;
		}
		
		if(!MMI_ModemAdp_WS_Is_HomeSchool_Has_LoginInfo())
		{
			char showtxt[128]={0};
			#if USE_LV_WATCH_VOICE_KAER_YNYD != 0
			if(strlen(MMI_ModemAdp_WS_HomeSchool_Login_Failmsg()) == 0)
			{
				tip_content_create_by_text(NULL,(char *)lv_lang_get_text(WATCH_TEXT_ID_QQ_LOGGING_IN));
				return ;
			}
			#endif
			memcpy(showtxt,MMI_ModemAdp_WS_HomeSchool_Login_Failmsg(),127);
			tip_content_create_by_text(NULL,showtxt);
			return ;
		}
	
        lv_obj_t * obj = voice_msg_create(NULL, 0, NULL);
        LV_ASSERT_MEM(obj);
    }
}

void voice_session_msg_create_btn_cb(lv_obj_t * btn, lv_event_t e)
{
    (void)btn;
    if(LV_EVENT_CLICKED == e) {
        lv_watch_png_cache_all_free();
		
		if(!MMI_ModemAdp_WS_Is_Online())
		{
			tip_content_create(NULL,WATCH_TEXT_ID_BINDAPP_FIRST);
			return ;
		}
		
		if(!MMI_ModemAdp_WS_Is_HomeSchool_Has_LoginInfo())
		{
			char showtxt[128]={0};
			#if USE_LV_WATCH_VOICE_KAER_YNYD != 0
			if(strlen(MMI_ModemAdp_WS_HomeSchool_Login_Failmsg()) == 0)
			{
				tip_content_create_by_text(NULL,(char *)lv_lang_get_text(WATCH_TEXT_ID_QQ_LOGGING_IN));
				return ;
			}
			#endif
			memcpy(showtxt,MMI_ModemAdp_WS_HomeSchool_Login_Failmsg(),127);
			tip_content_create_by_text(NULL,showtxt);
			return ;
		}
	
        lv_obj_t * obj = voice_session_msg_create(NULL, 0, NULL);
        LV_ASSERT_MEM(obj);
    }
}

static void voice_msg_msg_incoming_over_cb(void * para)
{
    Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
}

void app_adaptor_voice_msg_handle_rcv_ind(app_adaptor_voice_msg_t * msg)
{
    if(NULL == msg) return;
    if(NULL == msg->msg) return;
    if(NULL == msg->name) {
        if(WATCH_VOICE_MSG_FAMILY_GROUP_CHAT == msg->chat_type) {
            msg->name = voice_msg_copy_text(WATCH_TEXT_ID_FAMILY_GROUP);
        } else if(WATCH_VOICE_MSG_FRIENDS_GROUP_CHAT == msg->chat_type) {
            msg->name = voice_msg_copy_text(WATCH_TEXT_ID_FRIENDS_GROUP);
        }
    }

    printf("%s: chat type %d, addcontent:%d,rcv msg from ...\n", __FUNCTION__, msg->chat_type, msg->addcontent);
    lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG);
    lv_voice_msg_chat_ext_t * chat_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
    lv_voice_msg_incoming_ext_t * incom_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
    lv_voice_msg_ext_t * sess_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_SESSION);
	
    lv_ll_t * contact_list = NULL;
    //voice_msg_contact_t ** cur_contact = NULL;
    bool ring_enable = true;
	
    printf("%s: msg->msg->type %d, rcv msg from ...\n", __FUNCTION__, msg->msg->type);
	
	if((msg->msg->type==WATCH_VOICE_MSG_SET_CONTACT_LIST)||(msg->msg->type==WATCH_VOICE_MSG_SET_SESSION_LIST) ||(msg->msg->type==WATCH_VOICE_MSG_SET_SESSION_VOICE_UPLOAD))
	{
		ring_enable=false;
	}
	
	if((msg->chat_type==WATCH_VOICE_MSG_SINGLE_CHAT)||(msg->chat_type==WATCH_VOICE_MSG_FRIENDS_GROUP_CHAT))
	{
		if(msg->addcontent==0)
			ring_enable=false;
	}

	if(WatchUtility_IsMuteTime()) 
	{
		ring_enable=false;
	}

	
    printf("%s: ring_enable: %d \n", __FUNCTION__,ring_enable);
	
    if(chat_ext) {
        contact_list = chat_ext->contact_list;
    } else if(ext) {
		printf("%s:  in ACT_ID_VOICE_MSG ...\n", __FUNCTION__);
		if(ext->contact_list){
			_lv_ll_clear(ext->contact_list);
			lv_mem_free(ext->contact_list);
		}
		
		ext->contact_list=voice_msg_read_nvm();
		contact_list = ext->contact_list;
    } else if(sess_ext) {
		printf("%s:  in ACT_ID_VOICE_MSG_SESSION ...\n", __FUNCTION__);
		if(sess_ext->contact_list){
			_lv_ll_clear(sess_ext->contact_list);
			lv_mem_free(sess_ext->contact_list);
		}
		//recreate
		sess_ext->contact_list=voice_session_msg_read_nvm();
		contact_list=sess_ext->contact_list;
		
    } else if(incom_ext) {
        contact_list = incom_ext->contact_list;
        //cur_contact = &incom_ext->cur_contact;
    } else {
		if(false == watch_is_ready()) {
            printf("%s: rcv msg during power off, not display new msg interface\n", __FUNCTION__);
            //voice_msg_update_nvm(msg);
            voice_msg_free_msgs(msg);
            return;
        } else {
			printf("%s: --line:%d\n", __FUNCTION__,__LINE__);
            //contact_list = voice_msg_read_nvm();
        }
    }


    if(chat_ext) {
		printf("%s: %s,%s\n", __FUNCTION__,chat_ext->cur_contact->imei,msg->imei);
		if(strcmp(chat_ext->cur_contact->imei,msg->imei)==0)
		{
			voice_msg_add_new_msg_to_chat(msg,ring_enable);
		}
		else{
			printf("%s: in chat ,other session id ,add content\n", __FUNCTION__);
		}
	
    } else if(ext) {
		if(ext->contact_list)
        	voice_msg_update_contact_list(ext,0);
    } else if(sess_ext) {
		if(sess_ext->contact_list)
        	voice_msg_update_contact_list(sess_ext,1);
    } else {
    		if(NULL == incom_ext) {
				if((phone_get_call_state() == MMI_MODEM_CALL_STATE_NULL)
		           	#if USE_LV_WATCH_VIDEOCALL !=0
					&& (get_voipcall_params_call_state() == MMI_VOIP_CALL_STATE_NULL)
					#endif
					#if USE_LV_WATCH_WS_DUER != 0
					&& !is_duer_activity()
					#endif
					#if USE_LV_WATCH_ALARM
					&&(get_alarm_is_ringing()==false)
					#endif
				&& !WatchUtility_IsMuteTime())
				{
					if((msg->msg->type==WATCH_VOICE_MSG_SET_SESSION_CONTENT_ADD)||msg->addcontent)
					{

							voice_msg_contact_t *contact=(app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(voice_msg_contact_t));
							//printf("%s: standby --incoming imei:%s,name:%s\n", __FUNCTION__,msg->imei,msg->msg[0].name);
							memset(contact,0,sizeof(voice_msg_contact_t));
							strcpy(contact->name,msg->msg[0].name);
							strcpy(contact->imei,msg->imei);
							strcpy(contact->sessionId,msg->imei);
				            voice_msg_create_multi_unread_msgs(NULL, 0, contact_list, contact);
					}
				}
    	}
		else{
			
			if(contact_list){
				_lv_ll_clear(contact_list);
				lv_mem_free(contact_list);
			}

            lv_obj_t * incom_obj = voice_msg_get_obj(ACT_ID_VOICE_MSG_INCOMING);
       		voice_msg_update_multi_unread_msgs(incom_obj, 0);
			printf("%s: --line:%d\n", __FUNCTION__,__LINE__);

		}
    }
	
	bool already_moved = false;
	
    if(!watch_is_locked_screen(true, LCS_VMSG) &&(true == ring_enable) && (AUDIO_CTRL_PRIORITY_5 == Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_5,voice_msgincome_audio_ctrl_callback))) 
	{
        Voice_Msg_Play_Onetime(AUDIO_MSG, 0, query_current_volume(), NULL, NULL);
		#if 0//USE_LV_WATCH_VOICE_INCOMING_TRUN_OFF_LCD !=0
        voice_msg_lcd_wakeup();//Maybe need not turn on lcd ,play ring only! For power saving!
        #endif
    }else
    {
		Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
        lv_obj_t * incoming_activity_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_INCOMING);        
        if(incoming_activity_obj == lv_watch_get_top_activity_obj()){
			lv_obj_t * launcher_obj = lv_watch_get_activity_obj(ACT_ID_LAUNCHER);
            lv_obj_t * parent = lv_obj_get_parent(incoming_activity_obj);
            lv_obj_t * next_obj = _lv_ll_get_next(&parent->child_ll, incoming_activity_obj);
            if(next_obj && launcher_obj != next_obj) next_obj = _lv_ll_get_next(&parent->child_ll, next_obj);
            if(next_obj){
                lv_obj_move_before_obj(incoming_activity_obj, next_obj);
            }else{
                lv_obj_move_before_obj(incoming_activity_obj, launcher_obj);
            }
			already_moved = true;
        }
    }
	
#if USE_WATCH_FACE_UNLOCK != 0
	if ((lv_watch_get_activity_obj(ACT_ID_APP_FACE_UNLOCK)||lv_watch_get_activity_obj(ACT_ID_APP_FACE_INPUT)\
		||lv_watch_get_activity_obj(ACT_ID_INPUT_PASSWORD)) && !already_moved)
	{
        lv_obj_t * incoming_activity_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_INCOMING);        
        if(incoming_activity_obj == lv_watch_get_top_activity_obj()){
			lv_obj_t * launcher_obj = lv_watch_get_activity_obj(ACT_ID_LAUNCHER);
            lv_obj_t * parent = lv_obj_get_parent(incoming_activity_obj);
            lv_obj_t * next_obj = _lv_ll_get_next(&parent->child_ll, incoming_activity_obj);
            if(next_obj && launcher_obj != next_obj) next_obj = _lv_ll_get_next(&parent->child_ll, next_obj);
            if(next_obj){
                lv_obj_move_before_obj(incoming_activity_obj, next_obj);
            }else{
                lv_obj_move_before_obj(incoming_activity_obj, launcher_obj);
            }
        }
	}
#endif

    watch_locked_screen_update();

    voice_msg_free_msgs(msg);
}

void app_adaptor_voice_msg_rcv_ind(app_adaptor_voice_msg_t * voice_msg)
{
#if USE_LV_WATCH_MODEM_ADAPTOR
    mmi_msg_app_adaptor_voice_msg_rcv_ind_t * msg;
    msg = (mmi_msg_app_adaptor_voice_msg_rcv_ind_t *)lv_mem_alloc(sizeof(mmi_msg_app_adaptor_voice_msg_rcv_ind_t));
    msg->header.MsgId = MMI_APP_ADAPTOR_VOICE_MSG_RCV_IND;
    msg->msg = voice_msg;

    Hal_Send_Message((TASK_ID)gui_task_id, (void *)msg);
#else /*for PC simulator*/
    app_adaptor_voice_msg_handle_rcv_ind(voice_msg);
#endif
}

void app_adaptor_voice_msg_handle_send_cnf(watch_app_adp_voice_msg_result_t result)
{
    printf("%s\n", __FUNCTION__);

    lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_SEND);
    if(NULL == activity_obj) return;

    printf("%s: VOICE_MSG_SEND\n", __FUNCTION__);
    lv_obj_t * obj;
    lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &obj);
    if(NULL == obj) return;

    lv_obj_t * img_bkg = lv_obj_get_child_back(obj, NULL);
    if(NULL == img_bkg) return;

    lv_obj_t * img_sending = lv_obj_get_child_back(img_bkg, NULL);
    if(NULL == img_sending) return;
    lv_obj_del(img_sending);

    if(WATCH_VOICE_MSG_RESULT_SUCCESS == result) {
        lv_img_set_src(img_bkg, ICON_SND_VOICE_SUCC);
    } else if(WATCH_VOICE_MSG_RESULT_FAILURE == result) {
        lv_img_set_src(img_bkg, ICON_SND_VOICE_FAILED);
    } else {
        return;
    }

#if USE_LV_WATCH_MULTI_LANG_SUPPORT != 0
    lv_obj_t * label = lv_label_create(img_bkg, NULL);
    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
    lv_obj_set_click(label, false);
    lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font20_black);
    if(WATCH_VOICE_MSG_RESULT_SUCCESS == result) {
        lv_label_set_text_id(label, WATCH_TEXT_ID_MSG_SEND_SUCCESS);
    } else if(WATCH_VOICE_MSG_RESULT_FAILURE == result) {
        lv_label_set_text_id(label, WATCH_TEXT_ID_MSG_SEND_FAILDED);
    }
    lv_label_set_long_mode(label, LV_LABEL_LONG_BREAK);
    lv_obj_set_width(label, LV_HOR_RES_MAX*7/8);
    #if defined(__XF_LCD_STYLE_ROUND__)
    lv_obj_align(label, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, -30);
    #elif defined(__XF_LCD_SIZE_128X128__)
    lv_obj_align(label, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, -10);
    #else 
    lv_obj_align(label, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, -20);
    #endif
#endif

    lv_anim_t a = {};
    lv_anim_init(&a);
    lv_anim_set_var(&a, activity_obj);
    lv_anim_set_ready_cb(&a, (lv_anim_ready_cb_t)voice_msg_send_end_anim);
    lv_anim_set_time(&a, 2000);
    lv_anim_start(&a);
}



void app_adaptor_voice_msg_send_cnf(watch_app_adp_voice_msg_result_t result)
{
#if USE_LV_WATCH_MODEM_ADAPTOR
    mmi_msg_app_adaptor_voice_msg_send_cnf_t * msg;
    msg = (mmi_msg_app_adaptor_voice_msg_send_cnf_t *)lv_mem_alloc(sizeof(mmi_msg_app_adaptor_voice_msg_send_cnf_t));
    msg->header.MsgId = MMI_APP_ADAPTOR_VOICE_MSG_SEND_CNF;
    msg->result = result;

    Hal_Send_Message((TASK_ID)gui_task_id, (void *)msg);
#else /*for PC simulator*/
    app_adaptor_voice_msg_handle_send_cnf(result);
#endif
}




void voice_msg_touch_call_signal(lv_obj_t * img, lv_event_t e)
{
#if USE_LV_WATCH_WS_ZNSH_FRIEND_CALL != 0
    if(LV_EVENT_CLICKED == e) {
        lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
        uint8_t length = strlen(ext->cur_contact->number) + 1;
        if(length <= 1){
            tip_content_create(NULL, WATCH_TEXT_ID_FRIEND_PHONE_NUMBER_NULL);
            return LV_RES_OK;
        }
		if(phone_is_monitor_on())
		{
			tip_content_create(lv_watch_get_top_activity_obj(),WATCH_TEXT_ID_MONITOR_ON);
			return LV_RES_OK;
		}
        char * number = (char *)lv_mem_alloc(length);
        memcpy(number, ext->cur_contact->number, length);
        printf("%s: number = %s\n", __FUNCTION__, number);
        phone_voice_call_req(number);
    } 
#endif 
    
}


#if USE_LV_WATCH_WS_ZNSH_FRIEND_VCALL
char friend_name[NV_VCALL_MAX_NAME_LEN];
#endif

void voice_msg_touch_vcall_signal(lv_obj_t * img, lv_event_t e)
{
#if USE_LV_WATCH_WS_ZNSH_FRIEND_VCALL != 0
		if(LV_EVENT_CLICKED == e) {
			lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
			uint8_t length = strlen(ext->cur_contact->number) + 1;
			char * number = (char *)lv_mem_alloc(length);
			memcpy(number, ext->cur_contact->number, length);
			
			if(voip_check_mo_permission()) {
				voip_phone_voice_call_req(number, MODEM_VIDEO_CALL_TYPE_VIDEO);
			} else {
				voip_call_fail_ind(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT));
			}
		} 
		
#endif 
    return LV_RES_OK;
}


void voice_msg_send_emo_event_cb(lv_obj_t * img, lv_event_t e){
    if(LV_EVENT_CLICKED == e) {
        lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
        voice_msg_chat_stop_voice(ext);
        voice_msg_create_expression(NULL);
    }
}


void voice_msg_touch_spk_event_cb(lv_obj_t * img, lv_event_t e)
{
	//printf("%s: e:%d\n", __FUNCTION__, e);

    if(LV_EVENT_LONG_PRESSED == e) {
		
#if USE_LV_WATCH_MODE_SWITCH != 0
		if(get_flightmode_state())
		{
			tip_content_create(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT),WATCH_TEXT_ID_FLIGHTMODE_ON);
			return LV_RES_OK;
		}
		else if(get_powersaving_state())
		{
			tip_content_create(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT),WATCH_TEXT_ID_POWERSAVE_ON);
			return LV_RES_OK;
		}
#endif	
		if(!MMI_ModemAdp_WS_Is_Online())
		{
			#if USE_LV_WATCH_LIHAO_DUER_REMINDER !=0
			tip_content_create(lv_watch_get_activity_obj(ACT_ID_VOIP_PHONEBOOK),WATCH_TEXT_ID_BINDAPP_FIRST);
			#else
			tip_content_create(lv_watch_get_activity_obj(ACT_ID_VOIP_PHONEBOOK),WATCH_TEXT_ID_SERVER_OFFLINE);
			#endif
			return LV_RES_OK;
		}

		if(phone_is_monitor_on())
		{
			tip_content_create(lv_watch_get_activity_obj(ACT_ID_PHONEBOOK),WATCH_TEXT_ID_MONITOR_ON);
			return LV_RES_OK;
		}
        lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
        LV_ASSERT_MEM(ext);

        if(0 == ext->old_ext.element_click_en) {
            /*sliding to the right, not click button*/
            return;
        }

        if(voice_msg_out_of_memory_check())
        {
            return LV_RES_OK;
        }

        voice_msg_set_slide_enable(&ext->old_ext, false); /*prevent to slide out while recording*/
        voice_msg_chat_stop_voice(ext);
        voice_msg_create_speak();
        ext->buffer = (uint8_t *)lv_mem_alloc(VOICE_MSG_MAX_RECORD_SIZE);
        Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_5,voice_msg_audio_ctrl_callback);
#ifdef BUILD_IN_PC_SIMULATOR /*for pc test*/
        voice_msg_test_record_buffer_start_req(ext->buffer,
                                               VOICE_MSG_MAX_RECORD_SIZE,
                                               VOICE_MSG_MAX_RECORD_DURATION * 1000,
                                               voice_msg_chat_record_cb);
#else
        Hal_Record_Buffer_Start_Req(ext->buffer,
                                    VOICE_MSG_MAX_RECORD_SIZE,
                                    VOICE_MSG_MAX_RECORD_DURATION * 1000,
                                    voice_msg_chat_record_cb);
#endif
    } else if(LV_EVENT_CLICKED == e || LV_EVENT_PRESS_LOST == e) {
        if(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_SPEAK)) {
            lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
            LV_ASSERT_MEM(ext);

            voice_msg_set_slide_enable(&ext->old_ext, true); /*reset slide_enable at the end of recording*/
#if VOICE_MSG_SLIDE_UP_TO_CANCEL_RECORDING != 0
            lv_indev_t * indev = lv_indev_get_act();
            if(indev) {
                lv_coord_t point_y = indev->proc.types.pointer.last_point.y;
                if(point_y < img->coords.y1) {
                    printf("%s: img y %d, last point %d\n", __FUNCTION__, img->coords.y1, point_y);
                    /*last point is above img, consider as sliding up and cancel recording*/
                    if(ext->buffer) {
                        voice_msg_record_buffer_stop_req();
                        lv_mem_free(ext->buffer);
                        ext->buffer = NULL;
                        voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);
                        Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
                    }
                }
            }
#endif

            bool result = voice_msg_send_voice(ext);
            voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);
            Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
            if(result) voice_msg_create_send(NULL);
        }
    }
}


bool voice_msg_on_mute_time()
{
    voice_msg_mt_call_ind();

    return true;
}


void voice_msg_mt_call_ind(void)
{
    voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);

    lv_voice_msg_chat_ext_t * chat_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
    if(chat_ext) {
        printf("%s: ACT_ID_VOICE_MSG_CHAT\n", __FUNCTION__);
        voice_msg_chat_stop_voice(chat_ext);
		voice_msg_set_slide_enable(&chat_ext->old_ext, true); /*reset slide_enable at the end of recording*/
        /*not send voice*/
        if(chat_ext->buffer) {
#if 0
            uint32_t duration_ms;
            uint32_t size;
            Hal_Record_Buffer_Stop_Req(&size, &duration_ms);
            lv_mem_free(chat_ext->buffer);
            chat_ext->buffer = NULL;
#else
            voice_msg_send_voice(chat_ext);
#endif
        }
    }

#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
    lv_voice_msg_incoming_ext_t * incoming_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
    if(incoming_ext) {
        printf("%s: ACT_ID_VOICE_MSG_INCOMING\n", __FUNCTION__);
        if(true == incoming_ext->voice_playing) {
            if(0 == incoming_ext->voice_size) {
                Hal_File_Play_End();
            } else {
                Hal_Tone_Play_End();
            }
            lv_anim_del(incoming_ext->img_voice_anim, NULL);
            lv_img_set_src(incoming_ext->img_voice_anim, &icon_playing3);
            lv_obj_set_free_num(incoming_ext->img_voice_anim, 0);
        }
    }
#endif
}

void voice_msg_audio_ctrl_callback(AUDIO_CTRL_PRIORITY priority)
{
    printf("%s,priority is %d\n", __FUNCTION__,priority);
    if(AUDIO_CTRL_PRIORITY_5 == priority)
    {
        if(false == watch_is_ready()) {
            printf("%s: not handle voice msg during power off\n", __FUNCTION__);
            return;
        }
        voice_msg_high_priority_task_end();
    }
    else if(AUDIO_CTRL_PRIORITY_5 > priority)
    {
        voice_msg_mt_call_ind();
        Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
    }
}

void voice_msgincome_audio_ctrl_callback(AUDIO_CTRL_PRIORITY priority)
{
    printf("%s,priority is %d\n", __FUNCTION__,priority);
    if(AUDIO_CTRL_PRIORITY_5 == priority)
    {
        if(false == watch_is_ready()) {
            printf("%s: not handle voice msg during power off\n", __FUNCTION__);
            return;
        }
        voice_msg_high_priority_task_end();
    }
    else if(AUDIO_CTRL_PRIORITY_5 > priority)
    {
        voice_msg_mt_call_ind();
        Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
    }
}

#if 0
void voice_msg_call_end(void)
{
    if(false == watch_is_ready()) {
        printf("%s: not handle voice msg during power off\n", __FUNCTION__);
        return;
    } else if(voice_msg_is_higher_prio_task_present(ACT_ID_PHONE)) {
        printf("%s: not handle voice msg during other higher priority task\n", __FUNCTION__);
        return;
    }

    voice_msg_high_priority_task_end();
}

void voice_msg_alarm_end(void)
{
    printf("%s\n", __FUNCTION__);

    if(false == watch_is_ready()) {
        printf("%s: not handle voice msg during power off\n", __FUNCTION__);
        return;
    } else if(voice_msg_is_higher_prio_task_present(ACT_ID_ALARM)) {
        printf("%s: not handle voice msg during other higher priority task\n", __FUNCTION__);
        return;
    }

    voice_msg_high_priority_task_end();
}

void voice_msg_speech_recog_end(void)
{
    printf("%s\n", __FUNCTION__);

    if(false == watch_is_ready()) {
        printf("%s: not handle voice msg during power off\n", __FUNCTION__);
        return;
    }

    voice_msg_high_priority_task_end();
}

void voice_msg_mt_call_ind(void)
{
    voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);

    lv_voice_msg_chat_ext_t * chat_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
    if(chat_ext) {
        printf("%s: ACT_ID_VOICE_MSG_CHAT\n", __FUNCTION__);
        voice_msg_chat_stop_voice(chat_ext);

        /*not send voice*/
        if(chat_ext->buffer) {
            voice_msg_set_slide_enable(&chat_ext->old_ext, true); /*reset slide_enable at the end of recording*/
#if 0
            uint32_t duration_ms;
            uint32_t size;
            Hal_Record_Buffer_Stop_Req(&size, &duration_ms);
            lv_mem_free(chat_ext->buffer);
            chat_ext->buffer = NULL;
#else
            voice_msg_send_voice(chat_ext);
#endif
        }
    }

#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
    lv_voice_msg_incoming_ext_t * incoming_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
    if(incoming_ext) {
        printf("%s: ACT_ID_VOICE_MSG_INCOMING\n", __FUNCTION__);
        if(true == incoming_ext->voice_playing) {
            if(0 == incoming_ext->voice_size) {
                Hal_File_Play_End();
            } else {
                Hal_Tone_Play_End();
            }
            lv_anim_del(incoming_ext->img_voice_anim, NULL);
            lv_img_set_src(incoming_ext->img_voice_anim, &icon_playing3);
            lv_obj_set_free_num(incoming_ext->img_voice_anim, 0);
        }
    }
#endif
}

void voice_msg_alarm_ind(void)
{
    printf("%s\n", __FUNCTION__);
    voice_msg_mt_call_ind();
}
#endif

void voice_msg_shut_down(void)
{
    printf("%s\n", __FUNCTION__);

    lv_voice_msg_incoming_ext_t * incom_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
    if(incom_ext) {
        voice_msg_write_nvm(incom_ext->contact_list);
        return;
    }
    lv_voice_msg_chat_ext_t * chat_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
    if(chat_ext) {
        voice_msg_write_nvm(chat_ext->contact_list);
    } else {
        lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG);
        if(ext) {
            voice_msg_write_nvm(ext->contact_list);
        }
    }
}

void * voice_msg_get_ext(lv_watch_Activity_Id_t actId)
{
    lv_obj_t * obj = voice_msg_get_obj(actId);
    if(NULL == obj) return NULL;

    if(obj) {
        void * ext = lv_obj_get_ext_attr(obj);
        return ext;
    }
    return NULL;
}

uint32_t voice_msg_read_file_size(char * file_name)
{
    printf("%s: read file %s\n", __FUNCTION__, file_name);

    uint32_t size = 0;
    ui_file_t * pfile = UI_FILE_OPEN((const char *)file_name, UI_FILE_READ_ONLY);
    if(NULL == pfile->file_d) {
        printf("%s: please check file %s\n", __FUNCTION__, file_name);
    } else {
        if(LV_FS_RES_OK != UI_FILE_SIZE(pfile, &size)) {
            printf("%s: read file size %d failure\n", __FUNCTION__, size);
            size = 0;
        }
        UI_FILE_CLOSE(pfile);
    }

    return size;
}

void * voice_msg_read_file_data(char * file_name, uint8_t msg_type, uint32_t * data_size)
{
    printf("%s: read file %s\n", __FUNCTION__, file_name);

    void * data = NULL;
    uint32_t size = 0;

    ui_file_t * pfile = UI_FILE_OPEN((const char *)file_name, UI_FILE_READ_ONLY);
    if(NULL == pfile->file_d) {
        printf("%s: please check file\n", __FUNCTION__);
    } else {
        if(LV_FS_RES_OK == UI_FILE_SIZE(pfile, &size)) {
            if(VOICE_MSG_TYPE_TEXT == msg_type) {
                data = lv_mem_alloc(size + 1);
                memset(data, 0, (size + 1));
            } else {
                data = lv_mem_alloc(size);
            }
            UI_FILE_SEEK(pfile, SEEK_SET);
            UI_FILE_READ(pfile, data, size);
        } else {
            printf("%s: read file size %d failure\n", __FUNCTION__, size);
            size = 0;
        }
        UI_FILE_CLOSE(pfile);
    }

    if(data_size) {
        *data_size = size;
    }

    return data;
}

void voice_msg_fill_chat_id(app_adaptor_voice_msg_chat_id_t * id, voice_msg_contact_t * contact)
{
    id->name = NULL;
    id->number = NULL;
    id->imei = NULL;
    id->index = contact->index;
	printf("%s, index:%d,imei:%s",__func__,contact->index,contact->imei);
	
    if(VOICE_MSG_FAMILY_GROUP == contact->index) {
        id->chat_type = WATCH_VOICE_MSG_FAMILY_GROUP_CHAT;
        id->name = voice_msg_copy_text(WATCH_TEXT_ID_FAMILY_GROUP);
    } else if(VOICE_MSG_FRIENDS_GROUP == contact->index) {
        id->chat_type = WATCH_VOICE_MSG_FRIENDS_GROUP_CHAT;
        id->name = voice_msg_copy_text(WATCH_TEXT_ID_FRIENDS_GROUP);
    } else {
        id->chat_type = WATCH_VOICE_MSG_SINGLE_CHAT;
        uint8_t text_len = strlen(contact->name) + 1;
        id->name = (char *)lv_mem_alloc(text_len);
        memcpy(id->name, contact->name, text_len);
        text_len = strlen(contact->number) + 1;
        id->number = (char *)lv_mem_alloc(text_len);
        memcpy(id->number, contact->number, text_len);
        text_len = strlen(contact->imei) + 1;
        id->imei = (char *)lv_mem_alloc(text_len);
        memcpy(id->imei, contact->imei, text_len);
    }
}

void voice_msg_send_photo(void * buf, uint32_t size, voice_msg_contact_t * contact)
{
    printf("%s: buf %p, size %d\n", __FUNCTION__, buf, size);

    if((NULL == buf) || (0 == size) || (NULL == contact)) {
        return;
    }

    printf("%s: name %s, number %s, mark %d\n",
           __FUNCTION__, contact->name, contact->number, contact->mark);

    voice_msg_create_send(NULL);

    app_adaptor_voice_msg_t * photo_msg = (app_adaptor_voice_msg_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_t));
    memset(photo_msg, 0, sizeof(app_adaptor_voice_msg_t));
    app_adaptor_voice_msg_chat_id_t id;
    voice_msg_fill_chat_id(&id, contact);
    photo_msg->chat_type = id.chat_type;
    photo_msg->name = id.name;
    photo_msg->number = id.number;
    photo_msg->imei = id.imei;
    photo_msg->index = id.index;
    photo_msg->count = 1;
    photo_msg->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
    memset(photo_msg->msg, 0, sizeof(app_adaptor_voice_msg_info_t));
    photo_msg->msg->direction = WATCH_VOICE_MSG_FROM_UI;
    hal_rtc_t time;
    Hal_Rtc_Gettime(&time);
    photo_msg->msg->time.year = time.tm_year;
    photo_msg->msg->time.month = time.tm_mon;
    photo_msg->msg->time.day = time.tm_mday;
    photo_msg->msg->time.hour = time.tm_hour;
    photo_msg->msg->time.min = time.tm_min;
    photo_msg->msg->type = WATCH_VOICE_MSG_TYPE_PHOTO;
    photo_msg->msg->data_type = WATCH_VOICE_DATA_TYPE_BUFFER;
    photo_msg->msg->content.img.data_size = size;
    photo_msg->msg->content.img.data = (uint8_t *)lv_mem_alloc(size);
    memcpy(photo_msg->msg->content.img.data, buf, size);
    app_adaptor_voice_msg_send_req(photo_msg);

    /*delete voice msg*/
    voice_msg_del_interface(ACT_ID_VOICE_MSG);
}

void voice_msg_enlarge_photo(void * small_photo)
{
    voice_msg_create_enlarged_photo(small_photo);
}

void voice_msg_set_user_num(lv_obj_t * obj, uint32_t user_num)
{
    lv_obj_user_data_t * user_data = lv_obj_get_user_data_ptr(obj);
    user_data->user_num = user_num;
}

void voice_msg_set_user_ptr(lv_obj_t * obj, void * user_ptr)
{
    lv_obj_user_data_t * user_data = lv_obj_get_user_data_ptr(obj);
    user_data->user_data = user_ptr;
}

uint32_t voice_msg_get_user_num(lv_obj_t * obj)
{
    lv_obj_user_data_t * user_data = lv_obj_get_user_data_ptr(obj);
    return user_data->user_num;
}

void * voice_msg_get_user_ptr(lv_obj_t * obj)
{
    lv_obj_user_data_t * user_data = lv_obj_get_user_data_ptr(obj);
    return user_data->user_data;
}

void voice_msg_play_end(uint32_t len)
{
    printf("%s: len %d\n", __FUNCTION__, len);

#ifdef BUILD_IN_PC_SIMULATOR /*for pc test*/
    if(0 == len) {
        // file
        voice_msg_test_file_play_end();
    } else {
        // buffer
        voice_msg_test_tone_play_end();
    }
#else
#if USE_LV_WATCH_VOICE_MSG_YNYD == 0
    if(0 == len) {
        // file
        Hal_NFFS_File_Play_End();
    } 
	else
#endif
    {
#if USE_LV_WATCH_VOICE_MSG_YNYD == 0
        // buffer
        Hal_Tone_Play_End();
#endif
		// stream
		streaming_audioplayer_stop(false);
		
    }
#endif
}

void Voice_Msg_Play_Onetime(void * buffer, uint32_t len, HAL_SPEAKER_GAIN volume, CB_FUNC func,
                            void * para)
{
    printf("%s: len %d\n", __FUNCTION__, len);
    if(!func) func = voice_msg_msg_incoming_over_cb;
#ifdef BUILD_IN_PC_SIMULATOR /*for pc test*/
    if(0 == len) {
        // file
        voice_msg_test_file_play_onetime(buffer, volume, func, para);
    } else {
        // buffer
        voice_msg_test_tone_play_onetime(buffer, len, volume, func, para);
    }
#else
    if(0 == len) {
        // file
        Hal_NFFS_File_Play_Onetime(buffer, volume, func, para);
    } else {
        // buffer
        Hal_Media_Tone_Play_Onetime(buffer, len, volume, func, para);
    }
#endif
}

static uint32_t voice_msg_record_buffer_stop_req(void)
{
    uint32_t duration_ms;
    uint32_t size;
#ifdef BUILD_IN_PC_SIMULATOR /*for pc test*/
    voice_msg_test_record_buffer_stop_req(&size, &duration_ms);
#else
    Hal_Record_Buffer_Stop_Req(&size, &duration_ms);
#endif
    return size;
}

/**********************
 *   STATIC FUNCTIONS
 **********************/
static void voice_msg_set_slide_enable(lv_watch_obj_ext_t * ext, bool enable)
{
    printf("%s: enable %d\n", __FUNCTION__, enable);
    if(enable) {
        ext->slide_enable = 1; /*enable to slide to the right*/
    } else {
        ext->slide_enable = 0; /*disable to slide to the right*/
    }
}

static void voice_msg_lcd_wakeup(void)
{
    printf("%s\n", __FUNCTION__);
	
#if USE_WATCH_FACE_UNLOCK != 0
	if (get_faceunlock_state())
		return;
#endif
    if(0 == Hal_Pm_Get_State()) {
        Hal_Pm_WakeUp();
        watch_set_lcd_status(true);
    } else {
        watch_set_lcd_status(true);
        watch_wakeup_time_reset();
    }
}

static bool voice_msg_is_higher_prio_task_present(lv_watch_Activity_Id_t exception)
{
    if((ACT_ID_PHONE != exception)
            && (phone_is_monitor_on() || lv_watch_get_activity_obj(ACT_ID_PHONE))) {
        return true;
    } else if((ACT_ID_ALARM != exception) && lv_watch_get_activity_obj(ACT_ID_ALARM)) {
        return true;
    } else if((ACT_ID_VOIPPHONE != exception) && lv_watch_get_activity_obj(ACT_ID_VOIPPHONE)) {
        return true;
    } else if((ACT_ID_SPEECH_RECOG != exception) && lv_watch_get_activity_obj(ACT_ID_SPEECH_RECOG)) {
        return true;
    } else {
        return false;
    }
}

static bool voice_msg_compare_contacts(app_adaptor_voice_msg_t * msg, voice_msg_contact_t * contact)
{
    if(WATCH_VOICE_MSG_SINGLE_CHAT == msg->chat_type) {
        if(NULL != msg->imei && contact->imei != NULL)
        {
            if(0 == strcmp(msg->imei, contact->imei)) {
                return true;
            }
        }
        else if(NULL != msg->number && NULL != contact->number)
        {
            if(0 == strcmp(msg->number, contact->number)) {
                return true;
            }
        }
        else 
        {
            return false;
        }
    } else {
        if(0 == strcmp(msg->name, contact->name)) {
            return true;
        }
    }
    return false;
}

static char * voice_msg_copy_text(uint16_t text_id)
{
    const char * text = lv_lang_get_text(text_id);
    uint8_t len = strlen(text) + 1;
    char * str = (char *)lv_mem_alloc(len);
    memcpy(str, text, len);

    return str;
}

static nv_watch_voice_msg_t * voice_msg_read_index_nvm(void)
{
    uint32_t length = sizeof(nv_watch_voice_msg_t);
    nv_watch_voice_msg_t * nvm = (nv_watch_voice_msg_t *)lv_mem_alloc(length);
    if(length != UI_NV_Read_Req(NV_SECTION_UI_VOICE_MSG, 0, length, (uint8_t *)nvm)) {
        printf("read pb index from nvm error in voice_msg_read_index_nvm\n");
        lv_mem_free(nvm);
        return NULL;
    }

    return nvm;
}

static void friend_copy_contact(nv_watch_friend_info_t * dest, app_adaptor_friend_t * src)
{
    if((NULL == dest) || (NULL == src)) {
        printf("error, dest or src contact is NULL in friend_copy_contact\n");
    }
    memcpy(dest->name, src->friend_name, WATCH_CONTACTS_MAX_NAME_LEN);
    memcpy(dest->number, src->friend_number, WATCH_CONTACTS_MAX_NUMBER_LEN);
    memcpy(dest->imei, src->friend_imei, WATCH_CONTACTS_MAX_NUMBER_LEN);
    memcpy(dest->image, src->friend_image, WATCH_CONTACTS_MAX_NUMBER_LEN);
    dest->time = src->time;
    dest->contact_type = src->contact_type;
    dest->portrait_id = src->portrait_id;
    //dest->canVideo = src->canVideo;
}

static nv_watch_friends_t * voice_msg_read_pb_nvm(void)
{
    uint32_t length = sizeof(nv_watch_friends_t);
    nv_watch_friends_t * nvm_pb = (nv_watch_friends_t *)lv_mem_alloc(length);
    if(length != UI_NV_Read_Req(NV_SECTION_UI_FRIENDS, 0, length, (uint8_t *)nvm_pb)) {
        printf("read pb from nvm error in voice_msg_read_pb_nvm\n");
        lv_mem_free(nvm_pb);
        return NULL;
    }

    return nvm_pb;
}

static void voice_msg_write_pb_nvm(nv_watch_friends_t * nvm)
{
    if(NULL == nvm) return;

}


static lv_ll_t * voice_msg_read_nvm(void)
{
    lv_ll_t * contact_list = (lv_ll_t *)lv_mem_alloc(sizeof(lv_ll_t));
    _lv_ll_init(contact_list, sizeof(voice_msg_contact_t));
	
	printf("%s:   ...\n", __FUNCTION__);

#if 0 //yn_debug
	if(strlen(gYnContacts[0].name)==0){
	strncpy(gYnContacts[0].name, "acb", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnContacts[0].imei, "01223", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[0].sessionId, "01223", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[0].userId, "01223", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnContacts[0].portrait_id=3;
	
	strncpy(gYnContacts[1].name, "adddd", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnContacts[1].imei, "15544", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[1].sessionId, "15544", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[1].userId, "15544", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnContacts[1].portrait_id=2;
	
	strncpy(gYnContacts[2].name, "kuuhqh", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnContacts[2].imei, "66666", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[2].sessionId, "66666", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[2].userId, "66666", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnContacts[2].portrait_id=1;
	
	strncpy(gYnContacts[3].name, "khuh", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnContacts[3].imei, "688888", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[3].sessionId, "688888", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnContacts[3].userId, "688888", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnContacts[3].portrait_id=1;
	}
#endif


    voice_msg_contact_t * contact;
    for(uint8_t i = 0; i <= APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM; i++) {
        if(0 == strlen(gYnContacts[i].userId)) break;
        contact = _lv_ll_ins_tail(contact_list);
        contact->index = gYnContacts[i].index;
        strncpy(contact->name, gYnContacts[i].name, NV_CONTACTS_MAX_NAME_LEN);
        memset(contact->number, 0, NV_CONTACTS_MAX_NUMBER_LEN);
		memcpy(contact->imei, gYnContacts[i].sessionId, NV_CONTACTS_MAX_NUMBER_LEN);
		memcpy(contact->sessionId, gYnContacts[i].sessionId, NV_CONTACTS_MAX_NUMBER_LEN);
		memcpy(contact->userId, gYnContacts[i].userId, NV_CONTACTS_MAX_NUMBER_LEN);
        contact->type =gYnSessions[i].type;
        contact->mark =gYnContacts[i].mark;
		contact->isInsession=0;
		if(gYnContacts[i].portrait_id==3)
       	 contact->portrait_id =WATCH_PORTRAIT_ID_NO_INFO;
		else if(gYnContacts[i].portrait_id==2)
			contact->portrait_id =VOICE_MSG_PORTRAIT_ID_FAMILY_GROUP;
		else 
			contact->portrait_id = WATCH_PORTRAIT_ID_FATHER;
			
    }

    return contact_list;
}


static lv_ll_t * voice_session_msg_read_nvm(void)
{
    lv_ll_t * contact_list = (lv_ll_t *)lv_mem_alloc(sizeof(lv_ll_t));
    _lv_ll_init(contact_list, sizeof(voice_msg_contact_t));
	printf("%s:   ...\n", __FUNCTION__);

#if 0 //yn_debug //yn_debug
	if(strlen(gYnSessions[0].name)==0)
	{
	strncpy(gYnSessions[0].name, "aaa ", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnSessions[0].imei, "01223", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[0].sessionId, "01223", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[0].userId, "01223", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnSessions[0].portrait_id=1;
	
	strncpy(gYnSessions[1].name, "bbb ", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnSessions[1].imei, "15544", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[1].sessionId, "15544", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[1].userId, "15544", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnSessions[1].portrait_id=1;
	gYnSessions[1].mark=1;
	
	strncpy(gYnSessions[2].name, "ccc ", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnSessions[2].imei, "66666", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[2].sessionId, "66666", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[2].userId, "66666", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnSessions[2].portrait_id=1;
	gYnSessions[2].mark=5;
	
	strncpy(gYnSessions[3].name, "ddd 4", NV_CONTACTS_MAX_NAME_LEN);
	memcpy(gYnSessions[3].imei, "688888", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[3].sessionId, "688888", NV_CONTACTS_MAX_NUMBER_LEN);
	memcpy(gYnSessions[3].userId, "688888", NV_CONTACTS_MAX_NUMBER_LEN);
	gYnSessions[3].portrait_id=1;
	gYnSessions[3].mark=10;
	}
#endif


    voice_msg_contact_t * contact;
    for(uint8_t i = 0; i <= APP_ADAPTOR_VOICE_MSG_YN_MAX_CONTACTS_NUM; i++) {
        if(0 == strlen(gYnSessions[i].sessionId)) break;
        contact = _lv_ll_ins_tail(contact_list);
        contact->index = gYnSessions[i].index;
        strncpy(contact->name, gYnSessions[i].name, NV_CONTACTS_MAX_NAME_LEN);
        memset(contact->number, 0, NV_CONTACTS_MAX_NUMBER_LEN);
		memcpy(contact->imei, gYnSessions[i].sessionId, NV_CONTACTS_MAX_NUMBER_LEN);
		memcpy(contact->sessionId, gYnSessions[i].sessionId, NV_CONTACTS_MAX_NUMBER_LEN);
		memcpy(contact->userId, gYnSessions[i].userId, NV_CONTACTS_MAX_NUMBER_LEN);
        contact->mark =gYnSessions[i].mark;
        contact->type =gYnSessions[i].type;
		contact->isInsession=1;
		contact->portrait_id = WATCH_PORTRAIT_ID_CUST;
		WS_PRINTF("voice_session_msg_read_nvm [%d]=%s",i,contact->sessionId);
    }

    return contact_list;
}


static void voice_msg_write_nvm(lv_ll_t * contact_list)
{

}

static bool voice_msg_update_nvm(app_adaptor_voice_msg_t * msg)
{


    return true;
}

static void voice_msg_recover_buffed_msg(app_adaptor_voice_msg_t * msg)
{
    if(NULL == msg) return;
    if(NULL == msg->msg) return;
    if(NULL == msg->name) {
        if(WATCH_VOICE_MSG_FAMILY_GROUP_CHAT == msg->chat_type) {
            msg->name = voice_msg_copy_text(WATCH_TEXT_ID_FAMILY_GROUP);
        } else if(WATCH_VOICE_MSG_FRIENDS_GROUP_CHAT == msg->chat_type) {
            msg->name = voice_msg_copy_text(WATCH_TEXT_ID_FRIENDS_GROUP);
        }
    }

    printf("%s: chat type %d, rcv msg from %s\n", __FUNCTION__, msg->chat_type, msg->name);

    /*note: nvm has been updated in app_adaptor_voice_msg_rcv_ind*/

    lv_ll_t * contact_list = voice_msg_read_nvm();
    voice_msg_contact_t * node = _lv_ll_get_head(contact_list);
    voice_msg_contact_t * contact = NULL;
    uint16_t num_unread_msg = 0;
    while(node) {
        if(true == voice_msg_compare_contacts(msg, node)) {
            contact = node;
        }
        if(0xFF != node->mark) {
            num_unread_msg += node->mark;
        }
        node = _lv_ll_get_next(contact_list, node);
    }
    if(NULL == contact) {
        printf("%s: warning, contact not found\n", __FUNCTION__);
        voice_msg_free_msgs(msg);
        return;
    }

#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
    if(1 == num_unread_msg) {
        voice_msg_create_one_unread_msg(msg, contact_list, contact);
    } else {
		if (num_unread_msg > 0)
        	voice_msg_create_multi_unread_msgs(NULL, num_unread_msg, contact_list, contact);
    }
#else
	if (num_unread_msg > 0)
    	voice_msg_create_multi_unread_msgs(NULL, num_unread_msg, contact_list, contact);
#endif

    voice_msg_free_msgs(msg);
}

static void voice_msg_high_priority_task_end(void)
{
    lv_voice_msg_incoming_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
    bool wakeup = false;
    printf("%s: msg_buf %p, ext %p\n", __FUNCTION__, msg_buf, ext);
    if(ext) {
        if(ext->update) {
            wakeup = true;
            ext->update = false;
        }
    } else if(msg_buf) {
        voice_msg_recover_buffed_msg(msg_buf);
        msg_buf = NULL;
        wakeup = true;
    } else {
        return;
    }

    if(wakeup) {
        voice_msg_lcd_wakeup();
    }
}

static void voice_msg_update_contact_list(lv_voice_msg_ext_t * ext,bool is_sesslist)
{
    if(NULL == ext) return;

	if(ext->page==NULL)
	{
	ws_printf("voice_msg_update_contact_list---is_sesslist=%d-",is_sesslist);
		if(is_sesslist){
			lv_obj_t * obj = voice_msg_get_obj(ACT_ID_VOICE_MSG_SESSION);
			voice_msg_contacts_create(obj,1);
		}
		else{
			lv_obj_t * obj = voice_msg_get_obj(ACT_ID_VOICE_MSG);
			voice_msg_contacts_create(obj,0);
		}

		return ;
	}
	if(ext->label_tip)
	{
    	lv_label_set_text(ext->label_tip, "");
	}
	
    lv_obj_t * page_scrl = lv_page_get_scrl(ext->page);
    lv_obj_clean(page_scrl);
    voice_msg_contact_list_create(ext, lv_obj_get_y(page_scrl));
#if VOICE_MSG_SORT_CONTACTS_BY_MSG_TIME != 0
    lv_obj_t * btn = lv_obj_get_child_back(page_scrl, NULL);
    if(btn) {
        lv_page_focus(ext->page, btn, 0);
    }
#endif
}

static void voice_msg_chat_stop_voice(lv_voice_msg_chat_ext_t * ext)
{
    if(NULL == ext) return;
    if(NULL == ext->list) return;
    lv_voice_msg_list_ext_t * list_ext = lv_obj_get_ext_attr(ext->list);
    if(NULL == list_ext) return;
    if(list_ext->voice_playing_anim.var) {
        lv_obj_t * bubble_img = lv_obj_get_parent(list_ext->voice_playing_anim.var);
        lv_obj_t * len_label = lv_obj_get_child_back(bubble_img, list_ext->voice_playing_anim.var);
        uint32_t voice_size = voice_msg_get_user_num(len_label);
        printf("%s: voice_size %d\n", __FUNCTION__, voice_size);
        voice_msg_play_end(voice_size);
        Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
        lv_anim_del(list_ext->voice_playing_anim.var, NULL);
        if(VOICE_MSG_IS_MSG_FROM_SERVICE(voice_msg_get_user_num(bubble_img))) {
            lv_img_set_src(list_ext->voice_playing_anim.var, ICON_PLAYING3);
        } else {
            lv_img_set_src(list_ext->voice_playing_anim.var, ICON_MY_PLAYING3);
        }
        list_ext->voice_playing_anim.var = NULL;
    }
}


static void voice_msg_common_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);
    lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);

    if( ext && ext->buffer) {
        uint32_t duration_ms;
        uint32_t size;
        Hal_Record_Buffer_Stop_Req(&size, &duration_ms);
        lv_mem_free(ext->buffer);
        ext->buffer = NULL;
    }

#if USE_LV_WATCH_MEDIA_KEY != 0
	    activity_obj->handle_key_ptr = NULL;
#endif
	
    lv_watch_png_cache_all_free();
}

static void voice_msg_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    if(lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_CHAT)) {
        printf("%s: error, delete ACT_ID_VOICE_MSG before ACT_ID_VOICE_MSG_CHAT\n", __FUNCTION__);
        return;
    }

    lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG);
    if(NULL == ext) return;
    if(ext->other_app) {
#if USE_LV_WATCH_ALBUM != 0
        album_photo_share_flag_reset();
#endif /* USE_LV_WATCH_ALBUM */
    }
    if(ext->contact_list) {
#if VOICE_MSG_TEST == 0
        voice_msg_write_nvm(ext->contact_list);
#elif VOICE_MSG_APP_SPECIFIC_TEST != 0
        voice_msg_write_nvm(ext->contact_list);
#endif
        _lv_ll_clear(ext->contact_list);
        lv_mem_free(ext->contact_list);
    }

    lv_style_reset(&(ext->style_cont_mark));
    lv_style_reset(&(ext->style_label_mark));
    lv_watch_png_cache_all_free();
	
    printf("%s--end\n", __FUNCTION__);

#if VOICE_MSG_TEST != 0
#if VOICE_MSG_APP_SPECIFIC_TEST == 0
    voice_msg_clear_nvm_for_test();
    voice_msg_test_clear();
#endif
#endif
}

static lv_obj_t * voice_msg_get_obj(lv_watch_Activity_Id_t actId)
{
    lv_obj_t * activity_obj = lv_watch_get_activity_obj(actId);
    if(NULL == activity_obj) return NULL;

    lv_obj_t * obj = NULL;
    lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &obj);
    return obj;
}

static void * voice_msg_get_img_src(uint8_t portrait_id, char *id)
{
#if USE_LV_WATCH_VOICE_MSG_USER_IMAGE != 0
    static char image_name[NV_CONTACTS_MAX_NUMBER_LEN+30];
    lv_fs_file_t path_file;
    lv_fs_res_t res;
    sprintf(image_name, "C:/portrait_%s.png", id);
    res = lv_fs_open(&path_file, image_name, LV_FS_MODE_RD);
    if(res == LV_FS_RES_OK) {
        lv_fs_close(&path_file);
        return image_name;
    }else{
		return ICON_FAMILY;
    }
#else
    char * img_src = NULL;
WS_PRINTF("voice_msg_get_img_src============portrait_id=%d",portrait_id);
    switch(portrait_id) {
        case WATCH_PORTRAIT_ID_FATHER:
        case WATCH_PORTRAIT_ID_PATERNAL_GRADFATHER:
			img_src = ICON_VOICE_PERSON;
			break;

        case VOICE_MSG_PORTRAIT_ID_FAMILY_GROUP:
        case VOICE_MSG_PORTRAIT_ID_FRIENDS_GROUP:
            img_src = ICON_VOICE_GROUP;
            break;
		
        case WATCH_PORTRAIT_ID_NO_INFO:
            img_src = ICON_VOICE_SERVICE;
            break;
		case WATCH_PORTRAIT_ID_CUST:
			img_src =ICON_VOICE_SESSION;
			break;
        default:
			img_src = ICON_VOICE_PERSON;
            break;
    }

    return img_src;
#endif	
}


static lv_res_t voice_msg_del_top_interface(void)
{
	lv_obj_t * activity_obj = lv_watch_get_top_activity_obj();
    if(NULL == activity_obj) return LV_RES_INV;

    lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(activity_obj);
    if(NULL == ext) return LV_RES_INV;
    if(ext->prepare_destory) {
        ext->prepare_destory(activity_obj);
    }
    lv_obj_del(activity_obj);

    return LV_RES_OK;
}

void voice_msg_del_interface(lv_watch_Activity_Id_t act_id)
{
    lv_obj_t * activity_obj = lv_watch_get_activity_obj(act_id);
    if(NULL == activity_obj) return;

    lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(activity_obj);
    LV_ASSERT_MEM(ext);
    if(ext->prepare_destory) {
        ext->prepare_destory(activity_obj);
    }
    lv_obj_del(activity_obj);

    return;
}

static void voice_msg_add_group(uint8_t group_index, lv_ll_t * contact_list)
{
    printf("%s: add group for contact_index %d\n", __FUNCTION__, group_index);

    if(NULL == contact_list) {
        printf("%s: contact_list is NULL\n", __FUNCTION__);
        return;
    }

    if((VOICE_MSG_FAMILY_GROUP != group_index) && (VOICE_MSG_FRIENDS_GROUP != group_index)) {
        printf("%s: invalid group index %d\n", __FUNCTION__, group_index);
        return;
    }

    voice_msg_contact_t * contact = _lv_ll_ins_tail(contact_list);
    if(VOICE_MSG_FAMILY_GROUP == group_index) {
        const char * text = lv_lang_get_text(WATCH_TEXT_ID_FAMILY_GROUP);
        strncpy(contact->name, text, NV_CONTACTS_MAX_NAME_LEN);
        contact->portrait_id = VOICE_MSG_PORTRAIT_ID_FAMILY_GROUP;
    } else if(VOICE_MSG_FRIENDS_GROUP == group_index) {
        const char * text = lv_lang_get_text(WATCH_TEXT_ID_FRIENDS_GROUP);
        strncpy(contact->name, text, NV_CONTACTS_MAX_NAME_LEN);
        contact->portrait_id = VOICE_MSG_PORTRAIT_ID_FRIENDS_GROUP;
    }
    contact->index = group_index;
    memset(contact->number, 0, NV_CONTACTS_MAX_NUMBER_LEN);
    contact->mark = 0xFF;
}


static void voice_msg_record_task_cb(void * para)
{
    lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
    if(NULL == ext) return;

    printf("%s: event %d\n", __FUNCTION__, ext->event);

    bool result;
    switch(ext->event) {
        case MCI_EVENT_EOS:
            voice_msg_set_slide_enable(&ext->old_ext, true); /*reset slide_enable at the end of recording*/
            result = voice_msg_send_voice(ext);
            voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);
            Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
            Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
            if(result) voice_msg_create_send(NULL);
            break;
        case MCI_EVENT_ERROR:
            voice_msg_set_slide_enable(&ext->old_ext, true); /*reset slide_enable at the end of recording*/
            if(ext->buffer) {
                lv_mem_free(ext->buffer);
                ext->buffer = NULL;
            }
            voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);
            Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
            Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
            break;
        default:
            break;
    }
}

static void voice_msg_chat_record_cb(MCI_EVNET_T event, MCI_INFO_T info_type, int32_t value)
{
    printf("%s: event %d, type %d, value %d\n", __FUNCTION__, event, info_type, value);

    if(MCI_EVENT_INFO == event) {
        return;
    }

    lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
    if(NULL == ext) return;
    ext->event = event;
    lv_task_t * record_task = lv_task_create(voice_msg_record_task_cb, 10, LV_TASK_PRIO_MID, NULL);
    lv_task_once(record_task);
}

static void voice_msg_speaking_anim(lv_anim_t * a)
{
    lv_obj_t * img = a->var;
    uint8_t step = voice_msg_get_user_num(img);
    uint8_t record = step % 5;
    switch(record) {
        case 0:
            lv_img_set_src(img, ICON_RECORD1);
            break;
        case 1:
            lv_img_set_src(img, ICON_RECORD2);
            break;
        case 2:
            lv_img_set_src(img, ICON_RECORD3);
            break;
        case 3:
            lv_img_set_src(img, ICON_RECORD4);
            break;
        case 4:
            lv_img_set_src(img, ICON_RECORD5);
            break;
        default:
            break;
    }
	
#if USE_LV_WATCH_MEDIA_KEY != 0
	watch_wakeup_time_reset();
#endif

    voice_msg_set_user_num(img, step + 1);
    if(0 == (step % 4)) {
        lv_voice_msg_speak_ext_t * speak_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_SPEAK);
        if(NULL == speak_ext) return;
        int8_t count_down = VOICE_MSG_MAX_RECORD_DURATION - step / 4;
        if(0 > count_down) {
            printf("%s: warning, count down %d\n", __FUNCTION__, count_down);
            return;
        }
        const char * content = lv_lang_get_text(WATCH_TEXT_ID_RELEASE_SENDING);
        uint8_t len = strlen(content) + 4;
        char * text = (char *)lv_mem_alloc(len);
        snprintf(text, len, "%s %d", content, count_down);
        lv_label_set_text(speak_ext->label, text);
        lv_mem_free(text);
        #if defined(__XF_LCD_STYLE_ROUND__)
        lv_obj_align(speak_ext->label, img, LV_ALIGN_OUT_BOTTOM_MID, 0, -24);
        #else 
        lv_obj_align(speak_ext->label, img, LV_ALIGN_OUT_BOTTOM_MID, 0, 6);
        #endif
    }
}


static void voice_msg_release_send_event_cb(lv_obj_t * cont, lv_event_t e){

	//printf("%s e:%d\n", __FUNCTION__,e);
    if(LV_EVENT_CLICKED == e) {
        printf("%s\n", __FUNCTION__);
        lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
        if(NULL == ext) return;

        bool result = voice_msg_send_voice(ext);
        voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);
        Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
        if(result) voice_msg_create_send(NULL);
    }
}

static void voice_msg_create_speak(void)
{
    printf("%s\n", __FUNCTION__);

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_SPEAK;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_common_prepare_destory;
#if USE_LV_WATCH_MEDIA_KEY != 0
	activity_ext.key_handle = lv_watch_voice_msg_speak_key_handle;
#endif
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);
	

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    lv_voice_msg_speak_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_speak_ext_t));

    lv_obj_t * cont = lv_cont_create(obj, NULL);
    lv_obj_set_size(cont, lv_obj_get_width(obj), lv_obj_get_height(obj));
    lv_obj_align(cont, obj, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_local_bg_color(cont,
                                    LV_CONT_PART_MAIN,
                                    LV_STATE_DEFAULT,
                                    LV_COLOR_BLACK);
    lv_obj_set_click(cont, true);
    lv_obj_set_event_cb(cont, voice_msg_release_send_event_cb);
    lv_obj_add_protect(cont, LV_PROTECT_PRESS_LOST);

    lv_obj_t * img = lv_img_create(cont, NULL);
    lv_img_set_src(img, ICON_RECORD1);
#if defined(__XF_LCD_STYLE_ROUND__)
	lv_obj_align(img, cont, LV_ALIGN_IN_TOP_MID, 0, LV_VER_RES/4+10);
#else
    lv_obj_align(img, cont, LV_ALIGN_IN_TOP_MID, 0,30);
#endif
    voice_msg_set_user_num(img, 0);

    ext->label = lv_label_create(cont, NULL);
    lv_obj_add_style(ext->label, LV_LABEL_PART_MAIN, &lv_watch_font20);
    const char * rel_snd = lv_lang_get_text(WATCH_TEXT_ID_RELEASE_SENDING);
    uint8_t len = strlen(rel_snd) + 4;
    char * text = (char *)lv_mem_alloc(len);
    snprintf(text, len, "%s %d", rel_snd, VOICE_MSG_MAX_RECORD_DURATION);
    lv_label_set_text(ext->label, text);
    lv_mem_free(text);
    #if defined(__XF_LCD_STYLE_ROUND__)
    lv_obj_align(ext->label, img, LV_ALIGN_OUT_BOTTOM_MID, 0, -24);
    #else 
    lv_obj_align(ext->label, img, LV_ALIGN_OUT_BOTTOM_MID, 0, 6);
    #endif
#if USE_LV_WATCH_WS_BASE != 0
    lv_obj_t * img_release_snd = lv_img_create(cont, NULL);
    lv_img_set_src(img_release_snd, ICON_RELEASE_SND);
    #if defined(__XF_LCD_SIZE_128X128__)|| defined(__XF_LCD_SIZE_240X280__)
    lv_obj_align(img_release_snd, cont, LV_ALIGN_IN_BOTTOM_MID,0, -8);
    #else 
    lv_obj_align(img_release_snd, cont, LV_ALIGN_IN_BOTTOM_MID,-10, -8);
    #endif
#else
    lv_obj_t * img_send_emo = lv_img_create(cont, NULL);
    lv_img_set_src(img_send_emo, ICON_SEND_EMO);

    lv_obj_t * img_release_snd = lv_img_create(cont, NULL);
    lv_img_set_src(img_release_snd, ICON_RELEASE_SND);
    #if defined(__XF_LCD_SIZE_128X128__)|| defined(__XF_LCD_SIZE_240X280__)
    lv_obj_align(img_release_snd, cont, LV_ALIGN_IN_BOTTOM_RIGHT, 0, -8);
    #else 
    lv_obj_align(img_release_snd, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -14, -8);
    #endif
#endif

    lv_anim_t a = {};
    lv_anim_init(&a);
    lv_anim_set_var(&a, img);
    lv_anim_set_start_cb(&a, (lv_anim_ready_cb_t)voice_msg_speaking_anim);
    lv_anim_set_repeat_count(&a, LV_ANIM_REPEAT_INFINITE);
    lv_anim_set_time(&a, 249);
    lv_anim_start(&a);

#if VOICE_MSG_SLIDE_UP_TO_CANCEL_RECORDING != 0
    lv_obj_t * label_warning = lv_label_create(cont, NULL);
    lv_obj_set_style_local_text_font(label_warning,
                                     LV_LABEL_PART_MAIN,
                                     LV_STATE_DEFAULT,
                                     LV_THEME_WATCH_NIGHT_FONT_SMALL);
    if(LANG_EN == lv_lang_act()
        #if USE_LV_WATCH_LANG_MALA != 0
        ||LANG_MALA == lv_lang_act()
        #endif
        ) {
        lv_label_set_align(label_warning, LV_LABEL_ALIGN_CENTER);
        lv_label_set_long_mode(label_warning, LV_LABEL_LONG_BREAK);
        lv_obj_set_width(label_warning, lv_obj_get_width(obj) * 2 / 3);
    }
    lv_obj_set_style_local_text_color(label_warning,
                                      LV_LABEL_PART_MAIN,
                                      LV_STATE_DEFAULT,
                                      LV_COLOR_RED);
    lv_label_set_text_id(label_warning, WATCH_TEXT_ID_SLIDE_UP_WARNING);
	#if defined(__XF_LCD_STYLE_ROUND__)
	lv_obj_align(label_warning, cont, LV_ALIGN_IN_TOP_MID, 0, 20);
	#else
    lv_obj_align(label_warning, cont, LV_ALIGN_IN_TOP_MID, 0, 8);
	#endif
#endif

    return;
}

static void voice_msg_note_create(lv_obj_t * obj, uint16_t text_id)
{
    lv_obj_t * label = lv_label_create(obj, NULL);
    lv_obj_add_style(label, LV_OBJ_PART_MAIN, &lv_watch_font20);

    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
    lv_obj_set_click(label, false);
    lv_label_set_long_mode(label, LV_LABEL_LONG_BREAK);
    lv_obj_set_width(label, lv_obj_get_width(obj) - 50);
    lv_label_set_text_id(label, text_id);
    lv_obj_align(label, obj, LV_ALIGN_CENTER, 0, 0);

    return;
}

static void voice_msg_no_network_or_sim_create(lv_obj_t * obj)
{
    lv_obj_t * label = lv_label_create(obj, NULL);
    if(1 == lv_lang_act()) {
		#if defined(__XF_LCD_SIZE_128X128__)
       		lv_obj_add_style(label, LV_LABEL_PART_MAIN,&lv_watch_font20);
		#else
        	lv_obj_add_style(label, LV_LABEL_PART_MAIN,&lv_watch_font30);
		#endif
    } else {
        lv_obj_add_style(label, LV_LABEL_PART_MAIN,&lv_watch_font20);
    }

    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
    lv_obj_set_click(label, false);
    lv_label_set_text_id(label, WATCH_TEXT_ID_NO_SIM_OR_NETWORK);
    lv_obj_align(label, obj, LV_ALIGN_CENTER, 0, 0);

    return;
}

static void voice_msg_no_online_create(lv_obj_t * obj)
{
    lv_obj_t * label = lv_label_create(obj, NULL);
    
    lv_obj_add_style(label,LV_LABEL_PART_MAIN, &lv_watch_font20);
	

    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
    lv_obj_set_click(label, false);
    lv_label_set_text_id(label, WATCH_TEXT_ID_BINDAPP_FIRST);
    lv_obj_align(label, obj, LV_ALIGN_CENTER, 0, 0);

    return;
}


static lv_obj_t * voice_msg_no_contacts_create(lv_obj_t * obj,uint32_t textid)
{
    lv_obj_t * label = lv_label_create(obj, NULL);
    if(1 == lv_lang_act()) {
        lv_obj_add_style(label,LV_LABEL_PART_MAIN, &lv_watch_font30);
    } else {
        lv_obj_add_style(label,LV_LABEL_PART_MAIN, &lv_watch_font20);
    }

    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
    lv_obj_set_click(label, false);
    lv_label_set_text_id(label, textid);
    lv_obj_align(label, obj, LV_ALIGN_CENTER, 0, 0);
	printf("%s",__func__);

    return label;
}

static void voice_msg_title_img_back_event_cb(lv_obj_t * img, lv_event_t e)
{
    if(LV_EVENT_CLICKED == e) {
        voice_msg_del_top_interface();
    }
}

static lv_res_t voice_msg_title_img_add_signal(lv_obj_t * img, lv_event_t e)
{   
#if USE_LV_WATCH_MAKE_FRIENDS != 0
    if(LV_EVENT_CLICKED == e) {
        //voice_msg_del_top_interface();
       // make_friends_create_event_cb(img,e);
         voice_friend_add_choice_way_create(NULL);
    }
#endif
    return LV_RES_OK;
}

static lv_res_t voice_msg_title_img_tocontact_signal(lv_obj_t * img, lv_event_t e)
{   
    if(LV_EVENT_CLICKED == e) {
	        lv_watch_go_back();
	        voice_msg_create(NULL,0,NULL);
    }
    return LV_RES_OK;
}
static lv_res_t voice_msg_title_img_tosession_signal(lv_obj_t * img, lv_event_t e)
{   
	//printf("fun:%s,sign:%d",__func__,sign);
    if(LV_EVENT_CLICKED == e) {
	        lv_watch_go_back();
	        voice_session_msg_create(NULL,0,NULL);
    }
    return LV_RES_OK;
}



static void voice_msg_title_img_delete_signal(lv_obj_t * img, lv_event_t e)
{   
    if(LV_EVENT_CLICKED == e) {
        lv_voice_msg_chat_ext_t * chat_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
        if(chat_ext){
            voice_msg_chat_msg_delete_all_confirm(chat_ext->cur_contact);
        }
    }
}

static lv_obj_t * voice_msg_create_title(lv_obj_t * cont, char * text, uint16_t actId)
{
    if(NULL == cont) return NULL;
	printf("fun:%s,actId:%d",__func__,actId);

    lv_coord_t width_obj = lv_obj_get_width(cont);
    lv_obj_t * cont_title = lv_cont_create(cont, NULL);
    lv_coord_t height_label_cont = lv_obj_get_height(cont) / 6;
    lv_obj_set_size(cont_title, width_obj, height_label_cont);
    lv_obj_add_style(cont_title, LV_CONT_PART_MAIN, &lv_style_transp_tight);
    lv_cont_set_layout(cont_title, LV_LAYOUT_OFF);
    lv_obj_set_click(cont_title, false);
    lv_watch_obj_add_element(cont_title);
    lv_obj_clear_protect(cont_title, LV_PROTECT_PRESS_LOST); /*slide down for list from title*/

	
    lv_obj_t * img_back = lv_img_create(cont_title, NULL);
    lv_img_set_src(img_back, ICON_BACK_BLUE);
    if(LV_BIDI_DIR_RTL == lv_obj_get_base_dir(cont)) {
        lv_img_set_angle(img_back, 1800);
        lv_obj_align(img_back, cont_title, LV_ALIGN_IN_RIGHT_MID, 0, 2);
    } else {
        #if defined(__XF_LCD_SIZE_128X128__)
        lv_obj_align(img_back, cont_title, LV_ALIGN_IN_LEFT_MID, -4, 2);
        #else 
        lv_obj_align(img_back, cont_title, LV_ALIGN_IN_LEFT_MID, 0, 2);
        #endif
    }


    lv_obj_set_click(img_back, true);
    lv_obj_set_event_cb(img_back, voice_msg_title_img_back_event_cb);
    lv_obj_set_adv_hittest(img_back, false);
    lv_watch_obj_add_element(img_back);
    lv_obj_clear_protect(img_back, LV_PROTECT_PRESS_LOST); /*slide down for list from title*/

    if(actId == ACT_ID_VOICE_MSG_CHAT)
    {

		lv_obj_t * cont_name = lv_cont_create(cont_title, NULL);
		
		printf("fun:%s,name_label_w:%d",__func__,width_obj * 5 / 8);
		lv_obj_set_size(cont_name, width_obj * 5 / 8, height_label_cont);
		lv_obj_add_style(cont_name,LV_CONT_PART_MAIN, &lv_watch_style_transp);
#if USE_LV_WATCH_INSIDE_SHOW != 0 || USE_SMALL_CIRCLE_SCREEN !=0
		lv_obj_align(cont_name, cont_title, LV_ALIGN_CENTER, 0, 3);
#else
		lv_obj_align(cont_name, cont_title, LV_ALIGN_CENTER, 0, 2);
#endif
		lv_obj_set_click(cont_name, false);
	
		lv_obj_t * label_name = lv_label_create(cont_name, NULL);
		lv_obj_add_style(label_name, LV_LABEL_PART_MAIN,&lv_watch_font20);
		lv_label_set_long_mode(label_name, LV_LABEL_LONG_SROLL);
		lv_obj_set_size(label_name, width_obj * 5 / 8, height_label_cont);
		lv_label_set_text(label_name, text);
		lv_obj_align(label_name, cont_name, LV_ALIGN_CENTER, 0, 5);

	
        lv_obj_t * img_delete = lv_img_create(cont_title, NULL);
        lv_img_set_src(img_delete, ICON_DELETE_BLUE);
	#if USE_LV_WATCH_INSIDE_SHOW != 0
        lv_obj_align(img_delete, cont_title, LV_ALIGN_IN_RIGHT_MID, -2, 5);
	#else
		lv_obj_align(img_delete, cont_title, LV_ALIGN_IN_RIGHT_MID, -2, 0);
	#endif
        lv_obj_set_click(img_delete, true);
        lv_obj_set_event_cb(img_delete, voice_msg_title_img_delete_signal);
        lv_obj_set_adv_hittest(img_delete, false);
        lv_watch_obj_add_element(img_delete);
        lv_obj_clear_protect(img_delete, LV_PROTECT_PRESS_LOST);
    }
	
    else if(actId == ACT_ID_VOICE_MSG)
    {

		lv_obj_t * img_session = lv_img_create(cont_title, NULL);
		lv_img_set_src(img_session,ICON_SESSION);
		lv_obj_align(img_session, NULL, LV_ALIGN_IN_TOP_LEFT, 60, 2);
		lv_obj_set_click(img_session, true);
		lv_obj_set_event_cb(img_session, voice_msg_title_img_tosession_signal);
        lv_obj_set_adv_hittest(img_session, false);
		lv_watch_obj_add_element(img_session);
		lv_obj_clear_protect(img_session, LV_PROTECT_PRESS_LOST);
		
		lv_obj_t * img_contact = lv_img_create(cont_title, NULL);
		lv_img_set_src(img_contact, ICON_CONTACT_FOCUS);
		lv_obj_align(img_contact, NULL, LV_ALIGN_IN_TOP_LEFT, 120, 2);
		lv_obj_set_click(img_contact, false);
        lv_obj_set_adv_hittest(img_contact, false);
		lv_watch_obj_add_element(img_contact);
		lv_obj_clear_protect(img_contact, LV_PROTECT_PRESS_LOST);
		
        lv_obj_t * img_add = lv_img_create(cont_title, NULL);
        lv_img_set_src(img_add, ICON_ADD_BLUE);
	#if USE_LV_WATCH_INSIDE_SHOW != 0
        lv_obj_align(img_add, cont_title, LV_ALIGN_IN_RIGHT_MID, -2, 5);
	#else
        lv_obj_align(img_add, cont_title, LV_ALIGN_IN_RIGHT_MID, -2, 0);
	#endif
        lv_obj_set_click(img_add, true);
        lv_obj_set_event_cb(img_add, voice_msg_title_img_add_signal);
        lv_obj_set_adv_hittest(img_add, false);
        lv_watch_obj_add_element(img_add);
        lv_obj_clear_protect(img_add, LV_PROTECT_PRESS_LOST);
    }

	else if(actId == ACT_ID_VOICE_MSG_SESSION)
	 {
		 lv_obj_t * img_session = lv_img_create(cont_title, NULL);
		 lv_img_set_src(img_session, ICON_SESSION_FOCUS);
		 lv_obj_align(img_session, NULL, LV_ALIGN_IN_TOP_LEFT, 60, 2);
		 lv_obj_set_click(img_session, false);
		 lv_obj_set_adv_hittest(img_session, false);
		 lv_watch_obj_add_element(img_session);
		 lv_obj_clear_protect(img_session, LV_PROTECT_PRESS_LOST);
	
		 lv_obj_t * img_contact = lv_img_create(cont_title, NULL);
		 lv_img_set_src(img_contact, ICON_CONTACT);
		 lv_obj_align(img_contact, NULL, LV_ALIGN_IN_TOP_LEFT, 120, 2);
		 lv_obj_set_click(img_contact, true);
		 lv_obj_set_adv_hittest(img_contact, false);
		 lv_obj_set_event_cb(img_contact, voice_msg_title_img_tocontact_signal);
		 lv_watch_obj_add_element(img_contact);
		 lv_obj_clear_protect(img_contact, LV_PROTECT_PRESS_LOST);
	 
	 }

    return cont_title;
}



static uint16_t voice_msg_calc_days_between_two_times(uint32_t new_year,
        uint32_t new_mon,
        uint32_t new_day,
        uint32_t old_year,
        uint32_t old_mon,
        uint32_t old_day)
{
    uint16_t y1, m1, d1;
    uint16_t y2, m2, d2;

    m2 = (new_mon + 9) % 12;
    y2 = new_year - m2 / 10;
    d2 = 365 * y2 + y2 / 4 - y2 / 100 + (m2 * 306 + 5) / 10 + (new_day - 1);

    m1 = (old_mon + 9) % 12;
    y1 = old_year - m1 / 10;
    d1 = 365 * y1 + y1 / 4 - y1 / 100 + (m1 * 306 + 5) / 10 + (old_day - 1);

    return (d2 - d1);
}

static char * voice_msg_display_msg_time(lv_bidi_dir_t dir,
                                         hal_rtc_t * msg_time,
                                         hal_rtc_t * cur_time,
                                         hal_rtc_t * last_msg_time)
{
    char * time = NULL;
    if(0 == last_msg_time->tm_year) {
        time = (char *)lv_mem_alloc(17); /*for example 2015/12/14 20:15*/
        memset(time, 0, 17);
        if(LV_BIDI_DIR_RTL == dir) {
            snprintf(time, 17, "%02d:%02d %d/%d/%d", msg_time->tm_hour, msg_time->tm_min,
                     msg_time->tm_mon, msg_time->tm_mday, msg_time->tm_year);
        } else {
            snprintf(time, 17, "%d/%d/%d %02d:%02d", msg_time->tm_year, msg_time->tm_mon,
                     msg_time->tm_mday, msg_time->tm_hour, msg_time->tm_min);
        }
        return time;
    }

    uint32_t msg_sec = time_to_seconds(msg_time);
    uint32_t last_msg_sec = time_to_seconds(last_msg_time);
    if(msg_sec < last_msg_sec) {
        time = (char *)lv_mem_alloc(17); /*for example 2015/12/14 20:15*/
        memset(time, 0, 17);
        if(LV_BIDI_DIR_RTL == dir) {
            snprintf(time, 17, "%02d:%02d %d/%d/%d", msg_time->tm_hour, msg_time->tm_min,
                     msg_time->tm_mon, msg_time->tm_mday, msg_time->tm_year);
        } else {
            snprintf(time, 17, "%d/%d/%d %02d:%02d", msg_time->tm_year, msg_time->tm_mon,
                     msg_time->tm_mday, msg_time->tm_hour, msg_time->tm_min);
        }
    } else if(msg_sec > (last_msg_sec + VOICE_MSG_CHAT_TIME_INTERVAL)) {
        uint32_t cur_sec = time_to_seconds(cur_time);
        if((msg_sec > cur_sec)
           || (msg_time->tm_year < cur_time->tm_year)) {
            time = (char *)lv_mem_alloc(17); /*for example 2015/12/14 20:15*/
            memset(time, 0, 17);
            if(LV_BIDI_DIR_RTL == dir) {
                snprintf(time, 17, "%02d:%02d %d/%d/%d", msg_time->tm_hour, msg_time->tm_min,
                         msg_time->tm_mon, msg_time->tm_mday, msg_time->tm_year);
            } else {
                snprintf(time, 17, "%d/%d/%d %02d:%02d", msg_time->tm_year, msg_time->tm_mon,
                         msg_time->tm_mday, msg_time->tm_hour, msg_time->tm_min);
            }
        } else {
            uint32_t today_sec = cur_sec
                - cur_time->tm_hour * 3600
                - cur_time->tm_min * 60
                - cur_time->tm_sec;
            if(msg_sec >= today_sec) {
                /*today*/
                time = (char *)lv_mem_alloc(6); /*for example 20:15*/
                memset(time, 0, 6);
                snprintf(time, 6, "%02d:%02d", msg_time->tm_hour, msg_time->tm_min);
            } else if(msg_sec >= (today_sec - 24 * 3600)) {
                /*yesterday*/
                const char * yesterday = lv_lang_get_text(WATCH_TEXT_ID_YESTERDAY);
                uint8_t len = strlen(yesterday) + 7;
                time = (char *)lv_mem_alloc(len); /*for example yesterday 20:15*/
                memset(time, 0, len);
                if(LV_BIDI_DIR_RTL == dir) {
                    snprintf(time, len, "%02d:%02d %s", msg_time->tm_hour,
                             msg_time->tm_min, yesterday);
                } else {
                    snprintf(time, len, "%s %02d:%02d", yesterday, msg_time->tm_hour,
                             msg_time->tm_min);
                }
            } else {
                uint8_t wday;
                if(0 == cur_time->tm_wday) {
                    /*today is Sunday*/
                    wday = 6;
                } else {
                    wday = cur_time->tm_wday - 1;
                }
                /*the start of a week is monday, the end is sunday*/
                if(msg_sec >= (today_sec - wday * 24 * 3600)) {
                    /*current week before yesterday*/
                    const char * wday_str;
                    switch(rtc_calc_weekday(msg_time)) {
                    case 1:
                        /*Monday*/
                        wday_str = lv_lang_get_text(WATCH_TEXT_ID_MONDAY);
                        break;
                    case 2:
                        /*Tuesday*/
                        wday_str = lv_lang_get_text(WATCH_TEXT_ID_TUESDAY);
                        break;
                    case 3:
                        /*Wednesday*/
                        wday_str = lv_lang_get_text(WATCH_TEXT_ID_WEDNESDAY);
                        break;
                    case 4:
                        /*Thursday*/
                        wday_str = lv_lang_get_text(WATCH_TEXT_ID_THURSDAY);
                        break;
                    case 5:
                        /*Friday*/
                        wday_str = lv_lang_get_text(WATCH_TEXT_ID_FRIDAY);
                        break;
                    default:
                        printf("%s: week error\n", __FUNCTION__);
                        break;
                    }
                    uint8_t len = strlen(wday_str) + 7;
                    time = (char *)lv_mem_alloc(len); /*for example Monday 20:15*/
                    memset(time, 0, len);
                    if(LV_BIDI_DIR_RTL == dir) {
                        snprintf(time, len, "%02d:%02d %s", msg_time->tm_hour,
                                 msg_time->tm_min, wday_str);
                    } else {
                        snprintf(time, len, "%s %02d:%02d", wday_str, msg_time->tm_hour,
                                 msg_time->tm_min);
                    }
                } else {
                    time = (char *)lv_mem_alloc(12); /*for example 11/14 20:15*/
                    memset(time, 0, 12);
                    if(LV_BIDI_DIR_RTL == dir) {
                        snprintf(time, 12, "%02d:%02d %d/%d", msg_time->tm_hour, msg_time->tm_min,
                                 msg_time->tm_mon, msg_time->tm_mday);
                    } else {
                        snprintf(time, 12, "%d/%d %02d:%02d", msg_time->tm_mon, msg_time->tm_mday,
                                 msg_time->tm_hour, msg_time->tm_min);
                    }
                }
            }
        }
    }

    return time;
}


static bool voice_msg_is_more_than_three_mins(app_adp_time_and_date_t * new_time, voice_msg_time_t * old_time)
{
    uint16_t days = voice_msg_calc_days_between_two_times(new_time->year,
                    new_time->month,
                    new_time->day,
                    old_time->year,
                    old_time->month,
                    old_time->day);
    if(1 < days) {
        return true;
    }

    uint16_t new_mins = new_time->hour * 60 + new_time->min + days * 24 * 60;
    uint16_t old_mins = old_time->hour * 60 + old_time->min;
    if(new_mins > (old_mins + 3)) {
        return true;
    }

    return false;
}

static bool voice_msg_is_group_msg(watch_app_adp_voice_msg_chat_type_t chat_type, char * name)
{
    bool group_msg = false;

    if((WATCH_VOICE_MSG_FAMILY_GROUP_CHAT == chat_type)
            || (WATCH_VOICE_MSG_FRIENDS_GROUP_CHAT == chat_type)) {
        group_msg = true;
    } else if((WATCH_VOICE_MSG_INVALID_CHAT == chat_type) && (NULL != name)) {
        const char * family_group = lv_lang_get_text(WATCH_TEXT_ID_FAMILY_GROUP);
        const char * friends_group = lv_lang_get_text(WATCH_TEXT_ID_FRIENDS_GROUP);
        if((0 == strcmp(name, family_group))
                || (0 == strcmp(name, friends_group))) {
            group_msg = true;
        }

    }
    printf("%s: chat_type:%d group msg %d\n", __FUNCTION__, chat_type,group_msg);

    return group_msg;
}

static lv_obj_t * voice_msg_display_msg_to_chat(lv_obj_t * list,
                                                app_adaptor_voice_msg_t * msgs,
                                                hal_rtc_t * last_msg_time)
{
    printf("%s\n", __FUNCTION__);

    if(NULL == msgs) return NULL;
    if(NULL == last_msg_time) return NULL;

    voice_msg_desc_t msg_desc;
    char * contact_name = NULL;
    uint8_t i = 0;
    if(VOICE_MSG_MAX_CHAT_MSG_NUM < msgs->count) {
        i = msgs->count - VOICE_MSG_MAX_CHAT_MSG_NUM;
    }

    hal_rtc_t cur_time;
    Hal_Rtc_Gettime(&cur_time);

    for(; i < msgs->count; i++) {
        hal_rtc_t msg_time;
        msg_time.tm_year = msgs->msg[i].time.year;
        msg_time.tm_mon = msgs->msg[i].time.month;
        msg_time.tm_mday = msgs->msg[i].time.day;
        msg_time.tm_hour = msgs->msg[i].time.hour;
        msg_time.tm_min = msgs->msg[i].time.min;
        msg_time.tm_sec = 0;

        msg_desc.time_str = voice_msg_display_msg_time(lv_obj_get_base_dir(list),
                                                       &msg_time, &cur_time, last_msg_time);
        if(NULL != msg_desc.time_str) {
            msg_desc.type = VOICE_MSG_TYPE_TIME;
            voice_msg_list_add(list, &msg_desc);
            lv_mem_free(msg_desc.time_str);
        }

        last_msg_time->tm_year = msgs->msg[i].time.year;
        last_msg_time->tm_mon = msgs->msg[i].time.month;
        last_msg_time->tm_mday = msgs->msg[i].time.day;
        last_msg_time->tm_hour = msgs->msg[i].time.hour;
        last_msg_time->tm_min = msgs->msg[i].time.min;

        msg_desc.node = msgs->msg[i].node;
		msg_desc.msg_id=NULL;  //ynyd
		msg_desc.cmdTxt=NULL;
		printf("%s,type:%d,msg_id:%s\n", __FUNCTION__,msgs->msg[i].type,msgs->msg[i].msg_id);

        if(WATCH_VOICE_MSG_FROM_UI == msgs->msg[i].direction) {
            msg_desc.direction = 0;
        } else {
            msg_desc.direction = 1;
            
        #if USE_LV_WATCH_WS_BASE != 0
            /* none*/
        #else
            if(true == voice_msg_is_group_msg(msgs->chat_type, msgs->name)) {
                if((NULL == contact_name)
                        || (VOICE_MSG_TYPE_TIME == msg_desc.type)
                        || ((NULL != contact_name)
                            && (0 != strcmp(contact_name, msgs->msg[i].name)))) {
                    /*display contact name*/
                    msg_desc.type = VOICE_MSG_TYPE_NAME;
                    msg_desc.name_str = msgs->msg[i].name;
                    voice_msg_list_add(list, &msg_desc);
                }
                contact_name = msgs->msg[i].name;
            }
        #endif
        }
        msg_desc.size = 0;
        if(WATCH_VOICE_MSG_TYPE_TEXT == msgs->msg[i].type) {
            msg_desc.type = VOICE_MSG_TYPE_TEXT;
            if(WATCH_VOICE_DATA_TYPE_BUFFER == msgs->msg[i].data_type) {
                msg_desc.size = strlen(msgs->msg[i].content.text) + 1;
            }
            msg_desc.data = msgs->msg[i].content.text;
        } else if(WATCH_VOICE_MSG_TYPE_VOICE == msgs->msg[i].type) {
            msg_desc.type = VOICE_MSG_TYPE_VOICE;
            msg_desc.voice_index = msgs->msg[i].content.voice.index;
            if(true == msgs->msg[i].read_flag) {
                msg_desc.have_read = 1;
            } else {
                msg_desc.have_read = 0;
				//ynyd save msg_id
				if(strlen(msgs->msg[i].msg_id)){
					uint8_t len=strlen(msgs->msg[i].msg_id)+1;
					msg_desc.msg_id=(char *)lv_mem_alloc(len);
					memset(msg_desc.msg_id,0,len);
					strcpy(msg_desc.msg_id,msgs->msg[i].msg_id);
					printf("%s voice i:%d ,desc:%s, msg_id:%s\n", __FUNCTION__,i,msg_desc.msg_id,msgs->msg[i].msg_id);
				}

            }
            if(WATCH_VOICE_DATA_TYPE_FILE == msgs->msg[i].data_type) {
                /*file path string*/
                uint32_t size = strlen((char *)msgs->msg[i].content.voice.file) + 1;
                msg_desc.data = lv_mem_alloc(size);
                strncpy(msg_desc.data, (char *)msgs->msg[i].content.voice.file, size);
            } else {
                /*data buffer*/
                msg_desc.size = msgs->msg[i].content.voice.voice_len;
                msg_desc.data = lv_mem_alloc(msg_desc.size);
                memcpy(msg_desc.data, msgs->msg[i].content.voice.file, msg_desc.size);
            }
            msg_desc.duration = msgs->msg[i].content.voice.duration;
        } else if((WATCH_VOICE_MSG_TYPE_EXPRESSION == msgs->msg[i].type)
                  || (WATCH_VOICE_MSG_TYPE_PHOTO == msgs->msg[i].type)) {
            if(WATCH_VOICE_MSG_TYPE_EXPRESSION == msgs->msg[i].type) {
                msg_desc.type = VOICE_MSG_TYPE_EXPRESSION;
            } else {
                msg_desc.type = VOICE_MSG_TYPE_PHOTO;
            }
            if(WATCH_VOICE_DATA_TYPE_FILE == msgs->msg[i].data_type) {
                /*file path string*/
                msg_desc.data = msgs->msg[i].content.img.data;
            } else {
                /*data buffer*/
                msg_desc.size = msgs->msg[i].content.img.data_size;
                msg_desc.data = lv_mem_alloc(msg_desc.size);
                memcpy(msg_desc.data, msgs->msg[i].content.img.data, msg_desc.size);
            }
        } else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == msgs->msg[i].type) {
            msg_desc.type = VOICE_MSG_TYPE_EMOJI_LIST;
            msg_desc.size = msgs->msg[i].content.emoji_list.cnt;
            msg_desc.data = lv_mem_alloc(msg_desc.size * sizeof(uint32_t));
            memcpy(msg_desc.data, msgs->msg[i].content.emoji_list.list, msg_desc.size*sizeof(uint32_t));
            //printf("%s:%d\n", __FUNCTION__, __LINE__);
        }else if(WATCH_VOICE_MSG_TYPE_CMD_CFM == msgs->msg[i].type) {
            msg_desc.type = VOICE_MSG_TYPE_CMD_CFM;
            if(WATCH_VOICE_DATA_TYPE_BUFFER == msgs->msg[i].data_type) {
                msg_desc.size = strlen(msgs->msg[i].content.text) + 1;
            }
			if(strlen(msgs->msg[i].msg_id)){
				uint8_t len=strlen(msgs->msg[i].msg_id)+1;
				msg_desc.msg_id=(char *)lv_mem_alloc(len);
				memset(msg_desc.msg_id,0,len);
				strcpy(msg_desc.msg_id,msgs->msg[i].msg_id);
				printf("%s voice i:%d ,desc:%s, msg_id:%s\n", __FUNCTION__,i,msg_desc.msg_id,msgs->msg[i].msg_id);
			}	
			msg_desc.cmdTxt=(char *)lv_mem_alloc(strlen(msgs->msg[i].content.text)+1);
			memset(msg_desc.cmdTxt,0,strlen(msgs->msg[i].content.text)+1);
			strcpy(msg_desc.cmdTxt,msgs->msg[i].content.text);
			
            msg_desc.data = msgs->msg[i].content.text;
        }
        voice_msg_list_add(list, &msg_desc);
    }
    msg_desc.type = VOICE_MSG_TYPE_EMO_AND_TOUCH;
    lv_obj_t * cont = voice_msg_list_add(list, &msg_desc);

    app_adaptor_voice_msg_read_txt_req(msgs->index);
    return cont;
}


static void voice_msg_free_msgs(app_adaptor_voice_msg_t * msgs)
{
    if(NULL == msgs) return;
    if(msgs->name) lv_mem_free(msgs->name);
    if(msgs->number) lv_mem_free(msgs->number);
    if(msgs->imei) lv_mem_free(msgs->imei);
    if(NULL == msgs->msg) return;
    for(uint8_t i = 0; i < msgs->count; i++) {
        if(msgs->msg[i].name) lv_mem_free(msgs->msg[i].name);
        if(WATCH_VOICE_MSG_TYPE_TEXT == msgs->msg[i].type ||WATCH_VOICE_MSG_TYPE_CMD_CFM== msgs->msg[i].type)
            lv_mem_free(msgs->msg[i].content.text);
        else if(WATCH_VOICE_MSG_TYPE_VOICE == msgs->msg[i].type)
            lv_mem_free(msgs->msg[i].content.voice.file);
        else if((WATCH_VOICE_MSG_TYPE_EXPRESSION == msgs->msg[i].type)
                || (WATCH_VOICE_MSG_TYPE_PHOTO == msgs->msg[i].type))
            lv_mem_free(msgs->msg[i].content.img.data);
        else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == msgs->msg[i].type)
            lv_mem_free(msgs->msg[i].content.emoji_list.list);
    }
    lv_mem_free(msgs->msg);
    lv_mem_free(msgs);
}

static void voice_msg_chat_free_mem(lv_voice_msg_chat_ext_t * ext)
{
    if(NULL == ext) return;
    if(NULL == ext->list) return;
    lv_obj_t * scrl = lv_page_get_scrl(ext->list);
    lv_obj_t * child = lv_obj_get_child_back(scrl, NULL);
    while(child) {
        voice_msg_type_t type = VOICE_MSG_GET_MSG_TYPE(voice_msg_get_user_num(child));
        if((VOICE_MSG_TYPE_EXPRESSION == type) || (VOICE_MSG_TYPE_PHOTO == type)) {
			#if 0
            const void * src = lv_img_get_src(child);
            if(LV_IMG_SRC_VARIABLE == lv_img_src_get_type(src)) {
                lv_img_dsc_t * dsc = (lv_img_dsc_t *)src;
                lv_mem_free(dsc->data);
                lv_mem_free(dsc);
            }
			#else
			
			lv_obj_t * ind_cont = lv_obj_get_child(child, NULL);
				void * ptr =voice_msg_get_user_ptr(ind_cont);
				if(ptr) lv_mem_free(ptr);
		
			#endif 
			
        } else if(VOICE_MSG_TYPE_VOICE == type) {
            lv_obj_t * len_label = lv_obj_get_child(child, NULL);
            void * ptr = voice_msg_get_user_ptr(len_label);
            if(ptr) lv_mem_free(ptr);

			/*free msg_id,if have--ynyd*/
			lv_obj_t * playing_img = lv_obj_get_child(child, len_label);
			if(playing_img){
				//printf("%s: playing_img:%p\n",__FUNCTION__,playing_img);
				void * ptr2 = voice_msg_get_user_ptr(playing_img);
				printf("%s:  ptr2:%p\n",__FUNCTION__,ptr2);
				if(ptr2) lv_mem_free(ptr2);
			}
        }else if(VOICE_MSG_TYPE_CMD_CFM == type) {
            lv_obj_t * cont = lv_obj_get_child(child, NULL);
            lv_obj_t * chat_label = lv_obj_get_child(cont, NULL);
            void * ptr = voice_msg_get_user_ptr(cont);
            if(ptr) lv_mem_free(ptr);
			printf("%s:  \n",__FUNCTION__);
			/*free msg_id,if have*/
			if(chat_label){
	            void * ptr2 = voice_msg_get_user_ptr(chat_label);
				printf("%s:  ptr2:%p\n",__FUNCTION__,ptr2);
				if(ptr2) lv_mem_free(ptr2);
			}
			
        }
        child = lv_obj_get_child_back(scrl, child);
    }
}


static void voice_msg_chat_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);
	uint8_t chatFrom=0;
    lv_voice_msg_chat_ext_t * chat_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
	lv_voice_msg_ext_t * session_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_SESSION);
	lv_voice_msg_ext_t * ext_msg = voice_msg_get_ext(ACT_ID_VOICE_MSG);
    if(NULL == chat_ext) return;

    if(chat_ext->list) {
        lv_voice_msg_list_ext_t * list_ext = lv_obj_get_ext_attr(chat_ext->list);
        if(NULL == list_ext) return;
        lv_style_reset(&(list_ext->style_cont));
        lv_style_reset(&(list_ext->style_label_time));
        lv_style_reset(&(list_ext->style_text));
        lv_style_reset(&(list_ext->style_emoji));
        lv_style_reset(&(list_ext->style_label_len_other));
        lv_style_reset(&(list_ext->style_label_len_ui));
    }
	
	MMI_ModemAdp_WS_Yn_Chat_Close(chat_ext->cur_contact->imei);  //ynyd
	chatFrom=chat_ext->cur_contact->toChatFromSess;

    voice_msg_chat_stop_voice(chat_ext);
	Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
	if((session_ext==NULL)&&(ext_msg==NULL))
	{
		if(chat_ext->contact_list) {
			_lv_ll_clear(chat_ext->contact_list);
			lv_mem_free(chat_ext->contact_list);
			chat_ext->contact_list=NULL;
		}
	
		if(chat_ext->cur_contact)
			lv_mem_free(chat_ext->cur_contact);  //ynyd
	}
	
    voice_msg_chat_free_mem(chat_ext);
    lv_watch_png_cache_all_free();
	#if 0
	if(yn_voicedw_buff)
	{
		lv_mem_free(yn_voicedw_buff);
		yn_voicedw_buff=NULL;
		WS_PRINTF("%s--lv_mem_free(yn_voicedw_buff)\n", __FUNCTION__);

	}

	yn_voicedw_buff_len=0;
	#endif
	
	
	watch_set_suspend_enable(true, 0, 0);
	
    printf("%s chatFrom:%d\n", __FUNCTION__,chatFrom);

	if(chatFrom)
	{
		if(session_ext) {
			if(chat_ext->contact_list_update) {
				printf("%s: update contact list\n", __FUNCTION__);
				voice_msg_update_contact_list(session_ext,1);
			}
		} else {
			voice_session_msg_create(NULL, false, chat_ext->contact_list);
		}

	}
	else{
	    if(ext_msg) {
	        if(chat_ext->contact_list_update) {
	            printf("%s: update contact list\n", __FUNCTION__);
	            voice_msg_update_contact_list(ext_msg,0);
	        }
	    } else {
	    
	        voice_msg_create(NULL, false, NULL);
	    }
	}
	
#if USE_LV_WATCH_MEDIA_KEY != 0
			activity_obj->handle_key_ptr = NULL;
#endif
}

static void voice_msg_create_chat(lv_ll_t * contact_list, voice_msg_contact_t * contact)
{
    //if(NULL == contact_list) return;
    if(NULL == contact) return;

	if(phone_is_monitor_on())
	{
		tip_content_create(lv_watch_get_top_activity_obj(),WATCH_TEXT_ID_MONITOR_ON);
		return;
	}
	
    printf("%s: chat with %s ,imei:%s, session_id:%s\n", __FUNCTION__, contact->name,contact->imei,contact->sessionId);

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_CHAT;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_chat_prepare_destory;
	#if USE_LV_WATCH_MEDIA_KEY != 0
	activity_ext.key_handle = lv_watch_voice_msg_chat_key_handle;
	#endif	
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    lv_voice_msg_chat_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_chat_ext_t));
    LV_ASSERT_MEM(ext);
    ext->list = NULL;
    memset(&ext->last_msg_time, 0, sizeof(hal_rtc_t));
    ext->contact_list_update = false;
    ext->contact_list = contact_list;
    ext->cur_contact = contact;
    ext->label_no_msg = NULL;
    ext->img_emo = NULL;
    ext->img_touch = NULL;
    ext->buffer = NULL;
    ext->img_call = NULL;
    ext->img_vcall = NULL;

    lv_obj_t * cont = lv_cont_create(obj, NULL);
    lv_obj_set_size(cont, lv_obj_get_width(obj), lv_obj_get_height(obj));
    lv_obj_align(cont, obj, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_local_bg_color(cont,
                                    LV_CONT_PART_MAIN,
                                    LV_STATE_DEFAULT,
                                    LV_COLOR_BLACK);  //LV_COLOR_MAKE(194, 252, 252)
    lv_watch_obj_add_element(cont);

    ext->cont_title = voice_msg_create_title(cont, contact->name, ACT_ID_VOICE_MSG_CHAT);

    app_adaptor_voice_msg_chat_id_t * id = (app_adaptor_voice_msg_chat_id_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_chat_id_t));
    voice_msg_fill_chat_id(id, contact);

	MMI_ModemAdp_WS_Yn_Chat_Open(id->imei);  //ynyd
	
    app_adaptor_voice_msg_t * msgs = app_adaptor_voice_msg_get_msgs_req(id);
    uint8_t msg_count = 0;
    if(NULL != msgs) {
        msg_count = msgs->count;
        if(VOICE_MSG_MAX_CHAT_MSG_NUM < msgs->count) {
            printf("%s: msgs->count %d exceeds max num\n", __FUNCTION__, msgs->count);
            msg_count = VOICE_MSG_MAX_CHAT_MSG_NUM;
        }
        if(0 < msgs->count) {
            lv_coord_t list_height = lv_obj_get_height(cont) - lv_obj_get_height(ext->cont_title) - 4;
            lv_obj_t * list = voice_msg_list_create(cont, lv_obj_get_width(cont), list_height);
            ext->list = list;
            lv_obj_align(list, ext->cont_title, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 0);
            lv_coord_t scrl_scrn_height = list_height
                - lv_obj_get_style_pad_top(list, LV_LIST_PART_BG) * 2;
            lv_obj_t * btn = voice_msg_display_msg_to_chat(list, msgs, &ext->last_msg_time);
            lv_obj_t * scrl = lv_page_get_scrl(list);
            lv_coord_t scrl_h = lv_obj_get_height(scrl);
            if(scrl_scrn_height >= scrl_h) {
                /*set emo and touch img to bottow of chat window*/
                lv_cont_set_layout(scrl, LV_LAYOUT_OFF);
                lv_coord_t btn_y = scrl_scrn_height - lv_obj_get_height(btn);
                lv_obj_set_y(btn, btn_y);
            } else
                lv_list_focus(btn, false);
        }

        voice_msg_free_msgs(msgs);
    }

    if(0 == msg_count) {
        ext->label_no_msg = lv_label_create(cont, NULL);
        lv_obj_add_style(ext->label_no_msg, LV_LABEL_PART_MAIN, &lv_watch_font20);
        lv_label_set_text_id(ext->label_no_msg, WATCH_TEXT_ID_WAITING);
        lv_obj_align(ext->label_no_msg, cont, LV_ALIGN_CENTER, 0, -10);
		
#if defined(__XF_LCD_STYLE_ROUND__)
	#if USE_LV_WATCH_WS_BASE != 0
		ext->img_touch = lv_img_create(cont, NULL);
		lv_img_set_src(ext->img_touch, ICON_TOUCH_SPK);
		lv_obj_set_click(ext->img_touch, true);
		lv_obj_set_event_cb(ext->img_touch, voice_msg_touch_spk_event_cb);
		lv_obj_set_adv_hittest(ext->img_touch, false);
		lv_watch_obj_add_element(ext->img_touch);
	
		if(ext->cur_contact->index < VOICE_MSG_FAMILY_GROUP)
		{
		#if USE_LV_WATCH_WS_ZNSH_FRIEND_CALL != 0
			ext->img_call = lv_img_create(cont, NULL);
			lv_img_set_src(ext->img_call, ICON_MSG_CALL);
			lv_obj_set_click(ext->img_call, true);
			lv_obj_set_event_cb(ext->img_call, voice_msg_touch_call_signal);
			lv_obj_set_adv_hittest(ext->img_call, false);
			lv_watch_obj_add_element(ext->img_call);
		#endif
			
		#if USE_LV_WATCH_WS_ZNSH_FRIEND_VCALL != 0
			ext->img_vcall = lv_img_create(cont, NULL);
			lv_img_set_src(ext->img_vcall, ICON_MSG_VCALL);
			lv_obj_set_click(ext->img_vcall, true);
			lv_obj_set_event_cb(ext->img_vcall, voice_msg_touch_vcall_signal);
			lv_obj_set_adv_hittest(ext->img_vcall, false);
			lv_watch_obj_add_element(ext->img_vcall);
		#endif
		}
		
		if(ext->cur_contact->index < VOICE_MSG_FAMILY_GROUP)
		{
			if (ext->img_vcall && ext->img_call){
				lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, 0, -6);
				lv_obj_align(ext->img_vcall, ext->img_touch, LV_ALIGN_OUT_LEFT_MID, -2, -14);
				lv_obj_align(ext->img_call, ext->img_touch, LV_ALIGN_OUT_RIGHT_MID, 2, -14);
			}else if (ext->img_call){
				lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, -29, -6);
				lv_obj_align(ext->img_call, ext->img_touch, LV_ALIGN_OUT_RIGHT_MID, 4, 0);
			}else if (ext->img_vcall){
				lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, -29, -6);
				lv_obj_align(ext->img_vcall, ext->img_touch, LV_ALIGN_OUT_RIGHT_MID, 4, 0);
			}else{
				lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, 0, -5);
			}
		}else{
			lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, 0, -5);
		}
	#else
		ext->img_emo = lv_img_create(cont, NULL);
		lv_img_set_src(ext->img_emo, ICON_SEND_EMO);
		lv_obj_set_click(ext->img_emo, true);
		lv_obj_set_event_cb(ext->img_emo, voice_msg_send_emo_event_cb);
		lv_obj_set_adv_hittest(ext->img_emo, false);
		lv_watch_obj_add_element(ext->img_emo);
	
		ext->img_touch = lv_img_create(cont, NULL);
		lv_img_set_src(ext->img_touch, ICON_TOUCH_SPK);
		#if defined(__XF_LCD_SIZE_240X280__)  
        lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, 0, -8);
        #else 
		lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -14, -8);
		#endif
		lv_obj_set_click(ext->img_touch, true);
		lv_obj_set_event_cb(ext->img_touch, voice_msg_touch_spk_event_cb);
		lv_obj_set_adv_hittest(ext->img_touch, false);
		lv_watch_obj_add_element(ext->img_touch);
	
		if(LV_BIDI_DIR_RTL == lv_obj_get_base_dir(obj)){
			lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_LEFT, 14, -8);
			lv_obj_align(ext->img_emo, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -14, -8);
		} else {
			lv_obj_align(ext->img_emo, cont, LV_ALIGN_IN_BOTTOM_LEFT, 14, -8);
			lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -14, -8);
		}
	#endif

#else
	#if USE_LV_WATCH_WS_BASE != 0
        ext->img_touch = lv_img_create(cont, NULL);
        lv_img_set_src(ext->img_touch, ICON_TOUCH_SPK);
        #if defined(__XF_LCD_SIZE_128X128__)|| defined(__XF_LCD_SIZE_240X280__)
        lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, 0, -8);
        #else 
        lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_MID, -10, -8);
        #endif
        lv_obj_set_click(ext->img_touch, true);
        lv_obj_set_event_cb(ext->img_touch, voice_msg_touch_spk_event_cb);
        lv_obj_set_adv_hittest(ext->img_touch, false);
        lv_watch_obj_add_element(ext->img_touch);

        if(ext->cur_contact->index < VOICE_MSG_FAMILY_GROUP)
        {
        #if USE_LV_WATCH_WS_ZNSH_FRIEND_CALL != 0
            ext->img_call = lv_img_create(cont, NULL);
            lv_img_set_src(ext->img_call, ICON_MSG_CALL);
            lv_obj_align(ext->img_call, cont, LV_ALIGN_IN_BOTTOM_RIGHT, 0, -8);
            lv_obj_set_click(ext->img_call, true);
            lv_obj_set_event_cb(ext->img_call, voice_msg_touch_call_signal);
            lv_obj_set_adv_hittest(ext->img_call, false);
            lv_watch_obj_add_element(ext->img_call);
        #endif
            
        #if USE_LV_WATCH_WS_ZNSH_FRIEND_VCALL != 0
            ext->img_vcall = lv_img_create(cont, NULL);
            lv_img_set_src(ext->img_vcall, ICON_MSG_VCALL);
            lv_obj_align(ext->img_vcall, cont, LV_ALIGN_IN_BOTTOM_LEFT, 0, -8);
            lv_obj_set_click(ext->img_vcall, true);
            lv_obj_set_event_cb(ext->img_vcall, voice_msg_touch_vcall_signal);
            lv_obj_set_adv_hittest(ext->img_vcall, false);
            lv_watch_obj_add_element(ext->img_vcall);
        #endif
        }
	#else
        ext->img_emo = lv_img_create(cont, NULL);
        lv_img_set_src(ext->img_emo, ICON_SEND_EMO);
        lv_obj_set_click(ext->img_emo, true);
        lv_obj_set_event_cb(ext->img_emo, voice_msg_send_emo_event_cb);
        lv_obj_set_adv_hittest(ext->img_emo, false);
        lv_watch_obj_add_element(ext->img_emo);

        ext->img_touch = lv_img_create(cont, NULL);
        lv_img_set_src(ext->img_touch, ICON_TOUCH_SPK);
        #if defined(__XF_LCD_SIZE_128X128__)|| defined(__XF_LCD_SIZE_240X280__)
        lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_RIGHT, 0, -8);
        #else 
        lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -14, -8);
        #endif
        lv_obj_set_click(ext->img_touch, true);
        lv_obj_set_event_cb(ext->img_touch, voice_msg_touch_spk_event_cb);
        lv_obj_set_adv_hittest(ext->img_touch, false);
        lv_watch_obj_add_element(ext->img_touch);

        if(LV_BIDI_DIR_RTL == lv_obj_get_base_dir(obj)){
            lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_LEFT, 14, -8);
            lv_obj_align(ext->img_emo, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -14, -8);
        } else {
            lv_obj_align(ext->img_emo, cont, LV_ALIGN_IN_BOTTOM_LEFT, 14, -8);
            lv_obj_align(ext->img_touch, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -14, -8);
        }
	#endif
#endif	
    }

    ext->msg_count = msg_count;
}



static void voice_msg_del_chat(lv_ll_t * contact_list, voice_msg_contact_t * contact)
{
    if(NULL == contact_list) return;
    if(NULL == contact) return;

    printf("%s: chat with %s\n", __FUNCTION__, contact->name);
    voice_msg_contact_t * node = _lv_ll_get_head(contact_list);

    if(node)
    {
    	node = _lv_ll_get_next(contact_list, node);
    }
    
    while(node) {

        if(0 == strcmp(contact->imei , node->imei)) {            
            voice_msg_delete_msg_db(node->index);
            _lv_ll_remove(contact_list, node);
            break;
        }
        node = _lv_ll_get_next(contact_list, node);
    }
    
    voice_msg_write_nvm(contact_list);  

    MMI_ModemAdp_WS_DelFriendReq(contact->imei);

}

static void voice_msg_del_contact(lv_ll_t * contact_list, voice_msg_contact_t * contact)
{
    if(NULL == contact_list) return;
    if(NULL == contact) return;

    printf("%s: chat with imei:%s ,session_id:%s\n", __FUNCTION__, contact->imei,contact->sessionId);
    voice_msg_contact_t * node = _lv_ll_get_head(contact_list);

    if(node)
    {
    	node = _lv_ll_get_next(contact_list, node);
    }
    
    while(node) {

        if(0 == strcmp(contact->imei , node->imei)) {            
           _lv_ll_remove(contact_list, node);
            break;
        }
        node = _lv_ll_get_next(contact_list, node);
    }
    
    MMI_ModemAdp_WS_Yn_del_contact(contact->userId);

}
static void voice_msg_del_session(lv_ll_t * contact_list, voice_msg_contact_t * contact)
{
    if(NULL == contact_list) return;
    if(NULL == contact) return;

    printf("%s: chat with %s\n", __FUNCTION__, contact->name);
    voice_msg_contact_t * node = _lv_ll_get_head(contact_list);

    if(node)
    {
    	node = _lv_ll_get_next(contact_list, node);
    }
    
    while(node) {

        if(0 == strcmp(contact->imei , node->imei)) {            
          _lv_ll_remove(contact_list, node);
            break;
        }
        node = _lv_ll_get_next(contact_list, node);
    }
	
    MMI_ModemAdp_WS_Yn_del_session(contact->imei);

}




void voice_msg_del_chat_msg(void * node, uint8_t index)
{
    if(NULL == node) return;

    voice_msg_delete_old_info(index, node);
    voice_msg_list_dump_to_fs(index);
}

void voice_msg_del_fiend_confirm_prepare_destory(lv_obj_t * activity_obj)
{
    lv_watch_png_cache_all_free();
}

lv_res_t voice_msg_del_fiend_yes_btn_action(lv_obj_t * btn, lv_event_t e)
{
    if(LV_EVENT_CLICKED != e) {
        return;
    }
    
    lv_obj_t * act_obj = lv_obj_get_parent(btn);
    if(!act_obj) return;
        
    lv_voice_msg_chat_ext_t * ext = lv_obj_get_ext_attr(act_obj);
    if(!ext) return;
    voice_msg_del_contact(ext->contact_list, ext->cur_contact);

    //voice_msg_del_interface(ACT_ID_VOICE_MSG);
    voice_msg_del_interface(ACT_ID_VOICE_MSG_DEL_CONFIRM);
    
    return LV_RES_OK;
}

lv_res_t voice_msg_del_session_yes_btn_action(lv_obj_t * btn, lv_event_t e)
{
    if(LV_EVENT_CLICKED != e) {
        return;
    }
    lv_obj_t * act_obj = lv_obj_get_parent(btn);
    if(!act_obj) return;
        
    lv_voice_msg_chat_ext_t * ext = lv_obj_get_ext_attr(act_obj);
    if(!ext) return;

	printf("%s,userid:%s,session_id:%s",__func__,ext->cur_contact->userId,ext->cur_contact->imei);
	
    voice_msg_del_session(ext->contact_list, ext->cur_contact);

    //voice_msg_del_interface(ACT_ID_VOICE_MSG);
    voice_msg_del_interface(ACT_ID_VOICE_MSG_DEL_CONFIRM);
    
    return LV_RES_OK;
}



void voice_msg_del_fiend_no_btn_action(lv_obj_t * btn, lv_event_t e)
{
    if(LV_EVENT_CLICKED == e) {
        lv_watch_go_back();
    }    
}


lv_obj_t * voice_msg_del_fiend_confirm_create(lv_ll_t * contact_list, voice_msg_contact_t * contact)
{
    lv_obj_t * label;
    lv_obj_t * label_tip;
    lv_obj_t * btn_no;
    lv_obj_t * btn_yes;

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_DEL_CONFIRM;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_del_fiend_confirm_prepare_destory;
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    lv_voice_msg_chat_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_chat_ext_t));
    LV_ASSERT_MEM(ext);
    ext->list = NULL;
    ext->contact_list_update = false;
    ext->contact_list = contact_list;
    ext->cur_contact = contact;
    ext->label_no_msg = NULL;
    ext->img_emo = NULL;
    ext->img_touch = NULL;
    ext->img_call = NULL;
    ext->img_vcall = NULL;
	printf("%s,userid:%s,session_id:%s",__func__,ext->cur_contact->userId,ext->cur_contact->imei);
    label = lv_label_create(obj, NULL);
    lv_obj_set_size(label, LV_HOR_RES, LV_VER_RES / 6);
    lv_label_set_text_id(label, WATCH_TEXT_ID_DEL_FRIEND);
    lv_obj_add_style(label,LV_LABEL_PART_MAIN, &lv_watch_font30);
    lv_obj_align(label, obj, LV_ALIGN_IN_TOP_MID, 0, LV_VER_RES/10);

    label_tip = lv_label_create(obj, NULL);
	char nam_str[100]={0};
	sprintf(nam_str,"%s: %s",lv_lang_get_text(WATCH_TEXT_ID_DEL_CONTACT_CONFIRM),contact->name);
    lv_label_set_text(label_tip, nam_str);
    lv_obj_add_style(label_tip,LV_LABEL_PART_MAIN, &lv_watch_font20);
    lv_label_set_long_mode(label_tip, LV_LABEL_LONG_BREAK);
    lv_obj_set_width(label_tip, LV_HOR_RES-10);
    lv_obj_align(label_tip, NULL, LV_ALIGN_IN_TOP_MID, 0,60);

    btn_no = lv_imgbtn_create(obj, NULL);
    lv_imgbtn_set_src(btn_no, LV_BTN_STATE_RELEASED, ICON_CANCEL);
    lv_imgbtn_set_src(btn_no, LV_BTN_STATE_PRESSED, ICON_CANCEL);
    lv_obj_align(btn_no, NULL, LV_ALIGN_IN_BOTTOM_LEFT, LV_HOR_RES/10, -LV_VER_RES/24);
    lv_obj_set_event_cb(btn_no, voice_msg_del_fiend_no_btn_action);
    lv_watch_obj_add_element(btn_no);

    btn_yes = lv_imgbtn_create(obj, NULL);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_RELEASED, ICON_OK);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_PRESSED, ICON_OK);
    lv_obj_align(btn_yes, NULL, LV_ALIGN_IN_BOTTOM_RIGHT, -LV_HOR_RES/10, -LV_VER_RES/24);
    lv_obj_set_event_cb(btn_yes, voice_msg_del_fiend_yes_btn_action);
    lv_watch_obj_add_element(btn_yes);

    return obj;
}

lv_obj_t * voice_msg_del_session_confirm_create(lv_ll_t * contact_list, voice_msg_contact_t * contact)
{
    lv_obj_t * label;
    lv_obj_t * label_tip;
    lv_obj_t * btn_no;
    lv_obj_t * btn_yes;

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_DEL_CONFIRM;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_del_fiend_confirm_prepare_destory;
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    lv_voice_msg_chat_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_chat_ext_t));
    LV_ASSERT_MEM(ext);
    ext->list = NULL;
    ext->contact_list_update = false;
    ext->contact_list = contact_list;
    ext->cur_contact = contact;
    ext->label_no_msg = NULL;
    ext->img_emo = NULL;
    ext->img_touch = NULL;
    ext->img_call = NULL;
    ext->img_vcall = NULL;
	printf("%s,userid:%s,session_id:%s",__func__,ext->cur_contact->userId,ext->cur_contact->imei);
	
    label = lv_label_create(obj, NULL);
    lv_obj_set_size(label, LV_HOR_RES, LV_VER_RES / 6);
    lv_label_set_text_id(label, WATCH_TEXT_ID_SESSION_DEL);
    lv_obj_add_style(label,LV_LABEL_PART_MAIN, &lv_watch_font30);
    lv_obj_align(label, obj, LV_ALIGN_IN_TOP_MID, 0, LV_VER_RES/10);

    label_tip = lv_label_create(obj, NULL);
	char nam_str[100]={0};
	sprintf(nam_str,"%s: %s",lv_lang_get_text(WATCH_TEXT_ID_DEL_SESSION_CONFIRM),contact->name);
    lv_label_set_text(label_tip, nam_str);
    lv_obj_add_style(label_tip,LV_LABEL_PART_MAIN, &lv_watch_font20);
    lv_label_set_long_mode(label_tip, LV_LABEL_LONG_BREAK);
    lv_obj_set_width(label_tip, LV_HOR_RES-10);
    lv_obj_align(label_tip, NULL, LV_ALIGN_IN_TOP_MID, 0,60);

    btn_no = lv_imgbtn_create(obj, NULL);
    lv_imgbtn_set_src(btn_no, LV_BTN_STATE_RELEASED, ICON_CANCEL);
    lv_imgbtn_set_src(btn_no, LV_BTN_STATE_PRESSED, ICON_CANCEL);
    lv_obj_align(btn_no, NULL, LV_ALIGN_IN_BOTTOM_LEFT, LV_HOR_RES/10, -LV_VER_RES/24);
    lv_obj_set_event_cb(btn_no, voice_msg_del_fiend_no_btn_action);
    lv_watch_obj_add_element(btn_no);

    btn_yes = lv_imgbtn_create(obj, NULL);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_RELEASED, ICON_OK);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_PRESSED, ICON_OK);
    lv_obj_align(btn_yes, NULL, LV_ALIGN_IN_BOTTOM_RIGHT, -LV_HOR_RES/10, -LV_VER_RES/24);
    lv_obj_set_event_cb(btn_yes, voice_msg_del_session_yes_btn_action);
    lv_watch_obj_add_element(btn_yes);

    return obj;
}

static void voice_msg_img_event_cb(lv_obj_t * img, lv_event_t e){

    lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG);
    if(NULL == ext) return;

    if(LV_EVENT_PRESSED == e) {
        ext->pressing = true;
    } else if((true == ext->pressing) && (LV_EVENT_CLICKED == e)) {
        ext->pressing = false;
        /*click the contact*/
        lv_obj_t * img_portrait = lv_obj_get_child_back(img, NULL);
        voice_msg_contact_t * contact = voice_msg_get_user_ptr(img_portrait);
        if((0xFF != contact->mark) && (0 < contact->mark)) {
            contact->mark = 0;
            lv_obj_t * cont_mark = lv_obj_get_child_back(img, img_portrait);
            if(NULL == lv_obj_get_child_back(img, cont_mark)) {
                printf("%s: warning, cont_mark is NULL\n", __FUNCTION__);
            } else {
                lv_obj_del(cont_mark);
            }
        }

		contact->toChatFromSess=0; //ynyd
		
        voice_msg_create_chat(ext->contact_list, contact);
        #if 0 //LV_USE_OBJMASK != 0
        lv_obj_t * om = lv_obj_get_child_back(img_portrait, NULL);
        lv_obj_t * cont_mark = lv_obj_get_child_back(img_portrait, om);
        #else
        lv_obj_t * cont_mark = lv_obj_get_child_back(img_portrait, NULL);
        #endif 
        if(cont_mark) {
            lv_obj_del(cont_mark);
        }
    }
    else if((true == ext->pressing)&& (LV_EVENT_LONG_PRESSED == e))
    {
        lv_obj_t * img_portrait = lv_obj_get_child_back(img, NULL);
        voice_msg_contact_t * contact = voice_msg_get_user_ptr(img_portrait);
        #if USE_LV_WATCH_MAKE_FRIENDS_CODE != 0
        if((contact->index < NV_WATCH_MAX_FRIENDS_NUM) && (voice_msg_friend_id_all_is_num(contact->imei)))
        #else 
        if(contact->index < NV_WATCH_MAX_FRIENDS_NUM)
        #endif
        {
			ext->pressing = false;
			voice_msg_del_fiend_confirm_create(ext->contact_list, contact);
        }
    }
}

static void voice_session_msg_img_event_cb(lv_obj_t * img, lv_event_t e){

    lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_SESSION);
    if(NULL == ext) return;

    if(LV_EVENT_PRESSED == e) {
        ext->pressing = true;
    } else if((true == ext->pressing) && (LV_EVENT_CLICKED == e)) {
        ext->pressing = false;
        /*click the contact*/
        lv_obj_t * img_portrait = lv_obj_get_child_back(img, NULL);
        voice_msg_contact_t * contact = voice_msg_get_user_ptr(img_portrait);
        if((0xFF != contact->mark) && (0 < contact->mark)) {
            contact->mark = 0;
            lv_obj_t * cont_mark = lv_obj_get_child_back(img, img_portrait);
            if(NULL == lv_obj_get_child_back(img, cont_mark)) {
                printf("%s: warning, cont_mark is NULL\n", __FUNCTION__);
            } else {
                lv_obj_del(cont_mark);
            }
        }

		contact->toChatFromSess=1;
		
        voice_msg_create_chat(ext->contact_list, contact);
        #if 0 //LV_USE_OBJMASK != 0
        lv_obj_t * om = lv_obj_get_child_back(img_portrait, NULL);
        lv_obj_t * cont_mark = lv_obj_get_child_back(img_portrait, om);
        #else
        lv_obj_t * cont_mark = lv_obj_get_child_back(img_portrait, NULL);
        #endif 
        if(cont_mark) {
            lv_obj_del(cont_mark);
        }
    }
    else if((true == ext->pressing)&& (LV_EVENT_LONG_PRESSED == e))
    {
        lv_obj_t * img_portrait = lv_obj_get_child_back(img, NULL);
        voice_msg_contact_t * contact = voice_msg_get_user_ptr(img_portrait);
        #if USE_LV_WATCH_MAKE_FRIENDS_CODE != 0
        if((contact->index < NV_WATCH_MAX_FRIENDS_NUM) && (voice_msg_friend_id_all_is_num(contact->imei)))
        #else 
        if(contact->index < NV_WATCH_MAX_FRIENDS_NUM)
        #endif
        {
			ext->pressing = false;
			voice_msg_del_session_confirm_create(ext->contact_list, contact);
        }
    }
}


static lv_res_t voice_msg_page_scrl_signal(lv_obj_t * page_scrl, lv_signal_t sign, void * param)
{
    lv_res_t res;

    lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG);
    if(NULL == ext) return LV_RES_INV;

    /* Include the ancient signal function */
    res = ext->ancient_page_scrl_signal(page_scrl, sign, param);
    if(res != LV_RES_OK) return res;

    if(LV_SIGNAL_COORD_CHG == sign) {
        if(true == ext->pressing) {
            ext->pressing = false;
        }
    } else if(LV_SIGNAL_DRAG_END == sign) {
        lv_coord_t init_y = lv_obj_get_style_pad_top(lv_obj_get_parent(page_scrl),
                                                     LV_PAGE_PART_BG);
        lv_coord_t y = lv_obj_get_y(page_scrl);
        if(init_y < y) {
            return LV_RES_INV;
        } else if(init_y == y) {
            return LV_RES_OK;
        }

        lv_coord_t top = lv_obj_get_style_pad_top(lv_obj_get_parent(page_scrl),
                                                  LV_PAGE_PART_SCROLLABLE);
        if((0 > y) && (-y > (top + ext->img_height / 2))) {
            y = -y - top;
            lv_coord_t inner = lv_obj_get_style_pad_inner(lv_obj_get_parent(page_scrl),
                                                          LV_PAGE_PART_SCROLLABLE);
            lv_coord_t move_height = ext->img_height + inner;
            uint8_t i = y / move_height;
            y -= i * move_height;
            lv_coord_t aligned_y;
            if(ext->img_height / 2 < y) {
                aligned_y = init_y - (top + (i + 1) * move_height);
            } else {
                aligned_y = init_y - (top + i * move_height);
            }
            lv_obj_set_y(page_scrl, aligned_y);
        } else {
            lv_obj_set_y(page_scrl, init_y);
        }
    }

    return LV_RES_OK;
}


static lv_res_t voice_session_msg_page_scrl_signal(lv_obj_t * page_scrl, lv_signal_t sign, void * param)
{
    lv_res_t res;

    lv_voice_msg_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_SESSION);
    if(NULL == ext) return LV_RES_INV;

    /* Include the ancient signal function */
    res = ext->ancient_page_scrl_signal(page_scrl, sign, param);
    if(res != LV_RES_OK) return res;

    if(LV_SIGNAL_COORD_CHG == sign) {
        if(true == ext->pressing) {
            ext->pressing = false;
        }
    } else if(LV_SIGNAL_DRAG_END == sign) {
        lv_coord_t init_y = lv_obj_get_style_pad_top(lv_obj_get_parent(page_scrl),
                                                     LV_PAGE_PART_BG);
        lv_coord_t y = lv_obj_get_y(page_scrl);
        if(init_y < y) {
            return LV_RES_INV;
        } else if(init_y == y) {
            return LV_RES_OK;
        }

        lv_coord_t top = lv_obj_get_style_pad_top(lv_obj_get_parent(page_scrl),
                                                  LV_PAGE_PART_SCROLLABLE);
        if((0 > y) && (-y > (top + ext->img_height / 2))) {
            y = -y - top;
            lv_coord_t inner = lv_obj_get_style_pad_inner(lv_obj_get_parent(page_scrl),
                                                          LV_PAGE_PART_SCROLLABLE);
            lv_coord_t move_height = ext->img_height + inner;
            uint8_t i = y / move_height;
            y -= i * move_height;
            lv_coord_t aligned_y;
            if(ext->img_height / 2 < y) {
                aligned_y = init_y - (top + (i + 1) * move_height);
            } else {
                aligned_y = init_y - (top + i * move_height);
            }
            lv_obj_set_y(page_scrl, aligned_y);
        } else {
            lv_obj_set_y(page_scrl, init_y);
        }
    }

    return LV_RES_OK;
}

static lv_obj_t * voice_msg_add_contact(lv_voice_msg_ext_t * ext, voice_msg_contact_t * contact)
{
    if(NULL == ext) return NULL;
    if(NULL == contact) return NULL;
	WS_PRINTF("voice_msg_add_contact==sess=%d=id=%d=",contact->isInsession,contact->portrait_id);

    lv_bidi_dir_t dir = lv_obj_get_base_dir(ext->page);

    lv_obj_t * img_bkg = lv_img_create(ext->page, NULL);
    lv_img_set_src(img_bkg, ICON_BKG);
    lv_obj_set_click(img_bkg, true);
    lv_page_glue_obj(img_bkg, true);
    if(VOICE_MSG_FROM_SELF == ext->other_app) {
		if(contact->isInsession)
			lv_obj_set_event_cb(img_bkg, voice_session_msg_img_event_cb);
		else
        	lv_obj_set_event_cb(img_bkg, voice_msg_img_event_cb);
    } else if(VOICE_MSG_FROM_ALBUM == ext->other_app){
    #if USE_LV_WATCH_ALBUM != 0
        lv_obj_set_event_cb(img_bkg, album_photo_share_event_cb);
    #endif
    }  else if(VOICE_MSG_FROM_IMGVIEWER == ext->other_app){
#if USE_LV_WATCH_IMGVIEWER
        lv_obj_set_event_cb(img_bkg, imgviewer_photo_share_event_cb);
#endif
    }
    lv_obj_set_adv_hittest(img_bkg, false);
    lv_obj_add_protect(img_bkg, LV_PROTECT_PRESS_LOST);
    lv_watch_obj_add_element(img_bkg);

    void *img_src = voice_msg_get_img_src(contact->portrait_id, contact->imei);
	
    #if 0 //LV_USE_OBJMASK != 0
    lv_obj_t * img_portrait = lv_cont_create(img_bkg, NULL);
    lv_obj_add_style(img_portrait, LV_CONT_PART_MAIN, &lv_watch_style_transp);
    lv_cont_set_fit(img_portrait, LV_FIT_TIGHT);
    lv_cont_set_layout(img_portrait, LV_LAYOUT_OFF);
    lv_img_header_t img_header;
    memset(&img_header, 0, sizeof(lv_img_header_t));
    lv_img_decoder_get_info(img_src, &img_header);
    lv_obj_t * om = lv_objmask_create(img_portrait, NULL);
    lv_obj_set_size(om, img_header.w, img_header.h);
    lv_obj_align(om, NULL, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_click(om, false);
    lv_area_t a;
    lv_draw_mask_radius_param_t r1;
    a.x1 = 0; a.y1 = 0;
    a.x2 = img_header.w - 1;
    a.y2 = img_header.h - 1;
    lv_draw_mask_radius_init(&r1, &a, LV_RADIUS_CIRCLE, false);
    lv_objmask_add_mask(om, &r1);    
    lv_obj_t *img = lv_img_create(om, NULL);    
    lv_obj_set_click(img, false);
    lv_img_set_src(img, img_src);
    #else 
    lv_obj_t * img_portrait = lv_img_create(img_bkg, NULL);
    lv_img_set_src(img_portrait, img_src);
    #endif 
    lv_coord_t space = 2;
    lv_coord_t zoom_h = lv_obj_get_height(img_bkg) - 2 * space;
    lv_coord_t img_portrait_w = lv_obj_get_width(img_portrait);
    lv_coord_t img_portrait_h = lv_obj_get_height(img_portrait);
    lv_coord_t zoom_w = img_portrait_w * zoom_h / img_portrait_h;
    lv_img_set_zoom(img_portrait, LV_IMG_ZOOM_NONE * zoom_h / img_portrait_h);
    if(LV_BIDI_DIR_RTL == dir) {
        lv_obj_align(img_portrait, img_bkg, LV_ALIGN_IN_RIGHT_MID,
                     - space - (zoom_w - img_portrait_w) / 2, 0);
    } else {
        lv_obj_align(img_portrait, img_bkg, LV_ALIGN_IN_LEFT_MID,
                     space + (zoom_w - img_portrait_w) / 2, 0);
    }
    voice_msg_set_user_ptr(img_portrait, contact);

    if((0xFF != contact->mark) && (0 < contact->mark)) {
        lv_obj_t * cont_mark = lv_cont_create(img_bkg, NULL);
        lv_obj_set_click(cont_mark, false);
        lv_obj_t * label_mark = lv_label_create(cont_mark, NULL);
        lv_obj_add_style(label_mark, LV_LABEL_PART_MAIN, &ext->style_label_mark);
        lv_coord_t h_font
            = lv_font_get_line_height(lv_obj_get_style_text_font(label_mark, LV_LABEL_PART_MAIN))
            + 2;
        if(10 > contact->mark) {
            lv_obj_set_size(cont_mark, h_font, h_font);
        } else if(100 > contact->mark) {
            lv_obj_set_size(cont_mark, h_font * 3 / 2, h_font);
        } else {
            lv_obj_set_size(cont_mark, h_font * 2, h_font);
        }
        if(LV_BIDI_DIR_RTL == dir) {
            lv_obj_align(cont_mark, img_bkg, LV_ALIGN_IN_TOP_RIGHT,
                         - space - zoom_w + lv_obj_get_width(cont_mark), space);
        } else {
            lv_obj_align(cont_mark, img_bkg, LV_ALIGN_IN_TOP_LEFT,
                         space + zoom_w - lv_obj_get_width(cont_mark), space);
        }
        lv_obj_add_style(cont_mark, LV_CONT_PART_MAIN, &ext->style_cont_mark);
        if(9 < contact->mark)
		 	lv_label_set_text(label_mark, "9+");
		else{
	        char text[4] = {0};
	        snprintf(text, 4, "%d", contact->mark);
       		lv_label_set_text(label_mark, text);
		}
        lv_obj_align(label_mark, cont_mark, LV_ALIGN_CENTER, 0, 1);
    }

    lv_obj_t * label = lv_label_create(img_bkg, NULL);
    lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font18_black);
    lv_label_set_long_mode(label, LV_LABEL_LONG_SROLL_CIRC);
    lv_coord_t inner = 8;
    lv_obj_set_width(label, lv_obj_get_width(img_bkg) - 2 * space - zoom_w - inner);
    lv_label_set_text(label, contact->name);
    if(LV_BIDI_DIR_RTL == dir) {
        lv_obj_align(label, img_bkg, LV_ALIGN_IN_LEFT_MID, inner, 0);
    } else {
        lv_obj_align(label, img_bkg, LV_ALIGN_IN_RIGHT_MID, -inner, 0);
    }

    return img_bkg;
}


static void voice_msg_contact_list_create(lv_voice_msg_ext_t * ext, lv_coord_t pos)
{
    lv_page_set_scrollable_fit2(ext->page, LV_FIT_NONE, LV_FIT_TIGHT);
    lv_obj_t * img;
    voice_msg_contact_t * contact = _lv_ll_get_head(ext->contact_list);
    while(NULL != contact) {
		ws_printf("voice_msg_contact_list_create +++++" );
        img = voice_msg_add_contact(ext, contact);
        contact = _lv_ll_get_next(ext->contact_list, contact);
    }

    ext->img_height = lv_obj_get_height(img);

    // add blank at the end of page scrl
    lv_coord_t move_height = ext->img_height + lv_obj_get_style_pad_inner(ext->page, LV_PAGE_PART_SCROLLABLE);
    lv_obj_t * page_scrl = lv_page_get_scrl(ext->page);
    lv_coord_t height_scrl = lv_obj_get_height(page_scrl) + lv_obj_get_height(ext->page) - 2 * move_height;
    lv_page_set_scrollable_fit(ext->page, false);
    lv_obj_set_height(page_scrl, height_scrl);
    lv_obj_set_y(page_scrl, pos);
}


static void voice_msg_contacts_create(lv_obj_t * obj,uint8_t type)
{
    lv_voice_msg_ext_t * ext = lv_obj_get_ext_attr(obj);
    if(NULL == ext) return;
	
	ws_printf("%s--ext->other_app:%d--type=%d\n", __FUNCTION__,ext->other_app,type);

    lv_coord_t width_obj = lv_obj_get_width(obj);
    lv_obj_t * content = lv_cont_create(obj, NULL);
    LV_ASSERT_MEM(content);
    lv_obj_set_size(content, width_obj, lv_obj_get_height(obj));
    lv_cont_set_layout(content, LV_LAYOUT_COLUMN_MID);
    lv_obj_set_style_local_bg_color(content,
                                    LV_CONT_PART_MAIN,
                                    LV_STATE_DEFAULT,
                                    LV_COLOR_MAKE(0, 0, 0)); //(47, 233, 248)
    lv_obj_set_click(content, false);
    lv_obj_align(content, obj, LV_ALIGN_CENTER, 0, 0);

    lv_obj_t * label_cont;
    if(VOICE_MSG_FROM_SELF != ext->other_app)
        label_cont = voice_msg_create_title(content,
                                            (char *)(lv_lang_get_text(WATCH_TEXT_ID_CHOOSE_CONTACT)), 0);
    else{
		if(type==1)
        label_cont = voice_msg_create_title(content,
                                            (char *)(lv_lang_get_text(WATCH_TEXT_ID_WECHAT)),
                                             ACT_ID_VOICE_MSG_SESSION);
			
		else
        label_cont = voice_msg_create_title(content,
                                            (char *)(lv_lang_get_text(WATCH_TEXT_ID_WECHAT)),
                                             ACT_ID_VOICE_MSG);
	}

    ext->page = lv_page_create(content, NULL);
    LV_ASSERT_MEM(ext->page);
    lv_coord_t height_page = lv_obj_get_height(content) - lv_obj_get_height(label_cont);
    lv_obj_set_size(ext->page, lv_obj_get_width(content), height_page);
    lv_page_set_scrollbar_mode(ext->page, LV_SCROLLBAR_MODE_OFF);
    lv_obj_add_style(ext->page, LV_PAGE_PART_BG, &lv_watch_style_transp);
    lv_obj_set_style_local_bg_opa(ext->page,
                                  LV_PAGE_PART_SCROLLABLE,
                                  LV_STATE_DEFAULT,
                                  LV_OPA_0);
    lv_obj_set_style_local_pad_top(ext->page,
                                   LV_PAGE_PART_SCROLLABLE,
                                   LV_STATE_DEFAULT,
                                   0);
    lv_obj_set_style_local_pad_bottom(ext->page,
                                      LV_PAGE_PART_SCROLLABLE,
                                      LV_STATE_DEFAULT,
                                      0);
    lv_obj_set_style_local_pad_inner(ext->page,
                                     LV_PAGE_PART_SCROLLABLE,
                                     LV_STATE_DEFAULT,
                                     8);
    lv_obj_set_style_local_bg_color(ext->page,
                                    LV_PAGE_PART_SCROLLBAR,
                                    LV_STATE_DEFAULT,
                                    LV_COLOR_MAKE(0, 150, 250));  //LV_COLOR_MAKE(16, 112, 194);
    lv_obj_set_style_local_pad_inner(ext->page,
                                     LV_PAGE_PART_SCROLLBAR,
                                     LV_STATE_DEFAULT,
                                     LV_DPI / 16);
    lv_obj_set_style_local_radius(ext->page,
                                  LV_PAGE_PART_SCROLLBAR,
                                  LV_STATE_DEFAULT,
                                  0);
    lv_page_set_scrl_layout(ext->page, LV_LAYOUT_COLUMN_MID);
    lv_obj_t * page_scrl = lv_page_get_scrl(ext->page);
    ext->ancient_page_scrl_signal = lv_obj_get_signal_cb(page_scrl);
	if(type==1)
    lv_obj_set_signal_cb(page_scrl, voice_session_msg_page_scrl_signal);
	else
    lv_obj_set_signal_cb(page_scrl, voice_msg_page_scrl_signal);
    lv_watch_obj_add_element(ext->page);
    lv_watch_obj_add_element(page_scrl);

    voice_msg_contact_list_create(ext, 2);
}


static void voice_msg_img_close_event_cb(lv_obj_t * img, lv_event_t e)
{
    if(LV_EVENT_CLICKED == e) {

		lv_voice_msg_incoming_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
		if(ext){
			//ynyd
			if(ext->cur_contact)
				lv_mem_free(ext->cur_contact);
		}
	
        voice_msg_del_top_interface();
#ifndef USE_WATCH_LITE	
	    watch_thirdparty_resume(THIRDPARTY_PAUSE_VOICEMSG);
#endif
    }
}

static void voice_msg_img_back_event_cb(lv_obj_t * img, lv_event_t e)
{
    if(LV_EVENT_CLICKED == e) {		
		watch_thirdparty_exit();

        lv_obj_t * activity_obj = lv_watch_get_top_activity_obj();
        if(activity_obj == NULL) return LV_RES_INV;
        lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(activity_obj);
        if(NULL == ext) return LV_RES_INV;
        lv_obj_t * obj = NULL;
        lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &obj);
        lv_voice_msg_incoming_ext_t * obj_ext = lv_obj_get_ext_attr(obj);
        if(NULL == obj_ext) return LV_RES_INV;

        lv_ll_t * contact_list = obj_ext->contact_list;
        obj_ext->contact_list = NULL;
        voice_msg_contact_t * cur_contact = obj_ext->cur_contact;
        if(cur_contact) {
            cur_contact->mark = 0;
        }

        if(ext->prepare_destory) {
            ext->prepare_destory(activity_obj);
        }
        lv_obj_del(activity_obj);

        if(cur_contact) {
            voice_msg_create_chat(contact_list, cur_contact);
        } else {
            printf("contact has been deleted in %s\n", __FUNCTION__);
            obj = voice_msg_create(NULL, false, contact_list);
            LV_ASSERT_MEM(obj);
        }
    }

    return LV_RES_OK;
}

static void voice_msg_incoming_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    lv_voice_msg_incoming_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
    if(NULL == ext) return;
#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
    if(true == ext->voice_playing) {
        if(0 == ext->voice_size) {
            Hal_File_Play_End();
        } else {
            Hal_Tone_Play_End();
        }
    }
    Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
#endif
    if(ext->contact_list) {
        voice_msg_write_nvm(ext->contact_list);
        _lv_ll_clear(ext->contact_list);
        lv_mem_free(ext->contact_list);
    }
#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
    if(ext->voice_data) {
        lv_mem_free(ext->voice_data);
    } else if(ext->img_src) {
        lv_mem_free(ext->img_src->data);
        lv_mem_free(ext->img_src);
    }
#endif

    lv_watch_png_cache_all_free();

#if VOICE_MSG_TEST != 0
    /* voice_msg_clear_nvm_for_test(); */
    /* voice_msg_test_clear(); */
#endif
#ifndef USE_WATCH_LITE	
	watch_thirdparty_resume(THIRDPARTY_PAUSE_VOICEMSG);
#endif
}

#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
static void voice_msg_voice_over_cb(void * para)
{
    lv_voice_msg_incoming_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
    if(NULL == ext) return;

    ext->index = 0xFF;
    ext->voice_playing = false;
    lv_anim_del(ext->img_voice_anim, NULL);
    lv_img_set_src(ext->img_voice_anim, ICON_PLAYING3);
    voice_msg_set_user_num(ext->img_voice_anim, 0);
    Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
}

void voice_msg_one_unread_msg_anim_voice(lv_anim_t * a)
{
    lv_obj_t * img_voice_anim = a->var;
    uint32_t anim_step = voice_msg_get_user_num(img_voice_anim);
    if(3 == anim_step) {
        lv_img_set_src(img_voice_anim, ICON_PLAYING1);
        voice_msg_set_user_num(img_voice_anim, 1);
    } else if(2 == anim_step) {
        lv_img_set_src(img_voice_anim, ICON_PLAYING3);
        voice_msg_set_user_num(img_voice_anim, 3);
    } else if(1 == anim_step) {
        lv_img_set_src(img_voice_anim, ICON_PLAYING2);
        voice_msg_set_user_num(img_voice_anim, 2);
    }
}

static void voice_msg_one_unread_msg_voice_event_cb(lv_obj_t * img, lv_event_t e)
{
    if(LV_EVENT_CLICKED == e) {
        lv_voice_msg_incoming_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_INCOMING);
        if(NULL == ext) return;
        LV_ASSERT_MEM(ext->voice_data);
        uint32_t anim_step = voice_msg_get_user_num(ext->img_voice_anim);
        if(0 == anim_step) {
            /*animation not started*/
            lv_anim_t a = {};
            lv_anim_init(&a);
            lv_anim_set_var(&a, ext->img_voice_anim);
            lv_anim_set_start_cb(&a, (lv_anim_ready_cb_t)voice_msg_one_unread_msg_anim_voice);
            lv_anim_set_repeat_count(&a, LV_ANIM_REPEAT_INFINITE);
            lv_anim_set_time(&a, 400);
            lv_anim_start(&a);
            voice_msg_set_user_num(ext->img_voice_anim, 3);

            if(0xFF != ext->index) {
                app_adaptor_voice_msg_chat_id_t * id = (app_adaptor_voice_msg_chat_id_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_chat_id_t));
                voice_msg_fill_chat_id(id, ext->cur_contact);
                app_adaptor_voice_msg_read_voice_req(id, ext->index);
                ext->index = 0xFF;
            }
        } else {
            voice_msg_play_end(ext->voice_size);
        }
        Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_5,voice_msg_audio_ctrl_callback);
        Voice_Msg_Play_Onetime(ext->voice_data,
                               ext->voice_size,
                                #if LV_WATCH_VOLUME_LEVEL_MAX < 10
                                  query_current_volume()+(HAL_AUDIO_SPK_LEVEL_MAX - LV_WATCH_VOLUME_LEVEL_MAX),
                                #else
                                  query_current_volume(),
                                #endif 
                               voice_msg_voice_over_cb,
                               NULL);

        ext->voice_playing = true;
    }
}

static void voice_msg_create_one_unread_msg(app_adaptor_voice_msg_t * info,
        lv_ll_t * contact_list,
        voice_msg_contact_t * contact)
{
    WS_PRINTF("%s: contact mark %d\n", __FUNCTION__, contact->mark);

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_INCOMING;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_incoming_prepare_destory;
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    lv_voice_msg_incoming_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_incoming_ext_t));
    LV_ASSERT_MEM(ext);
    ext->voice_playing = false;
    ext->index = 0xFF;
    ext->contact_list = contact_list;
    ext->cur_contact = contact;
    ext->voice_size = 0;
    ext->voice_data = NULL;
    ext->img_src = NULL;
    ext->update = false;

    lv_coord_t width_obj = lv_obj_get_width(obj);
    lv_obj_t * cont = lv_cont_create(obj, NULL);
    lv_obj_set_size(cont, width_obj, lv_obj_get_height(obj));
    lv_obj_set_click(cont, false);
    lv_obj_align(cont, obj, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_local_bg_color(cont,
                                    LV_CONT_PART_MAIN,
                                    LV_STATE_DEFAULT,
                                    LV_COLOR_MAKE(194, 252, 252));

    lv_bidi_dir_t dir = lv_obj_get_base_dir(cont);

    lv_font_t * font = LV_THEME_WATCH_NIGHT_FONT_NORMAL;
    lv_coord_t font_h = lv_font_get_line_height(font);
    lv_obj_t * img_portrait = lv_img_create(cont, NULL);
    lv_img_set_src(img_portrait, voice_msg_get_img_src(contact->portrait_id));
    lv_coord_t inner = 8;
    lv_coord_t zoom_h = font_h * 2 + inner;
    lv_coord_t img_portrait_w = lv_obj_get_width(img_portrait);
    lv_coord_t img_portrait_h = lv_obj_get_height(img_portrait);
    lv_img_set_zoom(img_portrait, LV_IMG_ZOOM_NONE * zoom_h / img_portrait_h);
    lv_coord_t hor = 5;
    lv_coord_t zoom_w = img_portrait_w * zoom_h / img_portrait_h;
    if(LV_BIDI_DIR_RTL == dir) {
        lv_obj_align(img_portrait, cont, LV_ALIGN_IN_TOP_RIGHT, - hor - (zoom_w - img_portrait_w) / 2,
                     inner + (zoom_h - img_portrait_h) / 2);
    } else {
        lv_obj_align(img_portrait, cont, LV_ALIGN_IN_TOP_LEFT, hor + (zoom_w - img_portrait_w) / 2,
                     inner + (zoom_h - img_portrait_h) / 2);
    }

    lv_coord_t width_name = width_obj - hor * 2 - zoom_h - inner;
    lv_obj_t * label_name = lv_label_create(cont, NULL);
    lv_obj_set_style_local_text_font(label_name, LV_LABEL_PART_MAIN, LV_STATE_DEFAULT, font);
    lv_obj_set_style_local_text_color(label_name, LV_LABEL_PART_MAIN, LV_STATE_DEFAULT,
                                      LV_COLOR_MAKE(64, 225, 238));
    lv_label_set_long_mode(label_name, LV_LABEL_LONG_SROLL_CIRC);
    lv_obj_set_width(label_name, width_name);
    lv_label_set_text(label_name, contact->name);
    lv_coord_t offset_x = inner + (zoom_w - img_portrait_w) / 2;
#if USE_LV_WATCH_WS_BASE != 0
    lv_obj_align(cont_name, img_portrait, LV_ALIGN_OUT_RIGHT_MID, 8, 0);
#else
    if((VOICE_MSG_PORTRAIT_ID_FAMILY_GROUP == contact->portrait_id)
       || (VOICE_MSG_PORTRAIT_ID_FRIENDS_GROUP == contact->portrait_id)) {
        lv_obj_t * label_child_name = lv_label_create(cont, label_name);
        lv_label_set_text(label_child_name, info->msg->name);

        if(LV_BIDI_DIR_RTL == dir) {
            lv_obj_align(label_name, img_portrait, LV_ALIGN_OUT_LEFT_MID, -offset_x,
                         -font_h / 2 - inner / 4);
            lv_obj_align(label_child_name, img_portrait, LV_ALIGN_OUT_LEFT_MID, -offset_x,
                         font_h / 2 + inner / 4);
        } else {
            lv_obj_align(label_name, img_portrait, LV_ALIGN_OUT_RIGHT_MID, offset_x,
                         -font_h / 2 - inner / 4);
            lv_obj_align(label_child_name, img_portrait, LV_ALIGN_OUT_RIGHT_MID, offset_x,
                         font_h / 2 + inner / 4);
        }
    } else {
        if(LV_BIDI_DIR_RTL == dir) {
            lv_obj_align(label_name, img_portrait, LV_ALIGN_OUT_LEFT_MID, -offset_x, 0);
        } else {
            lv_obj_align(label_name, img_portrait, LV_ALIGN_OUT_RIGHT_MID, offset_x, 0);
        }
    }
#endif 

    lv_obj_t * img_close = lv_img_create(cont, NULL);
    lv_img_set_src(img_close, ICON_CLOSE);
    lv_obj_set_click(img_close, true);
    lv_obj_set_event_cb(img_close, voice_msg_img_close_event_cb);
    lv_obj_set_adv_hittest(img_close, false);
    lv_watch_obj_add_element(img_close);

    lv_obj_t * img_back = lv_img_create(cont, NULL);
    lv_img_set_src(img_back, ICON_BACK);
    lv_obj_set_click(img_back, true);
    lv_obj_set_event_cb(img_back, voice_msg_img_back_event_cb);
    lv_obj_set_adv_hittest(img_back, false);
    lv_watch_obj_add_element(img_back);

    if(LV_BIDI_DIR_RTL == dir) {
        lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_LEFT, 5, -8);
        lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -5, -8);
    } else {
        lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_LEFT, 5, -8);
        lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -5, -8);
    }

    if(WATCH_VOICE_MSG_TYPE_TEXT == info->msg->type) {
        lv_obj_t * img_text = lv_img_create(cont, NULL);
        lv_img_set_src(img_text, ICON_TEXT_BKG);
        lv_obj_align(img_text, cont, LV_ALIGN_CENTER, 0, 0);

        lv_obj_t * label_text = lv_label_create(img_text, NULL);
        lv_obj_add_style(label_text, LV_LABEL_PART_MAIN, &lv_watch_font20_black);
        lv_label_set_long_mode(label_text, LV_LABEL_LONG_BREAK);
        lv_coord_t width_label_text = lv_obj_get_width(img_text) - 20;
        lv_coord_t height_label_text = 3 * lv_font_get_line_height(LV_THEME_WATCH_NIGHT_FONT_SMALL) + 4;
        lv_obj_set_size(label_text, width_label_text, height_label_text);
        lv_coord_t y_lable_text = (lv_obj_get_height(img_text) - height_label_text) / 2;
        lv_obj_align(label_text, img_text, LV_ALIGN_IN_TOP_LEFT, 10, y_lable_text);
        char * text = NULL;
        if(WATCH_VOICE_DATA_TYPE_FILE == info->msg->data_type) {
            /*file path string*/
            text = voice_msg_read_file_data(info->msg->content.text, VOICE_MSG_TYPE_TEXT, NULL);
            lv_label_set_text(label_text, text);
            lv_mem_free(text);
        } else {
            /*text string*/
            lv_label_set_text(label_text, info->msg->content.text);
        }
    } else if(WATCH_VOICE_MSG_TYPE_VOICE == info->msg->type) {
        ext->index = info->msg->content.voice.index;
        lv_obj_t * img_voice = lv_img_create(cont, NULL);
        lv_img_set_src(img_voice, ICON_VOICE_BUBBLE);
        lv_obj_align(img_voice, cont, LV_ALIGN_IN_LEFT_MID, 20, 0);
        lv_obj_set_click(img_voice, true);
        lv_obj_set_event_cb(img_voice, voice_msg_one_unread_msg_voice_event_cb);
        lv_obj_set_adv_hittest(img_voice, false);
        lv_watch_obj_add_element(img_voice);

        ext->img_voice_anim = lv_img_create(img_voice, NULL);
        lv_img_set_src(ext->img_voice_anim, ICON_PLAYING3);
        lv_obj_align(ext->img_voice_anim, img_voice, LV_ALIGN_IN_RIGHT_MID, -16, 0);
        voice_msg_set_user_num(ext->img_voice_anim, 0);

        uint32_t voice_size;
        if(WATCH_VOICE_DATA_TYPE_FILE == info->msg->data_type) {
            /*file path string*/
            voice_size = voice_msg_read_file_size((char *)info->msg->content.voice.file);
            ext->voice_size = 0;
        } else {
            /*data buffer*/
            voice_size = info->msg->content.voice.voice_len;
            ext->voice_size = info->msg->content.voice.voice_len;
        }
        if(VOICE_MSG_AMR_HEAD_SIZE <= voice_size) {
            voice_size -= VOICE_MSG_AMR_HEAD_SIZE;
        }
        uint8_t sec = voice_size / HAL_AMR_BUFSIZE_PER_SECOND;
        if(voice_size % HAL_AMR_BUFSIZE_PER_SECOND) sec++;
        char time[4] = {0};
        snprintf(time, 4, "%dS", sec);
        lv_obj_t * label_time = lv_label_create(cont, NULL);
        lv_obj_set_style_local_text_font(label_time,
                                         LV_LABEL_PART_MAIN,
                                         LV_STATE_DEFAULT,
                                         LV_THEME_WATCH_NIGHT_FONT_SMALL);
        lv_obj_set_style_local_text_color(label_time,
                                          LV_LABEL_PART_MAIN,
                                          LV_STATE_DEFAULT,
                                          LV_COLOR_MAKE(209, 150, 255));
        lv_label_set_text(label_time, time);
        lv_obj_align(label_time, img_voice, LV_ALIGN_OUT_RIGHT_MID, 8, 0);
        ext->voice_data = info->msg->content.voice.file;
        info->msg->content.voice.file = NULL;
    } else if((WATCH_VOICE_MSG_TYPE_EXPRESSION == info->msg->type)
              || (WATCH_VOICE_MSG_TYPE_PHOTO == info->msg->type)) {
        if(NULL != info->msg->content.img.data) {
            lv_obj_t * img_picture = lv_img_create(cont, NULL);
            if(WATCH_VOICE_DATA_TYPE_FILE == info->msg->data_type) {
                ext->img_src = NULL;
                lv_img_set_src(img_picture, info->msg->content.img.data);
            } else {
                ext->img_src = (lv_img_dsc_t *)lv_mem_alloc(sizeof(lv_img_dsc_t));
                ext->img_src->header.always_zero = 0;
                if(WATCH_VOICE_MSG_TYPE_EXPRESSION == info->msg->type) {
                    ext->img_src->header.w = VOICE_MSG_EXPRESSION_WIDTH;
                    ext->img_src->header.h = VOICE_MSG_EXPRESSION_HEIGHT;
                    ext->img_src->header.cf = LV_IMG_CF_RAW_ALPHA;
                } else {
                    ext->img_src->header.w = VOICE_MSG_PHOTO_WIDTH;
                    ext->img_src->header.h = VOICE_MSG_PHOTO_HEIGHT;
                    ext->img_src->header.cf = LV_IMG_CF_RAW;
                }
                ext->img_src->data_size = info->msg->content.img.data_size;
                ext->img_src->data = info->msg->content.img.data;
                info->msg->content.img.data = NULL;
                lv_img_set_src(img_picture, ext->img_src);
            }
            lv_obj_align(img_picture, cont, LV_ALIGN_CENTER, 0, 0);
            lv_obj_set_click(img_picture, false);
        } else {
            lv_obj_t * ta_unsupport = lv_textarea_create(cont, NULL);
            lv_cont_set_fit2(ta_unsupport, LV_FIT_NONE, LV_FIT_TIGHT);
            lv_obj_set_width(ta_unsupport, lv_obj_get_width(cont) - 8);
            lv_obj_add_style(ta_unsupport, LV_TEXTAREA_PART_BG, &lv_style_pretty);
            lv_obj_set_style_local_bg_color(ta_unsupport, LV_TEXTAREA_PART_BG,
                                            LV_STATE_DEFAULT, LV_COLOR_WHITE);
            lv_textarea_set_text(ta_unsupport, lv_lang_get_text(WATCH_TEXT_ID_IMG_UNSUPP));
            lv_obj_align(ta_unsupport, cont,
                         LV_ALIGN_IN_TOP_MID,
                         0,
                         lv_obj_get_height(img_portrait) + 6);
        }
    } else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == info->msg->type) {
    ///TODO:: felix will check it!!!
    }
}
#endif

static void voice_msg_set_multi_label(lv_obj_t * label, uint8_t unread_count)
{
	#if 0
	const char * new_msg = lv_lang_get_text(WATCH_TEXT_ID_MULTI_NEW_MSG);
	uint8_t len_new_msg = strlen(new_msg);
	uint8_t len_text = 1 + 3 + len_new_msg + 1 + 1;
	char * text = (char *)lv_mem_alloc(len_text);
	snprintf(text, len_text, "[%d%s]", unread_count, new_msg);
	lv_label_set_text(label, text);
	lv_mem_free(text);
	#else
	lv_label_set_text(label,lv_lang_get_text(WATCH_TEXT_ID_MULTI_NEW_MSG_INCOME));
	#endif 
}

static void voice_msg_create_multi_unread_msgs(lv_obj_t * obj,
        uint8_t unread_count,
        lv_ll_t * contact_list,
        voice_msg_contact_t * contact)
{
    printf("%s: unread count %d\n", __FUNCTION__, unread_count);
	
#if USE_LV_WATCH_STOPWATCH != 0
	if (stopwatch_state() == 1)  // stopwatch is running
	{
		lv_mem_free(contact);
		return; 
	}
#endif

#ifdef USE_WATCH_LITE
	watch_thirdparty_exit();
#else
	watch_thirdparty_pause(THIRDPARTY_PAUSE_VOICEMSG);
#endif
	
    lv_voice_msg_incoming_ext_t * ext;
#if VOICE_MSG_ENABLE_ONE_UNREAD_MSG != 0
    if(NULL == obj) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_MSG_INCOMING;
        activity_ext.create = NULL;
        activity_ext.prepare_destory = voice_msg_incoming_prepare_destory;
        lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);

        obj = lv_watch_obj_create(activity_obj);
        LV_ASSERT_MEM(obj);
        ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_incoming_ext_t));
        LV_ASSERT_MEM(ext);
        ext->voice_playing = false;
        ext->update = false;
    } else {
        ext = lv_obj_get_ext_attr(obj);
        if(ext->voice_playing) {
            ext->voice_playing = false;
            voice_msg_play_end(ext->voice_size);
            Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
        }
        if(ext->voice_data) {
            lv_mem_free(ext->voice_data);
        } else if(ext->img_src) {
            lv_mem_free(ext->img_src->data);
            lv_mem_free(ext->img_src);
        }
        lv_obj_clean(obj);
        if(voice_msg_is_higher_prio_task_present(ACT_ID_ANYOBJ)) {
            ext->update = true;
        }
    }
    ext->voice_size = 0;
    ext->voice_data = NULL;
    ext->img_src = NULL;
#else
    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_INCOMING;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_incoming_prepare_destory;
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_incoming_ext_t));
    LV_ASSERT_MEM(ext);
    ext->update = false;
#endif
    ext->contact_list = contact_list;
    ext->cur_contact = contact;

    lv_obj_t * cont = lv_cont_create(obj, NULL);
    lv_obj_set_size(cont, lv_obj_get_width(obj), lv_obj_get_height(obj));
    lv_obj_align(cont, obj, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_style_local_bg_color(cont,
                                    LV_CONT_PART_MAIN,
                                    LV_STATE_DEFAULT,
                                    LV_COLOR_MAKE(194, 252, 252));
    lv_watch_obj_add_element(cont);

    lv_obj_t * img_multi = lv_img_create(cont, NULL);
    lv_img_set_src(img_multi, ICON_MULTI_MSG);
	#if defined(__XF_LCD_SIZE_128X128__)
    lv_obj_align(img_multi, cont, LV_ALIGN_IN_TOP_MID, 0, 8);
	#elif defined(__XF_LCD_STYLE_ROUND__)
    lv_obj_align(img_multi, cont, LV_ALIGN_IN_TOP_MID, 0, 10);
	#else
    lv_obj_align(img_multi, cont, LV_ALIGN_IN_TOP_MID, 0, 20);
	#endif

    lv_obj_t * label_multi = lv_label_create(cont, NULL);
    lv_obj_set_style_local_text_font(label_multi,
                                     LV_LABEL_PART_MAIN,
                                     LV_STATE_DEFAULT,
                                     LV_THEME_WATCH_NIGHT_FONT_SMALL);
    lv_obj_set_style_local_text_color(label_multi,
                                      LV_LABEL_PART_MAIN,
                                      LV_STATE_DEFAULT,
                                      LV_COLOR_MAKE(34, 34, 34));
    voice_msg_set_multi_label(label_multi, unread_count);
    #if defined(__XF_LCD_SIZE_128X128__)
    lv_obj_align(label_multi, img_multi, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    #else 
    lv_obj_align(label_multi, img_multi, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    #endif
    lv_obj_t * img_close = lv_img_create(cont, NULL);
    lv_img_set_src(img_close, ICON_CLOSE);
    lv_obj_set_click(img_close, true);
    lv_obj_set_event_cb(img_close, voice_msg_img_close_event_cb);
    lv_obj_set_adv_hittest(img_close, false);
    lv_watch_obj_add_element(img_close);

    lv_obj_t * img_back = lv_img_create(cont, NULL);
    lv_img_set_src(img_back, ICON_BACK);
    lv_obj_set_click(img_back, true);
    lv_obj_set_event_cb(img_back, voice_msg_img_back_event_cb);
    lv_obj_set_adv_hittest(img_back, false);
    lv_watch_obj_add_element(img_back);

    if(LV_BIDI_DIR_RTL == lv_obj_get_base_dir(cont)) {
	#if defined(__XF_LCD_SIZE_128X128__)
		lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_LEFT, 8, -6);
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -8, -6);
	#elif defined(__XF_LCD_STYLE_ROUND__)
		lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_LEFT, 32, -22);
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -32, -22);
	#elif defined(__XF_LCD_SIZE_240X280__)
		lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_LEFT, 8, -26);
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -8, -26);
	#else
		lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_LEFT, 5, -8);
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -5, -8);
	#endif
    } else {
	#if defined(__XF_LCD_SIZE_128X128__)
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_LEFT, 8, -6);
		lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -8, -6);
	#elif defined(__XF_LCD_STYLE_ROUND__)
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_LEFT, 32, -22);
		lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -32, -22);
	#elif defined(__XF_LCD_SIZE_240X280__)
		lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_LEFT, 8, -26);
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -8, -26);
	#else
		lv_obj_align(img_close, cont, LV_ALIGN_IN_BOTTOM_LEFT, 5, -8);
        lv_obj_align(img_back, cont, LV_ALIGN_IN_BOTTOM_RIGHT, -5, -8);
	#endif
    }
}


static void voice_msg_update_multi_unread_msgs(lv_obj_t * obj, uint8_t unread_count)
{
    printf("%s: unread count %d\n", __FUNCTION__, unread_count);

    lv_obj_t * cont = lv_obj_get_child_back(obj, NULL);
    lv_obj_t * img_multi = lv_obj_get_child_back(cont, NULL);
    lv_obj_t * label_multi = lv_obj_get_child_back(cont, img_multi);
    voice_msg_set_multi_label(label_multi, unread_count);
    #if defined(__XF_LCD_SIZE_128X128__)
    lv_obj_align(label_multi, img_multi, LV_ALIGN_OUT_BOTTOM_MID, 0, 0);
    #else 
    lv_obj_align(label_multi, img_multi, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    #endif
    lv_voice_msg_incoming_ext_t * ext = lv_obj_get_ext_attr(obj);
    LV_ASSERT_MEM(ext);
    if(voice_msg_is_higher_prio_task_present(ACT_ID_ANYOBJ)) {
        ext->update = true;
    }
}


static void voice_msg_sending_anim(lv_anim_t * a)
{
    lv_obj_t * img = a->var;
    uint8_t step = voice_msg_get_user_num(img);
#if USE_LV_WATCH_MULTI_LANG_SUPPORT != 0
    lv_obj_t * label = lv_obj_get_child(img, NULL);
    switch(step) {
        case 1:
            lv_label_set_text_id(label, WATCH_TEXT_ID_MSG_SENDING2);
            voice_msg_set_user_num(img, 2);
            break;
        case 2:
            lv_label_set_text_id(label, WATCH_TEXT_ID_MSG_SENDING3);
            voice_msg_set_user_num(img, 3);
            break;
        case 3:
            lv_label_set_text_id(label, WATCH_TEXT_ID_MSG_SENDING1);
            voice_msg_set_user_num(img, 1);
            break;
        default:
            break;
    }
#else     
    switch(step) {
        case 1:
            lv_img_set_src(img, ICON_SENDING2);
            voice_msg_set_user_num(img, 2);
            break;
        case 2:
            lv_img_set_src(img, ICON_SENDING3);
            voice_msg_set_user_num(img, 3);
            break;
        case 3:
            lv_img_set_src(img, ICON_SENDING1);
            voice_msg_set_user_num(img, 1);
            break;
        default:
            break;
    }

#endif
    
}



lv_obj_t * voice_msg_create_send(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    if(NULL == activity_obj) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_MSG_SEND;
        activity_ext.create = voice_msg_create_send;
        activity_ext.prepare_destory = voice_msg_common_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);
    }

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);

    lv_obj_t * img_bkg = lv_img_create(obj, NULL);
    lv_img_set_src(img_bkg, ICON_SENDING_VOICE_BKG);
    lv_obj_align(img_bkg, obj, LV_ALIGN_CENTER, 0, 0);

    lv_obj_t * img_sending = lv_img_create(img_bkg, NULL);
    lv_img_set_src(img_sending, ICON_SENDING3);
    lv_obj_align(img_sending, img_bkg, LV_ALIGN_IN_BOTTOM_MID, 0, -20);
    voice_msg_set_user_num(img_sending, 3);
#if USE_LV_WATCH_MULTI_LANG_SUPPORT != 0
    lv_obj_t * label = lv_label_create(img_sending, NULL);
    lv_label_set_text_id(label, WATCH_TEXT_ID_MSG_SENDING3);
    lv_label_set_align(label, LV_LABEL_ALIGN_CENTER);
    lv_obj_set_click(label, false);
    lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font20_black);
    #if defined(__XF_LCD_SIZE_128X128__)
    lv_obj_align(label, NULL, LV_ALIGN_CENTER, 0, -2);
    #else 
    lv_obj_align(label, NULL, LV_ALIGN_CENTER, 0, 0);
    #endif
#endif

    lv_anim_t a = {};
    lv_anim_init(&a);
    lv_anim_set_var(&a, img_sending);
    lv_anim_set_start_cb(&a, (lv_anim_ready_cb_t)voice_msg_sending_anim);
    lv_anim_set_repeat_count(&a, LV_ANIM_REPEAT_INFINITE);
    lv_anim_set_repeat_delay(&a, 500);
    lv_anim_start(&a);

    return obj;
}




static void voice_msg_remove_list_btn(lv_obj_t * btn, bool final)
{
    voice_msg_type_t type = VOICE_MSG_GET_MSG_TYPE(voice_msg_get_user_num(btn));
    if((VOICE_MSG_TYPE_EXPRESSION == type) || (VOICE_MSG_TYPE_PHOTO == type)) {
        const void * src = lv_img_get_src(btn);
        if(LV_IMG_SRC_VARIABLE == lv_img_src_get_type(src)) {
            lv_img_dsc_t * dsc = (lv_img_dsc_t *)src;
            lv_mem_free(dsc->data);
            lv_mem_free(dsc);
        }
    } else if(VOICE_MSG_TYPE_VOICE == type) {
        lv_obj_t * len_label = lv_obj_get_child(btn, NULL);
        void * ptr = voice_msg_get_user_ptr(len_label);
        if(ptr) lv_mem_free(ptr);
    }
    if(final) {
        lv_obj_t * scrl = lv_obj_get_parent(btn);
        if(lv_obj_get_child(scrl, btn)) {
            /*move up the objs below btn*/
            lv_obj_t * child = lv_obj_get_child_back(scrl, btn);
            lv_coord_t last_y = lv_obj_get_y(btn);
            while(child) {
                lv_obj_set_y(child, last_y);
                last_y += lv_obj_get_height(child)
                    + lv_obj_get_style_pad_inner(scrl, LV_STATE_DEFAULT);
                if(VOICE_MSG_IS_UNREAD_FLAG(voice_msg_get_user_num(child))) {
                    /*next obj is unread flag*/
                    lv_obj_t * read_img = lv_obj_get_child_back(scrl, child);
                    lv_obj_align(read_img, child, LV_ALIGN_OUT_RIGHT_MID, 5, 0);
                    child = read_img;
                }
                child = lv_obj_get_child_back(scrl, child);
            }
        }
    }
    lv_obj_del(btn);
}


static void voice_msg_remove_top_msg(lv_obj_t * scrl)
{
    lv_obj_t * btn = lv_obj_get_child_back(scrl, NULL);
    lv_obj_t * btn_time = NULL;
    lv_obj_t * btn_name = NULL;
    lv_obj_t * btn_del = NULL;
    lv_obj_t * btn_cur;
    while(btn) {
        btn_cur = btn;
        btn = lv_obj_get_child_back(scrl, btn);

        uint32_t free_num = voice_msg_get_user_num(btn_cur);
        voice_msg_type_t type = VOICE_MSG_GET_MSG_TYPE(free_num);
        if(VOICE_MSG_TYPE_TIME == type) {
            if(NULL == btn_time) {
                btn_time = btn_cur;
            } else {
                voice_msg_remove_list_btn(btn_time, false);
            }
        } else if(VOICE_MSG_TYPE_NAME == type) {
            if(NULL == btn_name) {
                btn_name = btn_cur;
            } else {
                voice_msg_remove_list_btn(btn_name, false);
                btn_name = NULL;
            }
        } else if(VOICE_MSG_TYPE_UNREAD_FLAG == type) {
            voice_msg_remove_list_btn(btn_cur, false);
        } else {
            if(NULL == btn_del) {
                btn_del = btn_cur;
            } else {
                if((VOICE_MSG_TYPE_VOICE == type)
                        || (VOICE_MSG_TYPE_EXPRESSION == type)
                        || (VOICE_MSG_TYPE_PHOTO == type)) {
                    if((NULL != btn_name) && (0 == VOICE_MSG_IS_MSG_FROM_SERVICE(free_num))) {
                        /*voice or expression or photo message from ui, not name above the message */
                        voice_msg_remove_list_btn(btn_name, false);
                    }
                }
                voice_msg_remove_list_btn(btn_del, true);
                break;
            }
        }
    }
    lv_obj_set_y(scrl, 0);
}


static void voice_msg_play_tone_and_wakeup(uint8_t direction,bool playtone)
{
	printf("%s: playtone %d\n", __FUNCTION__, playtone);
    if((WATCH_VOICE_MSG_FROM_OTHER_DEVICE == direction)
            && (AUDIO_CTRL_PRIORITY_5 == Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_5,voice_msgincome_audio_ctrl_callback))
            && (NULL == lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_SPEAK))) {
        voice_msg_lcd_wakeup();
          if(playtone){
      		 Voice_Msg_Play_Onetime(AUDIO_MSG, 0, query_current_volume(), NULL, NULL);
          }
    }
}

static void voice_msg_add_new_msg_to_chat(app_adaptor_voice_msg_t * msgs,bool playtone)
{
    lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
    if(NULL == ext) return;
    LV_ASSERT_MEM(ext->img_touch);

    lv_obj_t * scrl;
    lv_voice_msg_list_ext_t * list_ext;
    bool at_bottom = false;
    lv_obj_t * cont = lv_obj_get_parent(ext->cont_title);
    lv_coord_t scrl_init_y;
    lv_coord_t scrl_scrn_height;
    if(NULL == ext->list) {
        lv_obj_del(ext->label_no_msg);
        ext->label_no_msg = NULL;

        /*no message, create list for adding new message*/
        lv_coord_t list_height = lv_obj_get_height(cont) - lv_obj_get_height(ext->cont_title) - 4;
        ext->list = voice_msg_list_create(cont, lv_obj_get_width(cont), list_height);
        lv_obj_align(ext->list, ext->cont_title, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 0);
        list_ext = lv_obj_get_ext_attr(ext->list);
        if(NULL == list_ext) return;
        scrl_init_y = lv_obj_get_style_pad_top(ext->list, LV_LIST_PART_BG);
        scrl_scrn_height = list_height - scrl_init_y * 2;
        scrl = lv_page_get_scrl(ext->list);
        ext->msg_count = 1;
    } else {
        list_ext = lv_obj_get_ext_attr(ext->list);
        if(NULL == list_ext) return;

        scrl = lv_page_get_scrl(ext->list);
        lv_coord_t scrl_h = lv_obj_get_height(scrl);
        lv_coord_t scrl_y = lv_obj_get_y(scrl);
        scrl_init_y = lv_obj_get_style_pad_top(ext->list, LV_LIST_PART_BG);
        lv_coord_t scrl_top_h = scrl_init_y - scrl_y; /*part of scrl at the top of list*/
        scrl_scrn_height = lv_obj_get_height(ext->list) - scrl_init_y * 2;
        if((scrl_h - scrl_top_h) <= scrl_scrn_height) {
            /*at the bottom of chat window*/
            at_bottom = true;
        }
        
        /*delete emo and touch img*/
        if(ext->img_emo) {
            lv_obj_set_parent(ext->img_emo, cont);
        }
    #if USE_LV_WATCH_WS_ZNSH_FRIEND_CALL != 0
        if(ext->img_call) {
            lv_obj_set_parent(ext->img_call, cont);
        }
    #endif
    #if USE_LV_WATCH_WS_ZNSH_FRIEND_VCALL != 0
        if(ext->img_vcall) {
            lv_obj_set_parent(ext->img_vcall, cont);
        }
    #endif
        lv_obj_t * cont_emo_touch = lv_obj_get_parent(ext->img_touch);
        lv_obj_set_parent(ext->img_touch, cont);
        lv_obj_del(cont_emo_touch);

        printf("%s: msg count %d\n", __FUNCTION__, ext->msg_count);
        if(VOICE_MSG_MAX_CHAT_MSG_NUM == ext->msg_count) {
            /*reach maximum number of messages,  delete the oldest message*/
            voice_msg_remove_top_msg(scrl);
        } else {
            ext->msg_count++;
        }
    }

    /* add new voice message to message list */
    lv_obj_t * btn_emo_touch = voice_msg_display_msg_to_chat(ext->list, msgs, &ext->last_msg_time);

    lv_coord_t scrl_height = lv_obj_get_height(scrl);
    if(scrl_scrn_height >= scrl_height) {
        /*set emo and touch img to bottom of chat window*/
        lv_coord_t btn_h = lv_obj_get_height(btn_emo_touch);
        lv_coord_t new_btn_y = scrl_scrn_height - btn_h;
        lv_obj_set_y(btn_emo_touch, new_btn_y);
        voice_msg_play_tone_and_wakeup(msgs->msg->direction,playtone);
    } else {
        if(false == at_bottom) {
            if(list_ext->voice_playing_anim.var) {
                /*voice is playing*/
                Hal_Vibrator_Play_Onetime(NULL, 0);
            } else {
                lv_page_focus(ext->list, btn_emo_touch, false);
                voice_msg_play_tone_and_wakeup(msgs->msg->direction,playtone);
            }
        } else {
            /*at the bottom of chat window*/
            lv_page_focus(ext->list, btn_emo_touch, false);

            if(NULL == list_ext->voice_playing_anim.var) {
                /*not playing voice*/
                voice_msg_play_tone_and_wakeup(msgs->msg->direction,playtone);
            }
        }
    }
}


static void voice_msg_msg_chat_no_content_update(uint32_t pa, int32_t pb, void *pc)
{
	char *imei=(char*)pc;
    lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
   // printf("%s: imei:%s\n", __FUNCTION__, imei);
	
    if((NULL == ext)||(NULL == pc)) return;
    //printf("%s: cur_contact->imei:%s\n", __FUNCTION__, ext->cur_contact->imei);
	if(strcmp(ext->cur_contact->imei,imei)==0)
	{
		//printf("%s: label_no_msg:%p\n", __FUNCTION__, ext->label_no_msg);
		if(ext->label_no_msg)
		{
			lv_label_set_text_id(ext->label_no_msg, WATCH_TEXT_ID_HEALTH_NO_RECORD);
			lv_obj_realign(ext->label_no_msg);
		}
	}

	lv_mem_free(pc);
	
}

static bool voice_msg_send_voice(lv_voice_msg_chat_ext_t * ext)
{
    if(NULL == ext) return false;
    if(NULL == ext->buffer) return false;

   // uint32_t size = voice_msg_record_buffer_stop_req();
	
    uint32_t duration_ms;
    uint32_t size;
	
    Hal_Record_Buffer_Stop_Req(&size, &duration_ms);
    printf("%s: buffer size %d\n", __FUNCTION__, size);
    Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_5);
    if(VOICE_MSG_AMR_HEAD_SIZE >= size) {
        lv_mem_free(ext->buffer);
        ext->buffer = NULL;
        return false;
    }

    /*send voice to adaptor*/
    app_adaptor_voice_msg_t * msgs = (app_adaptor_voice_msg_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_t));
    memset(msgs, 0, sizeof(app_adaptor_voice_msg_t));
    app_adaptor_voice_msg_chat_id_t id;
    voice_msg_fill_chat_id(&id, ext->cur_contact);
    msgs->chat_type = id.chat_type;
    msgs->name = id.name;
    msgs->number = id.number;
    msgs->imei = id.imei;
    msgs->index = id.index;
    msgs->count = 1;
    msgs->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
    memset(msgs->msg, 0, sizeof(app_adaptor_voice_msg_info_t));
    msgs->msg->direction = WATCH_VOICE_MSG_FROM_UI;
    hal_rtc_t time;
    Hal_Rtc_Gettime(&time);
    msgs->msg->time.year = time.tm_year;
    msgs->msg->time.month = time.tm_mon;
    msgs->msg->time.day = time.tm_mday;
    msgs->msg->time.hour = time.tm_hour;
    msgs->msg->time.min = time.tm_min;
    msgs->msg->type = WATCH_VOICE_MSG_TYPE_VOICE;
    msgs->msg->data_type = WATCH_VOICE_DATA_TYPE_BUFFER;
    msgs->msg->content.voice.voice_len = size;
    msgs->msg->content.voice.file = lv_mem_alloc(size);
    memcpy(msgs->msg->content.voice.file, ext->buffer, size);

	msgs->msg->content.voice.duration=duration_ms/1000;   //ynyd add duration
	if(msgs->msg->content.voice.duration==0)
	{
		msgs->msg->content.voice.duration=1;
	}

    char * path = app_adaptor_voice_msg_send_req(msgs);
    if(NULL == path) {
        printf("%s: buffer\n", __FUNCTION__);
    } else {
        printf("%s: file path %s\n", __FUNCTION__, path);
    }

    /*update chat*/
	#if 0
    msgs = (app_adaptor_voice_msg_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_t));
    memset(msgs, 0, sizeof(app_adaptor_voice_msg_t));
    voice_msg_fill_chat_id(&id, ext->cur_contact);
    msgs->chat_type = id.chat_type;
    msgs->name = id.name;
    msgs->number = id.number;
    msgs->imei = id.imei;
    msgs->index = id.index;
    msgs->count = 1;
    msgs->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
    memset(msgs->msg, 0, sizeof(app_adaptor_voice_msg_info_t));
    msgs->msg->direction = WATCH_VOICE_MSG_FROM_UI;
    msgs->msg->time.year = time.tm_year;
    msgs->msg->time.month = time.tm_mon;
    msgs->msg->time.day = time.tm_mday;
    msgs->msg->time.hour = time.tm_hour;
    msgs->msg->time.min = time.tm_min;
    msgs->msg->type = WATCH_VOICE_MSG_TYPE_VOICE;
#if VOICE_MSG_XPHONE_TEST != 0
    msgs->msg->node = VOICE_MSG_NODE_TAIL(msgs->index);
#endif
    if(path) {
        /*buffer has been written into file by SDK*/
        msgs->msg->data_type = WATCH_VOICE_DATA_TYPE_FILE;
        msgs->msg->content.voice.file = (uint8_t *)lv_mem_alloc(1+strlen(path));
        strcpy(msgs->msg->content.voice.file, path);
        msgs->msg->content.voice.voice_len = 0;
        lv_mem_free(ext->buffer);
    } else {
        msgs->msg->data_type = WATCH_VOICE_DATA_TYPE_BUFFER;
        msgs->msg->content.voice.voice_len = size;
        msgs->msg->content.voice.file = ext->buffer;
    }
    msgs->msg->content.voice.duration = 0;
    ext->buffer = NULL;
    voice_msg_add_new_msg_to_chat(msgs);
    voice_msg_free_msgs(msgs);

#if VOICE_MSG_SORT_CONTACTS_BY_MSG_TIME != 0
    voice_msg_contact_t * head = _lv_ll_get_head(ext->contact_list);
    if(head != ext->cur_contact) {
        lv_ll_move_before(ext->contact_list, ext->cur_contact, head);
        ext->contact_list_update = true;
    }
#endif
#endif

    return true;
}


static void voice_msg_send_end_anim(lv_anim_t * a)
{
    lv_obj_t * activity_obj = ((lv_anim_t *)a)->var;
    lv_watch_activity_ext_t * act_ext = lv_obj_get_ext_attr(activity_obj);
    if(NULL == act_ext) return;

    if(act_ext->prepare_destory) {
        act_ext->prepare_destory(activity_obj);
    }
    lv_obj_del(activity_obj);
}


static void voice_msg_expression_event_cb(lv_obj_t * img, lv_event_t e)
{
    lv_voice_msg_expression_ext_t * exp_ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_EXPRESSION);
    if(NULL == exp_ext) return;

    if(LV_EVENT_PRESSED == e) {
        exp_ext->pressing = true;
    } else if((true == exp_ext->pressing) && (LV_EVENT_CLICKED == e)) {
        char * src = (char *)lv_img_get_src(img);
        if(NULL == src) return;

        lv_voice_msg_chat_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_CHAT);
        if(NULL == ext) return;

        /*send expression to adaptor*/
        app_adaptor_voice_msg_t * msgs = (app_adaptor_voice_msg_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_t));
        memset(msgs, 0, sizeof(app_adaptor_voice_msg_t));
        app_adaptor_voice_msg_chat_id_t id;
        voice_msg_fill_chat_id(&id, ext->cur_contact);
        msgs->chat_type = id.chat_type;
        msgs->name = id.name;
        msgs->number = id.number;
        msgs->imei = id.imei;
        msgs->index = id.index;
        msgs->count = 1;
        msgs->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
        memset(msgs->msg, 0, sizeof(app_adaptor_voice_msg_info_t));
        msgs->msg->direction = WATCH_VOICE_MSG_FROM_UI;
        hal_rtc_t time;
        Hal_Rtc_Gettime(&time);
        msgs->msg->time.year = time.tm_year;
        msgs->msg->time.month = time.tm_mon;
        msgs->msg->time.day = time.tm_mday;
        msgs->msg->time.hour = time.tm_hour;
        msgs->msg->time.min = time.tm_min;
        #if 0
        msgs->msg->type = WATCH_VOICE_MSG_TYPE_EXPRESSION;
        msgs->msg->data_type = WATCH_VOICE_DATA_TYPE_FILE;
        msgs->msg->content.img.data_size = strlen(src);
        printf("%s: src %s\n", __FUNCTION__, src);
        msgs->msg->content.img.data = lv_mem_alloc(msgs->msg->content.img.data_size + 1);
        memcpy(msgs->msg->content.img.data, src, msgs->msg->content.img.data_size + 1);
        #else 
        msgs->msg->type = WATCH_VOICE_MSG_TYPE_EMOJI_LIST;
        msgs->msg->data_type = WATCH_VOICE_DATA_TYPE_BUFFER;
        msgs->msg->content.emoji_list.cnt = 1;
        msgs->msg->content.emoji_list.list = (uint32_t*)lv_mem_alloc(1 * sizeof(uint32_t));
        msgs->msg->content.emoji_list.list[0] = voice_msg_get_emoji_id(src);
        #endif

        /*update chat*/
        voice_msg_add_new_msg_to_chat(msgs,false);

        app_adaptor_voice_msg_send_req(msgs);

        voice_msg_del_top_interface();

        //voice_msg_create_send(NULL);

#if VOICE_MSG_SORT_CONTACTS_BY_MSG_TIME != 0
        voice_msg_contact_t * head = _lv_ll_get_head(ext->contact_list);
        if(head != ext->cur_contact) {
            lv_ll_move_before(ext->contact_list, ext->cur_contact, head);
            ext->contact_list_update = true;
        }
#endif
    }
}


static lv_res_t voice_msg_expression_page_scrl_signal(lv_obj_t * page_scrl, lv_signal_t sign, void * param)
{
    lv_voice_msg_expression_ext_t * ext = voice_msg_get_ext(ACT_ID_VOICE_MSG_EXPRESSION);
    if(NULL == ext) return LV_RES_INV;

    lv_res_t res = ext->ancient_page_scrl_signal(page_scrl, sign, param);
    if(res != LV_RES_OK) return res;

    if(LV_SIGNAL_COORD_CHG == sign) {
        if(true == ext->pressing) {
            ext->pressing = false;
        }
    }

    return LV_RES_OK;
}

static lv_obj_t  * voice_msg_create_expression(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    if(activity_obj == NULL) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_VOICE_MSG_EXPRESSION;
        activity_ext.create = voice_msg_create_expression;
        activity_ext.prepare_destory = voice_msg_common_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);
    }

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    lv_voice_msg_expression_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_expression_ext_t));
    LV_ASSERT_MEM(ext);
    ext->pressing = false;

    lv_watch_obj_set_anim_mode(obj, LV_WATCH_ANIM_VER_UP_HIDE);

    lv_obj_t * page = lv_page_create(obj, NULL);
    lv_obj_set_size(page, lv_obj_get_width(obj), lv_obj_get_height(obj));
    lv_obj_add_style(page, LV_PAGE_PART_BG, &lv_style_transp_tight);
    lv_obj_add_style(page, LV_PAGE_PART_SCROLLABLE, &lv_style_transp_tight);
    lv_obj_add_style(page, LV_PAGE_PART_EDGE_FLASH, &lv_watch_cont_opa1);
    lv_page_set_scrollbar_mode(page, LV_SCROLLBAR_MODE_OFF);
    lv_page_set_scrollable_fit2(page, LV_FIT_TIGHT, LV_FIT_NONE);
    lv_page_set_edge_flash(page, true);
    lv_page_set_scrl_height(page, lv_obj_get_height(obj));
    lv_obj_t * scrl = lv_page_get_scrl(page);
    ext->ancient_page_scrl_signal = lv_obj_get_signal_cb(scrl);
    lv_obj_set_signal_cb(scrl, voice_msg_expression_page_scrl_signal);
    lv_watch_obj_add_element(page);

    /*image "icon_expression_test" only for test, replace them with actual expression images */
#if USE_LV_WATCH_WS_XIAOXUN != 0
    char * expression[] = {&icon_emoji101,
                                         &icon_emoji102,
                                         &icon_emoji103,
                                         &icon_emoji104,
                                         &icon_emoji105,
                                         &icon_emoji106,
                                         &icon_emoji107,
                                         &icon_emoji108,
                                         &icon_emoji109,
                                         &icon_emoji110,
                                         &icon_emoji111,
                                         &icon_emoji112,
                                        };
#else 
    char * expression[] = {ICON_EXPRESSION_TEST,
                           ICON_EXPRESSION_TEST,
                           ICON_EXPRESSION_TEST,
                           ICON_EXPRESSION_TEST,
                           ICON_EXPRESSION_TEST,
                           ICON_EXPRESSION_TEST
    };
#endif 
    uint8_t exp_count = sizeof(expression) / sizeof(lv_img_dsc_t *);
    printf("%s: expression count %d\n", __FUNCTION__, exp_count);

    lv_bidi_dir_t dir = lv_obj_get_base_dir(obj);

    lv_obj_t * img = lv_img_create(page, NULL);
    lv_img_set_src(img, expression[0]);
    if(LV_BIDI_DIR_RTL == dir) {
        //lv_obj_align(img, page, LV_ALIGN_IN_TOP_RIGHT, 0, 0);
    } else {
        //lv_obj_align(img, page, LV_ALIGN_IN_TOP_LEFT, 0, 0);
    }
    voice_msg_set_user_ptr(img, (void *)expression[0]);
    lv_obj_add_protect(img, LV_PROTECT_PRESS_LOST);
    lv_obj_set_click(img, true);
    lv_page_glue_obj(img, true);
    lv_obj_set_event_cb(img, voice_msg_expression_event_cb);
    lv_obj_set_adv_hittest(img, false);
    lv_watch_obj_add_element(img);
    for(uint8_t i = 1; i < exp_count; i++) {
        lv_obj_t * new_img = lv_img_create(page, NULL);
        lv_img_set_src(new_img, expression[i]);
#if 0
        if(0 != (i % 2)) {
            if(LV_BIDI_DIR_RTL == dir) {
                lv_obj_align(new_img, img, LV_ALIGN_OUT_BOTTOM_RIGHT, 0, 0);
            } else {
                lv_obj_align(new_img, img, LV_ALIGN_OUT_BOTTOM_LEFT, 0, 0);
            }
        } else {
            if(LV_BIDI_DIR_RTL == dir) {
                lv_obj_align(new_img, img, LV_ALIGN_OUT_LEFT_TOP, 0, 0);
            } else {
                lv_obj_align(new_img, img, LV_ALIGN_OUT_RIGHT_TOP, 0, 0);
            }
            img = new_img;
        }
#endif
        lv_watch_set_free_ptr(new_img, (void *)expression[i]);
        lv_obj_set_click(new_img, true);
        lv_page_glue_obj(new_img, true);
        lv_obj_set_event_cb(new_img, voice_msg_expression_event_cb);
        lv_watch_obj_add_element(new_img);
    }

    if(LV_BIDI_DIR_RTL == dir) {
        lv_obj_set_x(scrl, -lv_obj_get_width(scrl));
    }

    return obj;
}


static void voice_msg_enlarged_photo_event_cb(lv_obj_t * obj, lv_event_t e)
{
    if(LV_EVENT_CLICKED == e) {
        printf("%s\n", __FUNCTION__);

        lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_PHOTO);
        LV_ASSERT_MEM(activity_obj);
        lv_watch_activity_ext_t * act_ext = lv_obj_get_ext_attr(activity_obj);
        LV_ASSERT_MEM(act_ext);

        if(act_ext->prepare_destory) {
            act_ext->prepare_destory(activity_obj);
        }
        lv_obj_del(activity_obj);
    }
}

static void voice_msg_enlarged_photo_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);

    lv_obj_t * obj = NULL;
    lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &obj);
    LV_ASSERT_MEM(obj);
    lv_obj_t * img = lv_obj_get_child(obj, NULL);
    LV_ASSERT_MEM(img);
    lv_mem_free(lv_img_get_src(img));

    lv_watch_png_cache_all_free();
}

static void voice_msg_create_enlarged_photo(lv_img_dsc_t * photo)
{
    if(NULL == photo) {
        return;
    }

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_PHOTO;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_enlarged_photo_prepare_destory;
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);

    lv_obj_t * cont = lv_cont_create(obj, NULL);
    lv_obj_set_style_local_bg_color(cont,
                                    LV_CONT_PART_MAIN,
                                    LV_STATE_DEFAULT,
                                    LV_COLOR_BLACK);
    lv_coord_t width_obj = lv_obj_get_width(obj);
    lv_coord_t height_obj = lv_obj_get_height(obj);
    lv_obj_set_size(cont, width_obj, height_obj);
    lv_watch_obj_add_element(cont);

    lv_obj_t * img = lv_img_create(cont, NULL);
    lv_img_set_src(img, photo);
    lv_img_set_auto_size(img, true);
    lv_coord_t width_img;
    lv_coord_t height_img;
    if(LV_IMG_SRC_FILE == lv_img_src_get_type(photo)) {
        lv_img_header_t header;
        lv_img_decoder_get_info(photo, &header);
        width_img = header.w;
        height_img = header.h;
    } else {
        width_img = ((lv_img_dsc_t *)photo)->header.w;
        height_img = ((lv_img_dsc_t *)photo)->header.h;
    }
    uint8_t multiple = ((width_obj / width_img) > (height_obj / height_img))
        ? (height_obj / height_img) : (width_obj / width_img);
    printf("%s: obj w %d, h %d, multiple %d, w %d, h %d\n",
           __FUNCTION__, width_obj, height_obj, multiple, width_img, height_img);
    lv_img_set_zoom(img, 256 * multiple);
    lv_obj_align(img, obj, LV_ALIGN_CENTER, 0, 0);
    lv_obj_set_click(img, true);
    lv_obj_set_event_cb(img, voice_msg_enlarged_photo_event_cb);
    lv_watch_obj_add_element(img);
}


static void voice_msg_show_url_photo_process( lv_obj_t * obj )
{
    lv_obj_t * text = lv_label_create(obj, NULL);
    lv_label_set_text_id(text, WATCH_TEXT_ID_DOWNLOADING_NOW);
    lv_obj_add_style(text,LV_LABEL_PART_MAIN,&lv_watch_font30);
    lv_obj_align(text, obj, LV_ALIGN_CENTER, 0, 0);
}

static void voice_msg_dw_photo_prepare_destory(lv_obj_t * activity_obj)
{
    printf("%s\n", __FUNCTION__);
	lv_img_dsc_t * img_dsc =NULL;

	if(yn_photodw_buff)
	{
		lv_mem_free(yn_photodw_buff);
		yn_photodw_buff=NULL;
		yn_photodw_buff_len=0;
		yn_photodw_isgif=0;
	}
	 
    lv_watch_png_cache_all_free();
}


static void voice_msg_dw_photo_update( uint32_t pa, int32_t pb, void *pc)
{
    printf("%s: yn_photodw_buff %p,yn_photodw_buff_len:%d,yn_photodw_isgif:%d\n", __FUNCTION__, yn_photodw_buff,yn_photodw_buff_len,yn_photodw_isgif);
	if(yn_photodw_buff_len&&yn_photodw_buff)
	{
		lv_obj_t * activity_obj = lv_watch_get_activity_obj(ACT_ID_VOICE_MSG_PHOTO);
		if(NULL == activity_obj) return NULL;
		
		lv_obj_t * obj = NULL;
		lv_watch_get_child_obj(activity_obj, lv_watch_obj_signal, &obj);
		lv_obj_clean(obj);

		static lv_img_dsc_t photo = {
			.header.always_zero = 0,
			.header.w = 240,
			.header.h = 240,
			.header.cf = LV_IMG_CF_RAW,
		};
								
		photo.header.w = lv_obj_get_width(obj);
		photo.header.h = lv_obj_get_height(obj);
		photo.data = yn_photodw_buff;
		photo.data_size= yn_photodw_buff_len;
		
		printf("%s: header.w:%d,header.h:%d\n", __FUNCTION__, photo.header.w,photo.header.h);

		if(yn_photodw_isgif){
			gif_info_t * gif_info = gif_open(obj, &photo, 1);
			if (gif_info)
			{
				lv_obj_set_click(gif_info,true);
				lv_obj_set_event_cb(gif_info, voice_msg_enlarged_photo_event_cb);
			}
		}
		else{
		    lv_obj_t * img = lv_img_create(obj, NULL);
			if(img){
			    lv_img_set_src(img, &photo);
			    lv_obj_align(img, obj, LV_ALIGN_CENTER, 0, 0);
			    lv_obj_set_click(img, true);
			    lv_obj_set_event_cb(img, voice_msg_enlarged_photo_event_cb);
			    lv_watch_obj_add_element(img);
			}
		}
	}
}

 void voice_msg_create_url_photo(void)
{
    printf("%s: \n", __FUNCTION__);

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_PHOTO;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_dw_photo_prepare_destory;
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
	voice_msg_show_url_photo_process(obj);

}


#if VOICE_MSG_XPHONE_TEST != 0

void voice_msg_delete_msg_db(uint8_t index)
{
    char fileName[60];
    
    printf("%s: VOICE_MSG_CNT(%d)=%d\n", __FUNCTION__, index, VOICE_MSG_CNT(index));
    if(VOICE_MSG_CNT(index)==0)
    {
        return;
    }
	if(index==VOICE_MSG_FRIENDS_GROUP)
		strcpy(fileName, VOICE_MSG_LIST_DB2);
    else if(index >= VOICE_MSG_FAMILY_GROUP)
    {
        strcpy(fileName, VOICE_MSG_LIST_DB);
    }
    else 
    {        
        nv_watch_friends_t * nvm_pb = voice_msg_read_pb_nvm();
        sprintf(fileName, "C:/vml_%s.json", nvm_pb->info[index].imei);
        lv_mem_free(nvm_pb);
    }

    lv_fs_remove(fileName);
	
	app_adaptor_voice_msg_info_t * msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_head(VOICE_MSG_LIST(index));
	while(msg_info)
	{
        printf("%s: msg_info->type=%d\n", __FUNCTION__, msg_info->type);
        if(msg_info->name)
        {
            lv_mem_free(msg_info->name);
        }
        
        if(WATCH_VOICE_MSG_TYPE_TEXT == msg_info->type) {
            if(WATCH_VOICE_DATA_TYPE_FILE == msg_info->data_type) {
                /*delete file*/
                printf("%s: file name %s\n", __FUNCTION__, msg_info->content.text);
                UI_FILE_REMOVE(msg_info->content.text);
            }
            lv_mem_free(msg_info->content.text);
        } else if(WATCH_VOICE_MSG_TYPE_VOICE == msg_info->type) {
            if(WATCH_VOICE_DATA_TYPE_FILE == msg_info->data_type) {
                /*delete file*/
                printf("%s: file name %x\n", __FUNCTION__, (char *)msg_info->content.voice.file);
                printf("%s: file name %s\n", __FUNCTION__, (char *)msg_info->content.voice.file);
                UI_FILE_REMOVE((char *)msg_info->content.voice.file);
            }
            
            lv_mem_free(msg_info->content.voice.file);
            
        } else if((WATCH_VOICE_MSG_TYPE_EXPRESSION == msg_info->type)
                  || WATCH_VOICE_MSG_TYPE_PHOTO == msg_info->type) {
            if(WATCH_VOICE_DATA_TYPE_FILE == msg_info->data_type) {
                /*delete file*/
                printf("%s: file name %s\n", __FUNCTION__, (char *)msg_info->content.img.data);
                UI_FILE_REMOVE((char *)msg_info->content.img.data);
            }
            lv_mem_free(msg_info->content.img.data);
        } else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == msg_info->type) {

            lv_mem_free(msg_info->content.emoji_list.list);
            
        }
        
        VOICE_MSG_REMOVE(index, msg_info);

        msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_head(VOICE_MSG_LIST(index));
	}
    
			  
}

void voice_msg_list_load_from_fs(uint8_t index, char *imei)
{
    char fileName[60];
    char* json=NULL;
    uint32_t size = 0;
    if(imei == NULL){
        strcpy(fileName, VOICE_MSG_LIST_DB);
#if USE_LV_WATCH_VOICE_MSG_YNYD!=0
	if(index==VOICE_MSG_FRIENDS_GROUP)
		strcpy(fileName, VOICE_MSG_LIST_DB2);
#endif	
    }else{
        sprintf(fileName, "C:/vml_%s.json", imei);
    }
    
    ui_file_t * pfile = UI_FILE_OPEN((const char *)fileName, UI_FILE_READ_ONLY);
    if(NULL == pfile->file_d) {
        return;
    } else {
        if(LV_FS_RES_OK != UI_FILE_SIZE(pfile, &size)) {
            size = 0;
        }
        if(size>0){
            json = lv_mem_alloc(size+1);
            json[size]=0;

            UI_FILE_SEEK(pfile, SEEK_SET);
            UI_FILE_READ(pfile, json, size);
        
        }
        
        UI_FILE_CLOSE(pfile);

        if(json==NULL){ return;}

        if(strlen(json) == 0){
            lv_mem_free(json);  
            return;
        }
    }

    ws_cJSON    *root_parse = NULL;
    ws_cJSON    *msg_array_s = NULL;
    ws_cJSON    *cell_s       = NULL;
    uint16_t     i,cnt = 0;
    
    root_parse = ws_cJSON_Parse(json);
    if(root_parse==NULL){
        lv_mem_free(json);  
        return;
    }

    msg_array_s = ws_cJSON_GetObjectItem(root_parse, "msgs");
    if(!msg_array_s){return ;}
    cnt = ws_cJSON_GetArraySize(msg_array_s);
    for(i=0; i<cnt; i++){
        app_adaptor_voice_msg_info_t *msg_info = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
        memset(msg_info, 0, sizeof(app_adaptor_voice_msg_info_t));
        
        cell_s = ws_cJSON_GetArrayItem(msg_array_s, i);
       
        ws_cJSON *item_s = ws_cJSON_GetObjectItem(cell_s, "name");
        if(item_s) {
            msg_info->name = (char*)lv_mem_alloc(strlen(item_s->valuestring)+1);
            strcpy(msg_info->name, item_s->valuestring);
        }
        msg_info->direction = ws_cJSON_GetObjectItem(cell_s, "direction")->valueint;
        msg_info->read_flag = ws_cJSON_GetObjectItem(cell_s, "read_flag")->valueint;
        msg_info->type      = ws_cJSON_GetObjectItem(cell_s, "type")->valueint;
        msg_info->data_type = ws_cJSON_GetObjectItem(cell_s, "data_type")->valueint;
        item_s = ws_cJSON_GetObjectItem(cell_s, "time");
        msg_info->time.year = ws_cJSON_GetObjectItem(item_s, "year")->valueint;
        msg_info->time.month= ws_cJSON_GetObjectItem(item_s, "month")->valueint;
        msg_info->time.day  = ws_cJSON_GetObjectItem(item_s, "day")->valueint;
        msg_info->time.hour = ws_cJSON_GetObjectItem(item_s, "hour")->valueint;
        msg_info->time.min  = ws_cJSON_GetObjectItem(item_s, "min")->valueint;
        item_s = ws_cJSON_GetObjectItem(cell_s, "msg_id");
        if(item_s){
            strncpy(msg_info->msg_id, item_s->valuestring, 63);
        }
        
        
        if(WATCH_VOICE_MSG_TYPE_TEXT == msg_info->type ||WATCH_VOICE_MSG_TYPE_CMD_CFM== msg_info->type) {
            item_s = ws_cJSON_GetObjectItem(cell_s, "text");
            if(item_s) {
                msg_info->content.text = (char*)lv_mem_alloc(strlen(item_s->valuestring)+1);
                strcpy(msg_info->content.text, item_s->valuestring);
            }
        } else if(WATCH_VOICE_MSG_TYPE_VOICE == msg_info->type) {
            ws_cJSON *voice_s = ws_cJSON_GetObjectItem(cell_s, "voice");

            //ws_cJSON *index_s = ws_cJSON_GetObjectItem(voice_s, "index");
            //msg_info->content.voice.index = index_s->valueint;
            msg_info->content.voice.index = VOICE_MSG_IDX(index)++;
            ws_cJSON *voice_len_s = ws_cJSON_GetObjectItem(voice_s, "voice_len");
            if(voice_len_s){
                msg_info->content.voice.voice_len = voice_len_s->valueint;
            }

            msg_info->content.voice.duration = 0;
            ws_cJSON *voice_dur_s = ws_cJSON_GetObjectItem(voice_s, "duration");
            if(voice_dur_s){
                msg_info->content.voice.duration = voice_dur_s->valueint;
            }
            
            ws_cJSON *file_s = ws_cJSON_GetObjectItem(voice_s, "file");
            if(file_s) 
            {
                msg_info->content.voice.file = (char*)lv_mem_alloc(strlen(file_s->valuestring)+1);
                strcpy(msg_info->content.voice.file, file_s->valuestring);
            }

        } else if(WATCH_VOICE_MSG_TYPE_PHOTO == msg_info->type) {
            ws_cJSON *picture_s = ws_cJSON_GetObjectItem(cell_s, "picture");

            ws_cJSON *file_s = ws_cJSON_GetObjectItem(picture_s, "file");
            if(file_s) 
            {
                msg_info->content.img.data = (char*)lv_mem_alloc(strlen(file_s->valuestring)+1);
                strcpy(msg_info->content.img.data, file_s->valuestring);
            }
        } else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == msg_info->type) {
            ws_cJSON *emojis_s = ws_cJSON_GetObjectItem(cell_s, "emoji");
        
            if(emojis_s) 
            {
                ws_cJSON *item_s=NULL;
                int cnt = ws_cJSON_GetArraySize(emojis_s);
                
                msg_info->content.emoji_list.cnt = cnt;
                msg_info->content.emoji_list.list = (uint32_t)lv_mem_alloc(cnt * sizeof(uint32_t));
                for(int i=0; i<cnt; i++){
                    item_s = ws_cJSON_GetArrayItem(emojis_s, i);
                    msg_info->content.emoji_list.list[i] = item_s->valueint;
                }
            }
        }
        
        
        VOICE_MSG_ADD(index, msg_info);
        lv_mem_free(msg_info);
        
    }

    voice_msg_list_dump_to_fs(index);

    ws_cJSON_Delete(root_parse);

    lv_mem_free(json);  
    
}

void voice_msg_list_dump_to_fs(uint8_t index)
{


}

uint8_t voice_msg_find_index_by_imei(char *imei)
{
    nv_watch_friends_t * nvm_pb = voice_msg_read_pb_nvm();
    for(uint8_t i=0; i<NV_WATCH_MAX_FRIENDS_NUM ; i++)
    {
        if(strcmp(nvm_pb->info[i].imei, imei) == 0)
        {
            lv_mem_free(nvm_pb);
            return i;
        }
    }
    lv_mem_free(nvm_pb);

    return 0xff;
    
}

uint8_t voice_msg_find_index_by_phone(char *phone, char* name)
{
    nv_watch_friends_t * nvm_pb = voice_msg_read_pb_nvm();
    for(uint8_t i=0; i<NV_WATCH_MAX_FRIENDS_NUM ; i++)
    {
        if(strcmp(nvm_pb->info[i].number, phone) == 0)
        {
            if(name){
                strcpy(name, nvm_pb->info[i].name);
            }
            lv_mem_free(nvm_pb);
            return i;
        }
    }
    lv_mem_free(nvm_pb);

    return 0xff;
    
}

int voice_msg_get_friends_count(void)
{
    nv_watch_friends_t * nvm_pb = voice_msg_read_pb_nvm();
    for(int i=0; i<NV_WATCH_MAX_FRIENDS_NUM ; i++)
    {
        if(strlen(nvm_pb->info[i].imei)==0)
        {
            return i;
        }
    }
    lv_mem_free(nvm_pb);

    return NV_WATCH_MAX_FRIENDS_NUM;

}

char * voice_msg_write_file(void * data, uint32_t size, char* name, uint8_t file_type)
{
    uint16_t file_name_len = strlen(VOICE_MSG_TEST_FILE_DIR) + strlen(name) + 1;
    char * file_name;

    file_name = (char *)lv_mem_alloc(file_name_len);
    snprintf(file_name, file_name_len, "%s%s", VOICE_MSG_TEST_FILE_DIR, name);

    printf("%s: file name %s\n", __FUNCTION__, file_name);

    ui_file_t * pfile = UI_FILE_OPEN((const char *)file_name, UI_FILE_WRITE_ONLY);
    if(NULL == pfile->file_d) {
        printf("%s: please check file path\n", __FUNCTION__);
		strcpy(file_name,UI_EMPTY_FILE);
        return file_name;
    }

    UI_FILE_SEEK(pfile, SEEK_SET);
    uint32_t write_size = UI_FILE_WRITE(pfile, data, size);
    if(0 == write_size) {
        printf("%s: write in fs is wrong\n", __FUNCTION__);
        UI_FILE_CLOSE(pfile);
        lv_fs_remove(file_name);
		strcpy(file_name,UI_EMPTY_FILE);
        return file_name;
    }
    UI_FILE_CLOSE(pfile);

    return file_name;
}


static void _voice_msg_xphone_init(void)
{


}


static void voice_msg_xphone_init(void)
{

}

void voice_msg_free_msg_db(uint8_t index)
{
    char fileName[60];
    
    printf("%s: VOICE_MSG_CNT(%d)=%d\n", __FUNCTION__, index, VOICE_MSG_CNT(index));
    if(VOICE_MSG_CNT(index)==0)
    {
        return;
    }

	app_adaptor_voice_msg_info_t * msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_head(VOICE_MSG_LIST(index));
	while(msg_info)
	{
        printf("%s: msg_info->type=%d\n", __FUNCTION__, msg_info->type);
        if(msg_info->name)
        {
            lv_mem_free(msg_info->name);
        }
        
        if(WATCH_VOICE_MSG_TYPE_TEXT == msg_info->type ||WATCH_VOICE_MSG_TYPE_CMD_CFM== msg_info->type) {
            lv_mem_free(msg_info->content.text);
        } else if(WATCH_VOICE_MSG_TYPE_VOICE == msg_info->type) {
            lv_mem_free(msg_info->content.voice.file);
            
        } else if((WATCH_VOICE_MSG_TYPE_EXPRESSION == msg_info->type)
                  || WATCH_VOICE_MSG_TYPE_PHOTO == msg_info->type) {
            lv_mem_free(msg_info->content.img.data);
        } else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == msg_info->type){
            lv_mem_free(msg_info->content.emoji_list.list);
        }
        
        VOICE_MSG_REMOVE(index, msg_info);

        msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_head(VOICE_MSG_LIST(index));
	}
    
			  
}


static void voice_msg_xphone_reinit(void)
{
    for(int i = 0; i< NV_WATCH_MAX_VOICE_MSG_CHAT_NUM; i++){
        voice_msg_free_msg_db(i);
    }
    _voice_msg_xphone_init();    
}


void voice_msg_delete_old_info(uint8_t index, void *node)
{
	app_adaptor_voice_msg_info_t * msg_info = NULL;
	if(node)
	{
		void * temp_node = _lv_ll_get_head(VOICE_MSG_LIST(index));
		
        for(uint8_t i = 0; i < VOICE_MSG_CNT(index); i++) {
            if(node == temp_node)
            {
                msg_info = node;
                break;
            }

			temp_node = (app_adaptor_voice_msg_info_t *)_lv_ll_get_next(VOICE_MSG_LIST(index), temp_node);	  
        }
	}
    else
    {
        msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_head(VOICE_MSG_LIST(index));
    }
    
	if(msg_info==NULL)
	{
		return;
	}

    if(msg_info->name)
    {
        lv_mem_free(msg_info->name);
    }
	
    if(WATCH_VOICE_MSG_TYPE_TEXT == msg_info->type ||WATCH_VOICE_MSG_TYPE_CMD_CFM== msg_info->type) {
        if(WATCH_VOICE_DATA_TYPE_FILE == msg_info->data_type) {
            /*delete file*/
            printf("%s: file name %s\n", __FUNCTION__, msg_info->content.text);
            UI_FILE_REMOVE(msg_info->content.text);
        }
        lv_mem_free(msg_info->content.text);
    } else if(WATCH_VOICE_MSG_TYPE_VOICE == msg_info->type) {
        if(WATCH_VOICE_DATA_TYPE_FILE == msg_info->data_type) {
            /*delete file*/
            printf("%s: file name %s\n", __FUNCTION__, (char *)msg_info->content.voice.file);
            UI_FILE_REMOVE((char *)msg_info->content.voice.file);
        }
        
        lv_mem_free(msg_info->content.voice.file);
        
    } else if((WATCH_VOICE_MSG_TYPE_EXPRESSION == msg_info->type)
              || WATCH_VOICE_MSG_TYPE_PHOTO == msg_info->type) {
        if(WATCH_VOICE_DATA_TYPE_FILE == msg_info->data_type) {
            /*delete file*/
            printf("%s: file name %s\n", __FUNCTION__, (char *)msg_info->content.img.data);
            UI_FILE_REMOVE((char *)msg_info->content.img.data);
        }
        lv_mem_free(msg_info->content.img.data);
    } else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == msg_info->type){
        lv_mem_free(msg_info->content.emoji_list.list);
    }

	VOICE_MSG_REMOVE(index, msg_info);
			  
}

void voice_msg_ui_close_all(void)
{
	voice_msg_del_interface(ACT_ID_VOICE_DEL_CHAT);
	voice_msg_del_interface(ACT_ID_VOICE_MSG_DEL_CONFIRM);
	voice_msg_del_interface(ACT_ID_VOICE_MSG_INCOMING);
	voice_msg_del_interface(ACT_ID_VOICE_MSG_SPEAK);
	voice_msg_del_interface(ACT_ID_VOICE_MSG_SEND);
	voice_msg_del_interface(ACT_ID_VOICE_MSG_CHAT);
	voice_msg_del_interface(ACT_ID_VOICE_MSG_SESSION);
	voice_msg_del_interface(ACT_ID_VOICE_MSG);
}

void voice_msg_delete_all(void)
{
    voice_msg_ui_close_all();
    #if 0
    uint32_t length_test = sizeof(nv_watch_voice_msg_t);
    nv_watch_voice_msg_t * nvm_test = (nv_watch_voice_msg_t *)lv_mem_alloc(length_test);
    memset(nvm_test, 0xFF, length_test);
    nvm_test->index[0] = VOICE_MSG_FAMILY_GROUP;
    if(length_test != UI_NV_Write_Req(NV_SECTION_UI_VOICE_MSG, 0, length_test, (uint8_t *)nvm_test));
    lv_mem_free(nvm_test);

    for(uint8_t i=0; i<NV_WATCH_MAX_VOICE_MSG_CHAT_NUM; i++)
    {
        voice_msg_delete_msg_db(i);
    }
	#endif
			  
}


app_adaptor_voice_msg_t * app_adaptor_voice_msg_get_msgs_req(app_adaptor_voice_msg_chat_id_t * id)
{
    app_adaptor_voice_msg_t * msgs =NULL;
    uint8_t index = id->index;//VOICE_MSG_FAMILY_GROUP; //TODO:
	MMI_ModemAdp_WS_Yn_Chat_Get_SessionContent(id->imei);
	
    lv_mem_free(id);

    return msgs;
}

char * app_adaptor_voice_msg_send_req(app_adaptor_voice_msg_t * msg)
{
    char* path=NULL;
    
    app_adaptor_voice_msg_info_t *msg_info = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
    memcpy(msg_info,  msg->msg, sizeof(app_adaptor_voice_msg_info_t));

    uint8_t index = msg->index;//VOICE_MSG_FAMILY_GROUP; //TODO:
    
    hal_rtc_t cur_time;
    Hal_Rtc_Gettime(&cur_time);
	char name_head[30];
	
    if(WATCH_VOICE_MSG_TYPE_VOICE == msg->msg->type) {
		sprintf(name_head, "Send_%d%02d%02d%02d%02d%02d.amr", cur_time.tm_year, cur_time.tm_mon, cur_time.tm_mday,
							cur_time.tm_hour, cur_time.tm_min, cur_time.tm_sec);
        msg_info->read_flag = true;
        msg_info->data_type = WATCH_VOICE_DATA_TYPE_FILE;
        msg_info->content.voice.index = VOICE_MSG_IDX(index)++;
        msg_info->content.voice.voice_len = 0;
        msg_info->content.voice.file =NULL;  //ynyd
        path = msg_info->content.voice.file;
        //send to network...
        MMI_ModemAdp_WS_Send_Voice(msg->msg->content.voice.file, msg->msg->content.voice.voice_len, msg->imei,msg->msg->content.voice.duration);

    } else if(WATCH_VOICE_MSG_TYPE_PHOTO == msg->msg->type){
		sprintf(name_head, "Send_%d%02d%02d%02d%02d%02d.jpg", cur_time.tm_year, cur_time.tm_mon, cur_time.tm_mday,
							cur_time.tm_hour, cur_time.tm_min, cur_time.tm_sec);
        msg_info->read_flag = true;
        msg_info->data_type = WATCH_VOICE_DATA_TYPE_FILE;
        msg_info->content.img.data_size = 0;
        msg_info->content.img.data = (uint8_t *)voice_msg_write_file(msg->msg->content.img.data,
                msg->msg->content.img.data_size,
                name_head,
                WATCH_VOICE_MSG_TYPE_PHOTO);
        path = msg_info->content.voice.file;
        //send to network...
        MMI_ModemAdp_WS_Send_Picture(msg->msg->content.img.data, msg->msg->content.img.data_size, msg->imei);

    } else if(WATCH_VOICE_MSG_TYPE_EMOJI_LIST == msg->msg->type){
        int cnt = msg->msg->content.emoji_list.cnt;
        msg_info->read_flag = true;
        msg_info->data_type = WATCH_VOICE_DATA_TYPE_BUFFER;
        msg_info->content.emoji_list.cnt = cnt;
        msg_info->content.emoji_list.list = (uint32_t *)lv_mem_alloc(cnt*sizeof(uint32_t));
        memcpy(msg_info->content.emoji_list.list, msg->msg->content.emoji_list.list, cnt*sizeof(uint32_t));
        //send to network...
        MMI_ModemAdp_WS_Send_Emoji(msg_info->content.emoji_list.list, cnt, msg->imei);
    
	}
    
	voice_msg_free_msgs(msg);
    VOICE_MSG_SAVE(index, msg_info);
    lv_mem_free(msg_info);

    return path;
}


void app_adaptor_voice_msg_read_voice_req(app_adaptor_voice_msg_chat_id_t * id, uint8_t index)
{
    uint8_t item = id->index;//VOICE_MSG_FAMILY_GROUP; //TODO:;
    printf("%s: read voice, chat type %d, name %s, index %d\n", __FUNCTION__, id->chat_type, id->name, index);
    if(id->name) lv_mem_free(id->name);
    if(id->number) lv_mem_free(id->number);
    if(id->imei) lv_mem_free(id->imei);
    lv_mem_free(id);
	
    /*
	app_adaptor_voice_msg_info_t * msg_info;
	msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_head(VOICE_MSG_LIST(item));
	
    for(uint8_t i = 0; i < VOICE_MSG_CNT(item); i++) {
        
        if(WATCH_VOICE_MSG_TYPE_VOICE == msg_info->type){
            if(WATCH_VOICE_MSG_FROM_OTHER_DEVICE == msg_info->direction &&
                index==msg_info->content.voice.index) {
                msg_info->read_flag = true;
                voice_msg_list_dump_to_fs(item);
                break;
            }
        }

		msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_next(VOICE_MSG_LIST(item), msg_info);	  
    }  
    */
}


void app_adaptor_voice_msg_read_txt_req(uint8_t item)
{    


}

void voice_msg_new_msg_ind(uint8_t type, uint8_t * data, uint32_t len, uint16_t duration, char* filename, char* id, char* nicky, char* msg_id)
{
    uint8_t index; 
	printf("%s,type:%d ,len:%d",__func__,type,len);

	app_adaptor_voice_msg_t * a_msg=NULL ;
	app_adaptor_voice_msg_t * b_msgs=NULL;
	if(type==WATCH_VOICE_MSG_SET_SESSION_CONTENT)
	{
		if(data==NULL)
		{
			printf("%s,id:%s",__func__,id);
			if(id &&strlen(id))
			{
				char *imei=(char *) lv_mem_alloc(strlen(id)+1);
				memset(imei,0,sizeof(strlen(id)+1));
				strcpy(imei,id);
				watch_gui_rpc_req(voice_msg_msg_chat_no_content_update, 0, 0, imei);
				return;
			}
		}

	}
	
	a_msg = (app_adaptor_voice_msg_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_t));
	memset(a_msg, 0, sizeof(app_adaptor_voice_msg_t));
	
	 b_msgs =(app_adaptor_voice_msg_t *)data;

    /*send new message to ui*/
	if((WATCH_VOICE_MSG_SET_SESSION_CONTENT==type)||(WATCH_VOICE_MSG_SET_SESSION_CONTENT_ADD==type)||(WATCH_VOICE_MSG_SET_SESSION_VOICE_UPLOAD==type))
	{
		uint32_t info_mallcen = sizeof(app_adaptor_voice_msg_info_t) * len;
		printf("%s,type:%d ,id:%s,count:%d,info_mallcen:%d",__func__,type,id,len,info_mallcen);
		a_msg->index = index;
		a_msg->count = len;
		a_msg->name = nicky;
	    a_msg->chat_type =b_msgs->chat_type ; //WATCH_VOICE_MSG_SINGLE_CHAT;
	    a_msg->addcontent =b_msgs->addcontent ; //WATCH_VOICE_MSG_SINGLE_CHAT;
	    a_msg->imei = (char *)lv_mem_alloc(strlen(id)+1);
	    strcpy(a_msg->imei, id);
		a_msg->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(info_mallcen);
		if(a_msg->msg==NULL)
		{
			voice_msg_free_msgs(a_msg);
			voice_msg_free_msgs(b_msgs);
			printf("%s,type:%d ,id:%s,count:%d--malloc fial",__func__,type,id,len);
			return;
		}
		
		memset(a_msg->msg,0,info_mallcen);
		
		for(int i=0; i<len; i++){
		    a_msg->msg[i].direction =b_msgs->msg[i].direction;
		    a_msg->msg[i].read_flag = b_msgs->msg[i].read_flag;
		    memcpy(&a_msg->msg[i].time, &b_msgs->msg[i].time, sizeof(app_adp_time_and_date_t));
		    a_msg->msg[i].type =b_msgs->msg[i].type;
		    a_msg->msg[i].data_type = b_msgs->msg[i].data_type;
			strcpy(a_msg->msg[i].msg_id, b_msgs->msg[i].msg_id);
			if(b_msgs->msg[i].name)
			{
				uint8_t len=strlen(b_msgs->msg[i].name)+1;
				a_msg->msg[i].name=(char*)lv_mem_alloc(len);
				memset(a_msg->msg[i].name,0,len);
				strcpy(a_msg->msg[i].name,b_msgs->msg[i].name);
			}
			
		    if(a_msg->msg[i].type == WATCH_VOICE_MSG_TYPE_TEXT ||WATCH_VOICE_MSG_TYPE_CMD_CFM==a_msg->msg[i].type){
				a_msg->msg[i].content.text = (uint8_t *)lv_mem_alloc(1+strlen(b_msgs->msg[i].content.text));
				strcpy(a_msg->msg[i].content.text, b_msgs->msg[i].content.text);
		    
		    }else if(a_msg->msg[i].type ==WATCH_VOICE_MSG_TYPE_PHOTO){
				a_msg->msg[i].content.img.data = (uint8_t *)lv_mem_alloc(1+strlen(b_msgs->msg[i].content.img.data));
				strcpy(a_msg->msg[i].content.img.data, b_msgs->msg[i].content.img.data);
				a_msg->msg[i].content.img.data_size=strlen( b_msgs->msg[i].content.img.data)+1;
				printf("%s,photo url:%s",__func__,a_msg->msg[i].content.img.data);
		    }else{

				a_msg->msg[i].content.voice.index = b_msgs->msg[i].content.voice.index;
				a_msg->msg[i].content.voice.voice_len =b_msgs->msg[i].content.voice.voice_len;
				a_msg->msg[i].content.voice.duration = b_msgs->msg[i].content.voice.duration ;
				a_msg->msg[i].content.voice.file = (uint8_t *)lv_mem_alloc(1+strlen(b_msgs->msg[i].content.voice.file ));
				strcpy(a_msg->msg[i].content.voice.file, b_msgs->msg[i].content.voice.file );
			}
		}
		
	}
	else
	{
	    a_msg->index = index;
	    a_msg->count = 1;
	    a_msg->imei = id;
	    a_msg->name = nicky;
	    a_msg->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
	    memset(a_msg->msg, 0, sizeof(app_adaptor_voice_msg_info_t));
	    a_msg->chat_type = type;
	    a_msg->msg->name = NULL;
	    a_msg->msg->type = type;
	    a_msg->msg->data_type = type;
	}

    app_adaptor_voice_msg_rcv_ind(a_msg);

    voice_msg_free_msgs(b_msgs);

}



void voice_msg_new_emoji_msg_ind(uint32_t *emoji_list, int cnt, char* id)
{
    uint8_t index = VOICE_MSG_FAMILY_GROUP; //TODO:
	char name[48];

	if(emoji_list==NULL){
		return;
	}

	if(id==NULL || strlen(id)==0){
		index = VOICE_MSG_FAMILY_GROUP;
	}else{
		index = voice_msg_find_index_by_imei(id);
	}
    
    printf("%s:%d id: %s  emoji_list[0]=%d\n", __FUNCTION__, __LINE__ , id, emoji_list[0]);

    if(index == 0xff)
    {
        return;
    }
	
    voice_msg_xphone_init();

    app_adaptor_voice_msg_info_t *msg_info = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
    memset(msg_info, 0, sizeof(app_adaptor_voice_msg_info_t));
    printf("%s: info_count %d\n", __FUNCTION__, VOICE_MSG_CNT(index));
    
    hal_rtc_t cur_time;
    Hal_Rtc_Gettime(&cur_time);
    msg_info->direction = WATCH_VOICE_MSG_FROM_OTHER_DEVICE;
    msg_info->read_flag = false;
    msg_info->time.year = cur_time.tm_year;
    msg_info->time.month = cur_time.tm_mon;
    msg_info->time.day = cur_time.tm_mday;
    msg_info->time.hour = cur_time.tm_hour;
    msg_info->time.min = cur_time.tm_min;
    msg_info->type = WATCH_VOICE_MSG_TYPE_EMOJI_LIST;

    msg_info->content.emoji_list.cnt = cnt;
    msg_info->content.emoji_list.list = (uint32_t *)lv_mem_alloc(cnt*sizeof(uint32_t));
    memcpy(msg_info->content.emoji_list.list, emoji_list, cnt*sizeof(uint32_t));

    /*send new message to ui*/
    app_adaptor_voice_msg_t * msg = (app_adaptor_voice_msg_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_t));
    memset(msg, 0, sizeof(app_adaptor_voice_msg_t));
    msg->count = 1;
    msg->msg = (app_adaptor_voice_msg_info_t *)lv_mem_alloc(sizeof(app_adaptor_voice_msg_info_t));
    memset(msg->msg, 0, sizeof(app_adaptor_voice_msg_info_t));
	if(index == VOICE_MSG_FAMILY_GROUP){
        msg->chat_type = WATCH_VOICE_MSG_FAMILY_GROUP_CHAT;
        msg->msg->name = (char *)lv_mem_alloc(2);
        strcpy(msg->msg->name, " ");
		msg->imei = NULL;
	}else{
	    msg->chat_type = WATCH_VOICE_MSG_SINGLE_CHAT;
	    msg->msg->name = NULL;
	    msg->imei = (char *)lv_mem_alloc(strlen(id)+1);
	    strcpy(msg->imei, id);
	}
    msg->msg->direction = msg_info->direction;
    msg->msg->read_flag = msg_info->read_flag;
    memcpy(&msg->msg->time, &msg_info->time, sizeof(app_adp_time_and_date_t));
    msg->msg->type = msg_info->type;
    msg->msg->data_type = msg_info->data_type;
    msg->msg->content.emoji_list.cnt = cnt;
    msg->msg->content.emoji_list.list = (uint32_t *)lv_mem_alloc(cnt*sizeof(uint32_t));;
    memcpy(msg->msg->content.emoji_list.list, emoji_list, cnt*sizeof(uint32_t));

    VOICE_MSG_SAVE(index, msg_info);
    msg->msg->node = VOICE_MSG_NODE_TAIL(index);
    lv_mem_free(msg_info);
    app_adaptor_voice_msg_rcv_ind(msg);

}


static void voice_msg_update_contact_mass(app_adaptor_friend_t * friend_list, uint8_t cnt)
{
    voice_msg_contact_t family_item;
    lv_ll_t * contact_list = voice_msg_read_nvm();
    voice_msg_contact_t * node = _lv_ll_get_head(contact_list);

    printf("%s():%d  node=%x", __func__, __LINE__, node);

    if(node)
    {
        memcpy(&family_item, node, sizeof(voice_msg_contact_t));// save the family
    	node = _lv_ll_get_next(contact_list, node);
    }
    else 
    {
        memset(&family_item, 0, sizeof(voice_msg_contact_t));
        family_item.index = VOICE_MSG_FAMILY_GROUP;
    }
    
    while(node) {
        voice_msg_contact_t * contact = NULL;

        for(uint8_t i=0; i<cnt; i++)
        {
            if(0 == strcmp(friend_list[i].friend_imei , node->imei)) {
                contact = node;
                break;
            }
        }

        if(contact == NULL) // It will be deleted!
        {
            voice_msg_delete_msg_db(node->index);
        }
        
        node = _lv_ll_get_next(contact_list, node);
    }
    
    _lv_ll_clear(contact_list);
    voice_msg_contact_t * contact;
#if VOICE_MSG_NO_FAMILY_GROUP != 0
    /* Nothing to add...*/
#else 
    contact = _lv_ll_ins_tail(contact_list);
    memcpy(contact, &family_item, sizeof(voice_msg_contact_t));
    contact->index = VOICE_MSG_FAMILY_GROUP;
#endif 
    
    for(uint8_t i = 0; i < cnt && i < NV_WATCH_MAX_FRIENDS_NUM; i++) {
        contact = _lv_ll_ins_tail(contact_list);
        contact->index = i;
        memcpy(contact->name, friend_list[i].friend_name, NV_CONTACTS_MAX_NAME_LEN);
        memcpy(contact->number, friend_list[i].friend_number, NV_CONTACTS_MAX_NUMBER_LEN);
        memcpy(contact->imei, friend_list[i].friend_imei, NV_CONTACTS_MAX_NUMBER_LEN);
        contact->portrait_id = friend_list[i].portrait_id;
        contact->mark = 0xff;
    }

    voice_msg_write_nvm(contact_list);
    
    _lv_ll_clear(contact_list);
    lv_mem_free(contact_list);

}

void app_adaptor_update_friend_ind(app_adaptor_friend_t * friend_list, uint8_t count)
{
    voice_msg_ui_close_all();
    voice_msg_update_contact_mass(friend_list, count);

    uint32_t length = sizeof(nv_watch_friends_t);
    nv_watch_friends_t * nvm_contact = (nv_watch_friends_t *)lv_mem_alloc(length);

	memset(nvm_contact, 0, length);
	
    for(uint8_t i = 0; i < NV_WATCH_MAX_FRIENDS_NUM && i < count; i++) {
        friend_copy_contact(&nvm_contact->info[i], &friend_list[i]);
    }
    voice_msg_write_pb_nvm(nvm_contact);
    lv_mem_free(nvm_contact);
    lv_mem_free(friend_list);

    voice_msg_xphone_reinit();

}

bool voice_msg_have_unread_message(void)
{

    voice_msg_xphone_init();

    for(uint8_t item = 0; item <= VOICE_MSG_FAMILY_GROUP; item++){
        
        app_adaptor_voice_msg_info_t * msg_info;
        msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_head(VOICE_MSG_LIST(item));
        
        for(uint8_t i = 0; i < VOICE_MSG_CNT(item); i++) {
            
            if(!msg_info->read_flag){
                return 1;
            }
        
            msg_info = (app_adaptor_voice_msg_info_t *)_lv_ll_get_next(VOICE_MSG_LIST(item), msg_info);    
        }  
    }

	return 0;
}

#endif  


void voice_msg_chat_msg_delete_all_confirm_prepare_destory(lv_obj_t * activity_obj)
{
    lv_watch_png_cache_all_free();
}

void voice_msg_del_all_msg_yes_btn_action (lv_obj_t * btn, lv_event_t e)
{
    if(LV_EVENT_CLICKED != e) {
        return;
    }
	
    lv_obj_t * act_obj = lv_obj_get_parent(btn);
    if(!act_obj) return;
        
    lv_voice_msg_del_ext_t * ext = lv_obj_get_ext_attr(act_obj);
    if(!ext) return;
    voice_msg_delete_msg_db(ext->cur_contact->index);

	MMI_ModemAdp_WS_Yn_del_session(ext->cur_contact->imei);

    voice_msg_del_interface(ACT_ID_VOICE_MSG_CHAT);
#if defined(__XF_LCD_STYLE_ROUND__)
	voice_msg_del_interface(ACT_ID_VOICE_DEL_CHAT);
#endif
    voice_msg_del_interface(ACT_ID_VOICE_MSG_DEL_CONFIRM);
    
	
}

lv_res_t voice_msg_del_all_msg_no_btn_action(lv_obj_t * btn, lv_event_t e)
{
    if(LV_EVENT_CLICKED == e) {
        voice_msg_del_interface(ACT_ID_VOICE_MSG_DEL_CONFIRM);
    }
}

lv_obj_t * voice_msg_chat_msg_delete_all_confirm(voice_msg_contact_t * cur_contact)
{
    lv_obj_t * label;
    lv_obj_t * label_tip;
    lv_obj_t * content;
    lv_obj_t * btn_no;
    lv_obj_t * btn_yes;

    lv_watch_activity_ext_t activity_ext;
    memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
    activity_ext.actId = ACT_ID_VOICE_MSG_DEL_CONFIRM;
    activity_ext.create = NULL;
    activity_ext.prepare_destory = voice_msg_chat_msg_delete_all_confirm_prepare_destory;
    lv_obj_t * activity_obj = lv_watch_creat_activity_obj(&activity_ext);
    LV_ASSERT_MEM(activity_obj);

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);
    lv_voice_msg_del_ext_t * ext = lv_obj_allocate_ext_attr(obj, sizeof(lv_voice_msg_del_ext_t));
    LV_ASSERT_MEM(ext);
    ext->cur_contact = cur_contact;
    ext->node = NULL;

    label = lv_label_create(obj, NULL);
    lv_obj_set_size(label, LV_HOR_RES, LV_VER_RES / 6);
    lv_label_set_text_id(label, WATCH_TEXT_ID_DEL_MSG);
    lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font20);
    lv_obj_align(label, obj, LV_ALIGN_IN_TOP_MID, 0, LV_VER_RES/10);

    label_tip = lv_label_create(obj, NULL);
    lv_label_set_text_id(label_tip, WATCH_TEXT_ID_DEL_ALL_MSG_CONFIRM);
    lv_obj_add_style(label_tip, LV_LABEL_PART_MAIN, &lv_watch_font20);
    lv_label_set_long_mode(label_tip, LV_LABEL_LONG_DOT);
    lv_obj_set_size(label_tip, LV_HOR_RES-10, LV_VER_RES / 4);
    lv_obj_align(label_tip, NULL, LV_ALIGN_IN_TOP_MID, 0,60);
    btn_no = lv_imgbtn_create(obj, NULL);
    lv_imgbtn_set_src(btn_no, LV_BTN_STATE_RELEASED, ICON_CANCEL);
    lv_imgbtn_set_src(btn_no, LV_BTN_STATE_PRESSED, ICON_CANCEL);
#if defined(__XF_LCD_STYLE_ROUND__)
	lv_obj_align(btn_no, NULL, LV_ALIGN_IN_BOTTOM_LEFT, 35, -35);
#else
	lv_obj_align(btn_no, NULL, LV_ALIGN_IN_BOTTOM_LEFT, LV_HOR_RES/8, -LV_VER_RES/12);
#endif
    lv_obj_set_event_cb(btn_no, voice_msg_del_all_msg_no_btn_action);
    lv_watch_obj_add_element(btn_no);

    btn_yes = lv_imgbtn_create(obj, NULL);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_RELEASED, ICON_OK);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_PRESSED, ICON_OK);
#if defined(__XF_LCD_STYLE_ROUND__)
	lv_obj_align(btn_yes, NULL, LV_ALIGN_IN_BOTTOM_RIGHT, -35, -35);
#else
	lv_obj_align(btn_yes, NULL, LV_ALIGN_IN_BOTTOM_RIGHT, -LV_HOR_RES/8, -LV_VER_RES/12);
#endif
    lv_obj_set_event_cb(btn_yes,  voice_msg_del_all_msg_yes_btn_action);
    lv_watch_obj_add_element(btn_yes);

    return obj;
}



void voice_msg_out_of_memory_prepare_destory(lv_obj_t * activity_obj)
{
    lv_watch_png_cache_all_free();
}

lv_res_t out_of_memory_yes_btn_action(lv_obj_t * btn)
{
    voice_msg_del_interface(ACT_ID_OUT_OFF_MEMORY);
    return LV_RES_OK;
}

static void voice_msg_out_of_memory_create(lv_obj_t * activity_obj)
{
    lv_obj_t * label;
    lv_obj_t * label_tip;
    lv_obj_t * content;
    lv_obj_t * btn_yes;

    if(activity_obj == NULL) {
        lv_watch_activity_ext_t activity_ext;
        memset(&activity_ext, 0, sizeof(lv_watch_activity_ext_t));
        activity_ext.actId = ACT_ID_OUT_OFF_MEMORY;
        activity_ext.create = voice_msg_out_of_memory_create;
        activity_ext.prepare_destory = voice_msg_out_of_memory_prepare_destory;
        activity_obj = lv_watch_creat_activity_obj(&activity_ext);
        LV_ASSERT_MEM(activity_obj);
    }

    lv_obj_t * obj = lv_watch_obj_create(activity_obj);
    LV_ASSERT_MEM(obj);

    label = lv_label_create(obj, NULL);
    lv_obj_set_size(label, LV_HOR_RES, 32);
    lv_label_set_text_id(label, WATCH_TEXT_ID_OUT_OFF_MEMORY);
    lv_obj_add_style(label, LV_LABEL_PART_MAIN, &lv_watch_font20);
    lv_obj_align(label, obj, LV_ALIGN_IN_TOP_MID, 0, 20);

    content = lv_cont_create(obj, NULL);
    lv_obj_add_style(content, LV_CONT_PART_MAIN,  &lv_watch_style_transp);
    lv_cont_set_fit(content, LV_FIT_TIGHT);
    lv_cont_set_layout(content, LV_LAYOUT_CENTER);
    LV_ASSERT_MEM(content);
    if(NULL == content) {
        return NULL;
    }
    lv_obj_align(content, label, LV_ALIGN_OUT_BOTTOM_MID, 0, 10);
    lv_obj_set_click(content, false);

    label_tip = lv_label_create(content, NULL);
    char *text_show = lv_lang_get_text(WATCH_TEXT_ID_OUT_OFF_MEMORY_INFO);
    lv_coord_t width = _lv_txt_get_width(text_show, strlen(text_show), LV_THEME_WATCH_NIGHT_FONT_BIG, 0, LV_TXT_FLAG_NONE);
    if(width > LV_HOR_RES) {
        lv_label_set_long_mode(label_tip, LV_LABEL_LONG_BREAK);
        lv_obj_set_width(label_tip, LV_HOR_RES);
    }
    lv_label_set_text(label_tip, text_show);
    lv_obj_add_style(label_tip, LV_LABEL_PART_MAIN, &lv_watch_font20);

    btn_yes = lv_imgbtn_create(obj, NULL);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_RELEASED, ICON_OK);
    lv_imgbtn_set_src(btn_yes, LV_BTN_STATE_PRESSED, ICON_OK);
    lv_obj_align(btn_yes, NULL, LV_ALIGN_IN_BOTTOM_MID, 0, -10);
    lv_obj_set_event_cb(btn_yes, out_of_memory_yes_btn_action);
    lv_watch_obj_add_element(btn_yes);

}



void voice_msg_out_of_memory_show()
{
    if(lv_watch_get_activity_obj(ACT_ID_OUT_OFF_MEMORY))
    {
        return LV_RES_OK;
    }

    voice_msg_out_of_memory_create(NULL);
}


bool voice_msg_out_of_memory_check()
{
	#if 0
    if(FDI_GetFreeSpaceSize() < USE_LV_WATCH_TFS_RESERVED_SPACE)
    {
        voice_msg_out_of_memory_show();
        return true;
    }
	#endif

    return false;
}

/*
voice_msg_DownloadVoiceFileCB() voice_msg.len=0, len=1797, num=3942
voice_msg_DownloadVoiceFileCB() voice_msg.len=1797, len=2048, num=3942
voice_msg_DownloadVoiceFileCB() voice_msg.len=3845, len=97, num=3942
voice_msg_DownloadVoiceFileCB() voice_msg.len=3942, len=0, num=-1
voice_msg_DownloadVoiceFileCB() num<0!
*/
#if 0
static int voice_msg_DownloadVoiceFileCB(char * data, int len, int num, void *cbdata)
{
   ws_msg_info * msg_info = (ws_msg_info *)cbdata;

    WS_PRINTF("voice_msg_DownloadVoiceFileCB() voice_msg.len=%d,voice_msg.filelen:%d,len=%d, num=%d\n", msg_info->content.voice_msg.len,msg_info->content.voice_msg.filelen, len, num);
    if(data == NULL && num == 0){
        WS_PRINTF("voice_msg_DownloadVoiceFileCB() data error, reset!\n");
        msg_info->content.voice_msg.len = 0;
        return -1;
    }
	if(num < 0)
	{
		WS_PRINTF("voice_msg_DownloadVoiceFileCB() num<0!\n");
		if(yn_voicedw_buff==NULL){
			yn_voicedw_buff_len=msg_info->content.voice_msg.filelen;
			yn_voicedw_buff=(char*)lv_mem_alloc(yn_voicedw_buff_len+1);
			memset(yn_voicedw_buff,0,yn_voicedw_buff_len+1);
			memcpy(yn_voicedw_buff,msg_info->content.voice_msg.file,yn_voicedw_buff_len);
			WS_PRINTF("voice_msg_DownloadVoiceFileCB() save to buff!\n");
		}
		
		if(msg_info){
			if(msg_info->content.voice_msg.file){
				free(msg_info->content.voice_msg.file);
			}
			lv_mem_free(msg_info);
		}
		
		return -1;
	}

	if((msg_info->content.voice_msg.filelen == 0) && (msg_info->content.voice_msg.file == NULL)){
		msg_info->content.voice_msg.filelen = num;
		msg_info->content.voice_msg.file = malloc(num);
	}else{
		if(msg_info->content.voice_msg.filelen < num){
            msg_info->content.voice_msg.filelen = num;
    		msg_info->content.voice_msg.file = realloc(msg_info->content.voice_msg.file, num);
		}
    }
		
    if(msg_info->content.voice_msg.len>=msg_info->content.voice_msg.filelen){
        WS_PRINTF("voice_msg_DownloadVoiceFileCB() voice_msg.filelen=%d\n", msg_info->content.voice_msg.filelen);
		if(yn_voicedw_buff==NULL){
			yn_voicedw_buff_len=msg_info->content.voice_msg.filelen;
			yn_voicedw_buff=(char*)lv_mem_alloc(yn_voicedw_buff_len+1);
			memset(yn_voicedw_buff,0,yn_voicedw_buff_len+1);
			strcpy(yn_voicedw_buff,msg_info->content.voice_msg.file);
		}
		
		free(msg_info->content.voice_msg.file);
		lv_mem_free(msg_info);
        return 0;
    }

    if((msg_info->content.voice_msg.len + len)<=msg_info->content.voice_msg.filelen){
        memcpy(msg_info->content.voice_msg.file + msg_info->content.voice_msg.len, data, len);

        msg_info->content.voice_msg.len += len;
    }
    
    return 0;

}
void voice_msg_DownloadVoiceFile(char *url)
{
    void *http_req=NULL;
     ws_msg_info *msg_info = (ws_msg_info *)lv_mem_alloc(sizeof(ws_msg_info));
    
	WS_PRINTF("voice_msg_DownloadVoiceFile() url:%s\n", url);
	if(yn_voicedw_buff&&(strcmp(voiceurl_bak,url)==0))
	{
		WS_PRINTF("voice_msg_DownloadVoiceFile() avoid repeat ,error return\n");
		return ;
	}
	
	if(yn_voicedw_buff)
	{
		lv_mem_free(yn_voicedw_buff);
		yn_voicedw_buff=NULL;
	}
	yn_voicedw_buff_len=0;
	
	memset(voiceurl_bak,0,sizeof(voiceurl_bak));
	strncpy(voiceurl_bak,url,256);
	
    memset(msg_info, 0, sizeof(ws_msg_info));
    
    http_req = ws_client_http_req_msg_create_GET(url, voice_msg_DownloadVoiceFileCB, msg_info, 3);
	
    MMI_ModemAdp_SendHttpReq(MMI_ModemAdp_WS_GetClient(), http_req, 0);
}
#endif

static int voice_msg_DownloadPhotoFileCB(char * data, int len, int num, void *cbdata)
{
   ws_msg_info * msg_info = (ws_msg_info *)cbdata;

    WS_PRINTF("voice_msg_DownloadPhotoFileCB() voice_msg.len=%d,voice_msg.filelen:%d,len=%d, num=%d\n", msg_info->content.voice_msg.len,msg_info->content.voice_msg.filelen, len, num);
    if(data == NULL && num == 0){
        WS_PRINTF("voice_msg_DownloadPhotoFileCB() data error, reset!\n");
        msg_info->content.voice_msg.len = 0;
        return -1;
    }
	if(num < 0)
	{
		WS_PRINTF("voice_msg_DownloadPhotoFileCB() num<0!\n");
		if(yn_photodw_buff==NULL){
			yn_photodw_buff_len=msg_info->content.voice_msg.filelen;
			yn_photodw_buff=(char*)lv_mem_alloc(yn_photodw_buff_len+1);
			memset(yn_photodw_buff,0,yn_photodw_buff_len+1);
			memcpy(yn_photodw_buff,msg_info->content.voice_msg.file,yn_photodw_buff_len);
			
			watch_gui_rpc_req(voice_msg_dw_photo_update, 0, 0, 0);
			WS_PRINTF("voice_msg_DownloadPhotoFileCB() save to buff!\n");
		}
		
		if(msg_info){
			if(msg_info->content.voice_msg.file){
				free(msg_info->content.voice_msg.file);
			}
			lv_mem_free(msg_info);
		}
		
		return -1;
	}

	if((msg_info->content.voice_msg.filelen == 0) && (msg_info->content.voice_msg.file == NULL)){
		msg_info->content.voice_msg.filelen = num;
		msg_info->content.voice_msg.file = malloc(num);
	}else{
		if(msg_info->content.voice_msg.filelen < num){
            msg_info->content.voice_msg.filelen = num;
    		msg_info->content.voice_msg.file = realloc(msg_info->content.voice_msg.file, num);
		}
    }
		
    if(msg_info->content.voice_msg.len>=msg_info->content.voice_msg.filelen){
        WS_PRINTF("voice_msg_DownloadVoiceFileCB() voice_msg.filelen=%d\n", msg_info->content.voice_msg.filelen);
		if(yn_photodw_buff==NULL){
			yn_photodw_buff_len=msg_info->content.voice_msg.filelen;
			yn_photodw_buff=(char*)lv_mem_alloc(yn_photodw_buff_len+1);
			memset(yn_photodw_buff,0,yn_photodw_buff_len+1);
			strcpy(yn_photodw_buff,msg_info->content.voice_msg.file);
			
			watch_gui_rpc_req(voice_msg_dw_photo_update, 0, 0, 0);
		}
		
		free(msg_info->content.voice_msg.file);
		lv_mem_free(msg_info);
        return 0;
    }

    if((msg_info->content.voice_msg.len + len)<=msg_info->content.voice_msg.filelen){
        memcpy(msg_info->content.voice_msg.file + msg_info->content.voice_msg.len, data, len);

        msg_info->content.voice_msg.len += len;
    }
    
    return 0;

}

void voice_msg_DownloadPhotoFile(char *url)
{
    void *http_req=NULL;
     ws_msg_info *msg_info = (ws_msg_info *)lv_mem_alloc(sizeof(ws_msg_info));
	if(url==NULL) return;
	WS_PRINTF("voice_msg_DownloadVoiceFile() url:%s\n", url);
	if(yn_photodw_buff&&(strcmp(photourl_bak,url)==0))
	{
		WS_PRINTF("voice_msg_DownloadVoiceFile() avoid repeat ,error return\n");
		return ;
	}
	
	if(yn_photodw_buff)
	{
		lv_mem_free(yn_photodw_buff);
		yn_photodw_buff=NULL;
	}
	yn_photodw_buff_len=0;
	
	memset(photourl_bak,0,sizeof(photourl_bak));
	strncpy(photourl_bak,url,256);
	yn_photodw_isgif=0;
	if(strstr(url,".gif")||strstr(url,".GIF")||strstr(url,".Gif"))
	{
		yn_photodw_isgif=1;
	}
    memset(msg_info, 0, sizeof(ws_msg_info));
    
    http_req = ws_client_http_req_msg_create_GET(url, voice_msg_DownloadPhotoFileCB, msg_info, 3);
	
    MMI_ModemAdp_SendHttpReq(MMI_ModemAdp_WS_GetClient(), http_req, 0);
}




#endif /*USE_LV_WATCH_VOICE_MSG*/
