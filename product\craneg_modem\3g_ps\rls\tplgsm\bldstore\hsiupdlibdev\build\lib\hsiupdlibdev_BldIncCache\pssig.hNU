/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/****************************************************************************
 *
 *                    TTP GSM Phase 2 Protocol Stack
 *
 *           Copyright (c) 1997 The Technology Partnership plc.
 *
 ****************************************************************************
 *
 *   $Id: //central/releases/Branch_release_9/tplgsm/psinc/pssig.h#4 $
 *   $Revision: #4 $
 *   $DateTime: 2004/01/14 14:10:56 $
 *
 ****************************************************************************
 *
 *  File Description :
 *      Protocol Stack Signal Definitions
 *
 ****************************************************************************/

#if !defined (EXCLUDE_MNCC)
    /*
    ** CC to AL signals
    */
    SIG_DEF( SIG_MNCC_DUMMY = MNCC_SIGNAL_BASE, EmptySignal              mncc_dummy)
    SIG_DEF( SIG_MNCC_SETUP_REQ,                MnccSetupReq             mnccSetupReq)
    SIG_DEF( SIG_MNCC_SETUP_RSP,                MnccSetupRsp             mnccSetupRsp)
    SIG_DEF( SIG_MNCC_REJ_REQ,                  MnccRejReq               mnccRejReq)
    SIG_DEF( SIG_MNCC_CALL_CONF_REQ,            MnccCallConfReq          mnccCallConfReq)
    SIG_DEF( SIG_MNCC_ALERT_REQ,                MnccAlertReq             mnccAlertReq)
    SIG_DEF( SIG_MNCC_NOTIFY_REQ,               MnccNotifyReq            mnccNotifyReq)
    SIG_DEF( SIG_MNCC_DISC_REQ,                 MnccDiscReq              mnccDiscReq)
    SIG_DEF( SIG_MNCC_REL_REQ,                  MnccRelReq               mnccRelReq)
    SIG_DEF( SIG_MNCC_FACILITY_REQ,             MnccFacilityReq          mnccFacilityReq)
    SIG_DEF( SIG_MNCC_START_DTMF_REQ,           MnccStartDtmfReq         mnccStartDtmfReq)
    SIG_DEF( SIG_MNCC_STOP_DTMF_REQ,            MnccStopDtmfReq          mnccStopDtmfReq)
    SIG_DEF( SIG_MNCC_MODIFY_REQ,               MnccModifyReq            mnccModifyReq)
    SIG_DEF( SIG_MNCC_MODIFY_RSP,               MnccModifyRsp            mnccModifyRsp)
    SIG_DEF( SIG_MNCC_HOLD_REQ,                 MnccHoldReq              mnccHoldReq)
    SIG_DEF( SIG_MNCC_RETRIEVE_REQ,             MnccRetrieveReq          mnccRetrieveReq)
    SIG_DEF( SIG_MNCC_SETUP_IND,                MnccSetupInd             mnccSetupInd)
    SIG_DEF( SIG_MNCC_SETUP_CNF,                MnccSetupCnf             mnccSetupCnf)
    SIG_DEF( SIG_MNCC_SETUP_COMPLETE_IND,       MnccSetupCompleteInd     mnccSetupCompleteInd)
    SIG_DEF( SIG_MNCC_CALL_PROCEEDING_IND,      MnccCallProceedingInd    mnccCallProceedingInd)
    SIG_DEF( SIG_MNCC_CALL_PROGRESS_IND,        MnccCallProgressInd      mnccCallProgressInd)
    SIG_DEF( SIG_MNCC_ALERT_IND,                MnccAlertInd             mnccAlertInd)
    SIG_DEF( SIG_MNCC_MODIFY_IND,               MnccModifyInd            mnccModifyInd)
    SIG_DEF( SIG_MNCC_NOTIFY_IND,               MnccNotifyInd            mnccNotifyInd)
    SIG_DEF( SIG_MNCC_DISC_IND,                 MnccDiscInd              mnccDiscInd)
    SIG_DEF( SIG_MNCC_REL_IND,                  MnccRelInd               mnccRelInd)
    SIG_DEF( SIG_MNCC_REL_CNF,                  MnccRelCnf               mnccRelCnf)
    SIG_DEF( SIG_MNCC_FACILITY_IND,             MnccFacilityInd          mnccFacilityInd)
    SIG_DEF( SIG_MNCC_START_DTMF_CNF,           MnccStartDtmfCnf         mnccStartDtmfCnf)
    SIG_DEF( SIG_MNCC_STOP_DTMF_CNF,            MnccStopDtmfCnf          mnccStopDtmfCnf)
    SIG_DEF( SIG_MNCC_MODIFY_CNF,               MnccModifyCnf            mnccModifyCnf)
    SIG_DEF( SIG_MNCC_SYNC_IND,                 MnccSyncInd              mnccSyncInd)
    SIG_DEF( SIG_MNCC_HOLD_CNF,                 MnccHoldCnf              mnccHoldCnf)
    SIG_DEF( SIG_MNCC_RETRIEVE_CNF,             MnccRetrieveCnf          mnccRetrieveCnf)
    SIG_DEF( SIG_MNCC_REJ_IND,                  MnccRejInd               mnccRejInd)
    SIG_DEF( SIG_MNCC_CRSS_CHANGE_STATE_REQ,    MnccCrssChangeStateReq   mnccCrssChangeStateReq)
#endif

#if !defined (EXCLUDE_MNSS)
    /*
    ** SS to AL signals
    */
    SIG_DEF( SIG_MNSS_DUMMY = MNSS_SIGNAL_BASE, EmptySignal              mnss_dummy)
    SIG_DEF( SIG_MNSS_BEGIN_REQ,                MnssBeginReq             mnssBeginReq)
    SIG_DEF( SIG_MNSS_FACILITY_REQ,             MnssFacilityReq          mnssFacilityReq)
    SIG_DEF( SIG_MNSS_END_REQ,                  MnssEndReq               mnssEndReq)
    SIG_DEF( SIG_MNSS_BEGIN_IND,                MnssBeginInd             mnssBeginInd)
    SIG_DEF( SIG_MNSS_FACILITY_IND,             MnssFacilityInd          mnssFacilityInd)
    SIG_DEF( SIG_MNSS_END_IND,                  MnssEndInd               mnssEndInd)
    SIG_DEF( SIG_MNSS_REJ_IND,                  MnssRejInd               mnssRejInd)
#endif

#if !defined (EXCLUDE_MNSM)
    /*
    ** sm-rl to sm-cm signals
    */
    SIG_DEF( SIG_MNSM_DUMMY = MNSM_SIGNAL_BASE, EmptySignal              mnsm_dummy)
    SIG_DEF( SIG_MNSM_ABORT_REQ,                MnsmAbortReq             mnsmAbortReq)
    SIG_DEF( SIG_MNSM_DATA_REQ,                 MnsmDataReq              mnsmDataReq)
    SIG_DEF( SIG_MNSM_DATA_IND,                 MnsmDataInd              mnsmDataInd)
    SIG_DEF( SIG_MNSM_EST_REQ,                  MnsmEstReq               mnsmEstReq)
    SIG_DEF( SIG_MNSM_EST_IND,                  MnsmEstInd               mnsmEstInd)
    SIG_DEF( SIG_MNSM_ERR_IND,                  MnsmErrInd               mnsmErrInd)
    SIG_DEF( SIG_MNSM_REL_REQ,                  MnsmRelReq               mnsmRelReq)
    SIG_DEF( SIG_MNSM_PS_ERROR_IND,             MnsmPsErrorInd           mnsmPsErrorInd)
    SIG_DEF( SIG_MNSM_CONFIG_REQ,               MnsmConfigReq            mnsmConfigReq)
#endif

#if !defined (EXCLUDE_MMXX)
    /*
    ** MM-CM signals
    */
    SIG_DEF( SIG_MMXX_DUMMY = MMXX_SIGNAL_BASE, EmptySignal              mmxx_dummy)
    SIG_DEF( SIG_MMXX_DATA_REQ,                 MmxxDataReq              mmxxDataReq)
    SIG_DEF( SIG_MMXX_EST_REQ,                  MmxxEstReq               mmxxEstReq)
    SIG_DEF( SIG_MMXX_REEST_REQ,                MmxxReestReq             mmxxReestReq)
    SIG_DEF( SIG_MMXX_REL_REQ,                  MmxxRelReq               mmxxRelReq)
    SIG_DEF( SIG_MMXX_REDUNDANT_1,              EmptySignal              mmxxRedundant1 )
    SIG_DEF( SIG_MMXX_EST_CNF,                  MmxxEstCnf               mmxxEstCnf)
    SIG_DEF( SIG_MMXX_REEST_CNF,                MmxxReestCnf             mmxxReestCnf)
    SIG_DEF( SIG_MMXX_DATA_IND,                 MmxxDataInd              mmxxDataInd)
    SIG_DEF( SIG_MMXX_ERR_IND,                  MmxxErrInd               mmxxErrInd)
    SIG_DEF( SIG_MMXX_EST_IND,                  MmxxEstInd               mmxxEstInd)
    SIG_DEF( SIG_MMXX_REL_IND,                  MmxxRelInd               mmxxRelInd)
    SIG_DEF( SIG_MMXX_GSMS_ROUTE_REQ,           MmxxGsmsRouteReq         mmxxGsmsRouteReq)
    SIG_DEF( SIG_MMXX_GSMS_ROUTE_CNF,           MmxxGsmsRouteCnf         mmxxGsmsRouteCnf)

    SIG_DEF( SIG_MMCC_SYNC_IND,                 MmccSyncInd              mmccSyncInd)
#endif

#if !defined (EXCLUDE_MMR)
    /*
    ** MM-AL (registration) signals
    */
    SIG_DEF( SIG_MMR_DUMMY = MMR_SIGNAL_BASE,   EmptySignal              mmr_dummy)
    SIG_DEF( SIG_MMR_CAMP_REQ,                  MmrCampReq               mmrCampReq)
    SIG_DEF( SIG_MMR_NREG_REQ,                  MmrNregReq               mmrNregReq)
    SIG_DEF( SIG_MMR_PLMN_LIST_REQ,             MmrPlmnListReq           mmrPlmnListReq)
    SIG_DEF( SIG_MMR_CLASSMARK_REQ,             MmrClassmarkReq          mmrClassmarkReq)
    SIG_DEF( SIG_MMR_REG_REQ,                   MmrRegReq                mmrRegReq)
    SIG_DEF( SIG_MMR_CAMP_CNF,                  MmrCampCnf               mmrCampCnf)
    SIG_DEF( SIG_MMR_PLMN_LIST_CNF,             MmrPlmnListCnf           mmrPlmnListCnf)
    SIG_DEF( SIG_MMR_REG_CNF,                   MmrRegCnf                mmrRegCnf)
    SIG_DEF( SIG_MMR_NREG_IND,                  MmrNregInd               mmrNregInd)
    SIG_DEF( SIG_MMR_ERROR_IND,                 MmrErrorInd              mmrErrorInd)
    SIG_DEF( SIG_MMR_RSSI_IND,                  MmrRssiInd               mmrRssiInd)
    SIG_DEF( SIG_MMR_REG_IND,                   MmrRegInd                mmrRegInd)
    SIG_DEF( SIG_MMR_ME_DATA_REQ,               MmrMeDataReq             mmrMeDataReq)
    SIG_DEF( SIG_MMR_CAMP_IND,                  MmrCampInd               mmrCampInd)
    SIG_DEF( SIG_MMR_NCAMP_IND,                 EmptySignal              mmrNCampInd)
    SIG_DEF( SIG_MMR_NREG_CNF,                  MmrNregCnf               mmrNregCnf)
    SIG_DEF( SIG_MMR_RETRY_PLMN_IND,            EmptySignal              mmrRetryPlmnInd)
    SIG_DEF( SIG_MMR_INFO_IND,                  MmrInfoInd               mmrInfoInd) /* 9802-1981 */
    SIG_DEF( SIG_MMR_PLMN_LIST_IND,             MmrPlmnListInd           mmrPlmnListInd)
    SIG_DEF( SIG_MMR_ENG_INFO_REQ,              MmrEngInfoReq            mmrEngInfoReq)    /* FR9805-2463 */
    SIG_DEF( SIG_MMR_NETWORK_INFO_IND,          MmrNetworkInfoInd        mmrNetworkInfoInd)
    SIG_DEF( SIG_MMR_CIPHER_IND,                MmrCipherInd             mmrCipherInd)
# if defined (RR_FORCE_HANDOVER)
    SIG_DEF( SIG_MMR_HO_CONTROL_REQ,            MmrHoControlReq          mmrHoControlReq)
# endif






       
    SIG_DEF( SIG_MMR_GPRS_MSLOT_CHANGE_REQ,     MmrGprsMslotChangeReq    mmrGprsMslotChangeReq)
# if defined (UPGRADE_R99)
    SIG_DEF( SIG_MMR_EQUIV_PLMN_LIST_REQ,       MmrEquivPlmnListReq      mmrEquivPlmnListReq)
# endif
#endif

#if !defined (EXCLUDE_CB)
    /*
    ** SMS/CB-RR signal
    */
    SIG_DEF( SIG_RRCB_DUMMY = CB_SIGNAL_BASE,   EmptySignal              rrcb_dummy)
    SIG_DEF( SIG_GRRCB_CONTROL_REQ,             GrrCbControlReq          grrCbControlReq)
    SIG_DEF( SIG_GRRCB_CELL_CHANGE_IND,         GrrCbCellChangeInd       grrCbCellChangeInd)
    /*
    ** SMS/CB-L2 signals
    */
    SIG_DEF( SIG_L2CB_HEADER_IND,               L2cbHeaderInd            l2cbHeaderInd)
    SIG_DEF( SIG_L2CB_DATA_IND,                 L2cbDataInd              l2cbDataInd)
    SIG_DEF( SIG_L2CB_SCHED_HEADER_IND,         L2cbSchedHeaderInd       l2cbSchedHeaderInd)
#endif

#if !defined (EXCLUDE_DL)
    /*
    ** RR-L2 signals
    */
    SIG_DEF( SIG_DL_DUMMY = DL_MDL_SIGNAL_BASE, EmptySignal              dl_dummy)
    SIG_DEF( SIG_DL_DATA_IND,                   DlDataInd                dlDataInd)
    SIG_DEF( SIG_DL_UNIT_DATA_IND,              DlUnitDataInd            dlUnitDataInd)
    SIG_DEF( SIG_DL_RELEASE_IND,                DlReleaseInd             dlReleaseInd)
    SIG_DEF( SIG_DL_ESTABLISH_IND,              DlEstablishInd           dlEstablishInd)
    SIG_DEF( SIG_DL_BCCH_BSIC_IND,              DlBcchBsicInd            dlBcchBsicInd)
    SIG_DEF( SIG_DL_ESTABLISH_CNF,              DlEstablishCnf           dlEstablishCnf)
    SIG_DEF( SIG_DL_RELEASE_CNF,                DlReleaseCnf             dlReleaseCnf)
    SIG_DEF( SIG_DL_SUSPEND_CNF,                DlSuspendCnf             dlSuspendCnf)
    SIG_DEF( SIG_DL_REDUNDANT_1,                EmptySignal              dlRedundant1)
    SIG_DEF( SIG_DL_REDUNDANT_2,                EmptySignal              dlRedundant2)
    SIG_DEF( SIG_DL_DATA_REQ,                   DlDataReq                dlDataReq)
    SIG_DEF( SIG_DL_UNIT_DATA_REQ,              DlUnitDataReq            dlUnitDataReq)
    SIG_DEF( SIG_DL_ESTABLISH_REQ,              DlEstablishReq           dlEstablishReq)
    SIG_DEF( SIG_DL_RELEASE_REQ,                DlReleaseReq             dlReleaseReq)
    SIG_DEF( SIG_DL_SUSPEND_REQ,                EmptySignal              dlSuspendReq)
    SIG_DEF( SIG_DL_RESUME_REQ,                 DlResumeReq              dlResumeReq)
    SIG_DEF( SIG_DL_RECONNECT_REQ,              DlReconnectReq           dlReconnectReq)
    SIG_DEF( SIG_DL_REDUNDANT_3,                EmptySignal              dlRedundant3)
    SIG_DEF( SIG_MDL_ERROR_IND,                 MdlErrorInd              mdlErrorInd)
    SIG_DEF( SIG_MDL_RELEASE_REQ,               EmptySignal              mdlReleaseReq)
#endif

#if !defined (EXCLUDE_SMS)
    /*
    ** SMS signals
    */
    SIG_DEF( SIG_TS_DUMMY = SMS_SIGNAL_BASE,    EmptySignal              ts_dummy)
    SIG_DEF( SIG_TS_SUBMIT_REQ,                 TsSubmitReq              tsSubmitReq)
    SIG_DEF( SIG_TS_COMMAND_REQ,                TsCommandReq             tsCommandReq)
    SIG_DEF( SIG_TS_DELIVER_REPORT_REQ,         TsDeliverReportReq       tsDeliverReportReq)
    SIG_DEF( SIG_TS_MEM_AVAIL_REQ,              TsMemAvailReq            tsMemAvailReq)
    SIG_DEF( SIG_TS_DELIVER_IND,                TsDeliverInd             tsDeliverInd)
    SIG_DEF( SIG_TS_STATUS_REPORT_IND,          TsStatusReportInd        tsStatusReportInd)
    SIG_DEF( SIG_TS_REPORT_IND,                 TsReportInd              tsReportInd)
    SIG_DEF( SIG_TS_PS_ERROR_IND,               TsPsErrorInd             tsPsErrorInd)
    SIG_DEF( SIG_TS_CONFIG_REQ,                 TsConfigReq              tsConfigReq)
    SIG_DEF( SIG_SMRL_DATA_REQ,                 SmrlDataReq              smrlDataReq)
    SIG_DEF( SIG_SMRL_DATA_IND,                 SmrlDataInd              smrlDataInd)
    SIG_DEF( SIG_SMRL_MEM_AVAIL_REQ,            SmrlMemAvailReq          smrlMemAvailReq)
    SIG_DEF( SIG_SMRL_REPORT_REQ,               SmrlReportReq            smrlReportReq)
    SIG_DEF( SIG_SMRL_REPORT_IND,               SmrlReportInd            smrlReportInd)
    SIG_DEF( SIG_SMRL_PS_ERROR_IND,             SmrlPsErrorInd           smrlPsErrorInd)
    SIG_DEF( SIG_TS_RAW_DELIVER_IND,            TsRawDeliverInd          tsRawDeliverInd)
    SIG_DEF( SIG_SMCM_UNIT_DATA_IND,            SmcmUnitDataInd          smcmUnitDataInd)
    SIG_DEF( SIG_SMRL_CONFIG_REQ,               SmrlConfigReq            smrlConfigReq)
#endif

#if !defined (EXCLUDE_MMSI)
    /*
    ** MM-SIM signals
    */
    SIG_DEF( SIG_GMM_SIM_DUMMY = SIM_SIGNAL_BASE, EmptySignal               sim_dummy)
    SIG_DEF( SIG_GMM_SIM_READ_DATA_REQ,           EmptySignal               gmmSimReadDataReq)
    SIG_DEF( SIG_GMM_SIM_READ_DATA_CNF,           GmmSimReadDataCnf         gmmSimReadDataCnf)
    SIG_DEF( SIG_GMM_SIM_WRITE_DATA_REQ,          GmmSimWriteDataReq        gmmSimWriteDataReq)
    SIG_DEF( SIG_GMM_SIM_WRITE_DATA_CNF,          GmmSimWriteDataCnf        gmmSimWriteDataCnf)





     
    SIG_DEF( SIG_GMM_SIM_GPRS_AUTH_REQ,           GmmSimGprsAuthenticateReq gmmSimGprsAuthenticateReq)
    SIG_DEF( SIG_GMM_SIM_GPRS_AUTH_CNF,           GmmSimGprsAuthenticateCnf gmmSimGprsAuthenticateCnf)
    SIG_DEF( SIG_GMM_SIM_AUTH_REQ,                GmmSimAuthenticateReq     gmmSimAuthenticateReq)
    SIG_DEF( SIG_GMM_SIM_AUTH_CNF,                GmmSimAuthenticateCnf     gmmSimAuthenticateCnf)
      
    SIG_DEF( SIG_GMM_SIM_CONNECTION_IND,          GmmSimConnectionInd       gmmSimConnectionInd)
    SIG_DEF( SIG_GMM_SIM_REMOVED_IND,             EmptySignal               gmmSimRemovedInd)
    SIG_DEF( SIG_GMM_SIM_INSERTED_IND,            GmmSimInsertedInd         gmmSimInsertedInd) /*job 100850*/
#if defined (UPGRADE_SIM_APP_TOOLKIT)
# if defined (UPGRADE_SAT97)
    SIG_DEF( SIG_GMM_SIM_LOCAL_INFO_IND,          EmptySignal               gmmSimLocalInfoInd)
    SIG_DEF( SIG_GMM_SIM_LOCAL_INFO_RSP,          GmmSimLocalInfoRsp        gmmSimLocalInfoRsp)
# endif
#endif

#endif    /* EXCLUDE_MMSI */

#if !defined (EXCLUDE_MNCB)
    /*
    ** CB signals to/from Application layer
    */
    SIG_DEF( SIG_MNCB_DUMMY = MNCB_SIGNAL_BASE, EmptySignal              mncb_dummy)
    SIG_DEF( SIG_MNCB_CONFIGURE_REQ,            MncbConfigureReq         mncbConfigureReq)
    SIG_DEF( SIG_MNCB_MESSAGE_IND,              MncbMessageInd           mncbMessageInd)
    SIG_DEF( SIG_MNCB_MACRO_MESSAGE_IND,        MncbMacroMessageInd      mncbMacroMessageInd)
    SIG_DEF( SIG_MNCB_CELL_CHANGE_IND,          MncbCellChangeInd        mncbCellChangeInd)
#endif

#if !defined (EXCLUDE_ALSI)
    /*
    ** SIM signals to/from Application layer
    */
    SIG_DEF( SIG_ALSI_DUMMY = ALSI_SIGNAL_BASE, EmptySignal              alsi_dummy)





     
    SIG_DEF( SIG_ALSI_SIM_INSERTED_IND,         AlsiSimInsertedInd       alsiSimInsertedInd)
    SIG_DEF( SIG_ALSI_SIM_REMOVED_IND,          AlsiSimRemovedInd        alsiSimRemovedInd)
    SIG_DEF( SIG_ALSI_SIM_INITIALISE_REQ,       AlsiSimInitialiseReq     alsiSimInitialiseReq)
    SIG_DEF( SIG_ALSI_SIM_INITIALISE_CNF,       AlsiSimInitialiseCnf     alsiSimInitialiseCnf)
      



      
    SIG_DEF( SIG_ALSI_SIM_GEN_ACCESS_REQ,       AlsiSimGenAccessReq      alsiSimGenAccessReq)
    SIG_DEF( SIG_ALSI_SIM_GEN_ACCESS_CNF,       AlsiSimGenAccessCnf      alsiSimGenAccessCnf)
    SIG_DEF( SIG_ALSI_TERMINATE_SESSION_REQ,    AlsiTerminateSessionReq  alsiTerminateSessionReq)
    SIG_DEF( SIG_ALSI_TERMINATE_SESSION_CNF,    AlsiTerminateSessionCnf  alsiTerminateSessionCnf)
    SIG_DEF( SIG_ALSI_READ_LP_REQ,              AlsiReadLpReq            alsiReadLpReq)
    SIG_DEF( SIG_ALSI_READ_LP_CNF,              AlsiReadLpCnf            alsiReadLpCnf)
    SIG_DEF( SIG_ALSI_WRITE_LP_REQ,             AlsiWriteLpReq           alsiWriteLpReq)
    SIG_DEF( SIG_ALSI_WRITE_LP_CNF,             AlsiWriteLpCnf           alsiWriteLpCnf)
    SIG_DEF( SIG_ALSI_READ_PLMNSEL_REQ,         AlsiReadPlmnSelReq       alsiReadPlmnSelReq)
    SIG_DEF( SIG_ALSI_READ_PLMNSEL_CNF,         AlsiReadPlmnSelCnf       alsiReadPlmnSelCnf)
    SIG_DEF( SIG_ALSI_WRITE_PLMNSEL_REQ,        AlsiWritePlmnSelReq      alsiWritePlmnSelReq)
    SIG_DEF( SIG_ALSI_WRITE_PLMNSEL_CNF,        AlsiWritePlmnSelCnf      alsiWritePlmnSelCnf)
    SIG_DEF( SIG_ALSI_READ_ACMMAX_REQ,          AlsiReadAcmMaxReq        alsiReadAcmMaxReq)
    SIG_DEF( SIG_ALSI_READ_ACMMAX_CNF,          AlsiReadAcmMaxCnf        alsiReadAcmMaxCnf)
    SIG_DEF( SIG_ALSI_WRITE_ACMMAX_REQ,         AlsiWriteAcmMaxReq       alsiWriteAcmMaxReq)
    SIG_DEF( SIG_ALSI_WRITE_ACMMAX_CNF,         AlsiWriteAcmMaxCnf       alsiWriteAcmMaxCnf)
    SIG_DEF( SIG_ALSI_READ_ACM_REQ,             AlsiReadAcmReq           alsiReadAcmReq)
    SIG_DEF( SIG_ALSI_READ_ACM_CNF,             AlsiReadAcmCnf           alsiReadAcmCnf)
    SIG_DEF( SIG_ALSI_WRITE_ACM_REQ,            AlsiWriteAcmReq          alsiWriteAcmReq)
    SIG_DEF( SIG_ALSI_WRITE_ACM_CNF,            AlsiWriteAcmCnf          alsiWriteAcmCnf)
    SIG_DEF( SIG_ALSI_INCREASE_ACM_REQ,         AlsiIncreaseAcmReq       alsiIncreaseAcmReq)
    SIG_DEF( SIG_ALSI_INCREASE_ACM_CNF,         AlsiIncreaseAcmCnf       alsiIncreaseAcmCnf)
    SIG_DEF( SIG_ALSI_READ_PUCT_REQ,            AlsiReadPuctReq          alsiReadPuctReq)
    SIG_DEF( SIG_ALSI_READ_PUCT_CNF,            AlsiReadPuctCnf          alsiReadPuctCnf)
    SIG_DEF( SIG_ALSI_WRITE_PUCT_REQ,           AlsiWritePuctReq         alsiWritePuctReq)
    SIG_DEF( SIG_ALSI_WRITE_PUCT_CNF,           AlsiWritePuctCnf         alsiWritePuctCnf)
    SIG_DEF( SIG_ALSI_READ_CBMI_REQ,            AlsiReadCbmiReq          alsiReadCbmiReq)
    SIG_DEF( SIG_ALSI_READ_CBMI_CNF,            AlsiReadCbmiCnf          alsiReadCbmiCnf)
    SIG_DEF( SIG_ALSI_WRITE_CBMI_REQ,           AlsiWriteCbmiReq         alsiWriteCbmiReq)
    SIG_DEF( SIG_ALSI_WRITE_CBMI_CNF,           AlsiWriteCbmiCnf         alsiWriteCbmiCnf)
    SIG_DEF( SIG_ALSI_READ_BCCH_REQ,            AlsiReadBcchReq          alsiReadBcchReq)
    SIG_DEF( SIG_ALSI_READ_BCCH_CNF,            AlsiReadBcchCnf          alsiReadBcchCnf)
    SIG_DEF( SIG_ALSI_WRITE_BCCH_REQ,           AlsiWriteBcchReq         alsiWriteBcchReq)
    SIG_DEF( SIG_ALSI_WRITE_BCCH_CNF,           AlsiWriteBcchCnf         alsiWriteBcchCnf)
    SIG_DEF( SIG_ALSI_ADD_FPLMN_REQ,            AlsiAddFplmnReq          alsiAddFplmnReq)
    SIG_DEF( SIG_ALSI_ADD_FPLMN_CNF,            AlsiAddFplmnCnf          alsiAddFplmnCnf)
    SIG_DEF( SIG_ALSI_DELETE_FPLMN_REQ,         AlsiDeleteFplmnReq       alsiDeleteFplmnReq)
    SIG_DEF( SIG_ALSI_DELETE_FPLMN_CNF,         AlsiDeleteFplmnCnf       alsiDeleteFplmnCnf)
    SIG_DEF( SIG_ALSI_READ_LOCI_REQ,            AlsiReadLociReq          alsiReadLociReq)
    SIG_DEF( SIG_ALSI_READ_LOCI_CNF,            AlsiReadLociCnf          alsiReadLociCnf)
    SIG_DEF( SIG_ALSI_READ_DIALNUM_REQ,         AlsiReadDialNumReq       alsiReadDialNumReq)
    SIG_DEF( SIG_ALSI_READ_DIALNUM_CNF,         AlsiReadDialNumCnf       alsiReadDialNumCnf)





      
    SIG_DEF( SIG_ALSI_DELETE_DIALNUM_REQ,       AlsiDeleteDialNumReq     alsiDeleteDialNumReq)
    SIG_DEF( SIG_ALSI_DELETE_DIALNUM_CNF,       AlsiDeleteDialNumCnf     alsiDeleteDialNumCnf)
    SIG_DEF( SIG_ALSI_WRITE_DIALNUM_REQ,        AlsiWriteDialNumReq      alsiWriteDialNumReq)
    SIG_DEF( SIG_ALSI_WRITE_DIALNUM_CNF,        AlsiWriteDialNumCnf      alsiWriteDialNumCnf)



      

    SIG_DEF( SIG_ALSI_ADD_DIALNUM_REQ,          AlsiAddDialNumReq        alsiAddDialNumReq)
    SIG_DEF( SIG_ALSI_ADD_DIALNUM_CNF,          AlsiAddDialNumCnf        alsiAddDialNumCnf)
      
    SIG_DEF( SIG_ALSI_LIST_DIALNUM_REQ,         AlsiListDialNumReq       alsiListDialNumReq)
    SIG_DEF( SIG_ALSI_LIST_DIALNUM_CNF,         AlsiListDialNumCnf       alsiListDialNumCnf)
    SIG_DEF( SIG_ALSI_DIALNUM_STATUS_REQ,       AlsiDialNumStatusReq     alsiDialNumStatusReq)
    SIG_DEF( SIG_ALSI_DIALNUM_STATUS_CNF,       AlsiDialNumStatusCnf     alsiDialNumStatusCnf)
    SIG_DEF( SIG_ALSI_FIXED_DIAL_REQ,           AlsiFixedDialReq         alsiFixedDialReq)
    SIG_DEF( SIG_ALSI_FIXED_DIAL_CNF,           AlsiFixedDialCnf         alsiFixedDialCnf)
    SIG_DEF( SIG_ALSI_READ_SM_REQ,              AlsiReadSmReq            alsiReadSmReq)
    SIG_DEF( SIG_ALSI_READ_SM_CNF,              AlsiReadSmCnf            alsiReadSmCnf)
    SIG_DEF( SIG_ALSI_WRITE_SM_REQ,             AlsiWriteSmReq           alsiWriteSmReq)
    SIG_DEF( SIG_ALSI_WRITE_SM_CNF,             AlsiWriteSmCnf           alsiWriteSmCnf)
    SIG_DEF( SIG_ALSI_DELETE_SM_REQ,            AlsiDeleteSmReq          alsiDeleteSmReq)
    SIG_DEF( SIG_ALSI_DELETE_SM_CNF,            AlsiDeleteSmCnf          alsiDeleteSmCnf)
    SIG_DEF( SIG_ALSI_ADD_SM_REQ,               AlsiAddSmReq             alsiAddSmReq)
    SIG_DEF( SIG_ALSI_ADD_SM_CNF,               AlsiAddSmCnf             alsiAddSmCnf)
    SIG_DEF( SIG_ALSI_SET_SM_RECORDSTAT_REQ,    AlsiSetSmRecordStatReq   alsiSetSmRecordStatReq)
    SIG_DEF( SIG_ALSI_SET_SM_RECORDSTAT_CNF,    AlsiSetSmRecordStatCnf   alsiSetSmRecordStatCnf)
    SIG_DEF( SIG_ALSI_READ_SMSP_REQ,            AlsiReadSmspReq          alsiReadSmspReq)
    SIG_DEF( SIG_ALSI_READ_SMSP_CNF,            AlsiReadSmspCnf          alsiReadSmspCnf)
    SIG_DEF( SIG_ALSI_WRITE_SMSP_REQ,           AlsiWriteSmspReq         alsiWriteSmspReq)
    SIG_DEF( SIG_ALSI_WRITE_SMSP_CNF,           AlsiWriteSmspCnf         alsiWriteSmspCnf)
    SIG_DEF( SIG_ALSI_DELETE_SMSP_REQ,          AlsiDeleteSmspReq        alsiDeleteSmspReq)
    SIG_DEF( SIG_ALSI_DELETE_SMSP_CNF,          AlsiDeleteSmspCnf        alsiDeleteSmspCnf)
    SIG_DEF( SIG_ALSI_ADD_SMSP_REQ,             AlsiAddSmspReq           alsiAddSmspReq)
    SIG_DEF( SIG_ALSI_ADD_SMSP_CNF,             AlsiAddSmspCnf           alsiAddSmspCnf)
    SIG_DEF( SIG_ALSI_LIST_SMSP_REQ,            AlsiListSmspReq          alsiListSmspReq)
    SIG_DEF( SIG_ALSI_LIST_SMSP_CNF,            AlsiListSmspCnf          alsiListSmspCnf)
    SIG_DEF( SIG_ALSI_READ_SMSS_REQ,            AlsiReadSmssReq          alsiReadSmssReq)
    SIG_DEF( SIG_ALSI_READ_SMSS_CNF,            AlsiReadSmssCnf          alsiReadSmssCnf)
    SIG_DEF( SIG_ALSI_WRITE_SMSS_REQ,           AlsiWriteSmssReq         alsiWriteSmssReq)
    SIG_DEF( SIG_ALSI_WRITE_SMSS_CNF,           AlsiWriteSmssCnf         alsiWriteSmssCnf)
    SIG_DEF( SIG_ALSI_CHV_FUNCTION_REQ,         AlsiChvFunctionReq       alsiChvFunctionReq)
    SIG_DEF( SIG_ALSI_CHV_FUNCTION_CNF,         AlsiChvFunctionCnf       alsiChvFunctionCnf)
    SIG_DEF( SIG_ALSI_READ_DIR_STATUS_REQ,      AlsiReadDirStatusReq     alsiReadDirStatusReq)
    SIG_DEF( SIG_ALSI_READ_DIR_STATUS_CNF,      AlsiReadDirStatusCnf     alsiReadDirStatusCnf)
    SIG_DEF( SIG_ALSI_READ_EF_STATUS_REQ,       AlsiReadEfStatusReq      alsiReadEfStatusReq)
    SIG_DEF( SIG_ALSI_READ_EF_STATUS_CNF,       AlsiReadEfStatusCnf      alsiReadEfStatusCnf)
    SIG_DEF( SIG_ALSI_READ_GID_REQ,             AlsiReadGidReq           alsiReadGidReq)
    SIG_DEF( SIG_ALSI_READ_GID_CNF,             AlsiReadGidCnf           alsiReadGidCnf)
    SIG_DEF( SIG_ALSI_READ_SPN_REQ,             AlsiReadSpnReq           alsiReadSpnReq)
    SIG_DEF( SIG_ALSI_READ_SPN_CNF,             AlsiReadSpnCnf           alsiReadSpnCnf)
    SIG_DEF( SIG_ALSI_LIST_DIALNUM_EXT_REQ,     AlsiListDialNumExtReq    alsiListDialNumExtReq)
    SIG_DEF( SIG_ALSI_LIST_DIALNUM_EXT_CNF,     AlsiListDialNumExtCnf    alsiListDialNumExtCnf)
    SIG_DEF( SIG_ALSI_CHV_STATUS_IND,           AlsiChvStatusInd         alsiChvStatusInd)  /*job 104913*/
# if defined (UPGRADE_SIM_PHASE_2P)
    SIG_DEF( SIG_ALSI_READ_ELP_REQ,             AlsiReadElpReq            alsiReadElpReq)
    SIG_DEF( SIG_ALSI_READ_ELP_CNF,             AlsiReadElpCnf            alsiReadElpCnf)
    SIG_DEF( SIG_ALSI_WRITE_ELP_REQ,            AlsiWriteElpReq           alsiWriteElpReq)
    SIG_DEF( SIG_ALSI_WRITE_ELP_CNF,            AlsiWriteElpCnf           alsiWriteElpCnf)
    SIG_DEF( SIG_ALSI_BARRED_DIAL_REQ,          AlsiBarredDialReq         alsiBarredDialReq)
    SIG_DEF( SIG_ALSI_BARRED_DIAL_CNF,          AlsiBarredDialCnf         alsiBarredDialCnf)
    SIG_DEF( SIG_ALSI_READ_VGCS_VBS_REQ,        AlsiReadVgcsVbsReq        alsiReadVgcsVbsReq)
    SIG_DEF( SIG_ALSI_READ_VGCS_VBS_CNF,        AlsiReadVgcsVbsCnf        alsiReadVgcsVbsCnf)
    SIG_DEF( SIG_ALSI_READ_VGCS_VBS_STATUS_REQ, AlsiReadVgcsVbsStatusReq  alsiReadVgcsVbsStatusReq)
    SIG_DEF( SIG_ALSI_READ_VGCS_VBS_STATUS_CNF, AlsiReadVgcsVbsStatusCnf  alsiReadVgcsVbsStatusCnf)
    SIG_DEF( SIG_ALSI_READ_EMLPP_REQ,           AlsiReadEmlppReq          alsiReadEmlppReq)
    SIG_DEF( SIG_ALSI_READ_EMLPP_CNF,           AlsiReadEmlppCnf          alsiReadEmlppCnf)
    SIG_DEF( SIG_ALSI_READ_AAEM_REQ,            AlsiReadAaemReq           alsiReadAaemReq)
    SIG_DEF( SIG_ALSI_READ_AAEM_CNF,            AlsiReadAaemCnf           alsiReadAaemCnf)
    SIG_DEF( SIG_ALSI_WRITE_AAEM_REQ,           AlsiWriteAaemReq          alsiWriteAaemReq)
    SIG_DEF( SIG_ALSI_WRITE_AAEM_CNF,           AlsiWriteAaemCnf          alsiWriteAaemCnf)
    SIG_DEF( SIG_ALSI_READ_CBMID_REQ,           AlsiReadCbmidReq          alsiReadCbmidReq)
    SIG_DEF( SIG_ALSI_READ_CBMID_CNF,           AlsiReadCbmidCnf          alsiReadCbmidCnf)
    SIG_DEF( SIG_ALSI_READ_CBMIR_REQ,           AlsiReadCbmirReq          alsiReadCbmirReq)
    SIG_DEF( SIG_ALSI_READ_CBMIR_CNF,           AlsiReadCbmirCnf          alsiReadCbmirCnf)
    SIG_DEF( SIG_ALSI_WRITE_CBMIR_REQ,          AlsiWriteCbmirReq         alsiWriteCbmirReq)
    SIG_DEF( SIG_ALSI_WRITE_CBMIR_CNF,          AlsiWriteCbmirCnf         alsiWriteCbmirCnf)
    SIG_DEF( SIG_ALSI_READ_DCK_REQ,             AlsiReadDckReq            alsiReadDckReq)
    SIG_DEF( SIG_ALSI_READ_DCK_CNF,             AlsiReadDckCnf            alsiReadDckCnf)
    SIG_DEF( SIG_ALSI_RESET_DCK_REQ,            AlsiResetDckReq           alsiResetDckReq)
    SIG_DEF( SIG_ALSI_RESET_DCK_CNF,            AlsiResetDckCnf           alsiResetDckCnf)
    SIG_DEF( SIG_ALSI_READ_CNL_REQ,             AlsiReadCnlReq            alsiReadCnlReq)
    SIG_DEF( SIG_ALSI_READ_CNL_CNF,             AlsiReadCnlCnf            alsiReadCnlCnf)
    SIG_DEF( SIG_ALSI_READ_NIA_REQ,             AlsiReadNiaReq            alsiReadNiaReq)
    SIG_DEF( SIG_ALSI_READ_NIA_CNF,             AlsiReadNiaCnf            alsiReadNiaCnf)
    SIG_DEF( SIG_ALSI_READ_SMSR_REQ,            AlsiReadSmsrReq           alsiReadSmsrReq)
    SIG_DEF( SIG_ALSI_READ_SMSR_CNF,            AlsiReadSmsrCnf           alsiReadSmsrCnf)
    SIG_DEF( SIG_ALSI_WRITE_SMSR_REQ,           AlsiWriteSmsrReq          alsiWriteSmsrReq)
    SIG_DEF( SIG_ALSI_WRITE_SMSR_CNF,           AlsiWriteSmsrCnf          alsiWriteSmsrCnf)
    SIG_DEF( SIG_ALSI_LIST_SMSR_REQ,            AlsiListSmsrReq           alsiListSmsrReq)
    SIG_DEF( SIG_ALSI_LIST_SMSR_CNF,            AlsiListSmsrCnf           alsiListSmsrCnf)
# endif

# if !defined (SIM_REMOVE_CPHS)
    /* CPHS specific signals */
    SIG_DEF( SIG_ALSI_READ_CPHS_DATA_REQ,       EmptySignal               alsiReadCphsDataReq)
    SIG_DEF( SIG_ALSI_CPHS_DATA_IND,            AlsiCphsDataInd           alsiCphsDataInd)
    SIG_DEF( SIG_ALSI_CPHS_READ_CFF_REQ,        AlsiCphsReadCffReq        alsiCphsReadCffReq)
    SIG_DEF( SIG_ALSI_CPHS_READ_CFF_CNF,        AlsiCphsReadCffCnf        alsiCphsReadCffCnf)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_CFF_REQ,       AlsiCphsWriteCffReq       alsiCphsWriteCffReq)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_CFF_CNF,       AlsiCphsWriteCffCnf       alsiCphsWriteCffCnf)
    SIG_DEF( SIG_ALSI_CPHS_READ_VMWF_REQ,       AlsiCphsReadVmwfReq       alsiCphsReadVmwfReq)
    SIG_DEF( SIG_ALSI_CPHS_READ_VMWF_CNF,       AlsiCphsReadVmwfCnf       alsiCphsReadVmwfCnf)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_VMWF_REQ,      AlsiCphsWriteVmwfReq      alsiCphsWriteVmwfReq)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_VMWF_CNF,      AlsiCphsWriteVmwfCnf      alsiCphsWriteVmwfCnf)

    SIG_DEF( SIG_ALSI_CPHS_READ_CSP_REQ,        AlsiCphsReadCspReq        alsiCphsReadCspReq)
    SIG_DEF( SIG_ALSI_CPHS_READ_CSP_CNF,        AlsiCphsReadCspCnf        alsiCphsReadCspCnf)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_CSP_ENTRY_REQ, AlsiCphsWriteCspEntryReq  alsiCphsWriteCspEntryReq)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_CSP_ENTRY_CNF, AlsiCphsWriteCspEntryCnf  alsiCphsWriteCspEntryCnf)
    SIG_DEF( SIG_ALSI_CPHS_INFO_NUM_STATUS_REQ, AlsiCphsInfoNumStatusReq  alsiCphsInfoNumStatusReq)
    SIG_DEF( SIG_ALSI_CPHS_INFO_NUM_STATUS_CNF, AlsiCphsInfoNumStatusCnf  alsiCphsInfoNumStatusCnf)
    SIG_DEF( SIG_ALSI_CPHS_LIST_INFO_NUM_REQ,   AlsiCphsListInfoNumReq    alsiCphsListInfoNumReq)
    SIG_DEF( SIG_ALSI_CPHS_LIST_INFO_NUM_CNF,   AlsiCphsListInfoNumCnf    alsiCphsListInfoNumCnf)
    SIG_DEF( SIG_ALSI_CPHS_READ_INFO_NUM_REQ,   AlsiCphsReadInfoNumReq    alsiCphsReadInfoNumReq)
    SIG_DEF( SIG_ALSI_CPHS_READ_INFO_NUM_CNF,   AlsiCphsReadInfoNumCnf    alsiCphsReadInfoNumCnf)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_INFO_NUM_REQ,  AlsiCphsWriteInfoNumReq   alsiCphsWriteInfoNumReq)
    SIG_DEF( SIG_ALSI_CPHS_WRITE_INFO_NUM_CNF,  AlsiCphsWriteInfoNumCnf   alsiCphsWriteInfoNumCnf)
# endif /* SIM REMOVE CPHS */

    SIG_DEF( SIG_ALSI_WRITE_RAW_SM_REQ,         AlsiWriteRawSmReq         alsiWriteRawSmReq)
    SIG_DEF( SIG_ALSI_WRITE_RAW_SM_CNF,         AlsiWriteRawSmCnf         alsiWriteRawSmCnf)
    SIG_DEF( SIG_ALSI_ADD_RAW_SM_REQ,           AlsiAddRawSmReq           alsiAddRawSmReq)
    SIG_DEF( SIG_ALSI_ADD_RAW_SM_CNF,           AlsiAddRawSmCnf           alsiAddRawSmCnf)

    SIG_DEF( SIG_ALSI_GET_FREE_SM_REQ,          AlsiGetFreeSmReq          alsiGetFreeSmReq)
    SIG_DEF( SIG_ALSI_GET_FREE_SM_CNF,          AlsiGetFreeSmCnf          alsiGetFreeSmCnf)












      
#endif

#if defined (UPGRADE_SIM_APP_TOOLKIT)
# if !defined (EXCLUDE_ALSA)
    /*
    ** SIM Application Toolkit signals to/from Application layer
    */
    SIG_DEF( SIG_ALSA_DUMMY = ALSA_SIGNAL_BASE, EmptySignal              alsa_dummy)
    SIG_DEF( SIG_ALSA_DISPLAY_TEXT_IND,         AlsaDisplayTextInd       alsaDisplayTextInd)
    SIG_DEF( SIG_ALSA_DISPLAY_TEXT_RSP,         AlsaDisplayTextRsp       alsaDisplayTextRsp)
    SIG_DEF( SIG_ALSA_GET_INKEY_IND,            AlsaGetInkeyInd          alsaGetInkeyInd)
    SIG_DEF( SIG_ALSA_GET_INKEY_RSP,            AlsaGetInkeyRsp          alsaGetInkeyRsp)
    SIG_DEF( SIG_ALSA_GET_INPUT_IND,            AlsaGetInputInd          alsaGetInputInd)
    SIG_DEF( SIG_ALSA_GET_INPUT_RSP,            AlsaGetInputRsp          alsaGetInputRsp)
    SIG_DEF( SIG_ALSA_PLAY_TONE_IND,            AlsaPlayToneInd          alsaPlayToneInd)
    SIG_DEF( SIG_ALSA_PLAY_TONE_RSP,            AlsaPlayToneRsp          alsaPlayToneRsp)
    SIG_DEF( SIG_ALSA_SET_UP_MENU_IND,          AlsaSetUpMenuInd         alsaSetUpMenuInd)
    SIG_DEF( SIG_ALSA_SET_UP_MENU_RSP,          AlsaSetUpMenuRsp         alsaSetUpMenuRsp)
    SIG_DEF( SIG_ALSA_SELECT_ITEM_IND,          AlsaSelectItemInd        alsaSelectItemInd)
    SIG_DEF( SIG_ALSA_SELECT_ITEM_RSP,          AlsaSelectItemRsp        alsaSelectItemRsp)
    SIG_DEF( SIG_ALSA_SEND_SM_IND,              AlsaSendSmInd            alsaSendSmInd)
    SIG_DEF( SIG_ALSA_SEND_SM_RSP,              AlsaSendSmRsp            alsaSendSmRsp)
    SIG_DEF( SIG_ALSA_SEND_SS_IND,              AlsaSendSsInd            alsaSendSsInd)
    SIG_DEF( SIG_ALSA_SEND_SS_RSP,              AlsaSendSsRsp            alsaSendSsRsp)
    SIG_DEF( SIG_ALSA_SET_UP_CALL_IND,          AlsaSetUpCallInd         alsaSetUpCallInd)
    SIG_DEF( SIG_ALSA_SET_UP_CALL_RSP,          AlsaSetUpCallRsp         alsaSetUpCallRsp)
    SIG_DEF( SIG_ALSA_REFRESH_IND,              AlsaRefreshInd           alsaRefreshInd)
    SIG_DEF( SIG_ALSA_REFRESH_RSP,              AlsaRefreshRsp           alsaRefreshRsp)
    SIG_DEF( SIG_ALSA_PROVIDE_LOCAL_INFO_IND,   AlsaProvideLocalInfoInd  alsaProvideLocalInfoInd)
    SIG_DEF( SIG_ALSA_PROVIDE_LOCAL_INFO_RSP,   AlsaProvideLocalInfoRsp  alsaProvideLocalInfoRsp)
    SIG_DEF( SIG_ALSA_SMS_PP_DOWNLOAD_REQ,      AlsaSmsPpDownloadReq     alsaSmsPpDownloadReq)
    SIG_DEF( SIG_ALSA_SMS_PP_DOWNLOAD_CNF,      AlsaSmsPpDownloadCnf     alsaSmsPpDownloadCnf)
    SIG_DEF( SIG_ALSA_CB_DOWNLOAD_REQ,          AlsaCbDownloadReq        alsaCbDownloadReq)
    SIG_DEF( SIG_ALSA_CB_DOWNLOAD_CNF,          AlsaCbDownloadCnf        alsaCbDownloadCnf)
    SIG_DEF( SIG_ALSA_MENU_SELECTION_REQ,       AlsaMenuSelectionReq     alsaMenuSelectionReq)
    SIG_DEF( SIG_ALSA_MENU_SELECTION_CNF,       AlsaMenuSelectionCnf     alsaMenuSelectionCnf)
    /* signals for call control implementation */
    SIG_DEF( SIG_ALSA_CC_CALL_SET_UP_REQ,       AlsaCcCallSetUpReq       alsaCcCallSetUpReq)
    SIG_DEF( SIG_ALSA_CC_CALL_SET_UP_CNF,       AlsaCcCallSetUpCnf       alsaCcCallSetUpCnf)
    SIG_DEF( SIG_ALSA_CC_SS_OPERATION_REQ,      AlsaCcSsOperationReq     alsaCcSsOperationReq)
    SIG_DEF( SIG_ALSA_CC_SS_OPERATION_CNF,      AlsaCcSsOperationCnf     alsaCcSsOperationCnf)
    /*
    ** This signal is only used internally by the SIM Manager. No other task
    ** should send this request/response to the SIM Manager.
    */
    SIG_DEF( SIG_ALSA_INTERNAL_FETCH_REQ,       EmptySignal              alsaInternalFetchReq)
    /*
    ** This signal is sent to the Application Layer when a FETCH command
    ** fails it indicates the reasons for the failure.
    */
    SIG_DEF( SIG_ALSA_FETCH_FAIL_IND,           AlsaFetchFailInd         alsaFetchFailInd)

    SIG_DEF( SIG_ALSA_PROACTIVE_SESSION_END_IND,EmptySignal              alsaProactiveSessionEndInd)
#  if defined (UPGRADE_SAT97)
    /* New signals for SIM Application Toolkit Release 97 implementation */
    SIG_DEF( SIG_ALSA_SEND_USSD_IND,            AlsaSendUssdInd          alsaSendUssdInd)
    SIG_DEF( SIG_ALSA_SEND_USSD_RSP,            AlsaSendUssdRsp          alsaSendUssdRsp)
    SIG_DEF( SIG_ALSA_SET_UP_EVENT_LIST_IND,    AlsaSetUpEventListInd    alsaSetUpEventListInd)
    SIG_DEF( SIG_ALSA_SET_UP_EVENT_LIST_RSP,    AlsaSetUpEventListRsp    alsaSetUpEventListRsp)
    SIG_DEF( SIG_ALSA_EVENT_DOWNLOAD_REQ,       AlsaEventDownloadReq     alsaEventDownloadReq)
    SIG_DEF( SIG_ALSA_EVENT_DOWNLOAD_CNF,       AlsaEventDownloadCnf     alsaEventDownloadCnf)
    SIG_DEF( SIG_ALSA_MOSM_CONTROL_REQ,         AlsaMoSmControlReq       alsaMoSmControlReq)
    SIG_DEF( SIG_ALSA_MOSM_CONTROL_CNF,         AlsaMoSmControlCnf       alsaMoSmControlCnf)
    /* job 103179 : indicate MM-IDLE mode */
    SIG_DEF( SIG_ALSA_MM_MODE_IND,              AlsaMmModeInd            alsaMmModeInd)
    SIG_DEF( SIG_ALSA_SEND_TERM_PROFILE_REQ,    AlsaSendTermProfileReq   alsaSendTermProfileReq)
    SIG_DEF( SIG_ALSA_SEND_TERM_PROFILE_CNF,    AlsaSendTermProfileCnf   alsaSendTermProfileCnf)
    /* job 102762 : get network measurements */
    SIG_DEF( SIG_ALSA_NETWORK_MEAS_REQ,         EmptySignal              alsaNetworkMeasReq)
    SIG_DEF( SIG_ALSA_NETWORK_MEAS_CNF,         AlsaNetworkMeasCnf       alsaNetworkMeasCnf)
#  endif









         /* UPGRADE_HOMEZONE */





















          















        

# endif
#endif








     
# if defined (UPGRADE_CURSOR_2)
    SIG_DEF( SIG_CSR_DUMMY = CSR_SIGNAL_BASE,   EmptySignal              csr_dummy)
    SIG_DEF( SIG_CSR_POSN_DATA_REQ,             CsrPosnDataReq           csrPosnDataReq)
    SIG_DEF( SIG_CSR_POSN_DATA_CNF,             CsrPosnDataCnf           csrPosnDataCnf)
    SIG_DEF( SIG_CSR_ABORT_REQ,                 CsrAbortReq              csrAbortReq)
    SIG_DEF( SIG_CSR_ABORT_CNF,                 CsrAbortCnf              csrAbortCnf)
    SIG_DEF( SIG_CSR_PROCEEDING_IND,            CsrProceedingInd         csrProceedingInd)
# endif
      


#if !defined (EXCLUDE_GRR)
    /*
    ** GMM-GRR signals
    */
    SIG_DEF( SIG_GRR_DUMMY = GRR_SIGNAL_BASE,    EmptySignal              grr_dummy)


     
    SIG_DEF( SIG_GRR_ABORT_REQ,                  EmptySignal              grrAbortReq)
      
    SIG_DEF( SIG_GRR_ACT_REQ,                    GrrActReq                grrActReq)
    SIG_DEF( SIG_GRR_DATA_REQ,                   GrrDataReq               grrDataReq)
    SIG_DEF( SIG_GRR_DEACT_REQ,                  GrrDeactReq              grrDeactReq)
    SIG_DEF( SIG_GRR_EST_REQ,                    GrrEstReq                grrEstReq)
    SIG_DEF( SIG_GRR_PLMN_LIST_REQ,              GrrPlmnListReq           grrPlmnListReq)
    SIG_DEF( SIG_GRR_CLASSMARK_REQ,              GrrClassmarkReq          grrClassmarkReq)
    SIG_DEF( SIG_GRR_EST_CNF,                    EmptySignal              grrEstCnf)
    SIG_DEF( SIG_GRR_PLMN_LIST_CNF,              GrrPlmnListCnf           grrPlmnListCnf)
    SIG_DEF( SIG_GRR_ABORT_IND,                  EmptySignal              grrAbortInd)
    SIG_DEF( SIG_GRR_ACT_IND,                    GrrActInd                grrActInd)
    SIG_DEF( SIG_GRR_DATA_IND,                   GrrDataInd               grrDataInd)
    SIG_DEF( SIG_GRR_EST_IND,                    EmptySignal              grrEstInd)
    SIG_DEF( SIG_GRR_REL_IND,                    GrrRelInd                grrRelInd)
    SIG_DEF( SIG_GRR_SYNC_IND,                   GrrSyncInd               grrSyncInd)
    SIG_DEF( SIG_GRR_UNIT_DATA_IND,              GrrUnitDataInd           grrUnitDataInd)
    SIG_DEF( SIG_GRR_PLMN_LIST_IND,              GrrPlmnListInd           grrPlmnListInd)
    SIG_DEF( SIG_GRR_TEST_IDLE_INFO_IND,         GrrTestIdleInfoInd       grrTestIdleInfoInd)
    SIG_DEF( SIG_GRR_TEST_DED_INFO_IND,          GrrTestDedInfoInd        grrTestDedInfoInd)
    SIG_DEF( SIG_GRR_TEST_IDLE_SCELL_IND,        GrrTestIdleSCellInd      grrTestIdleSCellInd)
    SIG_DEF( SIG_GRR_TEST_DED_SCELL_IND,         GrrTestDedSCellInd       grrTestDedSCellInd)
    SIG_DEF( SIG_GRR_DEACT_CNF,                  EmptySignal              grrDeactCnf)
    SIG_DEF( SIG_GRR_ACT_CNF,                    GrrActCnf                grrActCnf)
    SIG_DEF( SIG_GRR_ASSIGN_REQ,                 GrrAssignReq             grrAssignReq)
    SIG_DEF( SIG_GRR_PAGE_IND,                   GrrPageInd               grrPageInd)
    SIG_DEF( SIG_GRR_SYS_INFO_IND,               GrrSysInfoInd            grrSysInfoInd)
    SIG_DEF( SIG_GRR_ME_DATA_REQ,                GrrMeDataReq             grrMeDataReq)
    SIG_DEF( SIG_GRR_MS_DATA_REQ,                GrrMsDataReq             grrMsDataReq)
    SIG_DEF( SIG_GRR_UPDATE_REQ,                 GrrUpdateReq             grrUpdateReq)
    SIG_DEF( SIG_GRR_CELL_UPDATE_IND,            GrrCellUpdateInd         grrCellUpdateInd)
    SIG_DEF( SIG_GRR_TEST_MODE_REQ,              GrrTestModeReq           grrTestModeReq)
    SIG_DEF( SIG_GRR_TST_IDLE_SI_BUFFER_IND,     GrrTstIdleSiBufferInd    grrTstIdleSiBufferInd)
    SIG_DEF( SIG_GRR_TST_IDLE_RESEL_IND,         GrrTstIdleReselInd       grrTstIdleReselInd)
    SIG_DEF( SIG_GRR_TST_DED_BSIC_MON_IND,       GrrTstDedBsicMonInd      grrTstDedBsicMonInd)
    SIG_DEF( SIG_GRR_GPRS_MSLOT_CHANGE_REQ,      GrrGprsMslotChangeReq    grrGprsMslotChangeReq)
    SIG_DEF( SIG_GRR_GPRS_READY_STATE_REQ,       GrrGprsReadyStateReq     grrGprsReadyStateReq)
    SIG_DEF( SIG_GRR_PKT_MSG_DECODE_IND,         GrrPktMsgDecodeInd       grrPktMsgDecodeInd)
    SIG_DEF( SIG_GRR_TEST_RESEL_INFO_IND,        GrrTestReselInfoInd      grrTestReselInfoInd)
    SIG_DEF( SIG_GRR_TEST_GPRS_ENG_INFO_IND,     GrrTestGprsEngInfoInd    grrTestGprsEngInfoInd)

#if defined (UPGRADE_APP_INFO)
    SIG_DEF( SIG_GRR_AI_DATA_IND,                GrrAiDataInd             grrAiDataInd)
    SIG_DEF( SIG_GRR_AI_DATA_REQ,                GrrAiDataReq             grrAiDataReq)
    SIG_DEF( SIG_GRR_AI_RESET_IND,               GrrAiResetInd            grrAiResetInd)
#endif






     
#if defined (UPGRADE_CURSOR_2)
    SIG_DEF( SIG_GRR_CSR_POSN_DATA_REQ,          GrrCsrPosnDataReq         grrCsrPosnDataReq)
    SIG_DEF( SIG_GRR_CSR_POSN_DATA_CNF,          GrrCsrPosnDataCnf         grrCsrPosnDataCnf)
    SIG_DEF( SIG_GRR_CSR_MEAS_IND,               GrrCsrMeasInd             grrCsrMeasInd)
    SIG_DEF( SIG_GRR_CSR_TIMINGADV_IND,          GrrCsrTimingAdvInd        grrCsrTimingAdvInd)
    SIG_DEF( SIG_CSR_TEST_TA_IND,                CsrTestTaInd              csrTestTaInd)
    SIG_DEF( SIG_CSR_TEST_MEAS_IND,              CsrTestMeasInd            csrTestMeasInd)
#endif
      

#if defined (UPGRADE_R99)
    SIG_DEF( SIG_GRR_SHORT_PD_DECODE_IND,        GrrShortPdDecodeInd        grrShortPdDecodeInd)
#endif

#if defined (UPGRADE_SIM_APP_TOOLKIT)
#if defined (UPGRADE_SAT97)
    SIG_DEF( SIG_GRR_LOCAL_INFO_REQ,             EmptySignal                grrLocalInfoReq)
    SIG_DEF( SIG_GRR_LOCAL_INFO_CNF,             GrrLocalInfoCnf            grrLocalInfoCnf)
#endif
#endif   /* UPGRADE_SIM_APP_TOOLKIT */



         /* UPGRADE_DUAL_MODE */
#endif   /* EXCLUDE_GRR */











       /* UPGRADE_DUAL_MODE */
/* END OF FILE */
