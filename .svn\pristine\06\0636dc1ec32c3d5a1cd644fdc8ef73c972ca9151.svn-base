/**
 * @file upgrade.h
 *
 */
#ifndef UPGRADE_H
#define UPGRADE_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lvgl.h"
#include "lv_watch_conf.h"
#else
#include "../../../lvgl/lvgl.h"
#include "../../../lv_watch_conf.h"
#endif

#if USE_LV_WATCH_UPGRADE != 0

#if 1//defined(MSFOTARS)
#define UPGRADE_FOTA_MSRS  1
#include "../../../../product/craneg_modem/softutil/msfota/inc/msfotadm_define.h"
#endif 

/*********************
 *      DEFINES
 *********************/
#if USE_LV_FOTA_AUTO_CHECK != 0
typedef enum {
	MMI_FOTA_NO_CHECK,
	MMI_FOTA_CHECKED ,    
	MMI_FOTA_CHECKING
}MmiFotaCheckStatus;
#endif

typedef struct {
    char * versionName; //version name
    INT32   fileSize;
} FotaVersionInfo;

/**********************
 *      TYPEDEFS
 **********************/
typedef enum {
    UGD_STATE_NULL = 0,
    UGD_STATE_CV,
    UGD_STATE_DL,
    UGD_STATE_UPDATE_CONFIRM,
} lv_upgrade_state_t;

typedef struct {
    lv_watch_obj_ext_t lv_watch_obj;
    /*New data for this type */
    lv_obj_t * label_new_ver;
    lv_obj_t * label_result;
    lv_obj_t * cont_new_ver;
    lv_obj_t * cont_bar;
    lv_obj_t * bar;
    lv_obj_t * obj_no_update;
    lv_obj_t * obj_connecting_process;
    lv_obj_t * obj_downloading_process;
    lv_obj_t * button_check;
    lv_obj_t * lable_check;
    lv_obj_t * obj_confirm;
    lv_upgrade_state_t state;
    TIMER_ID download_timer_id;    //for test
} lv_upgrade_obj_ext_t;

/**********************
 * GLOBAL PROTOTYPES
 **********************/
lv_obj_t * upgrade_create(lv_obj_t * activity_obj);
void upgrade_create_btn_action(lv_obj_t * btn, lv_event_t e);
lv_upgrade_state_t upgrade_get_state(void);
void upgrade_check_version_ind_handler(FotaVersionInfo *version);
void upgrade_download_percent_ind_handler(INT32 percent);
void upgrade_restart_ind_handler(void);
void upgrade_result_ind_handler(INT32 result);

/**********************
 *      MACROS
 **********************/

#endif /*USE_LV_WATCH_UPGRADE*/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*UPGRADE_H*/
