/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/* must be included before checking signal DIAG_L1GKI, as it is defined there */
#include "ICAT_config.h"
#include "diag_osif.h"
#include INCLUDE_ASSERT_OR_DUMMY

#ifndef  DIAG_L1GKI
#ifndef DIAG_NO_GKI

#include "global_types.h"

#include "diagm.h"
#include "diag_API.h"

#include "DiagSig_PS.h"
#include "diag_tx.h"

#include "diag_mem.h"
#include "diag_init.h"

#include INCLUDE_ASSERT_OR_DUMMY

//Theoratically it si 32767 , but we it should not exceed reasonable size (~5K)
#define MAX_DIAG_BUFF_SIZE (0x7FFF)


// SAP for signal
#if !defined (DIAGNEWHEADER)
#define SIGNAL_SAP	LOGGED_MI
#else
#define SIGNAL_SAP	SAP_SIGNAL_SERVICE_REQUEST
#endif

#ifdef CRANEL_FP_8MRAM
#if defined _HERMON_
#if !defined(UPGRADE_LTE_ONLY)
#define M_KiOsTtiSetFrameNumber(a)  ((a) = HawKiOsTtiSetFrameNumber())
#define M_KiOsTtiSetGkiTimestamp(a) ((a) = HawKiOsTtiSetGkiTimestamp())
#else//UPGRADE_LTE_ONLY
#define M_KiOsTtiSetFrameNumber(a)  ((a) = 0xffffffff)
#define M_KiOsTtiSetGkiTimestamp(a) ((a) = 0xffffffff)
#endif//UPGRADE_LTE_ONLY
#endif
#else
#if defined _HERMON_
#define M_KiOsTtiSetFrameNumber(a)  ((a) = HawKiOsTtiSetFrameNumber())
#define M_KiOsTtiSetGkiTimestamp(a) ((a) = HawKiOsTtiSetGkiTimestamp())
#endif

#endif

// Default value at startup
BOOL SignalsTrace = TRUE;//True by default 25 Aug 2008
/*#ifdef DIAG_SIG_TRACE_DISABLE
  FALSE;
#else
  TRUE;
#endif*/


/******************************************************************/
/*                   Signal Filter Matrix                         */
/******************************************************************/
#ifndef  KI_DISABLE_TASK_SETS
UINT8 ***currentFilterLog = NULL;
UINT32   currentFilterLogSize = 0;
#else
typedef UINT8 **TinyFilterMatrix;
TinyFilterMatrix *currentFilterLog = NULL;
#endif
UINT16 matrixTotalSize = 0 ;

typedef struct
{
    UINT32  SignalID;
    UINT32  IsFiltered;
}SignalFilterInfoStruct;

/*^^Barak. couldn't find these definitions in TTP, therefore didn't remove it */
/******************************************************************/
/* Signal ID: coupled with TTPCom code: MUST be changed if IDs    */
/* modified inside the SYS group or SYS group base ID changes     */
#define SYS_SIGNAL_BASE        0x0100                  /* kisig.h */
#if defined(DIAG_4_BYTE_SIGNAL_IDS)
#define SIG_KI_SETS_FILTER_MATRIX (SYS_SIGNAL_BASE+19) /* pssignal.h */
#else
#define SIG_TINY_FILTER_MATRIX (SYS_SIGNAL_BASE+16) /* pssignal.h */
#endif
/******************************************************************/

typedef UINT8 Int8;
typedef UINT16 Int16;
typedef UINT32 Int32;

extern void ATRecv(int AtpIndex, void* data, UINT32 length);


#ifndef KI_DISABLE_TASK_SETS
int updateDiagSignalFilter(UINT8 *SigBodyPtr)
{
	Int32               dataSize;
    KiSetsFilterMatrix 	*signalData;
    Int8              **basePointer;
    Int8               *sigPointer;
    Int8               *sourcePointer;
    Int32               counter;
    Int32               tempData;
    Int8              **workPtr;
    Int32 				cpsr;

	UINT8 ***newFilterLog;
	UINT8 ***oldFilterLog;
	UINT32   newFilterLogSize;

	oldFilterLog	=	currentFilterLog;


    signalData = (KiSetsFilterMatrix *)(SigBodyPtr);

    /* Calculate how much memory is required for the matrix */
    dataSize = (signalData->numSets  * sizeof(Int8 **)) + (signalData->numBases * sizeof(Int8 *)) +
			   (signalData->matrixSize - ((signalData->numSets  * 4) + (signalData->numBases * 4))) ;


    newFilterLogSize    =   dataSize;

    /* Allocate the required memory */
    newFilterLog = DiagAlignMalloc( dataSize+300 ) ;

	if	(newFilterLog == NULL)
	{
		DIAG_FILTER(SW_PLAT,DIAGSIG,UPDATE_SIGNALS_FILTER_MATRIX_ERROR,DIAG_ERROR)
		diagTextPrintf("Error allocating memory for new filter matrix. Old filter is still valid");
		return	0;	//fail
	}

	/* Using a ** rather than *** makes the following a bit tidier */
    workPtr = (Int8 **)newFilterLog;

    /* Fill in matrix, starting with set pointers */
    /* Point to the start of the base pointers */
    basePointer   = &(workPtr[signalData->numSets]);
    /* Set indexes are at the start of the matrix */
    sourcePointer = signalData->matrix;
    for (counter = 0; (Int16)counter < signalData->numSets; counter++)
    {
    	/* Reconstruct the base index from the input signal */
        tempData = (Int32)((sourcePointer[0] << 24) | (sourcePointer[1] << 16) | (sourcePointer[2] <<  8) | sourcePointer[3]);

		/* Save the pointer to the first base in this set */
        workPtr[counter] = (Int8 *)&basePointer[tempData];
        /* Increment the sourcePointer */
        sourcePointer += 4;
    }

    /* Now fill in base pointers */
    /* Point to the start of the signal bitmaps */
    sigPointer   = (Int8 *)&(workPtr[signalData->numSets + signalData->numBases]);
    for (/* deliberate */; counter < (signalData->numSets + signalData->numBases); counter++)
    {
    	/* Reconstruct the signal bitmap index from the input signal */
        tempData = (Int32)((sourcePointer[0] << 24) | (sourcePointer[1] << 16) | (sourcePointer[2] <<  8) | sourcePointer[3]);

		/* Save the pointer to the first base in this set */
        workPtr[counter] = &(sigPointer[tempData]);
        /* Increment the sourcePointer */
        sourcePointer += 4;
    }

    /* Now copy over the signal bit maps */
    /* sourcePointer now points to the place where we want to copy the signal bitmap from */
    memcpy(sigPointer, sourcePointer, (size_t)(signalData->matrixSize - ((signalData->numSets  * 4) + (signalData->numBases * 4))));

	// update global filter matrix
	diag_lock(&cpsr);
	currentFilterLog	=	newFilterLog;
	currentFilterLogSize	=	newFilterLogSize;
    diag_unlock(cpsr);

    if (oldFilterLog != NULL)
	{
        DiagAlignFree(oldFilterLog);
		oldFilterLog=NULL;
	}

	return	1;	//success

}

static
BOOL diagSignalFiltered(SignalRecord* pSignal)
{
    if ( currentFilterLog )
    {
        SignalId
            signalId = pSignal->id ;

        Int32 bitOffset  = signalId;        /* ssssssssssssssssbbbbbbbbyyyyyiii */
        Int32 byteOffset = bitOffset  >> 3; /* 000ssssssssssssssssbbbbbbbbyyyyy */
        Int32 baseOffset = byteOffset >> 5; /* 00000000ssssssssssssssssbbbbbbbb */
        Int32 setOffset  = baseOffset >> 8; /* 0000000000000000ssssssssssssssss */

        bitOffset  &= 0x07;                 /* 00000000000000000000000000000iii */
        byteOffset &= 0x1f;                 /* 000000000000000000000000000yyyyy */
        baseOffset &= 0xff;                 /* 000000000000000000000000bbbbbbbb */

        // if ((((currentFilterLog[setOffset][baseOffset][byteOffset]) >> bitOffset) & 0x01) != 0)
        if ((((currentFilterLog[setOffset][baseOffset][byteOffset]) >> bitOffset) & 0x01) == 0)
        {
			return TRUE;
        }
    }
    return FALSE;
}

void    diagSetSignalFiltered   (SignalRecord* pSignal , UINT32 isFiltered)
{
    if ( currentFilterLog )
    {
        SignalId
            signalId = pSignal->id ;

        Int32 bitOffset  = signalId;        /* ssssssssssssssssbbbbbbbbyyyyyiii */
        Int32 byteOffset = bitOffset  >> 3; /* 000ssssssssssssssssbbbbbbbbyyyyy */
        Int32 baseOffset = byteOffset >> 5; /* 00000000ssssssssssssssssbbbbbbbb */
        Int32 setOffset  = baseOffset >> 8; /* 0000000000000000ssssssssssssssss */

        bitOffset  &= 0x07;                 /* 00000000000000000000000000000iii */
        byteOffset &= 0x1f;                 /* 000000000000000000000000000yyyyy */
        baseOffset &= 0xff;                 /* 000000000000000000000000bbbbbbbb */

        if  (isFiltered)
            currentFilterLog[setOffset][baseOffset][byteOffset] |=  (1 << bitOffset);
        else
            currentFilterLog[setOffset][baseOffset][byteOffset] &=  ~(1 << bitOffset);

    }
}
#else /* KI_DISABLE_TASK_SETS */

static
int updateDiagSignalFilter(UINT8 *SigBodyPtr)
{
    Int8   numBases;
    Int8  *pcMatrix, *tgtMatrix;
    Int8 **matrix;
    Int16  matrixSize, offset, count;
    TinyFilterData *fData;
    Int32 cpsr;

	TinyFilterMatrix *	oldFilterLog;
	TinyFilterMatrix *  newFilterLog;
	UINT16				newMatrixTotalSize;

	oldFilterLog	=	currentFilterLog;

    fData = (TinyFilterData *)(SigBodyPtr);

    numBases = fData->numberBases;

    pcMatrix = (fData->baseOffsets + (sizeof(Int8) * 2 * numBases));

    matrixSize = fData->matrixSize;
    newMatrixTotalSize   = (sizeof(Int8 *) * numBases) + matrixSize;

    newFilterLog = (TinyFilterMatrix *)DiagAlignMalloc( newMatrixTotalSize+300 ) ;

	if	(newFilterLog == NULL)
	{
		DIAG_FILTER(SW_PLAT,DIAGSIG,UPDATE_SIGNALS_FILTER_MATRIX_ERROR,DIAG_ERROR)
		diagTextPrintf("Error allocating memory for new filter matrix. Old filter is still valid");
		return	0;	//fail
	}


    matrix = (Int8 **)newFilterLog;

    tgtMatrix = (Int8 *)((Int8 *)(newFilterLog) + (sizeof(Int8 *) * numBases));

    for (count = 0; count < (numBases * 2); count += 2)
    {
        offset  = (fData->baseOffsets[count] << 8) + fData->baseOffsets[count + 1];
        *matrix = tgtMatrix + offset;
        matrix++;
    }

    DIAG_ASSERT (((Int8 *)matrix - (Int8 *)newFilterLog) == (newMatrixTotalSize - matrixSize));
    DIAG_ASSERT ((Int8 *)matrix == tgtMatrix);

    memcpy (tgtMatrix, pcMatrix, matrixSize);

	diag_lock(&cpsr);
	currentFilterLog	=	newFilterLog;
	matrixTotalSize		=	newMatrixTotalSize;
    diag_unlock(cpsr);

    if (oldFilterLog != NULL)
	{
        DiagAlignFree(oldFilterLog);
		oldFilterLog=NULL;
	}

	return	1; //sucess
}

static
BOOL diagSignalFiltered(SignalRecord* pSignal)
{
    if ( currentFilterLog )
    {
        SignalId
            signalId = pSignal->id ;

        Int8
            *pFilterMask = (Int8 *)currentFilterLog[signalId >> 8] + ((signalId & 0xf8) >> 3),
            Mask ;

        if ( (Int32)pFilterMask < (Int32)currentFilterLog  || (Int32)pFilterMask >= (Int32)currentFilterLog+matrixTotalSize )
            return FALSE;

        Mask = *pFilterMask >> (signalId & 0x07U) ;

//        if (((*((Int8*)currentFilterLog[signalId >> 8] + ((signalId & 0xf8) >> 3)) >> (signalId & 0x07U)) & 0x01) == 0 )
        if ( (Mask & 0x01) == 0 )
            return TRUE;
    }
    return FALSE;
}

void diagSetSignalFiltered(SignalRecord* pSignal , UINT32 isFiltered)
{
    if ( currentFilterLog )
    {
        SignalId
            signalId = pSignal->id ;

        Int8
            *pFilterMask = (Int8 *)currentFilterLog[signalId >> 8] + ((signalId & 0xf8) >> 3),
            Mask ;


        Mask    =   1 << (signalId & 0x07U);

        if  (isFiltered)
            *pFilterMask    |=  Mask;
        else
            *pFilterMask    &=  (~Mask);
    }
}

#endif	/* KI_DISABLE_TASK_SETS */

#define DIAGSIG_MAX_DATA_LENGTH (MAX_DIAG_BUFF_SIZE-(SIZE_OF_TX_PDU_SIG_HEADER + TX_PDU_FOOTER_SIZE + sizeof(LoggedSignalRecord) - sizeof(union Signal)))

void L1TraceSignal( void * signalRecord )
{
    LoggedSignalRecord *pHeader;
    SignalStructure * sig = ((SignalStructure *)signalRecord);
    UINT8 *p;
    UINT32 extraLength;
    UINT16 sigLen = sig->record.length;
	UINT32 decreasedBytes;

    if ((!SignalsTrace) || isDiagBlocked()) return;
    if (diagSignalFiltered(&sig->record)) return;


    // Small change from the original Manitoba version: limit the size of the data send to the DIAG

//   extraLength = (sigLen > DIAGSIG_MAX_DATA_LENGTH) ? DIAGSIG_MAX_DATA_LENGTH : sigLen;

	if (sigLen > DIAGSIG_MAX_DATA_LENGTH)
	{
		extraLength = DIAGSIG_MAX_DATA_LENGTH;
	}
	else
	{
		extraLength = sigLen;
	}

	// diagAlignAndHandleErr will add the TX_PDU_HEADER_SIZE (sizeof(TxPDU) - bigger then SIZE_OF_TX_PDU_SIG_HEADER) , TX_PDU_FOOTER_SIZE)
    p = (UINT8 *)diagAlignAndHandleErr (sizeof(LoggedSignalRecord) - sizeof(union Signal) + extraLength, sig->record.id, &decreasedBytes, SIGNAL_SAP ) ;

    if (!p)
        return ;

    pHeader = (LoggedSignalRecord *)(((TxPDU *)p)->data-decreasedBytes);//(p + SIZE_OF_TX_PDU_SIG_HEADER) ;
    pHeader->directives  = sig->record.directives;

//#if defined _HERMON_
//change framenumber setting from GSM To both 2G and Td ,Xinhua
#if defined (PHS_SW_DEMO_TTC) || defined (PHS_SW_TAVORP_YARDEN_CP) ||defined (PHS_SW_TTC_CPONLY) || defined _HERMON_

    M_KiOsTtiSetFrameNumber(pHeader->frameNumber);
    M_KiOsTtiSetGkiTimestamp( pHeader->time );
#else
    pHeader->frameNumber = L1GetFrameNumber();
    KiOsGetRelativeTime( &pHeader->time );
#endif

    pHeader->length = sig->record.length;
    pHeader->id = sig->record.id;

    //memcpy (p + SIZE_OF_TX_PDU_SIG_HEADER + sizeof (LoggedSignalRecord) - sizeof(union Signal), &sig->record.body, extraLength) ;
	memcpy (((TxPDU *)p)->data-decreasedBytes + sizeof (LoggedSignalRecord) - sizeof(union Signal), &sig->record.body, extraLength) ;

    diagSendPDUSignal ((void *)p, sizeof(LoggedSignalRecord) - sizeof(union Signal) + extraLength +  SIZE_OF_TX_PDU_SIG_HEADER);
	p=0;
}

void TraceSignal( void * signalRecord )
{
    LoggedSignalRecord *pHeader;
    UINT8 *p ;
    UINT32 extraLength;
	UINT32 decreasedBytes;

    if ((!SignalsTrace) || isDiagBlocked()) return;
	if (diagSignalFiltered((SignalRecord *)signalRecord)) return;

    // Small change from the original Manitoba version: limit the size of the data send to the DIAG
//    extraLength = (((SignalRecord *)signalRecord)->length > DIAGSIG_MAX_DATA_LENGTH) ? DIAGSIG_MAX_DATA_LENGTH : ((SignalRecord *)signalRecord)->length;

	if (((SignalRecord *)signalRecord)->length > DIAGSIG_MAX_DATA_LENGTH)
	{
		extraLength = DIAGSIG_MAX_DATA_LENGTH;
	}
	else
	{
		extraLength = ((SignalRecord *)signalRecord)->length;
	}

	// diagAlignAndHandleErr will add the TX_PDU_HEADER_SIZE (sizeof(TxPDU) - bigger then SIZE_OF_TX_PDU_SIG_HEADER) , TX_PDU_FOOTER_SIZE)
    p = (UINT8 *)diagAlignAndHandleErr (sizeof(LoggedSignalRecord) - sizeof(union Signal) + extraLength, ((SignalRecord *)signalRecord)->id, &decreasedBytes, SIGNAL_SAP) ;

    if ( ! p )
        return ;

    //pHeader = (LoggedSignalRecord *)(p + SIZE_OF_TX_PDU_SIG_HEADER) ;
	pHeader = (LoggedSignalRecord *)(((TxPDU *)p)->data-decreasedBytes);//(p + SIZE_OF_TX_PDU_SIG_HEADER) ;
    pHeader->directives  = ((SignalRecord *)signalRecord)->directives;

//#if defined _HERMON_
//change framenumber setting from GSM To both 2G and Td ,Xinhua
#if defined (PHS_SW_DEMO_TTC) || defined (PHS_SW_TAVORP_YARDEN_CP) ||defined (PHS_SW_TTC_CPONLY) || defined _HERMON_
    M_KiOsTtiSetFrameNumber(pHeader->frameNumber);
    M_KiOsTtiSetGkiTimestamp( pHeader->time );
#else
    pHeader->frameNumber = L1GetFrameNumber();
    KiOsGetRelativeTime( &pHeader->time );
#endif

    pHeader->length = ((SignalRecord *)signalRecord)->length;
    pHeader->id = ((SignalRecord *)signalRecord)->id;

    memcpy( ((TxPDU *)p)->data-decreasedBytes+sizeof(LoggedSignalRecord)-sizeof(union Signal), &((SignalRecord *)signalRecord)->body, extraLength ) ;

    diagSendPDUSignal( (void *)p, sizeof(LoggedSignalRecord) - sizeof(union Signal) + extraLength  +  SIZE_OF_TX_PDU_SIG_HEADER);
	p=0;
}

void TraceLoggedSignal( void * loggedSignalRecord )
{
    UINT8 *p ;
    UINT32 extraLength;
	SignalRecord signalRecord;//={0};
	UINT32 decreasedBytes;

	memset(&signalRecord, 0, sizeof(SignalRecord));

    if ((!SignalsTrace) || isDiagBlocked()) return;

    signalRecord.id = ((LoggedSignalRecord *)loggedSignalRecord)->id;
    if (diagSignalFiltered(&signalRecord)) return;

//  extraLength = (((LoggedSignalRecord *)loggedSignalRecord)->length > DIAGSIG_MAX_DATA_LENGTH) ? DIAGSIG_MAX_DATA_LENGTH : ((LoggedSignalRecord *)loggedSignalRecord)->length;

	if (((LoggedSignalRecord *)loggedSignalRecord)->length > DIAGSIG_MAX_DATA_LENGTH)
	{
		extraLength = DIAGSIG_MAX_DATA_LENGTH;
	}
	else
	{
		extraLength = ((LoggedSignalRecord *)loggedSignalRecord)->length;
	}

	// diagAlignAndHandleErr will add the TX_PDU_HEADER_SIZE (sizeof(TxPDU) - bigger then SIZE_OF_TX_PDU_SIG_HEADER) , TX_PDU_FOOTER_SIZE)
    p = (UINT8 *)diagAlignAndHandleErr (sizeof(LoggedSignalRecord) - sizeof(union Signal) + extraLength, signalRecord.id, &decreasedBytes, SIGNAL_SAP) ;

    if ( ! p )
        return ;

    /* Copy the passed logged signal including the body */
    memcpy(((TxPDU *)p)->data-decreasedBytes, loggedSignalRecord, sizeof(LoggedSignalRecord) - sizeof(union Signal) + extraLength ) ;

    diagSendPDUSignal( (void *)p, sizeof(LoggedSignalRecord) - sizeof(union Signal) + extraLength  +  SIZE_OF_TX_PDU_SIG_HEADER);
	p=0;
}

/************************************************************************\
 * due to different versions with TTPCOM, they need a call with 3 parameters to KiGetSignalBlock,
 * therefore, they will define this switch. In the future, when we will have the same version
 * we should remove the switch and have only the relevant code when that swith is defined
\************************************************************************/
#define GKI_GEN_STRUCT_MALLOC(a,l)  KiGetSignalBlock((l),0,&(a))
#define GKI_GEN_STRUCT_FREE(a)      KiDestroySignalBlock((a))



#ifndef _DIAG_RX_H_
#define SIGNAL_SERVER               6
#define GKI_CMD_SERVER              5
#endif

/*---------------------------------------------------------------------------
* The diagSendGkiCommandOrSignal() sends GKI only into RUNNING TTPCom stack.
* The _diagSigPsCheckFn() is used for checking.
*/

void diagSendGkiCommandOrSignal(UINT8 DiagSAP , void* ptr , UINT16 dataLength)
{

  if ((*(diagIntData._diagSigPsCheckFn))())
  {
    GenericStructure    *genStruct;
    SignalRecord        *sigRecord = (SignalRecord*)(ptr);

    /*-----------------4/4/2006 14:47-------------------
     * Boaz Sommer
     * First, check if this is a filter signal, and if so
     * update Diag-signals filter matrix
     * --------------------------------------------------*/
    if ((DiagSAP == SIGNAL_SERVER) &&
#ifndef KI_DISABLE_TASK_SETS
        (sigRecord->id == SIG_KI_SETS_FILTER_MATRIX))
#else
        (sigRecord->id == SIG_TINY_FILTER_MATRIX))
#endif
    {
		/*-----------------5/8/2006 14:32-------------------
		 * If updateDiagSignalFilter return 0, it failed its mission
		 * return without sending new filter matrix to Test Task
		 * --------------------------------------------------*/
        if	(!updateDiagSignalFilter((UINT8 *)&(sigRecord->body)))
			return;
    }


    if  (DiagSAP    ==  GKI_CMD_SERVER)
    {
        GKI_GEN_STRUCT_MALLOC   (genStruct,dataLength);
        genStruct->header.format    =   KI_COMMAND_FORMAT;
        memcpy  (&genStruct->command.record , ptr , dataLength);

        KiTtiProcessCommsRx (genStruct);
    }
    else if (DiagSAP == SIGNAL_SERVER)
    {
        GKI_GEN_STRUCT_MALLOC   (genStruct,dataLength);
        genStruct->header.format    =   KI_SIGNAL_FORMAT;
        memcpy  (&genStruct->signal.record , ptr , dataLength);

		// DIAG_FILTER(SW_PLAT, DIAG, diagsig_signal, INFO)
		// diagPrintf("Rx GKI, KI_SIGNAL_FORMAT");

    }
  }
}


//ICAT EXPORTED FUNCTION - SW_PLAT,DIAG,TurnSignalsOn
void TurnGKISignalsOn(void)
{
    UINT32 cpsr;

    diag_lock(&cpsr);
    SignalsTrace = TRUE;
    diag_unlock(cpsr);

	DIAG_FILTER(SW_PLAT,DIAG,TurnSignalsOnReply,DIAG_INFORMATION)
    diagTextPrintf("Signal trace is now ON");
}

//ICAT EXPORTED FUNCTION - SW_PLAT,DIAG,TurnSignalsOff
void TurnGKISignalsOff(void)
{
    UINT32 cpsr;

    diag_lock(&cpsr);
    SignalsTrace = FALSE;
    diag_unlock(cpsr);

	DIAG_FILTER(SW_PLAT,DIAG,TurnSignalsOffReply,DIAG_INFORMATION)
    diagTextPrintf("Signal trace is now OFF");
}

//ICAT EXPORTED FUNCTION - SW_PLAT,DIAG,getSignalsFilterMatrix
void    getSignalsFilterMatrix  (void)
{
    UINT32  filter_matrix_size;

    // if there's valid filter matrix
    if  (currentFilterLog)
    {
#ifndef KI_DISABLE_TASK_SETS
        filter_matrix_size  =   currentFilterLogSize;
#else
        filter_matrix_size  =   matrixTotalSize;
#endif

        DIAG_FILTER(SW_PLAT,DIAGSIG,SIGNALS_FILTER_MATRIX_REPORT,DIAG_INFORMATION)
        diagStructPrintf    ("%S" , currentFilterLog , filter_matrix_size);

    }
    // else, all signals have their default filter state
    else
    {
        DIAG_FILTER(SW_PLAT,DIAGSIG,DEFAULT_SIGNALS_FILTER_MATRIX_REPORT,DIAG_INFORMATION)
        diagPrintf    ("1");
    }
}

//ICAT EXPORTED FUNCTION - SW_PLAT,DIAG,setSignalFilter
void    setSignalFilter (SignalFilterInfoStruct *signalFilterInfo)
{
    SignalRecord    pSignal;

    pSignal.id  =   (SignalId)(signalFilterInfo->SignalID);

    diagSetSignalFiltered   (&pSignal , signalFilterInfo->IsFiltered);
}


//ICAT EXPORTED FUNCTION - SW_PLAT,DIAG,getSignalFilter
void    getSignalFilter (SignalFilterInfoStruct *signalFilterInfo)
{
    SignalRecord    pSignal;
    BOOL                isFiltered;

    pSignal.id  =   (SignalId)(signalFilterInfo->SignalID);


    isFiltered  =   diagSignalFiltered(&pSignal);


    DIAG_FILTER(SW_PLAT,DIAGSIG,SIGNAL_FILTER_REPORT,DIAG_INFORMATION)
    diagPrintf  ("%d:%d" , signalFilterInfo->SignalID , isFiltered);
}

typedef struct
{
//  unsigned char array[8*1024];
	unsigned char array[6*1024];
}St8kTest;

//ICAT EXPORTED FUNCTION - Diag,Utils,Test8KStructure
void _Test8KStructure(void)
{
	UINT32 i;
	unsigned char* array;
	array = (unsigned char*)(DiagAlignMalloc(6*1024));
	for (i=0 ; i<6*1024; i++)
		array[i] = (unsigned char)i;

    DIAG_FILTER(Diag,Utils,ST_8kTest, DIAG_INFORMATION)
	diagStructPrintf("%S{St8kTest}",(VOID*)array, (6*1024));

	DiagAlignFree(array);
	array=0;
 }

#endif /* DIAG_NO_GKI */
#endif /* DIAG_L1GKI */

