/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 *
 *                    TPL GSM GPRS Protocol Stack
 *
 *             Copyright (c) 1999 TTP Communications Ltd.
 *
 ***************************************************************************
 *
 *   $Id: //central/releases/Branch_release_9/tplgsm/gpinc/gpgentyp.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2004/01/14 14:10:56 $
 *
 ***************************************************************************
 *
 *  File Description :
 *
 *      General Types used in GPRS
 *
 ***************************************************************************/

#if !defined (GPGENTYP_H)
#define       GPGENTYP_H


/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <gp13_typ.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

#define NUM_TIMESLOTS           8

#define SEQUENCE_NUMBER_SPACE   128
#define WINDOW_SIZE             64

#if defined (UPGRADE_EDGE)

#if !defined (EGPRS_MULTISLOT_CLASS)
/*Assume EGPRS_MULTISLOT_CLASS 1 if not defined*/
#define EGPRS_MULTISLOT_CLASS 1
#endif

#if (EGPRS_MULTISLOT_CLASS == 1)
/*Max 1 down, max 1 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 192
#define EGPRS_MAX_UL_WINDOW_SIZE 192

#elif (EGPRS_MULTISLOT_CLASS == 2)
/*Max 2 down, Max 1 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 256
#define EGPRS_MAX_UL_WINDOW_SIZE 192

#elif (EGPRS_MULTISLOT_CLASS == 3)
/*Max 2 down, Max 2 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 256
#define EGPRS_MAX_UL_WINDOW_SIZE 256

#elif (EGPRS_MULTISLOT_CLASS == 4)
/*Max 3 down, Max 1 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 384
#define EGPRS_MAX_UL_WINDOW_SIZE 192

#elif (EGPRS_MULTISLOT_CLASS == 5)
/*Max 2 down, Max 2 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 256
#define EGPRS_MAX_UL_WINDOW_SIZE 256

#elif (EGPRS_MULTISLOT_CLASS == 6)
/*Max 3 down, Max 2 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 384
#define EGPRS_MAX_UL_WINDOW_SIZE 256

#elif (EGPRS_MULTISLOT_CLASS == 7)
/*Max 3 down, Max 3 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 384
#define EGPRS_MAX_UL_WINDOW_SIZE 384

#elif (EGPRS_MULTISLOT_CLASS == 8)
/*Max 4 down, Max 1 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 512
#define EGPRS_MAX_UL_WINDOW_SIZE 192

#elif (EGPRS_MULTISLOT_CLASS == 9)
/*Max 3 down, Max 2 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 384
#define EGPRS_MAX_UL_WINDOW_SIZE 256

#elif (EGPRS_MULTISLOT_CLASS == 10)
/*Max 4 down, Max 2 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 512
#define EGPRS_MAX_UL_WINDOW_SIZE 256

#elif (EGPRS_MULTISLOT_CLASS == 11)
/*Max 4 down, Max 3 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 512
#define EGPRS_MAX_UL_WINDOW_SIZE 384

#elif (EGPRS_MULTISLOT_CLASS == 12)
/*Max 4 down, Max 4 up*/
#define EGPRS_MAX_DL_WINDOW_SIZE 512
#define EGPRS_MAX_UL_WINDOW_SIZE 512

#endif

#define EGPRS_SEQUENCE_NUMBER_SPACE 2048

/*These defines are used to enable quick construction of the CPS field from MCS and PS's*/
#define MAC_CPS_MCS_8_BASE 0x0b  /* 0 1011 MCS_8, PS_1, PS_1*/
#define MAC_CPS_MCS_7_BASE 0x14  /* 1 0100 MCS_7, PS_1, PS_1*/
#define MAC_CPS_MCS_5_BASE 0x04  /*    100 MCS_5, PS_1*/
#define MAC_CPS_MCS_3_BASE 0x03  /*   0011 MCS_3, PS_1*/
#define MAC_CPS_MCS_2_BASE 0x09  /*   1001 MCS_2, PS_1*/
#define MAC_CPS_MCS_1_BASE 0x0b  /*   1011 MCS_1, PS_1*/

#endif

#define BITMAP_WINDOW_SIZE      WINDOW_SIZE/ALL_TIMESLOTS

#define RLC_MAC_PADDING_OCTET   0x2B    /* 0x0010 1011 04.60[11] */

#define DEFAULT_BS_CV_VALUE     15
#define INVALID_BS_CV_VALUE     0xFF

#define RLC_BLOCKS_IN_SHORT_ACCESS  8   /* number of blocks that can be sent */

#define FILL_FRAME              0x2b

#define DEFAULT_CONTROL_ACK     0x03    /* Packet Control Acknowledgement for*/
                                        /* non segmented control messages    */
                                        /* value '11' 04.60 sec 11.2.2       */

/***************************************************************************
 * Typed Constants
 **************************************************************************/

typedef enum GprsLogicalChannelTag
{
    LC_PACCH,
    LC_PAGCH,
    LC_PBCCH,
    LC_PCCCH
}
GprsLogicalChannel;

typedef enum RlcModeTag
{
    RLC_ACKNOWLEDGED_MODE   = 0,
    RLC_UNACKNOWLEDGED_MODE = 1
}
RlcMode;

typedef enum RlcmacReleaseCauseTag
{
    RLC_NORMAL_EVENT,
    RLC_NORMAL_RELEASE,
    RLC_ABNORMAL_RELEASE,

    /* release causes to Grr */
    /* A Packet UL Ack/Nack not received before T3182 timeout and the counter N3102 count
     * has not expired. GRR should send a channel request (PRACH) to re-establish the TBF */
    RLC_T3182_EXP_N3102_COUNTDOWN_NOT_EXP,

    /* A Packet UL Ack/Nack not received before T3182 timeout and the counter N3102 count
     * has expired. GRR should perform cell reselection and re-establish the TBF */
    RLC_T3182_EXP_N3102_COUNTDOWN_EXP,

    /* The number of blocks transmitted before a Packet UL Ack/Nack has been received to
     * complete contention resolution has exceeded the N3104 count. */
    RLC_N3104_MAXED_CONTENTION_RES_FAILED,

    /* There have been no RLCMAC downlink data blocks received by MAC for the T3190 period */
    RLC_T3190_TIMED_OUT,

    /* The network has not granted USF or we could not decode the USF to send uplink data
     * blocks for the T3180 period */
    RLC_T3180_TIMED_OUT,
    RLC_MAC_CTRL_BUFF_FULL,

    /* release causes from GRR */
    RLC_TLLI_CONTENTION_RES_FAILED,
    RLC_RADIO_PRIORITY_BARRED,
    RLC_ACCESS_CLASS_BARRED,
    RLC_QUEUEING_FAILURE,
    RLC_CIRCUIT_SWITCH_OPERATION,
    RLC_CELL_RESELECTION,
    RLC_CELL_NOT_AVAILABLE,
    RLC_T3142_RUNNING,
    RLC_RAC_HAS_CHANGED,
    RLC_CELL_ALLOCATION_NOT_AVAILABLE,
    RLC_PACKET_ACCESS_REJECT,
    RLC_RACH_FAIL,
    RLC_T3172_RUNNING,
    RLC_DL_TBF_ACTIVE,
    RLC_INVALID_SERVICE_STATUS,
    RLC_PRACH_FAIL,
    RLC_ILLEGAL_ALLOCATION,
    RLC_WAITING_FOR_UPDATE,
    RLC_BAD_TS_ALLOCATION,
    RLC_BAD_FREQ_PARAM_ELEMENT,
    RLC_FREQ_NOT_IMPLEMENTED
}
RlcmacReleaseCause;

typedef enum UplinkMessageTypeTag
{
    /*
     * GPRS encode utils assume that values of this enum
     * match the values of the 6-bit MESSAGE_TYPE field as defined in
     * 04.60 v8.0.0 11.2.0.2
     */
    PACKET_CELL_CHANGE_FAILURE          = 0x00,  /* 00 0000  */
    PACKET_CONTROL_ACKNOWLEDGEMENT      = 0x01,  /* 00 0001  */
    PACKET_DOWNLINK_ACK_NACK            = 0x02,  /* 00 0010  */
    PACKET_UPLINK_DUMMY_CONTROL_BLOCK   = 0x03,  /* 00 0011  */
    PACKET_MEASUREMENT_REPORT           = 0x04,  /* 00 0100  */
    PACKET_RESOURCE_REQUEST             = 0x05,  /* 00 0101  */
    PACKET_MOBILE_TBF_STATUS            = 0x06,  /* 00 0110  */
    PACKET_PSI_STATUS                   = 0x07,  /* 00 0111  */
    PACKET_ENHANCED_MEAS_REPORT         = 0x0A,  /* 00 1010  */
    PACKET_PAUSE                        = 0x09,  /* 00 1001  */
#if defined (UPGRADE_EDGE)
    EGPRS_PACKET_DOWNLINK_ACK_MESSAGE   = 0x08,  /* 00 1000  */
    ADDITIONAL_MS_RADIO_CAPABILITIES    = 0x0B,  /* 00 1011  */
#endif
    PACKET_CELL_CHANGE_NOTIFICATION     = 0x0C,  /* 00 1100  */
    PACKET_SI_STATUS                    = 0x0D,  /* 00 1101  */
    PACKET_INVALID_UPLINK_MESSAGE       = 0xFF
}
UplinkMessageType;

/***************************************************************************
 * Types
 **************************************************************************/

/***************************************************************************
 * Function Prototypes
 **************************************************************************/

/*
 * Arithmetic in modulo 128
 */
/* Add ADD to BSN in modulo 128 */
#define M_MOD128_ADD( BSN, ADD)    ((BSN  + ADD) & 0x7F)
/* Substract SUB from BSN in modulo 128 */
#define M_MOD128_SUB( BSN, SUB)    ((BSN >= SUB) ?       (BSN - SUB) : \
                                                  (0x80 + BSN - SUB))
/* Increment BSN by INC in modulo 128 */
#define M_MOD128_INC( BSN, INC)    BSN = M_MOD128_ADD (BSN, INC)
/* Decrement BSN by DEC in modulo 128 */
#define M_MOD128_DEC( BSN, DEC)    BSN = M_MOD128_SUB (BSN, DEC)
/* Compare two BSNs in modulo 128 */
extern SignedInt16 MacMod128Cmp (Int16 n1, Int16 n2);

#if defined (UPGRADE_EDGE)
/* Add ADD to BSN in modulo 2048 */
#define M_MOD2048_ADD( BSN, ADD)    ((BSN  + ADD) & (EGPRS_SEQUENCE_NUMBER_SPACE - 1))
/* Increment BSN by INC in modulo 128 */
#define M_MOD2048_INC( BSN, INC)    BSN = M_MOD2048_ADD (BSN, INC)
/* Substract SUB from BSN in modulo 128 */
#define M_MOD2048_SUB( BSN, SUB)    ((BSN >= SUB) ?       (BSN - SUB) : \
                                                  (0x800 + BSN - SUB))

/* Compare two BSNs in modulo 2048 */
extern SignedInt16 MacMod2048Cmp (Int16 n1, Int16 n2);

#endif /* UPGRADE_EDGE */


#endif


/* END OF FILE */

