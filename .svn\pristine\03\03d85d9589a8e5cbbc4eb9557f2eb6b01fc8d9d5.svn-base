/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "Ver2-ULP-Components"
 * 	found in "supl202.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_RTD_H_
#define	_RTD_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "RTDUnits.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* RTD */
typedef struct RTD {
	long	 rTDValue;
	RTDUnits_t	 rTDUnits;
	long	*rTDAccuracy	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} RTD_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_RTD;

#ifdef __cplusplus
}
#endif

#endif	/* _RTD_H_ */
#include <asn_internal.h>
