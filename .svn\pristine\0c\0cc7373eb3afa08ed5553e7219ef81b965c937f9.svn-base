/********************************************************************************//**
 * @file asr3601s_fota.c
 * @version   V1.00
 * @brief     
 * @note
 * <AUTHOR>
 * Copyright (c) 2019 ASR Microelectronics (Shanghai) Co., Ltd. All rights reserved.
 */
#include "asr_crane.h"
#include "qspi_common.h"
#include "qspi_flash.h"
#include "qspi_nor.h"
#include "spi_nor.h"

#ifdef SPINAND_SUPPORT
#include "spinand.h"
#endif

extern void crane_ds_mpu_xip_cache_enable(void);
extern void crane_ds_mpu_xip_cache_disable(void);

unsigned char spi_rd_buf[READ_PAGE_SIZE];
//unsigned char spi_wr_buf[READ_PAGE_SIZE];

unsigned char sector_buf[FLASH_SECTOR_SIZE];
#define NORFLASH_BLOCK_SIZE  (64*1024)
extern UINT32 FlashIsSPI;

extern struct spi_flash_chip *chip;
extern struct spi_flash_chip *chip2;

extern struct spinor_chip_info *spichip;

static OSSemaRef Asr3601spiSemaRef = NULL;
extern UINT32 EEHandlerFlag;
extern void watchdogKick(void);
extern void dcache_invalidate_range(unsigned int addr, unsigned int len);

void asr3601_SPI_MutexInit(void)
{
    OSA_STATUS status;

    if (Asr3601spiSemaRef == NULL)
    {
        status = OSASemaphoreCreate (&Asr3601spiSemaRef, 1, OSA_FIFO);
        ASSERT(status == OS_SUCCESS);
    }
}
void asr3601_SPI_MutexLock(void)
{
    OSA_STATUS status;
    if (EEHandlerFlag == 1)
		return;
    status = OSASemaphoreAcquire(Asr3601spiSemaRef, OS_SUSPEND);
    ASSERT(status == OS_SUCCESS);
}
void asr3601_SPI_MutexUnlock(void)
{
    OSA_STATUS status;
    if (EEHandlerFlag == 1)
		return;
    status = OSASemaphoreRelease(Asr3601spiSemaRef);
    ASSERT(status == OS_SUCCESS);

}

unsigned int asr3601spi_nor_do_erase(unsigned int addr, unsigned int size)
{

	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
    QSPI_MutexLock();
    crane_ds_mpu_xip_cache_disable();
	ret = spichip->ops->erase_block(spichip, addr, size);
    crane_ds_mpu_xip_cache_enable();
    QSPI_MutexUnlock();
	return 0;

}
int asr3601spi_nor_do_erase_4k(unsigned int addr, unsigned int size)
{

	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
    QSPI_MutexLock();
    crane_ds_mpu_xip_cache_disable();
	ret = spichip->ops->erase_sector(spichip, addr, size);
    crane_ds_mpu_xip_cache_enable();
    QSPI_MutexUnlock();
	return ret;

}
int asr3601spi_nor_do_erase_block(unsigned int addr, unsigned int size)
{

	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
    QSPI_MutexLock();
    crane_ds_mpu_xip_cache_disable();
	ret = spichip->ops->erase_block(spichip, addr, size);
    crane_ds_mpu_xip_cache_enable();
    QSPI_MutexUnlock();
	return ret;

}
unsigned int asr3601spi_nor_do_read_single(unsigned int addr, unsigned int buf_addr, unsigned int size)
{
	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
	//asr3601_SPI_MutexLock();
	ret = spichip->ops->read(spichip, addr, size, (uint32_t)buf_addr);
	//asr3601_SPI_MutexUnlock();
	return ret;
}

unsigned int asr3601spi_nor_do_read(unsigned int addr, unsigned int buf_addr, unsigned int size)
{
	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
	QSPI_MutexLock();
	ret = spichip->ops->read(spichip, addr, size, (uint32_t)buf_addr);
	QSPI_MutexUnlock();
	return ret;
}

unsigned int asr3601spi_nor_do_write(unsigned int addr, unsigned int buf_addr,unsigned int size)
{
	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
	QSPI_MutexLock();
	crane_ds_mpu_xip_cache_disable();
	ret = spichip->ops->write(spichip, addr, size, (uint32_t)buf_addr);
	crane_ds_mpu_xip_cache_enable();
	QSPI_MutexUnlock();
	return 0;

}

unsigned int asr3601_qspiflash_readid(uint8_t * buf)
{

	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
    QSPI_MutexLock();
#ifdef RUN_XIP_MODE
    watchdogKick();
#endif
	ret = chip->ops->read_id(chip, buf);
    QSPI_MutexUnlock();
	return ret;
}

int asr3601qspi_nor_do_erase(unsigned int addr, unsigned int size)
{

	int ret = 0;
	unsigned int lenth;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
    QSPI_MutexLock();
    //crane_ds_mpu_xip_cache_disable();
#ifdef RUN_XIP_MODE
    watchdogKick();
#endif
	if(addr>=chip->size){
		if((addr+size)>(chip->size+chip2->size)){
			ret=-1;
			goto exit_out;
		}
		ret = chip2->ops->erase(chip2, (addr-chip->size), size);
	}
	else if((addr+size)<=chip->size)
	{
		ret = chip->ops->erase(chip, addr, size);
	}
	else
	{
		lenth=(chip->size-addr);
		ret = chip->ops->erase(chip, addr, lenth);
		if (ret!=0)
			goto exit_out;
		size-=lenth;
		ret = chip2->ops->erase(chip2, 0, size);
	}
exit_out:	
   // crane_ds_mpu_xip_cache_enable();
    dcache_invalidate_range((addr + CRANE_QSPI_A1_BASE_ADD), size);
    QSPI_MutexUnlock();
	return 0;

}
int asr3601qspi_nor_do_erase_4k(unsigned int addr, unsigned int size)
{

	int ret = 0;
	unsigned int lenth;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
    QSPI_MutexLock();
    //crane_ds_mpu_xip_cache_disable();
#ifdef RUN_XIP_MODE
    watchdogKick();
#endif

	if(addr>=chip->size){
		if((addr+size)>(chip->size+chip2->size)){
			ret=-1;
			goto exit_out;
		}	
		ret = chip2->ops->erase_4k(chip2, (addr-chip->size), size);
	}
	else if((addr+size)<=chip->size)
	{
		ret = chip->ops->erase_4k(chip, addr, size);
	}
	else
	{
		lenth=(chip->size-addr);
		ret = chip->ops->erase_4k(chip, addr, lenth);
		if (ret!=0)
			goto exit_out;
		size-=lenth;
		ret = chip2->ops->erase_4k(chip2, 0, size);
	}
exit_out:	

    //crane_ds_mpu_xip_cache_enable();
	dcache_invalidate_range((addr + CRANE_QSPI_A1_BASE_ADD), size);	
    QSPI_MutexUnlock();
	return ret;

}

int asr3601qspi_nor_do_read(unsigned int addr, unsigned int buf_addr, unsigned int size)
{

	int ret = 0;
	unsigned int lenth;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
	QSPI_MutexLock();
	if(addr>=chip->size){
		if((addr+size)>(chip->size+chip2->size)){
			ret=-1;
			goto exit_out;
		}
		ret = chip2->ops->read(chip2, (addr-chip->size), size, (uint8_t *)buf_addr);
	}
	else if((addr+size)<=chip->size)
	{
		ret = chip->ops->read(chip, addr, size, (uint8_t *)buf_addr);
	}
	else
	{
		lenth=(chip->size-addr);
		ret = chip->ops->read(chip, addr, lenth, (uint8_t *)buf_addr);
		if (ret!=0)
			goto exit_out;
		size-=lenth;
		buf_addr+=lenth;
		ret = chip2->ops->read(chip2, 0, size, (uint8_t *)buf_addr);
	}
exit_out:	
	QSPI_MutexUnlock();
	return ret;
}

int asr3601qspi_nor_do_write(unsigned int addr, unsigned int buf_addr,unsigned int size)
{
	int ret = 0;
	unsigned int lenth;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
	QSPI_MutexLock();
	//crane_ds_mpu_xip_cache_disable();
#ifdef RUN_XIP_MODE
    watchdogKick();
#endif
	if(addr>=chip->size){
		if((addr+size)>(chip->size+chip2->size)){
			ret=-1;
			goto exit_out;
		}	
		ret = chip2->ops->write(chip2, (addr-chip->size), size, (uint8_t *)buf_addr);
	}
	else if((addr+size)<=chip->size)
	{
		ret = chip->ops->write(chip, addr, size, (uint8_t *)buf_addr);
	}
	else
	{
		lenth=(chip->size-addr);
		ret = chip->ops->write(chip, addr, lenth, (uint8_t *)buf_addr);
		if (ret!=0)
			goto exit_out;
		size-=lenth;
		buf_addr+=lenth;
		ret = chip2->ops->write(chip2, 0, size, (uint8_t *)buf_addr);
	}
exit_out:	
	//crane_ds_mpu_xip_cache_enable();
	dcache_invalidate_range((addr + CRANE_QSPI_A1_BASE_ADD), size);
	QSPI_MutexUnlock();
	return ret;

}

int asr_norflash_erase_4k(unsigned int addr, unsigned int size)
{
	int ret;
	//uart_printf("%s : 0x%x, 0x%x\r\n", __func__,addr, size);
	if(addr>=ASR3601S_SPI_VBASE_ADDR){
		addr-=ASR3601S_SPI_VBASE_ADDR;
		ret=asr3601spi_nor_do_erase_4k(addr,size);
		if(ret) return -1;
	}
	else if(addr>=CRANE_QSPI_BASE_ADD){
		addr-=CRANE_QSPI_BASE_ADD;
		ret = asr3601qspi_nor_do_erase_4k(addr, size);
		if(ret) return -1;	
	}
	return 0;
}

int asr_norflash_eraseblock(unsigned int addr, unsigned int size)
{
	int ret;
	//uart_printf("%s : 0x%x, 0x%x\r\n", __func__,addr, size);
	if(addr>=ASR3601S_SPI_VBASE_ADDR){
		addr-=ASR3601S_SPI_VBASE_ADDR;
		ret=asr3601spi_nor_do_erase_block(addr,size);
		if(ret) return -1;
	}
	else if(addr>=CRANE_QSPI_BASE_ADD){
		addr-=CRANE_QSPI_BASE_ADD;
		ret = asr3601qspi_nor_do_erase(addr, size);
		if(ret) return -1;	
	}
	return 0;
}
int asr_norflash_erase(unsigned int addr, unsigned int len)
{
	int ret;
	unsigned int erase_len;
	//uart_printf("%s : 0x%x, 0x%x\r\n", __func__,addr, len);
	/* check address align on block boundary */
	if (addr & FLASH_SECTOR_MASK) {
		return -1;
	}
	if (len & FLASH_SECTOR_MASK) {
		uart_printf("%s: Length not 4K-Sector aligned\n", __func__);
		return -1;
	}
	while (len > 0) {

		if (len >= NORFLASH_BLOCK_SIZE &&
		    !(addr & (NORFLASH_BLOCK_SIZE - 1))) {
			erase_len = NORFLASH_BLOCK_SIZE;
			ret=asr_norflash_eraseblock(addr,NORFLASH_BLOCK_SIZE);
		} else {
			erase_len = 4*1024;
			ret=asr_norflash_erase_4k(addr, erase_len);
		}

		if (ret != 0) {
			uart_printf("%s,block erase fail \r\n",__func__);
			return ret;
		}

		/* Increment page address and decrement length */
		len -= erase_len;
		addr += erase_len;

	}
	return 0;
}

int internalnorflash_read_single(unsigned int addr, unsigned int buf_addr, unsigned int size)
{
	int ret = 0;
	//Fota_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
	//asr3601_SPI_MutexLock();
	ret = spichip->ops->read(spichip, addr, size, (uint32_t)buf_addr);
	//asr3601_SPI_MutexUnlock();
	return ret;
}

int externalnorflash_read_single(unsigned int addr, unsigned int buf_addr, unsigned int size)
{
	int ret = 0;
	uart_printf("function:%s,offset:0x%x,len:0x%x\r\n",__func__,addr,size);
	ret = chip->ops->read(chip, addr, size, (uint8_t *)buf_addr);
	return ret;
}


int asr_norflash_read_single(unsigned int addr, unsigned int buf_addr, unsigned int size)
{

	int ret;
	uart_printf("%s : 0x%x, 0x%x\r\n", __func__,addr, size);
	if(addr>=ASR3601S_SPI_VBASE_ADDR){
		addr-=ASR3601S_SPI_VBASE_ADDR;
		ret=internalnorflash_read_single(addr,(unsigned int)buf_addr,size);
		if(ret) return -1;
	}
	else if(addr>=CRANE_QSPI_BASE_ADD){
		addr-=CRANE_QSPI_BASE_ADD;
		ret = externalnorflash_read_single(addr,(unsigned int)buf_addr,size);
		if(ret) return -1;	
	}
	return 0;
}
int asr_norflash_readpage(uint32_t addr, uint8_t* buf_addr, uint32_t size)
{
	int ret;
	//uart_printf("%s : 0x%x, 0x%x\r\n", __func__,addr, size);
	if(addr>=ASR3601S_SPI_VBASE_ADDR){
		addr-=ASR3601S_SPI_VBASE_ADDR;
		ret=asr3601spi_nor_do_read(addr,(unsigned int)buf_addr,size);
		if(ret) return -1;
	}
	else if(addr>=CRANE_QSPI_BASE_ADD){
		addr-=CRANE_QSPI_BASE_ADD;
		ret = asr3601qspi_nor_do_read(addr,(unsigned int)buf_addr,size);
		if(ret) return -1;	
	}
	return 0;
}

int asr_norflash_writepage(uint32_t addr,uint8_t* buf_addr, uint32_t size)
{
	int ret;
	//uart_printf("%s : 0x%x, 0x%x\r\n", __func__,addr, size);
	if(addr>=ASR3601S_SPI_VBASE_ADDR){
		addr-=ASR3601S_SPI_VBASE_ADDR;
		ret=asr3601spi_nor_do_write(addr,(unsigned int)buf_addr,size);
		if(ret) return -1;	
	}
	else if(addr>=CRANE_QSPI_BASE_ADD){
		addr-=CRANE_QSPI_BASE_ADD;
		ret = asr3601qspi_nor_do_write(addr,(unsigned int)buf_addr, size);
		if(ret) return -1;	
	}
	return 0;
}

int asr_norflash_read(uint32_t addr, uint8_t* buf_addr, uint32_t size)
{
    	int ret;	
		int block_addr;
		int block_offset;
		int block_size;
		uint8_t *buf;	
		//uart_printf("%s : addr=0x%x,size=0x%x\r\n",__func__, addr,size);
		if((chip == NULL) || (buf_addr == NULL) ||
			(addr < CRANE_QSPI_BASE_ADD))		
			return -1;

		block_addr	 = addr &(~READ_PAGE_MASK);
		block_offset = addr &( READ_PAGE_MASK);
		block_size	 = size;
		while(block_size)
		{	
			ret = asr_norflash_readpage(block_addr, (uint8_t *)spi_rd_buf, READ_PAGE_SIZE);
			if(ret) return -1;	
			if((block_offset + block_size) < READ_PAGE_SIZE)
			{	
				memcpy( (uint8_t *)buf_addr,(uint8_t *)spi_rd_buf + block_offset, block_size);
				return 0;
			}
			else
			{
				memcpy((uint8_t *)buf_addr,(uint8_t *)spi_rd_buf + block_offset, READ_PAGE_SIZE - block_offset);
		
				block_addr += READ_PAGE_SIZE;
				buf_addr += (READ_PAGE_SIZE - block_offset);		
				block_size -= (READ_PAGE_SIZE - block_offset);
				block_offset = 0;
			}
		}

		return 0;

}


int asr_norflash_write(uint32_t start, uint8_t* src, uint32_t size)
{
    int ret;
	int block_addr;
	int block_offset;
	int block_size;
	int write_size;

	//uart_printf("%s : 0x%x, 0x%x\r\n", __func__,start, size);

	if((chip == NULL) || (src == NULL) ||
		(start < CRANE_QSPI_BASE_ADD))		
		return -1;

	if(size == 0) 
		return 0;

	block_addr	 = start&(~FLASH_SECTOR_MASK);
	block_offset = start&( FLASH_SECTOR_MASK);
	block_size   = size;

	while(block_size)
	{	
		if((block_offset + block_size) < FLASH_SECTOR_SIZE)
		{	
			ret = asr_norflash_read(block_addr,(uint8_t *)sector_buf, FLASH_SECTOR_SIZE);
			if(ret) return -1;
			ret = asr_norflash_erase_4k(block_addr, FLASH_SECTOR_SIZE);
			if(ret) return -1;	
			memcpy((uint8_t *)sector_buf + block_offset, src, block_size);
			ret = asr_norflash_writepage(block_addr,(uint8_t *)sector_buf, FLASH_SECTOR_SIZE);
			if(ret) 
				return -1;
			else
				return 0;
		}
		else
		{
			if(block_offset!=0){
				ret = asr_norflash_read(block_addr,(uint8_t *)sector_buf, block_offset);
				if(ret) return -1;
				ret = asr_norflash_erase_4k(block_addr, FLASH_SECTOR_SIZE);
				if(ret) return -1;	
			}		
			memcpy((uint8_t *)sector_buf + block_offset, src, FLASH_SECTOR_SIZE - block_offset);
			ret = asr_norflash_writepage( block_addr,(uint8_t *)sector_buf, FLASH_SECTOR_SIZE);
			if(ret) 
				return -1;

			block_addr += FLASH_SECTOR_SIZE;
			src += (FLASH_SECTOR_SIZE - block_offset);		
			block_size -= (FLASH_SECTOR_SIZE - block_offset);
			block_offset = 0;
		}

	}

	return 0;
}

int SpiExternalFlashErase_nor(unsigned int addr, unsigned int len)
{
	int ret;
	unsigned int erase_len;
	uart_printf("%s : 0x%x, 0x%x\r\n", __func__,addr, len);
	/* check address align on block boundary */
	if (addr & FLASH_SECTOR_MASK) {
		return -1;
	}
	if (len & FLASH_SECTOR_MASK) {
		uart_printf("%s: Length not 4K-Sector aligned\n", __func__);
		return -1;
	}
	while (len > 0) {

		if (len >= NORFLASH_BLOCK_SIZE &&
		    !(addr & (NORFLASH_BLOCK_SIZE - 1))) {
			erase_len = NORFLASH_BLOCK_SIZE;
			ret=spi_nor_do_erase(addr,NORFLASH_BLOCK_SIZE,SPI_FLASH_EXTERNAL);
		} else {
			erase_len = 4*1024;
			ret=spi_nor_do_erase_4k(addr, erase_len,SPI_FLASH_EXTERNAL);
		}

		if (ret != 0) {
			uart_printf("%s,block erase fail \r\n",__func__);
			return ret;
		}

		/* Increment page address and decrement length */
		len -= erase_len;
		addr += erase_len;

	}
	return 0;
}

int SpiExternalFlashErase(unsigned int addr, unsigned int len)
{
	int ret;

#ifdef SPINAND_SUPPORT
	ret = nand_flash_erase_cache(addr, len);
#else
	ret = SpiExternalFlashErase_nor(addr, len);
#endif

	return ret;
}

#define EXTFLASH_READ_SIZE (256) //4096
#define EXTFLASH_READ_MASK (EXTFLASH_READ_SIZE-0x1) //4096 - 1

int SpiExternalFlashRead_nor(uint32_t addr, uint8_t* buf_addr, uint32_t size)
{
    	int ret;	
		int block_addr;
		int block_offset;
		int block_size;
		uint8_t *buf;	
		//uart_printf("%s : addr=0x%x,size=0x%x\r\n",__func__, addr,size);

		block_addr	 = addr &(~EXTFLASH_READ_MASK);
		block_offset = addr &( EXTFLASH_READ_MASK);
		block_size	 = size;
		while(block_size)
		{	
			ret = spi_nor_do_read(block_addr, (unsigned int)spi_rd_buf, EXTFLASH_READ_SIZE,SPI_FLASH_EXTERNAL);
			if(ret) return -1;	
			if((block_offset + block_size) < EXTFLASH_READ_SIZE)
			{	
				memcpy( (uint8_t *)buf_addr,(uint8_t *)spi_rd_buf + block_offset, block_size);
				return 0;
			}
			else
			{
				memcpy((uint8_t *)buf_addr,(uint8_t *)spi_rd_buf + block_offset, EXTFLASH_READ_SIZE - block_offset);
		
				block_addr += EXTFLASH_READ_SIZE;
				buf_addr += (EXTFLASH_READ_SIZE - block_offset);		
				block_size -= (EXTFLASH_READ_SIZE - block_offset);
				block_offset = 0;
			}
		}

		return 0;

}

int SpiExternalFlashRead(uint32_t addr, uint8_t* buf_addr, uint32_t size)
{
	int ret;
	
#ifdef SPINAND_SUPPORT
	ret = nand_flash_read_cache(addr, buf_addr, size);
#else
	ret = SpiExternalFlashRead_nor(addr, buf_addr, size);
#endif

	return ret;
}

int SpiExternalFlashWrite_nor(uint32_t start, uint8_t* src, uint32_t size)
{
    int ret;
	int block_addr;
	int block_offset;
	int block_size;
	int write_size;

	uart_printf("%s : 0x%x, 0x%x\r\n", __func__,start, size);

	if(size == 0) 
		return 0;

	block_addr	 = start&(~FLASH_SECTOR_MASK);
	block_offset = start&( FLASH_SECTOR_MASK);
	block_size   = size;

	while(block_size)
	{	
		if((block_offset + block_size) < FLASH_SECTOR_SIZE)
		{	
			ret = spi_nor_do_read(block_addr,(unsigned int)sector_buf, FLASH_SECTOR_SIZE,SPI_FLASH_EXTERNAL);
			if(ret) return -1;
			ret = spi_nor_do_erase_4k(block_addr, FLASH_SECTOR_SIZE,SPI_FLASH_EXTERNAL);
			if(ret) return -1;	
			memcpy((uint8_t *)sector_buf + block_offset, src, block_size);
			ret = spi_nor_do_write(block_addr,(unsigned int)sector_buf, FLASH_SECTOR_SIZE,SPI_FLASH_EXTERNAL);
			if(ret) 
				return -1;
			else
				return 0;
		}
		else
		{
			if(block_offset!=0){
				ret = spi_nor_do_read(block_addr,(unsigned int)sector_buf, block_offset,SPI_FLASH_EXTERNAL);
				if(ret) return -1;
				ret = spi_nor_do_erase_4k(block_addr, FLASH_SECTOR_SIZE,SPI_FLASH_EXTERNAL);
				if(ret) return -1;	
			}		
			memcpy((uint8_t *)sector_buf + block_offset, src, FLASH_SECTOR_SIZE - block_offset);
			ret = spi_nor_do_write( block_addr,(unsigned int)sector_buf, FLASH_SECTOR_SIZE,SPI_FLASH_EXTERNAL);
			if(ret) 
				return -1;

			block_addr += FLASH_SECTOR_SIZE;
			src += (FLASH_SECTOR_SIZE - block_offset);		
			block_size -= (FLASH_SECTOR_SIZE - block_offset);
			block_offset = 0;
		}

	}

	return 0;
}
int SpiExternalFlashWrite(uint32_t start, uint8_t* src, uint32_t size)
{
	int ret;

#ifdef SPINAND_SUPPORT
	ret = nand_flash_write_cache(start, src, size);
#else
	ret = SpiExternalFlashWrite_nor(start, src, size);
#endif

	return ret;
}

INT32 Asr3601s_fotaFlash_Erase(UINT32 vaddr,  UINT32 len)
{
	//Fota_printf("%s,offset:0x%x,len:0x%x\r\n",__func__,vaddr,len);
	asr_norflash_erase(vaddr,len);

	return 0;
}


INT32 Asr3601s_fotaFlash_Read(UINT32 vaddr,  UINT32 len,UINT8 *buf_addr)
{
	UINT32 ret;
	//Fota_printf("%s,offset:0x%x,len:0x%x\r\n",__func__,vaddr,len);
	if((spichip == NULL) || (buf_addr == NULL))
		return -1;
	ret=asr_norflash_read(vaddr,buf_addr,len);
	if(ret) 
		return -1;	
	return 0;
}


INT32 Asr3601s_fotaFlash_Write(UINT32 vaddr,  UINT32 len,UINT8 *buf)
{
	UINT32 ret;
	//Fota_printf("%s,offset:0x%x,len:0x%x\r\n",__func__,vaddr,len);
	ret=asr_norflash_write(vaddr,buf,len);
	if(ret) 
		return -1;	
	return 0;
}

#ifndef SPINAND_SUPPORT
int nand_dev_init(void)
{
	return 0;
}
int nand_dev_deinit(void)
{
	return 0;
}
void nand_cache_flush(void)
{
}

#endif


/*Note: For fota_pkg in extra spinand flash, customer needs to call APIs as below:*/
void nand_fota_test(void)
{
	/*before fota process, customer needs to call this API*/	
	nand_dev_init();

	/*fota process*/	
	
	/*before fota reset, customer needs to call the APIs*/	
	nand_cache_flush();
	nand_dev_deinit();
}

