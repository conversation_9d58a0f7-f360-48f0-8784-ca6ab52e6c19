#------------------------------------------------------------
# (C) Copyright [2006-2008] ASR International Ltd.
# All Rights Reserved
#------------------------------------------------------------

# ============================================================================
# File        : CRANE_DS_SDK.mak
# Description : include(!) makefile
#               to be used only by SDK build by CUST
## ============================================================================

# for CRANE SDK 
TARGET_DFLAGS += -D__DATE__="\"NO_DATE\""
TARGET_DFLAGS += -D__TIME__="\"NO_TIME\""
ifneq (,$(findstring CRANE_CUST,${BLD_OPT}))

##------------------------ NO IMS --------------------------
##
ifneq (,$(findstring NOIMS,${VARIANT_LIST}))
CBA_USELIBS_EXCEPTION =   \
                volte\volte_components \
                ims\ims_components
endif

##------------------------ NO TPAGPS --------------------------
##
ifeq (,$(findstring AGPSTP_SUPPORT,${VARIANT_LIST}))
CBA_USELIBS_EXCEPTION +=   \
                agpstp\agpstp_components
endif

##---------------------- NO Dialer ---------------------------
##
ifneq (,$(findstring NODIALER,${VARIANT_LIST}))
CBA_USELIBS_EXCEPTION +=   \
                pcac\dial
endif

##---------------------- NO Sulog ---------------------------
##
ifneq (,$(findstring NOSULG,${VARIANT_LIST}))
CBA_USELIBS_EXCEPTION +=   \
                nota\sulog
endif

##---------------------- NO SD Card ---------------------------
##
ifneq (,$(findstring SD_NOT_SUPPORT,${VARIANT_LIST}))
CBA_USELIBS_EXCEPTION +=   \
                softutil\fatfs
endif

##---------------------- NO LFS ---------------------------
##
ifneq (,$(findstring NOLFS,${VARIANT_LIST}))
CBA_USELIBS_EXCEPTION +=   \
                softutil\littlefs
endif



CBA_LIBS_DIR = ${CRANE_LIB_DIR}
LOCAL_LIBS += ${CBA_LIBS_DIR}/armps.lib
TARGET_DFLAGS += -DCRANE_CUST_BUILD

CBA_USE_COMMON_LIB_DIR = 1
$(info CBA_USELIBS_EXCEPTION = ${CBA_USELIBS_EXCEPTION})
CBA_USELIBS := $(filter-out ${CBA_USELIBS_EXCEPTION},${CRANE_SDK_USELIBS})
PACKAGE_LIST := $(filter-out ${CRANE_SDK_USELIBS},${PACKAGE_LIST})

#TODO : below manual config INC_PATH need to be fixed
GLOBAL_INC_PATHS += \
		    ${XROOT}\hop\telephony\atcmdsrv\inc \
		    ${XROOT}\hop\telephony\atparser\inc \
		    ${XROOT}\pcac\lwipv4v6\src\include\lwip \
		    ${XROOT}\softutil\fatsys\fs\hdr \
		    ${XROOT}\hal\MMU\inc

SDK_BLD_INC_CACHE_PATHS=${HSIUPDLIBDEV}\build\lib\hsiupdlibdev_BldIncCache

endif
