/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "RRLP-Components"
 * 	found in "rrlp12_1_0.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_GANSSAdditionalAssistanceChoicesForOneGANSS_H_
#define	_GANSSAdditionalAssistanceChoicesForOneGANSS_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include "GANSSModelID.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* GANSSAdditionalAssistanceChoicesForOneGANSS */
typedef struct GANSSAdditionalAssistanceChoicesForOneGANSS {
	long	*ganssID	/* OPTIONAL */;
	GANSSModelID_t	*ganssClockModelChoice	/* OPTIONAL */;
	GANSSModelID_t	*gannsOrbitModelChoice	/* OPTIONAL */;
	GANSSModelID_t	*ganssAlmanacModelChoice	/* OPTIONAL */;
	GANSSModelID_t	*ganssAdditionalUTCModelChoice	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GANSSAdditionalAssistanceChoicesForOneGANSS_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GANSSAdditionalAssistanceChoicesForOneGANSS;

#ifdef __cplusplus
}
#endif

#endif	/* _GANSSAdditionalAssistanceChoicesForOneGANSS_H_ */
#include <asn_internal.h>
