/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utvjcomp.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description:
 *
 * definition of Van Jacobson (RFC1144) TCP/IP header compression functions
 * and data structures - this file is based on sample code given in RFC1144.
 **************************************************************************/

#ifndef UTVJCOMP_H
#define UTVJCOMP_H

#if !defined(SYSTEM_H)
#  include <system.h>
#endif

#if !defined (PPMSGTYP_H)
//#  include <ppmsgtyp.h>
#endif

#if !defined (NPDU_TYP_H)
#  include <npdu_typ.h>
#endif

#if !defined (UTENDIAN_H)
#  include <utendian.h>
#endif


#if !defined (UTIP_H)
#  include <utip.h>
#endif

#if !defined (UTHDCOMP_H)
#  include <uthdcomp.h>
#endif

/*Cat0 20160615*/
typedef enum PppProtocolTag
{
    PPP_PROTO_PADDING = 0x0001,
    PPP_PROTO_LCP = 0xC021,
    PPP_PROTO_PAP = 0xC023,
    PPP_PROTO_LQR = 0xC025,
    PPP_PROTO_CHAP = 0xC223,
    PPP_PROTO_IPCP = 0x8021,
    PPP_PROTO_IP = 0x0021,
    PPP_PROTO_COMP_TCPIP = 0x002D,   /* used in VJ compression negotiation */
    PPP_PROTO_UNCOMP_TCPIP = 0x002F,
    PPP_PROTO_LAST_NETWORK_PROTOCOL = 0x3fff, /* just for range testing */
    PPP_PROTO_UNRECOGNISED = 0xffff /* an invalid id, for marking duff messages */
}
PppProtocol;
#define IP_OFFS_PROTO           9    /* 1 shows TCP, UDP, etc or slot id */
#define IP_OFFS_CHECKSUM        10   /* 2                                */


/**** FUNCTION DECLARATIONS ***********************************************/
extern PppProtocol
VjCompressIpPacket (Npdu *npdu, VjConnState *comp);

extern Boolean
VjDecompressIpPacket (Npdu *npdu, VjConnState *comp, PppProtocol proto);

extern void
VjReset (VjConnState *comp, Rfc1144Params *negParams);

#endif

/* END OF FILE */
