/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "RRLP-Components"
 * 	found in "rrlp12_1_0.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_NavModel_GLONASSecef_H_
#define	_NavModel_GLONASSecef_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeInteger.h>
#include <BIT_STRING.h>
#include <BOOLEAN.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* NavModel-GLONASSecef */
typedef struct NavModel_GLONASSecef {
	long	 gloEn;
	BIT_STRING_t	 gloP1;
	BOOLEAN_t	 gloP2;
	long	 gloM;
	long	 gloX;
	long	 gloXdot;
	long	 gloXdotdot;
	long	 gloY;
	long	 gloYdot;
	long	 gloYdotdot;
	long	 gloZ;
	long	 gloZdot;
	long	 gloZdotdot;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} NavModel_GLONASSecef_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_NavModel_GLONASSecef;

#ifdef __cplusplus
}
#endif

#endif	/* _NavModel_GLONASSecef_H_ */
#include <asn_internal.h>
