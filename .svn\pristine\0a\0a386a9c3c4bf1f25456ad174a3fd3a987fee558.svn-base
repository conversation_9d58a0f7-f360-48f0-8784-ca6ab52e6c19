#include "common.h"

typedef struct 
{
	UINT32 reg;
	UINT32 padding[2];
}GPIO_Single_Register;

typedef volatile struct
{
	volatile GPIO_Single_Register PLR;//0x0
	volatile GPIO_Single_Register PDR;//0xc
	volatile GPIO_Single_Register PSR;//0X18
	volatile GPIO_Single_Register PCR;//0X24
	volatile GPIO_Single_Register RER;//0X30
	volatile GPIO_Single_Register FER;//0X3C
	volatile GPIO_Single_Register EDR;//0X48
	volatile GPIO_Single_Register SDR;//0X54
	volatile GPIO_Single_Register CDR;//0X60
	volatile GPIO_Single_Register SRER;//0X6C
	volatile GPIO_Single_Register CRER;//0X78
	volatile GPIO_Single_Register SFER;//0X84
	volatile GPIO_Single_Register CFER;//0X90
	volatile GPIO_Single_Register AP_MASK;//0X9C
	volatile GPIO_Single_Register CP_MASK;//0XA8
}GPIORegisters;

typedef enum
{
	VSPI_GPIO_0=0,
	VSPI_GPIO_32=32,
	VSPI_GPIO_33=33,
	VSPI_GPIO_34,
	VSPI_GPIO_35,
	VSPI_GPIO_36,
	VSPI_GPIO_64=64,
	VSPI_GPIO_96=96,
	VSPI_GPIO_121=121,
	VSPI_GPIO_MAX=127,
	VSPI_GPIO_NULL=128
}VSpiGpio;
#define GPIO_REGISTER_BASE				0xD4019000
#define GPIO_REGISTER_GROUPS				4

#define GPIO_GROUP_BASE1					0
#define GPIO_GROUP_BASE2					4
#define GPIO_GROUP_BASE3					8
#define GPIO_GROUP_BASE4					0x100

static UINT32 GPIORegisterBase[GPIO_REGISTER_GROUPS] = {	GPIO_GROUP_BASE1,
															GPIO_GROUP_BASE2,
															GPIO_GROUP_BASE3,
															GPIO_GROUP_BASE4};
#define GPIO_SHIFT(gpio) (1 << (gpio%32))


static UINT32 *GetBaseAddr(UINT32 portHandle)
{
	UINT32 base_addr;
	
	if(portHandle < VSPI_GPIO_32)
		base_addr =  GPIO_REGISTER_BASE + GPIORegisterBase[0];
	else if(portHandle < VSPI_GPIO_64)
		base_addr =  GPIO_REGISTER_BASE + GPIORegisterBase[1];
	else if(portHandle < VSPI_GPIO_96)
		base_addr =  GPIO_REGISTER_BASE + GPIORegisterBase[2];
	else if(portHandle <= VSPI_GPIO_MAX)
		base_addr =  GPIO_REGISTER_BASE + GPIORegisterBase[3];
	else
		base_addr =  0;

	return (UINT32 *)base_addr;
}

void cgpio_set_output(unsigned int port)
{
	GPIORegisters *GPIOReg;
	
	GPIOReg = (GPIORegisters *)GetBaseAddr(port);
	GPIOReg->SDR.reg =	GPIO_SHIFT(port);
}

void cgpio_set_value(unsigned int port, int value)
{
	GPIORegisters *GPIOReg;
	
	GPIOReg = (GPIORegisters *)GetBaseAddr(port);
	if(value == 0) {
		GPIOReg->PCR.reg = GPIO_SHIFT(port);
	}
	else {
		GPIOReg->PSR.reg = GPIO_SHIFT(port);
	}
}




