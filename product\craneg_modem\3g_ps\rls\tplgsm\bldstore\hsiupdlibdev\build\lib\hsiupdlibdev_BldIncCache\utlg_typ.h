/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/utlg_typ.h#8 $
 *   $Revision: #8 $
 *   $DateTime: 2006/04/20 12:15:54 $
 **************************************************************************
 * File Description:
 *
 * Definition of UTLG data types.
 **************************************************************************/

#ifndef UTLG_TYP_H
#define UTLG_TYP_H

/**** NESTED INCLUDE FILES *************************************************/

#include <system.h>
#include <gkisig.h>
#include <gkitimer.h>
#include <uterr.h>

/**** CONSTANTS ************************************************************/

/**** TYPEDEFS *************************************************************/

typedef struct UtErrorTag
{
    FrameTicks      relativeTime;
    UtErrorType     id;
    Int16           errorSize;
    Int8            errorData[MAX_UT_LOG_ERROR_SIZE];
}
UtError;

#endif

/* END OF FILE */
