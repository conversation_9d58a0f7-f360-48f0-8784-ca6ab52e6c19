#=========================================================================
# File Name      : FDI_7_1.mak
# Description    : Main make file for the softutil/FDI package.
#
# Usage          : make [-s] -f FDI_6.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
# Copyright (c) 2001 Intel Corporation. All Rights Reserved
#=========================================================================

# Package build options
include $(OPT_FILE)
# No PrePass needed
 PP =

# Package Makefile information
GEN_PACK_MAKEFILE = $(BUILD_ROOT)/env/$(HOST)/build/package.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = fatsys
PACKAGE_BASE     = softutil
PACKAGE_DEP_FILE = fatsys_dep.mak
PACKAGE_PATH     = $(BUILD_ROOT)/$(PACKAGE_BASE)/$(PACKAGE_NAME)

# The path locations of source and include file directories.
PACKAGE_SRC_PATH    = $(PACKAGE_PATH)
PACKAGE_INC_PATHS   = $(PACKAGE_PATH)//flash    \
                      $(PACKAGE_PATH)//flash/nand   \
                      $(PACKAGE_PATH)//flash/oneNand   \
                      $(PACKAGE_PATH)//flash/eMMC   \
                      $(PACKAGE_PATH)//flash/spinor   \
                      $(PACKAGE_PATH)/hop/dma/inc \
                      $(PACKAGE_PATH)/fs/hdr \
                      $(PACKAGE_PATH)/fs/src \
                      ${BUILD_ROOT}/hop/utilities/inc


# Package source files, paths not required
ifneq (,$(findstring CIRAM,${VARIANT_LIST}))
PACKAGE_SRC_FILES =  \
				flash/qspi_core.c \
				flash/qspi_dma.c \
				flash/qspi_host.c \
				flash/qspi_nor.c  \
				flash/qspi_nor_bp.c  \
				fs/src/timeM.c

# FatSys is Suppprt Here.
ifneq (,$(findstring SPINAND_SUPPORT,${VARIANT_LIST}))
PACKAGE_SRC_FILES += \
				flash/Flash_wk.c \
				flash/FlashManager.c	\
				flash/FM.c	\
				flash/nand_hal.c \
				fs/src/add.c    \
				fs/src/fat.c    \
				fs/src/fatdir.c    \
				fs/src/fatio.c    \
				fs/src/sdsys.c   \
				fs/src/dirent.c    \
				fs/src/fatcard.c    \
				fs/src/fatfile.c     \
				fs/src/find.c    \
				fs/src/sector.c
endif

ifneq (,$(findstring SPIMUX_SUPPORT,${VARIANT_LIST}))
PACKAGE_SRC_FILES += 	\
				flash/spinor/spi_nor.c \
				flash/spinor/ssp_host.c
endif

ifneq (,$(findstring FOTA_GS_SUPPORT,${VARIANT_LIST}))
PACKAGE_SRC_FILES += 	\
				fs/src/add.c    \
				fs/src/fat.c    \
				fs/src/fatdir.c    \
				fs/src/fatio.c    \
				fs/src/sdsys.c   \
				fs/src/dirent.c    \
				fs/src/fatcard.c    \
				fs/src/fatfile.c     \
				fs/src/find.c    \
				fs/src/sector.c    \
				fs/src/fat_os_api.c    \
				flash/nand/xllp_dfc_1.c    \
				flash/nand/xllp_dfc_support.c    \
				flash/nand/nand.c   \
				flash/oneNand/oneNAND.c   \
				flash/PlatformConfig.c    \
				flash/dataflash.c    \
				flash/nand_hal.c   \
				flash/nand_bbm.c   \
				flash/nand_group.c \
				flash/spinor/spi_nor.c \
				flash/spinor/ssp_host.c
endif

ifneq (,$(findstring FOTA_RS_SUPPORT,${VARIANT_LIST}))
PACKAGE_SRC_FILES += 	\
				fs/src/add.c    \
				fs/src/fat.c    \
				fs/src/fatdir.c    \
				fs/src/fatio.c    \
				fs/src/sdsys.c   \
				fs/src/dirent.c    \
				fs/src/fatcard.c    \
				fs/src/fatfile.c     \
				fs/src/find.c    \
				fs/src/sector.c    \
				fs/src/fat_os_api.c    \
				flash/nand/xllp_dfc_1.c    \
				flash/nand/xllp_dfc_support.c    \
				flash/nand/nand.c   \
				flash/oneNand/oneNAND.c   \
				flash/PlatformConfig.c    \
				flash/dataflash.c    \
				flash/nand_hal.c   \
				flash/nand_bbm.c   \
				flash/nand_group.c \
				flash/spinor/spi_nor.c \
				flash/spinor/ssp_host.c
endif

else
PACKAGE_SRC_FILES =  \
				fs/src/add.c    \
				fs/src/fat.c    \
				fs/src/fatdir.c    \
				fs/src/fatio.c    \
				fs/src/sdsys.c   \
				fs/src/timeM.c    \
				fs/src/dirent.c    \
				fs/src/fatcard.c    \
				fs/src/fatfile.c     \
				fs/src/find.c    \
				fs/src/sector.c    \
				fs/src/fat_os_api.c    \
				flash/nand/xllp_dfc_1.c    \
				flash/nand/xllp_dfc_support.c    \
				flash/nand/nand.c   \
				flash/oneNand/oneNAND.c   \
				flash/PlatformConfig.c    \
				flash/dataflash.c    \
				flash/nand_hal.c   \
				flash/nand_bbm.c   \
				flash/nand_group.c \
				flash/qspi_core.c \
				flash/qspi_dma.c \
				flash/qspi_host.c \
				flash/qspi_nor.c \
				flash/spinor/spi_nor.c \
				flash/spinor/ssp_host.c
#				flash/eMMC/sdhc_controller.c    \
#				flash/eMMC/sdhc.c   \
#				flash/eMMC/sdmmc_api.c   \
#				fattest.c
endif

# These are the tool flags specific to the FDI package only.
# The environment, target, and group also set flags.
PACKAGE_ASMFLAGS =
PACKAGE_CFLAGS   =
PACKAGE_CPPFLAGS =
PACKAGE_DFLAGS   =	-DNO_SDMMC	-DNO_FAT12
PACKAGE_ARFLAGS  =

# The package dependency file
PACKAGE_DEP_FILE = fatsys_dep.mak

# Define Package Variants -------------------------------

# look for the variants in the VARIANT_LIST and override
# setting from the previous section. The variable
# FDI_VARIANT_1 is meant to be overwritten
# with actual variant names. More variants can be added
# as required.



# Include the Standard Package Make File ---------------
include $(GEN_PACK_MAKEFILE)

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include $(PACKAGE_DEP_FILE)









