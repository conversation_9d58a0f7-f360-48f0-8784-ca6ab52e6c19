/*--------------------------------------------------------------------------------------------------------------------
(C) Copyright 2021 ASR Ltd. All Rights Reserved
-------------------------------------------------------------------------------------------------------------------*/

#include <stdint.h>
#include <string.h>
#include <stdlib.h>

#include "diags.h"
#include "gbl_types.h"
#include "acm_comm.h"
#include "mp3enc_api.h"
#include "acm_audio_record.h"
#include "acm_audio_effect.h"
#include "audio_def.h"
#include "acm_audio_def.h"
#include "audio_file.h"
#include "layer3.h"

#ifndef MP3_ENC_USER_MAXIMUM
#define MP3_ENC_USER_MAXIMUM        (1)
#endif

#ifndef MP3_FILE_NAME_MAXIMUM
#define MP3_FILE_NAME_MAXIMUM       (100)
#endif

#ifndef MP3_ENC_PCM_SOURCE_8K
#define MP3_ENC_PCM_SOURCE_8K       (1)
#endif

#ifndef MP3_ENC_DEFAULT_BITR
#if MP3_ENC_PCM_SOURCE_8K == 1
#define MP3_ENC_DEFAULT_BITR        (8)
#else
#define MP3_ENC_DEFAULT_BITR        (16)
#endif
#endif

#define MP3_ENC_CONTINUE			(0x1)
#define MP3_ENC_STOP				(0x2)
#define MP3_ENC_START				(0x4)
#define MP3_ENC_TASK_MASK			(MP3_ENC_START | MP3_ENC_STOP | MP3_ENC_CONTINUE)

typedef struct MP3_ENC_USER_INFO {
    shine_t handle;
    int samples_per_pass;
    int written;
    int readed_short;
    int16_t* buffer;
    acm_audio_record_handle handle_pcm;
    mp3EncConfigInfo config;
    int close_flag;
    char file_name[MP3_FILE_NAME_MAXIMUM];
    AUDIO_FILE_ID fd;
    union {
        mp3EncodeHandle unique_id;
        char* magic;
    };
}MP3_ENC_USER_INFO;

static void* mp3enc_stack_ptr = 0;
static OSATaskRef mp3enc_task_ref = 0;
static OSAFlagRef mp3enc_flag_ref = 0;
static OSASemaRef mp3enc_sema_ref = 0;
static MP3_ENC_USER_INFO mp3_enc_info[MP3_ENC_USER_MAXIMUM] = { 0 };

/* Use these default settings, can be overridden */
static void set_defaults(shine_config_t *config)
{
    shine_set_config_mpeg_defaults(&config->mpeg);
}

static void mp3_record_event_callback(acm_audio_record_handle handle, acm_audio_record_event_t event) {
    if (handle > 0 && event == AUDIO_RECORD_EVENT_TICK) {
        int i = 0;
        for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
            if (handle == mp3_enc_info[i].handle_pcm) {
                OSAFlagSet(mp3enc_flag_ref, MP3_ENC_CONTINUE, OSA_FLAG_OR);
                break;
            }
        }
    }
}

static void mp3_enc_start(void) {
    int i = 0;
    for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
        if (mp3_enc_info[i].handle == 0) {
            shine_config_t config;
            memset(&config, 0, sizeof(shine_config_t));
            set_defaults(&config);
#if MP3_ENC_PCM_SOURCE_8K == 1
            config.wave.samplerate = 8000;
#else
            config.wave.samplerate = 16000;
#endif
            config.wave.channels = PCM_MONO;
            config.mpeg.mode = MONO;
            if (mp3_enc_info[i].config.bitr > 0) {
                config.mpeg.bitr = mp3_enc_info[i].config.bitr;
            }
            else {
                config.mpeg.bitr = MP3_ENC_DEFAULT_BITR;
            }
            mp3_enc_info[i].handle = shine_initialise(&config);
            if (mp3_enc_info[i].handle) {
                acm_audio_record_config_t record_config;
                memset(&record_config, 0, sizeof(record_config));
                record_config.mode = (acm_audio_record_mode_t)mp3_enc_info[i].config.mode;
#if MP3_ENC_PCM_SOURCE_8K == 1
                record_config.rate = 8000;
#else
                record_config.rate = 16000;
#endif
                record_config.event_cb = mp3_record_event_callback;
                record_config.gain_value = (int32_t)(AUDIO_GAIN_QFORMAT * mp3_enc_info[i].config.gain);
                if (acm_audio_record_open(&record_config, &mp3_enc_info[i].handle_pcm) != 0) {
                    shine_close(mp3_enc_info[i].handle);
                    memset(&mp3_enc_info[i], 0, sizeof(MP3_ENC_USER_INFO));
                }
                else {
                    int samples_per_pass = shine_samples_per_pass(mp3_enc_info[i].handle);
                    mp3_enc_info[i].buffer = (short*)malloc(2 * SHINE_MAX_SAMPLES * sizeof(short));
                    mp3_enc_info[i].samples_per_pass = samples_per_pass;
                    if (strlen(mp3_enc_info[i].file_name) > 0)
                        mp3_enc_info[i].fd = common_fopen(mp3_enc_info[i].file_name, "wb");
                }
            }
        }
    }
}

static int mp3_enc_continue(void) {
    int i = 0;
    for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
        while (1) {
#if MP3_ENC_PCM_SOURCE_8K == 1
            int16_t pcm_data[160] = { 0 };
#else
            int16_t pcm_data[320] = { 0 };
#endif
            int total_short = sizeof(pcm_data) / sizeof(short);
            uint32_t sz = sizeof(pcm_data);
            int error_code = acm_audio_record_read(mp3_enc_info[i].handle_pcm, pcm_data, &sz, 0);
            if (mp3_enc_info[i].close_flag == 0 && error_code == 0) {
                int available_short = total_short;
                int needed_short = mp3_enc_info[i].samples_per_pass - mp3_enc_info[i].readed_short;
                DIAG_FILTER(AUDIO, MP3_ENC, mp3enc_source, DIAG_INFORMATION)
                diagStructPrintf("mp3enc_source_dump", pcm_data, sz);
                while (available_short > 0) {
                    if (needed_short > available_short) {
                        memcpy(&mp3_enc_info[i].buffer[mp3_enc_info[i].readed_short], &pcm_data[total_short - available_short], available_short * sizeof(short));
                        mp3_enc_info[i].readed_short += available_short;
                        needed_short -= available_short;
                        available_short = 0;
                        break;
                    }
                    else {
                        uint8_t* data = 0;
                        memcpy(&mp3_enc_info[i].buffer[mp3_enc_info[i].readed_short], &pcm_data[total_short - available_short], needed_short * sizeof(short));
                        mp3_enc_info[i].readed_short += needed_short;
                        available_short -= needed_short;
                        data = shine_encode_buffer_interleaved(mp3_enc_info[i].handle, mp3_enc_info[i].buffer, &mp3_enc_info[i].written);
                        needed_short = mp3_enc_info[i].samples_per_pass;
                        mp3_enc_info[i].readed_short = 0;
                        if (mp3_enc_info[i].fd > 0) {
                            common_fwrite(data, 1, mp3_enc_info[i].written, mp3_enc_info[i].fd);
                        }
                        if (mp3_enc_info[i].config.callback) {
                            mp3_enc_info[i].config.callback(data, (uint32_t)mp3_enc_info[i].written);
                        }
                    }
                }
            }
            else {
                break;
            }
        }
    }
    return 0;
}

static void mp3_enc_stop(void) {
    int i = 0;
    for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
        if (mp3_enc_info[i].close_flag) {
            if (mp3_enc_info[i].handle) {
                uint8_t* data = shine_flush(mp3_enc_info[i].handle, &mp3_enc_info[i].written);
                if (mp3_enc_info[i].fd > 0) {
                    common_fwrite(data, 1, mp3_enc_info[i].written, mp3_enc_info[i].fd);
                    common_fclose(mp3_enc_info[i].fd);
                }
                if (mp3_enc_info[i].config.callback) {
                    mp3_enc_info[i].config.callback(data, (uint32_t)mp3_enc_info[i].written);
                }
                shine_close(mp3_enc_info[i].handle);
            }
            if (mp3_enc_info[i].handle_pcm) {
                acm_audio_record_close(mp3_enc_info[i].handle_pcm);
            }
            if (mp3_enc_info[i].magic) {
                free(mp3_enc_info[i].magic);
            }
            if (mp3_enc_info[i].buffer) {
                free(mp3_enc_info[i].buffer);
            }
            memset(&mp3_enc_info[i], 0, sizeof(MP3_ENC_USER_INFO));
        }
    }
}

static void mp3Enc(void*p) {
    // Encode loop
    while (1) {
        UINT32 event = 0;
        OSAFlagWait(mp3enc_flag_ref, MP3_ENC_TASK_MASK, OSA_FLAG_OR_CLEAR, &event, OSA_SUSPEND);
        DIAG_FILTER(AUDIO, MP3_ENC, mp3_enc_event, DIAG_INFORMATION)
        diagPrintf("mp3_enc_event:0x%lx", event);

        if (MP3_ENC_CONTINUE & event) {
            mp3_enc_continue();
        }

        if (MP3_ENC_START & event) {
            mp3_enc_start();
        }

        if (MP3_ENC_STOP & event) {
            mp3_enc_stop();
        }
    }
}

void mp3EncInit(void) {
    if (!mp3enc_sema_ref) {
        OS_STATUS status = OSASemaphoreCreate(&mp3enc_sema_ref, 1, OSA_FIFO);
        ASSERT(status == OS_SUCCESS);
    }
}

static void mp3enc_task_init(void) {
    static int inited = 0;
    OS_STATUS status;
    size_t mp3_stack_size = 1024 * 5;
    int mp3_thread_priority = 75;

    if (!inited) {
        status = OSAFlagCreate(&mp3enc_flag_ref);
        ASSERT(status == OS_SUCCESS);

        mp3enc_stack_ptr = malloc(mp3_stack_size);
        ASSERT(mp3enc_stack_ptr);

        status = OSATaskCreate(&mp3enc_task_ref, mp3enc_stack_ptr, mp3_stack_size, mp3_thread_priority, "mp3Enc", mp3Enc, NULL);
        ASSERT(status == OS_SUCCESS);

        mp3EncInit();
        inited = 1;
    }
}

static int openEncHandle(const char* file_name, const mp3EncConfigInfo* config, mp3EncodeHandle* handle) {
    int i = 0, error_code = -1;
    OSA_STATUS osa_status;
    osa_status = OSASemaphoreAcquire(mp3enc_sema_ref, OSA_SUSPEND);
    ASSERT(osa_status == OS_SUCCESS);
    for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
        if (file_name && strlen(file_name) > 0 && strcmp(mp3_enc_info[i].file_name, file_name) == 0) {
            error_code = -2;
            break;
        }
        if (!config->callback && (!file_name)) {
            error_code = -3;
            break;
        }
        if (mp3_enc_info[i].handle == 0) {
            if (file_name)
                strcpy(mp3_enc_info[i].file_name, file_name);
            memcpy(&mp3_enc_info[i].config, config, sizeof(mp3EncConfigInfo));
            mp3_enc_info[i].magic = (char*)malloc(sizeof(char));
            if (handle) {
                *handle = (mp3EncodeHandle)mp3_enc_info[i].magic;
            }
            error_code = 0;
            break;
        }
    }
    osa_status = OSASemaphoreRelease(mp3enc_sema_ref);
    ASSERT(osa_status == OS_SUCCESS);

    DIAG_FILTER(AUDIO, MP3_ENC, openEncHandle, DIAG_INFORMATION)
    diagPrintf("error_code:%d", error_code);
    return error_code;
}

static int closeEncHandle(mp3EncodeHandle handle) {
    int i = 0, error_code = -1;
    OSA_STATUS osa_status;
    osa_status = OSASemaphoreAcquire(mp3enc_sema_ref, OSA_SUSPEND);
    ASSERT(osa_status == OS_SUCCESS);
    for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
        if ((handle & mp3_enc_info[i].unique_id) == mp3_enc_info[i].unique_id) {
            mp3_enc_info[i].close_flag = 1;
            error_code = 0;
            break;
        }
    }
    osa_status = OSASemaphoreRelease(mp3enc_sema_ref);
    ASSERT(osa_status == OS_SUCCESS);
    return error_code;
}

int mp3EncStart(const char* file_name, const mp3EncConfigInfo* config, mp3EncodeHandle* handle) {
    DIAG_FILTER(AUDIO, MP3_ENC, mp3EncStart, DIAG_INFORMATION)
    diagPrintf("file_name:%s", file_name);

    if (mp3enc_task_ref == NULL) {
        mp3enc_task_init();
    }

    if (openEncHandle(file_name, config, handle) != 0)
        return -1;

    OSAFlagSet(mp3enc_flag_ref, MP3_ENC_START, OSA_FLAG_OR);
    return 0;
}

int mp3EncStopWithName(const char* file_name) {
    int i = 0;
    DIAG_FILTER(AUDIO, MP3_ENC, mp3EncStopWithName, DIAG_INFORMATION)
    diagPrintf("mp3EncStopWithName:%s", file_name);

    for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
        if (file_name && strlen(file_name) > 0 && strcmp(file_name, mp3_enc_info[i].file_name) == 0)
            return mp3EncStop(mp3_enc_info[i].unique_id);
    }

    return -1;
}

int mp3GetPCMInHandle(mp3EncodeHandle handle, uint32_t* record_handle) {
    int i = 0;
    DIAG_FILTER(AUDIO, MP3_ENC, mp3GetPCMInHandle, DIAG_INFORMATION)
    diagPrintf("handle:0x%lx, record_handle:0x%lx", handle, record_handle);
    for (i = 0; i < MP3_ENC_USER_MAXIMUM; i++) {
        if ((handle & mp3_enc_info[i].unique_id) == mp3_enc_info[i].unique_id) {
            if (record_handle) {
                *record_handle = mp3_enc_info[i].handle_pcm;
                return 0;
            }
        }
    }

    return -1;
}

int mp3EncStop(mp3EncodeHandle handle) {
    DIAG_FILTER(AUDIO, MP3_ENC, mp3EncStop, DIAG_INFORMATION)
    diagPrintf("handle:0x%lx", handle);
    if (closeEncHandle(handle) != 0)
        return -1;

    OSAFlagSet(mp3enc_flag_ref, MP3_ENC_STOP, OSA_FLAG_OR);
    return 0;
}

//ICAT EXPORTED FUNCTION - Audio,MP3,mp3enc_test_start
int mp3enc_test_start(void) {
    int rc = 0;
    mp3EncConfigInfo config = { 0 };
    config.mode = AUDIO_RECORD_MODE_TX;
    config.gain = 6;

    rc = mp3EncStart("test_enc.mp3", &config, 0);
    if (rc) {
        DIAG_FILTER(AUDIO, MP3_ENC, mp3_enc_test_start_fail, DIAG_INFORMATION)
        diagPrintf("rc:%d", rc);
    }
    else {
        DIAG_FILTER(AUDIO, MP3_ENC, mp3_enc_test_start_success, DIAG_INFORMATION)
        diagPrintf("rc:%d", rc);
    }
    return 0;
}

static void on_mp3_frame_encoded(const uint8_t* buf, uint32_t size) {
    DIAG_FILTER(AUDIO, MP3_ENC, on_mp3_frame_encoded, DIAG_INFORMATION);
    diagStructPrintf("frame_dump", (void*)buf, size);
}

//ICAT EXPORTED FUNCTION - Audio,MP3,mp3enc_test_stop
int mp3enc_test_stop(void) {
    mp3EncStopWithName("test_enc.mp3");
    return 0;
}

//ICAT EXPORTED FUNCTION - Audio,MP3,mp3enc_test2_start
int mp3enc_test2_start(void) {
    mp3EncConfigInfo config = { 0 };
    config.bitr = 0;
    config.mode = AUDIO_RECORD_MODE_TX;
    config.callback = on_mp3_frame_encoded;
    mp3EncStart(0, &config, 0);
    return 0;
}

//ICAT EXPORTED FUNCTION - Audio,MP3,mp3enc_test2_stop
int mp3enc_test2_stop(void) {
    mp3EncStop(0xffffffff);
    return 0;
}
