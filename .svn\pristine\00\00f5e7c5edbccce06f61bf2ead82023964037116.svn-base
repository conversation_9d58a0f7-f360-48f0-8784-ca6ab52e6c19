/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*****************************************************************************
 *               MODULE IMPLEMENTATION FILE
 ******************************************************************************
 *  COPYRIGHT (C) 2005 Intel Corporation, All rights reserved..
 *
 *  This file and the software in it is furnished under
 *  license and may only be used or copied in accordance with the terms of the
 *  license. The information in this file is furnished for informational use
 *  only, is subject to change without notice, and should not be construed as
 *  a commitment by Intel Corporation. Intel Corporation assumes no
 *  responsibility or liability for any errors or inaccuracies that may appear
 *  in this document or any software that may be provided in association with
 *  this document.
 *  Except as permitted by such license, no part of this document may be
 *  reproduced, stored in a retrieval system, or transmitted in any form or by
 *  any means without the express written consent of Intel Corporation.
 *
 * Title: USIM transport layer implementation file
 *
 * Filename: usim_transport.c
 *
 * Author: Eilam Ben-Dror
 *
 * Description: This file implements the transport layer of the USIM protocol.
 *
 * Last Updated: 14-Dec-2005
 *
 *****************************************************************************/

/*----------- Local include files -------------------------------------------*/
#include "usim_db.h"
#include "usim.h"
#include "usim_hw.h"
#include "usim_dl.h"
#include "usim_transport.h"

/*----------- External include files ----------------------------------------*/
#include <string.h>

#include "diag_API.h"

/*----------- Local defines -------------------------------------------------*/

// procedure bytes:
#define USIM_REPEAT_HEADER_BYTE 0x6C
#define USIM_GET_RESPONSE_BYTE 0x61
#define USIM_WARNING_BYTE_1      0x62
#define USIM_WARNING_BYTE_2      0x63
#define USIM_GSM_GET_RESPONSE_BYTE 0x9F
#define USIM_GSM_DDL_ERROR            0x9E
#define USIM_NORMAL_END_BYTE 0x90
#define USIM_GET_RESPONSE_CLASS 0x00
#define USIM_GSM_GET_RESPONSE_CLASS 0xA0

#define USIM_INSTRUCTION_GET_RESPONSE 0xC0
#define USIM_INSTRUCTION_ENVELOPE   0xC2

#define USIM_INSTRUCTION_VERIFY 0x20
#define USIM_INSTRUCTION_CHANGE_PIN 0x24
#define USIM_INSTRUCTION_DISABLE_PIN 0x26
#define USIM_INSTRUCTION_ENABLE_PIN 0x28
#define USIM_INSTRUCTION_UBLOCK_PIN 0x2C
#define USIM_INSTRUCTION_UPDATE_BINARY 0xD6
#define USIM_INSTRUCTION_UPDATE_RECORD 0xDC
#define USIM_INSTRUCTION_SELECT        0xA4

// number of bytes in a command header:
#define USIM_HEADER_LEN 5
#define USIM_SW1SW2_LENGTH 2L

/*----------- Local macro definitions ---------------------------------------*/

// check whether a given byte is a warning:
//#define USIM_IS_WARNING_BYTE(byte) (((byte)==0x62)||((byte)==0x63))

// check whether a given byte is application status:
#define USIM_IS_APPLICATION_STATUS(b) ((((b)&0xF0)==9)&&(b!=0x90))

// check whether a given byte is normal ending:
#define USIM_IS_NORMAL_ENDING(b) ((b)==0x90)

/*----------- Local type definitions ----------------------------------------*/

/*----------- Global constant definitions -----------------------------------*/

/*----------- Local constant definitions ------------------------------------*/

/*----------- Local variable definitions ------------------------------------*/
static UINT8 _USIMT1CommandBuffer[USIM_CARDS_AMOUNT][USIM_T1_MAX_BLOCK_SIZE];

/*----------- Global variable definition ------------------------------------*/

/*------------ Local function declaration ------------------------------------*/

//static USIM_ReturnCode USIMT0case2Send(USIM_Card card, UINT8 * header, UINT32 expectedLen);
static USIM_ReturnCode USIMT0case12Send(USIM_Card card);
/*------------ Local function definition ------------------------------------*/







/******************************************************************************
* Function     :	USIMT0case12Send
*******************************************************************************
*
* Description  :	Sends an instruction to the card and receives the response
*
* Parameters   :	card - the card whose response is decoded
*					header - command header
*
* Return value :	USIM_RC_OK
*					USIM_RC_SEND_ERROR
*					USIM_RC_RECEIVE_ERROR
*
* Notes:
******************************************************************************/
//static USIM_ReturnCode USIMT0case2Send(USIM_Card card, UINT8 * header, UINT32 expectedLen)
static USIM_ReturnCode USIMT0case12Send(USIM_Card card )
{
    UINT32 responseLen = 0;		// number of bytes in card's response
	USIM_ReturnCode rc = USIM_RC_OK;

    UINT8 *txHeader = _USIMtxBuffers[card];

	UINT8 * rxPtr =     _USIMrxBuffers[card];

	// make a command header
	txHeader[USIM_CLA]  = _USIMcommandHeaders[card].instructionClass;
	txHeader[USIM_INS]  = _USIMcommandHeaders[card].instructionCode;
	txHeader[USIM_P1]   = _USIMcommandHeaders[card].param1;
	txHeader[USIM_P2]   = _USIMcommandHeaders[card].param2;
    txHeader[USIM_LEN]  = (UINT8) _USIMcommandHeaders[card].expectDataLength;



	DIAG_FILTER(USIMLOG,USIMT0case12Send,LOG001,DIAG_INFORMATION)
	diagPrintf("USIMT0case12Send %u, %x %x %x %x %x", card, txHeader[USIM_CLA], txHeader[USIM_INS], txHeader[USIM_P1], txHeader[USIM_P2], txHeader[USIM_LEN]); 		

    // send the command header:
    responseLen = USIM_DL_writeRead(card, USIM_HEADER_LEN, txHeader,  (_USIMcommandHeaders[card].expectDataLength)+2, NULL);
    
    /* Check for HW removed or Guard Timer detected */
    rc = USIM_DL_Check_States( card);

	DIAG_FILTER(USIMLOG,USIMT0case12Send,LOG002,DIAG_INFORMATION)
	diagPrintf("USIMT0case12Send %u, rc=%d, responseLen=%lu, error=%lu", card, rc, responseLen,  USIM_DL_ErrorDetectedT0(card)); 	


	
    if ( rc!= USIM_RC_OK)
    {
    
        return rc;
    }

    if((responseLen >= USIM_SW1SW2_LENGTH) && ( !USIM_DL_ErrorDetectedT0(card)))
    {
        responseLen -= USIM_SW1SW2_LENGTH;

        

        _USIMresponseHeaders[card].status1 = rxPtr[responseLen];
        _USIMresponseHeaders[card].status2 = rxPtr[responseLen + 1];

        if (responseLen)   // copy the received data to data buffer:
        {
            memcpy(&(_USIMdataBuffers[card][_USIMdataBufferSize[card]]), rxPtr, responseLen);
        }

        // update size:
        _USIMdataBufferSize[card] += responseLen;

		DIAG_FILTER(USIMLOG,USIMT0case12Send,LOG003,DIAG_INFORMATION)
		diagPrintf("USIMT0case12Send %u, status=%x %x, _USIMdataBufferSize=%lu", card, rxPtr[responseLen], rxPtr[responseLen + 1], _USIMdataBufferSize[card]);	


		
        return USIM_RC_OK;
   
    }
    else
    {
        if(responseLen == 1)
            _USIMresponseHeaders[card].status1 = ( _USIMrxBuffers[card])[0]; /*in case 1 byte returned - store it */



		DIAG_FILTER(USIMLOG,USIMT0case12Send,LOG004,DIAG_INFORMATION)
		diagPrintf("USIMT0case12Send %u, responseLen=%lu, status1=%lx", card, responseLen, _USIMresponseHeaders[card].status1);	

		
        return USIM_RC_RECEIVE_ERROR;
    }

}/*		End of USIMT0case2Send	*/

/******************************************************************************
* Function     :    USIMT0Case34Command
*******************************************************************************
*
* Description  :    Sends a T0 protocol case 3 or 4 command to the card
*
* Parameters   :	card - the card to which the data is sent.
*
* Return value :	USIM_RC_OK
*					USIM_RC_SEND_ERROR
*					USIM_RC_RECEIVE_ERROR
*
* Notes:
******************************************************************************/
static USIM_ReturnCode USIMT0Case34Command(USIM_Card card)
{
	UINT8 header [USIM_HEADER_LEN];
	USIM_CommandHeader * headerPtr = _USIMcommandHeaders + card;
	UINT32 responseLen;
    UINT8 dataLen = (UINT8)headerPtr->dataLength;
	UINT8 dataToSend;
	UINT8 *procedureBytes = _USIMT1CommandBuffer[card]; //USe Global  Buffer to prevent memory damaage
	UINT8 * dataPtr = _USIMdataBuffers[card];
	USIM_ReturnCode rc = USIM_RC_OK;
        BOOL errorSt = FALSE;





	// make a command header
	header[USIM_CLA]	= headerPtr->instructionClass;
	header[USIM_INS]	= headerPtr->instructionCode;
	header[USIM_P1]		= headerPtr->param1;
	header[USIM_P2]		= headerPtr->param2;
	header[USIM_LEN]	= dataLen;




	DIAG_FILTER(USIMLOG,USIMT0Case34Command,LOG001,DIAG_INFORMATION)
	diagPrintf("USIMT0Case34Command %u, %x %x %x %x %x", card, header[USIM_CLA], header[USIM_INS], header[USIM_P1], header[USIM_P2], header[USIM_LEN]); 



	// send the command header:
    responseLen = USIM_DL_writeRead(card, USIM_HEADER_LEN, header,
                                    1, procedureBytes);
    
    /* Check for HW removed or Guard Timer detected */
    rc = USIM_DL_Check_States( card);



	
    if ( rc!= USIM_RC_OK)
    {
		DIAG_FILTER(USIMLOG,USIMT0Case34Command,LOG002,DIAG_INFORMATION)
		diagPrintf("USIMT0Case34Command %u, rc=%d", card, rc); 

   
        return rc;
    }

    errorSt = USIM_DL_ErrorDetectedT0(card);
    while ((dataLen>0) && (responseLen>0)&& (!errorSt))
    {

		DIAG_FILTER(USIMLOG,USIMT0Case34Command,LOG003,DIAG_INFORMATION)
		diagPrintf("USIMT0Case34Command %u, dataLen=%lu, responseLen=%lu, errorSt=%lu, procedureBytes=%x", card, dataLen, responseLen, errorSt, procedureBytes[0]); 



    	if (procedureBytes[0] == USIM_NULL_BYTE)
    	{
    		procedureBytes++; // update pointer to remove NULL_BYTE
    		continue;
    	}

        if( ( procedureBytes[0]== header[USIM_INS]) || ( procedureBytes[0] == USIM_BYTEWISE_NOT(header[USIM_INS])))//( CHECK_FOR_ACK(header[USIM_INS], procedureBytes[0])  )
        {




		
            if (procedureBytes[0] == header[USIM_INS])
            {
            	dataToSend = dataLen;
            }
            else
            {
            	dataToSend = 1;
            }
            procedureBytes =  _USIMT1CommandBuffer[card];
            // send data & receive receive at least one procedure byte or pair Sw1/Sw2

			
            responseLen = USIM_DL_writeRead(card, dataToSend, dataPtr, 1, procedureBytes);
            
            /* Check for HW removed or Guard Timer detected */
            rc = USIM_DL_Check_States( card);


			DIAG_FILTER(USIMLOG,USIMT0Case34Command,LOG004,DIAG_INFORMATION)
			diagPrintf("USIMT0Case34Command %u, responseLen=%lu, dataLen=%lu, dataToSend=%lu", card, responseLen, dataLen, dataToSend); 


			
            if ( rc!= USIM_RC_OK)
                return rc;
            
            dataLen -= dataToSend;
            dataPtr += dataToSend;
            
            errorSt = USIM_DL_ErrorDetectedT0(card);
        }
        else
        {// error code returned
       
            dataLen = 0;
        }
    }// end while(dataLen>0)

	if((responseLen < USIM_SW1SW2_LENGTH) || (errorSt))
	{		            /* Less than 2 bytes received - return error */


			
            if (responseLen==1)
		    _USIMresponseHeaders[card].status1 = procedureBytes[0];      /* Just log everything what we have */

		DIAG_FILTER(USIMLOG,USIMT0Case34Command,LOG005,DIAG_INFORMATION)
		diagPrintf("USIMT0Case34Command %u, responseLen=%lu, errorSt=%lu, status=%x", card, responseLen, errorSt, _USIMresponseHeaders[card].status1); 

			
	    return USIM_RC_RECEIVE_ERROR;
	}

	_USIMresponseHeaders[card].status1 = procedureBytes[0];
	_USIMresponseHeaders[card].status2 = procedureBytes[1];

	DIAG_FILTER(USIMLOG,USIMT0Case34Command,LOG006,DIAG_INFORMATION)
	diagPrintf("USIMT0Case34Command %u, status=%x %x", card, _USIMresponseHeaders[card].status1, _USIMresponseHeaders[card].status2); 

	
	return rc;

}/*     End of USIMT0Case34Command   */

void USIMSetResponseReadFlag(UINT8 card, UINT32 responsereadFlag)
{
	DIAG_FILTER(USIMLOG,USIMSetResponseReadFlag,LOG001,DIAG_INFORMATION)
	diagPrintf("USIMSetResponseReadFlag %u, responsereadFlag %x", card, responsereadFlag); 

	_USIMResponseReadFlag[card] = responsereadFlag;
	return;
}

UINT32 USIMGetResponseReadFlag(UINT8 card)
{	
	DIAG_FILTER(USIMLOG,USIMGetResponseReadFlag,LOG001,DIAG_INFORMATION)
	diagPrintf("USIMGetResponseReadFlag %u, responsereadFlag %x", card,  _USIMResponseReadFlag[card]); 

	return _USIMResponseReadFlag[card];
}


/*----------- Global function definition ------------------------------------*/
/******************************************************************************
* Function     :	USIMTransportT0CommandSend
*******************************************************************************
*
* Description  :	Sends a command to the card
*
* Parameters   :	card - the card to which the data is sent.
*
* Return value :	USIM_RC_OK
*
* Notes:
******************************************************************************/
UINT8 _USIMCla; 
USIM_ReturnCode USIMTransportT0CommandSend(USIM_Card card)
{
	UINT8   sw2, sw1,sw1first=0;
	USIM_CommandHeader *header = _USIMcommandHeaders + card;
	USIM_ReturnCode rc = USIM_RC_OK;
	BOOL    sendMoreCommad,    repeatLastCmd, 	sendMoreCommadOnce = FALSE, 	case34cmd = FALSE ;
	UINT8 instruction = _USIMcommandHeaders[card].instructionCode; 



	_USIMCla = _USIMcommandHeaders[card].instructionClass;
	_USIMrxBufferPtrs[card] = _USIMrxBuffers[card];




	DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG001,DIAG_INFORMATION)
	diagPrintf("USIMTransportT0CommandSend %u, dataLength=%lu", card, header->dataLength); 




	if (header->dataLength)
	{




		case34cmd = TRUE;
		rc=USIMT0Case34Command(card);
	}
	else
	{


		
		rc = USIMT0case12Send(card);
	}


	do
	{  
		sw1 = _USIMresponseHeaders[card].status1;
		sw1first = (sw1first ==0)?sw1:sw1first;






		if ( (rc != USIM_RC_OK) || (USIM_IS_NORMAL_ENDING(sw1)))   
		{
				DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG002,DIAG_INFORMATION)
				diagPrintf("USIMTransportT0CommandSend %u, rc=%d, sw1=%lx", card, rc, sw1); 
						            
			break;            
		}

		sw2 = _USIMresponseHeaders[card].status2;           
		sendMoreCommad = FALSE;
		repeatLastCmd = FALSE;

		switch (sw1)
		{    // the following is for case 4 command:
#if !defined (USIM_NO_GSM_GET_RESPONSE_HANDLE)
			/* GSM ONLY: for GSM ENVELOPE follwing indication could be present see TS 11.11 TS 100 977 section 9.1 */
			case USIM_GSM_DDL_ERROR:

						DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG003,DIAG_INFORMATION)
						diagPrintf("USIMTransportT0CommandSend %u, USIM_GSM_DDL_ERROR, instruction=%lx, sendMoreCommadOnce=%lu", card, instruction, sendMoreCommadOnce); 



				if (instruction == USIM_INSTRUCTION_ENVELOPE)
				{
					if (!sendMoreCommadOnce)
					{
						sendMoreCommad = TRUE;
						sendMoreCommadOnce = TRUE;
					}                                
				}
				break;

			case USIM_GSM_GET_RESPONSE_BYTE:
						DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG004,DIAG_INFORMATION)
						diagPrintf("USIMTransportT0CommandSend %u, USIM_GSM_GET_RESPONSE_BYTE, instructionClass=%lx, sendMoreCommad=%lu", card, _USIMcommandHeaders[card].instructionClass, sendMoreCommad); 

                        sendMoreCommad = TRUE;
                        break;
#endif 
               case USIM_GET_RESPONSE_BYTE:
			   	
						//sendMoreCommad = TRUE;
						
                        //if(((_USIMcommandHeaders[card].instructionClass&0xF)!=0))
                        {
                        	sendMoreCommad=FALSE;	
							
							if(1 == USIMGetResponseReadFlag(card))
							{
								sendMoreCommad = TRUE;
							}
                        }
                        //sendMoreCommad = TRUE;
                        
						DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG005,DIAG_INFORMATION)
						diagPrintf("USIMTransportT0CommandSend %u, USIM_GET_RESPONSE_BYTE, instructionClass=%x,instruction=%x, sendMoreCommad=%lu, %lu, %lu", card, _USIMcommandHeaders[card].instructionClass, instruction, sendMoreCommad, _USIMdataBufferSize[card], sw2); 

                        break;

			case USIM_WARNING_BYTE_1: 
			case USIM_WARNING_BYTE_2:  
						 DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG006,DIAG_INFORMATION)
						 diagPrintf("USIMTransportT0CommandSend %u, USIM_WARNING_BYTE_12, sw=%lx %lx, instruction=%lx, case34cmd=%lu, sendMoreCommadOnce=%lu", card, sw1, sw2, instruction, case34cmd, sendMoreCommadOnce); 
                
                           
                        if (instruction == USIM_INSTRUCTION_ENVELOPE)
                        {   /* according to TS 102 221 section 7.4.2.2 WARNING should be treated differentlly 
                                        for ENVELOPE instruction */
                            sendMoreCommad  = TRUE;
                            break; /* Return SW1/SW2 to application */                            
                        }

				if (((sw1== USIM_WARNING_BYTE_2) && ((sw2 & 0xF0) == 0xC0))  &&
				((instruction == USIM_INSTRUCTION_VERIFY) ||
				(instruction == USIM_INSTRUCTION_CHANGE_PIN) ||
				(instruction == USIM_INSTRUCTION_DISABLE_PIN) ||
				(instruction == USIM_INSTRUCTION_ENABLE_PIN) ||
				(instruction == USIM_INSTRUCTION_UBLOCK_PIN) ||
				(instruction == USIM_INSTRUCTION_UPDATE_BINARY) ||
				(instruction == USIM_INSTRUCTION_UPDATE_RECORD) ) )
				{


					/* SW1/SW2 = 63CX see TS 102 221 section 11.1.9, 11.1.13 and table 10.16 */
					sendMoreCommad  = FALSE;
					break;
				}

				/* according to TS 102 221 sections 7.3.1.1.4(case 4b),  7.3.1.1 or section C.1.7
				warning for case 4 command ONLY should be followed by GET RESPONSE */

				if (case34cmd)
				{


					if(!sendMoreCommadOnce)
					{


						sendMoreCommad = TRUE;
						sendMoreCommadOnce = TRUE; /* warning should work only once. Second time is used to be SW1/SW2 */
						sw2 = 0;
					}
				}
				break;                    

			case USIM_REPEAT_HEADER_BYTE:
					DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG007,DIAG_INFORMATION)
					diagPrintf("USIMTransportT0CommandSend %u, USIM_REPEAT_HEADER_BYTE", card); 
				// Repeat last command with Le/P3 == SW2
				sendMoreCommad = TRUE;               
				repeatLastCmd = TRUE;                       
				//           sw2 = 0;
				//break;


		}



		
		if ( sendMoreCommad)
		{




			_USIMcommandHeaders[card].dataLength = sw2;
			_USIMcommandHeaders[card].expectDataLength = sw2;
			if (sw2==0)
				_USIMcommandHeaders[card].expectDataLength = USIM_ALL_AVAILABLE_DATA;

					DIAG_FILTER(USIMLOG,USIMTransportT0CommandSend,LOG008,DIAG_INFORMATION)
					diagPrintf("USIMTransportT0CommandSend %u, sendMoreCommad, sw2=%lx, repeatLastCmd=%lu", card, sw2, repeatLastCmd); 				
                
			if (!repeatLastCmd)
			{   /* Build GET RESPONSE COMMAND with known length sw2 */


				_USIMcommandHeaders[card].instructionClass =((header->instructionClass == 0x80)?0:header->instructionClass);    

				_USIMcommandHeaders[card].instructionCode = USIM_INSTRUCTION_GET_RESPONSE;
				_USIMcommandHeaders[card].param1 = 0;
				_USIMcommandHeaders[card].param2 = 0;

			}

			rc = USIMT0case12Send(card);

		}
	}while ( sendMoreCommad);


	/* restore Original SW1 */
	if (sw1first == USIM_GSM_DDL_ERROR)
	{
	
		_USIMresponseHeaders[card].status1 = sw1first;
	}
	return rc;

}/* End of USIMTransportT0CommandSend */







/******************************************************************************
* Function     :	USIMTransportT1CommandSend
*******************************************************************************
*
* Description  :	Sends a command to the card
*
* Parameters   :	card - the card to which the data is sent.
*
* Return value :	USIM_RC_OK
*
* Notes:
******************************************************************************/
USIM_ReturnCode USIMTransportT1CommandSend(USIM_Card card)
{
    USIM_ReturnCode rc = USIM_RC_OK;
	USIM_CommandHeader * headerPtr = _USIMcommandHeaders + card;
	UINT32 sentDataLen = headerPtr->dataLength;
    UINT32 infoLen = USIM_T1_FRAME_SIZE; // min header size for case 1 command
    UINT32 returnedDataLen = 0;

	// copy the header into the info buffer:
	_USIMT1CommandBuffer[card][USIM_CLA]	= headerPtr->instructionClass;
	_USIMT1CommandBuffer[card][USIM_INS]	= headerPtr->instructionCode;
	_USIMT1CommandBuffer[card][USIM_P1]	= headerPtr->param1;
	_USIMT1CommandBuffer[card][USIM_P2]	= headerPtr->param2;

	if(sentDataLen)
	{// there's data to be sent:
		_USIMT1CommandBuffer[card][infoLen]	= sentDataLen;

		infoLen++;

		// copy the data to be sent into the info buffer:
		memcpy((_USIMT1CommandBuffer[card]+infoLen), _USIMdataBuffers[card], sentDataLen);

		// Reset the data buffer size:
		_USIMdataBufferSize[card] = 0;

		infoLen += sentDataLen;
	}

	if(headerPtr->expectDataLength)
	{// data is expected to be returned from the card:
        _USIMT1CommandBuffer[card][infoLen] = (UINT8)headerPtr->expectDataLength;
		infoLen++;
    }

    rc = USIM_DL_T1Send(card, infoLen, _USIMT1CommandBuffer[card]);
    
     /* Check for HW remove detected */
    if (rc == USIM_RC_CARD_REMOVED)
        return rc;

/*----------------------------*/
        returnedDataLen = _USIMdataBufferSize[card];
        if(returnedDataLen >= USIM_SW1SW2_LENGTH)
        {
            _USIMresponseHeaders[card].status1 =   _USIMdataBuffers[card][returnedDataLen-2];
            _USIMresponseHeaders[card].status2 =   _USIMdataBuffers[card][returnedDataLen-1];
            _USIMdataBufferSize[card] -= USIM_SW1SW2_LENGTH;
        }
        else
         if ( returnedDataLen == 1)
        { /* Hypotetical scenario - error, just trace 1 byte */
            _USIMresponseHeaders[card].status1 =   _USIMdataBuffers[card][0];
        }
        

/*  ---------------------------*/
/*
    if( rc == USIM_RC_OK) 
    {
        returnedDataLen = _USIMdataBufferSize[card];
        
        if ( returnedDataLen < USIM_SW1SW2_LENGTH))
        {
            rc = USIM_RC_RECEIVE_ERROR;
        } 
        else       
        {
            // get the status bytes:
            _USIMresponseHeaders[card].status1 =   _USIMdataBuffers[card][returnedDataLen-2];
            _USIMresponseHeaders[card].status2 =   _USIMdataBuffers[card][returnedDataLen-1];
            returnedDataLen -=2;            // len doesn't include status bytes

            // send response to manager

            if(returnedDataLen)
            {
        		USIMResponseNotify(card, &(_USIMresponseHeaders[card]),
                                        returnedDataLen,
                                        _USIMdataBuffers[card]);
            }
            else
            {
        		USIMResponseNotify(card, &(_USIMresponseHeaders[card]), 0, NULL);
            }
        }
    }

    // set the data buffer size to 0:
    _USIMdataBufferSize[card] = 0;
*/            

    return rc;

}/* End of USIMTransportT1CommandSend */


