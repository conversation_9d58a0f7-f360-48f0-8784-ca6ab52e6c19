#ifndef _SDK_ATCHAN_H_
#define _SDK_ATCHAN_H_

#include <stdint.h>

typedef enum
{
   TEL_AT_CMD_ATP_0,       //SIM1  indication
   TEL_AT_CMD_ATP_1,      // AT modem fpr SIM1
   TEL_AT_CMD_ATP_2,	//CC for SIM1
   TEL_AT_CMD_ATP_3,    //MM for SIM1
   TEL_AT_CMD_ATP_4,    //MSG for SIM1
   TEL_AT_CMD_ATP_5,    //SIM/PB  for SIM1
   TEL_AT_CMD_ATP_6,    //DEV for SIM1
   TEL_AT_CMD_ATP_7,	//PS for SIM1
   TEL_AT_CMD_ATP_8,	//SS for SIM1
   TEL_AT_CMD_ATP_9,
   TEL_AT_CMD_ATP_10,
   TEL_AT_CMD_ATP_11,
   TEL_AT_CMD_ATP_12,
   TEL_AT_CMD_ATP_13,
   TEL_AT_CMD_ATP_14,
   TEL_AT_CMD_ATP_15,
   TEL_AT_CMD_ATP_16,
   T<PERSON>_AT_CMD_ATP_17,
   TEL_AT_CMD_ATP_18,
   TEL_AT_CMD_ATP_19,
   TEL_AT_CMD_ATP_20,
   TEL_AT_CMD_ATP_21,
   TEL_AT_CMD_ATP_22,
   TEL_AT_CMD_ATP_23,
   TEL_AT_CMD_ATP_24,
   TEL_AT_CMD_ATP_25,

   TEL_AT_CMD_ATP_26,
   TEL_MODEM_AT_CMD_ATP,
   TEL_AT_CMD_ATP_28,
   TEL_AT_CMD_ATP_29,
   TEL_AT_CMD_ATP_30,
   TEL_AT_CMD_ATP_31,
   TEL_AT_CMD_ATP_32,
   TEL_AT_CMD_ATP_33,
   TEL_AT_CMD_ATP_34,
   TEL_AT_CMD_ATP_35,

   TEL_AT_CMD_ATP_36,		// SIM2  indication
   TEL_AT_CMD_ATP_37,		// AT modem fpr SIM2
   TEL_AT_CMD_ATP_38,		//CC for SIM2
   TEL_AT_CMD_ATP_39,		//MM for SIM2
   TEL_AT_CMD_ATP_40,		//MSG for SIM2
   TEL_AT_CMD_ATP_41,		//SIM/PB  for SIM2
   TEL_AT_CMD_ATP_42,		//DEV for SIM2
   TEL_AT_CMD_ATP_43,		//PS for SIM2
   TEL_AT_CMD_ATP_44,		//SS for SIM2
   TEL_AT_CMD_ATP_45,
   TEL_AT_CMD_ATP_46,
   TEL_AT_CMD_ATP_47,
   TEL_AT_CMD_ATP_48,
   TEL_AT_CMD_ATP_49,
   TEL_AT_CMD_ATP_50,
   TEL_AT_CMD_ATP_51,
   TEL_AT_CMD_ATP_52,
   TEL_AT_CMD_ATP_53,
   TEL_AT_CMD_ATP_54,
   TEL_AT_CMD_ATP_55,
   TEL_AT_CMD_ATP_56,
   TEL_AT_CMD_ATP_57,
   TEL_AT_CMD_ATP_58,
   TEL_AT_CMD_ATP_59,
   TEL_AT_CMD_ATP_60,
   TEL_AT_CMD_ATP_61,
   TEL_AT_CMD_ATP_62,
   TEL_MODEM_AT_CMD_ATP_1,

   TEL_AT_CMD_ATP_64,
   TEL_AT_CMD_ATP_65,
   TEL_AT_CMD_ATP_66,
   TEL_AT_CMD_ATP_67,
   TEL_AT_CMD_ATP_68,
   TEL_AT_CMD_ATP_69,
   TEL_AT_CMD_ATP_70,
   TEL_AT_CMD_ATP_71,
   NUM_OF_TEL_ATP
} TelAtParserID;

#ifdef __cplusplus
extern "C" {
#endif

typedef unsigned char uint8;
typedef unsigned short uint16;
typedef uint32_t UINT32;

typedef int (*MATConfIndCB)(TelAtParserID chnlId, const char* response, int size);

void MATSetConfIndCB(TelAtParserID channelId, MATConfIndCB cb);

void ATRecv(int AtpIndex, const char *s, UINT32 length);

typedef struct tag_tt_func_list {
    bool (*tt_is_playing)(void);
    bool (*tt_is_recording)(void);
    void (*tt_record_on)(uint8);
    void (*tt_record_off)(void);
    void (*tt_play_on)(char *);
    void (*tt_play_off)(void);
    uint8 (*tt_get_mode)(void);
    void (*tt_get_record_file)(char *);
    void (*tt_set_data)(void *);
    void (*tt_onkey)(void);
    void (*tt_set_mode)(uint8);
    void (*tt_app)(char *);
    void (*tt_reset)(void);
}tt_func_list;
void TT_CMD_CALLBACK_REGISTER(tt_func_list *handler);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif
