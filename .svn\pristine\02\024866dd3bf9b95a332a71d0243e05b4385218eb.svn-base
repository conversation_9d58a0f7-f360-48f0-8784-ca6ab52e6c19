/******************************************************************************
 *  Filename: tellwip.c
 *
 *  Authors: <AUTHORS>
 *
 *  Description: AT interface implementation for lwip related AT commands
 *
 *  History:
 *   May 15, 2019 - Creation of file
******************************************************************************/
#include "telutl.h"
#include "telatparamdef.h"
#include "atlwip.h"
#include "lwip_at_api.h"
#include "tellwip.h"

static struct atLwipRequestArgs *atLwipOpenGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "open";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipOpenSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipOpenSetReq *openSetReq = (struct atLwipOpenSetReq *)malloc(sizeof(struct atLwipOpenSetReq));
  ASSERT(openSetReq != NULL);
  char cBuff[128];
  memset(cBuff, 0, 128);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(openSetReq, 0, sizeof(struct atLwipOpenSetReq));

  if(getExtValue(parameter_values_p, 0, &(openSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, cBuff, TEL_AT_LWIP_DOMAIN_MAX_LEN,
                  &str_len, TEL_AT_LWIP_DOMAIN_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "IPV4", 4) == 0){
    openSetReq->domain = PF_INET;
  } else if(strncasecmp(cBuff, "IPV6", 4) == 0){
    openSetReq->domain = PF_INET6;
  } else if(strncasecmp(cBuff, "UNSPEC", 6) == 0){
    openSetReq->domain = PF_UNSPEC;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtString(parameter_values_p, 2, cBuff, TEL_AT_LWIP_OPEN_TYPE_MAX_LEN,
                  &str_len, TEL_AT_LWIP_OPEN_TYPE_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "STREAM", 6) == 0){
    openSetReq->type = SOCK_STREAM;
  } else if(strncasecmp(cBuff, "DGRAM", 5) == 0){
    openSetReq->type = SOCK_DGRAM;
  } else if(strncasecmp(cBuff, "RAW", 3) == 0){
    openSetReq->type = SOCK_RAW;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtString(parameter_values_p, 3, cBuff, TEL_AT_LWIP_OPEN_PROTOCOL_MAX_LEN,
                  &str_len, TEL_AT_LWIP_OPEN_PROTOCOL_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "IP", 2) == 0){
    openSetReq->protocol = IPPROTO_IP;
  } else if(strncasecmp(cBuff, "TCP", 3) == 0){
    openSetReq->protocol = IPPROTO_TCP;
  } else if(strncasecmp(cBuff, "UDP", 3) == 0){
    openSetReq->protocol = IPPROTO_UDP;
  } else if(strncasecmp(cBuff, "IPV6", 4) == 0){
    openSetReq->protocol = IPPROTO_IPV6;
  } else if(strncasecmp(cBuff, "UDPLITE", 7) == 0){
    openSetReq->protocol = IPPROTO_UDPLITE;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtString(parameter_values_p, 4, openSetReq->option, TEL_AT_LWIP_OPEN_OPTION_MAX_LEN,
                   &str_len, TEL_AT_LWIP_OPEN_OPTION_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)openSetReq;

  reqArgs->primName = "open";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;

exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(openSetReq);
  openSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipOpenTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "open";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipBindGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "bind";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipBindSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipBindSetReq *bindSetReq = (struct atLwipBindSetReq *)malloc(sizeof(struct atLwipBindSetReq));
  ASSERT(bindSetReq != NULL);
  char cBuff[128];
  memset(cBuff, 0, 128);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(bindSetReq, 0, sizeof(struct atLwipBindSetReq));

  if(getExtValue(parameter_values_p, 0, &(bindSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, cBuff, TEL_AT_LWIP_DOMAIN_MAX_LEN,
                  &str_len, TEL_AT_LWIP_DOMAIN_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "IPV4", 4) == 0){
    bindSetReq->domain = PF_INET;
  } else if(strncasecmp(cBuff, "IPV6", 4) == 0){
    bindSetReq->domain = PF_INET6;
  } else if(strncasecmp(cBuff, "UNSPEC", 6) == 0){
    bindSetReq->domain = PF_UNSPEC;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtString(parameter_values_p, 2, bindSetReq->ip, TEL_AT_LWIP_IP_MAX_LEN,
                   &str_len, TEL_AT_LWIP_IP_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 3, &(bindSetReq->port), TEL_AT_LWIP_PORT_MIN,
                  TEL_AT_LWIP_PORT_MAX, TEL_AT_LWIP_PORT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)bindSetReq;

  reqArgs->primName = "bind";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(bindSetReq);
  bindSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipBindTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "bind";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipListenGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "listen";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipListenSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipListenSetReq *listenSetReq = (struct atLwipListenSetReq *)malloc(sizeof(struct atLwipListenSetReq));
  ASSERT(listenSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(listenSetReq, 0, sizeof(struct atLwipListenSetReq));

  if(getExtValue(parameter_values_p, 0, &(listenSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 1, &(listenSetReq->backlog), TEL_AT_LWIP_LSTN_BACKLOG_MIN,
                  TEL_AT_LWIP_LSTN_BACKLOG_MAX, TEL_AT_LWIP_LSTN_BACKLOG_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)listenSetReq;

  reqArgs->primName = "listen";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;  
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(listenSetReq);
  listenSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipListenTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "listen";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipAcceptGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "accept";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipAcceptSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipAcceptSetReq *acceptSetReq = (struct atLwipAcceptSetReq *)malloc(sizeof(struct atLwipAcceptSetReq));
  ASSERT(acceptSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(acceptSetReq, 0, sizeof(struct atLwipAcceptSetReq));

  if(getExtValue(parameter_values_p, 0, &(acceptSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)acceptSetReq;

  reqArgs->primName = "accept";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(acceptSetReq);
  acceptSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipAcceptTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "accept";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipConnectGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "connect";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipConnectSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipConnectSetReq *connectSetReq = (struct atLwipConnectSetReq *)malloc(sizeof(struct atLwipConnectSetReq));
  ASSERT(connectSetReq != NULL);
  char cBuff[128];
  memset(cBuff, 0, 128);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(connectSetReq, 0, sizeof(struct atLwipConnectSetReq));

  if(getExtValue(parameter_values_p, 0, &(connectSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, cBuff, TEL_AT_LWIP_DOMAIN_MAX_LEN,
                  &str_len, TEL_AT_LWIP_DOMAIN_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "IPV4", 4) == 0){
    connectSetReq->domain = PF_INET;
  } else if(strncasecmp(cBuff, "IPV6", 4) == 0){
    connectSetReq->domain = PF_INET6;
  } else if(strncasecmp(cBuff, "UNSPEC", 6) == 0){
    connectSetReq->domain = PF_UNSPEC;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtString(parameter_values_p, 2, connectSetReq->ip, TEL_AT_LWIP_IP_MAX_LEN,
                   &str_len, TEL_AT_LWIP_IP_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 3, &(connectSetReq->port), TEL_AT_LWIP_PORT_MIN,
                  TEL_AT_LWIP_PORT_MAX, TEL_AT_LWIP_PORT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)connectSetReq;

  reqArgs->primName = "connect";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(connectSetReq);
  connectSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipConnectTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "connect";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipReadGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "read";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipReadSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipReadSetReq *readSetReq = (struct atLwipReadSetReq *)malloc(sizeof(struct atLwipReadSetReq));
  ASSERT(readSetReq != NULL);
  char cBuff[16];
  INT16 str_len = 0;
  memset(cBuff, 0, 16);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(readSetReq, 0, sizeof(struct atLwipReadSetReq));

  if(getExtValue(parameter_values_p, 0, &(readSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 1, &(readSetReq->count), TEL_AT_LWIP_READ_COUNT_MIN,
                  TEL_AT_LWIP_READ_COUNT_MAX, TEL_AT_LWIP_READ_COUNT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 2, cBuff, TEL_AT_LWIP_READ_PRINT_MAX_LEN,
                  &str_len, TEL_AT_LWIP_READ_PRINT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if (cBuff[0] != '\0') {
    if(strncasecmp(cBuff, "print", 5) == 0){
      readSetReq->print = TRUE;
    } else {
      AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
      goto exit;
    }
    memset(cBuff, 0, 16);
  }

  commandReq->action = "set";
  commandReq->param = (void *)readSetReq;

  reqArgs->primName = "read";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(readSetReq);
  readSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipReadTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "read";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipRecvGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "recv";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipRecvSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipRecvSetReq *recvSetReq = (struct atLwipRecvSetReq *)malloc(sizeof(struct atLwipRecvSetReq));
  ASSERT(recvSetReq != NULL);
  char cBuff[16];
  INT16 str_len = 0;
  memset(cBuff, 0, 16);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(recvSetReq, 0, sizeof(struct atLwipRecvSetReq));

  if(getExtValue(parameter_values_p, 0, &(recvSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 1, &(recvSetReq->count), TEL_AT_LWIP_READ_COUNT_MIN,
                  TEL_AT_LWIP_READ_COUNT_MAX, TEL_AT_LWIP_READ_COUNT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 2, cBuff, TEL_AT_LWIP_READ_PRINT_MAX_LEN,
                  &str_len, TEL_AT_LWIP_READ_PRINT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "print", 5) == 0){
    recvSetReq->print = TRUE;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 16);

  commandReq->action = "set";
  commandReq->param = (void *)recvSetReq;

  reqArgs->primName = "recv";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(recvSetReq);
  recvSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipRecvTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "recv";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipRecvFromGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "recvFrom";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipRecvFromSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipRecvFromSetReq *recvFromSetReq = (struct atLwipRecvFromSetReq *)malloc(sizeof(struct atLwipRecvFromSetReq));
  ASSERT(recvFromSetReq != NULL);
  char cBuff[16];
  INT16 str_len = 0;
  memset(cBuff, 0, 16);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(recvFromSetReq, 0, sizeof(struct atLwipRecvFromSetReq));

  if(getExtValue(parameter_values_p, 0, &(recvFromSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 1, &(recvFromSetReq->count), TEL_AT_LWIP_READ_COUNT_MIN,
                  TEL_AT_LWIP_READ_COUNT_MAX, TEL_AT_LWIP_READ_COUNT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 2, cBuff, TEL_AT_LWIP_READ_PRINT_MAX_LEN,
                  &str_len, TEL_AT_LWIP_READ_PRINT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "print", 5) == 0){
    recvFromSetReq->print = TRUE;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 16);
  
  commandReq->action = "set";
  commandReq->param = (void *)recvFromSetReq;

  reqArgs->primName = "recvFrom";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(recvFromSetReq);
  recvFromSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipRecvFromTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "recvFrom";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipWriteGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "write";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipWriteSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipWriteSetReq *writeSetReq = (struct atLwipWriteSetReq *)malloc(sizeof(struct atLwipWriteSetReq));
  ASSERT(writeSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(writeSetReq, 0, sizeof(struct atLwipWriteSetReq));

  if(getExtValue(parameter_values_p, 0, &(writeSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, writeSetReq->buf, TEL_AT_LWIP_WRITE_BUF_MAX_LEN,
                   &str_len, TEL_AT_LWIP_WRITE_BUF_DEFAULT ) == FALSE ) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 2, &(writeSetReq->len), TEL_AT_LWIP_WRITE_LEN_MIN,
                  TEL_AT_LWIP_WRITE_LEN_MAX, TEL_AT_LWIP_WRITE_LEN_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)writeSetReq;

  reqArgs->primName = "write";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(writeSetReq);
  writeSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipWriteTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "write";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSendGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "send";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSendSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipSendSetReq *sendSetReq = (struct atLwipSendSetReq *)malloc(sizeof(struct atLwipSendSetReq));
  ASSERT(sendSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(sendSetReq, 0, sizeof(struct atLwipSendSetReq));

  if(getExtValue(parameter_values_p, 0, &(sendSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, sendSetReq->buf, TEL_AT_LWIP_WRITE_BUF_MAX_LEN,
                   &str_len, TEL_AT_LWIP_WRITE_BUF_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 2, &(sendSetReq->len), TEL_AT_LWIP_WRITE_LEN_MIN,
                  TEL_AT_LWIP_WRITE_LEN_MAX, TEL_AT_LWIP_WRITE_LEN_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)sendSetReq;

  reqArgs->primName = "send";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(sendSetReq);
  sendSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipSendTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "send";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSendToGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "sendTo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSendToSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipSendToSetReq *sendToSetReq = (struct atLwipSendToSetReq *)malloc(sizeof(struct atLwipSendToSetReq));
  ASSERT(sendToSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(sendToSetReq, 0, sizeof(struct atLwipSendToSetReq));

  if(getExtValue(parameter_values_p, 0, &(sendToSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, sendToSetReq->buf, TEL_AT_LWIP_WRITE_BUF_MAX_LEN,
                   &str_len, TEL_AT_LWIP_WRITE_BUF_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 2, &(sendToSetReq->len), TEL_AT_LWIP_WRITE_LEN_MIN,
                  TEL_AT_LWIP_WRITE_LEN_MAX, TEL_AT_LWIP_WRITE_LEN_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 3, sendToSetReq->ip, TEL_AT_LWIP_IP_MAX_LEN,
                   &str_len, TEL_AT_LWIP_IP_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }
  

  if(getExtValue(parameter_values_p, 4, &(sendToSetReq->port), TEL_AT_LWIP_PORT_MIN,
                  TEL_AT_LWIP_PORT_MAX, TEL_AT_LWIP_PORT_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)sendToSetReq;

  reqArgs->primName = "sendTo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(sendToSetReq);
  sendToSetReq = NULL;
  return NULL;  
}

static struct atLwipRequestArgs *atLwipSendToTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "sendTo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipCloseGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "close";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipCloseSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipCloseSetReq *closeSetReq = (struct atLwipCloseSetReq *)malloc(sizeof(struct atLwipCloseSetReq));
  ASSERT(closeSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(closeSetReq, 0, sizeof(struct atLwipCloseSetReq));

  if(getExtValue(parameter_values_p, 0, &(closeSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)closeSetReq;

  reqArgs->primName = "close";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(closeSetReq);
  closeSetReq = NULL;
  return NULL;  
}

static struct atLwipRequestArgs *atLwipCloseTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "close";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipShutDownGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "shutDown";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipShutDownSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipShutDownSetReq *shutDownSetReq = (struct atLwipShutDownSetReq *)malloc(sizeof(struct atLwipShutDownSetReq));
  ASSERT(shutDownSetReq != NULL);
  char cBuff[128];
  INT16 str_len = 0;
  memset(cBuff, 0, 128);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(shutDownSetReq, 0, sizeof(struct atLwipShutDownSetReq));

  if(getExtValue(parameter_values_p, 0, &(shutDownSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, cBuff, TEL_AT_LWIP_SHUTDOWN_HOW_MAX_LEN,
                  &str_len, TEL_AT_LWIP_SHUTDOWN_HOW_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "RD", 2) == 0){
    shutDownSetReq->how = PF_INET;
  } else if(strncasecmp(cBuff, "WR", 2) == 0){
    shutDownSetReq->how = PF_INET6;
  } else if(strncasecmp(cBuff, "RDWR", 4) == 0){
    shutDownSetReq->how = PF_UNSPEC;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  commandReq->action = "set";
  commandReq->param = (void *)shutDownSetReq;

  reqArgs->primName = "shutDown";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(shutDownSetReq);
  shutDownSetReq = NULL;
  return NULL;   
}

static struct atLwipRequestArgs *atLwipShutDownTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "shutDown";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipFcntlGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "fcntl";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipFcntlSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipFcntlSetReq *fcntlSetReq = (struct atLwipFcntlSetReq *)malloc(sizeof(struct atLwipFcntlSetReq));
  ASSERT(fcntlSetReq != NULL);
  char cBuff[128];
  INT16 str_len = 0;
  memset(cBuff, 0, 128);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(fcntlSetReq, 0, sizeof(struct atLwipFcntlSetReq));

  if(getExtValue(parameter_values_p, 0, &(fcntlSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, cBuff, TEL_AT_LWIP_FCNTL_CMD_MAX_LEN,
                  &str_len, TEL_AT_LWIP_FCNTL_CMD_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "FGET", 4) == 0){
    fcntlSetReq->cmd = F_GETFL;
  } else if(strncasecmp(cBuff, "FSET", 4) == 0){
    fcntlSetReq->cmd = F_SETFL;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtValue(parameter_values_p, 2, &(fcntlSetReq->val), TEL_AT_LWIP_FCNTL_VAL_MIN,
                  TEL_AT_LWIP_FCNTL_VAL_MAX, TEL_AT_LWIP_FCNTL_VAL_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)fcntlSetReq;

  reqArgs->primName = "fcntl";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(fcntlSetReq);
  fcntlSetReq = NULL;
  return NULL; 
}

static struct atLwipRequestArgs *atLwipFcntlTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "fcntl";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSelectGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);

  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "select";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSelectSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipSelectSetReq *selectSetReq = (struct atLwipSelectSetReq *)malloc(sizeof(struct atLwipSelectSetReq));
  ASSERT(selectSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(selectSetReq, 0, sizeof(struct atLwipSelectSetReq));
  if(getExtString(parameter_values_p, 0, selectSetReq->fdSet, TEL_AT_LWIP_SELECT_FDSET_MAX_LEN,
                   &str_len, TEL_AT_LWIP_SELECT_FDSET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, selectSetReq->option, TEL_AT_LWIP_SELECT_OPTION_MAX_LEN,
                   &str_len, TEL_AT_LWIP_SELECT_OPTION_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)selectSetReq;

  reqArgs->primName = "select";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;

  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(selectSetReq);
  selectSetReq = NULL;
  return NULL; 
}

static struct atLwipRequestArgs *atLwipSelectTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "select";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipIOCtlSocketGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);

  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "ioCtlSocket";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipIOCtlSocketSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  char cBuff[128];
  memset(cBuff, 0, 128);
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipIOCtlSocketSetReq *ioCtlSocketSetReq = (struct atLwipIOCtlSocketSetReq *)malloc(sizeof(struct atLwipIOCtlSocketSetReq));
  ASSERT(ioCtlSocketSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(ioCtlSocketSetReq, 0, sizeof(struct atLwipIOCtlSocketSetReq));

  if(getExtValue(parameter_values_p, 0, &(ioCtlSocketSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, cBuff, TEL_AT_LWIP_IOCTLSOCKET_CMD_MAX_LEN,
                   &str_len, TEL_AT_LWIP_IOCTLSOCKET_CMD_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "IONREAD", 7) == 0) {
    ioCtlSocketSetReq->cmd = FIONREAD;
  } else if(strncasecmp(cBuff, "IONBIO", 6) == 0) {
    ioCtlSocketSetReq->cmd = FIONBIO;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtValue(parameter_values_p, 2, &(ioCtlSocketSetReq->val), TEL_AT_LWIP_IOCTLSOCKET_VAL_MIN,
                  TEL_AT_LWIP_IOCTLSOCKET_VAL_MAX, TEL_AT_LWIP_IOCTLSOCKET_VAL_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)ioCtlSocketSetReq;

  reqArgs->primName = "ioCtlSocket";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;

  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(ioCtlSocketSetReq);
  ioCtlSocketSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipIOCtlSocketTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "ioCtlSocket";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSockNameGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "sockName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSockNameSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipSockNameSetReq *sockNameSetReq = (struct atLwipSockNameSetReq *)malloc(sizeof(struct atLwipSockNameSetReq));
  ASSERT(sockNameSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(sockNameSetReq, 0, sizeof(struct atLwipSockNameSetReq));

  if(getExtValue(parameter_values_p, 0, &(sockNameSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)sockNameSetReq;

  reqArgs->primName = "sockName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(sockNameSetReq);
  sockNameSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipSockNameTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "sockName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipPeerNameGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "peerName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipPeerNameSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipPeerNameSetReq *peerNameSetReq = (struct atLwipPeerNameSetReq *)malloc(sizeof(struct atLwipPeerNameSetReq));
  ASSERT(peerNameSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(peerNameSetReq, 0, sizeof(struct atLwipPeerNameSetReq));

  if(getExtValue(parameter_values_p, 0, &(peerNameSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)peerNameSetReq;

  reqArgs->primName = "peerName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(peerNameSetReq);
  peerNameSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipPeerNameTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "peerName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSockOptGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "sockOpt";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSockOptSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipSockOptSetReq *sockOptSetReq = (struct atLwipSockOptSetReq *)malloc(sizeof(struct atLwipSockOptSetReq));
  ASSERT(sockOptSetReq != NULL);
  char cBuff[128];
  memset(cBuff, 0, 128);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(sockOptSetReq, 0, sizeof(struct atLwipSockOptSetReq));

  if(getExtValue(parameter_values_p, 0, &(sockOptSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, cBuff, TEL_AT_LWIP_SOKCOPT_LEVEL_MAX_LEN,
                  &str_len, TEL_AT_LWIP_SOKCOPT_LEVEL_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(strncasecmp(cBuff, "IP", 2) == 0){
    sockOptSetReq->level = IPPROTO_IP;
  } else if(strncasecmp(cBuff, "TCP", 3) == 0){
    sockOptSetReq->level = IPPROTO_TCP;
  } else if(strncasecmp(cBuff, "UDP", 3) == 0){
    sockOptSetReq->level = IPPROTO_UDP;
  } else if(strncasecmp(cBuff, "IPV6", 4) == 0){
    sockOptSetReq->level = IPPROTO_IPV6;
  } else if(strncasecmp(cBuff, "UDPLITE", 7) == 0){
    sockOptSetReq->level = IPPROTO_UDPLITE;
  } else if(strncasecmp(cBuff, "SOL_SOCKET", 10) == 0){
    sockOptSetReq->level = SOL_SOCKET;
  } else {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cBuff);
    goto exit;
  }
  memset(cBuff, 0, 128);

  if(getExtValue(parameter_values_p, 2, &(sockOptSetReq->option), TEL_AT_LWIP_SOKCOPT_OPTION_MIN,
                  TEL_AT_LWIP_SOKCOPT_OPTION_MAX, TEL_AT_LWIP_SOKCOPT_OPTION_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 3, &(sockOptSetReq->val1), TEL_AT_LWIP_SOKCOPT_OPTION_MIN,
                  TEL_AT_LWIP_SOKCOPT_OPTION_MAX, TEL_AT_LWIP_SOKCOPT_OPTION_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 4, &(sockOptSetReq->val2), TEL_AT_LWIP_SOKCOPT_OPTION_MIN,
                  TEL_AT_LWIP_SOKCOPT_OPTION_MAX, TEL_AT_LWIP_SOKCOPT_OPTION_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 5, &(sockOptSetReq->len), TEL_AT_LWIP_SOKCOPT_OPTION_MIN,
                  TEL_AT_LWIP_SOKCOPT_OPTION_MAX, TEL_AT_LWIP_SOKCOPT_OPTION_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)sockOptSetReq;

  reqArgs->primName = "sockOpt";
  reqArgs->cmdName  = NULL;
//  memcpy(reqArgs->cmdName, sockOptSetReq->cmd, str_len);
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(sockOptSetReq);
  sockOptSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipSockOptTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "sockOpt";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSetMapGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "accept";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipSetMapSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipSetMapSetReq *setMapSetReq = (struct atLwipSetMapSetReq *)malloc(sizeof(struct atLwipSetMapSetReq));
  ASSERT(setMapSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(setMapSetReq, 0, sizeof(struct atLwipSetMapSetReq));

  if(getExtValue(parameter_values_p, 0, &(setMapSetReq->inSockFd), TEL_AT_LWIP_SOCKET_MIN,
                  TEL_AT_LWIP_SOCKET_MAX, TEL_AT_LWIP_SOCKET_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)setMapSetReq;

  reqArgs->primName = "setMap";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(setMapSetReq);
  setMapSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipSetMapTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "accept";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipGetHostByNameGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "getHostByName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipGetHostByNameSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipGetHostByNameSetReq *getHostByNameSetReq = (struct atLwipGetHostByNameSetReq *)malloc(sizeof(struct atLwipGetHostByNameSetReq));
  ASSERT(getHostByNameSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(getHostByNameSetReq, 0, sizeof(struct atLwipGetHostByNameSetReq));

  if(getExtString(parameter_values_p, 0, getHostByNameSetReq->name, TEL_AT_LWIP_GET_HOST_BY_NAME_MAX_LEN,
                   &str_len, TEL_AT_LWIP_GET_HOST_BY_NAME_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, getHostByNameSetReq->cmd, TEL_AT_LWIP_GET_HOST_BY_NAME_CMD_MAX_LEN,
                   &str_len, TEL_AT_LWIP_GET_HOST_BY_NAME_CMD_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 2, &(getHostByNameSetReq->pcid), TEL_AT_LWIP_DNS_PCID_MIN,
                  TEL_AT_LWIP_DNS_PCID_MAX, TEL_AT_LWIP_DNS_PCID_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)getHostByNameSetReq;

  reqArgs->primName = "getHostByName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(getHostByNameSetReq);
  getHostByNameSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipGetHostByNameTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "getHostByName";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipGetAddrInfoGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "getAddrInfo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipGetAddrInfoSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipGetAddrInfoSetReq *getAddrInfoSetReq = (struct atLwipGetAddrInfoSetReq *)malloc(sizeof(struct atLwipGetAddrInfoSetReq));
  ASSERT(getAddrInfoSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(getAddrInfoSetReq, 0, sizeof(struct atLwipGetAddrInfoSetReq));

  if(getExtString(parameter_values_p, 0, getAddrInfoSetReq->nodeName, TEL_AT_LWIP_NODE_NAME_MAX_LEN,
                   &str_len, TEL_AT_LWIP_NODE_NAME_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, getAddrInfoSetReq->servName, TEL_AT_LWIP_SERVER_NAME_MAX_LEN,
                   &str_len, TEL_AT_LWIP_SERVER_NAME_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 2, &(getAddrInfoSetReq->pcid), TEL_AT_LWIP_DNS_PCID_MIN,
                  TEL_AT_LWIP_DNS_PCID_MAX, TEL_AT_LWIP_DNS_PCID_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)getAddrInfoSetReq;

  reqArgs->primName = "getAddrInfo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(getAddrInfoSetReq);
  getAddrInfoSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipGetAddrInfoTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "getAddrInfo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipFreeAddrInfoGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "freeAddrInfo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipFreeAddrInfoSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipFreeAddrInfoSetReq *freeAddrInfoSetReq = (struct atLwipFreeAddrInfoSetReq *)malloc(sizeof(struct atLwipFreeAddrInfoSetReq));
  ASSERT(freeAddrInfoSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(freeAddrInfoSetReq, 0, sizeof(struct atLwipFreeAddrInfoSetReq));

  if(getExtString(parameter_values_p, 0, freeAddrInfoSetReq->nodeName, TEL_AT_LWIP_NODE_NAME_MAX_LEN,
                   &str_len, TEL_AT_LWIP_NODE_NAME_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtString(parameter_values_p, 1, freeAddrInfoSetReq->servName, TEL_AT_LWIP_SERVER_NAME_MAX_LEN,
                   &str_len, TEL_AT_LWIP_SERVER_NAME_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if(getExtValue(parameter_values_p, 2, &(freeAddrInfoSetReq->pcid), TEL_AT_LWIP_DNS_PCID_MIN,
                  TEL_AT_LWIP_DNS_PCID_MAX, TEL_AT_LWIP_DNS_PCID_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)freeAddrInfoSetReq;

  reqArgs->primName = "freeAddrInfo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(freeAddrInfoSetReq);
  freeAddrInfoSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipFreeAddrInfoTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "freeAddrInfo";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipTcpSyncBackOffGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "tcpSyncBackOff";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipTcpSyncBackOffSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  INT16 str_len = 0;
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipTcpSyncBackOffSetReq *tcpSyncBackOffSetReq = (struct atLwipTcpSyncBackOffSetReq *)malloc(sizeof(struct atLwipTcpSyncBackOffSetReq));
  ASSERT(tcpSyncBackOffSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(tcpSyncBackOffSetReq, 0, sizeof(struct atLwipTcpSyncBackOffSetReq));

  if(getExtString(parameter_values_p, 0, tcpSyncBackOffSetReq->backOff, TEL_AT_LWIP_TCP_SYNC_BACKOFF_MAX_LEN,
                   &str_len, TEL_AT_LWIP_TCP_SYNC_BACKOFF_DEFAULT ) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  commandReq->action = "set";
  commandReq->param = (void *)tcpSyncBackOffSetReq;

  reqArgs->primName = "tcpSyncBackOff";
  reqArgs->cmdName = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(tcpSyncBackOffSetReq);
  tcpSyncBackOffSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipTcpSyncBackOffTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  reqArgs->primName = "tcpSyncBackOff";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static struct atLwipRequestArgs *atLwipLogEnvGetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "get";
  commandReq->param = NULL;

  reqArgs->primName = "logEnv";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static int setModuleSettingSwitch(char *moduleUnit, struct moduleDebugSettingT *moduleDebugSetting)
{
  if (strstr(moduleUnit, "on") != NULL) {
    moduleDebugSetting->onoff = LWIP_DBG_ON;
  } else if (strstr(moduleUnit, "off") != NULL) {
    moduleDebugSetting->onoff = LWIP_DBG_OFF;
  } else {
    AtLwipPrintf("%s %d Failed When Set Module Debug Switch, Module Unit : %s\n", __FUNCTION__, __LINE__, moduleUnit);
    return -1;
  }
  return 0;
}

static int setModuleSettingLevel(char *moduleUnit, struct moduleDebugSettingT *moduleDebugSetting)
{
  if (strstr(moduleUnit, "all") != NULL) {
    moduleDebugSetting->level= LWIP_DBG_LEVEL_ALL;
  } else if (strstr(moduleUnit, "info") != NULL) {
    moduleDebugSetting->level = LWIP_DBG_LEVEL_INFO;
  } else if (strstr(moduleUnit, "warning") != NULL) {
    moduleDebugSetting->level = LWIP_DBG_LEVEL_WARNING;
  } else if (strstr(moduleUnit, "serious") != NULL) {
    moduleDebugSetting->level = LWIP_DBG_LEVEL_SERIOUS;
  } else if (strstr(moduleUnit, "severe") != NULL) {
    moduleDebugSetting->level = LWIP_DBG_LEVEL_SEVERE;
  } else {
    AtLwipPrintf("%s %d Failed When Set Module Debug Switch, Module Unit : %s\n", __FUNCTION__, __LINE__, moduleUnit);
    return -1;
  }
  return 0;
}

static int pickModuleUnit(char *in, char *out)
{
  int num = 0;
  char *p = in;
  if (*in == '\0') {
    return -1;
  }

  while (*in != '\0') {
    if (*in == ';') {
      memcpy(out, p, num);
      AtLwipPrintf("%s %d module debug setting unit : %s, num of char : %d\n", __FUNCTION__, __LINE__, out, num);
      return 0;
    } else {
      num++;
      in++;
    }
  }
  memcpy(out, p, num);
  AtLwipPrintf("%s %d module debug setting unit : %s, num of char : %d\n", __FUNCTION__, __LINE__, out, num);
  return 0;
}

/**
 * cModuleBuff[] = {"arp:on,info;icmp:off,warning"}
 * moduleUnit[] = {arp:on,info}
 **/
static int setModuleSetting(char *cModuleBuff, struct moduleDebugSettingT *moduleDebugSettings)
{
  int i = 0, j = 0, found = 0;
  char *location = NULL;
  char moduleUnit[32] = { 0 };
  char *moduleName[lwipModuleSwitchSize] = {
    "moduleon", "arp", "netif", "pbuf", "apilib", "apimsg", "sockets", "netconn",
    "icmp", "igmp", "inet", "ip", "ipreass", "raw", "mem", "memp",
    "sys", "timers", "tcp", "tcpslowtmr", "tcpinput", "tcpfr", "tcprto", "tcpcwnd",
    "tcpwnd", "tcpoutput", "tcprst", "tcpqlen", "udp", "tcpip", "ppp", "slip",
    "dhcp", "autoip", "snmpmsg", "snmpmib", "dns", "ip6", "ipnat", "lwipapi",
    "nd6", "ipv4trace", "ipv6trace"
  };

  for (i = 0; i < lwipModuleSwitchSize; i++) {
    if ((location = strstr(cModuleBuff, moduleName[i])) != NULL) {
      moduleDebugSettings[j].name = moduleName[i];
      if (pickModuleUnit(location, moduleUnit) == 0) {
        if (setModuleSettingSwitch(moduleUnit, &moduleDebugSettings[j]) != 0) {
          return -1;
        }
        if (setModuleSettingLevel(moduleUnit, &moduleDebugSettings[j]) != 0) {
          return -1;
        }
        j++;
        found++;
      } else {
        AtLwipPrintf("%s %d Failed When Pick Module, String : %s\n", __FUNCTION__, __LINE__, location);
        return -1;
      }
    }
  }
  return found;
}

static struct atLwipRequestArgs *atLwipLogEnvSetConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  int i = 0;
  INT16 str_len = 0;
  char cBuff[16];
  memset(cBuff, 0, 16);
  char cModuleBuff[2048];
  memset(cModuleBuff, 0, 2048);
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  ASSERT(reqArgs != NULL);
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  ASSERT(commandReq != NULL);
  struct atLwipLogEnvSetReq *logEnvSetReq = (struct atLwipLogEnvSetReq *)malloc(sizeof(struct atLwipLogEnvSetReq));
  ASSERT(logEnvSetReq != NULL);
  // remember free this maollc buffer
  memset(reqArgs, 0, sizeof(struct atLwipRequestArgs));
  memset(commandReq, 0, sizeof(struct atLwipCommandReq));
  memset(logEnvSetReq, 0, sizeof(struct atLwipLogEnvSetReq));

  //logEnvSetReq->moduleSwitchs
  if(getExtString(parameter_values_p, 0, cModuleBuff, TEL_AT_LWIP_LOG_CMD_MODULE_MAX_LEN,
                  &str_len, TEL_AT_LWIP_LOG_CMD_MODULE_DEFAULT) == FALSE) {
    AtLwipPrintf("%s %d Line Get parameter of at command failed\n", __FUNCTION__, __LINE__);
    goto exit;
  }

  if ((logEnvSetReq->moduleDebugSettingCnt = setModuleSetting(cModuleBuff, logEnvSetReq->moduleSwitchSettings)) == 0) {
    AtLwipPrintf("%s %d Line Get invalide parameter(%s) of at command failed\n", __FUNCTION__, __LINE__, cModuleBuff);
    goto exit;
  }
  memset(cModuleBuff, 0, 2048);

  commandReq->action = "set";
  commandReq->param = (void *)logEnvSetReq;

  reqArgs->primName = "logEnv";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
exit:
  free(reqArgs);
  reqArgs = NULL;
  free(commandReq);
  commandReq = NULL;
  free(logEnvSetReq);
  logEnvSetReq = NULL;
  return NULL;
}

static struct atLwipRequestArgs *atLwipLogEnvTestConstruct(UINT32 atHandle, const utlAtParameterValue_P2c parameter_values_p)
{
  struct atLwipRequestArgs *reqArgs = (struct atLwipRequestArgs *)malloc(sizeof(struct atLwipRequestArgs));
  struct atLwipCommandReq *commandReq = (struct atLwipCommandReq *)malloc(sizeof(struct atLwipCommandReq));
  commandReq->action = "test";
  commandReq->param = NULL;

  AtLwipPrintf("%s %d moduleDebugSetting log env\n", __FUNCTION__, __LINE__);

  reqArgs->primName = "logEnv";
  reqArgs->cmdName  = NULL;
  reqArgs->param    = (void *)commandReq;
  reqArgs->atHandle = atHandle;
  return reqArgs;
}

static int getFunIndex(const CHAR *name, struct atLwipRequestHandle *reqHandle, int len)
{
  int i;
  for (i = 0; i < len; i++) {
    if (strcmp(name, reqHandle[i].name) == 0) {
      return i;
    }
  }
  AtLwipPrintf("invalid function name : %d\n", name);
  return -1;
}

struct atLwipRequestHandle requestHandle[] = {
  {"atLwipOpen", {{"exec", NULL}, {"get", atLwipOpenGetConstruct},
                  {"set", atLwipOpenSetConstruct}, {"act", NULL},
                  {"test", atLwipOpenTestConstruct}}
  },
  {"atLwipBind", {{"exec", NULL}, {"get", atLwipBindGetConstruct},
                  {"set", atLwipBindSetConstruct}, {"act", NULL},
                  {"test", atLwipBindTestConstruct}}
  },
  {"atLwipListen", {{"exec", NULL}, {"get", atLwipListenGetConstruct},
                  {"set", atLwipListenSetConstruct}, {"act", NULL},
                  {"test", atLwipListenTestConstruct}}
  },
  {"atLwipAccept", {{"exec", NULL}, {"get", atLwipAcceptGetConstruct},
                  {"set", atLwipAcceptSetConstruct}, {"act", NULL},
                  {"test", atLwipAcceptTestConstruct}}
  },
  {"atLwipConnect", {{"exec", NULL}, {"get", atLwipConnectGetConstruct},
                  {"set", atLwipConnectSetConstruct}, {"act", NULL},
                  {"test", atLwipConnectTestConstruct}}
  },
  {"atLwipRead", {{"exec", NULL}, {"get", atLwipReadGetConstruct},
                  {"set", atLwipReadSetConstruct}, {"act", NULL},
                  {"test", atLwipReadTestConstruct}}
  },
  {"atLwipRecv", {{"exec", NULL}, {"get", atLwipRecvGetConstruct},
                  {"set", atLwipRecvSetConstruct}, {"act", NULL},
                  {"test", atLwipRecvTestConstruct}}
  },
  {"atLwipRecvFrom", {{"exec", NULL}, {"get", atLwipRecvFromGetConstruct},
                  {"set", atLwipRecvFromSetConstruct}, {"act", NULL},
                  {"test", atLwipRecvFromTestConstruct}}
  },
  {"atLwipWrite", {{"exec", NULL}, {"get", atLwipWriteGetConstruct},
                  {"set", atLwipWriteSetConstruct}, {"act", NULL},
                  {"test", atLwipWriteTestConstruct}}
  },
  {"atLwipSend", {{"exec", NULL}, {"get", atLwipSendGetConstruct},
                  {"set", atLwipSendSetConstruct}, {"act", NULL},
                  {"test", atLwipSendTestConstruct}}
  },
  {"atLwipSendTo", {{"exec", NULL}, {"get", atLwipSendToGetConstruct},
                  {"set", atLwipSendToSetConstruct}, {"act", NULL},
                  {"test", atLwipSendToTestConstruct}}
  },
  {"atLwipClose", {{"exec", NULL}, {"get", atLwipCloseGetConstruct},
                  {"set", atLwipCloseSetConstruct}, {"act", NULL},
                  {"test", atLwipCloseTestConstruct}}
  },
  {"atLwipShutDown", {{"exec", NULL}, {"get", atLwipShutDownGetConstruct},
                  {"set", atLwipShutDownSetConstruct}, {"act", NULL},
                  {"test", atLwipShutDownTestConstruct}}
  },
  {"atLwipFcntl", {{"exec", NULL}, {"get", atLwipFcntlGetConstruct},
                  {"set", atLwipFcntlSetConstruct}, {"act", NULL},
                  {"test", atLwipFcntlTestConstruct}}
  },
  {"atLwipSelect", {{"exec", NULL}, {"get", atLwipSelectGetConstruct},
                  {"set", atLwipSelectSetConstruct}, {"act", NULL},
                  {"test", atLwipSelectTestConstruct}}
  },
  {"atLwipIOCtlSocket", {{"exec", NULL}, {"get", atLwipIOCtlSocketGetConstruct},
                  {"set", atLwipIOCtlSocketSetConstruct}, {"act", NULL},
                  {"test", atLwipIOCtlSocketTestConstruct}}
  },
  {"atLwipSockName", {{"exec", NULL}, {"get", atLwipSockNameGetConstruct},
                  {"set", atLwipSockNameSetConstruct}, {"act", NULL},
                  {"test", atLwipSockNameTestConstruct}}
  },
  {"atLwipPeerName", {{"exec", NULL}, {"get", atLwipPeerNameGetConstruct},
                  {"set", atLwipPeerNameSetConstruct}, {"act", NULL},
                  {"test", atLwipPeerNameTestConstruct}}
  },
  {"atLwipSockOpt", {{"exec", NULL}, {"get", atLwipSockOptGetConstruct},
                  {"set", atLwipSockOptSetConstruct}, {"act", NULL},
                  {"test", atLwipSockOptTestConstruct}}
  },
  {"atLwipSetMap", {{"exec", NULL}, {"get", atLwipSetMapGetConstruct},
                  {"set", atLwipSetMapSetConstruct}, {"act", NULL},
                  {"test", atLwipSetMapTestConstruct}}
  },
  {"atLwipGetHostByName", {{"exec", NULL}, {"get", atLwipGetHostByNameGetConstruct},
                  {"set", atLwipGetHostByNameSetConstruct}, {"act", NULL},
                  {"test", atLwipGetHostByNameTestConstruct}}
  },
  {"atLwipGetAddrInfo", {{"exec", NULL}, {"get", atLwipGetAddrInfoGetConstruct},
                  {"set", atLwipGetAddrInfoSetConstruct}, {"act", NULL},
                  {"test", atLwipGetAddrInfoTestConstruct}}
  },
  {"atLwipFreeAddrInfo", {{"exec", NULL}, {"get", atLwipFreeAddrInfoGetConstruct},
                  {"set", atLwipFreeAddrInfoSetConstruct}, {"act", NULL},
                  {"test", atLwipFreeAddrInfoTestConstruct}}
  },
  {"atLwipTcpSyncBackOff", {{"exec", NULL}, {"get", atLwipTcpSyncBackOffGetConstruct},
                  {"set", atLwipTcpSyncBackOffSetConstruct}, {"act", NULL},
                  {"test", atLwipTcpSyncBackOffTestConstruct}}
  },
  {"atLwipLogEnv", {{"exec", NULL}, {"get", atLwipLogEnvGetConstruct},
                  {"set", atLwipLogEnvSetConstruct}, {"act", NULL},
                  {"test", atLwipLogEnvTestConstruct}}
  },
};

RETURNCODE_T generalAtCallback(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p,
  const char                      *atCallbackName,
  const char                      *protocol)
{
	UNUSEDPARAM(command_name_p);
	UNUSEDPARAM(info_text_p);
  UNUSEDPARAM(num_parameters);
  
  RETURNCODE_T ret = utlSUCCESS;
  struct atLwipRequestArgs *reqArgs = NULL;
	TelAtParserID sAtpIndex = *(TelAtParserID *) arg_p;
	UINT32 atHandle = MAKE_AT_HANDLE(sAtpIndex);
  *xid_p = atHandle;

  int index = getFunIndex(atCallbackName, requestHandle, sizeof(requestHandle)/sizeof(struct atLwipRequestHandle));
  if (index == -1) {
    ret = atRespStrForLwip(atHandle, ATLWIP_RESULT_CODE_ERROR, ATLWIPRC_FAIL, "");
    AtLwipPrintf("invalid atCallback name : %s\n", atCallbackName);
    return ret;
  }

  if (op < utlAT_PARAMETER_OP_EXEC || op > utlAT_PARAMETER_OP_SYNTAX) {
    AtLwipPrintf("Undefined Operation : %d\n", op);
    ret = utlFAILED;
    ret = atRespStrForLwip(atHandle, ATLWIP_RESULT_CODE_ERROR, ATLWIPRC_FAIL, "");
    return ret;
  }
  // op : 0 unknown, 1  "exec", 2 "get", 3 "set", 4 "act", 5 "test"
  if (requestHandle[index].request[op-1].construct != NULL) {
    reqArgs = requestHandle[index].request[op-1].construct(atHandle, parameter_values_p); // need to free reqArgs
    if (reqArgs == NULL) {
      return utlFAILED;
    }
  } else {
    ret = atRespStrForLwip(atHandle, ATLWIP_RESULT_CODE_ERROR, ATLWIPRC_FAIL, "");
    AtLwipPrintf("Undefined Function for Operation(%d)\n", op);
    ret = utlFAILED;
    return ret;
  }

  ret = atLwipRequst(protocol, reqArgs);
	return ret;
}

RETURNCODE_T atLwipOpen(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipBind(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipListen(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipAccept(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipConnect(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipRead(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipRecv(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipRecvFrom(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipWrite(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipSend(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipSendTo(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipClose(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipShutDown(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipFcntl(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipSelect(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipIOCtlSocket(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipSockName(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipPeerName(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipSockOpt(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipSetMap(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "socket"));
}

RETURNCODE_T atLwipGetHostByName(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "dns"));
}

RETURNCODE_T atLwipGetAddrInfo(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "dns"));
}

RETURNCODE_T atLwipFreeAddrInfo(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "dns"));
}

RETURNCODE_T atLwipTcpSyncBackOff(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "tcp"));
}

RETURNCODE_T atLwipLogEnv(const utlAtParameterOp_T       op,
  const char                      *command_name_p,
  const utlAtParameterValue_P2c   parameter_values_p,
  const size_t                    num_parameters,
  const char                      *info_text_p,
  unsigned int                    *xid_p,
  void                            *arg_p)
{
  return (generalAtCallback(op, command_name_p, parameter_values_p, num_parameters,
                    info_text_p, xid_p, arg_p, __FUNCTION__, "log"));
}
