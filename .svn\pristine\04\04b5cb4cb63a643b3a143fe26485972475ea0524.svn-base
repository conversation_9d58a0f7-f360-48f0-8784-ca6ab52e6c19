#------------------------------------------------------------
# (C) Copyright [2019-2025] ASR International Ltd.
# All Rights Reserved
#------------------------------------------------------------

# ====================================================================
# File        : inc_Project.mak
# Description : include(!) makefile
#               to be used by any kind of target makefile
#               for different project
#
# ====================================================================

##===================================================================##
##
##            DEFINITIONS AND DECLARATIONS FOR PROJECT
##
##===================================================================##

##===================================================================##
##
ifneq (,$(findstring JACANA_GPS_SUPPORT,${VARIANT_LIST}))
TARGET_DFLAGS += -DGPS_SUPPORT
endif
##----------------------- AGPSTP --------------------------
##
ifneq (,$(findstring AGPSTP_SUPPORT,${VARIANT_LIST}))
TARGET_DFLAGS +=  -DAGPSTP_ENABLE
TARGET_ARMLINK_OPT += --predefine="-DAGPSTP_ENABLE"
endif
##            DEFINITIONS AND DECLARATIONS FOR MODULE
##
##===================================================================##
##---------------------- NO Dialer ---------------------------
##
ifneq (,$(findstring NODIALER,${VARIANT_LIST}))
TARGET_DFLAGS += -DNO_DIALER
endif

##---------------------- NO IMS ---------------------------------
##
ifeq (,$(findstring NOIMS,${VARIANT_LIST}))
INC_PROJECT_DFLAGS += -DVOLTE_ENABLE
endif


##---------------------- NO SULOG ---------------------------
##
ifneq (,$(findstring NOSULG,${VARIANT_LIST}))
TARGET_DFLAGS += -DSULOG_DISABLE
endif

##---------------------- NO paho mqtt ---------------------------
##
ifneq (,$(findstring NOPAHO,${VARIANT_LIST}))
TARGET_DFLAGS += -DNO_PAHO_MQTT
endif
##---------------------- NO mbedTLS ---------------------------
##
ifneq (,$(findstring NOTLS,${VARIANT_LIST}))
TARGET_DFLAGS += -DNO_MBEDTLS
else
#VARIANT_LIST += mbedTLS_2_1_8
VARIANT_LIST += mbedTLS_3_2_1
endif

##----------------------- NO SD card ---------------------------
##
ifneq (,$(findstring SD_NOT_SUPPORT,${VARIANT_LIST}))
TARGET_DFLAGS += -DCRANE_SD_NOT_SUPPORT
endif

##---------------------- NO NTP ---------------------------
##
ifeq (,$(findstring NONTP,${VARIANT_LIST}))
TARGET_DFLAGS += -DNTP
endif
##----------------------Mass Storage --------------------
##
ifeq (,$(findstring NOMASS,${VARIANT_LIST}))
TARGET_DFLAGS += -DMV_USB2_MASS_STORAGE
endif

##----------------------YMODEM DUMP --------------------
##
ifeq (,$(findstring NOYMODEM,${VARIANT_LIST}))
TARGET_DFLAGS += -DYMODEM_EEH_DUMP
endif

##----------------------- Little FS ---------------------------
##
ifeq (,$(findstring NOLFS,${VARIANT_LIST}))
TARGET_DFLAGS += -DLFS_FILE_SYS
VARIANT_LIST += LFS_SUPPORT
endif

##---------------------- NO CHARGER_SUPPORT----------------------
##
ifeq (,$(findstring NOCHARGER,${VARIANT_LIST}))
TARGET_DFLAGS += -DSUPPORT_CHARGER
VARIANT_LIST += SUPPORT_CHARGER
endif

##----------------------LPA SUPPORT --------------------
##
ifneq (,$(findstring LPA_SUPPORT,${VARIANT_LIST}))
TARGET_DFLAGS       += -DLPA_SUPPORT
endif

##----------------------LZMA SUPPORT --------------------
##
ifeq (,$(findstring NOLZMA_SUPPORT,${VARIANT_LIST}))
TARGET_DFLAGS       += -DLZMA_SUPPORT
VARIANT_LIST += LZMA_SUPPORT
endif

$(info INC_PROJECT_DFLAGS = ${INC_PROJECT_DFLAGS})
TARGET_DFLAGS +=$(INC_PROJECT_DFLAGS)
