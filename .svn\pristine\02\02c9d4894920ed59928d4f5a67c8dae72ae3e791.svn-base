/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_GNSS_UTC_Model_H_
#define	_GNSS_UTC_Model_H_


#include <asn_application.h>

/* Including external dependencies */
#include "UTC-ModelSet1.h"
#include "UTC-ModelSet2.h"
#include "UTC-ModelSet3.h"
#include "UTC-ModelSet4.h"
#include "UTC-ModelSet5-r12.h"
#include <constr_CHOICE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum GNSS_UTC_Model_PR {
	GNSS_UTC_Model_PR_NOTHING,	/* No components present */
	GNSS_UTC_Model_PR_utcModel1,
	GNSS_UTC_Model_PR_utcModel2,
	GNSS_UTC_Model_PR_utcModel3,
	GNSS_UTC_Model_PR_utcModel4,
	/* Extensions may appear below */
	GNSS_UTC_Model_PR_utcModel5_r12
} GNSS_UTC_Model_PR;

/* GNSS-UTC-Model */
typedef struct GNSS_UTC_Model {
	GNSS_UTC_Model_PR present;
	union {
		UTC_ModelSet1_t	 utcModel1;
		UTC_ModelSet2_t	 utcModel2;
		UTC_ModelSet3_t	 utcModel3;
		UTC_ModelSet4_t	 utcModel4;
		/*
		 * This type is extensible,
		 * possible extensions are below.
		 */
		UTC_ModelSet5_r12_t	 utcModel5_r12;
	};
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GNSS_UTC_Model_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GNSS_UTC_Model;

#ifdef __cplusplus
}
#endif

#endif	/* _GNSS_UTC_Model_H_ */
#include <asn_internal.h>
