\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \pcac\psm\src\fatwk_psm.c
\pcac\psm\src\fatwk_psm.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\psm_wrapper.h
\tavor\Arbel\obj_PMD2NONE\inc\psm_wrapper.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\psm.h
\tavor\Arbel\obj_PMD2NONE\inc\psm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hal\UART\inc\UART.h
\hal\UART\inc\UART.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\pmu\inc\pmu.h
\hop\pmu\inc\pmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \diag\diag_logic\src\diag_nvm.h
\diag\diag_logic\src\diag_nvm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\psm_module_def.h
\tavor\Arbel\obj_PMD2NONE\inc\psm_module_def.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\duster_applets.h
\tavor\Arbel\obj_PMD2NONE\inc\duster_applets.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\armv7r\include\alios_type.h
\os\alios\kernel\armv7r\include\alios_type.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\osa_old_api.h
\os\osa\inc\osa_old_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\osa.h
\os\osa\inc\osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\osa_utils.h
\os\osa\inc\osa_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\BSP\inc\bsp_hisr.h
\csw\BSP\inc\bsp_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\nu_xscale\inc\nucleus.h
\os\nu_xscale\inc\nucleus.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\osa_internals.h
\os\osa\inc\osa_internals.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_api.h
\os\alios\kernel\rhino\include\k_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\asr3601\config\k_config.h
\os\alios\asr3601\config\k_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_default_config.h
\os\alios\kernel\rhino\include\k_default_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\armv7r\include\k_types.h
\os\alios\kernel\armv7r\include\k_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\armv7r\include\k_compiler.h
\os\alios\kernel\armv7r\include\k_compiler.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_err.h
\os\alios\kernel\rhino\include\k_err.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_sys.h
\os\alios\kernel\rhino\include\k_sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_critical.h
\os\alios\kernel\rhino\include\k_critical.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_spin_lock.h
\os\alios\kernel\rhino\include\k_spin_lock.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_list.h
\os\alios\kernel\rhino\include\k_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_obj.h
\os\alios\kernel\rhino\include\k_obj.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_sched.h
\os\alios\kernel\rhino\include\k_sched.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_task.h
\os\alios\kernel\rhino\include\k_task.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_ringbuf.h
\os\alios\kernel\rhino\include\k_ringbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_queue.h
\os\alios\kernel\rhino\include\k_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_buf_queue.h
\os\alios\kernel\rhino\include\k_buf_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_sem.h
\os\alios\kernel\rhino\include\k_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_task_sem.h
\os\alios\kernel\rhino\include\k_task_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_mutex.h
\os\alios\kernel\rhino\include\k_mutex.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_timer.h
\os\alios\kernel\rhino\include\k_timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_time.h
\os\alios\kernel\rhino\include\k_time.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_event.h
\os\alios\kernel\rhino\include\k_event.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_stats.h
\os\alios\kernel\rhino\include\k_stats.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_mm_debug.h
\os\alios\kernel\rhino\include\k_mm_debug.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_mm.h
\os\alios\kernel\rhino\include\k_mm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_mm_blk.h
\os\alios\kernel\rhino\include\k_mm_blk.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_mm_region.h
\os\alios\kernel\rhino\include\k_mm_region.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_workqueue.h
\os\alios\kernel\rhino\include\k_workqueue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_internal.h
\os\alios\kernel\rhino\include\k_internal.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_trace.h
\os\alios\kernel\rhino\include\k_trace.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_soc.h
\os\alios\kernel\rhino\include\k_soc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_hook.h
\os\alios\kernel\rhino\include\k_hook.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\rhino\include\k_bitmap.h
\os\alios\kernel\rhino\include\k_bitmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\armv7r\include\port.h
\os\alios\kernel\armv7r\include\port.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\armv7r\include\k_vector.h
\os\alios\kernel\armv7r\include\k_vector.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\armv7r\include\k_cache.h
\os\alios\kernel\armv7r\include\k_cache.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\alios\kernel\armv7r\include\k_mmu.h
\os\alios\kernel\armv7r\include\k_mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\osa_ali.h
\os\osa\inc\osa_ali.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\alios_hisr.h
\os\osa\inc\alios_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\osa\inc\osa_um_extr.h
\os\osa\inc\osa_um_extr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \os\nu_xscale\inc\um_defs.h
\os\nu_xscale\inc\um_defs.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\duster.h
\tavor\Arbel\obj_PMD2NONE\inc\duster.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\FDI\src\INCLUDE\FDI_TYPE.h
\softutil\FDI\src\INCLUDE\FDI_TYPE.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\platform\inc\fdi_cfg.h
\csw\platform\inc\fdi_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\platform\inc\mmap.h
\csw\platform\inc\mmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hal\MMU\inc\mmu.h
\hal\MMU\inc\mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\BSP\inc\bsp.h
\csw\BSP\inc\bsp.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\PM\inc\powerManagement.h
\csw\PM\inc\powerManagement.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\pm\inc\pm_config.h
\hop\pm\inc\pm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\TickManager\inc\tick_manager.h
\softutil\TickManager\inc\tick_manager.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\timer\inc\timer.h
\hop\timer\inc\timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\SysCfg\inc\syscfg.h
\csw\SysCfg\inc\syscfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\platform\inc\hal_cfg.h
\csw\platform\inc\hal_cfg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\timer\inc\timer_config.h
\hop\timer\inc\timer_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\intc_list.h
\hop\intc\inc\intc_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hal\GPIO\inc\gpio_config.h
\hal\GPIO\inc\gpio_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\intc_config.h
\hop\intc\inc\intc_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\intc_list_xirq.h
\hop\intc\inc\intc_list_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\xirq_config.h
\hop\intc\inc\xirq_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hal\GPIO\inc\gpio.h
\hal\GPIO\inc\gpio.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hal\GPIO\inc\cgpio_HW.h
\hal\GPIO\inc\cgpio_HW.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\intc_xirq.h
\hop\intc\inc\intc_xirq.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\intc\inc\intc.h
\hop\intc\inc\intc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\BSP\inc\levante_hw.h
\hop\BSP\inc\levante_hw.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\BSP\inc\levante.h
\hop\BSP\inc\levante.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\BSP\inc\PMChip.h
\csw\BSP\inc\PMChip.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\BSP\inc\loadTable.h
\csw\BSP\inc\loadTable.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\BSP\inc\bsp_config.h
\csw\BSP\inc\bsp_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \csw\BSP\inc\ptable.h
\csw\BSP\inc\ptable.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \hop\BSP\inc\asr_property.h
\hop\BSP\inc\asr_property.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\FDI\src\INCLUDE\FDI_CUST.h
\softutil\FDI\src\INCLUDE\FDI_CUST.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\FDI\src\FDI_ADD\runvars.h
\softutil\FDI\src\FDI_ADD\runvars.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\FDI\src\INCLUDE\FDI_ERR.h
\softutil\FDI\src\INCLUDE\FDI_ERR.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\FDI\src\FDI_ADD\FDI_OS.h
\softutil\FDI\src\FDI_ADD\FDI_OS.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\FDI\src\INCLUDE\FDI_TYPE.h
\softutil\FDI\src\INCLUDE\FDI_TYPE.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\FDI\src\FM_INC\FDI_FILE.h
\softutil\FDI\src\FM_INC\FDI_FILE.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\fatwk_psm.h
\tavor\Arbel\obj_PMD2NONE\inc\fatwk_psm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\inc\platform.h
\tavor\Arbel\inc\platform.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\fatsys\fs\hdr\fattypes.h
\softutil\fatsys\fs\hdr\fattypes.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\fatsys\fs\hdr\timeM.h
\softutil\fatsys\fs\hdr\timeM.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\fatsys\fs\hdr\aptypes.h
\softutil\fatsys\fs\hdr\aptypes.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\softutil\inc\FlashPartition.h
\softutil\softutil\inc\FlashPartition.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\crossPlatformSW.h
\tavor\Arbel\obj_PMD2NONE\inc\crossPlatformSW.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \CrossPlatformSW\nvmClient\inc\FDI2FS_API.h
\CrossPlatformSW\nvmClient\inc\FDI2FS_API.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \CrossPlatformSW\nvmClient\inc\nvmClient.h
\CrossPlatformSW\nvmClient\inc\nvmClient.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\nvm_shared.h
\tavor\Arbel\obj_PMD2NONE\inc\nvm_shared.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \CrossPlatformSW\nvmClient\inc\nvmClient_def.h
\CrossPlatformSW\nvmClient\inc\nvmClient_def.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \CrossPlatformSW\nvmClient\inc\nvmClient_utils.h
\CrossPlatformSW\nvmClient\inc\nvmClient_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \CrossPlatformSW\nvmClient\inc\GenericFS_API.h
\CrossPlatformSW\nvmClient\inc\GenericFS_API.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\softutil\inc\fat32_utils.h
\softutil\softutil\inc\fat32_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\ff.h
\tavor\Arbel\obj_PMD2NONE\inc\ff.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\ffconf.h
\tavor\Arbel\obj_PMD2NONE\inc\ffconf.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \tavor\Arbel\obj_PMD2NONE\inc\ff_dump_mem.h
\tavor\Arbel\obj_PMD2NONE\inc\ff_dump_mem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\fatsys\flash\qspi_nor.h
\softutil\fatsys\flash\qspi_nor.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_psm/fatwk_psm.o : \softutil\fatsys\flash\qspi_flash.h
\softutil\fatsys\flash\qspi_flash.h:
