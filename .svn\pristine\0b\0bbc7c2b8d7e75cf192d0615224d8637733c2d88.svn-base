/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "RRLP-Components"
 * 	found in "rrlp12_1_0.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_AssistanceNeeded_H_
#define	_AssistanceNeeded_H_


#include <asn_application.h>

/* Including external dependencies */
#include "GPSAssistanceData.h"
#include "GANSSAssistanceData.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* AssistanceNeeded */
typedef struct AssistanceNeeded {
	GPSAssistanceData_t	*gpsAssistanceData	/* OPTIONAL */;
	GANSSAssistanceData_t	*ganssAssistanceData	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} AssistanceNeeded_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_AssistanceNeeded;

#ifdef __cplusplus
}
#endif

#endif	/* _AssistanceNeeded_H_ */
#include <asn_internal.h>
