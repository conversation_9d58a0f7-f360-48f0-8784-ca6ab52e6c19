/*====*====*====*====*====*====*====*====*====*====*====*====*====*====*====*

                Guilin.c


GENERAL DESCRIPTION

    This file is for ASR I2C package.

<PERSON>XT<PERSON><PERSON><PERSON><PERSON>ZED FUNCTIONS

INITIALIZATION AND SEQUENCING REQUIREMENTS

   Copyright (c) 2017 by ASR, Incorporated.  All Rights Reserved.
*====*====*====*====*====*====*====*====*====*====*====*====*====*====*====*

*===========================================================================

                        EDIT HISTORY FOR MODULE

  This section contains comments describing changes made to the module.
  Notice that changes are listed in reverse chronological order.


when         who        what, where, why
--------   ------     ----------------------------------------------------------
03/07/2018   Qianying    Created module
===========================================================================*/

/*===========================================================================

                     INCLUDE FILES FOR MODULE

===========================================================================*/

#include "guilin.h"
#include "pmic.h"
#include "UART.h"
#include "diag.h"
#include "ripc.h"
#include "bsp.h"

#if !defined(ONLY_SUPPORT_PM803)
/*===========================================================================

            LOCAL DEFINITIONS AND DECLARATIONS FOR MODULE

This section contains local definitions for constants, macros, types,
variables and other items needed by this module.

===========================================================================*/
Pmic_Int_Handler mGuilinIntHandler [GUILIN_INTC_MAX+1];

/* USIM sleep disable status*/
extern unsigned char VsimSleep_Disable_status;

/* USIM disable status*/
extern unsigned char Vsim_Disable_status;

extern int uart_printf(const char* fmt, ...);

/*===========================================================================

            EXTERN DEFINITIONS AND DECLARATIONS FOR MODULE

===========================================================================*/

/*===========================================================================

                          INTERNAL FUNCTION DEFINITIONS

===========================================================================*/

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      GuilinRead                                                       */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function Read Guilin by PI2C interface.                      */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
int GuilinRead( Guilin_Reg_Type guilin_reg_type, unsigned char reg, unsigned char *value )
{
    int res = 0;
	INT32	result; 

	result = Get_RIPC_ustica(3,200);
	if (result == RIPC_FAILURE)
		ASSERT(0);

    switch( guilin_reg_type )
    {
        case GUILIN_BASE_Reg:
        {
            res = USTICAI2CReadDi_base(reg);
            GUILIN_UART_DEBUG( "[%s] SLAVE=[BASE], REG=[0x%.2x] , VAL=[0x%.2x]", __FUNCTION__,reg,res);
            break;
        }

        case GUILIN_POWER_Reg:
        {
            res = USTICAI2CReadDi_power(reg);
            GUILIN_UART_DEBUG( "[%s] SLAVE=[POWER], REG=[0x%.2x] , VAL=[0x%.2x]", __FUNCTION__,reg,res);
            break;
        }

        case GUILIN_GPADC_Reg:
        {
            res = USTICAI2CReadDi_GPADC(reg);
            GUILIN_UART_DEBUG( "[%s] SLAVE=[GPADC], REG=[0x%.2x] , VAL=[0x%.2x]", __FUNCTION__,reg,res);
            break;
        }

        default:
        {
            GUILIN_UART_DEBUG( "[%s] UNKNOW TARGET REG", __FUNCTION__);
            break;
        }
    }

	Release_RIPC_ustica(3);

    *value = res;

    return 0;
}


/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      GuilinWrite                                                       */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function Write Guilin by PI2C interface.                     */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/*************************************************************************/
int GuilinWrite( Guilin_Reg_Type guilin_reg_type, unsigned char reg, unsigned char value )
{
	INT32	result; 

	result = Get_RIPC_ustica(3,200);
	if (result == RIPC_FAILURE)
		ASSERT(0);

    switch( guilin_reg_type )
    {
        case GUILIN_BASE_Reg:
        {
			USTICAI2CWriteDi_base(reg, value);
            GUILIN_UART_DEBUG( "[%s] SLAVE=[BASE ], REG=[0x%.2x] , VAL=[0x%.2x]", __FUNCTION__,reg,value);
            break;
        }
        case GUILIN_POWER_Reg:
        {
            USTICAI2CWriteDi_power(reg, value);
            GUILIN_UART_DEBUG( "[%s] SLAVE=[POWER] , REG=[0x%.2x] , VAL=[0x%.2x]", __FUNCTION__,reg,value);
            break;
        }
		case GUILIN_GPADC_Reg:
		{
            USTICAI2CWriteDi_GPADC(reg, value);
            GUILIN_UART_DEBUG( "[%s] SLAVE=[GPADC], REG=[0x%.2x] , VAL=[0x%.2x]", __FUNCTION__,reg,value);
			break;
		}
        default:
        {
            GUILIN_UART_DEBUG( "[%s] UNKNOW TARGET REG", __FUNCTION__);
            break;
        }
    }

	Release_RIPC_ustica(3);

    return 0;
}

/////////////////////////////////////////////////////////////////////////////////////////////////////
//enable/disable the  INT report to master processer
static void Guilin_INT_TO_HOST_ENABLE(GUILIN_INTC intc)
{
	UINT8 reg_addr = GUILIN_INTC_TO_ENABLE_REG(intc);
	UINT8 tmp = PMIC_READ_REG_BASE(reg_addr);
	tmp |= GUILIN_INTC_TO_ENABLE_BIT(intc);
	PMIC_WRITE_REG_BASE(reg_addr,tmp);
}
static void Guilin_INT_TO_HOST_DISABLE(GUILIN_INTC intc)
{
	UINT8 reg_addr = GUILIN_INTC_TO_ENABLE_REG(intc);
	UINT8 tmp = PMIC_READ_REG_BASE(reg_addr);
	tmp &= (~(GUILIN_INTC_TO_ENABLE_BIT(intc)));
	PMIC_WRITE_REG_BASE(reg_addr,tmp);
}

void Guilin_INT_DISABLE(GUILIN_INTC intc)
{
	uart_printf("[%s] intc=[%d]\r\n",__func__,intc);
	mGuilinIntHandler[intc].pmic_intc_enabled = FALSE;
	Guilin_INT_TO_HOST_DISABLE(intc);
}

void Guilin_INT_ENABLE(GUILIN_INTC intc)
{
	uart_printf("[%s]  intc=[%d]\r\n",__func__,intc);
	mGuilinIntHandler[intc].pmic_intc_enabled = TRUE;
	Guilin_INT_TO_HOST_ENABLE(intc);
}

void Guilin_INT_CALLBACK_REGISTER(GUILIN_INTC intc,PmicCallback isr)
{
	uart_printf("[%s] intc=[%d] , isr=[%x]\r\n",__func__,intc,isr);
	mGuilinIntHandler[intc].pmic_isr = isr;
}
/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      guilin_read_volt_meas_val                                        */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function Read ADCs with voltage of guilin PMIC.              */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
unsigned short guilin_read_volt_meas_val(unsigned char meaReg)
{
	unsigned short meas_val;
	unsigned char  reg_value[2];
	//unsigned char  status1,status2;

    /* Read two registers, the alignment will be done as follows:
       Register 1 - bits 7:0 => 8 LSB bits <7:0> of measurement value
       Register 2 - bits 3:0 => 4 MSB bits <11:8> of measurement value
    */

	GuilinRead( GUILIN_GPADC_Reg, meaReg, &reg_value[0] );
	GuilinRead( GUILIN_GPADC_Reg, meaReg+1, &reg_value[1] );

	meas_val = (reg_value[1] << 8) | (reg_value[0]);

	return meas_val;
}
/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Guilin_VsimSleep_Enable                                          */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function enable the VSIM sleep mode.                         */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
void Guilin_VsimSleep_Enable(void)
{
	if(VsimSleep_Disable_status==0)
	{
		/* LDO3 off */
		Guilin_LDO_Set_Slpmode(GUILIN_LDO3_SLEEP_MODE_REG,GUILIN_LDO_OFF);
        VsimSleep_Disable_status = 1;
		//MIFI_LOG_TRACE(MIFI, PMIC, Guilin_VsimSleep_Disable, "Guilin Enable Vsim sleep");
	}
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Guilin_VsimSleep_Disable                                         */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function disable the VSIM sleep mode.                        */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
void Guilin_VsimSleep_Disable(void)
{
	if(VsimSleep_Disable_status==1)
	{
        /* LDO3 active */
		Guilin_LDO_Set_Slpmode(GUILIN_LDO3_SLEEP_MODE_REG,GUILIN_LDO_ACTIVE);
		VsimSleep_Disable_status = 0;
		//MIFI_LOG_TRACE(MIFI, PMIC, Guilin_VsimSleep_Disable, "Guilin Disable Vsim sleep");
	}
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Guilin_miccoDisableUsimV                                         */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function disable the USIM voltage.                           */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
void Guilin_miccoDisableUsimV(void)
{

	if(Vsim_Disable_status == 0)
	{
		Guilin_LDO_Set_Enable(GUILIN_LDO3_ENABLE_REG, FALSE);
		Vsim_Disable_status = 1;
		//MIFI_LOG_TRACE(MIFI, PMIC, Guilin_miccoEnableUsimV, "Guilin Disable USIM LDO3");

	}
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Guilin_miccoEnableUsimV                                          */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function enable the USIM voltage.                            */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
//TODO confirm USIM Enable on LDO3 or LDO2
void Guilin_miccoEnableUsimV(void)
{

	if(Vsim_Disable_status==1)
	{

		Guilin_LDO_Set_Enable(GUILIN_LDO3_ENABLE_REG,TRUE);
		Vsim_Disable_status = 0;
		//MIFI_LOG_TRACE(MIFI, PMIC, Guilin_miccoEnableUsimV, "Guilin Enable USIM LDO3");
	}
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      Guilin_miccoConfigUsimV                                          */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function configure the USIM voltage.                         */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
void Guilin_miccoConfigUsimV(unsigned char volatge)
{
    switch(volatge)
    {
        case 0x00:
        {
			Guilin_LDO_Set_VOUT(GUILIN_LDO3_ACTIVE_VOUT_REG,GUILIN_LDO3_ACTIVE_1V80);
			Guilin_LDO_Set_VOUT(GUILIN_LDO3_SLEEP_VOUT_REG,GUILIN_LDO3_SLEEP_1V80);
			//MIFI_LOG_TRACE(MIFI, PMIC, Guilin_miccoConfigUsimV, "Guilin Config Vsim, NORMAL=1.8v, SLEEP=1.8v");
            break;
        }

        case 0x36:
        {
			Guilin_LDO_Set_VOUT(GUILIN_LDO3_ACTIVE_VOUT_REG,GUILIN_LDO3_ACTIVE_2V90);
			Guilin_LDO_Set_VOUT(GUILIN_LDO3_SLEEP_VOUT_REG,GUILIN_LDO3_SLEEP_2V90);
			//MIFI_LOG_TRACE(MIFI, PMIC, Guilin_miccoConfigUsimV, "Guilin Config Vsim, NORMAL=2.9v, SLEEP=2.9v");
            break;
        }

        default:
        {
			Guilin_LDO_Set_VOUT(GUILIN_LDO3_ACTIVE_VOUT_REG,GUILIN_LDO3_ACTIVE_1V80);
			Guilin_LDO_Set_VOUT(GUILIN_LDO3_SLEEP_VOUT_REG,GUILIN_LDO3_SLEEP_1V80);
			//MIFI_LOG_TRACE(MIFI, PMIC, Guilin_miccoConfigUsimV, "Guilin Config Vsim, NORMAL=1.8v, SLEEP=1.8v");
            break;
        }
    }

}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      GuilinDisableWDT                                                 */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function Disable Watchdog of Ustia.                          */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
//BIT_0 = 0	:	WD_ENABLE
//BIT_0 = 1	:	WD_DISABLE
void GuilinDisableWDT( void )
{
    unsigned char var = 0;

    GuilinRead( GUILIN_BASE_Reg, GUILIN_WD_REG, &var );

    /* Disable Watchdog functoin.       */
    var |= GUILIN_WD_DIS;

    GuilinWrite( GUILIN_BASE_Reg, GUILIN_WD_REG, var );
}

/*************************************************************************/
/*                                                                       */
/* FUNCTION                                                              */
/*                                                                       */
/*      GuilinClkInit                                                    */
/*                                                                       */
/* DESCRIPTION                                                           */
/*                                                                       */
/*      The function initialize the Ustia clock.                         */
/*                                                                       */
/* CALLED BY                                                             */
/*                                                                       */
/*      Application                                                      */
/*                                                                       */
/* CALLS                                                                 */
/*                                                                       */
/*      Application                         The application function     */
/*                                                                       */
/* INPUTS                                                                */
/*                                                                       */
/*      None                                N/A                          */
/*                                                                       */
/* OUTPUTS                                                               */
/*                                                                       */
/*      meas_val                            The 12bit ADC value          */
/*                                                                       */
/*************************************************************************/
extern UINT8 check_if_DCS_mode(void);

void GuilinClkInit( void )
{
    unsigned char var = 0;

    if (check_if_DCS_mode())
    {    
    	//switch clk to XO
        GuilinRead( GUILIN_BASE_Reg, GUILIN_RTC_CONTROL_REG, &var );//BP_0xF1[2]
    	var |= GUILIN_RTC_USE_XO;
    	GuilinWrite( GUILIN_BASE_Reg, GUILIN_RTC_CONTROL_REG, var );

        GuilinRead( GUILIN_BASE_Reg, GUILIN_RTC_CTRL_REG, &var );//BP_0xD0[7]=1
    	var |= GUILIN_RTC_USE_XO_BIT;
    	GuilinWrite( GUILIN_BASE_Reg, GUILIN_RTC_CTRL_REG, var );

    	//set clock mux
    	GuilinRead( GUILIN_BASE_Reg, GUILIN_CLK_32K_SEL_REG, &var );//BP,A=0xE4,Val=0x47
    	var |= GUILIN_CLK_32K_SEL;
    	GuilinWrite( GUILIN_BASE_Reg, GUILIN_CLK_32K_SEL_REG, var );

#if 1
    	// NOTICE: for A0/A1 version , the CAP were set outside the chip
    	//         if software config the CAP reg , the 32K clock would
    	//         not start-up, So, the CAP should noly set after B0
    	//
        GuilinRead( GUILIN_BASE_Reg, GUILIN_CRYSTAL_CAP_SET_REG, &var );
        var &= ~0xE0;
        var |= GUILIN_CRYSTAL_CAP_20PF;
        GuilinWrite( GUILIN_BASE_Reg, GUILIN_CRYSTAL_CAP_SET_REG, var );
#endif
    }
    else
    {
        //set clock mux
        GuilinRead( GUILIN_BASE_Reg, GUILIN_CLK_32K_SEL_REG, &var );//BP,A=0xE4,Val=0x43
        var &= ~(0x3<<2); //CLK_32K_OUT=0
        GuilinWrite( GUILIN_BASE_Reg, GUILIN_CLK_32K_SEL_REG, var );

        GuilinRead( GUILIN_BASE_Reg, GUILIN_RTC_CTRL_REG, &var );//BP_0xD0[7]=0
    	var &= ~GUILIN_RTC_USE_XO_BIT;
    	GuilinWrite( GUILIN_BASE_Reg, GUILIN_RTC_CTRL_REG, var );

        //switch clk to internal 32K
        GuilinRead( GUILIN_BASE_Reg, GUILIN_RTC_CONTROL_REG, &var );//BP_0xF1[2]=0
        var &= ~GUILIN_RTC_USE_XO;
        GuilinWrite( GUILIN_BASE_Reg, GUILIN_RTC_CONTROL_REG, var );
    }
}
void Guilin_VBUCK1_Set_FPWM( void )
{
    unsigned char var = 0;

	//set fpwm mode for buck1, power page, @0x25[3]=1
    GuilinRead( GUILIN_POWER_Reg, GUILIN_VBUCK1_FSM_REG4, &var );
	var |= 0x1<<3;
	GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK1_FSM_REG4, var );
}
void Guilin_VBUCK3_Set_FPWM( void )
{
    unsigned char var = 0;

	//set fpwm mode for buck3, power page, @0x45[3]=1
    GuilinRead( GUILIN_POWER_Reg, GUILIN_VBUCK3_FSM_REG4, &var );
	var |= 0x1<<3;
	GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK3_FSM_REG4, var );
}


void Guilin_VBUCK4_Set_FPWM( void )
{
    unsigned char var = 0;

	//set fpwm mode for buck4, power page, @0x55[3]=1
    GuilinRead( GUILIN_POWER_Reg, GUILIN_VBUCK4_FSM_REG4, &var );
	var |= 0x1<<3;
	GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK4_FSM_REG4, var );
}

//VBUCK FUNC
int Guilin_VBUCK_Set_DVC_Enable(unsigned char reg, unsigned char enable){
		unsigned char tmp;

		//keep the ENABLE_BIT[6:0] as previous
		if(GUILIN_CONTAIN_VBUCK_DVC_EN_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			if(enable){
				tmp |= GUILIN_VBUCK_ENABLE_DVC_MASK;
			}else{
				tmp &= ~GUILIN_VBUCK_ENABLE_DVC_MASK;
			}
			return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
		}
		else
		{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}

}



//VBUCK FUNC
int Guilin_VBUCK_Set_Enable(unsigned char reg, unsigned char enable){
		unsigned char tmp;

		//keep the ENABLE_BIT[6:0] as previous
		if(GUILIN_CONTAIN_VBUCK_EN_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			if(enable){
				tmp |= GUILIN_VBUCK_ENABLE_MASK;
			}else{
				tmp &= ~GUILIN_VBUCK_ENABLE_MASK;
			}
			return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
		}
		else
		{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}

}

int Guilin_VBUCK_Set_Slpmode(unsigned char reg, unsigned char mode){
		unsigned char tmp;
		//keep other expect SLP_BIT[4:3]
		if(GUILIN_CONTAIN_VBUCK_SLEEP_MODE_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN_VBUCK_SLEEP_MODE_MASK;
			tmp |= (mode & GUILIN_VBUCK_SLEEP_MODE_MASK);
			return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
		}
		else
		{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}
}


int Guilin_VBUCK_Set_VOUT(unsigned char reg, unsigned char value){
		unsigned char tmp;

		// keep the ENABLE_BIT as previous
		if(GUILIN_CONTAIN_VBUCK_ACTIVE_VOUT_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN_CONTAIN_VBUCK_ACTIVE_VOUT_MASK;
			tmp |= (value & GUILIN_CONTAIN_VBUCK_ACTIVE_VOUT_MASK);
		}
		//keep the DVC_ENABLE_BIT bit as previous
		else if(GUILIN_CONTAIN_VBUCK_SLEEP_VOUT_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN_CONTAIN_VBUCK_SLEEP_VOUT_MASK;
			tmp |= (value & GUILIN_CONTAIN_VBUCK_SLEEP_VOUT_MASK);
		}
		else if(GUILIN_CONTAIN_VBUCK_DVC_VOUT_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN_CONTAIN_VBUCK_DVC_VOUT_MASK;
			tmp |= (value & GUILIN_CONTAIN_VBUCK_DVC_VOUT_MASK);
		}else{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}
		return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
}

//LDO FUNC
int Guilin_LDO_Set_Enable(unsigned char reg, unsigned char enable){
		unsigned char tmp;

		//keep the ENABLE_BIT[5:0] as previous
		if(GUILIN_CONTAIN_LDO_EN_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			if(enable){
				tmp |= GUILIN_LDO_ENABLE_MASK;
			}else{
				tmp &= ~GUILIN_LDO_ENABLE_MASK;
			}
			return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
		}
		else
		{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}
}

int Guilin2_LDO_Set_Enable(unsigned char reg, unsigned char enable){
		unsigned char tmp;

		//keep the ENABLE_BIT[5:0] as previous
		if(GUILIN_CONTAIN_LDO_EN_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			if(enable){
				tmp |= GUILIN2_LDO_ENABLE_MASK;
			}else{
				tmp &= ~GUILIN2_LDO_ENABLE_MASK;
			}
			return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
		}
		else
		{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}
}

int Guilin_LDO_Set_Slpmode(unsigned char reg, unsigned char mode){
		unsigned char tmp;

		//keep the SLP_MODE[3:0]
		if(GUILIN_CONTAIN_LDO_SLEEP_MODE_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			mode &= GUILIN_LDO_SLEEP_MODE_MASK;
			tmp &= ~GUILIN_LDO_SLEEP_MODE_MASK;
			tmp |= mode;
			return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
		}
		else
		{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}
}

int Guilin_LDO_Set_VOUT(unsigned char reg, unsigned char value){
		unsigned char tmp;

		if(GUILIN_CONTAIN_LDO_ACTIVE_VOUT_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN_LDO_ACTIVE_VOUT_MASK;
			tmp |= (value & GUILIN_LDO_ACTIVE_VOUT_MASK);
		}
		else if(GUILIN_CONTAIN_LDO_SLEEP_VOUT_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN_LDO_SLEEP_VOUT_MASK;
			tmp |= (value & GUILIN_LDO_SLEEP_VOUT_MASK);
		}else{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}
		return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
}

int Guilin2_LDO_Set_VOUT(unsigned char reg, unsigned char value){
		unsigned char tmp;

		if(GUILIN_CONTAIN_LDO_ACTIVE_VOUT_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN2_LDO_ACTIVE_VOUT_MASK;
			tmp |= (value & GUILIN2_LDO_ACTIVE_VOUT_MASK);
		}
		else if(GUILIN_CONTAIN_LDO_SLEEP_VOUT_BIT(reg))
		{
			GuilinRead( GUILIN_POWER_Reg, reg, &tmp );
			tmp &= ~GUILIN2_LDO_SLEEP_VOUT_MASK;
			tmp |= (value & GUILIN2_LDO_SLEEP_VOUT_MASK);
		}else{
			GUILIN_UART_DEBUG("[%s] ERROR REG=[0x%.2x]",__FUNCTION__,reg);
			return 1;
		}
		return GuilinWrite( GUILIN_POWER_Reg, reg, tmp );
}


void Guilin_BUCK5_Enable(void)
{
    unsigned char tmp;
    //Buck5 (RF PA)
    Guilin_VBUCK_Set_Slpmode(GUILIN_VBUCK5_SLEEP_MODE_REG,GUILIN_VBUCK_OFF);
    Guilin_VBUCK_Set_VOUT(GUILIN_VBUCK5_ACTIVE_VOUT_REG, GUILIN_VBUCK_1V5000);
    Guilin_VBUCK_Set_Enable(GUILIN_VBUCK5_ENABLE_REG,TRUE);

    GuilinWrite(GUILIN_POWER_Reg, 0x62, 0x64);
    GuilinWrite(GUILIN_POWER_Reg, 0x67, 0x61);

    // Set Buck5 APT mode
	GuilinWrite( GUILIN_POWER_Reg, 0x65, 0x1 );
}

int Guilin_APT_Set(unsigned char enable)
{
	unsigned char tmp;

	GuilinRead( GUILIN_POWER_Reg, GUILIN_APT_REG, &tmp );
	if(enable){
		//set [11] enable APT
		tmp |= GUILIN_APT_ENABLE_MASK;
	}else{
		//set [00] disable APT
		tmp &= ~GUILIN_APT_ENABLE_MASK;
	}
	GUILIN_UART_DEBUG("[%s] APT [%s]",__FUNCTION__,enable?"ENABLE":"DISABLE");

	GuilinWrite( GUILIN_POWER_Reg, GUILIN_APT_REG, tmp );

	return 0;
}

void Guilin_APT_enable(void)
{
    Guilin_APT_Set(TRUE);

    /* Buck5 APT increment speed. */
    if (PMIC_IS_PM802())
    {
    	GuilinWrite( GUILIN_BASE_Reg, 0x16, 0x20 );
    	GuilinWrite( GUILIN_BASE_Reg, 0x17, 0xF );
	}
	else //802S
	{
        GuilinWrite( GUILIN_POWER_Reg, 0x1E, 0x8 );
        GuilinWrite( GUILIN_POWER_Reg, 0x1F, 0xF );
	}
}

//ICAT EXPORTED FUNCTION - PMIC,GUILIN,SW_reset
int Guilin_SW_Reset(void){
	unsigned char tmp;

	//set discharge_time to 0
	GuilinRead( GUILIN_BASE_Reg,  GUILIN_RESET_DISCHARGE_REG , &tmp);
	tmp &= ~GUILIN_RESET_DISCHARGE_MASK;
	GuilinWrite( GUILIN_BASE_Reg, GUILIN_RESET_DISCHARGE_REG, tmp);

	//set fault_wu_en then set fault_wu
	GuilinRead(  GUILIN_BASE_Reg, GUILIN_FAULT_WU_REG, &tmp);
	GuilinWrite( GUILIN_BASE_Reg, GUILIN_FAULT_WU_REG, (tmp|GUILIN_FAULT_WU_ENABLE_BIT));
	GuilinRead(  GUILIN_BASE_Reg, GUILIN_FAULT_WU_REG, &tmp);
	GuilinWrite( GUILIN_BASE_Reg, GUILIN_FAULT_WU_REG, (tmp|GUILIN_FAULT_WU_BIT));

	//force a software powerdown
	GUILIN_UART_DEBUG( "PMIC Reset......");
	GuilinRead(  GUILIN_BASE_Reg, GUILIN_RESET_REG ,&tmp );
	GuilinWrite( GUILIN_BASE_Reg, GUILIN_RESET_REG ,(tmp | GUILIN_SW_PDOWN_BIT));

    return 0;
}

//ICAT EXPORTED FUNCTION - PMIC,GUILIN,Dump_REG
void Guilin_Dump_PMIC_Register(void)
{
    unsigned char var,count;

    GUILIN_UART_DEBUG( "====================================GUILIN BASE PAGE============================");
	for(count=0x01; count<=0xf7; count++){GuilinRead( GUILIN_BASE_Reg, count, &var );GUILIN_UART_DEBUG( "[BASE	]ADDR=[0x%.2x] VAL=[0x%.2x]",count,var); }
    GUILIN_UART_DEBUG( "====================================GUILIN POWER PAGE============================");
	for(count=0x01; count<=0xf7; count++){GuilinRead( GUILIN_POWER_Reg, count, &var );GUILIN_UART_DEBUG( "[POWER	]ADDR=[0x%.2x] VAL=[0x%.2x]",count,var); }
    GUILIN_UART_DEBUG( "====================================GUILIN GPADC PAGE============================");
	for(count=0x01; count<=0xf7; count++){GuilinRead( GUILIN_GPADC_Reg, count, &var );GUILIN_UART_DEBUG( "[GPADC	]ADDR=[0x%.2x] VAL=[0x%.2x]",count,var); }

}

extern unsigned char CRN_SVC_FP[];

void Guilin_Aditional_Workaround(void)
{
/* [1] set pwr_hold bit
   PAGE	: BASE_PAGE
   ADDR	: 0x0D
   VAL	: BIT_7 = 1	*/

    unsigned char var;
    GuilinRead(GUILIN_BASE_Reg, GUILIN_PWR_HOLD_REG, &var);
    GuilinWrite(GUILIN_BASE_Reg, GUILIN_PWR_HOLD_REG, (var|GUILIN_PWR_HOLD_BIT));

    GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK1_FSM_REG3, &var);
    GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK1_FSM_REG3, (var&~(0x1<<7)));//DVC auto for buck1

#ifdef SPI_MUX
    GuilinRead(GUILIN_BASE_Reg, 0xEC, &var);
    GuilinWrite(GUILIN_BASE_Reg, 0xEC, (var|(0x2<<2))); //BP_0xEC[3:2]=0b10, select buck4 switch freq as 2.1M for RF.
#endif

    GuilinRead(GUILIN_GPADC_Reg, 0xA, &var);
    var |= (0x1 << 1); //use GPADC 100K clock when sleep for power save, about 80uA for standby
    GuilinWrite(GUILIN_GPADC_Reg, 0xA, var);

    Guilin_VBUCK_Set_Skip_Mode(1,1); //set buck1 to skip mode for better power ripple

    if (PMIC_IS_PM802())
    {    
        GuilinRead(GUILIN_BASE_Reg, GUILIN_EX_BUCK_FPWM_EN_REG, &var);
        GuilinWrite(GUILIN_BASE_Reg, GUILIN_EX_BUCK_FPWM_EN_REG, (var|0x3));//enable buck1/2 fpwm controlled externally

        GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK1_DVC_VOUT_REG_SET0, CRN_SVC_FP[0] );//416
        GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK1_DVC_VOUT_REG_SET1, CRN_SVC_FP[1] );//499
        GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK1_DVC_VOUT_REG_SET2, CRN_SVC_FP[2] );//624
        GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK1_DVC_VOUT_REG_SET3, CRN_SVC_FP[3] );//832 
    }
    else //802S
    {
        GuilinRead(GUILIN_POWER_Reg, GUILIN2_EX_BUCK_FPWM_EN_REG, &var);
        GuilinWrite(GUILIN_POWER_Reg, GUILIN2_EX_BUCK_FPWM_EN_REG, (var|(0x3<<3)));//enable buck1/2 fpwm controlled externally
        
        GuilinWrite( GUILIN_POWER_Reg, GUILIN2_VBUCK1_DVC_VOUT_REG_SET0, CRN_SVC_FP[0] );//416
        GuilinWrite( GUILIN_POWER_Reg, GUILIN2_VBUCK1_DVC_VOUT_REG_SET1, CRN_SVC_FP[1] );//499
        GuilinWrite( GUILIN_POWER_Reg, GUILIN2_VBUCK1_DVC_VOUT_REG_SET2, CRN_SVC_FP[2] );//624
        GuilinWrite( GUILIN_POWER_Reg, GUILIN2_VBUCK1_DVC_VOUT_REG_SET3, CRN_SVC_FP[3] );//832 
    }
    GuilinRead( GUILIN_POWER_Reg, GUILIN_VBUCK1_SLEEP_VOUT_REG, &var );
    GuilinWrite( GUILIN_POWER_Reg, GUILIN_VBUCK1_SLEEP_VOUT_REG, (var|(0x1<<7))); //enable DVC_buck1

}

void Guilin_VBUCK1_CFG(UINT8 value)
{
    if ((value < GUILIN_VBUCK_0V8000)||(value > GUILIN_VBUCK_1V2000))
    {
        fatal_printf("Wrong buck1 value input!\r\n");
        return;
    }
    Guilin_VBUCK_Set_VOUT(GUILIN_VBUCK1_ACTIVE_VOUT_REG,value);
}

//ICAT EXPORTED FUNCTION - SW_PLAT, PM802, Vcore0V90
void PM802_Buck1_Config0V90(void)
{
    Guilin_VBUCK1_CFG(GUILIN_VBUCK_0V9000);
	DIAG_FILTER(SW_PLAT, PM802, PM802_Buck1_Config0V90, DIAG_INFORMATION)
	diagPrintf ("buck1 set val 0.90V.");
}

//ICAT EXPORTED FUNCTION - SW_PLAT, PM802, Vcore0V95
void PM802_Buck1_Config0V95(void)
{
    Guilin_VBUCK1_CFG(GUILIN_VBUCK_0V9500);
	DIAG_FILTER(SW_PLAT, PM802, PM802_Buck1_Config0V95, DIAG_INFORMATION)
	diagPrintf ("buck1 set val 0.95V.");
}

//ICAT EXPORTED FUNCTION - SW_PLAT, PM802, Vcore1V00
void PM802_Buck1_Config1V00(void)
{
    Guilin_VBUCK1_CFG(GUILIN_VBUCK_1V0000);
	DIAG_FILTER(SW_PLAT, PM802, PM802_Buck1_Config1V00, DIAG_INFORMATION)
	diagPrintf ("buck1 set val 1.00V.");
}

//ICAT EXPORTED FUNCTION - SW_PLAT, PM802, Vcore1V05
void PM802_Buck1_Config1V05(void)
{
    Guilin_VBUCK1_CFG(GUILIN_VBUCK_1V0500);
	DIAG_FILTER(SW_PLAT, PM802, PM802_Buck1_Config1V05, DIAG_INFORMATION)
	diagPrintf ("buck1 set val 1.05V.");
}

//ICAT EXPORTED FUNCTION - SW_PLAT, PM802, Vcore1V10
void PM802_Buck1_Config1V10(void)
{
    Guilin_VBUCK1_CFG(GUILIN_VBUCK_1V1000);
	DIAG_FILTER(SW_PLAT, PM802, PM802_Buck1_Config1V10, DIAG_INFORMATION)
	diagPrintf ("buck1 set val 1.10V.");
}

//ICAT EXPORTED FUNCTION - SW_PLAT, PM802, Vcore1V15
void PM802_Buck1_Config1V15(void)
{
    Guilin_VBUCK1_CFG(GUILIN_VBUCK_1V1500);
	DIAG_FILTER(SW_PLAT, PM802, PM802_Buck1_Config1V15, DIAG_INFORMATION)
	diagPrintf ("buck1 set val 1.15V.");
}

//ICAT EXPORTED FUNCTION - SW_PLAT, PM802, Vcore1V20
void PM802_Buck1_Config1V20(void)
{
    Guilin_VBUCK1_CFG(GUILIN_VBUCK_1V2000);
	DIAG_FILTER(SW_PLAT, PM802, PM802_Buck1_Config1V20, DIAG_INFORMATION)
	diagPrintf ("buck1 set val 1.20V.");
}

void Guilin_Ldo_1_set_2_8(void)
{
    if (PMIC_IS_PM802())
    {
        Guilin_LDO_Set_VOUT(GUILIN_LDO1_ACTIVE_VOUT_REG, GUILIN_LDO1_ACTIVE_2V80);
        DIAG_FILTER(HAL, PM802, PM802_LDO1_2V8, DIAG_INFORMATION)
        diagPrintf("Guilin_Ldo_1_set_2_8");
    }
    else //802S
    {
        Guilin2_LDO_Set_VOUT(GUILIN_LDO1_ACTIVE_VOUT_REG, GUILIN2_LDO1_ACTIVE_2V80);
        DIAG_FILTER(HAL, PM802S, PM802S_LDO1_2V8, DIAG_INFORMATION)
        diagPrintf("Guilin2_Ldo_1_set_2_8");
    }
}

void Guilin_Ldo_1_set(BOOL OnOff)
{
    if (PMIC_IS_PM802())
	    Guilin_LDO_Set_Enable(GUILIN_LDO1_ENABLE_REG,OnOff);
	else //802S
	    Guilin2_LDO_Set_Enable(GUILIN_LDO1_ENABLE_REG,OnOff);
}

void Guilin_Ldo_3_set_1_8(void)
{
    if (PMIC_IS_PM802())
    {
    	Guilin_LDO_Set_VOUT(GUILIN_LDO3_ACTIVE_VOUT_REG,GUILIN_LDO3_ACTIVE_1V80);
    	DIAG_FILTER(HAL, PM802, PM802_LDO3_1V8, DIAG_INFORMATION)
    	diagPrintf("Guilin_Ldo_3_set_1_8");
	}
	else //802S
	{
    	Guilin2_LDO_Set_VOUT(GUILIN_LDO3_ACTIVE_VOUT_REG,GUILIN2_LDO3_ACTIVE_1V80);
    	DIAG_FILTER(HAL, PM802S, PM802S_LDO3_1V8, DIAG_INFORMATION)
    	diagPrintf("Guilin2_Ldo_3_set_1_8");
	}
}

void Guilin_Ldo_3_set_3_0(void)
{
    if (PMIC_IS_PM802())
    {
        Guilin_LDO_Set_VOUT(GUILIN_LDO3_ACTIVE_VOUT_REG, GUILIN_LDO3_ACTIVE_3V00);
        DIAG_FILTER(HAL, PM802, PM802_LDO3_3V0, DIAG_INFORMATION)
        diagPrintf("Guilin_Ldo_3_set_3_0");
    }
    else //802S
    {
        Guilin2_LDO_Set_VOUT(GUILIN_LDO3_ACTIVE_VOUT_REG, GUILIN2_LDO3_ACTIVE_3V00);
        DIAG_FILTER(HAL, PM802S, PM802S_LDO3_3V0, DIAG_INFORMATION)
        diagPrintf("Guilin2_Ldo_3_set_3_0");
    }
}

void Guilin_Ldo_3_set(BOOL OnOff)
{
    if (PMIC_IS_PM802())    
	    Guilin_LDO_Set_Enable(GUILIN_LDO3_ENABLE_REG,OnOff);
	else //802S
	    Guilin2_LDO_Set_Enable(GUILIN_LDO3_ENABLE_REG,OnOff);
}   

void Guilin_Ldo_4_set_1_8(void)
{
    if (PMIC_IS_PM802())    
    {
    	Guilin_LDO_Set_VOUT(GUILIN_LDO4_ACTIVE_VOUT_REG,GUILIN_LDO4_ACTIVE_1V80);
    	DIAG_FILTER(HAL, PM802, PM802_LDO4_1V8, DIAG_INFORMATION)
    	diagPrintf("Guilin_Ldo_4_set_1_8");
	}
	else //802S
	{
    	Guilin2_LDO_Set_VOUT(GUILIN_LDO4_ACTIVE_VOUT_REG,GUILIN2_LDO4_ACTIVE_1V80);
    	DIAG_FILTER(HAL, PM802S, PM802S_LDO4_1V8, DIAG_INFORMATION)
    	diagPrintf("Guilin2_Ldo_4_set_1_8");
	}
}

void Guilin_Ldo_4_set_3_0(void)
{
    if (PMIC_IS_PM802())    
    {
        Guilin_LDO_Set_VOUT(GUILIN_LDO4_ACTIVE_VOUT_REG, GUILIN_LDO4_ACTIVE_3V00);
        DIAG_FILTER(HAL, PM802, PM802_LDO4_3V0, DIAG_INFORMATION)
        diagPrintf("Guilin_Ldo_4_set_3_0");
    }
    else //802S
    {
        Guilin2_LDO_Set_VOUT(GUILIN_LDO4_ACTIVE_VOUT_REG, GUILIN2_LDO4_ACTIVE_3V00);
        DIAG_FILTER(HAL, PM802S, PM802S_LDO4_3V0, DIAG_INFORMATION)
        diagPrintf("Guilin2_Ldo_4_set_3_0");
    }
}

void Guilin_Ldo_4_set(BOOL OnOff)
{
    if (PMIC_IS_PM802())    
	    Guilin_LDO_Set_Enable(GUILIN_LDO4_ENABLE_REG,OnOff);
	else //802S
        Guilin2_LDO_Set_Enable(GUILIN_LDO4_ENABLE_REG,OnOff);
}

void Guilin_Ldo_6_set_1_8(void)
{
    if (PMIC_IS_PM802())    
    {
    	Guilin_LDO_Set_VOUT(GUILIN_LDO6_ACTIVE_VOUT_REG,GUILIN_LDO6_ACTIVE_1V80);
    	DIAG_FILTER(HAL, PM802, PM802_LDO8_1V8, DIAG_INFORMATION)
    	diagPrintf("Guilin_Ldo_6_set_1_8");
	}
	else //802S
	{
        Guilin2_LDO_Set_VOUT(GUILIN_LDO6_ACTIVE_VOUT_REG,GUILIN2_LDO6_ACTIVE_1V80);
        DIAG_FILTER(HAL, PM802S, PM802S_LDO8_1V8, DIAG_INFORMATION)
        diagPrintf("Guilin2_Ldo_6_set_1_8");
	}
}

void Guilin_Ldo_6_set_2_8(void)
{
    if (PMIC_IS_PM802())    
    {
        Guilin_LDO_Set_VOUT(GUILIN_LDO6_ACTIVE_VOUT_REG, GUILIN_LDO6_ACTIVE_2V80);
        DIAG_FILTER(HAL, PM802, PM802_LDO8_2V8, DIAG_INFORMATION)
        diagPrintf("Guilin_Ldo_6_set_2_8");
    }
    else //802S
    {
        Guilin2_LDO_Set_VOUT(GUILIN_LDO6_ACTIVE_VOUT_REG, GUILIN2_LDO6_ACTIVE_2V80);
        DIAG_FILTER(HAL, PM802S, PM802S_LDO8_2V8, DIAG_INFORMATION)
        diagPrintf("Guilin2_Ldo_6_set_2_8");
    }
}

void Guilin_Ldo_6_set_3_3(void)
{
    if (PMIC_IS_PM802())    
    {
        Guilin_LDO_Set_VOUT(GUILIN_LDO6_ACTIVE_VOUT_REG, GUILIN_LDO6_ACTIVE_3V30);
        DIAG_FILTER(HAL, PM802, PM802_LDO8_3V3, DIAG_INFORMATION)
        diagPrintf("Guilin_Ldo_6_set_3_3");
    }
    else //802S
    {
        Guilin2_LDO_Set_VOUT(GUILIN_LDO6_ACTIVE_VOUT_REG, GUILIN2_LDO6_ACTIVE_3V30);
        DIAG_FILTER(HAL, PM802S, PM802S_LDO8_3V3, DIAG_INFORMATION)
        diagPrintf("Guilin2_Ldo_6_set_3_3");
    }
}

void Guilin_Ldo_6_set(BOOL OnOff)
{
    if (PMIC_IS_PM802())    
    	Guilin_LDO_Set_Enable(GUILIN_LDO6_ENABLE_REG,OnOff);
	else //802S
    	Guilin2_LDO_Set_Enable(GUILIN_LDO6_ENABLE_REG,OnOff);
}

void Guilin_PowerSave_Config(void)
{
	volatile unsigned char val;

	// VBUCK1 sleep voltage 0.7v
	GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK1_SLEEP_MODE_REG, (unsigned char *)&val);
	val &= ~(0x3 << 3);
	val |= (0x2 << 3);
	GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK1_SLEEP_MODE_REG, val);

	GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK1_SLEEP_VOUT_REG, (unsigned char *)&val);
	val &= ~0x7F;
	val |= CHIP_IS_CRANE? GUILIN_VBUCK_0V7000: GUILIN_VBUCK_0V6500 /*CraneG/M*/;
	GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK1_SLEEP_VOUT_REG, val);	

    Guilin_VBUCK_Set_Slpmode(GUILIN_VBUCK4_SLEEP_MODE_REG,GUILIN_VBUCK_SLEEP);
    Guilin_VBUCK_Set_VOUT(GUILIN_VBUCK4_SLEEP_VOUT_REG, GUILIN_VBUCK_1V80); //set buck4 sleep mode to save power.

    Guilin_VBUCK_Set_Enable(GUILIN_VBUCK3_ENABLE_REG,FALSE); //switch off buck3, no use so far
	
#if 0 //for SD card
	GuilinRead(GUILIN_POWER_Reg, GUILIN_LDO4_SLEEP_MODE_REG, &val);
	val &= ~(0x3 << 4);
	GuilinWrite(GUILIN_POWER_Reg, GUILIN_LDO4_SLEEP_MODE_REG, val);

	GuilinRead(GUILIN_POWER_Reg, GUILIN_LDO6_SLEEP_MODE_REG, &val);
	val &= ~(0x3 << 4);
	GuilinWrite(GUILIN_POWER_Reg, GUILIN_LDO6_SLEEP_MODE_REG, val);		
#endif

}

void Guilin_VBUCK_Set_Skip_Mode(unsigned char buck, unsigned char OnOff)
{
    unsigned char tmp, tmp1;
    switch (buck)
    {
        case 1:
            GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK1_FSM_REG1, &tmp);
            if (OnOff)
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK1_FSM_REG1, (tmp|GUILIN_VBUCK_SKIP_MODE));
            else
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK1_FSM_REG1, (tmp&~GUILIN_VBUCK_SKIP_MODE));
            break;
        case 2:
            GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK2_FSM_REG1, &tmp);
            if (OnOff)
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK2_FSM_REG1, (tmp|GUILIN_VBUCK_SKIP_MODE));
            else
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK2_FSM_REG1, (tmp&~GUILIN_VBUCK_SKIP_MODE));            
            break;
        case 3:
            GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK3_FSM_REG1, &tmp);
            if (OnOff)
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK3_FSM_REG1, (tmp|GUILIN_VBUCK_SKIP_MODE));
            else
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK3_FSM_REG1, (tmp&~GUILIN_VBUCK_SKIP_MODE));            
            break;
        case 4:
            GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK4_FSM_REG1, &tmp);
            if (OnOff)
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK4_FSM_REG1, (tmp|GUILIN_VBUCK_SKIP_MODE));
            else
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK4_FSM_REG1, (tmp&~GUILIN_VBUCK_SKIP_MODE));            
            break;
        case 5:
            GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK5_FSM_REG4, &tmp);
            GuilinRead(GUILIN_POWER_Reg, GUILIN_VBUCK5_FSM_REG1, &tmp1);
            if (OnOff)
            {
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK5_FSM_REG4, (tmp&~0x1));//not auto mode
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK5_FSM_REG1, (tmp1&~(0x1<<6)));//not apt mode
            }
            else
            {
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK5_FSM_REG4, (tmp|0x1));            
                GuilinWrite(GUILIN_POWER_Reg, GUILIN_VBUCK5_FSM_REG1, (tmp1|(0x1<<6)));            
            }
            break;
        default:
            uart_printf("wrong Guilin(2) buck number!\r\n");
            return;
    }
}

static UINT8 guilinLcdLightEnabled = FALSE;
static UINT8 guilinLcdLightLevel = 0xFF;

void GuilinLcdBackLightStatusRecord(UINT8 status)
{
    guilinLcdLightEnabled = status;
}

void GuilinLcdBackLightLevelRecord(UINT8 level) 
{
    guilinLcdLightLevel = level;
}

UINT8 PM802_LcdBacklight_Status_Get(void)
{
    return guilinLcdLightEnabled;
}

UINT8 PM802_LcdBacklight_Level_Get(void)
{
    return guilinLcdLightLevel;
}

#define VOLT_CONVERT_12BIT_MV(meas) ((meas)*(1300*128)/(129*4096)) //(CODE)/4096*1.3*128/129 *1000 ->mv, CODE is the adc output, 12bit

UINT32 pm802_get_battery_voltage(void)
{
    UINT32 meas_val;
    UINT8 batMeas, adcTrig;

	//enable BAT MEAS
	batMeas = PMIC_READ_REG_GPADC(GUILIN_GPADC_MEAS_EN_REG2);
	batMeas |= GUILIN_VINLDO_MEAS_EN;
	PMIC_WRITE_REG_GPADC(GUILIN_GPADC_MEAS_EN_REG2,batMeas);

	/*trigger meas, use non-stop instead of single-trigger, 
	since the AVG value can be correct only after the first four times of read of single-trigger. */
	adcTrig = PMIC_READ_REG_GPADC(GUILIN_GPADC_MISC_CONFIG_REG2);
	adcTrig |= (GUILIN_NON_STOP|GUILIN_GPADC_EN); 
	PMIC_WRITE_REG_GPADC(GUILIN_GPADC_MISC_CONFIG_REG2,adcTrig);

	//get VBAT meas 
    meas_val = guilin_read_volt_meas_val(GUILIN_VINLDO_AVE_REG);
    //Voltage=hex2dec(CODE)/4096*1.3*128/129 (unit: V)(CODE is the adc output, 12bit)
    meas_val = VOLT_CONVERT_12BIT_MV(meas_val)*5/* to be confirmed about the divider */; 

    //disable non-stop to save power
	adcTrig &= ~(GUILIN_NON_STOP|GUILIN_GPADC_EN); 
	PMIC_WRITE_REG_GPADC(GUILIN_GPADC_MISC_CONFIG_REG2,adcTrig);
    
	//disable BAT MEAS
	batMeas &= ~GUILIN_VINLDO_MEAS_EN;
	PMIC_WRITE_REG_GPADC(GUILIN_GPADC_MEAS_EN_REG2,batMeas);

	return meas_val;
}

#else //!defined(ONLY_SUPPORT_PM803)
int GuilinRead( Guilin_Reg_Type guilin_reg_type, unsigned char reg, unsigned char *value )
{
    return 0;
}
int GuilinWrite( Guilin_Reg_Type guilin_reg_type, unsigned char reg, unsigned char value )
{
    return 0;
}
void Guilin_Ldo_1_set_2_8(void)
{
}
void Guilin_Ldo_1_set(BOOL OnOff)
{
}
void Guilin_Ldo_6_set_2_8(void)
{
}
void Guilin_Ldo_6_set(BOOL OnOff)
{
}

static UINT8 guilinLcdLightEnabled = FALSE;
static UINT8 guilinLcdLightLevel = 0xFF;

void GuilinLcdBackLightStatusRecord(UINT8 status)
{
    guilinLcdLightEnabled = status;
}

void GuilinLcdBackLightLevelRecord(UINT8 level) 
{
    guilinLcdLightLevel = level;
}

UINT8 PM802_LcdBacklight_Status_Get(void)
{
    return guilinLcdLightEnabled;
}

UINT8 PM802_LcdBacklight_Level_Get(void)
{
    return guilinLcdLightLevel;
}

#endif

