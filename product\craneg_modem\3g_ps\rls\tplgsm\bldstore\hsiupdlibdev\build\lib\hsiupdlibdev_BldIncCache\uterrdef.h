/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/utinc/uterrdef.h#6 $
 *   $Revision: #6 $
 *   $DateTime: 2006/02/09 12:25:17 $
 **************************************************************************
 * File Description:
 *
 * macros to define how to interpret the ERROR_DEF macro
 **************************************************************************/

/* 
ERROR_VAR should be defined to be ERROR_TYPE, ERROR_SIZE or ERROR_STRUCT
by the file which includes this file
*/
#define ERROR_TYPE 1
#define ERROR_SIZE 2
#define ERROR_STRUCT 3

#if defined (ERROR_DEF)
# undef ERROR_DEF
#endif

#if ERROR_VAR == ERROR_TYPE
# define ERROR_DEF(errType, errSize, errStruct) errType,
#endif

#if ERROR_VAR == ERROR_SIZE
# define ERROR_DEF(errType, errSize, errStruct) errSize,
#endif

#if ERROR_VAR == ERROR_STRUCT
# define ERROR_DEF(errType, errSize, errStruct) errStruct;
#endif


#if !defined (ERROR_DEF)
# error ERROR_DEF must be defined
#endif

/* prepare for this file to be included in another file */
#undef ERROR_VAR


/* END OF FILE */
