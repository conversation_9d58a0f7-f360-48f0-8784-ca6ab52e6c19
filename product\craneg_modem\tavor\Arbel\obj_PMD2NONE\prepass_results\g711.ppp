# 1 "\\aud_sw\\AudioService\\src\\g711.c"
/*
 * SpanDSP - a series of DSP components for telephony
 *
 * g711.c - A-law and u-law transcoding routines
 *
 * Written by <PERSON> <<EMAIL>>
 *
 * Copyright (C) 2006 <PERSON>
 *
 *  Despite my general liking of the GPL, I place this code in the
 *  public domain for the benefit of all mankind - even the slimy
 *  ones who might try to proprietize my work and use it to my
 *  detriment.
 *
 * $Id: g711.c,v 1.1 2006/06/07 15:46:39 steveu Exp $
 *
 * Modifications for WebRtc, 2011/04/28, by tlegrand:
 * -Removed unused include files
 * -Changed to use WebRtc types
 * -Added option to run encoder bitexact with ITU-T reference implementation
 */

# 1 "\\aud_sw\\AudioService\\src\\g711.h"
/*
 * SpanDSP - a series of DSP components for telephony
 *
 * g711.h - In line A-law and u-law conversion routines
 *
 * Written by <PERSON> <<EMAIL>>
 *
 * Copyright (C) 2001 <PERSON>
 *
 *  Despite my general liking of the GPL, I place this code in the
 *  public domain for the benefit of all mankind - even the slimy
 *  ones who might try to proprietize my work and use it to my
 *  detriment.
 *
 * $Id: g711.h,v 1.1 2006/06/07 15:46:39 steveu Exp $
 *
 * Modifications for WebRtc, 2011/04/28, by tlegrand:
 * -Changed to use WebRtc types
 * -Changed __inline__ to __inline
 * -Two changes to make implementation bitexact with ITU-T reference
 * implementation
 */

/*! \page g711_page A-law and mu-law handling
Lookup tables for A-law and u-law look attractive, until you consider the impact
on the CPU cache. If it causes a substantial area of your processor cache to get
hit too often, cache sloshing will severely slow things down. The main reason
these routines are slow in C, is the lack of direct access to the CPU's "find
the first 1" instruction. A little in-line assembler fixes that, and the
conversion routines can be faster than lookup tables, in most real world usage.
A "find the first 1" instruction is available on most modern CPUs, and is a
much underused feature.

If an assembly language method of bit searching is not available, these routines
revert to a method that can be a little slow, so the cache thrashing might not
seem so bad :(

Feel free to submit patches to add fast "find the first 1" support for your own
favourite processor.

Look up tables are used for transcoding between A-law and u-law, since it is
difficult to achieve the precise transcoding procedure laid down in the G.711
specification by other means.
*/








# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 54 "\\aud_sw\\AudioService\\src\\g711.h"

# 106 "\\aud_sw\\AudioService\\src\\g711.h"
static __inline int top_bit(unsigned int bits) {
  int i;

  if (bits == 0) {
    return -1;
  }
  i = 0;
  if (bits & 0xFFFF0000) {
    bits &= 0xFFFF0000;
    i += 16;
  }
  if (bits & 0xFF00FF00) {
    bits &= 0xFF00FF00;
    i += 8;
  }
  if (bits & 0xF0F0F0F0) {
    bits &= 0xF0F0F0F0;
    i += 4;
  }
  if (bits & 0xCCCCCCCC) {
    bits &= 0xCCCCCCCC;
    i += 2;
  }
  if (bits & 0xAAAAAAAA) {
    bits &= 0xAAAAAAAA;
    i += 1;
  }
  return i;
}

static __inline int bottom_bit(unsigned int bits) {
  int i;

  if (bits == 0) {
    return -1;
  }
  i = 32;
  if (bits & 0x0000FFFF) {
    bits &= 0x0000FFFF;
    i -= 16;
  }
  if (bits & 0x00FF00FF) {
    bits &= 0x00FF00FF;
    i -= 8;
  }
  if (bits & 0x0F0F0F0F) {
    bits &= 0x0F0F0F0F;
    i -= 4;
  }
  if (bits & 0x33333333) {
    bits &= 0x33333333;
    i -= 2;
  }
  if (bits & 0x55555555) {
    bits &= 0x55555555;
    i -= 1;
  }
  return i;
}


/* N.B. It is tempting to use look-up tables for A-law and u-law conversion.
 *      However, you should consider the cache footprint.
 *
 *      A 64K byte table for linear to x-law and a 512 byte table for x-law to
 *      linear sound like peanuts these days, and shouldn't an array lookup be
 *      real fast? No! When the cache sloshes as badly as this one will, a tight
 *      calculation may be better. The messiest part is normally finding the
 *      segment, but a little inline assembly can fix that on an i386, x86_64
 * and many other modern processors.
 */

/*
 * Mu-law is basically as follows:
 *
 *      Biased Linear Input Code        Compressed Code
 *      ------------------------        ---------------
 *      00000001wxyza                   000wxyz
 *      0000001wxyzab                   001wxyz
 *      000001wxyzabc                   010wxyz
 *      00001wxyzabcd                   011wxyz
 *      0001wxyzabcde                   100wxyz
 *      001wxyzabcdef                   101wxyz
 *      01wxyzabcdefg                   110wxyz
 *      1wxyzabcdefgh                   111wxyz
 *
 * Each biased linear code has a leading 1 which identifies the segment
 * number. The value of the segment number is equal to 7 minus the number
 * of leading 0's. The quantization interval is directly available as the
 * four bits wxyz.  * The trailing bits (a - h) are ignored.
 *
 * Ordinarily the complement of the resulting code word is used for
 * transmission, and so the code word is complemented before it is returned.
 *
 * For further information see John C. Bellamy's Digital Telephony, 1982,
 * John Wiley & Sons, pps 98-111 and 472-476.
 */

//#define ULAW_ZEROTRAP                 /* turn on the trap as per the MIL-STD
//*/


/*! \brief Encode a linear sample to u-law
    \param linear The sample to encode.
    \return The u-law value.
*/
static __inline uint8_t linear_to_ulaw(int linear) {
  uint8_t u_val;
  int mask;
  int seg;

  /* Get the sign and the magnitude of the value. */
  if (linear < 0) {
    /* WebRtc, tlegrand: -1 added to get bitexact to reference implementation */
    linear = 0x84 - linear - 1;
    mask = 0x7F;
  } else {
    linear = 0x84 + linear;
    mask = 0xFF;
  }

  seg = top_bit(linear | 0xFF) - 7;

  /*
   * Combine the sign, segment, quantization bits,
   * and complement the code word.
   */
  if (seg >= 8)
    u_val = (uint8_t)(0x7F ^ mask);
  else
    u_val = (uint8_t)(((seg << 4) | ((linear >> (seg + 3)) & 0xF)) ^ mask);





  return u_val;
}

/*! \brief Decode an u-law sample to a linear value.
    \param ulaw The u-law sample to decode.
    \return The linear value.
*/
static __inline int16_t ulaw_to_linear(uint8_t ulaw) {
  int t;

  /* Complement to obtain normal u-law value. */
  ulaw = ~ulaw;
  /*
   * Extract and bias the quantization bits. Then
   * shift up by the segment number and subtract out the bias.
   */
  t = (((ulaw & 0x0F) << 3) + 0x84) << (((int)ulaw & 0x70) >> 4);
  return (int16_t)((ulaw & 0x80) ? (0x84 - t) : (t - 0x84));
}

/*
 * A-law is basically as follows:
 *
 *      Linear Input Code        Compressed Code
 *      -----------------        ---------------
 *      0000000wxyza             000wxyz
 *      0000001wxyza             001wxyz
 *      000001wxyzab             010wxyz
 *      00001wxyzabc             011wxyz
 *      0001wxyzabcd             100wxyz
 *      001wxyzabcde             101wxyz
 *      01wxyzabcdef             110wxyz
 *      1wxyzabcdefg             111wxyz
 *
 * For further information see John C. Bellamy's Digital Telephony, 1982,
 * John Wiley & Sons, pps 98-111 and 472-476.
 */



/*! \brief Encode a linear sample to A-law
    \param linear The sample to encode.
    \return The A-law value.
*/
static __inline uint8_t linear_to_alaw(int linear) {
  int mask;
  int seg;

  if (linear >= 0) {
    /* Sign (bit 7) bit = 1 */
    mask = 0x55 | 0x80;
  } else {
    /* Sign (bit 7) bit = 0 */
    mask = 0x55;
    /* WebRtc, tlegrand: Changed from -8 to -1 to get bitexact to reference
     * implementation */
    linear = -linear - 1;
  }

  /* Convert the scaled magnitude to segment number. */
  seg = top_bit(linear | 0xFF) - 7;
  if (seg >= 8) {
    if (linear >= 0) {
      /* Out of range. Return maximum value. */
      return (uint8_t)(0x7F ^ mask);
    }
    /* We must be just a tiny step below zero */
    return (uint8_t)(0x00 ^ mask);
  }
  /* Combine the sign, segment, and quantization bits. */
  return (uint8_t)(((seg << 4) | ((linear >> ((seg) ? (seg + 3) : 4)) & 0x0F)) ^
                   mask);
}

/*! \brief Decode an A-law sample to a linear value.
    \param alaw The A-law sample to decode.
    \return The linear value.
*/
static __inline int16_t alaw_to_linear(uint8_t alaw) {
  int i;
  int seg;

  alaw ^= 0x55;
  i = ((alaw & 0x0F) << 4);
  seg = (((int)alaw & 0x70) >> 4);
  if (seg)
    i = (i + 0x108) << (seg - 1);
  else
    i += 8;
  return (int16_t)((alaw & 0x80) ? i : -i);
}

/*! \brief Transcode from A-law to u-law, using the procedure defined in G.711.
    \param alaw The A-law sample to transcode.
    \return The best matching u-law value.
*/
uint8_t alaw_to_ulaw(uint8_t alaw);

/*! \brief Transcode from u-law to A-law, using the procedure defined in G.711.
    \param alaw The u-law sample to transcode.
    \return The best matching A-law value.
*/
uint8_t ulaw_to_alaw(uint8_t ulaw);





# 24 "\\aud_sw\\AudioService\\src\\g711.c"

/* Copied from the CCITT G.711 specification */
static const uint8_t ulaw_to_alaw_table[256] = {
   42,  43,  40,  41,  46,  47,  44,  45,  34,  35,  32,  33,  38,  39,  36,
   37,  58,  59,  56,  57,  62,  63,  60,  61,  50,  51,  48,  49,  54,  55,
   52,  53,  10,  11,   8,   9,  14,  15,  12,  13,   2,   3,   0,   1,   6,
    7,   4,  26,  27,  24,  25,  30,  31,  28,  29,  18,  19,  16,  17,  22,
   23,  20,  21, 106, 104, 105, 110, 111, 108, 109,  98,  99,  96,  97, 102,
  103, 100, 101, 122, 120, 126, 127, 124, 125, 114, 115, 112, 113, 118, 119,
  116, 117,  75,  73,  79,  77,  66,  67,  64,  65,  70,  71,  68,  69,  90,
   91,  88,  89,  94,  95,  92,  93,  82,  82,  83,  83,  80,  80,  81,  81,
   86,  86,  87,  87,  84,  84,  85,  85, 170, 171, 168, 169, 174, 175, 172,
  173, 162, 163, 160, 161, 166, 167, 164, 165, 186, 187, 184, 185, 190, 191,
  188, 189, 178, 179, 176, 177, 182, 183, 180, 181, 138, 139, 136, 137, 142,
  143, 140, 141, 130, 131, 128, 129, 134, 135, 132, 154, 155, 152, 153, 158,
  159, 156, 157, 146, 147, 144, 145, 150, 151, 148, 149, 234, 232, 233, 238,
  239, 236, 237, 226, 227, 224, 225, 230, 231, 228, 229, 250, 248, 254, 255,
  252, 253, 242, 243, 240, 241, 246, 247, 244, 245, 203, 201, 207, 205, 194,
  195, 192, 193, 198, 199, 196, 197, 218, 219, 216, 217, 222, 223, 220, 221,
  210, 210, 211, 211, 208, 208, 209, 209, 214, 214, 215, 215, 212, 212, 213,
  213
};

/* These transcoding tables are copied from the CCITT G.711 specification. To
   achieve optimal results, do not change them. */
static const uint8_t alaw_to_ulaw_table[256] = {
   42,  43,  40,  41,  46,  47,  44,  45,  34,  35,  32,  33,  38,  39,  36,
   37,  57,  58,  55,  56,  61,  62,  59,  60,  49,  50,  47,  48,  53,  54,
   51,  52,  10,  11,   8,   9,  14,  15,  12,  13,   2,   3,   0,   1,   6,
    7,   4,   5,  26,  27,  24,  25,  30,  31,  28,  29,  18,  19,  16,  17,
   22,  23,  20,  21,  98,  99,  96,  97, 102, 103, 100, 101,  93,  93,  92,
   92,  95,  95,  94,  94, 116, 118, 112, 114, 124, 126, 120, 122, 106, 107,
  104, 105, 110, 111, 108, 109,  72,  73,  70,  71,  76,  77,  74,  75,  64,
   65,  63,  63,  68,  69,  66,  67,  86,  87,  84,  85,  90,  91,  88,  89,
   79,  79,  78,  78,  82,  83,  80,  81, 170, 171, 168, 169, 174, 175, 172,
  173, 162, 163, 160, 161, 166, 167, 164, 165, 185, 186, 183, 184, 189, 190,
  187, 188, 177, 178, 175, 176, 181, 182, 179, 180, 138, 139, 136, 137, 142,
  143, 140, 141, 130, 131, 128, 129, 134, 135, 132, 133, 154, 155, 152, 153,
  158, 159, 156, 157, 146, 147, 144, 145, 150, 151, 148, 149, 226, 227, 224,
  225, 230, 231, 228, 229, 221, 221, 220, 220, 223, 223, 222, 222, 244, 246,
  240, 242, 252, 254, 248, 250, 234, 235, 232, 233, 238, 239, 236, 237, 200,
  201, 198, 199, 204, 205, 202, 203, 192, 193, 191, 191, 196, 197, 194, 195,
  214, 215, 212, 213, 218, 219, 216, 217, 207, 207, 206, 206, 210, 211, 208,
  209
};

uint8_t alaw_to_ulaw(uint8_t alaw) { return alaw_to_ulaw_table[alaw]; }

uint8_t ulaw_to_alaw(uint8_t ulaw) { return ulaw_to_alaw_table[ulaw]; }
