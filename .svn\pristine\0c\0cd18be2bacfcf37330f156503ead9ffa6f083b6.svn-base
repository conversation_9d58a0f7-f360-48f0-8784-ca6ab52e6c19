/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/***********************************************************************
File name:  pl_msr_intra_csm.h
==========
************************************************************************
Changes:
========
Date          Name            Description
=====         ===========     ==========================================
03-Sep-2006  rnir                      Initial version

***********************************************************************/
#ifndef       msr_intra_csm
#define       msr_intra_csm


#include "pl_msr_db.h"
#include "pl_msr_cse.h"
/**********************************************************************/
/* Definitions                                                        */
/**********************************************************************/
/**********************************************************************/
/* Enumerators  
*/
/**********************************************************************/
/* type definition                                                    */

//ICAT EXPORTED STRUCT
typedef struct idleModeCseBitmap
{
    /* These bitmaps keep tracks of the state of the neighbour cells during Idle*/
    cdbMap_ts IdleNbr4bssBmap;
    cdbMap_ts IdleNbr4mpsBmap;
}imCseBmap_ts;

/**********************************************************************/
/*                              Services prototypes                   */
/**********************************************************************/
void plMsrIntraCsmNoSleepCellSearchReplyFound (UINT8 dbCellIndex,BOOL anchorMoved);
void plMsrIntraCsmNoSleepCpichSearchReply (UINT8 dbCellIndex,SearchReplyStatus_ts *ReplyStatus);
void plMsrIntraCsmNoSleepAddCellToSearch (UINT8 dbCellIndex,RestoringParameters *pRestoringParams,triggeringOptions_ts *pTriggerOpt);
void plMsrIntraCsmNoSleepRemoveCellFromSearch (UINT8 dbCellIndex,triggeringOptions_ts *pTriggerOpt);
UINT8 plMsrIntraCsmNoSleepExtendedCpichSearchReply (UINT8 dbCellIndex,Bool SearchPassed);
void plMsrIntraCsmInSleepAddCellToSearchOnDRX_MSR_WAKEUP (UINT8 dbCellIndex,RestoringParameters *pRestoringParams,triggeringOptions_ts *pTriggerOpt);
void plMsrIntraCsmInSleepAddCellToSearch (UINT8 dbCellIndex,RestoringParameters *pRestoringParams,triggeringOptions_ts *pTriggerOpt,Bool enableSearch);
void plMsrIntraCsmInNoSleepNoSearch (UINT8 dbCellIndex,RestoringParameters *pRestoringParams,triggeringOptions_ts *pTriggerOpt);
void plMsrIntraCsmInSleepCellSearchReplyFound (UINT8 dbCellIndex/*,SearchReplyStatus_ts *ReplyStatus*/);
void plMsrIntraCsmInSleepPostCpichSearchReply (Bool lastReplyInMessage);
void plMsrIntraCsmInSleepCpichSearchReply (UINT8 dbCellIndex,SearchReplyStatus_ts *ReplyStatus);
void plMsrIntraCsmClearIdleModeCellBitmap(void);
void plMsrIntraCsmDrxStart (void);
void plMsrIntraCsmUpdateIdleBmps (UINT32 *pAddedCellsBmp,UINT32 *pRemovedCellsBmp);
void plMsrIntraCsmDrxStop (void);
UINT8 plMsrIntraCsmGetNextNbr4BssCell (UINT8 startCellIndex);
UINT8 plMsrIntraCsmGetNextNbr4MpsCell (UINT8 startCellIndex);
void plMsrIntraCsmMpsBmpToArray (UINT8* dbCellIndexArray,UINT8* numOfCells);
Bool plMsrIntraCsmIsNbr4BssIsZero (void);
void plMsrIntraCsmPringIdleBmap (void);
Bool plMsrIntraCsmNoSleepTryAddingCellToCellSearch (UINT8 dbCellIndex,triggeringOptions_ts *pTriggerOpt);
Bool plMsrIntraCsmNoSleepTryAddingCellToCpichSearch (UINT8 dbCellIndex,triggeringOptions_ts *pTriggerOpt);
void plMsrIntraResetIdleCell (UINT8 dbCellIndex);
void plMsrIntraSetIdleCell (UINT8 dbCellIndex,UINT8 SearchType);
void plMsrIntraCsmInSleepCellSearchReplyEndOfCycle (void);
BOOL plMsrIntraCsmNoSleepTryMovingExtendedCellToCpichSearch(UINT8 dbCellIndex, triggeringOptions_ts *TriggerOpt);
/**********************************************************************/
/**********************************************************************/
/*                              private prototypes                   */
/**********************************************************************/

/**********************************************************************/

#endif
/**********************************************************************
End_of_file:   pl_msr_intra_csm.h
============
***********************************************************************/
