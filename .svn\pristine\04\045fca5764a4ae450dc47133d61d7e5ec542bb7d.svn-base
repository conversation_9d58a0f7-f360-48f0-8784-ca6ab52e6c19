/**
 ******************************************************************************
 *
 * rwnx_cmds.h
 *
 * Copyright (C) RivieraWaves 2014-2018
 *
 ******************************************************************************
 */

#ifndef _RWNX_CMDS_H_
#define _RWNX_CMDS_H_

#ifdef ON_LINUX
#include <linux/spinlock.h>
#include <linux/completion.h>
#else
#include "rwnx_rtos_api.h"
#endif
#include "lmac_msg.h"
#ifdef CONFIG_ON_RTOS
#include "osa_old_api.h"//to use semaphore
#endif //#ifdef CONFIG_ON_RTOS
#ifdef WPA_MAC_SIM
#include "wpa_msg.h"
#endif //#ifdef WPA_MAC_SIM

#ifdef CONFIG_RWNX_SDM
#define RWNX_80211_CMD_TIMEOUT_MS    (20 * 300)
#elif defined(CONFIG_RWNX_FHOST)
#define RWNX_80211_CMD_TIMEOUT_MS    (10000)
#else
#ifdef COOP_WITH_1826
#define RWNX_80211_CMD_TIMEOUT_MS    1000 //300 //300
#else
#define RWNX_80211_CMD_TIMEOUT_MS    300
#endif //#ifdef COOP_WITH_1826
#endif //#ifdef CONFIG_RWNX_SDM

#define RWNX_CMD_FLAG_NONBLOCK      BIT(0)
#define RWNX_CMD_FLAG_REQ_CFM       BIT(1)
#define RWNX_CMD_FLAG_WAIT_PUSH     BIT(2)
#define RWNX_CMD_FLAG_WAIT_ACK      BIT(3)
#define RWNX_CMD_FLAG_WAIT_CFM      BIT(4)
#define RWNX_CMD_FLAG_DONE          BIT(5)
/* ATM IPC design makes it possible to get the CFM before the ACK,
 * otherwise this could have simply been a state enum */
#define RWNX_CMD_WAIT_COMPLETE(flags) \
    (!(flags & (RWNX_CMD_FLAG_WAIT_ACK | RWNX_CMD_FLAG_WAIT_CFM)))

#define RWNX_CMD_MAX_QUEUED         (NX_REMOTE_STA_MAX*2+5) //8

#ifdef CONFIG_RWNX_FHOST
#include "ipc_fhost.h"
#define rwnx_cmd_e2amsg ipc_fhost_msg
#define rwnx_cmd_a2emsg ipc_fhost_msg
#define RWNX_CMD_A2EMSG_LEN(m) (m->param_len)
#define RWNX_CMD_E2AMSG_LEN_MAX IPC_FHOST_MSG_BUF_SIZE
struct rwnx_term_stream;

#else /* !CONFIG_RWNX_FHOST*/
#include "ipc_shared.h"
#ifdef WPA_MAC_SIM
#include "wpa_msg.h"
#endif //#ifdef WPA_MAC_SIM
#define rwnx_cmd_e2amsg ipc_e2a_msg
#ifdef WPA_MAC_SIM
#define rwnx_cmd_a2emsg _WpaMsgNode
#define RWNX_CMD_A2EMSG_LEN(m) (sizeof(struct _WpaMsgNode) + m->param_len)
#else
#define rwnx_cmd_a2emsg lmac_msg
#define RWNX_CMD_A2EMSG_LEN(m) (sizeof(struct lmac_msg) + m->param_len)
#endif

#define RWNX_CMD_E2AMSG_LEN_MAX (IPC_E2A_MSG_PARAM_SIZE * 4)

#endif /* CONFIG_RWNX_FHOST*/

struct rwnx_hw;
struct rwnx_cmd;
typedef int (*msg_cb_fct)(struct rwnx_hw *rwnx_hw, struct rwnx_cmd *cmd,
                          struct rwnx_cmd_e2amsg *msg);

enum rwnx_cmd_mgr_state {
    RWNX_CMD_MGR_STATE_DEINIT,
    RWNX_CMD_MGR_STATE_INITED,
    RWNX_CMD_MGR_STATE_CRASHED,
};

struct rwnx_cmd {
    struct list_head list;
    lmac_msg_id_t id;
    lmac_msg_id_t reqid;
    struct rwnx_cmd_a2emsg *a2e_msg;
    char *e2a_msg;
    u32 tkn;
    u16 flags;

#ifdef ON_LINUX
    struct completion complete;
#endif
#ifdef CONFIG_ON_RTOS
    u32 index;
#endif //#ifdef CONFIG_ON_RTOS
    u32 result;
    #ifdef CONFIG_RWNX_FHOST
    struct rwnx_term_stream *stream;
    #endif
};

struct rwnx_cmd_mgr {
    enum rwnx_cmd_mgr_state state;
#ifdef ON_LINUX
    spinlock_t lock;
#else
    OSASemaRef lock;
#endif
    u32 next_tkn;
    u32 queue_sz;
    u32 max_queue_sz;

    struct list_head cmds;

    int  (*queue)(struct rwnx_cmd_mgr *, struct rwnx_cmd *);
    int  (*llind)(struct rwnx_cmd_mgr *, struct rwnx_cmd *);
    int  (*msgind)(struct rwnx_cmd_mgr *, struct rwnx_cmd_e2amsg *, msg_cb_fct);
    void (*print)(struct rwnx_cmd_mgr *);
    void (*drain)(struct rwnx_cmd_mgr *);
};

void rwnx_cmd_mgr_init(struct rwnx_cmd_mgr *cmd_mgr);
void rwnx_cmd_mgr_deinit(struct rwnx_cmd_mgr *cmd_mgr);
#ifdef CONFIG_SDIO
int cmd_mgr_send_next(struct rwnx_hw *rwnx_hw);
#endif //#ifdef CONFIG_SDIO
#ifdef CONFIG_ON_RTOS
void rwnx_free_static_cmds(struct rwnx_cmd *cmd);
#endif //#ifdef CONFIG_ON_RTOS

#endif /* _RWNX_CMDS_H_ */
