/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_PolygonPoints_H_
#define	_PolygonPoints_H_


#include <asn_application.h>

/* Including external dependencies */
#include <NativeEnumerated.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Dependencies */
typedef enum PolygonPoints__latitudeSign {
	PolygonPoints__latitudeSign_north	= 0,
	PolygonPoints__latitudeSign_south	= 1
} e_PolygonPoints__latitudeSign;

/* PolygonPoints */
typedef struct PolygonPoints {
	long	 latitudeSign;
	long	 degreesLatitude;
	long	 degreesLongitude;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PolygonPoints_t;

/* Implementation */
/* extern asn_TYPE_descriptor_t asn_DEF_latitudeSign_2;	// (Use -fall-defs-global to expose) */
extern asn_TYPE_descriptor_t asn_DEF_PolygonPoints;

#ifdef __cplusplus
}
#endif

#endif	/* _PolygonPoints_H_ */
#include <asn_internal.h>
