/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/gp.mod/lib/src/xsmmain.h
 *   $Revision: 
 *   $DateTime: 
 **************************************************************************
 * File Description:
 *
 * ESM main module - receives signals from SNDCP and ABGP and PDP and EMM tasks
 * and processes them through state machine
 **************************************************************************/

#ifndef XSMMAIN_H
#define XSMMAIN_H

#include <esmdata.h>
Boolean XsmMainProcessCommSignal(SmEntity *sm);
void XsmProcessPendingSignal(SmEntity *sm);

#endif

