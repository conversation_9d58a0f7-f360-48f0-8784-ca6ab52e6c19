#ifndef _BOARD_H_
#define _BOARD_H_

#include <stdbool.h>

#define PMIC_POWERUP_ONKEY      (1<<0)
#define PMIC_POWERUP_BAT        (1<<3)
#define PMIC_POWERUP_RTC_ALARM  (1<<4)
#define PMIC_POWERUP_REBOOT     (1<<5)
#define PMIC_POWERUP_USB        (1<<6)

#define UOS_SEND_EVT            (1 << 0)
#define UOS_SEND_MSG            0

#define UOS_WAIT_FOREVER        0xFFFFFFFF
#define UOS_NO_WAIT             0x0

#define PM_RESUME_ONKEY   (0x1 << 0)
#define PM_RESUME_ALARM   (0x1 << 1)
#define PM_RESUME_SDK     (0x1 << 2)
#define PM_RESUME_UI      (0x1 << 3)
#define PM_RESUME_USB     (0x1 << 4)
#define PM_RESUME_OVP     (0x1 << 5) /* pmic uv irq 13 */
#define PM_RESUME_ENDKEY     (0x1 << 6)
#define PM_RESUME_NORMALKEY     (0x1 << 7) /* all key except onkey/endkey */
#define PM_RESUME_AMBIENT     (0x1 << 8) /* ambient 1 min timeout to wake up gui */
#define PM_RESUME_ALL     (PM_RESUME_ONKEY|PM_RESUME_ALARM|PM_RESUME_SDK|PM_RESUME_UI|PM_RESUME_USB|PM_RESUME_OVP|PM_RESUME_ENDKEY|PM_RESUME_NORMALKEY|PM_RESUME_AMBIENT)

#define UOS_FLAG_OR             7

typedef struct _UI_EVENT {
    uint32_t nEventId;
    uint32_t nParam1;
    uint32_t nParam2;
    uint32_t nParam3;
} UI_EVENT;

#define cplog_printf printf

typedef struct rtc_time {
  int tm_sec;   //seconds [0,59]
  int tm_min;   //minutes [0,59]
  int tm_hour;  //hour [0,23]
  int tm_mday;  //day of month [1,31]
  int tm_mon;   //month of year [1,12]
  int tm_year;  // since 1970
  int tm_wday;  // sunday = 0
}rtc_time_t;

typedef struct _TaskDesc
{
    void (*TaskBody)(void *);
    const char *Name;
    void *Parameter;
    uint32_t nStackSize;
    uint8_t nPriority;
}TaskDesc;

typedef struct _TASK_HANDLE
{
    TaskDesc sTaskDesc;
    uint8_t nTaskId;
    uint8_t nMailBoxId;
    // indicate the task's status
    uint8_t nStatus;
    uint8_t padding[4];
}TASK_HANDLE;

typedef void (*alarm_cb_t)(void);
typedef void (*chg_cb_t)(void);

unsigned irq_disable(void);
unsigned irq_enable(void);
void irq_restore(unsigned state);
int irq_is_in(void);

void pmic_rtc_set_time(rtc_time_t *t);
void pmic_rtc_get_time(rtc_time_t *t);
void pmic_rtc_set_alarm(rtc_time_t *time, alarm_cb_t callback);
void pmic_rtc_get_alarm(rtc_time_t *time);
void pmic_rtc_enable_alarm(uint8_t on_off);
int pmic_get_chg_status(void);
void pmic_register_chg_handle(chg_cb_t handle);
uint8_t pmic_get_bat_remain(void);
uint8_t pmic_powerup_get_reason(void);
void pmic_vibrator_on(void);
void pmic_vibrator_off(void);

void lcd_backlight_ctrl(int level);

unsigned lv_get_ticks_per_second(void);
uint32_t tick_to_ms(uint32_t tick);
void pmic_sw_pdown(void);
void pmic_sw_reboot(void);
uint8_t uos_send_msg (void *Msg, uint8_t index, uint8_t MsgStatus);
void *uos_wait_msg (uint32_t *Evt, uint8_t index, uint32_t nTimeOut);
uint8_t uos_new_mutex (void);
void  uos_free_mutex (uint8_t id);
int uos_take_mutex (uint8_t id);
int uos_release_mutex (uint8_t id);
void uos_sleep (uint32_t Ticks);
int uos_set_flag(uint8_t id, uint32_t mask, uint32_t op);
void ambient_status_set(bool status);

/*mci_audio.h */
// ============================================================================
// AUDIO_EQ
// ----------------------------------------------------------------------------
/// Enum describing the various equalizer modes
// ============================================================================
typedef enum
{
    NORMAL,   /*EQ=0*/
    BASS,     /*EQ=1*/
    DANCE,    /*EQ=2*/
    CLASSICAL,/*EQ=3*/
    TREBLE,   /*EQ=4*/
    PARTY,    /*EQ=5*/
    POP,      /*EQ=6*/
    ROCK,     /*EQ=7*/
    AUDIO_EQ_NUM
} AUDIO_EQ;

// ============================================================================
// MCI_AUDIO_PATH_T
// ----------------------------------------------------------------------------
/// Enum describing the various audio paths
// ============================================================================
typedef enum
{
    MCI_PATH_NORMAL    = 0,      /* normal  */
    MCI_PATH_HP        = 1,      /* earphone, carkit */
    MCI_PATH_LSP       = 2,      /* loudspeaker  */
    MCI_PATH_FM_HP     = 3,
    MCI_PATH_FM_LSP_HP = 4
} MCI_AUDIO_PATH_T;

// =============================================================================
// MMC_ANALOG_MSGID
// -----------------------------------------------------------------------------
// =============================================================================
typedef enum
{
    STREAM_STATUS_REQUEST_DATA,
    STREAM_STATUS_NO_MORE_DATA,
    STREAM_STATUS_END,
    STREAM_STATUS_ERR,
} APBS_STREAM_STATUS_T;

typedef enum
{
    MSG_MMC_AUDIODEC_VOC,
    MSG_MMC_AUDIODEC_PCM,
//  MSG_MMC_AUDIODEC_A2DP,
    MSG_MMC_AUDIODEC_SCO,
}AUDIODEC_USER_MSG;

typedef enum
{
    MCI_ERR_NO,
    MCI_ERR_UNKNOWN_FORMAT,
    MCI_ERR_BUSY,
    MCI_ERR_INVALID_PARAMETER,
    MCI_ERR_ACTION_NOT_ALLOWED,
    MCI_ERR_OUT_OF_MEMORY,
    MCI_ERR_CANNOT_OPEN_FILE,
    MCI_ERR_END_OF_FILE,
    MCI_ERR_TERMINATED,
    MCI_ERR_BAD_FORMAT,
    MCI_ERR_INVALID_FORMAT,
    MCI_ERR_ERROR,
} MCI_ERR_T;

typedef enum
{
    MCI_INFO_NONE,
    MCI_INFO_ERROR,
    MCI_INFO_RECORDER_CUR_DURATION_MS,
    MCI_INFO_RECORDER_CUR_SIZE
} MCI_INFO_T;

// =============================================================================
// APBS_PLAY_MODE_T
// -----------------------------------------------------------------------------
/// This type describes the encoding mode used in a stream to play.
// =============================================================================
typedef enum
{
    MCI_PLAY_MODE_AMR475,
    MCI_PLAY_MODE_AMR515,
    MCI_PLAY_MODE_AMR59,
    MCI_PLAY_MODE_AMR67,
    MCI_PLAY_MODE_AMR74,
    MCI_PLAY_MODE_AMR795,
    MCI_PLAY_MODE_AMR102,
    MCI_PLAY_MODE_AMR122,
    MCI_PLAY_MODE_FR,
    MCI_PLAY_MODE_HR,
    MCI_PLAY_MODE_EFR,
    MCI_PLAY_MODE_PCM,
    // TODO Implement that mode
    MCI_PLAY_MODE_AMR_RING,
    MCI_PLAY_MODE_MP3,
    MCI_PLAY_MODE_AAC,
    MCI_PLAY_MODE_WAV,
    MCI_PLAY_MODE_MID,
    MCI_PLAY_MODE_STREAM_PCM, //for TTS stream play
    MCI_PLAY_MODE_QTY
} MCI_PLAY_MODE_T;

typedef enum
{
    MCI_AUDIO_DTMF_0 = 0,
    MCI_AUDIO_DTMF_1,
    MCI_AUDIO_DTMF_2,
    MCI_AUDIO_DTMF_3,
    MCI_AUDIO_DTMF_4,
    MCI_AUDIO_DTMF_5,
    MCI_AUDIO_DTMF_6,
    MCI_AUDIO_DTMF_7,
    MCI_AUDIO_DTMF_8,
    MCI_AUDIO_DTMF_9,
    MCI_AUDIO_DTMF_A,
    MCI_AUDIO_DTMF_B,
    MCI_AUDIO_DTMF_C,
    MCI_AUDIO_DTMF_D,
    MCI_AUDIO_DTMF_S,        //* key
    MCI_AUDIO_DTMF_P,        //# key
    MCI_AUDIO_COMFORT_425,
    MCI_AUDIO_COMFORT_950,
    MCI_AUDIO_COMFORT_1400,
    MCI_AUDIO_COMFORT_1800,
} MCI_AUDIO_TONE_TYPE_T;

typedef enum
{
    MCI_AUDIO_TONE_0DB = 0,
    MCI_AUDIO_TONE_M3DB,     // -3 dB
    MCI_AUDIO_TONE_M9DB,     // -9 dB
    MCI_AUDIO_TONE_M15DB,    // -15 dB
    MCI_AUDIO_TONE_QTY
} MCI_AUDIO_TONE_ATTENUATION_T;

typedef enum
{
    MCI_EVENT_INFO,
    MCI_EVENT_ERROR,
    MCI_EVENT_EOS,
} MCI_EVNET_T;

typedef uint8_t mci_type_enum;

typedef enum
{
    MCI_SPK_MUTE         = 0,
    MCI_SPK_VOL_1,
    MCI_SPK_VOL_2,
    MCIL_SPK_VOL_3,
    MCI_SPK_VOL_4,
    MCI_SPK_VOL_5,
    MCI_SPK_VOL_6,
    MCI_SPK_VOL_7,
    MCI_SPK_VOL_8,
    MCI_SPK_VOL_9,
    MCI_SPK_VOL_10,
    MCI_SPK_VOL_11, // 11

    MCIL_SPK_VOL_QTY // 12
} MCI_SPK_LEVEL_T;

typedef struct _PlayInfromation
{
    int32_t PlayProgress;
    int32_t curPlayTimeMs;
} MCI_PlayInf;

#define MCI_SPK_MAX MCI_SPK_VOL_11

typedef struct _ProgressInfromation
{
    int32_t DurationTime;//ms
    int32_t BitRate;
    int32_t ID3Offset;
    int32_t reserve;
} MCI_ProgressInf;

// ============================================================================
// MCI_AUDIO_PLAY_CALLBACK_T
// ----------------------------------------------------------------------------
/// Callback function called when the service changes state
// ============================================================================

typedef void (*MCI_AUDIO_PLAY_CALLBACK_T)(MCI_ERR_T result);

typedef void (*MCI_AUDIO_BUFFER_PLAY_CALLBACK_T)(MCI_ERR_T result);
typedef void (*MCI_AUDIO_FILE_RECORD_CALLBACK_T)(MCI_EVNET_T event , MCI_INFO_T info_type, int32_t value);

typedef void (*APBS_STREAM_USER_HANDLER_T)(APBS_STREAM_STATUS_T);
typedef void (*MCI_AUDIO_RECORD_BUFFER_PLAY_CALLBACK_T)(unsigned char *buf_p, uint32_t len, uint32_t *record_len) ;

typedef int32_t HANDLE;

// ============================================================================
// Functions
// ============================================================================

/*-------------------------------------------------------------------------------------------
Function: MCI_AudioPlay
Description: play the audio

Parameters: OutputPath: audio output device type. Curretly only support AUD_ITF_EAR_PIECE
            fileHandle: audio file handle
            fielType:   audio format, currently support MCI_TYPE_WAV,MCI_TYPE_DAF,MCI_TYPE_AMR
            callback:   Some actions that need to be processed, such as switch file play back status
            playprogress: [0,10000]. If set 0, play from the head;
                        set to 10000 ,jump to the end of the file.

Return value: If play success, return MCI_ERR_NO
Note:        1. If test PlayProgress, modify this value in test file("mdi_testbench.c"). Default set to 0.
--------------------------------------------------------------------------------------------*/
uint32_t MCI_AudioPlay(int32_t OutputPath,HANDLE fileHandle,mci_type_enum fielType,MCI_AUDIO_PLAY_CALLBACK_T callback,int32_t PlayProgress);

/*-------------------------------------------------
Function: MCI_AudioSeekTo
Description: seek to position
Parameters: ms
Return value: MCI_ERR_NO success, others error
---------------------------------------------------*/
uint32_t MCI_AudioSeekTo(int32_t seekMs);

/*-------------------------------------------------
Function: MCI_AudioPause
Description: Switch status from play to pause
Parameters: NONE
Return value: If pause success, return MCI_ERR_NO
---------------------------------------------------*/
uint32_t MCI_AudioPause(void);

/*-------------------------------------------------
Function: MCI_AudioResume
Description: Switch status from pause to play
Parameters: fileHandle: paused audio file handle
Return value: if resume success, return MCI_ERR_NO
---------------------------------------------------*/
uint32_t MCI_AudioResume (HANDLE fileHandle);

/*-------------------------------------------------
Function: MCI_AudioStop
Description: Switch status into stop
Parameters: NONE
Return value: if stop success, return MCI_ERR_NO
---------------------------------------------------*/
uint32_t MCI_AudioStop (void);

// ============================================================================
// MCI_AudioSetOutputPath
// ----------------------------------------------------------------------------
/// Allows to change the audio path while playing
/// @param OutputPath : Selects the audio path to use
/// @param Mute : If Mute=1 the sound will be muted
/// @return error code MCI_ERR_ACTION_NOT_ALLOWED, MCI_ERR_INVALID_PARAMETER,
/// MCI_ERR_NO
// ============================================================================
uint32_t MCI_AudioSetOutputPath(uint16_t OutputPath,uint16_t Mute);

/*-------------------------------------------------------------------------------------------
Function:    MCI_AudioSetMute
Description: set mute during a voice conversation, the person answering the phone can not hear the sound.
Parameters:
        onoff: 1 --- turn on mute
               0 --- turn off mute
Return value: NONE
--------------------------------------------------------------------------------------------*/
void MCI_AudioSetMute(uint8_t onoff);

/*-------------------------------------------------------------------------------------------
Function:    MCI_AudioSetVolume
Description: Allows to change the audio volume
            If no audio is currently played, the value will be saved and used for the next stream played
Parameters:
            volume : Selects the audio volume.
                The range of values of volume please refer to the enum AUDIOHAL_SPK_LEVEL_T
Return value:
            If set success, return the sound value.
--------------------------------------------------------------------------------------------*/
uint32_t MCI_AudioSetVolume(uint16_t volume);
uint32_t MCI_AudioPlayTone(MCI_AUDIO_TONE_TYPE_T tone, MCI_AUDIO_TONE_ATTENUATION_T attenuation);
uint32_t MCI_AudioStopTone(MCI_AUDIO_TONE_TYPE_T tone, MCI_AUDIO_TONE_ATTENUATION_T attenuation);
uint32_t MCI_AudioPauseTone(void);
uint32_t MCI_AudioResumeTone(void);
int32_t  MCI_AudioPlayBuffer(int32_t *pBuffer, uint32_t len, uint8_t loop, MCI_AUDIO_BUFFER_PLAY_CALLBACK_T callback, MCI_PLAY_MODE_T  format, int32_t startPosition);
int32_t  MCI_AudioStopBuffer(void);
uint32_t MCI_AudioRecordStart (HANDLE fhd,mci_type_enum format, uint8_t quality, MCI_AUDIO_FILE_RECORD_CALLBACK_T callback,  MCI_AUDIO_RECORD_BUFFER_PLAY_CALLBACK_T usercallback);
uint32_t MCI_AudioRecordStop(void);
uint32_t MCI_AudioBufferRecordStart(uint8_t *pBuf, uint32_t bufSize, uint32_t maxDuration, mci_type_enum format, uint8_t quality, MCI_AUDIO_FILE_RECORD_CALLBACK_T callback, MCI_AUDIO_RECORD_BUFFER_PLAY_CALLBACK_T usercallback);
uint32_t MCI_AudioBufferRecordStop(uint32_t *recSize, uint32_t *durationMs);
uint32_t MCI_AudioGetDurationTime(HANDLE fileHandle, mci_type_enum fielType,int32_t BeginPlayProgress, int32_t OffsetPlayProgress, MCI_ProgressInf* PlayInformation);
uint32_t MCI_AudioPause(void);
uint32_t MCI_AudioResume(int32_t fileHandle);
uint32_t MCI_AudioGetPlayInformation(MCI_PlayInf * PlayInformation);
/*end mci_audio.h*/

/*AudioHAL.h*/
//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_ERR_NO = 0,  //No error
    AUDIOHAL_ERR_RESOURCE_RESET,
    AUDIOHAL_ERR_RESOURCE_BUSY,
    AUDIOHAL_ERR_RESOURCE_TIMEOUT,
    AUDIOHAL_ERR_RESOURCE_NOT_ENABLED,
    AUDIOHAL_ERR_BAD_PARAMETER,

    AUDIOHAL_ERR_UART_RX_OVERFLOW,
    AUDIOHAL_ERR_UART_TX_OVERFLOW,
    AUDIOHAL_ERR_UART_PARITY,
    AUDIOHAL_ERR_UART_FRAMING,
    AUDIOHAL_ERR_UART_BREAK_INT,

    AUDIOHAL_ERR_TIM_RTC_NOT_VALID,
    AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_ENABLED,
    AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_DISABLED,

    AUDIOHAL_ERR_COMMUNICATION_FAILED,

    /* Must be at the end */
    AUDIOHAL_ERR_QTY,


    AUDIOHAL_ERR_ENUM_32_BIT		    	= 0x7FFFFFFF //32bit enum compiling enforcement
} AUDIOHAL_ERR_T;

//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_ITF_RECEIVER         = 0,
    AUDIOHAL_ITF_EARPIECE,
    AUDIOHAL_ITF_HEADPHONE = AUDIOHAL_ITF_EARPIECE,
    AUDIOHAL_ITF_LOUDSPEAKER,
    AUDIOHAL_ITF_LOUDSPEAKER_AND_HEADPHONE,
    //AUDIOHAL_ITF_LOUDSPEAKER_AND_HEADPHONE = AUDIOHAL_ITF_LOUDSPEAKER_AND_EARPIECE,
    AUDIOHAL_ITF_BLUETOOTH,
    AUDIOHAL_ITF_FM,
    AUDIOHAL_ITF_FM2SPK,
    AUDIOHAL_ITF_TV,

    AUDIOHAL_ITF_QTY,
    AUDIOHAL_ITF_NONE = 255,
} AUDIOHAL_ITF_T;

typedef enum
{
    AUDIOHAL_SPK_RECEIVER         = 0,
    AUDIOHAL_SPK_EARPIECE,
    AUDIOHAL_SPK_LOUDSPEAKER,
    AUDIOHAL_SPK_LOUDSPEAKER_EARPIECE,  //Output on both hands-free loud speaker and earpiece

    AUDIOHAL_SPK_QTY,
    AUDIOHAL_SPK_DISABLE = 255,
} AUDIOHAL_SPK_T;

//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_SPEAKER_STEREO         = 0,
    AUDIOHAL_SPEAKER_MONO_RIGHT,
    AUDIOHAL_SPEAKER_MONO_LEFT,
    AUDIOHAL_SPEAKER_STEREO_NA,  //Output is mono only

    AUDIOHAL_SPEAKER_QTY,
    AUDIOHAL_SPEAKER_DISABLE = 255,
} AUDIOHAL_SPEAKER_TYPE_T;

//ICAT EXPORTED ENUM
typedef enum
{
    AUDIOHAL_MIC_RECEIVER         = 0,
    AUDIOHAL_MIC_EARPIECE,
    AUDIOHAL_MIC_LOUDSPEAKER,

    AUDIOHAL_MIC_QTY,
    AUDIOHAL_MIC_DISABLE = 255,
} AUDIOHAL_MIC_T;

typedef struct {
    uint8_t spkLevel;
    uint8_t  micLevel;
    uint8_t sideLevel;
    uint8_t toneLevel;
} AUDIOHAL_AIF_LEVEL_T;

typedef struct {
    AUDIOHAL_SPK_T          spkSel;
    AUDIOHAL_SPEAKER_TYPE_T spkType;    //Kind of speaker(stereo/mono/etc)
    AUDIOHAL_MIC_T          micSel;
    AUDIOHAL_AIF_LEVEL_T  * level;
} AUDIOHAL_AIF_DEVICE_CFG_T;

AUDIOHAL_ERR_T AudioHAL_AifOpen(AUDIOHAL_ITF_T itf, AUDIOHAL_AIF_DEVICE_CFG_T *config);
/*end AudioHAL.h*/
int FS_PowerOn(void);

void sys_pm_init(void);
uint32_t sys_pm_set_state(int state);
void board_power_off(int type);
int startup_silent_reset(void);

extern int pm_state_flag;

typedef enum {
    RSTREASON_POWEROFF_CHARGE = 'C', //Charging in "powering off" mode
    RSTREASON_RD_PRODUCTION   = 'R', //Rd production mode
    RSTREASON_RTC_ALARM       = 'A', //rtc Alarm
    RSTREASON_NORMAL_POWERON  = 'N', //Normal powering on
    RSTREASON_ERROR_CONDITION = 'E', //Error reset, eg. watchdog reset triggered by system hang or silent reset
} SW_RESTART_REASON ;

unsigned char SysRestartReasonGet(void);
unsigned char isSysRestartByCharging(void);
unsigned char isSysRestartByRdProduction(void);
unsigned char isSysRestartByAlarm(void);
unsigned char isSysRestartByNormal(void);
unsigned char isSysRestartByError(void);

bool pm813_set_max_cc_current(uint32_t limit);

typedef enum {
    //INT ENABLE REG 2 addr=0x0a
    NINGBO_TINT_INT=0,
    NINGBO_GPADC0_INT,
    NINGBO_GPADC1_INT,
    NINGBO_VINLDO_INT,
    NINGBO_VBAT_INT,
    NINGBO_CP_START_ERROR_DET_INT,
    NINGBO_CLASSD_OUT_DET_INT,
    NINGBO_RTC_INT,

    //INT ENABLE REG 1 addr=0x09
    NINGBO_ONKEY_INT=8,
    NINGBO_EXTON1_INT,
    NINGBO_EXTON2_INT,
    NINGBO_BAT_INT,
    NINGBO_VBUS_UVLO_INT,
    NINGBO_VBUS_OVP_INT,
    NINGBO_VBUS_DET_INT,
    NINGBO_CP_START_DONE_DET_INT,
} NINGBO_INTC ;

typedef void (*PmicCallback)(void);
void Ningbo_INT_CALLBACK_REGISTER(NINGBO_INTC intc,PmicCallback isr);
void Ningbo_INT_ENABLE(NINGBO_INTC intc);
void Ningbo_INT_DISABLE(NINGBO_INTC intc);

uint8_t AudioHAL_SetResBufCnt(unsigned int bufCnt);
uint8_t GetBacklightLevelBeforeReset(void);
bool GetBacklightStatusBeforeReset(void);

/*sd card mount to U storage*/
bool charger_is_usb(void);
bool sdcard_is_ready(void);

typedef enum
{
    USB_CHARGER_ONLY,
    USB_STORAGE_ONLY,
}USBDeviceType;

void USBDeviceSelect(USBDeviceType DeviceSelect);
int sdcard_fat_is_ok(void);
bool PMIC_IS_PM803(void);
bool usb_is_connected(void);

typedef struct
{
    unsigned short start_x;
    unsigned short start_y;
    unsigned short end_x;
    unsigned short end_y;
    unsigned int image_width;
    unsigned int image_height;
    unsigned short preview_rotate;    //xiaoyifeng add  10/11/1
    //effects
    unsigned short nightmode;
    unsigned short imageQuality;
    unsigned short factor;
    unsigned short contrast;
    unsigned short specialEffect;
    unsigned short brightNess;
    unsigned short whiteBlance;
    unsigned short exposure;
    unsigned short addFrame;
    // flash
    unsigned short flashenable;
} CAM_PREVIEW_STRUCT;

typedef struct
{
    unsigned short image_width;
    unsigned short image_height;
    unsigned char media_mode;

} CAM_CAPTURE_STRUCT;

typedef int (*camRecordReleaseBufferCallback)(void *, void *);
typedef struct
{
    unsigned recordWidth;
    unsigned recordHeight;
    unsigned recordFormat;
    unsigned recordFramerate;
    void (*setReleaseBufferCallBack)(camRecordReleaseBufferCallback callBackHandle, void *userData);
    void (*enqueueCamRecordBuffer)(void *pBuf, int bufIndex);
    void (*flushCamRecordBuffers)(void);
} CAM_RECORD_PARMETERS_STRUCT;

#endif
