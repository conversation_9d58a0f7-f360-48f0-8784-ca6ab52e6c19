/*
 * Copyright (c) 2021 Rx Networks, Inc. All rights reserved.
 *
 * Property of Rx Networks
 * Proprietary and Confidential
 * Do NOT modify. Do NOT distribute.
 * 
 * Any use, distribution, or copying of this document requires a 
 * license agreement with Rx Networks. 
 * Any product development based on the contents of this document 
 * requires a license agreement with Rx Networks. 
 * If you have received this document in error, please notify the 
 * sender immediately by telephone and email, delete the original 
 * document from your electronic files, and destroy any printed 
 * versions.
 *
 * This header documents the core library function calls.
 *
 *************************************************************************
 * $LastChangedDate: 2021-02-12 02:00:26 +0000 (Fri, 12 Feb 2021) $
 * $Revision: 162562 $
 *************************************************************************
 *
 */

/**
 * \file
 * \brief
 * Unified application programming interface for Rx Networks predictive system.
 * 
 * \details
 * RXN API is a facade for users of the system. It hides the implementation 
 * details behind a simple API. The goal is to allow users to integrate 
 * into their target platform easily.
 *   
 * 
 * \since
 * 1.0.0
 */
 
#ifndef RXN_API_H
#define RXN_API_H
#include "asr_agps_api.h"
#include "RXN_structs.h"
#include "RXN_constants.h"
#include "RXN_API_calls.h"

#ifdef __cplusplus
extern "C" {
#endif
enum MSL_seed_states
{
    MSL_SEED_OKAY,
    MSL_SEED_DOWNLOAD,
    MSL_SEED_MSL_GPS_TIME_SOURCE_INVALID
};
    /**
    * \brief 
    * Retrieve the API version.
    *
    * \param version [OUT] 
    * A memory location to be filled with the current API version.
    * This is a null terminated string and is at most 
    * RXN_CONSTANT_VERSION_STRING_LENGTH (50) characters long.
    *
    * \return RXN_SUCCESS 
    * If the version is returned successfully.
    * 
    * \return RXN_FAIL 
    * If the version cannot be returned.
    *
    * \details
    * <b>Description</b>\n
    * This function returns the version of the PG Lite API library.
    * The version number will be incremented as new features, enhancements, 
    * and bug fixes are added to the library. The version number is an important 
    * identification when reporting and troubleshooting issues.
    * Rx Networks' revision numbering convention are described within the 
    * Integration User's Manuals.
    *
    * \since
    * 1.0.0
    * <b>Example Usage</b>
    * \code
    * // Declare a string to hold version information.
    * char version[RXN_CONSTANT_VERSION_STRING_LENGTH];
    * 
    * // Retrieve and output version information.
    * RXN_Get_API_Version(&version[0]);
    * printf("RXN_Get_API_Version(): %s\n", version);
    * \endcode
    *
    * <b>Output</b>\n
    * \code   
    * RXN_Get_API_Version(): PG Lite API 1.0.0
    * \endcode
    */
    unsigned short RXN_Get_API_Version(char version[RXN_CONSTANT_VERSION_STRING_LENGTH]);

    /**
    * \brief 
    * Decodes the binary data received from location.io server.
    *
    * \param data [IN]
    * A pointer to the bytes received from location.io server. In order to 
    * minimize the size of the network transmission, data is sent from the 
    * server in a packed format, and needs to be decoded on the
    * client. After the data is decoded to the internal data structures, 
    * the library will call the appropriate abstraction functions to save the 
    * prediction data permanently. The library will also call RT assistance handlers
    * to decode RT assistance data. RT assistance handlers need to be implemented by
    * the system integrator.
    *
    * \param size [IN]
    * The length of the response byte buffer response points to.
    *
    * \return RXN_SUCCESS
    * If the function completes successfully.
    * 
    * \return RXN_FAIL
    * Other errors.
    *
    * \details
    * This function will decode the binary data in packed format received from 
    * location.io server and save prediction data to flash.
    *
    * \note
    * This method parses the payload of the HTTP response message and not the entire 
    * HTTP response message. Parsing the HTTP response message and handling of the
    * HTTP status codes must be handled by the integrator.
    *
    * \since
    * 1.0.0
    *
    */
    int RXN_Decode(unsigned char* data, const unsigned int size,
            supl_assist_t *assist);

    /**
    * \brief 
    * Get EE in RTCM format from the prediction buffer.
    *
    * \param constel [IN]
    * The constellation to get EE for.
    *
    * \param time [IN]
    * The current constellation time.
    *
    * \param rtcmData [OUT]
    * RTCM data for the constellation requested and the time of interest.
    *
    * \param rtcmLength [OUT]
    * The length of RTCM data.
    *
    * \return RXN_SUCCESS
    * If the function succeeds in getting EE for the specified constellation and time.
    *
    * \return RXN_FAIL
    * Any errors.
    *
    * \since
    * 1.0.0
    *
    * <b>Example Usage</b>
    * \code
    * // Example. Utilize a flag to signal when EE is required by the GNSS receiver.
    * // This flag must be set by integration code when the GNSS receiver needs EE.
    * bool needEE;
    *
    * // Wait until signalled by the GNSS receiver that EE is required. The needEE
    * // signal may be checked within a thread loop or timer event as required by
    * // the integration application.
    * if(needEE)
    * {
    *    // Get the current GPS time (num sec since GPS start at midnight on
    *    // Jan 6, 1980).
    *    U32 currGpsTime = RXN_MSL_GetBestGPSTime();
    *
    *    // Convert to other constellation time if the requested constellation is not GPS.
    *    U32 currBdsTime = MSL_ConvertGPSTimeToBeidouTime(currGpsTime);
    *
    *    // Setup an array to store EE (ensure array elements are clear).
    *    U08 rtcmData[4096];
    *    U32 rtcmLength;
    *    memset(rtcmData, 0, sizeof(rtcmData));
    *
    *    // Get ephemeris in RTCM format.
    *    if(RXN_Get_Ephemeris_Rtcm(RXN_BDS_CONSTEL, currBdsTime, rtcmData, &rtcmLength)) != RXN_SUCCESS)
    *    {
    *        // Handle errors.
    *    }
    *
    *    // Inject RTCM data into the GNSS receiver.
    * }
    * \endcode
    *
    */
    unsigned short RXN_Get_Ephemeris_Rtcm(
        const RXN_constel_t constel,
        RXN_config_t *config,
        unsigned char *payloadData,
        const unsigned int time,
        supl_assist_t *assist
        );

    /**
    * \brief 
    * Get EE file information from the stored prediction file.
    *
    * \param constel [IN]
    * The constellation to get EE information for.
    *
    * \param eeInfo [OUT] 
    * A RXN_ee_info_t structure with the EE file information.
    *   
    * \returns RXN_SUCCESS
    * If the function completes successfully.
    * 
    * \return RXN_FAIL
    * Any errors.
    * 
    * \details
    * Use this to get the EE file information in the RXN_ee_info_t structure.
    *
    * \since
    * 1.0.0
    *
    * <b>Example Usage</b>
    * \code
    * RXN_ee_info_t eeInfo;   
    * memset(&eeInfo, 0, sizeof(eeInfo));
    * RXN_Get_EE_Info(RXN_GPS_CONSTEL, &eeInfo);
    * \endcode
    *
    */  
    unsigned short RXN_Get_EE_Info(
        const RXN_constel_t constel,
        unsigned char *data,
        RXN_ee_info_t* eeInfo
        );

    /**
    * \brief 
    * Set configuration structure
    *
    * \param config [IN] 
    * A RXN_config_t structure with time offsets & configuration parameters.
    *
    * \return RXN_SUCCESS
    * If the function completes successfully.
    * 
    * \return RXN_FAIL
    * Any errors.
    *   
    * \details
    * Use this to set the configuration parameters in the RXN_config_t 
    * structure after reading the configuration parameters.
    *
    * \note
    * However the library *does* need leap_secs for Glonass and will use nextLeapSecs
    * if not zero. The user is therefore advised not zero either of these out when 
    * calling RXN_Set_Configuration().
    *
    * The best practice is to call RXN_Get_Configuration() and then alter the ephemeris 
    * duration parameters. It is not a good idea to declare a config instance, memset 
    * and then set.
    *
    * \since
    * 1.0.0
    *
    * \see
    * RXN_Get_Configuration()
    *
    * <b>Example Usage</b>
    * \code
    * RXN_config_t config;
    * RXN_Get_Configuration(&config);
    * config.timeOfNextLeapSecInGpsWeek = 0;
    * config.timeOfNextLeapSecInGpsTowInSecs = 0;
    * config.currentLeapSecs = 18;
    * config.nextLeapSecs = 0;
    * RXN_Set_Configuration(&config);
    * \endcode
    *
    */
    unsigned short RXN_Set_Configuration(const RXN_config_t* config);

    /**
    * \brief 
    * Get current configuration structure
    *
    * \param config [OUT] 
    * A RXN_config_t structure with the current configuration parameters.
    *   
    * \returns RXN_SUCCESS
    * If the function completes successfully.
    * 
    * \return RXN_FAIL
    * Any errors.
    * 
    * \details
    * Use this to get the configuration parameters in the RXN_config_t structure.
    *
    * \note
    * The configuration parameters will be set to default values 
    * if RXN_Read_Config() abstraction function call fails.
    *
    * \since
    * 1.0.0
    *
    * \see
    * RXN_Get_Configuration()
    *
    * <b>Example Usage</b>
    * \code
    * RXN_config_t config;          
    * RXN_Get_Configuration(&config);
    * \endcode
    *
    */
    unsigned short RXN_Get_Configuration(RXN_config_t* config);

    /**
    * \brief 
    * Provide the customer key to the library.
    *
    * \param customerKey [IN]
    * The pointer to a block of memory containing the customer key.
    * The key length, strlen(key) must be 16.
    *
    * \return RXN_SUCCESS
    * If the function completes successfully.
    *
    * \return RXN_FAIL
    * If the function fails. 
    *
    * \attention
    * This function must be called with a valid key before
    * RXN_Decode() can be used.
    *
    * \since
    * 1.0.0
    *
    */
    unsigned short RXN_Set_Customer_Key(
        const char* customerKey
        );

    /**
    * \brief
    * Returns the maximum number of possible PRNs for this constellation.
    * 
    * \param constel [IN] 
    * The constellation
    * \return The number of PRNs this constellation can hold
    *
    * \since
    * 1.0.0
    */
    unsigned char getMaxNumPrns(RXN_constel_t constel);
    int getSeedData(char *filename,unsigned char **data,int *len,int lat,int lon,int accuracy);
    unsigned short seedCheckAction(RXN_constel_t type,RXN_section_types_t sectionid,unsigned char *data,int len,int *need_download);
    int updateEeInfo(RXN_constel_t type,unsigned char *data,int len);
    int loadFile(char *filename,unsigned char **data,int *len);
    unsigned short RXN_RT_CRC_Check(unsigned char* data, const unsigned int size);
    unsigned short RXN_Get_RT_Ephemeris(supl_assist_t* ephemeris,unsigned char* data, const unsigned int size);

#ifdef __cplusplus
} // extern "C"
#endif

#endif // RXN_API_H
