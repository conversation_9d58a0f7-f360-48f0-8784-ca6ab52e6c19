/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/******************************************************************************
*               MODULE IMPLEMENTATION FILE
*******************************************************************************
* Title: Voice path control
*
* Filename: vpath_ctrl.h
*
* Authors: <AUTHORS>
*
* Description: DSP I/F over IPC for voice path modules control
*
* Last Updated:
*
* Notes:
******************************************************************************/
#ifndef VOICE_PATH_CONTROL_H_
#define VOICE_PATH_CONTROL_H_


/*----------- Local include files --------------------------------------------*/
#include "gbl_types.h"
#include "IPCComm.h"
#include "audio_bind.h"

/*----------- Global defines -------------------------------------------------*/

/*----------- Global macro definitions ---------------------------------------*/
#define MSA_VOICE_HANDOVER_START()   								 \
							{  										 \
								vpathHandover(TRUE); 			     \
							}
#define MSA_VOICE_HANDOVER_END() 									 \
							{  										 \
								vpathHandover(FALSE); 				 \
							}


/*----------- Global type definitions ----------------------------------------*/

/*----------- Extern definition ----------------------------------------------*/
#ifndef _VOICE_PATH_CONTROL_NO_EXTERN_
  #define EXTERN extern
#else
  #define EXTERN
#endif /* _VOICE_PATH_CONTROL_NO_EXTERN_ */

/*----------- Global variable declarations -----------------------------------*/

EXTERN unsigned short _lastDtmfFreq1, _lastDtmfFreq2;
EXTERN UINT32       _maxStructPrintPacketSize;

/*----------- Global constant definitions ------------------------------------*/
#define ENCODER_RESET_BIT			0x0001
#define DECODER_RESET_BIT			0x0002
#define DTX_MODE_UPDATE_BIT			0x0004
#define VOCODER_CHANGE_BIT			0x0008
#define VOCODER_RATE_BIT			0x0010
#define VOCODER_TYPE_BIT			0x0020      

#define ENCODER_RESET_SHIFT			0
#define DECODER_RESET_SHIFT			1
#define DTX_MODE_UPDATE_SHIFT		2
#define VOCODER_CHANGE_SHIFT		3
#define VOCODER_RATE_SHIFT			4

#define VOCODER_LOOP_BIT			 0x0001
#define PCM_LOOP_BIT				 0x0002
#define DRIFT_COMPENSATION_OFF_BIT	 0x0008
#define DRIFT_REPORT_BIT			 0x0010
#define DAI_DECODER_TEST_BIT	 	 0x0020
#define DAI_ENCODER_TEST_BIT		 0x0040
#define DAI_ACOUSTIC_TEST_BIT		 0x0080
#define PRINT_RX_SPEECH_BIT          0x0100
#define PRINT_PCM_TEST_BIT		     0x0800
#define VOICE_PATH_MODE_BITS		 0x3000  /* '00' - VE rate is according to Vocoder Type, '01'-VE is 8 kHz, '10'- VE is 16 kHz, '11'- Reserved */

#define VOCODER_LOOP_SHIFT			 0
#define PCM_LOOP_SHIFT				 1
#define DRIFT_COMPENSATION_OFF_SHIFT 3
#define DRIFT_REPORT_SHIFT			 4
#define DAI_DECODER_TEST_SHIFT	 	 5
#define DAI_ENCODER_TEST_SHIFT		 6
#define DAI_ACOUSTIC_TEST_SHIFT		 7
#define VOICE_PATH_MODE_SHIFT		 12

#define NUM_OF_VOICE_TEST_CONTROLS		1
#define NUM_OF_VOCODER_CONTROLS			5       //Append one short for ADSP. last one: 0: PCM; 1: AMR

//Jackie, 20110613, add a field 'Dual_Mic'(0:Single mic, 1: Dual Mic)
//#define CONFIG_CODEC_DB_CMD_LENGTH		14
#define CONFIG_CODEC_DB_CMD_LENGTH		15

#define VOICE_CONTROL_CMD_LENGTH		2
#define VOLUME_CONTROL_CMD_LENGTH 		3
#define SIDETONE_CONTROL_CMD_LENGTH     3  /* Currently no taps; gain only */
#define EC_TRESHOLD_CMD_LENGTH     		5
#define MUTE_CONTROL_CMD_LENGTH 		3
#define NS_CONTROL_CMD_LENGTH 			5
#define EC_CONTROL_CMD_LENGTH 			11

#define ECMODE_CONTROL_CMD_LENGTH 	    3

#define EQ_CONTROL_NUM_FILTER_COEF		15

#define AVC_CONTROL_CMD_LENGTH 			17

#if defined (SAMSUNG_NXP)
	#define VOICE_START_COMMAND_LENGTH	8
#else
	#define VOICE_START_COMMAND_LENGTH	7
#endif
#define DTMF_CONTROL_CMD_LENGTH 		7
#define AUX_MODE_CONTROL_CMD_LENGTH 	1
#define EC_TEST_CMD_LENGTH 				3
#define NS_TEST_CMD_LENGTH 				2
#define AVC_TEST_CMD_LENGTH 			2
#define SELF_INVOCATION_CMD_LENGTH 		3
#define SELF_INVOCATION_CMD_MIN_LENGTH  1
#define DITHER_GEN_CONTROL_CMD_LENGTH	8
#define CTM_CONTROL_CMD_LENGTH          2

#define HLPF_CONTROL_CMD_LENGTH 9  //count in short

#define COMPANDER_MODE_CMD_LENGTH 		3
#define COMPANDER_CONTROL_CMD_LENGTH 	1
#if defined (SAMSUNG_NXP)
#define SPEECH_PLUGIN_CONTROL_CMD_LENGTH 2
#define SPEECH_PLUGIN_MODULE_CONTROL_CMD_LENGTH 2
#endif


#ifdef OPT_IPC
#define IPC_CONTROL_CMD_LENGTH          4
#endif

#define  AUC_SP                		0x0001
#define  AUC_VAD               		0x0002
#define  AUC_SID_1             		0x0800
#define  AUC_SID_2             		0x1000
#define  AUC_SID               		(AUC_SID_1 | AUC_SID_2)
#define  AUC_AMR_NO_DATA       		0x2000
#define  AUC_AMR_SID_UPDATE    		0x4000
#define  AUC_AMR_SID_FIRST     		0x8000


//VOICE_HANDOVER
#define VOCODER_PAUSE_BIT			  0x0001
#define VOCODER_PAUSE_SHIFT			  0

#define PCM_STREAM_RECORD_CONTROL_MASK			 0x0001
#define PCM_STREAM_PLAY_CONTROL_MASK           	 0x0002
#define PCM_STREAM_RECORD_SIDE_MASK				 0x000C
#define PCM_STREAM_PLAY_SIDE_MASK				 0x0030
#define PCM_STREAM_PLAY_OVERRIDE_CONTROL_MASK	 0x00C0
#define PCM_STREAM_VOICE_PATH_RATE_MASK          0x0400
#define PCM_STREAM_RECORD_CONTROL_SHIFT			 0
#define PCM_STREAM_PLAY_CONTROL_SHIFT            1
#define PCM_STREAM_RECORD_SIDE_SHIFT             2
#define PCM_STREAM_PLAY_SIDE_SHIFT               4
#define PCM_STREAM_PLAY_OVERRIDE_CONTROL_SHIFT 	 6
#define PCM_STREAM_VOICE_PATH_RATE_SHIFT         10



//voice path control cmd bitmap (note: bit 0 to 2 resereved)
#if defined (SAMSUNG_TOTORO_DIAMONDVOICE)
#define DIAMONDVOICE_ENABLE		(1 << 0 )
#else
#define RESERVED_0				(1 << 0 )
#endif
#define DUALMIC_ENABLE			(1 << 1 )
#if defined (SAMSUNG_LVVE)
#define LVVE_ENABLE		        (1 << 2 )
#else
#define RESERVED_2				(1 << 2 )
#endif
#define EC_ENABLE				(1 << 3 )
#define RES_ENABLE              (1 << 4 )

#if defined (SAMSUNG_WISEVOICE)
#define WISEVOICE_ENABLE		(1 << 5)
#else
#define RESERVED_5				(1 << 5 )
#endif

#define TX_NS_ENABLE            (1 << 6 )
#define RX_NS_ENABLE            (1 << 7 )
#define RESERVED_8              (1 << 8 )
#define RESERVED_9              (1 << 9 )
#define TX_EQU_ENABLE           (1 << 10)
#define RX_EQU_ENABLE           (1 << 11)
#define RESERVED_12             (1 << 12)
#define RESERVED_13             (1 << 13)
#define TX_AGC_ENABLE           (1 << 14)
#define RX_AVC_ENABLE           (1 << 15)
#define RESERVED_16             (1 << 16)
#define RESERVED_17            (1 << 17)
#define TX_VOLUME_ENABLE        (1 << 18)
#define RX_VOLUME_ENABLE        (1 << 19)
#define SIDE_TONE_ENABLE        (1 << 20)
#define DITHER_GENERATOR_ENABLE (1 << 21)
#define CTM_ENABLE              (1 << 22)
#define CTM_RESET               (1 << 23)

#define TX_HPF_ENABLE           (1 << 26)
#define RX_HPF_ENABLE           (1 << 27)
#define TX_LPF_ENABLE           (1 << 28)
#define RX_LPF_ENABLE           (1 << 29)


#define DEFAULT_VOICE_PATH_CONTROL_CMD   					   \
									((EC_ENABLE)        |  \
									 (RES_ENABLE)       |  \
									 (TX_NS_ENABLE)     |  \
									 (RX_NS_ENABLE)     |  \
									 (TX_EQU_ENABLE)    |  \
									 (RX_EQU_ENABLE)    |  \
									 (TX_AGC_ENABLE)    |  \
									 (RX_AVC_ENABLE)    |  \
									 (TX_VOLUME_ENABLE) |  \
									 (RX_VOLUME_ENABLE))

//TODO: define for other scenarios
#define VPATH_MODULES_SETTINGS           DEFAULT_VOICE_PATH_CONTROL_CMD

#define MAX_LARGE_RX_TX_BUFFER_SIZE 1500

enum
{
	OPR_ON  = 0,
	OPR_OFF = 1,
	OPR_NC  = 2
};

typedef enum
{
	BT_APPLIANCE_CLOCK,
	NON_BT_APPLIANCE_CLOCK
} CodecClock;


typedef enum
{
	PCM_STREAM_OFF			   = 0,
	PCM_STREAM_TO_NEAR_SIDE    = 1,
	PCM_STREAM_TO_FAR_SIDE 	   = 2,
	PCM_STREAM_TO_BOTH_SIDES   = 3,
	PCM_STREAM_FROM_NEAR_SIDE  = PCM_STREAM_TO_NEAR_SIDE,
	PCM_STREAM_FROM_FAR_SIDE   = PCM_STREAM_TO_FAR_SIDE,
	PCM_STREAM_FROM_BOTH_SIDES = PCM_STREAM_TO_BOTH_SIDES,

	PCM_STREAM_NO_COMMAND,

	PCM_STREAM_LAST_COMMAND,
	PCM_NUM_OF_STREAM_COMMANDS = PCM_STREAM_LAST_COMMAND
} PcmStreamCommand;

typedef enum
{
	PCM_STREAM_PLAY_NO_OVERRIDE			= 0,
	PCM_STREAM_PLAY_OVERRIDE_NEAR_SIDE  = 1,
	PCM_STREAM_PLAY_OVERRIDE_FAR_SIDE   = 2,
	PCM_STREAM_PLAY_OVERRIDE_BOTH_SIDES = 3
} PcmStreamPlayOverride;
typedef enum
{
	CODEC_IF 				      = 0,
	BLUETOOTH_IF				  = 1,
	DAI_IF                        = 2,
	CODEC_AND_DAI_IF              = 3,
	DYNAMIC_IF					  = 4
} CodecIfConfig;


//------------------------------------------------------------------------------------------------------------------------


//-------------------------------------------------------------------------------------------------------------------------



/*dsp configurations*/
#define  SSP_CONFIG_FOR_VOICE_ON_I2S 0x00
#define  SSP_CONFIG_FOR_BLUETOOTH 	 0x01
#define  SSP_DYNAMIC_CONFIG			 0X02

#define  CODEC_DEVICE			     0x00
#define  DAI_DEVICE				 	 0x01
#define  BOTH_DEVICES		 	     0x02




typedef enum
{
	GSSP0_PORT = 0,
	GSSP1_PORT,

	NUM_OF_SSP_PORTS
} DevicePort;


/*----------- Global function prototypes -------------------------------------*/
BOOL vpathSetDSPActiveControl(BOOL is_on);
BOOL vpathCodecDBConfig(UINT16 *sspConfigData);
void vpathVocoderCtrl(UINT16 ctrlFlags, UINT16 dtxOnOff, UINT16 vocoderType, UINT16 vocoderRate);
BOOL vpathHandover(BOOL pause);

BOOL vpathStart(UINT32 voiceControl,
				UINT16 voiceAuxMode,
				UINT16 vocoderType,
				UINT16 encoderMode,
				UINT16 voiceTestControl,
				UINT16 dtxFlag);
BOOL vpathStartDefault(void);
BOOL vpathSelfInvocation(BOOL pcmSelfInvoked, BOOL encSelfInvoked, BOOL decSelfInvoked);
BOOL vpathIsPcmSelfInvoked(void);
BOOL vpathIsEncSelfInvoked(void);
BOOL vpathIsActive(void);
BOOL vpathVoiceTestControl(UINT16 voiceTestControl);
BOOL vpathStop(BOOL notifyPM);
BOOL vpathAuxModeControl(UINT16 data);
void vpathCodecClockSet(CodecClock clock);
BOOL vpathVolumeControl(UINT16 gainType, UINT16 gain_8k, UINT16 gain_16k);
BOOL vpathSideToneControl(UINT16 voicePathRate, UINT16 *sideToneGainData);
BOOL vpathECThreshold(UINT16 voicePathRate, UINT16 RefThExp, UINT16 PeakInThExp, UINT16 resRefThExp);
BOOL vpathMuteControl(UINT16 muteSide, BOOL muteOnOff, UINT16 muteType);

BOOL vpathPcmStreamControl(PcmStreamCommand pcmPlayCmd,
						   PcmStreamCommand pcmRecordCmd,
						   PcmStreamPlayOverride pcmPlayOverride,
						   UINT16 *pcmRecordBuffer1,
						   UINT16 *pcmRecordBuffer2,
						   UINT16 *pcmPlayBuffer1,
						   UINT16 *pcmPlayBuffer2);

BOOL vpathECModeControl(UINT16 voicePathRate,
                    UINT16 dir,
                    UINT16 Mode);

BOOL vpathDTMFControl(UINT16 *data, UINT16 cmdLength);
BOOL vpathAVCControl(UINT16 *data, UINT16 length);
BOOL vpathDebugCmd(UINT16 opcode, UINT16 length, UINT16 *data);
UINT16 vpathGetVoiceTestControl(void);
BOOL vpathCodecIfChange(unsigned short devicePort);
void vpathCodecClockStop(void);
UINT32 vpathGetVoiceControl(void);
BOOL vpathVoiceControl(UINT32 voiceControl);
BOOL vpathDitherGen(UINT16 *ditherGenConfig);
BOOL vpathCTMControl(UINT16 *ctmControl);
#if defined (SAMSUNG_NXP)
BOOL vpathSpeechPluginControl(UINT16 *speechPluginControl);
#endif
void vpathSetPcmLoop(UINT32 OnOff,UINT32 pcmPrint);
void vpathSetVocoderLoop(UINT32 OnOff,UINT32 pcmPrint);
void vpathSetSelfInvocation(UINT32 OnOff);
void vpathStartForce(int wb, int rxPcmStreamControl);
void vpathStopForce(void);


BOOL vpathSetCompanderMode(UINT16 instruction, UINT32 timestamp);
BOOL vpathCompanderControl(UINT16 on);

BOOL vpathRxbControl(UINT16 *data, UINT16 length);

void vpathECallDataSet(unsigned long num_of_args_present, unsigned short cmd_id, unsigned long param1, void *pData, unsigned long dataLength);
void vpathECallDataGet(unsigned short op, unsigned short param1);
void vpathECallVoiceSet(unsigned short cmd_id, unsigned short param1, unsigned short param2);
void vpathECallVoiceGet(unsigned short cmd_id, unsigned short res_id);

void vpathDisableMFPRGSSP(void);
void vpathEnableMFPRGSSP(void);
BOOL vpathGSSPControl(BOOL GSSP_switch);
BOOL vpathGSSPRead(void);
BOOL vpathDTMFDetectionControl(DTMFDetectionControl_t *pDTMFDetectionControl);
BOOL vpathDmaIntInd(unsigned short txchan, unsigned short rxchan);

BOOL vpathHeadphoneTypeDetect(void);
BOOL vpathSetCodecSspa(UINT16 codec, UINT16 codec2);
BOOL vpathSetSspaTestMode(UINT16 on, UINT16 l, UINT16 r);

BOOL vpathVibrationControl(UINT16 onoff, UINT16 hfpMode, UINT16 gain,UINT16 coeff,UINT16 initalValue);
BOOL vpathFMControl(UINT16 onoff);
BOOL vpathSetAudioPLL(BOOL is_on, BOOL bitClk_on);
BOOL vpathSpeakerProtection(INT16 maxRms, INT16 Alpha, INT16 N2cos);
BOOL vpathMp3HpfControl(UINT16 hfpMode);





#undef EXTERN

#endif /*VOICE_PATH_CONTROL_H_*/

