#include "ui_os_api.h"
#include "ui_log_api.h"

#include "plat_basic_api.h"

#define TIMER_FREE 0
#define TIMER_BUSY 1

typedef struct
{
    OSATimerRef TimerRef;
    u32 TimerStatus;
    u32 timer_context[UOS_EVT_MBX_SIZE];
    u32 TimerMode;
	u32 Ticks;
	const char *TimerName;
    u8 Mbx;
#ifdef PERIOD_TIMER_OPTIMIZE
	u32 num_of_misses;
	u32 in_size;
	u32 out_size;
#endif
} TimerCtrl_t;

static TimerCtrl_t TimerSet[UOS_NB_MAX_TIMER_ENVELOPPE];

static u8 __get_timer(void);
void UOS_InitTimer (void)
{
    u8 id;
	u8 dummy_timer_id;
    OS_STATUS status = OS_FAIL;

    for (id = 0; id < UOS_NB_MAX_TIMER_ENVELOPPE; id++)
    {
        status = OSATimerCreate(&(TimerSet[id].TimerRef));
        ASSERT(OS_SUCCESS == status);
		TimerSet[id].TimerStatus = TIMER_FREE;
		TimerSet[id].Mbx = INVALID_MSGQ_ID;
    }

	// avoid to return timer_id: 0
	dummy_timer_id = __get_timer();
	TimerSet[dummy_timer_id].TimerName = "dummy_timer";
}

static u8 __get_timer(void)
{
	u8 i;
	u8 id;
	static u8 base_id = 0;
	static u8 timer_mutex = INVALID_MUTEX_ID;

	if (INVALID_MUTEX_ID == timer_mutex)
	{
		timer_mutex = UOS_NewMutex("get_timer");
	}	

	UOS_TakeMutex(timer_mutex);
	for (i = 0; i < UOS_NB_MAX_TIMER_ENVELOPPE; i++)
	{
		id = (base_id + i) % UOS_NB_MAX_TIMER_ENVELOPPE;
		if (TIMER_FREE == TimerSet[id].TimerStatus) {
			TimerSet[id].TimerStatus = TIMER_BUSY;
			base_id = id + 1;
			break;
		}
	}
	UOS_ReleaseMutex(timer_mutex);

	ASSERT(i < UOS_NB_MAX_TIMER_ENVELOPPE);

	return id;
}

static void __release_timer(u8 id)
{
	ASSERT(id < UOS_NB_MAX_TIMER_ENVELOPPE);
	// if the timer released already
	if (TIMER_FREE == TimerSet[id].TimerStatus)
		return;/* Maybe timer is single mode, it has been killed by other task*/
	
	OSATimerStop(TimerSet[id].TimerRef);

#if 0
	// for debug, and then you can see the timer info when it be released
	TimerSet[id].Mbx	= INVALID_MSGQ_ID;
	TimerSet[id].Ticks	= 0;
	TimerSet[id].TimerName = NULL;
#endif
	TimerSet[id].TimerStatus = TIMER_FREE;
}

#define MAX_BLOCKED_TIMER_EVENT_SIZE 16
static void __timer_callback(UINT32 id)
{
	void (*Fun)(void *);
	void *Param;
	u32 val;
	u32 free_size;

	// if the timer has been released already
	if (TIMER_FREE == TimerSet[id].TimerStatus) {
//		raw_uart_log("Warning:__timer_callback with a released timer id %d\n", id);
		return;
	}

	switch (TimerSet[id].TimerMode) {
	case UI_TIMER_MODE_SINGLE:
		UOS_SendMsg (TimerSet[id].timer_context, TimerSet[id].Mbx, UOS_SEND_EVT);
        __release_timer(id);
		break;

	case UI_TIMER_MODE_PERIODIC:
#ifdef PERIOD_TIMER_OPTIMIZE
		TimerSet[id].num_of_misses++;
		ASSERT(TimerSet[id].in_size >= TimerSet[id].out_size);
		// the number of timer message in queue
		val = TimerSet[id].in_size - TimerSet[id].out_size;
		if (val < MAX_BLOCKED_TIMER_EVENT_SIZE) {
			free_size = UOS_MsgQAvailableSize(TimerSet[id].Mbx);
			if (free_size > MAX_BLOCKED_TIMER_EVENT_SIZE) {
				val = (TimerSet[id].num_of_misses > MAX_BLOCKED_TIMER_EVENT_SIZE) ? MAX_BLOCKED_TIMER_EVENT_SIZE : TimerSet[id].num_of_misses;				
				while (val--)
				{
					TimerSet[id].num_of_misses--;
					TimerSet[id].in_size++;
					UOS_SendMsg (TimerSet[id].timer_context, TimerSet[id].Mbx, UOS_SEND_EVT);				
				}
			}
		}
#else
		UOS_SendMsg (TimerSet[id].timer_context, TimerSet[id].Mbx, UOS_SEND_EVT);			
#endif
		break;

	case UI_TIMER_FUNC_MODE_SINGLE:
	case UI_TIMER_FUNC_MODE_PERIODIC:
		Fun = (void(*)(void *))TimerSet[id].timer_context[0];
    	ASSERT(NULL != Fun);
		Param = (void *)TimerSet[id].timer_context[1];
		Fun(Param);
		break;

	default:
		break;
	}
}

static u8 __StartFunctionTimer_ID(u8 id, u32 Ticks, void (*Function)(void *), void *Param, uint8 nMode, const char *pName)
{
    OS_STATUS status = OS_FAIL;

    TimerSet[id].timer_context[0] = (u32)Function;
    TimerSet[id].timer_context[1] = (u32)Param;	
	TimerSet[id].Ticks = Ticks;
	TimerSet[id].TimerName = pName;

	TimerSet[id].TimerMode = nMode;

	if (UI_TIMER_FUNC_MODE_SINGLE == nMode) {
		status = OSATimerStart(TimerSet[id].TimerRef, Ticks, 0, __timer_callback, id);
	} else if (UI_TIMER_FUNC_MODE_PERIODIC == nMode) {
		status = OSATimerStart(TimerSet[id].TimerRef, Ticks, Ticks, __timer_callback, id);	
	} else {
		ASSERT(0);
	}

    ASSERT(OS_SUCCESS == status);

	return id;
}

/*Please call the UOS_get_FunctionTimer, after used, need call UOS_KillFunctionTimer to release*/
u8 UOS_StartFunctionTimer_periodic(u8 id, u32 Ticks, void (*Function)(void *), void *Param, const char *pName)
{

	return __StartFunctionTimer_ID(id, Ticks, Function, Param, UI_TIMER_FUNC_MODE_PERIODIC, pName);
}

/*Please call the UOS_get_FunctionTimer, after used, need call UOS_KillFunctionTimer to release*/
u8 UOS_StartFunctionTimer_single(u8 id, u32 Ticks, void (*Function)(void *), void *Param, const char *pName)
{

	return __StartFunctionTimer_ID(id, Ticks, Function, Param, UI_TIMER_FUNC_MODE_SINGLE, pName);
}

u8 UOS_KillFunctionTimer(u8 TimerId)
{	
	ASSERT(TimerId < UOS_NB_MAX_TIMER_ENVELOPPE);

	__release_timer(TimerId);
	return TimerId;
}

static u8 __start_timerEx(u32 Ticks, u32 id, u32 nMode, u8 Mbx, const char *pName)
{
    u8 FreeTimerId;
    OS_STATUS status = OS_FAIL;

	//fixme-asr: need be fix here!
	if(Ticks < 10) 
	{
		raw_uart_log("%s alloc short timer, ticks %d with mode %d\n", pName, Ticks, nMode);
//		Ticks = 10;
	}
    FreeTimerId = __get_timer();
    TimerSet[FreeTimerId].Mbx = Mbx;
    TimerSet[FreeTimerId].timer_context[0] = id;
    TimerSet[FreeTimerId].timer_context[1] = nMode;
	TimerSet[FreeTimerId].Ticks = Ticks;
	TimerSet[FreeTimerId].TimerName = pName;
	TimerSet[FreeTimerId].TimerMode = nMode;

#ifdef PERIOD_TIMER_OPTIMIZE
	// record the timerset's index
    TimerSet[FreeTimerId].timer_context[2] = FreeTimerId;
	TimerSet[FreeTimerId].num_of_misses = 0;
	TimerSet[FreeTimerId].in_size = 0;
	TimerSet[FreeTimerId].out_size = 0; 
#endif

    if (nMode == UI_TIMER_MODE_PERIODIC) {		
		status = OSATimerStart(TimerSet[FreeTimerId].TimerRef, Ticks, Ticks, __timer_callback, FreeTimerId);	
    } else {
		status = OSATimerStart(TimerSet[FreeTimerId].TimerRef, Ticks, 0, __timer_callback, FreeTimerId);
    }

    ASSERT(OS_SUCCESS == status);

	return FreeTimerId;
}

BOOL UOS_StartTimerEX(HANDLE hTask, UINT16 nTimerId, UINT8 nMode, UINT32 Ticks, const char *TimerName)
{
    TASK_HANDLE* pHTask = (TASK_HANDLE*)hTask;

	if (nTimerId >= UOS_CMID_TIMER_ID_END_)
		return FALSE;

    if (Ticks > 0x7FFFFFFF)
        return FALSE;

	if (pHTask)
	{
	    if (INVALID_MSGQ_ID != pHTask->nMailBoxId)
	    {
	    	if ((nMode != UI_TIMER_MODE_SINGLE) && (nMode != UI_TIMER_MODE_PERIODIC))
				ASSERT(0);
			__start_timerEx(Ticks, HVY_TIMER_IN + nTimerId, nMode, pHTask->nMailBoxId, TimerName);
			return TRUE;
	    }
	}

    return FALSE;
}

static void __stop_timerEx(u32 _id, u32 data, u8 Mbx)
{
    u8 id;
	for (id = 0; id < UOS_NB_MAX_TIMER_ENVELOPPE; id++)
	{
		// fixme, need to compare the timer_context[1]?
		if (TIMER_BUSY == TimerSet[id].TimerStatus &&
			(TimerSet[id].timer_context[0] == _id) &&
			(TimerSet[id].Mbx == Mbx))
		{
			break;
		}
	}
#if 1
	if (id == UOS_NB_MAX_TIMER_ENVELOPPE)
	{
//		raw_uart_log("Warning: __stop_timer try to stop the timer id = 0x%x\n", _id);
		return;
	}
#else
	ASSERT(id < UOS_NB_MAX_TIMER_ENVELOPPE);
#endif
	__release_timer(id);
}

BOOL UOS_KillTimerEX(HANDLE hTask,UINT16 nTimerId)
{
    TASK_HANDLE* pHTask = (TASK_HANDLE*)hTask;
    
	if (nTimerId >= UOS_CMID_TIMER_ID_END_)
		return FALSE;
	
    if (pHTask)
    {    	
		if (INVALID_MSGQ_ID != pHTask->nMailBoxId)
		{
			__stop_timerEx(HVY_TIMER_IN + nTimerId, 0, pHTask->nMailBoxId);
			return TRUE;
		}
    }

    return FALSE;
}

u8 UOS_get_FunctionTimer(void)
{
	return __get_timer();
}

#ifdef PERIOD_TIMER_OPTIMIZE
void UOS_PeriodTimerHandleRsp(u8 timer_id, u32 event_id)
{

	if (TIMER_BUSY == TimerSet[timer_id].TimerStatus &&
		TimerSet[timer_id].timer_context[0] == event_id) {
		if ((TimerSet[timer_id].out_size + 1) > TimerSet[timer_id].in_size) {
			raw_uart_log("Warning: invalid timer event: timer_id:%x event_id:%x\n", timer_id, event_id);
		} else {			
			TimerSet[timer_id].out_size++;
		}
	}
}
#endif
