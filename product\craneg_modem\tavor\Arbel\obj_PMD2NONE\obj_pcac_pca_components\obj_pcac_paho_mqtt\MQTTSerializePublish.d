\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTSerializePublish.c
\pcac\paho_mqtt\mqtt\MQTTSerializePublish.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTPacket.h
\pcac\paho_mqtt\mqtt\MQTTPacket.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTConnect.h
\pcac\paho_mqtt\mqtt\MQTTConnect.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTPublish.h
\pcac\paho_mqtt\mqtt\MQTTPublish.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTSubscribe.h
\pcac\paho_mqtt\mqtt\MQTTSubscribe.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTUnsubscribe.h
\pcac\paho_mqtt\mqtt\MQTTUnsubscribe.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTFormat.h
\pcac\paho_mqtt\mqtt\MQTTFormat.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\StackTrace.h
\pcac\paho_mqtt\mqtt\StackTrace.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_paho_mqtt/MQTTSerializePublish.o : \pcac\paho_mqtt\mqtt\MQTTPacket.h
\pcac\paho_mqtt\mqtt\MQTTPacket.h:
