/**
 * @file setting_volte_switch.h
 *
 */
#ifndef SETTING_PROFILE_SWITCH_H
#define SETTING_PROFILE_SWITCH_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/
#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lvgl.h"
#include "lv_watch_conf.h"
#else
#include "../../../lvgl/lvgl.h"
#include "../../../lv_watch_conf.h"
#endif

#if USE_LV_WATCH_SETTING_PROFILE != 0
/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/
typedef struct {
	lv_watch_obj_ext_t lv_watch_obj;
	/*New data for this type */
	lv_obj_t * cb_normal;
	lv_obj_t * cb_ring;
	lv_obj_t * cb_vib;
	lv_obj_t * cb_none;
	#if USE_LV_WATCH_LEZHI_MODIFY_PROFILE != 0
	lv_obj_t * cb_disturb;
	#endif
} lv_profile_switch_obj_ext_t;

/**********************
 * GLOBAL PROTOTYPES
 **********************/
lv_res_t profile_switch_create_btn_action(lv_obj_t * btn,lv_event_t e);
uint8_t read_setting_profile_status(void);
uint32_t write_setting_profile_status(uint8_t value);

/**********************
 *      MACROS
 **********************/

#endif /*USE_LV_WATCH_VOLTE_SWTICH*/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*SETTING_VOLTE_SWITCH_H*/
