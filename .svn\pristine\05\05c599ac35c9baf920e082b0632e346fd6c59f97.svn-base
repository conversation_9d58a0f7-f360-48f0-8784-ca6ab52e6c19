/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "LPP-Messages"
 * 	found in "../LPP.asn"
 * 	`asn1c -fcompound-names -funnamed-unions -gen-PER`
 */

#ifndef	_GNSS_ReferenceTimeReq_H_
#define	_GNSS_ReferenceTimeReq_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BOOLEAN.h>
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct GNSS_ID;

/* GNSS-ReferenceTimeReq */
typedef struct GNSS_ReferenceTimeReq {
	struct GNSS_ReferenceTimeReq__gnss_TimeReqPrefList {
		A_SEQUENCE_OF(struct GNSS_ID) list;
		
		/* Context for parsing across buffer boundaries */
		asn_struct_ctx_t _asn_ctx;
	} gnss_TimeReqPrefList;
	BOOLEAN_t	*gps_TOW_assistReq	/* OPTIONAL */;
	BOOLEAN_t	*notOfLeapSecReq	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} GNSS_ReferenceTimeReq_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_GNSS_ReferenceTimeReq;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "GNSS-ID.h"

#endif	/* _GNSS_ReferenceTimeReq_H_ */
#include <asn_internal.h>
