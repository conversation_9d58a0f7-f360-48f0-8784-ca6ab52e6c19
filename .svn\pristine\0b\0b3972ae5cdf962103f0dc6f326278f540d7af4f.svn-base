/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "RRLP-Components"
 * 	found in "rrlp12_1_0.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_SeqOfGANSSTimeModel_R10_Ext_H_
#define	_SeqOfGANSSTimeModel_R10_Ext_H_


#include <asn_application.h>

/* Including external dependencies */
#include <asn_SEQUENCE_OF.h>
#include <constr_SEQUENCE_OF.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct GANSSTimeModelElement_R10_Ext;

/* SeqOfGANSSTimeModel-R10-Ext */
typedef struct SeqOfGANSSTimeModel_R10_Ext {
	A_SEQUENCE_OF(struct GANSSTimeModelElement_R10_Ext) list;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} SeqOfGANSSTimeModel_R10_Ext_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_SeqOfGANSSTimeModel_R10_Ext;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "GANSSTimeModelElement-R10-Ext.h"

#endif	/* _SeqOfGANSSTimeModel_R10_Ext_H_ */
#include <asn_internal.h>
