/******************************************************************************
 * *(C) Copyright 2008 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
/* ******************************************************************************/
#include "../libatchannel/atchannel.h"
#include "../../board.h"
#include "dataapi.h"
#include "asr-ril.h"
#include "modem-device.h"
#include "ril-requestdatahandler.h"
#include "ril-mm.h"
#include "ril-ps.h"
#include <string.h>
#include <stdio.h>
#include <errno.h>
#include <ctype.h>

namespace watch_ril {

#define DATA_PROFILE_DEFAULT 0
#define DATA_PROFILE_IMS 2
#define DATA_PROFILE_MMS 5
#define DATA_PROFILE_SOS 4    //use TYPE_MOBILE_CBS to active SOS PDP

#define PDP_REFERNCE_FAIL_GENERIC -1
#define PDP_REFERNCE_FAIL_ACTIVATION -2

#define IMS_CAPABILITY_VIDEO 8

#ifndef DEFAULT_MTU_SIZE
#define DEFAULT_MTU_SIZE 1400
#endif

#define PROP_PPP_CID "sys.ppp.cid"
#define PHONE_SWITCH_MODE_KEY "persist.sys.phone.switch.mode"

typedef RIL_Data_Call_Response_v11 RIL_Data_Call_Response_ps;

enum PdpType {
    PDP_TYPE_UNSUPPORTED = 0,
    PDP_TYPE_IP = 1 << 0,
    PDP_TYPE_IPV6 = 1 << 1,
    PDP_TYPE_IPV4V6 = PDP_TYPE_IP | PDP_TYPE_IPV6
};

typedef enum {
    NORMAL_STATE = 0,
    WAIT_INTERNET_CONNECTED_STATE = 1,
    WAIT_TAU_STATE = 2,
    WAIT_TAU_COMPLETED = 3
} DataRetryState;

typedef enum {
    VZW_DATA_RETRY = 0x01
} VzwTestOptions;

typedef struct imsRegInfo_t {
    int regInfo;
    int extInfo;
} imsRegInfo;

struct PdpContextInfo {
    PdpContextInfo(int cid, char * pdp_type, char * apn, char * address, char * dnses, char * pcscf, int mtu)
    {
        this->cid = cid;
        this->pdp_type = pdp_type;
        this->apn = apn;
        this->address = address;
        this->dnses = dnses;
        this->pcscf = pcscf;
        this->mtu = mtu;
    };
    ~PdpContextInfo()
    {
        UI_FREE(pdp_type);
        UI_FREE(apn);
        UI_FREE(address);
        UI_FREE(dnses);
        UI_FREE(pcscf);
    }

    int cid;
    char * pdp_type;
    char * apn;
    char * address;
    char * dnses;
    char * pcscf;
    int mtu;
};

#ifdef SUPPORT_RDP
// struct define for ps
// pdp context read dynamic parameters
typedef struct {
    int             cid;
    int             bearer_id;
    char      *     apn;
    char      *     localAddSubnetMask;
    char      *     gw_addr;
    char      *     DNS_prim_addr;
    char      *     DNS_sec_addr;
    char      *     PCSCF_prim_addr;
    char      *     PCSCF_sec_addr;
    int             IM_CN_Signal_Flg;
    //int             LIPA_indication;
} PDP_Context_RDP_parameters;

#endif


static int g_datacall_if_index[SIM_COUNT][MAX_DATA_CALLS];

static struct PdpContextInfo * gPdpContextInfoList[SIM_COUNT][MAX_DATA_CALLS];

static int no_ra_retry_count[SIM_COUNT];
#ifdef MMI_ASR_RIL_BRINGUP
static u8 ps_mutex = INVALID_MUTEX_ID;
#else
static pthread_mutex_t ps_mutex = PTHREAD_MUTEX_INITIALIZER;
#endif
static imsRegInfo sImsRegInfo[SIM_COUNT];
static int sVzwImsState[SIM_COUNT];
static char sOperNum[SIM_COUNT][PROPERTY_VALUE_MAX];
static int sLastMmsCid[SIM_COUNT] = {-1};

void ril_ps_globals_init()
{
    for(int socketId = 0; socketId < SIM_COUNT; socketId++) {
        for(int i = 0; i < MAX_DATA_CALLS; i++) {
            g_datacall_if_index[socketId][i] = -1;
        }
        no_ra_retry_count[socketId] = -1;
    }

    ps_mutex = uos_new_mutex();
}

static int startVzwDataRetryTest(RIL_SOCKET_ID socketId)
{
    const char * prop_name = (RIL_SOCKET_1 == socketId) ?
                             "persist.radio.vzwtestoptions" :
                             "persist.radio.vzwtestoptions2";
    int vzwTestOption = property_get_int32(prop_name, 0);
    RLOGD("%s, vzwTestOption is %x", __FUNCTION__, vzwTestOption);
    return (vzwTestOption & VZW_DATA_RETRY) == VZW_DATA_RETRY;
}

static int findV6Partner(int index, RIL_SOCKET_ID socketId)
{
    int i;
    for(i = 0; i < MAX_DATA_CALLS; i++) {
        if(g_datacall_if_index[socketId][i] == index && i != index)
            return i;
    }
    return -1;
}

#ifdef MMI_ASR_RIL_BRINGUP
#else
static void * netlinkEventHandler(void * arg)
{
    UNUSED(arg);
    RLOGD("NetlinkEventHandler Begin");
    ril_tls_set("RIL-NetLink");
    pthread_setname_np(pthread_self(), "RIL-NetLink");
    for(;;) {
        int fd = socket(PF_NETLINK, SOCK_RAW, NETLINK_ROUTE);
        if(fd < 0) {
            RLOGE("%s: socket error: %s", __FUNCTION__, strerror(errno));
            continue;
        }
        struct sockaddr_nl nladdr;
        socklen_t nladdr_len = sizeof(nladdr);
        memset(&nladdr, 0, sizeof(nladdr));
        nladdr.nl_family = AF_NETLINK;
        //nladdr.nl_pid = getpid();
        nladdr.nl_groups = RTMGRP_IPV6_IFADDR;
        if(bind(fd, (struct sockaddr *) &nladdr, sizeof(nladdr)) < 0) {
            RLOGE("Unable to bind route socket: %s", strerror(errno));
            close(fd);
            continue;
        }
        for(;;) {
            char buf[4096];
            ssize_t bytes = recvfrom(fd, buf, sizeof(buf), 0, (struct sockaddr *)&nladdr, &nladdr_len);
            if(bytes == -1) {
                RLOGW("NetlinkEventHandler: failed to recv from netlink socket");
                break;
            }

            /* Ignore message if it is not from kernel */
            if(nladdr.nl_pid != 0)
                continue;

            struct nlmsghdr * nlm;
            for(nlm = (struct nlmsghdr *)(void *)buf; NLMSG_OK(nlm, (size_t)bytes); nlm = NLMSG_NEXT(nlm, bytes)) {
                if(nlm->nlmsg_type != RTM_NEWADDR)
                    continue;
                struct ifaddrmsg * ifa = (struct ifaddrmsg *)NLMSG_DATA(nlm);
                RLOGD("NetlinkEventHandler: %d, %d", nlm->nlmsg_type, nlm->nlmsg_pid);
                if(ifa->ifa_family != AF_INET6 || ifa->ifa_scope != RT_SCOPE_UNIVERSE)
                    continue;
                char ifn[IF_NAMESIZE + 1];
                ModemDevice * modem = ModemDevice::get_current_modem(RIL_SOCKET_1);
                if(if_indextoname(ifa->ifa_index, ifn) && modem->is_interface_support_by_modem(ifn) && isInterfaceUp(ifn)) {
                    int i = modem->get_netcard_index(ifn);
                    if(g_datacall_if_index[RIL_SOCKET_1][i] != -1) {
                        notifyDataCallChanged(RIL_SOCKET_1);
                    }
#if (SIM_COUNT >= 2)
                    else if(g_datacall_if_index[RIL_SOCKET_2][i] != -1) {
                        notifyDataCallChanged(RIL_SOCKET_2);
                    }
#endif
                }
            }
        }
        close(fd);
    }
    return NULL;
}
#endif

void startNetlinkEventHandler()
{
#ifdef MMI_ASR_RIL_BRINGUP
#else
    pthread_t tid;
    pthread_attr_t attr;
    pthread_attr_init(&attr);
    pthread_attr_setdetachstate(&attr, PTHREAD_CREATE_DETACHED);
    pthread_create(&tid, &attr, netlinkEventHandler, NULL);
#endif
}

static void freePdpContextInfo(struct PdpContextInfo ** pp)
{
    struct PdpContextInfo * p = *pp;
    if(p) {
        delete p;
        *pp = NULL;
    }
}


static inline int isPdpActivated(int i, RIL_SOCKET_ID socketId)
{
    return gPdpContextInfoList[socketId][i] && gPdpContextInfoList[socketId][i]->address;
}

static void clearPdpContextInfoList(RIL_SOCKET_ID socketId)
{
    int i;
    for(i = 0; i < MAX_DATA_CALLS; i++) {
        freePdpContextInfo(&gPdpContextInfoList[socketId][i]);
    }
}

void clearDataCallState(RIL_SOCKET_ID socketId)
{
    int i;
    for(i = 0; i < MAX_DATA_CALLS; i++) {
#ifdef MMI_ASR_RIL_BRINGUP
#else
        disableInterface(socketId, i);
#endif
        g_datacall_if_index[socketId][i] = -1;
    }
    clearPdpContextInfoList(socketId);
}

static inline enum PdpType getPdpType(const char * protocol)
{
    if(!protocol) {
        return PDP_TYPE_UNSUPPORTED;
    } else if(strcasecmp(protocol, "IP") == 0) {
        return PDP_TYPE_IP;
    } else if(strcasecmp(protocol, "IPv6") == 0) {
        return PDP_TYPE_IPV6;
    } else if(strcasecmp(protocol, "IPv4v6") == 0) {
        return PDP_TYPE_IPV4V6;
    } else {
        return PDP_TYPE_UNSUPPORTED;
    }
}

void rilPsMutexLock(void)
{
#ifdef MMI_ASR_RIL_BRINGUP
    uos_take_mutex(ps_mutex);
#else
    pthread_mutex_lock(&ps_mutex);
#endif
}

void rilPsMutexUnlock(void)
{
#ifdef MMI_ASR_RIL_BRINGUP
    uos_release_mutex(ps_mutex);
#else
    pthread_mutex_unlock(&ps_mutex);
#endif
}

static int findInactivePdp(RIL_SOCKET_ID socketId, int begin)
{
    for(int i = begin; i < MAX_DATA_CALLS; i++) {
        if(g_datacall_if_index[socketId][i] == -1 && !isPdpActivated(i, socketId)) {
            return i;
        }
    }

    return -1;
}
#define INET_ADDRSTRLEN  16
#define INET6_ADDRSTRLEN 46

#define AF_UNSPEC       0
#define AF_INET         2
#if LWIP_IPV6
#define AF_INET6        10
#else /* LWIP_IPV6 */
#define AF_INET6        AF_UNSPEC
#endif /* LWIP_IPV6 */
#define PF_INET         AF_INET

static void getAddressesAndGateways(const char * ifname, char ** adrresses, char ** gateways)
{
    char ip4[INET6_ADDRSTRLEN] = {0};
    char ip6[INET6_ADDRSTRLEN] = {0};

#ifdef MMI_ASR_RIL_BRINGUP
#else
    getInterfaceAddr(AF_INET, ifname, ip4, sizeof(ip4));
    getInterfaceAddr(AF_INET6, ifname, ip6, sizeof(ip6));
#endif
    *adrresses = (char *)UI_MALLOC(sizeof("%s %s") + 100);
    sprintf(*adrresses, "%s %s", ip4, ip6);

    if(ip4[0]) {
        strcpy(ip4, "0.0.0.0");
    }
    if(ip6[0]) {
        strcpy(ip6, "::");
    }
    *gateways = (char *)UI_MALLOC(sizeof("%s %s") + 100);
    sprintf(*gateways, "%s %s", ip4, ip6);
}

static void freeDataCallResponses(RIL_Data_Call_Response_ps * pdpResponses, int num)
{
    int i;

    for(i = 0; i < num; i++) {
        UI_FREE(pdpResponses[i].type);
        UI_FREE(pdpResponses[i].addresses);
        UI_FREE(pdpResponses[i].ifname);
        UI_FREE(pdpResponses[i].dnses);
        UI_FREE(pdpResponses[i].gateways);
        UI_FREE(pdpResponses[i].pcscf);
    }
    UI_FREE(pdpResponses);
}

#define PDP_FAIL_REACTIVATION_REQ 39
#define PDP_FAIL_PDP_REJECT_ON_DSDS 0x3300
static inline int getSuggestedRetryTime(int fail_cause)
{
    switch(fail_cause) {
        case PDP_FAIL_OPERATOR_BARRED:
        case PDP_FAIL_MISSING_UKNOWN_APN:
        case PDP_FAIL_UNKNOWN_PDP_ADDRESS_TYPE:
        case PDP_FAIL_USER_AUTHENTICATION:
        case PDP_FAIL_ACTIVATION_REJECT_GGSN:
        case PDP_FAIL_SERVICE_OPTION_NOT_SUPPORTED:
        case PDP_FAIL_SERVICE_OPTION_NOT_SUBSCRIBED:
        //case PDP_FAIL_NSAPI_IN_USE:
        case PDP_FAIL_ONLY_IPV4_ALLOWED:
        case PDP_FAIL_ONLY_IPV6_ALLOWED:
        case PDP_FAIL_PROTOCOL_ERRORS:
        case PDP_FAIL_SIGNAL_LOST:
            // no retry
            return INT_MAX;
        case PDP_FAIL_REACTIVATION_REQ:
            // retry immediately
            return 0;
        case PDP_FAIL_PDP_REJECT_ON_DSDS:
            // retry 5 seconds later
            return (5 * 1000);
    }
    // no suggestion
    return -1;
}

static inline int getVzwDataRetryTime(int fail_cause)
{
    //do retry fail_cause is #8，#27，#29,#30,#31，#34，
    //#38，#39, #95，#96，#97，#98，#99,  #100, #101, #111, #112
#define PDP_FAIL_REGULAR_DEACTIVATION 38
#define PDP_FAIL_SEMANTICALLY_INCORRECT_MESSAGE 95
#define PDP_FAIL_INVALID_MANDATORY_INFORMATION 96
#define PDP_FAIL_MSGTYPE_NONEXISTENT_NOT_IMPLEMENTED 97
#define PDP_FAIL_MSGTYPE_NOTCOMPATIBLE_PROTOCOL_STATE 98
#define PDP_FAIL_INFOELE_NONEXISTENT_NOT_IMPLEMENTED 99
#define PDP_FAIL_CONDITIONAL_IE_ERROR 100
#define PDP_FAIL_APN_INCOMPATIBLE_EPS_BEARER_CONTEXT 112
    switch(fail_cause) {
        case PDP_FAIL_UNKNOWN_PDP_ADDRESS_TYPE:
        case PDP_FAIL_SERVICE_OPTION_NOT_SUPPORTED:
        case PDP_FAIL_SERVICE_OPTION_NOT_SUBSCRIBED:
        case PDP_FAIL_NSAPI_IN_USE:
        case PDP_FAIL_ONLY_IPV4_ALLOWED:
        case PDP_FAIL_ONLY_IPV6_ALLOWED:
        case PDP_FAIL_SIGNAL_LOST:
            // no retry
            return INT_MAX;
        case PDP_FAIL_OPERATOR_BARRED: //#8
        case PDP_FAIL_INSUFFICIENT_RESOURCES: //#26
        case PDP_FAIL_MISSING_UKNOWN_APN: //#27
        case PDP_FAIL_USER_AUTHENTICATION: //#29
        case PDP_FAIL_ACTIVATION_REJECT_GGSN: //#30
        case PDP_FAIL_ACTIVATION_REJECT_UNSPECIFIED: //#31
        case PDP_FAIL_SERVICE_OPTION_OUT_OF_ORDER: //#34
        case PDP_FAIL_REGULAR_DEACTIVATION: //#38
        case PDP_FAIL_REACTIVATION_REQ: //#39
        case PDP_FAIL_SEMANTICALLY_INCORRECT_MESSAGE: //#95
        case PDP_FAIL_INVALID_MANDATORY_INFORMATION: //#96
        case PDP_FAIL_MSGTYPE_NONEXISTENT_NOT_IMPLEMENTED: //#97
        case PDP_FAIL_MSGTYPE_NOTCOMPATIBLE_PROTOCOL_STATE: //#98
        case PDP_FAIL_INFOELE_NONEXISTENT_NOT_IMPLEMENTED: //#99
        case PDP_FAIL_CONDITIONAL_IE_ERROR: //#100
        case PDP_FAIL_PROTOCOL_ERRORS: //#111
        case PDP_FAIL_APN_INCOMPATIBLE_EPS_BEARER_CONTEXT: //#112
            // retry immediately
            return 0;
    }
    // no suggestion
    return -1;

}

static int get_retry_time(int retry_count, RIL_SOCKET_ID socketId)
{
    switch(retry_count) {
        case 0:
            return 0;
        case 1:
            return 0;
        case 2:
            return 60 * 1000;
        case 3:
            return 2 * 60 * 1000;
        case 4:
            return 8 * 60 * 1000;
        case 5:
            return 15 * 60 * 1000;
        case 6:
            return 15 * 60 * 1000;
        case 7:
            no_ra_retry_count[socketId] = -1;
            return INT_MAX;
        default:
            return INT_MAX;
    }
}

//
// The APN Operator Identifier is composed of three labels.
//
// Subdomain Format
//<service_id>.mnc<MNC>.mcc<MCC>.gprs
//<service_id>.mnc<MNC>.mcc<MCC>.3gppnetwork.org
//<service_id>.mnc<MNC>.mcc<MCC>.pub.3gppnetwork.org
//<service_id>.mnc<MNC>.mcc<MCC>.ipxuni.3gppnetwork.org
//
// Workaround: Some NW returns .mcc<MCC>.mnc<MNC>
static int isValidOI(const char * oi)
{
    int ret = 0;

#ifdef MMI_ASR_RIL_BRINGUP
#else
    if(oi != NULL) {
        int len = strlen(oi);
        char * new_oi = (char *)UI_MALLOC(len + 1);
        if(new_oi != NULL) {
            std::regex reg(R"(.(mnc\d+.mcc\d+|mcc\d+.mnc\d+).(gprs|(pub.|ipxuni.)?3gppnetwork.org))");

            for(int i = 0; i < len; i++)
                new_oi[i] = tolower(oi[i]);
            new_oi[len] = 0;
            ret = std::regex_match(new_oi, reg);
            UI_FREE(new_oi);
        }
    }
#endif
    if(!ret)
        RLOGW("'%s' is not a valid Operator Identifier", oi);
    return ret;
}

/**
  * TS 23.003 section 9 definition of Access Point Name
  * The APN is composed of two parts Network Identifier + Operator Identifier
  * The APN Operator Identifier is composed of three labels. the last labet shall be "gprs".
  */
static int matchApnNetworkIdentifier(const char * eps_apn, const char * apn)
{
    size_t len = strlen(apn);
    if(strncasecmp(eps_apn, apn, len) == 0) {
        const char * oi = eps_apn + len;
        return *oi == '\0' || isValidOI(oi);
    }
    return 0;
}

static inline int matchApn(struct PdpContextInfo * p, const char * apn)
{
    return p && p->address &&
           p->apn && matchApnNetworkIdentifier(p->apn, apn);
}

static inline int matchApnPdpType(struct PdpContextInfo * p, const char * apn, const char * pdp_type)
{
    return matchApn(p, apn) &&
           p->pdp_type && strcasecmp(p->pdp_type, pdp_type) == 0;
}

static int getPPPContextCid()
{
    char ppp_cid[PROPERTY_VALUE_MAX] = "\0";
    property_get(PROP_PPP_CID, ppp_cid, "-1");
    return atoi(ppp_cid);
}

static int  getSavedContextIndex(const char * apn, const char * protocol, RIL_SOCKET_ID socketId)
{
    int i;

    int pppCid = getPPPContextCid();
    RLOGI("pppCid = %d", pppCid);
    for(i = 0; i < MAX_DATA_CALLS; i++) {
        struct PdpContextInfo * p = gPdpContextInfoList[socketId][i];
        if(g_datacall_if_index[socketId][i] == -1  && (pppCid != i + 1) && matchApnPdpType(p, apn, protocol))
            return i;
    }
    if(strcasecmp(protocol, "IPV4V6")) {
        // request IP or IPv6, try to match IPV4V6
        for(i = 0; i < MAX_DATA_CALLS; i++) {
            struct PdpContextInfo * p = gPdpContextInfoList[socketId][i];
            if(g_datacall_if_index[socketId][i] == -1 && (pppCid != i + 1) && matchApnPdpType(p, apn, "IPV4V6"))
                return i;
        }
    } else {
        // request IPV4V6, try to match IPv4 or IPv6
        for(i = 0; i < MAX_DATA_CALLS; i++) {
            struct PdpContextInfo * p = gPdpContextInfoList[socketId][i];
            if(g_datacall_if_index[socketId][i] == -1 && (pppCid != i + 1) && matchApn(p, apn))
                return i;
        }
    }
    return -1;
}

static int definePDPContext(int profile, int cid, const char * protocol, const char * apn)
{
    int err;
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH];
    //RIL_SOCKET_ID socketId = getSocketId();

    if(profile == DATA_PROFILE_IMS) {
        sprintf(cmdString, "AT+CGDCONT=%d,\"%s\",\"%s\",,,,,,1", cid, protocol, apn);
    } else if(profile == DATA_PROFILE_SOS) {
        sprintf(cmdString, "AT+CGDCONT=%d,\"%s\",\"%s\",,,,,1,1", cid, protocol, apn ? apn : "");
    } else if(profile == DATA_PROFILE_MMS) {
        sprintf(cmdString, "AT+CGDCONT=%d,\"%s\",\"%s\",,,,,10", cid, protocol, apn ? apn : "");
#ifdef MMI_ASR_RIL_BRINGUP
#else
        sLastMmsCid[socketId] = cid;
#endif
        RLOGI("DATA_PROFILE_MMS\n");
    } else {
        sprintf(cmdString, "AT+CGDCONT=%d,\"%s\",\"%s\"", cid, protocol, apn);
    }
    err = at_send_command(cmdString, &p_response);

    if(err < 0) {
        err = -1;
    } else if(p_response->success == 0) {
        if(at_get_cme_error(p_response) == CME_OPERATION_NOT_ALLOWED)
            err = -EAGAIN;
        else
            err = -1;
    }
    at_response_free(p_response);
    p_response = NULL;
    if(err != 0)
        RLOGW("Fail to define the PDP context: %d", cid);
    return err;
}

static inline int isEmpty(const char * str)
{
    return str == NULL || str[0] == '\0';
}

static int setAuthType(int cid, int auth_type, const char * user, const char * passwd)
{
    int err;
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH];

    if(auth_type == 0)
        return 0;

    if(auth_type == 3)  //PAP /CHAP may be performed - baseband dependent.
        auth_type = 1; //use PAP as default
    sprintf(cmdString, "AT*AUTHReq=%d,%d,\"%s\",\"%s\"", cid, auth_type,
            isEmpty(user) ? "none" : user, isEmpty(passwd) ? "none" : passwd);
    err = at_send_command(cmdString, &p_response);

    if(err < 0 || p_response->success == 0) {
        RLOGW("Fail to set Auth Type for cid: %d", cid);
        if(err == 0)
            err = AT_ERROR_INVALID_RESPONSE;
    }
    at_response_free(p_response);
    p_response = NULL;
    return err;
}


static int activatePDPContext(int cid)
{
    int err;
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH];

    sprintf(cmdString, "AT+CGDATA=\"\",%d", cid);
    err = at_send_command_timeout(cmdString, &p_response, TIMEOUT_CGDATA);

    if(err < 0 || p_response->success == 0) {
        RLOGW("Fail to activate the PDP context: %d", cid);
        if(err == 0)
            err = AT_ERROR_INVALID_RESPONSE;
    }
    at_response_free(p_response);
    p_response = NULL;
    return err;
}

static int deactivatePDPContext(int cid, RIL_SOCKET_ID socketId)
{
    int err;
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH];

    sprintf(cmdString, "AT+CGACT=0,%d", cid);
    err = at_send_command_timeout(cmdString, &p_response, TIMEOUT_CGACT_DEACT);

    if(err < 0 || p_response->success == 0) {
        RLOGW("Fail to deactivate the PDP context: %d", cid);
        if(err == 0)
            err = AT_ERROR_INVALID_RESPONSE;
        goto exit;
    }
    sprintf(cmdString, "AT+CGDCONT=%d", cid);
    at_send_command(cmdString, NULL);
    freePdpContextInfo(&gPdpContextInfoList[socketId][cid - 1]);
exit:
    at_response_free(p_response);
    p_response = NULL;
    return err;
}

static int deactiveAndDetach(int cid, int profile, RIL_SOCKET_ID socketId)
{
    int err;
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH];

    err = deactivatePDPContext(cid, socketId);
    if(err == AT_ERROR_INVALID_RESPONSE) {
        if(profile == DATA_PROFILE_IMS) {
            RLOGD(" %s change default APN value", __FUNCTION__);
            //mode = 0, don't save it to NVM, just change CP global values
            sprintf(cmdString, "AT*CGDFLT=%d,\"%s\",\"%s\",%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d,%d",
                    0, "IPV4V6", "VZWINTERNET", 0, 1, 0, 0, 1, 0, 0, 0, 0, 1, 0, 0, 1, 0, 0, 0, 0, 1);
            at_send_command(cmdString, NULL);

            err = at_send_command_timeout("AT+CGATT=0", &p_response, TIMEOUT_CGATT);
            if(err < 0 || p_response->success == 0) {
                RLOGW("Fail to  PS detach %d", cid);
                if(err == 0)
                    err = AT_ERROR_INVALID_RESPONSE;
                goto exit;
            }
            RLOGD(" %s ps Detach, freePdpContextInfo[%d]", __FUNCTION__, cid - 1);
            freePdpContextInfo(&gPdpContextInfoList[socketId][cid - 1]);
            //do attach again.
            at_send_command_timeout("AT+CGATT=1", NULL, TIMEOUT_CGATT);
            sVzwImsState[socketId] = WAIT_INTERNET_CONNECTED_STATE;
        } else if(profile == DATA_PROFILE_DEFAULT) {
            //detach with cause 0x11 (CI_PS_DETACH_IPV6_RS_FAIL)
            err = at_send_command_timeout("AT*CGATTC=0,17", &p_response, TIMEOUT_CGATT);
            if(err < 0 || p_response->success == 0) {
                RLOGW("Fail to  PS detach %d", cid);
                if(err == 0)
                    err = AT_ERROR_INVALID_RESPONSE;
                goto exit;
            }
            RLOGD(" %s ps Detach, freePdpContextInfo[%d]", __FUNCTION__, cid - 1);
            freePdpContextInfo(&gPdpContextInfoList[socketId][cid - 1]);
            //Verify that the UE detaches from the LTE network. and starts timer T3402. for internet CP do attach.
            sVzwImsState[socketId] = WAIT_TAU_STATE;
            property_get("gsm.operator.numeric", sOperNum[socketId], "");
        } else {
            RLOGD(" %s profile is not ims", __FUNCTION__);
        }
    } else {
        err = 1;
        no_ra_retry_count[socketId] ++;
    }
exit:
    at_response_free(p_response);
    p_response = NULL;
    return err;
}

static int getLastFailCause(int * cause)
{
    ATResponse * response = NULL;
    char * line;
    int err = at_send_command_singleline("AT+PEER", "+PEER:", &response);

    if(err < 0) {
        RLOGW("Fail to getLastFailCause");
        goto exit;
    }
    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto exit;

    err = at_tok_nextint(&line, cause);

exit:
    at_response_free(response);
    return err;
}

#ifdef MMI_ASR_RIL_BRINGUP
#else
static int getPDPContextFailCause(int cid)
{
    ATResponse * response = NULL;
    int cause = 0;
    char * line;

    char cmd[MAX_AT_LENGTH];
    sprintf(cmd, "AT+GETIP=%d", cid);

    int err = at_send_command_singleline(cmd, "+GETIP:", &response);

    if(err < 0 || response->success == 0) {
        RLOGW("Fail to GETIP");
        goto exit;
    }
    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto exit;

    char * ip;
    err = at_tok_nextstr(&line, &ip);
    if(err < 0) goto exit;
    err = at_tok_nextstr(&line, &ip);
    if(err < 0) goto exit;

    if(at_tok_hasmore(&line)) {
        err = at_tok_nextint(&line, &cause);
    }
exit:
    at_response_free(response);
    return cause;
}
#endif

static void getPcoList(int cid, char ** dnslist, char ** pcscflist, int * mtu)
{
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH];
    int err;
    char * line;
    int n;
    char * ignore = NULL, *dnses = NULL, *pcscf = NULL;

    sprintf(cmdString, "AT*PCO=%d", cid);
    err = at_send_command_singleline(cmdString, "*PCO:", &p_response);
    if(err < 0 || p_response->success == 0 || p_response->p_intermediates == NULL) {
        RLOGD("%s: AT*PCO not supported", __FUNCTION__);
        goto exit;
    } else {
        line = p_response->p_intermediates->line;
        err = at_tok_start(&line);
        if(err < 0) goto exit;
        err = at_tok_nextint(&line, &n);
        if(err < 0) goto exit;
        err = at_tok_nextstr(&line, &ignore);
        if(err < 0) goto exit;
        if(dnslist != NULL && ignore[0]) {
            dnses = lv_strdup(ignore);
            *dnslist = dnses;
        }
        err = at_tok_nextstr(&line, &ignore);
        if(err < 0) goto exit;
        if(pcscflist != NULL && ignore[0]) {
            pcscf = lv_strdup(ignore);
            *pcscflist = pcscf;
        }
        err = at_tok_nextint(&line, &n);
        if(err < 0) goto exit;
        *mtu = n;

        at_response_free(p_response);
        p_response = NULL;
        return;
    }
exit:
    at_response_free(p_response);
    p_response = NULL;
    UI_FREE(dnses);
    UI_FREE(pcscf);
    return;
}


#ifdef SUPPORT_RDP
static void freePDPContextRDP(PDP_Context_RDP_parameters * rdpResponse, int num)
{
    int i;

    for(i = 0; i < num; i++) {
        UI_FREE(rdpResponse[i].apn);
        UI_FREE(rdpResponse[i].localAddSubnetMask);
        UI_FREE(rdpResponse[i].gw_addr);
        UI_FREE(rdpResponse[i].DNS_prim_addr);
        UI_FREE(rdpResponse[i].DNS_sec_addr);
        UI_FREE(rdpResponse[i].PCSCF_prim_addr);
        UI_FREE(rdpResponse[i].PCSCF_sec_addr);
    }
    UI_FREE(rdpResponse);
}

/* return 0: no more parameter
  * return AF_INET/AF_INNET6/1: convert successfully
  * return -1: convert failed
  */
static int checkMoreParamsOfRDP(char ** line, char ** param)
{
    int err = -1;
    char * out;
    char * address = NULL;
    if(at_tok_hasmore(line)) {
        err = at_tok_nextstr(line, &out);
        if(err < 0) goto error;
        address = lv_strdup(out);
        if(*address != '\0') {
            err = convertDotNotation2IpString(address, param, NULL);
            UI_FREE(address);
        } else {
            *param = address;
            err = 1;
        }
        if(err < 0) {
            *param = NULL;
            RLOGW("%s, convert param error", __FUNCTION__);
            goto error;
        }
        RLOGI("%s, param=%s", __FUNCTION__, *param);
    } else {
        RLOGI("%s, no param", __FUNCTION__);
        *param = NULL;
        err = 0;
    }
error:
    return err;
}

/* parse PDP Context read dynamic parameters
 *  +CGCONTRDP: <cid>,<bearer_id>,<apn>[,<source address and subnet mask>[,<gw_addr>[
 *  ,<DNS_prim_addr>[,<DNS_sec_addr>[,<P-CSCF_prim_addr>[,<P-CSCF_sec_addr>[,<IM_CN_Signalling_Flag>]]]]]]]
 * input: line
 * output: rdpResponse
 * return: 0 parse successfully, -1 failed
 */
static int parsePDPContextRDP(char * line, PDP_Context_RDP_parameters * rdpResponse)
{
    int err;
    char * out, *apn, *address;

    apn = address  = NULL;

    err = at_tok_start(&line);
    if(err < 0) goto exit;

    err = at_tok_nextint(&line, &rdpResponse->cid);
    if(err < 0) goto exit;
    RLOGI("%s, cid=%d", __FUNCTION__, rdpResponse->cid);

    err = at_tok_nextint(&line, &rdpResponse->bearer_id);
    if(err < 0) goto exit;
    RLOGI("%s, bearer_id=%d", __FUNCTION__, rdpResponse->bearer_id);

    err = at_tok_nextstr(&line, &out);
    if(err < 0) goto exit;
    apn = lv_strdup(out);
    rdpResponse->apn = apn;
    RLOGI("%s, apn=%s", __FUNCTION__, rdpResponse->apn);

    if(at_tok_hasmore(&line)) {
        err = at_tok_nextstr(&line, &out);
        if(err < 0) goto exit;
        address = lv_strdup(out);
        rdpResponse->localAddSubnetMask = address;
        RLOGI("%s, localAddSubnetMask=%s", __FUNCTION__, rdpResponse->localAddSubnetMask);
    } else {
        RLOGI("%s, no localAddSubnetMask !", __FUNCTION__);
        rdpResponse->localAddSubnetMask = NULL;
        err = -1;
        goto exit;
    }

    err = checkMoreParamsOfRDP(&line, &rdpResponse->gw_addr);
    //err = 0, no gw, don't check more, return 0 (consider it's normal)
    if(err <= 0) {
        RLOGI("%s, check gw_addr return %d", __FUNCTION__, err);
        goto exit;
    } else {
        RLOGI("%s, gw_addr=%s", __FUNCTION__, rdpResponse->gw_addr);
        err = checkMoreParamsOfRDP(&line, &rdpResponse->DNS_prim_addr);
        if(err <= 0) {
            RLOGI("%s, check DNS_prim_addr return %d", __FUNCTION__, err);
            goto exit;
        } else {
            RLOGI("%s, DNS_prim_addr=%s", __FUNCTION__, rdpResponse->DNS_prim_addr);
            err = checkMoreParamsOfRDP(&line, &rdpResponse->DNS_sec_addr);
            if(err <= 0) {
                RLOGI("%s, check DNS_sec_addr return %d", __FUNCTION__, err);
                goto exit;
            } else {
                RLOGI("%s, DNS_sec_addr=%s", __FUNCTION__, rdpResponse->DNS_sec_addr);
                err = checkMoreParamsOfRDP(&line, &rdpResponse->PCSCF_prim_addr);
                if(err <= 0) {
                    RLOGI("%s, check PCSCF_prim_addr return %d", __FUNCTION__, err);
                    goto exit;
                } else {
                    RLOGI("%s, PCSCF_prim_addr=%s", __FUNCTION__, rdpResponse->PCSCF_prim_addr);
                    err = checkMoreParamsOfRDP(&line, &rdpResponse->PCSCF_sec_addr);
                    if(err <= 0) {
                        RLOGI("%s, check PCSCF_sec_addr return %d", __FUNCTION__, err);
                        goto exit;
                    } else {
                        RLOGI("%s, PCSCF_sec_addr=%s", __FUNCTION__, rdpResponse->PCSCF_sec_addr);
                    }
                }
            }
        }
        err = 0;
    }

    if(at_tok_hasmore(&line)) {
        err = at_tok_nextint(&line, &rdpResponse->IM_CN_Signal_Flg);
        if(err < 0) goto exit;
        RLOGI("%s, im_flg=%d", __FUNCTION__, rdpResponse->IM_CN_Signal_Flg);
    }

exit:
    return err;
}

/* fill PdpContextInfo according to the cid */
static PdpContextInfo * getPDPContextInfo(int cid)
{
    PdpContextInfo * pdpContextInfo = NULL;
    int err, i = 0, num = 0, mtuSize = DEFAULT_MTU_SIZE;
    char * line;
    ATLine * p_cur;
    char cmdString[MAX_AT_LENGTH];
    ATResponse * p_response = NULL;
    PDP_Context_RDP_parameters * rdpResponses = NULL;
    int ret = -1;
    char addrs[100] = { '\0' };
    char dnses[200] = { '\0' };
    char gws[100] = { '\0' };
    char pcscf[200] = { '\0' };
    char ipv4addr[INET_ADDRSTRLEN] = { '\0' };
    char ipv6addr[INET6_ADDRSTRLEN] = { '\0' };
    int pdpType = PDP_TYPE_UNSUPPORTED;
    const char * type = NULL;

    sprintf(cmdString, "AT+CGCONTRDP=%d", cid);
    //If the MT indicates more than two IP addresses of P-CSCF servers or more than two IP addresses of DNS servers,
    //multiple lines of information per <cid> will be returned.
    err = at_send_command_multiline(cmdString, "+CGCONTRDP:", &p_response);
    if(err != 0 || p_response->success == 0)
        goto exit;

    for(p_cur = p_response->p_intermediates; p_cur != NULL; p_cur = p_cur->p_next)
        num++;

    rdpResponses = (PDP_Context_RDP_parameters *)UI_MALLOC(num * sizeof(PDP_Context_RDP_parameters));

    for(i = 0, p_cur = p_response->p_intermediates; p_cur != NULL; i++, p_cur = p_cur->p_next) {
        line = p_cur->line;

        ret = parsePDPContextRDP(line, &rdpResponses[i]);
        if(ret == 0) {
            char * ipstring = NULL;
            int af = convertDotNotation2IpString((const char *)rdpResponses[i].localAddSubnetMask, &ipstring, NULL);
            if(af == -1) {
                ret = -1;
                UI_FREE(ipstring);
                goto exit;
            }

            RLOGI("%s,af=%d, ipstring=%s", __FUNCTION__, af, ipstring);

            if(af == AF_INET) {
                pdpType |= PDP_TYPE_IP;
                strcpy(ipv4addr, ipstring);
            }

            if(af == AF_INET6) {
                pdpType |= PDP_TYPE_IPV6;
                strcpy(ipv6addr, ipstring);
            }

            if(i == 0) {
                sprintf(dnses, "%s %s", rdpResponses[i].DNS_prim_addr, rdpResponses[i].DNS_sec_addr);
                sprintf(pcscf, "%s %s", rdpResponses[i].PCSCF_prim_addr, rdpResponses[i].PCSCF_sec_addr);
                strcpy(gws, rdpResponses[i].gw_addr);
            } else {
                char tmp[100];
                sprintf((char *)tmp, " %s %s", rdpResponses[i].DNS_prim_addr, rdpResponses[i].DNS_sec_addr);
                strcat(dnses, tmp);
                sprintf((char *)tmp, " %s %s", rdpResponses[i].PCSCF_prim_addr, rdpResponses[i].PCSCF_sec_addr);
                strcat(pcscf, tmp);
                sprintf((char *)tmp, " %s", rdpResponses[i].gw_addr);
                strcat(gws, tmp);
            }
            UI_FREE(ipstring);
        }
    }

    switch(pdpType) {
        case PDP_TYPE_IP:
            type = "IP";
            sprintf(addrs, "%s", ipv4addr);
            break;
        case PDP_TYPE_IPV6:
            type = "IPV6";
            sprintf(addrs, "%s", ipv6addr);
            break;
        case PDP_TYPE_IPV4V6:
            type = "IPV4V6";
            sprintf(addrs, "%s %s", ipv4addr, ipv6addr);
            break;
        default:
            type = "UNKNOWN";
            RLOGW("%s, ip address error", __FUNCTION__);
            addrs[0] = '\0';
            break;
    }
    if(!rdpResponses[0].apn) {
        RLOGW("%s: No APN\n", __FUNCTION__);
        goto exit;
    }

    //get PDP MTU size
    getPcoList(cid, NULL, NULL, &mtuSize);
    RLOGD("getPcoList mtuSize=%d", mtuSize);
    pdpContextInfo = new PdpContextInfo(cid, lv_strdup(type), lv_strdup(rdpResponses[0].apn), lv_strdup(addrs), lv_strdup(dnses), lv_strdup(pcscf), mtuSize);

exit:
    if(rdpResponses) freePDPContextRDP(rdpResponses, num);
    at_response_free(p_response);

    return pdpContextInfo;
}

static void updatePdpContextInfoList(RIL_SOCKET_ID socketId)
{
    int err;
    ATResponse * p_response = NULL;
    char * line;
    int cid;

    RLOGI("%s, entry", __FUNCTION__);
    clearPdpContextInfoList(socketId);
    //get the list of <cid>s assoiated with active non secondary contexts
    err = at_send_command_singleline("AT+CGCONTRDP=?", "+CGCONTRDP:", &p_response);
    if(err != 0 || p_response->success == 0)
        goto exit;
    line = p_response->p_intermediates->line;
    err = at_tok_start(&line);
    if(err < 0) goto exit;

    //command format is +CGCONTRDP:(cid,cid..), remove opening parentesis, ignore closing parentesis
    while(*line != '\0') {
        if(*line == '(') {
            line++;
            break;
        }
        line++;
    }

    while(at_tok_hasmore(&line)) {
        err = at_tok_nextint(&line, &cid);
        if(err < 0) continue;
        gPdpContextInfoList[socketId][cid - 1] = getPDPContextInfo(cid);
    }

exit:
    at_response_free(p_response);
}

#else
#ifdef MMI_ASR_RIL_BRINGUP
#else
static int hexCharToInt(char c)
{
    if(c >= '0' && c <= '9') return (c - '0');
    if(c >= 'A' && c <= 'F') return (c - 'A' + 10);
    if(c >= 'a' && c <= 'f') return (c - 'a' + 10);

    RLOGE("hexCharToInt: illegal hex char:%c", c);
    return c;
}

static int hexStringToBytes(const char * s, char ** d)
{
    int i, sz = strlen(s);
    char * p = reinterpret_cast<char *>(UI_MALLOC(sz / 2));
    if(!p) {
        return 0;
    }
    for(i = 0 ; i < sz ; i += 2) {
        p[i / 2] = (char)((hexCharToInt(s[i]) << 4) | hexCharToInt(s[i + 1]));
    }
    *d = p;
    return sz / 2;
}
#endif

static int isValidAddress(char * address)
{
    int valid = 0;
    while(address && *address != '\0') {
        if(!isspace(*address)) {
            valid = 1;
            break;
        }
        address++;
    }
    return valid;
}

/* Internal tool to parse PDP context list.
 * Sample: +CGDCONT: 1,"IP","cmnet","*************",0,0,802110030100108106d38870328306d38814cb,
 * Input para: line (as above exsample)
 * Output para: pCid, type, apn, address, dns
 * Note: for the output para with type char *, the space is allocated in this func
 */
static int parsePDPContexstList(char * line, int * pCid, char ** pType, char ** pApn, char ** pAddress, char ** pDns)
{
    int err, ignore;
    char * out, *type, *apn, *address, *dns;

    type = apn = address = dns = NULL;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, pCid);
    if(err < 0) goto error;

    err = at_tok_nextstr(&line, &out);
    if(err < 0) goto error;
    if(pType != NULL && out[0]) {
        type = lv_strdup(out);
        *pType = type;
    }

    err = at_tok_nextstr(&line, &out);
    if(err < 0) goto error;
    if(pApn != NULL && out[0]) {
        apn = lv_strdup(out);
        *pApn = apn;
    }

    err = at_tok_nextstr(&line, &out);
    if(err < 0) goto error;
    if(pAddress != NULL && out[0]) {
        address = lv_strdup(out);
        *pAddress = address;
        if(!isValidAddress(address)) {
            goto error;
        }
    }

    err = at_tok_nextint(&line, &ignore);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &ignore);
    if(err < 0) goto error;

    err = at_tok_nextstr(&line, &out);
    if(err < 0) goto error;
    if(pDns != NULL && out[0]) {
        dns = lv_strdup(out);
        *pDns = dns;
    }

    return 0;

error:
    if(type) UI_FREE(type);
    if(apn) UI_FREE(apn);
    if(address) UI_FREE(address);
    if(pType) *pType = NULL;
    if(pApn) *pApn = NULL;
    if(pAddress) *pAddress = NULL;
    if(pDns) *pDns = NULL;
    return err;
}

static void updatePdpContextInfoList(RIL_SOCKET_ID socketId)
{
    int err;
    ATResponse * p_response = NULL;
    ATLine * p_cur;

    clearPdpContextInfoList(socketId);
    err = at_send_command_multiline("AT+CGDCONT?", "+CGDCONT:", &p_response);
    if(err < 0 || p_response->success == 0) {
        RLOGW("Fail to query the PDP context");
        goto exit;
    }

    for(p_cur = p_response->p_intermediates; p_cur; p_cur = p_cur->p_next) {
        int cid;
        char * pdp_type = NULL, *apn = NULL, *pco = NULL, *address = NULL;
        char * line = p_cur->line;
        char * dnslist = NULL, *pcscflist = NULL;
        int mtu = DEFAULT_MTU_SIZE;
        err = parsePDPContexstList(line, &cid, &pdp_type, &apn, &address, &pco);
        if(err == 0) {
            getPcoList(cid, &dnslist, &pcscflist, &mtu);
            gPdpContextInfoList[socketId][cid - 1] = new PdpContextInfo(cid, pdp_type, apn, address, dnslist, pcscflist, mtu);
            UI_FREE(pco);
        }
    }
exit:
    at_response_free(p_response);
}
#endif

static void clearInterfaceIndexAndPDP(RIL_SOCKET_ID socketId, int index)
{
    if(isPdpActivated(index, socketId)) {
        deactivatePDPContext(index + 1, socketId);
        freePdpContextInfo(&gPdpContextInfoList[socketId][index]);
    }
    g_datacall_if_index[socketId][index] = -1;
}

static void syncIfxWithPdpContextInfo(int i, RIL_SOCKET_ID socketId)
{
    if(g_datacall_if_index[socketId][i] == -1 || isPdpActivated(i, socketId))
        return;

    int if_index = g_datacall_if_index[socketId][i];
    if(if_index == i) {
        // The is the main index
        int v6_index = findV6Partner(if_index, socketId);
        if(v6_index >= 0)
            clearInterfaceIndexAndPDP(socketId, v6_index);
#ifdef MMI_ASR_RIL_BRINGUP
#else
        disableInterface(socketId, if_index);
#endif
        clearInterfaceIndexAndPDP(socketId, if_index);
    } else {
        // The is the v6 partner index
        clearInterfaceIndexAndPDP(socketId, i);
    }
}

static int generateDataCallList(RIL_Data_Call_Response_ps ** list, RIL_SOCKET_ID socketId)
{
    int i, j = 0, num = 0;
    for(i = 0; i < MAX_DATA_CALLS; i++) {
        if(g_datacall_if_index[socketId][i] == i && isPdpActivated(i, socketId))
            num++;
    }
    if(num == 0)
        return 0;

    RIL_Data_Call_Response_ps * pdpResponses = (RIL_Data_Call_Response_ps *)UI_MALLOC(num * sizeof(RIL_Data_Call_Response_ps));
    for(i = 0; i < MAX_DATA_CALLS; i++) {
        if(g_datacall_if_index[socketId][i] == i && isPdpActivated(i, socketId)) {
            pdpResponses[j].status = PDP_FAIL_NONE;
            pdpResponses[j].cid = gPdpContextInfoList[socketId][i]->cid;
            pdpResponses[j].active = 2;
#ifdef MMI_ASR_RIL_BRINGUP
#else
            pdpResponses[j].ifname = ModemDevice::get_current_modem(socketId)->get_interface_name(i);
#endif
            getAddressesAndGateways(pdpResponses[j].ifname, &pdpResponses[j].addresses, &pdpResponses[j].gateways);
            int v6_index = findV6Partner(i, socketId);
            char * dnses = gPdpContextInfoList[socketId][i]->dnses;
            if(v6_index >= 0) {
                char * dnsesv6 = gPdpContextInfoList[socketId][v6_index]->dnses;
                pdpResponses[j].type = lv_strdup("IPV4V6");
                pdpResponses[j].dnses = (char *)UI_MALLOC(sizeof("%s %s") + 100);
                sprintf(pdpResponses[j].dnses, "%s %s", dnses ? dnses : "", dnsesv6 ? dnsesv6 : "");
            } else {
                pdpResponses[j].type = lv_strdup(gPdpContextInfoList[socketId][i]->pdp_type);
                pdpResponses[j].dnses = dnses ? lv_strdup(gPdpContextInfoList[socketId][i]->dnses) : NULL;
            }
            char * pcscf = gPdpContextInfoList[socketId][i]->pcscf;
            pdpResponses[j].pcscf = pcscf ? lv_strdup(gPdpContextInfoList[socketId][i]->pcscf) : NULL;
            pdpResponses[j].mtu = gPdpContextInfoList[socketId][i]->mtu;
            j++;
        }
    }
    *list = pdpResponses;
    return num;
}

static int referenceActivePdp(int profile, const char * apn, int auth_type, const char * user, const char * passwd, const char * protocol, RIL_SOCKET_ID socketId)
{
    enum PdpType pdpType = getPdpType(protocol);
    if(pdpType == PDP_TYPE_UNSUPPORTED) {
        return PDP_REFERNCE_FAIL_GENERIC;
    }

    int index = getSavedContextIndex(apn, protocol, socketId);
    if(index >= 0) {
        g_datacall_if_index[socketId][index] = index;
        return index;
    }

    /* Step1: Define the CID */
    for(int i = 0; i < MAX_DATA_CALLS; ++i) {
        i = findInactivePdp(socketId, i);
        if(i < 0) {
            return PDP_REFERNCE_FAIL_GENERIC;
        }
        int ret = definePDPContext(profile, i + 1, protocol, apn);
        if(ret == 0) {
            index = i;
            break;
        } else if(ret != -EAGAIN) {
            return PDP_REFERNCE_FAIL_GENERIC;
        }
    }

    if(index < 0)
        return PDP_REFERNCE_FAIL_GENERIC;

    int cid = index + 1;
    /* Step2: set PPP auth parameters for direct IP type*/
    if(setAuthType(cid, auth_type, user, passwd) < 0)
        return PDP_REFERNCE_FAIL_GENERIC;

    /* Step3: Active the PDP Context */
    if(activatePDPContext(cid) < 0)
        return PDP_REFERNCE_FAIL_ACTIVATION;

    g_datacall_if_index[socketId][index] = index;
    return index;
}

#ifdef MMI_ASR_RIL_BRINGUP
static int PDPContextPending[SIM_COUNT];
#else
static std::atomic_int PDPContextPending[SIM_COUNT];
#endif
static void onPDPContextListChanged(void * param)
{
    int num = 0;
    RIL_Data_Call_Response_ps * pdpResponses = NULL;
    RIL_SOCKET_ID socketId = static_cast<RIL_SOCKET_ID>(reinterpret_cast<intptr_t>(param));

    if(PDPContextPending[socketId] > 1) {
        RLOGD("%s: pending number :%d, wait the following work item", __FUNCTION__, PDPContextPending[socketId]);
        goto exit;
    }
    updatePdpContextInfoList(socketId);
    for(int i = 0; i < MAX_DATA_CALLS; i++) {
        syncIfxWithPdpContextInfo(i, socketId);
    }

    num = generateDataCallList(&pdpResponses, socketId);

    RIL_onUnsolicitedResponse(RIL_UNSOL_DATA_CALL_LIST_CHANGED, pdpResponses, num * sizeof(RIL_Data_Call_Response_ps), socketId);
exit:
    if(pdpResponses) freeDataCallResponses(pdpResponses, num);
    --PDPContextPending[socketId];
}

static void onPDPClear(void * param)
{
    RIL_SOCKET_ID socketId = static_cast<RIL_SOCKET_ID>(reinterpret_cast<intptr_t>(param));
    clearDataCallState(socketId);
    onPDPContextListChanged(param);
}

void notifyDataCallChanged(RIL_SOCKET_ID socketId)
{
    ++PDPContextPending[socketId];
    enque(getWorkQueue(SERVICE_PS, socketId), onPDPContextListChanged, reinterpret_cast<void *>(socketId), socketId);
}

static void notifyPDPClear(RIL_SOCKET_ID socketId)
{
    ++PDPContextPending[socketId];
    enque(getWorkQueue(SERVICE_PS, socketId), onPDPClear, reinterpret_cast<void *>(socketId), socketId);
}

static void checkPcoVzwUse(int cid, RIL_SOCKET_ID socketId)
{
    UNUSED(socketId);
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH];
    int err;
    char * line;
    int n, mtu, action;
    char * ignore = NULL;

    sprintf(cmdString, "AT*PCO=%d", cid);
    err = at_send_command_singleline(cmdString, "*PCO:", &p_response);
    if(err < 0 || p_response->success == 0 || p_response->p_intermediates == NULL) {
        RLOGD("%s: AT*PCO not supported", __FUNCTION__);
        goto exit;
    } else {
        line = p_response->p_intermediates->line;
        err = at_tok_start(&line);
        if(err < 0) goto exit;
        err = at_tok_nextint(&line, &n);
        if(err < 0) goto exit;
        err = at_tok_nextstr(&line, &ignore);
        if(err < 0) goto exit;
        err = at_tok_nextstr(&line, &ignore);
        if(err < 0) goto exit;
        err = at_tok_nextint(&line, &mtu);
        if(err < 0) goto exit;
        err = at_tok_nextint(&line, &action);
        if(err < 0) goto exit;

        if(action >= 0) {
            RIL_onUnsolicitedResponse(RIL_UNSOL_VZW_PCO_ACTION, &action, sizeof(action), socketId);
        }
    }
exit:
    at_response_free(p_response);
    p_response = NULL;
    UI_FREE(ignore);
    return;
}

static void onPDPClearOverIms(void * param)
{
    RIL_SOCKET_ID socketId = static_cast<RIL_SOCKET_ID>(reinterpret_cast<intptr_t>(param));
    int i;
#ifdef MMI_ASR_RIL_BRINGUP
#else
    int imsIndex = 6;
    int index = -1;
    bool isPdp = false;
    char ifname[32];
#endif

    if(PDPContextPending[socketId] > 1) {
        RLOGD("%s: pending number :%d, wait the following work item", __FUNCTION__, PDPContextPending[socketId]);
        return;
    }

    RLOGD("%s: entry on socket:%d", __FUNCTION__, socketId);
    rilPsMutexLock();

    for(i = 0; i < MAX_DATA_CALLS; i++) {
        int if_index = g_datacall_if_index[socketId][i];
        if(gPdpContextInfoList[socketId][i] == NULL || gPdpContextInfoList[socketId][i]->apn == NULL) {
            RLOGD("%s: skip empty cid:%d", __FUNCTION__, i);
            continue;
        }
        char * pIms = strstr(gPdpContextInfoList[socketId][i]->apn, "ims");
        if(pIms != NULL) {
            RLOGD("%s: skip ims index:%d apn:%s", __FUNCTION__, i, pIms);
            continue;
        }
        if(if_index == i) {
            // The is the main index
            int v6_index = findV6Partner(if_index, socketId);
            if(v6_index >= 0)
                clearInterfaceIndexAndPDP(socketId, v6_index);
#ifdef MMI_ASR_RIL_BRINGUP
#else
            disableInterface(socketId, if_index);
#endif
            clearInterfaceIndexAndPDP(socketId, if_index);
        } else {
            // The is the v6 partner index
            clearInterfaceIndexAndPDP(socketId, i);
        }
    }

    int num = 0;
    RIL_Data_Call_Response_ps * pdpResponses = NULL;
    RLOGD("%s: updatePdpContextInfoList ", __FUNCTION__);
    updatePdpContextInfoList(socketId);
    RLOGD("%s: generateDataCallList ", __FUNCTION__);
    num = generateDataCallList(&pdpResponses, socketId);
    RIL_onUnsolicitedResponse(RIL_UNSOL_DATA_CALL_LIST_CHANGED, pdpResponses, num * sizeof(RIL_Data_Call_Response_ps), socketId);
    RLOGD("%s: freeDataCallResponses num:%d ", __FUNCTION__, num);

    if(pdpResponses) freeDataCallResponses(pdpResponses, num);
    rilPsMutexUnlock();
    --PDPContextPending[socketId];
    return;
}
bool isDualLEnabled()
{
    char value[PROPERTY_VALUE_MAX];
    property_get("persist.radio.asr.lplusg", value, "");
    if(strcasecmp(value, "true")) {
        return true;
    }
    return false;
}
void notifyPDPClearOverIms(RIL_SOCKET_ID socketId)
{
    ++PDPContextPending[socketId];
    enque(getWorkQueue(SERVICE_PS, socketId), onPDPClearOverIms, reinterpret_cast<void *>(socketId), socketId);
}
void restoreDefaultPdpOnOtherCard(RIL_SOCKET_ID socketId)
{
    UNUSED(socketId);
    //MMS_TODO: do we need to restore the default PDP for MMS?
}
void ril_request_setup_data_call(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    //const char* radio_technology = ((const char **)data)[0];
    const char * profile_type = ((const char **)data)[1];
    const char * apn = ((const char **)data)[2];
    const char * user = ((const char **)data)[3];
    const char * passwd = ((const char **)data)[4];
    const char * auth_type_str = ((const char **)data)[5];
    const char * protocol = ((const char **)data)[6];

    int profile = atoi(profile_type);
    int auth_type = atoi(auth_type_str);

    RIL_Data_Call_Response_ps result;
    char ifname[32];
    int index = -1, index2 = -1;
    int ret = -1;
    RIL_SOCKET_ID socketId = getSocketId();
    //add PLMN check, qurey COPS
    char * curOperNum = NULL;

    RLOGD("%s: profile_type=%d, apn=%s, user=%s,passwd=%s,auth_type=%d, protocol=%s",
          __FUNCTION__, profile, apn ? apn : "NULL", user ? user : "NULL", passwd ? passwd : "NULL", auth_type, protocol);

    rilPsMutexLock();
    memset(&result, 0, sizeof(result));
    result.status = PDP_FAIL_ERROR_UNSPECIFIED;
    result.suggestedRetryTime = -1;

    enum PdpType pdpType = getPdpType(protocol);
    if(pdpType == PDP_TYPE_UNSUPPORTED) {
        RLOGE("PDP type is not supported: %s", protocol);
        goto exit;
    }

    if(profile == DATA_PROFILE_IMS) {
        if(sVzwImsState[socketId] == WAIT_INTERNET_CONNECTED_STATE
                || sVzwImsState[socketId] == WAIT_TAU_STATE) {
            RLOGE("%s: ignore vzwims due to %d", __FUNCTION__, sVzwImsState[socketId]);
            goto exit;
        }

        getCurrentOperNumStr(&curOperNum, socketId);
        if(curOperNum && strcmp("302220", curOperNum) == 0) {
            RLOGE("%s: ignore vzwims for Canada Telues", __FUNCTION__);
            goto exit;
        }

        if(sVzwImsState[socketId] == WAIT_TAU_COMPLETED) {
            if(curOperNum != NULL) {
                if(strcmp(sOperNum[socketId], curOperNum) == 0) {
                    RLOGE("%s: ignore vzwims wait PLMN changed", __FUNCTION__);
                    goto exit;
                } else {
                    RLOGE("%s: TAU happen, now setup ims can sent to CP", __FUNCTION__);
                    sVzwImsState[socketId] = NORMAL_STATE;
                }
            } else {
                RLOGE("%s: wait service registered", __FUNCTION__);
                goto exit;
            }
        }
    }

    if(!isDataServiceRegistered(socketId) && !(profile == DATA_PROFILE_SOS && isDataRegForEmergecyOnly(socketId))) {
        RLOGE("%s: data service is not registered", __FUNCTION__);
        goto exit;
    }

    updatePdpContextInfoList(socketId);

    if(IsLTEAttached(socketId)) {
        int pdpActivedNum = 0;
        int i;
        for(i = 0; i < MAX_DATA_CALLS; i++) {
            if(isPdpActivated(i, socketId)) {
                pdpActivedNum ++;
            }
        }
        RLOGI("%s: LTE pdpActivedNum = %d", __FUNCTION__, pdpActivedNum);
        if(pdpActivedNum == 0) {
            goto exit;
        }
    }

    index = referenceActivePdp(profile, apn, auth_type, user, passwd, protocol, socketId);
    if(index == PDP_REFERNCE_FAIL_GENERIC) {
        RLOGE("Fail to find PDP for Profile: %d", profile);
        goto exit;
    } else if(index == PDP_REFERNCE_FAIL_ACTIVATION) {
        int fail_cause;
        if(getLastFailCause(&fail_cause) < 0)
            goto exit;
        if(isNetworkOfChinaMobile(socketId) && PDP_FAIL_UNKNOWN_PDP_ADDRESS_TYPE == fail_cause
                && pdpType == PDP_TYPE_IPV4V6) {
            // Setup V4V6 with 2 Pdp
            index = referenceActivePdp(profile, apn, auth_type, user, passwd, "IP", socketId);
            if(index >= 0) {
                index2 = referenceActivePdp(profile, apn, auth_type, user, passwd, "IPV6", socketId);
                if(index2 >= 0) {
                    g_datacall_if_index[socketId][index2] = index;
                    goto config_interface;
                } else {
                    goto deactivate_and_exit;
                }
            }
        }
        if(startVzwDataRetryTest(socketId)) {
            int resultTime = getVzwDataRetryTime(fail_cause);
            if(resultTime == 0)
                result.status = PDP_FAIL_ERROR_UNSPECIFIED;
            else
                result.status = fail_cause;
            result.suggestedRetryTime = resultTime;
        } else {
            result.status = fail_cause;
            result.suggestedRetryTime = getSuggestedRetryTime(fail_cause);
        }
        goto exit;
    }
    RLOGD("%s : to config_interface ", __FUNCTION__);
config_interface:
    /* Step4: Enable the network interface */
    if(!isPdpActivated(index, socketId) || (index2 >= 0 && !isPdpActivated(index2, socketId))) {
        updatePdpContextInfoList(socketId);
        if(!isPdpActivated(index, socketId) || (index2 >= 0 && !isPdpActivated(index2, socketId)))
            goto deactivate_and_exit;
    }
#ifdef MMI_ASR_RIL_BRINGUP
#else
    ModemDevice::get_current_modem(socketId)->get_netcard_name(ifname, sizeof(ifname), index);

    if(pdpType == PDP_TYPE_IP) {
        ret = configInterface(socketId, index, gPdpContextInfoList[socketId][index]->address, NULL, -1);
    } else if(pdpType == PDP_TYPE_IPV6) {
        ret = configInterface(socketId, index, NULL, gPdpContextInfoList[socketId][index]->address, -1);
    } else if(index2 < 0) {
        char * address = gPdpContextInfoList[socketId][index]->address;
        char * space = strchr(address, ' ');
        if(!space) {
            RLOGW("find no space in IPv4v6 address:%s", address);
            int is_ipv6 = !!strchr(address, ':');
            if(isNetworkOfChinaMobile(socketId)
                    && getPDPContextFailCause(index + 1) == PDP_FAIL_ONLY_SINGLE_BEARER_ALLOWED) {
                // setup another PDP to form a joint interface
                index2 = referenceActivePdp(profile, apn, auth_type, user, passwd, is_ipv6 ? "IP" : "IPV6", socketId);
                if(index2 >= 0) {
                    if(is_ipv6) {
                        // switch index and index2
                        int tmp = index;
                        index = index2;
                        index2 = tmp;
                    }
                    g_datacall_if_index[socketId][index2] = index;
                    goto config_interface;
                }
            }
            if(!is_ipv6) {
                protocol = "IP";
                ret = configInterface(socketId, index, address, NULL, -1);
            } else {
                protocol = "IPV6";
                ret = configInterface(socketId, index, NULL, address, -1);
            }
        } else {
            char * v4_address = address;
            char * v6_address = space + 1;
            *space = '\0';
            ret = configInterface(socketId, index, v4_address, v6_address, -1);
            *space = ' ';
        }
    } else {
        // Two PDPs are ready
        ret = configInterface(socketId, index, gPdpContextInfoList[socketId][index]->address,
                              gPdpContextInfoList[socketId][index2]->address, index2);
    }
#endif
    if(ret < 0) {
        if(ret == -2) {
            RLOGW("can't get global address for IPV6");
            if(startVzwDataRetryTest(socketId) && IsLTEAttached(socketId)) {
                ret = deactiveAndDetach(index + 1, profile, socketId);
                if(ret == 1) {
                    result.status = PDP_FAIL_ERROR_UNSPECIFIED;
                    RLOGD("no RA response from network %d", no_ra_retry_count[socketId]);
                    result.suggestedRetryTime = get_retry_time(no_ra_retry_count[socketId], socketId);
                }
                g_datacall_if_index[socketId][index] = -1;
                goto exit;
            }
        }
        goto deactivate_and_exit;
    }

    no_ra_retry_count[socketId] = -1;

    if(index2 < 0) {
        result.dnses = gPdpContextInfoList[socketId][index]->dnses;
    } else {
        char * dns = gPdpContextInfoList[socketId][index]->dnses;
        char * dns6 = gPdpContextInfoList[socketId][index2]->dnses;
        result.dnses = (char *)UI_MALLOC(sizeof("%s %s") + 100);
        sprintf(result.dnses, "%s %s", dns ? dns : "", dns6 ? dns6 : "");
    }
    getAddressesAndGateways(ifname, &result.addresses, &result.gateways);
    result.pcscf = gPdpContextInfoList[socketId][index]->pcscf;
    result.mtu = gPdpContextInfoList[socketId][index]->mtu;
    result.status = PDP_FAIL_NONE;
    result.active = 2;
    result.cid = index + 1;
    result.ifname = ifname;
    result.type = (char *)protocol;

    if(sVzwImsState[socketId] == WAIT_INTERNET_CONNECTED_STATE && profile == DATA_PROFILE_DEFAULT) {
        sVzwImsState[socketId] = NORMAL_STATE;
    } else if(sVzwImsState[socketId] == WAIT_TAU_STATE && profile == DATA_PROFILE_DEFAULT) {
        sVzwImsState[socketId] = WAIT_TAU_COMPLETED;
    }

    if(profile == DATA_PROFILE_DEFAULT) {
        UI_FREE(curOperNum);
        curOperNum = NULL;
        getCurrentOperNumStr(&curOperNum, socketId);
        if(curOperNum && strcmp("311480", curOperNum) == 0) {
            // check whether pco include VZW special subscription status
            checkPcoVzwUse(result.cid, socketId);
        }
    }

    goto exit;

deactivate_and_exit:
    if(index2 >= 0) {
        deactivatePDPContext(index2 + 1, socketId);
        g_datacall_if_index[socketId][index2] = -1;
    }
    if(index >= 0) {
        deactivatePDPContext(index + 1, socketId);
        g_datacall_if_index[socketId][index] = -1;
    }
exit:
    rilPsMutexUnlock();

    RLOGD("%s: compelete", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(result));
    UI_FREE(curOperNum);
    UI_FREE(result.addresses);
    UI_FREE(result.gateways);
    if(index2 >= 0) {
        UI_FREE(result.dnses);
    }
}

void ril_request_deactivate_data_call(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);
    RIL_SOCKET_ID socketId = getSocketId();
    bool reasonRadioOff = atoi(((char **)data)[1]) == 1;

    RLOGD("ARIL_PS %s socketId:%d reasonRadioOff:%d", __FUNCTION__, socketId, reasonRadioOff);
    rilPsMutexLock();

    int cid = atoi(((char **)data)[0]);
    int index = cid - 1;

#if (SIM_COUNT >= 2)
    if(cid == sLastMmsCid[socketId] && isDualLEnabled()) {
        RIL_SOCKET_ID slaveSocketId = RIL_SOCKET_1;
        if(socketId == RIL_SOCKET_1) {
            slaveSocketId = RIL_SOCKET_2;
        }
        RLOGD("%s: MMS deactivates on the slave ril, calling restoreDefaultPdpOnOtherCard on %d", __FUNCTION__, slaveSocketId);
        restoreDefaultPdpOnOtherCard(slaveSocketId);
        sLastMmsCid[socketId] = -1;
    }
#endif

    int v6_index = findV6Partner(index, socketId);

#ifdef MMI_ASR_RIL_BRINGUP
#else
    char ifname[32];
    ModemDevice::get_current_modem(socketId)->get_netcard_name(ifname, sizeof(ifname), index);
    ifc_reset_connections(ifname, RESET_ALL_ADDRESSES);
#endif

    if(v6_index >= 0) {
        if(reasonRadioOff) {
            freePdpContextInfo(&gPdpContextInfoList[socketId][v6_index]);
        } else {
            deactivatePDPContext(v6_index + 1, socketId);
        }
        g_datacall_if_index[socketId][v6_index] = -1;
    }
    if(reasonRadioOff) {
        freePdpContextInfo(&gPdpContextInfoList[socketId][cid - 1]);
    } else {
        deactivatePDPContext(cid, socketId);
    }
    g_datacall_if_index[socketId][index] = -1;
#ifdef MMI_ASR_RIL_BRINGUP
#else
    disableInterface(socketId, index);
#endif
    rilPsMutexUnlock();
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
}

void ril_request_last_data_call_fail_cause(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int result;
    if(getLastFailCause(&result) < 0)
        RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
    else
        RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(result));
}

void ril_request_data_call_list(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int num = 0;
    RIL_Data_Call_Response_ps * pdpResponses = NULL;
    RIL_SOCKET_ID socketId = getSocketId();

    RLOGD("%s entry", __FUNCTION__);

    num = generateDataCallList(&pdpResponses, socketId);
    RIL_onRequestComplete(token, RIL_E_SUCCESS, pdpResponses, num * sizeof(RIL_Data_Call_Response_ps));
    if(pdpResponses) freeDataCallResponses(pdpResponses, num);
    RLOGD("%s exit", __FUNCTION__);
}

#ifdef ASR_EXTENDED
void ril_request_set_fdy(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int enable = ((int *)data)[0];
    char cmdString[MAX_AT_LENGTH];

    if(enable) {
        int interval = ((int *)data)[1];
        sprintf(cmdString, "AT*FDY=1,%d", interval);
    } else {
        strcpy(cmdString, "AT*FDY=0");
    }
    ril_handle_cmd_default_response(cmdString, token);
}

void ril_request_fast_dormancy(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ril_handle_cmd_default_response("AT*FASTDORM", token);
}

void ril_request_set_medcr(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int flag = ((int *)data)[0];
    int position = ((int *)data)[1];
    int configVal = ((int *)data)[2];
    ATResponse * response = NULL;
    char cmd[MAX_AT_LENGTH];
    int err;

    memset(cmd, 0, MAX_AT_LENGTH);
    sprintf(cmd, "AT+MEDCR=%d,%d,%d", flag, position, configVal);
    err = at_send_command_timeout(cmd, &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_set_ltepower(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    //UNUSED(datalen);

    int option = ((int *)data)[0];
    int channel = ((int *)data)[1];
   int power = ((int *)data)[2];
    ATResponse * response = NULL;
    char cmd[MAX_AT_LENGTH];
    int err;

    printf("%s, option=0x%x, channel=0x%x, power=0x%x \n", __FUNCTION__, option, channel, power);
    memset(cmd, 0, MAX_AT_LENGTH);
    sprintf(cmd, "AT+LTEPOWER=%d,%d,%d", option, channel, power);
    err = at_send_command_timeout(cmd, &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    printf("%s, RIL_E_MODEM_ERR \n", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

#endif

static int isApnActive(const char * apn, const char * protocol, RIL_SOCKET_ID socketId)
{
    updatePdpContextInfoList(socketId);
    int pppCid = getPPPContextCid();
    for(int i = 0; i < MAX_DATA_CALLS; i++) {
        struct PdpContextInfo * p = gPdpContextInfoList[socketId][i];
        if((pppCid != i + 1) && matchApnPdpType(p, apn, protocol))
            return 1;
    }

    return 0;
}

void ril_request_set_initial_attach_apn(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);
    RIL_InitialAttachApn * iaapn = reinterpret_cast<RIL_InitialAttachApn *>(data);
    const char * ia_user, *ia_passwd;
    char * protocol = NULL, *apn = NULL, *user = NULL, *passwd = NULL;
    ATResponse * p_response = NULL;
    char * line = NULL;
    char cmdString[MAX_AT_LENGTH];
    RIL_SOCKET_ID socketId = getSocketId();
    int apn_diff, auth_diff;
    int authtype = 0;

    // There is only one NVM data copy of LTE initial attach apn for both cards.
    // So only set LTE initial attach apn for the master(LTE) card.
    if(!isMasterRil(socketId)) {
        RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
        return;
    }

    int err = at_send_command_singleline("AT*CGDFLT=1", "*CGDFLT:", &p_response);
    if(err < 0 || p_response->success == 0) {
        RLOGW("Fail to get initial attach apn.");
        goto error;
    }
    line = p_response->p_intermediates->line;
    err = at_tok_start(&line);
    if(err < 0) goto error;
    err = at_tok_nextstr(&line, &protocol);
    if(err < 0) goto error;
    err = at_tok_nextstr(&line, &apn);
    if(err < 0) goto error;
    apn_diff = strcasecmp(iaapn->apn, apn) || strcasecmp(iaapn->protocol, protocol);
    if(apn_diff) {
        at_response_free(p_response);
        p_response = NULL;
        sprintf(cmdString, "AT*CGDFLT=1,\"%s\",\"%s\",,,,,,,,,,,,,,,,,,1", iaapn->protocol, iaapn->apn);
        err = at_send_command(cmdString,  &p_response);
        if(err < 0 || p_response->success == 0) {
            RLOGW("Fail to set initial attach apn.");
            goto error;
        }
    }

    at_response_free(p_response);
    p_response = NULL;
    err = at_send_command_singleline("AT*CGDFAUTH?", "*CGDFAUTH:", &p_response);
    if(err < 0 || p_response->success == 0) {
        RLOGW("Fail to get initial attach PDN authentication info.");
        goto error;
    }
    line = p_response->p_intermediates->line;
    err = at_tok_start(&line);
    if(err < 0) goto error;
    err = at_tok_nextint(&line, &authtype);
    if(err < 0) goto error;
    err = at_tok_nextstr(&line, &user);
    if(err < 0) goto error;
    err = at_tok_nextstr(&line, &passwd);
    if(err < 0) goto error;

    if(iaapn->authtype < 0)
        iaapn->authtype = isEmpty(iaapn->username) ? 0 : 1;
    if(iaapn->authtype == 3)
        iaapn->authtype = 1;

    // strcmp does not accept NULL
    ia_user = iaapn->username ? iaapn->username : "";
    ia_passwd = iaapn->password ? iaapn->password : "";

    // user name and password is not allowed to be empty by SAC if authtype is not 0(none)
    if(iaapn->authtype > 0) {
        ia_user = isEmpty(ia_user) ? "none" : ia_user;
        ia_passwd = isEmpty(ia_passwd) ? "none" : ia_passwd;
    }
    auth_diff = iaapn->authtype != authtype || strcmp(ia_user, user) || strcmp(ia_passwd, passwd);
    if(auth_diff) {
        at_response_free(p_response);
        p_response = NULL;
        sprintf(cmdString, "AT*CGDFAUTH=1,%d,\"%s\",\"%s\"", iaapn->authtype, ia_user, ia_passwd);
        err = at_send_command(cmdString,  &p_response);
        if(err < 0 || p_response->success == 0) {
            RLOGW("Fail to set initial attach PDN authentication info.");
            goto error;
        }
    }

    if(apn_diff || auth_diff) {
        int reattach = 0;
        if(getRadioState(socketId) == RADIO_STATE_OFF) {
            reattach = 0;
        } else if(!isDataServiceRegistered(socketId)) {
            // Data service is attaching.
            reattach = 1;
        } else if(IsLTEAttached(socketId))  {
            // LTE attached
            reattach = !isApnActive(iaapn->apn, iaapn->protocol, socketId);
        }
        if(reattach && getDesiredAllowDataState(socketId)) {
            at_send_command_timeout("AT+CGATT=0", NULL, TIMEOUT_CGATT);
            at_send_command_timeout("AT+CGATT=1", NULL, TIMEOUT_CGATT);
        }
    }
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    at_response_free(p_response);
    return;
error:
    at_response_free(p_response);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
}
bool using3GPP2()
{
    char sNwType[PROPERTY_VALUE_MAX] = "\0";
    property_get("ro.telephony.default_network", sNwType, "-1");
    int iNwType = atoi(sNwType);

    if((iNwType > 3 &&  iNwType < 9) ||
            (iNwType == 21)) {//See TelephonyManager.getPhoneType(...)
        RLOGW("ARIL_PS using3GPP2? nwType:%d", iNwType);
        return true;
    }
    return false;
}
void ril_request_get_ims_registration_state(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int result[3] = {0};
    int err;
    ATResponse * p_response = NULL;
    char * line = NULL;
    int n;

    err = at_send_command_singleline("AT+CIREG?", "+CIREG:", &p_response);

    if(err < 0 || p_response->success == 0) {
        RLOGW("Fail to get IMS registration state.");
        goto error;
    }

    line = p_response->p_intermediates->line;
    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &n);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &(result[0]));
    if(err < 0) goto error;

    //android RIL_REQUEST_IMS_REGISTRATION_STATE response)[1] is IMS SMS format
    //3GPP Technologies - 1 / 3GPP2 Technologies - 2
    //our IMS stack expect mIms is false in ImsSMSDispatcher,so set FORMAT_UNKNOWN
    if(using3GPP2()) {
        result[1] = 2;
    } else {
        result[1] = 1;
    }

    if(at_tok_hasmore(&line)) {
        err = at_tok_nextint(&line, &(result[2]));
        if(err < 0) goto error;
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(result));
    at_response_free(p_response);
    p_response = NULL;
    return;
error:
    at_response_free(p_response);
    p_response = NULL;
    RLOGE("%s: Format error in this AT response", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
}

void ril_request_set_data_profile(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);

    int count = 0, i = 0, err = 0;
    char cmdString[MAX_AT_LENGTH];
    ATResponse * p_response = NULL;

    RIL_DataProfileInfo ** pp =
        (RIL_DataProfileInfo **) data;

    count = datalen / sizeof(RIL_DataProfileInfo *);

    for(i = 0; i < count; i++) {
        RIL_DataProfileInfo * p = pp[i];

        snprintf(cmdString, MAX_AT_LENGTH,
                 "AT+VZWAPNE=%d,%d,\"%s\",\"%s\",\"%s\",\"%s\",%d",
                 p->profileId, p->profileId, p->apn, p->protocol,
                 "LTE", p->enabled ? "Enabled" : "Disabled", p->waitTime);

        err = at_send_command(cmdString, &p_response);

        if(err < 0 || p_response->success == 0) {
            RLOGW("Fail sync APN. CMD: %s", cmdString);
            at_response_free(p_response);
            p_response = NULL;
            goto error;
        }
        at_response_free(p_response);
        p_response = NULL;
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    return;
error:
    RLOGE("%s: Format error in this AT response", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_REQUEST_NOT_SUPPORTED, NULL, 0);
}

void handle_ciregu(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);

    char * line = NULL;
    int err;
    char * linesave = NULL;
    RIL_SOCKET_ID socketId = getSocketId();
    int oldRegInfo = sImsRegInfo[socketId].regInfo;
    int oldExtInfo = sImsRegInfo[socketId].extInfo;
    int regInfo = 0;
    int extInfo = 0;

    line = lv_strdup(s);
    linesave = line;
    err = at_tok_start(&line);
    if(err < 0) goto exit;
    err = at_tok_nextint(&line, &regInfo);
    if(err < 0) goto exit;

    if(at_tok_hasmore(&line)) {
        err = at_tok_nextint(&line, &extInfo);
        if(err < 0) goto exit;
        RLOGD("%s: Ims ext info: %d\n", __FUNCTION__, extInfo);
        sImsRegInfo[socketId].extInfo = extInfo;
    }
    sImsRegInfo[socketId].regInfo = regInfo;
    RLOGD("%s: Ims Register info: %d\n", __FUNCTION__, regInfo);

    if((regInfo != oldRegInfo) || (extInfo != oldExtInfo))
        RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_IMS_NETWORK_STATE_CHANGED, &regInfo, sizeof(int), socketId);

exit:
    if(linesave != NULL) UI_FREE(linesave);
    return;
}

/* Really, we can ignore NW CLASS and ME CLASS events here,
 * but right now we don't since extranous
 * RIL_UNSOL_DATA_CALL_LIST_CHANGED calls are tolerated
 */
/* can't issue AT commands here -- call on ps queue thread */
void handle_cgev(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);
    int cid;
    RIL_SOCKET_ID socketId = getSocketId();
    if(sscanf(s, "+CGEV: NW PDN DEACT %d", &cid) == 1
            || sscanf(s, "+CGEV: ME PDN DEACT %d", &cid) == 1) {
        if(g_datacall_if_index[socketId][cid - 1] != -1)
            notifyDataCallChanged(socketId);
    } else if(!strcmp(s, "+CGEV: ME DETACH")) {
        // PS is detached, clear everything
        notifyPDPClear(socketId);
    }
}

int isImsVTSupported()
{
    RIL_SOCKET_ID socketId = getSocketId();
    return ((sImsRegInfo[socketId].extInfo & IMS_CAPABILITY_VIDEO) == IMS_CAPABILITY_VIDEO) ? 1 : 0;
}

typedef struct __DATA_ALLOW_INFO__ {
    RIL_SOCKET_ID socketId;
    RIL_Token token;
} DataAlowInfo;
void delay_proc_request_allow_data(void * param)
{
    DataAlowInfo * daI = static_cast<DataAlowInfo *>(param);
    int socketId = daI->socketId;
    RIL_Token token = daI->token;
    int err = -1;

    RLOGD("ARIL_PS: [%s]: socketId:%d.", __FUNCTION__, socketId);
    rilPsMutexLock();
    err = libSetAllowData(0);
    rilPsMutexUnlock();

    if(err != 0) {
        goto error;
    }
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    RLOGI("ARIL_PS:[%s]: delay_proc_request_allow_data err = %d", __FUNCTION__, err);
    delete daI;
}
void serial_allow_data_by_mode(RIL_SOCKET_ID socketId, RIL_Token token)
{
    char ptv[PROPERTY_VALUE_MAX] = "\0";
    property_get(PHONE_SWITCH_MODE_KEY, ptv, "0");
    //if(!strcmp(ptv,"1")) {} TODO just disable the network interfaces  for the slave card MMS request ?
    RLOGI("ARIL_PS:[%s]: PHONE_SWITCH_MODE_KEY = %s", __FUNCTION__, ptv);
    notifyPDPClearOverIms(socketId);

    DataAlowInfo * r = new DataAlowInfo;
    ASSERT(r != NULL);
    r->socketId = socketId;
    r->token = token;
    enque(getWorkQueue(SERVICE_PS, socketId), delay_proc_request_allow_data, reinterpret_cast<void *>(r), socketId);
}
/**
 * RIL_REQUEST_ALLOW_DATA
 *
 * Tells the modem whether data calls are allowed or not
 *
 *  Valid errors:
 *  SUCCESS
 *  RADIO_NOT_AVAILABLE (radio resetting)
 *  GENERIC_FAILURE
 */
void ril_request_allow_data(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int allow = ((int *)data)[0];
    int err = -1, state;
    RIL_SOCKET_ID socketId = getSocketId();

    if(getRadioState(socketId) == RADIO_STATE_ON) {
        // If radio is already on, send AT*PSDC=0/1 to CP directly;
        // If radio is still off, just record the allow data state,
        // and this state will set to CP while handling power on
        // request.
        err = libGetAllowData(&state);

        if(err != 0)
            goto error;

        if(allow == state) {
            RLOGI("[%s]: already in this state.", __FUNCTION__);
        } else {
            if(allow == 0) {
                RLOGD("ARIL_PS:[%s]: invoke serial_allow_data_by_mode.", __FUNCTION__);
                serial_allow_data_by_mode(socketId, token);
                return;
            }
            rilPsMutexLock();
            err = libSetAllowData(allow);
            rilPsMutexUnlock();

            if(err != 0)
                goto error;
        }
#if (SIM_COUNT >= 2)
        bool desiredState = getDesiredAllowDataState(socketId);
        RLOGD("ARIL_PS:[%s]: socketId:%d desiredState:%d err:%d", __FUNCTION__, socketId, desiredState, err);

        if(isDualLEnabled() && err == 0 && allow == 1 && desiredState) {
            bool propChange = true;
            if(state == allow) { //Skip libSetAllowData
                char sim2Enable[512];
                property_get("persist.sys.sim2.master.enable", sim2Enable, "0");
                if((socketId == RIL_SOCKET_2 && 0 == strcmp(sim2Enable, "1"))
                        || (socketId == RIL_SOCKET_1 && 0 == strcmp(sim2Enable, "0"))) {
                    propChange = false;
                }
            } else {
                triggerPrevOperProc(3, modemSwitchOnMasterCardChange);
            }
            if(propChange) {
                if(socketId == RIL_SOCKET_2) {
                    property_set("persist.sys.sim2.master.enable", "1");
                } else {
                    property_set("persist.sys.sim2.master.enable", "0");
                }
            }
        }
#endif
    }
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    RLOGI("[%s]: set allow data done, allow data - %d, err = %d",
          __FUNCTION__, allow, err);
}

};
