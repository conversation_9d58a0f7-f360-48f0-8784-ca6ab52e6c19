{
  __esan_aligned*;
  __esan_exit;
  __esan_init;
  __esan_unaligned*;
  __fprintf_chk;
  __getdelim;
  __interceptor___fprintf_chk;
  __interceptor___getdelim;
  __interceptor___isoc99_fprintf;
  __interceptor___isoc99_fscanf;
  __interceptor___isoc99_printf;
  __interceptor___isoc99_scanf;
  __interceptor___isoc99_snprintf;
  __interceptor___isoc99_sprintf;
  __interceptor___isoc99_sscanf;
  __interceptor___isoc99_vfprintf;
  __interceptor___isoc99_vfscanf;
  __interceptor___isoc99_vprintf;
  __interceptor___isoc99_vscanf;
  __interceptor___isoc99_vsnprintf;
  __interceptor___isoc99_vsprintf;
  __interceptor___isoc99_vsscanf;
  __interceptor___lxstat;
  __interceptor___lxstat64;
  __interceptor___overflow;
  __interceptor___pthread_mutex_lock;
  __interceptor___pthread_mutex_unlock;
  __interceptor___snprintf_chk;
  __interceptor___sprintf_chk;
  __interceptor___strndup;
  __interceptor___strxfrm_l;
  __interceptor___uflow;
  __interceptor___underflow;
  __interceptor___vsnprintf_chk;
  __interceptor___vsprintf_chk;
  __interceptor___wcsxfrm_l;
  __interceptor___woverflow;
  __interceptor___wuflow;
  __interceptor___wunderflow;
  __interceptor___xpg_strerror_r;
  __interceptor___xstat;
  __interceptor___xstat64;
  __interceptor__exit;
  __interceptor__obstack_begin;
  __interceptor__obstack_begin_1;
  __interceptor__obstack_newchunk;
  __interceptor_accept;
  __interceptor_accept4;
  __interceptor_asctime;
  __interceptor_asctime_r;
  __interceptor_asprintf;
  __interceptor_backtrace;
  __interceptor_backtrace_symbols;
  __interceptor_calloc;
  __interceptor_canonicalize_file_name;
  __interceptor_capget;
  __interceptor_capset;
  __interceptor_clock_getres;
  __interceptor_clock_gettime;
  __interceptor_clock_settime;
  __interceptor_confstr;
  __interceptor_creat;
  __interceptor_creat64;
  __interceptor_ctermid;
  __interceptor_ctime;
  __interceptor_ctime_r;
  __interceptor_dlclose;
  __interceptor_dlopen;
  __interceptor_drand48_r;
  __interceptor_endgrent;
  __interceptor_endpwent;
  __interceptor_ether_aton;
  __interceptor_ether_aton_r;
  __interceptor_ether_hostton;
  __interceptor_ether_line;
  __interceptor_ether_ntoa;
  __interceptor_ether_ntoa_r;
  __interceptor_ether_ntohost;
  __interceptor_eventfd_read;
  __interceptor_eventfd_write;
  __interceptor_fclose;
  __interceptor_fdopen;
  __interceptor_fflush;
  __interceptor_fgetgrent;
  __interceptor_fgetgrent_r;
  __interceptor_fgetpwent;
  __interceptor_fgetpwent_r;
  __interceptor_fgets;
  __interceptor_fgetxattr;
  __interceptor_flistxattr;
  __interceptor_fmemopen;
  __interceptor_fopen;
  __interceptor_fopen64;
  __interceptor_fopencookie;
  __interceptor_fprintf;
  __interceptor_fputs;
  __interceptor_fread;
  __interceptor_free;
  __interceptor_freopen;
  __interceptor_freopen64;
  __interceptor_frexp;
  __interceptor_frexpf;
  __interceptor_frexpl;
  __interceptor_fscanf;
  __interceptor_fstatfs;
  __interceptor_fstatfs64;
  __interceptor_fstatvfs;
  __interceptor_fstatvfs64;
  __interceptor_ftime;
  __interceptor_fwrite;
  __interceptor_get_current_dir_name;
  __interceptor_getaddrinfo;
  __interceptor_getcwd;
  __interceptor_getdelim;
  __interceptor_getgrent;
  __interceptor_getgrent_r;
  __interceptor_getgrgid;
  __interceptor_getgrgid_r;
  __interceptor_getgrnam;
  __interceptor_getgrnam_r;
  __interceptor_getgroups;
  __interceptor_gethostbyaddr;
  __interceptor_gethostbyaddr_r;
  __interceptor_gethostbyname;
  __interceptor_gethostbyname2;
  __interceptor_gethostbyname2_r;
  __interceptor_gethostbyname_r;
  __interceptor_gethostent;
  __interceptor_gethostent_r;
  __interceptor_getifaddrs;
  __interceptor_getitimer;
  __interceptor_getline;
  __interceptor_getloadavg;
  __interceptor_getmntent;
  __interceptor_getmntent_r;
  __interceptor_getnameinfo;
  __interceptor_getpass;
  __interceptor_getpeername;
  __interceptor_getpwent;
  __interceptor_getpwent_r;
  __interceptor_getpwnam;
  __interceptor_getpwnam_r;
  __interceptor_getpwuid;
  __interceptor_getpwuid_r;
  __interceptor_getresgid;
  __interceptor_getresuid;
  __interceptor_getsockname;
  __interceptor_getsockopt;
  __interceptor_getutent;
  __interceptor_getutid;
  __interceptor_getutline;
  __interceptor_getutxent;
  __interceptor_getutxid;
  __interceptor_getutxline;
  __interceptor_getxattr;
  __interceptor_glob;
  __interceptor_glob64;
  __interceptor_gmtime;
  __interceptor_gmtime_r;
  __interceptor_iconv;
  __interceptor_if_indextoname;
  __interceptor_if_nametoindex;
  __interceptor_inet_aton;
  __interceptor_inet_ntop;
  __interceptor_inet_pton;
  __interceptor_initgroups;
  __interceptor_ioctl;
  __interceptor_lgamma;
  __interceptor_lgamma_r;
  __interceptor_lgammaf;
  __interceptor_lgammaf_r;
  __interceptor_lgammal;
  __interceptor_lgammal_r;
  __interceptor_lgetxattr;
  __interceptor_listxattr;
  __interceptor_llistxattr;
  __interceptor_localtime;
  __interceptor_localtime_r;
  __interceptor_lrand48_r;
  __interceptor_malloc;
  __interceptor_mbsnrtowcs;
  __interceptor_mbsrtowcs;
  __interceptor_mbstowcs;
  __interceptor_mcheck;
  __interceptor_mcheck_pedantic;
  __interceptor_memchr;
  __interceptor_memcmp;
  __interceptor_memcpy;
  __interceptor_memmem;
  __interceptor_memmove;
  __interceptor_memrchr;
  __interceptor_memset;
  __interceptor_mincore;
  __interceptor_mktime;
  __interceptor_mlock;
  __interceptor_mlockall;
  __interceptor_mmap;
  __interceptor_mmap64;
  __interceptor_modf;
  __interceptor_modff;
  __interceptor_modfl;
  __interceptor_mprobe;
  __interceptor_mprotect;
  __interceptor_munlock;
  __interceptor_munlockall;
  __interceptor_name_to_handle_at;
  __interceptor_open;
  __interceptor_open64;
  __interceptor_open_by_handle_at;
  __interceptor_open_memstream;
  __interceptor_open_wmemstream;
  __interceptor_opendir;
  __interceptor_pclose;
  __interceptor_poll;
  __interceptor_popen;
  __interceptor_ppoll;
  __interceptor_prctl;
  __interceptor_pread;
  __interceptor_pread64;
  __interceptor_preadv;
  __interceptor_preadv64;
  __interceptor_printf;
  __interceptor_process_vm_readv;
  __interceptor_process_vm_writev;
  __interceptor_pthread_attr_getaffinity_np;
  __interceptor_pthread_attr_getdetachstate;
  __interceptor_pthread_attr_getguardsize;
  __interceptor_pthread_attr_getinheritsched;
  __interceptor_pthread_attr_getschedparam;
  __interceptor_pthread_attr_getschedpolicy;
  __interceptor_pthread_attr_getscope;
  __interceptor_pthread_attr_getstack;
  __interceptor_pthread_attr_getstacksize;
  __interceptor_pthread_barrierattr_getpshared;
  __interceptor_pthread_condattr_getclock;
  __interceptor_pthread_condattr_getpshared;
  __interceptor_pthread_getname_np;
  __interceptor_pthread_getschedparam;
  __interceptor_pthread_mutex_lock;
  __interceptor_pthread_mutex_unlock;
  __interceptor_pthread_mutexattr_getprioceiling;
  __interceptor_pthread_mutexattr_getprotocol;
  __interceptor_pthread_mutexattr_getpshared;
  __interceptor_pthread_mutexattr_getrobust;
  __interceptor_pthread_mutexattr_getrobust_np;
  __interceptor_pthread_mutexattr_gettype;
  __interceptor_pthread_rwlockattr_getkind_np;
  __interceptor_pthread_rwlockattr_getpshared;
  __interceptor_pthread_setcancelstate;
  __interceptor_pthread_setcanceltype;
  __interceptor_pthread_setname_np;
  __interceptor_pthread_sigmask;
  __interceptor_ptrace;
  __interceptor_puts;
  __interceptor_pututxline;
  __interceptor_pwrite;
  __interceptor_pwrite64;
  __interceptor_pwritev;
  __interceptor_pwritev64;
  __interceptor_rand_r;
  __interceptor_random_r;
  __interceptor_read;
  __interceptor_readdir;
  __interceptor_readdir64;
  __interceptor_readdir64_r;
  __interceptor_readdir_r;
  __interceptor_readlink;
  __interceptor_readlinkat;
  __interceptor_readv;
  __interceptor_recv;
  __interceptor_recvfrom;
  __interceptor_recvmmsg;
  __interceptor_recvmsg;
  __interceptor_regcomp;
  __interceptor_regerror;
  __interceptor_regexec;
  __interceptor_regfree;
  __interceptor_remquo;
  __interceptor_remquof;
  __interceptor_remquol;
  __interceptor_rmdir;
  __interceptor_scandir;
  __interceptor_scandir64;
  __interceptor_scanf;
  __interceptor_sched_getaffinity;
  __interceptor_sched_getparam;
  __interceptor_sem_destroy;
  __interceptor_sem_getvalue;
  __interceptor_sem_init;
  __interceptor_sem_post;
  __interceptor_sem_timedwait;
  __interceptor_sem_trywait;
  __interceptor_sem_wait;
  __interceptor_send;
  __interceptor_sendmmsg;
  __interceptor_sendmsg;
  __interceptor_sendto;
  __interceptor_setbuf;
  __interceptor_setbuffer;
  __interceptor_setgrent;
  __interceptor_setitimer;
  __interceptor_setlinebuf;
  __interceptor_setlocale;
  __interceptor_setpwent;
  __interceptor_setvbuf;
  __interceptor_shmctl;
  __interceptor_sigaction;
  __interceptor_sigemptyset;
  __interceptor_sigfillset;
  __interceptor_signal;
  __interceptor_sigpending;
  __interceptor_sigprocmask;
  __interceptor_sigtimedwait;
  __interceptor_sigwait;
  __interceptor_sigwaitinfo;
  __interceptor_sincos;
  __interceptor_sincosf;
  __interceptor_sincosl;
  __interceptor_snprintf;
  __interceptor_sprintf;
  __interceptor_sscanf;
  __interceptor_statfs;
  __interceptor_statfs64;
  __interceptor_statvfs;
  __interceptor_statvfs64;
  __interceptor_strcasecmp;
  __interceptor_strcasestr;
  __interceptor_strchr;
  __interceptor_strchrnul;
  __interceptor_strcmp;
  __interceptor_strcpy;
  __interceptor_strcspn;
  __interceptor_strerror;
  __interceptor_strerror_r;
  __interceptor_strlen;
  __interceptor_strncasecmp;
  __interceptor_strncmp;
  __interceptor_strncpy;
  __interceptor_strndup;
  __interceptor_strnlen;
  __interceptor_strpbrk;
  __interceptor_strptime;
  __interceptor_strrchr;
  __interceptor_strspn;
  __interceptor_strstr;
  __interceptor_strtoimax;
  __interceptor_strtok;
  __interceptor_strtoumax;
  __interceptor_strxfrm;
  __interceptor_strxfrm_l;
  __interceptor_sysinfo;
  __interceptor_tcgetattr;
  __interceptor_tempnam;
  __interceptor_textdomain;
  __interceptor_time;
  __interceptor_timerfd_gettime;
  __interceptor_timerfd_settime;
  __interceptor_times;
  __interceptor_tmpnam;
  __interceptor_tmpnam_r;
  __interceptor_tsearch;
  __interceptor_ttyname_r;
  __interceptor_unlink;
  __interceptor_vasprintf;
  __interceptor_vfprintf;
  __interceptor_vfscanf;
  __interceptor_vprintf;
  __interceptor_vscanf;
  __interceptor_vsnprintf;
  __interceptor_vsprintf;
  __interceptor_vsscanf;
  __interceptor_wait;
  __interceptor_wait3;
  __interceptor_wait4;
  __interceptor_waitid;
  __interceptor_waitpid;
  __interceptor_wcrtomb;
  __interceptor_wcscat;
  __interceptor_wcslen;
  __interceptor_wcsncat;
  __interceptor_wcsnlen;
  __interceptor_wcsnrtombs;
  __interceptor_wcsrtombs;
  __interceptor_wcstombs;
  __interceptor_wcsxfrm;
  __interceptor_wcsxfrm_l;
  __interceptor_wordexp;
  __interceptor_write;
  __interceptor_writev;
  __interceptor_xdr_bool;
  __interceptor_xdr_bytes;
  __interceptor_xdr_char;
  __interceptor_xdr_double;
  __interceptor_xdr_enum;
  __interceptor_xdr_float;
  __interceptor_xdr_hyper;
  __interceptor_xdr_int;
  __interceptor_xdr_int16_t;
  __interceptor_xdr_int32_t;
  __interceptor_xdr_int64_t;
  __interceptor_xdr_int8_t;
  __interceptor_xdr_long;
  __interceptor_xdr_longlong_t;
  __interceptor_xdr_quad_t;
  __interceptor_xdr_short;
  __interceptor_xdr_string;
  __interceptor_xdr_u_char;
  __interceptor_xdr_u_hyper;
  __interceptor_xdr_u_int;
  __interceptor_xdr_u_long;
  __interceptor_xdr_u_longlong_t;
  __interceptor_xdr_u_quad_t;
  __interceptor_xdr_u_short;
  __interceptor_xdr_uint16_t;
  __interceptor_xdr_uint32_t;
  __interceptor_xdr_uint64_t;
  __interceptor_xdr_uint8_t;
  __interceptor_xdrmem_create;
  __interceptor_xdrstdio_create;
  __isoc99_fprintf;
  __isoc99_fscanf;
  __isoc99_printf;
  __isoc99_scanf;
  __isoc99_snprintf;
  __isoc99_sprintf;
  __isoc99_sscanf;
  __isoc99_vfprintf;
  __isoc99_vfscanf;
  __isoc99_vprintf;
  __isoc99_vscanf;
  __isoc99_vsnprintf;
  __isoc99_vsprintf;
  __isoc99_vsscanf;
  __lxstat;
  __lxstat64;
  __overflow;
  __pthread_mutex_lock;
  __pthread_mutex_unlock;
  __sanitizer_acquire_crash_state;
  __sanitizer_get_module_and_offset_for_pc;
  __sanitizer_install_malloc_and_free_hooks;
  __sanitizer_report_error_summary;
  __sanitizer_sandbox_on_notify;
  __sanitizer_set_death_callback;
  __sanitizer_set_report_fd;
  __sanitizer_set_report_path;
  __sanitizer_symbolize_global;
  __sanitizer_symbolize_pc;
  __sanitizer_syscall_post_impl_accept;
  __sanitizer_syscall_post_impl_accept4;
  __sanitizer_syscall_post_impl_access;
  __sanitizer_syscall_post_impl_acct;
  __sanitizer_syscall_post_impl_add_key;
  __sanitizer_syscall_post_impl_adjtimex;
  __sanitizer_syscall_post_impl_alarm;
  __sanitizer_syscall_post_impl_bdflush;
  __sanitizer_syscall_post_impl_bind;
  __sanitizer_syscall_post_impl_brk;
  __sanitizer_syscall_post_impl_capget;
  __sanitizer_syscall_post_impl_capset;
  __sanitizer_syscall_post_impl_chdir;
  __sanitizer_syscall_post_impl_chmod;
  __sanitizer_syscall_post_impl_chown;
  __sanitizer_syscall_post_impl_chroot;
  __sanitizer_syscall_post_impl_clock_adjtime;
  __sanitizer_syscall_post_impl_clock_getres;
  __sanitizer_syscall_post_impl_clock_gettime;
  __sanitizer_syscall_post_impl_clock_nanosleep;
  __sanitizer_syscall_post_impl_clock_settime;
  __sanitizer_syscall_post_impl_close;
  __sanitizer_syscall_post_impl_connect;
  __sanitizer_syscall_post_impl_creat;
  __sanitizer_syscall_post_impl_delete_module;
  __sanitizer_syscall_post_impl_dup;
  __sanitizer_syscall_post_impl_dup2;
  __sanitizer_syscall_post_impl_dup3;
  __sanitizer_syscall_post_impl_epoll_create;
  __sanitizer_syscall_post_impl_epoll_create1;
  __sanitizer_syscall_post_impl_epoll_ctl;
  __sanitizer_syscall_post_impl_epoll_pwait;
  __sanitizer_syscall_post_impl_epoll_wait;
  __sanitizer_syscall_post_impl_eventfd;
  __sanitizer_syscall_post_impl_eventfd2;
  __sanitizer_syscall_post_impl_exit;
  __sanitizer_syscall_post_impl_exit_group;
  __sanitizer_syscall_post_impl_faccessat;
  __sanitizer_syscall_post_impl_fchdir;
  __sanitizer_syscall_post_impl_fchmod;
  __sanitizer_syscall_post_impl_fchmodat;
  __sanitizer_syscall_post_impl_fchown;
  __sanitizer_syscall_post_impl_fchownat;
  __sanitizer_syscall_post_impl_fcntl;
  __sanitizer_syscall_post_impl_fcntl64;
  __sanitizer_syscall_post_impl_fdatasync;
  __sanitizer_syscall_post_impl_fgetxattr;
  __sanitizer_syscall_post_impl_flistxattr;
  __sanitizer_syscall_post_impl_flock;
  __sanitizer_syscall_post_impl_fork;
  __sanitizer_syscall_post_impl_fremovexattr;
  __sanitizer_syscall_post_impl_fsetxattr;
  __sanitizer_syscall_post_impl_fstat;
  __sanitizer_syscall_post_impl_fstat64;
  __sanitizer_syscall_post_impl_fstatat64;
  __sanitizer_syscall_post_impl_fstatfs;
  __sanitizer_syscall_post_impl_fstatfs64;
  __sanitizer_syscall_post_impl_fsync;
  __sanitizer_syscall_post_impl_ftruncate;
  __sanitizer_syscall_post_impl_futimesat;
  __sanitizer_syscall_post_impl_get_mempolicy;
  __sanitizer_syscall_post_impl_get_robust_list;
  __sanitizer_syscall_post_impl_getcpu;
  __sanitizer_syscall_post_impl_getcwd;
  __sanitizer_syscall_post_impl_getdents;
  __sanitizer_syscall_post_impl_getdents64;
  __sanitizer_syscall_post_impl_getegid;
  __sanitizer_syscall_post_impl_geteuid;
  __sanitizer_syscall_post_impl_getgid;
  __sanitizer_syscall_post_impl_getgroups;
  __sanitizer_syscall_post_impl_gethostname;
  __sanitizer_syscall_post_impl_getitimer;
  __sanitizer_syscall_post_impl_getpeername;
  __sanitizer_syscall_post_impl_getpgid;
  __sanitizer_syscall_post_impl_getpgrp;
  __sanitizer_syscall_post_impl_getpid;
  __sanitizer_syscall_post_impl_getppid;
  __sanitizer_syscall_post_impl_getpriority;
  __sanitizer_syscall_post_impl_getresgid;
  __sanitizer_syscall_post_impl_getresuid;
  __sanitizer_syscall_post_impl_getrlimit;
  __sanitizer_syscall_post_impl_getrusage;
  __sanitizer_syscall_post_impl_getsid;
  __sanitizer_syscall_post_impl_getsockname;
  __sanitizer_syscall_post_impl_getsockopt;
  __sanitizer_syscall_post_impl_gettid;
  __sanitizer_syscall_post_impl_gettimeofday;
  __sanitizer_syscall_post_impl_getuid;
  __sanitizer_syscall_post_impl_getxattr;
  __sanitizer_syscall_post_impl_init_module;
  __sanitizer_syscall_post_impl_inotify_add_watch;
  __sanitizer_syscall_post_impl_inotify_init;
  __sanitizer_syscall_post_impl_inotify_init1;
  __sanitizer_syscall_post_impl_inotify_rm_watch;
  __sanitizer_syscall_post_impl_io_cancel;
  __sanitizer_syscall_post_impl_io_destroy;
  __sanitizer_syscall_post_impl_io_getevents;
  __sanitizer_syscall_post_impl_io_setup;
  __sanitizer_syscall_post_impl_io_submit;
  __sanitizer_syscall_post_impl_ioctl;
  __sanitizer_syscall_post_impl_ioperm;
  __sanitizer_syscall_post_impl_ioprio_get;
  __sanitizer_syscall_post_impl_ioprio_set;
  __sanitizer_syscall_post_impl_ipc;
  __sanitizer_syscall_post_impl_kexec_load;
  __sanitizer_syscall_post_impl_keyctl;
  __sanitizer_syscall_post_impl_kill;
  __sanitizer_syscall_post_impl_lchown;
  __sanitizer_syscall_post_impl_lgetxattr;
  __sanitizer_syscall_post_impl_link;
  __sanitizer_syscall_post_impl_linkat;
  __sanitizer_syscall_post_impl_listen;
  __sanitizer_syscall_post_impl_listxattr;
  __sanitizer_syscall_post_impl_llistxattr;
  __sanitizer_syscall_post_impl_llseek;
  __sanitizer_syscall_post_impl_lookup_dcookie;
  __sanitizer_syscall_post_impl_lremovexattr;
  __sanitizer_syscall_post_impl_lseek;
  __sanitizer_syscall_post_impl_lsetxattr;
  __sanitizer_syscall_post_impl_lstat;
  __sanitizer_syscall_post_impl_lstat64;
  __sanitizer_syscall_post_impl_madvise;
  __sanitizer_syscall_post_impl_mbind;
  __sanitizer_syscall_post_impl_migrate_pages;
  __sanitizer_syscall_post_impl_mincore;
  __sanitizer_syscall_post_impl_mkdir;
  __sanitizer_syscall_post_impl_mkdirat;
  __sanitizer_syscall_post_impl_mknod;
  __sanitizer_syscall_post_impl_mknodat;
  __sanitizer_syscall_post_impl_mlock;
  __sanitizer_syscall_post_impl_mlockall;
  __sanitizer_syscall_post_impl_mmap_pgoff;
  __sanitizer_syscall_post_impl_mount;
  __sanitizer_syscall_post_impl_move_pages;
  __sanitizer_syscall_post_impl_mprotect;
  __sanitizer_syscall_post_impl_mq_getsetattr;
  __sanitizer_syscall_post_impl_mq_notify;
  __sanitizer_syscall_post_impl_mq_open;
  __sanitizer_syscall_post_impl_mq_timedreceive;
  __sanitizer_syscall_post_impl_mq_timedsend;
  __sanitizer_syscall_post_impl_mq_unlink;
  __sanitizer_syscall_post_impl_mremap;
  __sanitizer_syscall_post_impl_msgctl;
  __sanitizer_syscall_post_impl_msgget;
  __sanitizer_syscall_post_impl_msgrcv;
  __sanitizer_syscall_post_impl_msgsnd;
  __sanitizer_syscall_post_impl_msync;
  __sanitizer_syscall_post_impl_munlock;
  __sanitizer_syscall_post_impl_munlockall;
  __sanitizer_syscall_post_impl_munmap;
  __sanitizer_syscall_post_impl_name_to_handle_at;
  __sanitizer_syscall_post_impl_nanosleep;
  __sanitizer_syscall_post_impl_newfstat;
  __sanitizer_syscall_post_impl_newfstatat;
  __sanitizer_syscall_post_impl_newlstat;
  __sanitizer_syscall_post_impl_newstat;
  __sanitizer_syscall_post_impl_newuname;
  __sanitizer_syscall_post_impl_ni_syscall;
  __sanitizer_syscall_post_impl_nice;
  __sanitizer_syscall_post_impl_old_getrlimit;
  __sanitizer_syscall_post_impl_old_mmap;
  __sanitizer_syscall_post_impl_old_readdir;
  __sanitizer_syscall_post_impl_old_select;
  __sanitizer_syscall_post_impl_oldumount;
  __sanitizer_syscall_post_impl_olduname;
  __sanitizer_syscall_post_impl_open;
  __sanitizer_syscall_post_impl_open_by_handle_at;
  __sanitizer_syscall_post_impl_openat;
  __sanitizer_syscall_post_impl_pause;
  __sanitizer_syscall_post_impl_pciconfig_iobase;
  __sanitizer_syscall_post_impl_pciconfig_read;
  __sanitizer_syscall_post_impl_pciconfig_write;
  __sanitizer_syscall_post_impl_perf_event_open;
  __sanitizer_syscall_post_impl_personality;
  __sanitizer_syscall_post_impl_pipe;
  __sanitizer_syscall_post_impl_pipe2;
  __sanitizer_syscall_post_impl_pivot_root;
  __sanitizer_syscall_post_impl_poll;
  __sanitizer_syscall_post_impl_ppoll;
  __sanitizer_syscall_post_impl_pread64;
  __sanitizer_syscall_post_impl_preadv;
  __sanitizer_syscall_post_impl_prlimit64;
  __sanitizer_syscall_post_impl_process_vm_readv;
  __sanitizer_syscall_post_impl_process_vm_writev;
  __sanitizer_syscall_post_impl_pselect6;
  __sanitizer_syscall_post_impl_ptrace;
  __sanitizer_syscall_post_impl_pwrite64;
  __sanitizer_syscall_post_impl_pwritev;
  __sanitizer_syscall_post_impl_quotactl;
  __sanitizer_syscall_post_impl_read;
  __sanitizer_syscall_post_impl_readlink;
  __sanitizer_syscall_post_impl_readlinkat;
  __sanitizer_syscall_post_impl_readv;
  __sanitizer_syscall_post_impl_reboot;
  __sanitizer_syscall_post_impl_recv;
  __sanitizer_syscall_post_impl_recvfrom;
  __sanitizer_syscall_post_impl_recvmmsg;
  __sanitizer_syscall_post_impl_recvmsg;
  __sanitizer_syscall_post_impl_remap_file_pages;
  __sanitizer_syscall_post_impl_removexattr;
  __sanitizer_syscall_post_impl_rename;
  __sanitizer_syscall_post_impl_renameat;
  __sanitizer_syscall_post_impl_request_key;
  __sanitizer_syscall_post_impl_restart_syscall;
  __sanitizer_syscall_post_impl_rmdir;
  __sanitizer_syscall_post_impl_rt_sigaction;
  __sanitizer_syscall_post_impl_rt_sigpending;
  __sanitizer_syscall_post_impl_rt_sigprocmask;
  __sanitizer_syscall_post_impl_rt_sigqueueinfo;
  __sanitizer_syscall_post_impl_rt_sigtimedwait;
  __sanitizer_syscall_post_impl_rt_tgsigqueueinfo;
  __sanitizer_syscall_post_impl_sched_get_priority_max;
  __sanitizer_syscall_post_impl_sched_get_priority_min;
  __sanitizer_syscall_post_impl_sched_getaffinity;
  __sanitizer_syscall_post_impl_sched_getparam;
  __sanitizer_syscall_post_impl_sched_getscheduler;
  __sanitizer_syscall_post_impl_sched_rr_get_interval;
  __sanitizer_syscall_post_impl_sched_setaffinity;
  __sanitizer_syscall_post_impl_sched_setparam;
  __sanitizer_syscall_post_impl_sched_setscheduler;
  __sanitizer_syscall_post_impl_sched_yield;
  __sanitizer_syscall_post_impl_select;
  __sanitizer_syscall_post_impl_semctl;
  __sanitizer_syscall_post_impl_semget;
  __sanitizer_syscall_post_impl_semop;
  __sanitizer_syscall_post_impl_semtimedop;
  __sanitizer_syscall_post_impl_send;
  __sanitizer_syscall_post_impl_sendfile;
  __sanitizer_syscall_post_impl_sendfile64;
  __sanitizer_syscall_post_impl_sendmmsg;
  __sanitizer_syscall_post_impl_sendmsg;
  __sanitizer_syscall_post_impl_sendto;
  __sanitizer_syscall_post_impl_set_mempolicy;
  __sanitizer_syscall_post_impl_set_robust_list;
  __sanitizer_syscall_post_impl_set_tid_address;
  __sanitizer_syscall_post_impl_setdomainname;
  __sanitizer_syscall_post_impl_setfsgid;
  __sanitizer_syscall_post_impl_setfsuid;
  __sanitizer_syscall_post_impl_setgid;
  __sanitizer_syscall_post_impl_setgroups;
  __sanitizer_syscall_post_impl_sethostname;
  __sanitizer_syscall_post_impl_setitimer;
  __sanitizer_syscall_post_impl_setns;
  __sanitizer_syscall_post_impl_setpgid;
  __sanitizer_syscall_post_impl_setpriority;
  __sanitizer_syscall_post_impl_setregid;
  __sanitizer_syscall_post_impl_setresgid;
  __sanitizer_syscall_post_impl_setresuid;
  __sanitizer_syscall_post_impl_setreuid;
  __sanitizer_syscall_post_impl_setrlimit;
  __sanitizer_syscall_post_impl_setsid;
  __sanitizer_syscall_post_impl_setsockopt;
  __sanitizer_syscall_post_impl_settimeofday;
  __sanitizer_syscall_post_impl_setuid;
  __sanitizer_syscall_post_impl_setxattr;
  __sanitizer_syscall_post_impl_sgetmask;
  __sanitizer_syscall_post_impl_shmat;
  __sanitizer_syscall_post_impl_shmctl;
  __sanitizer_syscall_post_impl_shmdt;
  __sanitizer_syscall_post_impl_shmget;
  __sanitizer_syscall_post_impl_shutdown;
  __sanitizer_syscall_post_impl_sigaction;
  __sanitizer_syscall_post_impl_signal;
  __sanitizer_syscall_post_impl_signalfd;
  __sanitizer_syscall_post_impl_signalfd4;
  __sanitizer_syscall_post_impl_sigpending;
  __sanitizer_syscall_post_impl_sigprocmask;
  __sanitizer_syscall_post_impl_socket;
  __sanitizer_syscall_post_impl_socketcall;
  __sanitizer_syscall_post_impl_socketpair;
  __sanitizer_syscall_post_impl_splice;
  __sanitizer_syscall_post_impl_spu_create;
  __sanitizer_syscall_post_impl_spu_run;
  __sanitizer_syscall_post_impl_ssetmask;
  __sanitizer_syscall_post_impl_stat;
  __sanitizer_syscall_post_impl_stat64;
  __sanitizer_syscall_post_impl_statfs;
  __sanitizer_syscall_post_impl_statfs64;
  __sanitizer_syscall_post_impl_stime;
  __sanitizer_syscall_post_impl_swapoff;
  __sanitizer_syscall_post_impl_swapon;
  __sanitizer_syscall_post_impl_symlink;
  __sanitizer_syscall_post_impl_symlinkat;
  __sanitizer_syscall_post_impl_sync;
  __sanitizer_syscall_post_impl_syncfs;
  __sanitizer_syscall_post_impl_sysctl;
  __sanitizer_syscall_post_impl_sysfs;
  __sanitizer_syscall_post_impl_sysinfo;
  __sanitizer_syscall_post_impl_syslog;
  __sanitizer_syscall_post_impl_tee;
  __sanitizer_syscall_post_impl_tgkill;
  __sanitizer_syscall_post_impl_time;
  __sanitizer_syscall_post_impl_timer_create;
  __sanitizer_syscall_post_impl_timer_delete;
  __sanitizer_syscall_post_impl_timer_getoverrun;
  __sanitizer_syscall_post_impl_timer_gettime;
  __sanitizer_syscall_post_impl_timer_settime;
  __sanitizer_syscall_post_impl_timerfd_create;
  __sanitizer_syscall_post_impl_timerfd_gettime;
  __sanitizer_syscall_post_impl_timerfd_settime;
  __sanitizer_syscall_post_impl_times;
  __sanitizer_syscall_post_impl_tkill;
  __sanitizer_syscall_post_impl_truncate;
  __sanitizer_syscall_post_impl_umask;
  __sanitizer_syscall_post_impl_umount;
  __sanitizer_syscall_post_impl_uname;
  __sanitizer_syscall_post_impl_unlink;
  __sanitizer_syscall_post_impl_unlinkat;
  __sanitizer_syscall_post_impl_unshare;
  __sanitizer_syscall_post_impl_uselib;
  __sanitizer_syscall_post_impl_ustat;
  __sanitizer_syscall_post_impl_utime;
  __sanitizer_syscall_post_impl_utimensat;
  __sanitizer_syscall_post_impl_utimes;
  __sanitizer_syscall_post_impl_vfork;
  __sanitizer_syscall_post_impl_vhangup;
  __sanitizer_syscall_post_impl_vmsplice;
  __sanitizer_syscall_post_impl_wait4;
  __sanitizer_syscall_post_impl_waitid;
  __sanitizer_syscall_post_impl_waitpid;
  __sanitizer_syscall_post_impl_write;
  __sanitizer_syscall_post_impl_writev;
  __sanitizer_syscall_pre_impl_accept;
  __sanitizer_syscall_pre_impl_accept4;
  __sanitizer_syscall_pre_impl_access;
  __sanitizer_syscall_pre_impl_acct;
  __sanitizer_syscall_pre_impl_add_key;
  __sanitizer_syscall_pre_impl_adjtimex;
  __sanitizer_syscall_pre_impl_alarm;
  __sanitizer_syscall_pre_impl_bdflush;
  __sanitizer_syscall_pre_impl_bind;
  __sanitizer_syscall_pre_impl_brk;
  __sanitizer_syscall_pre_impl_capget;
  __sanitizer_syscall_pre_impl_capset;
  __sanitizer_syscall_pre_impl_chdir;
  __sanitizer_syscall_pre_impl_chmod;
  __sanitizer_syscall_pre_impl_chown;
  __sanitizer_syscall_pre_impl_chroot;
  __sanitizer_syscall_pre_impl_clock_adjtime;
  __sanitizer_syscall_pre_impl_clock_getres;
  __sanitizer_syscall_pre_impl_clock_gettime;
  __sanitizer_syscall_pre_impl_clock_nanosleep;
  __sanitizer_syscall_pre_impl_clock_settime;
  __sanitizer_syscall_pre_impl_close;
  __sanitizer_syscall_pre_impl_connect;
  __sanitizer_syscall_pre_impl_creat;
  __sanitizer_syscall_pre_impl_delete_module;
  __sanitizer_syscall_pre_impl_dup;
  __sanitizer_syscall_pre_impl_dup2;
  __sanitizer_syscall_pre_impl_dup3;
  __sanitizer_syscall_pre_impl_epoll_create;
  __sanitizer_syscall_pre_impl_epoll_create1;
  __sanitizer_syscall_pre_impl_epoll_ctl;
  __sanitizer_syscall_pre_impl_epoll_pwait;
  __sanitizer_syscall_pre_impl_epoll_wait;
  __sanitizer_syscall_pre_impl_eventfd;
  __sanitizer_syscall_pre_impl_eventfd2;
  __sanitizer_syscall_pre_impl_exit;
  __sanitizer_syscall_pre_impl_exit_group;
  __sanitizer_syscall_pre_impl_faccessat;
  __sanitizer_syscall_pre_impl_fchdir;
  __sanitizer_syscall_pre_impl_fchmod;
  __sanitizer_syscall_pre_impl_fchmodat;
  __sanitizer_syscall_pre_impl_fchown;
  __sanitizer_syscall_pre_impl_fchownat;
  __sanitizer_syscall_pre_impl_fcntl;
  __sanitizer_syscall_pre_impl_fcntl64;
  __sanitizer_syscall_pre_impl_fdatasync;
  __sanitizer_syscall_pre_impl_fgetxattr;
  __sanitizer_syscall_pre_impl_flistxattr;
  __sanitizer_syscall_pre_impl_flock;
  __sanitizer_syscall_pre_impl_fork;
  __sanitizer_syscall_pre_impl_fremovexattr;
  __sanitizer_syscall_pre_impl_fsetxattr;
  __sanitizer_syscall_pre_impl_fstat;
  __sanitizer_syscall_pre_impl_fstat64;
  __sanitizer_syscall_pre_impl_fstatat64;
  __sanitizer_syscall_pre_impl_fstatfs;
  __sanitizer_syscall_pre_impl_fstatfs64;
  __sanitizer_syscall_pre_impl_fsync;
  __sanitizer_syscall_pre_impl_ftruncate;
  __sanitizer_syscall_pre_impl_futimesat;
  __sanitizer_syscall_pre_impl_get_mempolicy;
  __sanitizer_syscall_pre_impl_get_robust_list;
  __sanitizer_syscall_pre_impl_getcpu;
  __sanitizer_syscall_pre_impl_getcwd;
  __sanitizer_syscall_pre_impl_getdents;
  __sanitizer_syscall_pre_impl_getdents64;
  __sanitizer_syscall_pre_impl_getegid;
  __sanitizer_syscall_pre_impl_geteuid;
  __sanitizer_syscall_pre_impl_getgid;
  __sanitizer_syscall_pre_impl_getgroups;
  __sanitizer_syscall_pre_impl_gethostname;
  __sanitizer_syscall_pre_impl_getitimer;
  __sanitizer_syscall_pre_impl_getpeername;
  __sanitizer_syscall_pre_impl_getpgid;
  __sanitizer_syscall_pre_impl_getpgrp;
  __sanitizer_syscall_pre_impl_getpid;
  __sanitizer_syscall_pre_impl_getppid;
  __sanitizer_syscall_pre_impl_getpriority;
  __sanitizer_syscall_pre_impl_getresgid;
  __sanitizer_syscall_pre_impl_getresuid;
  __sanitizer_syscall_pre_impl_getrlimit;
  __sanitizer_syscall_pre_impl_getrusage;
  __sanitizer_syscall_pre_impl_getsid;
  __sanitizer_syscall_pre_impl_getsockname;
  __sanitizer_syscall_pre_impl_getsockopt;
  __sanitizer_syscall_pre_impl_gettid;
  __sanitizer_syscall_pre_impl_gettimeofday;
  __sanitizer_syscall_pre_impl_getuid;
  __sanitizer_syscall_pre_impl_getxattr;
  __sanitizer_syscall_pre_impl_init_module;
  __sanitizer_syscall_pre_impl_inotify_add_watch;
  __sanitizer_syscall_pre_impl_inotify_init;
  __sanitizer_syscall_pre_impl_inotify_init1;
  __sanitizer_syscall_pre_impl_inotify_rm_watch;
  __sanitizer_syscall_pre_impl_io_cancel;
  __sanitizer_syscall_pre_impl_io_destroy;
  __sanitizer_syscall_pre_impl_io_getevents;
  __sanitizer_syscall_pre_impl_io_setup;
  __sanitizer_syscall_pre_impl_io_submit;
  __sanitizer_syscall_pre_impl_ioctl;
  __sanitizer_syscall_pre_impl_ioperm;
  __sanitizer_syscall_pre_impl_ioprio_get;
  __sanitizer_syscall_pre_impl_ioprio_set;
  __sanitizer_syscall_pre_impl_ipc;
  __sanitizer_syscall_pre_impl_kexec_load;
  __sanitizer_syscall_pre_impl_keyctl;
  __sanitizer_syscall_pre_impl_kill;
  __sanitizer_syscall_pre_impl_lchown;
  __sanitizer_syscall_pre_impl_lgetxattr;
  __sanitizer_syscall_pre_impl_link;
  __sanitizer_syscall_pre_impl_linkat;
  __sanitizer_syscall_pre_impl_listen;
  __sanitizer_syscall_pre_impl_listxattr;
  __sanitizer_syscall_pre_impl_llistxattr;
  __sanitizer_syscall_pre_impl_llseek;
  __sanitizer_syscall_pre_impl_lookup_dcookie;
  __sanitizer_syscall_pre_impl_lremovexattr;
  __sanitizer_syscall_pre_impl_lseek;
  __sanitizer_syscall_pre_impl_lsetxattr;
  __sanitizer_syscall_pre_impl_lstat;
  __sanitizer_syscall_pre_impl_lstat64;
  __sanitizer_syscall_pre_impl_madvise;
  __sanitizer_syscall_pre_impl_mbind;
  __sanitizer_syscall_pre_impl_migrate_pages;
  __sanitizer_syscall_pre_impl_mincore;
  __sanitizer_syscall_pre_impl_mkdir;
  __sanitizer_syscall_pre_impl_mkdirat;
  __sanitizer_syscall_pre_impl_mknod;
  __sanitizer_syscall_pre_impl_mknodat;
  __sanitizer_syscall_pre_impl_mlock;
  __sanitizer_syscall_pre_impl_mlockall;
  __sanitizer_syscall_pre_impl_mmap_pgoff;
  __sanitizer_syscall_pre_impl_mount;
  __sanitizer_syscall_pre_impl_move_pages;
  __sanitizer_syscall_pre_impl_mprotect;
  __sanitizer_syscall_pre_impl_mq_getsetattr;
  __sanitizer_syscall_pre_impl_mq_notify;
  __sanitizer_syscall_pre_impl_mq_open;
  __sanitizer_syscall_pre_impl_mq_timedreceive;
  __sanitizer_syscall_pre_impl_mq_timedsend;
  __sanitizer_syscall_pre_impl_mq_unlink;
  __sanitizer_syscall_pre_impl_mremap;
  __sanitizer_syscall_pre_impl_msgctl;
  __sanitizer_syscall_pre_impl_msgget;
  __sanitizer_syscall_pre_impl_msgrcv;
  __sanitizer_syscall_pre_impl_msgsnd;
  __sanitizer_syscall_pre_impl_msync;
  __sanitizer_syscall_pre_impl_munlock;
  __sanitizer_syscall_pre_impl_munlockall;
  __sanitizer_syscall_pre_impl_munmap;
  __sanitizer_syscall_pre_impl_name_to_handle_at;
  __sanitizer_syscall_pre_impl_nanosleep;
  __sanitizer_syscall_pre_impl_newfstat;
  __sanitizer_syscall_pre_impl_newfstatat;
  __sanitizer_syscall_pre_impl_newlstat;
  __sanitizer_syscall_pre_impl_newstat;
  __sanitizer_syscall_pre_impl_newuname;
  __sanitizer_syscall_pre_impl_ni_syscall;
  __sanitizer_syscall_pre_impl_nice;
  __sanitizer_syscall_pre_impl_old_getrlimit;
  __sanitizer_syscall_pre_impl_old_mmap;
  __sanitizer_syscall_pre_impl_old_readdir;
  __sanitizer_syscall_pre_impl_old_select;
  __sanitizer_syscall_pre_impl_oldumount;
  __sanitizer_syscall_pre_impl_olduname;
  __sanitizer_syscall_pre_impl_open;
  __sanitizer_syscall_pre_impl_open_by_handle_at;
  __sanitizer_syscall_pre_impl_openat;
  __sanitizer_syscall_pre_impl_pause;
  __sanitizer_syscall_pre_impl_pciconfig_iobase;
  __sanitizer_syscall_pre_impl_pciconfig_read;
  __sanitizer_syscall_pre_impl_pciconfig_write;
  __sanitizer_syscall_pre_impl_perf_event_open;
  __sanitizer_syscall_pre_impl_personality;
  __sanitizer_syscall_pre_impl_pipe;
  __sanitizer_syscall_pre_impl_pipe2;
  __sanitizer_syscall_pre_impl_pivot_root;
  __sanitizer_syscall_pre_impl_poll;
  __sanitizer_syscall_pre_impl_ppoll;
  __sanitizer_syscall_pre_impl_pread64;
  __sanitizer_syscall_pre_impl_preadv;
  __sanitizer_syscall_pre_impl_prlimit64;
  __sanitizer_syscall_pre_impl_process_vm_readv;
  __sanitizer_syscall_pre_impl_process_vm_writev;
  __sanitizer_syscall_pre_impl_pselect6;
  __sanitizer_syscall_pre_impl_ptrace;
  __sanitizer_syscall_pre_impl_pwrite64;
  __sanitizer_syscall_pre_impl_pwritev;
  __sanitizer_syscall_pre_impl_quotactl;
  __sanitizer_syscall_pre_impl_read;
  __sanitizer_syscall_pre_impl_readlink;
  __sanitizer_syscall_pre_impl_readlinkat;
  __sanitizer_syscall_pre_impl_readv;
  __sanitizer_syscall_pre_impl_reboot;
  __sanitizer_syscall_pre_impl_recv;
  __sanitizer_syscall_pre_impl_recvfrom;
  __sanitizer_syscall_pre_impl_recvmmsg;
  __sanitizer_syscall_pre_impl_recvmsg;
  __sanitizer_syscall_pre_impl_remap_file_pages;
  __sanitizer_syscall_pre_impl_removexattr;
  __sanitizer_syscall_pre_impl_rename;
  __sanitizer_syscall_pre_impl_renameat;
  __sanitizer_syscall_pre_impl_request_key;
  __sanitizer_syscall_pre_impl_restart_syscall;
  __sanitizer_syscall_pre_impl_rmdir;
  __sanitizer_syscall_pre_impl_rt_sigaction;
  __sanitizer_syscall_pre_impl_rt_sigpending;
  __sanitizer_syscall_pre_impl_rt_sigprocmask;
  __sanitizer_syscall_pre_impl_rt_sigqueueinfo;
  __sanitizer_syscall_pre_impl_rt_sigtimedwait;
  __sanitizer_syscall_pre_impl_rt_tgsigqueueinfo;
  __sanitizer_syscall_pre_impl_sched_get_priority_max;
  __sanitizer_syscall_pre_impl_sched_get_priority_min;
  __sanitizer_syscall_pre_impl_sched_getaffinity;
  __sanitizer_syscall_pre_impl_sched_getparam;
  __sanitizer_syscall_pre_impl_sched_getscheduler;
  __sanitizer_syscall_pre_impl_sched_rr_get_interval;
  __sanitizer_syscall_pre_impl_sched_setaffinity;
  __sanitizer_syscall_pre_impl_sched_setparam;
  __sanitizer_syscall_pre_impl_sched_setscheduler;
  __sanitizer_syscall_pre_impl_sched_yield;
  __sanitizer_syscall_pre_impl_select;
  __sanitizer_syscall_pre_impl_semctl;
  __sanitizer_syscall_pre_impl_semget;
  __sanitizer_syscall_pre_impl_semop;
  __sanitizer_syscall_pre_impl_semtimedop;
  __sanitizer_syscall_pre_impl_send;
  __sanitizer_syscall_pre_impl_sendfile;
  __sanitizer_syscall_pre_impl_sendfile64;
  __sanitizer_syscall_pre_impl_sendmmsg;
  __sanitizer_syscall_pre_impl_sendmsg;
  __sanitizer_syscall_pre_impl_sendto;
  __sanitizer_syscall_pre_impl_set_mempolicy;
  __sanitizer_syscall_pre_impl_set_robust_list;
  __sanitizer_syscall_pre_impl_set_tid_address;
  __sanitizer_syscall_pre_impl_setdomainname;
  __sanitizer_syscall_pre_impl_setfsgid;
  __sanitizer_syscall_pre_impl_setfsuid;
  __sanitizer_syscall_pre_impl_setgid;
  __sanitizer_syscall_pre_impl_setgroups;
  __sanitizer_syscall_pre_impl_sethostname;
  __sanitizer_syscall_pre_impl_setitimer;
  __sanitizer_syscall_pre_impl_setns;
  __sanitizer_syscall_pre_impl_setpgid;
  __sanitizer_syscall_pre_impl_setpriority;
  __sanitizer_syscall_pre_impl_setregid;
  __sanitizer_syscall_pre_impl_setresgid;
  __sanitizer_syscall_pre_impl_setresuid;
  __sanitizer_syscall_pre_impl_setreuid;
  __sanitizer_syscall_pre_impl_setrlimit;
  __sanitizer_syscall_pre_impl_setsid;
  __sanitizer_syscall_pre_impl_setsockopt;
  __sanitizer_syscall_pre_impl_settimeofday;
  __sanitizer_syscall_pre_impl_setuid;
  __sanitizer_syscall_pre_impl_setxattr;
  __sanitizer_syscall_pre_impl_sgetmask;
  __sanitizer_syscall_pre_impl_shmat;
  __sanitizer_syscall_pre_impl_shmctl;
  __sanitizer_syscall_pre_impl_shmdt;
  __sanitizer_syscall_pre_impl_shmget;
  __sanitizer_syscall_pre_impl_shutdown;
  __sanitizer_syscall_pre_impl_sigaction;
  __sanitizer_syscall_pre_impl_signal;
  __sanitizer_syscall_pre_impl_signalfd;
  __sanitizer_syscall_pre_impl_signalfd4;
  __sanitizer_syscall_pre_impl_sigpending;
  __sanitizer_syscall_pre_impl_sigprocmask;
  __sanitizer_syscall_pre_impl_socket;
  __sanitizer_syscall_pre_impl_socketcall;
  __sanitizer_syscall_pre_impl_socketpair;
  __sanitizer_syscall_pre_impl_splice;
  __sanitizer_syscall_pre_impl_spu_create;
  __sanitizer_syscall_pre_impl_spu_run;
  __sanitizer_syscall_pre_impl_ssetmask;
  __sanitizer_syscall_pre_impl_stat;
  __sanitizer_syscall_pre_impl_stat64;
  __sanitizer_syscall_pre_impl_statfs;
  __sanitizer_syscall_pre_impl_statfs64;
  __sanitizer_syscall_pre_impl_stime;
  __sanitizer_syscall_pre_impl_swapoff;
  __sanitizer_syscall_pre_impl_swapon;
  __sanitizer_syscall_pre_impl_symlink;
  __sanitizer_syscall_pre_impl_symlinkat;
  __sanitizer_syscall_pre_impl_sync;
  __sanitizer_syscall_pre_impl_syncfs;
  __sanitizer_syscall_pre_impl_sysctl;
  __sanitizer_syscall_pre_impl_sysfs;
  __sanitizer_syscall_pre_impl_sysinfo;
  __sanitizer_syscall_pre_impl_syslog;
  __sanitizer_syscall_pre_impl_tee;
  __sanitizer_syscall_pre_impl_tgkill;
  __sanitizer_syscall_pre_impl_time;
  __sanitizer_syscall_pre_impl_timer_create;
  __sanitizer_syscall_pre_impl_timer_delete;
  __sanitizer_syscall_pre_impl_timer_getoverrun;
  __sanitizer_syscall_pre_impl_timer_gettime;
  __sanitizer_syscall_pre_impl_timer_settime;
  __sanitizer_syscall_pre_impl_timerfd_create;
  __sanitizer_syscall_pre_impl_timerfd_gettime;
  __sanitizer_syscall_pre_impl_timerfd_settime;
  __sanitizer_syscall_pre_impl_times;
  __sanitizer_syscall_pre_impl_tkill;
  __sanitizer_syscall_pre_impl_truncate;
  __sanitizer_syscall_pre_impl_umask;
  __sanitizer_syscall_pre_impl_umount;
  __sanitizer_syscall_pre_impl_uname;
  __sanitizer_syscall_pre_impl_unlink;
  __sanitizer_syscall_pre_impl_unlinkat;
  __sanitizer_syscall_pre_impl_unshare;
  __sanitizer_syscall_pre_impl_uselib;
  __sanitizer_syscall_pre_impl_ustat;
  __sanitizer_syscall_pre_impl_utime;
  __sanitizer_syscall_pre_impl_utimensat;
  __sanitizer_syscall_pre_impl_utimes;
  __sanitizer_syscall_pre_impl_vfork;
  __sanitizer_syscall_pre_impl_vhangup;
  __sanitizer_syscall_pre_impl_vmsplice;
  __sanitizer_syscall_pre_impl_wait4;
  __sanitizer_syscall_pre_impl_waitid;
  __sanitizer_syscall_pre_impl_waitpid;
  __sanitizer_syscall_pre_impl_write;
  __sanitizer_syscall_pre_impl_writev;
  __sanitizer_weak_hook_memcmp;
  __sanitizer_weak_hook_memmem;
  __sanitizer_weak_hook_strcasecmp;
  __sanitizer_weak_hook_strcasestr;
  __sanitizer_weak_hook_strcmp;
  __sanitizer_weak_hook_strncasecmp;
  __sanitizer_weak_hook_strncmp;
  __sanitizer_weak_hook_strstr;
  __snprintf_chk;
  __sprintf_chk;
  __strndup;
  __strxfrm_l;
  __uflow;
  __underflow;
  __vsnprintf_chk;
  __vsprintf_chk;
  __wcsxfrm_l;
  __woverflow;
  __wuflow;
  __wunderflow;
  __xpg_strerror_r;
  __xstat;
  __xstat64;
  _exit;
  _obstack_begin;
  _obstack_begin_1;
  _obstack_newchunk;
  accept;
  accept4;
  asctime;
  asctime_r;
  asprintf;
  backtrace;
  backtrace_symbols;
  calloc;
  canonicalize_file_name;
  capget;
  capset;
  clock_getres;
  clock_gettime;
  clock_settime;
  confstr;
  creat;
  creat64;
  ctermid;
  ctime;
  ctime_r;
  dlclose;
  dlopen;
  drand48_r;
  endgrent;
  endpwent;
  ether_aton;
  ether_aton_r;
  ether_hostton;
  ether_line;
  ether_ntoa;
  ether_ntoa_r;
  ether_ntohost;
  eventfd_read;
  eventfd_write;
  fclose;
  fdopen;
  fflush;
  fgetgrent;
  fgetgrent_r;
  fgetpwent;
  fgetpwent_r;
  fgets;
  fgetxattr;
  flistxattr;
  fmemopen;
  fopen;
  fopen64;
  fopencookie;
  fprintf;
  fputs;
  fread;
  free;
  freopen;
  freopen64;
  frexp;
  frexpf;
  frexpl;
  fscanf;
  fstatfs;
  fstatfs64;
  fstatvfs;
  fstatvfs64;
  ftime;
  fwrite;
  get_current_dir_name;
  getaddrinfo;
  getcwd;
  getdelim;
  getgrent;
  getgrent_r;
  getgrgid;
  getgrgid_r;
  getgrnam;
  getgrnam_r;
  getgroups;
  gethostbyaddr;
  gethostbyaddr_r;
  gethostbyname;
  gethostbyname2;
  gethostbyname2_r;
  gethostbyname_r;
  gethostent;
  gethostent_r;
  getifaddrs;
  getitimer;
  getline;
  getloadavg;
  getmntent;
  getmntent_r;
  getnameinfo;
  getpass;
  getpeername;
  getpwent;
  getpwent_r;
  getpwnam;
  getpwnam_r;
  getpwuid;
  getpwuid_r;
  getresgid;
  getresuid;
  getsockname;
  getsockopt;
  getutent;
  getutid;
  getutline;
  getutxent;
  getutxid;
  getutxline;
  getxattr;
  glob;
  glob64;
  gmtime;
  gmtime_r;
  iconv;
  if_indextoname;
  if_nametoindex;
  inet_aton;
  inet_ntop;
  inet_pton;
  initgroups;
  ioctl;
  lgamma;
  lgamma_r;
  lgammaf;
  lgammaf_r;
  lgammal;
  lgammal_r;
  lgetxattr;
  listxattr;
  llistxattr;
  localtime;
  localtime_r;
  lrand48_r;
  malloc;
  mbsnrtowcs;
  mbsrtowcs;
  mbstowcs;
  mcheck;
  mcheck_pedantic;
  memchr;
  memcmp;
  memmem;
  memmove;
  memrchr;
  memset;
  mincore;
  mktime;
  mlock;
  mlockall;
  mmap;
  mmap64;
  modf;
  modff;
  modfl;
  mprobe;
  mprotect;
  munlock;
  munlockall;
  name_to_handle_at;
  open;
  open64;
  open_by_handle_at;
  open_memstream;
  open_wmemstream;
  opendir;
  pclose;
  poll;
  popen;
  ppoll;
  prctl;
  pread;
  pread64;
  preadv;
  preadv64;
  printf;
  process_vm_readv;
  process_vm_writev;
  pthread_attr_getdetachstate;
  pthread_attr_getguardsize;
  pthread_attr_getinheritsched;
  pthread_attr_getschedparam;
  pthread_attr_getschedpolicy;
  pthread_attr_getscope;
  pthread_attr_getstack;
  pthread_attr_getstacksize;
  pthread_barrierattr_getpshared;
  pthread_condattr_getclock;
  pthread_condattr_getpshared;
  pthread_getname_np;
  pthread_getschedparam;
  pthread_mutex_lock;
  pthread_mutex_unlock;
  pthread_mutexattr_getprioceiling;
  pthread_mutexattr_getprotocol;
  pthread_mutexattr_getpshared;
  pthread_mutexattr_getrobust;
  pthread_mutexattr_getrobust_np;
  pthread_mutexattr_gettype;
  pthread_rwlockattr_getkind_np;
  pthread_rwlockattr_getpshared;
  pthread_setcancelstate;
  pthread_setcanceltype;
  pthread_setname_np;
  pthread_sigmask;
  ptrace;
  puts;
  pututxline;
  pwrite;
  pwrite64;
  pwritev;
  pwritev64;
  rand_r;
  random_r;
  read;
  readdir;
  readdir64;
  readdir64_r;
  readdir_r;
  readlink;
  readlinkat;
  readv;
  recv;
  recvfrom;
  recvmmsg;
  recvmsg;
  regcomp;
  regerror;
  regexec;
  regfree;
  remquo;
  remquof;
  remquol;
  rmdir;
  scandir;
  scandir64;
  scanf;
  sched_getparam;
  sem_destroy;
  sem_getvalue;
  sem_init;
  sem_post;
  sem_timedwait;
  sem_trywait;
  sem_wait;
  send;
  sendmmsg;
  sendmsg;
  sendto;
  setbuf;
  setbuffer;
  setgrent;
  setitimer;
  setlinebuf;
  setlocale;
  setpwent;
  setvbuf;
  shmctl;
  sigaction;
  sigemptyset;
  sigfillset;
  signal;
  sigpending;
  sigprocmask;
  sigtimedwait;
  sigwait;
  sigwaitinfo;
  sincos;
  sincosf;
  sincosl;
  snprintf;
  sprintf;
  sscanf;
  statfs;
  statfs64;
  statvfs;
  statvfs64;
  strcasecmp;
  strcasestr;
  strchr;
  strchrnul;
  strcmp;
  strcpy;
  strcspn;
  strerror;
  strerror_r;
  strlen;
  strncasecmp;
  strncmp;
  strncpy;
  strndup;
  strnlen;
  strpbrk;
  strptime;
  strrchr;
  strspn;
  strstr;
  strtoimax;
  strtok;
  strtoumax;
  strxfrm;
  strxfrm_l;
  sysinfo;
  tcgetattr;
  tempnam;
  textdomain;
  time;
  timerfd_gettime;
  timerfd_settime;
  times;
  tmpnam;
  tmpnam_r;
  tsearch;
  ttyname_r;
  unlink;
  vasprintf;
  vfprintf;
  vfscanf;
  vprintf;
  vscanf;
  vsnprintf;
  vsprintf;
  vsscanf;
  wait;
  wait3;
  wait4;
  waitid;
  waitpid;
  wcrtomb;
  wcscat;
  wcslen;
  wcsncat;
  wcsnlen;
  wcsnrtombs;
  wcsrtombs;
  wcstombs;
  wcsxfrm;
  wcsxfrm_l;
  wordexp;
  write;
  writev;
  xdr_bool;
  xdr_bytes;
  xdr_char;
  xdr_double;
  xdr_enum;
  xdr_float;
  xdr_hyper;
  xdr_int;
  xdr_int16_t;
  xdr_int32_t;
  xdr_int64_t;
  xdr_int8_t;
  xdr_long;
  xdr_longlong_t;
  xdr_quad_t;
  xdr_short;
  xdr_string;
  xdr_u_char;
  xdr_u_hyper;
  xdr_u_int;
  xdr_u_long;
  xdr_u_longlong_t;
  xdr_u_quad_t;
  xdr_u_short;
  xdr_uint16_t;
  xdr_uint32_t;
  xdr_uint64_t;
  xdr_uint8_t;
  xdrmem_create;
  xdrstdio_create;
};
