/*
sport_homework of xiaoben 
timr 2024/2/2
file sport_homework.c
*/
#include "lv_watch.h"
#if USE_LV_WATCH_HEALTH != 0

#include "health.h"
const struct {
    void * img_src;
    watchLangTxtId_t txtId;
    lv_event_cb_t action;
} splitList[] = {
    {ICON_HEALTH_HEART_RATE , WATCH_TEXT_ID_HEART_RATE, heart_rate_create_event_cb },
    {ICON_HEALTH_BLOOD_OXYGEN , WATCH_TEXT_BLOOD_OXYGEN, blood_oxygen_create_event_cb},
    {ICON_HEALTH_PRESSURE , WATCH_TEXT_ID_STRESS, heart_stress_create_event_cb},
};


static void health_list_prepare_destory(lv_obj_t * activity_obj)
{
    lv_watch_png_cache_all_free();
}



lv_obj_t * health_prepare_create(lv_obj_t * activity_obj)
{   
 
    lv_obj_t * obj = lv_watch_list_create(activity_obj);
    LV_ASSERT_MEM(obj);
    if(NULL == obj) {
        return NULL;
    }
	lv_watch_list_set_name_txt(obj, WATCH_TEXT_ID_HEALTH);
		lv_obj_t * btn;
	 for(int8_t i = 0; i < (sizeof(splitList) / sizeof(splitList[0])); i++) 
	  {        	
	  		btn=lv_watch_list_add(obj, (void *)splitList[i].img_src, splitList[i].txtId, splitList[i].action);
	  }
	
   return obj;
}


void health_create_event_cb(lv_obj_t * btn, lv_event_t e)
{
    (void)btn;
    if(LV_EVENT_CLICKED == e)
    {
        lv_watch_png_cache_all_free();
        lv_obj_t * obj =  health_prepare_create(NULL);
        LV_ASSERT_MEM(obj);
        if(obj == NULL) return;
    }
}
#endif

