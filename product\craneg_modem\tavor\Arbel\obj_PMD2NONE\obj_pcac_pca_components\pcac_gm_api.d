\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\pca_components\src\pcac_gm_api.c
\pcac\pca_components\src\pcac_gm_api.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\pca_components\inc\pcac_gm_types.h
\pcac\pca_components\inc\pcac_gm_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\pca_components\inc\pcac_gm_config.h
\pcac\pca_components\inc\pcac_gm_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \tavor\Arbel\inc\gbl_config.h
\tavor\Arbel\inc\gbl_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\pca_components\inc\pcac_gm_api.h
\pcac\pca_components\inc\pcac_gm_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\pca_components\inc\pcac_gm_trace.h
\pcac\pca_components\inc\pcac_gm_trace.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\msl_utils\inc\msl_trace.h
\pcac\msl_utils\inc\msl_trace.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\msl_utils\inc\msl_trmsg.h
\pcac\msl_utils\inc\msl_trmsg.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \pcac\msl_utils\inc\msl_measures.h
\pcac\msl_utils\inc\msl_measures.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \csw\platform\inc\global_types.h
\csw\platform\inc\global_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \hal\core\inc\utils.h
\hal\core\inc\utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components/pcac_gm_api.o : \csw\BSP\inc\asserts.h
\csw\BSP\inc\asserts.h:
