#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#--------------------------------------------------------------------------------------------------------------------
# (C) Copyright 2006 Marvell International Ltd. All Rights Reserved
#-------------------------------------------------------------------------------------------------------------------

#=========================================================================
# File Name      : RF_Driver_SP8.mak
# Description    : Main make file for the aplp/RF_Driver package.
#
# Usage          : make [-s] -f RF_Driver_SP8.mak OPT_FILE=<path>/<opt_file>
#
# Notes          : The options file defines macro values defined
#                  by the environment, target, and groups. It
#                  must be included for proper package building.
#
# Copyright (c) 2001 Intel Corporation. All Rights Reserved
#=========================================================================

# Package build options
include ${OPT_FILE}

# Package Makefile information
GEN_PACK_MAKEFILE = ${BUILD_ROOT}/env/${HOST}/build/package.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = RF_Driver_SP8
PACKAGE_BASE     = aplp
PACKAGE_PATH     = ${BUILD_ROOT}/${PACKAGE_BASE}/${PACKAGE_NAME}

# The path locations of source and include file directories.
PACKAGE_SRC_PATH    = ${PACKAGE_PATH}/src

# for DIGRF3
ifneq (,$(findstring PHSUPDATE,$(VARIANT_LIST)))
PACKAGE_SRC_PATH    = ${PACKAGE_PATH}/src/DigRf3G
endif

PACKAGE_INC_PATHS   = ${PACKAGE_PATH}/src \
                      ${PACKAGE_PATH}/src/include \
                      ${PACKAGE_PATH}/inc 
                      #${WROOT}/3g_ps/rls/tplgsm/sys/gki.mod/api/inc \
                      #${WROOT}/3g_ps/rls/tplgsm/sys/gki.typ/api/cfg \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/pscommon/2g.typ/api/inc  \
                      #${WROOT}/3g_ps/rls/tplgsm/sys/gki.mod/api/shinc \
                      #${WROOT}/3g_ps/rls/tplgsm/sys/gki.typ/api/shinc \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/pscommon/gp.typ/api/inc \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/psnas/2g.mod/api/inc \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/psnas/gp.mod/api/shinc \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/psnas/4g.mod/api/inc \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/psnas/gp.mod/api/inc  \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/psas_w/3g.mod/lib/src  \
                      #${WROOT}/3g_ps/rls/tplgsm/utinc \
                      #${WROOT}/3g_ps/rls/tplgsm/modem/psas_w/3g.mod/api/inc 
                     #${WROOT}/3g_ps/rls/tplgsm/sys/gki.typ/api/inc \	
						

				
# for DIGRF3
PACKAGE_INC_PATHS += ${BUILD_ROOT}/gplc/GenericRfDriver/src/RspDigRf3G/inc  \
                       ${XROOT}/gplc/L1GKI/inc/psinc  \
                       ${XROOT}/gplc/L1GKI/inc/gpinc  \
                       ${XROOT}/gplc/abp/inc                          

# Package source files, paths not required

PACKAGE_SRC_FILES =  	pl_rf_api_dig.c \
					 	pl_rf_dig.c \
                     	pl_rf_plp_dig.c \
		     		 	pl_rf_afe_dig.c \
		     			pl_rf_agc_dig.c \
		     			pl_rf_apc_dig.c \
		     			pl_rf_nvm_dig.c \
             			pl_rf_calibration_process_dig.c \
             			pl_rf_nvm_db_dig.c \
			 			pl_rf_fast_calibration_process_dig.c \
             			pl_rf_dbg_dig.c
          

# These are the tool flags specific to the RF_Driver package only.
# The environment, target, and group also set flags.
PACKAGE_ASMFLAGS =
PACKAGE_CFLAGS   += --diag_error 9,47,68,69,111,117,120,127,144,152,167,174,186,188,192,223,513,550,991,1295,2874
#PACKAGE_DFLAGS   =
PACKAGE_DFLAGS   = -D_B0_AGC_APC_
#PACKAGE_DFLAGS   = -D_ABC_WD_DEBUG_MODE -D_ABC_DEBUG_MODE -D_B0_AGC_APC_
PACKAGE_ARFLAGS  =


PACKAGE_DFLAGS   += -DEX_KERNEL \
                    -DEXCLUDE_D1 \
                    -DEXCLUDE_TEST \
                    -DENABLE_8PSK_TX \
                    -DINTEL_UPGRADE_L1_PRODUCT \
                    -DUPGRADE_GPRS
                    
# The package dependency file
PACKAGE_DEP_FILE = RF_Driver_SP8_dep.mak

# Define Package Variants -------------------------------

# look for the variants in the VARIANT_LIST and override
# setting from the previous section. The variable
# RF_DRIVER_VARIANT_1 is meant to be overwritten
# with actual variant names. More variants can be added
# as required.

# handle the RF_DRIVER_VARIANT_1 variant ------------
ifneq (,$(findstring RF_DRIVER_VARIANT_1,${VARIANT_LIST}))

PACKAGE_VARIANT = RF_DRIVER_VARIANT_1

# Package source files, paths not required
PACKAGE_SRC_FILES = ^SRC_FILE_LIST

                    
                    
# These are the tool flags specific to the RF_Driver package only.
# The environment, target, and group also set flags.
PACKAGE_ASMFLAGS =
PACKAGE_CFLAGS   =
PACKAGE_DFLAGS   =
PACKAGE_ARFLAGS  =

endif

# Include the Standard Package Make File ---------------
include ${GEN_PACK_MAKEFILE}

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include ${PACKAGE_DEP_FILE}









