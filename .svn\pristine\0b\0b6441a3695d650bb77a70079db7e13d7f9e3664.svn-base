/**
 * @file dial_simple_theme.h
 *
 */
#ifndef DIAL_SIMPLE_THEME_H
#define DIAL_SIMPLE_THEME_H

#ifdef __cplusplus
extern "C" {
#endif

/*********************
 *      INCLUDES
 *********************/

#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lvgl.h"
#include "lv_watch_conf.h"
#else
#include "../../../lvgl/lvgl.h"
#include "../../../lv_watch_conf.h"
#endif

#if USE_LV_WATCH_DIAL_SIMPLE_THEME != 0

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 * GLOBAL PROTOTYPES
 **********************/
lv_obj_t * dial_simple_theme_create(lv_obj_t * obj);

/**********************
 *      MACROS
 **********************/

#endif /*USE_LV_WATCH_DIAL_SIMPLE_THEME*/

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /*DIAL_SIMPLE_THEME_H*/
