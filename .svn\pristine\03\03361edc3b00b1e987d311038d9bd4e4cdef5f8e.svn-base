
#ifndef _WS_PTHREAD_H_
#define _WS_PTHREAD_H_


#define WS_PTHREAD_MUTEX_NORMAL      0
#define WS_PTHREAD_MUTEX_ERRORCHECK  1
#define WS_PTHREAD_MUTEX_RECURSIVE   2
#define WS_PTHREAD_MUTEX_DEFAULT     3


#define WS_PTHREAD_MUTEX_INITIALIZER {0, 0}

typedef struct ws_pthread_mutex_t_tag {
   	void *m;
	int   type;
}ws_pthread_mutex_t;

typedef struct ws_pthread_mutexattr_t_tag {
}ws_pthread_mutexattr_t;


extern int ws_pthread_mutex_init(ws_pthread_mutex_t * mutex, const ws_pthread_mutexattr_t * attr);
extern int ws_pthread_mutex_destroy(ws_pthread_mutex_t *mutex);
extern int ws_pthread_mutex_lock(ws_pthread_mutex_t *mutex);
extern int ws_pthread_mutex_trylock(ws_pthread_mutex_t *mutex);
extern int ws_pthread_mutex_unlock(ws_pthread_mutex_t *mutex);

extern int ws_pthread_mutexattr_init(ws_pthread_mutexattr_t *mattr);
extern int ws_pthread_mutexattr_settype(ws_pthread_mutexattr_t *attr, int type);


typedef void * ws_pthread_t;

typedef struct ws_pthread_attr_t_tag {
    int priority;
    int stack_size;
    int autodestroy;
    char name[16];
}ws_pthread_attr_t;


extern int ws_pthread_create(ws_pthread_t *thread, const ws_pthread_attr_t *attr, void *(*start_routine)(void *), void *arg);
extern int ws_pthread_join(ws_pthread_t thread, void **retval);
extern ws_pthread_t ws_pthread_self(void);
extern int ws_pthread_detach(ws_pthread_t thread);
extern void  ws_pthread_exit(void  *retval);
extern void ws_pthread_start(void *(*start_routine)(void *), void *arg);
extern void ws_pthread_start_ext(void *(*start_routine)(void *), void *arg, int priority, int stack_size);

#define pthread_mutex_t                 ws_pthread_mutex_t
#define pthread_mutexattr_t             ws_pthread_mutexattr_t

#define pthread_mutex_init(a,b)         ws_pthread_mutex_init(a,b)
#define pthread_mutex_destroy(a)        ws_pthread_mutex_destroy(a)
#define pthread_mutex_lock(a)           ws_pthread_mutex_lock(a)
#define pthread_mutex_trylock(a)        ws_pthread_mutex_trylock(a)
#define pthread_mutex_unlock(a)         ws_pthread_mutex_unlock(a)
#define pthread_mutexattr_init(a)       ws_pthread_mutexattr_init(a)
#define pthread_mutexattr_settype(a,b)  pthread_mutexattr_settype(a,b)


#define pthread_t ws_pthread_t
#define pthread_attr_t ws_pthread_attr_t

#define pthread_create(a,b,c,d)    ws_pthread_create(a,b,c,d)
#define pthread_join(a,b)          ws_pthread_join(a,b)
#define pthread_self()             ws_pthread_self()
#define pthread_detach(a)          ws_pthread_detach(a)
#define pthread_exit(a)            ws_pthread_exit(a)
#define pthread_start(a,b)         ws_pthread_start(a,b)
#define pthread_start_ext(a,b,c,d)         ws_pthread_start(a,b,c,d)


#endif
