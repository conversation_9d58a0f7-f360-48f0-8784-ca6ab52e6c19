/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/tplgsm/dminc/whichPlatform.h#18 $
 *   $Revision: #18 $
 *   $DateTime: 2006/02/10 12:10:40 $
 **************************************************************************
 * File Description:
 *
 * This file includes the relevant platform specific files according to
 * the compile-switch used at compile-time.
 **************************************************************************/
#ifndef WHICHPLATFORM_H
#define WHICHPLATFORM_H

/***************************************************************************
 * Include files
 ***************************************************************************/

/* NB display defines must be included before platform defines */

  #if defined(ON_PC)
  #elif defined   (LP8773_RUBY_DISPLAY)
    //zzzz #include <di_lp8773_ruby.h>
  #elif defined   (DI_2F50282T00_DISPLAY)
    //zzzz #include <di_2f50292t00.h>
  #elif defined   (PANASONIC_EDTC46QBF_DISPLAY)
    #include <di46qbfdisplay.h>
  #elif defined (HITACHI_HD66421_DISPLAY)
    #include <di66421display.h>
  #elif defined (PHILIPS_PCD8544_DISPLAY)
    #include <di8544display.h>
  #elif defined (PANASONIC_86524_DISPLAY)
    #include <di86524display.h>
  #elif defined (EPSON_DISPLAY)
    #include <dis1d15display.h>
  #elif defined (TFS_6040008201_DISPLAY)
    #include <ditfs6040008201.h>
  #elif !defined(NO_DISPLAY)
    /* No particular display has been defined but build has not explicitly
     * requested "No display" so default to Hitachi one. */
    #define HITACHI_LM041L_DISPLAY
    #include <dilm041ldisplay.h>
  #endif



        



        

  #if defined (TTC_PLATFORM_S9C)
    #include <s9cplatform.h>
  #endif
  #if defined (TTC_PLATFORM_S9A)
    #include <s9aplatform.h>
  #endif
  #if defined (TTC_PLATFORM_S9M)
    #include <s9mplatform.h>
  #endif

  #if defined (TTC_PLATFORM_S3M)
    #include <s3mplatform.h>
  #endif



        

/* WHICHPLATFORM_H */
#endif
