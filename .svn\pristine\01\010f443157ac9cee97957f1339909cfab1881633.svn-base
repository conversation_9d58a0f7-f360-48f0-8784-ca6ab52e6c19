/******************************************************************************
*(C) Copyright 2019-2022 ASR Microelectronics
* All Rights Reserved
******************************************************************************/
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <stdarg.h>

//os related header files
#include "osa.h"
#include "UART.h"
#include "bt_api.h"
#include "bt_api_a2dp.h"
#include "bt_api_avrcp.h"

#if !defined(BT_UI_PRESENT) && defined(BT_SBC_TEST)
extern int bt_a2dp_SBC_test_send_message(unsigned int msg_id);
#endif

void bt_usr_handle_avrcp(struct bt_task_event * msg)
{
    switch(msg->event_id)
    {
        case BTTASK_IND_AVRCP_CONNECTED:
            {
                struct bt_event_avrcp_connect *connect = 
                                (struct bt_event_avrcp_connect *)msg->payload;

                appbt_debug("handle AVRCP connect msg: %02x%02x%02x%02x%02x%02x\r\n", 
                            connect->addr[0], connect->addr[1], connect->addr[2], 
                            connect->addr[3], connect->addr[4], connect->addr[5]);
                break;
            }

        case BTTASK_IND_AVRCP_DISCONNECTED:
            {
                appbt_debug("handle AVRCP disconnect msg\r\n");
                break;
            }

        case BTTASK_IND_AVRCP_KEY_PRESSED:
            {
                unsigned char *id = (unsigned char *)msg->payload;
                appbt_debug("handle AVRCP key pressed %02x\r\n", *id);
                break;
            }

        case BTTASK_IND_AVRCP_KEY_RELEASED:
            {
                unsigned char *id = (unsigned char *)msg->payload;
                appbt_debug("handle AVRCP key released %02x\r\n", *id);
                switch(*id)
                {
                    // user handle key here
                    case AVRCP_KEY_PLAY:
#if !defined(BT_UI_PRESENT) && defined(BT_SBC_TEST)
                        bt_a2dp_SBC_test_send_message(1); // start media data send
#endif
                        break;

                    case AVRCP_KEY_PAUSE:
#if !defined(BT_UI_PRESENT) && defined(BT_SBC_TEST)
                        appbt_a2dp_send_suspend();
#endif
                        break;

                    case AVRCP_KEY_STOP:
#if !defined(BT_UI_PRESENT) && defined(BT_SBC_TEST)
                        bt_a2dp_SBC_test_send_message(0); // stop
#endif
                        break;

                    case AVRCP_KEY_FORWARD:
                        break;

                    case AVRCP_KEY_BACKWARD:
                        break;

                    default:
                        break;
                }
                break;
            }

        default:
            appbt_debug("[USER]handle unknow AVRCP event %d", msg->event_id);
            break;
    }
}

int appbt_avrcp_connect(struct bt_addr addr)
{
    struct bt_task_command * cmd = 
                    alloc_bttask_command(BTTASK_CMD_CONNECT_AVRCP, 6);
    unsigned char *temp = NULL;
    temp = (unsigned char *)cmd->payload;

    memcpy(temp, &addr, 6);
    return send_bttask_command(cmd);
}

int appbt_avrcp_disconnect(void)
{
    struct bt_task_command * cmd = alloc_bttask_command(BTTASK_CMD_DISCONNECT_AVRCP, 0);
    return send_bttask_command(cmd);
}

//for avrcp controller
int appbt_avrcp_play(void)
{
    struct bt_task_command * cmd = alloc_bttask_command(BTTASK_CMD_AVRCP_PLAY, 0);
    return send_bttask_command(cmd);
}

int appbt_avrcp_pause(void)
{
    struct bt_task_command * cmd = alloc_bttask_command(BTTASK_CMD_AVRCP_PAUSE, 0);
    return send_bttask_command(cmd);
}

int appbt_avrcp_stop(void)
{
    struct bt_task_command * cmd = alloc_bttask_command(BTTASK_CMD_AVRCP_STOP, 0);
    return send_bttask_command(cmd);
}

int appbt_avrcp_forward(void)
{
    struct bt_task_command * cmd = alloc_bttask_command(BTTASK_CMD_AVRCP_FORWARD, 0);
    return send_bttask_command(cmd);
}

int appbt_avrcp_backward(void)
{
    struct bt_task_command * cmd = alloc_bttask_command(BTTASK_CMD_AVRCP_BACKWARD, 0);
    return send_bttask_command(cmd);
}