/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/pscommon/3g_ut.mod/api/inc/utftf.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 ***************************************************************************
 * File Description: Header file for Flexible Trace Framework
 **************************************************************************/

#ifndef UT_FTF_H
#define UT_FTF_H

#if defined (DEVELOPMENT_VERSION)
#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK)
/**************************************************************************
 * Nested Include Files
 **************************************************************************/
#include <system.h>
#include <string.h>
#include <stdarg.h>
#include <stdio.h>
#include <utftfta.h>
#include <utftf_sig.h>

/**************************************************************************
 * Manifest Constants
 **************************************************************************/
/* Used to enable/disable all traces */
#define UT_ALL_TRACES 0xFFFFFFFF
#define UT_INTERNAL_SIGNAL_NOT_VALID 0xFFFFFFFF

/**************************************************************************
 * Type Definitions
 **************************************************************************/

/* Data type for data common to all task */
typedef struct UtFtfCommonDataTag
{
    /* triggerOnExternal == TRUE will trigger on external signal number */
    Boolean triggerEnableOnExternal;
    Boolean triggerDisableOnExternal;
    /* External signal number that will trigger enabling of traces */
    SignalId enablingSignalExt;
    /* External signal number that will trigger disabling of traces */
    SignalId disablingSignalExt;
    /* triggerOnInternal == TRUE will trigger on external signal number */
    Boolean triggerEnableOnInternal;
    Boolean triggerDisableOnInternal;
    /* Internal signal number that will trigger enabling of traces */
    Int32 enablingSignalInt;
    /* Internal signal number that will trigger disabling of traces */
    Int32 disablingSignalInt;
    /* signal used for logging */
    SignalBuffer sig;

} UtFtfCommonData;

/* Data type for data per subprocess */
typedef struct UtFtfIndDataTag
{
    /* Active traces for the sub-process */
    Int32 activeTraces;
    /* Pending activation traces for the sub-process. After reception */
    /* of the activation signal, the sub-process will use this trace setting */
    Int32 pendingTracesEnable;
    /* Pending deactivation traces for the sub-process. After reception */
    /* of the deactivation signal, the sub-process will use this trace */
    /* setting */
    Int32 pendingTracesDisable;
    /* User-defined condition data */
    Int32 conditionData;

} UtFtfIndData;

/* Trace Categories */
#define UT_FTF_TRACE_USER1    (1UL << 0)
#define UT_FTF_TRACE_USER2    (1UL << 1)
#define UT_FTF_TRACE_USER3    (1UL << 2)
#define UT_FTF_TRACE_USER4    (1UL << 3)
#define UT_FTF_TRACE_USER5    (1UL << 4)
#define UT_FTF_TRACE_USER6    (1UL << 5)
#define UT_FTF_TRACE_USER7    (1UL << 6)
#define UT_FTF_TRACE_USER8    (1UL << 7)
#define UT_FTF_TRACE_USER9    (1UL << 8)
#define UT_FTF_TRACE_USER10   (1UL << 9)
#define UT_FTF_TRACE_USER11   (1UL << 10)
#define UT_FTF_TRACE_USER12   (1UL << 11)
#define UT_FTF_TRACE_USER13   (1UL << 12)
#define UT_FTF_TRACE_USER14   (1UL << 13)
#define UT_FTF_TRACE_USER15   (1UL << 14)
#define UT_FTF_TRACE_USER16   (1UL << 15)
#define UT_FTF_TRACE_USER17   (1UL << 16)
#define UT_FTF_TRACE_USER18   (1UL << 17)
#define UT_FTF_TRACE_USER19   (1UL << 18)
#define UT_FTF_TRACE_USER20   (1UL << 19)
#define UT_FTF_TRACE_USER21   (1UL << 20)
#define UT_FTF_TRACE_USER22   (1UL << 21)
#define UT_FTF_TRACE_ERROR    (1UL << 22)
#define UT_FTF_TRACE_WARNING  (1UL << 23)
#define UT_FTF_TRACE_PARAM    (1UL << 24)
#define UT_FTF_TRACE_ENTER    (1UL << 25)
#define UT_FTF_TRACE_TX_SIG   (1UL << 26)
#define UT_FTF_TRACE_RX_SIG   (1UL << 27)
#define UT_FTF_TRACE_VAR      (1UL << 28)
#define UT_FTF_TRACE_INFO     (1UL << 29)
#define UT_FTF_TRACE_TIMER    (1UL << 30)
#define UT_FTF_TRACE_STATE    (1UL << 31)
/* note: (1 << 31) is the last possible category */

/* Maximum number of sub-processes per process/executable - 1*/
#if ! defined (UT_FTF_MAX_NUMBER_OF_SUB_PROCESS)
#define UT_FTF_MAX_NUMBER_OF_SUB_PROCESS 10 /* nine sub-processes */
#endif

/* Validate sub-process is not over-indexing array */
#if (UT_FTF_SUBPROCESS >= UT_FTF_MAX_NUMBER_OF_SUB_PROCESS)
#error "UT_FTF_SUBPROCESS >= UT_FTF_MAX_NUMBER_OF_SUB_PROCESS. Please increase UT_FTF_MAX_NUMBER_OF_SUB_PROCESS."
#endif

/* All files that include this header file also needs to define
 * UT_FTF_SUBPROCESS which is the sub-process identity and will act as the
 * index to the array */
#if ! defined (UT_FTF_SUBPROCESS)
#error "UT_FTF_SUBPROCESS not defined. Needs to be defined at the top of every source file."
#endif

/* Helper macros for FILE and LINE information */
#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK_EXTRA)
#define fTFtHISfILE ,__FILE__
#define fTFsIZEoFtHISfILE ,sizeof (__FILE__)
#define fTFlINE ,__LINE__
#else
#define fTFtHISfILE
#define fTFsIZEoFtHISfILE
#define fTFlINE
#endif

/**************************************************************************
 * Macros
 **************************************************************************/

/*******************************************************************************
 *
 * Function     : UT_FTF_DEFINE_TRACE_DATA
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Defines the Flexible Framework Data structure. To be called
 *                once per task. Two static pointers are also set-up to point
 *                to the two areas within the data structure. These pointers
 *                are used by the trace macros.
 *
 ******************************************************************************/
#define UT_FTF_DEFINE_TRACE_DATA(tASK)                                         \
typedef struct UtFtfDataTag##tASK                                              \
{                                                                              \
    /* --- data per process/executable --- */                                  \
    UtFtfCommonData utFtfCommonData;                                           \
                                                                               \
    /* --- data per sub-process --- */                                         \
    UtFtfIndData utFtfIndData [UT_FTF_MAX_NUMBER_OF_SUB_PROCESS];              \
                                                                               \
} UtFtfData##tASK;                                                             \
                                                                               \
/* Trace control data */                                                       \
extern UtFtfData##tASK utFtfData##tASK;                                        \
                                                                               \
/* Pointers to data (used by macros). Note that the pointer to the */          \
/* sub-process data points to the first item in the array always. */           \
static UtFtfCommonData *utFtfCommonData = &utFtfData##tASK.utFtfCommonData;    \
static UtFtfIndData *utFtfIndData = &utFtfData##tASK.utFtfIndData [0];

/*******************************************************************************
 *
 * Function     : UT_FTF_DECLARE_TRACE_DATA
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tASK - name of task
  *
 * Returns      : None
 *
 * Description  : Declares the Flexible Framework Data structure. To be called
 *                once per task.
 *
 ******************************************************************************/
#define UT_FTF_DECLARE_TRACE_DATA(tASK) UtFtfData##tASK utFtfData##tASK;

/*******************************************************************************
 *
 * Function     : UT_FTF_INIT_TRACE
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tASK - name of task
  *
 * Returns      : None
 *
 * Description  : Initialise the Flexible Trace Framework. Should be called once
 *                for each process/executable. Called by URRMTC for URRC.
 *
 ******************************************************************************/
#define UT_FTF_INIT_TRACE(tASK)                                                \
{                                                                              \
    memset((void *)&utFtfData##tASK, 0, sizeof (UtFtfData##tASK));             \
                                                                               \
    /* Mark internal trigger signal invalid. zero cannot be used as it is a */ \
    /* valid internal signal */                                                \
    utFtfCommonData->enablingSignalInt = UT_INTERNAL_SIGNAL_NOT_VALID;         \
    utFtfCommonData->disablingSignalInt = UT_INTERNAL_SIGNAL_NOT_VALID;        \
                                                                               \
    /* allocate FTF trace signal. Note: this is only done once */              \
    KiCreateSignal (SIG_UT_FLEXIBLE_TRACE_OUT,                                 \
                    sizeof (UtFlexibleTraceOut),                               \
                    &utFtfCommonData->sig);                                    \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_HANDLE_FLEXIBLE_TRACE_CONTROL_REQ
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : sIG_P - pointer to UtFlexibleTraceControlReq
  *
 * Returns      : None
 *
 * Description  : Function to handle SIG_RRC_FLEXIBLE_TRACE_CONTROL_REQ.
 *                Called by URRMTC for URRC.
 *
 ******************************************************************************/
#define UT_FTF_HANDLE_FLEXIBLE_TRACE_CONTROL_REQ(sIG_P)                        \
{                                                                              \
    utFtfHandleFlexibleTraceControlReq ((sIG_P),                               \
                                        utFtfCommonData,                       \
                                        utFtfIndData,                          \
                                        UT_FTF_MAX_NUMBER_OF_SUB_PROCESS);     \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_SET_DEFAULT_TRACE_CONTROL
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : subProcess - sub process ID to log
 *                traceCategoryEnable - trace category to use
 *
 * Returns      : None
 *
 * Description  : Macro to enable tasks to setup a default ftf logging
 *                level without the need to use the ftf pass through
 *
 ******************************************************************************/

#define UT_FTF_SET_DEFAULT_TRACE_CONTROL(subProcess,traceCategoryEnable)       \
{                                                                              \
    utFtfSetDefaultTraceControl        ((subProcess),                          \
                                        (traceCategoryEnable),                 \
                                        utFtfCommonData,                       \
                                        utFtfIndData);                         \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_CHECK_TRACE_TRIGGER_SIGNAL_EXT
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : sIGNAL_nUM - Signal number of received external signal
 *
 * Returns      : None
 *
 * Description  : Check if the current signal should trigger enabling or
 *                disabling of trace. Only one sub-process can use this
 *                mechanism at one particular time. Called by URRMTC for URRC.
 *
 ******************************************************************************/
#define UT_FTF_CHECK_TRACE_TRIGGER_SIGNAL_EXT(sIGNAL_nUM)                      \
{                                                                              \
    utFtfCheckTraceTriggerSignalExt ((sIGNAL_nUM),                             \
                                     utFtfCommonData,                          \
                                     utFtfIndData,                             \
                                     UT_FTF_MAX_NUMBER_OF_SUB_PROCESS);        \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_CHECK_TRACE_TRIGGER_SIGNAL_INT
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : sIGNAL_nUM - Signal number of received internal signal
 *
 * Returns      : None
 *
 * Description  : Check if the current signal should trigger enabling or
 *                disabling of trace. Only one sub-process can use this
 *                mechanism at one particular time. Called by URRMTC for URRC.
 *
 ******************************************************************************/
#define UT_FTF_CHECK_TRACE_TRIGGER_SIGNAL_INT(sIGNAL_nUM)                      \
{                                                                              \
    utFtfCheckTraceTriggerSignalInt ((sIGNAL_nUM),                             \
                                     utFtfCommonData,                          \
                                     utFtfIndData,                             \
                                     UT_FTF_MAX_NUMBER_OF_SUB_PROCESS);        \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_ENABLE_TRACE
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_pROCESS - sub-process for which to enable trace category
 *                tRACE_cATEGORY_mAP - Flexible Trace Category Bitmap
 *
 * Returns      : None
 *
 * Description  : Enable trace category. The indicated trace categories are
 *                enabled.
 *
 ******************************************************************************/
#define UT_FTF_ENABLE_TRACE(tRACE_pROCESS, tRACE_cATEGORY_mAP)                 \
{                                                                              \
    utFtfIndData [tRACE_pROCESS].activeTraces = (tRACE_cATEGORY_mAP);          \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_PENDING_ENABLE_TRACE
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_pROCESS - sub-process for which to store pending
 *                                trace category
 *                tRACE_cATEGORY_mAP - Pending Flexible Trace Category Bitmap
 *
 * Returns      : None
 *
 * Description  : Store pending enable trace category. The indicated trace
 *                categories are enabled once the trigger condition is reached.
 *
 ******************************************************************************/
#define UT_FTF_PENDING_ENABLE_TRACE(tRACE_pROCESS, tRACE_cATEGORY_mAP)         \
{                                                                              \
    utFtfIndData [tRACE_pROCESS].pendingTracesEnable = (tRACE_cATEGORY_mAP);   \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_PENDING_DISABLE_TRACE
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_pROCESS - sub-process for which to store pending
 *                                trace category
 *                tRACE_cATEGORY_mAP - Pending Flexible Trace Category Bitmap
 *
 * Returns      : None
 *
 * Description  : Store pending enable trace category. The indicated trace
 *                categories are enabled once the trigger condition is reached.
 *
 ******************************************************************************/
#define UT_FTF_PENDING_DISABLE_TRACE(tRACE_pROCESS, tRACE_cATEGORY_mAP)        \
{                                                                              \
    utFtfIndData [tRACE_pROCESS].pendingTracesDisable = (tRACE_cATEGORY_mAP);  \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_DISABLE_TRACE
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_pROCESS - sub-process for which to disable trace category
 *                tRACE_cATEGORY_mAP - Flexible Trace Category Bitmap
 *
 * Returns      : None
 *
 * Description  : Disable trace. The indicated trace categories are disabled.
 *
 ******************************************************************************/
#define UT_FTF_DISABLE_TRACE(tRACE_pROCESS, tRACE_cATEGORY_mAP)                \
{                                                                              \
    utFtfIndData [tRACE_pROCESS].activeTraces &= (~(tRACE_cATEGORY_mAP));      \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_TRACE_IS_ENABLED
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_cATEGORY - Flexible Trace Category
 *
 * Returns      : None
 *
 * Description  : Macro to check if specified trace category is enabled. Can
 *                be used to wrap larger chunks of code. Example code:
 *
 *                if (UT_FTF_TRACE_IS_ENABLED (USER12))
 *                {
 *                   <... large chunk of code here ...>
 *                   UT_TRACE (USER12, UT_STR( ... ));
 *                }
 *
 ******************************************************************************/
#define UT_FTF_TRACE_IS_ENABLED(tRACE_cATEGORY)                                \
    (utFtfIndData [UT_FTF_SUBPROCESS].activeTraces &                           \
                                             ((UT_FTF_TRACE_##tRACE_cATEGORY)))

/*******************************************************************************
 *
 * Function     : UT_TRACE
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_cATEGORY - Flexible Trace Category as defined at the
 *                                 top of this header file, without the prefix
 *                                 UT_FTF_TRACE_. In case a printout should
 *                                 belong to multiple trace categories it is
 *                                 possible to define a new trace category
 *                                 (called EXTRA_1) like this:
 *
 *     #define UT_FTF_TRACE_EXTRA_1 (UT_FTF_TRACE_USER1 | UT_FTF_TRACE_USER2)
 *
 *                                 This trace category can then be used as
 *                                 any other category:
 *
 *                                 UT_TRACE (EXTRA_1, UT_CSTR("in both"));
 *
 *                pRINT_fUNCTION - mACRO to run. Either UT_STR, UT_CSTR or
 *                                 UT_VSTR.
 *
 * Returns      : None
 *
 * Description  : Produce trace printout for specified trace category if the
 *                category has been enabled. Examples
 *                UT_TRACE (VAR, CSTR("var set"));  for variable trace
 *                UT_TRACE (USER1, CSTR("user category 1"));
 *
 ******************************************************************************/
#define UT_TRACE(tRACE_cATEGORY, pRINT_fUNCTION)                               \
{                                                                              \
    /* Check if the trace category is enabled */                               \
    if (utFtfIndData [UT_FTF_SUBPROCESS].activeTraces &                        \
                                            ((UT_FTF_TRACE_##tRACE_cATEGORY))) \
    {                                                                          \
        /* store internal trace data */                                        \
        utFtfPrepareForPrintout (utFtfCommonData,                              \
                                 UT_FTF_SUBPROCESS,                            \
                                 UT_FTF_TRACE_##tRACE_cATEGORY                 \
                                 fTFtHISfILE                                   \
                                 fTFsIZEoFtHISfILE                             \
                                 fTFlINE                                       \
                                 );                                            \
        /* store the user provided string (and parameters) */                  \
        pRINT_fUNCTION                                                         \
    }                                                                          \
}

/*******************************************************************************
 *
 * Function     : UT_TRACE_COND
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : tRACE_cATEGORY - Flexible Trace Category as defined at the
 *                                 top of this header file, without the prefix
 *                                 UT_FTF_TRACE_. In case a printout should
 *                                 belong to multiple trace categories it is
 *                                 possible to define a new trace category
 *                                 (called EXTRA_1) like this:
 *
 *     #define UT_FTF_TRACE_EXTRA_1 (UT_FTF_TRACE_USER1 | UT_FTF_TRACE_USER2)
 *
 *                                 This trace category can then be used as
 *                                 any other category:
 *
 *                                 UT_TRACE (EXTRA_1, UT_CSTR("in both"));
 *
 *                cONDITION      - user-defined condition that needs to be
 *                                 met for printout to trigger
 *
 *                pRINT_fUNCTION - mACRO to run. Either UT_STR, UT_CSTR or
 *                                 UT_VSTR.
 *
 * Returns      : None
 *
 * Description  : Produce trace printout for specified trace category if the
 *                user defined condition andcategory has been enabled. Examples
 *                UT_TRACE_COND (VAR, (rbId == RB_2), CSTR("var set"));
 *                for variable trace
 *                UT_TRACE_COND (USER1, (i == 0), CSTR("user category 1"));
 *
 ******************************************************************************/
#define UT_TRACE_COND(tRACE_cATEGORY, cONDITION, pRINT_fUNCTION)               \
{                                                                              \
    /* Check if the condition is met and the trace category is enabled */      \
    if ((cONDITION) &&                                                         \
        utFtfIndData [UT_FTF_SUBPROCESS].activeTraces &                        \
                                            ((UT_FTF_TRACE_##tRACE_cATEGORY))) \
    {                                                                          \
        /* store internal trace data */                                        \
        utFtfPrepareForPrintout (utFtfCommonData,                              \
                                 UT_FTF_SUBPROCESS,                            \
                                 UT_FTF_TRACE_##tRACE_cATEGORY                 \
                                 fTFtHISfILE                                   \
                                 fTFsIZEoFtHISfILE                             \
                                 fTFlINE                                       \
                                 );                                            \
        /* store the user provided string (and parameters) */                  \
        pRINT_fUNCTION                                                         \
    }                                                                          \
}

/*******************************************************************************
 *
 * Function     : UT_STR
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1 - arguments in printf format within brackets ()
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate a formatted printout.
 *                vsprintf is used so this macro should not be used when
 *                performance is critical.
 *
 ******************************************************************************/
#define UT_STR(a1)                                                             \
{                                                                              \
    utFtfPrint a1;                                                             \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_DATA
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Helper macro to hide the common task data pointer.
 *                Used in conjunction with the UT_STR macro
 *
 ******************************************************************************/
#define UT_FTF_DATA utFtfCommonData

/*******************************************************************************
 *
 * Function     : UT_CSTR
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1 - const char*
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate an unformatted printout.
 *                vsprintf is not used.
 *
 ******************************************************************************/
#define UT_CSTR(a1)                                                            \
{                                                                              \
    utFtfPrintConst (utFtfCommonData, a1, sizeof (a1));                        \
}

/*******************************************************************************
 *
 * Function     : UT_VSTR1
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1        - const char*
 *                a2        - Int32
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate an unformatted printout with
 *                arguments. The formatting will be done by the pass-through
 *                task. vsprintf is not used.
 *
 ******************************************************************************/
#define UT_VSTR1(a1,a2)                                                        \
{                                                                              \
    utFtfPrintVar (utFtfCommonData, a1, sizeof (a1), 1, a2);                   \
}

/*******************************************************************************
 *
 * Function     : UT_VSTR2
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1        - const char*
 *                a2 - a3   - Int32
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate an unformatted printout with
 *                arguments. The formatting will be done by the pass-through
 *                task. vsprintf is not used.
 *
 ******************************************************************************/
#define UT_VSTR2(a1,a2,a3)                                                     \
{                                                                              \
    utFtfPrintVar (utFtfCommonData, a1, sizeof (a1), 2, a2, a3);               \
}

/*******************************************************************************
 *
 * Function     : UT_VSTR3
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1        - const char*
 *                a2 - a4   - Int32
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate an unformatted printout with
 *                arguments. The formatting will be done by the pass-through
 *                task. vsprintf is not used.
 *
 ******************************************************************************/
#define UT_VSTR3(a1,a2,a3,a4)                                                  \
{                                                                              \
    utFtfPrintVar (utFtfCommonData, a1, sizeof (a1), 3, a2, a3, a4);           \
}

/*******************************************************************************
 *
 * Function     : UT_VSTR4
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1        - const char*
 *                a2 - a5   - Int32
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate an unformatted printout with
 *                arguments. The formatting will be done by the pass-through
 *                task. vsprintf is not used.
 *
 ******************************************************************************/
#define UT_VSTR4(a1,a2,a3,a4,a5)                                               \
{                                                                              \
    utFtfPrintVar (utFtfCommonData, a1, sizeof (a1), 4, a2, a3, a4, a5);       \
}

/*******************************************************************************
 *
 * Function     : UT_VSTR5
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1        - const char*
 *                a2 - a6   - Int32
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate an unformatted printout with
 *                arguments. The formatting will be done by the pass-through
 *                task. vsprintf is not used.
 *
 ******************************************************************************/
#define UT_VSTR5(a1,a2,a3,a4,a5,a6)                                            \
{                                                                              \
    utFtfPrintVar (utFtfCommonData, a1, sizeof (a1), 5, a2, a3, a4, a5, a6);   \
}

/*******************************************************************************
 *
 * Function     : UT_VSTR6
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : a1        - const char*
 *                a2 - a7   - Int32
 *
 * Returns      : None
 *
 * Description  : Helper macro to generate an unformatted printout with
 *                arguments. The formatting will be done by the pass-through
 *                task. vsprintf is not used.
 *
 ******************************************************************************/
#define UT_VSTR6(a1,a2,a3,a4,a5,a6,a7)                                         \
{                                                                              \
    utFtfPrintVar (utFtfCommonData, a1, sizeof (a1), 6, a2, a3, a4, a5, a6, a7);\
}

/*******************************************************************************
 *
 * Function     : UT_FTF_USER_CONDITION
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Helper macro to access the user defined condition variable.
 *                Example code:
 *
 *                UT_TRACE_COND (USER1,
 *                               (UT_FTF_USER_CONDITION & rbId),
 *                               CSTR("Event triggered"));
 *
 ******************************************************************************/
#define UT_FTF_USER_CONDITION (utFtfIndData [UT_FTF_SUBPROCESS].conditionData)

/**************************************************************************
 * Function Prototypes
 **************************************************************************/

void utFtfSetDefaultTraceControl (Int32 subProcess,
                                  Int32 traceCategoryEnable,
                                  UtFtfCommonData* utFtfCommonData_p,
                                  UtFtfIndData* utFtfIndData_p);
 
void utFtfHandleFlexibleTraceControlReq (UtFlexibleTraceControlReq *sig_p,
                                         UtFtfCommonData *utFtfCommonData,
                                         UtFtfIndData *utFtfIndData,
                                         Int8 utFtfMaxNumerOfSubProcesses);

void utFtfCheckTraceTriggerSignalExt (SignalId sigNum,
                                      UtFtfCommonData *utFtfCommonData,
                                      UtFtfIndData *utFtfIndData,
                                      Int8 utFtfMaxNumerOfSubProcesses);

void utFtfCheckTraceTriggerSignalInt (Int32 sigNum,
                                      UtFtfCommonData *utFtfCommonData,
                                      UtFtfIndData *utFtfIndData,
                                      Int8 utFtfMaxNumerOfSubProcesses);

void utFtfPrepareForPrintout (const UtFtfCommonData *utFtfCommonData,
                              const Int32 subProcess,
                              const Int32 traceCategory
#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK_EXTRA)
                              ,const char* file
                              ,const size_t length
                              ,const Int32 line
#endif
                              );

void utFtfPrint (const UtFtfCommonData *utFtfCommonData,
                 const char *format, ...);

void utFtfPrintConst (const UtFtfCommonData *utFtfCommonData,
                      const char *str,
                      const size_t length);

void utFtfPrintVar (const UtFtfCommonData *utFtfCommonData,
                    const char *str,
                    const size_t length,
                    const Int32 n,
                    ...);


#endif /* ENABLE_FLEXIBLE_TRACE_FRAMEWORK */
#endif /* DEVELOPMENT_VERSION */

/* Define empty macros is case the framework is not enabled above */
#if ! defined (UT_FTF_INIT_TRACE)
#define UT_ALL_TRACES
/* Trace control macros */
#define UT_FTF_INIT_TRACE(tASK)
#define UT_FTF_ENABLE_TRACE(tRACE_pROCESS, tRACE_cATEGORY_mAP)
#define UT_FTF_PENDING_TRACE(tRACE_pROCESS, tRACE_cATEGORY_mAP)
#define UT_FTF_DISABLE_TRACE(tRACE_pROCESS, tRACE_cATEGORY_mAP)
#define UT_FTF_CHECK_TRACE_TRIGGER_SIGNAL_EXT(sIGNAL_nUMBER)
#define UT_FTF_CHECK_TRACE_TRIGGER_SIGNAL_INT(sIGNAL_nUMBER)
#define UT_FTF_HANDLE_FLEXIBLE_TRACE_CONTROL_REQ(sIG_P)
#define UT_FTF_SET_DEFAULT_TRACE_CONTROL(subProcess,traceCategoryEnable)
#define UT_FTF_DEFINE_TRACE_DATA(tASK)
#define UT_FTF_DECLARE_TRACE_DATA(tASK)
/* Trace execution macros */
#define UT_TRACE(tRACE_cATEGORY, sTRING)
#define UT_TRACE_COND(tRACE_cATEGORY, cONDITION, pRINT_fUNCTION)
/* Trace helper functions */
#define UT_FTF_TRACE_IS_ENABLED(tRACE_cATEGORY) (0)
#define UT_STR(a1)
#define UT_FTF_DATA
#define UT_CSTR(a1)
#define UT_VSTR1(a1,a2)
#define UT_VSTR2(a1,a2,a3)
#define UT_VSTR3(a1,a2,a3,a4)
#define UT_VSTR4(a1,a2,a3,a4,a5)
#define UT_VSTR5(a1,a2,a3,a4,a5,a6)
#define UT_VSTR6(a1,a2,a3,a4,a5,a6,a7)
#define UT_FTF_USER_CONDITION


#endif

#endif

/* END OF FILE */

