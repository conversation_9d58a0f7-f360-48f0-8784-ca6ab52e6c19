#! armcc -E -I .\

#include "Crane_DS_4M_Ram_4M_Flash_XIP_CIPSRAM_Common.h"

DDR_RO  QSPI_BASE_ADDR  DDR_RO_SIZE
{
    DDR_RO_INIT_CODE   QSPI_BASE_ADDR   4
    {
			Init.o (Init,+First)
    }


    ; LOADTABLE_REFACTOR@xiaokeweng_20190423++
    ;
    ; [1] remove the DDR_DLM_JUMP_TBL_RO region as aplp-dlm nolonger used for CRANE project
    ; [2] enlarge the LOAD_TABLE region for new loadtable .const space
    ;
    LOAD_TABLE  (QSPI_BASE_ADDR+4) FIXED DDR_RO_INIT_CODE_SIZE-4
    {
      loadTable.o  (+CONST)
    }
    ;DDR_DLM_JUMP_TBL_RO  (QSPI_BASE_ADDR+0x200)  FIXED 0x1000
    ;{
    ;	dlm_Arbel.o(+RO)
    ;}
    ;
    ; LOADTABLE_REFACTOR@xiaokeweng_20190423--

    BOARD_CONFIG  (QSPI_BASE_ADDR+DDR_RO_INIT_CODE_SIZE) FIXED 0x300
    {
      bsp_tavor.o  (+CONST)
    }

    PS_CONFIG  (QSPI_BASE_ADDR+0x1500) FIXED 0x100;
    {
      bsp_tavor.o  (PSHandle)
    }

	NVMBUF (QSPI_BASE_ADDR+0x1600) FIXED 0x8000
	{
	  utilities.o (RTIMD)
	}
	
    DDR_SYS_RO_EXEC  +0
    {
        * (+RO)       ; Everything else of code goes here
        diagDB.o (+RO)
        *(.init_array)
    }


    DDR_RO_EXEC  +0  FIXED
    {
        *ims-ims_components.lib         (+RO)
        *aud_sw-Audio.lib				(+RO)
        *nota-nota.lib                  (+RO)
		*volte-volte_components.lib		(+RO)
        netif_wifi_uap.o                (+RO)
        netif_test.o                    (+RO)
        netif_ppp.o                     (+RO)
        netif_pc.o                (+RO)
        net_bridge.o                (+RO)
        etharp.o                (+RO)
        sa.o                (+RO)
        ipsecdev.o                (+RO)
        ipsec_util.o                (+RO)
        ipsec_sha1.o                (+RO)
        ipsec_md5.o                (+RO)
        ipsec_des.o                (+RO)
        ipsec_aes.o                (+RO)
        ipsec.o                (+RO)
        esp.o                (+RO)
        dumpdev.o                (+RO)
        ah.o                (+RO)
        bih46.o                (+RO)
        sip_alg.o                (+RO)
        nf_nat_utils.o                (+RO)
        nf_nat_sip.o                (+RO)
        nf_nat_helper.o                (+RO)
        nf_conntrack_sip.o                (+RO)
        nat_pptp.o                (+RO)
        bsd_mapt.o                (+RO)
        bsd_inet.o                (+RO)
        bsd_checksum.o                (+RO)
        nat_gre.o                (+RO)
        udpecho.o                (+RO)
        tcpecho.o                (+RO)
        mfg_tool.o                (+RO)
        lwip_temp.o                (+RO)
        dns_relay.o                (+RO)
        dhcpd.o                (+RO)
        dhcp6d.o                (+RO)
        netifapi.o                (+RO)
        lwip_sttest.o                (+RO)
        lwip_atctl.o                (+RO)

        lwip_api.o                (+RO)
        lwip_stats.o                (+RO)
        stats.o                (+RO)
        redirect.o                (+RO)
        dhcp.o                (+RO)
        mld6.o                (+RO)
        ethip6.o                (+RO)
        dhcp6.o                (+RO)
        ip_filter.o                (+RO)
        igmp.o                (+RO)
        ip_nat.o                (+RO)
    }

	DDR_NONCACHE_USB    (DDR_NONCACHE_USB_BASE_ADDR) DDR_NONCACHE_USB_SIZE
	{
		mvUsbDevCh9.o	(+ZI)
		mvUsbDevMain.o	(+ZI)
		mvUsbDevRecv.o	(+ZI)
		mvUsbDevSend.o	(+ZI)
		mvUsbDevUtl.o	(+ZI)
		mvUsbHsDevCncl.o	(+ZI)
		mvUsbHsDevMain.o	(+ZI)
		mvUsbHsDevUtl.o	(+ZI)
		udc_driver.o	(+ZI)
		usb1_device.o	(+ZI)
		usb2_device.o	(+ZI)
		usb_device.o	(+ZI)
		mvUsbMemory.o       (UsbPool)
		mvUsbMemory.o       (UsbNetRxBuf)
		mvUsbModem.o        (UsbModem)
		mvUsbLog.o          (UsbLog)
		usb_descriptor.o    (UsbDesc)
		rndis.o             (+ZI,+RW)
		usbMgr.o        (+ZI)
		hsi.o			(+ZI)
		mvUsbStorage.o   (+ZI,+RW)
		diag_comm_EXTif_OSA_NUCLEUS.o  (diag_noncache)
		bt_uart.o                (UARTTx)
		bt_uart.o                (UARTRx)
		gps_uart_drv.o           (GPSUARTTx)
		gps_uart_drv.o           (GPSUARTRx)
		gps_uart.o           (UARTTx)
	        gps_uart.o           (UARTRx)
		UART.o                (UARTTxRx)
		spinand.o           (SPINAND)
		spinand.o           (DMARdBuf)
	}



    DDR_DTCM DTCM_BASE_ADDR   DTCM_SIZE
    {
		bsp_hisr.o       (+ZI)
		cinit2.o         (+ZI)
		timer.o          (+CONST,+ZI,+RW)
		intc_xirq.o           (+CONST,+ZI,+RW)
		inc.o            (+CONST,+ZI,+RW)
		EE_DSP_DATA.o    (+ZI)
		;;;;i2c_tcm.o        (+CONST,+ZI,+RW)
		timer_memRetain.o  (+CONST,+ZI,+RW)
		;osa.o            (+CONST,+ZI,+RW)
                osa_common_run.o (+CONST,+ZI,+RW)                ;;      This is for OSA version 4.
                osa_nu_run.o     (+CONST,+ZI,+RW)                ;;      This is for OSA version 4.
                osa_mem.o        (+CONST,+ZI,+RW)                ;;      This is for OSA version 4.

		l1frint.o        (+CONST,+ZI,+RW)
		plkmpelm.o       (+CONST,+ZI,+RW)
		dlcoreirq.o      (+CONST,+ZI,+RW)
		*(dtcm_ps_data)
		commpm.o           (LPT_STACK)
		commpm.o           (LPM_DEBUG_TCM)
		;;; acipc_memRetain.o			(+CONST,+ZI,+RW)	;; for PM D2 exit
		commpm_memRetain.o			(+CONST,+ZI,+RW)	;; for PM D2 exit
		;;; timer_memRetain.o			(+CONST,+ZI,+RW)	;; for PM D2 exit
		commpm_asm_utils.o			(+CONST,+ZI,+RW)	;; for PM D2 exit
		loadTable.o  (+RW)
		modemlwip.o  (+CONST,+ZI,+RW)
		osa_tx_run.o        (+CONST,+ZI,+RW)
		utilities.o	 (+CONST,+RW)
		utilities.o	 (RTI_DATA)
		tx_timer_info_get.o (+CONST,+ZI,+RW)
		tx_initialize_high_level.o (+CONST,+ZI,+RW)
		tx_timer_initialize.o	(+CONST,+ZI,+RW)
		tx_timer_thread_entry.o	(+CONST,+ZI,+RW)
		tx_thread_initialize.o	(+CONST,+ZI,+RW)
		qspi_nor.o            	(SPI_NOR_CHIP)
		EEHandler_fatal.o   	(EE_SAVEDREG)

    }
    DDR_DTCM_ENDMARKER  (DTCM_BASE_ADDR + DTCM_SIZE - 0x4)   EMPTY  0x4  {}

    DDR_DSP_RO  MSA_BASE_ADDR  MSA_SIZE
    {

    }
    DDR_DSP_RO_ENDMARK (MSA_BASE_ADDR + MSA_SIZE - 0x4) EMPTY  0x4 {}

    ;DDR_RELIABLE_RO_AREA  (DDR_RELIABLE_RO_AREA_BASE_ADDR)  EMPTY  (DDR_RELIABLE_RO_AREA_SIZE - 0x4)
    ;{

    ;}
    ;DDR_RELIABLE_RO_AREA_ENDMARK (DDR_RELIABLE_RO_AREA_BASE_ADDR + DDR_RELIABLE_RO_AREA_SIZE - 0x4) EMPTY  0x4 {}

    DDR_HEAP_GUARD   (DDR_HEAP_BASE_ADDR) EMPTY (DDR_HEAP_SIZE - 0x4)
    {
    }
    DDR_HEAP_ENDMARK        (DDR_HEAP_BASE_ADDR + DDR_HEAP_SIZE - 0x4) EMPTY  0x4 {}

    DDR_NONCACHE_REGION_ACIPC_PS_UL  (DDR_NONCACHE_PS_UL_BASE_ADDR)  EMPTY  (DDR_NONCACHE_PS_UL_SIZE - 0x4)
    {

    }
    DDR_NONCACHE_REGION_ACIPC_PS_UL_ENDMARKER	 (DDR_NONCACHE_PS_UL_BASE_ADDR + DDR_NONCACHE_PS_UL_SIZE - 0x4)   EMPTY  0x4  {}

   	DDR_NONCACHE_REGION_ACIPC_PS_DL  (DDR_NONCACHE_PS_DL_BASE_ADDR)  EMPTY  (DDR_NONCACHE_PS_DL_SIZE - 0x4)
   	{

   	}
   	DDR_NONCACHE_REGION_ACIPC_PS_DL_ENDMARKER		(DDR_NONCACHE_PS_DL_BASE_ADDR + DDR_NONCACHE_PS_DL_SIZE - 0x4)   EMPTY  0x4  {}

	D2_VECT (D2_VECT_BASE_ADDR) (D2_VECT_SIZE)
	{
		vectors.o (D2_Vect,+First)
	}

;;    ;;64k BYTES
;;    DTC_DATA (DDR_RW_BASE_ADDR + 0x3B0300)   0x10000
;;    {
;;    	udtcif.o (+ZI)
;;    	;;;;;;udtcifdb.o (+ZI)
;;    	umaemuxheaders.o (+ZI)
;;    	urlcipherkeys.o (+ZI)
;;			umacipherkeys.o (+ZI)
;;    }

    ;;24k bytes
    DDR_NONCACHE_REGION    (DDR_NONCACHE_BASE_ADDR)		(DDR_NONCACHE_SIZE - 0x4)
    {
		mipsRamBuffer.o*  (.bss)
		EEIRAMBuffer.o*   (.bss)
		EEIRAMBuffer.o*   (.data)
    }
    DDR_NONCACHE_REGION_ENDMARKER  (DDR_NONCACHE_BASE_ADDR + DDR_NONCACHE_SIZE - 0x4)   EMPTY  0x4  {}

;    DDR_NONCACHE_REGION_AC_SHARE  (DDR_NONCACHE_AC_SHARE_BASE_ADDR)   EMPTY  (DDR_NONCACHE_AC_SHARE_SIZE - 4)
;   {
;
;  }
;   DDR_NONCACHE_REGION_AC_SHARE_ENDMARKER  (DDR_NONCACHE_AC_SHARE_BASE_ADDR + DDR_NONCACHE_AC_SHARE_SIZE - 0x4)   EMPTY  0x4  {}

    ;;reserved on Twochip platform 4k bytes
;    DDR_NONCACHE_REGION_AUDIO (DDR_NONCACHE_AUDIO_BASE_ADDR) DDR_NONCACHE_AUDIO_SIZE
;    {
;			vpath_data_noncache.o   (+ZI);
;    }

    ;;Ram log 16k bytes remove memlog for Crane DataModule
    ;DDR_NONCACHE_REGION_MEMLOG (DDR_NONCACHE_MEMLOG_BASE_ADDR) EMPTY DDR_NONCACHE_MEMLOG_SIZE
    ;{

    ;}

    DDR_NONCACHE_REGION_PTABLE (DDR_NONCACHE_PTABLE_BASE_ADDR) EMPTY (DDR_NONCACHE_PTABLE_SIZE -0x04)
    {

    }
    DDR_NONCACHE_REGION_PTABLE_ENDMARK (DDR_NONCACHE_PTABLE_BASE_ADDR + DDR_NONCACHE_PTABLE_SIZE -0x04) EMPTY 0x4 {}

   DDR_NONCACHE_REGION_PROP (DDR_NONCACHE_PROP_BASE_ADDR) EMPTY (DDR_NONCACHE_PROP_SIZE -0x04)
   {
   }
   DDR_NONCACHE_REGION_PROP_ENDMARK (DDR_NONCACHE_PROP_BASE_ADDR + DDR_NONCACHE_PROP_SIZE -0x04) EMPTY 0x4 {}
   

       ;;4k bytes
    DDR_PRIVATE_RW_AREA   (DDR_PRIVATE_RW_AREA_BASE_ADDR)     (DDR_PRIVATE_RW_AREA_SIZE - 0x4)
    {
	    simPin.o           (+ZI,+RW)
	    ;add for silent reset
            EE_silentReset.o   (+ZI,+RW)
	    mep_data.o         (+ZI,+RW)
    }
    DDR_PRIVATE_RW_AREA_ENDMARK (DDR_PRIVATE_RW_AREA_BASE_ADDR + DDR_PRIVATE_RW_AREA_SIZE - 0x4) EMPTY  0x4 {}


    ; xiaoke remove INIT_DATA region
    ;INIT_DATA (INIT_DATA_BASE_ADDR) (INIT_DATA_SIZE - 0x4)
    ;{
    ;    	 init_reserved_data.o	                 (+CONST,+RW,+ZI);
    ;}
    ;INIT_DATA_ENDMARKER  (INIT_DATA_BASE_ADDR + INIT_DATA_SIZE - 0x4)   EMPTY  0x4  {}

;		MSA_IR_REGION (0x07900000)  0x200000
;    {
;    }

;		MSA_Reserved1_REGION (0x07B00000) 0x100000
;    {

;    }


;    DDR_RFBIN_RO RFBIN_BASE_ADDR EMPTY OVERLAY 0x40000
;    {
;    }

;;    DSP_SHM 0xd1e5c080 ; this is the image address for Wujing
    DSP_SHM 0xD1005000
    {
      l1dspshm.o  (+RW, +ZI)
    }

    DSP_BUF +0
    {
      l1dspbuf.o  (+RW, +ZI)
    }

    USB_POOL SQU_USB_POOL_ADDR SQU_USB_POOL_SIZE
    {
    	usb2_device.o (USB_POOL)
    }

	USB_POOL_DQH SQU_USB_DQH_ADDR SQU_USB_DQH_SIZE
    {
    	mvUsbHsDevMain.o (USB_POOL_DQH)
    }

	LTEBMBLOCK_BUF	SQU_LTEBMBLOCK_BUF_ADDR SQU_LTEBMBLOCK_BUF_SIZE
	{
		emcumsasm.o (SQU_BMBlockHeader)
	}

    ;MMI_AREA_ENDMARK (0x7F000000 - 0x1000 - 0x4) EMPTY  0x4 {}
    ;DEBUG_AREA (0x7F000000 - 0x2000 ) EMPTY  0x1000 {}
    ;BOOT_SP_AREA (0x7F000000 - 0x1000 ) EMPTY  0x1000 {}

;    IMAGE_END  +0  EMPTY  0x4 {}
     PSRAM_END  (PSRAM_END_MARK - 0x4) EMPTY 0x04 {}

;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
;;
;; append RW regions in the tail for better external compress
;; for more detail refer to /csw/BSP/src/loadTable.c with comment
;; xiaoke@20190303
;;
;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

	;; MARK= "RW_CPZ_1" , NAME= "DDR_RW_\0",
	DDR_RW_DATA  (DDR_RW_DATA_BASE_ADDR) DDR_RW_DATA_SIZE
	{
		* (+RW)	 	; Everything else of code goes here
		* (+ZI)	 	; Everything else goes here
	}

	;; MARK= "RW_CPZ_2" , NAME= "MMI_RW_\0",
;	MMI_DATA +0
;	{
;		*libmgapollo.a(+RW,+ZI)
;		*libmgmgeff.a(+RW,+ZI)
;		*libmgminigui.a(+RW,+ZI)
;		*libmgngux.a(+RW,+ZI)
;		*libtarget.a(+RW,+ZI)
;		*libthirdparty.a(+RW,+ZI)
;		*LIBHAL.a (+RW,+ZI)
;		*hal_init.o(+RW,+ZI)
;	}

	;; MARK= "RW_CPZ_3" , NAME= "PS_NONC\0",
	PS_NONCACHE_DATA (PS_NONCACHE_DATA_BASE_ADDR) (PS_NONCACHE_DATA_SIZE - 0x4)
	{
		ltebm.o (ltebm_noncache)
		umaphydl.o (umts_dpch_data_noncache)
		umaul.o (umts_dpch_data_noncache)
		emcumsasm.o (+ZI,+RW)
		*(wb_dtc_squ_data)
		*(nezha_lte_dtc_squ_data)
	}
	PS_NONCACHE_DATA_ENDMARK     (PS_NONCACHE_DATA_BASE_ADDR + PS_NONCACHE_DATA_SIZE - 0x4) EMPTY  0x4 {}

	;; MARK= "RW_CPZ_4" , NAME= "DDR_ITCM\0",
    DDR_ITCM 			0   0x10000
    {
		vectors.o (Vect,+First)
		timer.o          (HW_TIMER)
		intc_xirq.o	     (+CODE)		;; for PM D2 exit (P/PV) and HSDPA
		irq.o            (+CODE)
		utils_memRetain.o            (COMMPMWFI)
		timer_memRetain.o  (+CODE)
		tmt.o            (+CODE)
		dmc.o            (+CODE)
		tct.o            (+CODE)
		tcc.o            (+CODE)

		;qspi_core.o            (+CODE)
		qspi_host.o            (+CODE)
		qspi_nor.o            (+CODE)

		tx_timer_info_get.o  (REMAIN_TICKS) 		;PLAT TEST: add for timer
		txe_timer_info_get.o  (+CODE)
		tx_timer_system_activate.o  	(+CODE) 	;PLAT TEST: add for timer
		tx_timer_interrupt.o  			(+CODE)		;PLAT TEST: add for timer interrupt
		tx_timer_thread_entry.o	 		(+CODE)		;PLAT TEST: add for timer thread
		tx_thread_timeout.o(+CODE)

		txe_block_pool_create.o			(+CODE)
		tx_block_allocate.o	  			(+CODE)		;PLAT TEST: add for block pool
		tx_block_release.o 				(+CODE)
		tx_block_pool_cleanup.o 		(+CODE)

		txe_queue_create.o				(+CODE)
		tx_queue_receive.o				(+CODE)
		tx_queue_send.o					(+CODE)
		tx_queue_cleanup.o				(+CODE)

		txe_thread_create.o 	 		(+CODE)
		tx_queue_front_send.o			(+CODE)
		tx_thread_context_restore.o  	(+CODE)		;PLAT TEST: add for thread schedule
		tx_thread_context_save.o 		(+CODE)		;PLAT TEST: add for thread schedule
		tx_thread_schedule.o 	 		(+CODE)		;PLAT TEST: add for thread schedule
		tx_thread_shell_entry.o	 		(+CODE)	 	;PLAT TEST: add for thread schedule
		tx_thread_system_return.o 		(+CODE)	 	;PLAT TEST: add for thread schedule
		tx_thread_system_suspend.o		(+CODE)
		tx_thread_sleep.o				(+CODE)

		txe_semaphore_create.o			(+CODE)
		tx_semaphore_put.o				(+CODE)		;PLAT TEST: add for semaphore put
		tx_semaphore_get.o				(+CODE)		;PLAT TEST: add for semaphore get
		tx_semaphore_cleanup.o			(+CODE)

		tx_hisr.o						(+CODE)		;PPLAT TEST: add for HISR

	tx_thread_interrupt_control.o(+CODE)
	tx_thread_identify.o(+CODE)
	tx_thread_system_preempt_check.o(+CODE)
 	tx_thread_system_resume.o(+CODE)
 	tx_time_get.o(+CODE)
 	tx_time_set.o(+CODE)
 	tx_event_flags_get.o(+CODE)
 	tx_thread_resume.o(+CODE)
		txe_event_flags_create.o		(+CODE)
		tx_event_flags_set.o	(+CODE)
		tx_event_flags_cleanup.o	(+CODE)
		txe_timer_create.o				(+CODE)
 	tx_timer_deactivate.o(+CODE)
 	tx_timer_system_deactivate.o(+CODE)
	tx_timer_activate.o(+CODE)
		tx_timer_change.o(+CODE)
		tx_timer_expiration_process.o(+CODE)

         osa_tx_run.o(+CODE)
                osa_common_run.o (+CODE)                ;;      This is for OSA version 4.
		osa_mem.o        (OSA_MEM)                ;;      This is for OSA version 4.
		kiosq.o            (+CODE)
		kiosmem.o          (+CODE)
		kiossig.o          (+CODE)
		kiostim.o          (+CODE)
		kioslow.o          (+CODE)
		kiostask.o         (+CODE)
		;kmdynmem.o         (+CODE)
		L1ccipher.o        (+CODE)
		l1frint.o        (+CODE)
		;acipc_memRetain.o			(+CODE)	;; for PM D2 exit
		;commpm_memRetain.o			(+CODE)	;; for PM D2 exit
		;timer_memRetain.o			(+CODE)	;; for PM D2 exit
		commpm_asm_utils.o			(+CODE)	;; for PM D2 exit
		commpm.o               (ISSUE_WFI)
		asm_arm946e_s.o        (+CODE)
      		crane_ds_mpu.o         (+CODE) ; mpu config
		mpu.o                  (+CODE) ; mpu config
		FreqChange.o		   (PSRAM_FC)
		ps_itcm.o              (+CODE)
		WS_CmdMsg.o            (+CODE)
		osa_umc.o               (+CODE)
		usb_modem.o            (+CODE)
		ipnet.o                 (+CODE)
		ipnet_usb.o             (+CODE)

		tcpip.o			(tcpip)
		ip4.o			(lwipip4)
		;etharp.o		(etharp)
		net_bridge.o	        (netbridge)
		netif_td.o		(netiftd)
		modemlwip.o		(MODEMLWIP_MEM)
		utilities.o	        (RTI_SWITCH)
		bsp_tavor.o             (VERSION)
		exceptionHandlers.o     (+CODE)
		EEHandler_fatal.o  	    (ABORT_XIP)
		utilities.o	            (CURRENT_TICK)
		EEHandler_fatal.o	    (SYS_IS_ASSERT)
		qspi_core.o             (QSPI_INIT)
        
        ;;if itcm not enough,this code can delete,roy add
        tcpip.o                   (+CODE) 
    }

	


	;; MARK= "RW_CPZ_5" , NAME= "PS_CODE_IN_PSRAM\0",
	PS_CODE_IN_PSRAM    (PS_CODE_IN_PSRAM_BASE)		(PS_CODE_IN_PSRAM_SIZE - 0x4)
	{
		ltenetworkcard.o       (+CODE)
		ltebm.o            (+CODE)
		emacut.o            (+CODE)
		emacra.o            (+CODE)
		emaccf.o            (+CODE)
		emacul.o            (+CODE)
		emacdl.o            (+CODE)
		emcumsasm.o            (+CODE)
		emacsig.o            (+CODE)
		epdcp.o            (+CODE)
		epdcpcipherandintegrity.o            (+CODE)
		epdcplinklist.o            (+CODE)
		epdcprlcsap.o            (+CODE)
		epdcprrcsap.o            (+CODE)
		epdcpupsap.o            (+CODE)
		aes.o            (+CODE)
		snow_3g.o            (+CODE)
		lterlccommon.o            (+CODE)
		lterlc.o            (+CODE)
		lterlcam.o            (+CODE)
		lterlcum.o            (+CODE)
		lterabmgmm.o            (+CODE)
		lterabmmain.o            (+CODE)
		lterabmpdp.o            (+CODE)
		lterabmrrc.o            (+CODE)
		lterabmsm.o            (+CODE)
		lterabmtimers.o            (+CODE)
		lterabmutil.o            (+CODE)
		lterabmisc.o            (+CODE)
		ipnetbuf.o            (+CODE)
		mvUsbDevRecv.o         (+CODE)
		mvUsbDevSend.o         (+CODE)
		mvUsbHsDevMain.o         (+CODE)
		mvUsbNet.o         (+CODE)
		usb2_device.o           (+CODE)
		diag_comm_EXTif_OSA_NUCLEUS.o (+CODE)
		diag_comm_EXTif.o        (+CODE)
		diag_port.o     (+CODE)
		diag_comm_L2.o     (+CODE)
		dirent.o        			(+CODE)
		fat.o        					(+CODE)
		fat_os_api.o        	(+CODE)
		fatio.o        			(+CODE)
		find.o        				(+CODE)
		sdsys.o        				(+CODE)
		sector.o        			(+CODE)

		nvram_local.o						(+CODE)
		nvram_api.o							(+CODE)
		nvram_interface.o				(+CODE)
		ip6.o                   (+CODE)
		ip4.o                   (+CODE)
		ip_nat.o                   (+CODE)
		mem.o                   (+CODE)
		memp.o                   (+CODE)
		pbuf.o                   (+CODE)
		netif.o                   (+CODE)
		;tcpip.o                   (+CODE)
		etharp.o                   (+CODE)
		net_bridge.o                   (+CODE)
		netif_pc.o                   (+CODE)
		netif_td.o                   (+CODE)

		plkmsort.o									(+CODE)
		L1RspScenarios.o									(+CODE)
		DigRf3GRspSequence.o									(+CODE)
		plkmpelm.o									(+CODE)
		l1frloop_INT.o									(+CODE)
		l1frgpwr.o									(+CODE)
		l1frpnclWB_INT.o            (+CODE)
		l1frwbncl_INT.o             (+CODE)
		 l1frpncl_INT.o		(+CODE)
		 l1frprch.o		(+CODE)
		 l1frptst.o		(+CODE)
		 l1frpttu.o		(+CODE)
		 l1frptev.o		(+CODE)
		 l1frptds_INT.o		(+CODE)
		 l1frptsh_INT.o		(+CODE)
		 l1frptrc.o		(+CODE)
		 l1frpreserv_INT.o		(+CODE)
		 l1frpt.o		(+CODE)
		 l1frptrb.o		(+CODE)
		 l1frptit.o		(+CODE)
		 l1frptmc_INT.o		(+CODE)
		 l1frptpa_INT.o		(+CODE)
		 l1fridle.o		(+CODE)
		 l1cell.o		(+CODE)
		 l1sqnb.o		(+CODE)
		 l1sqrx.o		(+CODE)
		 pssqcfgpwr.o		(+CODE)
		 pssqsfsseq.o		(+CODE)
		 dlslow.o		(+CODE)
		 l1frafc.o		(+CODE)
		 l1frtcb.o		(+CODE)
		 l1frtds.o		(+CODE)
		 plkmirq.o		(+CODE)
		 l1sqmon.o		(+CODE)
		 pssqsfstx.o		(+CODE)
		 l1slow.o				(+CODE)
		 l1frltencl.o			(+CODE)
		 Sqcfgcal.o			(+CODE)
		 dlcoreirq.o			(+CODE)
		 l1fragc.o			(+CODE)
		 l1frgims.o			(+CODE)
		 l1frgitm.o			(+CODE)
		 l1frrept.o			(+CODE)
		 l1frseq.o			(+CODE)
		 l1frsig.o			(+CODE)
		 l1frldt.o			(+CODE)
		 l1pwrctl.o			(+CODE)
		 l1pwrptm.o			(+CODE)
		 l1sqafc.o			(+CODE)
		 l1sqagc.o			(+CODE)
		 l1sqfb.o			(+CODE)
		 l1sqimon.o			(+CODE)
		 l1sqsb.o			(+CODE)
		 l1kisig.o			(+CODE)
		 plkmbufm.o			(+CODE)
		 l1sqlte.o			(+CODE)
		 l1kimem.o			(+CODE)
		 DigRf3GRspEngine.o			(+CODE)
		 GenericRfDriver.o			(+CODE)
		 Sqsfsrad.o			(+CODE)
		 Sqsfsrx.o			(+CODE)
		 Sqsfstx.o			(+CODE)
		 RspRegChainEngine.o			(+CODE)
		 L1agccfg.o			(+CODE)
		 L1sqrxtc.o			(+CODE)
		;
		; request by Zhengrui for L+G mode (SNDCP/LLC/RD/MAC)
		;
		emacbgctl.o		(+CODE)
		emacl1rx.o		(+CODE)
		emacl1seq.o		(+CODE)
		emacl1tx.o		(+CODE)
		emacl1utl.o		(+CODE)
		emacmain.o		(+CODE)
		emacutil.o		(+CODE)
		macstats.o		(+CODE)
		rdasspdu.o		(+CODE)
		rdbg.o			(+CODE)
		rdmain.o		(+CODE)
		rdsegpdu.o		(+CODE)
		rdutil.o		(+CODE)
		llc.o			(+CODE)
		llme.o			(+CODE)
		lle_xid.o		(+CODE)
		lle_s1.o		(+CODE)
		lle_s2.o		(+CODE)
		lle_s3.o		(+CODE)
		lle_s4.o		(+CODE)
		lle_s5.o		(+CODE)
		lle_s6.o		(+CODE)
		llfcs.o			(+CODE)
		lle_i.o			(+CODE)
		lle_ui.o		(+CODE)
		llheader.o		(+CODE)
		llm_sig.o		(+CODE)
		llc_ref.o		(+CODE)
		llc_gki.o		(+CODE)
		llmux.o			(+CODE)
		sncompr.o		(+CODE)
		snllc.o			(+CODE)
		snmem.o			(+CODE)
		snpdp.o			(+CODE)
		snseg.o			(+CODE)
		snsignal.o		(+CODE)
		snsm.o			(+CODE)
		sntimers.o		(+CODE)
		snutil.o		(+CODE)
		snv42bis.o		(+CODE)
		snxid.o			(+CODE)
		ulbgrabmdtc.o		(+CODE)
		dlbgrabmdtc.o		(+CODE)
		xrabmmain.o		(+CODE)



		usbnet.o		(+CODE)
		timer.o          (+CODE)
		osa_mem.o          (+CODE)
		csw_mem.o          (+CODE)
		osa_tx_init.o(+CODE)

		;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
		;; all TX os +CODE lib load inito here
		;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;

		*os-threadx.lib(+RO)

		;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;
		;;;;; lzo code on PSRAM to reduce DSP decompress time consumption
		lzop_buf.o(+CODE)
		lzop.o(+CODE)
		minilzo.o(+CODE)


		os-alios.lib         (+CODE)

		;;;;;;;;;;;;;;;;;;;;;;;;;
		;; for SPI nor flash load
		spi_nor.o (+CODE)
		ssp_host.o (+CODE)
		rm.o (+CODE)
		aam.o (+CODE)
		tick_manager.o (+CODE)
		commpm.o (+CODE)
		;timer.o          (HW_TIMER)

		;qspi_core.o            (+CODE)
		;qspi_host.o            (+CODE)
		;;qspi_nor.o            (+CODE)

		modemvolte.o		(MODEMVLOTE_CID)
		cimodem.o		(+CODE)
		GKITick.o  (+CODE)
		NUtick.o	(+CODE)

		;osa_common_run.o (+CODE)
		osa_common_init.o (+CODE)
		osa_ali_init.o	(+CODE)

		WS_CmnSrv.o  (+CODE)
		WS_Data.o   (+CODE)
		WS_HwAcs.o   (+CODE)
		kiossem.o    (+CODE)
		WS_IPCICATFunc.o  (+CODE)


		bsp_hisr.o   (+CODE)
		utils_memRetain.o(+CODE)
		alios_hisr.o(+CODE)

	}
	PS_CODE_IN_PSRAM_ENDMARKER  (PS_CODE_IN_PSRAM_BASE + PS_CODE_IN_PSRAM_SIZE - 0x4)   EMPTY  0x4  {}
}
