#ifndef _CST816T_H
#define _CST816T_H

#ifdef __cplusplus
extern "C" {
#endif

#include "lv_drv_conf.h"

#include <stdbool.h>
#include "lvgl/src/lv_hal/lv_hal_indev.h"
#include "lv_watch_conf.h"

#if USE_CST816T

#define CST816T_UPGRADE 1
struct CST816T_info {
    int irq_pin;
    int rst_pin;
    int i2c_addr;
};

int CST816T_init(const struct CST816T_info *info);
bool CST816T_read(lv_indev_drv_t * indev_drv,lv_indev_data_t *data);
bool CST816T_vk_read(lv_indev_data_t *data);

#endif /* USE_CST816T */

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* _CST816T_H */
