/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/****************************************************************************
 * TTPCom Software Copyright (c) 1997-2005 TTPCom Ltd
 * Licensed to Marvell International Ltd
 ****************************************************************************
 *   $Id: //central/main/wsd/modem/pscommon/3g_ut.mod/api/inc/utftfta.h#1 $
 *   $Revision: #1 $
 *   $DateTime: 2007/03/19 13:59:30 $
 ****************************************************************************
 * File Description:
 *
 *    Contains declarations for the Timing Analysis (TA) Module
 ****************************************************************************/

#if !defined (UT_FTFTA_H)
#define       UT_FTFTA_H

/****************************************************************************
 * Nested Include Files
 ****************************************************************************/
#include "utftftaevents.h"
#include "utftf_sig.h"

/*****************************************************************************
 * Manifest Constants (independant of build switch)
 *****************************************************************************/
#define UT_FTF_TA_TIMER_USER_VALUE 0xffff

#if defined (ENABLE_FLEXIBLE_TRACE_FRAMEWORK_TA)

/*****************************************************************************
 * Function Prototypes
 *****************************************************************************/

/*****************************************************************************
 * Type Definitions
 *****************************************************************************/

/****************************************************************************
 * Global Variables
 ****************************************************************************/
extern Int32   utFtfTa_Filling;
extern Int32   utFtfTa_FillingCnt;
extern Int32   utFtfTa_Output;
extern Int32   utFtfTa_SequenceNumber;
extern Int32   utFtfTa_ProcMisfire;

extern UtFlexibleTraceTaOut utFtfTa_Containers[UT_FTF_TA_NUM_STATIC_CONTAINERS];

/****************************************************************************
 * Function Prototypes
 ****************************************************************************/
void utFtfTaConstruct ( void );

void utFtfTaDestruct ( void );

void utFtfTaHandleFlexibleTraceTaControlReq 
                                        ( UtFlexibleTraceTaControlReq *sig_p );

void utFtfTaLogEvent ( utFtfTaEvent     event,
                       Int32            data,
                       Int32            profile );

void utFtfTaOutputEvents ( void );

/****************************************************************************
 * Macros
 ****************************************************************************/

/*******************************************************************************
 *
 * Function     : The TA Flag Setters
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : Varying
 *
 * Returns      : None
 *
 * Description  : Sets the appropriate bits in a SIG_UT_FLEXIBLE_TRACE_TA_OUT
 *                Int32 flag element.
 *
 ******************************************************************************/
/* bits 0..3   [4]  Version Number */
#define UT_FTF_TA_FLAG_SET_VER(fLAG,vER) (fLAG |= (vER&0xf))
/* bits 4..11  [8]  Sequence Number */
#define UT_FTF_TA_FLAG_SET_SN(fLAG,sN) (fLAG |= ((sN&0xff)<<4))
/* bit  12     [1]  Data Lost */
#define UT_FTF_TA_FLAG_SET_DATALOST(fLAG) (fLAG |= 0x000001000)
/* bits 13..17 [5]  Timing Method */
#define UT_FTF_TA_FLAG_SET_TM(fLAG,tM) (fLAG |= ((tM&0x1f)<<13))
/* bit  18     [1]  Static\Dynamic Container */
#define UT_FTF_TA_FLAG_SET_STATICCONT(fLAG) (fLAG |= 0x00040000)
/* bits 19..31 [13] Unused */

/*******************************************************************************
 *
 * Function     : The TA Flag Getters
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : fLAG - the SIG_UT_FLEXIBLE_TRACE_TA_OUT flag to get values
 *                       from.
 *
 * Returns      : Varying
 *
 * Description  : Gets specific values from a SIG_UT_FLEXIBLE_TRACE_TA_OUT flag
 *                element.
 *
 ******************************************************************************/
#define UT_FTF_TA_FLAG_GET_VER(fLAG)        (fLAG&0xf)
#define UT_FTF_TA_FLAG_GET_SN(fLAG)         ((fLAG>>4)&0xff)
#define UT_FTF_TA_FLAG_GET_DATALOST(fLAG)   (fLAG&0x1000)
#define UT_FTF_TA_FLAG_GET_TM(fLAG)         ((fLAG>>13)&0x1f)
#define UT_FTF_TA_FLAG_GET_STATICCONT(fLAG) (fLAG&0x40000)

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_CONSTRUCT
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Constructs the Timing Analysis Module
 *
 ******************************************************************************/
#define UT_FTF_TA_CONSTRUCT    utFtfTaConstruct();

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_DESTRUCT
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Destructs the Timing Analysis Module
 *
 ******************************************************************************/
#define UT_FTF_TA_DESTRUCT     utFtfTaDestruct();

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_HANDLE_TRACE_CONTROL_TA_REQ
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : Instance of SIG_UT_FLEXIBLE_TRACE_TA_CONTROL_REQ signal.
 *
 * Returns      : None
 *
 * Description  : Handles SIG_UT_FLEXIBLE_TRACE_TA_CONTROL_REQ
 *
 ******************************************************************************/
#define UT_FTF_TA_HANDLE_TRACE_CONTROL_TA_REQ(sIG)                             \
{                                                                              \
    utFtfTaHandleFlexibleTraceTaControlReq(sIG);                               \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_LOG_EVENT
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : eVENT    - Event Id
 *                dATA     - Event Specific Data
 *                pROFILE  - Event Profile Key
 *
 * Returns      : None
 *
 * Description  : Stores an event for logging later.
 *
 ******************************************************************************/
#define UT_FTF_TA_LOG_EVENT(eVENT,dATA,pROFILE)                                \
{                                                                              \
   utFtfTaLogEvent ((utFtfTaEvent)eVENT,dATA,pROFILE);                         \
}

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_OUTPUT_EVENTS
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Processes stored events logging them.
 *
 ******************************************************************************/
#define UT_FTF_TA_OUTPUT_EVENTS                                                \
{                                                                              \
   utFtfTaOutputEvents();                                                      \
}

/* If they haven't been defined already, define them.                         */

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_GET_TIMER_FREQUENCY
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : Int32 indicating the frequency the timestamp free running
 *                counter increments in hertz.
 *
 * Description  : Implement as part of the platform port.
 *                Returns an Int32 indicating the timestamp free running counter
 *                in hertz. This value is used to convert the output of the 
 *                timestamp into a meaningfull time for timing analysis.
 *
 *                This macro is called once at system initialisation. The system
 *                does not expect this value to dynamically change.
 *
 ******************************************************************************/
#if !defined (UT_FTF_TA_GET_TIMER_FREQUENCY)
#define UT_FTF_TA_GET_TIMER_FREQUENCY   0
#endif /* UT_FTF_TA_GET_TIMER_FREQUENCY */

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_GET_TIMESTAMP
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : Int32 indicating the current value of a free running timer 
 *                whose frequency was reported in UT_FTF_TA_GET_TIMER_FREQUENCY.
 *
 * Description  : Returns the current value of a free running counter. This 
 *                is used with the frequency reported by
 *                UT_FTF_TA_GET_TIMER_FREQUENCY to determine a time reference.
 *
 ******************************************************************************/
#if !defined (UT_FTF_TA_GET_TIMESTAMP)
#define UT_FTF_TA_GET_TIMESTAMP 0
#endif /* UT_FTF_TA_GET_TIMESTAMP */

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_TASK_START
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Resets the platform specific algorithm for extracting task
 *                names.
 *
 ******************************************************************************/
#if !defined (UT_FTF_TA_TASK_START)
#define UT_FTF_TA_TASK_START
#endif /* UT_FTF_TA_TASK_START */

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_TASK_GET_NUM_TASKS
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : The total number of tasks defined in the system.
 *
 * Description  : Returs the platform specific number of tasks defined in the
 *                system.
 *
 ******************************************************************************/
#if !defined (UT_FTF_TA_TASK_GET_NUM_TASKS)
#define UT_FTF_TA_TASK_GET_NUM_TASKS    0
#endif /* UT_FTF_TA_TASK_GET_NUM_TASKS */

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_TASK_GET_INFO
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : n             - the nth task
 *              : idpTR         - pointer to store the task ID in
 *              : pRIpTR        - pointer to store the task priority in
 *              : nAMEpTR       - buffer to copy the task name to
 *              : nAMEmAXlENGTH - maximum length of task name buffer
 *
 * Returns      : None
 *
 * Description  : Copies the platform specific task information into the provided
 *                buffers.
 *
 ******************************************************************************/
#if !defined (UT_FTF_TA_TASK_GET_INFO)
#define UT_FTF_TA_TASK_GET_INFO(n,iDpTR,pRIpTR,nAMEpTR,nAMEmAXlENGTH)
#endif /* UT_FTF_TA_TASK_GET_INFO */

/*******************************************************************************
 *
 * Function     : UT_FTF_TA_TASK_END
 *
 * Scope        : GLOBAL MACRO
 *
 * Parameters   : None
 *
 * Returns      : None
 *
 * Description  : Prompts the platform specific algorithm for extracting task
 *                names to deconstruct.
 *
 ******************************************************************************/
#if !defined (UT_FTF_TA_TASK_END)
#define UT_FTF_TA_TASK_END
#endif /* UT_FTF_TA_TASK_END */

#else /* ENABLE_FLEXIBLE_TRACE_FRAMEWORK_TA */

/* Define empty macros if compile switch not on */
#define UT_FTF_TA_GET_TIMER_FREQUENCY
#define UT_FTF_TA_GET_TIMESTAMP
#define UT_FTF_TA_CONSTRUCT
#define UT_FTF_TA_HANDLE_TRACE_CONTROL_TA_REQ(sIG)
#define UT_FTF_TA_LOG_EVENT(eVENT,dATA,pROFILE)
#define UT_FTF_TA_OUTPUT_EVENTS

#endif /* !ENABLE_FLEXIBLE_TRACE_FRAMEWORK_TA */
#endif /* UT_FTFTA_H */

/* END OF FILE */
