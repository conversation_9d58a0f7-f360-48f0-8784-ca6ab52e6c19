#include <stdlib.h>

#include "rs_mem.h"
#include "rs_debug.h"


// ASR  header
#include "asr_crane_rs.h"


#define RSDL_STACK_SIZE   (20480)
char rsdl_stack[RSDL_STACK_SIZE] = {0};
//TX_BYTE_POOL   rsdl_byte_pool;   /* byte pool for stacks and queues */
rs_bool g_memeryInit = RS_FALSE;

void rs_memory_init()
{
	RS_PORITNG_LOG( RS_LOG_INFO "%s\n",__func__);
}

void* rs_malloc_porting(rs_u32 allocSize)
{
	void *ptr=NULL;
	
	ptr = OsaMemAlloc(NULL, allocSize);
	return ptr;


}

void  rs_free_porting(void* memBlock)
{
	if(memBlock != NULL)
	{
        OsaMemFree(memBlock);
		memBlock= NULL;
	}

}

