/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/**************************************************************************
 * TTPCom Software Copyright (c) 2006 TTPCom Ltd
 * Licensed to Marvell International Ltd
 **************************************************************************
 *   $Id: //central/main/wsd/modem/psnas/csd.mod/api/inc/vgmx_typ.h#5 $
 *   $Revision: #5 $
 *   $DateTime: 2007/03/19 13:59:30 $
 **************************************************************************/
/** \file
 * Definition of constants and structures for the Voyager Multiplexer interface.
 **************************************************************************/
#ifndef VGMXTYP_H
#define VGMXTYP_H

#include <system.h>
#include <kitqid.h>
#include <cimux_typ.h>

/** COM Port type.
 * This enumeration contains all possible COM port types supported by the
 * system.
 */
typedef enum VgmxComPortTypeTag
{
  VG_COM_PORT_TYPE_SERIAL,  /**< Serial port. */
  VG_COM_PORT_TYPE_USB,     /**< USB port. */
  VG_COM_PORT_TYPE_BT,      /**< Bluetooth port. */
  VG_COM_PORT_TYPE_IRDA,    /**< Infrared port. */
  VG_NUM_COM_PORT_TYPES     /**< Number of port types. */
} VgmxComPortType;

/** COM port Index. Identifies the COM port to which the Mux is assigned.*/
typedef struct VgmxComPortIndexTag
{
  VgmxComPortType portType;  /**< COM port type */
  Int8            index;     /**< COM port index - for specific type */
}VgmxComPortIndex;

#if defined (ENABLE_CHANNEL_MANAGEMENT)
/** Number of Multiplexers supported - 1 for non-MultiMux builds */
#define VG_NO_OF_MUX         (1)
/** Invalid Mux index. Set for an entity which is yet to be assigned to a Mux */
#define VG_INVALID_MUX_INDEX (0xFFFF)

#if defined (MUX_DEF)
# undef MUX_DEF
#endif
/** Macro used to retrieve available multiplexer names.
 * \param nAME          The multiplexer name.
 * \param tASK          The multiplexer task (unused).
 * \param vERSIONnUMBER The multiplexer version number (unused).
 * \param rEADABLEnAME  The readable multiplexer name (unused).
 */
#define MUX_DEF(nAME, tASK, vERSIONnUMBER, rEADABLEnAME) nAME,
/** Multiplexer Identifier type.
 * This enumeration is used to identify all the available multiplexers.
 */
typedef enum VgmuxIdTag
{
#include <vgmxlist.h>
  VG_MUX_NUMBER_OF_MUX,  /**< The total number of multiplexers available. */
  VG_MUX_INVALID = 255   /**< An invalid multiplexer identifier. */
} VgmuxId;

/** Multiplexer Index. Identifies the specific Mux - always 0 for non-MultiMux builds */
typedef TP_UInt16 VgmxMuxIndex;

/** Port Rate Type.
 * This enumeration is used to specify the port rate applied for
 * multiplexer communications.
 */
typedef enum VgmxPortRateTag
{
  VG_DTEIF_1200 = 0,              /**< 1200 baud. */
  VG_DTEIF_2400,                  /**< 2400 baud. */
  VG_DTEIF_4800,                  /**< 4800 baud. */
  VG_DTEIF_9600,                  /**< 9600 baud. */
  VG_DTEIF_14400,                 /**< 14400 baud. */
  VG_DTEIF_19200,                 /**< 19200 baud. */
  VG_DTEIF_28800,                 /**< 28800 baud. */
  VG_DTEIF_38400,                 /**< 38400 baud. */
  VG_DTEIF_57600,                 /**< 57600 baud. */
  VG_DTEIF_115200,                /**< 115200 baud. */
  VG_DTEIF_230400,                /**< 230400 baud. */
  VG_DTEIF_460800,                /**< 460800 baud. */
  VG_DTEIF_921600,                /**< 921600 baud. */
  VG_DTEIF_NUMBER_OF_BAUD_RATES,  /**< Total number of baud rates supported. */
  VG_DTEIF_RESET_BAUD_RATE        /**< A baud rate reset. Used to distinguish between:
                                   * - A baud rate reset initiated by ATZ or AT&F which may be
                                   *   prohibited, for example during a fax operation.
                                   * - An AT+IPR initiated change which is always allowed. */
} VgmxPortRate;

/** Parity Information Type.
 * This enumeration is used to specify the parity type applied for
 * multiplexer communications.
 */
typedef enum VgmxParityInformationTag
{
  VG_PARITY_ODD         = 0, /**< Odd Parity. */
  VG_PARITY_EVEN        = 2, /**< Even Parity. */
  VG_PARITY_NONE        = 3, /**< No Parity. */
  VG_PARITY_FORCED_TO_0 = 4, /**< Forced to 0. */
  VG_PARITY_FORCED_TO_1 = 5  /**< Forced to 1. */
}
VgmxParityInformation;

/** Flow Control type.
 * This type is used to configure the local flow control settings used for
 * multiplexer communications.
 */
typedef struct VgmxFlowCtrlInfoTag
{
  VgFlowControlType  uplink;    /**< The flow control uplink setting (data sent by modem). */
  VgFlowControlType  downlink;  /**< The flow control downlink setting (data received by modem). */
} VgmxFlowCtrlInfo;

/** Character Framing type.
 * This type is used to configure the local port start-stop (asynchronous)
 * character framing used for multiplexer communications.
 */
typedef struct VgmxCharFormatInfoTag
{
  Int8                   dataBits;  /**< The number of data bits to use, typically 7 or 8. */
  Int8                   stopBits;  /**< The number of stop bits to use, typically 1 or 2. */
  VgmxParityInformation  parity;    /**< The parity settings to use, typically odd/even/none etc. */
} VgmxCharFormatInfo;

/** COM Port Settings Type.
 * This type is used to specify the com port settings rate applied for
 * multiplexer communications.
 */
typedef struct VgmxComPortSettingsTag
{
  /** COM port Index. Identifies the COM port on which the MUX is running. */
  VgmxComPortIndex   portIndex;
  /** Port Rate. */
  VgmxPortRate       portRate;
  /** Flow Control. */
  VgmxFlowCtrlInfo   flowCtrlInfo;
  /** Char Format. */
  VgmxCharFormatInfo charFormatInfo;
} VgmxComPortSettings;

/** Open Data Connection Request Status. */
typedef enum VgmxOpenConnStatusTag
{
  VGMX_STATUS_OK,    /**< Operation successful. */
  VGMX_STATUS_NOK   /**< Operation failed. */
} VgmxOpenConnStatus;

/** Multiplexer Channel Type.
 * The type is used to identify multiplexer channels.
 */
typedef TP_UInt8  VgmuxChannelNumber;
#define VGMUX_CHANNEL_COMMAND_1   (0)    /**< AT command channel number 1. */
#define VGMUX_CHANNEL_COMMAND_2   (1)    /**< AT command channel number 2. */
#define VGMUX_CHANNEL_COMMAND_3   (2)    /**< AT command channel number 3. */
#define VGMUX_CHANNEL_COMMAND_4   (3)    /**< AT command channel number 4. */

#define VGMUX_CHANNEL_NUMBER_OF_AT_CHANNELS (4)
                                          /**< The maximum number of multiplexer channels used
                                          * for AT commands. */
#define VGMUX_CHANNEL_UNSOLICITED (VGMUX_CHANNEL_NUMBER_OF_AT_CHANNELS)
                                          /**< The unsolicited channel used for broadcasting
                                          * unsolicited events. Used only internally within CI.
                                          * Unsolicited messages are then sent to the multiplexer
                                          * on appropriate channel. */
#define VGMUX_CHANNEL_INVALID     (0xF0)    /**< An invalid multiplexer channel. Unsolicited messages
                                          * sent on this channel are yet to be converted to Channel
                                          * Management format. */
#define VGMUX_CHANNEL_BROADCAST   (0xFF) /**< A broadcast multiplexer channel. Unsolicited messages
                                          * sent on this channel should be displayed on all the
                                          * multiplexer channels. */
#else
/* The list of all multiplexers */

#if defined (MUX_DEF)
# undef MUX_DEF
#endif
/** Macro used to retrieve available multiplexer names.
 * \param nAME          The multiplexer name.
 * \param tASK          The multiplexer task (unused).
 * \param vERSIONnUMBER The multiplexer version number (unused).
 * \param rEADABLEnAME  The readable multiplexer name (unused).
 */
#define MUX_DEF(nAME, tASK, vERSIONnUMBER, rEADABLEnAME) nAME,

/** Multiplexer Identifier type.
 * This enumeration is used to identify all the available multiplexers.
 */
typedef enum VgmuxIdTag
{
#include <vgmxlist.h>
  VG_MUX_NUMBER_OF_MUX,  /**< The total number of multiplexers available. */
  VG_MUX_INVALID = 255   /**< An invalid multiplexer identifier. */
} VgmuxId;

/** Multiplexer Channel Type.
 * This enumeration is used to identify the multiplexer channels. The
 * channels can be used to exchange AT command data and GSM/GPRS data
 * communications.
 */

typedef enum VgmuxChannelNumberTag
{
  VGMUX_CHANNEL_COMMAND_1   = 0,  /**< AT command channel number 1. */
  VGMUX_CHANNEL_COMMAND_2   = 1,  /**< AT command channel number 2. */
  VGMUX_CHANNEL_COMMAND_3   = 2,  /**< AT command channel number 3. */
  VGMUX_CHANNEL_COMMAND_4   = 3,  /**< AT command channel number 4. */
  VGMUX_CHANNEL_UNSOLICITED = 4,  /**< The unsolicited channel used for broadcasting unsolicited
                                   * events. Depending on the multiplexer implementation data sent
                                   * on the unsolicited channel may be interleaved on a command
                                   * channel or be maintained on a separate unsolicited
                                   * channel. */
  VGMUX_CHANNEL_GSM_DATA    = 5,  /**< GSM data channel, used to exchange data during a CSD
                                   * call. */
  VGMUX_CHANNEL_GPRS_DATA   = 6,  /**< GPRS data channel, used to exchange data during a GPRS
                                   * connection. */
  VGMUX_CHANNEL_INVALID,          /**< An invalid multiplexer channel. */
  VGMUX_CHANNEL_BROADCAST   = VGMUX_CHANNEL_UNSOLICITED  /**< Setting the broadcast channel to be
                                                          * identical to the unsolicited channel. */
} VgmuxChannelNumber;

/** The maximum number of multiplexer channels used for AT commands. */
#define VGMUX_CHANNEL_NUMBER_OF_AT_CHANNELS (4)
#endif

/** The total number of multiplexer channels used for AT command and data
 * communciations. */
#if defined (UPGRADE_GPRS)
#define VGMUX_CHANNEL_NUMBER_OF_CHANNELS (7)
#else
#define VGMUX_CHANNEL_NUMBER_OF_CHANNELS (6)
#endif /* UPGRADE_GPRS */

/* Channel Type Defines */
#define VGMUX_CHANNEL_00_AT_COMMAND      /**< Multiplexer channel 0, used for AT commands. */
#define VGMUX_CHANNEL_01_AT_COMMAND      /**< Multiplexer channel 1, used for AT commands. */
#define VGMUX_CHANNEL_02_AT_COMMAND      /**< Multiplexer channel 2, used for AT commands. */
#define VGMUX_CHANNEL_03_AT_COMMAND      /**< Multiplexer channel 3, used for AT commands. */
#define VGMUX_CHANNEL_04_AT_UNSOLICITED  /**< Multiplexer channel 4, used for unsolicited data. */
#define VGMUX_CHANNEL_05_CSD             /**< Multiplexer channel 5, used for CSD. */
#if defined (UPGRADE_GPRS)
#define VGMUX_CHANNEL_06_PPP             /**< Multiplexer channel 6, used for PPP data. */
#endif /* UPGRADE_GPRS */

/** The maximum number of COM ports of the same type. */
#define VG_MAX_COM_PORT_INDEX  (3)

/* CI will only send up to VG_AT_DATA_REQ_TX_WINDOW unacknowledged AtDataReq signals */
#define VG_AT_DATA_REQ_TX_WINDOW (5)

/* The MUX will send the AtDataCnf after receiving VG_NO_AT_DATA_REQ_TO_CNF AtDataReq signals */
#define VG_NO_AT_DATA_REQ_TO_CNF (3)

/** Switch COM Port status.
 *  This enumeration contains Switch COM port result codes.
 */
typedef enum VgmxSwitchComPortStatusTag
{
  VG_SWITCH_OK,           /**< Switch successful. */
  VG_SWITCH_PORT_INVALID  /**< Port Invalid. */
} VgmxSwitchComPortStatus;

/*****************************************************************************/
/** The Data Stack Domain type.
 * The purpose of `VgmuxDomain' is to distinguish among different series of
 * numbers used in the associated cid.  The value of this domain,
 * together with the cid, uniquely identifies a data stream between
 * the mux and the rest of the protocol stack.  When the domain is
 * VGMUX_PS_DOMAIN, then the cid is intended to be a cid
 * as used in the packet-switched AT commands.
 * When the domain is VGMUX_CS_DOMAIN,
 * then the cid is intended to be an identifier closely related to
 * the stream identifier.  In future, the choice of (domain, cid)
 * pairs should be irrelevant to the mux: all the mux requires
 * is that they should uniquely identify the data stream.
 * For historial reasons, present multiplexers
 * other than the `golden mux' require that
 * VGMUX_PS_DOMAIN be used for GPRS-bound traffic,
 * and VGMUX_CS_DOMAIN for CSD-bound traffic.
 * The latter includes PPP packets which are to be transmitted
 * over 64 kbit/s synchronous CS calls.
 * For the time being, VGMUX_PS_DOMAIN is also used for
 * LPPP traffic.
 */
typedef enum VgmuxDomainTag
{
  /** Used for GPRS-bound traffic, and also for LPPP traffic.
   * In future the cid may be used for the cid
   * as used in the packet-switched AT commands.
   */
  VGMUX_PS_DOMAIN = 0,
  /** Used for CSD-bound traffic.
   * This includes PPP packets which are to be transmitted
   * over 64 kbit/s synchronous CS calls.
   */
  VGMUX_CS_DOMAIN = 1,
  VGMUX_NUM_OF_DOMAINS,  /**< Number of Domains. */
  INVALID_VGMUX_DOMAIN  /**< Invalid domain. */
}
VgmuxDomain;

/** \def VG_MUX_AT_MAX_DATA
 * The maximum quantity of data that can be contained and transferred using
 * the #VgmuxAtDataReq and #VgmuxAtDataInd signals.
 */
#if !defined (VG_MUX_AT_MAX_DATA)
#define VG_MUX_AT_MAX_DATA (128)
#endif /* !defined (VG_MUX_AT_MAX_DATA) */

/****************************************************************************
 * Macros
 ****************************************************************************/

/*****************************************************************************/
/* Global Data                                                               */
/*****************************************************************************/

/* Define the range of supported multiplexer versions                  */
/*                                                                     */
/* There must be one definition for each supported multiplexer version */
/* which must define SUPPORTED_MUX_VERSION_NUMBER_n as n.              */
/* This allows compile time checking of multiplexer version numbers    */

/** This define for version 3 of the multiplexer is used in compile
 * time checking that the version is supported. */
#define SUPPORTED_MUX_VERSION_NUMBER_3 (3)

/** This define for version 4 of the multiplexer is used in compile
 * time checking that the version is supported. */
#define SUPPORTED_MUX_VERSION_NUMBER_4 (4)

/** This constant defines the lowest version of the MUX which requires
 * a response when SIG_VGMX_AT_DATA_IND signals are freed.
 */
#define LOWEST_MUX_VERSION_WHICH_REQUIRES_DATA_RSP (3)

/** Version 4 muxen support AT Data Confirmation and AT Data Req flow control */
#define LOWEST_MUX_VERSION_WHICH_SUPPORTS_AT_DATA_CNF (4)

#endif /* VGMXTYP_H */
/* END OF FILE */

