#ifndef _CRANE_WATCH_KEYPAD_H_
#define _CRANE_WATCH_KEYPAD_H_

#ifdef __cplusplus
extern "C" {
#endif

#include "lv_drv_conf.h"

#if USE_CRANE_WATCH_KEYPAD
#include "crane_onkey.h"

typedef void (*keypad_cb_t)(struct keypad_param *arg);

bool keypad_watch_read(lv_indev_drv_t *indev_drv, lv_indev_data_t *data);
void keypad_watch_init(keypad_cb_t cb);
#endif /* end #if USE_CRANE_WATCH_KEYPAD */

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* _CRANE_WATCH_KEYPAD_H_ */

