@echo off
setlocal enabledelayedexpansion
@del /Q ..\bin\*
rmdir /s /q ..\bin\tools
del /Q ..\..\..\AbootTool\releasepackage\*.zip

set BLD_OPT=CRANE_CUST
set INPUT_ARG=5

call SetSDKLib.bat

set SDK_PROJ_ARG=MODULE_CIRAM_LTEONLY_THIN_4M_AGPS_TP
set SDK_PROD_TYPE=DM
set SDK_PS_MODE=LTEONLY
set SDK_CUST_SKU=THIN
set SDK_CHIP_VER=Z2A0
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lte_only_r13.bld
set SDK_OS_TYPE=TX

echo SDK_PROD_TYPE   : [%SDK_PROD_TYPE%]
echo SDK_CUST_SKU    : [%SDK_CUST_SKU%]
echo SDK_PS_MODE     : [%SDK_PS_MODE%]
echo SDK_CHIP_VER    : [%SDK_CHIP_VER%]
echo SDK_OS_TYPE     : [%SDK_OS_TYPE%]

::::::::::::::::::::::::::::::::::::::::::::::::::::::::
:: [SDK_PROD_TYPE]={FP|DM}
:: NOTE: production type should be featurePhone or datamodule
::::::::::::::::::::::::::::::::::::::::::::::::::::::::
if /i "%SDK_PROD_TYPE%"=="FP" (
set PROJECT_ARG=XIP_LWIP_%SDK_PROJ_ARG%
set SDK_DISTRIBUTION_MARK=%SDK_CUST_SKU%_SDK_%SDK_DISTRIBUTION_DATE%
)
if /i "%SDK_PROD_TYPE%"=="DM" (
set PROJECT_ARG=XIP_LWIP_%SDK_PROJ_ARG%
set SDK_DISTRIBUTION_MARK=%SDK_CUST_SKU%_SDK_%SDK_DISTRIBUTION_DATE%
)

::::::::::::::::::::::::::::::::::::::::::::::::::::::::
:: [SDK_PS_MODE]={LTEGSM|LTEONLY}
:: NOTE: ps mode should be singlemode lteonly or dualmode lte+gsm
::::::::::::::::::::::::::::::::::::::::::::::::::::::::
if "%SDK_PS_MODE%"=="LTEGSM" (
set LG_FOR_CAT1=TRUE
set LTE_ONLY_FOR_CAT1=FALSE
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% ^
gplc\GPLC
)
if "%SDK_PS_MODE%"=="LTEONLY" (
set LG_FOR_CAT1=FALSE
set LTE_ONLY_FOR_CAT1=TRUE
)


set CRANE_LIB_DIR_PREFIX=%XROOT%\tavor\Arbel\CRANE_SDK_LIB
if "%SDK_OS_TYPE%"=="TX" (
@rem **************************
@rem keep TX CRANE_LIB_DIR the same as prevois structure
@rem **************************
set CRANE_LIB_DIR=%CRANE_LIB_DIR_PREFIX%\%SDK_PROD_TYPE%_%SDK_CUST_SKU%_%SDK_PS_MODE%
echo CRANE_LIB_DIR   : [!CRANE_LIB_DIR!]

set CBA_USE_COMMON_LIB_DIR=1
set TARGET_OS=THREADX
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% ^
os\threadx
)
if "%SDK_OS_TYPE%"=="ALIOS" (
set CRANE_LIB_DIR=%CRANE_LIB_DIR_PREFIX%\%SDK_PROD_TYPE%_%SDK_CUST_SKU%_%SDK_OS_TYPE%_%SDK_PS_MODE%
echo CRANE_LIB_DIR   : [!CRANE_LIB_DIR!]
set CBA_USE_COMMON_LIB_DIR=1
set TARGET_OS=ALIOS
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% ^
os\alios
)

set XROOT=
set WROOT=%XROOT%
cd /d %XROOT%\tavor\Arbel\build
set TPLGSM=%WROOT%\3g_ps\rls\tplgsm
set HSIUPDLIBDEV=%TPLGSM%\bldstore\hsiupdlibdev
rmdir /q /s %TPLGSM%
compress.exe x !CRANE_LIB_DIR!\ps.7z -o%HSIUPDLIBDEV%\build\ -y

if not exist %HSIUPDLIBDEV%\test (
mkdir %HSIUPDLIBDEV%\test
)
if exist !CRANE_LIB_DIR!\hsiupdlibdev.i (
COPY /Y !CRANE_LIB_DIR!\hsiupdlibdev.i %HSIUPDLIBDEV%\test\hsiupdlibdev.i
)

call buildall PMD2NONE BUILD_LWG DKBFEATURE NEZHA3_%SDK_CHIP_VER%_%PROJECT_ARG%_NOIMS_NOAUDIO_CRANEL_NOSDCARD_NOPB DOUBLE_LTE FRAMEWORK

if %errorlevel% equ 1 (
exit /b 1
)

@rem # RN_PROJECT -> project name 			(CRANE)
@rem # RN_MODE    -> PS mode 				(LTE/NBIOT/CATM)
@rem # RN_BOARD   -> target HW board 			(HAPS/DKB/Z1)
@rem # RN_MEDIA   -> CP code running media 		(XIP/PSRAM)
@rem # RN_FLAG    -> some aditional special flags       (MMI/SDK/LWIP/......)
@rem # RN_BUILD_MARK -> build info mark append          (@xiaokeweng_20181106_205832)

set RN_PROJECT=CRANEL_DS_%SDK_OS_TYPE%_%SDK_CHIP_VER%
set RN_MODE=_%SDK_PS_MODE%
set RN_BOARD=_DKB
set RN_MEDIA=_%PROJECT_ARG%
set RN_FLAG=_%SDK_CUST_SKU%_SDK

for /f "delims=" %%i in ('perl localdate.pl') do (set RN_DATE=%%i)
set RN_BUILD_MARK=@%USERNAME%_%RN_DATE%_%time:~0,8%
set RN_BUILD_MARK=%RN_BUILD_MARK::=%
set RN_BUILD_MARK=%RN_BUILD_MARK: =%
set RN_BUILD_MARK=%RN_BUILD_MARK:-=%

set RN_GENERAL_INFO=%RN_PROJECT%%RN_MODE%%RN_BOARD%%RN_MEDIA%%RN_FLAG%
set RENAME_STR=%RN_GENERAL_INFO%%RN_BUILD_MARK%

@rem rename map/axf/MDB/NO_TIM_bin
rename ..\bin\Arbel_PMD2NONE_MDB.txt %RENAME_STR%_MDB.txt
rename ..\bin\Arbel_PMD2NONE_*.map   %RENAME_STR%.map
rename ..\bin\Arbel_PMD2NONE_*.axf   %RENAME_STR%.axf
rename ..\bin\Arbel_PMD2NONE_*NO_TIM.bin  Arbel_PMD2NONE_NO_TIM.bin

platform -p DKB_SS -t EVB_3  -i NONE  -b ..\bin\Arbel_PMD2NONE_NO_TIM.bin -o ..\bin\%RENAME_STR%.bin
perl external_rw_region_lzma.pl ..\bin\%RENAME_STR%.bin %RN_GENERAL_INFO%
perl external_loadtable_update.pl ..\bin\%RENAME_STR%.bin %RN_GENERAL_INFO%
call generate_reliabledata ..\bin\%RENAME_STR%.bin %RN_GENERAL_INFO%

xcopy %XROOT%\os\tools %XROOT%\tavor\Arbel\bin\tools /y /e /i /q

del /Q ..\bin\*Arbel_PMD2NONE*
dir /B ..\bin\*%RENAME_STR%*

@echo off
set AR_PATH=..\..\..\AbootTool
if not exist %AR_PATH%\releasepackage mkdir %AR_PATH%\releasepackage
COPY /Y ..\bin\%RENAME_STR%.bin %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\cp.bin
COPY /Y ..\bin\boot33_craneL.bin %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\boot33.bin
COPY /Y ..\bin\logo\logo_no_dsp_adc.bin %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\logo.bin
COPY /Y ..\bin\updater\updater_no_dsp_adc.bin %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\updater.bin
COPY /Y ..\bin\ReliableData.bin %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\ReliableData.bin
COPY /Y ..\bin\apn.bin %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\apn.bin
%AR_PATH%\arelease.exe -c %AR_PATH%\configurations\releasepack-ASR1606-source -g -p ASR1606_EVB -v ASR1606_ASR5311_04MB -i cp=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\cp.bin, -i dsp=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\dsp.bin -i rfbin=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\rf.bin -i boot33_bin=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\boot33.bin -i apn=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\apn.bin -i rd=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\ReliableData.bin -i updater=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\updater.bin -i logo=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\logo.bin -i jacana_fw=%AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\jacana_fw.bin --release-pack %AR_PATH%\releasepackage\ASR1606C_LTEOnly_ASR5311_DataModule_4MB_Source.zip %AR_PATH%\releasepackage\ASR1606C_LTEOnly_ASR5311_DataModule_4MB.zip 2>&1 >arelease.log
del /Q %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\cp.bin
del /Q %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\boot33.bin
del /Q %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\logo.bin
del /Q %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\updater.bin
del /Q %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\ReliableData.bin
del /Q %AR_PATH%\configurations\releasepack-ASR1606-source\images\ASR1606C_LTEOnly_ASR5311_DataModule\apn.bin
