/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "SUPL-POS-INIT"
 * 	found in "supl202.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_RequestedAssistData_H_
#define	_RequestedAssistData_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BOOLEAN.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct NavigationModel_ULP;
struct Ver2_RequestedAssistData_extension;

/* RequestedAssistData */
typedef struct RequestedAssistData {
	BOOLEAN_t	 almanacRequested;
	BOOLEAN_t	 utcModelRequested;
	BOOLEAN_t	 ionosphericModelRequested;
	BOOLEAN_t	 dgpsCorrectionsRequested;
	BOOLEAN_t	 referenceLocationRequested;
	BOOLEAN_t	 referenceTimeRequested;
	BOOLEAN_t	 acquisitionAssistanceRequested;
	BOOLEAN_t	 realTimeIntegrityRequested;
	BOOLEAN_t	 navigationModelRequested;
	struct NavigationModel_ULP	*navigationModelData	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	struct Ver2_RequestedAssistData_extension	*ver2_RequestedAssistData_extension	/* OPTIONAL */;
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} RequestedAssistData_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_RequestedAssistData;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "NavigationModel-ULP.h"
#include "Ver2-RequestedAssistData-extension.h"

#endif	/* _RequestedAssistData_H_ */
#include <asn_internal.h>
