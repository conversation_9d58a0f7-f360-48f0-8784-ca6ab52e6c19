\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \pcac\mbedTLS\mbedTLS_3_2_1\asros\asros_mbedtls_platform.c
\pcac\mbedTLS\mbedTLS_3_2_1\asros\asros_mbedtls_platform.c:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/osa.h
/os/osa/inc/osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\armv7r\include\alios_type.h
\os\alios\kernel\armv7r\include\alios_type.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \csw\platform\inc\gbl_types.h
\csw\platform\inc\gbl_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \env\win32\inc\xscale_types.h
\env\win32\inc\xscale_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/osa_old_api.h
/os/osa/inc/osa_old_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/osa.h
/os/osa/inc/osa.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/osa_utils.h
/os/osa/inc/osa_utils.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \csw\BSP\inc\bsp_hisr.h
\csw\BSP\inc\bsp_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\nu_xscale\inc\nucleus.h
\os\nu_xscale\inc\nucleus.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/osa_internals.h
/os/osa/inc/osa_internals.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_api.h
\os\alios\kernel\rhino\include\k_api.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\asr3601\config\k_config.h
\os\alios\asr3601\config\k_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_default_config.h
\os\alios\kernel\rhino\include\k_default_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\armv7r\include\k_types.h
\os\alios\kernel\armv7r\include\k_types.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\armv7r\include\k_compiler.h
\os\alios\kernel\armv7r\include\k_compiler.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_err.h
\os\alios\kernel\rhino\include\k_err.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_sys.h
\os\alios\kernel\rhino\include\k_sys.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_critical.h
\os\alios\kernel\rhino\include\k_critical.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_spin_lock.h
\os\alios\kernel\rhino\include\k_spin_lock.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_list.h
\os\alios\kernel\rhino\include\k_list.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_obj.h
\os\alios\kernel\rhino\include\k_obj.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_sched.h
\os\alios\kernel\rhino\include\k_sched.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_task.h
\os\alios\kernel\rhino\include\k_task.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_ringbuf.h
\os\alios\kernel\rhino\include\k_ringbuf.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_queue.h
\os\alios\kernel\rhino\include\k_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_buf_queue.h
\os\alios\kernel\rhino\include\k_buf_queue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_sem.h
\os\alios\kernel\rhino\include\k_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_task_sem.h
\os\alios\kernel\rhino\include\k_task_sem.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_mutex.h
\os\alios\kernel\rhino\include\k_mutex.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_timer.h
\os\alios\kernel\rhino\include\k_timer.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_time.h
\os\alios\kernel\rhino\include\k_time.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_event.h
\os\alios\kernel\rhino\include\k_event.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_stats.h
\os\alios\kernel\rhino\include\k_stats.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_mm_debug.h
\os\alios\kernel\rhino\include\k_mm_debug.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_mm.h
\os\alios\kernel\rhino\include\k_mm.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_mm_blk.h
\os\alios\kernel\rhino\include\k_mm_blk.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_mm_region.h
\os\alios\kernel\rhino\include\k_mm_region.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_workqueue.h
\os\alios\kernel\rhino\include\k_workqueue.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_internal.h
\os\alios\kernel\rhino\include\k_internal.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_trace.h
\os\alios\kernel\rhino\include\k_trace.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_soc.h
\os\alios\kernel\rhino\include\k_soc.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_hook.h
\os\alios\kernel\rhino\include\k_hook.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\rhino\include\k_bitmap.h
\os\alios\kernel\rhino\include\k_bitmap.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\armv7r\include\port.h
\os\alios\kernel\armv7r\include\port.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\armv7r\include\k_vector.h
\os\alios\kernel\armv7r\include\k_vector.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\armv7r\include\k_cache.h
\os\alios\kernel\armv7r\include\k_cache.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\alios\kernel\armv7r\include\k_mmu.h
\os\alios\kernel\armv7r\include\k_mmu.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/osa_ali.h
/os/osa/inc/osa_ali.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/alios_hisr.h
/os/osa/inc/alios_hisr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /os/osa/inc/osa_um_extr.h
/os/osa/inc/osa_um_extr.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \os\nu_xscale\inc\um_defs.h
\os\nu_xscale\inc\um_defs.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \pcac\mbedTLS\mbedTLS_3_2_1\asros\asros_mbedtls_platform.h
\pcac\mbedTLS\mbedTLS_3_2_1\asros\asros_mbedtls_platform.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/build_info.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : \tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h
\tavor\Arbel\obj_PMD2NONE\inc\alios_mbedtls_config.h:
\tavor\Arbel\obj_PMD2NONE\obj_pcac_pca_components\obj_pcac_mbedTLS/asros_mbedtls_platform.o : /pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h
/pcac/mbedTLS/mbedTLS_3_2_1/include/mbedtls/check_config.h:
