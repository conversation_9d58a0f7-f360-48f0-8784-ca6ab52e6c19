# Package build options
include ${OPT_FILE}

# Pre-Pass
PP =

# Package Makefile information
GEN_PACK_MAKEFILE = ${BUILD_ROOT}/env/${HOST}/build/package.mak

# Define Package ---------------------------------------

PACKAGE_NAME     = threadx
PACKAGE_BASE     = os
PACKAGE_PATH     = ${BUILD_ROOT}/${PACKAGE_BASE}/${PACKAGE_NAME}

# The path locations of source and include file directories.
PACKAGE_SRC_PATH    = ${PACKAGE_PATH}/src/arm \
						${PACKAGE_PATH}/src/common \
						${PACKAGE_PATH}/src/HISR \
						${PACKAGE_PATH}/src/posix  \
						#${PACKAGE_PATH}/src/init \
						#${PACKAGE_PATH}/src/Timer \
						#${PACKAGE_PATH}/src/uart \
						#${PACKAGE_PATH}/src/INT
						
PACKAGE_INC_PATHS   = ${PACKAGE_PATH}/inc ${BUILD_ROOT}/os/posix/inc

# Package source files, paths not required
CORE_TX_SRC_FILES = \
			     tx_initialize_low_level.S tx_thread_context_restore.S \
				 tx_thread_context_save.S tx_thread_interrupt_control.S \
				 tx_thread_schedule.S tx_thread_stack_build.S \
				 tx_thread_system_return.S tx_timer_interrupt.S\
				 tx_block_allocate.c tx_block_pool_cleanup.c tx_block_pool_create.c \
				 tx_block_pool_delete.c tx_block_pool_info_get.c \
				 tx_block_pool_initialize.c tx_block_pool_performance_info_get.c \
				 tx_block_pool_performance_system_info_get.c tx_block_pool_prioritize.c \
				 tx_block_release.c tx_byte_allocate.c tx_byte_pool_cleanup.c \
				 tx_byte_pool_create.c tx_byte_pool_delete.c tx_byte_pool_info_get.c \
				 tx_byte_pool_initialize.c tx_byte_pool_performance_info_get.c \
				 tx_byte_pool_performance_system_info_get.c tx_byte_pool_prioritize.c \
				 tx_byte_pool_search.c tx_byte_release.c tx_event_flags_cleanup.c \
				 tx_event_flags_create.c tx_event_flags_delete.c tx_event_flags_get.c \
				 tx_event_flags_info_get.c tx_event_flags_initialize.c tx_event_flags_performance_info_get.c \
				 tx_event_flags_performance_system_info_get.c tx_event_flags_set.c tx_event_flags_set_notify.c \
				 tx_initialize_high_level.c tx_initialize_kernel_enter.c \
				 tx_initialize_kernel_setup.c tx_mutex_cleanup.c \
				 tx_mutex_create.c tx_mutex_delete.c tx_mutex_get.c tx_mutex_info_get.c \
				 tx_mutex_initialize.c tx_mutex_performance_info_get.c \
				 tx_mutex_performance_system_info_get.c tx_mutex_prioritize.c tx_mutex_priority_change.c \
				 tx_mutex_put.c tx_queue_cleanup.c tx_queue_create.c tx_queue_delete.c \
				 tx_queue_flush.c tx_queue_front_send.c tx_queue_info_get.c \
				 tx_queue_initialize.c tx_queue_performance_info_get.c \
				 tx_queue_performance_system_info_get.c tx_queue_prioritize.c \
				 tx_queue_receive.c tx_queue_send.c tx_queue_send_notify.c \
				 tx_semaphore_ceiling_put.c tx_semaphore_cleanup.c \
				 tx_semaphore_create.c tx_semaphore_delete.c tx_semaphore_get.c \
				 tx_semaphore_info_get.c tx_semaphore_initialize.c \
				 tx_semaphore_performance_info_get.c tx_semaphore_performance_system_info_get.c \
				 tx_semaphore_prioritize.c tx_semaphore_put.c \
				 tx_semaphore_put_notify.c tx_thread_create.c tx_thread_delete.c \
				 tx_thread_entry_exit_notify.c tx_thread_identify.c \
				 tx_thread_info_get.c tx_thread_initialize.c tx_thread_performance_info_get.c \
				 tx_thread_performance_system_info_get.c \
				 tx_thread_preemption_change.c tx_thread_priority_change.c \
				 tx_thread_relinquish.c tx_thread_reset.c tx_thread_resume.c \
				 tx_thread_shell_entry.c tx_thread_sleep.c tx_thread_stack_analyze.c \
				 tx_thread_stack_error_handler.c tx_thread_stack_error_notify.c \
				 tx_thread_suspend.c tx_thread_system_preempt_check.c tx_thread_system_resume.c \
				 tx_thread_system_suspend.c tx_thread_terminate.c \
				 tx_thread_time_slice.c tx_thread_time_slice_change.c \
				 tx_thread_timeout.c tx_thread_wait_abort.c tx_time_get.c \
				 tx_time_set.c tx_timer_activate.c tx_timer_change.c \
				 tx_timer_create.c tx_timer_deactivate.c tx_timer_delete.c \
				 tx_timer_expiration_process.c tx_timer_info_get.c \
				 tx_timer_initialize.c tx_timer_performance_info_get.c \
				 tx_timer_performance_system_info_get.c tx_timer_system_activate.c \
				 tx_timer_system_deactivate.c tx_timer_thread_entry.c \
				 tx_trace_disable.c tx_trace_enable.c tx_trace_initialize.c \
				 tx_trace_interrupt_control.c tx_trace_isr_enter_insert.c \
				 tx_trace_isr_exit_insert.c tx_trace_object_register.c \
				 tx_trace_object_unregister.c tx_trace_user_event_insert.c \
				 txe_block_allocate.c txe_block_pool_create.c \
				 txe_block_pool_delete.c txe_block_pool_info_get.c \
				 txe_block_pool_prioritize.c txe_block_release.c \
				 txe_block_release.c txe_byte_allocate.c txe_byte_pool_create.c \
				 txe_byte_pool_delete.c txe_byte_pool_info_get.c \
				 txe_byte_pool_prioritize.c txe_byte_release.c \
				 txe_event_flags_create.c txe_event_flags_delete.c \
				 txe_event_flags_get.c txe_event_flags_info_get.c \
				 txe_event_flags_set.c txe_event_flags_set_notify.c \
				 txe_mutex_create.c txe_mutex_delete.c txe_mutex_get.c \
				 txe_mutex_info_get.c txe_mutex_prioritize.c txe_mutex_put.c \
				 txe_queue_create.c txe_queue_delete.c txe_queue_flush.c \
				 txe_queue_front_send.c txe_queue_info_get.c \
				 txe_queue_prioritize.c txe_queue_receive.c \
				 txe_queue_send.c txe_queue_send_notify.c \
				 txe_semaphore_ceiling_put.c txe_semaphore_create.c \
				 txe_semaphore_delete.c txe_semaphore_get.c txe_semaphore_info_get.c \
				 txe_semaphore_prioritize.c txe_semaphore_put.c \
				 txe_semaphore_put_notify.c txe_thread_create.c \
				 txe_thread_delete.c txe_thread_entry_exit_notify.c \
				 txe_thread_info_get.c txe_thread_preemption_change.c \
				 txe_thread_priority_change.c txe_thread_relinquish.c \
				 txe_thread_reset.c txe_thread_resume.c txe_thread_suspend.c \
				 txe_thread_terminate.c txe_thread_time_slice_change.c \
				 txe_thread_wait_abort.c txe_timer_activate.c \
				 txe_timer_change.c txe_timer_create.c \
				 txe_timer_deactivate.c txe_timer_delete.c \
				 txe_timer_info_get.c \
				 tx_hisr.c
				 
CORE_TX_SRC_FILES += px_pth_init.c px_system_manager.c px_memory_allocate.c px_memory_release.c px_px_initialize.c 
CORE_TX_SRC_FILES += px_in_thread_context.c px_pth_create.c  px_pth_cancel.c px_pth_attr_init.c px_error.c px_pth_join.c px_pth_exit.c
CORE_TX_SRC_FILES += px_pth_yield.c px_pth_equal.c px_pth_getschedparam.c px_pth_self.c px_pth_detach.c
CORE_TX_SRC_FILES += px_pth_setcancelstat.c px_pth_getcanceltype.c px_pth_set_default_pthread_attr.c
CORE_TX_SRC_FILES += px_pth_once.c px_pth_setschedparam.c px_pth_testcancel.c
CORE_TX_SRC_FILES += px_pth_lock.c px_pth_unlock.c

CORE_TX_SRC_FILES += px_pth_attr_getstacksize.c  px_pth_attr_setstacksize.c
CORE_TX_SRC_FILES += px_pth_attr_getstackaddr.c  px_pth_attr_setstackaddr.c 
CORE_TX_SRC_FILES += px_pth_attr_setstack.c px_pth_attr_getstack.c
CORE_TX_SRC_FILES += px_pth_attr_destroy.c  
CORE_TX_SRC_FILES += px_pth_attr_getdetachstate.c px_pth_attr_setdetachstate.c
CORE_TX_SRC_FILES += px_pth_attr_getinheritsched.c px_pth_attr_setinheritsched.c
CORE_TX_SRC_FILES += px_pth_attr_getschedparam.c px_pth_attr_setschedparam.c
CORE_TX_SRC_FILES += px_pth_attr_getschedpolicy.c px_pth_attr_setschedpolicy.c

CORE_TX_SRC_FILES += px_cond_init.c px_cond_destroy.c px_cond_signal.c px_cond_broadcast.c px_cond_wait.c
CORE_TX_SRC_FILES += px_cond_timedwait.c px_abs_time_to_rel_ticks.c

CORE_TX_SRC_FILES += px_mq_create.c px_mq_find_queue.c px_mq_open.c px_mq_receive.c px_mq_send.c px_mq_queue_init.c  
CORE_TX_SRC_FILES += px_mq_create.c px_mq_attr_init.c px_mq_unlink.c px_mq_close.c px_mq_queue_delete.c px_mq_get_queue_desc.c
CORE_TX_SRC_FILES += px_mq_reset_queue.c  
CORE_TX_SRC_FILES += px_mq_putback_queue.c px_mq_getattr.c px_mq_get_new_queue.c

CORE_TX_SRC_FILES += px_mx_init.c px_mx_destroy.c px_mx_lock.c px_mx_attr_init.c px_mx_attr_destroy.c
CORE_TX_SRC_FILES += px_mx_unlock.c px_mx_trylock.c px_mx_timedlock.c
CORE_TX_SRC_FILES += px_mx_attr_setprotocol.c px_mx_attr_getprotocol.c
CORE_TX_SRC_FILES += px_mx_attr_setpshared.c px_mx_attr_getpshared.c
CORE_TX_SRC_FILES += px_mx_attr_settype.c px_mx_attr_gettype.c
CORE_TX_SRC_FILES += px_mx_set_default_mutexattr.c

CORE_TX_SRC_FILES += px_sem_get_new_sem.c px_sem_find_sem.c px_sem_set_sem_name.c  px_sem_reset.c px_sem_close.c 
CORE_TX_SRC_FILES += px_sem_getvalue.c px_sem_open.c px_sem_post.c px_sem_trywait.c px_sem_unlink.c px_sem_wait.c
CORE_TX_SRC_FILES += px_sem_destroy.c px_sem_init.c

CORE_TX_SRC_FILES += px_clock_getres.c px_clock_gettime.c px_clock_settime.c

CORE_TX_SRC_FILES += px_sched_yield.c px_sched_get_prio.c

#CORE_TX_SRC_FILES += px_nanosleep.c
#CORE_TX_SRC_FILES += px_sleep.c
CORE_TX_SRC_FILES += timespec_sub.c
#CORE_TX_SRC_FILES += px_timer.c

CORE_TX_SRC_FILES += px_pth_thrd_spec_data.c
		 
PACKAGE_SRC_FILES = ${CORE_TX_SRC_FILES}

# These are the tool flags specific to the nu_xscale package only.
# The environment, target, and group also set flags.
PACKAGE_ASMFLAGS = -I${PACKAGE_PATH}/src/IQ80310 --predefine "__THUMB_INTERWORK SETL {TRUE}"
PACKAGE_CFLAGS   = --arm
PACKAGE_DFLAGS   = -DNU_DEBUG -DNU_ENABLE_STACK_CHECK -DNU_ERROR_STRING -D__THUMB_INTERWORK -DPLATFORM_OS_ONLY
PACKAGE_ARFLAGS  =


# The package dependency file
PACKAGE_DEP_FILE = threadx_dep.mak


# Include the Standard Package Make File ---------------
include ${GEN_PACK_MAKEFILE}

# Include the Make Dependency File ---------------------
# This must be the last line in the file
include ${PACKAGE_DEP_FILE}









