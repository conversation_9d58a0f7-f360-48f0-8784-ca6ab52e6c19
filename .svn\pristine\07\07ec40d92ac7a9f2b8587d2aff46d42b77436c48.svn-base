/******************************************************************************
 * * watch_modemadp_interface.h
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#ifndef _WATCH_MODEMADP_INTERFACE_H_
#define _WATCH_MODEMADP_INTERFACE_H_

#ifdef __cplusplus
extern "C" {
#endif /* __cpluscplus */

#ifdef LV_CONF_INCLUDE_SIMPLE
#include "lv_watch_conf.h"
#else
#include "../../../lv_watch_conf.h"
#endif

#include "../modem/mmi_modemadp_interface.h"

/**
 * power off request
 * param (in) void
 * return  void
 */
extern void watch_modem_power_off_req(void);

/**
 * power off  confrom
 * param (in) void
 * return  void
 */
extern void watch_modem_power_off_cnf(void);

/**
 * Requests current call list ( AT+CLCC)
 * param (out) calls:
 * return  void
 */
extern void watch_modem_call_request_get_current_calls(void);

/**
 * Initiate voice call ( ATD<dial string>; or AT*DIALE=<dial string>,<service Type>)
 * param (in) number:  phone number to dial
 * return  void
 */
extern void watch_modem_call_request_dial(char *number, MMI_MODEM_CALL_SERVICE_TYPE servicetype);

/**
 * Hang up a specific line (like AT+CHLD=1x)
 * param (in) index:   Connection index (value of 'x' in CHLD above)
 * return  void
 */
extern void watch_modem_call_request_hangup(int32_t index);

/**
 * Hang up active, and resume waiting or held (like AT+CHLD=1)
 * param (in) void
 * return  void
 */
extern void watch_modem_call_request_hangup_foreground_resume_background(void);

/*
 * Switch waiting or holding call and active call (like AT+CHLD=2)
 *
 * State transitions should be is follows:
 *
 * If call 1 is waiting and call 2 is active, then if this re
 *
 *   BEFORE                               AFTER
 * Call 1   Call 2                 Call 1       Call 2
 * ACTIVE   HOLDING                HOLDING     ACTIVE
 * ACTIVE   WAITING                HOLDING     ACTIVE
 * HOLDING  WAITING                HOLDING     ACTIVE
 * ACTIVE   IDLE                   HOLDING     IDLE
 * IDLE     IDLE                   IDLE        IDLE
 *
 * param (in) void
 * return  void
 */
extern void watch_modem_call_request_switch_waiting_or_hold_and_active(void);

/**
 * Send UDUB (user determined user busy) to ringing or
 * waiting call answer  (AT+CHLD=0)
 * param (in) void
 * return  void
 */
extern void watch_modem_call_request_udub(void);

/**
 * Answer incoming call (ATA)
 *
 * Will not be called for WAITING calls.
 * RIL_REQUEST_SWITCH_WAITING_OR_HOLDING_AND_ACTIVE will be used in this case instead
 *
 * param (in) void
 * return  void
 */
extern void watch_modem_call_request_answer(void);

/**
 * Send a DTMF tone (AT+VTS=<DTMF>)
 *
 * If the implementation is currently playing a tone requested via
 * RIL_REQUEST_DTMF_START, that tone should be cancelled and the new tone
 * should be played instead
 *
 * param (in) simId:  MMI_MODEM_SIM_ID
 * param (in) dtmf:  MMI_MODEM_CALL_REQUEST_DTMF *
 * return  void
 */
extern void watch_modem_call_request_dtmf(MMI_Modem_Call_Request_Dtmf_t *dtmf);

/**
 * Start playing a DTMF tone. Continue playing DTMF tone until
 * RIL_REQUEST_DTMF_STOP is received (AT+VTS=<DTMF>)
 *
 * If a RIL_REQUEST_DTMF_START is received while a tone is currently playing,
 * it should cancel the previous tone and play the new one.
 *
 * param (in) dtmf: a single character with one of 12 values: 0-9,*,#
 * return  void
 */
extern void watch_modem_call_request_dtmf_start(char dtmf);

/**
 * Stop playing a currently playing DTMF tone. (AT)
 * param (in) void
 * return  void
 */
extern void watch_modem_call_request_dtmf_stop(void);

/**
 *
 * This message is sent to the calling mobile station
 *  to indicate that the call is originating. (after ATD <number>;)
 * param index:  Connection index
 * return  void
 */
extern void watch_modem_call_response_originating(int32_t index);

/**
 *
 * This message is sent to the calling mobile station
 *  to indicate that the called user alerting has been initiated.
 * param index:  Connection index
 * return  void
 */
extern void watch_modem_call_response_alerting(int32_t index);

/**
 * This message is sent to the called mobile station
 *  to indicate an incoming call (voice, data or fax). (RING)
 * param index:  Connection index
 * return  void
 */
extern void watch_modem_call_response_ring(int32_t index);

/**
 * This message is sent to the calling/called mobile station
 *  to indication that the call is connected.
 * param index:  Connection index
 * return  void
 */
extern void watch_modem_call_response_connect(int32_t index);

/**
 * This message is sent to the calling/called mobile station
 *  to indication that the call is disconnected.
 * param index:  Connection index
 * return  void
 */
extern void watch_modem_call_response_disconnect(int32_t index);

/**
 * Indicates a disconnect for Call Clearing or a Call Failed for an outgoing call. (NO CARRIER)
 * param index:  Connection index
 * param isNoCarrier:  uint8_t
 * return  void
 */
extern void watch_modem_call_response_no_carrier(int32_t index, uint8_t isNoCarrier);

/**
 * Indicates ATD OK or cme error for an outgoing call.
 * param simId:  MMI_MODEM_SIM_ID
 * param isOk:  uint8_t,0 is OK,1 is cme error
 * return  void
 */
extern void watch_modem_call_response_dial(uint8_t isOk);

/**
 * Indicates the calling line identity of the calling party after every RING/+CRING. (+CLIP:)
 * +CLIP: <number>,<type>
 * param index:  Connection index
 * param clip:  MMI_MODEM_CALL_RESPONSE_CLIP *
 * return  void
 */
extern void watch_modem_call_response_clip(int32_t index, MMI_Modem_Call_Response_Clip_t *clip);
extern void watch_modem_call_response_clip_auto(int32_t index, MMI_Modem_Call_Response_Clip_t *clip);

/**
 * Indicates call waiting
 * param index:  Connection index
 * param ccwa:  MMI_MODEM_CALL_RESPONSE_CCWA *
 * return  void
 */
extern void watch_modem_call_response_ccwa(int32_t index, MMI_Modem_Call_Response_Ccwa_t *ccwa);

/**
 * Indicats the call state is changed
 *
 * param state:  MMI_MODEM_CALL_RESPONSE_STATE_CHANGE *
 * return  void
 */
extern void watch_modem_call_response_state_change(MMI_Modem_Call_Response_State_Change_t *state);

/**
 * Indicates call fail cause.
 * param ceer:  call fail cause
 * return  void
 */
extern void watch_modem_call_response_ceer(char *ceer);

/**
 * Signal bar indication
 * param (in) Bar:  MMI_MODEM_SIGNAL_BAR
 * return  void
 */
extern void watch_modem_signal_bar_ind(MMI_MODEM_SIGNAL_BAR Bar);

/**
 * Signal bar indication
 * param (in) void
 * return MMI_MODEM_SIGNAL_BAR
 */
extern MMI_MODEM_SIGNAL_BAR watch_modem_get_signal_bar_req(void);

/**
 * time auto-update indication
 * param (in) :
 * return  void
 */
extern void watch_modem_time_update_ind(MMI_Modem_Time_Info_t *TimeInfo);

/**
 * SIM status indicator
 * param (in) SimPresent: uint8_t
 * return  void
 */
extern void watch_modem_sim_status_ind(MMI_Modem_Sim_Status_t *SimStatus);

/**
 * check whether sim is present.
 * param (in) void
 * return  uint8_t: ture means sim present
 */
extern uint8_t watch_modem_sim_present_check_req(void);

/**
 * to do the sim soft reset.
 * param (in) void
 * return  void
 */
extern void  watch_modem_sim_soft_reset_req(void);

/*
 * Request current voice registration state
 * param (in) void
 * return  uint8_t: MMI_MODEM_VOICE_REG_STATE
 */
extern uint8_t watch_modem_voice_registration_state_req(void);

/**
 * Operator(short name) indicator reported when register at the first time
 * param (in) SimId: MMI_MODEM_SIM_ID
 * param (in) Operator: char*
 * return  void
 */
extern void watch_modem_operator_ind(MMI_MODEM_PLMN_RAT Rat);

/**
 * get Operator(short name)
 * param (in) SimId: MMI_MODEM_SIM_ID
 * param (in) Operator: char*
 * return  void
 */
extern void watch_modem_get_operator_req(MMI_MODEM_PLMN_RAT *Rat);

/**
 * get IMS register state
 * param (in) void
 * return  uint8_t
 */
extern uint8_t watch_modem_get_ims_reg_state_req(void);

/**
 * get GSM register state
 * param (in) void
 * return  uint8_t
 */
extern uint8_t watch_modem_get_gsm_reg_state_req(void);

/**
 * get UMTS register state
 * param (in) void
 * return  uint8_t
 */
extern uint8_t watch_modem_get_umts_reg_state_req(void);

/**
 * send new sms req
 * param (in) pSendSms: MMI_Modem_Send_Sms_Req_t
 * return  void
 */
extern void watch_modem_send_sms_req(MMI_Modem_Send_Sms_Req_t *pSendSms);

/**
 * send new sms cnf
 * param (in) pSendSmsCnf: MMI_Modem_Send_Sms_Cnf_t
 * return  void
 */
extern void watch_modem_send_sms_cnf(MMI_Modem_Send_Sms_Cnf_t *pSendSmsCnf);

/**
 * read sms info req
 * param (in) SmsBox: MMI_MODEM_SMS_OF_BOX
 * return  void
 */
extern void watch_modem_read_sms_info_req(MMI_MODEM_SMS_OF_BOX SmsBox);

/**
 * read sms info cnf
 * param (in) pReadSmsInfoCnf: MMI_Modem_Read_Sms_Info_Cnf_t
 * return  void
 */
extern void watch_modem_read_sms_info_cnf(MMI_Modem_Read_Sms_Info_Cnf_t *pReadSmsInfoCnf);

/**
 * read sms detail on sim req
 * param (in) pReadSmsReq: MMI_Modem_Read_Sms_Detail_On_Sim_Req_t
 * return  void
 */
extern void watch_modem_read_sms_detail_on_sim_req(
    MMI_Modem_Read_Sms_Detail_On_Sim_Req_t *pReadSmsReq);

/**
 * read sms detail on sim cnf
 * param (in) pReadSmsCnf: MMI_Modem_Read_Sms_Detail_On_Sim_Cnf_t
 * return  void
 */
extern void watch_modem_read_sms_detail_on_sim_cnf(
    MMI_Modem_Read_Sms_Detail_On_Sim_Cnf_t *pReadSmsCnf);

/**
 * report new sms ind
 * param (in) pRepNewSms:MMI_Modem_Report_New_Sms_Ind_t
 * return  void
 */
extern void watch_modem_rep_new_sms_ind(MMI_Modem_Report_New_Sms_Ind_t *pRepNewSms);

/**
 * report pin status change ind
 * param (in) pinreqtype:MMI_MODEM_PIN_STATUS_TYPE
 * return  void
 */
extern void watch_modem_pin_status_change_ind(MMI_MODEM_PIN_STATUS_TYPE pinreqtype);

/**
 * Request modem adpter to try PIN code
 * param (in) MMI_MODEM_PIN_TYPE pintype
 *         char *pin
 * return  uint8_t :Retry Left: MMI_MODEM_PIN_CHECK_OK when check OK
 */
extern uint8_t watch_modem_request_enter_sim_pin(MMI_MODEM_PIN_TYPE pintype,char *pin);

/**
 * Request modem adpter to change PIN code
 * param (in)   MMI_MODEM_PIN_TYPE pintype
 *          char *pin,char *oldpin
 * return  uint8_t :Retry Left: MMI_MODEM_PIN_CHECK_OK when check OK
 */
extern uint8_t watch_modem_change_sim_pin(MMI_MODEM_PIN_TYPE pintype,char *pin,char *oldpin);

/**
 * Query PIN lock information
 * param (in)   MMI_MODEM_PIN_TYPE pintype,
 *              pinlockinfo: MMI_Modem_Query_Pin_Lock_Info_t *,return the pin lock info
 * return  void
 */
extern void watch_modem_query_pin_lock_info(MMI_MODEM_PIN_TYPE pintype,MMI_Modem_Query_Pin_Lock_Info_t *pinlockinfo);

/**
 * Enable or disable PIN lock
 * param (in)   MMI_MODEM_PIN_TYPE pintype
 *             pinenable:uint8_t 1 is Enable,0 is DISABLE
 *            char *pin
 * return  uint8_t :Result: MMI_MODEM_PIN_CHECK_OK means set OK ,otherwise means the retry-left times
 */
extern uint8_t watch_modem_enable_or_disable_pin_lock(MMI_MODEM_PIN_TYPE pintype,uint8_t pinenable,char *pin);

/**
 * get IMSI of sim
 * param (out)  imsi: char*
 * return  void
 */
extern void watch_modem_get_imsi_req(char * imsi);

/**
 * get IMEI of watch
 * param (out) imei: char*
 * return  void
 */
extern void watch_modem_get_imei_req(char * imei);

/**
 * get baseband version of watch
 * param (out) baseband_ver: char*
 * return  void
 */
extern void watch_modem_get_baseband_ver_req(char * baseband_ver);

/**
 * init modem adaptor
 * param (in) void
 * return  void
 */
extern void watch_modem_adp_init_req(void);

/**
 * indicate srceen state to modem
 * param (in) void
 * return  void
 */
extern void watch_modem_screen_state_req(int state);
/**
 * power up request
 * param (in) void
 * return  void
 */
void watch_modem_power_up_req(void);
/**
 * power up confirm
 * param (in) void
 * return  void
 */
void watch_modem_power_up_cnf(void);

/**
 * query current call active status
 * param (in) void
 * return  bool
 */
extern bool watch_modem_call_get_call_active_status(void);

/**
 * set band information for engineer mode
 * param (in) bandmode: uint8_t
 * param (in) gsmband: uint8_t
 * param (in) ltebandh: uint16_t
 * param (in) ltebandl: uint32_t
 * return  void
 */
extern void watch_modem_set_band_req(uint8_t bandmode, uint8_t gsmband, uint16_t ltebandh, uint32_t ltebandl);

/**
 * process the lte cells information
 * param (in) MMI_Modem_Lte_Cells_t *pltecells
 * return  void
 */
extern void watch_modem_lte_cells_info_process(MMI_Modem_Lte_Cells_t *pltecells);

/**
 * process the gsm cells information
 * param (in) MMI_Modem_Gsm_Cells_t *pgsmcells
 * return  void
 */
extern void watch_modem_gsm_cells_info_process(MMI_Modem_Gsm_Cells_t *pgsmcells);

/**
 * get cell info req
 * param (in) void
 * return  void
 */
extern void watch_modem_adp_get_cell_info_req(void);

extern uint8_t watch_modem_adp_get_ril_ready_state_req(void);

/**
 * get modem wake up reason
 * param (in) void
 * return  uint8_t wakeup reason
 */
extern void watch_modem_call_forward_set_req(MMI_Modem_Call_Forward_Info_t * CallForwardInfo);

extern MMI_Modem_Call_Forward_Info_t * watch_modem_call_forward_query_req(MMI_MODEM_CALL_FORWARD_TYPE CallForwardType);

extern void watch_modem_call_wait_set_req(uint8_t callwait_onoff_flg);

extern uint8_t watch_modem_call_wait_query_req(void);

/**
 * set volte state
 * param (in) state: uint8_t
 * return  void
 */
extern void watch_modem_set_volte_state_req(uint8_t state);

/**
 * Get the status of SIM phonebook
 * param (in) void
 * return  void
 */
extern void watch_modem_get_phonebook_status_req(void);

/**
 * Write the contact into SIM phonebook
 * param (in) index: if MMI_MODEM_CONTACT_INDEX_INVALID, add the new contact into sim phonebook.
 *                   otherwise delete the contact with the index in sim phonebook, name and number are omit
 * param (in) name: char *, string,
 *                  support empty name,
 *                  max 6 characters if including one chinese character at lest,
 *                  otherwise 14 charaters for example "1234567890abcd";
 * param (in) number: char *, string,
 *                    not support empty number,
 *                    max 20 characters
 * return  bool: parameter error if false, otherwise true
 */
extern bool watch_modem_write_phonebook_req(uint16_t index, char * name, char * number);

/**
 * Read the contacts from SIM phonebook
 * param (in) start_index: the starting index in SIM phonebook for the contact to read
 * param (in) end_index: the end index in SIM phonebook for the contact to read
 * return  bool: parameter error if false, otherwise true
 */
extern bool watch_modem_read_phonebook_req(uint16_t start_index, uint16_t end_index);

/**
 * ims vops ind
 * param (in) uint8_t display_sendsms_flg//0:display not sendsms,1:display send sms
 * return  void
 */
extern void watch_modem_ims_vops_ind(uint8_t display_sendsms_flg);

/**
 * get calibration info req
 * param (in) void
 * return  void
 */
extern void watch_modem_adp_get_calibration_info_req(void);
extern void watch_modem_adp_get_onegocali_info_req(char* cmd,uint16 lens);

/**
 * get sn info req
 * param (in) void
 * return  void
 */
extern void watch_modem_adp_get_sn_info_req(void);

/**
 * process the calibration result
 * param (in) result: uint8_t
 * return  void
 */
extern void watch_modem_calibration_result_process(uint8_t result);

/**
* process the lte cells information
* param (in) psn: int8_t*
* return  void
*/
extern void watch_modem_sn_info_process(char *psn);

/**
 * This is used to start  video call
 * param calltype: MODEM_VIDEO_CALL_TYPE
 * param pContactUri: char*
 * return  UINT32
 */
extern int32_t wach_modem_video_call_dial_req(MODEM_VIDEO_CALL_TYPE calltype, char * pContactUri);

/**
 * This is used to control video call action
 * param op: UINT16
 * param vidType: UINT16
 * return  UINT32
 */
extern uint32_t watch_modem_video_call_action_req(MODEM_VIDEO_CALL_ACTION op,
        MODEM_VIDEO_CALL_BARESIPVIDMODE vidType);

/**
 * This is used to set local preview
 * param stat:
 * return  void
 */
extern void watch_modem_video_call_set_local_preview_req(bool localpreview);

/**
 * This is used to get the reg state of video call.
 * param stat:  reg status
 * return  void
 */
extern uint8_t watch_modem_video_call_get_reg_stat_req(void);

/**
 * This is used to get SIM id
 * param (in) void
 * return  void
 */
extern MMI_MODEM_SIM_ID watch_modem_get_sim_id(void);

/**
 * This is used to set SIM id
 * param (in) SimId: MMI_MODEM_SIM_ID
 * return  void
 */
extern void watch_modem_set_sim_id(MMI_MODEM_SIM_ID SimId);

/**
 * This is used to switch SIM
 * param (in) SimTo: SIM id switch to
 * return  void
 */
extern void watch_modem_switch_to_sim(MMI_MODEM_SIM_ID SimTo);

/**
 * This is used to set connection switch
 * param (in) OnOff: BOOL
 * return  void
 */
extern void watch_modem_set_connection_switch(BOOL OnOff);

/**
 * This is used to get connection switch
 * param (in) void
 * return  BOOL
 */
extern BOOL watch_modem_get_connection_switch(void);

/**
 * This is used to set data roaming
 * param (in) OnOff: BOOL
 * return  void
 */
extern void watch_modem_set_data_roaming(BOOL OnOff);

/**
 * This is used to get data roaming
 * param (in) void
 * return  BOOL
 */
extern BOOL watch_modem_get_data_roaming(void);

/**
 * This is used to get default connection APN name
 * param (in) Name: buffer to get apn name string
 * param (in) NameLen: buffer len of name
 * return 0: get default name successfully;  -1: get default name failed
 */
extern int watch_modem_get_default_apn_name(char *Name, int NameLen);

/**
 * This is used to get default IP type
 * param (in) void
 * return 0-IPV4V6; 1-IPV4; 2-IPV6
 */
extern int watch_modem_get_default_ip_type(void);

/**
 * This is used to set apn info
 * param (in) ApnInfo: MMI_Modem_Apn_Info_t *
 * return void
 */
void watch_modem_set_apn_info(MMI_Modem_Apn_Info_t *ApnInfo);

/**
 * This is used to reset apn to default
 * param (in) void
 * return void
 */
extern void watch_modem_reset_apn(void);

/**
 * This is used to config apn
 * param (in) Apn: char *
 * param (in) IpType: 0: IPV4V6;  1: IPV4;  2: IPV6
 * return void
 */
extern void watch_modem_config_apn(char *Apn, UINT8 IpType);

/**
 * This is used to send SMS to operator to open VOLTE
 * param (in) void
 * return void
 */
extern void watch_modem_open_volte(void);
#if USE_LV_WATCH_ALIPAY != 0
extern bool watch_modem_get_network_connect_status(void);

/**
 * check whether sim or esim is present.
 * param (in) void
 * return  bool: true means sim or esim present
 */
extern bool watch_modem_sim_or_esim_present_check_req(void);
#endif
#ifdef __cplusplus
}
#endif /* __cpluscplus */

#endif /* end of _WATCH_MODEMADP_INTERFACE_H_ */
