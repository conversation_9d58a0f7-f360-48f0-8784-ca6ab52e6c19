/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "SUPL-REPORT"
 * 	found in "supl202.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_PositionData_ULP_H_
#define	_PositionData_ULP_H_


#include <asn_application.h>

/* Including external dependencies */
#include "Position.h"
#include "PosMethod.h"
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* Forward declarations */
struct GNSSPosTechnology;
struct GANSSsignalsInfo;

/* PositionData-ULP */
typedef struct PositionData_ULP {
	Position_t	 position;
	PosMethod_t	*posMethod	/* OPTIONAL */;
	struct GNSSPosTechnology	*gnssPosTechnology	/* OPTIONAL */;
	struct GANSSsignalsInfo	*ganssSignalsInfo	/* OPTIONAL */;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} PositionData_ULP_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_PositionData_ULP;

#ifdef __cplusplus
}
#endif

/* Referred external types */
#include "GNSSPosTechnology.h"
#include "GANSSsignalsInfo.h"

#endif	/* _PositionData_ULP_H_ */
#include <asn_internal.h>
