/******************************************************************************
 * *(C) Copyright 2008 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#include <stdio.h>
#include <string.h>
#include "../libatchannel/atchannel.h"
#include "../../board.h"

#include "asr-ril.h"
#include "modem-device.h"
#include "ril-requestdatahandler.h"
#include "ril-mm.h"
#include "ril-ps.h"
#include "ril-sim.h"

#if defined(__XF_LCD_SIZE_240X280__)
void (*nfc_swipe_card_after_shutdown_p)(void)=NULL;
#endif

namespace watch_ril {

#define FLAG_NW_IMS_VOPS 0x8

// max operator id length, need to align with CP side
#define MM_MAX_OPER_ID_LENGTH 32

#define MM_NETOP_ID_FORMAT_ALPHA_LONG 0
#define MM_NETOP_ID_FORMAT_ALPHA_SHORT 1

#define NR_CSQ_BUFFER 10

#define DEFAULT_MTU_SIZE 1500

#define VALUE_NUM_PER_UPLMN 3
#define ACT_GSM 0
#define ACT_GSM_COMPACT 1
#define ACT_3G 2
#define ACT_LTE 3
#define IS_ACT_VALID 1

#define PROP_IMS_CONFIG "persist.radio.ims.config"
#define PROP_DOMAIN_SETTING "persist.sys.domain.sel"
#define PROP_DOMAIN_SETTING2 "persist.sys.domain.sel2"

typedef RIL_SignalStrength_v10 RIL_SignalStrength_mm;
static void init_signal_strength(RIL_SignalStrength_mm & result)
{
    result.LTE_SignalStrength.timingAdvance = INT_MAX;
    result.TD_SCDMA_SignalStrength.rscp = INT_MAX;
}

struct RegState {
    RegState() : stat(-1), lac(-1), cid(-1), act(-1), rejectCause(-1), psc(-1) {};
    int stat;
    int lac;
    int cid;
    int act;
    int rejectCause;
    int psc; //only valid for CREG
};

struct OperInfo {
    OperInfo() : mode(-1)
    {
        operLongStr[0] = '\0';
        operShortStr[0] = '\0';
        operNumStr[0] = '\0';
    };
    int mode;
    char operLongStr[MM_MAX_OPER_ID_LENGTH + 1];
    char operShortStr[MM_MAX_OPER_ID_LENGTH + 1];
    char operNumStr[MM_MAX_OPER_ID_LENGTH + 1];
};

struct SignalQuality {
    SignalQuality() : rssi(99), ber(99) {};
    int rssi;
    int ber;
};

struct ExtendedSignalQuality {
    ExtendedSignalQuality() : rxlev(99), ber(99), rscp(255), ecno(255), rsrq(255), rsrp(255) {};
    int rxlev;
    int ber;
    int rscp;
    int ecno;
    int rsrq;
    int rsrp;
};

typedef struct MtuTable_s {
    const char * mcc_mnc;
    int mtuSize;
} MtuTable;

static RegState sCregState[SIM_COUNT];
static RegState sCgregState[SIM_COUNT];
static RegState sCeregState[SIM_COUNT];
static OperInfo sOperInfo[SIM_COUNT];
static int sScreenState[SIM_COUNT] = {0};   //default screen state = OFF, it will turn to ON after RIL is initialized
static SignalQuality sCSQ[SIM_COUNT];
static ExtendedSignalQuality sCESQ[SIM_COUNT];
#define MAX_NEIGHBORING_CELLS 6 //max neighboring cell number is set as 6 in defualt
static RIL_NeighboringCell sNeighboringCell[SIM_COUNT][MAX_NEIGHBORING_CELLS];
static int sCellNumber[SIM_COUNT] = {0};
struct timespec lastNitzTimeReceived;
char lastNitzTime[21];

static int gSignalIntervalMode[SIM_COUNT];
static bool gPrevOperCaching = false;
static char gPrevOperNumStr[SIM_COUNT][MM_MAX_OPER_ID_LENGTH + 1];
int parse_cesq(char ** line, int * rxlev, int * ber, int * rscp, int * ecno, int * rsrq, int * rsrp);
int process_rssi(int rxlev, int rscp);
extern bool isDualLEnabled();

#define MM_NETOP_ID_FORMAT_ALPHA_LONG 0
#define MM_NETOP_ID_FORMAT_ALPHA_SHORT 1

#define NR_CSQ_BUFFER 10

#define DEFAULT_MTU_SIZE 1500

#define VALUE_NUM_PER_UPLMN 3
#define ACT_GSM 0
#define ACT_GSM_COMPACT 1
#define ACT_3G 2
#define ACT_LTE 3
#define IS_ACT_VALID 1

#define PROP_IMS_CONFIG "persist.radio.ims.config"
#define PROP_DOMAIN_SETTING "persist.sys.domain.sel"
#define PROP_DOMAIN_SETTING2 "persist.sys.domain.sel2"

/*
 * RIL_PreferredNetworkType
 *
 * PREF_NET_TYPE_GSM_WCDMA                  = 0, GSM/WCDMA (WCDMA preferred)
 * PREF_NET_TYPE_GSM_ONLY                   = 1, GSM only
 * PREF_NET_TYPE_WCDMA                      = 2, WCDMA
 * PREF_NET_TYPE_GSM_WCDMA_AUTO             = 3, GSM/WCDMA (auto mode, according to PRL)
 * PREF_NET_TYPE_CDMA_EVDO_AUTO             = 4, CDMA and EvDo (auto mode, according to PRL)
 * PREF_NET_TYPE_CDMA_ONLY                  = 5, CDMA only
 * PREF_NET_TYPE_EVDO_ONLY                  = 6, EvDo only
 * PREF_NET_TYPE_GSM_WCDMA_CDMA_EVDO_AUTO   = 7, GSM/WCDMA, CDMA, and EvDo (auto mode, according to PRL)
 * PREF_NET_TYPE_LTE_CDMA_EVDO              = 8, LTE, CDMA and EvDo
 * PREF_NET_TYPE_LTE_GSM_WCDMA              = 9, LTE, GSM/WCDMA
 * PREF_NET_TYPE_LTE_CMDA_EVDO_GSM_WCDMA    = 10, LTE, CDMA, EvDo, GSM/WCDMA
 * PREF_NET_TYPE_LTE_ONLY                   = 11, LTE only
 * PREF_NET_TYPE_LTE_WCDMA                  = 12, LTE/WCDMA
 * PREF_NET_TYPE_TD_SCDMA_ONLY              = 13, TD-SCDMA only
 * PREF_NET_TYPE_TD_SCDMA_WCDMA             = 14, TD-SCDMA and WCDMA
 * PREF_NET_TYPE_TD_SCDMA_LTE               = 15, TD-SCDMA and LTE
 * PREF_NET_TYPE_TD_SCDMA_GSM               = 16, TD-SCDMA and GSM
 * PREF_NET_TYPE_TD_SCDMA_GSM_LTE           = 17, TD-SCDMA,GSM and LTE
 * PREF_NET_TYPE_TD_SCDMA_GSM_WCDMA         = 18, TD-SCDMA, GSM/WCDMA
 * PREF_NET_TYPE_TD_SCDMA_WCDMA_LTE         = 19, TD-SCDMA, WCDMA and LTE
 * PREF_NET_TYPE_TD_SCDMA_GSM_WCDMA_LTE     = 20, TD-SCDMA, GSM/WCDMA and LTE
 * PREF_NET_TYPE_TD_SCDMA_GSM_WCDMA_CDMA_EVDO_AUTO = 21, TD-SCDMA,EvDo,CDMA,GSM/WCDMA
 * PREF_NET_TYPE_TD_SCDMA_LTE_CDMA_EVDO_GSM_WCDMA  = 22 TD-SCDMA/LTE/GSM/WCDMA, CDMA, and EvDo
 */


/*
 * AT*BAND network Mode
 *
 * 0 for GSM only
 * 1 for WCDMA only
 * 2 for GSM/WCDMA (auto mode)
 * 3 for GSM/WCDMA (GSM preferred)
 * 4 for GSM/WCDMA (WCDMA preferred)
 */

/*
int NETWORK_MODE_WCDMA_PREF                           = 0;         4
int NETWORK_MODE_GSM_ONLY                             = 1;         0
int NETWORK_MODE_WCDMA_ONLY                           = 2;         1
int NETWORK_MODE_GSM_UMTS                             = 3;         2
int NETWORK_MODE_CDMA                                 = 4;
int NETWORK_MODE_CDMA_NO_EVDO                         = 5;
int NETWORK_MODE_EVDO_NO_CDMA                         = 6;
int NETWORK_MODE_GLOBAL                               = 7;
int NETWORK_MODE_LTE_CDMA_EVDO                        = 8;
int NETWORK_MODE_LTE_GSM_WCDMA                        = 9;         15
int NETWORK_MODE_LTE_CMDA_EVDO_GSM_WCDMA              = 10;
int NETWORK_MODE_LTE_ONLY                             = 11;        5
int NETWORK_MODE_LTE_WCDMA                            = 12;        11
int NETWORK_MODE_TDSCDMA_ONLY                         = 13;        1
int NETWORK_MODE_TDSCDMA_WCDMA                        = 14;
int NETWORK_MODE_LTE_TDSCDMA                          = 15;        11
int NETWORK_MODE_TDSCDMA_GSM                          = 16;        4
int NETWORK_MODE_LTE_TDSCDMA_GSM                      = 17;        15
int NETWORK_MODE_TDSCDMA_GSM_WCDMA                    = 18;
int NETWORK_MODE_LTE_TDSCDMA_WCDMA                    = 19;
int NETWORK_MODE_LTE_TDSCDMA_GSM_WCDMA                = 20;
int NETWORK_MODE_TDSCDMA_CDMA_EVDO_GSM_WCDMA          = 21;
int NETWORK_MODE_LTE_TDSCDMA_CDMA_EVDO_GSM_WCDMA      = 22;
*/
/* map ril request network type to AT*BAND network mode */
static const int RILNetworkTypeTostarBandNetworkType[] = {
    4,  /* GSM/WCDMA (WCDMA preferred) */
    0,  /* GSM only */
    1,  /* WCDMA  */
    2,  /* GSM/WCDMA (auto mode, according to PRL) */
    -1, /* CDMA and EvDo (auto mode, according to PRL) */
    -1, /* CDMA only */
    -1, /* EvDo only */
    -1, /* GSM/WCDMA, CDMA, and EvDo (auto mode, according to PRL) */
    -1, /* LTE, CDMA and EvDo */
    15, /* LTE/GSM/WCDMA (LTE preferred) */
    -1, /* LTE, CDMA, EvDo, GSM/WCDMA */
    5,  /* LTE only */
    11, /* LTE/WCDMA (LTE preferred)*/
    1,  /* TD-SCDMA only */
    -1, /* TD-SCDMA and WCDMA */
    11, /* TD-SCDMA and LTE (LTE preferred)*/
    4,  /* TD-SCDMA and GSM (TD-SCDMA preferred)*/
    15, /* TD-SCDMA,GSM and LTE (LTE preferred)*/
    -1, /* TD-SCDMA, GSM/WCDMA */
    -1, /* TD-SCDMA, WCDMA and LTE */
    -1, /* TD-SCDMA, GSM/WCDMA and LTE */
    -1, /* TD-SCDMA,EvDo,CDMA,GSM/WCDMA*/
    -1, /* TD-SCDMA/LTE/GSM/WCDMA, CDMA, and EvDo */
};


/* map AT*BAND network mode to ril request network type */
static const int starBandNetworkTypeToRILNetworkTypeLtg[] = {
    PREF_NET_TYPE_GSM_ONLY,                 /* GSM only */
    PREF_NET_TYPE_TD_SCDMA_ONLY,            /* TD-SCDMA only */
    -1,                                     /* TD-SCDMA and GSM */
    -1,                                     /* TD-SCDMA/GSM (GSM preferred) */
    PREF_NET_TYPE_TD_SCDMA_GSM,             /* TD-SCDMA/GSM (TD-SCDMA preferred) */
    PREF_NET_TYPE_LTE_ONLY,                 /* LTE only */
    -1,                                     /* LTE/GSM (auto mode)*/
    -1,                                     /* LTE/GSM  (GSM preferred)*/
    -1,                                     /* LTE/GSM  (LTE preferred)*/
    -1,                                     /* LTE/TD-SCDMA (auto mode)*/
    -1,                                     /* LTE/TD-SCDMA (WCDMA preferred) */
    PREF_NET_TYPE_TD_SCDMA_LTE,             /* LTE/TD-SCDMA (LTE preferred)*/
    -1,                                     /* LTE/TD-SCDMA/GSM (auto mode) */
    -1,                                     /* LTE/TD-SCDMA/GSM (GSM preferred) */
    -1,                                     /* LTE/TD-SCDMA/GSM (TD-SCDMA preferred) */
    PREF_NET_TYPE_TD_SCDMA_GSM_LTE,         /* LTE/TD-SCDMA/GSM (LTE preferred) */
    -1,                                     /* LTE/GSM (dual link)*/
    -1,                                     /* LTE/WCDMA (dual link)*/
    -1,                                     /* LTE/GSM/WCDMA (dual link)*/
};

static const int starBandNetworkTypeToRILNetworkTypeLwg[] = {
    PREF_NET_TYPE_GSM_ONLY,                 /* GSM only */
    PREF_NET_TYPE_WCDMA,                    /* WCDMA only */
    PREF_NET_TYPE_GSM_WCDMA_AUTO,           /* GSM/WCDMA (auto mode) */
    -1,                                     /* GSM/WCDMA (GSM preferred) */
    PREF_NET_TYPE_GSM_WCDMA,                /* GSM/WCDMA (WCDMA preferred) */
    PREF_NET_TYPE_LTE_ONLY,                 /* LTE only */
    -1,                                     /* LTE/GSM (auto mode)*/
    -1,                                     /* LTE/GSM  (GSM preferred)*/
    -1,                                     /* LTE/GSM  (LTE preferred)*/
    -1,                                     /* LTE/WCDMA (auto mode)*/
    -1,                                     /* LTE/WCDMA (WCDMA preferred) */
    PREF_NET_TYPE_LTE_WCDMA,                /* LTE/WCDMA (LTE preferred)*/
    -1,                                     /* LTE/GSM/WCDMA (auto mode) */
    -1,                                     /* LTE/GSM/WCDMA (GSM preferred) */
    -1,                                     /* LTE/GSM/WCDMA (WCDMA preferred) */
    PREF_NET_TYPE_LTE_GSM_WCDMA,            /* LTE/GSM/WCDMA (LTE preferred) */
    -1,                                     /* LTE/GSM (dual link)*/
    -1,                                     /* LTE/WCDMA (dual link)*/
    -1,                                     /* LTE/GSM/WCDMA (dual link)*/
};

static void getNetworkTypeAndSize(const int ** networkType, int * size)
{
    BASEBAND_TYPE bandType = getBaseBandType();

    if(bandType == BASEBAND_TD) {
        *networkType = starBandNetworkTypeToRILNetworkTypeLtg;
        *size = (int)sizeof(starBandNetworkTypeToRILNetworkTypeLtg) / (int)sizeof(starBandNetworkTypeToRILNetworkTypeLtg[0]);
    } else {
        *networkType = starBandNetworkTypeToRILNetworkTypeLwg;
        *size = (int)sizeof(starBandNetworkTypeToRILNetworkTypeLwg) / (int)sizeof(starBandNetworkTypeToRILNetworkTypeLwg[0]);
    }
}

const MtuTable mtu_size_table[] = {
    {"460", 1400},    //for china
    {"311390", 1428}, //for VZW
    {"311480", 1428}, //for VZW
    {"311489", 1428}, //for VZW
    {"310028", 1428}, //for VZW
    {"310480", 1420}, //for ATT
};

static inline int updateValue(int * p, int value);
static RIL_RadioTechnology convertActToRilRadioTech(int act);

int getScreenState(RIL_SOCKET_ID socketId)
{
    return sScreenState[socketId];
}

int getMtuSize(RIL_SOCKET_ID socketId)
{
    int i;
    int mtuSize = DEFAULT_MTU_SIZE;
    for(i = 0; i < (int)sizeof(mtu_size_table) / (int)sizeof(mtu_size_table[0]); i++) {
        if(strncmp(sOperInfo[socketId].operNumStr, mtu_size_table[i].mcc_mnc, strlen(mtu_size_table[i].mcc_mnc)) == 0) {
            mtuSize = mtu_size_table[i].mtuSize;
            RLOGI("%s, find %s(%zd) in mtuSizeTable, mtuSize=%d",
                  __FUNCTION__, sOperInfo[socketId].operNumStr, strlen(mtu_size_table[i].mcc_mnc), mtuSize);
            return mtuSize;
        }
    }
    return mtuSize;
}

/* Return value indicate whether network is in china
* -1: unknown
*   1: in china
*   0: not in china
*/
int isNetworkInChina(RIL_SOCKET_ID socketId)
{
    if(sOperInfo[socketId].operNumStr[0] == '\0')
        return -1;
    return strncmp(sOperInfo[socketId].operNumStr, "460", 3) == 0;
}

/* Return value indicate whether network is in china
* -1: unknown
*   1: in china
*   0: not in china
*/
static int isPrevNetworkInChina()
{
    int ret = -1;
    for(int i = 0 ; i < SIM_COUNT ; i++) {
        if(gPrevOperNumStr[i][0] == '\0')
            ret = -1;
        else {
            if(strncmp(gPrevOperNumStr[i], "460", 3) == 0) {
                ret = 1;
                break;
            } else {
                ret = 0;
            }
        }
    }
    return ret;
}

int isNetworkOfChinaMobile(RIL_SOCKET_ID socketId)
{
    if(isNetworkInChina(socketId) == 1) {
        char * mnc = sOperInfo[socketId].operNumStr + 3;
        return mnc[0] == '0' && (mnc[1] == '0' || mnc[1] == '2' || mnc[1] == '7' || mnc[1] == '8');
    }
    return 0;
}

int isNetworkOfVzW(RIL_SOCKET_ID socketId)
{
    return ((strncmp(sOperInfo[socketId].operNumStr, "311390", 6) == 0)
            || (strncmp(sOperInfo[socketId].operNumStr, "311480", 6) == 0)
            || (strncmp(sOperInfo[socketId].operNumStr, "311489", 6) == 0)
            || (strncmp(sOperInfo[socketId].operNumStr, "310028", 6) == 0));
}

static inline int IsStateInService(int state)
{
    return (state == 1 || state == 5);
}

static int checkForSmsOnlyMode(int iState)
{
    char smsOnlyMode[512];
    property_get("persist.radio.smsonly", smsOnlyMode, "0");
    if(strcmp(smsOnlyMode, "1") == 0) {
        return (iState == 6) ? 1 : ((iState == 7) ? 5 : iState);
    }

    return iState;
}
/* Parse  AT reply of +CREG or +CGREG or +CEREG
 * Output: responseInt[0] : <stat>
          responseInt[1] : <lac>
          responseInt[2] : <cid>
          responseInt[3] : <AcT>
          responseInt[4] : <rejectCause>
          responseInt[5] : <psc>
 */
int parseResponseWithMoreInt(ATResponse * response, int responseInt[], int * pNum, int isDollarCreg)
{
    int err = 0, num;
    char * line;

    if(response->success == 0 || response->p_intermediates == NULL) {
        goto error;
    }

    line = response->p_intermediates->line;
    err = at_tok_start(&line);
    if(err < 0) goto error;

    num = 0;
    while(at_tok_hasmore(&line)) {
        if(*line == ',') {
            line++;
            responseInt[num] = -1;
        } else if(num == 2 || num == 3 || (isDollarCreg && (num == 5))) { //for <lac>,<cid>,<psc>
            err = at_tok_nexthexint(&line, &(responseInt[num]));
        } else {
            err = at_tok_nextint(&line, &(responseInt[num]));
        }
        if(err < 0) goto error;
        num++;
    }

    /* AT Reply format: +CREG: <n>,<stat>[,<lac>,<ci>[,<AcT>]] (Take +CREG: as example in following comments)   */
    switch(num) {
        case 2: { /* +CREG/$CREG/+CGREG/CEREG: <n>, <stat> */
            /* responseInt[1] is stat, copy to responseInt[0]. <lac> and <ci> are unavailable, <AcT> is unknown  */
            responseInt[0] = responseInt[1];
            responseInt[1] = -1;
            responseInt[2] = -1;
            responseInt[3] = -1;
            responseInt[4] = -1;
            responseInt[5] = -1;

            break;
        }
        case 5: /* +CREG/+CEREG: <n>, <stat>, <lac>, <cid>, <AcT> | $CREG: <n>, <stat>, <lac>, <cid>, <AcT>,*/
        case 6: { /* +CGREG: <n>, <stat>, <lac>, <cid>, <AcT>, <rac>  | $CREG: <n>, <stat>, <lac>, <cid>, <AcT>, <psc> */
            /* Need to change the place */
            responseInt[0] = responseInt[1];
            responseInt[1] = responseInt[2];
            responseInt[2] = responseInt[3];
            responseInt[3] = responseInt[4];
            responseInt[4] = -1;
            if(!isDollarCreg || num == 5)
                responseInt[5] = -1;
            break;
        }
        case 7: /* +CREG/+CEREG: <n>, <stat>, <lac>, <cid>, <AcT>, <cause type>, <reject cause> */
        case 8: /* +CGREG: <n>, <stat>, <lac>, <cid>, <AcT>, <rac>, <cause type>, <reject cause>
            | $CREG: <n>, <stat>, <lac>, <cid>, <AcT>, <psc>, <cause type>, <reject cause> */
        {
            /* Need to change the place */
            responseInt[0] = responseInt[1];
            responseInt[1] = responseInt[2];
            responseInt[2] = responseInt[3];
            responseInt[3] = responseInt[4];
            if(num == 7) {
                responseInt[4] = responseInt[6];
                responseInt[5] = -1;
            } else {
                responseInt[4] = responseInt[7];
                if(!isDollarCreg)
                    responseInt[5] = -1;
            }

            break;
        }

        default:
            err = -1;
            goto error;
    }

    *pNum = num;

    return 0;

error:
    return err;
}

static int CESQSupport[SIM_COUNT] = {0};
void setCESQSupport(int value, RIL_SOCKET_ID socketId)
{
    CESQSupport[socketId] = value;
}

int getCESQSupport(RIL_SOCKET_ID socketId)
{
    return CESQSupport[socketId];
}

static int convertRsrqToDbm(int n)
{
    if(n >= 0 && n <= 34)
        return 20 - n / 2;
    else
        return INT_MAX;
}

static int convertRsrpToDbm(int n)
{
    if(n >= 0 && n <= 97)
        return 141 - n;
    else
        return INT_MAX;
}

static int convertRxlevToDbm(int n)
{
    if(n >= 0 && n <= 64)
        return -113 + n;
    else
        return INT_MAX;
}

/*
static int convertEcnoToDB(int n)
{
    if( n >= 0 && n <= 49)
        return -24 + n / 2;
    else
        return 0;
}
*/

static int convertRscpToDbm(int n)
{
    if(n >= 0 && n <= 96)
        return -121 + n;
    else
        return INT_MAX;
}

static int convertDbmToRssi(int d)
{
    if(d == INT_MAX)
        return 99;
    else if(d < -113)
        return 0;
    else if(d > -51)
        return 31;
    else
        return (113 + d) / 2;
}

static void checkCereg()
{
    ATResponse * p_response = NULL;
    int err;
    char * line;
    int n;

    err = at_send_command_singleline("AT+CEREG?", "+CEREG:", &p_response);
    if(err < 0 || p_response->success == 0 || p_response->p_intermediates == NULL) { //not supported, send cgreg
        RLOGD("%s: AT+CEREG not supported, send cgreg", __FUNCTION__);
        goto exit;
    } else {
        line = p_response->p_intermediates->line;
        err = at_tok_start(&line);
        if(err < 0) goto exit;

        err = at_tok_nextint(&line, &n);
        if(err < 0) goto exit;
        //after airplane mode, IMSD will send AT+CEREG=0, so here we need initialize
        if(n == 0) {
            /*  EPS registration events */
            at_response_free(p_response);
            p_response = NULL;
            err = at_send_command("AT+CEREG=3", &p_response);
            if(err < 0 || p_response->success == 0) {
                at_response_free(p_response);
                p_response = NULL;
                err = at_send_command("AT+CEREG=2",  &p_response);
                if(err < 0 || p_response->success == 0) {
                    at_send_command("AT+CEREG=1", NULL);
                }
            }
        }
    }
exit:
    at_response_free(p_response);
    p_response = NULL;
    return;
}

/* Report to upper layer about signal strength (unsol msg of  +CSQ: ) when screen state is ON */
void reportSignalStrength(void * param, RIL_SOCKET_ID socketId)
{
    UNUSED(param);
    //RLOGE("%s: sScreenState=%d\n", __FUNCTION__, sScreenState[socketId]);
    if(sScreenState[socketId]) {
        RIL_SignalStrength_mm result;
        memset(&result, 0, sizeof(result));
        init_signal_strength(result);
        result.GW_SignalStrength.signalStrength = sCSQ[socketId].rssi;
        result.GW_SignalStrength.bitErrorRate = sCSQ[socketId].ber;
        // set LTE signal strength
        // to match framework/base/telephony/java/android/telephony/SignalStrength.java
        result.LTE_SignalStrength.signalStrength = 99;
        if (!IsGSMAttached(socketId)) {
            if(IsLTEAttached(socketId)) {
                result.LTE_SignalStrength.signalStrength = result.GW_SignalStrength.signalStrength;
                result.GW_SignalStrength.signalStrength = 99;
                result.LTE_SignalStrength.rsrq = convertRsrqToDbm(sCESQ[socketId].rsrq);
                result.LTE_SignalStrength.rsrp = sCESQ[socketId].rsrp;
            //RLOGE("%s, lte is  attached, rsrq %d, rsrp %d, simID = %d \n", __FUNCTION__, sCESQ[socketId].rsrq, sCESQ[socketId].rsrp, socketId);
            } else {
            //RLOGE("%s, lte is not attached, set rsrp and rsrq to INT_MAX, simID = %d \n", __FUNCTION__, socketId);
                result.GW_SignalStrength.signalStrength = 99;
                result.LTE_SignalStrength.rsrq = INT_MAX;
                result.LTE_SignalStrength.rsrp = INT_MAX;
            }
        }
        result.LTE_SignalStrength.rssnr = INT_MAX;
        result.LTE_SignalStrength.cqi = INT_MAX;
        RIL_onUnsolicitedResponse(RIL_UNSOL_SIGNAL_STRENGTH, &result, sizeof(result), socketId);

    }
}

static void resetRegState(RegState * pRegState)
{
    if(pRegState) {
        pRegState->stat = -1;
        pRegState->lac = -1;
        pRegState->cid = -1;
        pRegState->act = -1;
        pRegState->rejectCause = -1;
    }
}

/* Reset all local saved reginfo and operInfo to NULL, force to update by AT cmd */
void resetLocalRegInfo(RIL_SOCKET_ID socketId)
{
    sOperInfo[socketId].mode = -1;
    sOperInfo[socketId].operLongStr[0] = '\0';
    sOperInfo[socketId].operShortStr[0] = '\0';
    sOperInfo[socketId].operNumStr[0] = '\0';
    //sOperInfo.act = -1;
    resetRegState(&(sCregState[socketId]));
    resetRegState(&(sCgregState[socketId]));
    resetRegState(&(sCeregState[socketId]));
}


static void filter_cesq(RIL_SOCKET_ID socketId)
{
    static int buffer_rsrp[NR_CSQ_BUFFER];
    static int buffer_rsrq[NR_CSQ_BUFFER];
    static int isFull = 0;
    static int num = 0;
    int i = 0;
    int rsrq_sum = 0, max_rsrq, min_rsrq;
    int rsrp_sum = 0, max_rsrp, min_rsrp;

    if((sCESQ[socketId].rsrq == 255 && sCESQ[socketId].rsrp == 255) || (sScreenState[socketId] == 0) || !IsLTEAttached(socketId)) {
        memset(buffer_rsrp, 0, NR_CSQ_BUFFER);
        memset(buffer_rsrq, 0, NR_CSQ_BUFFER);
        num = 0;
        isFull = 0;
        return;
    }
    if(num >= (NR_CSQ_BUFFER - 1))
        isFull = 1;
    if(num >= NR_CSQ_BUFFER)
        num = 0;
    buffer_rsrq[num] = sCESQ[socketId].rsrq;
    buffer_rsrp[num] = sCESQ[socketId].rsrp;
    num ++;
    // If buffer is full.
    if(isFull) {
        max_rsrq = min_rsrq = buffer_rsrq[0];
        max_rsrp = min_rsrp = buffer_rsrp[0];
        for(i = 0; i < NR_CSQ_BUFFER; i++) {
            rsrq_sum += buffer_rsrq[i];
            if(max_rsrq < buffer_rsrq[i])
                max_rsrq = buffer_rsrq[i];
            if(min_rsrq > buffer_rsrq[i])
                min_rsrq = buffer_rsrq[i];

            rsrp_sum += buffer_rsrp[i];
            if(max_rsrp < buffer_rsrp[i])
                max_rsrp = buffer_rsrp[i];
            if(min_rsrp > buffer_rsrp[i])
                min_rsrp = buffer_rsrp[i];
        }
        // Calculate average value remove max and min.
        sCESQ[socketId].rsrq = (rsrq_sum - max_rsrq - min_rsrq) / (NR_CSQ_BUFFER - 2);
        sCESQ[socketId].rsrp = (rsrp_sum - max_rsrp - min_rsrp) / (NR_CSQ_BUFFER - 2);
    } else {
        for(i = 0; i < num; i++)
            rsrq_sum += buffer_rsrq[i];
        sCESQ[socketId].rsrq = rsrq_sum / num;

        for(i = 0; i < num; i++)
            rsrp_sum += buffer_rsrp[i];
        sCESQ[socketId].rsrp = rsrp_sum / num;
    }
}



static void filter_csq(RIL_SOCKET_ID socketId)

{
    static int buffer_CSQ[NR_CSQ_BUFFER];
    static int isFull = 0;
    static int num = 0;
    int i = 0, sum = 0, maxCSQ, minCSQ;

    if((sCSQ[socketId].rssi == 99 && sCSQ[socketId].ber == 99) || (sScreenState[socketId] == 0)) {
        memset(buffer_CSQ, 0, NR_CSQ_BUFFER);
        num = 0;
        isFull = 0;
        return;
    }
    if(num >= (NR_CSQ_BUFFER - 1))
        isFull = 1;
    if(num >= NR_CSQ_BUFFER)
        num = 0;
    buffer_CSQ[num++] = sCSQ[socketId].rssi;
    // If buffer is full.
    if(isFull) {
        maxCSQ = minCSQ = buffer_CSQ[0];
        for(i = 0; i < NR_CSQ_BUFFER; i++) {
            sum += buffer_CSQ[i];
            if(maxCSQ < buffer_CSQ[i])
                maxCSQ = buffer_CSQ[i];
            if(minCSQ > buffer_CSQ[i])
                minCSQ = buffer_CSQ[i];
        }
        // Calculate average value remove max and min.
        sCSQ[socketId].rssi = (sum - maxCSQ - minCSQ) / (NR_CSQ_BUFFER - 2);
    } else {
        for(i = 0; i < num; i++)
            sum += buffer_CSQ[i];
        sCSQ[socketId].rssi = sum / num;
    }
}

static inline void setScreenState(int state, RIL_SOCKET_ID socketId)
{
    sScreenState[socketId] = state;
    // If Screen is off, clear csq buffer.
    if((sScreenState[socketId] == 0) && gSignalIntervalMode[socketId]) {
        filter_csq(socketId);
        filter_cesq(socketId);
    }
}

/* Get registe state: return 1: registered, 0: unregistered */
int isRegistered(RIL_SOCKET_ID socketId)
{
    return (IsStateInService(sCregState[socketId].stat) || IsStateInService(sCeregState[socketId].stat));
}

/* : Check whether the UE has service
return 1: has service(include emergency only case), 0: out of service */
int hasService(int state)
{
    if(state == 1 || state == 5 || state >= 10) {
        return 1;
    } else {
        return 0;
    }
}

void getCurrentOperNumStr(char ** operNumStr, RIL_SOCKET_ID socketId)
{
    ATResponse * response = NULL;
    int err;
    int skip;
    ATLine * p_cur;
    char * value = NULL;

    if(!isRegistered(socketId)) {
        goto exit;
    }

    err = at_send_command_multiline_timeout("AT+COPS=3,2;+COPS?", "+COPS:", &response, TIMEOUT_COPS);
    if(err < 0 || response->success == 0)
        goto exit;

    if(strStartsWith(response->finalResponse, "+CME ERROR:") || response->p_intermediates == NULL) {
        goto exit;
    }

    for(p_cur = response->p_intermediates; p_cur != NULL; p_cur = p_cur->p_next) {
        char * line = p_cur->line;
        int format;

        err = at_tok_start(&line);
        if(err < 0) goto exit;

        err = at_tok_nextint(&line, &skip);
        if(err < 0) goto exit;

        // If we're unregistered, we may just get a "+COPS: 0" response
        if(!at_tok_hasmore(&line)) {
            goto exit;
        }

        err = at_tok_nextint(&line, &format);
        if(err < 0) goto exit;

        // a "+COPS: 0, n" response is also possible
        if(!at_tok_hasmore(&line) || format > 2 || format < 0) {
            continue;
        }

        err = at_tok_nextstr(&line, &value);
        if(err < 0) goto exit;

    }

exit:
    if(value) {
        *operNumStr = lv_strdup(value);
    }
    at_response_free(response);
}

/*add emergency call enabled for some registration status of +CREG*/
static inline int registerStateTransform(int state)
{
    if(state == 3 || state == 11)
        state = 13; //Register state extention: 13 - Same as 3 but indicates that emergency calls are enabled
    return state;
}

/* Convert AcT value in AT cmd to RIL_RadioTechnology value defined in ril.h
 *  Input para: AcT (3GPP spec definition):
 *      0   GSM
 *      1   GSM Compact
 *      2   UTRAN
 *      3   GSM w/EGPRS (see NOTE 1)
 *      4   UTRAN w/HSDPA (see NOTE 2)
 *      5   UTRAN w/HSUPA (see NOTE 2)
 *      6   UTRAN w/HSDPA and HSUPA (see NOTE 2)
 *  Output para: state (Refer to ril.h, RIL_RadioTechnology)
 *   RADIO_TECH_UNKNOWN = 0,
 *   RADIO_TECH_GPRS = 1,
 *   RADIO_TECH_EDGE = 2,
 *   RADIO_TECH_UMTS = 3,
 *   RADIO_TECH_IS95A = 4,
 *   RADIO_TECH_IS95B = 5,
 *   RADIO_TECH_1xRTT =  6,
 *   RADIO_TECH_EVDO_0 = 7,
 *   RADIO_TECH_EVDO_A = 8,
 *   RADIO_TECH_HSDPA = 9,
 *   RADIO_TECH_HSUPA = 10,
 *   RADIO_TECH_HSPA = 11,
 *   RADIO_TECH_EVDO_B = 12,
 *   RADIO_TECH_EHRPD = 13,
 *   RADIO_TECH_LTE = 14,
 *   RADIO_TECH_HSPAP = 15 // HSPA+
 *   RADIO_TECH_GSM = 16, // Only supports voice
 *   RADIO_TECH_TD_SCDMA = 17,
 *   RADIO_TECH_IWLAN = 18,
 *   RADIO_TECH_LTE_CA = 19
 */

static RIL_RadioTechnology convertActToRilRadioTech(int act)
{

    switch(act) {
        case 0:
        case 1:
            return RADIO_TECH_GPRS;
        case 2:
            return RADIO_TECH_UMTS;
        case 3:
            return RADIO_TECH_EDGE;
        case 4:
            return RADIO_TECH_HSDPA;
        case 5:
            return RADIO_TECH_HSUPA;
        case 6:
            return RADIO_TECH_HSPA;
        case 7:
            return RADIO_TECH_LTE;
        case 8:
            return RADIO_TECH_HSPAP;
        case 9:
            return RADIO_TECH_LTE_CA;
        case -1:
        default:
            return RADIO_TECH_UNKNOWN;
    }
}


static inline void libConvertActToRilState(int AcT, char * state)
{
    sprintf(state, "%d", convertActToRilRadioTech(AcT));
}


int get_phone_functionality()
{
    ATResponse * p_response = NULL;
    int ret = -1;
    char * line;
#if defined(__XF_2D4G_SUPPORT__)

	RIL_SOCKET_ID socketId = getSocketId();
	RLOGI("===%s socketId:%d \n", __FUNCTION__,socketId);
	if(socketId == RIL_SOCKET_2)
		return 0; /*CFUN=0 */
#endif

    int err = at_send_command_singleline("AT+CFUN?", "+CFUN:", &p_response);
    if(err < 0 || p_response->success == 0) {
        goto exit;
    }

    line = p_response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto exit;

    err = at_tok_nextint(&line, &ret);

exit:
    at_response_free(p_response);
    return ret;
}

int isRadioOn()
{
    return get_phone_functionality() == 1;
}

int IsLTEAttached(RIL_SOCKET_ID socketId)
{
    RIL_RadioTechnology stat = convertActToRilRadioTech(sCeregState[socketId].act);
    return ((stat ==  RADIO_TECH_LTE || stat ==  RADIO_TECH_LTE_CA)
            && IsStateInService(sCeregState[socketId].stat));
}

int IsGSMAttached(RIL_SOCKET_ID socketId)
{
    RIL_RadioTechnology stat = convertActToRilRadioTech(sCregState[socketId].act);
    return ((stat ==  RADIO_TECH_GPRS || stat ==  RADIO_TECH_EDGE || stat == RADIO_TECH_UMTS
             || stat == RADIO_TECH_HSDPA || stat == RADIO_TECH_HSUPA || stat == RADIO_TECH_HSPA)
            && IsStateInService(sCregState[socketId].stat));
}

int isDataServiceRegistered(RIL_SOCKET_ID socketId)
{
    return  IsStateInService(sCeregState[socketId].stat) || IsStateInService(sCgregState[socketId].stat);
}

int isDataRegForEmergecyOnly(RIL_SOCKET_ID socketId)
{
    return  sCeregState[socketId].stat == 8 || sCeregState[socketId].stat == 11 ||
            sCgregState[socketId].stat == 8 || sCgregState[socketId].stat == 11;
}

int libGetVoiceRegInfo(int * pNum, RIL_SOCKET_ID socketId)
{
    ATResponse * p_response = NULL;
    int err = 0, num = 0;
    int responseInt[10] = {0};
    int changed = 0, act_changed = 0;

    /* Update CREG info */
    err = at_send_command_singleline("AT$CREG?", "$CREG:", &p_response);
    if(err < 0 || p_response->success == 0) goto error;

    err = parseResponseWithMoreInt(p_response, responseInt, &num, 1);
    if(err < 0) goto error;

    responseInt[0] = registerStateTransform(responseInt[0]);

    /* Save latest reg status locally */
    responseInt[0] = checkForSmsOnlyMode(responseInt[0]);
    changed |= updateValue(&sCregState[socketId].stat, responseInt[0]);
    changed |= updateValue(&sCregState[socketId].lac, responseInt[1]);
    changed |= updateValue(&sCregState[socketId].cid, responseInt[2]);
    if(num >= 5)
        act_changed |= updateValue(&sCregState[socketId].act, responseInt[3]);
    if(num >= 7)
        updateValue(&sCregState[socketId].rejectCause, responseInt[4]);
    updateValue(&sCregState[socketId].psc, responseInt[5]);

    if(changed || act_changed) {
        /* Report to upper layer */
        RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED, NULL, 0, socketId);
    }
    if(act_changed) {
        int radioTech = convertActToRilRadioTech(sCregState[socketId].act);
        RIL_onUnsolicitedResponse(RIL_UNSOL_VOICE_RADIO_TECH_CHANGED, &radioTech, sizeof(radioTech), socketId);
    }

    if(pNum != NULL) {
        *pNum = num;
    }
    at_response_free(p_response);

    return 0;

error:
//    RLOGE("%s: Error in parsing AT response", __FUNCTION__);
    at_response_free(p_response);
    return -1;
}

int libGetDataRegInfo(int * pNum, RegState ** ppDataState, RIL_SOCKET_ID socketId)
{
    ATResponse * p_response = NULL;
    int err = 0, num = 0;
    int responseInt[10] = {0};
    RegState * pDataState = NULL;
    int changed = 0;

    err = at_send_command_singleline("AT+CEREG?", "+CEREG:", &p_response);
    if(err < 0 || p_response->success == 0) { //not supported, send cgreg
        RLOGD("%s: AT+CEREG not supported, send cgreg", __FUNCTION__);
        at_response_free(p_response);
        p_response = NULL;
        goto CGREG;
    } else {
        err = parseResponseWithMoreInt(p_response, responseInt, &num, 0);
        if(err < 0) goto error;

        RLOGD("%s:  LTE is connected? :%d", __FUNCTION__, responseInt[0]);
        if(IsStateInService(responseInt[0]) || (responseInt[0] == 11)) { //LTE is linked
            pDataState = &sCeregState[socketId];
            goto SaveResult;
        } else {        //LTE is not linked, send at+cgreg;
            at_response_free(p_response);
            p_response = NULL;
            goto CGREG;
        }
    }

CGREG:
    pDataState = &sCgregState[socketId];
    err = at_send_command_singleline("AT+CGREG?", "+CGREG:", &p_response);
    if(err < 0 || p_response->success == 0)
        goto error;

    err = parseResponseWithMoreInt(p_response, responseInt, &num, 0);
    if(err < 0) goto error;

SaveResult:
    /* Save latest reg status locally */
    responseInt[0] = checkForSmsOnlyMode(responseInt[0]);
    changed |= updateValue(&(pDataState->stat), responseInt[0]);
    changed |= updateValue(&(pDataState->lac), responseInt[1]);
    changed |= updateValue(&(pDataState->cid), responseInt[2]);
    if(num >= 5)
        changed |= updateValue(&(pDataState->act), responseInt[3]);
    if(num >= 7)
        updateValue(&(pDataState->rejectCause), responseInt[4]);

    if(changed) {
        /* Report to upper layer */
        RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED, NULL, 0, socketId);
    }

    at_response_free(p_response);

    if(pNum != NULL)
        *pNum = num;

    if(ppDataState != NULL)
        *ppDataState = pDataState;

    return 0;

error:
    RLOGE("%s: Error in parsing AT response", __FUNCTION__);
    at_response_free(p_response);
    return -1;
}

static int CSFB2DualMode(int band_mode)
{
    int mode_after = band_mode;
    char mode_value[512];
    property_get("sys.lte.mode", mode_value, "unknown");

    if(strcasecmp(mode_value, "duallink") == 0) {
        switch(band_mode) {
            case 6:
            case 7:
            case 8:
                mode_after = 16;
                break;
            case 9:
            case 10:
            case 11:
                mode_after = 17;
                break;
            case 12:
            case 13:
            case 14:
            case 15:
                mode_after = 18;
                break;
            default:
                break;
        }
    }
    return mode_after;
}


void modemSwitchOnNoService(void * param)
{
    struct timespec tp;
    int index = (int)(unsigned long)param;
    RIL_SOCKET_ID socketId = getSocketId();

    if(clock_gettime(CLOCK_MONOTONIC, &tp)) {
        RLOGE("%s get time error\n", __FUNCTION__);
    } else {
        if(tp.tv_sec - g_tp.tv_sec > g_modem_switch_time_array[index] ||
                (tp.tv_sec - g_tp.tv_sec == g_modem_switch_time_array[index] &&
                 tp.tv_nsec >= g_tp.tv_nsec)) {
            int simStatus = getSimStatus(socketId);
            if(simStatus == SIM_PIN || simStatus == SIM_PUK ||
                    simStatus == SIM_PIN2 || simStatus == SIM_PUK2) {
                RLOGW("%s: PIN or PUK required:%d", __FUNCTION__, simStatus);
                return;
            }
            libGetVoiceRegInfo(NULL, socketId);
            libGetDataRegInfo(NULL, NULL, socketId);
            switch_modem(-1, sCregState[socketId].stat, sCeregState[socketId].stat, SIM_OPERATOR_NUM, socketId);
        }
    }
}

int libSetAllowData(int state)
{
    int err = 0;
    ATResponse * p_response = NULL;
    char cmdString[MAX_AT_LENGTH] = {0};

    snprintf(cmdString, sizeof(cmdString), "AT*PSDC=%d", state);
    err = at_send_command_timeout(cmdString, &p_response, TIMEOUT_CGATT);

    if(err < 0 || p_response->success == 0) {
        err = -1;
        RLOGW("%s: FAIL to set ALLOW_DATA command", __FUNCTION__);
    }
    at_response_free(p_response);
    p_response = NULL;
    return err;
}

int libGetAllowData(int * state)
{
    int err = 0;
    ATResponse * p_response = NULL;

    err = at_send_command_singleline("AT*PSDC?", "*PSDC:", &p_response);
    if(err < 0 || p_response->success == 0) {
        err = -1;
        RLOGE("%s: Fait to get ALLOW_DATA state", __FUNCTION__);
    } else {
        char * line = p_response->p_intermediates->line;

        err = at_tok_start(&line);
        if(err < 0) goto exit;

        err = at_tok_nextint(&line, state);
    }
exit:
    at_response_free(p_response);
    p_response = NULL;
    return err;
}

/* Process RIL_REQUEST_SHUTDOWN */
void ril_request_shutdown(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);
    RIL_SOCKET_ID socketId = getSocketId();
    int shutdown_type = *(int *)data;
#if defined(__XF_2D4G_SUPPORT__)
    static int shutdown_flag[RIL_SOCKET_NUM] = {0, 1};
	RLOGI("===%s socketId:%d shutdown_type:%d \n", __FUNCTION__,socketId,shutdown_type);
	if(socketId == RIL_SOCKET_2)
		return;
	#else
		static int shutdown_flag[RIL_SOCKET_NUM] = {0, 0};
	#endif
    rilPsMutexLock();

    int err = 0;
    ATResponse * p_response = NULL;

    assert(socketId < RIL_SOCKET_NUM);

    if (!shutdown_flag[socketId]) {
        err = at_send_command_timeout("AT+CFUN=0", &p_response, TIMEOUT_CFUN);
        if(err < 0 || p_response->success == 0) goto error;
        resetLocalRegInfo(socketId);

        setRadioState(RADIO_STATE_OFF, socketId);

        if (shutdown_type != 255) {
            shutdown_flag[socketId] = 1;
        }
    }
    if (shutdown_flag[RIL_SOCKET_1] && shutdown_flag[RIL_SOCKET_2]) {
        if (shutdown_type != 2) { /* not factory reset */
            ATResponse * p_response = NULL;
            err = at_send_command_timeout("AT*NVMFLUSH=1", &p_response, TIMEOUT_CFUN);
            if(err < 0 || p_response->success == 0) {
                printf("flash nvm failed\n");
            }
        }

        if(shutdown_type == 0) { /* power off */
            lcd_backlight_ctrl(0);
			
#if defined(__XF_LCD_SIZE_240X280__)
	if(nfc_swipe_card_after_shutdown_p!=NULL)nfc_swipe_card_after_shutdown_p();	
#endif
            pmic_sw_pdown();
            pmic_sw_reboot();
        } else if (shutdown_type == 1) { /* reboot */
            pmic_sw_reboot();
        } else if (shutdown_type == 2) { /* factory reset */
            FDI_Factory_Reset();
        } else {
            printf("ril_request_shutdown: unknown shutdown type %d\n", shutdown_type);
        }
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;

error:

    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(p_response);
    rilPsMutexUnlock();
}

void syncVoiceDomainSetting(RIL_SOCKET_ID socketId)
{
    UNUSED(socketId);
    const char * p_voice_domain = NULL;
    char value[PROPERTY_VALUE_MAX];
    char cmdString[MAX_AT_LENGTH] = { '\0' };

#if (SIM_COUNT >= 2)
    /*  Sync Voice Domain Setting  */
    if(RIL_SOCKET_2 == socketId) {
        p_voice_domain = PROP_DOMAIN_SETTING2;
    } else
#endif
    {
        p_voice_domain = PROP_DOMAIN_SETTING;
    }

    property_get(p_voice_domain, value, "N/A");
    if(!strcmp(value, "N/A")) {
        /* if ims is required, set the domain as PS preferred (3)
         * otherwise, set CS only (1) as default parameter */
        property_get(PROP_IMS_CONFIG, value, "0");
        sprintf(cmdString, "AT+CEVDP=%d", (atoi(value) & 0x03) == 0 ? 1 : 3);
    } else {
        /* the property defined in atcmdsrv is different with +CEVDP definition*/
        sprintf(cmdString, "AT+CEVDP=%d", (atoi(value) & 0x03) + 1);
    }
    at_send_command(cmdString, NULL);
}

/* Process RIL_REQUEST_RADIO_POWER */
void ril_request_radio_power(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);
    UNUSED(token);

    int onOff = ((int *)data)[0];
    int err, desiredState, allowDataFail;
    RIL_RadioState currentState;
    ATResponse * p_response = NULL;
    RIL_SOCKET_ID socketId = getSocketId();
    int ril_first = isRilFirstConnected(socketId);

    currentState = getRadioState(socketId);
    //RLOGI("%s  ril-first=%d socketId:%d onOff:%d currentState:%d\n", __FUNCTION__, ril_first,socketId,onOff,currentState);

    if((onOff == 0) && (currentState != RADIO_STATE_OFF)) {
        char value[PROPERTY_VALUE_MAX];
        property_get("sys.shutdown.requested", value, "not-requested");
        //RLOGI("%s  ril-first=%d\n", __FUNCTION__, ril_first);
        if(strcmp(value, "not-requested") == 0) {
            if(!ril_first) {
                rilPsMutexLock();
                err = at_send_command_timeout("AT+CFUN=4", &p_response, TIMEOUT_CFUN);
                rilPsMutexUnlock();
            } else {
                err = 0;
            }
        } else {
            //RLOGI("%s: shutdown is requested", __FUNCTION__);
            err = at_send_command_timeout("AT+CFUN=0", &p_response, TIMEOUT_CFUN);
        }
        if(!ril_first) {
            if(err < 0 || p_response->success == 0) goto error;
            setRadioState(RADIO_STATE_OFF, socketId);
            resetLocalRegInfo(socketId);
        }
    }

    else if((onOff == 1) && (currentState == RADIO_STATE_OFF)) {
        if(!wait_sim_detect_and_euicc(socketId)) {
            // UICC is not OK after waiting, still continue..
            RLOGW("%s: uicc is not OK\n", __FUNCTION__);
            goto error;
        }
        // we need make sure enable network registration before AT+CFUN=1
        checkCereg();
        // Always try to set allow data state to desired before AT+CFUN=1.
        desiredState = getDesiredAllowDataState(socketId);
        rilPsMutexLock();
        allowDataFail = libSetAllowData(desiredState);
        rilPsMutexUnlock();
        if(allowDataFail) {
            RLOGW("%s: fail to allow data\n", __FUNCTION__);
            goto error;
        }
        syncVoiceDomainSetting(socketId);

        rilPsMutexLock();
        err = at_send_command_timeout("AT+CFUN=1", &p_response, TIMEOUT_CFUN);
        rilPsMutexUnlock();
        if(err < 0 || p_response->success == 0) {
            /* Some stacks return an error when there is no SIM, but they really turn the RF portion on
             * So, if we get an error, let's check to see if it turned on anyway
             */
            if(isRadioOn() != 1) goto error;
        }
        setRadioState(RADIO_STATE_ON, socketId);
    }

    else {
        ;//RLOGD("Already in current state, return directly");
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;

error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    if(ril_first) {
        //RLOGI("%s first ril connected completed\n", __FUNCTION__);
        setRilFirstConnectedProp(0, socketId);
    }
    at_response_free(p_response);
}

/* Process RIL_REQUEST_RESET_RADIO */
void ril_request_reset_radio(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int err;
    ATResponse * p_response = NULL;
    RIL_SOCKET_ID socketId = getSocketId();

    rilPsMutexLock();
    err = at_send_command_timeout("AT+CFUN=4", &p_response, TIMEOUT_CFUN);
    rilPsMutexUnlock();
    if(err < 0 || p_response->success == 0) goto error;

    at_response_free(p_response);
    p_response = NULL;
    setRadioState(RADIO_STATE_OFF, socketId);
    resetLocalRegInfo(socketId);

    err = at_send_command_timeout("AT+CFUN=1", &p_response, TIMEOUT_CFUN);
    if(err < 0 || p_response->success == 0) {
        /* Some stacks return an error when there is no SIM, but they really turn the RF portion on
         * So, if we get an error, let's check to see if it turned on anyway
         */
        if(isRadioOn() != 1) goto error;
    }

    setRadioState(RADIO_STATE_ON, socketId);

    at_response_free(p_response);
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    return;

error:
    at_response_free(p_response);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
    return;
}

/* Process RIL_REQUEST_SIM_RESTART */
void ril_request_sim_restart(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int err;
    ATResponse * p_response = NULL;
    RIL_SOCKET_ID socketId = getSocketId();

    rilPsMutexLock();
    err = at_send_command_timeout("AT+CFUN=5", &p_response, TIMEOUT_CFUN);
    rilPsMutexUnlock();
    if(err < 0 || p_response->success == 0) goto error;

    at_response_free(p_response);
    p_response = NULL;
    setRadioState(RADIO_STATE_OFF, socketId);
    resetLocalRegInfo(socketId);

    err = at_send_command_timeout("AT+CFUN=1", &p_response, TIMEOUT_CFUN);
    if(err < 0 || p_response->success == 0) {
        /* Some stacks return an error when there is no SIM, but they really turn the RF portion on
         * So, if we get an error, let's check to see if it turned on anyway
         */
        if(isRadioOn() != 1) goto error;
    }

    setRadioState(RADIO_STATE_ON, socketId);

    at_response_free(p_response);
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    return;

error:
    at_response_free(p_response);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
    return;
}

void ril_request_screen_state(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);
    RIL_SOCKET_ID socketId = getSocketId();

    setScreenState(((int *)data)[0], socketId);
    if(getRadioState(socketId) != RADIO_STATE_UNAVAILABLE) {
        if(sScreenState[socketId])
            //notify CP that AP will wake
            at_send_command("AT*POWERIND=0", NULL);
        else
            //notify CP that AP will sleep
            at_send_command("AT*POWERIND=1", NULL);
    }
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
}

/**
 * RIL_REQUEST_VOICE_RADIO_TECH
 *
 * Request current voice radio tech.
 */
void ril_request_voice_radio_tech(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int cached = 0, num = 0;
    size_t error_length = 0;
    int error_response = 0;
    RIL_Errno ril_error = RIL_E_MODEM_ERR;
    RIL_SOCKET_ID socketId = getSocketId();
    int simStatus = getSimStatus(socketId);

    if(simStatus == SIM_ABSENT) {
        ril_error = RIL_E_SUCCESS;
        error_length = sizeof(int);
        goto error;
    }

    if((sCregState[socketId].stat != -1) && (sCregState[socketId].act != -1)) {
        //RLOGD("%s: Return local saved voice radio tech", __FUNCTION__);
        cached = 1;
    } else {
        int err = libGetVoiceRegInfo(&num, socketId);
        if(err < 0)
            goto error;
    }

    if(num >= 5 || cached != 0) {
        int radioTech = convertActToRilRadioTech(sCregState[socketId].act);
        RIL_onRequestComplete(token, RIL_E_SUCCESS, &radioTech, sizeof(radioTech));
        return;
    }
error:
    RLOGE("%s: Error to get voice registration info", __FUNCTION__);
    RIL_onRequestComplete(token, ril_error, &error_response, error_length);

}

struct voice_reg_state_t {
    int stat;
    int lac;
    int cid;
    int act;
    int causeType;
    int rejectCause;
    int psc;
} voice_reg_state;

void *get_voice_reg_state(void)
{
    return &voice_reg_state;
}

/**
 * RIL_REQUEST_VOICE_REGISTRATION_STATE
 *
 * Request current voice registration state.
 */
void ril_request_voice_registration_state(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int cached = 0, num = 0;
    char * responseStr[15] = {NULL}, radiotech[3];
    RIL_SOCKET_ID socketId = getSocketId();

    //export voice_reg_state
    voice_reg_state.act = sCregState[socketId].act;
    voice_reg_state.causeType = 0;
    voice_reg_state.cid = sCregState[socketId].cid;
    voice_reg_state.lac = sCregState[socketId].lac;
    voice_reg_state.psc = sCregState[socketId].psc;
    voice_reg_state.rejectCause =  sCregState[socketId].rejectCause;
    voice_reg_state.stat = sCregState[socketId].stat;

    if((sCregState[socketId].stat != -1) && (sCregState[socketId].lac != -1) && (sCregState[socketId].act != -1)) {
        //RLOGD("%s: Return local saved registration state", __FUNCTION__);
        cached = 1;
    } else {
        int err = libGetVoiceRegInfo(&num, socketId);
        if(err < 0)
            goto error;
    }
    responseStr[0] = (char *)UI_MALLOC(2 * sizeof(int) +4);
    sprintf(responseStr[0], "%d", sCregState[socketId].stat);
    if(num > 2 || cached != 0) {
        responseStr[1] = (char *)UI_MALLOC(2 * sizeof(int) +4);
        responseStr[2] = (char *)UI_MALLOC(2 * sizeof(int) +4);
        sprintf(responseStr[1], "%x", sCregState[socketId].lac);
        sprintf(responseStr[2], "%x", sCregState[socketId].cid);
    }

    if(num >= 5 || cached != 0) {
        libConvertActToRilState(sCregState[socketId].act, radiotech);
        responseStr[3] = radiotech;
    }

    if(num >= 7) {
        responseStr[4] = (char *)UI_MALLOC(2 * sizeof(int) +4);
        sprintf(responseStr[4], "%d", sCregState[socketId].rejectCause);
    }
    responseStr[14] = (char *)UI_MALLOC(2 * sizeof(int) +4);
    sprintf(responseStr[14], "%x", sCregState[socketId].psc);

    RIL_onRequestComplete(token, RIL_E_SUCCESS, responseStr, sizeof(responseStr));
    if(NULL != responseStr[0])UI_FREE(responseStr[0]);
    if(NULL != responseStr[1])UI_FREE(responseStr[1]);
    if(NULL != responseStr[2])UI_FREE(responseStr[2]);
    if(NULL != responseStr[4])UI_FREE(responseStr[4]);
    if(NULL != responseStr[14])UI_FREE(responseStr[14]);

    return;
error:
    RLOGE("%s: Error get voice reg info\n", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
}


void ril_request_data_registration_state(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    int cached = 0, num = 0;
    RegState * pDataState = NULL;
    RIL_SOCKET_ID socketId = getSocketId();
    char * responseStr[6] = {NULL}, gprsState[3];

    if(IsLTEAttached(socketId)) {
        //RLOGD("%s: Return local saved E-UTRAN registration state.", __FUNCTION__);
        pDataState = &sCeregState[socketId];
        cached = 1;
    } else if((sCgregState[socketId].stat != -1) && (sCgregState[socketId].lac != -1) && (sCgregState[socketId].act != -1)) {
        //RLOGD("%s: Return local saved GPRS registration state.", __FUNCTION__);
        pDataState = &sCgregState[socketId];
        cached = 1;
    } else {
        int err = libGetDataRegInfo(&num, &pDataState, socketId);
        if(err < 0)
            goto error;
    }
    responseStr[0] = (char *)UI_MALLOC(2 * sizeof(int) +4);
    sprintf(responseStr[0], "%d", pDataState->stat);
    if(num > 2 || cached != 0) {
        responseStr[1] = (char *)UI_MALLOC(2 * sizeof(int) +4);
        responseStr[2] = (char *)UI_MALLOC(2 * sizeof(int) +4);
        sprintf(responseStr[1], "0x%x", pDataState->lac);
        sprintf(responseStr[2], "0x%x", pDataState->cid);
    }

    if(num >= 5 || cached != 0) {
        /* Convert AcT value of 3GPP spec to GPRS reg state defined in ril.h */
        libConvertActToRilState(pDataState->act, gprsState);
        responseStr[3] = gprsState;
    }

    if(num >= 7) {
        responseStr[4] = (char *)UI_MALLOC(2 * sizeof(int) +4);
        sprintf(responseStr[4], "%d", pDataState->rejectCause);
    }
    responseStr[5] = (char *)UI_MALLOC(2 * sizeof(int) +4);
    sprintf(responseStr[5], "%d", MAX_DATA_CALLS);

    RIL_onRequestComplete(token, RIL_E_SUCCESS, responseStr, sizeof(responseStr));
    if(NULL != responseStr[0])UI_FREE(responseStr[0]);
    if(NULL != responseStr[1])UI_FREE(responseStr[1]);
    if(NULL != responseStr[2])UI_FREE(responseStr[2]);
    if(NULL != responseStr[4])UI_FREE(responseStr[4]);
    if(NULL != responseStr[5])UI_FREE(responseStr[5]);

    return;

error:
    RLOGE("%s: Format error in this AT response", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
}

/**
 * RIL_REQUEST_OPERATOR
 *
 * Request current operator ONS or EONS.
 */
void ril_request_operator(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int err;
    ATLine * p_cur;
    char * result[4];
    RIL_SOCKET_ID socketId = getSocketId();

    if(!isRegistered(socketId)) {
#if 0
        char * result[3];
        result[0] =  "";
        result[1] =  "";
        result[2] =  "";

        RLOGD("%s: unregistered, return empty info", __FUNCTION__);
        RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(result));
        return;
#endif
        err = at_send_command_multiline_timeout("AT+COPS=3,2;+COPS?", "+COPS:", &response, TIMEOUT_COPS);
    } else if((sOperInfo[socketId].operLongStr[0] != '\0') && (sOperInfo[socketId].operShortStr[0] != '\0') && (sOperInfo[socketId].operNumStr[0] != '\0')) {
        result[0] =  &(sOperInfo[socketId].operLongStr[0]);
        result[1] =  &(sOperInfo[socketId].operShortStr[0]);
        result[2] =  &(sOperInfo[socketId].operNumStr[0]);
        result[3] = (char *)&sOperInfo[socketId].mode;
        //RLOGD("%s: Return local saved operator info", __FUNCTION__);
        RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(result));
        return;
    } else {
        err = at_send_command_multiline_timeout("AT+COPS=3,0;+COPS?;+COPS=3,1;+COPS?;+COPS=3,2;+COPS?", "+COPS:", &response, TIMEOUT_COPS);
    }
    if(err < 0 || response->success == 0)
        goto error;

    if(strStartsWith(response->finalResponse, "+CME ERROR:") || response->p_intermediates == NULL) {
        goto error;
    }

    memset(result, 0, sizeof(result));

    for(p_cur = response->p_intermediates; p_cur != NULL; p_cur = p_cur->p_next) {
        char * line = p_cur->line;
        int format;

        err = at_tok_start(&line);
        if(err < 0) goto error;

        err = at_tok_nextint(&line, &(sOperInfo[socketId].mode));
        if(err < 0) goto error;

        // If we're unregistered, we may just get a "+COPS: 0" response
        if(!at_tok_hasmore(&line)) {
            goto unregistered;
        }

        err = at_tok_nextint(&line, &format);
        if(err < 0) goto error;

        // a "+COPS: 0, n" response is also possible
        if(!at_tok_hasmore(&line) || format > 2 || format < 0) {
            continue;
        }

        err = at_tok_nextstr(&line, &(result[format]));
        if(err < 0) goto error;

        if (at_tok_hasmore(&line))
        {
            err = at_tok_nextint(&line, &sOperInfo[socketId].mode);
            if (err < 0) goto error;
            result[3] = (char *)&sOperInfo[socketId].mode;
        }
    }

    /* Save operator info locally */
    if(result[0])
        strlcpy(sOperInfo[socketId].operLongStr, result[0], sizeof(sOperInfo[socketId].operLongStr));
    else
        sOperInfo[socketId].operLongStr[0] = '\0';
    if(result[1])
        strlcpy(sOperInfo[socketId].operShortStr, result[1], sizeof(sOperInfo[socketId].operShortStr));
    else
        sOperInfo[socketId].operShortStr[0] = '\0';
    if(result[2])
        strlcpy(sOperInfo[socketId].operNumStr, result[2], sizeof(sOperInfo[socketId].operNumStr));
    else
        sOperInfo[socketId].operNumStr[0] = '\0';

    RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(result));

    /* [Jerry, 2009/01/06] When operator name is available, the network should be registered.
    * If CP doesn't send indication msg, we need to query CP and update reg info
    */
#if 0
    if((sCregState[socketId].stat != 1) && (sCregState[socketId].stat != 5)) {
        const struct timeval TIMEVAL_30s = { 30, 0 };
        RIL_requestTimedCallback(updateLocalRegInfo, NULL, &TIMEVAL_30s);
    }
#endif

    if(worldPhoneAutoSwitchEnabled(socketId)) {
        int bIsNetworkInChina = isNetworkInChina(socketId);
        /*Only when know whether indeed in china or not, then need consider to switch image*/
        if(bIsNetworkInChina >= 0)
            switch_modem(bIsNetworkInChina, -1, -1, SIM_OPERATOR_NUM, socketId);
    }
    goto exit ;

unregistered:
    RLOGD("RIL_REQUEST_OPERATOR callback: network not registered");
    /* The reason to return RIL_E_SUCCESS instead of RADIO_NOT_AVAILABLE:
    * GsmServiceStateTracker handlePollStateResult() will cancelPollState if RADIO_NOT_AVAILABLE
    * is received, which will cause phone.notifyServiceStateChanged() in pollStateDone() never be called,
    * and it is root cause why after enabling airplane mode, the screen keeps waiting
    */
    result[0] = NULL;
    result[1] = NULL;
    result[2] = NULL;
    RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(result));
    goto exit;

error:
    RLOGE("%s: Error in this AT response", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_QUERY_NETWORK_SELECTION_MODE
 *
 * Query current network selectin mode.
 */
void ril_request_query_network_selection_mode(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int err;
    int result = 0;
    char * line;
    RIL_SOCKET_ID socketId = getSocketId();

    err = at_send_command_singleline("AT+COPS?", "+COPS:", &response);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL)
        goto error;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);

    if(err < 0) {
        goto error;
    }

    err = at_tok_nextint(&line, &result);

    if(err < 0) {
        goto error;
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(int));

    sOperInfo[socketId].mode = result;
    goto exit;
error:
    RLOGE("%s: Respond error, return default value 0: auto selection", __FUNCTION__);
    result = 0;
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(int));
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_SIGNAL_STRENGTH
 *
 * Requests current signal strength and bit error rate.
 *
 * Must succeed if radio is on.
 */
void ril_request_signal_strength(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int err;
    RIL_SignalStrength_mm result;
    char * line;
    RIL_SOCKET_ID socketId = getSocketId();

    memset(&result, 0, sizeof(result));
    init_signal_strength(result);

    if(!getCESQSupport(socketId)) {
        err = at_send_command_singleline("AT+CSQ", "+CSQ:", &response);
        if(err < 0 || response->success == 0)
            goto error;

        line = response->p_intermediates->line;
        err = at_tok_start(&line);
        if(err < 0)
            goto error;

        err = at_tok_nextint(&line, &result.GW_SignalStrength.signalStrength);
        if(err < 0)
            goto error;

        err = at_tok_nextint(&line, &result.GW_SignalStrength.bitErrorRate);
        if(err < 0)
            goto error;
    } else {
        int rxlev, rscp, ecno;
        int rscpdbm;
#ifdef MMI_ASR_RIL_BRINGUP
#else
        int rxlevdbm;
        int ecnodb;
#endif
        err = at_send_command_singleline("AT+CESQ", "+CESQ:", &response);
        if(err < 0 || response->success == 0)
            goto report;

        line = response->p_intermediates->line;
        err = parse_cesq(&line, &rxlev,
                         &result.GW_SignalStrength.bitErrorRate, &rscp, &ecno,
                         &result.LTE_SignalStrength.rsrq, &result.LTE_SignalStrength.rsrp);
        if(err < 0) goto error;

#ifdef MMI_ASR_RIL_BRINGUP
#else
        rxlevdbm = convertRxlevToDbm(rxlev);
#endif
        rscpdbm = convertRscpToDbm(rscp);
//      ecnodb = convertEcnoToDB(ecno);
        result.GW_SignalStrength.signalStrength = process_rssi(rxlev, rscpdbm);
    }
    // set LTE signal strength
    // to match framework/base/telephony/java/android/telephony/SignalStrength.java
    result.LTE_SignalStrength.signalStrength = 99;
    result.LTE_SignalStrength.rssnr = INT_MAX;
    result.LTE_SignalStrength.cqi = INT_MAX;
    if(IsLTEAttached(socketId)) {
        result.LTE_SignalStrength.signalStrength = result.GW_SignalStrength.signalStrength;
        result.GW_SignalStrength.signalStrength = 99;
        result.LTE_SignalStrength.rsrq = convertRsrqToDbm(result.LTE_SignalStrength.rsrq);
        result.LTE_SignalStrength.rsrp = convertRsrpToDbm(result.LTE_SignalStrength.rsrp);
    } else if (!IsGSMAttached(socketId)) {
        result.GW_SignalStrength.signalStrength = 99;
        result.LTE_SignalStrength.rsrq = INT_MAX;
        result.LTE_SignalStrength.rsrp = INT_MAX;
    }

report:
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(result));

    goto exit;

error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_SET_NETWORK_SELECTION_AUTOMATIC
 *
 * Specify that the network should be selected automatically.
 */
void ril_request_set_network_selection_automatic(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int err;
    RIL_SOCKET_ID socketId = getSocketId();

    resetLocalRegInfo(socketId);
    err = at_send_command_timeout("AT+COPS=0", &response, TIMEOUT_COPS);
    if(err < 0 || response->success == 0)
        goto error;

    // Local Registration info is reset to null before auto network selection,
    // CP may not send +CREG/+CGREG indicator if registration information is not
    // changed during auto network selection.
    // we need query these info here. Otherwise, the cached local registradion
    // info may be not synced with CP.
    libGetVoiceRegInfo(NULL, socketId);
    libGetDataRegInfo(NULL, NULL, socketId);

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED, NULL, 0, socketId);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_INTERNAL_ERR, NULL, 0);
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_SET_NETWORK_SELECTION_MANUAL
 *
 * Manually select a specified network.
 *
 * The radio baseband/RIL implementation will try to camp on the manually
 * selected network regardless of coverage, i.e. there is no fallback to
 * automatic network selection.
 */
void ril_request_set_network_selection_manual(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int err;
    char cmd[MAX_AT_LENGTH];
    RIL_SOCKET_ID socketId = getSocketId();

    resetLocalRegInfo(socketId);

    sprintf(cmd, "AT+COPS=1,2,%s", (const char *)data);
    err = at_send_command_timeout(cmd, &response, TIMEOUT_COPS);
    if(err < 0 || response->success == 0)
        goto error;

    // Local Registration info is reset to null before auto network selection,
    // CP may not send +CREG/+CGREG indicator if registration information is not
    // changed during auto network selection.
    // we need query these info here. Otherwise, the cached local registradion
    // info may be not synced with CP.
    libGetVoiceRegInfo(NULL, socketId);
    libGetDataRegInfo(NULL, NULL, socketId);

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED, NULL, 0, socketId);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_INTERNAL_ERR, NULL, 0);
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_QUERY_AVAILABLE_NETWORKS
 *
 * Scans for available networks.
 */
void ril_request_query_available_networks(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int err, availableOptNumber, lparen;
    char * line, *p;
    char ** result;
    int quoted = 0;
    const int result_len = 5;

    err = at_send_command_singleline_timeout("AT+COPS=?", "+COPS:", &response, TIMEOUT_COPS_TEST);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL)
        goto error;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    /* count number of outter lparen */
    lparen = 0;
    for(p = line; *p != '\0'; p++) {
        if(*p == '(' && !quoted) {
            lparen++;
        } else if(*p == '"') {
            quoted = !quoted;
        }
    }

    if(quoted) {
        RLOGE("%s: invalid network format %s", __FUNCTION__, line);
        goto error;
    }

    /*
     * the response is +COPS:(op1),(op2),...(opn),,(0,1,2,3,4),(0-6)
     * so available operator count should be num_of_left_parentheses - 2
     */
    if(lparen > 1)
        availableOptNumber = lparen - 2;
    else
        availableOptNumber = 0;
    RLOGD("%s: available operator number:%d", __FUNCTION__, availableOptNumber);
    result = reinterpret_cast<char **>(UI_MALLOC(availableOptNumber * result_len * sizeof(char *)));

    for(int i = 0; i < availableOptNumber; i++) {
        char * status, *act;
        at_tok_nextstr(&line, &status);
        RLOGD("status:%s", status);
        result[i * result_len + 3] = status;
        /*switch(status[1]) {
            case '1':
                result[i * result_len + 3] = const_cast<char *>("available");
                break;
            case '2':
                result[i * result_len + 3] = const_cast<char *>("current");
                break;
            case '3':
                result[i * result_len + 3] = const_cast<char *>("forbidden");
                break;
            default:
                result[i * result_len + 3] = const_cast<char *>("unknown");
        }*/
        RLOGD("state:%d,", result[i * result_len + 3][1]-'0');
        at_tok_nextstr(&line, &result[i * result_len]);
        RLOGD("longname:%s", result[i * result_len]);
        at_tok_nextstr(&line, &result[i * result_len + 1]);
        RLOGD("shortname:%s", result[i * result_len + 1]);
        at_tok_nextstr(&line, &result[i * result_len + 2]);
        RLOGD("numbername:%s", result[i * result_len + 2]);
        at_tok_nextstr(&line, &act);
        RLOGD("act:%s", act);
        result[i * result_len + 4] = act;
        /*switch (act[0])
        {
        case '0':
            result[i * result_len + 4] = const_cast<char*>("GSM");
            break;
        case '1':
            result[i * result_len + 4] = const_cast<char*>("GSM_COMPACT");
            break;
        case '2':
            result[i * result_len + 4] = const_cast<char*>("UTRAN");
            break;
        case '7':
            result[i * result_len + 4] = const_cast<char*>("E_UTRAN");
            break;
        default:
            result[i * result_len + 4] = const_cast<char*>("UNKNOWN");
        }*/
        RLOGD("act:%d\n", result[i * result_len + 4][0]-'0');
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(char *) * availableOptNumber * result_len);
    goto exit;

error:
    RLOGE("%s: Format error in this AT response", __FUNCTION__);
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);

}

/**
 * RIL_REQUEST_SET_PREFERRED_NETWORK_TYPE
 *
 * Requests to set the preferred network type for searching and registering
 * (CS/PS domain, RAT, and operation mode).
 */
void ril_request_set_preferred_network_type(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);
    int networkType;
    int starBandNetworkType;
    int err;
    int mode;
    int gsmBand, umtsBand, lteBandH, lteBandL;
    char * line;

    networkType = ((int *)data)[0];

    //Check for L+G dual mode
    RIL_SOCKET_ID socketId = getSocketId();
    RLOGD("ARIL ril_request_set_preferred_network_type:%d isMasterRil:%d", networkType, isMasterRil(socketId));

    char value[PROPERTY_VALUE_MAX];
    property_get("persist.radio.band.test", value, "false");
    if(!strcmp(value, "true")) {
        RLOGI("%s: We are in band test mode, return directly", __FUNCTION__);
        RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
        return;
    }

    if(networkType < 0 || networkType >= (int)sizeof(RILNetworkTypeTostarBandNetworkType) / (int)sizeof(RILNetworkTypeTostarBandNetworkType[0]))
        starBandNetworkType = -1;
    else {
        starBandNetworkType = RILNetworkTypeTostarBandNetworkType[networkType];
        starBandNetworkType = CSFB2DualMode(starBandNetworkType);
    }
    if(starBandNetworkType < 0) {
        RIL_onRequestComplete(token, RIL_E_MODE_NOT_SUPPORTED, NULL, 0);
        return;
    }

    ATResponse * response = NULL;
    char cmd[MAX_AT_LENGTH];
    err = at_send_command_singleline_timeout("AT*BAND?", "*BAND:", &response, TIMEOUT_EBAND);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL)
        goto error;
    line = response->p_intermediates->line;
    err = at_tok_start(&line);
    if(err < 0) goto error;
    err = at_tok_nextint(&line, &mode);
    if(err < 0) goto error;
    err = at_tok_nextint(&line, &gsmBand);
    if(err < 0) goto error;
    err = at_tok_nextint(&line, &umtsBand);
    if(err < 0) goto error;
    err = at_tok_nextint(&line, &lteBandH);
    RLOGI("%s: err is %d, lteBandH=%d", __FUNCTION__, err, lteBandH);
    if(err < 0 || lteBandH < 0) lteBandH = -1;
    err = at_tok_nextint(&line, &lteBandL);
    RLOGI("%s: err is %d, lteBandL=%d", __FUNCTION__, err, lteBandL);
    if(err < 0 || lteBandL < 0) lteBandL = -1;
    at_response_free(response);
    response = NULL;

    RLOGI("%s: request mode %d, current mode %d", __FUNCTION__, starBandNetworkType, mode);
    if(mode == starBandNetworkType) {
        RLOGI("%s: request mode is equal to current mode, return directly", __FUNCTION__);
        RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
        return;
    }

#ifndef CP_SUPPORT_UNLOCK
    gsmBand = gsmBand & 0xFDFF;
#endif

    if((lteBandH == -1) && (lteBandL == -1)) {
        sprintf(cmd, "AT*BAND=%d,%d,%d", starBandNetworkType, gsmBand, umtsBand);
    } else if(lteBandH == -1) {
        sprintf(cmd, "AT*BAND=%d,%d,%d,,%d", starBandNetworkType, gsmBand, umtsBand, lteBandL);
    } else if(lteBandL == -1) {
        sprintf(cmd, "AT*BAND=%d,%d,%d,%d", starBandNetworkType, gsmBand, umtsBand, lteBandH);
    } else {
        sprintf(cmd, "AT*BAND=%d,%d,%d,%d,%d", starBandNetworkType, gsmBand, umtsBand, lteBandH, lteBandL);
    }

    if(starBandNetworkType != 0 && !isDualLEnabled()) { //Not Gsm Only
        triggerPrevOperProc(3, modemSwitchOnMasterCardChange);
    }
    rilPsMutexLock();
    err = at_send_command_timeout(cmd, &response, TIMEOUT_EBAND);
    rilPsMutexUnlock();

    if(err < 0 || response->success == 0)
        goto error;
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_NEIGHBORINGCELL_IDS
 */
void ril_request_get_neighboring_cell_ids(int request, void * data, size_t datalen, RIL_Token token)
{
    int err;
    ATResponse * response = NULL;
    int mode, network;
    unsigned char nw_type;
    char * line;
    RIL_SOCKET_ID socketId = getSocketId();

    int simStatus = getSimStatus(socketId);
    if(simStatus == SIM_ABSENT) {
        RIL_onRequestComplete(token, RIL_E_SIM_ABSENT, NULL, 0);
        return;
    }
    //if screen state is OFF, CP will not indicate CELL IDS
//    if(!sScreenState[socketId]) goto error;

    err = at_send_command_singleline("AT+EEMGINFO=1", "+EEMGINFO:", &response);
    if(err < 0 || response->success == 0) goto error;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &mode);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &network);
    if(err < 0) goto error;

    nw_type = network;
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &nw_type, sizeof(unsigned char));

    at_response_free(response);
    uos_sleep(200);

    return;
error:
    nw_type = 0xFF;
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &nw_type, sizeof(unsigned char));
    at_response_free(response);

    return;
}

/**
 * RIL_REQUEST_GET_PREFERRED_NETWORK_TYPE
 *
 * Query the preferred network type (CS/PS domain, RAT, and operation mode)
 * for searching and registering.
 */
void ril_request_get_preferred_network_type(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int result[1];
    int mode;
    int rilNetworkType;
    char * line;
    int err;
    const int * networkType = NULL;
    int size;

    err = at_send_command_singleline("AT*BAND?", "*BAND:", &response);
    if(err < 0 || response->success == 0)
        goto error;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &mode);
    if(err < 0) goto error;

    getNetworkTypeAndSize(&networkType, &size);
    if(mode < 0 || mode >= size)
        rilNetworkType = -1;
    else {
        rilNetworkType = networkType[mode];
    }

    if(rilNetworkType < 0) goto error;

    RLOGI("%s: Preferred Network Type: %d", __FUNCTION__, rilNetworkType);
    result[0] = rilNetworkType;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(result));

    goto exit;

error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_SET_LOCATION_UPDATES
 *
 * Enables/disables network state change notifications due to changes in
 * LAC and/or CID (basically, *EREG=2 vs. *EREG=1).
 *
 * Note:  The RIL implementation should default to "updates enabled"
 * when the screen is on and "updates disabled" when the screen is off.
 *
 * See also: RIL_REQUEST_SCREEN_STATE, RIL_UNSOL_RESPONSE_NETWORK_STATE_CHANGED.
 */
void ril_request_set_location_updates(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int enable = ((int *)data)[0];
    if(enable) {
        int err = at_send_command("AT+CREG=2", &response);
        if(err < 0 || response->success == 0)
            goto error;
    }
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;

error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}
extern void notifyPDPClearOverIms(RIL_SOCKET_ID socketId);

#ifdef ASR_EXTENDED
void ril_request_add_uplmn(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    char cmd[MAX_AT_LENGTH];
    char * index = ((char **)data)[0];
    char * mccmnc = ((char **)data)[1];
    const char * act = ((char **)data)[2];
    char * isLTE = ((char **)data)[3];
    RLOGW("%s: isLTE: %s\n", __FUNCTION__, isLTE);

    if(!strcmp("1", isLTE)) {
        if(!strcmp("0", act)) { // GSM
            act = "1,0,0,0";
        } else if(!strcmp("1", act)) { // GSM Compact
            act = "0,1,0,0";
        } else if(!strcmp("2", act)) { // 3G
            act = "0,0,1,0";
        } else if(!strcmp("3", act)) { // LTE
            act = "0,0,0,1";
        } else { // no prefer act by default
            act = "0,0,0,0";
        }
    } else {
        if(!strcmp("0", act)) { // GSM
            act = "1,0,0";
        } else if(!strcmp("1", act)) { // GSM Compact
            act = "0,1,0";
        } else if(!strcmp("2", act)) { // 3G
            act = "0,0,1";
        } else { // no prefer act by default
            act = "0,0,0";
        }
    }

    sprintf(cmd, "AT+CPOL=%s,2,%s,%s", index, mccmnc, act);
    ril_handle_cmd_default_response(cmd, token);
}

void ril_request_get_uplmn(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    ATLine * p_cur;
    char * p_line = NULL;
    int strCount, value, err, bufIndex = 0;
    int actGsm, actGsmc, act3g, actLte = 0;
    char ** ppUplmn = NULL;
    char * pMccMnc = NULL;
    int isLTE = 0;

    err = at_send_command_multiline("AT+CPOL?", "+CPOL:", &response);
    if(err < 0 || response->success == 0)
        goto error;

    /* count the uplmn */
    for(value = 0, p_cur = response->p_intermediates; p_cur != NULL; p_cur = p_cur->p_next)
        value++;

    strCount = (value * VALUE_NUM_PER_UPLMN + 1) * sizeof(char *);
    ppUplmn = (char **)UI_MALLOC(strCount);
    if(ppUplmn == NULL)
        goto error;

    RLOGI("%s: strCount: %d\n", __FUNCTION__, strCount);
    memset(ppUplmn, 0, strCount);

    /* Analyze AT response and report */
    for(p_cur = response->p_intermediates; p_cur != NULL; p_cur = p_cur->p_next) {
        p_line = p_cur->line;
        err = at_tok_start(&p_line);
        if(err < 0) goto error;

        err = at_tok_nextint(&p_line, &value); // Index
        if(err < 0) goto error;
        ppUplmn[bufIndex] = (char *)UI_MALLOC(2 * sizeof(value) + 4);
        sprintf(ppUplmn[bufIndex++], "%d", value);

        err = at_tok_nextint(&p_line, &value); // Format, ignore it.
        if(err < 0) goto error;

        err = at_tok_nextstr(&p_line, &pMccMnc); // MCCMNC.
        if(err < 0) goto error;
        ppUplmn[bufIndex] = (char *)UI_MALLOC(2 * sizeof(pMccMnc) + 4);
        sprintf(ppUplmn[bufIndex++], "%s", pMccMnc);

        ppUplmn[bufIndex] = (char *)UI_MALLOC(2 * sizeof(int) +4);
        if(at_tok_hasmore(&p_line)) { // ACT is valid
            err = at_tok_nextint(&p_line, &actGsm); // gsm act.
            if(err < 0) goto error;
            err = at_tok_nextint(&p_line, &actGsmc); // gsm compact act.
            if(err < 0) goto error;
            err = at_tok_nextint(&p_line, &act3g); // 3G act.
            if(err < 0) goto error;
            if(at_tok_hasmore(&p_line)) {
                isLTE = 1;
                err = at_tok_nextint(&p_line, &actLte); // LTE act.
                if(err < 0) goto error;
            }

            if(actGsm == IS_ACT_VALID) {
                sprintf(ppUplmn[bufIndex++], "%d", ACT_GSM);
            } else if(actGsmc == IS_ACT_VALID) {
                sprintf(ppUplmn[bufIndex++], "%d", ACT_GSM_COMPACT);
            } else if(act3g == IS_ACT_VALID) {
                sprintf(ppUplmn[bufIndex++], "%d", ACT_3G);
            } else if(actLte == IS_ACT_VALID) {
                sprintf(ppUplmn[bufIndex++], "%d", ACT_LTE);
            } else {
                sprintf(ppUplmn[bufIndex++], "%d", -1);
            }
        } else { // does not response act, use GSM as default
            sprintf(ppUplmn[bufIndex++], "%d", ACT_GSM);
        }
    }
    RLOGI("%s: isLTE: %d\n", __FUNCTION__, isLTE);
    ppUplmn[bufIndex] = (char *)UI_MALLOC(8);
    sprintf(ppUplmn[bufIndex++], "%d", isLTE);

    RIL_onRequestComplete(token, RIL_E_SUCCESS, ppUplmn, strCount);
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    if(ppUplmn != NULL) {
        for(value = 0; value < bufIndex; value++)
            if(NULL != ppUplmn[value])UI_FREE(ppUplmn[value]);
        UI_FREE(ppUplmn);
    }
    at_response_free(response);

}

void ril_request_delete_uplmn(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    char cmd[MAX_AT_LENGTH];

    int index = ((int *)data)[0];
    sprintf(cmd, "AT+CPOL=%d", index);
    ril_handle_cmd_default_response(cmd, token);
}

void ril_request_select_band(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    RIL_MM_BAND_Info * set_band = (RIL_MM_BAND_Info *)data;
    ATResponse * response = NULL;
    int mode = set_band->ucMode;//-1;
    char cmd[MAX_AT_LENGTH];
//  int ril_mode = set_band->ucMode;//((int*)data)[0];
    int gsmBand = set_band->ucGsmBand;//((int*)data)[1];
    int umtsBand = 0; //((int*)data)[2];
    int lteBandH = set_band->usLteBandH;//((int*)data)[3];
    int lteBandL = set_band->ulLteBandL;//((int*)data)[4];
    RIL_SOCKET_ID socketId = getSocketId();
    int err;

#if 0
    if(ril_mode < 0 || ril_mode >= (int)sizeof(RILNetworkTypeTostarBandNetworkType) / (int)sizeof(RILNetworkTypeTostarBandNetworkType[0]))
        mode = -1;
    else {
        mode = RILNetworkTypeTostarBandNetworkType[ril_mode];
        mode = CSFB2DualMode(mode);
    }

    if(mode == -1) {
        RLOGW("%s: this networkmode not support: %d\n", __FUNCTION__, ril_mode);
        goto error;
    }
#endif

    if((mode < 0) || (mode > 18)) goto error;
    if(gsmBand < 0) goto error;
//  if (umtsBand < 0) goto error;  ///not support 3G
    if(lteBandH < 0) lteBandH = -1;
    if(lteBandL < 0) lteBandL = -1;

#ifndef CP_SUPPORT_UNLOCK
    gsmBand = gsmBand & 0xFDFF;
#endif

    if((lteBandH == -1) && (lteBandL == -1)) {
        sprintf(cmd, "AT*BAND=%d,%d,%d", mode, gsmBand, umtsBand);
    } else if(lteBandH == -1) {
        sprintf(cmd, "AT*BAND=%d,%d,%d,,%d", mode, gsmBand, umtsBand, lteBandL);
    } else if(lteBandL == -1) {
        sprintf(cmd, "AT*BAND=%d,%d,%d,%d", mode, gsmBand, umtsBand, lteBandH);
    } else {
        sprintf(cmd, "AT*BAND=%d,%d,%d,%d,%d", mode, gsmBand, umtsBand, lteBandH, lteBandL);
    }
    err = at_send_command_timeout(cmd, &response, TIMEOUT_EBAND);
    if(err < 0 || response->success == 0)
        goto error;

    resetLocalRegInfo(socketId);
    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;

error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_get_band(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int result[5];
    int mode;
    int gsmBand, umtsBand, lteBandH, lteBandL;
    char * line;

    int err = at_send_command_singleline("AT*BAND?", "*BAND:", &response);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL)
        goto error;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &mode);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &gsmBand);
    if(err < 0) goto error;
    if(gsmBand < 0) gsmBand = -1;

    err = at_tok_nextint(&line, &umtsBand);
    if(err < 0) goto error;
    if(umtsBand < 0) umtsBand = -1;

    err = at_tok_nextint(&line, &lteBandH);
    if(err < 0 || lteBandH < 0) lteBandH = -1;

    err = at_tok_nextint(&line, &lteBandL);
    if(err < 0 || lteBandL < 0) lteBandL = -1;

    RLOGI("%s: mode=%d, gsmband=%d, umtsband=%d, ltebandH=%d, ltebandL=%d", __FUNCTION__, mode, gsmBand, umtsBand, lteBandH, lteBandL);
    result[0] = mode;
    result[1] = gsmBand;
    result[2] = umtsBand;
    result[3] = lteBandH;
    result[4] = lteBandL;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, result, sizeof(result));
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);

}

/**
 * RIL_REQUEST_SET_NETWORK_SELECTION_MANUAL_EXT
 *
 * Manually select a specified network.
 *
 * The radio baseband/RIL implementation will try to camp on the manually
 * selected network regardless of coverage, i.e. there is no fallback to
 * automatic network selection.
 */
void ril_request_set_network_selection_manual_ext(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    RIL_OperInfo_ext * info = (RIL_OperInfo_ext *)data;
    ATResponse * response = NULL;
    int err;
    char cmd[MAX_AT_LENGTH];
    RIL_SOCKET_ID socketId = getSocketId();

    resetLocalRegInfo(socketId);

    sprintf(cmd, "AT+COPS=1,2,%s,%d", info->operNumStr, info->act);
    err = at_send_command_timeout(cmd, &response, TIMEOUT_COPS);
    if(err < 0 || response->success == 0)
        goto error;

    // Local Registration info is reset to null before auto network selection,
    // CP may not send +CREG/+CGREG indicator if registration information is not
    // changed during auto network selection.
    // we need query these info here. Otherwise, the cached local registradion
    // info may be not synced with CP.
    libGetVoiceRegInfo(NULL, socketId);
    libGetDataRegInfo(NULL, NULL, socketId);

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED, NULL, 0, socketId);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

/**
 * RIL_REQUEST_CANCEL_PLMN_SEARCH
 *
 * Notify modem to cancel plmn search
 */
void ril_request_cancel_plmn_search(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);
    UNUSED(data);

    ATResponse * response = NULL;
    int err;

    err = at_send_command_timeout("AT+CPLMNS", &response, TIMEOUT_CPLMNS);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_set_wifictrl(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int *param = (int *)data;
    ATResponse * response = NULL;
    int err;
    char cmd[MAX_AT_LENGTH];

    sprintf(cmd, "AT*WIFICTRL=%d,%d,%d,%d,%d", param[0],param[1],param[2],param[3],param[4]);
    err = at_send_command_timeout(cmd, &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_set_OOS_phase_period(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int *param = (int *)data;
    ATResponse * response = NULL;
    int err;
    char cmd[MAX_AT_LENGTH];

    printf("##interface## %s\n");

    if(param[0])
    {
        sprintf(cmd, "AT+OOSPP=%d,%d,%d,%d", param[0],param[1],param[2],param[3]);
    }
    else
    {
        sprintf(cmd, "AT+OOSPP=0");
    }
    
    err = at_send_command_timeout(cmd, &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}


void ril_request_set_cellchange(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int option = ((int *)data)[0];
    ATResponse * response = NULL;
    int err;
    char cmd[MAX_AT_LENGTH];
    RIL_SOCKET_ID socketId = getSocketId();

    sprintf(cmd, "AT+MEDCR=0,30,%d", !!option);
    err = at_send_command_timeout(cmd, &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_set_volte(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int option = ((int *)data)[0];
    ATResponse * response = NULL;
    int err;
    char cmd[MAX_AT_LENGTH];
    RIL_SOCKET_ID socketId = getSocketId();

    sprintf(cmd, "AT*IMSCFG=%s,%s", "switch", option ? "on" : "off");
    err = at_send_command_timeout(cmd, &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_get_volte2(RIL_Token token)
{
    ATResponse * response = NULL;
    int result = -1;
    char *line = NULL;
    char *strSwitch = NULL;
    char *switchFlag = NULL;
    static bool getVolteAgain = false;

    int err = at_send_command_singleline("AT*IMSRCFG=switch", "*IMSRCFG:", &response);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL) {
        if (getVolteAgain)
            goto error;
        goto error0;
    }

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextstr(&line, &strSwitch);
    if(err < 0) goto error;

    if (strcmp(strSwitch, "switch") == 0) {
        err = at_tok_nextstr(&line, &switchFlag);
        if(err < 0) goto error;
        result = ((strcmp(switchFlag, "on") == 0) ? 1 : 0);
    }
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(result));
    goto exit;
error0:
    struct timeval timeDelay;
    timeDelay.tv_usec = 0;
    timeDelay.tv_sec = 3;
    getVolteAgain = true;
    enqueDelayed(getWorkQueue(SERVICE_MM, getSocketId()), ril_request_get_volte2, (void *)token, &timeDelay, getSocketId());
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_get_volte(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int result = -1;
    char *line = NULL;
    char *strSwitch = NULL;
    char *switchFlag = NULL;

    int err = at_send_command_singleline("AT*IMSRCFG=switch", "*IMSRCFG:", &response);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL)
        goto error0;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextstr(&line, &strSwitch);
    if(err < 0) goto error;

    if (strcmp(strSwitch, "switch") == 0) {
        err = at_tok_nextstr(&line, &switchFlag);
        if(err < 0) goto error;
        result = ((strcmp(switchFlag, "on") == 0) ? 1 : 0);
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(result));
    goto exit;

error0:
    struct timeval timeDelay;
    timeDelay.tv_usec = 0;
    timeDelay.tv_sec = 3;
    enqueDelayed(getWorkQueue(SERVICE_MM, getSocketId()), ril_request_get_volte2, (void *)token, &timeDelay, getSocketId());
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

void ril_request_set_ncellbch(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int option = ((int *)data)[0];
    ATResponse * response = NULL;
    int err;
    char * line;
    char cmd[MAX_AT_LENGTH];
    int mode, network;
    unsigned char nw_type;

    sprintf(cmd, "AT+EEMGINFO=%d", !!option);
    err = at_send_command_singleline_timeout(cmd, "+EEMGINFO:", &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &mode);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &network);
    if(err < 0) goto error;

    nw_type = network;
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &nw_type, sizeof(unsigned char));

    at_response_free(response);
    uos_sleep(200);

    return;
error:
    nw_type = 0xFF;
    RIL_onRequestComplete(token, RIL_E_SUCCESS, &nw_type, sizeof(unsigned char));
    at_response_free(response);
}

void ril_request_get_radiostate(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(datalen);

    int result = -1;

    result = get_phone_functionality();
    if (result < 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, &result, sizeof(result));
    return;

error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
    return;
}

void ril_request_get_imsvops(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);
    UNUSED(data);
    UNUSED(datalen);

    ATResponse * response = NULL;
    int nwimsvops;
    int reporting;
    char * line;

    int err = at_send_command_singleline("AT+CIREP?", "+CIREP:", &response);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL)
        goto error;

    line = response->p_intermediates->line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &reporting);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &nwimsvops);
    if(err < 0) goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, &nwimsvops, sizeof(nwimsvops));
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);

}

void ril_request_get_calinfo(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);

    char *line = NULL;
    char *strItem = NULL;
    char *strInfo = NULL;
    ATResponse * response = NULL;
    char cmd[MAX_AT_LENGTH];

    sprintf(cmd, "AT*CALINFO=%s", (char *)data);
    int err = at_send_command_singleline(cmd, "*CALINFO:", &response);
    if(err < 0 || response->success == 0 || response->p_intermediates == NULL)
        goto error;

    line = response->p_intermediates->line;
    RLOGI("%s: line:%s ", __FUNCTION__,line);

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextstr(&line, &strItem);
    if(err < 0) goto error;

    err = at_tok_nextstr(&line, &strInfo);
    if(err < 0) goto error;

    if (strstr(strInfo, "PASS") == NULL) {
        RLOGI("%s: strInfo:%s ", __FUNCTION__,strInfo);
        goto error;
    }

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);
    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

//onego-zly-20230816
void ril_request_save_calinfo(int request, void * data, size_t datalen, RIL_Token token)
{
    UNUSED(request);

    char *line = NULL;
    char *strItem = NULL;
    char *strInfo = NULL;
    int err;
    ATResponse * response = NULL;
   // char cmd[MAX_AT_LENGTH];

    //sprintf(cmd, "AT*MRD_CDF=%s", (char *)data);
    err = at_send_command_timeout("AT*MRD_CDF=U,PL_Cal_Info.nvm", &response, TIMEOUT_DEFALUT);
    if(err < 0 || response->success == 0)
        goto error;

    RIL_onRequestComplete(token, RIL_E_SUCCESS, NULL, 0);

    goto exit;
error:
    RIL_onRequestComplete(token, RIL_E_MODEM_ERR, NULL, 0);
exit:
    at_response_free(response);
}

#endif

static inline int updateValue(int * p, int value)
{
    if(*p != value) {
        *p = value;
        return 1;
    }
    return 0;
}

void handle_bandind(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);
    RIL_SOCKET_ID socketId = getSocketId();

    Ril_MM_Response_Bandind((RIL_SIM_ID)socketId, s);
}

/* Process ind msg of signal length */
void handle_csq(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);

    char * line = NULL, *linesave = NULL;
    int err;
    RIL_SignalStrength_mm result;
    RIL_SOCKET_ID socketId = getSocketId();

    memset(&result, 0, sizeof(result));
    init_signal_strength(result);
    line = lv_strdup(s);
    linesave = line;
    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &sCSQ[socketId].rssi);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &sCSQ[socketId].ber);
    if(err < 0) goto error;

    if(!getCESQSupport(socketId) && gSignalIntervalMode[socketId])
        filter_csq(socketId);
#ifdef MMI_ASR_RIL_BRINGUP
    if(/*!getCESQSupport*/IsGSMAttached(socketId)) {
        reportSignalStrength(NULL, socketId);
    }

#else
    /* CP is asserted or resetting, we need to reset our global variables */
    if(ModemDevice::get_current_modem(socketId)->is_cp_assert(sCSQ[socketId].rssi, sCSQ[socketId].ber)) { //Hope  unify the assert process for all modems in the future.
        resetLocalRegInfo(socketId);
        resetSimStatusInfo(socketId);
        setRadioState(RADIO_STATE_UNAVAILABLE, socketId);

        result.GW_SignalStrength.signalStrength = 67;
        result.GW_SignalStrength.bitErrorRate = 89;

        // set LTE signal strength
        // to match framework/base/telephony/java/android/telephony/SignalStrength.java
        result.LTE_SignalStrength.signalStrength = 99;
        result.LTE_SignalStrength.rsrp = 0x7FFFFFFF;
        result.LTE_SignalStrength.rsrq = 0x7FFFFFFF;
        result.LTE_SignalStrength.rssnr = 0x7FFFFFFF;
        result.LTE_SignalStrength.cqi = 0x7FFFFFFF;

        RIL_onUnsolicitedResponse(RIL_UNSOL_SIGNAL_STRENGTH, &result, sizeof(result), socketId);

        result.GW_SignalStrength.bitErrorRate = 88;
        RIL_onUnsolicitedResponse(RIL_UNSOL_SIGNAL_STRENGTH, &result, sizeof(result), socketId);
    } else if(!getCESQSupport(socketId)) {
        reportSignalStrength(NULL, socketId);
    }
#endif

    /* Free allocated memory and return */
    if(linesave != NULL) UI_FREE(linesave);
    return;

error:
    if(linesave != NULL) UI_FREE(linesave);
    RLOGE("%s: Error parameter in ind msg: %s", __FUNCTION__, s);
    return;
}

/* Process ind msg of signal length */
void handle_cesq(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);

    char * line = NULL, *linesave = NULL;
    int err;
    int rxlevdbm;
    int rsrpdbm;
#ifdef MMI_ASR_RIL_BRINGUP
#else
    int ecnodb;
#endif
    RIL_SOCKET_ID socketId = getSocketId();

    line = lv_strdup(s);
    linesave = line;
    err = parse_cesq(&line, &sCESQ[socketId].rxlev, &sCESQ[socketId].ber,
                     &sCESQ[socketId].rscp, &sCESQ[socketId].ecno,
                     &sCESQ[socketId].rsrq, &sCESQ[socketId].rsrp);
    if(err < 0) goto error;

    rxlevdbm = convertRxlevToDbm(sCESQ[socketId].rxlev);
    sCSQ[socketId].ber = sCESQ[socketId].ber;

    rsrpdbm = convertRscpToDbm(sCESQ[socketId].rsrp);
//  ecnodb = convertEcnoToDB(sCESQ[socketId].ecno);
    sCSQ[socketId].rssi = process_rssi(rxlevdbm, rsrpdbm);

    if(gSignalIntervalMode[socketId]) {
        filter_csq(socketId);
        filter_cesq(socketId);
    }

    if (IsLTEAttached(socketId)) reportSignalStrength(NULL, socketId);

    /* Free allocated memory and return */
    if(linesave != NULL) UI_FREE(linesave);
    return;

error:
    if(linesave != NULL) UI_FREE(linesave);
    RLOGE("%s: Error parameter in ind msg: %s", __FUNCTION__, s);
    return;
}

#define CHECK_EMPTY_COMMA(p_cur, p_out) \
    do { \
        if (**p_cur == ',') { \
            (*p_cur)++; \
            *p_out = -1; \
            return 0; \
        } \
    } while (0)

static inline int next_hex_int(char ** p_cur, int * p_out)
{
    CHECK_EMPTY_COMMA(p_cur, p_out);
    return at_tok_nexthexint(p_cur, p_out);
}

static inline int next_int(char ** p_cur, int * p_out)
{
    CHECK_EMPTY_COMMA(p_cur, p_out);
    return at_tok_nextint(p_cur, p_out);
}

#define NEXT_VALUE(line, value, func) \
    do { \
        if (!at_tok_hasmore(&line)) goto finish_parse; \
        if (func(&line, &value) < 0) goto error; \
    } while (0)

#define NEXT_HEX_INT(line, value) NEXT_VALUE(line, value, next_hex_int)
#define NEXT_INT(line, value) NEXT_VALUE(line, value, next_int)


/* Process ind msg of network registration status */
void handle_creg(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);

    int is_voice = strStartsWith(s, "+CREG:");
    int is_LTE = strStartsWith(s, "+CEREG:");
    int is_G = strStartsWith(s, "+CGREG:");
    RegState * p = NULL;
    RIL_SOCKET_ID socketId = getSocketId();

    if(is_voice) {
        p = &sCregState[socketId];
    } else if(is_G) {
        p = &sCgregState[socketId];
    } else if(is_LTE) {
        p = &sCeregState[socketId];
    } else {
        return;
    }

    RegState reg_state;
    bool changed = false, act_changed = false;
    int value;
    int oldRegstat = p->stat;
    char * line = lv_strdup(s);
    char * linesave = line;

    int err = at_tok_start(&line);
    if(err < 0) goto error;

    // state
    NEXT_INT(line, reg_state.stat);
    if(is_voice)
        reg_state.stat = registerStateTransform(reg_state.stat);

    // lac
    NEXT_HEX_INT(line, reg_state.lac);

    // cell id
    NEXT_HEX_INT(line, reg_state.cid);

    // act
    NEXT_INT(line, reg_state.act);

    if(is_G) {
        // rac
        NEXT_INT(line, value);
    } else if(is_voice) {
        // psc
        NEXT_HEX_INT(line, reg_state.psc);
    }

    // cause type
    NEXT_INT(line, value);

    // reject cause
    NEXT_INT(line, reg_state.rejectCause);

finish_parse:
    changed = memcmp(p, &reg_state, sizeof(RegState)) != 0;
    if(changed) {
        RIL_MM_Reg_info *pRegInfo;

        *p = reg_state;

        if(linesave != NULL) UI_FREE(linesave);
        // reset local oper info
        sOperInfo[socketId].mode = -1;
        sOperInfo[socketId].operLongStr[0] = '\0';
        sOperInfo[socketId].operShortStr[0] = '\0';
        sOperInfo[socketId].operNumStr[0] = '\0';

        if (((!is_voice) && (reg_state.act != 7)) ||
            ((is_voice) && (reg_state.stat == 2)) ||
            ((is_voice) && (reg_state.act == 7)&&(reg_state.stat!=1))) return;

        pRegInfo = (RIL_MM_Reg_info *)UI_MALLOC(sizeof(RIL_MM_Reg_info));
        if (pRegInfo != NULL) {
            memset(pRegInfo, 0, sizeof(RIL_MM_Reg_info));
            pRegInfo->rt = convertActToRilRadioTech(reg_state.act);
            pRegInfo->stat = reg_state.stat;
            if (((sCregState[socketId].act == 0) || (sCregState[socketId].act == 1) || (sCregState[socketId].act == 3))
                    && (sCgregState[socketId].stat == 0) && (sCeregState[socketId].stat == 0)) {
               pRegInfo->rt = RADIO_TECH_GSM;
            }
            pRegInfo->lac = reg_state.lac;
            pRegInfo->cid = reg_state.cid;
            Ril_MM_Response_Radio_Tech_Changed(pRegInfo, RIL_SIM_ID(socketId));
        }

        return;
    }
    act_changed = p->act != reg_state.act;
    if(changed) {
        if(is_voice || is_LTE) {
            reg_state.stat = checkForSmsOnlyMode(reg_state.stat);
        }
        *p = reg_state;
        // reset local oper info
        sOperInfo[socketId].mode = -1;
        sOperInfo[socketId].operLongStr[0] = '\0';
        sOperInfo[socketId].operShortStr[0] = '\0';
        sOperInfo[socketId].operNumStr[0] = '\0';
        /* Report to upper layer */
        RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED, NULL, 0, socketId);
        if(is_voice && act_changed) {
            int radioTech = convertActToRilRadioTech(p->act);
            RIL_onUnsolicitedResponse(RIL_UNSOL_VOICE_RADIO_TECH_CHANGED, &radioTech, sizeof(radioTech), socketId);
        }
    }
    if(worldPhoneAutoSwitchEnabled(socketId)) {
        if(is_voice) {
            if(hasService(oldRegstat) && !hasService(p->stat)) {
                RIL_RadioState state = getRadioState(socketId);
                if(state == RADIO_STATE_ON) {
                    if(clock_gettime(CLOCK_MONOTONIC, &g_tp)) {
                        RLOGE("%s get time error\n", __FUNCTION__);
                    } else {
                        struct timeval timeDelay;
                        timeDelay.tv_usec = 0;
                        timeDelay.tv_sec = g_modem_switch_time_array[g_modem_switch_time_array_index];
                        enqueDelayed(getWorkQueue(SERVICE_MM, socketId), modemSwitchOnNoService, (void *)(long)g_modem_switch_time_array_index, &timeDelay, socketId);
                    }
                }
            } else if(!hasService(oldRegstat) && hasService(p->stat)) {
                g_modem_switch_time_array_index = 0;
                if(clock_gettime(CLOCK_MONOTONIC, &g_tp))
                    RLOGE("%s get time error\n", __FUNCTION__);
            }
        }
    }

    /* Free allocated memory and return */
    if(linesave != NULL) UI_FREE(linesave);
    return;

error:
    if(linesave != NULL) UI_FREE(linesave);
    RLOGE("%s: Error parameter in ind msg: %s", __FUNCTION__, s);
    return;
}

/* Process ind msg of network time */
void handle_nitz(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);

    char * response, *linesave = NULL;
    int err;
    char * dst, *tz;
    char nitztime[25];
    RIL_SOCKET_ID socketId = getSocketId();

    linesave = lv_strdup(s);
    response = linesave;
    err = at_tok_start(&response);
    if(err < 0) goto error;

    err = at_tok_nextstr(&response, &dst);
    if(err < 0) goto error;
    if(at_tok_hasmore(&response)) {
        err = at_tok_nextstr(&response, &tz);
        if(err < 0) goto error;

        if(at_tok_hasmore(&response)) {
            // show the result at last. form required by Android: "yy/mm/dd,hh:mm:ss(+/-)tz,dt" in UTC
            snprintf(nitztime, sizeof(nitztime), "%s%s,%s", response, tz, dst);

            //save last Nitz recevied
            clock_gettime(CLOCK_MONOTONIC, &lastNitzTimeReceived);
            snprintf(lastNitzTime, sizeof(lastNitzTime), "%s", response);

            //RIL_onUnsolicitedResponse( RIL_UNSOL_NITZ_TIME_RECEIVED, nitztime, strlen(nitztime), socketId);
        } else {
            //sometimes GMM only contains tz and dst, if it comes not too later than last NITZ time,
            //we use last Nitz time to notify uper layer, it should be removed after android support
            //update TZ only
            struct timespec nitzTimeReceived;
            clock_gettime(CLOCK_MONOTONIC, &nitzTimeReceived);
            if(nitzTimeReceived.tv_sec - lastNitzTimeReceived.tv_sec  < 10 * 60) {
                // show the result at last. form required by Android: "yy/mm/dd,hh:mm:ss(+/-)tz,dt" in UTC
                snprintf(nitztime, sizeof(nitztime), "%s%s,%s", lastNitzTime, tz, dst);
                lastNitzTimeReceived = nitzTimeReceived;

                //RIL_onUnsolicitedResponse( RIL_UNSOL_NITZ_TIME_RECEIVED, nitztime, strlen(nitztime), socketId);
            }
        }
        RIL_NitzTimeInfo info;
        strcpy(info.NitzTimeData, nitztime);
        info.NitzTimeDataSize = strlen(nitztime);
        Ril_MM_Response_Nitz_Time_Received(&info, (RIL_SIM_ID)socketId);
    }

    /* Free allocated memory and return */
    if(linesave != NULL) UI_FREE(linesave);
    return;

error:
    if(linesave != NULL) UI_FREE(linesave);
    RLOGE("%s: Error parameter in ind msg: %s", __FUNCTION__, s);
    return;
}

void handle_copn(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);

    char * response, *linesave = NULL;
    int err;
    int format;
    int changed = 0;
    RIL_SOCKET_ID socketId = getSocketId();

    linesave = lv_strdup(s);
    response = linesave;
    err = at_tok_start(&response);
    if(err < 0) goto error;

    err = at_tok_nextint(&response, &format);
    if(err < 0) goto error;
    //long format alphanumeric <oper>
    if(format == MM_NETOP_ID_FORMAT_ALPHA_LONG) {
        char * longname;
        err = at_tok_nextstr(&response, &longname);
        if(err < 0) goto error;
        if(strcmp(sOperInfo[socketId].operLongStr, longname)) {
            strlcpy(sOperInfo[socketId].operLongStr, longname, sizeof(sOperInfo[socketId].operLongStr));
            changed = 1;
        }
    }
    //short format alphanumeric <oper>
    else if(format == MM_NETOP_ID_FORMAT_ALPHA_SHORT) {
        char * shortName;
        err = at_tok_nextstr(&response, &shortName);
        if(err < 0) goto error;
        if(strcmp(sOperInfo[socketId].operShortStr, shortName)) {
            strlcpy(sOperInfo[socketId].operShortStr, shortName, sizeof(sOperInfo[socketId].operShortStr));
            changed = 1;
        }
    } else {
        RLOGI("onUnsolicited_mm: not supportted oper format: %d", format);
        goto error;
    }

    if(changed) {
        /* Report to upper layer */
        //RIL_onUnsolicitedResponse(RIL_UNSOL_RESPONSE_VOICE_NETWORK_STATE_CHANGED, NULL, 0, socketId);
        Ril_MM_Response_Network_State_Changed((RIL_SIM_ID)socketId);
    }

    /* Free allocated memory and return */
    if(linesave != NULL) UI_FREE(linesave);
    return;

error:
    if(linesave != NULL) UI_FREE(linesave);
    RLOGE("%s: Error parameter in ind msg: %s", __FUNCTION__, s);
    return;
}

void handle_eemumtsind(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);

    char * line = NULL, *linesave = NULL;
    int err;
    //UMTS neighboring cell info
    int loop, i;
    int psc; //Primary Scrambling Code (as described in TS 25.331) in 9 bits in UMTS FDD ,
    int cellparaId; // cellParameterId in UMTS TDD
    int rscp;//Level index of CPICH Received Signal Code Power in UMTS FDD, PCCPCH Received Signal Code Power in UMTS TDD
    RIL_SOCKET_ID socketId = getSocketId();

    //TDD case (+EEMUMTSINTER: or +EEMUMTSINTRA: same structure)
    //+EEMUMTSINTER: 0, -826, 0, -792, 1120, 0, 65534, 0, 10071, 71
    //+EEMUMTSINTER: index, pccpchRSCP, utraRssi, sRxLev,mcc, mnc, lac, ci, arfcn, cellParameterId

    //FDD case (+EEMUMTSINTER: or +EEMUMTSINTRA: same structure)
    //+EEMUMTSINTER: 0, -32768, 0, -32768, -144, -760, 65535, 65535, 65534, 0, 10663, 440
    //+EEMUMTSINTER: index, cpichRSCP, utraRssi, cpichEcN0, sQual, sRxLev,mcc, mnc, lac, ci, arfcn, psc

    if(sCellNumber[socketId] < MAX_NEIGHBORING_CELLS) {
        line = lv_strdup(s);
        linesave = line;
        err = at_tok_start(&line);
        if(err < 0) goto error;

        err = at_tok_nextint(&line, &loop);
        if(err < 0) goto error;

        err = at_tok_nextint(&line, &rscp);
        if(err < 0) goto error;

        for(i = 0; i < 7; i++) {
            err = at_tok_nextint(&line, &loop);
            if(err < 0) goto error;
        }

        err = at_tok_nextint(&line, &cellparaId);
        if(err < 0) goto error;


        //FDD cases
        if(at_tok_hasmore(&line)) {
            err = at_tok_nextint(&line, &loop);
            if(err < 0) goto error;

            err = at_tok_nextint(&line, &psc);
            if(err < 0) goto error;

            sNeighboringCell[socketId][sCellNumber[socketId]].cid = (char *)UI_MALLOC(8);
            sprintf(sNeighboringCell[socketId][sCellNumber[socketId]].cid, "%x", psc);
            sNeighboringCell[socketId][sCellNumber[socketId]].rssi = rscp;
            sCellNumber[socketId]++;


            RLOGI("onUnsolicited_mm new cell info cid:%s, rssi: %d", sNeighboringCell[socketId][sCellNumber[socketId] - 1].cid, rscp);

        } else {
            sNeighboringCell[socketId][sCellNumber[socketId]].cid = (char *)UI_MALLOC(8);
            sprintf(sNeighboringCell[socketId][sCellNumber[socketId]].cid, "%x", cellparaId);
            sNeighboringCell[socketId][sCellNumber[socketId]].rssi = rscp;
            sCellNumber[socketId]++;

            RLOGI("onUnsolicited_mm new cell info cid:%s, rssi: %d", sNeighboringCell[socketId][sCellNumber[socketId] - 1].cid, rscp);
        }

    } else {
        RLOGD("onUnsolicited_mm ignor cell info ");
    }

    /* Free allocated memory and return */
    if(linesave != NULL) UI_FREE(linesave);
    return;

error:
    if(linesave != NULL) UI_FREE(linesave);
    RLOGE("%s: Error parameter in ind msg: %s", __FUNCTION__, s);
    return;
}

#define EEMGINFOC_VAL_MAX 12
void handle_eemginfonc(const char * s, const char * smsPdu)
{
    RIL_Gsm_NCell_Info * n_cell = NULL;
    RIL_Gsm_Cell_Info * cell = NULL;
    RIL_SOCKET_ID socket_id;
    int * pval = NULL;
    int cnt, err, i;
    char * line = NULL, *line_s = NULL;

    line = lv_strdup(s);
    if(!line) {
        printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
        goto error;
    }
    line_s = line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    err = at_tok_nextint(&line, &cnt);
    if(err < 0) goto error;

    pval = (int *)UI_MALLOC(cnt * sizeof(int));
    if(!pval) {
        printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
        goto error;
    }

    n_cell = (RIL_Gsm_NCell_Info *)UI_MALLOC(sizeof(RIL_Gsm_NCell_Info));
    if(!pval) {
        printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
        goto error;
    }

    n_cell->Ncell = (RIL_Gsm_Cell_Info *)UI_MALLOC(cnt * sizeof(RIL_Gsm_Cell_Info));
    if(n_cell->Ncell) {
        printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
        goto error;
    }

    for(i = 0; i < cnt; i++) {
        at_tok_nint(&line, EEMGINFOC_VAL_MAX, pval);
        cell = n_cell->Ncell + i;
        cell->mcc = pval[0];
        cell->mnc = pval[1];
        cell->lac = pval[2];
        cell->rxlev = pval[5];
        cell->bsic = pval[6];
        cell->arfcn = pval[9];
    }
    n_cell->Number = cnt;

    if(line_s) UI_FREE(line_s);
    if(pval) UI_FREE(pval);

    socket_id = getSocketId();
    Ril_EEM_Response_Gsm_Ncell_Info((RIL_SIM_ID)socket_id, n_cell);

    return;
error:
    printf("%s failed\n", __FUNCTION__);
    if(line_s) UI_FREE(line_s);
    if(pval) UI_FREE(pval);
    if(n_cell) {
        if(n_cell->Ncell) UI_FREE(n_cell->Ncell);
        UI_FREE(n_cell);
    }
    return;
}

/* Porcess ind msg of ServiceRestrictionsInd */
void handle_msri(const char * s, const char * smsPdu)
{
    UNUSED(s);
    UNUSED(smsPdu);
}

void handle_eemumtsinterrat(const char * s, const char * smsPdu)
{
    UNUSED(s);
    UNUSED(smsPdu);
    // placeholder
}

void handle_eemumtssvc(const char * s, const char * smsPdu)
{
    UNUSED(s);
    UNUSED(smsPdu);
    // placeholder
}

void handle_eemginfobasic(const char * s, const char * smsPdu)
{
    UNUSED(s);
    UNUSED(smsPdu);
    // placeholder
}

#define EEMGINFOSVC_VAL_MAX 23
void handle_eemginfosvc(const char * s, const char * smsPdu)
{
    RIL_Gsm_Cell_Info * scell;
    RIL_SOCKET_ID socket_id;
    int * pval;
    char * line;
    int cnt;

    pval = at_tok_read_ints(s, EEMGINFOSVC_VAL_MAX, &line, &cnt);
    if(!pval || (cnt != EEMGINFOSVC_VAL_MAX)) goto error;

    scell = (RIL_Gsm_Cell_Info *)UI_MALLOC(sizeof(RIL_Gsm_Cell_Info));
    if(!scell) {
        printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
        goto error;
    }

    scell->mcc = pval[0];
    scell->mnc = pval[1];
    scell->lac = pval[2];
    scell->cellid = pval[3];
    scell->bsic = pval[6];
    scell->rxlev = pval[11];
    scell->arfcn = pval[22];

    UI_FREE(pval);

    socket_id = getSocketId();
    Ril_EEM_Response_Gsm_Scell_Info((RIL_SIM_ID)socket_id, scell);
    return;

error:
    if(pval) UI_FREE(pval);
    if(scell) UI_FREE(scell);
    printf("%s failed\n", __FUNCTION__);
    return;
}

void handle_eemginfops(const char * s, const char * smsPdu)
{
    UNUSED(s);
    UNUSED(smsPdu);
    // placeholder
}

void handle_eemginbftm(const char * s, const char * smsPdu)
{
    UNUSED(s);
    UNUSED(smsPdu);
    // placeholder
}

void handle_cirepi(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);
    char * line = NULL;
    char * linesave = NULL;
    int err;
    int nwimsvops;
    RIL_SOCKET_ID socketId = getSocketId();

    line = lv_strdup(s);
    linesave = line;
    err = at_tok_start(&line);
    if(err < 0) goto exit;
    err = at_tok_nextint(&line, &nwimsvops);
    if(err < 0) goto exit;

    if(nwimsvops == 1) {
        setTelephonyProperties(FLAG_NW_IMS_VOPS, 0, socketId);
    } else {
        setTelephonyProperties(0, FLAG_NW_IMS_VOPS, socketId);
    }
    RIL_onUnsolicitedResponse(RIL_UNSOL_IMS_VOPS, &nwimsvops, sizeof(nwimsvops), socketId);

exit:
    if(linesave != NULL) UI_FREE(linesave);
}

#define EEMLTESVC_VAL_MAX 51
void handle_eemltesvc(const char * s, const char * smsPdu)
{
    int * pval;
    char * line;
    int cnt;
    RIL_Lte_Scell_Info * scell;
    RIL_SOCKET_ID socket_id;

    scell = (RIL_Lte_Scell_Info *)UI_MALLOC(sizeof(RIL_Lte_Scell_Info));
    if(!scell) {
        printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
        goto err;
    }

    pval = at_tok_read_ints(s, EEMLTESVC_VAL_MAX, &line, &cnt);
    if(!pval || (cnt != EEMLTESVC_VAL_MAX)) {
        UI_FREE(scell);
        goto err;
    }
    scell->mcc = pval[0];                             /* p1 */
    printf("scell->mcc is %d\n", scell->mcc);
    scell->mnc = pval[2];                             /* p3 */
    scell->tac = pval[3];                             /* p4 */
    scell->phycellid = pval[4];                       /* p5 */
    scell->euarfcn = pval[5];                         /* p6 */
    scell->uleuarfcn = pval[6];                       /* p7 */
    scell->band = pval[7];                            /* p8 */
    scell->dlbandwidth = pval[8];                     /* p9 */
    scell->rsrp = pval[9];                            /* p10 */
    scell->rsrq = pval[10];                           /* p11 */
    scell->sinr = pval[11];                           /* p12 */
    scell->errcmodestate = pval[12];                  /* p13 */
    scell->emmstate = pval[13];                       /* p14 */
    scell->servicestate = pval[14];                   /* p15 */
    scell->issingleemmrejectcause = pval[15];         /* p16 */
    scell->emmrejectcause = pval[16];                 /* p17 */
    scell->mmegroupid = pval[17];                     /* p18 */
    scell->mmecode = pval[18];                        /* p19 */
    scell->mtmsi = pval[19];                          /* p20 */
    printf("scell->mtmsi is %d\n", scell->mtmsi);
    scell->scellpresent = pval[20];                   /* p21 */
    scell->cellid = pval[21];                         /* p22 */
    scell->subframeassigntype = pval[22];             /* p23 */
    scell->specialsubframepatterns = pval[23];        /* p24 */
    scell->transmode = pval[24];                      /* p25 */
    scell->mainrsrp = pval[25];                       /* p26 */
    scell->diversityrsrp = pval[26];                  /* p27 */
    scell->mainrsrq = pval[27];                       /* p28 */
    scell->diversityrsrq = pval[28];                  /* p29 */
    scell->rssi = pval[29];                           /* p30 */
    scell->cqi = pval[30];                            /* p31 */
    scell->pathloss = pval[31];                       /* p32 */
    scell->tb0dltpt = pval[32];                       /* p33 */
    scell->tb1dltpt = pval[33];                       /* p34 */
    scell->tb0dlpeaktpt = pval[34];                   /* p35 */
    scell->tb1dlpeaktpt = pval[35];                   /* p36 */
    scell->tb0ulpeaktpt = pval[36];                   /* p37 */
    scell->tb1ulpeaktpt = pval[37];                   /* p38 */
    scell->dlthroughput = pval[38];                   /* p39 */
    scell->dlpeakthroughput = pval[39];               /* p40 */
    printf("scell->dlpeakthroughput is %d\n", scell->dlpeakthroughput);
    scell->averdlprb = pval[40];                      /* p41 */
    scell->avercqitb0 = pval[41];                     /* p42 */
    scell->avercqitb1 = pval[42];                     /* p43 */
    scell->rankindex = pval[43];                      /* p44 */
    scell->granttotal = pval[44];                     /* p45 */
    scell->ulthroughput = pval[45];                   /* p46 */
    scell->ulpeakthroughput = pval[46];               /* p47 */
    scell->currpuschtxpower = pval[47];               /* p48 */
    scell->averulprb = pval[48];                      /* p49 */
    scell->dlbler = pval[49];                         /* p50 */
    scell->ulbler = pval[50];                         /* p51 */
    printf("scell->ulbler is %d\n", scell->ulbler);

    socket_id = getSocketId();
    UI_FREE(pval);
    Ril_EEM_Response_Lte_Scell_Info((RIL_SIM_ID)socket_id, scell);

    return;
err:
    printf("%s failed, %s\n", __FUNCTION__, s);
    return;
}

#define EEMLTE_FREQ_VAL_MAX 6
static RIL_Lte_Ncell_Info * eemlte_freq(const char * s, const char * smsPdu)
{
    RIL_Lte_Ncell_Info * ncell;
    int * pval;
    char * line;
    int cnt;

    ncell = (RIL_Lte_Ncell_Info *)UI_MALLOC(sizeof(RIL_Lte_Ncell_Info));
    if(!ncell) {
        printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
        goto error;
    }

    pval = at_tok_read_ints(s, EEMLTE_FREQ_VAL_MAX, &line, &cnt);
    if(!pval || (cnt != EEMLTE_FREQ_VAL_MAX)) {
        UI_FREE(ncell);
        goto error;
    }

    ncell->index = pval[0]; /* p1 */
    ncell->phycellid = pval[1]; /* p2 */
    ncell->euarfcn = pval[2]; /* p3 */
    ncell->rsrp = pval[3]; /* p4 */
    ncell->rsrq = pval[4]; /* p5 */
    ncell->cellid = pval[5]; /* p6 */

    UI_FREE(pval);

    return ncell;
error:
    printf("%s failed, %s\n", __FUNCTION__, s);
    return NULL;
}

void handle_eemlteintra(const char * s, const char * smsPdu)
{
    RIL_Lte_Ncell_Info * ncell;
    RIL_SOCKET_ID socket_id;

    ncell = eemlte_freq(s, smsPdu);
    if(ncell) {
        socket_id = getSocketId();
        Ril_EEM_Response_Lte_Inter_Ncell_Info((RIL_SIM_ID)socket_id, ncell);
    } else {
        printf("%s failed\n", __FUNCTION__);
    }

    return;
}

void handle_eemlteinter(const char * s, const char * smsPdu)
{
    RIL_Lte_Ncell_Info * ncell;
    RIL_SOCKET_ID socket_id;

    ncell = eemlte_freq(s, smsPdu);
    if(ncell) {
        socket_id = getSocketId();
        Ril_EEM_Response_Lte_Inter_Ncell_Info((RIL_SIM_ID)socket_id, ncell);
    } else {
        printf("%s failed\n", __FUNCTION__);
    }

    return;
}

#define EEMLTEIRAT_VAL_MAX 9
void handle_eemlteinterrat(const char * s, const char * smsPdu)
{
    RIL_Gsm_Cell_Info * scell = NULL;
    RIL_SOCKET_ID socket_id;
    int * pval = NULL;
    char * line;
    int cnt;

    pval = at_tok_read_ints(s, EEMLTEIRAT_VAL_MAX, &line, &cnt);
    if(!pval || (cnt < 2)) goto error;

    if(pval[1] != 0) {
        scell = (RIL_Gsm_Cell_Info *)UI_MALLOC(sizeof(RIL_Gsm_Cell_Info));
        if(!scell) {
            printf("%s(%d) failed: no mem\n", __FUNCTION__, __LINE__);
            goto error;
        }
        scell->mcc = pval[2]; /* p3 */
        scell->mnc = pval[3]; /* p4 */
        scell->lac = pval[4]; /* p5 */
        scell->cellid = pval[5]; /* p6 */
        scell->arfcn = pval[6]; /* p7 */
        scell->bsic = pval[7]; /* p8 */
        scell->rxlev = pval[8]; /* p9 */
    }

    UI_FREE(pval);

    socket_id = getSocketId();
    Ril_EEM_Response_Lte_Irat_Ncell_Info((RIL_SIM_ID)socket_id, scell);

    return;
error:
    if(scell) UI_FREE(scell);
    if(pval) UI_FREE(pval);
    printf("%s failed\n", __FUNCTION__);
    return;
}

static void modemSwitchOnPlmnInd(void * param)
{
    UNUSED(param);
    RIL_SOCKET_ID socketId = getSocketId();
    int bIsNetworkInChina = isNetworkInChina(socketId);
    RLOGI("%s: is network in China:%d", __FUNCTION__, bIsNetworkInChina);
    /*Only when know whether indeed in china or not, then need consider to switch image*/
    if(bIsNetworkInChina >= 0)
        switch_modem(bIsNetworkInChina, -1, -1, SIM_OPERATOR_NUM, socketId);
}

void handle_network_operator(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);
    char * line = NULL;
    char * linesave = NULL;
    int err;
    char * operNumStr;
    RIL_SOCKET_ID socketId = getSocketId();

    line = lv_strdup(s);
    linesave = line;
    err = at_tok_start(&line);
    if(err < 0) goto exit;
    err = at_tok_nextstr(&line, &operNumStr);
    if(err < 0) goto exit;

    strlcpy(sOperInfo[socketId].operNumStr, operNumStr, sizeof(sOperInfo[socketId].operNumStr));

    if(worldPhoneAutoSwitchEnabled(socketId)) {
        enque(getWorkQueue(SERVICE_MM, socketId), modemSwitchOnPlmnInd, NULL, socketId);
    }
exit:
    if(linesave != NULL) UI_FREE(linesave);
}

void handle_cireph(const char * s, const char * smsPdu)
{
    UNUSED(smsPdu);
    char * line = NULL;
    char * linesave = NULL;
    int result[1] = {0};
    int err;
    RIL_SOCKET_ID socketId = getSocketId();

    line = lv_strdup(s);
    linesave = line;
    err = at_tok_start(&line);
    if(err < 0) goto exit;
    err = at_tok_nextint(&line, &(result[0]));
    if(err < 0) goto exit;
    RIL_onUnsolicitedResponse(RIL_UNSOL_SRVCC_STATE_NOTIFY, &result, sizeof(result), socketId);

exit:
    if(linesave != NULL) UI_FREE(linesave);
}
void modemSwitchOnMasterCardChange(void * param)
{
    UNUSED(param);
    RIL_SOCKET_ID socketId = getSocketId();

    if(worldPhoneAutoSwitchEnabled(socketId)) {
        SIM_Operator simId = imsi2SimOperator(socketId);
        RLOGD("%s: socketId:%d simId:%d", __FUNCTION__, socketId, simId);
        if(simId != SIM_OPERATOR_NUM) {
            BASEBAND_TYPE basebandType = getBaseBandType();
            int bIsNetworkInChina = isPrevNetworkInChina();
            SIM_Operator simOperator = imsi2SimOperator(socketId);

            RLOGD("%s: bIsNetworkInChina:%d basebandType:%d simOperator:%d", __FUNCTION__, bIsNetworkInChina, basebandType, simOperator);
            if(simId != CMCC_SIM) {
                if(basebandType == BASEBAND_UMTS) {
                    return;
                }
            } else {
                if((bIsNetworkInChina == -1) //Don't switch for the unkown band type
                        || (bIsNetworkInChina == 1 && basebandType == BASEBAND_TD) //In China & loaded LTG
                        || (bIsNetworkInChina == 0 && basebandType == BASEBAND_UMTS)) {//Oversea & loaded LWG
                    return;
                }
            }
            RLOGD("%s: check to switch_modem", __FUNCTION__);
            switch_modem(bIsNetworkInChina, -1, -1, simOperator, socketId);
        }
    }
}

void triggerPrevOperProc(int secs, work_function_t func)
{
    RIL_SOCKET_ID socketId = getSocketId();
    for(int i = 0 ; i < SIM_COUNT ; i++) {
        if(sOperInfo[i].operNumStr[0] != '\0') {
            strcpy(gPrevOperNumStr[i], sOperInfo[i].operNumStr);
        }
    }
    struct timeval timeDelay;
    timeDelay.tv_usec = 0;
    timeDelay.tv_sec = secs;
    enqueDelayed(getWorkQueue(SERVICE_MM, socketId), func, NULL, &timeDelay, socketId);
}

void timeoutClearPrevOper(void * param)
{
    UNUSED(param);
    if(gPrevOperCaching) {
        for(int i = 0 ; i < SIM_COUNT ; i++) {
            gPrevOperNumStr[i][0] = '\0';
        }
        gPrevOperCaching = false;
    }
}
void startPrevOperCached(RIL_SOCKET_ID socketId, int secs)
{
    if(isMasterRil(socketId)) {
        RLOGD("%s: startPrevOperCached isMasterRil:%d", __FUNCTION__, socketId);
        gPrevOperCaching = true;
        triggerPrevOperProc(secs, timeoutClearPrevOper);
    }
}
void stopPrevOperCached(RIL_SOCKET_ID socketId)
{
    if(isMasterRil(socketId)) {
        RLOGD("%s: stopPrevOperCached isMasterRil:%d", __FUNCTION__, socketId);
        gPrevOperCaching = false;
    }
}

int parse_cesq(char ** line, int * rxlev, int * ber, int * rscp, int * ecno, int * rsrq, int * rsrp)
{
    int err = at_tok_start(line);
    if(err < 0) return err;

    err = at_tok_nextint(line, rxlev);
    if(err < 0) return err;

    err = at_tok_nextint(line, ber);
    if(err < 0) return err;

    err = at_tok_nextint(line, rscp);
    if(err < 0) return err;

    err = at_tok_nextint(line, ecno);
    if(err < 0) return err;

    err = at_tok_nextint(line, rsrq);
    if(err < 0) return err;

    err = at_tok_nextint(line, rsrp);
    if(err < 0) return err;

    return 0;
}

int process_rssi(int rxlevdbm, int rscpdbm)
{
    int rssidbm = 99;

    if (rscpdbm != INT_MAX) {
        rssidbm = convertDbmToRssi(rscpdbm);
    } else if (rxlevdbm != INT_MAX) {
        rssidbm = convertDbmToRssi(rxlevdbm);
    }

    return rssidbm;
}

void handle_wifi_cellinfo(const char * s, const char * cellinfo)
{
    UNUSED(cellinfo);

    RIL_SOCKET_ID socketId = getSocketId();

    if (socketId == RIL_SOCKET_2)
       return;

    RIL_Wifi_Cell_Info * response = (RIL_Wifi_Cell_Info *)UI_MALLOC(sizeof(RIL_Wifi_Cell_Info));
    char * line = NULL, *linesave = NULL;
    char * out = NULL;
    int err;

    if(response == NULL)
        goto error;

    memset(response, 0, sizeof(RIL_Wifi_Cell_Info));

    line = lv_strdup(s);
    linesave = line;

    err = at_tok_start(&line);
    if(err < 0) goto error;

    at_tok_nextstr(&line, &out);
    memcpy(response->macAddr, out, strlen(out) + 1);

    err = at_tok_nextint(&line, &response->rssi);
    if (err < 0) goto error;

    if (at_tok_hasmore(&line)) {
        err = at_tok_nextint(&line, &response->chanNum);
        if(err < 0) goto error;
    }
    if ((strcmp(response->macAddr, "") == 0) && (response->rssi == 0)) {
        RIL_onUnsolicitedResponse(RIL_UNSOL_WIFI_SCAN_CELLINFO, NULL, 0, socketId);
        UI_FREE(response);
    } else {
        RIL_onUnsolicitedResponse(RIL_UNSOL_WIFI_SCAN_CELLINFO, response, sizeof(RIL_Wifi_Cell_Info), socketId);
    }

    /* Free allocated memory and return */
    if (linesave != NULL)
        UI_FREE(linesave);
    return;

error:
    if(linesave != NULL) UI_FREE(linesave);
    RLOGW("%s: Error parameter in ind msg: %s", __FUNCTION__, s);
    return;
}

};
