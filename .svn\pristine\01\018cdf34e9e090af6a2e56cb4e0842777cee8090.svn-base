#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

#==========================================================================
# File Name      : target.mak
# Description    : General target make file for win32 systems
#
# Notes          : This file is used by win32 targets and should
#                  not be modified for a specific target.
#
# Copyright (c) 2004 Intel Corporation. All Rights Reserved
#==========================================================================

############################################ Target Variant ###########################################
ifeq ($(TARGET_VARIANT),$(empty))
 SPACER=
else
 SPACER=_
endif

######################################## Filter files from file ########################################
ifneq ($(strip $(CBA_SRC_FILES_FILTER)),$(empty))
# Read list of files that should be filtered
FROZEN_FILES_LIST = $(shell $(CAT) $(GRND_ROOT)$(BUILD_ROOT)$(CBA_SRC_FILES_FILTER))
# Filtering out
LOCAL_SRC_FILES := $(filter-out $(FROZEN_FILES_LIST),$(LOCAL_SRC_FILES))
endif

######################################## Extract special flags files ########################################
ifneq ($(strip $(CBA_SRC_FILES_SPECIAL_FLAGS)),$(empty))
# Read list of files that should be built with special flags
SPECIAL_FLAGS_FILES_LIST = $(shell $(CAT) $(GRND_ROOT)$(BUILD_ROOT)$(CBA_SRC_FILES_SPECIAL_FLAGS))
endif

######################################## Suppress some default make verbosity ############################
MAKEFLAGS += --no-print-directory

############################################ Make utility depended settings ##############################
DONT_INCLUDE_DEP_FILES = 0

MAKEDEP ?= 1
FORCE_BUILD_SWITCH = -B
ifneq	(,$(findstring B, $(MAKEFLAGS)))
# gnumake run in "force build" mode
FB_EXTMAKE ?= -a
FB_TTP	= -B
DONT_INCLUDE_DEP_FILES = 1
endif

EXT_MAKE ?= omake

########################################### Tools Arbiter ####################################################
REAL_ASM ?= $(ASM)
REAL_ASM_DEP ?= $(ASM_DEP)
ifeq (,$(findstring gnu, $(ENV)))
FILE_CC_TOOL = $(or $($(<F).CC),$(CC))
else
FILE_CC_TOOL = $(CC)
endif

CBA_BM_DB = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_PATH)$(GOOD_SLASH)build$(GOOD_SLASH)$(TARGET_NAME).dat)

########################################### Create separate temp folder ####################################################
ifneq ($(strip $(CBA_SEPARATE_TEMPDIR)),$(empty))
TEMPDIR = $(CBA_TEMP)$(GOOD_SLASH)temp_$(TARGET_NAME)_$(TV)
A := $(shell $(MKDIR) $(TEMPDIR) $(ERR_TO_NUL) && echo DONE)
ifeq ($(A),DONE)
override TEMP := $(TEMPDIR)
override TMP :=  $(TEMPDIR)
CLEAN_SEPARATE_TEMPDIR = clean_separate_tempdir
CBA_GARBAGE_COLLECTOR  = cba_garbage_collector
endif
endif

########################################### Generate one PPL filer file ####################################################
ifneq (,$(strip $(CBA_PPL_FILTER_FILE)))
PPL_FILTER_SWITCH = $(addprefix -f ,$(CBA_PPL_FILTER_FILE))
endif

########################################### Using libraries definitions #####################################
# have to be defined at any case with 'good' (fixed) slashes
ORIG_GROUP_LIST   := $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GROUP_LIST))
ORIG_PACKAGE_LIST := $(subst $(BAD_SLASH),$(GOOD_SLASH),$(PACKAGE_LIST))
GROUP_LIST        := $(ORIG_GROUP_LIST)
PACKAGE_LIST      := $(ORIG_PACKAGE_LIST)

ifneq ($(strip $(CBA_USELIBS)),${empty})
CBA_LIBS_DIR ?= lib
CBA_USE_COMMON_LIB_DIR ?= 0
USE_LIBS 	   = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(CBA_USELIBS))
PACKAGE_LIST 	  := $(filter-out $(USE_LIBS),$(ORIG_PACKAGE_LIST))
GROUP_LIST   	  := $(filter-out $(USE_LIBS),$(ORIG_GROUP_LIST))

ifeq ($(CBA_USE_COMMON_LIB_DIR),1)
LOCAL_LIBS_TMP = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,$(strip $(CBA_LIBS_DIR))/%,$(addsuffix $(ENV_LIB_EXT),$(subst $(GOOD_SLASH),-,$(USE_LIBS)))))
USE_LDF = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,$(strip $(CBA_LIBS_DIR))/%,$(addsuffix $(ENV_LDF_EXT),$(subst $(GOOD_SLASH),-,$(USE_LIBS)))))
USE_PP = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,$(strip $(CBA_LIBS_DIR))/%,$(addsuffix .pp,$(subst $(GOOD_SLASH),-,$(USE_LIBS)))))
LDF_DIRS := $(subst $(BAD_SLASH),$(GOOD_SLASH),$(CBA_LIBS_DIR))
else
LOCAL_LIBS_TMP = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(join $(patsubst %,$(BUILD_ROOT)/%/$(strip $(CBA_LIBS_DIR))/,$(USE_LIBS)),$(addsuffix $(ENV_LIB_EXT),$(subst $(GOOD_SLASH),-,$(USE_LIBS)))))
USE_LDF := $(subst $(BAD_SLASH),$(GOOD_SLASH),$(join $(patsubst %,$(BUILD_ROOT)/%/$(strip $(CBA_LIBS_DIR))/,$(USE_LIBS)),$(addsuffix $(ENV_LDF_EXT),$(subst $(GOOD_SLASH),-,$(USE_LIBS)))))
USE_PP = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(join $(patsubst %,$(BUILD_ROOT)/%/$(strip $(CBA_LIBS_DIR))/,$(USE_LIBS)),$(addsuffix .pp,$(subst $(GOOD_SLASH),-,$(USE_LIBS)))))
LDF_DIRS := $(patsubst %$(GOOD_SLASH),%,$(subst $(BAD_SLASH),$(GOOD_SLASH),$(dir $(USE_LDF))))
endif
LOCAL_LIBS_TMP += $(LOCAL_LIBS)
LOCAL_LIBS := $(LOCAL_LIBS_TMP)
USE_LDF_LOCAL_TARGET = $(patsubst %,$(TARGET_OBJ_PATH)/%,$(notdir $(USE_LDF)))
endif

ifneq ($(strip $(CBA_USEOBJS)),${empty})
LOCAL_LIBS += $(subst $(BAD_SLASH),$(GOOD_SLASH),$(join $(patsubst %,$(strip $(CBA_OBJS_DIR))/,$(USE_OBJS)),$(addsuffix .o,$(USE_OBJS))))
USE_OBJS 	   = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(CBA_USEOBJS))
USE_PP += $(subst $(BAD_SLASH),$(GOOD_SLASH),$(join $(patsubst %,$(strip $(CBA_OBJS_DIR))/,$(USE_OBJS)),$(addsuffix .pp,$(USE_OBJS))))
endif

######################################### Verbosity level #################################################
CBA_VL ?= NOISY
ifneq ($(CBA_VL),$(findstring $(CBA_VL),MUTE SILENT NORMAL LOUD NOISY))
override CBA_VL := NORMAL
endif

SAY_IT := $(empty)
#SHUT_UP := @
SHUT_UP := @

ifeq ($(CBA_VL),MUTE)
VL_MSG := $(SHUT_UP)
VL_CMD := $(SHUT_UP)
VL_DTL := $(SHUT_UP)
VL_EXP := $(SHUT_UP)
endif

ifeq ($(CBA_VL),SILENT)
VL_MSG := $(SAY_IT)
VL_CMD := $(SHUT_UP)
VL_DTL := $(SHUT_UP)
VL_EXP := $(SHUT_UP)
endif

ifeq ($(CBA_VL),NORMAL)
VL_MSG := $(SAY_IT)
VL_CMD := $(SAY_IT)
VL_DTL := $(SHUT_UP)
VL_EXP := $(SHUT_UP)
endif

ifeq ($(CBA_VL),LOUD)
VL_MSG := $(SAY_IT)
VL_CMD := $(SAY_IT)
VL_DTL := $(SAY_IT)
VL_EXP := $(SHUT_UP)
endif

ifeq ($(CBA_VL),NOISY)
VL_MSG := $(SAY_IT)
VL_CMD := $(SAY_IT)
VL_DTL := $(SAY_IT)
VL_EXP := $(SAY_IT)
endif

############################################## Adding and Removing flags #################################
#    add switches:      file.c.ADD = -opt1 -opt2
#    remove switches:   file.c.RMV = -opt3 -opt4
#    remove sequence:   file.c.RMVSEQ = -a -b -c

FILE_ADD_OPTIONS = $($(<F).ADD)
FILE_RMV_OPTIONS = $($(<F).RMV)
FILE_RMVSEQ_OPTIONS = $($(<F).RMVSEQ)

############################################## Bug Fixer Options ####################################
FILE_BF_ADD_OPTIONS = $($(<F).BFADD)
FILE_BF_RMV_OPTIONS = $($(<F).BFRMV)
FILE_NO_BF = $($(<F).NOBF)

############################################## Set include path order ###################################
INC_PATH_ORDER ?= PARENT_LAST

##################################### Incorporate no-native sources ###################################
NONATIVE_DIRS := $(patsubst %$(GOOD_SLASH),%$(GOOD_SLASH).,$(subst $(BAD_SLASH),$(GOOD_SLASH),$(dir $(NONATIVE_SRC_FILES))))
LOCAL_SRC_FILES := $(LOCAL_SRC_FILES) $(notdir $(NONATIVE_SRC_FILES))

############################################# Build Paths and Flags ######################################
TARGET_OUT_DIR  = obj$(SPACER)$(TARGET_VARIANT)
TARGET_OBJ_PATH = $(subst $(BAD_SLASH),$(GOOD_SLASH),../$(TARGET_OUT_DIR))
TARGET_LIB_PATH = $(subst $(BAD_SLASH),$(GOOD_SLASH),../$(TARGET_OUT_DIR))
ifneq ($(MAKELIB),$(EMPTY))
TARGET_OUT_PATH = ..$(GOOD_SLASH)lib
else
TARGET_OUT_PATH = ..$(GOOD_SLASH)bin
endif
TARGET_INCCACHE_PATH = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_PATH)$(GOOD_SLASH)$(TARGET_OUT_DIR)$(GOOD_SLASH)inc)
TARGET_CODECACHE_PATH = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_PATH)$(GOOD_SLASH)$(TARGET_OUT_DIR)$(GOOD_SLASH)code)
GROUP_OBJ_DIRS = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %, $(TARGET_OBJ_PATH)$(GOOD_SLASH)obj_%, $(subst $(GOOD_SLASH),_, $(ORIG_GROUP_LIST))))
PACKAGE_OBJ_DIRS = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %, $(TARGET_OBJ_PATH)$(GOOD_SLASH)obj_%, $(subst $(GOOD_SLASH),_, $(ORIG_PACKAGE_LIST))))
ALL_CHILD_OBJ_DIRS = $(GROUP_OBJ_DIRS) $(PACKAGE_OBJ_DIRS)
PPR_PATH := $(subst $(BAD_SLASH),$(GOOD_SLASH),$(PPR_PATH))

####################################### Include Cache & Code Cache #######################################
TARGET_INCCACHE_FILE = $(TARGET_INCCACHE_PATH)$(GOOD_SLASH)inccache.txt
ifeq ($(strip $(ENV_TS_WRAPPER)),REAL)
CHAIN_PATCHING = chain_patching
endif

############################################## Build include paths #######################################
PACKAGE_INC_PATHS = $(patsubst %, $(BUILD_ROOT)/%/inc, $(ORIG_PACKAGE_LIST))
GROUP_INC_PATHS   = $(patsubst %, $(BUILD_ROOT)/%/inc, $(ORIG_GROUP_LIST))
LOCAL_INC_PATHS  := $(filter-out $(TARGET_PATH)$(GOOD_SLASH)bin,$(LOCAL_INC_PATHS)) $(TARGET_OBJ_PATH)
LOCAL_LDF_PATH    = ../src
ALL_INC_PATHS     = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_INC_PATHS) $(ENV_INC_PATH) $(PACKAGE_INC_PATHS) $(GROUP_INC_PATHS) ${GLOBAL_INC_PATHS} ${DIAG_PATH} )
U_ALL_INC_PATHS := $(shell perl -e "map {$(SCALAR)u = $(SCALAR)_; unless(grep {lc($(SCALAR)u) eq lc($(SCALAR)_)} @U){push(@U, $(SCALAR)u); print qq($(SCALAR)u\n)}} @ARGV" $(subst $(BAD_SLASH),$(GOOD_SLASH),$(LOCAL_INC_PATHS) $(ALL_INC_PATHS) ))

################################## Construct the flags from the environment, target and local target #############
CFLAGS   = $(ENV_CFLAGS) $(TARGET_CFLAGS) $(LOCAL_CFLAGS)
DFLAGS   = $(ENV_DFLAGS) $(TARGET_DFLAGS) $(addprefix -D,$(VARIANT_LIST_SPREAD2)) $(LOCAL_DFLAGS)
VFLAGS   = $(ENV_VFLAGS) $(TARGET_VFLAGS) $(LOCAL_VFLAGS)
C_DFLAGS     = $(DFLAGS) $(TARGET_C_DFLAGS) $(LOCAL_C_DFLAGS)
ASM_DFLAGS   = $(DFLAGS) $(TARGET_ASM_DFLAGS) $(LOCAL_ASM_DFLAGS)
ARFLAGS  = $(ENV_ARFLAGS) $(TARGET_ARFLAGS) $(LOCAL_ARFLAGS)
ASMFLAGS = $(ENV_ASMFLAGS) $(addprefix $(ENV_ASMFLAGS_PREFIX) $(empty),$(TARGET_ASMFLAGS) $(LOCAL_ASMFLAGS)) $(patsubst %,$(ENV_ASM_DEFFLAG) "% SETL {TRUE}",$(VARIANT_LIST_SPREAD2))
PARENT_ASMFLAGS = $(ENV_ASMFLAGS) $(addprefix $(ENV_ASMFLAGS_PREFIX) $(empty),$(patsubst %,$(ENV_ASM_DEFFLAG) "% SETL {TRUE}",$(VARIANT_LIST_SPREAD2)) $(TARGET_ASMFLAGS) )
LDFLAGS  = $(ENV_LDFLAGS) $(TARGET_LDFLAGS)
LDFLAGS_40M  = $(ENV_LDFLAGS) $(TARGET_LDFLAGS_40M)
LDFLAGS_40M_A0  = $(ENV_LDFLAGS) $(TARGET_LDFLAGS_40M_A0)
LDFLAGS_40M_A0_RFS  = $(ENV_LDFLAGS) $(TARGET_LDFLAGS_40M_A0_RFS)
DEP_CFLAGS = $(filter-out -c,$(CFLAGS))
ifeq ($(strip $(ENV_USE_BUGFIXER)),1)
ifeq ($(strip $(RUN_BUGFIXER)),0)
DRIVER_BUGFIXER_FLAGS=$(BUGFIXER_FLAGS)
endif
endif
C_IFLAGS   = $(patsubst %,$(ENV_C_ISPACER)%,$(TARGET_INCCACHE_PATH) $(U_ALL_INC_PATHS))
ASM_IFLAGS = $(patsubst %,$(ENV_ASM_ISPACER)%,$(TARGET_INCCACHE_PATH) $(U_ALL_INC_PATHS))
REAL_ASM_IFLAGS = $(patsubst %,$(ENV_REAL_ASM_ISPACER)%,$(TARGET_INCCACHE_PATH) $(U_ALL_INC_PATHS))

########################################## IFLAGS IN ALL TOOLS #########################################
# don't use via files by default
CBA_DONT_USE_VIA_FILE ?= 0

ifneq ($(strip $(CBA_DONT_USE_VIA_FILE)),1)
C_IFLAGS_VIA_FILE_NAME = $(TARGET_OBJ_PATH)/targ_c.via
ASM_IFLAGS_VIA_FILE_NAME = $(TARGET_OBJ_PATH)/targ_asm.via
ifneq ($(REAL_ASM),$(ASM))
REAL_ASM_IFLAGS_VIA_FILE_NAME = $(TARGET_OBJ_PATH)/targ_real_asm.via
endif
endif

ifneq ($(strip $(CBA_DONT_USE_VIA_FILE)),1)
C_VIA_IFLAGS     = $(ENV_C_VIAFLAG)$(C_IFLAGS_VIA_FILE_NAME)
ASM_VIA_IFLAGS = $(ENV_ASM_VIAFLAG)$(ASM_IFLAGS_VIA_FILE_NAME)

ifeq ($(REAL_ASM),$(ASM))
REAL_ASM_VIA_IFLAGS = $(ASM_VIA_IFLAGS)
else
REAL_ASM_VIA_IFLAGS = $(ENV_REAL_ASM_VIAFLAG)$(REAL_ASM_IFLAGS_VIA_FILE_NAME)
endif

else
C_VIA_IFLAGS     = $(C_IFLAGS)
ASM_VIA_IFLAGS = $(ASM_IFLAGS)

ifeq ($(REAL_ASM),$(ASM))
REAL_ASM_VIA_IFLAGS = $(ASM_VIA_IFLAGS)
else
REAL_ASM_VIA_IFLAGS = $(REAL_ASM_IFLAGS)
endif

endif
CREATE_C_IFLAGS_CMD = $(VL_EXP)$(ECHO) $(C_IFLAGS) > $(C_IFLAGS_VIA_FILE_NAME)
CREATE_ASM_IFLAGS_CMD = $(VL_EXP)$(ECHO) $(ASM_IFLAGS) > $(ASM_IFLAGS_VIA_FILE_NAME)
CREATE_REAL_ASM_IFLAGS_CMD = $(VL_EXP)$(ECHO) $(REAL_ASM_IFLAGS) > $(subst $(BAD_SLASH),$(GOOD_SLASH),$(REAL_ASM_IFLAGS_VIA_FILE_NAME))

######################################## Main target binary definition ######################################
OUTPUT_TARGET_NAME ?= $(TARGET_NAME)$(SPACER)$(TARGET_VARIANT)
OUTPUT_TARGET_NAME_40M ?= $(TARGET_NAME)$(SPACER)$(TARGET_VARIANT)_40M
OUTPUT_TARGET_NAME_40M_A0 ?= $(TARGET_NAME)$(SPACER)$(TARGET_VARIANT)_40M_A0
OUTPUT_TARGET_NAME_40M_A0_RFS ?= $(TARGET_NAME)$(SPACER)$(TARGET_VARIANT)_40M_A0_RFS
ifneq ($(MAKELIB),${EMPTY})
	TARGET_EXT = $(ENV_LIB_EXT)
else
	TARGET_EXT = $(ENV_BINARY_EXT)
endif
TARGET = $(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME)$(TARGET_EXT)
TARGET_40M = $(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME_40M)$(TARGET_EXT)
TARGET_40M_A0 = $(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME_40M_A0)$(TARGET_EXT)
TARGET_40M_A0_RFS = $(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME_40M_A0_RFS)$(TARGET_EXT)
####################################### Pre-processed LDF target definition #################################
ifeq ($(strip $(ENV_USE_LDF)),1)
ifeq ($(strip $(OUTPUT_LDF_TARGET_NAME)),$(empty))
LDF_TARGET = $(TARGET_OBJ_PATH)/$(notdir $(TARGET_LINK_FILE))
else
LDF_TARGET = $(TARGET_OBJ_PATH)/$(OUTPUT_LDF_TARGET_NAME)$(ENV_LDF_EXT)
endif
LDF_TARGET := $(subst $(BAD_SLASH),$(GOOD_SLASH),$(LDF_TARGET))
endif

############################## Co-mapped sections table (generated from the LDF_TARGET) definition ############
ifeq ($(strip $(ENV_USE_LDF)),1)
ROM_DATA_SRC = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)/$(ROM_DATA_NAME).h)
endif

######################################### Makefiles participating in the build process #########################
TARGET_MAKEFILE = $(TARGET_NAME).mak
GEN_TARGET_MAKEFILE = $(BUILD_ROOT)$(GOOD_SLASH)env$(GOOD_SLASH)$(HOST)$(GOOD_SLASH)build$(GOOD_SLASH)target.mak
TARGET_Mabpsagps.hAKEFILES = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_MAKEFILE) $(GEN_TARGET_MAKEFILE))
ifneq ($(strip $(IGNORE_MKFILE_CHANGE)),1)
TARGET_MKFILE_DEP	= $(sort $(filter %.mak,$(TARGET_MAKEFILES) $(INC_MAK_DEPS) $(MAKEFILE_LIST)) $(GLOBAL_DEPS))
endif
PACKAGE_LIST_MAKEFILES = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(join $(patsubst %, $(BUILD_ROOT)$(strip $(BKSLSH))%$(GOOD_SLASH)build$(GOOD_SLASH), $(PACKAGE_LIST)), $(patsubst %, %.mak, $(notdir $(PACKAGE_LIST)))))
GROUP_LIST_MAKEFILES   = $(subst,$(BAD_SLASH),$(GOOD_SLASH),$(join $(patsubst %, $(BUILD_ROOT)(strip $(BKSLSH))%$(GOOD_SLASH)build$(GOOD_SLASH), $(GROUP_LIST)), $(patsubst %, %.mak, $(notdir $(GROUP_LIST)))))

######################################## Option file definition #############################################
OPT_FILE = $(TARGET_NAME)$(SPACER)$(TARGET_VARIANT).opt
ifneq ($(strip $(IGNORE_OPTFILE_CHANGE)),1)
OPT_FILE_DEP = $(OPT_FILE)
endif
TRACK_VARS_FILE = $(TARGET_NAME)$(SPACER)$(TARGET_VARIANT).track

OPT_FILE_MAKE_STR = $(subst $(BAD_SLASH),$(GOOD_SLASH),"OPT_FILE=$(TARGET_PATH)$(GOOD_SLASH)build$(GOOD_SLASH)$(OPT_FILE)")

######################################## Object Store File List  #############################################
ifeq ($(strip $(CBA_USE_OBJSTORE)),1)
OBJSTORE_LIST = ../objstore$(SPACER)$(TARGET_VARIANT)/objstore_list$(SPACER)$(TARGET_VARIANT).txt
endif

###################################### Package, group and local target libraries ##############################
# DON'T bring LOCAL_LIBS into the ALL_LIBS variable.  There will never be
# any rules telling how to build these libs.  You just take whatever you have.
PACKAGE_LIBS   = $(patsubst %, $(TARGET_LIB_PATH)/%$(ENV_LIB_EXT), $(subst $(GOOD_SLASH),-, $(subst $(BAD_SLASH),$(GOOD_SLASH),$(PACKAGE_LIST))))
GROUP_LIBS     = $(patsubst %, $(TARGET_LIB_PATH)/%$(ENV_LIB_EXT), $(subst $(GOOD_SLASH),-, $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GROUP_LIST))))
ALL_LIBS       = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(PACKAGE_LIBS) $(GROUP_LIBS))
ALL_NONLIBS    = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,$(TARGET_LIB_PATH)/%,$(patsubst %, %_pk_nonliblist.txt, $(subst $(GOOD_SLASH),-, $(PACKAGE_LIST))))) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,$(TARGET_LIB_PATH)/%,$(patsubst %, %_grp_nonliblist.txt, $(subst $(GOOD_SLASH),-, $(GROUP_LIST)))))
TARG_OBJ_LIB_LIST_FILE = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)/$(strip $(OUTPUT_TARGET_NAME))_targ_objliblist.txt)
ifneq ($(strip $(CBA_SRC_FILES_LINK_BY_ORDER)),${empty})
TARG_OBJ_LINK_BY_ORDER_LIST = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)/$(strip $(OUTPUT_TARGET_NAME))_targ_objlinkbyorderlist.txt)
endif
ifneq ($(strip $(CBA_FLAT_BUILD)),$(empty))
GRP_OBJ_LISTS = $(patsubst %, $(TARGET_LIB_PATH)/%_grp_objliblist.txt, $(subst $(GOOD_SLASH),-, $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GROUP_LIST))))
PKG_OBJ_LISTS = $(patsubst %, $(TARGET_LIB_PATH)/%_pk_objliblist.txt, $(subst $(GOOD_SLASH),-, $(subst $(BAD_SLASH),$(GOOD_SLASH),$(PACKAGE_LIST))))
ALL_OBJ_LISTS = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRP_OBJ_LISTS) $(PKG_OBJ_LISTS))
endif

###################################### Local source files and dirs definition #################################
LOCAL_SRC_FILE_NAMES = $(notdir $(LOCAL_SRC_FILES))
LOCAL_SRC_DIRS       = $(patsubst %$(GOOD_SLASH),%,$(sort $(patsubst %,$(subst $(BAD_SLASH),$(GOOD_SLASH),$(LOCAL_SRC_PATH))$(strip $(BKSLSH))%,$(subst $(BAD_SLASH),$(GOOD_SLASH),$(dir $(LOCAL_SRC_FILES))))))

###################################### Local object files defintion ##########################################
LOCAL_OBJ_FILES = $(patsubst %, $(TARGET_OBJ_PATH)/%, $(addsuffix $(ENV_OBJ_EXT), $(basename $(LOCAL_SRC_FILES))))
# # LOCAL_HP_TARGETS = $(subst .h,$(ENV_HP_EXT), $(notdir $(LOCAL_HP_FILES)))
ifneq ($(strip $(LOCAL_HP_FILES)),$(empty))
LOCAL_HP_TARGETS = hp_target
endif

LOCAL_HP_FILES := $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,$(TARGET_INCCACHE_PATH)/%,$(notdir $(LOCAL_HP_FILES))))

LOCAL_C_SRC_FILES  = $(filter %.c %.C,$(LOCAL_SRC_FILES))

###################################### Utility targets definitions ###########################################
SHOW_FLAGS_LIST   = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %, flags.%,      $(PACKAGE_LIST) $(GROUP_LIST)))
SHOW_FILES_LIST   = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %, files.%,      $(PACKAGE_LIST) $(GROUP_LIST)))
SHOW_INCCACHE_LIST = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %, inccache.%,  $(ORIG_PACKAGE_LIST) $(ORIG_GROUP_LIST)))
SHOW_CODECACHE_LIST = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,codecache.%,$(ORIG_PACKAGE_LIST) $(ORIG_GROUP_LIST)))

########################################### PP Definitions ###############################################
ifneq (${PP},${empty})
CHILD_PP_FILES    = $(patsubst %, $(TARGET_OBJ_PATH)/%, $(addsuffix .pp, $(basename $(notdir $(ALL_LIBS)))))
LOCAL_PP_FILES    = $(patsubst %, $(TARGET_OBJ_PATH)/%, $(addsuffix .pp, $(basename $(filter %.c, $(LOCAL_SRC_FILES)))))
PP_FILES = $(CHILD_PP_FILES) $(LOCAL_PP_FILES)
LOCAL_OBJ_FILES +=  $(TARGET_OBJ_PATH)/$(patsubst %.c,%$(ENV_OBJ_EXT),$(PP_GENERATED_C))
LOCAL_SRC_PATH  +=  $(TARGET_OBJ_PATH)
endif
DIAG_MDB_FILE_NAME = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME)_DIAG.mdb)
NVM_MDB_FILE_NAME = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME)_NVM.mdb)
TXT_MDB_FILE_NAME = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME)_MDB.txt)

####################################### Bin Maker Options ##########################################
ifeq ($(strip $(CBA_MAKEBIN)),1)
MAKEBIN_TARGET = $(notdir $(basename $(TARGET))).bin
endif

####################################### Set first level ##########################################
ifneq ($(strip $(CBA_SUMMARY)),$(empty))
CBA_LEVEL = 1
CBA_NEXTLEVEL=2
endif

########################################Export Parameters##########################################
EXPORT_VARS = "VL_MSG=$(VL_MSG)" "VL_CMD=$(VL_CMD)" "VL_DTL=$(VL_DTL)" "VL_EXP=$(VL_EXP)" "SAY_IT=$(SAY_IT)" "USE_LIBS=$(USE_LIBS)" "CBA_USE_COMMON_LIB_DIR=$(CBA_USE_COMMON_LIB_DIR)" "CBA_LIBS_DIR=$(CBA_LIBS_DIR)" "BUILD_PACKAGE_AT_ONCE=$(BUILD_PACKAGE_AT_ONCE)" "DONT_INCLUDE_DEP_FILES=$(DONT_INCLUDE_DEP_FILES)" "TTPCOM_BUILD_AT_ONCE=$(TTPCOM_BUILD_AT_ONCE)" "CBA_FLAT_BUILD=$(CBA_FLAT_BUILD)" "CBA_LEVEL=$(CBA_NEXTLEVEL)" "CBA_STOP_ON_WARNING=$(CBA_STOP_ON_WARNING)" "SDK_BLD_INC_CACHE_PATHS=$(SDK_BLD_INC_CACHE_PATHS)"

########################################## Summary Generation ##############################################
ifneq ($(strip $(CBA_SUMMARY)),$(empty))
START_REPORT = start_report
FINISH_REPORT = finish_report
ifeq (,$(findstring $(BUILD_ROOT),$(TARGET_PATH)))
CBA_SUMMARY_FILE = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(BUILD_ROOT)$(TARGET_PATH)$(GOOD_SLASH)build$(GOOD_SLASH)$(TARGET_NAME)$(SPACER)$(TARGET_VARIANT)).rpt
else
CBA_SUMMARY_FILE = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_PATH)$(GOOD_SLASH)build$(GOOD_SLASH)$(TARGET_NAME)$(SPACER)$(TARGET_VARIANT)).rpt
endif
GROUNDED_SUMMARY_FILE = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)$(CBA_SUMMARY_FILE))
ifneq (,$(findstring WARNING,$(CBA_SUMMARY)))
WARN_COUNTER_FILE = $(TARGET_OBJ_PATH)$(GOOD_SLASH)counter.w
WARN_COUNTER_LINKER = $(TARGET_OBJ_PATH)$(GOOD_SLASH)counter_linker.w
endif
endif

########################################## Summary Generation ##############################################
ifeq ($(strip $(CBA_NO_BUILD_INFO)),$(empty))
BUILD_INFO = build_info
endif

########################################### Vpath definitions #############################################
vpath
vpath %$(ENV_OBJ_EXT)   $(TARGET_OBJ_PATH)
vpath %$(ENV_ASM_EXT) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(LOCAL_SRC_PATH) $(LOCAL_SRC_DIRS) $(NONATIVE_DIRS))
vpath %.c     $(subst $(BAD_SLASH),$(GOOD_SLASH),$(LOCAL_SRC_PATH) $(LOCAL_SRC_DIRS) $(NONATIVE_DIRS))
vpath %.h     $(subst $(BAD_SLASH),$(GOOD_SLASH),$(U_ALL_INC_PATHS))
vpath %$(ENV_LDF_EXT) $(LDF_DIRS) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(LOCAL_SRC_PATH))
vpath %.txt  $(TARGET_OBJ_PATH)
vpath %.pp    $(TARGET_OBJ_PATH)
vpath %.mdb   $(TARGET_OUT_PATH)

########################################## Suffixes and phony #############################################
.SUFFIXES:
.SUFFIXES: .opt .txt $(ENV_OBJ_EXT) .c .h $(ENV_ASM_EXT) .mdb .pp
.NOTPARALLEL:
.PHONY:
.PHONY: files flags clean optfile track_vars force sart_report finish_report step1 step2 step3 step4 step5 step6 step7 cba_garbage_collector clean_separate_tempdir include_cache target_inccache fill_inccache hp_target

.INTERMEDIATE: $(LINKER_LOCK_FILE)
.DELETE_ON_ERROR: $(LINKER_LOCK_FILE)

############################################ PARALLEL ########################################
step1: step2 $(MAKEBIN_TARGET) $(FINISH_REPORT) $(CBA_GARBAGE_COLLECTOR) $(POSTBUILD_ACTIONS)
ifeq ($(strip $(VL_DTL)),$(strip $(SAY_IT)))
	@echo TARGET $(TARGET_NAME) $@ - DONE
endif

step2: step3 $(TARGET)
ifeq ($(strip $(VL_DTL)),$(strip $(SAY_IT)))
	@echo TARGET $(TARGET_NAME) $@ - DONE
endif

step3: step4  $(ROM_DATA_SRC)
ifeq ($(strip $(VL_DTL)),$(strip $(SAY_IT)))
	@echo TARGET $(TARGET_NAME) $@ - DONE
endif

step4: step5 $(PACKAGE_LIBS) $(GROUP_LIBS)
ifeq ($(strip $(VL_DTL)),$(strip $(SAY_IT)))
	@echo TARGET $(TARGET_NAME) $@ - DONE
endif

step5: step6 $(LOCAL_HP_TARGETS)
ifeq ($(strip $(VL_DTL)),$(strip $(SAY_IT)))
	@echo TARGET $(TARGET_NAME) $@ - DONE
endif

step6: step7 $(PPR_PATH) include_cache
ifeq ($(strip $(VL_DTL)),$(strip $(SAY_IT)))
	@echo TARGET $(TARGET_NAME) $@ - DONE
endif

step7: $(BUILD_INFO) $(PREBUILD_ACTIONS) $(START_REPORT) track_vars
ifeq ($(strip $(VL_DTL)),$(strip $(SAY_IT)))
	@echo TARGET $(TARGET_NAME) $@ - DONE
endif

force: ;
################### For Distributed Build these dependencies are required though it might lead to archiving on null build
ifneq ($(strip $(CBA_JOBS)),1)
$(CHILD_PP_FILES) $(LDF_TARGET) : $(PACKAGE_LIBS) $(GROUP_LIBS)
endif

######################################## Header preprocessing build #######################################
# # %_.h: %.h $(OPT_FILE_DEP)
$(LOCAL_HP_TARGETS): $(LOCAL_HP_FILES)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Proceding header files
endif
	$(VL_DTL)$(HP) $(TARGET_INCCACHE_PATH) $(notdir $?)

######################################## USE LDF ######################################################
ifeq ($(strip $(ENV_USE_LDF)),1)

$(TARGET_OBJ_PATH)/%$(ENV_LDF_EXT): %$(ENV_LDF_EXT)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Copying re-usable ldf file [$(notdir $@)] ---
endif
	@echo ---$< ---
	$(VL_DTL)$(COPY) $(subst $(BAD_SLASH),$(GOOD_SLASH),$< $@)

$(LDF_TARGET): $(TARGET_PATH)/src/$(TARGET_LINK_FILE) $(OPT_FILE_DEP) $(USE_LDF_LOCAL_TARGET) $(C_IFLAGS_VIA_FILE_NAME)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Generating Pre-processed link file [$@]
endif
	$(VL_CMD)$(LDF_PP) $(ENV_LDF_PPFLAGS) $(strip $(subst -MD,-D,$(filter -MD%,$(LDFLAGS))) $(DFLAGS) $(subst $(BAD_SLASH),$(GOOD_SLASH),-I$(TARGET_OBJ_PATH) -I$(TARGET_INCCACHE_PATH) $(C_VIA_IFLAGS)) -DPATCH_FILE_NAME=$(ROM_DATA_NAME) -DOUTPUT_DIR=$(TARGET_OUT_DIR)) $< -o $@.pp $(ENV_MMFLAGS) -Mt $@ -Mo $@.pp.dd
	$(VL_DTL)$(PERL) -p -e "$(SCALAR)a=chr(0x22);s|$(SCALAR)a||g" $@.pp.dd > $@.pp.d
	$(VL_EXP)$(IF_FILE_EXIST1) $@.pp.dd $(IF_FILE_EXIST2) $(DEL)  $@.pp.dd $(IF_FILE_EXIST3)
	$(VL_CMD)$(LDFFormat) $@.pp $@


$(ROM_DATA_SRC): $(LDF_TARGET)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Generating comapped sections information data [$@] ---
endif
	$(VL_CMD)$(ComapInfoGenerator) $(TARGET_OBJ_PATH)$(GOOD_SLASH)$(TARGET_LINK_FILE) $@ $(COMAP_FLAGS)
endif

################### Create text file with a list of objects files and libraries ###################################
# For linking a text file with all local objects and libraries is created.
$(TARG_OBJ_LIB_LIST_FILE): $(OPT_FILE_DEP) $(TARGET_MKFILE_DEP) $(PACKAGE_LIST_MAKEFILES) $(GROUP_LIST_MAKEFILES) $(CBA_SRC_FILES_FILTER) $(CBA_SRC_FILES_LINK_BY_ORDER) $(ALL_OBJ_LISTS)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Creating command line file for target [$(OUTPUT_TARGET_NAME)] ---
endif
	$(VL_DTL)$(IF_FILE_EXIST1) $@ $(IF_FILE_EXIST2) $(DEL) $@ $(IF_FILE_EXIST3)
	$(VL_DTL)$(IF_FILE_EXIST1) $@.tmp $(IF_FILE_EXIST2) $(DEL) $@.tmp $(IF_FILE_EXIST3)
ifeq ($(strip $(CBA_FLAT_BUILD)),$(empty))
	$(foreach NAME, $(LOCAL_OBJ_FILES) $(ALL_LIBS) $(LOCAL_LIBS),$(shell $(ECHO) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(NAME)) >> $@.tmp.tmp))
else
	$(VL_DTL)$(CAT) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(ALL_OBJ_LISTS)) >> $@.tmp
	$(foreach NAME, $(LOCAL_OBJ_FILES) $(LOCAL_LIBS),$(shell $(ECHO) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(NAME)) >> $@.tmp.tmp))
endif
	$(VL_DTL)-$(CAT) $@.tmp.tmp >> $@.tmp
ifneq ($(strip $(ALL_NONLIBS)),${empty})
	$(VL_DTL)$(CAT) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(ALL_NONLIBS)) >> $@.tmp
endif
ifneq ($(strip $(OBJSTORE_LIST)),${empty})
	$(VL_DTL)$(CAT) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(OBJSTORE_LIST)) >> $@.tmp
endif
ifneq ($(strip $(CBA_SRC_FILES_LINK_BY_ORDER)),${empty})
ifneq ($(strip $(MAKE_ORDER_LIST)),${empty})
	$(VL_DTL)$(MAKE_ORDER_LIST) $@.tmp $(CBA_SRC_FILES_LINK_BY_ORDER) $(TARG_OBJ_LINK_BY_ORDER_LIST)
endif
endif
	$(VL_DTL)-$(CAT) $@.tmp >> $@
	$(VL_DTL)$(IF_FILE_EXIST1) $@.tmp $(IF_FILE_EXIST2) $(DEL) $@.tmp $(IF_FILE_EXIST3)
	$(VL_DTL)$(IF_FILE_EXIST1) $@.tmp.tmp $(IF_FILE_EXIST2) $(DEL) $@.tmp.tmp $(IF_FILE_EXIST3)

####################################### Linking / Archiving target #######################################
ifeq ($(strip $(CBA_LOCK_LINK)),1)
LINKER_LOCK_FILE = $(TARGET_NAME).linklock
UNLOCK_W_ERROR_CMD = || $(VL_DTL)$(CBA_BUILD_LOCKER) UNLOCK_W_ERROR $(LINKER_LOCK_FILE)
SEPARATE_LINK_PATH_CMD = $(CD) $(TARGET_OBJ_PATH) $(CMD_SEPAR)
endif

ifneq ($(MAKELIB),$(EMPTY)) # Making library
$(TARGET) :  $(LOCAL_OBJ_FILES) $(ALL_LIBS) $(TARG_OBJ_LIB_LIST_FILE) $(ROM_DATA_SRC)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Making target library [$(OUTPUT_TARGET_NAME)] ---
endif
	$(VL_DTL)-$(MKDIR) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OUT_PATH))
	$(VL_CMD)$(AR) $(strip $(ENV_LIBCOMBINE_OPTIONS))

else #Making binary
$(TARGET) : $(TARGET_OBJ_PATH) $(LOCAL_OBJ_FILES) $(ALL_LIBS) $(TARG_OBJ_LIB_LIST_FILE) $(CHAIN_PATCHING)
ifeq ($(strip $(CBA_LOCK_LINK)),1)
	$(call LOCK_CMD,$(LINKER_LOCK_FILE))
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Linker LOCKED ---
endif
endif
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Making target [$(OUTPUT_TARGET_NAME)] ---
endif
ifneq ($(strip $(CBA_SRC_FILES_LINK_BY_ORDER)),${empty})
	$(VL_CMD)$(LD) $(subst $(R_BRACKET),$(SLASH_R_BRACKET),$(subst $(L_BRACKET),$(SLASH_L_BRACKET),$(strip $(ENV_LD_O_FLAG)$(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET) $(ENV_CMDLINE_SWITCH)$(TARG_OBJ_LIB_LIST_FILE) $(ENV_CMDLINE_SWITCH)$(TARG_OBJ_LINK_BY_ORDER_LIST) $(ENV_COMBINE_LDFLAGS)) $(CODEBASE_LD_FLAGS)))) $(ENV_POST_LINK) $(UNLOCK_W_ERROR_CMD)
else
#	$(VL_CMD)$(LD) $(subst $(R_BRACKET),$(SLASH_R_BRACKET),$(subst $(L_BRACKET),$(SLASH_L_BRACKET),$(strip $(ENV_LD_O_FLAG)$(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET) $(ENV_CMDLINE_SWITCH)$(TARG_OBJ_LIB_LIST_FILE) $(ENV_COMBINE_LDFLAGS)))) $(CODEBASE_LD_FLAGS)) $(ENV_POST_LINK) $(UNLOCK_W_ERROR_CMD)
ifneq (,$(findstring XIP,${PMIC_RF_T}))
	$(VL_CMD)$(LD) $(subst $(R_BRACKET),$(SLASH_R_BRACKET),$(subst $(L_BRACKET),$(SLASH_L_BRACKET),$(strip $(ENV_LD_O_FLAG)$(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_40M) $(ENV_CMDLINE_SWITCH)$(TARG_OBJ_LIB_LIST_FILE) $(ENV_COMBINE_LDFLAGS_40M)))) $(CODEBASE_LD_FLAGS)) $(ENV_POST_LINK) $(UNLOCK_W_ERROR_CMD)	
else
	$(VL_CMD)$(LD) $(subst $(R_BRACKET),$(SLASH_R_BRACKET),$(subst $(L_BRACKET),$(SLASH_L_BRACKET),$(strip $(ENV_LD_O_FLAG)$(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_40M_A0) $(ENV_CMDLINE_SWITCH)$(TARG_OBJ_LIB_LIST_FILE) $(ENV_COMBINE_LDFLAGS_40M_A0)))) $(CODEBASE_LD_FLAGS)) $(ENV_POST_LINK) $(UNLOCK_W_ERROR_CMD)	
endif
#	$(VL_CMD)$(LD) $(subst $(R_BRACKET),$(SLASH_R_BRACKET),$(subst $(L_BRACKET),$(SLASH_L_BRACKET),$(strip $(ENV_LD_O_FLAG)$(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_40M_A0_RFS) $(ENV_CMDLINE_SWITCH)$(TARG_OBJ_LIB_LIST_FILE) $(ENV_COMBINE_LDFLAGS_40M_A0_RFS)))) $(CODEBASE_LD_FLAGS)) $(ENV_POST_LINK) $(UNLOCK_W_ERROR_CMD)	
endif
ifeq ($(strip $(ENV_USE_SYMBOL_CONVERTOR)),1)
	$(VL_CMD)$(SymbolConveter) $(TARGET_SCFLAGS) $@ $(TARGET_SYMBOL)
endif
ifneq ($(strip $(CBA_DONTDELOVL)),1)
	$(VL_DTL)-$(DEL) *.ovl
	$(VL_DTL)-$(DEL) *.OVL
endif
ifeq ($(strip $(CBA_LOCK_LINK)),1)
	$(call UNLOCK_CMD,$(LINKER_LOCK_FILE))
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Linker UNLOCKED ---
endif
endif
endif

ifeq ($(strip $(CBA_MAKEBIN)),1)
$(MAKEBIN_TARGET): $(TARGET)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Making bin [$@] ---
endif
	$(VL_DTL)$(CD) $(subst $(BAD_SLASH),$(GOOD_SLASH), $(TARGET_OUT_PATH)) $(CMD_SEPAR) $(VL_CMD)$(BinMaker) $(notdir $(basename $(TARGET))).hx -bin
endif

######################################### Target object directory creation #######################################
# create the target obj directory if required
$(TARGET_OBJ_PATH) $(TARGET_OUT_PATH) $(ALL_CHILD_OBJ_DIRS) $(TARGET_INCCACHE_PATH) $(TARGET_CODECACHE_PATH):
ifeq ($(VL_MSG),$(SAY_IT))
	@echo ----- Make directory $@
endif
	$(VL_DTL)$(IF_DIR_EXIST1) $(subst $(BAD_SLASH),$(GOOD_SLASH),$@) $(IF_FILE_EXIST2) $(MKDIR) $(subst $(BAD_SLASH),$(GOOD_SLASH),$@) $(IF_FILE_EXIST3)

$(PPR_PATH):
ifeq ($(VL_MSG),$(SAY_IT))
		@echo ----- Make directory $@
endif
	$(VL_DTL)$(IF_DIR_EXIST1) $(GRND_ROOT)$(subst $(BAD_SLASH),$(GOOD_SLASH),$@) $(IF_FILE_EXIST2) $(MKDIR) $(GRND_ROOT)$(subst $(BAD_SLASH),$(GOOD_SLASH),$@) $(IF_FILE_EXIST3)

################################### VIA FILE CREATION RULES ##########################################
ifneq ($(strip $(CBA_DONT_USE_VIA_FILE)),1)

$(C_IFLAGS_VIA_FILE_NAME) : $(OPT_FILE_DEP) $(TARGET_MKFILE_DEP)
	$(CREATE_C_IFLAGS_CMD)

$(ASM_IFLAGS_VIA_FILE_NAME) : $(OPT_FILE_DEP) $(TARGET_MKFILE_DEP)
	$(CREATE_ASM_IFLAGS_CMD)

ifneq ($(ASM),$(REAL_ASM))
$(REAL_ASM_IFLAGS_VIA_FILE_NAME) : $(OPT_FILE_DEP) $(TARGET_MKFILE_DEP)
	$(CREATE_REAL_ASM_IFLAGS_CMD)
endif

endif

######################################### Assembly compilation ############################################
ASM_MAKEDEP_CMD = $(VL_CMD)$(call ENV_REFORMAT_COMMAND,$(REAL_ASM_DEP)) $(strip $(ENV_ASM_MFLAGS) $(subst $(FILE_RMVSEQ_OPTIONS),,$(filter-out $(FILE_RMV_OPTIONS),$(ENV_ASM_MAKEDEP_FLAGS)))) $(FILE_ADD_OPTIONS) $(ENV_ASM_COMPILER_OUT) $(ENV_ASM_CLEANDEP) $(TARGET_OBJ_PATH) > $(TARGET_OBJ_PATH)/$*.d
$(TARGET_OBJ_PATH)/%$(ENV_OBJ_EXT) : $(subst $(BAD_SLASH),$(GOOD_SLASH),%$(ENV_ASM_EXT)) $(OPT_FILE_DEP) $(ASM_EXTRA_DEPS) $(REAL_ASM_IFLAGS_VIA_FILE_NAME) $(ASM_IFLAGS_VIA_FILE_NAME)
ifeq ($(VL_DTL),$(SAY_IT))
	@echo -------- Making dependencies for $(notdir $<)
endif
	$(VL_DTL)$(RUN_ON_BACKGROUND) $(ASM_MAKEDEP_CMD)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo -------- Compiling $(notdir $<)
endif
	$(VL_CMD)$(ENV_ASM_MAKE_PATCH) $(REAL_ASM) $(if $(filter $(notdir $<),$(SPECIAL_FLAGS_FILES_LIST)),$(CBA_ASM_SPECIAL_FLAGS)) $(strip $(subst $(FILE_RMVSEQ_OPTIONS),,$(filter-out $(FILE_RMV_OPTIONS),$(ENV_ASM_CODECACHE_FLAGS)))) $(FILE_ADD_OPTIONS) $(ENV_ASM_CODE_OUT) $(ENV_POST_ASM_COMP)
ifeq ($(strip $(ENV_USE_BUGFIXER)),1)
ifeq ($(strip $(RUN_BUGFIXER)),1)
ifneq ($(strip $(FILE_NO_BF)),1)
	$(VL_CMD)$(BugFixer) --inp-file $@ $(filter-out $(FILE_BF_RMV_OPTIONS), $(BUGFIXER_FLAGS)) $(FILE_BF_ADD_OPTIONS)
endif
endif
endif
ifneq (,$(findstring BINUTIL_STATS,$(VARIANT_LIST)))
	@echo --- Creating stats file [$(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.nm] ---
	$(VL_CMD)$(NM_TOOL) -s --size-sort $(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.o > $(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.o.nm
endif

######################################### C compilation ############################################
C_MAKE_DEP_COMP_BUILDIN_CMD = $(VL_CMD)$(call ENV_REFORMAT_COMMAND,$(CC_DEP)) $(strip $(ENV_C_MFLAGS) $(subst $(FILE_RMVSEQ_OPTIONS),,$(filter-out $(FILE_RMV_OPTIONS),$(DEP_CFLAGS) $(DRIVER_BUGFIXER_FLAGS) $(C_DFLAGS) $(C_VIA_IFLAGS))) $(FILE_ADD_OPTIONS)) $(VFLAGS) $(ENV_COMP_BUILDINDEP) $(ENV_C_CLEANDEP) $(TARGET_OBJ_PATH) $(addsuffix $(ENV_OBJ_EXT),$(basename $@)) > $(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.d
ifneq ($(strip $(CPP_DEP)),$(empty))
ifeq  ($(strip $(CBA_UNIFORM_TOOLSET)),$(empty))
ifeq  ($(filter $(ENV_C_VIAFLAG),$(CFLAGS) $(C_DFLAGS) $(C_IFLAGS) $(VFLAGS)),$(empty))
C_MAKE_DEP_COMP_BUILDIN_CMD = $(VL_CMD)$(CPP_DEP) -MM -MF $(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.d -MT $(addsuffix $(ENV_OBJ_EXT),$(basename $@)) -isystem "$(CBA_ENV_INC)" $(filter -D%,$(CFLAGS)) $(ENV_CPP_DFLAGS) $(C_DFLAGS) $(C_IFLAGS) $(subst $(BACK_SLASH),/,$<)
endif
endif
endif

C_ALL_COMPILATION_FLAGS = $(strip $(subst $(FILE_RMVSEQ_OPTIONS),,$(filter-out $(FILE_RMV_OPTIONS),$(CFLAGS) $(DRIVER_BUGFIXER_FLAGS) $(C_DFLAGS) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(C_VIA_IFLAGS)))) $(FILE_ADD_OPTIONS)) $(VFLAGS) $(ENV_C_COMPILER_OUT)

C_ALL_CODECACHE_FLAGS = $(ENV_C_CODE_ADD) $(filter-out $(ENV_C_CODE_RMV),$(strip $(subst $(FILE_RMVSEQ_OPTIONS),,$(filter-out $(FILE_RMV_OPTIONS),$(CFLAGS) $(DRIVER_BUGFIXER_FLAGS) $(C_DFLAGS) $(C_VIA_IFLAGS))) $(FILE_ADD_OPTIONS)) $(VFLAGS) $(ENV_C_CODE_OUT))

define FORCED_SOURCE
$(TARGET_OBJ_PATH)/$(addsuffix $(ENV_OBJ_EXT),$(basename $(1))): force
endef

$(foreach SRC_FILE,$(notdir $(CBA_FORCE)),$(eval $(call FORCED_SOURCE,$(SRC_FILE))))

$(TARGET_OBJ_PATH)/%$(ENV_OBJ_EXT) : $(subst $(BAD_SLASH),$(GOOD_SLASH),%.c) $(OPT_FILE_DEP) $(C_EXTRA_DEPS) $(C_IFLAGS_VIA_FILE_NAME)
ifeq ($(VL_DTL),$(SAY_IT))
	@echo -------- Making dependencies for $(notdir $<)
endif
	$(VL_DTL)$(RUN_ON_BACKGROUND) $(C_MAKE_DEP_COMP_BUILDIN_CMD)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo -------- Compiling $(notdir $<)
endif
	$(VL_CMD)$(ENV_C_MAKE_PATCH) $(FILE_CC_TOOL) $(if $(filter $(notdir $<),$(SPECIAL_FLAGS_FILES_LIST)),$(CBA_C_SPECIAL_FLAGS)) $(C_ALL_CODECACHE_FLAGS) $(ENV_POST_C_COMP)
ifeq ($(strip $(ENV_USE_BUGFIXER)),1)
ifeq ($(strip $(RUN_BUGFIXER)),1)
ifneq ($(strip $(FILE_NO_BF)),1)
	$(VL_CMD)$(BugFixer) --inp-file $@ $(filter-out $(FILE_BF_RMV_OPTIONS), $(BUGFIXER_FLAGS)) $(FILE_BF_ADD_OPTIONS)
endif
endif
endif
#ifneq (,$(findstring BINUTIL_STATS,$(VARIANT_LIST)))
#	@echo --- Creating stats file [$(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.nm] ---
#	$(VL_CMD)$(NM_TOOL) -s --size-sort $(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.o > $(GRND_ROOT)$(TARGET_OBJ_PATH)$(GOOD_SLASH)$*.o.nm
#endif

################################## Build packages and groups ##########################################
# make packages
$(PACKAGE_LIBS): $(OPT_FILE_DEP) $(PPR_PATH) force
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Building package [$(subst $(BAD_SLASH),$(GOOD_SLASH),$(subst -,$(GOOD_SLASH),$(basename $(notdir $@))))] ---
endif
	$(VL_MSG)$(PSEUDO_MAKE) -C $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)$(BUILD_ROOT)$(GOOD_SLASH)$(subst -,$(GOOD_SLASH),$(basename $(notdir $@)))$(GOOD_SLASH)build) -f $(word $(words $(subst -, $(empty),$(basename $(notdir $@)))),$(subst -, $(empty),$(basename $(notdir $@)))).mak $(OPT_FILE_MAKE_STR) $(EXPORT_VARS)

# make groups
$(GROUP_LIBS): $(OPT_FILE_DEP) $(PPR_PATH) force
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Building group [$(subst $(BAD_SLASH),$(GOOD_SLASH),$(subst -,$(GOOD_SLASH),$(basename $(notdir $@))))] ---
endif
	$(VL_MSG)$(PSEUDO_MAKE) -C $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)$(BUILD_ROOT)$(GOOD_SLASH)$(subst -,$(GOOD_SLASH),$(basename $(notdir $@)))$(GOOD_SLASH)build) -f $(word $(words $(subst -, $(empty),$(basename $(notdir $@)))),$(subst -, $(empty),$(basename $(notdir $@)))).mak $(OPT_FILE_MAKE_STR) $(EXPORT_VARS)

########################################### Use PP ##################################################
ifneq ($(strip $(PP)),${empty})
C_PREPROCESS_CMD = $(CC_PP) $(CCPP_OPTION) $(subst /,$(BACK_SLASH),$<) $(strip $(CFLAGS) $(C_DFLAGS) $(C_VIA_IFLAGS)) $(VFLAGS) $(ENV_OFLAGS) ${PPR_PATH}$(GOOD_SLASH)$(subst .c,.ppp,${<F})
ifneq ($(strip $(CPP_DEP)),$(empty))
ifeq  ($(strip $(CBA_UNIFORM_TOOLSET)),$(empty))
ifeq  ($(filter $(ENV_C_VIAFLAG),$(CFLAGS) $(C_DFLAGS) $(C_IFLAGS) $(VFLAGS)),$(empty))
C_PREPROCESS_CMD = $(CPP_PP) $(CCPP_OPTION) $(subst /,$(BACK_SLASH),$<) -isystem "$(CBA_ENV_INC)" $(ENV_CPP_DFLAGS) $(filter -D%,$(CFLAGS)) $(strip $(C_DFLAGS) $(C_IFLAGS)) $(ENV_OFLAGS) ${PPR_PATH}$(GOOD_SLASH)$(subst .c,.ppp,${<F})
endif
endif
endif

$(TARGET_OBJ_PATH)/%.pp : $(subst $(BAD_SLASH),$(GOOD_SLASH),%.c) $(subst $(BAD_SLASH),$(GOOD_SLASH),%$(ENV_OBJ_EXT)) $(OPT_FILE_DEP) $(TARGET_MKFILE_DEP) $(C_IFLAGS_VIA_FILE_NAME)
	$(VL_DTL)$(C_PREPROCESS_CMD) $(CMD_SEPAR) $(PPC) $(subst $(BAD_SLASH),$(GOOD_SLASH),${PPR_PATH}$(GOOD_SLASH)$(subst .c,.ppp,${<F}) $@)

$(TARGET_OBJ_PATH)/$(patsubst %.c,%$(ENV_OBJ_EXT),$(PP_GENERATED_C)): $(TARGET_OBJ_PATH)/$(PP_GENERATED_C) $(C_IFLAGS_VIA_FILE_NAME)
	$(VL_CMD)$(CC) $(strip $(filter-out $(FILE_RMV_OPTIONS),$(CFLAGS) $(DFLAGS)) $(FILE_ADD_OPTIONS) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(C_VIA_IFLAGS)) $(ENV_OFLAGS)$@ $<)

$(DIAG_MDB_FILE_NAME): force $(subst $(BAD_SLASH),$(GOOD_SLASH),$(DIAG_MDB_TEMPLATE))
ifeq ($(VL_MSG),$(SAY_IT))
	@echo ----creating $(DIAG_MDB_FILE_NAME) from template $(DIAG_MDB_TEMPLATE)
endif
	$(VL_DTL)$(COPY) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)$(DIAG_MDB_TEMPLATE)) $(DIAG_MDB_FILE_NAME)
ifeq ($(IS_SH_SHELL),1)
	$(VL_DTL)chmod 777 $(DIAG_MDB_FILE_NAME)
endif

$(NVM_MDB_FILE_NAME): force $(subst $(BAD_SLASH),$(GOOD_SLASH),${DIAG_NVM_MDB_TEMPLATE})
ifeq ($(VL_MSG),$(SAY_IT))
	@echo ----creating $(NVM_MDB_FILE_NAME) from template $(DIAG_NVM_MDB_TEMPLATE)
endif
	$(VL_DTL)$(COPY) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)${DIAG_NVM_MDB_TEMPLATE}) $(NVM_MDB_FILE_NAME)
ifeq ($(IS_SH_SHELL),1)
	$(VL_DTL)chmod 777 $(NVM_MDB_FILE_NAME)
endif

$(TARGET_OBJ_PATH)/$(PP_GENERATED_C):  $(PP_FILES) $(DIAG_MDB_FILE_NAME)  $(NVM_MDB_FILE_NAME) force $(CBA_PPL_FILTER_FILE)
ifneq ($(MAKELIB),${empty})
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Creating pp-archive [$(OUTPUT_TARGET_NAME).pp] ---
endif
	$(VL_DTL)$(PPA) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(patsubst %,$(GRND_ROOT)$(TARGET_OBJ_PATH)/%,$(notdir $(filter-out $(DIAG_MDB_FILE_NAME)  $(NVM_MDB_FILE_NAME) force,$^))) > $(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME).pp)
endif
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Creating target pp output [$(notdir $@)] ---
endif
ifneq (,$(findstring ICAT_CMD_WHITELIST,$(VARIANT_LIST)))
	$(VL_MSG)$(IF_DIR_EXIST1) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)$(GOOD_SLASH)cmd_filter_dir) $(IF_FILE_EXIST2) $(MKDIR) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)$(GOOD_SLASH)cmd_filter_dir) $(IF_FILE_EXIST3)
	$(VL_MSG)$(PERL) $(ICAT_CMD_FILTER) $(ICAT_CMD_FILTER_WHITELIST) $(strip $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)$(GOOD_SLASH)cmd_filter_dir)) $(PP_FILES) $(CBA_NON_STANDARD_PP) $(USE_PP)
	$(VL_CMD)$(CD) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)) $(CMD_SEPAR) $(subst $(GOOD_SLASH),$(BACK_SLASH),$(PPL)) $(TXT_MDB_FILE_NAME) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH))$(GOOD_SLASH)$(PP_GENERATED_C) $(patsubst %,$(TARGET_OBJ_PATH)$(GOOD_SLASH)cmd_filter_dir/%,$(notdir $(PP_FILES) $(CBA_NON_STANDARD_PP) $(USE_PP))) $(PPL_FILTER_SWITCH) $(CBA_PPLFLAGS) 1>nul
else
	$(VL_CMD)$(CD) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH)) $(CMD_SEPAR) $(subst $(GOOD_SLASH),$(BACK_SLASH),$(PPL)) $(TXT_MDB_FILE_NAME) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OBJ_PATH))$(GOOD_SLASH)$(PP_GENERATED_C) $(PP_FILES) $(CBA_NON_STANDARD_PP) $(USE_PP) $(PPL_FILTER_SWITCH) $(CBA_PPLFLAGS) 1>nul
endif
ifneq (,$(findstring ICAT_CMD_WHITELIST,$(VARIANT_LIST)))
	\prepass\Hermon\Release\PPManCmd.exe -S$(TPLGSM)\bldstore\hsiupdlibdev\test\hsiupdlibdev.i -N$(TXT_MDB_FILE_NAME)
# skip PPU once ICAT_CMD_WHITELIST enabled to avoid diagDB.txt and whitelist freq maintain
#	..\build\PPU.exe $(TXT_MDB_FILE_NAME) \tavor\Arbel\$(TARGET_OUT_DIR)\$(PP_GENERATED_C) \tavor\Arbel\build\diagDB_lite.txt 
else
	\prepass\Hermon\Release\PPManCmd.exe -S$(TPLGSM)\bldstore\hsiupdlibdev\test\hsiupdlibdev.i -N$(TXT_MDB_FILE_NAME)
	..\build\PPU.exe $(TXT_MDB_FILE_NAME) \tavor\Arbel\$(TARGET_OUT_DIR)\$(PP_GENERATED_C) \tavor\Arbel\build\diagDB.txt 
endif
endif

########################################### Opt File generation ##################################################
sinclude $(TRACK_VARS_FILE)

# first check varibales that are tracked by default ...
# ... BUILD_ROOT ...
ifneq ($(strip $(TRACK.BUILD_ROOT)),$(strip $(BUILD_ROOT)))
FORCE_OPT_FILE = force
overwrite DONT_INCLUDE_DEP_FILES := 1
endif
# ... GRND_ROOT ...
ifneq ($(strip $(TRACK.GRND_ROOT)),$(strip $(GRND_ROOT)))
FORCE_OPT_FILE = force
overwrite DONT_INCLUDE_DEP_FILES := 1
endif
# ... and CBA_SUMMARY ...
ifneq ($(strip $(TRACK.CBA_SUMMARY)),$(strip $(CBA_SUMMARY)))
FORCE_OPT_FILE = force
endif
# ... and LOCK_ALIEN_BUILD ...
ifneq ($(strip $(TRACK.CBA_LOCK_ALIEN_BUILD)),$(strip $(CBA_LOCK_ALIEN_BUILD)))
FORCE_OPT_FILE = force
endif
# ... and UNIFORM_TOOLSET ...
ifneq ($(strip $(TRACK.CBA_UNIFORM_TOOLSET)),$(strip $(CBA_UNIFORM_TOOLSET)))
FORCE_OPT_FILE = force
endif
# ... and CBA_CODEBASE ...
ifneq ($(strip $(TRACK.CBA_CODEBASE)),$(strip $(CBA_CODEBASE)))
FORCE_OPT_FILE = force
endif
# Close meanwhile
# # ... and STOP_ON_WARNING ...
# ifneq ($(strip $(TRACK.CBA_STOP_ON_WARNING)),$(strip $(CBA_STOP_ON_WARNING)))
# FORCE_OPT_FILE = force
# endif


# ... then all user defined (if any at all)
FORCE_OPT_FILE ?= $(firstword $(foreach VAR,$(CBA_TRACK_VARS),$(if $(filter-out $(strip $(TRACK.$(VAR)))NONEMPTY,$(strip $($(VAR)))NONEMPTY),force)))

track_vars : force
ifneq (,$(strip $(FORCE_OPT_FILE)))
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Tracked variables have chnaged, will rebuild opt file ---
endif
endif
	$(VL_DTL)$(COPY) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)$(ENV_EMPTY_FILE) $(TRACK_VARS_FILE))
	$(VL_DTL)$(ECHO) TRACK.BUILD_ROOT           = $(BUILD_ROOT)>> $(TRACK_VARS_FILE)
	$(VL_DTL)$(ECHO) TRACK.CBA_SUMMARY          = $(CBA_SUMMARY)>> $(TRACK_VARS_FILE)
	$(VL_DTL)$(ECHO) TRACK.GRND_ROOT            = $(GRND_ROOT)>> $(TRACK_VARS_FILE)
	$(VL_DTL)$(ECHO) TRACK.CBA_LOCK_ALIEN_BUILD = $(CBA_LOCK_ALIEN_BUILD) >> $(TRACK_VARS_FILE)
	$(VL_DTL)$(ECHO) TRACK.CBA_UNIFORM_TOOLSET  = $(CBA_UNIFORM_TOOLSET) >> $(TRACK_VARS_FILE)
	$(VL_DTL)$(ECHO) TRACK.CBA_CODEBASE         = $(CBA_CODEBASE) >> $(TRACK_VARS_FILE)
ifneq ($(word 1,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 1,$(CBA_TRACK_VARS)) = $($(word 1,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 2,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 2,$(CBA_TRACK_VARS)) = $($(word 2,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 3,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 3,$(CBA_TRACK_VARS)) = $($(word 3,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 4,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 4,$(CBA_TRACK_VARS)) = $($(word 4,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 5,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 5,$(CBA_TRACK_VARS)) = $($(word 5,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 6,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 6,$(CBA_TRACK_VARS)) = $($(word 6,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 7,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 7,$(CBA_TRACK_VARS)) = $($(word 7,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 8,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 8,$(CBA_TRACK_VARS)) = $($(word 8,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 9,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 9,$(CBA_TRACK_VARS)) = $($(word 9,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif
ifneq ($(word 10,$(CBA_TRACK_VARS)),$(empty))
	$(VL_DTL)$(ECHO) TRACK.$(word 10,$(CBA_TRACK_VARS)) = $($(word 10,$(CBA_TRACK_VARS))) >> $(TRACK_VARS_FILE)
endif

optfile $(OPT_FILE) : $(TARGET_MKFILE_DEP) $(FORCE_OPT_FILE)
ifeq ($(VL_MSG),$(SAY_IT))
	@echo --- Generating target options file [$(OPT_FILE)] ---
endif
	$(VL_DTL)$(ECHO) $(CMNT_SIGN)========================================================================== > $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) File        : $(OPT_FILE) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Description : Target parameters for $(OUTPUT_TARGET_NAME) build >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN)                >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Notes       : This file is auto-generated by $(TARGET_MAKEFILE) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN)========================================================================== >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Tool location parameters ----------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Build Root ----------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) BUILD_ROOT   =$(BUILD_ROOT)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) IS_SH_SHELL=$(IS_SH_SHELL) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_USE_TCC = $(CBA_USE_TCC) >> $(OPT_FILE)
ifeq ($(strip $(IS_SH_SHELL)),1)
	$(VL_DTL)$(ECHO) GRND_ROOT=$(GRND_ROOT) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) DRIVELETTER=$(DRIVELETTER) >> $(OPT_FILE)
endif
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Summary generation request details ------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_SUMMARY         = $(CBA_SUMMARY)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_SUMMARY_FILE    = $(CBA_SUMMARY_FILE)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Benchmark database file ------------------------------ >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_BM_DB           = $(CBA_BM_DB) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Build Environment settings ------------------------------ >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) IS_SH_SHELL         = ^$(IS_SH_SHELL)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) RVCT_VER            = $(RVCT_VER)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CPP_DEP_CMD         = $(CPP_DEP_CMD)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CPP_PP_CMD          = $(CPP_PP_CMD)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_TEMP            = $(CBA_TEMP)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_RAND            = $(CBA_RAND)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_BUILD_ENV_SET   = YES>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) include environment description file ------ >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) include $(BUILD_ROOT)/env/$(HOST)/build/$(ENV)_env.mak>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) include paths  --- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) GLOBAL_INC_PATHS= $(GLOBAL_INC_PATHS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Environment build parmeters -------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) HOST         = $(HOST)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) ENV          = $(ENV)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) MAKEDEP      = $(MAKEDEP) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_DONT_USE_VIA_FILE = $(CBA_DONT_USE_VIA_FILE) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Tool Set Wrapper paramters -------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_MAKEPATCH_MODE = $(CBA_MAKEPATCH_MODE) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_CODEBASE       = $(CBA_CODEBASE) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_PM_F_THRESHOLDS  = $(CBA_PM_F_THRESHOLDS) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_PM_D_THRESHOLDS  = $(CBA_PM_D_THRESHOLDS) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Target build parameters ------------------------------------ >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TARGET_PATH     = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_PATH))>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TARGET_OUT_DIR  =$(TARGET_OUT_DIR)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_OUT_DIR  =$(TARGET_OUT_DIR)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TARGET_OBJ_PATH = $(TARGET_OBJ_PATH)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TARGET_INCCACHE_FILE = $(TARGET_INCCACHE_FILE)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TARGET_INCCACHE_PATH = $(TARGET_INCCACHE_PATH)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TARGET_CODECACHE_PATH = $(TARGET_CODECACHE_PATH)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_ASM_EXTRA_DEPS  = $(ASM_EXTRA_DEPS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_C_EXTRA_DEPS  = $(C_EXTRA_DEPS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) target build flags                  >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_ASMFLAGS = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(PARENT_ASMFLAGS))) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_CFLAGS   = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(ENV_CFLAGS) $(TARGET_CFLAGS))) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_DFLAGS   = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(ENV_DFLAGS) $(addprefix -D,$(VARIANT_LIST_SPREAD2)) $(TARGET_DFLAGS) )) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_VFLAGS   = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(ENV_VFLAGS) $(TARGET_VFLAGS))) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_C_DFLAGS     = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(ENV_DFLAGS) $(addprefix -D,$(VARIANT_LIST_SPREAD2)) $(TARGET_DFLAGS) $(TARGET_C_DFLAGS))) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_ASM_DFLAGS   = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(ENV_DFLAGS) $(TARGET_DFLAGS) $(TARGET_ASM_DFLAGS))) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_ARFLAGS  = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(ENV_ARFLAGS) $(TARGET_ARFLAGS))) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PARENT_INC_PATHS = $(subst ",$(QUOTE),$(ALL_INC_PATHS)) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) BUGFIXER_FLAGS  = $(subst \,$(BACK_SLASH),$(subst ",$(QUOTE),$(BUGFIXER_FLAGS))) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) RUN_BUGFIXER    = $(RUN_BUGFIXER) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Include path order ------------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) INC_PATH_ORDER = $(INC_PATH_ORDER) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Group build parameters ------------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) GROUP_LIST = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GROUP_LIST))>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) GROUP_INC_PATHS   = $(GROUP_INC_PATHS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Package build parameters ----------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PACKAGE_LIST = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(PACKAGE_LIST))>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PACKAGE_INC_PATHS = $(PACKAGE_INC_PATHS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Variant Parameters ----------------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TARGET_VARIANT  = $(TARGET_VARIANT)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) VARIANT_LIST    = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(VARIANT_LIST))>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Tracing parameters ----------------------------------------- >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) TRACE_LIST = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TRACE_LIST))>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PP = $(PP)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PPC_PIPE = $(PPC_PIPE) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PPC = $(subst \,$(BACK_SLASH),$(PPC))>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PPA = $(PPA)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PPR_PATH = $(PPR_PATH)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) DIAGDB_C = $(DIAGDB_C)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) PP_GENERATED_C = $(PP_GENERATED_C)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) DIAG_MDB_TEMPLATE = $(DIAG_MDB_TEMPLATE)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) DIAG_NVM_MDB_TEMPLATE = $(DIAG_NVM_MDB_TEMPLATE)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CCPP_OPTION = $(CCPP_OPTION)>> ${OPT_FILE}
	$(VL_DTL)$(ECHO) CCPP_AT_ONCE = $(CCPP_AT_ONCE)>> ${OPT_FILE}
	$(VL_DTL)$(ECHO) DIAG_MDB        = ${DIAG_MDB}>> ${OPT_FILE}
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Various filter and special lists parameters  ------------------------------ >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_SRC_FILES_FILTER = $(CBA_SRC_FILES_FILTER)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_SRC_FILES_SPECIAL_FLAGS = $(CBA_SRC_FILES_SPECIAL_FLAGS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_ASM_SPECIAL_FLAGS = $(CBA_ASM_SPECIAL_FLAGS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) CBA_C_SPECIAL_FLAGS = $(CBA_C_SPECIAL_FLAGS)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Third Party build parameters ------------------------------ >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) EXT_MAKE=$(EXT_MAKE)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) FB_EXTMAKE=$(FB_EXTMAKE)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) FB_TTP=$(FB_TTP)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) Various Parameters  --------------------------------- >> $(OPT_FILE)
ifneq ($(strip $(CBA_LOCK_ALIEN_BUILD)),$(empty))
	$(VL_DTL)$(ECHO) CBA_LOCK_ALIEN_BUILD = $(CBA_LOCK_ALIEN_BUILD) >> $(OPT_FILE)
endif
ifneq ($(strip $(CBA_UNIFORM_TOOLSET)),$(empty))
	$(VL_DTL)$(ECHO) CBA_UNIFORM_TOOLSET = $(CBA_UNIFORM_TOOLSET) >> $(OPT_FILE)
endif
	$(VL_DTL)$(ECHO) $(CMNT_SIGN) User Defined Variables  ------------------------------ >> $(OPT_FILE)
	$(VL_DTL)$(ECHO) USER_DEFINED_VAR1 = $(USER_DEFINED_VAR1)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) USER_DEFINED_VAR2 = $(USER_DEFINED_VAR2)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) USER_DEFINED_VAR3 = $(USER_DEFINED_VAR3)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) USER_DEFINED_VAR4 = $(USER_DEFINED_VAR4)>> $(OPT_FILE)
	$(VL_DTL)$(ECHO) USER_DEFINED_VAR5 = $(USER_DEFINED_VAR5)>> $(OPT_FILE)
ifneq ($(strip $(USER_DEFINED_VAR1)),$(empty))
ifeq  ($(strip $($(strip $(USER_DEFINED_VAR1)))),$(empty))
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR1) = >> $(OPT_FILE)
else
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR1) = $(SAFE_ECHO)$($(strip $(USER_DEFINED_VAR1)))>> $(OPT_FILE)
endif
endif
ifneq ($(strip $(USER_DEFINED_VAR2)),$(empty))
ifeq  ($(strip $($(strip $(USER_DEFINED_VAR2)))),$(empty))
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR2) = >> $(OPT_FILE)
else
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR2) = $(SAFE_ECHO)$($(strip $(USER_DEFINED_VAR2)))>> $(OPT_FILE)
endif
endif
ifneq ($(strip $(USER_DEFINED_VAR3)),$(empty))
ifeq  ($(strip $($(strip $(USER_DEFINED_VAR3)))),$(empty))
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR3) = >> $(OPT_FILE)
else
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR3) = $(SAFE_ECHO)$($(strip $(USER_DEFINED_VAR3)))>> $(OPT_FILE)
endif
endif
ifneq ($(strip $(USER_DEFINED_VAR4)),$(empty))
ifeq  ($(strip $($(strip $(USER_DEFINED_VAR4)))),$(empty))
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR4) = >> $(OPT_FILE)
else
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR4) = $(SAFE_ECHO)$($(strip $(USER_DEFINED_VAR4)))>> $(OPT_FILE)
endif
endif
ifneq ($(strip $(USER_DEFINED_VAR5)),$(empty))
ifeq  ($(strip $($(strip $(USER_DEFINED_VAR5)))),$(empty))
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR5) = >> $(OPT_FILE)
else
	$(VL_DTL)$(ECHO) $(USER_DEFINED_VAR5) = $(SAFE_ECHO)$($(strip $(USER_DEFINED_VAR5)))>> $(OPT_FILE)
endif
endif

# Build Utilities -------------------------------------------------------------

################################################ Garbage Collector #####################################
cba_garbage_collector : $(CLEAN_SEPARATE_TEMPDIR) force

clean_separate_tempdir : force
	$(VL_EXP)-$(RMDIR) $(TEMPDIR)

################################################ Including dependencies ####################################
FILE_WITH_DEP     = $(filter %.c %$(ENV_ASM_EXT),$(LOCAL_SRC_FILES))
LOCAL_DEP_FILES   = $(patsubst %, $(TARGET_OBJ_PATH)/%, $(addsuffix .d, $(basename $(FILE_WITH_DEP))))
INCLUDE_DEP_FILES = $(subst $(BAD_SLASH),$(GOOD_SLASH),$(LOCAL_DEP_FILES)) $(LDF_TARGET).pp.d

# include generated dependency rules if any
ifneq ($(strip $(DONT_INCLUDE_DEP_FILES)),1)
sinclude $(INCLUDE_DEP_FILES) nonexistant.file
endif

################################################ Force build ###########################################
forcebuild: track_vars optfile step1

################################################ Clean ###############################################
clean  : clean_obj clean_opt clean_bin clean_pre_pass
clean_obj:
	$(VL_EXP)echo --- Removing object directory and files
	$(VL_EXP)-$(RMDIR) /s /q $(TARGET_OBJ_PATH)

clean_opt:
	$(VL_EXP)echo --- Removing target options file [$(OPT_FILE)] ---
	$(VL_EXP)-$(DEL) $(OPT_FILE)

clean_bin:
	$(VL_EXP)echo --- Removing binary file [$(OUTPUT_TARGET_NAME)] ---
	$(VL_EXP)-$(DEL) $(TARGET) $(TARGET_OUT_PATH)$(GOOD_SLASH)*.map

clean_pre_pass:
	$(VL_EXP)echo --- Removing pre pass relavant pass [$(OUTPUT_TARGET_NAME)] ---
	$(VL_EXP)-$(DEL) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME)_DIAG.mdb)
	$(VL_EXP)-$(DEL) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME)_NVM.mdb)


############################################ Summary report ############################################
ifneq ($(strip $(CBA_SUMMARY)),$(empty))

start_report: force
ifneq (,$(findstring WARNING,$(CBA_SUMMARY)))
	$(VL_DTL)-$(COPY) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)$(ENV_EMPTY_FILE) $(GRND_ROOT)$(WARN_COUNTER_FILE)) $(ERROR_TO_NUL)
	$(VL_DTL)-$(COPY) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GRND_ROOT)$(ENV_EMPTY_FILE) $(GRND_ROOT)$(WARN_COUNTER_LINKER)) $(ERROR_TO_NUL)
endif
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)--------------------------------- > $(GROUNDED_SUMMARY_FILE)
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)[BEGIN] Target $(OUTPUT_TARGET_NAME) >> $(GROUNDED_SUMMARY_FILE)
ifneq (,$(findstring INFO,$(CBA_SUMMARY)))
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Built   : $(DATE) >> $(GROUNDED_SUMMARY_FILE)
ifneq ($(strip $(TARGET_VARIANT)),$(empty))
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Variant : $(TARGET_VARIANT) >> $(GROUNDED_SUMMARY_FILE)
else
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Variant : DEFAULT >> $(GROUNDED_SUMMARY_FILE)
endif
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Binary  : $(TARGET) >> $(GROUNDED_SUMMARY_FILE)
endif
ifneq (,$(findstring TIME,$(CBA_SUMMARY)))
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Start   : $(TIME) >> $(GROUNDED_SUMMARY_FILE)
endif

finish_report: force
ifneq (,$(findstring TIME,$(CBA_SUMMARY)))
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Finish  : $(TIME) >> $(GROUNDED_SUMMARY_FILE)
endif
ifneq (,$(findstring WARNING,$(CBA_SUMMARY)))
	$(VL_DTL)perl -e "$(SCALAR)w=do q($(WARN_COUNTER_FILE)); $(SCALAR)w=0 if !$(SCALAR)w;print qq($(CBA_LEVELOFFSET)     Warnings : $(SCALAR)w\n)" >> $(GROUNDED_SUMMARY_FILE)
	$(VL_DTL)perl -e "$(SCALAR)w=do q($(WARN_COUNTER_LINKER)); $(SCALAR)w=0 if !$(SCALAR)w;print qq($(CBA_LEVELOFFSET)     Linker Warnings : $(SCALAR)w\n)" >> $(GROUNDED_SUMMARY_FILE)
endif
ifneq (,$(findstring COUNT,$(CBA_SUMMARY)))
ifneq ($(strip $(LOCAL_SRC_FILES)),$(empty))
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Built   : >> $(GROUNDED_SUMMARY_FILE)
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)         $(words $(filter-out %$(ENV_ASM_EXT),$(LOCAL_SRC_FILES))) C files >> $(GROUNDED_SUMMARY_FILE)
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)         $(words $(filter-out %.c,$(LOCAL_SRC_FILES))) ASM files >> $(GROUNDED_SUMMARY_FILE)
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     Total Built $(words $(LOCAL_SRC_FILES)) files >> $(GROUNDED_SUMMARY_FILE)
else
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)     No local files >> $(GROUNDED_SUMMARY_FILE)
endif
endif
	$(VL_DTL)$(ECHO) $(CBA_LEVELOFFSET)[END] Target $(OUTPUT_TARGET_NAME) >> $(GROUNDED_SUMMARY_FILE)
	$(VL_DTL)$(ECHO) --------------------------------------------------- >> $(GROUNDED_SUMMARY_FILE)
	$(VL_DTL)$(CAT) $(GROUNDED_SUMMARY_FILE)
endif

############################################## Build Info ###############################################
build_info: force
	@echo ---------------------------------------------------------------------
	@echo $(TARGET_NAME) Build Configuration target varaint: $(TARGET_VARIANT)
	@echo REGISTERED BUILD VARIANTS
	@perl -e "foreach (grep { $(SCALAR)f = ${SCALAR}_ if ${SCALAR}f ne ${SCALAR}_ } sort {${SCALAR}a cmp ${SCALAR}b} @ARGV){print qq(\t${SCALAR}_\n)}" $(VARIANT_LIST)
	@echo ---------------------------------------------------------------------

############################################### Files ##################################################
files  : track_vars $(TARGET_OBJ_PATH) target_files $(SHOW_FILES_LIST)

target_files:
	@echo --- Target local build files [$(OUTPUT_TARGET_NAME)]---
	@echo Variant  : $(TARGET_VARIANT)
	@echo Source   : $(LOCAL_SRC_FILES)
	@echo Packages : $(subst $(BAD_SLASH),$(GOOD_SLASH),$(PACKAGE_LIST))
	@echo Groups   : $(subst $(BAD_SLASH),$(GOOD_SLASH),$(GROUP_LIST))
	@echo Binary   : $(TARGET)
	@echo -------------------------------------------------------

$(SHOW_FILES_LIST): $(OPT_FILE_DEP)
	$(VL_DTL)$(CD) $(GRND_ROOT)$(BUILD_ROOT)$(GOOD_SLASH)$(@:files.%=%)$(GOOD_SLASH)build $(CMD_SEPAR) $(MAKE) -s -f $(notdir $(@:files.%=%)).mak $(OPT_FILE_MAKE_STR) files

############################################### Flags #################################################
flags  : track_vars $(TARGET_OBJ_PATH) env_flags target_flags $(SHOW_FLAGS_LIST)

env_flags:
	@echo --- Environment build flags [$(ENV)]---
	@echo Env ASMFLAGS : $(ENV_ASMFLAGS)
	@echo Env CFLAGS   : $(ENV_CFLAGS)
	@echo Env CPPFLAGS : $(ENV_CPPFLAGS)
	@echo Env DFLAGS   : $(ENV_DFLAGS)
	@echo Env IFLAGS   : $(ENV_IFLAGS)
	@echo Env ARFLAGS  : $(ENV_ARFLAGS)
	@echo Env LDFLAGS  : $(ENV_LDFLAGS)
	@echo -------------------------------------------------------

target_flags:
	@echo --- Target local build flags [$(OUTPUT_TARGET_NAME)]---
	@echo Variant             : $(TARGET_VARIANT)
	@echo Target ASMFLAGS     : $(TARGET_ASMFLAGS)
	@echo Target CFLAGS       : $(TARGET_CFLAGS)
	@echo Target DFLAGS       : $(TARGET_DFLAGS)
	@echo Target C DFLAGS     : $(TARGET_C_DFLAGS)
	@echo Target ASM DFLAGS   : $(TARGET_ASM_DFLAGS)
	@echo Target IFLAGS       : $(TARGET_IFLAGS)
	@echo Target ARFLAGS      : $(TARGET_ARFLAGS)
	@echo Target LDFLAGS      : $(TARGET_LDFLAGS)
	@echo -----------------
	@echo Local Target ASMFLAGS      : $(LOCAL_ASMFLAGS)
	@echo Local Target CFLAGS        : $(LOCAL_CFLAGS)
	@echo Local Target DFLAGS        : $(LOCAL_DFLAGS)
	@echo Local Target C DFLAGS      : $(LOCAL_C_DFLAGS)
	@echo Local Target ASM DFLAGS    : $(LOCAL_ASM_DFLAGS)
	@echo Local Target INC PATHS     : $(LOCAL_INC_PATHS)
	@echo Local Target ARFLAGS       : $(LOCAL_ARFLAGS)
	@echo -----------------
	@echo All Target ASMFLAGS      : $(ASMFLAGS)
	@echo All Target CFLAGS        : $(CFLAGS)
	@echo All Target DFLAGS        : $(DFLAGS)
	@echo All Target C DFLAGS      : $(C_DFLAGS)
	@echo All Target ASM DFLAGS    : $(ASM_DFLAGS)
	@echo All Target IFLAGS        : $(C_VIA_IFLAGS)
	@echo All Target ARFLAGS       : $(ARFLAGS)
	@echo All Target LDFLAGS       : $(LDFLAGS)
	@echo ----------------
	@echo ALL_INC_PATHS  : $(U_ALL_INC_PATHS)
	@echo -------------------------------------------------------

$(SHOW_FLAGS_LIST): $(OPT_FILE_DEP)
	$(VL_CMD)$(CD) $(GRND_ROOT)$(BUILD_ROOT)$(GOOD_SLASH)$(@:flags.%=%)$(GOOD_SLASH)build $(CMD_SEPAR) $(MAKE) -s -f $(notdir $(@:flags.%=%)).mak $(OPT_FILE_MAKE_STR) flags

################################################# CBA Version Info ##########################################
version cba_version :
	@echo This is CBA version $(CBA_RELEASE)
	@echo Release date $(CBA_RELEASE_DATE)
	@echo ---------------------------------------------
	@echo Please refer to Release Summary (from CBA documentation repository)
	@echo for detailes regarding this release

################################################# Include Cache  ##########################################
include_cache : track_vars $(OPT_FILE) $(TARGET_OBJ_PATH) $(ALL_CHILD_OBJ_DIRS) $(PPR_PATH)  $(TARGET_INCCACHE_PATH) $(TARGET_CODECACHE_PATH) $(TARGET_OUT_PATH) target_inccache $(SHOW_INCCACHE_LIST) fill_inccache

target_inccache : 
ifeq ($(VL_MSG),$(SAY_IT))
	@echo - Exploring build tree
endif
	$(VL_DTL)$(COPY) $(BUILD_ROOT)$(GOOD_SLASH)env$(GOOD_SLASH)win32$(GOOD_SLASH)tools$(GOOD_SLASH)inccache.pl $(TARGET_INCCACHE_FILE) $(OUT_TO_NUL)

fill_inccache :
ifeq ($(VL_MSG),$(SAY_IT))
	@echo - Generating include cache
endif
	$(VL_DTL)$(PERL) $(TARGET_INCCACHE_FILE) $(TARGET_INCCACHE_PATH) $(ENV_INCCACHE_ERROR_ON_DUPLICATE)

$(SHOW_INCCACHE_LIST):
	$(VL_DTL)$(MAKE) -C $(GRND_ROOT)$(BUILD_ROOT)$(GOOD_SLASH)$(@:inccache.%=%)$(GOOD_SLASH)build -s -f $(notdir $(@:inccache.%=%)).mak $(OPT_FILE_MAKE_STR) $(EXPORT_VARS) include_cache

###################################### Code Cache & Patch Maker ####################################
chain_patching: force
ifeq ($(VL_MSG),$(SAY_IT))
	@echo - Patching chains
endif
	$(VL_DTL)$(ENV_PATCHMAKER) mkchain $(CBA_CODEBASE) $(TARGET_CODECACHE_PATH) $(CLEAN_CC) $(CLEAN_ASM) $(subst $(BAD_SLASH),$(GOOD_SLASH),$(TARGET_PATH)$(GOOD_SLASH)$(TARGET_OUT_DIR)) "$(strip $(CBA_PM_F_THRESHOLDS))" "$(strip $(CBA_PM_D_THRESHOLDS))" "$(strip $(CBA_MAKEPATCH_MODE))"

################################################# Codebase ##########################################

ifneq ($(filter mkcodebase,$(MAKECMDGOALS)),${empty})
CODEBASE_LD_FLAGS = -xref

override CBA_MAKEPATCH_MODE := LOG
export CBA_MAKEPATCH_MODE

mkcodebase: cb_msg1  			\
	    cb_msg2 step1		\
            cb_msg3 cb_initcb		\
	    cb_msg4 cb_initcc 		\
	    cb_msg5 cb_mklist 		\
	    cb_msg6 cb_mkcb 		\
            cb_msg7 cb_mktree 		\
            cb_msg8 cb_calcsize		\
	    cb_msg9

cb_mkcb: force
	$(VL_CMD)$(ENV_PATCHMAKER) mkcb $(TARGET_CODECACHE_PATH) $(CBA_CODEBASE)

cb_mklist: force
	$(VL_CMD)$(ENV_PATCHMAKER) mklist $(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME).map $(CBA_CODEBASE)

cb_calcsize: force
	$(VL_CMD)$(ENV_PATCHMAKER) calcsize $(TARGET_OUT_PATH)$(GOOD_SLASH)$(OUTPUT_TARGET_NAME).map $(CBA_CODEBASE)

cb_mktree: force
	$(VL_CMD)$(ENV_PATCHMAKER) mktree .$(GOOD_SLASH)p0.xrf $(CBA_CODEBASE)

cb_initcb: force
	$(VL_CMD)$(ENV_PATCHMAKER) initcb $(CBA_CODEBASE)

cb_initcc: force
	$(VL_CMD)$(ENV_PATCHMAKER) initcc $(TARGET_CODECACHE_PATH)

cb_msg%: force
	$(call CB_MSG,$(MSG$(*)))


MSG1 = START CODEBASE GENERATION AT $(CBA_CODEBASE)
MSG2 = STEP 1 - Building original code to obtain codebase raw materials, .map and .xrf files
MSG3 = STEP 2 - Creating new empty Codebase
MSG4 = STEP 3 - Initializing Code Cache repository
MSG5 = STEP 4 - Generating list of ROM sections
MSG6 = STEP 5 - Genrating codebase
MSG7 = STEP 6 - Genrating call tree
MSG8 = STEP 7 - Calculating patching penalty for each function
MSG9 = CODEBASE AT $(CBA_CODEBASE) HAS BEEN SUCCESSFULLY CREATED

define CB_MSG
@echo .
@echo .
@echo ---------------------------------------------------------------------
@echo $(1)
@echo .
endef

endif


################################################ CBA-T extension ##########################################
#INCLUDE_TEST_TARGET_MAK :=$(CBA_T_CLASSPATH)\make\test_msa_target.mak
#sinclude $(INCLUDE_TEST_TARGET_MAK) nonexistant.file
