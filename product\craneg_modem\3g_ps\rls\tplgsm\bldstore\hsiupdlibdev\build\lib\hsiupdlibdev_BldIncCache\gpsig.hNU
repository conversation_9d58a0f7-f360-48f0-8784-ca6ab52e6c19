/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 *
 *                       TTPCom GPRS
 *
 *                  Copyright (c) 1999 TTPCom Ltd.
 *
 ***************************************************************************
 *
 *   $Id: //central/releases/Branch_release_9/tplgsm/gpinc/gpsig.h#3 $
 *   $Revision: #3 $
 *   $DateTime: 2004/01/14 14:10:56 $
 *
 ************************************************************************
 *
 * File Description:
 *
 * GPRS signal definitions
 *
 ************************************************************************
 *
 ************************************************************************/


#if !defined (EXCLUDE_LLC)
SIG_DEF( SIG_LLC_DUMMY = GPLLC_SIGNAL_BASE, EmptySignal   gpllc_dummy)

SIG_DEF( SIG_LLGMM_ASSIGN_REQ,    LlgmmAssignReq     llgmmAssignReq)
SIG_DEF( SIG_LLGMM_RESET_REQ,     LlgmmResetReq      llgmmResetReq)  /* SGSN */
SIG_DEF( SIG_LLGMM_RESET_CNF,     LlgmmResetCnf      llgmmResetCnf)  /* SGSN */
SIG_DEF( SIG_LLGMM_TRIGGER_REQ,   LlgmmTriggerReq    llgmmTriggerReq)
SIG_DEF( SIG_LLGMM_SUSPEND_REQ,   LlgmmSuspendReq    llgmmSuspendReq)
SIG_DEF( SIG_LLGMM_RESUME_REQ,    LlgmmResumeReq     llgmmResumeReq)
SIG_DEF( SIG_LLGMM_PAGE_IND,      LlgmmPageInd       llgmmPageInd)   /* SGSN */
SIG_DEF( SIG_LLGMM_IOV_REQ,       LlgmmIovReq        llgmmIovReq)    /* SGSN */
SIG_DEF( SIG_LLGMM_IOV_CNF,       LlgmmIovCnf        llgmmIovCnf)    /* SGSN */
SIG_DEF( SIG_LLGMM_STATUS_IND,    LlgmmStatusInd     llgmmStatusInd)

SIG_DEF( SIG_LLC_RESET_IND,       LlcResetInd        llcResetInd)
SIG_DEF( SIG_LLC_ESTABLISH_REQ,   LlcEstablishReq    llcEstablishReq)
SIG_DEF( SIG_LLC_ESTABLISH_CNF,   LlcEstablishCnf    llcEstablishCnf)
SIG_DEF( SIG_LLC_ESTABLISH_IND,   LlcEstablishInd    llcEstablishInd)
SIG_DEF( SIG_LLC_ESTABLISH_RSP,   LlcEstablishRsp    llcEstablishRsp)
SIG_DEF( SIG_LLC_RELEASE_REQ,     LlcReleaseReq      llcReleaseReq)
SIG_DEF( SIG_LLC_RELEASE_CNF,     LlcReleaseCnf      llcReleaseCnf)
SIG_DEF( SIG_LLC_RELEASE_IND,     LlcReleaseInd      llcReleaseInd)
SIG_DEF( SIG_LLC_DATA_REQ,        LlcDataReq         llcDataReq)
SIG_DEF( SIG_LLC_DATA_CNF,        LlcDataCnf         llcDataCnf)
SIG_DEF( SIG_LLC_DATA_IND,        LlcDataInd         llcDataInd)
SIG_DEF( SIG_LLC_UNIT_DATA_REQ,   LlcUnitDataReq     llcUnitDataReq)
SIG_DEF( SIG_LLC_UNIT_DATA_CNF,   LlcUnitDataCnf     llcUnitDataCnf)
SIG_DEF( SIG_LLC_UNIT_DATA_IND,   LlcUnitDataInd     llcUnitDataInd)
SIG_DEF( SIG_LLC_XID_REQ,         LlcXidReq          llcXidReq)
SIG_DEF( SIG_LLC_XID_CNF,         LlcXidCnf          llcXidCnf)
SIG_DEF( SIG_LLC_XID_IND,         LlcXidInd          llcXidInd)
SIG_DEF( SIG_LLC_XID_RSP,         LlcXidRsp          llcXidRsp)
SIG_DEF( SIG_LLC_STATUS_IND,      LlcStatusInd       llcStatusInd)

SIG_DEF( SIG_LLC_RX_OFFSET_REQ,   LlcRxOffsetReq     llcRxOffsetReq)
SIG_DEF( SIG_LLC_CFG_REQ,         LlcCfgReq          llcCfgReq)
SIG_DEF( SIG_LLC_PDU_TRACE_REQ,   LlcPduTraceReq     llcPduTraceReq)
SIG_DEF( SIG_LLC_PDU_TRACE_IND,   LlcPduTraceInd     llcPduTraceInd)

#endif

#if !defined (EXCLUDE_SN)
SIG_DEF( SIG_SN_DUMMY = GPSN_SIGNAL_BASE, EmptySignal   gpsn_dummy)

SIG_DEF( SIG_SN_DATA_REQ,       SnDataReq           snDataReq)
SIG_DEF( SIG_SN_UNIT_DATA_REQ,  SnUnitDataReq       snUnitDataReq)
SIG_DEF( SIG_SN_DATA_IND,       SnDataInd           snDataInd)
SIG_DEF( SIG_SN_UNIT_DATA_IND,  SnUnitDataInd       snUnitDataInd)
SIG_DEF( SIG_SN_XID_REQ,        SnXidReq            snXidReq)
SIG_DEF( SIG_SN_XID_CNF,        SnXidCnf            snXidCnf)
SIG_DEF( SIG_SN_XID_IND,        SnXidInd            snXidInd)
SIG_DEF( SIG_SN_XID_RSP,        SnXidRsp            snXidRsp)
SIG_DEF( SIG_SN_RESET_IND,      SnResetInd          snResetInd)
SIG_DEF( SIG_SN_CONFIG_REQ,     SnConfigReq         snConfigReq)

#if defined (GPRS_SNDCP_TMM_STATS)
SIG_DEF( SIG_SNMEM_STATUS_REQ,  EmptySignal         snMemStatusReq)
SIG_DEF( SIG_SNMEM_STATUS_CNF,  SnMemStatusCnf      snMemStatusCnf)
#endif

#endif

#if !defined (EXCLUDE_SM)
SIG_DEF( SIG_SNSM_DUMMY = GPSNSM_SIGNAL_BASE, EmptySignal   gpsnsm_dummy)

SIG_DEF( SIG_SNSM_ACTIVATE_IND,   SnSmActivateInd    snsmActivateInd)
SIG_DEF( SIG_SNSM_ACTIVATE_RSP,   SnSmActivateRsp    snsmActivateRsp)
SIG_DEF( SIG_SNSM_DEACTIVATE_IND, SnSmDeactivateInd  snsmDeactivateInd)
SIG_DEF( SIG_SNSM_DEACTIVATE_RSP, SnSmDeactivateRsp  snsmDeactivateRsp)
SIG_DEF( SIG_SNSM_MODIFY_IND,     SnSmModifyInd      snsmModifyInd)
SIG_DEF( SIG_SNSM_MODIFY_RSP,     SnSmModifyRsp      snsmModifyRsp)
SIG_DEF( SIG_SNSM_STATUS_REQ,     SnSmStatusReq      snsmStatusReq)
SIG_DEF( SIG_SNSM_SEQUENCE_IND,   SnSmSequenceInd    snsmSequenceInd)
SIG_DEF( SIG_SNSM_SEQUENCE_RSP,   SnSmSequenceRsp    snsmSequenceRsp)
SIG_DEF( SIG_SNSM_REPORT_COUNTER_REQ,   SnSmReportCounterReq    snsmReportCounterReq)
SIG_DEF( SIG_SNSM_RESET_COUNTER_REQ,    SnSmResetCounterReq     snsmResetCounterReq)
SIG_DEF( SIG_SNSM_COUNTER_IND,   SnSmCounterInd      snsmCounterInd)





      
#endif

#if !defined (EXCLUDE_SMREG)
SIG_DEF( SIG_SMREG_DUMMY = GPSMREG_SIGNAL_BASE, EmptySignal       gpsmReg_dummy)

SIG_DEF( SIG_SMREG_PDP_ACTIVATE_REQ,     SmRegPdpActivateReq      smRegPdpActivateReq)
SIG_DEF( SIG_SMREG_PDP_ACTIVATE_CNF,     SmRegPdpActivateCnf      smRegPdpActivateCnf)
SIG_DEF( SIG_SMREG_PDP_ACTIVATE_REJ,     SmRegPdpActivateRej      smRegPdpActivateRej)
SIG_DEF( SIG_SMREG_PDP_ACTIVATE_IND,     SmRegPdpActivateInd      smRegPdpActivateInd)
SIG_DEF( SIG_SMREG_PDP_DEACTIVATE_REQ,   SmRegPdpDeactivateReq    smRegPdpDeactivateReq)
SIG_DEF( SIG_SMREG_PDP_DEACTIVATE_CNF,   SmRegPdpDeactivateCnf    smRegPdpDeactivateCnf)
SIG_DEF( SIG_SMREG_PDP_DEACTIVATE_IND,   SmRegPdpDeactivateInd    smRegPdpDeactivateInd)
SIG_DEF( SIG_SMREG_PDP_MODIFY_IND,       SmRegPdpModifyInd        smRegPdpModifyInd)

#if defined (UPGRADE_R99)
SIG_DEF( SIG_SMREG_PDP_MODIFY_REQ,       SmRegPdpModifyReq        smRegPdpModifyReq)
SIG_DEF( SIG_SMREG_PDP_MODIFY_CNF,       SmRegPdpModifyCnf        smRegPdpModifyCnf)
SIG_DEF( SIG_SMREG_PDP_MODIFY_REJ,       SmRegPdpModifyRej        smRegPdpModifyRej)
SIG_DEF( SIG_SMREG_PDP_ACTIVATE_SEC_REQ, SmRegPdpActivateSecReq   smRegPdpActivateSecReq)
SIG_DEF( SIG_SMREG_PDP_ACTIVATE_SEC_CNF, SmRegPdpActivateSecCnf   smRegPdpActivateSecCnf)
SIG_DEF( SIG_SMREG_PDP_ACTIVATE_SEC_REJ, SmRegPdpActivateSecRej   smRegPdpActivateSecRej)
SIG_DEF( SIG_SMREG_PDP_ACTIVATE_REJ_RSP, SmRegPdpActivateRejRsp   smRegPdpActivateRejRsp)
#endif

#if defined GPRS_PPP_DIALLER
SIG_DEF( SIG_SMREG_PDP_STORE_ASSIGNED_ADDR_REQ, SmRegPdpStoreAssignedAddr smRegPdpStoreAssignedAddr)
#endif
#endif

#if !defined (EXCLUDE_GMM)
SIG_DEF( SIG_GMM_DUMMY = GPGMM_SIGNAL_BASE, EmptySignal       gpgmm_dummy)
SIG_DEF( SIG_GMM_UNIT_DATA_REQ,          GmmUnitDataReq       gmmUnitDataReq)
SIG_DEF( SIG_GMM_UNIT_DATA_IND,          GmmUnitDataInd       gmmUnitDataInd)
SIG_DEF( SIG_GMM_PDU_UNIT_DATA_IND,      GmmPduUnitDataInd    gmmPduUnitDataInd)



       
#endif

#if !defined (EXCLUDE_GMMSM)
SIG_DEF( SIG_GMMSM_DUMMY = GPGMMSM_SIGNAL_BASE, EmptySignal   gpgmmSm_dummy)
SIG_DEF( SIG_GMMSM_ESTABLISH_REQ, EmptySignal                 gmmSmEstablishReq)
SIG_DEF( SIG_GMMSM_ESTABLISH_CNF, EmptySignal                 gmmSmEstablishCnf)
SIG_DEF( SIG_GMMSM_ESTABLISH_REJ, GmmSmEstablishRej           gmmSmEstablishRej)
SIG_DEF( SIG_GMMSM_RELEASE_IND,   EmptySignal                 gmmSmReleaseInd)
SIG_DEF( SIG_GMMSM_UNIT_DATA_REQ, GmmSmUnitDataReq            gmmSmUnitDataReq)
SIG_DEF( SIG_GMMSM_UNIT_DATA_IND, GmmSmUnitDataInd            gmmSmUnitDataInd)
SIG_DEF( SIG_GMMSM_SEQUENCE_IND,  GmmSmSequenceInd            gmmSmSequenceInd)
SIG_DEF( SIG_GMMSM_SEQUENCE_RSP,  GmmSmSequenceRsp            gmmSmSequenceRsp)
#if defined (UPGRADE_R99)
SIG_DEF( SIG_GMMSM_RESUME_IND,    GmmSmResumeInd              gmmSmResumeInd)
SIG_DEF( SIG_GMMSM_SUSPEND_IND,   GmmSmSuspendInd             gmmSmSuspendInd)
#else
SIG_DEF( SIG_GMMSM_RESUME_IND,    EmptySignal                 gmmSmResumeInd)
SIG_DEF( SIG_GMMSM_SUSPEND_IND,   EmptySignal                 gmmSmSuspendInd)
#endif
#if defined (UPGRADE_R99)
SIG_DEF( SIG_GMMSM_PDP_CONTEXT_IND,EmptySignal                gmmSmPdpContextInd)
SIG_DEF( SIG_GMMSM_PDP_CONTEXT_RSP,GmmSmPdpContextRsp         gmmSmPdpContextRsp)
#endif
SIG_DEF( SIG_GMMSM_TEST_MODE_IND, GmmSmTestModeInd            gmmSmTestModeInd)
#endif

#if !defined (EXCLUDE_ABGPSM)
SIG_DEF( SIG_ABGP_SM_DUMMY = GPABGPSM_SIGNAL_BASE, EmptySignal     abgpSm_dummy)
SIG_DEF( SIG_ABGP_SM_QOS_IND,              AbgpSmQosInd            abgpSmQosInd)
SIG_DEF( SIG_ABGP_SM_REG_PDP_ACTIVATE_IND, AbgpSmRegPdpActivateInd abgpSmRegPdpActivateInd)
SIG_DEF( SIG_ABGP_SM_REG_PDP_ACTIVATE_RSP, AbgpSmRegPdpActivateRsp abgpSmRegPdpActivateRsp)
SIG_DEF( SIG_ABGP_SM_REPORT_COUNTER_REQ,   AbgpSmReportCounterReq  abgpSmReportCounterReq)
SIG_DEF( SIG_ABGP_SM_RESET_COUNTER_REQ,    AbgpSmResetCounterReq   abgpSmResetCounterReq)
SIG_DEF( SIG_ABGP_SM_COUNTER_IND,          AbgpSmCounterInd        abgpSmCounterInd)
SIG_DEF( SIG_ABGP_SM_TEST_INFO_IND,        AbgpSmTestInfoInd       abgpSmTestInfoInd)
#endif

#if !defined (EXCLUDE_DSPPP)
SIG_DEF( SIG_DS_PPP_DUMMY = GPDSPPP_SIGNAL_BASE, EmptySignal   dsppp_dummy)
SIG_DEF( SIG_DS_PPP_ERROR_IND,        DsPppErrorInd    dsPppErrorInd)
SIG_DEF( SIG_DS_PPP_DATA_IND,         DsPppDataInd     dsPppDataInd)
SIG_DEF( SIG_DS_PPP_CONFIG_REQ,       DsPppConfigReq   dsPppConfigReq)
SIG_DEF( SIG_DS_PPP_DATA_REQ,         DsPppDataReq     dsPppDataReq)
#if defined (GPRS_TEST_PPP_PC_COMMS)
SIG_DEF( SIG_DS_PPP_FRAME_IND,        DsPppFrameInd    dsPppFrameInd)
#endif
#if defined (GPRS_PPP_DIALLER) && defined (GPRS_PPP_DIALLER_GUI)
SIG_DEF( SIG_DS_PPP_PROGRESS_REQ,     DsPppProgressReq dsPppProgressReq)
#endif
#endif

#if !defined (EXCLUDE_CIPPP)
SIG_DEF( SIG_CI_PPP_DUMMY = GPCIPPP_SIGNAL_BASE, EmptySignal ciPpp_dummy)
SIG_DEF( SIG_CI_PPP_DIAL_IND,         CiPppDialInd        ciPppDialInd)
SIG_DEF( SIG_CI_PPP_HANGUP_IND,       CiPppHangupInd      ciPppHangupInd)
SIG_DEF( SIG_CI_PPP_CONNECT_REQ,      CiPppConnectReq     ciPppConnectReq)
SIG_DEF( SIG_CI_PPP_NO_CARRIER_REQ,   CiPppNoCarrierReq   ciPppNoCarrierReq)
SIG_DEF( SIG_CI_PPP_NO_CARRIER_CNF,   Int8                ciPppNoCarrierCnf)
SIG_DEF( SIG_CI_PPP_BUSY_REQ,         Int8                ciPppBusyReq)
SIG_DEF( SIG_CI_PPP_NO_ANSWER_REQ,    Int8                ciPppNoAnswerReq)
/* 0206-18266: ERROR_REQ signal now has cause field */
SIG_DEF( SIG_CI_PPP_ERROR_REQ,        CiPppErrorReq       ciPppErrorReq)
SIG_DEF( SIG_CI_PPP_FLUSH_REQ,        EmptySignal         ciPppFlushReq)


SIG_DEF( SIG_CI_PPP_QOS_REQUIRED_REQ,        CiPppQosRequiredReq        ciPppQosRequiredReq)
SIG_DEF( SIG_CI_PPP_QOS_REQUIRED_CNF,        CiPppQosRequiredCnf        ciPppQosRequiredCnf)
SIG_DEF( SIG_CI_PPP_QOS_MINIMUM_REQ,         CiPppQosMinimumReq         ciPppQosMinimumReq)
SIG_DEF( SIG_CI_PPP_QOS_MINIMUM_CNF,         CiPppQosMinimumCnf         ciPppQosMinimumCnf)
SIG_DEF( SIG_CI_PPP_QOS_READ_REQ,            Int8                       ciPppQosReadReq)
SIG_DEF( SIG_CI_PPP_QOS_READ_CNF,            CiPppQosReadCnf            ciPppQosReadCnf)
SIG_DEF( SIG_CI_PPP_PDP_MODIFY_REQ,          CiPppPdpModifyReq          ciPppPdpModifyReq)
SIG_DEF( SIG_CI_PPP_PDP_MODIFY_CNF,          CiPppPdpModifyCnf          ciPppPdpModifyCnf)
SIG_DEF( SIG_CI_PPP_PDP_CONTEXT_IND,         CiPppPdpContextInd         ciPppPdpContextInd)
SIG_DEF( SIG_CI_PPP_PDP_ACTIVE_IND,          CiPppPdpActiveInd          ciPppPdpActiveInd)



       /* defined(UPGRADE_IP_TRANSPORT) */
SIG_DEF( SIG_CI_PPP_LOOPBACK_REQ,            CiPppLoopbackReq           ciPppLoopbackReq)
SIG_DEF( SIG_CI_PPP_LOOPBACK_CNF,            CiPppLoopbackCnf           ciPppLoopbackCnf)
#endif

#if !defined (EXCLUDE_RLCMAC)
SIG_DEF( SIG_RLCMAC_DUMMY = GPRLCMAC_SIGNAL_BASE, EmptySignal rlcmac_dummy)
SIG_DEF( SIG_RLCMAC_DATA_REQ,         RlcmacDataReq           rlcmacDataReq )
SIG_DEF( SIG_RLCMAC_DATA_IND,         RlcmacDataInd           rlcmacDataInd )
SIG_DEF( SIG_RLCMAC_UNIT_DATA_REQ,    RlcmacUnitDataReq       rlcmacUnitDataReq )
SIG_DEF( SIG_RLCMAC_UNIT_DATA_IND,    RlcmacUnitDataInd       rlcmacUnitDataInd )
SIG_DEF( SIG_RLCMAC_DATA_CFG_REQ,     RlcmacDataCfgReq        rlcmacDataCfgReq )
SIG_DEF( SIG_RLCMAC_FLUSH_REQ,        RlcmacFlushReq          rlcmacFlushReq )
#endif

#if defined (GPDEC_TEST)
/* Used during unit testing only */
SIG_DEF( SIG_GPDEC_DUMMY = GPDEC_SIGNAL_BASE, EmptySignal          gpdec_dummy)
SIG_DEF( SIG_GPDEC_DECODE_REQ,        GpdecDecodeReq               gpdecDecodeReq)
SIG_DEF( SIG_GPDEC_DECODE_IND,        EmptySignal                  gpdecDecodeInd)
#endif

#if defined (GPENC_TEST)
/* Used during unit testing only */
SIG_DEF( SIG_GPENC_DUMMY = GPENC_SIGNAL_BASE, EmptySignal               gpenc_dummy)
SIG_DEF( SIG_GPENC_PACKET_CONTROL_ACK_REQ,            GpencPacketControlAckReq            gpencPacketControlAckReq )
SIG_DEF( SIG_GPENC_PACKET_CELL_CHANGE_FAILURE_REQ,    GpencPacketCellChangeFailureReq     gpencPacketCellChangeFailureReq )
SIG_DEF( SIG_GPENC_PACKET_DOWNLINK_ACK_NACK_REQ,      GpencPacketDownlinkAckNackReq       gpencPacketDownlinkAckNackReq )
SIG_DEF( SIG_GPENC_PACKET_UPLINK_DUMMY_CTRL_BLK_REQ,  GpencPacketUplinkDummyCtrlBlkReq    gpencPacketUplinkDummyCtrlBlkReq )
SIG_DEF( SIG_GPENC_PACKET_MEASUREMENT_REPORT_REQ_EXT, GpencPacketMeasurementReportReqExt  gpencPacketMeasurementReportReqExt )
SIG_DEF( SIG_GPENC_PACKET_MOBILE_TBF_STATUS_REQ,      GpencPacketMobileTbfStatusReq       gpencPacketMobileTbfStatusReq )
SIG_DEF( SIG_GPENC_PACKET_RESOURCE_REQUEST_REQ,       GpencPacketResourceRequestReq       gpencPacketResourceRequestReq )
SIG_DEF( SIG_GPENC_PACKET_PSI_STATUS_REQ,             GpencPacketPsiStatusReq             gpencPacketPsiStatusReq )
SIG_DEF( SIG_GPENC_UT_BIT_REQ,                        GpencUtBitReq                       gpencUtBitReq )
SIG_DEF( SIG_GPENC_UT_BIT_FIELD_8_REQ,                GpencUtBitField8Req                 gpencUtBitField8Req )
SIG_DEF( SIG_GPENC_UT_BIT_FIELD_16_REQ,               GpencUtBitField16Req                gpencUtBitField16Req )
SIG_DEF( SIG_GPENC_UT_BIT_FIELD_32_REQ,               GpencUtBitField32Req                gpencUtBitField32Req )
SIG_DEF( SIG_GPENC_UT_OPT_FIELD_7_REQ,                GpencUtOptField7Req                 gpencUtOptField7Req )
SIG_DEF( SIG_GPENC_ENCODED_MESSAGE_IND,               GpencEncodedMessageInd              gpencEncodedMessageInd )
SIG_DEF( SIG_GPENC_ENCODED_MESSAGE_4_IND,             GpencEncodedMessage4Ind             gpencEncodedMessage4Ind )
SIG_DEF( SIG_GPENC_ENCODED_MESSAGE_11_IND,            GpencEncodedMessage11Ind            gpencEncodedMessage11Ind )
#endif

#if !defined (EXCLUDE_GMMRD)
SIG_DEF( SIG_RD_GMM_DUMMY = GPGMMRD_SIGNAL_BASE,      EmptySignal                          rdGmm_dummy     )
SIG_DEF( SIG_GMM_RD_TRIGGER_IND,                      GmmRdTriggerInd                      gmmRdTriggerInd )
SIG_DEF( SIG_GMM_RD_TEST_MODE_REQ,                    GmmRdTestModeReq                     gmmRdTestModeReq )
SIG_DEF( SIG_GMM_RD_RESUME_REQ,                       EmptySignal                          gmmRdResumeReq )
SIG_DEF( SIG_GMM_RD_SUSPEND_REQ,                      EmptySignal                          gmmRdSuspendReq )
#endif

#if !defined (EXCLUDE_GRRMAC)
SIG_DEF( SIG_GRR_MAC_DUMMY = GPGRRMAC_SIGNAL_BASE,    EmptySignal               grrMac_dummy            )
SIG_DEF( SIG_GRR_MAC_DL_TBF_ACK_NACK_IND,             GrrMacDlTbfAckNackInd     grrMacDlTbfAckNackInd   )
SIG_DEF( SIG_GRR_MAC_PKT_CONTROL_IND,                 GrrMacPktControlInd       grrMacPktControlInd     )
SIG_DEF( SIG_GRR_MAC_PKT_CONTROL_REQ,                 GrrMacPktControlReq       grrMacPktControlReq     )
SIG_DEF( SIG_GRR_MAC_PKT_SENT_IND,                    GrrMacPktSentInd          grrMacPktSentInd        )
SIG_DEF( SIG_GRR_MAC_UL_TBF_ACK_NACK_REQ,             GrrMacUlTbfAckNackReq     grrMacUlTbfAckNackReq   )
SIG_DEF( SIG_GRR_MAC_PKT_RELEASE_IND,                 GrrMacPktReleaseInd       grrMacPktReleaseInd     )
SIG_DEF( SIG_GRR_MAC_PKT_DECODE_ERROR_IND,            GrrMacPktDecodeErrorInd   grrMacPktDecodeErrorInd )
SIG_DEF( SIG_GRR_MAC_ENG_MODE_CTRL_REQ,               GrrMacEngModeCtrlReq      grrMacEngModeCtrlReq    )
SIG_DEF( SIG_GRR_MAC_ENG_INFO_IND,                    GrrMacEngInfoInd          grrMacEngInfoInd        )
#endif

#if !defined (EXCLUDE_RDMAC)
SIG_DEF( SIG_RD_MAC_DUMMY = GPRDMAC_SIGNAL_BASE,      EmptySignal               rdMac_dummy         )
SIG_DEF( SIG_RD_MAC_DATA_REQ,                         RdMacDataReq              rdMacDataReq        )
SIG_DEF( SIG_RD_MAC_DATA_IND,                         RdMacDataInd              rdMacDataInd        )
SIG_DEF( SIG_RD_MAC_READY_TO_SEND_IND,                RdMacReadyToSendInd       rdMacReadyToSendInd )
SIG_DEF( SIG_RD_MAC_LLC_PDU_ACK_IND,                  RdMacLlcPduAckInd         rdMacLlcPduAckInd   )
#endif

#if !defined (EXCLUDE_RDGRR)
SIG_DEF( SIG_RD_GRR_DUMMY = GPRDGRR_SIGNAL_BASE,      EmptySignal                 rdGrr_dummy                )
SIG_DEF( SIG_RD_GRR_CELL_CHANGE_IND,                  RdGrrCellChangeInd          rdGrrCellChangeInd         )
SIG_DEF( SIG_RD_GRR_DL_TBF_ASSIGN_IND,                EmptySignal                 rdGrrDlTbfAssignInd        )
SIG_DEF( SIG_RD_GRR_DL_TBF_FAILURE_IND,               RdGrrDlTbfFailureInd        rdGrrDlTbfFailureInd       )
SIG_DEF( SIG_RD_GRR_DL_TBF_RELEASE_IND,               EmptySignal                 rdGrrDlTbfReleaseInd       )
SIG_DEF( SIG_RD_GRR_UL_TBF_ASSIGN_IND,                RdGrrUlTbfAssignInd         rdGrrUlTbfAssignInd        )
SIG_DEF( SIG_RD_GRR_UL_TBF_ESTABLISH_REQ,             RdGrrUlTbfEstablishReq      rdGrrUlTbfEstablishReq     )
SIG_DEF( SIG_RD_GRR_UL_TBF_ESTABLISH_CNF,             RdGrrUlTbfEstablishCnf      rdGrrUlTbfEstablishCnf     )
SIG_DEF( SIG_RD_GRR_UL_TBF_ESTABLISH_FAIL_IND,        RdGrrUlTbfEstablishFailInd  rdGrrUlTbfEstablishFailInd )
SIG_DEF( SIG_RD_GRR_UL_TBF_RELEASE_IND,               EmptySignal                 rdGrrUlTbfReleaseInd       )
SIG_DEF( SIG_RD_GRR_UL_TBF_FAILURE_IND,               RdGrrUlTbfFailureInd        rdGrrUlTbfFailureInd       )
SIG_DEF( SIG_RD_GRR_UL_TBF_RESOURCE_REQ,              RdGrrUlTbfResourceReq       rdGrrUlTbfResourceReq      )
SIG_DEF( SIG_RD_GRR_SUSPEND_IND,                      EmptySignal                 rdGrrSuspendInd            )
SIG_DEF( SIG_RD_GRR_RESUME_IND,                       EmptySignal                 rdGrrResumeInd             )
SIG_DEF( SIG_RD_GRR_RECONNECT_IND,                    EmptySignal                 rdGrrReconnectInd          )
SIG_DEF( SIG_RD_GRR_PKT_TBF_REL_IND,                  RdGrrPktTbfRelInd           rdGrrPktTbfRelInd          )
#endif

#if !defined (EXCLUDE_MACINT)
SIG_DEF( SIG_MAC_INT_DUMMY = GPMACINT_SIGNAL_BASE,    EmptySignal                 macInt_dummy         )
SIG_DEF( SIG_MAC_INT_UL_TBF_STATUS_IND,               MacIntUlTbfStatusInd        macIntUlTbfStatusInd )
SIG_DEF( SIG_MAC_INT_DL_FLUSH_IND,                    EmptySignal                 macIntDlFlushInd     )
SIG_DEF( SIG_MAC_INT_UL_FLUSH_IND,                    EmptySignal                 macIntUlFlushInd     )
#if defined (DEVELOPMENT_VERSION)
SIG_DEF( SIG_MAC_INT_ERROR_IND,                       MacIntErrorInd              macIntErrorInd           )
SIG_DEF( SIG_MAC_INT_TX_DATA_DEBUG_IND,               MacIntTxDataDebugInd        macIntTxDataDebugInd     )
SIG_DEF( SIG_MAC_INT_RX_DATA_DEBUG_IND,               MacIntRxDataDebugInd        macIntRxDataDebugInd     )
SIG_DEF( SIG_MAC_INT_TX_STATE_DEBUG_IND,              MacIntTxStateDebugInd       macIntTxStateDebugInd    )
SIG_DEF( SIG_MAC_INT_RX_STATE_DEBUG_IND,              MacIntRxStateDebugInd       macIntRxStateDebugInd    )
SIG_DEF( SIG_MAC_INT_TX_RX_PTR_INFO_IND,              MacIntTxRxPtrInfoInd        macIntTxRxPtrInfoInd     )
SIG_DEF( SIG_MAC_INT_DATA_CONTENT_IND,                MacIntDataContentInd        macIntDataContentInd     )
SIG_DEF( SIG_MAC_INT_VS_VA_RDLB_DEBUG_IND,            MacIntTxVsVaRdLbDebugInd    macIntTxVsVaRdLbDebugInd )
SIG_DEF( SIG_MAC_INT_CTRL_MSG_BUFF_IND,               MacIntCtrlMsgBuffInd        macIntCtrlMsgBuffInd     )
SIG_DEF( SIG_MAC_INT_TX_INFO_REQ_DEBUG_IND,           MacIntTxInfoReqDebugInd     macIntTxInfoReqDebugInd  )
SIG_DEF( SIG_MAC_INT_TX_BLKS_PURG_DEBUG_IND,          MacIntTxBlksPurgDebugInd    macIntTxBlksPurgDebugInd )
#if defined (UPGRADE_EDGE)
SIG_DEF( SIG_MAC_INT_EGPRS_RX_QUAL_DEBUG_IND,         MacIntEgprsRxQualDebugInd   macIntEgprsRxQualDebugInd)
SIG_DEF( SIG_MAC_INT_EGPRS_RX_DATA_DEBUG_IND,         MacIntEgprsRxDataDebugInd   macIntEgprsRxDataDebugInd)
#endif /* UPGRADE_EDGE */
#endif /* DEVELOPMENT_VERSION */
#endif

#if defined (UPGRADE_EDGE)

#if defined (SDS_MEM_TRACK_VIA_SIGNALS)
SIG_DEF( SIG_MAC_INT_SDS_MEM_ALLOC_IND,               MacIntSdsMemAllocInd        macIntSdsMemAllocInd )
SIG_DEF( SIG_MAC_INT_SDS_MEM_FREE_IND,                MacIntSdsMemFreeInd         macIntSdsMemFreeInd  )
#endif

#if defined(SDS_MEM_STATISTICS)
SIG_DEF( SIG_MAC_INT_SDS_MEM_STATS_IND,               MacIntSdsMemStatisticsInd   macIntSdsMemStatsInd )
#endif

#endif

#if !defined (EXCLUDE_RDINT)
#if defined (DEVELOPMENT_VERSION)
SIG_DEF( SIG_RD_INT_DUMMY = GPRDINT_SIGNAL_BASE,     EmptySignal                  rdInt_dummy              )
SIG_DEF( SIG_RD_INT_STATUS_IND,                      RdIntStatusInd               rdIntStatusInd           )
SIG_DEF( SIG_RD_INT_TX_BLK_DATA_CONTENT_IND,         RdIntTxBlkDataContentInd     rdIntTxBlkDataContentInd )
SIG_DEF( SIG_RD_INT_RX_BLK_DATA_CONTENT_IND,         RdIntRxBlkDataContentInd     rdIntRxBlkDataContentInd )
SIG_DEF( SIG_RD_INT_PDU_DATA_CONTENT_IND,            RdIntPduDataContentInd       rdIntPduDataContentInd   )
SIG_DEF( SIG_RD_INT_OCTET_COUNT_DEBUG_IND,           RdIntOctetCountDebugInd      rdIntOctetCountDebugInd  )
SIG_DEF( SIG_RD_INT_SEG_CV_DEBUG_IND,                RdIntSegCvDebugInd           rdIntSegCvDebugInd       )
SIG_DEF( SIG_RD_INT_TX_PDU_QUEUE_DEBUG_IND,          RdIntTxPduQueueDebugInd      rdIntTxPduQueueDebugInd  )
#endif /* DEVELOPMENT_VERSION */
#endif


/* END OF FILE */
