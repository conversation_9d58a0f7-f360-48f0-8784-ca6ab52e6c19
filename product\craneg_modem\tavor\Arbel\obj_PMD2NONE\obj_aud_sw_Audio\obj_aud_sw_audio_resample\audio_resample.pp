//PPC Version : V2.1.9.30
//PPL Source File Name : \tavor\Arbel\obj_PMD2NONE\prepass_results\audio_resample.ppp
//PPL Source File Name : \\aud_sw\\audio_resample\\src\\audio_resample.c
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef unsigned int ACM_AudioConfirmID ;
typedef unsigned int ACM_MSAGain ;
typedef unsigned int ACM_AudioMISC ;
typedef signed char ACM_DigitalGain ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_MUTE_OFF = 0 ,	 
 ACM_MUTE_ON = 1 ,	 
	 
 ACM_AUDIO_MUTE_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_AudioMute;

typedef unsigned int size_t ;
typedef UINT32 Attribs_Mmu ;
typedef void ( * mmuNotifyEventFunc ) ( MMU_ERROR_STATUS mmuErrorHandle ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0=0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 ,	 
	 
	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
	 
	 
	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef union {
 rw_region_item compress_rw_region_list [ 8 ] ;

 UINT8 filer [ 224 ] ;
 } LOADTABLE_AREA_RW_CPZ_INFO ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 OSA_TASK_READY ,	 
 OSA_TASK_COMPLETED ,	 
 OSA_TASK_TERMINATED ,	 
 OSA_TASK_SUSPENDED ,	 
 OSA_TASK_SLEEP ,	 
 OSA_TASK_QUEUE_SUSP ,	 
 OSA_TASK_SEMAPHORE_SUSP ,	 
 OSA_TASK_EVENT_FLAG ,	 
 OSA_TASK_BLOCK_MEMORY ,	 
 OSA_TASK_MUTEX_SUSP ,	 
 OSA_TASK_STATE_UNKNOWN ,	 
 } OSA_TASK_STATE;

//ICAT EXPORTED STRUCT 
 typedef struct OSA_TASK_STRUCT 
 {	 
 char *task_name ; /* Pointer to thread ' s name */	 
 unsigned int task_priority ; /* Priority of thread ( 0 -255 ) */	 
 unsigned long task_stack_def_val ; /* default vaule of thread */	 
 OSA_TASK_STATE task_state ; /* Thread ' s execution state */	 
 unsigned long task_stack_ptr ; /* Thread ' s stack pointer */	 
 unsigned long task_stack_start ; /* Stack starting address */	 
 unsigned long task_stack_end ; /* Stack ending address */	 
 unsigned long task_stack_size ; /* Stack size */	 
 unsigned long task_run_count ; /* Thread ' s run counter */	 
	 
 } OSA_TASK;

typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPartitionPoolRef ;
typedef UINT8 OS_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef unsigned int size_t ;
typedef uint32_t cpu_stack_t ;
typedef uint64_t hr_timer_t ;
typedef uint64_t lr_timer_t ;
typedef uint32_t cpu_cpsr_t ;
typedef void ( *krhino_err_proc_t ) ( kstat_t err ) ;
typedef char name_t ;
typedef uint8_t suspend_nested_t ;
typedef uint32_t sem_count_t ;
typedef uint32_t mutex_nested_t ;
typedef uint64_t sys_time_t ;
typedef int64_t sys_time_i_t ;
typedef uint64_t tick_t ;
typedef int64_t tick_i_t ;
typedef uint64_t idle_count_t ;
typedef uint64_t ctx_switch_t ;
typedef void ( *task_entry_t ) ( void *arg ) ;
typedef void ( *timer_cb_t ) ( void *timer , void *arg ) ;
typedef void os_mmu_func_t ( uintptr_t vaddr , uintptr_t paddr , size_t len , int32_t isKenrel ) ;
typedef unsigned char BYTE ;
typedef unsigned short int WORD ;
typedef unsigned long DWORD ;
typedef WORD * WORD_PTR ;
typedef DWORD * DWORD_PTR ;
typedef BYTE BYTE_BITMASK ;
typedef VOID_PTR * VOID_PTR_PTR ;
typedef WORD FLASH_DATA_WIDTH ;
typedef char FDI_TCHAR ;
typedef OSASemaRef SEM_ID ;
typedef int SEM_STATUS ;
typedef SEM_MTX * SEM_MTX_ID ;
typedef WORD IDTYPE ;
typedef DATA_LOOKUP * DATA_LOOKUP_PTR ;
typedef DATA_LOCATION * DATA_LOC_PTR ;
typedef OPEN_PARAM * OPEN_STREAM_PTR ;
typedef COMMAND * COMMAND_PTR ;
typedef UNIT_HEADER * UNIT_HDR_PTR ;
typedef MULTI_INSTANCE * MULTI_INST_PTR ;
typedef BLOCK_INFO * BLK_INFO_PTR ;
typedef COMMAND_CONTROL * CMD_CNTRL_PTR ;
typedef unsigned short FILE_ID ;
//ICAT EXPORTED STRUCT 
 typedef struct NVM_Header 
 {	 
 unsigned long StructSize ; // the size of the user structure below	 
 unsigned long NumofStructs ; // >1 in case of array of structs ( default is 1 ) .	 
 char StructName [ 64 ] ; // the name of the user structure below	 
 char Date [ 32 ] ; // date updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char time [ 32 ] ; // time updated by the ICAT when the file is saved. Filled by calibration SW.	 
 char Version [ 64 ] ; // user version - this field is updated by the SW eng. Every time they update the UserStruct.	 
 char HW_ID [ 32 ] ; // signifies the board number. Filled by calibration SW.	 
 char CalibVersion [ 32 ] ; // signifies the calibration SW version. Filled by calibration SW.	 
 } NVM_Header_ts;

typedef char RES_BOOL ;
typedef short RES_HWORD ;
typedef int RES_WORD ;
typedef unsigned short RES_UHWORD ;
typedef unsigned int RES_UWORD ;
typedef va_list __gnuc_va_list ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
DIAG_FILTER ( AUDIO , ReSampleProcess , resampler_run , DIAG_INFORMATION)  
 diagPrintf ( " calculated processing time : %lu uSec " , delayInUsec );

//ICAT EXPORTED FUNCTION - Audio , ReSampleProcess , SwitchFileWrite 
 void ReSample_SwitchFileWrite_Env ( void *data ) 
 {	 
 Resample_WriteFile = * ( ( unsigned short* ) data ) ;	 
	 
DIAG_FILTER ( AUDIO , ReSampleProcess , SwitchFileWrite , DIAG_INFORMATION)  
 diagPrintf ( " Resample_WriteFile=%d " , Resample_WriteFile );

	 
	 
 if ( Resample_WriteFile == 1 )	 
 {		 
 Audio_ReSample_fileID = FDI_fopen ( " audio_out.nvm " , " wb " ) ;		 
 { if ( ! ( Audio_ReSample_fileID != 0 ) ) { utilsAssertFail ( " Audio_ReSample_fileID != 0 " , " audio_resample.c " , 583 , 1 ) ; } } ;		 
 }	 
 else	 
 {		 
 if ( Audio_ReSample_fileID != 0 )		 
 if ( FDI_fclose ( Audio_ReSample_fileID ) )		 
 { if ( ! ( 0 ) ) { utilsAssertFail ( " 0 " , " audio_resample.c " , 589 , 1 ) ; } } ;		 
 Audio_ReSample_fileID = 0 ;		 
 }	 
 }

//ICAT EXPORTED FUNCTION - Audio , ReSampleProcess , Test 
 void ReSample_Test_Env ( void *data ) 
 {	 
 unsigned short fs , ch , high_quality , large_filter ;	 
 size_t fdiFileBytesRead ;	 
 short In_len , out_len ;	 
 resampler resample ;	 
	 
	 
 UINT32 TSBefore , TSAfter , delayInUsec , delayInTick ;	 
	 
	 
 fs = * ( ( unsigned short* ) data ) ;	 
 ch = * ( ( unsigned short* ) data + 1 ) ;	 
 high_quality = * ( ( unsigned short* ) data + 2 ) ;	 
 large_filter = * ( ( unsigned short* ) data + 3 ) ;	 
	 
 In_len = fs * ch / 50 ;	 
 out_len = 960 * ch ;	 
	 
DIAG_FILTER ( AUDIO , ReSampleProcess , Test_0 , DIAG_INFORMATION)  
 diagPrintf ( " fs=%d , ch=%d , In_len=%d , out_len=%d , high_quality=%d , large_filter=%d " , fs , ch , In_len , out_len , high_quality , large_filter );

	 
	 
 if ( ( ch > 2 ) || ( ( fs!=8000 ) && ( fs!=16000 ) && ( fs!=22050 ) && ( fs!=24000 ) && ( fs!=32000 ) && ( fs!=44100 ) && ( fs!=48000 ) ) )	 
 {		 
DIAG_FILTER ( AUDIO , ReSampleProcess , Test_Err , DIAG_ERROR)  
 diagPrintf ( " Error sample rate or channel , test exit! " );

		 
 return ;		 
 }	 
	 
 // open source file	 
 Audio_ReSampleSRC_fileID = FDI_fopen ( " audio_src.nvm " , " rb " ) ;	 
 { if ( ! ( Audio_ReSampleSRC_fileID != 0 ) ) { utilsAssertFail ( " Audio_ReSampleSRC_fileID != 0 " , " audio_resample.c " , 627 , 1 ) ; } } ;	 
	 
 resampler_create ( high_quality , large_filter , fs , 48000 , In_len , &resample ) ;	 
	 
 while ( 1 )	 
 {		 
 fdiFileBytesRead = FDI_fread ( tmp_input , sizeof ( short ) , In_len , Audio_ReSampleSRC_fileID ) ;		 
		 
DIAG_FILTER ( AUDIO , ReSampleProcess , Test_Loop , DIAG_INFORMATION)  
 diagPrintf ( " fdiFileBytesRead=%d " , fdiFileBytesRead );

		 
		 
 // exit when reach file end		 
 if ( fdiFileBytesRead < In_len )		 
 break ;		 
		 
		 
 TSBefore = timerCountRead ( TCR_2 ) ;		 
		 
		 
 resampler_run ( &resample , tmp_input , tmp_output ) ;		 
		 
		 
 TSAfter = timerCountRead ( TCR_2 ) ;		 
		 
DIAG_FILTER ( AUDIO , ReSampleProcess , Time , DIAG_INFORMATION)  
 diagPrintf ( " TSBefore: %lu , TSAfter: %lu ( ticks ) " , TSBefore , TSAfter );

		 
		 
 /* calculate the delay base on the TS reading */		 
 delayInTick = ( TSAfter >= TSBefore ) ? TSAfter - TSBefore : ( ( 0xffffffff -TSBefore ) +TSAfter ) ;		 
 delayInUsec = ( timerClockRateGet ( TCR_2 ) == 13000000 ) ?		 
 ( delayInTick / 13 ) :		 
 ( ( delayInTick ) > ( 0xFFFFFFFF / 15625 ) ? ( ( delayInTick / 512 ) *15625+ ( ( delayInTick%512 ) *15625 ) / 512 ) : ( ( ( UINT32 ) ( delayInTick ) *15625 ) / 512 ) ) ;		 
		 
DIAG_FILTER ( AUDIO , ReSampleProcess , TimeGap , DIAG_INFORMATION)  
 diagPrintf ( " calculated processing time : %lu uSec " , delayInUsec );

		 
		 
		 
 if ( Audio_ReSample_fileID != 0 )		 
 {			 
 if ( !FDI_fwrite ( tmp_output , out_len * sizeof ( short ) , 1 , Audio_ReSample_fileID ) )			 
 { if ( ! ( 0 ) ) { utilsAssertFail ( " 0 " , " audio_resample.c " , 667 , 1 ) ; } } ;			 
 }		 
		 
 }	 
	 
 // close source file	 
 if ( FDI_fclose ( Audio_ReSampleSRC_fileID ) )	 
 { if ( ! ( 0 ) ) { utilsAssertFail ( " 0 " , " audio_resample.c " , 674 , 1 ) ; } } ;	 
 Audio_ReSampleSRC_fileID = 0 ;	 
 }

