//PPC Version : V2.1.9.30
//PPL Source File Name : \tavor\Arbel\obj_PMD2NONE\prepass_results\AudioHAL_Stream.ppp
//PPL Source File Name : \\aud_sw\\AudioHAL\\src\\AudioHAL_Stream.c
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef int ( *__heapprt ) ( void * , char const * , ... ) ;
typedef unsigned int size_t ;
typedef va_list __gnuc_va_list ;
typedef unsigned int size_t ;
typedef unsigned char BOOL ;
typedef unsigned char UINT8 ;
typedef unsigned short UINT16 ;
typedef unsigned long UINT32 ;
typedef char CHAR ;
typedef signed char INT8 ;
typedef signed short INT16 ;
typedef signed long INT32 ;
typedef unsigned char Bool ;
typedef UINT8 BYTE ;
typedef UINT8 UBYTE ;
typedef UINT16 UWORD ;
typedef UINT16 WORD ;
typedef INT16 SWORD ;
typedef UINT32 DWORD ;
typedef unsigned long long UINT64 ;
typedef void* VOID_PTR ;
typedef volatile UINT8 *V_UINT8_PTR ;
typedef volatile UINT16 *V_UINT16_PTR ;
typedef volatile UINT32 *V_UINT32_PTR ;
typedef unsigned int U32Bits ;
typedef BOOL BOOLEAN ;
typedef const char * SwVersion ;
typedef char CHAR ;
typedef unsigned char UCHAR ;
typedef int INT ;
typedef unsigned int UINT ;
typedef long LONG ;
typedef unsigned long ULONG ;
typedef short SHORT ;
typedef unsigned short USHORT ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 OSA_TASK_READY ,	 
 OSA_TASK_COMPLETED ,	 
 OSA_TASK_TERMINATED ,	 
 OSA_TASK_SUSPENDED ,	 
 OSA_TASK_SLEEP ,	 
 OSA_TASK_QUEUE_SUSP ,	 
 OSA_TASK_SEMAPHORE_SUSP ,	 
 OSA_TASK_EVENT_FLAG ,	 
 OSA_TASK_BLOCK_MEMORY ,	 
 OSA_TASK_MUTEX_SUSP ,	 
 OSA_TASK_STATE_UNKNOWN ,	 
 } OSA_TASK_STATE;

//ICAT EXPORTED STRUCT 
 typedef struct OSA_TASK_STRUCT 
 {	 
 char *task_name ; /* Pointer to thread ' s name */	 
 unsigned int task_priority ; /* Priority of thread ( 0 -255 ) */	 
 unsigned long task_stack_def_val ; /* default vaule of thread */	 
 OSA_TASK_STATE task_state ; /* Thread ' s execution state */	 
 unsigned long task_stack_ptr ; /* Thread ' s stack pointer */	 
 unsigned long task_stack_start ; /* Stack starting address */	 
 unsigned long task_stack_end ; /* Stack ending address */	 
 unsigned long task_stack_size ; /* Stack size */	 
 unsigned long task_run_count ; /* Thread ' s run counter */	 
	 
 } OSA_TASK;

typedef void *OsaRefT ;
typedef UINT8 OSA_STATUS ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSASemaRef ;
typedef void* OSAMutexRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPoolRef ;
typedef void* OSATimerRef ;
typedef void* OSAFlagRef ;
typedef void* OSAPartitionPoolRef ;
typedef void* OSTaskRef ;
typedef void* OSSemaRef ;
typedef void* OSMutexRef ;
typedef void* OSMsgQRef ;
typedef void* OSMailboxQRef ;
typedef void* OSPoolRef ;
typedef void* OSTimerRef ;
typedef void* OSFlagRef ;
typedef UINT8 OS_STATUS ;
typedef OsaTimerStatusParamsT OSATimerStatus ;
typedef void* OSATaskRef ;
typedef void* OSAHISRRef ;
typedef void* OSAMsgQRef ;
typedef void* OSAMailboxQRef ;
typedef void* OSAPartitionPoolRef ;
typedef UINT8 OS_STATUS ;
typedef UINT8 OS_STATUS ;
typedef void* OS_HISR ;
typedef unsigned long UNSIGNED ;
typedef long SIGNED ;
typedef unsigned char DATA_ELEMENT ;
typedef DATA_ELEMENT OPTION ;
typedef DATA_ELEMENT BOOLEAN ;
typedef int STATUS ;
typedef unsigned char UNSIGNED_CHAR ;
typedef unsigned int UNSIGNED_INT ;
typedef int INT ;
typedef unsigned long * UNSIGNED_PTR ;
typedef unsigned char * BYTE_PTR ;
typedef signed int ptrdiff_t ;
typedef unsigned int size_t ;
typedef unsigned short wchar_t ;
typedef long double max_align_t ;
typedef signed char int8_t ;
typedef signed short int int16_t ;
typedef signed int int32_t ;
typedef signed __int64 int64_t ;
typedef unsigned char uint8_t ;
typedef unsigned short int uint16_t ;
typedef unsigned int uint32_t ;
typedef unsigned __int64 uint64_t ;
typedef signed char int_least8_t ;
typedef signed short int int_least16_t ;
typedef signed int int_least32_t ;
typedef signed __int64 int_least64_t ;
typedef unsigned char uint_least8_t ;
typedef unsigned short int uint_least16_t ;
typedef unsigned int uint_least32_t ;
typedef unsigned __int64 uint_least64_t ;
typedef signed int int_fast8_t ;
typedef signed int int_fast16_t ;
typedef signed int int_fast32_t ;
typedef signed __int64 int_fast64_t ;
typedef unsigned int uint_fast8_t ;
typedef unsigned int uint_fast16_t ;
typedef unsigned int uint_fast32_t ;
typedef unsigned __int64 uint_fast64_t ;
typedef signed int intptr_t ;
typedef unsigned int uintptr_t ;
typedef signed long long intmax_t ;
typedef unsigned long long uintmax_t ;
typedef uint32_t cpu_stack_t ;
typedef uint64_t hr_timer_t ;
typedef uint64_t lr_timer_t ;
typedef uint32_t cpu_cpsr_t ;
typedef void ( *krhino_err_proc_t ) ( kstat_t err ) ;
typedef char name_t ;
typedef uint8_t suspend_nested_t ;
typedef uint32_t sem_count_t ;
typedef uint32_t mutex_nested_t ;
typedef uint64_t sys_time_t ;
typedef int64_t sys_time_i_t ;
typedef uint64_t tick_t ;
typedef int64_t tick_i_t ;
typedef uint64_t idle_count_t ;
typedef uint64_t ctx_switch_t ;
typedef void ( *task_entry_t ) ( void *arg ) ;
typedef void ( *timer_cb_t ) ( void *timer , void *arg ) ;
typedef void os_mmu_func_t ( uintptr_t vaddr , uintptr_t paddr , size_t len , int32_t isKenrel ) ;
typedef void ( *CommandAddress ) ( void ) ;
typedef char* CommandProto ;
typedef const char * DiagDBVersion ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PROTOCOL_TYPE_0 = 0 ,	 
 MAX_PROTOCOL_TYPES	 
 } ProtocolType;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 BOOL bEnabled ; // enable / disable the trace logging feature	 
 ProtocolType eProtocolType ; // protocol type for communication with ICAT , currently only protocol type 0 is supported	 
 UINT16 nMaxDataPerTrace ; // for each trace , what is the maximum data length to accompany the trace , in protocol type 0 , this is relevant only to DSP messages	 
 } DiagLoggerDefs;

typedef void ( *TIMER_CALLBACK_FUNCTION ) ( UINT8 ) ;
typedef void ( *ACC_TIMER_CALLBACK ) ( UINT32 ) ;
typedef int TIMER_STATUS ;
typedef int TIMER_ID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PM_RC_OK = 0 ,	 
 PM_RC_FAIL , // General Failure	 
 PM_RC_ALREADY_EXISTS // Exit function since required target alrteady exists	 
 } PM_ReturnCodeE;

typedef void ( *PM_CallbackFuncDDRstateT ) ( BOOL b_DDR_ready ) ;
typedef unsigned long long UINT64 ;
typedef unsigned long TimeIn32KhzUnit ;
typedef void ( *TickCallbackPtr ) ( UINT32 ) ;
typedef TimeIn32KhzUnit ( *SuspendCallbackPtr ) ( void ) ;
typedef void ( *PrepareTimeCallbackPtr ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_NOT_ASSIGNED = -1 ,	 
	 
 GPIO_PIN_0=0 , GPIO_PIN_1 , GPIO_PIN_2 , GPIO_PIN_3 , GPIO_PIN_4 , GPIO_PIN_5 , GPIO_PIN_6 , GPIO_PIN_7 ,	 
 GPIO_PIN_8 , GPIO_PIN_9 , GPIO_PIN_10 , GPIO_PIN_11 , GPIO_PIN_12 , GPIO_PIN_13 , GPIO_PIN_14 , GPIO_PIN_15 ,	 
 GPIO_PIN_16 , GPIO_PIN_17 , GPIO_PIN_18 , GPIO_PIN_19 , GPIO_PIN_20 , GPIO_PIN_21 , GPIO_PIN_22 , GPIO_PIN_23 ,	 
 GPIO_PIN_24 , GPIO_PIN_25 , GPIO_PIN_26 , GPIO_PIN_27 , GPIO_PIN_28 , GPIO_PIN_29 , GPIO_PIN_30 , GPIO_PIN_31 ,	 
 GPIO_PIN_32 ,	 
	 
	 
	 
 GPIO_MAX_AMOUNT_OF_PINS	 
	 
	 
	 
 } GPIO_PinNumbers;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_RC_OK = 1 ,	 
	 
 GPIO_RC_INVALID_PORT_HANDLE = -100 ,	 
 GPIO_RC_NOT_OUTPUT_PORT ,	 
 GPIO_RC_NO_TIMER ,	 
 GPIO_RC_NO_FREE_HANDLE ,	 
 GPIO_RC_AMOUNT_OUT_OF_RANGE ,	 
 GPIO_RC_INCORRECT_PORT_SIZE ,	 
 GPIO_RC_PORT_NOT_ON_ONE_REG ,	 
 GPIO_RC_INVALID_PIN_NUM ,	 
 GPIO_RC_PIN_USED_IN_PORT ,	 
 GPIO_RC_PIN_NOT_FREE ,	 
 GPIO_RC_PIN_NOT_LOCKED ,	 
 GPIO_RC_NULL_POINTER ,	 
 GPIO_RC_PULLED_AND_OUTPUT ,	 
 GPIO_RC_INCORRECT_PORT_TYPE ,	 
 GPIO_RC_INCORRECT_TRANSITION_TYPE ,	 
 GPIO_RC_INCORRECT_DEBOUNCE ,	 
 GPIO_RC_INCORRECT_DIRECTION ,	 
 GPIO_RC_INCORRECT_INIT_VALUE	 
	 
 , GPIO_RC_INTC_ERROR ,	 
 GPIO_RC_PRM_ERROR	 
	 
 } GPIO_ReturnCode;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INPUT_PIN = 1 ,	 
 GPIO_OUTPUT_PIN	 
 } GPIO_PinDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PIN_FREE_FOR_USE = 0 ,	 
 GPIO_PIN_USE_IN_PORT ,	 
 GPIO_PIN_USE_IN_INTERRUPT ,	 
 GPIO_PIN_USE_IN_PORT_WITH_INTERRUPT ,	 
 GPIO_PIN_LOCKED	 
 } GPIO_PinUsage;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinUsage pinUsage ;	 
 GPIO_PinDirection direction ;	 
 } GPIO_PinStatus;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_INITIAL_VALUE_NO_CHANGE = 0 ,	 
 GPIO_INITIAL_VALUE_LOW ,	 
 GPIO_INITIAL_VALUE_HIGH	 
 } GPIO_BitInitialValue;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 GPIO_PULL_UP_DOWN_DISABLE = 0 ,	 
 GPIO_PULL_UP_ENABLE ,	 
 GPIO_PULL_DOWN_ENABLE	 
 } GPIO_PullUpDown;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 GPIO_PinNumbers pinNumber ;	 
 GPIO_PinDirection direction ;	 
 GPIO_TransitionType transitionType ;	 
 GPIO_Debounce debounce ;	 
 GPIO_PullUpDown pullUpDown ;	 
 GPIO_BitInitialValue initialValue ;	 
 } GPIO_PinConfiguration;

typedef UINT8 GPIO_PortHandle ;
typedef void ( *GPIO_ISR ) ( void ) ;
typedef UINT32 INTC_InterruptPriorityTable [ MAX_INTERRUPT_CONTROLLER_SOURCES ] ;
typedef UINT32 INTC_InterruptInfo ;
typedef void ( *INTC_ISR ) ( INTC_InterruptInfo interruptInfo ) ;
typedef void ( *PMCNotifyEventFunc ) ( UINT64 eventRegs ) ;
typedef void ( *PMCGetStatusNotifyFunc ) ( UINT16 status ) ;
typedef void ( *PMCReadCallback ) ( UINT8 *dataBuffPtr , UINT16 dataSize , UINT16 userId ) ;
typedef void ( *PMCWriteCallback ) ( UINT16 dataBuffPtr ) ;
typedef void ( *PMCGetGPADCValueNotifyFunc ) ( PMC_adc_reg_t reg , UINT16 value ) ;
typedef void ( * ReadingCallback ) ( int ) ;
typedef void ( * LTETempReadingCallback ) ( unsigned short , unsigned short ) ;
typedef void ( * ReadingCallbackBoth ) ( BOOL , int , int ) ;
typedef union
 {
 UINT8 autoControl ;
 UINT8 autoControl2 ;
 UINT8 manControl ;
 } adcModeCntrl_t ;
typedef union
 {
 UINT64 all ;
 Registers_ts regs ;
 } PMCEvents ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SHD_POWER_DOWN ,	 
 SHD_RESET ,	 
 SHD_GHOST ,	 
 SHD_SW_ERROR /* EEHandler triggered the reset */	 
 } ShutDownType_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RR_NORMAL_POWER_ON = 0x00 , // default , not combined with others	 
 RR_WATCH_DOG_TIMEOUT = 0x01 ,	 
 RR_SOFTWARE_GENERATED = 0x02 ,	 
 RR_CHARGING_BATTERY = 0x04 ,	 
 RR_LOW_BATTERY = 0x08 ,	 
 RR_ALARM_POWER_ON = 0x10 ,	 
 RR_EXT_POWER_ON = 0x20	 
 } 
 StartupReason_te;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RE_RTC_ALARM = 0x01	 
 } StartupExtInd_te;

typedef BOOL ( *DiagPSisRunningFn ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 HEAD_PHONE_INIT = 0 ,	 
 HEAD_PHONE_OUT , // 1	 
 HEAD_TYPE_UNKNOWN , // 2	 
 HEAD_PHONE_TYPE3 , // 3	 
 HEAD_PHONE_TYPE4 , // 4	 
 HEAD_PHONE_TYPE_C_REVERSE , // 5	 
 HEAD_PHONE_TYPE_C , // 6	 
	 
 HEAD_PHONE_ERROR = 0xFFFF	 
 } HEADPHONE_STATE;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 HOOK_KEY_EVENT_NULL = 0 ,	 
 VOL_UP_PRESS , // 1	 
 VOL_DOWN_PRESS , // 2	 
 HOOK_KEY_PRESS , // 3	 
 VOL_UP_RELEASE , // 4	 
 VOL_DOWN_RELEASE , // 5	 
 HOOK_KEY_RELEASE , // 6	 
	 
 HEADPHONE_EVENT_ERROR = 0xFFFF	 
 } HEADPHONE_EVENT;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_ERR_NO = 0 , // No error	 
 AUDIOHAL_ERR_RESOURCE_RESET ,	 
 AUDIOHAL_ERR_RESOURCE_BUSY ,	 
 AUDIOHAL_ERR_RESOURCE_TIMEOUT ,	 
 AUDIOHAL_ERR_RESOURCE_NOT_ENABLED ,	 
 AUDIOHAL_ERR_BAD_PARAMETER ,	 
	 
 AUDIOHAL_ERR_UART_RX_OVERFLOW ,	 
 AUDIOHAL_ERR_UART_TX_OVERFLOW ,	 
 AUDIOHAL_ERR_UART_PARITY ,	 
 AUDIOHAL_ERR_UART_FRAMING ,	 
 AUDIOHAL_ERR_UART_BREAK_INT ,	 
	 
 AUDIOHAL_ERR_TIM_RTC_NOT_VALID ,	 
 AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_ENABLED ,	 
 AUDIOHAL_ERR_TIM_RTC_ALARM_NOT_DISABLED ,	 
	 
 AUDIOHAL_ERR_COMMUNICATION_FAILED ,	 
	 
 /* Must be at the end */	 
 AUDIOHAL_ERR_QTY ,	 
	 
	 
 AUDIOHAL_ERR_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } AUDIOHAL_ERR_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_ITF_RECEIVER = 0 ,	 
 AUDIOHAL_ITF_EARPIECE ,	 
 AUDIOHAL_ITF_HEADPHONE = AUDIOHAL_ITF_EARPIECE ,	 
 AUDIOHAL_ITF_LOUDSPEAKER ,	 
 AUDIOHAL_ITF_LOUDSPEAKER_AND_HEADPHONE ,	 
 // AUDIOHAL_ITF_LOUDSPEAKER_AND_HEADPHONE = AUDIOHAL_ITF_LOUDSPEAKER_AND_EARPIECE ,	 
 AUDIOHAL_ITF_BLUETOOTH ,	 
 AUDIOHAL_ITF_FM ,	 
 AUDIOHAL_ITF_FM2SPK ,	 
 AUDIOHAL_ITF_TV ,	 
 AUDIOHAL_ITF_BLUETOOTH_WB ,	 
 AUDIOHAL_ITF_HS_LEFT ,	 
 AUDIOHAL_ITF_HS_RIGHT ,	 
 AUDIOHAL_ITF_VAD ,	 
 AUDIOHAL_ITF_FM2RCV ,	 
	 
 AUDIOHAL_ITF_QTY ,	 
 AUDIOHAL_ITF_NONE = 255 ,	 
 } AUDIOHAL_ITF_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_SPK_RECEIVER = 0 ,	 
 AUDIOHAL_SPK_EARPIECE ,	 
 AUDIOHAL_SPK_LOUDSPEAKER ,	 
 AUDIOHAL_SPK_LOUDSPEAKER_EARPIECE , // Output on both hands-free loud speaker and earpiece	 
	 
 AUDIOHAL_SPK_QTY ,	 
 AUDIOHAL_SPK_DISABLE = 255 ,	 
 } AUDIOHAL_SPK_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_SPEAKER_STEREO = 0 ,	 
 AUDIOHAL_SPEAKER_MONO_RIGHT ,	 
 AUDIOHAL_SPEAKER_MONO_LEFT ,	 
 AUDIOHAL_SPEAKER_STEREO_NA , // Output is mono only	 
	 
 AUDIOHAL_SPEAKER_QTY ,	 
 AUDIOHAL_SPEAKER_DISABLE = 255 ,	 
 } AUDIOHAL_SPEAKER_TYPE_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_MIC_RECEIVER = 0 ,	 
 AUDIOHAL_MIC_EARPIECE ,	 
 AUDIOHAL_MIC_LOUDSPEAKER ,	 
	 
 AUDIOHAL_MIC_QTY ,	 
 AUDIOHAL_MIC_DISABLE = 255 ,	 
 } AUDIOHAL_MIC_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_SPK_MUTE = 0 ,	 
 AUDIOHAL_SPK_VOL_1 ,	 
 AUDIOHAL_SPK_VOL_2 ,	 
 AUDIOHAL_SPK_VOL_3 ,	 
 AUDIOHAL_SPK_VOL_4 ,	 
 AUDIOHAL_SPK_VOL_5 ,	 
 AUDIOHAL_SPK_VOL_6 ,	 
 AUDIOHAL_SPK_VOL_7 ,	 
 AUDIOHAL_SPK_VOL_8 ,	 
 AUDIOHAL_SPK_VOL_9 ,	 
 AUDIOHAL_SPK_VOL_10 ,	 
 AUDIOHAL_SPK_VOL_11 , // 11	 
	 
 AUDIOHAL_SPK_VOL_QTY // 12	 
 } AUDIOHAL_SPK_LEVEL_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_MIC_MUTE = 0 ,	 
 AUDIOHAL_MIC_ENABLE ,	 
	 
 AUDIOHAL_MIC_VOL_QTY ,	 
 } AUDIOHAL_MIC_LEVEL_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_SIDE_MUTE = 0 ,	 
 AUDIOHAL_SIDE_VOL_1 ,	 
 AUDIOHAL_SIDE_VOL_2 ,	 
 AUDIOHAL_SIDE_VOL_3 ,	 
 AUDIOHAL_SIDE_VOL_4 ,	 
 AUDIOHAL_SIDE_VOL_5 ,	 
 AUDIOHAL_SIDE_VOL_6 ,	 
 AUDIOHAL_SIDE_VOL_7 ,	 
 AUDIOHAL_SIDE_VOL_TEST ,	 
 AUDIOHAL_SIDE_VOL_QTY ,	 
 } AUDIOHAL_SIDE_LEVEL_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_FREQ_8000HZ = 8000 ,	 
 AUDIOHAL_FREQ_11025HZ = 11025 ,	 
 AUDIOHAL_FREQ_12000HZ = 12000 ,	 
 AUDIOHAL_FREQ_16000HZ = 16000 ,	 
 AUDIOHAL_FREQ_22050HZ = 22050 ,	 
 AUDIOHAL_FREQ_24000HZ = 24000 ,	 
 AUDIOHAL_FREQ_32000HZ = 32000 ,	 
 AUDIOHAL_FREQ_44100HZ = 44100 ,	 
 AUDIOHAL_FREQ_48000HZ = 48000 ,	 
 AUDIOHAL_FREQ_64000HZ = 64000 ,	 
 AUDIOHAL_FREQ_88200HZ = 88200 ,	 
 AUDIOHAL_FREQ_96000HZ = 96000 ,	 
 } AUDIOHAL_FREQ_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_MONO = 1 ,	 
 AUDIOHAL_STEREO = 2 ,	 
 } AUDIOHAL_CH_NB_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_DTMF_0 = 0 ,	 
 AUDIOHAL_DTMF_1 ,	 
 AUDIOHAL_DTMF_2 ,	 
 AUDIOHAL_DTMF_3 ,	 
 AUDIOHAL_DTMF_4 ,	 
 AUDIOHAL_DTMF_5 ,	 
 AUDIOHAL_DTMF_6 ,	 
 AUDIOHAL_DTMF_7 ,	 
 AUDIOHAL_DTMF_8 ,	 
 AUDIOHAL_DTMF_9 ,	 
 AUDIOHAL_DTMF_A ,	 
 AUDIOHAL_DTMF_B ,	 
 AUDIOHAL_DTMF_C ,	 
 AUDIOHAL_DTMF_D ,	 
 AUDIOHAL_DTMF_S , // * key	 
 AUDIOHAL_DTMF_P , // # key	 
 AUDIOHAL_COMFORT_425 ,	 
 AUDIOHAL_COMFORT_950 ,	 
 AUDIOHAL_COMFORT_1400 ,	 
 AUDIOHAL_COMFORT_1800 ,	 
 } AUDIOHAL_TONE_TYPE_T;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIOHAL_TONE_0DB = 0 ,	 
 AUDIOHAL_TONE_M3DB , // -3 dB	 
 AUDIOHAL_TONE_M9DB , // -9 dB	 
 AUDIOHAL_TONE_M15DB , // -15 dB	 
 AUDIOHAL_TONE_QTY	 
 } AUDIOHAL_TONE_ATTENUATION_T;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 AUDIOHAL_HEADPHONE_PLUG_OUT = 0 ,	 
 AUDIOHAL_HEADPHONE_PLUG_IN = 1	 
 } AUDIOHAL_HEADPHONE_PLUG_T;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 AUDIOHAL_HEADPHONE_TYPE_NULL = 0 ,	 
 AUDIOHAL_HEADPHONE_TYPE_UNKNOWN = 1 ,	 
	 
 AUDIOHAL_HEADPHONE_TYPE_3 = 3 , // HEAD_PHONE_TYPE3	 
 AUDIOHAL_HEADPHONE_TYPE_4 = 4 // HEAD_PHONE_TYPE4	 
 } AUDIOHAL_HEADPHONE_TYPE_T;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 AUDIOHAL_HEADPHONE_EVENT_NULL = 0 ,	 
 AUDIOHAL_HEADPHONE_EVENT_VOLUME_UP = 1 ,	 
 AUDIOHAL_HEADPHONE_EVENT_VOLUME_DOWN= 2 ,	 
 AUDIOHAL_HEADPHONE_EVENT_HOOK_KEY = 3 ,	 
	 
 AUDIOHAL_HEADPHONE_EVENT_TYPE_C_REVERSE = 7	 
	 
 } AUDIOHAL_HEADPHONE_EVENT_T;

typedef void ( *AUDIOHAL_HANDLER_T ) ( void ) ;
typedef void ( *AUDIOHAL_HeadsetReport_T ) ( UINT32 plug , UINT32 type , UINT32 event ) ;
typedef void ( *AUDIOHAL_SpeakerPA_T ) ( UINT32 on ) ;
typedef void ( *AUDIOHAL_Codec_T ) ( UINT32 on ) ;
typedef void ( *AUDIOHAL_BT_CALLBACK_T ) ( UINT32 on ) ;
typedef void ( *AUDIOHAL_KWS_CB_T ) ( unsigned int key ) ;
typedef int ( *AUDIO_EXTRA_CB ) ( short* dataPtr , UINT16 dataSize ) ;
//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 * startAddress ;	 
 UINT16 length ;	 
	 
 AUDIOHAL_FREQ_T sampleRate ;	 
 AUDIOHAL_CH_NB_T channelNb ;	 
 // BOOL voiceQuality ;	 
 // BOOL playSyncWithRecord ;	 
 INT voiceQuality ;	 
 INT playSyncWithRecord ;	 
	 
 AUDIOHAL_HANDLER_T halfHandler ;	 
 AUDIOHAL_HANDLER_T endHandler ;	 
 } AUDIOHAL_STREAM_T;

typedef unsigned int ACM_AudioConfirmID ;
typedef unsigned int ACM_MSAGain ;
typedef unsigned int ACM_AudioMISC ;
typedef signed char ACM_DigitalGain ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_MUTE_OFF = 0 ,	 
 ACM_MUTE_ON = 1 ,	 
	 
 ACM_AUDIO_MUTE_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_AudioMute;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ATC_DEFAULT = 0 ,	 
 ATC_HANDSET ,	 
 ATC_HEADSET ,	 
 ATC_HANDSFREE ,	 
 ATC_BLUETOOTH ,	 
 ATC_STEREO_BT ,	 
 ATC_SPEAKERPHONE ,	 
 ATC_HEADPHONE ,	 
 ATC_BT_NREC_OFF ,	 
 ATC_BT_WB ,	 
 ATC_BT_NREC_OFF_WB ,	 
 ATC_HANDSET_DUALMIC ,	 
 ATC_HEADSET_DUALMIC ,	 
 ATC_HANDSFREE_DUALMIC ,	 
 ATC_HANDSET_EXTRAVOL_ON ,	 
 ATC_HANDSFREE_EXTRAVOL_ON ,	 
 ATC_HANDSET_DUALMIC_EXTRAVOL_ON ,	 
 ATC_HANDSFREE_DUALMIC_EXTRAVOL_ON ,	 
	 
 ATC_TTY ,	 
 ATC_TTY_HCO ,	 
 ATC_TTY_VCO ,	 
 ATC_TTY_VCO_DUALMIC ,	 
	 
 ATC_HANDSET_LOOP ,	 
 ATC_HEADSET_LOOP ,	 
 ATC_HANDSFREE_LOOP ,	 
 ATC_HEADPHONE_LOOP ,	 
 ATC_STEREO_BT_LOOP ,	 
	 
 ATC_HANDSET_ENH_OFF ,	 
 ATC_HEADSET_ENH_OFF ,	 
 ATC_HANDSFREE_ENH_OFF ,	 
 ATC_HEADPHONE_ENH_OFF ,	 
 ATC_STEREO_BT_ENH_OFF ,	 
	 
 ATC_FM ,	 
	 
 ATC_PATH_NUM	 
 } ATCPath;

typedef ATCHandshakeExecMsg ATCHandshakeCfmMsg ;
typedef ATCPCMRecStrmIndiMsg ATCPCMPlayStrmRspMsg ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_RC_OK = 1 ,	 
 ACM_RC_DEVICE_ALREADY_ENABLED ,	 
 ACM_RC_DEVICE_ALREADY_DISABLED ,	 
 ACM_RC_NO_MUTE_CHANGE_NEEDED ,	 
 ACM_RC_INVALID_VOLUME_CHANGE ,	 
 ACM_RC_DEVICE_NOT_FOUND ,	 
 ACM_RC_BUFFER_GET_FUNC_INVALID ,	 
 ACM_RC_STREAM_OUT_NOT_PERFORMED ,	 
 ACM_RC_STREAM_IN_NOT_PERFORMED ,	 
 ACM_RC_STREAM_OUT_TO_BE_STOPPED_NOT_ACTIVE ,	 
 ACM_RC_STREAM_IN_TO_BE_STOPPED_NOT_ACTIVE ,	 
 ACM_RC_I2S_INVALID_DATA_POINTER ,	 
 ACM_RC_I2S_INVALID_DATA_SIZE ,	 
 ACM_RC_I2S_INVALID_NOTIFICATION_THRESHOLD ,	 
 ACM_RC_I2S_MESSAGE_QUEUE_IS_FULL ,	 
 ACM_RC_MEMORY_ALREADY_INITIALISED ,	 
	 
 //// Jackie add	 
 ACM_RC_NEED_DISABLE_PATH , // need APPS to disable codec path	 
 ACM_RC_MALLOC_ERROR , // memory error	 
 ACM_RC_OUTOF_RANGE ,	 
	 
 /* Must be at the end */	 
 ACM_RC_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_ReturnCode;

typedef UINT32 PM_TimeIn32KHzUnitsT ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 PM_EXT_DBG_EVENT_EMPTY = 0 ,	 
 PM_EXT_DBG_EVENT_GENERAL_PURPOSE = 99 , // for debug purposes , general event to	 
 // track something while debug ( not to be left in code permanently ) !	 
 PM_EXT_DBG_EVENT_D2_EXIT =100 , // 100	 
 PM_EXT_DBG_EVENT_C1_EXIT , // 101	 
 PM_EXT_DBG_EVENT_C1_GATED_EXIT , // 102	 
 PM_EXT_DBG_EVENT_TM_GET_NEAREST , // 103	 
 PM_EXT_DBG_EVENT_TM_SUSPEND , // 104	 
 PM_EXT_DBG_EVENT_TM_SYNCH_AFTER , // 105	 
 PM_EXT_DBG_EVENT_TM_NU_TICK , // 106	 
 PM_EXT_DBG_EVENT_TM_EXT_TICK , // 107	 
 PM_EXT_DBG_EVENT_TM_SKIP_OS_TICK , // 108	 
 PM_EXT_DBG_EVENT_TM_SUSPEND_ENABLE , // 109	 
 PM_EXT_DBG_EVENT_TM_SUSPEND_DISABLE , // 110	 
 PM_EXT_DBG_EVENT_TM_TRIGGER_ERROR , // 111	 
 PM_EXT_DBG_EVENT_TICK_FROM_SYNCH , // 112	 
 PM_EXT_DBG_EVENT_TICK_FROM_TRIGGER , // 113	 
 PM_EXT_DBG_EVENT_TM_HW_TIMER_SET , // 114	 
 PM_EXT_DBG_EVENT_OS_TIMER_EXPIRE , // 115	 
 PM_EXT_DBG_EVENT_TM_TICK_SUSPENDED , // 116	 
 PM_EXT_DBG_EVENT_ACTIVATE_NU_HISR , // 117	 
 PM_EXT_DBG_EVENT_ACTIVATE_GKI_HISR , // 118	 
 PM_EXT_DBG_EVENT_TIMER_DEACTIVATE , // 119	 
 PM_EXT_DBG_EVENT_TIMER_CONFIGURE , // 120	 
 PM_EXT_DBG_EVENT_TIMER_ACTIVATE , // 121	 
 PM_EXT_DBG_EVENT_TIMER_STATUS_CLEAR , // 122	 
 PM_EXT_DBG_EVENT_TIMER_TCMR_SET , // 123	 
 PM_EXT_DBG_EVENT_TIMER_STATUS_READ , // 124	 
 PM_EXT_DBG_EVENT_TIMER_TIER_CLEAR , // 125	 
 PM_EXT_DBG_EVENT_TIMER_TMR_SET , // 126	 
 PM_EXT_DBG_EVENT_TIMER_TCCR_SET , // 127	 
 PM_EXT_DBG_EVENT_TIMER_TIER_SET , // 128	 
 PM_EXT_DBG_EVENT_RM_PREVENT_D2 , // 129	 
 PM_EXT_DBG_EVENT_AAM_PREVENT_D2 , // 130	 
 PM_EXT_DBG_EVENT_GP_FLAG_1 , // 131	 
 PM_EXT_DBG_EVENT_AAM_D2_TIMER_WAKEUP , // 132	 
 PM_EXT_DBG_EVENT_AAM_D2_OWN_WAKEUP , // 133	 
 PM_EXT_DBG_EVENT_AAM_MANAGE_BUSY , // 134	 
 PM_EXT_DBG_EVENT_AAM_MANAGE_FREE , // 135	 
 PM_EXT_DBG_EVENT_AAM_ALLOW_D2 , // 136	 
 PM_EXT_DBG_EVENT_AAM_AA_FORBID_D2 , // 137	 
 PM_EXT_DBG_EVENT_AAM_TM_FORBID_D2 , // 138	 
 PM_EXT_DBG_EVENT_AAM_APP_TM_D2 , // 139	 
 PM_EXT_DBG_EVENT_AAM_OST_TM_D2 , // 140	 
 PM_EXT_DBG_EVENT_RM_TCU_ALLOC , // 141	 
 PM_EXT_DBG_EVENT_RM_TCU_FREE , // 142	 
 PM_EXT_DBG_EVENT_RM_SCK_ALLOC , // 143	 
 PM_EXT_DBG_EVENT_RM_SCK_FREE , // 144	 
 PM_EXT_DBG_EVENT_RM_ALLOW_D2 , // 145	 
 PM_EXT_DBG_EVENT_RM_FORBID_D2 , // 146	 
 PM_EXT_DBG_EVENT_RM_ALLOW_C1_GATED , // 147	 
 PM_EXT_DBG_EVENT_TCU_D2_PREPARE , // 148	 
 PM_EXT_DBG_EVENT_TCU_D2_RECOVER , // 149	 
 PM_EXT_DBG_EVENT_CPA_D2_PREPARE , // 150	 
 PM_EXT_DBG_EVENT_CPA_D2_RECOVER , // 151	 
 PM_EXT_DBG_EVENT_CPA_D2_WAKEUP , // 152	 
 PM_EXT_DBG_EVENT_D2_WAKEUP_TIMER , // 153	 
 PM_EXT_DBG_EVENT_GSM_WAKEUP_SWI , // 154	 
 PM_EXT_DBG_EVENT_GSM_SLEEP_SWI , // 155	 
 ////////////////////////////////////////////// DDR	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_HIGH_FREQ_ACK_WHILE_RELINQUISH_HIGH_IS_PENDING ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_REQUEST_ACK ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_SYSTEM_IN_REG_RUNNING_MODE_AND_SEND_REQ ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_REQUEST_ACK_WHILE_HIGH_IS_PENDING ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_HIGH_FREQ_ACK ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_WAIT_FOR_DDR_HIGH_FREQ_ACK_AND_SEND_REQ ,	 
 PM_EXT_DBG_EVENT_CHANGED_TO_SYSTEM_IN_REG_RUNNING_MODE ,	 
 PM_EXT_DBG_EVENT_AC_IPC_INTERRUPT_HANDLER ,	 
 PM_EXT_DBG_EVENT_260_REL_ACK ,	 
 PM_EXT_DBG_EVENT_CHANGED_SYSTEM_IN_HIGH_FREQ_MODE ,	 
 PM_EXT_DBG_EVENT_DDR_REG_REQ ,	 
 PM_EXT_DBG_EVENT_DDR_REG_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_DDR_REG_REQ_AND_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_DDR_HF_REQ ,	 
 PM_EXT_DBG_EVENT_DDR_HF_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_DDR_HF_REQ_AND_RELINQUISH ,	 
	 
 PM_EXT_DBG_EVENT_DDR_STATUS_FORBID_D2 ,	 
 ////////////////////////////////////////////// DDR	 
	 
 PM_EXT_DBG_EVENT_RM_ALLOC ,	 
 PM_EXT_DBG_EVENT_RM_FREE ,	 
	 
 PM_EXT_DBG_EVENT_D2_ENTRY ,	 
 PM_EXT_DBG_EVENT_C1_ENTRY ,	 
 PM_EXT_DBG_EVENT_C1_GATED_ENTRY ,	 
 PM_EXT_DBG_EVENT_D0CS_ENTRY ,	 
 PM_EXT_DBG_EVENT_D0CS_EXIT ,	 
 // BRN	 
 PM_EXT_DBG_EVENT_VCTCXO_RELINQUISH ,	 
 PM_EXT_DBG_EVENT_VCTCXO_REQUEST ,	 
 PM_EXT_DBG_EVENT_DDR_LPM_DONE ,	 
 PM_EXT_DBG_EVENT_POUT_DISABLE ,	 
 PM_EXT_DBG_EVENT_POUT_ENABLE ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_HIGH ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_LOW ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_USER ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_START ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_DONE ,	 
 PM_EXT_DBG_EVENT_FREQ_CHANGE_GET_FREQ ,	 
 PM_EXT_DBG_EVENT_DVFM_TABLE_UPDATE ,	 
 PM_EXT_DBG_EVENT_LPM_DECISION ,	 
 PM_EXT_DBG_EVENT_SRAM_MEMORY_ERRORS_COUNT ,	 
 PM_EXT_DBG_WAKEUP_SRC ,	 
 PM_EXT_DBG_WAKEUP_SRC_NOTREGISTER ,	 
 PM_EXT_DBG_EVENT_NO_DATA = 1500 , /* indicates that no data is send with the event	 
 ( and forces the enum to be treated as UINT32 ) */	 
 PM_EXT_DBG_DATA_FAKE_D2 =0x2000000 , //	 
 PM_EXT_DBG_DATA_REAL_D2 =0x4000000 // we add to this bit hte wakeup event register	 
 // - relevant bits are 0 -19	 
 } PM_EventTypeE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 PM_TimeIn32KHzUnitsT timeStamp ;	 
 PM_EventTypeE event ;	 
 UINT32 data ;	 
 } PM_TimeStampLogEnteryS;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 UINT32 nextEntryIndex ;	 
 PM_TimeStampLogEnteryS eventLog [ 256 ] ;	 
 BOOL logEnabled ;	 
 BOOL cyclic ;	 
 } PM_EventLogS;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 COMMPM_PP_NOTSET ,	 
 COMMPM_PP_1 ,	 
 COMMPM_PP_2 ,	 
 COMMPM_PP_3 ,	 
 COMMPM_PP_4 ,	 
 COMMPM_NUMBER_OF_PP = 4	 
 } CommPM_PPE;

typedef void ( *COMMPM_DDRAckNotificationT ) ( CommPM_DDRAckE ackType ) ;
typedef void ( *COMMPM_DDRDVFMNotificationT ) ( CommPM_PPE PPType ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 Service_UART = 0x01 ,	 
 Enable_TCU_clock= 0x02 ,	 
 Service_HSPDA = 0x04 ,	 
 Service_ICAT = 0x08 ,	 
 Service_client5 = 0x10 ,	 
 Service_client6 = 0x20 ,	 
 Service_client7 = 0x40 ,	 
 Service_client8 = 0x80	 
	 
 } CommPM_servicesD1E;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 COMMPM_MODEM_ENABLE = 0 ,	 
 COMMPM_MODEM_DISABLE ,	 
 COMMPM_INVALID_STATE	 
	 
 } PM_Modem_StateE;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 D2mode ; // 0 - no D2 , 1 - D2 alike , 2 - full D2	 
 UINT32 D2Variation ; // 0 Stay in D0 , 1 , go to C1 , 2 Full D2	 
 UINT32 LPTsleepTicks ; // how many ticks the LPT should sleep	 
 // temp solution for L1 Standalone.	 
 UINT32 LPTdebugBusyWait ; // to enable time to enter commands when	 
 // waking up ( since currently ( Sep 6 ) at C1 / D2	 
 // we can not submit commands )	 
 UINT32 DDRFunctionalityIsOn ; // 0 -regular work without DDR / 1 -DDR functionality is open for debug / 2 -DDR full functionality	 
 UINT32 endlessLoopAfterD2nExit ;	 
 UINT32 endlessLoopAfterDDRgrantnTimes ;	 
 UINT32 kickWDTonD2Exit ;	 
 UINT16 ServiceD1control ; // word that indicates service controlling D1 , if bit is set , D1 should be prevented	 
 // ( the bit position is set by the enum CommPM_servicesD1E )	 
 UINT8 AppsCommSyncActivated ;	 
 BOOL allowC1 ; // when set real C1 from HW ( if D2 set , also C1 ? )	 
 BOOL notifyMSA ; // if set - we work with L1 , and should notify them	 
 // otherwise we are in ' D2 standalone ' - only us	 
 BOOL LPTidle ; // LPT does nothing	 
 BOOL LPTdecisionOnly ; // do only decision no setting to HW , no prepare	 
 // ( means no real D2 , no C1 ) ( not relevant in LPTidle )	 
 // if not set - we have full LPT functionality	 
 BOOL L1isRegister ; // to synchronize D2 decisions on L1 registration	 
 BOOL PmDebugTaskEnalbe ; // to enable / disable comm pm debug task	 
 } CommPM_workModeS;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 DdrHfRequestTS ; // current client request TS ( maybe DDR in HF already )	 
 UINT32 DdrHfRequestFirstClientTS ; // signifies the first client request ( the one we wait for its ack )	 
 UINT32 DdrRegRequestTS ;	 
 UINT32 DdrRegMaxResponseTime ;	 
 UINT32 DdrHfClientTabResponseTime [ 30 ] ;	 
 UINT32 DdrRegClientTabResponseTime [ 30 ] ;	 
 UINT32 DdrHfMaxResponseTime ;	 
 } CommPM_DDRtimingS;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 CommPM_Active ,	 
 CommPM_Idle ,	 
 COMMPM_NUMBER_OF_CPU_STATES ,	 
 } CommPM_CPUStateE;

typedef int ( *MMIC1PrepareFunc ) ( void ) ;
typedef int ( *MMIC1RecoverFunc ) ( void ) ;
typedef int ( *MMID2PrepareFunc ) ( void ) ;
typedef int ( *MMID2RecoverFunc ) ( BOOL ExitFromD2 ) ;
typedef int ( *MMIStatusFunc ) ( void ) ;
typedef UINT32 AAM_AppsStatusT ;
typedef UINT32 AAM_HandleT ;
typedef void ( AAM_CallbackFuncT ) ( void ) ;
typedef void ( AAM_CallbackFuncPrepareT ) ( PM_PowerStatesE statetoprepare ) ;
typedef void ( AAM_CallbackFuncRecoverT ) ( PM_PowerStatesE stateexited , BOOL b_DDR_ready , BOOL b_RegsRetainedState ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /******************************************************************	 
 These ProcIDs are audio stub related	 
 *******************************************************************/	 
 // AUDIO_STUB_START_ID ,	 
 // AUDIO_STUB_CONFIRM ,	 
 // AUDIO_STUB_STOP_ID ,	 
	 
 /******************************************************************	 
 These ProcIDs are ACM_COMM related	 
 *******************************************************************/	 
 AUDIO_ACM_START_ID=100 , // ACM_COMM procID sarts from 100	 
 AUDIO_REGCPNT_ID , // 101	 
 AUDIO_REGCPNT_CONFIRM , // 102	 
	 
 AUDIO_ENABLEHWCPNT_ID , // 103	 
 AUDIO_ENABLEHWCPNT_CONFIRM , // 104	 
	 
 AUDIO_DISABLEHWCPNT_ID , // 105	 
 AUDIO_DISABLEHWCPNT_CONFIRM , // 106	 
	 
 AUDIO_SETCPNTVOL_ID , // 107	 
 AUDIO_SETCPNTVOL_CONFIRM , // 108	 
	 
 AUDIO_HWCPNTMUTE_ID , // 109	 
 AUDIO_HWCPNTMUTE_CONFIRM , // 110	 
	 
 AUDIO_STREAMINDICATE_ID , // 111 , From COMM to APPS	 
 AUDIO_STREAMRESPONSE , // 112 , From APPS to COMM	 
	 
 AUDIO_ENABLEOUTSTREAM_ID , // 113	 
 AUDIO_ENABLEOUTSTREAM_CONFIRM , // 114	 
	 
 AUDIO_DISABLEOUTSTREAM_ID , // 115	 
 AUDIO_DISABLEOUTSTREAM_CONFIRM , // 116	 
	 
 AUDIO_ENABLEINSTREAM_ID , // 117	 
 AUDIO_ENABLEINSTREAM_CONFIRM , // 118	 
	 
 AUDIO_DISABLEINSTREAM_ID , // 119	 
 AUDIO_DISABLEINSTREAM_CONFIRM , // 120	 
	 
 /* for enabling / disabling the audio at the begining / ending of a call */	 
 AUDIO_CONVERSATIONSTART_ID , // 121 , From COMM to APPS	 
 AUDIO_CONVERSATIONSTOP_ID , // 122 , From COMM to APPS	 
	 
 /*	 
 *Jackie , 2010 -0518	 
 * APPS audio send AUDIO_SETMSASETTING_ID to control MSA	 
 * COMM audio send AUDIO_GETMSASETTING_ID to APPS audio , APPS can refresh its audio calibration UI	 
 */	 
 AUDIO_SETMSASETTING_ID , // 123	 
 AUDIO_GETMSASETTING_ID , // 124	 
	 
 /*	 
 *Jackie , 2010 -0817	 
 * APPS audio send AUDIO_SYNC_CODEC_ID to sync gain information of codec chip	 
 */	 
 AUDIO_SYNC_CODEC_ID , // 125	 
	 
 /*	 
 *Jackie , 2010 -1207	 
 * MSA detect DTMF tone from far-end.	 
 */	 
 AUDIO_DTMFDETECT_ID , // 126 , From AP to CP: enable / disable	 
 AUDIO_DTMFDETECTED_ID , // 127 , From CP to AP:Forward detected DTMF code to AP audio	 
	 
 /*	 
 *Jackie , 2011 -0212	 
 * Speech logging feature	 
 */	 
 AUDIO_ENABLESPEECHLOGGING_ID , // 128 , From APPS to COMM: start speech logging	 
 AUDIO_DISABLESPEECHLOGGING_ID , // 129 , From APPS to COMM: stop speech logging	 
 AUDIO_TXSPEECHDATA_ID , // 130 , From COMM to APPS: tx speech data	 
 AUDIO_RXSPEECHDATA_ID , // 131 , From COMM to APPS: rx speech data	 
	 
 AUDIO_SETMSAGAIN_ID , // 132 , From APPS to COMM: Set MSA gain	 
 AUDIO_SETMSAGAIN_CONFIRM , // 133 , From COMM to APPS: Confirm	 
	 
 AUDIO_SETEQSETTING_ID , // 134 , From APPS to COMM: Set USER-EQ	 
 AUDIO_SETEQSETTING_CONFIRM , // 135 , From COMM to APPS: Confirm	 
	 
	 
 } ACM_EXTENT_ID;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_MSA_PCM = 0 , /* first format must be ' 0 ' - used by ' for ' loops */	 
 ACM_XSCALE_PCM ,	 
 ACM_I2S ,	 
 ACM_AUDIO_DATA , /* For DAI */	 
 ACM_AUX_FM ,	 
 ACM_AUX_HW_MIDI ,	 
 ACM_AUX_APP ,	 
	 
 /* Must be at the end */	 
 ACM_NO_FORMAT ,	 
 ACM_NUM_OF_AUDIO_FORMATS = ACM_NO_FORMAT ,	 
	 
	 
 ACM_AUDIO_FORMAT_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_AudioFormat;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 /* first device must be ' 0 ' - used by ' for ' loops */	 
 ACM_MAIN_SPEAKER = 0 // 0 , handset speaker	 
 , ACM_AUX_SPEAKER // 1 , handsfree speaker	 
 , ACM_HEADSET_SPEAKER // 2 , headset speaker	 
 , ACM_MONO_LEFT_SPEAKER // 3	 
 , ACM_MONO_RIGHT_SPEAKER // 4	 
 , ACM_BUZZER // 5	 
 , ACM_MIC // 6 , handset mic	 
 , ACM_MIC_DIFF // 7 ,	 
 , ACM_AUX_MIC // 8 , handsfree mic	 
 , ACM_AUX_MIC_DIFF // 9	 
 , ACM_BLUETOOTH_SPEAKER // 10 , bluetooth speaker	 
 , ACM_BLUETOOTH_MIC // 11 , bluetooth mic	 
 , ACM_DAI_OUT // 12	 
 , ACM_DAI_IN // 13	 
 , ACM_CAR_KIT_SPEAKER // 14	 
 , ACM_CAR_KIT_MIC // 15	 
 , ACM_INPUT_DEVICE_8 // 16	 
 , ACM_HEADSET_MIC // 17 , headset mic	 
 , ACM_MIC_EC // 18	 
 , ACM_AUX_MIC_EC // 19	 
 , ACM_HEADSET_MIC_EC // 20	 
 , ACM_MIC_LOOP_SPEAKER // 21	 
 , ACM_MIC_LOOP_EARPIECE // 22	 
 , ACM_HEADSET_MIC_LOOP // 23	 
	 
 , ACM_TTY_IN_45	 
 , ACM_INPUT_DEVICE_10 = ACM_TTY_IN_45 // 24	 
	 
 , ACM_TTY_IN_50	 
 , ACM_INPUT_DEVICE_11 = ACM_TTY_IN_50 // 25	 
	 
 , ACM_TTY_IN_HCO // 26	 
 , ACM_TTY_VCO_MIC // 27	 
 , ACM_TTY_VCO_MIC_DUALMIC // 28	 
 , ACM_INPUT_DEVICE_15 // 29	 
 , ACM_INPUT_DEVICE_16 // 30	 
 , ACM_INPUT_DEVICE_17 // 31	 
 , ACM_INPUT_DEVICE_18 // 32	 
 , ACM_INPUT_DEVICE_19 // 33	 
 , ACM_INPUT_DEVICE_20 // 34	 
 , ACM_INPUT_TEST_DEVICE = ACM_INPUT_DEVICE_20	 
 , ACM_INPUT_DEVICE_21 // 35	 
 , ACM_LINE_OUT = ACM_INPUT_DEVICE_21	 
	 
 , ACM_INPUT_DEVICE_22 // 36	 
 , ACM_INPUT_DEVICE_23 // 37	 
 , ACM_INPUT_DEVICE_24 // 38	 
 , ACM_INPUT_DEVICE_25 // 39	 
 , ACM_INPUT_DEVICE_26 // 40	 
 , ACM_INPUT_DEVICE_27 // 41	 
	 
 , ACM_TTY_OUT_45	 
 , ACM_OUTPUT_DEVICE_10 = ACM_TTY_OUT_45 // 42	 
	 
 , ACM_TTY_OUT_50	 
 , ACM_OUTPUT_DEVICE_11 = ACM_TTY_OUT_50 // 43	 
	 
 , ACM_TTY_HCO_SPEAKER // 44	 
 , ACM_TTY_OUT_VCO // 45	 
 , ACM_TTY_OUT_VCO_DUALMIC // 46	 
 , ACM_OUTPUT_DEVICE_15 // 47	 
 , ACM_OUTPUT_DEVICE_16 // 48	 
 , ACM_OUTPUT_DEVICE_17 // 49	 
 , ACM_OUTPUT_DEVICE_18 // 50	 
 , ACM_OUTPUT_DEVICE_19 // 51	 
 , ACM_OUTPUT_DEVICE_20 // 52	 
 , ACM_OUTPUT_TEST_DEVICE = ACM_OUTPUT_DEVICE_20	 
 , ACM_OUTPUT_DEVICE_21 // 53	 
 , ACM_OUTPUT_DEVICE_22 // 54	 
 , ACM_OUTPUT_DEVICE_23 // 55	 
 , ACM_OUTPUT_DEVICE_24 // 56	 
 , ACM_OUTPUT_DEVICE_25 // 57	 
 , ACM_OUTPUT_DEVICE_26 // 58	 
 , ACM_OUTPUT_DEVICE_27 // 59	 
 , ACM_OUTPUT_DEVICE_28 // 60	 
 , ACM_OUTPUT_DEVICE_29 // 61	 
	 
 , ACM_WB_BLUETOOTH_SPEAKER // 62 , WB BLUETOOTH speaker	 
 , ACM_WB_BLUETOOTH_MIC // 63 , WB BLUETOOTH mic	 
 , ACM_WB_BLUETOOTH_NREC_SPEAKER // 64 , WB BLUETOOTH NREC speaker	 
 , ACM_WB_BLUETOOTH_NREC_MIC // 65 , WB BLUETOOTH NREC mic	 
 , ACM_HEADPHONE_SPEAKER // 66 , HEADSET3POLE speaker	 
 , ACM_HEADPHONE_MIC // 67 , HEADSET3POLE mic	 
 , ACM_EXTRA_VOLUME_MAIN_SPEAKER // 68 , Handset speaker with extra volume on	 
 , ACM_EXTRA_VOLUME_MIC // 69 , Handset mic with extra volume on	 
 , ACM_EXTRA_VOLUME_AUX_SPEAKER // 70 , Aux speaker with extra volume on	 
 , ACM_EXTRA_VOLUME_AUX_MIC // 71 , Aux mic with extra volume on	 
 , ACM_BLUETOOTH6_SPEAKER // 72 , BLUETOOTH6 speaker	 
 , ACM_BLUETOOTH6_MIC // 73 , BLUETOOTH6 mic	 
 , ACM_BLUETOOTH7_SPEAKER // 74 , BLUETOOTH7 speaker	 
 , ACM_BLUETOOTH7_MIC // 75 , BLUETOOTH7 mic	 
 , ACM_BLUETOOTH8_SPEAKER // 76 , BLUETOOTH8 speaker	 
 , ACM_BLUETOOTH8_MIC // 77 , BLUETOOTH8 mic	 
 , ACM_BLUETOOTH_NREC_SPEAKER // 78 , BLUETOOTH-NREC speaker	 
 , ACM_BLUETOOTH_NREC_MIC // 79 , BLUETOOTH-NREC mic	 
	 
 // Jackie , 2011 -0222	 
 // Loop include codec and MSA	 
 , ACM_MAIN_SPEAKER__LOOP // 80 , handset speaker for loopback test	 
 , ACM_AUX_SPEAKER__LOOP // 81 , handsfree speaker for loopback test	 
 , ACM_HEADSET_SPEAKER__LOOP // 82 , headset speaker for loopback test	 
 , ACM_MIC__LOOP // 83 , handset mic for loopback test	 
 , ACM_AUX_MIC__LOOP // 84 , handsfree mic for loopback test	 
 , ACM_HEADSET_MIC__LOOP // 85 , headset mic for loopback test	 
 , ACM_HEADPHONE_SPEAKER__LOOP // 86 , bluetooth speaker for loopback test	 
 , ACM_HEADPHONE_MIC__LOOP // 87 , bluetooth mic for loopback test	 
	 
 // Jackie , 2011 -0603	 
 // Dual mic devices	 
 , ACM_MAIN_SPEAKER_DUALMIC // 88 , handset speaker for dual mic solution	 
 , ACM_AUX_SPEAKER_DUALMIC // 89 , handsfree speaker for dual mic solution	 
 , ACM_HEADSET_SPEAKER_DUALMIC // 90 , headset speaker for dual mic solution	 
 , ACM_BLUETOOTH_SPEAKER_DUALMIC // 91 , bluetooth speaker for dual mic solution	 
 , ACM_BLUETOOTH_NREC_SPEAKER_DUALMIC // 92 , bluetooth NREC speaker for dual mic solution	 
	 
 , ACM_MIC_DUALMIC // 93 , handset mic for dual mic solution	 
 , ACM_AUX_MIC_DUALMIC // 94 , handsfree mic for dual mic solution	 
 , ACM_HEADSET_MIC_DUALMIC // 95 , headset mic for dual mic solution	 
 , ACM_BLUETOOTH_MIC_DUALMIC // 96 , bluetooth mic for dual mic solution	 
 , ACM_BLUETOOTH_NREC_MIC_DUALMIC // 97 , bluetooth NREC mic for dual mic solution	 
	 
	 
 // Jackie , 2011 -0915	 
 // VT devices	 
 , ACM_MAIN_SPEAKER_VT // 98 , handset speaker for VT	 
 , ACM_AUX_SPEAKER_VT // 99 , handsfree speaker for VT	 
 , ACM_HEADSET_SPEAKER_VT // 100 , headset speaker for VT	 
 , ACM_BLUETOOTH_SPEAKER_VT // 101 , bluetooth speaker for VT	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VT // 102 , bluetooth NREC speaker for VT	 
	 
 , ACM_MIC_VT // 103 , handset mic for VT	 
 , ACM_AUX_MIC_VT // 104 , handsfree mic for VT	 
 , ACM_HEADSET_MIC_VT // 105 , headset mic for VT	 
 , ACM_BLUETOOTH_MIC_VT // 106 , bluetooth mic for VT	 
 , ACM_BLUETOOTH_NREC_MIC_VT // 107 , bluetooth NREC mic for VT	 
	 
 // VT_DUALMIC devices	 
 , ACM_MAIN_SPEAKER_VT_DUALMIC // 108 , handset speaker for VT_DUALMIC	 
 , ACM_AUX_SPEAKER_VT_DUALMIC // 109 , handsfree speaker for VT_DUALMIC	 
 , ACM_HEADSET_SPEAKER_VT_DUALMIC // 110 , headset speaker for VT_DUALMIC	 
 , ACM_BLUETOOTH_SPEAKER_VT_DUALMIC // 111 , bluetooth speaker for VT_DUALMIC	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VT_DUALMIC // 112 , bluetooth NREC speaker for VT_DUALMIC	 
	 
 , ACM_MIC_VT_DUALMIC // 113 , handset mic for VT_DUALMIC	 
 , ACM_AUX_MIC_VT_DUALMIC // 114 , handsfree mic for VT_DUALMIC	 
 , ACM_HEADSET_MIC_VT_DUALMIC // 115 , headset mic for VT_DUALMIC	 
 , ACM_BLUETOOTH_MIC_VT_DUALMIC // 116 , bluetooth mic for VT_DUALMIC	 
 , ACM_BLUETOOTH_NREC_MIC_VT_DUALMIC // 117 , bluetooth NREC mic for VT_DUALMIC	 
	 
 ////////////// Wu Bo , 2012 -0525 , support ' VOIP over modem ' ///////////////	 
 // VOIP devices	 
 , ACM_MAIN_SPEAKER_VOIP // 118 , handset speaker for VOIP	 
 , ACM_AUX_SPEAKER_VOIP // 119 , handfree speaker for VOIP	 
 , ACM_HEADSET_SPEAKER_VOIP // 120 , headset speaker for VOIP	 
 , ACM_BLUETOOTH_SPEAKER_VOIP // 121 , bluetooth speaker for VOIP	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VOIP // 122 , bluetooth NREC speaker for VOIP	 
 , ACM_BLUETOOTH_SPEAKER_VOIP_WB // 123 , bluetooth speaker for VOIP WB	 
 , ACM_BLUETOOTH_NREC_SPEAKER_VOIP_WB // 124 , bluetooth NREC speaker for VOIP WB	 
	 
 , ACM_MIC_VOIP // 125 , handset mic for VOIP	 
 , ACM_AUX_MIC_VOIP // 126 , handfree mic for VOIP	 
 , ACM_HEADSET_MIC_VOIP // 127 , headset mic for VOIP	 
 , ACM_BLUETOOTH_MIC_VOIP // 128 , bluetooth mic for VOIP , NB	 
 , ACM_BLUETOOTH_NREC_MIC_VOIP // 129 , bluetooth NREC mic for VOIP , NB	 
 , ACM_BLUETOOTH_MIC_VOIP_WB // 130 , bluetooth mic for VOIP , WB	 
 , ACM_BLUETOOTH_NREC_MIC_VOIP_WB // 131 , bluetooth NREC mic for VOIP , WB	 
	 
 // VOIP_DUALMIC devices	 
 , ACM_MAIN_SPEAKER_VOIP_DUALMIC // 132 , handset speaker for VOIP_DUALMIC	 
 , ACM_AUX_SPEAKER_VOIP_DUALMIC // 133 , handfree speaker for VOIP_DUALMIC	 
 , ACM_HEADSET_SPEAKER_VOIP_DUALMIC // 134 , headset speaker for VOIP_DUALMIC	 
	 
 , ACM_MIC_VOIP_DUALMIC // 135 , handset mic for VOIP_DUALMIC ,	 
 , ACM_AUX_MIC_VOIP_DUALMIC // 136 , handfree mic for VOIP_DUALMIC ,	 
 , ACM_HEADSET_MIC_VOIP_DUALMIC // 137 , headset mic for VOIP_DUALMIC ,	 
	 
 , ACM_MAIN_SPEAKER__LOOP2 // 138 , handset speaker for loopback test for factory test	 
 , ACM_AUX_SPEAKER__LOOP2 // 139 , handsfree speaker for loopback test for factory test	 
 , ACM_HEADSET_SPEAKER__LOOP2 // 140 , headset speaker for loopback test for factory test	 
 , ACM_MIC__LOOP2 // 141 , handset mic for loopback test for factory test	 
 , ACM_AUX_MIC__LOOP2 // 142 , handsfree mic for loopback test for factory test	 
 , ACM_HEADSET_MIC__LOOP2 // 143 , headset mic for loopback test for factory test	 
 , ACM_HEADPHONE_SPEAKER__LOOP2 // 144 , bluetooth speaker for loopback test for factory test	 
 , ACM_HEADPHONE_MIC__LOOP2 // 145 , bluetooth mic for loopback test for factory test	 
	 
 /* Must be at the end */	 
 , ACM_NUM_OF_AUDIO_DEVICES	 
	 
 // Tag for search end of audio device table	 
 , ACM_NOT_CONNECTED = 0x7fffffff	 
	 
 , ACM_AUDIO_DEVICE_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_AudioDevice;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_FORMAT_NOT_SUPPORTED = 0 ,	 
 ACM_FORMAT_SUPPORTED = 1 ,	 
	 
 ACM_FORMAT_SUPPORTED_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_FormatSupported;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_PATH_IN = 0 , // Tx	 
 ACM_PATH_OUT = 1 , // Rx	 
	 
 /* Must be at the end */	 
 ACM_NUM_OF_PATHS ,	 
	 
 ACM_PATH_DIRECTION_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_PathDirection;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_SCENARIO_AUDIO = 0 , // audio	 
 ACM_SCENARIO_VOICE = 1 , // voice	 
	 
 /* Must be at the end */	 
 ACM_SCENARIO_CNT ,	 
	 
 ACM_SCENARIO_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } ACM_SCENARIOT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short Configure ; // bit 0 :CP send confirmation to AP ; bit 1 : reuse speaker as receiver ; bit 2 : bypass PM813 PA	 
 unsigned short Always_Print_PCM ; // default:1 ( Print PCM in call ) ; If in production phase , set this to 0 to save traffic	 
 unsigned short Disable_All_Modules ; // default:0	 
	 
 unsigned char description [ 28 ] ;	 
 } ACM_Configuration;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short shift ;	 
 unsigned short SSCR0_HIGH ;	 
 unsigned short SSCR0_LOW ;	 
 unsigned short SSCR1_HIGH ;	 
 unsigned short SSCR1_LOW ;	 
 unsigned short SSTSA_LOW ;	 
 unsigned short SSRSA_LOW ;	 
 unsigned short SSPSP_HIGH ;	 
 unsigned short SSPSP_LOW ;	 
	 
 // Tavor only:	 
 unsigned short SSACD_LOW ;	 
 unsigned short SSACDD_HIGH ;	 
 unsigned short SSACDD_LOW ;	 
 } GSSP_Configuration;

typedef void ( *ACM_ResetStatusInd ) ( void ) ;
typedef ACMAudioStreamOutStartRsp ACMAudioStreamInStartRsp ;
typedef ACMAudioStreamOutStartRsp ACMAudioStreamOutStopRsp ;
typedef ACMAudioStreamOutStartRsp ACMAudioStreamInStopRsp ;
//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_SET {	 
 unsigned long command ;	 
 unsigned long op ;	 
 unsigned long param1 ;	 
 unsigned char data [ ( 140 ) ] ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_SET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_GET {	 
 unsigned long command ;	 
 unsigned long op ;	 
 unsigned long param1 ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_GET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_SET {	 
 unsigned long command ;	 
 unsigned long cmd_id ;	 
 unsigned long res_id ;	 
 unsigned long param2 ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_SET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_GET {	 
 unsigned long command ;	 
 unsigned long cmd_id ;	 
 unsigned long res_id ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_GET;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_IND {	 
 unsigned long command ;	 
 unsigned long urc_id ;	 
 unsigned long data ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_IND;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_DATA_GET_CNF {	 
 unsigned long command ;	 
 unsigned long op ;	 
 unsigned long param1 ;	 
 unsigned long value1 ;	 
 unsigned long value2 ;	 
 } ACIPC_AUDIO_VCM_ECALL_DATA_GET_CNF;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_IND {	 
 unsigned long command ;	 
 unsigned long res_id ;	 
 unsigned long res_state ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_IND;

//ICAT EXPORTED STRUCT 
 typedef struct _ACIPC_AUDIO_VCM_ECALL_VOICE_GET_CNF {	 
 unsigned long command ;	 
 unsigned long cmd_id ;	 
 unsigned long res_id ;	 
 unsigned long param2 ;	 
 } ACIPC_AUDIO_VCM_ECALL_VOICE_GET_CNF;

typedef void ( *AUDIO_ECALL_CNF_CB ) ( const ACIPC_AUDIO_VCM_ECALL_VOICE_GET_CNF* ecall_voice , const ACIPC_AUDIO_VCM_ECALL_DATA_GET_CNF* ecall_data ) ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT8 data [ 256 ] ;	 
 } ACMAudioDSPSettings;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT16 length ;	 
 UINT8 data [ 2048 ] ;	 
 } ACMAudioSpeechData;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 VC_HANDSET = 0 ,	 
 VC_HANDSFREE ,	 
 VC_HEADSET , // with mic	 
 VC_HEADPHONE , // without mic	 
 VC_BT ,	 
 VC_LOOPBACK ,	 
 VC_MAXCOUNT ,	 
	 
 AC_HANDSET = 64 ,	 
 AC_HANDSFREE ,	 
 AC_HEADSET , // with mic	 
 AC_HEADPHONE , // without mic	 
 AC_BT ,	 
 AC_KWS ,	 
	 
 AC_FM ,	 
 AC_MAXCOUNT ,	 
	 
 ACM_PROFILE_ID_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACM_PROFILE_ID;

typedef void ( *SetDownLinkStream ) ( DOWNLINKSTREAM_REQUEST *streamReq ) ;
typedef void ( *SetUpLinkStream ) ( const UINT8* data , UINT32 size ) ;
typedef UINT8 ApplicationID ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_RC_OK = 0 ,	 
 PRM_RC_FAIL , // MB _ Added General Fail	 
 PRM_RC_RESET_NOT_SUPPORTED ,	 
 PRM_RC_ERR_CLOCK = -100 ,	 
 PRM_RC_ERR_FREQ ,	 
 PRM_RC_ERR_NULL_POINTER ,	 
 PRM_RC_WAKEUP_NOT_SUPPORTED ,	 
 PRM_RC_SERVICE_NOT_SUPPORTED ,	 
 PRM_RC_ERR_CPMU // MB - Arbel Specific on reset on CPMU	 
 } PRM_ReturnCodeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_SRVC_DMA ,	 
 PRM_SRVC_DVFM ,	 
 PRM_SRVC_DSSP0_GB ,	 
 PRM_SRVC_DSSP1_GB ,	 
 PRM_SRVC_DSSP2_GB ,	 
 PRM_SRVC_I2C ,	 
 PRM_SRVC_MSL ,	 
 PRM_SRVC_RTC ,	 
 PRM_SRVC_SSP1 ,	 
 PRM_SRVC_SSP2 ,	 
 PRM_SRVC_SSP3 ,	 
 PRM_SRVC_TIMER0_13M ,	 
 PRM_SRVC_TIMER1_13M ,	 
 PRM_SRVC_TIMER2_13M_GB ,	 
 PRM_SRVC_TIMER3_13M_GB ,	 
 PRM_SRVC_VCTCXO ,	 
 PRM_SRVC_UART1 ,	 
 PRM_SRVC_USIM ,	 
 PRM_SRVC_WB_CIPHER_GB , // DTC	 
 PRM_SRVC_USIM2 ,	 
 /*should be deleted for wujing */	 
 PRM_SRVC_CPA_DDR_HPerf , // Seagull - DDR Request from Harbell ( calls PRM_SRVC_MC_DDR_HPerf if needed )	 
 PRM_SRVC_AIRQ , // Seagull	 
 PRM_SRVC_COMM_IPC , // Seagull	 
 PRM_SRVC_RESOURCE_IPC , // Seagull	 
 PRM_SRVC_AXI_CFG , // Seagull	 
 PRM_SRVC_ETB , // Seagull	 
 PRM_SRVC_DTC , // Seagull	 
 PRM_SRVC_TCU_CTRL , // Seagull	 
 PRM_SRVC_ABP_BUS , // Seagull	 
 PRM_SRVC_AXI_BUS , // Seagull	 
 PRM_LAST_SERVICE=PRM_SRVC_AXI_BUS , // Always update this field.	 
 PRM_NUM_OF_SRVCS ,	 
 PRM_SRVC_NOT_AVAILABLE ,	 
 PRM_SRVC_MC_DDR_HPerf = PRM_SRVC_NOT_AVAILABLE	 
	 
 } PRM_ServiceE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_WU_SRVC_TIMER , // Harbell , BRN ( relevant for RTOS )	 
 PRM_WU_SRVC_SSP , // Harbell	 
 PRM_WU_SRVC_SCK , // Harbell	 
 PRM_WU_SRVC_WB_SLEEP_MODULE , // Harbell	 
 PRM_WU_SRVC_TD_SLEEP_MODULE = PRM_WU_SRVC_WB_SLEEP_MODULE ,	 
 PRM_WU_SRVC_LTE_SLEEP_MODULE , // Harbell	 
 PRM_WU_SRVC_TD_LTE_SLEEP_MODULE ,	 
 PRM_WU_SRVC_TCU , // Harbell	 
 PRM_WU_SRVC_UART , // Harbell , ( BRN via GPIO ( relevant for RTOS ) )	 
 PRM_WU_SRVC_AC_IPC , // Harbell , BRN ( relevant for RTOS )	 
 PRM_WU_SRVC_RTC , // BRN	 
 PRM_WU_SRVC_ROTARY , // BRN	 
 PRM_WU_SRVC_USB20_CLIENT , // BRN - Do we need to USB events or not?	 
 PRM_WU_SRVC_USB_OTGP2 , // BRN - Tx , P2 , P3 ( 3 diferent wakeups )	 
 PRM_WU_SRVC_USB_OTGP3 , // BRN - Tx , P2 , P3 ( 3 diferent wakeups )	 
 PRM_WU_SRVC_KEYPAD , // BRN	 
 PRM_WU_SRVC_USIM , // BRN	 
 PRM_WU_SRVC_USB_OTGTX , // BRN - Tx , P2 , P3 ( 3 diferent wakeups )	 
 PRM_WU_SRVC_GPIO , // BRN ( relevant for RTOS )	 
 PRM_WU_SRVC_COMM_WDT , // BRN	 
 PRM_WU_SRVC_AC97 , // BRN ored with BSSP wakeup	 
 PRM_WU_SRVC_CI2C , // BRN	 
 PRM_WU_SRVC_MMC1 , // BRN	 
 PRM_WU_SRVC_SDIO1 , // BRN	 
 PRM_WU_SRVC_MMC2 , // BRN	 
 PRM_WU_SRVC_SDIO2 , // BRN	 
 PRM_WU_SRVC_NAND , // BRN	 
 PRM_WU_SRVC_PMIC , // BRN ( relevant for RTOS )	 
 PRM_WU_BTUART , // BRN	 
 PRM_WU_STUART , // BRN	 
 PRM_WU_SRVC_ICP , // BRN - In A0 is ored with UARTs wakeup	 
 PRM_WU_SRVC_KEYPAD_ROTARY , // BRN	 
 PRM_WU_SRVC_KEYPAD_DIRECT_KEYS , // BRN	 
 PRM_WU_SRVC_EXTERNAL_EVENT0 , // BRN - Special case - Driver not defined	 
 PRM_WU_SRVC_EXTERNAL_EVENT1 , // BRN - Special case - Driver not defined	 
 PRM_WU_SRVC_BSSP1 , // BRN	 
 PRM_WU_SRVC_BSSP2 , // BRN	 
 PRM_WU_SRVC_BSSP3 , // BRN	 
 PRM_WU_SRVC_BSSP4 , // BRN	 
	 
 PRM_NUM_OF_WU_SRVCS ,	 
 PRM_ORED_INT_MSL0 , // For BRM B0	 
 PRM_WU_INVALID_RSRC	 
 } PRM_WU_ServiceE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_NONRETAINED_SRVC_INTC = 0 , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_TIMER , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_SSP , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_DMA , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_I2C , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_WDT , // Harbell , BRN ( ? )	 
 PRM_NONRETAINED_SRVC_IPC , // Harbell	 
 PRM_NONRETAINED_SRVC_USIM , // Harbell	 
 PRM_NONRETAINED_SRVC_PMIC , // Harbell	 
 PRM_NONRETAINED_SRVC_MSL , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_SCK , // Harbell	 
 PRM_NONRETAINED_SRVC_WB_SLEEP_MODULE , // Harbell	 
 PRM_NONRETAINED_SRVC_LTE_SLEEP_MODULE , // Harbell	 
 PRM_NONRETAINED_SRVC_TD_LTE_SLEEP_MODULE , // Harbell	 
 PRM_NONRETAINED_SRVC_TCU , // Harbell	 
 PRM_NONRETAINED_SRVC_UART , // Harbell , BRN	 
 PRM_NONRETAINED_SRVC_HSI ,	 
 PRM_NONRETAINED_SRVC_GPIO , // BRN	 
 PRM_NONRETAINED_SRVC_USB20 , // BRN	 
 PRM_NONRETAINED_SRVC_UDC , // BRN	 
 PRM_NONRETAINED_SRVC_LCD , // BRN	 
 PRM_NONRETAINED_SRVC_DTC , // Seagull	 
 PRM_NONRETAINED_SRVC_PMNC , // Seagull	 
	 
 PRM_NUM_OF_NONRETAINED_SRVCS ,	 
 PRM_INVALID_NONRETAINED	 
	 
	 
 } PRM_NRS_ServiceE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_FREQ_13MHZ = 0 ,	 
 PRM_FREQ_26MHZ ,	 
 PRM_FREQ_52MHZ ,	 
 PRM_FREQ_78MHZ ,	 
 PRM_FREQ_89_1MHZ ,	 
 PRM_FREQ_104MHZ ,	 
 PRM_FREQ_124_8MHZ ,	 
 PRM_FREQ_156MHZ ,	 
 PRM_FREQ_208MHZ ,	 
 PRM_FREQ_260MHZ ,	 
 PRM_FREQ_312MHZ ,	 
 PRM_NUM_OF_FREQS ,	 
 PRM_INVALID_FREQ	 
 } PRM_ServiceFreqE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_RSRC_FREE=0 ,	 
 PRM_RSRC_ALLOC	 
	 
 } PRM_AllocFreeE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PRM_RSRC_SC_FREE=1 , // resource is free , single client handling	 
 PRM_RSRC_SC_BUSY , // resource is busy , single client handling	 
 PRM_RSRC_MC_FREE , // resource is free , multi client handling	 
 PRM_RSRC_MC_BUSY , // resource is busy , multi client handling	 
 PRM_RSRC_NOT_DEFINED // resource is not defined	 
 // in this plat / sub-system	 
 } PRM_resourceStatusE;

typedef void ( *PRM_CallbackFuncWakeupT ) ( PM_PowerStatesE sleepstate , PM_PowerStatesE WUState , BOOL b_DDR_ready , BOOL b_RegsRetainedState ) ;
typedef void ( *PRM_CallbackFuncPrepareT ) ( PM_PowerStatesE statetoprepare ) ;
typedef void ( *PRM_CallbackFuncRecoverT ) ( PM_PowerStatesE stateexited , BOOL b_DDR_ready , BOOL b_RegsRetainedState ) ;
typedef void ( *PRM_CallbackFuncBeforeIntT ) ( void ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 PMU_POR = 1 ,	 
 PMU_EMR ,	 
 PMU_WDTR = ( PMU_EMR+2 )	 
 } PMU_LastResetStatus;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 UINT32 dbID ;	 
 UINT32 filterBitLength ;	 
 UINT32 reserved [ 6 ] ;	 
 } DIAG_Nvm_Filter_File_Header_t;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 LOG_DISABLE = 0x0 ,	 
 UART_LOG_ENABLE = 0x1 ,	 
 ACAT_LOG_ENABLE = 0x2	 
 } Log_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 MSG_DISABLE = 0x0 ,	 
 ACAT_MSG_ENABLE = 0x1	 
 } Msg_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 RTI_LOG_DISABLE = 0x0 ,	 
 RTI_DUMP_ENABLE = 0x1 ,	 
 RTI_TASK_ENABLE = 0x2 ,	 
 RTI_MIPS_ENABLE = 0x3	 
 } RTI_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SD_ACAT_LOG_DISABLE = 0x0 ,	 
 SD_ACAT_LOG_ENABLE = 0x1	 
 } SD_ACAT_LOG_ConfigE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SD_DUMP_BIN_DISABLE = 0x0 ,	 
 SD_DUMP_BIN_ENABLE = 0x1	 
 } SD_Dump_bin_ConfigE;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Log_ConfigE log_cfg ;	 
 Msg_ConfigE msg_cfg ;	 
 RTI_ConfigE rti_cfg ;	 
 } Log_ConfigS;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 SD_ACAT_LOG_ConfigE log_cfg ;	 
 SD_Dump_bin_ConfigE dump_cfg ;	 
 } SD_ConfigS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 YMODEM_DISABLE = 0x0 ,	 
 YMODEM_ENABLE = 0x1	 
 } Ymodem_Dump_config;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 Ymodem_Dump_config config ;	 
 } Ymodem_Dump_type;

typedef UINT8 UART_Activity ;
//ICAT EXPORTED STRUCT 
 typedef struct /* This is structure of the UART Configuration */ 
 {	 
 UART_OpMode opMode ; /* fifo mode , non fifo mode or DMA for basic interface*/	 
 UART_TriggerLevel triggerLevel ; /* the trigger level interrupt on 1 , 8 , 16 , 32 */	 
 UART_BaudRates baudRate ; /* the rate of the transmit and the receive up to 111520 ( default - 9600 ) .*/	 
 UART_WordLen numDataBits ; /* 5 , 6 , 7 , or 8 number of data bits in the UART data frame ( default - 8 ) . */	 
 UART_StopBits stopBits ; /* 1 , 1.500000 or 2 stop bits in the UART data frame ( default - 1 ) . */	 
 UART_ParityTBits parityBitType ; /* Even , Odd or no-parity bit type in the UART data frame ( default - Non ) . */	 
 UART_InterfaceType interfaceType ; /* number of interface that the UART driver supplies ( default - UART_IF_TYPE_L2 ) */	 
 BOOL modemSignal ; /* enable operate modem - TRUE , disable modem - FALSE */	 
 BOOL flowControl ; /* enable Auto flow Control - TRUE , disable Auto flow Control - FALSE */	 
 UINT8 sleepMode ; /* enable sleep mode - TRUE , more fine control - see UARTSleepMode enum */	 
 UART_SIRConfigure sirIrDA ;	 
 } UARTConfiguration;

//ICAT EXPORTED ENUM 
 typedef enum // change the order -1 to + 
 {	 
 UART_RC_OK = 1 , /* 1 - no errors */	 
	 
 UART_RC_PORT_NUM_ERROR = -100 , /* -100 - Error in the UART port number */	 
 UART_RC_NO_DATA_TO_READ , /* -99 - Eror no data to read from the FIFO UART */	 
 UART_RC_ILLEGAL_BAUD_RATE , /* -98 - Error in the UART Bayd Rate */	 
 UART_RC_UART_PARITY_BITS_ERROR , /* -97 - Error in parity bit */	 
 UART_RC_UART_ONE_STOP_BIT_ERROR , /* -96 - Error in one stop bit */	 
 UART_RC_ONE_HALF_OR_TWO_STOP_BIT_ERROR , /* -95 - Error in two stop bit */	 
 UART_RC_BAD_INTERFACE_TYPE , /* -94 - Error in the Interface Type */	 
 UART_RC_UART_NOT_AVAILABLE , /* -93 - Error in try to open UART that is open */	 
 UART_RC_NO_DATA_TO_WRITE , /* -92 - Error No data to writ the len = 0 */	 
 UART_RC_NOT_ALL_BYTE_WRITTEN , /* -91 - Error Not all the Byte write to the UART FIFO */	 
 UART_RC_ISR_ALREADY_BIND , /* -90 - Error try to bind ISR for Basic Interface */	 
 UART_RC_WRONG_ISR_UNBIND , /* -89 - Error in the UnBind ISR for Basic Interface */	 
 UART_RC_FIFO_NOT_EMPTY , /* -88 - Error , the UART FIFO not empty */	 
 UART_RC_UART_OPEN , /* -87 - Error try chance the configurr when the UART open */	 
 UART_RC_GPIO_ERR , /* -86 - Error in the Configure of the GPIO */	 
 UART_RC_IRDA_CONFIG_ERR /* -85 - Illegal IrDA configuration */	 
 } UART_ReturnCode;

typedef void ( *UARTNotifyInterrupt ) ( UART_Port ) ;
typedef void ( *IPCCommNotifyMessageReceived ) ( UINT16 , UINT16 , UINT8 * ) ;
typedef void ( *IPCCommNotifyDataReceived ) ( IPC_DataStructReceived * , IPC_CmdMsgParams * ) ;
typedef void ( *IPCCommNotifyDataBufferFree ) ( UINT32 * , IPC_DataChannelNumber ) ;
typedef void ( *IPCCommNotifyDataChannelFree ) ( IPC_DataChannelNumber ) ;
typedef IPC_ReturnCode ( *IPCCommGetDataPointer ) ( UINT32 ** , UINT16 , IPC_DataChannelNumber , IPC_CmdMsgParams* ) ;
typedef void ( *IPCCommNotifySelfEventReceived ) ( UINT32 MessageParam ) ;
typedef void ( *IPCCommSpyCommandNotification ) ( UINT16 , UINT16 , UINT8* , SpyCmdData* ) ;
typedef void ( *IPCErrorIndicationCallBack ) ( IPC_ERROR_INDICATION * ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 AUDIO_GSM_RAT ,	 
 AUDIO_WCDMA_RAT ,	 
 AUDIO_LTE_RAT ,	 
 AUDIO_NULL_RAT ,	 
 AUDIO_ALL_RAT ,	 
 AUDIO_UNKNOWN_RAT ,	 
 } audioRat_te;

typedef audioRat_te ( *audioGetRAT_t ) ( void ) ;
typedef unsigned char ( *audioDoDataSwapBytes_t ) ( void ) ;
typedef void ( *audioGsmStartVoicePath_t ) ( void ) ;
typedef void ( *audioGsmStopVoicePath_t ) ( void ) ;
typedef void ( *audioGsmGetVocoderTypeRate_t ) ( unsigned short *vocoderType , unsigned short *vocoderRate ) ;
typedef BOOL ( *audioGsmGetDtxSupport_t ) ( void ) ;
typedef void* ( *audioGsmGetTxBuffer_t ) ( void ) ;
typedef void ( *audioGsmSetAmrToc_t ) ( unsigned short ) ;
typedef void ( *audioGsmSetAmrSidTypeInd_t ) ( unsigned short ) ;
typedef void ( *audioGsmSetEncoderFlags_t ) ( unsigned short ) ;
typedef void ( *audioGsmSetDecoderSidInd_t ) ( unsigned short ) ;
typedef void ( *POCTxPCMHandler_t ) ( short *pData , unsigned short Length ) ;
typedef void ( *POCRxPCMHandler_t ) ( short *pData , unsigned short Length ) ;
typedef int ( *POCTxAMRHandler_t ) ( void const * frame , unsigned short Length ) ;
typedef int ( *POCRxAMRHandler_t ) ( void* frame , unsigned short Length ) ;
typedef void ( *DTMFDetectionHandler_t ) ( unsigned short DTMFCode ) ;
typedef void ( *OEMAudio_GetVoiceDataCallback_t ) ( short *rx , short *tx , short *mixed , short length , unsigned int count ) ;
//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 short a [ 3 ] ; // a [ 0 ] , a [ 1 ] , a [ 2 ]	 
 short b [ 3 ] ; // b [ 0 ] , b [ 1 ] , b [ 2 ]	 
 } BiQuadCoeffsStruct;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short BiquadEnControl ; // bit 0 ~bit 4 is on / off of each filter.	 
 // unsigned short reserved0 ;	 
 // short reserve1 ;	 
 BiQuadCoeffsStruct Tuningmem [ 5 ] ; // get from user input or read workspace log	 
 } EqParamIn;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACMCODEC_CONTROL_BITS_MIC = 0 ,	 
 ACMCODEC_CONTROL_BITS_SIDETONE = 1 ,	 
	 
 ACMCODEC_CONTROL_BITS_CNT = 16 // 16 control bits	 
 } ACMCODEC_CONTROL_BITS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACMDSP_CONTROL_BITS_TX_EC = 0 ,	 
 ACMDSP_CONTROL_BITS_TX_DUALMIC = 1 ,	 
 ACMDSP_CONTROL_BITS_TX_NS = 2 ,	 
 ACMDSP_CONTROL_BITS_TX_EQ = 3 ,	 
 ACMDSP_CONTROL_BITS_TX_AVC = 4 ,	 
 ACMDSP_CONTROL_BITS_TX_VOLUME = 5 ,	 
 ACMDSP_CONTROL_BITS_RESERVED6 = 6 ,	 
 ACMDSP_CONTROL_BITS_RESERVED7 = 7 ,	 
	 
 ACMDSP_CONTROL_BITS_RX_NS = 8 ,	 
 ACMDSP_CONTROL_BITS_RX_EQ = 9 ,	 
 ACMDSP_CONTROL_BITS_RX_BOOST = 10 ,	 
 ACMDSP_CONTROL_BITS_RX_BIQUADIIR= 11 ,	 
 ACMDSP_CONTROL_BITS_RX_VOLUME = 12 ,	 
 ACMDSP_CONTROL_BITS_RX_SIDETONE = 13 ,	 
 ACMDSP_CONTROL_BITS_RX_SLOWVOICE= 14 ,	 
 ACMDSP_CONTROL_BITS_MP3_MODE = 15 ,	 
	 
 ACMDSP_CONTROL_BITS_CNT = 16 // 16 control bits	 
 } ACMDSP_CONTROL_BITS;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACMCODEC_PGA_STAGE1_M6dB = 0 ,	 
 ACMCODEC_PGA_STAGE1_M3dB ,	 
 ACMCODEC_PGA_STAGE1_0dB ,	 
 ACMCODEC_PGA_STAGE1_3dB ,	 
 ACMCODEC_PGA_STAGE1_6dB ,	 
 ACMCODEC_PGA_STAGE1_9dB ,	 
 ACMCODEC_PGA_STAGE1_12dB ,	 
 ACMCODEC_PGA_STAGE1_15dB ,	 
 ACMCODEC_PGA_STAGE1_18dB ,	 
 ACMCODEC_PGA_STAGE1_21dB ,	 
	 
 ACMCODEC_PGA_STAGE1_CNT ,	 
	 
 ACMCODEC_PGA_STAGE1_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_PGA_STAGE1;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACMCODEC_PGA_STAGE2_M6dB = 0 ,	 
 ACMCODEC_PGA_STAGE2_M3dB ,	 
 ACMCODEC_PGA_STAGE2_0dB ,	 
 ACMCODEC_PGA_STAGE2_3dB ,	 
 ACMCODEC_PGA_STAGE2_6dB ,	 
	 
 ACMCODEC_PGA_STAGE2_CNT ,	 
	 
 ACMCODEC_PGA_STAGE2_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_PGA_STAGE2;

//ICAT EXPORTED ENUM 
 typedef enum 
 { // 0 ~31 , RSNUM = 4	 
 ACMCODEC_ADC_DIGGAIN_BYPASS = 0 , // 0	 
 ACMCODEC_ADC_DIGGAIN_M24dB = 1 ,	 
 ACMCODEC_ADC_DIGGAIN_M18dB = 2 ,	 
 ACMCODEC_ADC_DIGGAIN_M14dB = 3 ,	 
 ACMCODEC_ADC_DIGGAIN_M12dB = 4 ,	 
 ACMCODEC_ADC_DIGGAIN_M10dB = 5 ,	 
 ACMCODEC_ADC_DIGGAIN_M8dB = 6 ,	 
 ACMCODEC_ADC_DIGGAIN_M7dB = 7 ,	 
 ACMCODEC_ADC_DIGGAIN_M6dB = 8 ,	 
 ACMCODEC_ADC_DIGGAIN_M5dB = 9 ,	 
 ACMCODEC_ADC_DIGGAIN_M4dB = 10 ,	 
 ACMCODEC_ADC_DIGGAIN_M3dB = 11 ,	 
 ACMCODEC_ADC_DIGGAIN_M2dB = 12 ,	 
 ACMCODEC_ADC_DIGGAIN_M1dB = 14 ,	 
 ACMCODEC_ADC_DIGGAIN_0dB = 16 ,	 
 ACMCODEC_ADC_DIGGAIN_1dB = 18 ,	 
 ACMCODEC_ADC_DIGGAIN_2dB = 20 ,	 
 ACMCODEC_ADC_DIGGAIN_3dB = 23 ,	 
 ACMCODEC_ADC_DIGGAIN_4dB = 25 ,	 
 ACMCODEC_ADC_DIGGAIN_5dB = 28 ,	 
 ACMCODEC_ADC_DIGGAIN_6dB = 31 ,	 
	 
 ACMCODEC_ADC_DIGGAIN_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_ADC_DIGGAIN;

//ICAT EXPORTED ENUM 
 typedef enum 
 { // 0 ~255 , RSNUM = 4	 
 ACMCODEC_DAC_DIGGAIN_BYPASS = 0 , // 0	 
 ACMCODEC_DAC_DIGGAIN_M24dB = 1 ,	 
 ACMCODEC_DAC_DIGGAIN_M18dB = 2 ,	 
 ACMCODEC_DAC_DIGGAIN_M14dB = 3 ,	 
 ACMCODEC_DAC_DIGGAIN_M12dB = 4 ,	 
 ACMCODEC_DAC_DIGGAIN_M10dB = 5 ,	 
 ACMCODEC_DAC_DIGGAIN_M8dB = 6 ,	 
 ACMCODEC_DAC_DIGGAIN_M7dB = 7 ,	 
 ACMCODEC_DAC_DIGGAIN_M6dB = 8 ,	 
 ACMCODEC_DAC_DIGGAIN_M5dB = 9 ,	 
 ACMCODEC_DAC_DIGGAIN_M4dB = 10 ,	 
 ACMCODEC_DAC_DIGGAIN_M3dB = 11 ,	 
 ACMCODEC_DAC_DIGGAIN_M2dB = 12 ,	 
 ACMCODEC_DAC_DIGGAIN_M1dB = 14 ,	 
 ACMCODEC_DAC_DIGGAIN_0dB = 16 ,	 
 ACMCODEC_DAC_DIGGAIN_1dB = 18 ,	 
 ACMCODEC_DAC_DIGGAIN_2dB = 20 ,	 
 ACMCODEC_DAC_DIGGAIN_3dB = 23 ,	 
 ACMCODEC_DAC_DIGGAIN_4dB = 25 ,	 
 ACMCODEC_DAC_DIGGAIN_5dB = 28 ,	 
 ACMCODEC_DAC_DIGGAIN_6dB = 32 ,	 
 ACMCODEC_DAC_DIGGAIN_7dB = 36 ,	 
 ACMCODEC_DAC_DIGGAIN_8dB = 40 ,	 
 ACMCODEC_DAC_DIGGAIN_9dB = 45 ,	 
 ACMCODEC_DAC_DIGGAIN_10dB = 51 ,	 
 ACMCODEC_DAC_DIGGAIN_11dB = 57 ,	 
 ACMCODEC_DAC_DIGGAIN_12dB = 64 ,	 
 ACMCODEC_DAC_DIGGAIN_13dB = 71 ,	 
 ACMCODEC_DAC_DIGGAIN_14dB = 80 ,	 
 ACMCODEC_DAC_DIGGAIN_15dB = 90 ,	 
 ACMCODEC_DAC_DIGGAIN_16dB = 101 ,	 
 ACMCODEC_DAC_DIGGAIN_17dB = 113 ,	 
 ACMCODEC_DAC_DIGGAIN_18dB = 127 ,	 
 ACMCODEC_DAC_DIGGAIN_19dB = 143 ,	 
 ACMCODEC_DAC_DIGGAIN_20dB = 160 ,	 
 ACMCODEC_DAC_DIGGAIN_21dB = 180 ,	 
 ACMCODEC_DAC_DIGGAIN_22dB = 201 ,	 
 ACMCODEC_DAC_DIGGAIN_23dB = 226 ,	 
 ACMCODEC_DAC_DIGGAIN_24dB = 254 ,	 
	 
 ACMCODEC_DAC_DIGGAIN_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_DAC_DIGGAIN;

//ICAT EXPORTED ENUM 
 typedef enum 
 { // 0 ~255 , RSNUM = 8	 
 ACMCODEC_SIDETONE_GAIN_BYPASS = 0 , // 0	 
 ACMCODEC_SIDETONE_GAIN_M48dB = 1 ,	 
 ACMCODEC_SIDETONE_GAIN_M42dB = 2 ,	 
 ACMCODEC_SIDETONE_GAIN_M38dB = 3 ,	 
 ACMCODEC_SIDETONE_GAIN_M36dB = 4 ,	 
 ACMCODEC_SIDETONE_GAIN_M34dB = 5 ,	 
 ACMCODEC_SIDETONE_GAIN_M32dB = 6 ,	 
 ACMCODEC_SIDETONE_GAIN_M30dB = 8 ,	 
 ACMCODEC_SIDETONE_GAIN_M28dB = 10 ,	 
 ACMCODEC_SIDETONE_GAIN_M26dB = 13 ,	 
 ACMCODEC_SIDETONE_GAIN_M24dB = 16 ,	 
 ACMCODEC_SIDETONE_GAIN_M22dB = 20 ,	 
 ACMCODEC_SIDETONE_GAIN_M20dB = 26 ,	 
 ACMCODEC_SIDETONE_GAIN_M18dB = 32 ,	 
 ACMCODEC_SIDETONE_GAIN_M16dB = 41 ,	 
 ACMCODEC_SIDETONE_GAIN_M14dB = 51 ,	 
 ACMCODEC_SIDETONE_GAIN_M12dB = 64 ,	 
 ACMCODEC_SIDETONE_GAIN_M10dB = 81 ,	 
 ACMCODEC_SIDETONE_GAIN_M9dB = 91 ,	 
 ACMCODEC_SIDETONE_GAIN_M8dB = 102 ,	 
 ACMCODEC_SIDETONE_GAIN_M7dB = 114 ,	 
 ACMCODEC_SIDETONE_GAIN_M6dB = 128 ,	 
 ACMCODEC_SIDETONE_GAIN_M5dB = 144 ,	 
 ACMCODEC_SIDETONE_GAIN_M4dB = 161 ,	 
 ACMCODEC_SIDETONE_GAIN_M3dB = 181 ,	 
 ACMCODEC_SIDETONE_GAIN_M2dB = 203 ,	 
 ACMCODEC_SIDETONE_GAIN_M1dB = 228 ,	 
 ACMCODEC_SIDETONE_GAIN_0dB = 255 ,	 
	 
 ACMCODEC_SIDETONE_GAIN_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_SIDETONE_GAIN;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACMCODEC_DAC_GAIN_6dB = 0 ,	 
 ACMCODEC_DAC_GAIN_3dB ,	 
 ACMCODEC_DAC_GAIN_0dB ,	 
 ACMCODEC_DAC_GAIN_M3dB ,	 
 ACMCODEC_DAC_GAIN_M6dB ,	 
	 
 ACMCODEC_DAC_GAIN_CNT ,	 
	 
 ACMCODEC_DAC_GAIN_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_DAC_GAIN;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 // ACMCODEC_RCV_GAIN_NOTUSED = 0 ,	 
 // ACMCODEC_RCV_GAIN_M6dB = 1 ,	 
 ACMCODEC_RCV_GAIN_M6dB = 0 ,	 
 ACMCODEC_RCV_GAIN_M3dB ,	 
 ACMCODEC_RCV_GAIN_0dB ,	 
 ACMCODEC_RCV_GAIN_3dB ,	 
 ACMCODEC_RCV_GAIN_6dB ,	 
	 
 ACMCODEC_RCV_GAIN_CNT ,	 
	 
 ACMCODEC_RCV_GAIN_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_RCV_GAIN;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACMCODEC_CLASSG_MODE_0_9V = 0 ,	 
 ACMCODEC_CLASSG_MODE_1_8V ,	 
	 
 ACMCODEC_CLASSG_MODE_CNT ,	 
	 
 ACMCODEC_CLASSG_MODE_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_CLASSG_MODE;

//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACMCODEC_CLASSD_GAIN_1_5VV = 0 ,	 
 ACMCODEC_CLASSD_GAIN_2_0VV ,	 
 ACMCODEC_CLASSD_GAIN_2_3VV ,	 
	 
 ACMCODEC_CLASSD_GAIN_CNT ,	 
	 
 ACMCODEC_CLASSD_GAIN_ENUM_16_BIT = 0x7FFF // 16 bit enum compiling enforcement	 
 } ACMCODEC_CLASSD_GAIN;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 ACMCODEC_PGA_STAGE1 PGA_Stage1 ; // 0 :0dB ; 1 :6dB ; 10 :12dB ; 11 :18dB	 
 ACMCODEC_PGA_STAGE2 PGA_Stage2 ; // 0 :-6dB ; 1 :-2.5dB ; 10 :0dB ; 11 :3.5dB ; 100 :6dB	 
 ACMCODEC_ADC_DIGGAIN ADC_DigGain ; // AdcDigGain [ 4 :0 ] :1~31	 
 ACMCODEC_SIDETONE_GAIN SideTone_Gain ; // SideToneGain [ 8 :0 ] : [ -256 , 255 ]	 
 signed short Reserved1 ; // Reserved1	 
 signed short Reserved2 ; // Reserved2	 
 } ACMCodec_GainInT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 ACMCODEC_DAC_DIGGAIN DAC_DigGain ; // DacDigGain [ 7 :0 ] :1~255	 
 ACMCODEC_DAC_GAIN DAC_Gain ; // 0 :6dB ; 1 :3.5dB ; 10 :0dB ; 11 :-2.5dB ; 100 :-6dB	 
 ACMCODEC_RCV_GAIN RCV_Gain ; // 1 :-6dB ; 10 :-2.5dB ; 11 :0dB ; 100 :3.5dB ; 101 :6dB	 
 ACMCODEC_CLASSG_MODE ClassG_Mode ; // 0 =+ / -0.9V ; 1 =+ / -1.8V	 
 ACMCODEC_CLASSD_GAIN ClassD_Gain ; // 0 =1.5V / V ; 1 =2V / V ; 2 =2.33V / V ; 3 =0	 
 signed short Reserved1 ; // Reserved1	 
 } ACMCodec_GainOutT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 ACM_PROFILE_ID Profile ;	 
 unsigned short CodecControl ;	 
 ACMCodec_GainInT Tx_CodecGain ; // tx has same gain for every volume	 
 signed short Tx_DSPGain ; // dB , tx has same gain for every volume	 
 ACMCodec_GainOutT Rx_CodecGain [ AUDIOHAL_SPK_VOL_QTY ] ;	 
 signed short Rx_DSPGain [ AUDIOHAL_SPK_VOL_QTY ] ; // dB	 
 signed short Rx_DSPSideToneGain ; // dB	 
 } ACMCodec_GainT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 signed short SampleDelay ; // reference delay to echo	 
 signed short NumOfTaps ; // Fir filter length	 
 signed short ConvergenceSpeed ; // slowdown	 
 signed short Ref2EchoPowerRatio ; // ratio of ref to echo , [ -12 ~30 ] db , decimal number	 
 signed short DC_factor ; // [ 14000 ~16350 ] but default should be 16200	 
 signed short reserved0 ;	 
 } ACMDSP_ECParamsT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 signed short BoostMode ; // 0 , 1 , 2 , 3	 
 signed short BoostGainDb ;	 
 signed short PeakGainDb ;	 
 signed short BoostNoiseGainDb ;	 
 signed short NoiseLevel ;	 
 signed short BoostAttach ;	 
 signed short BoostRelease ;	 
 signed short reserved ;	 
 signed short TargetLevel ;	 
 signed short AgcMaxGainDb ;	 
 signed short AgcMaxRMS ;	 
 signed short AgcEnvStep ; // expert parameters	 
 signed short RMStrackStep ; // expert parameters	 
 } ACMDSP_AGCParamsT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 signed short a [ 3 ] ;	 
 signed short b [ 3 ] ;	 
 } ACMDSP_BiQuadCoeffsT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short PeriodDelta ;	 
 unsigned short ExpandRate ;	 
 unsigned short CorrectionThreshold ;	 
 signed short reserved ;	 
 } ACMDSP_SlowVoiceParamsT;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 signed short MaxSuppressDb ; // [ -40 , 0 ) db.	 
 signed short PeakNoiseDb ; // [ -70 , -12 ) db.	 
 signed short NoiseMatrix ; // following is expert parameters	 
 signed short FrameSmoothFactor ;	 
 signed short reserved ;	 
 } ACMDSP_NSParamsT;

//ICAT EXPORTED STRUCT 
 typedef struct { // res parameters	 
 signed short ResMode ;	 
 signed short DtGainDb ;	 
 signed short FestGainDb ;	 
 signed short NoiseFloorDbov ;	 
 signed short CnGaindb ;	 
 signed short DtThresh ;	 
 signed short Gamma ;	 
 // following is expert paramters	 
 signed short AuditoryMasking ;	 
 signed short NonlinearModel ;	 
 signed short WinitCntThresh ;	 
 signed short EstResMatrix ;	 
 signed short VadhangRef ;	 
 signed short VadhangNear ;	 
 signed short reserved ;	 
 } ACMDSP_NSResParamsT;

//ICAT EXPORTED STRUCT 
 typedef struct { // reserved parameters for dual_mic	 
 signed short TapsNum ;	 
 signed short Oversuppression ;	 
 signed short OverAuditoryMasking ;	 
 signed short DualMicVmThresh ;	 
 signed short DualMicVmOffset ;	 
 signed short DualMicNoiseThresh ;	 
 signed short DualMicVadHang ;	 
 signed short DualMicVmRelease ;	 
 signed short NonStationaryLeftDb ;	 
 } ACMDSP_NSDmParamsT;

//ICAT EXPORTED STRUCT 
 typedef struct {	 
 ACMDSP_NSParamsT NsParams ;	 
 ACMDSP_NSResParamsT ResParams ;	 
 ACMDSP_NSDmParamsT DmParams ;	 
 } ACMDSP_NSTxParamsT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 ACM_PROFILE_ID Profile ;	 
 signed short Version ;	 
 unsigned short VoiceControl ;	 
	 
 // TX path	 
 signed short ExpertParam1 [ 2 ] ;	 
 ACMDSP_ECParamsT EcParams [ 2 ] ;	 
 ACMDSP_NSTxParamsT NsParamsTx [ 2 ] ;	 
 signed short TxFreq_eqCoeffArray [ 2 ] [ 24 ] ;	 
 ACMDSP_AGCParamsT TxAgcParams [ 2 ] ;	 
	 
 // RX path	 
 signed short ExpertParam2 [ 2 ] ;	 
 ACMDSP_NSParamsT NsParamsRx [ 2 ] ;	 
 signed short RxFreq_eqCoeffArray [ 2 ] [ 24 ] ;	 
 ACMDSP_AGCParamsT RxAgcParams [ 2 ] ;	 
 ACMDSP_BiQuadCoeffsT BiQuadIIR [ 2 ] ; // can be used as hpf / lpf or notch filter	 
 ACMDSP_SlowVoiceParamsT SlowVoiceParam ;	 
	 
 // Reserved	 
 unsigned short Reserved [ 6 ] ;	 
 unsigned short Tag ; // Fixed as 0xBEEF	 
 } ACMDSP_VEParametersT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short drcGain ;	 
 unsigned short cutOffFreqIdx ;	 
 unsigned short resv1 ;	 
 unsigned short resv2 ;	 
	 
 } ACMCP_MediaDRCParaT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short maxRms ;	 
 unsigned short Alpha ;	 
 unsigned short N2cos ;	 
 unsigned short resv ;	 
	 
 } ACMCP_SPKProParaT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned int index ;	 
 unsigned int onoffCtl ; // bit0:eq , bit1: drc , bit2: spkPro	 
 EqParamIn eqParam ;	 
 ACMCP_MediaDRCParaT drcParam ;	 
 ACMCP_SPKProParaT spkProParam ;	 
 unsigned short Reserved [ 7 ] ;	 
 unsigned short Tag ; // Fixed as 0xBEEF	 
 } ACMCP_MediaVEarametersT;

typedef void ( *AudioStatusCallBack ) ( UINT32 appHandle , UINT32 primitive , UINT32 status , UINT32 value ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 SCENARIO_VOICE_CALL_BASIC // 0	 
 , SCENARIO_VOICE_CALL_NO_SIDETONE // 1	 
 , SCENARIO_INBANDMESSAGE_FROM_FS // 2	 
 , SCENARIO_PLAY_TO_FS // 3	 
 , SCENARIO_PLAY_TO_NS // 4	 
 , SCENARIO_RECORD_FROM_FS // 5	 
 , SCENARIO_RECORD_FROM_NS // 6	 
 , SCENARIO_VOCODER_STREAM // 7	 
 , SCENARIO_DAI_MODE1 // 8	 
 , SCENARIO_DAI_MODE2 // 9	 
 , SCENARIO_RECORD_BOTH_SIDES // 10	 
 , SCENARIO_PLAY_TO_BOTH_SIDES // 11	 
 , SCENARIO_PLAY_TO_FS_OVERRIDE // 12	 
 , SCENARIO_PLAY_TO_NS_OVERRIDE // 13	 
 , SCENARIO_PLAY_TO_BOTH_SIDES_OVERRIDE // 14	 
 , SCENARIO_TEST_PCM_LOOPBACK // 15	 
 , SCENARIO_TEST_VOCODER_LOOPBACK // 16	 
 , SCENARIO_TONE_STREAM // 17	 
 , SCENARIO_DUMMY_STREAM // 18	 
 , SCENARIO_RESERVED01	 
 , SCENARIO_RESERVED02	 
 , SCENARIO_RESERVED03	 
 , SCENARIO_RESERVED04	 
 , SCENARIO_RESERVED05	 
 , SCENARIO_RESERVED06	 
 , SCENARIO_RESERVED07	 
 , SCENARIO_RESERVED08	 
 , SCENARIO_RESERVED09	 
 , SCENARIO_RESERVED0A	 
 , SCENARIO_RESERVED0B	 
 , SCENARIO_RESERVED0C	 
 , SCENARIO_RESERVED0D	 
 , SCENARIO_RESERVED0E	 
 , SCENARIO_RESERVED0F	 
 , SCENARIO_LAST = SCENARIO_RESERVED0F	 
 } pcaApiCallScenarioT;

//ICAT EXPORTED STRUCT 
 typedef struct 
 {	 
 unsigned short ResMode ;	 
 unsigned short nonLinearModel ;	 
 unsigned short betaFest ;	 
 unsigned short betaDT ;	 
 unsigned short gammaDT ;	 
 unsigned short vmThresDT ;	 
 unsigned short burst ;	 
 unsigned short hang ;	 
 unsigned short cnGain ;	 
 unsigned short cnFloor ;	 
 signed short MinCnFloorGain ;	 
 unsigned short maxAdaptStep ;	 
 unsigned short slowAdaptWeight ;	 
 unsigned short initPeriod ;	 
 unsigned short AuditoryMaskFactor ;	 
 unsigned short pathChangeThres ;	 
 unsigned short Reserved [ 4 ] ;	 
 } Nres_ts;

typedef void ( *StreamCallBack ) ( UINT32 streamType , unsigned int* buff [ ] ) ;
typedef void ( *AudioStubConversationCB ) ( BOOL isCallStart ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 ACM_DSP_IF_VOICE_CALL	 
 , ACM_DSP_IF_TONE	 
 , ACM_DSP_IF_PCM	 
 , ACM_DSP_IF_PCM_WB	 
 , ACM_DSP_IF_HR	 
 , ACM_DSP_IF_FR	 
 , ACM_DSP_IF_EFR	 
	 
 /* AMR Rates */	 
 , ACM_DSP_IF_AMR_MR475	 
 , ACM_DSP_IF_AMR_MR515	 
 , ACM_DSP_IF_AMR_MR59	 
 , ACM_DSP_IF_AMR_MR67	 
 , ACM_DSP_IF_AMR_MR74	 
 , ACM_DSP_IF_AMR_MR795	 
 , ACM_DSP_IF_AMR_MR102	 
 , ACM_DSP_IF_AMR_MR122	 
 , ACM_DSP_IF_AMR_MRCNF	 
 , ACM_DSP_IF_AMR_MRNO_TX_RX	 
	 
 /* WB-AMR Rates */	 
 , ACM_DSP_IF_AMR_WB_6_60	 
 , ACM_DSP_IF_AMR_WB_8_85	 
 , ACM_DSP_IF_AMR_WB_12_65	 
 , ACM_DSP_IF_AMR_WB_14_25	 
 , ACM_DSP_IF_AMR_WB_15_85	 
 , ACM_DSP_IF_AMR_WB_18_25	 
 , ACM_DSP_IF_AMR_WB_19_85	 
 , ACM_DSP_IF_AMR_WB_23_05	 
 , ACM_DSP_IF_AMR_WB_23_85	 
 , ACM_DSP_IF_AMR_WB_SID	 
	 
 , ACM_DSP_IF_DUMMY	 
	 
 /* Must be at the end */	 
 , ACM_DSP_IF_NO_STREAM_TYPE	 
 , ACM_DSP_IF_NUM_OF_STREAM_TYPES = ACM_DSP_IF_NO_STREAM_TYPE	 
 , ACM_DSP_IF_STREAM_TYPE_ENUM_32_BIT = 0x7FFFFFFF // 32 bit enum compiling enforcement	 
 } AudioStreamType;

typedef void ( *audioPacketTransferFunc ) ( AudioStreamType streamType , unsigned int* buff ) ;
//ICAT EXPORTED ENUM 
 typedef enum {	 
 NON_INITIALIZED // 0	 
 , VOICE_IDLE // 1	 
 , WAIT_FOR_CSDI_START // 2	 
 , WAIT_FOR_CSDI_START_AND_PCM_STREAM // 3	 
 , WBCDMA_VOICE // 4	 
 , WBCDMA_VOICE_AND_PCM_STREAM // 5	 
 , VOICE_HAND_OVER // 6	 
 , VOICE_HAND_OVER_AND_PCM_STREAM // 7	 
 , WAIT_FOR_CSDI_STOP // 8	 
 , WAIT_FOR_CSDI_STOP_AND_PCM_STREAM // 9	 
 , GSM_VOICE // 10	 
 , GSM_VOICE_AND_PCM_STREAM // 11	 
 , PCM_STREAM_ONLY // 12	 
 , VOCODER_STREAM // 13	 
 , VOCODER_STREAM_AND_PCM_STREAM // 14	 
	 
	 
	 
	 
	 
 } Audio_Ctrl_State;

//ICAT EXPORTED ENUM 
 typedef enum {	 
 ECALL_CMD_EIM_ABORT_TERMINATED = 0 ,	 
 ECALL_CMD_EIM_ARM_ACTIVATE = 1 ,	 
 ECALL_CMD_UPDATE_MSD = 2 ,	 
 ECALL_CMD_URC_CONTROL = 3 ,	 
 ECALL_CMD_FEATURE_CONTROL = 4 ,	 
 ECALL_CMD_TIMERS_UPDATE = 5 ,	 
 ECALL_CMD_MSD_UPDATE_CONFIG = 6 ,	 
 ECALL_CMD_RESERVED_3 = 7 ,	 
 ECALL_CMD_NUM_OPCODES	 
 } EcallUecallDataCmdIdEnum;

typedef void ( *amrConnectionEstablish_t ) ( void ) ;
typedef void ( *amrConnectionClose_t ) ( void ) ;
typedef void ( *ctmNegoReport_t ) ( CtmNegoReport ) ;
typedef void ( *amrTxFrame_t ) ( amrFrameInfo_ts* frame ) ;
typedef void ( *IMSTxFrame_t ) ( IMSFrameInfo_ts* frame ) ;
typedef void ( *BitstreamAmrTxFrame_t ) ( BitstreamAmrFrameInfo_ts* frame ) ;
typedef void ( *PcmRxTransfer_t ) ( INT16* pData ) ;
typedef void ( *amrRxRequest_t ) ( amrFrameInfo_ts* frame ) ;
typedef void ( *ims_netEQ_callback ) ( void* data ) ;
//ICAT EXPORTED ENUM 
 typedef enum 
 {	 
 IDLE_VOLTE ,	 
 PKT_VOLTE ,	 
 PCM_VOLTE	 
 } Audio_VOLTE_Mode;

typedef void ( * L1_CB_AMR_TX_Frame_t ) ( amrFrameInfo_ts *TX_Frame ) ;
typedef IPC_Command Scenario_Cmd ;
DIAG_FILTER ( AUDIO , HAL , AudioHAL_spkProSet , DIAG_INFORMATION)  
 diagPrintf ( " mediaVEParamIndex:%d , onoffCtl:0x%lx , maxRms:%d , maxRms:%d , maxRms:%d " , 
 mediaVEParamIndex , 
 g_pMediaVEParamsTable [ mediaVEParamIndex ] .onoffCtl , 
 g_pMediaVEParamsTable [ mediaVEParamIndex ] .spkProParam.maxRms , 
 g_pMediaVEParamsTable [ mediaVEParamIndex ] .spkProParam.Alpha , 
 g_pMediaVEParamsTable [ mediaVEParamIndex ] .spkProParam.N2cos );

DIAG_FILTER ( AUDIO , HAL , spkPro_off , DIAG_INFORMATION)  
 diagPrintf ( " spkPro_off " );

DIAG_FILTER ( AUDIO , HAL , DrcUpdate , DIAG_INFORMATION)  
 diagPrintf ( " drc_gain:%d , cutoff_Freq_index:%d " , drc_gain , cutoff_Freq_index );

DIAG_FILTER ( AUDIO , HAL , dump_mediaVE_index , DIAG_INFORMATION)  
 diagPrintf ( " mediaVEParamIndex: %lx " , mediaVEParamIndex );

DIAG_FILTER ( AUDIO , HAL , dump_mediaVE , DIAG_INFORMATION)  
 diagStructPrintf ( " %S { ACMCP_MediaVEarametersT } " , 
 & ( g_pMediaVEParamsTable [ mediaVEParamIndex ] ) , 
 sizeof ( ACMCP_MediaVEarametersT ) );

DIAG_FILTER ( AUDIO , HAL , set_MediaVEIndex , DIAG_INFORMATION)  
 diagPrintf ( " index:0x%x , g_MediaVEParamsCount:0x%x " , index , g_MediaVEParamsCount );

DIAG_FILTER ( AUDIO , HAL , set_MediaVEIndex_error , DIAG_INFORMATION)  
 diagPrintf ( " index:0x%x , g_MediaVEParamsCount:0x%x " , index , g_MediaVEParamsCount );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_set_close_delay , DIAG_INFORMATION)  
 diagPrintf ( " cnt:0x%d " , cnt );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_SetResBufCnt , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_SetResBufCnt , bufCnt:0x%lx , actual:0x%lx " , bufCnt , actual );

DIAG_FILTER ( AUDIO , HAL , pingpongTrigger_error , DIAG_INFORMATION)  
 diagPrintf ( " none halfHandler " );

DIAG_FILTER ( AUDIO , HAL , pingpongTrigger_draining , DIAG_INFORMATION)  
 diagPrintf ( " pingpongTrigger_draining , AUDIOHAL_Play_Drain_cnt:%d " , AUDIOHAL_Play_Drain_cnt );

DIAG_FILTER ( AUDIO , HAL , pingpongTrigger_1 , DIAG_INFORMATION)  
 diagPrintf ( " before playback halfHandler " );

DIAG_FILTER ( AUDIO , HAL , pingpongTrigger_2 , DIAG_INFORMATION)  
 diagPrintf ( " after playback halfHandler " );

DIAG_FILTER ( AUDIO , HAL , pingpongRecordTrigger_1 , DIAG_INFORMATION)  
 diagPrintf ( " before record halfHandler " );

DIAG_FILTER ( AUDIO , HAL , pingpongRecordTrigger_2 , DIAG_INFORMATION)  
 diagPrintf ( " after record halfHandler " );

DIAG_FILTER ( AUDIO , HAL , processEnhancement , DIAG_INFORMATION)  
 diagPrintf ( " bufL:0x%lx , bufL:0x%lx " , bufL , bufR );

DIAG_FILTER ( AUDIO , HAL , EQ_INPUT , DIAG_INFORMATION)  
 diagStructPrintf ( " EQ_INPUT " , bufL , ( 48 * 20 ) * 2 );

DIAG_FILTER ( AUDIO , HAL , EQ_R_INPUT , DIAG_INFORMATION)  
 diagStructPrintf ( " EQ_R_INPUT " , bufR , ( 48 * 20 ) * 2 );

DIAG_FILTER ( AUDIO , HAL , EQ_OUTPUT , DIAG_INFORMATION)  
 diagStructPrintf ( " EQ_OUTPUT " , bufL , ( 48 * 20 ) * 2 );

DIAG_FILTER ( AUDIO , HAL , EQ_R_OUTPUT , DIAG_INFORMATION)  
 diagStructPrintf ( " EQ_R_OUTPUT " , bufR , ( 48 * 20 ) * 2 );

DIAG_FILTER ( AUDIO , HAL , DRC_INPUT , DIAG_INFORMATION)  
 diagStructPrintf ( " DRC_INPUT " , bufL , ( 48 * 20 ) * 2 );

DIAG_FILTER ( AUDIO , HAL , DRC_OUTPUT , DIAG_INFORMATION)  
 diagStructPrintf ( " DRC_OUTPUT " , bufL , ( 48 * 20 ) * 2 );

DIAG_FILTER ( AUDIO , HAL , processEnhancement_end , DIAG_INFORMATION)  
 diagPrintf ( " processEnhancement_end " );

DIAG_FILTER ( AUDIO , HAL , resampleProcess , DIAG_INFORMATION)  
 diagPrintf ( " playPingpongUsed:%ld , inSamples:%ld , step:%ld " , playPingpongUsed , inSamples , step );

DIAG_FILTER ( AUDIO , HAL , resampleProcess_error , DIAG_INFORMATION)  
 diagPrintf ( " pingpong buffer too short: pingpingTrigger= %lx " , pingpingTrigger );

DIAG_FILTER ( AUDIO , HAL , PingpongInput , DIAG_INFORMATION)  
 diagStructPrintf ( " PingpongInput: " , startAddress , AUDIOHAL_PlayST.length / 2 );

DIAG_FILTER ( AUDIO , HAL , resampleProcess_end , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_Play_Res_Cnt= %lx , AUDIOHAL_Play_Res_trigger_Cnt= %lx " , 
 AUDIOHAL_Play_Res_Cnt , 
 AUDIOHAL_Play_Res_trigger_Cnt );

DIAG_FILTER ( AUDIO , HAL , resampleProcessAll , DIAG_INFORMATION)  
 diagPrintf ( " resampleProcessAll " );

DIAG_FILTER ( AUDIO , HAL , resampleProcessAll_0 , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_Play_Res_Cnt= %lx , AUDIOHAL_Play_Res_trigger_Cnt= %lx " , 
 AUDIOHAL_Play_Res_Cnt , 
 AUDIOHAL_Play_Res_trigger_Cnt );

DIAG_FILTER ( AUDIO , HAL , fillPingpongBuffer_error , DIAG_INFORMATION)  
 diagPrintf ( " pingpong buffer too short: pingpingTrigger= %lx " , pingpingTrigger );

DIAG_FILTER ( AUDIO , HAL , PingpongOutput , DIAG_INFORMATION)  
 diagStructPrintf ( " PingpongOutput: " , startAddress , AUDIOHAL_RecordST.length / 2 );

DIAG_FILTER ( AUDIO , HAL , resampleProcessRecord , DIAG_INFORMATION)  
 diagPrintf ( " resampleProcessRecord " );

DIAG_FILTER ( AUDIO , HAL , resampleProcessRecord_rate_error , DIAG_INFORMATION)  
 diagPrintf ( " MixedRate:%lu , record_resample_inputRate:%lu , record_resample_inputRate:%lu " , 
 MixedRate , record_resample_inputRate , record_resample_inputRate );

DIAG_FILTER ( AUDIO , HAL , Record_Output , DIAG_INFORMATION)  
 diagStructPrintf ( " Record Resample Output: " , AUDIOHAL_RecordResOutBuf , outputSamples * 2 );

DIAG_FILTER ( AUDIO , HAL , notify_poc_data_event , DIAG_INFORMATION)  
 diagPrintf ( " notify_poc_data_event " );

DIAG_FILTER ( AUDIO , HAL , notify_dtmfdetection_event , DIAG_INFORMATION)  
 diagPrintf ( " notify_dtmfdetection_event " );

DIAG_FILTER ( AUDIO , HAL , notify_getvoice_event , DIAG_INFORMATION)  
 diagPrintf ( " notify_getvoice_event " );

DIAG_FILTER ( AUDIO , HAL , notify_extra_event , DIAG_INFORMATION)  
 diagPrintf ( " notify_extra_event " );

DIAG_FILTER ( AUDIO , HAL , audio_call_extra_process , DIAG_INFORMATION)  
 diagPrintf ( " audio_call_extra_process " );

DIAG_FILTER ( AUDIO , HAL , audio_add_extra_process , DIAG_INFORMATION)  
 diagPrintf ( " cb:0x%lx " , cb );

DIAG_FILTER ( AUDIO , HAL , audio_add_extra_process_already_exist , DIAG_INFORMATION)  
 diagPrintf ( " i:%d " , i );

DIAG_FILTER ( AUDIO , HAL , audio_add_extra_process_OK , DIAG_INFORMATION)  
 diagPrintf ( " audioExtraCB [ %d ] =0x%x , audioExtraCBCount=%d " , i , cb , audioExtraCBCount );

DIAG_FILTER ( AUDIO , HAL , audio_add_extra_process_fail , DIAG_INFORMATION)  
 diagPrintf ( " audio_add_extra_fail " );

DIAG_FILTER ( AUDIO , HAL , audio_remove_extra_process , DIAG_INFORMATION)  
 diagPrintf ( " cb:0x%lx " , cb );

DIAG_FILTER ( AUDIO , HAL , audio_remove_extra_process_OK , DIAG_INFORMATION)  
 diagPrintf ( " audioExtraCB [ %d ] =NULL , audioExtraCBCount=%d " , i , audioExtraCBCount );

DIAG_FILTER ( AUDIO , HAL , audio_remove_extra_process_fail , DIAG_INFORMATION)  
 diagPrintf ( " can not find this cb in slot " );

DIAG_FILTER ( AUDIO , HAL , ResampleTask_AUDIO_EXTRA_EVENT , DIAG_INFORMATION)  
 diagPrintf ( " ResampleTask_AUDIO_EXTRA_EVENT " );

DIAG_FILTER ( AUDIO , HAL , ResampleTask_VoiceCall_Event , DIAG_INFORMATION)  
 diagPrintf ( " ResampleTask_VoiceCall_Event " );

DIAG_FILTER ( AUDIO , HAL , ResampleTask_Media_Stop , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_delayStop_on:%d " , AUDIOHAL_delayStop_on );

DIAG_FILTER ( AUDIO , HAL , ResampleTask_ignore , DIAG_INFORMATION)  
 diagPrintf ( " check if stopping or last time resample takes too much time " );

DIAG_FILTER ( AUDIO , HAL , TaskInit1 , DIAG_INFORMATION)  
 diagPrintf ( " Malloc audioHAL_TaskStack failed , size=%ld Bytes " , 1024 *8 );

DIAG_FILTER ( AUDIO , HAL , copyAudioHalData , DIAG_INFORMATION)  
 diagPrintf ( " samples=%d " , samples );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_DelayStopCheck , DIAG_INFORMATION)  
 diagPrintf ( " cnt=%d , AUDIOHAL_delayStop_on:%d " , AUDIOHAL_DelayStopCount , AUDIOHAL_delayStop_on );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_HandlePCMPlay , DIAG_INFORMATION)  
 diagPrintf ( " samples = %lx , AUDIOHAL_PlayResBufUsedCnt = 0x%lx " , samples , AUDIOHAL_PlayResBufUsedCnt );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_HandlePCMPlay_preResample , DIAG_INFORMATION)  
 diagPrintf ( " mp3_preResample = %lx , " , mp3_preResample );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_HandlePCMPlay_wait_resample , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_Play_Res_Cnt=%lx , AUDIOHAL_Play_Res_trigger_Cnt=%lx , AUDIOHAL_PlayResBufUsedCnt=%lx " , 
 AUDIOHAL_Play_Res_Cnt , 
 AUDIOHAL_Play_Res_trigger_Cnt , 
 AUDIOHAL_PlayResBufUsedCnt );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_HandlePCMPlay_mp3_current_volume , DIAG_INFORMATION)  
 diagPrintf ( " mp3_volume = 0x%lx , mp3_current_volume = 0x%lx , mp3_fading_volume = 0x%lx " , 
 mp3_volume , mp3_current_volume , mp3_fading_volume );

DIAG_FILTER ( AUDIO , HAL , HandlePCMPlay_resample_trigger , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_Play_Res_Cnt= %lx , AUDIOHAL_Play_Res_trigger_Cnt= %lx " , 
 AUDIOHAL_Play_Res_Cnt , 
 AUDIOHAL_Play_Res_trigger_Cnt );

DIAG_FILTER ( AUDIO , HAL , HandlePCMRecord_error , DIAG_INFORMATION)  
 diagPrintf ( " samplecnt=0x%lx " , samplecnt );

DIAG_FILTER ( AUDIO , HAL , Record_Discard , DIAG_INFORMATION)  
 diagPrintf ( " discard first N frame. AUDIOHAL_RecordCnt=0x%lx " , AUDIOHAL_RecordCnt );

DIAG_FILTER ( AUDIO , HAL , HandlePCMRecord , DIAG_INFORMATION)  
 diagPrintf ( " samplecnt=0x%lx " , samplecnt );

DIAG_FILTER ( AUDIO , HAL , updateCodecStreamStatus_AudioHAL , DIAG_INFORMATION)  
 diagPrintf ( " audioP.state= %e { Audio_Ctrl_State } , AUDIOHAL_dspLoopback=0x%lx , StreamPlaying=0x%lx , StreamRecording=0x%lx , vibration_status=0x%lx , AudioHAL_DTMFkey=0x%lx " , 
 audioP.state , AUDIOHAL_dspLoopback , AUDIOHAL_StreamPlaying , AUDIOHAL_StreamRecording , AudioHAL_vibration_status , AudioHAL_DTMFkey );

DIAG_FILTER ( AUDIO , HAL , updateCodecStreamStatus , DIAG_INFORMATION)  
 diagPrintf ( " codecStream_Rx_need=0x%lx , codecStream_Tx_need=0x%lx " , 
 codecStream_Rx_need , codecStream_Tx_need );

DIAG_FILTER ( AUDIO , HAL , AUDIOHAL_GetBufferSize_error , DIAG_INFORMATION)  
 diagPrintf ( " rate: %lx , chan: %lx , min: %lx , max: %lx " , rate , chan , min , max );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_VoiceCallEvent , DIAG_INFORMATION)  
 diagPrintf ( " event: %lx , AUDIOHAL_Stream_used: %lx " , event , AUDIOHAL_Stream_used );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_DSP_AUDIO_STATUS , DIAG_INFORMATION)  
 diagPrintf ( " event: %lx , AUDIOHAL_Stream_used: %lx " , event , AUDIOHAL_Stream_used );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_DRCReset , DIAG_INFORMATION)  
 diagPrintf ( " gain=0x%lx , index=0x%lx!!! " , gain , index );

DIAG_FILTER ( AUDIO , HAL , audiohal_bind_rx_handler , DIAG_INFORMATION)  
 diagPrintf ( " pData=0x%lx , length=%d , step=%d " , pData , Length , step );

DIAG_FILTER ( AUDIO , HAL , AudioHal_AifPlay2Farend , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_farend: %d , farend: %d " , AUDIOHAL_farend , farend );

DIAG_FILTER ( AUDIO , HAL , playStreamBufferInit_not_malloc_way , DIAG_INFORMATION)  
 diagPrintf ( " playStreamBufferInit_not_malloc_way " );

DIAG_FILTER ( AUDIO , HAL , AifPlayStreamLength , DIAG_INFORMATION)  
 diagPrintf ( " rate=0x%lx , channel=%d , length=%d , sizeof ( AUDIOHAL_STREAM_T ) =%d , AUDIOHAL_farend=%d , AUDIOHAL_adaptor=%d " , 
 playedStream->sampleRate , 
 playedStream->channelNb , 
 playedStream->length , 
 sizeof ( AUDIOHAL_STREAM_T ) , 
 AUDIOHAL_farend , 
 AUDIOHAL_adaptor );

DIAG_FILTER ( AUDIO , ACM , AudioHAL_AifPlayStream_unsupport_rate , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_AifPlayStream_unsupport_rate " );

DIAG_FILTER ( AUDIO , PCA_API , AudioHAL_AifPlayStream_not_ready , DIAG_ERROR)  
 diagPrintf ( " MSA not ready yet " );

DIAG_FILTER ( AUDIO , HAL , AifPlayStream_error , DIAG_INFORMATION)  
 diagPrintf ( " already open " );

DIAG_FILTER ( AUDIO , HAL , AifPlayStream_bad_channel , DIAG_INFORMATION)  
 diagPrintf ( " channelNb: %lx " , playedStream->channelNb );

DIAG_FILTER ( AUDIO , HAL , AifPlayStream_bad_length , DIAG_INFORMATION)  
 diagPrintf ( " length: %lx , sampleRate: %lx , channelNb: %lx " , playedStream->length , playedStream->sampleRate , playedStream->channelNb );

DIAG_FILTER ( AUDIO , HAL , AifPlayStream_bad_length_2 , DIAG_INFORMATION)  
 diagPrintf ( " length: %lx , sampleRate: %lx , channelNb: %lx " , playedStream->length , playedStream->sampleRate , playedStream->channelNb );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_AifPlayStream_MediaEqNvmStain , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_AifPlayStream_MediaEqNvmStain " );

DIAG_FILTER ( AUDIO , HAL , AifRecordStreamLength , DIAG_INFORMATION)  
 diagPrintf ( " MMI record rate=%u , length=%d , channelNb=%d " , 
 recordedStream->sampleRate , 
 recordedStream->length , 
 recordedStream->channelNb );

DIAG_FILTER ( AUDIO , PCA_API , AudioHAL_AifRecordStream_not_ready , DIAG_ERROR)  
 diagPrintf ( " MSA not ready yet " );

DIAG_FILTER ( AUDIO , PCA_API , AudioHAL_AifRecordStream_rate_not_support , DIAG_ERROR)  
 diagPrintf ( " AudioHAL_AifRecordStream_rate_not_support " );

DIAG_FILTER ( AUDIO , HAL , AifRecordStream_bad_length , DIAG_INFORMATION)  
 diagPrintf ( " length: %lx , sampleRate: %lx , channelNb: %lx " , recordedStream->length , recordedStream->sampleRate , recordedStream->channelNb );

DIAG_FILTER ( AUDIO , HAL , AifRecordStream_error , DIAG_INFORMATION)  
 diagPrintf ( " already open " );

DIAG_FILTER ( AUDIO , HAL , AifRecordStream_pcm_rate , DIAG_INFORMATION)  
 diagPrintf ( " audiohal_pcm_wb:%d , record_resample_inputRate:%d " , 
 audiohal_pcm_wb , record_resample_inputRate );

DIAG_FILTER ( AUDIO , HAL , AifPause , DIAG_INFORMATION)  
 diagPrintf ( " %s: %s " , __FUNCTION__ , pause? " on " : " off " );

DIAG_FILTER ( AUDIO , HAL , AifPauseErr , DIAG_ERROR)  
 diagPrintf ( " Pause while no in streaming " );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_AifDrain_notPlaying , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_AifDrain_notPlaying " );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_AifDrain , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_Play_Drain_cnt: %d " , AUDIOHAL_Play_Drain_cnt );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_AifDrain_wait , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_Play_Drain_cnt: %d , maxCnt: %d " , AUDIOHAL_Play_Drain_cnt , maxCnt );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_AifDrain_end , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_Play_Drain_cnt: %d " , AUDIOHAL_Play_Drain_cnt );

DIAG_FILTER ( AUDIO , HAL , AifStopPlay , DIAG_INFORMATION)  
 diagPrintf ( " %s " , __FUNCTION__ );

DIAG_FILTER ( AUDIO , HAL , AifStopRecord , DIAG_INFORMATION)  
 diagPrintf ( " %s " , __FUNCTION__ );

DIAG_FILTER ( AUDIO , HAL , reCreateRecordResampler , DIAG_INFORMATION)  
 diagPrintf ( " inputRate=%lu , outputRate=%lu " , inputRate , outputRate );

//ICAT EXPORTED FUNCTION - Audio , HAL , test_set_MediaEQIndex 
 void test_set_MediaVEIndex ( void* data ) 
 {	 
 unsigned int index = * ( ( unsigned int * ) data ) ;	 
DIAG_FILTER ( AUDIO , HAL , test_set_MediaVEIndex , DIAG_INFORMATION)  
 diagPrintf ( " index:0x%x , " , index );

	 
 set_MediaVEIndex ( index ) ;	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , HAL , test_Switch_close_delay 
 void AudioHAL_Switch_close_delay ( void ) 
 {	 
 if ( 0 == AUDIOHAL_DelayStopNum ) { AudioHAL_set_close_delay ( 150 ) ; }	 
 else { AudioHAL_set_close_delay ( 0 ) ; }	 
	 
DIAG_FILTER ( AUDIO , HAL , AudioHAL_Switch_close_delay , DIAG_INFORMATION)  
 diagPrintf ( " AUDIOHAL_DelayStopNum:0x%d " , AUDIOHAL_DelayStopNum );

	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , HAL , TestSetBufCnt 
 void TestSetBufCnt ( void* data ) 
 {	 
 UINT32 cnt = 0 ;	 
 cnt = * ( ( UINT16* ) data ) ;	 
	 
 AudioHAL_SetResBufCnt ( cnt ) ;	 
	 
DIAG_FILTER ( AUDIO , HAL , TestSetBufCnt , DIAG_INFORMATION)  
 diagPrintf ( " cnt=0x%lx! " , cnt );

	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , HAL , TestDRCReset 
 void AudioHAL_TestDRCReset ( void* data ) 
 {	 
 UINT16 gain = 0 ;	 
 UINT16 index = 0 ;	 
 UINT16* pData = ( UINT16* ) data ;	 
 gain = *pData ;	 
 pData++ ;	 
 index = *pData ;	 
 AudioHAL_DRCReset ( gain , index ) ;	 
	 
DIAG_FILTER ( AUDIO , HAL , AudioHAL_TestDRCReset , DIAG_INFORMATION)  
 diagPrintf ( " gain=0x%lx , index=0x%lx!!! " , gain , index );

	 
 return ;	 
 }

//ICAT EXPORTED FUNCTION - Audio , HAL , AudioHAL_test_adaptor 
 void AudioHAL_test_adaptor ( void ) 
 {	 
 static int farend = 0 ;	 
 farend = ( farend + 1 ) % 3 ;	 
DIAG_FILTER ( AUDIO , HAL , AudioHAL_test_adaptor , DIAG_INFORMATION)  
 diagPrintf ( " farend: %d " , farend );

	 
 AudioHal_AifPlay2Farend ( farend ) ;	 
 }

DIAG_FILTER ( AUDIO , HAL , TestRecord_HalfHandler_mediaLoop , DIAG_INFORMATION)  
 diagPrintf ( " bufferstart=%lx " , bufferstart );

DIAG_FILTER ( AUDIO , HAL , TestRecord_HalfHandler_mediaLoop_data , DIAG_INFORMATION)  
 diagStructPrintf ( " record data " , bufferstart , AudioHal_Adapt_Record.length / 2 );

DIAG_FILTER ( AUDIO , HAL , TestPlay_HalfHandler_mediaLoop_data , DIAG_INFORMATION)  
 diagPrintf ( " bufferstart=%lx , " , bufferstart );

DIAG_FILTER ( AUDIO , HAL , testBufferInit_error1 , DIAG_INFORMATION)  
 diagPrintf ( " malloc buffer for AudioHal_Adapt_Play_Data fail " );

DIAG_FILTER ( AUDIO , HAL , testBufferInit_error2 , DIAG_INFORMATION)  
 diagPrintf ( " malloc buffer for AudioHal_Adapt_Record_Data fail " );

DIAG_FILTER ( AUDIO , HAL , testBufferInit_error3 , DIAG_INFORMATION)  
 diagPrintf ( " malloc buffer for mediaLoopBuffer fail " );

DIAG_FILTER ( AUDIO , HAL , testBufferInit , DIAG_INFORMATION)  
 diagPrintf ( " AudioHal_Adapt_Play_Data:0x%lx , AudioHal_Adapt_Record_Data:0x%lx , mediaLoopBuffer:0x%lx " , 
 AudioHal_Adapt_Play_Data , 
 AudioHal_Adapt_Record_Data , 
 mediaLoopBuffer );

//ICAT EXPORTED FUNCTION - Audio , HAL , AudioHAL_TestMediaLoopback 
 void AudioHAL_TestMediaLoopback ( void *pData ) 
 {	 
	 
 AUDIOHAL_ERR_T rc = AUDIOHAL_ERR_NO ;	 
 static int cnt = 1 ;	 
	 
 cnt ++ ;	 
DIAG_FILTER ( AUDIO , HAL , TestMediaLoopback , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_TestMediaLoopback " );

	 
 if ( 0 != testBufferInit ( ) ) {		 
 return ;		 
 }	 
	 
 if ( cnt % 2 ) {		 
DIAG_FILTER ( AUDIO , HAL , TestMediaLoopback_close , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_TestMediaLoopback_close " );

		 
 // ACMAudio_StartDSPVoicePath ( FALSE ) ;		 
 AudioHAL_AifDrain ( ) ;		 
DIAG_FILTER ( AUDIO , HAL , TestMediaLoopback_drain_return , DIAG_INFORMATION)  
 diagPrintf ( " TestMediaLoopback_drain_return " ) 
 AudioHAL_AifStopRecord ( );

		 
 AudioHAL_AifStopPlay ( ) ;		 
 return ;		 
 }	 
 dbgRecCnt = 0 ;	 
	 
 AudioHAL_SetResBufCnt ( 20 ) ;	 
	 
 memset ( mediaLoopBuffer , 0 , sizeof ( mediaLoopBuffer ) ) ;	 
	 
	 
 AudioHal_Adapt_Play.channelNb = AUDIOHAL_MONO ;	 
	 
 AudioHal_Adapt_Play.length = AudioHal_Adapt_Play.channelNb * ( ( ( unsigned int ) voiceRecordRate ) * 20 * 2 * 2 / 1000 ) ;	 
 // AudioHal_Adapt_Play.length = 640 ;	 
 memset ( AudioHal_Adapt_Play_Data , 0 , AudioHal_Adapt_Play.length ) ;	 
	 
 AudioHal_Adapt_Play.playSyncWithRecord = 0 ;	 
 AudioHal_Adapt_Play.voiceQuality = 0 ;	 
 AudioHal_Adapt_Play.sampleRate = voiceRecordRate ;	 
 AudioHal_Adapt_Play.startAddress = ( UINT32 * ) AudioHal_Adapt_Play_Data ;	 
 AudioHal_Adapt_Play.halfHandler = TestPlay_HalfHandler_mediaLoop ;	 
	 
 AudioHAL_AifPlayStream ( &AudioHal_Adapt_Play ) ;	 
 OsaTaskSleep ( 5 , 0 ) ;	 
	 
	 
 // AudioHal_Adapt_Record.length = 9216 ; // 640 Bytes per 20 ms frame @16KHz	 
 AudioHal_Adapt_Record.length = ( ( unsigned int ) voiceRecordRate ) * 20 * 2 * 2 / 1000 ;	 
 // AudioHal_Adapt_Record.length = ( ( unsigned int ) AUDIOHAL_FREQ_16000HZ ) / 1000 * 20 * SAMPLE_BYTES * 2 ;	 
	 
 AudioHal_Adapt_Record.channelNb = AUDIOHAL_MONO ;	 
 AudioHal_Adapt_Record.playSyncWithRecord = 0 ;	 
 AudioHal_Adapt_Record.voiceQuality = 0 ;	 
 AudioHal_Adapt_Record.sampleRate = voiceRecordRate ;	 
 AudioHal_Adapt_Record.startAddress = ( UINT32 * ) AudioHal_Adapt_Record_Data ;	 
 AudioHal_Adapt_Record.halfHandler = TestRecord_HalfHandler_mediaLoop ;	 
	 
 AudioHAL_AifRecordStream ( &AudioHal_Adapt_Record ) ;	 
	 
 // Send control to DSP	 
 // ACMAudio_StartDSPVoicePath ( TRUE ) ;	 
	 
 }

//ICAT EXPORTED FUNCTION - Audio , HAL , AudioHAL_Switch_record_outputRate 
 void AudioHAL_Switch_record_outputRate ( void ) 
 {	 
 if ( AUDIOHAL_FREQ_8000HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_12000HZ ;		 
 } else if ( AUDIOHAL_FREQ_12000HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_16000HZ ;		 
 } else if ( AUDIOHAL_FREQ_16000HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_22050HZ ;		 
 } else if ( AUDIOHAL_FREQ_22050HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_24000HZ ;		 
 } else if ( AUDIOHAL_FREQ_24000HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_32000HZ ;		 
 } else if ( AUDIOHAL_FREQ_32000HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_44100HZ ;		 
 } else if ( AUDIOHAL_FREQ_44100HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_48000HZ ;		 
 } else if ( AUDIOHAL_FREQ_48000HZ == voiceRecordRate ) {		 
 voiceRecordRate = AUDIOHAL_FREQ_8000HZ ;		 
 }	 
	 
DIAG_FILTER ( AUDIO , HAL , AudioHAL_Switch_record_outputRate , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_Switch_record_outputRate: %lu " , voiceRecordRate );

	 
	 
 }

//ICAT EXPORTED FUNCTION - Audio , HAL , AudioHAL_VoiceRecord 
 void AudioHAL_VoiceRecord ( void *pData ) 
 {	 
	 
 AUDIOHAL_ERR_T rc = AUDIOHAL_ERR_NO ;	 
 static int cnt = 1 ;	 
	 
 cnt ++ ;	 
DIAG_FILTER ( AUDIO , HAL , VoiceRecord , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_VoiceRecord " );

	 
	 
 if ( 0 != testBufferInit ( ) ) {		 
 return ;		 
 }	 
	 
 if ( cnt % 2 ) {		 
DIAG_FILTER ( AUDIO , HAL , VoiceRecord_close , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_VoiceRecord_close " );

		 
 // ACMAudio_StartDSPVoicePath ( FALSE ) ;		 
 AudioHAL_AifStopRecord ( ) ;		 
 return ;		 
 }	 
	 
 dbgRecCnt = 0 ;	 
 // AudioHal_Adapt_Record.length = 9216 ; // 640 Bytes per 20 ms frame @16KHz	 
 // AudioHal_Adapt_Record.length = 640 ; // 640 Bytes per 20 ms frame @8KHz	 
 AudioHal_Adapt_Record.length = ( ( unsigned int ) voiceRecordRate ) / 1000 * 20 * 2 * 2 ;	 
	 
 AudioHal_Adapt_Record.channelNb = AUDIOHAL_MONO ;	 
 AudioHal_Adapt_Record.playSyncWithRecord = 0 ;	 
 // AudioHal_Adapt_Record.voiceQuality = 1 ;	 
 AudioHal_Adapt_Record.voiceQuality = 0 ;	 
 AudioHal_Adapt_Record.sampleRate = voiceRecordRate ;	 
 AudioHal_Adapt_Record.startAddress = ( UINT32 * ) AudioHal_Adapt_Record_Data ;	 
 AudioHal_Adapt_Record.halfHandler = TestRecord_HalfHandler_mediaLoop ;	 
	 
 AudioHAL_AifRecordStream ( &AudioHal_Adapt_Record ) ;	 
	 
 }

//ICAT EXPORTED FUNCTION - Audio , HAL , AudioHAL_PlaybackPause 
 void AudioHAL_PlaybackPause ( void ) 
 {	 
	 
 static int cnt = 0 ;	 
	 
 cnt ++ ;	 
 cnt %= 2 ;	 
DIAG_FILTER ( AUDIO , HAL , AudioHAL_PlaybackPause , DIAG_INFORMATION)  
 diagPrintf ( " AudioHAL_PlaybackPause , cnt:%lx " , cnt );

	 
 AudioHAL_AifPause ( cnt ) ;	 
 return ;	 
	 
 }

DIAG_FILTER ( AUDIO , HAL , AudioHAL_FadingControl , DIAG_INFORMATION)  
 diagPrintf ( " mode=0x%lx , mp3_fading=0x%lx , mp3_current_volume=0x%lx " , 
 mode , mp3_fading , mp3_current_volume );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_FadingCtl , DIAG_INFORMATION)  
 diagPrintf ( " on=0x%lx " , on );

DIAG_FILTER ( AUDIO , HAL , AudioHAL_SetFadingStep , DIAG_INFORMATION)  
 diagPrintf ( " step=0x%lx " , step );

