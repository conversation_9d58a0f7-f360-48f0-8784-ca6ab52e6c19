/************************************************************************
*
*  File Name:  nand_hal.h
*
*  Description:  NAND flash handware attaction layer
*
*  Rev  Author			Date		Changes
*  ---  ---------------	----------	-------------------------------
*  1.0  kunzheng cui	3/18/2007	Initial draft
*************************************************************************/
#ifndef	NAND_HAL_H
#define	NAND_HAL_H

#include "nand.h"

typedef struct NAND_Info{
U32 SectorNums;
U32 SectorSize;
} NAND_Info;

#ifndef sd_capacity_t

typedef struct 
{
    U32 sector_size;
    U32 sector_num;
}sd_capacity_t;

#endif

S32 NAND_Init(void);

S32 NAND_EraseAll(void);

/* the interface for MARVELL USB driver, similar to the SD.
    
    and the address must be on the boundary of 512
 */
void hal_NandGetCapacity(sd_capacity_t * pCapacity);
S32 hal_NandReadBlock(U32 Address, U32 Length, U8 * pDataBuf);
S32 hal_NandWriteBlock(U32 Address, U32 Length, U8 * pDataBuf);



/* the interface manipulating the nand flash directly,
    for File System, or other usage.
    
    the sector is based on 512 bytes for both small page and large page 
 */
void NAND_GetInfo(NAND_Info * NandInfo);
S32 NAND_ReadSector(U32 Sector, U8* Buf);
S32 NAND_WriteSector(U32 Sector, U8* Buf);


/* this function should be called in file closing function 
	or after write sector useing above function
		
	*/
S32 NAND_WriteBack(void);


#endif
