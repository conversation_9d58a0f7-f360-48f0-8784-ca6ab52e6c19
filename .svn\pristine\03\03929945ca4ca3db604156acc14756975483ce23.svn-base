/**
 * @file dial_clock_digital_pro_fou.c
 *
 */

/*********************
 *      INCLUDES
 *********************/
#include "lv_watch.h"

#if USE_LV_WATCH_DIAL_CLOCK_COLOUR != 0 || USE_LV_WATCH_DIAL_CLOCK_DIGITAL_PRO_FOU != 0

#include <stdio.h>

/*********************
 *      DEFINES
 *********************/

/**********************
 *      TYPEDEFS
 **********************/

/**********************
 *  STATIC PROTOTYPES
 **********************/



/**********************
 *  STATIC VARIABLES
 **********************/
static const void * clock_digital_fou[10] = {
    ICON_PRO_FOU_CLOCK_DIGITAL_0,
    ICON_PRO_FOU_CLOCK_DIGITAL_1,
    ICON_PRO_FOU_CLOCK_DIGITAL_2,
    ICON_PRO_FOU_CLOCK_DIGITAL_3,
    ICON_PRO_FOU_CLOCK_DIGITAL_4,
    ICON_PRO_FOU_CLOCK_DIGITAL_5,
    ICON_PRO_FOU_CLOCK_DIGITAL_6,
    ICON_PRO_FOU_CLOCK_DIGITAL_7,
    ICON_PRO_FOU_CLOCK_DIGITAL_8,
    ICON_PRO_FOU_CLOCK_DIGITAL_9
};


static const void * clock_date_digital_fou[10] = {
    ICON_PRO_FOU_DATE_DIGITAL_0,
    ICON_PRO_FOU_DATE_DIGITAL_1,
    ICON_PRO_FOU_DATE_DIGITAL_2,
    ICON_PRO_FOU_DATE_DIGITAL_3,
    ICON_PRO_FOU_DATE_DIGITAL_4,
    ICON_PRO_FOU_DATE_DIGITAL_5,
    ICON_PRO_FOU_DATE_DIGITAL_6,
    ICON_PRO_FOU_DATE_DIGITAL_7,
    ICON_PRO_FOU_DATE_DIGITAL_8,
    ICON_PRO_FOU_DATE_DIGITAL_9
};


static const void * clock_week_date_fou[7] = {
    ICON_PRO_FOU_WEEK_SUN,
    ICON_PRO_FOU_WEEK_MON,
    ICON_PRO_FOU_WEEK_TUE,
    ICON_PRO_FOU_WEEK_WED,
    ICON_PRO_FOU_WEEK_THU,
    ICON_PRO_FOU_WEEK_FRI,
    ICON_PRO_FOU_WEEK_SAT,
};

static const void * clock_pro_fou_bg[2] = {
    ICON_PRO_FOU_DIGITAL_BG1,
    ICON_PRO_FOU_DIGITAL_BG2,
};



/**********************
 *      MACROS
 **********************/

/**********************
 *   GLOBAL FUNCTIONS
***********************/
/*obj is a tab of tabview*/
lv_obj_t * dial_clock_digital_pro_fou_create(lv_obj_t * obj, uint8_t index)
{
    if(!obj) obj = lv_scr_act();

    lv_obj_t * img = lv_img_create(obj, NULL);
    LV_ASSERT_MEM(img);
    if(img == NULL) return NULL;

     lv_img_set_src(img, clock_pro_fou_bg[index]);
    lv_obj_set_size(img, lv_obj_get_width(obj), lv_obj_get_height(obj));

    // containt for digital clock
    lv_obj_t * clock_cont = lv_cont_create(img, NULL);
    lv_digital_clock_ext_t * digital_clock_ext =
        lv_obj_allocate_ext_attr(clock_cont, sizeof(lv_digital_clock_ext_t));
    lv_obj_set_click(clock_cont, false);
    lv_obj_add_style(clock_cont, LV_CONT_PART_MAIN, &lv_style_transp_tight);

    // set hour
    digital_clock_ext->digital_hour = (void **)clock_digital_fou;

    // img for units of hour
    digital_clock_ext->hour_units_img = lv_img_create(clock_cont, NULL);
    LV_ASSERT_MEM(digital_clock_ext->hour_units_img);
    if(digital_clock_ext->hour_units_img == NULL) {
        return(NULL);
    }

    // img for tens of hour
    digital_clock_ext->hour_tens_img = lv_img_create(clock_cont, NULL);
    LV_ASSERT_MEM(digital_clock_ext->hour_tens_img);
    if(digital_clock_ext->hour_tens_img == NULL) {
        return(NULL);
    }

   // set min
    digital_clock_ext->digital_min = (void **)clock_digital_fou;

    // img for units of minute
    digital_clock_ext->min_units_img = lv_img_create(clock_cont, NULL);
    LV_ASSERT_MEM(digital_clock_ext->min_units_img);
    if(digital_clock_ext->min_units_img == NULL) {
        return(NULL);
    }

    // img for tens of minute
    digital_clock_ext->min_tens_img = lv_img_create(clock_cont, NULL);
    LV_ASSERT_MEM(digital_clock_ext->min_tens_img);
    if(digital_clock_ext->min_tens_img == NULL) {
        return(NULL);
    }

    // label for day
    digital_clock_ext->day_label = lv_label_create(clock_cont, NULL);
    LV_ASSERT_MEM(digital_clock_ext->day_label);
    if(digital_clock_ext->day_label == NULL) {
        return(NULL);
    }
	//lv_img_set_src(digital_clock_ext->day_label,&icon_date_border);
    lv_obj_add_style(digital_clock_ext->day_label, LV_LABEL_PART_MAIN, &lv_watch_font20);

	
	digital_clock_ext->week_label = lv_label_create(clock_cont, NULL);
	LV_ASSERT_MEM(digital_clock_ext->week_label);
	if(digital_clock_ext->week_label == NULL) {
		return(NULL);
	}
	lv_obj_add_style(digital_clock_ext->week_label, LV_LABEL_PART_MAIN, &lv_watch_font30);

    // set first img
    dial_update_digital_display(clock_cont);

    // set alignment
    lv_cont_set_fit(clock_cont, LV_FIT_TIGHT);
	lv_obj_align(clock_cont, NULL, LV_ALIGN_IN_TOP_MID,0, 10);
    lv_obj_align(digital_clock_ext->hour_tens_img,NULL, LV_ALIGN_IN_TOP_MID, -40,10);
    lv_obj_align(digital_clock_ext->hour_units_img, digital_clock_ext->hour_tens_img, LV_ALIGN_OUT_RIGHT_MID,0,0);
	lv_obj_align(digital_clock_ext->day_label, digital_clock_ext->hour_units_img, LV_ALIGN_OUT_RIGHT_BOTTOM,5, -10);
	lv_obj_align(digital_clock_ext->week_label, digital_clock_ext->day_label, LV_ALIGN_OUT_BOTTOM_LEFT,0,0);
    lv_obj_align(digital_clock_ext->min_tens_img, digital_clock_ext->hour_tens_img, LV_ALIGN_OUT_BOTTOM_MID,0,5);
 	lv_obj_align(digital_clock_ext->min_units_img, digital_clock_ext->min_tens_img, LV_ALIGN_OUT_RIGHT_MID,0,0);

    dial_clock_start_update(clock_cont);
    return img;
}

/**********************
 *   STATIC FUNCTIONS
 **********************/

#endif /*USE_LV_WATCH_DIAL_CLOCK_DIGITAL_PRO*/
