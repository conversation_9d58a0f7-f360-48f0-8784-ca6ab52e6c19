LOAD 0x7E000000                              ;0x7E000000 - 0x7E004000, size: 4*64K=256K
{
    INIT 0x7E000000 0x2000                   ;8k
    {
        OBM_StartUp.o (Init, +First)         ; Startup code
        main.o (+RO)                        ; Place main() in a root region for the benefit of software breakpoints
		version_block.o (IMGVERBLOCK)
    }
	
    CODE  +0	0x0A000 ;             		;96k-8k=88k
    {
        * (+RO)                             ; Application code, including C library
    }

    ITCM  0x100	0x10000-0x100 ;             			;almost 64K , NOTIE: for SECBOOT version 2 , the lower addr area of ITCM wased used for SVC interrupt vector , reserve for syscall_init()
    {
        LzmaDec.o (+RO)                     ; LzmaDec code on ITCM
		FreqChange.o (PSRAM_FC)
    }
	
    DATA  0x7E00C000	(0x1A000-0x400) ;			;104K
    {
        * (+RW,+ZI)                         ; All RW and ZI Data
    }
    SEC_KEY_BUF  +0	0x400                   ; NOTICE: for SECBOOT version 2 , SQU should also be reserved ,so move SEC_KEY_BUR from SQU into PSRAM to avoid overwrite.
    {

    }
	
	DTCM  0xB0021C00   0xE400 {
		OBM_StartUp.o (+RW,+ZI)
		fip.o       (+RW,+ZI)
		secboot.o   (+RW,+ZI)
    };

	IMG_END +0 EMPTY 0x04{}
}


