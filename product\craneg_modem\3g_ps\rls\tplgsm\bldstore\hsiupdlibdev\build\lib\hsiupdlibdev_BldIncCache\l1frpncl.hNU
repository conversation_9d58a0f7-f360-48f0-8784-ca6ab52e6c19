/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/

/***************************************************************************
 *
 *                           TTPCom Software
 *
 *            Copyright (c) 1999-2000 TTP Communications Ltd.
 *
 ***************************************************************************
 *
 *   $Id: //central/releases/Branch_release_9/tplgsm/l1inc/l1frpncl.h#2 $
 *   $Revision: #2 $
 *   $DateTime: 2004/01/14 14:10:56 $
 *
 ***************************************************************************
 *
 *  File Description :
 *
 *      Header file for the L1 Packet Transfer Mode (PTM) Neighbour Cell
 *      Sub-system
 *
 ***************************************************************************
 *
 ***************************************************************************/
#if defined( INTEL_UPGRADE_PTM_NCL_UPM_RESERV )/* 3PT_EliadY_161103 */
// If phase II switch is on use its version:
#include <l1frpncl_INT.h>
#else 	/* INTEL_UPGRADE_PTM_NCL_UPM_RESERV */
// If phase II switch is off:
#if defined( INTEL_UPGRADE_PTM_NCL_UPM_BSIC )/* 3PT_EliadY_161103 */
// If phase I switch is on use its version:
#include <l1frpncl_P1_INT.h>
#else 	/* INTEL_UPGRADE_PTM_NCL_UPM_BSIC */
// Use this file version
#endif 	/* INTEL_UPGRADE_PTM_NCL_UPM_BSIC */
#endif 	/* INTEL_UPGRADE_PTM_NCL_UPM_RESERV */

#if !defined (L1FRPNCL_H)
#define       L1FRPNCL_H

/***************************************************************************
 * Nested Include Files
 **************************************************************************/

#include <system.h>


#include <l1evncl.h>
#include <mph_typ.h>
#include <l1cell.h>
#include <l1frgitm.h>

#include <l1dspshm.h>

/***************************************************************************
 * Manifest Constants
 **************************************************************************/

#define L1_PTM_NCL_NO_ACTION 0xFF

#define L1_PTM_NCL_SUB_FRAME_SIZE    (QBITS_PER_FRAME/2)     /* same as L1_PTM_INTERRUPT_PERIOD in l1frpt.c */

#define L1_PTM_NCL_MAX_NUM_SB_CANDIDATES 4
#define L1_PTM_NCL_MAX_TASKS             4
#define L1_PTM_NCL_MAX_INT_BURSTS        4

#define L1_PTM_NCL_INT_NEW_CHAN_CFG      0x01
#define L1_PTM_NCL_INT_NEW_TS_MIN        0x02

#define L1_PTM_NCL_SCELL_MON_INDEX       0xFE

/***************************************************************************
 * Structure shortcuts
 **************************************************************************/

/***************************************************************************
 * Type Definitions
 **************************************************************************/

typedef struct L1FrPtmSortedListElementTag
{
   Int16    rssiLevel;
   Int16    baIndex;
}
L1FrPtmSortedListElement;

typedef struct L1FrPtmNclRssiLrpDataTag
{
    Int8         numEntries;
    Int8         baInd;
    Int8         scellBaIndex;      /* set to l1ServingCellInfo_p->baIndex on entering SORT and Average mode */

    Int8         measIndStep;
    SignalBuffer measIndSignalBuf;
}
L1FrPtmNclRssiLrpData;


typedef struct L1FrPtmNclRssiDataTag
{
    /********************************************
     * Ncell RSSI results storage/processing data
     ********************************************/
    Int8                 nAccumList[MAX_BA_CHANNELS + 1]; /* number of measurements in RP */
    L1Rssi            rssiAccumList[MAX_BA_CHANNELS + 1]; /* rssi Accumulators */

    Int16                    sortedListNumEntries;
    L1FrPtmSortedListElement sortedList [MAX_BA_CHANNELS + 1];

    Int8              monitorRxLevel[MAX_BA_CHANNELS + 1]; /* RxLev not RSSI */


    /********************************************
     *  Ncell monitoring scheduling Data
     ********************************************/
    L1FrPtmNclRssiLrpData lrp;        /* Data required to generate GmphPtmMeasInd */

    L1BaListData  *lrpBaList;         /* last Report Period BA List need baInd, numEntries */
    L1BaListData  *crpBaList;

    Int8    scellMonOffset;          /* used to support 6 servCell monitors per multiframe if PC_MEAS_CHAN == FALSE */
    Boolean pcMeasChan;              /* Used to store changes to pcMeasChan in PTM, so updates can be performed on exiting */
#if defined (GPRS_PTM_NCL_LOG_PC_MEAS_SUPPORT)
    char    debugMsg[100];
    Int8    index;
#endif

    Int8    numEntries;             /* equal to crpBaList->numEntires, plus 1 if ServCell not in BaList */
    Int8    repeatRate;             /* range 6 - 32 */
    Int8    baMeasIndex;            /* only reset on new list */
    Int8    numRpMeasRequired;      /* set to nRpNumRpMeasRequired at end of RP */
    Int8    numRpMeasScheduled;     /* Number of measurements process in RP
                                     * Used to trigger gmphPtmMeasInd */
    Int8    nRpNumRpMeasRequired;   /* set on handling new BA List, Used at end of RP */

    ProcessMonitorMode  mode;       /* Only used in process Action function, but only set once
                                     * for each mode, during RP, so must be maintained through out
                                     */
}L1FrPtmNclRssiData;

#if !defined(DISABLE_GPRS_PTM_NCELL)

/* IN palce of L1NcellStatus which is currently in BG
 * needs to be moved into ISR really ?? */
typedef struct L1FrPtmNclStatusTag
{
    Boolean          active;       /* Set     on PEV_START_NCELL_ACTIVITY
                                    * Cleared on max attempts
                                    *         or success
                                    *         or PEV_STOP_NCELL_ACTIVITY
                                    */
#if defined(GPRS_PTM_NCL_SB_UNKNOWN_FN_OPT)
    Int8                attempts;    /* Support for multiple SB attempts, after FB to speed up Sync */
#endif
    CellInformation    *cellInfo_p;
    NcellPriority       priority;
    L1PtmNclAction      action;

    Int16               bcchMfFnOffset; /* 0 - 50 */
    Int16               subFrameQbo;    /*  */


}
L1FrPtmNclStatus;

/* After FB search, still will not have timing info, until such time
 * as SB decoded, this is because cells are not syncronised in GSM
 * */
typedef struct L1FrPtmNclFbCtrlDataTag
{
    Int8   instance;                     /* INVALID_INDEX if FB not pending/active */
    Int8   searchPtcchFramesSinceStart;  /* inc every search and PTCCH frame even if FB not scheduled
                                           * if FB search in progress, Set to 0 to restart FB search */
    Int8   startFrameOffset;

    Int16  slotsMonitored;
    Int16  framesMonitored;

    /* pointers TDSs for sequencer to use */
    FbTds *fbTds_p;

}
L1FrPtmNclFbCtrlData;

typedef struct L1FrPtmNclSbCtrlDataTag
{
    Int8    numPending;
    Int8    hpIndex;    /* index into orderedSbList, of high priority instance */
    Int8    orderedSbList[MAX_NUM_NCELL_INSTANCES];

    /* Data Required to spread processing over mulitiple Interrupts */
    Int8    sbListStartIndex;
    Int8    sbListStopIndex;
    Int8    searchFrameFn;    /* Range 0 - 51 Only */
    Int8    numCandidates;
    Int8    candidateList[L1_PTM_NCL_MAX_NUM_SB_CANDIDATES][2];

    Int16   minSchWindow;   /* associated to multislot class l1gpcfg.c */

}
L1FrPtmNclSbCtrlData;

typedef struct L1FrPtmNclIntCtrlDataTag
{
    Int8              newChanCfgCtrl;
    L1PtmSiIntMeasPrm newChanCfg;

    Boolean  intMeasNotPending;
    Boolean  searchFrameMonitored;  /* If true first set of measurements performed in search Frame */
    Int8     attempts;

    Int8     scellIndex;            /* if not INVALID_INDEX, perform interference measurements */
    Int8     tsMin;                 /* Generated in Bg. as defined in ********.1 - 05.08 */
    Int8     tsMax;                 /* as defined in ********.1 - 05.08, based on multi slot class */

    Int8     burst;                 /* used to index into rssiAccumList in process Action */
    Arfcn    arfcn;                 /* to schedule Interference burst on correct ARFCN, and correctRssi in process action */
    L1Rssi   rssiAccumList[L1_PTM_NCL_MAX_INT_BURSTS];
}
L1FrPtmNclIntCtrlData;


typedef enum L1FrPtmNclTaskIdsTag
{
    L1_PTM_NCL_ACT_FB,
    L1_PTM_NCL_ACT_SB,
    L1_PTM_NCL_ACT_INT,
    L1_PTM_NCL_ACT_NONE
}
L1FrPtmNclTaskIds;


typedef struct L1FrPtmNclTaskTag
{
    Int8                relSubFrameOffset;
    Int8                instance;
    Int16               startQbOffset;  /* Qbits */

    Int16               duration;       /* slots */
    PtmNclRxTdsSet     *rxTdsSet_p;

  /* Search start time - filled in by search sequencer */
    SignedInt16         startRefQbit;
    SignedInt32         startRefFrame;
}
L1FrPtmNclTask;

typedef struct L1FrPtmNclSchActionTag
{
    Int8              subFrameOffset; /* 4 slots */
    Int8              relSubFrameOffset;
    Int8              numScheduled;
    Int8              nextTask;       /* Used in L1FrPtmNclStartAction, must be maintained over interrupts */
    L1FrPtmNclTaskIds taskId;         /* All tasks must be of the same type per frame
                                       * Set to L1_PTM_NCL_ACT_NONE if no task pending */
    L1FrPtmNclTask    tasks[L1_PTM_NCL_MAX_TASKS];

    /* Result Processing - Set up in L1FrPtmNclStartAction */
    Int8    processResults1By52;
    Int8    processTaskIndex;
    void    (*processAction)(L1FrPtmNclTask *taskData);

}
L1FrPtmNclSchAction;

#endif  /*!DISABLE_GPRS_PTM_NCELL */

typedef struct L1FrPtmNcellDataTag
{
    /*
    ** This is the FB slot number at which an FB search could be started in
    ** search (and spare PTCCH) frames. If set to L1_PTM_INVALID_SLOT_NUMBER
    ** there isn't room to do an FB search.
    **
    ** We are guarenteed 1 slot in the search frame where no tarffic will
    ** occur. This slot is timeslot 5. This combined with the fact that we
    ** only ;-) need 9 timeslots means that the valid on-air timeslot values
    ** are 5, 6, 7, 0, 1, 2, 3, 4 and 5. When set to 5, 6 or 7 this is actually
    ** slots 5, 6 or 7 in the frame before the Search frame.
    */

    /*
    ** The number stored in startSlot shall be in the range 5, 6, 7, 8(0), 9(1),
    ** 10(2), 11(3), 12(4) or 13(5), real timeslots in brackets.
    */
    Int8                      startSlot;

    /*
    ** numSlots allowable range is 1 to 9 only.
    ** Only valid if startSlot != L1_PTM_INVALID_SLOT_NUMBER
    */
    Int8                      slotCount;

    /*
    ** Indicates whether the fbStart index has changed.
    */
    Boolean                   firstSlotChanged;


    /* RSSI Monitoring Related Data */
    L1FrPtmNclRssiData        rssiData;


#if !defined(DISABLE_GPRS_PTM_NCELL)
    Boolean                   subSystemActive; /* Used to ensure shutDown called once */
    /* BSIC Decode Related Data */
    Int8                      numActive;
    Int8                      bsicDecSchDuration;
    Int8                      bsicDecInstance;
    Int8                      highPriorityInstance; /* only 1 instance can be high priority */

    L1FrPtmNclStatus          ncellStatus[MAX_NUM_NCELL_INSTANCES]; /* MAX_NUM_NCELL_INSTANCES defined in l1evncl.h
                                                                     * move to l1cell.h ? */
    L1FrPtmNclFbCtrlData      fbCtrl;
    L1FrPtmNclSbCtrlData      sbCtrl;

    /* Interference Measurement Related Data */
    L1FrPtmNclIntCtrlData     intCtrl;

    /* Task Scheduler/ Processor */
    L1FrPtmNclSchAction       action;
#endif
} L1FrPtmNcellData;



/***************************************************************************
 * Global Variables
 **************************************************************************/

#define M_L1FrPtmNclSetNewTsMin() L1_PTM_DATA.ncellData.intCtrl.newChanCfgCtrl |= L1_PTM_NCL_INT_NEW_TS_MIN

/***************************************************************************
 *  Function Prototypes
 **************************************************************************/

void L1FrPtmNcellInit        (void);

Int8 L1FrPtmNextNcellMonitor (void);

void L1FrPtmNcellProcessMonitor (Int8               baIndex,
                                 Int8               numRpMeasScheduled,
                                 Boolean            monCompleted,
                                 L1BaCellData       *baList,
                                 RxTds              *rxTds);

void L1FrPtmNcellScheduler (void);

void L1FrPtmNclIntCellReselectionCfg (void);


#endif

/* END OF FILE */
