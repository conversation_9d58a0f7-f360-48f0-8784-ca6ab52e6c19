/*
 * Generated by asn1c-0.9.28 (http://lionet.info/asn1c)
 * From ASN.1 module "Ver2-ULP-Components"
 * 	found in "supl202.asn1"
 * 	`asn1c -gen-PER`
 */

#ifndef	_UmbCellInformation_H_
#define	_UmbCellInformation_H_


#include <asn_application.h>

/* Including external dependencies */
#include <BIT_STRING.h>
#include <NativeInteger.h>
#include <constr_SEQUENCE.h>

#ifdef __cplusplus
extern "C" {
#endif

/* UmbCellInformation */
typedef struct UmbCellInformation {
	BIT_STRING_t	 refSECTORID;
	long	 refMCC;
	long	 refMNC;
	long	 refBASELAT;
	long	 reBASELONG;
	long	 refWeekNumber;
	long	 refSeconds;
	/*
	 * This type is extensible,
	 * possible extensions are below.
	 */
	
	/* Context for parsing across buffer boundaries */
	asn_struct_ctx_t _asn_ctx;
} UmbCellInformation_t;

/* Implementation */
extern asn_TYPE_descriptor_t asn_DEF_UmbCellInformation;

#ifdef __cplusplus
}
#endif

#endif	/* _UmbCellInformation_H_ */
#include <asn_internal.h>
