..\obj_PMD2NONE/dsp_filters.o : \tavor\Arbel\src\dsp_filters.c
\tavor\Arbel\src\dsp_filters.c:
..\obj_PMD2NONE/dsp_filters.o : \3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\IPCCommFilters.h
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\IPCCommFilters.h:
..\obj_PMD2NONE/dsp_filters.o : \3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\global_types.h
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\global_types.h:
..\obj_PMD2NONE/dsp_filters.o : \3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\gbl_types.h
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\gbl_types.h:
..\obj_PMD2NONE/dsp_filters.o : \3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\xscale_types.h
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\xscale_types.h:
..\obj_PMD2NONE/dsp_filters.o : \3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\utils.h
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\utils.h:
..\obj_PMD2NONE/dsp_filters.o : \3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\global_types.h
\3g_ps\rls\tplgsm\bldstore\hsiupdlibdev\build\lib\hsiupdlibdev_BldIncCache\global_types.h:
..\obj_PMD2NONE/dsp_filters.o : \tavor\Arbel\obj_PMD2NONE\inc\DSP_Filter_file.h
\tavor\Arbel\obj_PMD2NONE\inc\DSP_Filter_file.h:
..\obj_PMD2NONE/dsp_filters.o : \tavor\Arbel\obj_PMD2NONE\inc\DSP_Cmd_Filter_file.h
\tavor\Arbel\obj_PMD2NONE\inc\DSP_Cmd_Filter_file.h:
