/*------------------------------------------------------------
(C) Copyright [2006-2016] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/
#include "bipIpSockets.h"
#include "uart.h"
#include "osa.h"
#include "netif_td_api.h"


#define BIPLOG(fmt, args...)  do{CPUartLogPrintf("BIP-"fmt, ##args);}while(0)

#if 0
BOOL MTIL_InitTcp(SOCKET_DESC * sock, unsigned short port)
{
	struct sockaddr_in addr;
	int optval = 1;

	*sock = socket(AF_INET, SOCK_STREAM, 0);
	if (*sock == -1) {
		BIPLOG("socket() error %d", MTIL_GetLastSocketError());
		return FALSE;
	}
	if ((setsockopt(*sock, SOL_SOCKET, SO_REUSEADDR, (const void *)&optval, sizeof optval)) < 0) {
		BIPLOG("setsockopt failed %d", MTIL_GetLastSocketError());
	}

	memset(&addr, 0, sizeof(addr));
	addr.sin_family = AF_INET;
	addr.sin_addr.s_addr = INADDR_ANY;
	addr.sin_port = htons(port);

	if (bind(*sock, (struct sockaddr *)&addr, sizeof(addr)) < 0) {
		BIPLOG("bind() error %d", MTIL_GetLastSocketError());
		return FALSE;
	}

	return TRUE;
}
#endif

void MTIL_CloseSocket(IN OUT SOCKET_DESC * sock, char *path)
{
	if (*sock != -1) {
		BIPLOG("MTIL_CloseSocket");
		closesocket(*sock);
	}
	*sock = -1;
}

BOOL MTIL_Accept(IN SOCKET_DESC mainSock, OUT SOCKET_DESC * sessionSock)
{
	struct sockaddr_in addr;

	int len;

	len = sizeof(addr);
	memset(&addr, 0, len);

	*sessionSock = accept(mainSock, (struct sockaddr *)&addr, (socklen_t *) & len);
	if (*sessionSock == -1) {
        BIPLOG("MTIL_Accept return false");
		return FALSE;
	}

	return TRUE;
}

BOOL MTIL_Listen(IN SOCKET_DESC mainSock, IN int backlog)
{
	if (listen(mainSock, backlog) == -1)
		return FALSE;

	return TRUE;
}

BOOL MTIL_Connect(IN SOCKET_DESC sock, struct sockaddr * addrTo)
{
	int size;
    int retry = 0;
    size = sizeof(struct sockaddr);

    do
    {
    	if ((connect(sock, addrTo, size)) == 0)
    		return TRUE;

        OSATaskSleep(40);
    }while(++retry < 10);

	return FALSE;

}

BOOL MTIL_RecvSock(SOCKET_DESC sock, char *rcvBuf, int *bufSize, long waitSec, struct sockaddr * from, int from_len)
{
	fd_set Fdset;
	int nfds, rcvBytes;

	if ((sock == -1) || (sock >= FD_SETSIZE))
		return FALSE;

	FD_ZERO(&Fdset);
	FD_SET(sock, &Fdset);

	if (waitSec != (long)0xffffffff) {
		struct timeval timev = { waitSec, 0 };

		if (!(nfds = select(((int)sock) + 1, &Fdset, NULL, NULL, &timev))) {
			*bufSize = 0;
			return TRUE;
		}

		if (nfds < 0) {
			BIPLOG("Select Error %d", MTIL_GetLastSocketError());

			*bufSize = 0;
			return FALSE;
		}
	}

	if (from) {
		if ((rcvBytes = recvfrom(sock, rcvBuf, *bufSize, 0, from, (socklen_t *) & from_len)) <= 0) {

			BIPLOG("Socket Error in recvfrom() %d", MTIL_GetLastSocketError());
			return FALSE;
		}
		BIPLOG("MTIL_RecvSock() recvfrom() return %d bytes", rcvBytes);
	} else {
		if ((rcvBytes = recv(sock, rcvBuf, *bufSize, 0)) <= 0) {

			BIPLOG("Socket Error in recv() %d", MTIL_GetLastSocketError());
			return FALSE;
		}
		BIPLOG("MTIL_RecvSock() recv return %d bytes", rcvBytes);
	}

	*bufSize = rcvBytes;

	return TRUE;
}

int MTIL_SelectReadMult(SOCKET_DESC * sock, int numSocks, long waitSec)
{
	fd_set Fdset;
	struct timeval timev = { waitSec, 0 };
	struct timeval *ptimev;
	int nfds;
	int i;
	int hi_fd = -1;

	FD_ZERO(&Fdset);

	for (i = 0; i < numSocks; i++) {
		FD_SET(sock[i], &Fdset);
		if ((int)sock[i] > hi_fd)
			hi_fd = (int)sock[i];
	}

	if (waitSec != (long)0xffffffff)
		ptimev = &timev;
	else
		ptimev = NULL;

	if (!(nfds = select(hi_fd + 1, &Fdset, NULL, NULL, ptimev)))
		return FALSE;

	if (nfds < 0) {
		BIPLOG("MTIL_SelectReadMult() Select Error %d", MTIL_GetLastSocketError());
		return FALSE;
	}

	i = 0;
	while (i < numSocks) {
		if (FD_ISSET(sock[i], &Fdset)) {
			return i + 1;
		}
		i++;
	}

	return 0;
}

BOOL MTIL_SendSock(SOCKET_DESC sock, char *sndBuf, int sndSize, struct sockaddr * to)
{
	int sent = 0;
	int lento = 0;

	lento = sizeof(struct sockaddr_in);

	BIPLOG("MTIL_SendSock() send %x with %d bytes lento=%d", sndBuf, sndSize, lento);

	while (sndSize) {
		if (to)
			sent = sendto(sock, sndBuf + sent, sndSize, 0, to, lento);
		else
			sent = send(sock, sndBuf + sent, sndSize, 0);

		if (sent < 0) {
			if (errno == EINTR)
				continue;

			BIPLOG("MTIL_SendSock() send %x with %d bytes lento=%d FAILED err=%d!!!", sndBuf, sndSize,
			      lento, MTIL_GetLastSocketError());
			return FALSE;
		} else {
			if (sent == 0) {
				BIPLOG("MTIL_SendSock() send zero bytes!!!");
			}
		}

		sndSize -= sent;
	}

	return TRUE;

}

int MTIL_GetLastSocketError()
{
	return errno;
}

#if 0
BOOL MTIL_SetSocketKeepAliveOpt(SOCKET_DESC sock)
{
	int optval = 1;
	if (setsockopt(sock, SOL_SOCKET, SO_KEEPALIVE, (const void *)&optval, sizeof(optval)) < 0) {
		//BIPLOG("MTIL_SetSocketKeepAliveOpt() set to %d KeepAlive option FAILED err=%d!!!", sock, MTIL_GetLastSocketError());
		return FALSE;
	}
	return TRUE;
}
#endif
