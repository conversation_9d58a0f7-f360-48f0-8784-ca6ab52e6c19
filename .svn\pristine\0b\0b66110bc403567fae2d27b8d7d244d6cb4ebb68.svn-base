#ifndef _GSENSOR_H
#define _GSENSOR_H

#ifdef __cplusplus
extern "C" {
#endif
#include "plat_basic_api.h"

#if defined(__XF_LCD_SIZE_240X280__) || defined(__XF_PRO_A0376__)
#define GSENSOR_I2C_INDEX	1
#else
#define GSENSOR_I2C_INDEX	0
#endif
#if defined(__XF_PRO_A0376__)
#define QMAX981_I2C_ADDR 0x12 /* 7-bit i2c addr */
#else
#define QMAX981_I2C_ADDR 0x13 /* 7-bit i2c addr */
#endif
#define	false	0
#define	true		1

#if defined(__XF_LCD_SIZE_240X280__) || defined(__XF_PRO_A0376__)
#define GRAVITY_EARTH_1000 				9807	// about (9.80665f)*1000   mm/s2
#define QMA6100P_ABS(X) 				((X) < 0 ? (-1 * (X)) : (X))

#define QMA6100P_DELAY				0xff
/*Register Map*/
#define QMA6100P_CHIP_ID		    0x00
#define QMA6100P_XOUTL				0x01
#define QMA6100P_XOUTH				0x02
#define QMA6100P_YOUTL				0x03
#define QMA6100P_YOUTH				0x04
#define QMA6100P_ZOUTL				0x05
#define QMA6100P_ZOUTH				0x06
#define QMA6100P_STEP_CNT_L			0x07
#define QMA6100P_STEP_CNT_M			0x08
#define QMA6100P_INT_STATUS_0		0x09
#define QMA6100P_INT_STATUS_1		0x0a
#define QMA6100P_INT_STATUS_2		0x0b
#define QMA6100P_INT_STATUS_3		0x0c
#define QMA6100P_STEP_CNT_H			0x0d
#define QMA6100P_FIFO_STATE			0x0e
#define QMA6100P_REG_RANGE			0x0f
#define QMA6100P_REG_BW_ODR			0x10
#define QMA6100P_REG_POWER_MANAGE	0x11
#define QMA6100P_STEP_SAMPLE_CNT	0x12
#define QMA6100P_STEP_PRECISION		0x13
#define QMA6100P_STEP_TIME_LOW		0x14
#define QMA6100P_STEP_TIME_UP		0x15
#define QMA6100P_INT_EN_0			0x16
#define QMA6100P_INT_EN_1			0x17
#define QMA6100P_INT_EN_2			0x18
#define QMA6100P_INT1_MAP_0			0x19
#define QMA6100P_INT1_MAP_1			0x1a
#define QMA6100P_INT2_MAP_0			0x1b
#define QMA6100P_INT2_MAP_1			0x1c

#define QMA6100P_INTPIN_CFG			0x20
#define QMA6100P_INT_CFG			0x21
#define QMA6100P_OS_CUST_X		    0x27
#define QMA6100P_OS_CUST_Y			0x28
#define QMA6100P_OS_CUST_Z			0x29

#define QMA6100P_REG_NVM			0x33
#define QMA6100P_REG_RESET			0x36


#define QMA6100P_DRDY_BIT			0x10	// enable 1

#define QMA6100P_AMD_X_BIT			0x01
#define QMA6100P_AMD_Y_BIT			0x02
#define QMA6100P_AMD_Z_BIT			0x04

typedef enum
{
	QMA6100P_DISABLE = 0,
	QMA6100P_ENABLE = 1
}qma6100p_enable;




typedef enum
{
	QMA6100P_BW_100 = 0,
	QMA6100P_BW_200 = 1,
	QMA6100P_BW_400 = 2,
	QMA6100P_BW_800 = 3,
	QMA6100P_BW_1600 = 4,
	QMA6100P_BW_50 = 5,
	QMA6100P_BW_25 = 6,
	QMA6100P_BW_12_5 = 7,
	QMA6100P_BW_OTHER = 8
}qma6100p_bw;

typedef enum
{
	QMA6100P_RANGE_2G = 0x01,
	QMA6100P_RANGE_4G = 0x02,
	QMA6100P_RANGE_8G = 0x04,
	QMA6100P_RANGE_16G = 0x08,
	QMA6100P_RANGE_32G = 0x0f
}qma6100p_range;

typedef enum
{
	QMA6100P_LPF_OFF = (0x00<<5),
	QMA6100P_LPF_1 = (0x04<<5),
	QMA6100P_LPF_2 = (0x01<<5),
	QMA6100P_LPF_4 = (0x02<<5),
	QMA6100P_LPF_8 = (0x03<<5),
	QMA6100P_LPF_RESERVED = 0xff
}qma6100p_nlpf;


typedef enum
{
	QMA6100P_HPF_DIV_OFF = (0x00<<5),
	QMA6100P_HPF_DIV_10 = (0x01<<5),
	QMA6100P_HPF_DIV_25 = (0x02<<5),
	QMA6100P_HPF_DIV_50 = (0x03<<5),
	QMA6100P_HPF_DIV_100 = (0x04<<5),
	QMA6100P_HPF_DIV_200 = (0x05<<5),
	QMA6100P_HPF_DIV_400 = (0x06<<5),
	QMA6100P_HPF_DIV_800 = (0x07<<5),
	QMA6100P_HPF_RESERVED = 0xff
}qma6100p_nhpf;


typedef enum
{
	QMA6100P_MODE_STANDBY = 0,
	QMA6100P_MODE_ACTIVE = 1,
	QMA6100P_MODE_MAX
}qma6100p_mode;

typedef enum
{
	QMA6100P_MCLK_102_4K = 0x03,
	QMA6100P_MCLK_51_2K = 0x04,
	QMA6100P_MCLK_25_6K = 0x05,
	QMA6100P_MCLK_12_8K = 0x06,
	QMA6100P_MCLK_6_4K = 0x07,
	QMA6100P_MCLK_RESERVED = 0xff
}qma6100p_mclk;

typedef enum
{
	QMA6100P_STEP_LPF_0 = (0x00<<6),
	QMA6100P_STEP_LPF_2 = (0x01<<6),
	QMA6100P_STEP_LPF_4 = (0x02<<6),
	QMA6100P_STEP_LPF_8 = (0x03<<6),
	QMA6100P_STEP_LPF_RESERVED = 0xff
}qma6100p_step_lpf;

typedef enum
{
	QMA6100P_STEP_AXIS_ALL = 0x00,
	QMA6100P_STEP_AXIS_YZ = 0x01,
	QMA6100P_STEP_AXIS_XZ = 0x02,
	QMA6100P_STEP_AXIS_XY = 0x03,
	QMA6100P_STEP_AXIS_RESERVED = 0xff
}qma6100p_step_axis;

typedef enum
{
	QMA6100P_STEP_START_0 = 0x00,
	QMA6100P_STEP_START_4 = 0x20,
	QMA6100P_STEP_START_8 = 0x40,
	QMA6100P_STEP_START_12 = 0x60,
	QMA6100P_STEP_START_16 = 0x80,
	QMA6100P_STEP_START_24 = 0xa0,
	QMA6100P_STEP_START_32 = 0xc0,
	QMA6100P_STEP_START_40 = 0xe0,
	QMA6100P_STEP_START_RESERVED = 0xff
}qma6100p_step_start_cnt;


typedef enum
{
	QMA6100P_TAP_SINGLE = 0x80,
	QMA6100P_TAP_DOUBLE = 0x20,
	QMA6100P_TAP_TRIPLE = 0x10,
	QMA6100P_TAP_QUARTER = 0x01,
	QMA6100P_TAP_MAX = 0xff
}qma6100p_tap;

#endif

typedef enum
{
	QMAX981_TYPE_UNKNOW,
	QMAX981_TYPE_6981,
	QMAX981_TYPE_7981,
	QMAX981_TYPE_6100,
	QMAX981_TYPE_MAX
}qmaX981_chip_type;

enum
{
	QMAX981_AXIS_X     =     0,
	QMAX981_AXIS_Y     =     1,
	QMAX981_AXIS_Z     =     2,
	QMAX981_AXES_NUM   =     3
};

struct hwmsen_convert {
	short sign[4];
	short map[4];
};

struct qmaX981_data
{
	uint8_t			chip_id;
	qmaX981_chip_type	chip_type;	
	uint8_t			layout;
	uint16_t			lsb_1g;					
	uint32_t			step;
#if defined(QMAX981_USE_INT1) 
	uint8_t			int1_no;
	uint8_t			int1_level;
#endif
};

/******************************************************
	Motion sensor controller register macro define
*******************************************************/
//#define QMAX981_HAND_LIGHT
#define QMAX981_STEPCOUNTER
//#define QMA7981_HAND_UP_DOWN
//#define QMA7981_ANY_MOTION
//#define QMA7981_SIGNIFICANT_MOTION
//#define QMA7981_NO_MOTION
//#define QMA7981_HAND_UP_DOWN
//#define QMA7981_INT_LATCH

#if defined(QMAX981_STEPCOUNTER)
//#define QMAX981_STEP_COUNTER_USE_INT		// for qma6981
#define QMAX981_CHECK_ABNORMAL_DATA
#endif

/***********QST TEAM***************************************/
#define GRAVITY_1G			9807

#define QMAX981_RANGE_2G        (1<<0)
#define QMAX981_RANGE_4G        (1<<1)
#define QMAX981_RANGE_8G        (1<<2)
#define QMAX981_RANGE_16G       (1<<3)
#define QMAX981_RANGE_32G       0x0f
	
#if defined(QMAX981_STEPCOUNTER)
#define QMAX981_OFFSET_X		0x60
#define QMAX981_OFFSET_Y		0x60
#define QMAX981_OFFSET_Z		0x60
#else
#define QMAX981_OFFSET_X		0x00
#define QMAX981_OFFSET_Y		0x00
#define QMAX981_OFFSET_Z		0x00
#endif
	
#define QMAX981_CHIP_ID			0x00
#define QMAX981_XOUTL			0x01	// 4-bit output value X
#define QMAX981_XOUTH			0x02	// 6-bit output value X
#define QMAX981_YOUTL			0x03	
#define QMAX981_YOUTH			0x04	
#define QMAX981_ZOUTL			0x05	
#define QMAX981_ZOUTH			0x06
#define QMAX981_STEP_CNT_L		0x07
	
#define QMAX981_RANGE			0x0f
#define QMAX981_ODR				0x10
#define QMAX981_MODE			0x11
	
#define QMAX981_INT_MAP0		0x19	// INT MAP
#define QMAX981_INT_STAT		0x0a    //interrupt statues
	
#define QMAX981_FIFO_WTMK		0x31	// FIFO water mark level
#define QMAX981_FIFO_CONFIG		0x3e	// fifo configure
#define QMAX981_FIFO_DATA		0x3f	//fifo data out 
	
#define SINGLE_TAP 1
#define DOUBLE_TAP 2


int qmax981_init(void);

bool Gsensor_I2C_BufferRead(u8 ReadAddr, u8* pBuffer, u16 NumByteToRead);

int Gsensor_I2C_ByteWrite(u8 WriteAddr,u8 pBuffer);


uint32_t qmax981_pedometer_sensor_get_step(void);

int qmaX981_check_abnormal_data(int data_in, int *data_out);

void qmax981_custom_reset_step(void);

int QMAX981_read_rawdata(int16_t *x, int16_t *y, int16_t *z);
unsigned char qmaX981_irq_hdlr(void);

#ifdef __cplusplus
} /* extern "C" */
#endif

#endif /* _GSENSOR_H */
