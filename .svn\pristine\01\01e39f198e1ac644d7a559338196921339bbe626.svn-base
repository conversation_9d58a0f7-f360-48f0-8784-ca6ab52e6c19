/******************************************************************************
 * * desktop_key_guard.c - implementation of key guard functions
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#include "desktop_global.h"

/**
 * Callback function for additional actions after Creating Desktop_Unlock interface
 * param (in) Ctrl: VOID *
 * return  VOID
 */
VOID Framework_Desktop_Unlock_OnCreate(VOID *Ctrl)
{
    UI_Desktop_Unlock_Desc_t *DesktopUnlockDesc = (UI_Desktop_Unlock_Desc_t *)Ctrl;
    hal_rtc_t                TimeAndDate;
    INT8                     TimeStr[13] = { 0 };
    INT8                     DateStr[11] = { 0 };

    if (FRAMEWORK_TIME_SHOW == g_FrameworkMng.TimeFormat.TimeDisplay)
    {
        Hal_Rtc_Gettime(&TimeAndDate);
        Framework_Change_Time_And_Date_To_String(&TimeAndDate, TimeStr, DateStr);
        lv_label_set_text(DesktopUnlockDesc->ContLabel[0].Label, TimeStr);
        lv_label_set_text(DesktopUnlockDesc->ContLabel[1].Label, DateStr);
    }

    if ((TRUE == g_FrameworkMng.AutoGuardOn)
        && (TRUE == g_FrameworkMng.StandbyScreenMode))
    {
        g_FrameworkMng.KeyGuardTimer = 0;
    }

    g_FrameworkMng.KeyGuardLock = TRUE;
}


/**
 * Callback function for restoring state on Desktop_Unlock interface after backward from another activity
 * param (in) Ctrl: VOID *
 * return  VOID
 */
VOID Framework_Desktop_Unlock_OnRestoreState(VOID *Ctrl)
{
    hal_rtc_t                TimeAndDate;
    INT8                     TimeStr[13]        = { 0 };
    INT8                     DateStr[11]        = { 0 };
    UI_Desktop_Unlock_Desc_t *DesktopUnlockDesc = (UI_Desktop_Unlock_Desc_t *)Ctrl;
    Setting_Wall_Paper_t     *SettingWallPaper;
    lv_obj_t                 *Img;

    Setting_Get_Wall_Paper_Req(&SettingWallPaper);
    if (SettingWallPaper != DesktopUnlockDesc->Wall_Paper_Img)
    {
        DesktopUnlockDesc->Wall_Paper_Img = SettingWallPaper;

        Img = lv_obj_get_parent(DesktopUnlockDesc->ContLabel[0].Label);
        lv_img_set_src(Img, SettingWallPaper);
    }

    if (FRAMEWORK_TIME_SHOW == g_FrameworkMng.TimeFormat.TimeDisplay)
    {
        Hal_Rtc_Gettime(&TimeAndDate);
        Framework_Change_Time_And_Date_To_String(&TimeAndDate, TimeStr, DateStr);
    }
    lv_label_set_text(DesktopUnlockDesc->ContLabel[0].Label, TimeStr);
    lv_label_set_text(DesktopUnlockDesc->ContLabel[1].Label, DateStr);

    if ((TRUE == g_FrameworkMng.AutoGuardOn)
        && (TRUE == g_FrameworkMng.StandbyScreenMode))
    {
        g_FrameworkMng.KeyGuardTimer = 0;
    }
}


/**
 * Confirmation for checking security key guard in setting module
 * param (in) VOID
 * return  VOID
 */
VOID Framework_Check_Security_Keyguard_Cnf(SETTING_RESULT Result)
{
    if (SETTING_OK != Result)
    {
        return;
    }

    g_FrameworkMng.KeyGuardLock = FALSE;
    Nav_Home(ACT_ID_ANY);
}


/**
 * Callback function for handling key pressed on Desktop_Unlock interface
 * param (in) Obj: lv_obj_t *
 * param (in) Key_Sta: UI_KEY_STATUS
 * param (in) Key_Val: UI_KEY_VALUE
 * return  UINT8
 */
UINT8 Framework_Desktop_Unlock_Key_Cb(lv_obj_t *Obj, UI_KEY_STATUS Key_Sta, UI_KEY_VALUE Key_Val)
{
    if (KEY_RELEASED == Key_Sta)
    {
        switch (Key_Val)
        {
        case KEY_SOFTLEFT:
            if (TRUE == g_FrameworkMng.KeyguardCodeOn)
            {
                Setting_Check_Security_Keyguard_Req(Framework_Check_Security_Keyguard_Cnf);
            }
            else
            {
                g_FrameworkMng.KeyGuardLock = FALSE;
                Nav_Home(ACT_ID_ANY);
            }
            break;

        case KEY_END:
            if ((TRUE == g_FrameworkMng.AutoGuardOn)
                && (TRUE == g_FrameworkMng.StandbyScreenMode))
            {
                Framework_Create_Key_Guard();
            }
            break;

        default:
            break;
        } /* switch */
    }

    return(1);
}


/**
 * Create Desktop_Unlock interface
 * param (in) VOID
 * return  VOID
 */
VOID Framework_Create_Desktop_Unlock(VOID)
{
    UI_Desktop_Unlock_Desc_t *DesktopUnlockDesc;
    Nav_Func_List_t          FuncList;

    UI_Log_Out(UI_SUB_MODULE_FRAMEWORK, UI_LOG_LEVEL_TRACE_1, "Framework_Create_Desktop_Unlock\n");

    DesktopUnlockDesc = (UI_Desktop_Unlock_Desc_t *)Hal_Mem_Alloc(sizeof(UI_Desktop_Unlock_Desc_t));
    Hal_Mem_Set(DesktopUnlockDesc, 0, sizeof(UI_Desktop_Unlock_Desc_t));

    Setting_Get_Wall_Paper_Req(&DesktopUnlockDesc->Wall_Paper_Img);

    DesktopUnlockDesc->ContLabel[0].TxtId      = LV_LANG_TXT_ID_NONE;
    DesktopUnlockDesc->ContLabel[0].TxtAlign   = LV_LABEL_ALIGN_CENTER;
    DesktopUnlockDesc->ContLabel[0].LabelAlign = LV_ALIGN_IN_TOP_LEFT;
    DesktopUnlockDesc->ContLabel[0].TxtFont    = LV_FONT_PTR_DRIODSANSFALLBACKFULL_SMALL;
    DesktopUnlockDesc->ContLabel[1].TxtId      = LV_LANG_TXT_ID_NONE;
    DesktopUnlockDesc->ContLabel[1].TxtAlign   = LV_LABEL_ALIGN_CENTER;
    DesktopUnlockDesc->ContLabel[1].LabelAlign = LV_ALIGN_OUT_BOTTOM_LEFT;
    DesktopUnlockDesc->ButtonBar.ButtonL.Valid = TRUE;
    DesktopUnlockDesc->ButtonBar.ButtonL.TxtId = PHONE_TEXT_ID_UNLOCK;
    DesktopUnlockDesc->KeyFunc                 = Framework_Desktop_Unlock_Key_Cb;

    Hal_Mem_Set(&FuncList, 0, sizeof(Nav_Func_List_t));
    FuncList.OnShow         = UI_Desktop_Unlock_Create;
    FuncList.OnCreate       = Framework_Desktop_Unlock_OnCreate;
    FuncList.OnRestoreState = Framework_Desktop_Unlock_OnRestoreState;
    FuncList.OnDestroy      = Framework_Interface_OnDestroy;

    Nav_Forward(ACT_ID_ANY, ACT_ID_DESKTOP_UNLOCK, &FuncList, DesktopUnlockDesc);
} /* Framework_Create_Desktop_Unlock */


/**
 * Callback function for additional actions after Creating Key_Guard interface
 * param (in) Ctrl: VOID *
 * return  VOID
 */
VOID Framework_Key_Guard_OnCreate(VOID *Ctrl)
{
    UI_FullScreen_Key_Guard_Desc_t *Interface = ((UI_FullScreen_Key_Guard_Desc_t *)Ctrl);
    hal_rtc_t                      TimeAndDate;
    INT8                           TimeStr[13] = { 0 };
    INT8                           DateStr[11] = { 0 };

    if (FRAMEWORK_TIME_SHOW == g_FrameworkMng.TimeFormat.TimeDisplay)
    {
        Hal_Rtc_Gettime(&TimeAndDate);
        Framework_Change_Time_And_Date_To_String(&TimeAndDate, TimeStr, DateStr);
        lv_label_set_text(Interface->TimeLabel.Label, TimeStr);
        lv_label_set_text(Interface->DateLabel.Label, DateStr);
    }

    if (TRUE == g_FrameworkMng.UnreadMissedCallFlg)
    {
        lv_img_set_src(Interface->SymbolImg[0].Img, SYMBOL_CALL_MISSED_L);
    }
    if (TRUE == g_FrameworkMng.UnreadSmsFlg)
    {
        lv_img_set_src(Interface->SymbolImg[1].Img, SYMBOL_UNREAD_SMS_L);
        lv_obj_align(Interface->SymbolImg[1].Img, Interface->SymbolImg[0].Img,
                     LV_ALIGN_OUT_RIGHT_MID, 2, 0);
    }
    lv_obj_align(lv_obj_get_parent(Interface->SymbolImg[1].Img), Interface->DateLabel.Label,
                 LV_ALIGN_OUT_BOTTOM_MID, 0, 10);

    g_FrameworkMng.KeyGuardLock = TRUE;
}


/**
 * Callback function for restoring state on Key_Guard interface after backward from another activity
 * param (in) Ctrl: VOID *
 * return  VOID
 */
VOID Framework_Key_Guard_OnRestoreState(VOID *Ctrl)
{
    Nav_Home(ACT_ID_ANY);
}


/**
 * Callback function for handling key pressed on Key_Guard interface
 * param (in) Obj: lv_obj_t *
 * param (in) Key_Sta: UI_KEY_STATUS
 * param (in) Key_Val: UI_KEY_VALUE
 * return  UINT8
 */
UINT8 Framework_Key_Guard_Key_Cb(lv_obj_t *Obj, UI_KEY_STATUS Key_Sta, UI_KEY_VALUE Key_Val)
{
    if (KEY_RELEASED == Key_Sta)
    {
        switch (Key_Val)
        {
        case KEY_END:
            Nav_Home(ACT_ID_ANY);
            break;

        default:
            break;
        } /* switch */
    }

    return(1);
}


/**
 * Create Key_Guard interface
 * param (in) VOID
 * return  VOID
 */
VOID Framework_Create_Key_Guard(VOID)
{
    UI_FullScreen_Key_Guard_Desc_t *FullScrnKeyGuardDesc;
    Nav_Func_List_t                FuncList;

    FullScrnKeyGuardDesc =
        (UI_FullScreen_Key_Guard_Desc_t *)Hal_Mem_Alloc(sizeof(UI_FullScreen_Key_Guard_Desc_t));
    Hal_Mem_Set(FullScrnKeyGuardDesc, 0, sizeof(UI_FullScreen_Key_Guard_Desc_t));

    FullScrnKeyGuardDesc->TimeLabel.TxtId        = LV_LANG_TXT_ID_NONE;
    FullScrnKeyGuardDesc->TimeLabel.TxtAlign     = LV_LABEL_ALIGN_CENTER;
    FullScrnKeyGuardDesc->TimeLabel.LabelAlign   = LV_ALIGN_IN_TOP_LEFT;
    FullScrnKeyGuardDesc->TimeLabel.TxtFont      = LV_THEME_SIMPLE_FONT_SMALL;
    FullScrnKeyGuardDesc->DateLabel.TxtId        = LV_LANG_TXT_ID_NONE;
    FullScrnKeyGuardDesc->DateLabel.TxtAlign     = LV_LABEL_ALIGN_CENTER;
    FullScrnKeyGuardDesc->DateLabel.LabelAlign   = LV_ALIGN_OUT_BOTTOM_LEFT;
    FullScrnKeyGuardDesc->KeyFunc                = Framework_Key_Guard_Key_Cb;

    Hal_Mem_Set(&FuncList, 0, sizeof(Nav_Func_List_t));
    FuncList.OnShow         = UI_FullScreen_Key_Guard_Create;
    FuncList.OnCreate       = Framework_Key_Guard_OnCreate;
    FuncList.OnRestoreState = Framework_Key_Guard_OnRestoreState;
    FuncList.OnDestroy      = Framework_Interface_OnDestroy;
    FuncList.OnSaveState    = Framework_Interface_OnSaveState;

    Nav_Forward(ACT_ID_ANY, ACT_ID_KEY_GUARD, &FuncList, FullScrnKeyGuardDesc);
}


/**
 * Create keyguard or unlock interface
 * param (in) VOID
 * return  VOID
 */
VOID Framework_Keyguard_Or_Unlock(VOID)
{
    UI_Log_Out(UI_SUB_MODULE_FRAMEWORK, UI_LOG_LEVEL_TRACE_1,
               "%s: StandbyScreenMode %d\n", __FUNCTION__, g_FrameworkMng.StandbyScreenMode);

    if (TRUE == g_FrameworkMng.StandbyScreenMode)
    {
        Framework_Create_Key_Guard();
    }
    else
    {
        Framework_Create_Desktop_Unlock();
    }
}


/**
 * Check keyguard timer
 * param (in) VOID
 * return  VOID
 */
VOID Framework_Check_Keyguard_Timer(VOID)
{
    ACTIVITY_ID ActId;

    if (TRUE == g_FrameworkMng.AutoGuardOn)
    {
        ActId = Nav_Get_Top();
        if ((ACT_ID_DESKTOP == ActId)
            || (ACT_ID_DESKTOP_MISSED_CALL == ActId)
            || (ACT_ID_DESKTOP_UNREAD_SMS == ActId)
            || ((ACT_ID_DESKTOP_UNLOCK == ActId)
                && (TRUE == g_FrameworkMng.StandbyScreenMode)))
        {
            if (g_FrameworkMng.AutoGuardDelay <= g_FrameworkMng.KeyGuardTimer)
            {
                Framework_Keyguard_Or_Unlock();
            }
        }
    }
}
