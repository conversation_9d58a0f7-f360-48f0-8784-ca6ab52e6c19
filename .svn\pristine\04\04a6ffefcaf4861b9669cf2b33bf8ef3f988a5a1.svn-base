/**********************************************************************
*
* Filename: pl_ms_ecf_hsdpa.h
*
* Programmers: <PERSON><PERSON> (yalva)
*
* Description: serves the ECF_HSDPA mode
*
* --------------------------------------------------------------------
* Revision History
*
* Date         Who        Version           Description
* --------------------------------------------------------------------
* 09-02-2010  Yalva        0.01              File Created
**********************************************************************/
#ifndef PL_MS_ECF_HSDPA_H
#define PL_MS_ECF_HSDPA_H

#include "pl_w_globs.h"
#include "aplp_config.h"
#include "pl_mode.h"
/**********************************************************************
*
* ECF-HSDPA states enum
*
**********************************************************************/
//ICAT EXPORTED ENUM
typedef enum
{
    ECF_HSDPA_SET_UP_STATE,
    ECF_HSDPA_RESELECTION_STATE,
    ECF_HSDPA_NORMAL_STATE,
    ECF_HSDPA_TERMINATE_STATE,
    ECF_HSDPA_RECONFIG_TO_DPCH,
    MAX_ECF_HSDPA_STATES,
    ECF_HSDPA_MODE_NOT_ACTIVE
}ECF_HSDPA_States;

extern state_descriptor *ecf_hsdpa_states[];

VOID    plMsReleaseEcfHsdpaAfterIfrMeasHandling (void);
void 	plMsReleaseEcfHsdpaAfterCloseFO(void);
VOID    plMsReleaseEcfHsdpaAfterIfrMeasHandlingWaitForIfr (void);
VOID  	plMsReleaseEcfHsdpaAfterIfrMeasHandlingWaitForXfn(void);
#if defined (L1_UPGRADE_R8)
VOID plMsEcfHsdpaSetFoState (foIsActive_te);

#endif
#endif /* PL_MS_ECF_HSDPA_H */
