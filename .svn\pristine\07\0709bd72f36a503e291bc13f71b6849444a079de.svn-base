/*------------------------------------------------------------
(C) Copyright [2006-2008] Marvell International Ltd.
All Rights Reserved
------------------------------------------------------------*/


/*--------------------------------------------------------------------------------------------------------------------
INTEL CONFIDENTIAL
Copyright 2006 Intel Corporation All Rights Reserved.
The source code contained or described herein and all documents related to the source code ("Material") are owned
by Intel Corporation or its suppliers or licensors. Title to the Material remains with Intel Corporation or
its suppliers and licensors. The Material contains trade secrets and proprietary and confidential information of
Intel or its suppliers and licensors. The Material is protected by worldwide copyright and trade secret laws and
treaty provisions. No part of the Material may be used, copied, reproduced, modified, published, uploaded, posted,
transmitted, distributed, or disclosed in any way without Intel's prior express written permission.

No license under any patent, copyright, trade secret or other intellectual property right is granted to or
conferred upon you by disclosure or delivery of the Materials, either expressly, by implication, inducement,
estoppel or otherwise. Any license under such intellectual property rights must be express and approved by
Intel in writing.
-------------------------------------------------------------------------------------------------------------------*/

/**********************************************************************
 * Filename: pl_w_types.h
 *
 * Description: Typesused in WB sa as well as in DR
 ***************************************************************************************************/
#ifndef PL_W_TYPES_H
#define PL_W_TYPES_H

#include "cmmn_dfs.h"

//ICAT EXPORTED ENUM
typedef enum
{
  NULL_RAT =0,
  WCDMA_RAT,
  TDSCDMA_RAT,
  GSM_RAT,
  LTE_RAT,
  ALL_RAT,
} initialRat_te;

#ifndef RATSETCAUSE
#define RATSETCAUSE
//ICAT EXPORTED ENUM
typedef enum RatSetCauseTag
{
	RAT_CAUSE_NULL,
	POWER_ON_ON_GSM,
	POWER_ON_ON_TD,
	DRAT_HO_TD_TO_GSM,
	DRAT_HO_TD_TO_GSM_FAIL,// 4
	DRAT_HO_GSM_TO_TD,
	DRAT_HO_GSM_TO_TD_FAIL,
	DRAT_RESEL_GSM_TO_TD,
	DRAT_RESEL_GSM_TO_TD_FAIL,
	DRAT_CCO_TD_DCH_TO_GSM,// 9
	DRAT_CCO_TD_DCH_TO_GSM_FAIL,
	DRAT_RESEL_TD_IDLE_TO_GSM,
	DRAT_RESEL_TD_IDLE_TO_GSM_FAIL,
	DRAT_RESEL_TD_FACH_TO_GSM,
	DRAT_RESEL_TD_FACH_TO_GSM_FAIL,// 14
	DRAT_SWITCH_TD_TO_GSM,
	DRAT_SWITCH_GSM_TO_TD,
	PLMN_SEARCH_IN_TD_GSM_BCCH_DECODE,
	PLMN_SEARCH_IN_TD_BACK_TO_TD,
	PLMN_SEARCH_IN_GSM_TD_BCH_DECODE,// 19
	PLMN_SEARCH_IN_GSM_BACK_TO_GSM,
	SWITCH_TO_TD_ATTER_GSM_TERMINATE,
	DRAT_SET_FROM_GPLC,
    POWER_ON_LTE,// 23
    /* ********** - Update IRAT feature - begin */
#if defined (UPGRADE_LTE)
    IRAT_HO_LTE_TO_TD,//24
    IRAT_HO_LTE_TO_TD_FAIL,
    IRAT_HO_TD_TO_LTE,
    IRAT_HO_TD_TO_LTE_FAIL,

    IRAT_RESEL_LTE_TO_TD,
    IRAT_RESEL_LTE_TO_TD_FAIL,//29
    IRAT_RESEL_TD_TO_LTE,
    IRAT_RESEL_TD_TO_LTE_FAIL,

    PLMN_SEARCH_IN_LTE_TD_BCCH_DECODE,
    PLMN_SEARCH_IN_TD_LTE_BCCH_DECODE,

    DRAT_RESEL_GSM_TO_LTE,//34
    DRAT_RESEL_GSM_TO_LTE_FAIL,

    DRAT_RESEL_LTE_TO_GSM,
    DRAT_RESEL_LTE_TO_GSM_FAIL,

    PLMN_SEARCH_IN_TD_LTE_BACK_TO_TD,
    PLMN_SEARCH_IN_GSM_LTE_BCCH_DECODE,//39
    PLMN_SEARCH_IN_GSM_LTE_BACK_TO_GSM,
    PLMN_SEARCH_IN_LTE_GSM_BCCH_DECODE,
    PLMN_SEARCH_IN_LTE_GSM_BACK_TO_LTE,
    PLMN_SEARCH_IN_LTE_TD_BACK_TO_LTE,
#endif
    /*Add by qhli begin*/
    IRAT_HO_LTE_TO_WB = 44,//44
    IRAT_HO_LTE_TO_WB_FAIL,
    IRAT_HO_WB_TO_LTE,
    IRAT_HO_WB_TO_LTE_FAIL,
        
    IRAT_RESEL_LTE_TO_WB,//48
    IRAT_RESEL_LTE_TO_WB_FAIL,
    IRAT_RESEL_WB_TO_LTE,
    IRAT_RESEL_WB_TO_LTE_FAIL,
    IRAT_REDIR_WB_TO_LTE_FAIL,//CQ65927 for WB Redir To Lte Fail issue
        
    PLMN_SEARCH_IN_LTE_WB_BCCH_DECODE,
    PLMN_SEARCH_IN_WB_LTE_BCCH_DECODE,
    PLMN_SEARCH_IN_LTE_WB_BACK_TO_LTE,
    PLMN_SEARCH_IN_WB_LTE_BACK_TO_WB,
    /*Add by qhli end*/

    //IRAT_RESEL_WB_TO_LTE,
    //PLMN_SEARCH_IN_WB_LTE_BCCH_DECODE,
    //PLMN_SEARCH_IN_WB_LTE_BACK_TO_WB,
	//IRAT_RESEL_LTE_TO_WB_FAIL,

    /* ********** - Update IRAT feature - end */

    //CQ00035825, Add IRAT CGI cause, start
    IRAT_WB_CGI_IN_LTE,     //57
    IRAT_WB_CGI_BACK_TO_LTE,
    IRAT_GSM_CGI_IN_LTE,
    IRAT_GSM_CGI_BACK_TO_LTE,
    IRAT_TD_CGI_IN_LTE,
    IRAT_TD_CGI_BACK_TO_LTE,
    //CQ00035825, Add IRAT CGI cause, end

    //CQ35801 w/g iRat Casue, start
    PLMN_SEARCH_IN_WB_GSM_BCCH_DECODE,
    PLMN_SEARCH_IN_WB_GSM_BACK_TO_WB,
    PLMN_SEARCH_IN_GSM_WB_BCCH_DECODE,
    PLMN_SEARCH_IN_GSM_WB_BACK_TO_GSM,
    IRAT_HO_WB_TO_GSM,
    IRAT_HO_WB_TO_GSM_FAIL,
    IRAT_HO_GSM_TO_WB,
    IRAT_HO_GSM_TO_WB_FAIL,
    IRAT_RESEL_GSM_TO_WB,
    IRAT_RESEL_GSM_TO_WB_FAIL,
    IRAT_RESEL_WB_TO_GSM,
    IRAT_RESEL_WB_TO_GSM_FAIL,
    IRAT_SWITCH_WB_TO_GSM,
    IRAT_SWITCH_GSM_TO_WB,    
    //CQ35801 w/g iRat Casue, end
    POWER_ON_WB,// CQ42646 for CQ42639
    /*Modify for CQ00054259  by qhli begin*/
    IRAT_SWITCH_WB_TO_LTE,
    IRAT_SWITCH_LTE_TO_WB,
    /*Modify for CQ00054259 by qhli end*/

    /* Add for LTE/GSM handover IRAT case */
    IRAT_HO_LTE_TO_GSM,
    IRAT_HO_LTE_TO_GSM_FAIL,
    IRAT_HO_GSM_TO_LTE,
    IRAT_HO_GSM_TO_LTE_FAIL,

    LTE_IPC_RECEIVED_IN_NON_LTE_MODE,
    LTE_CSFB_GSM,/*CQ00079576 add, value = 84*/
    GSM_PWROFF_TO_RESETMODE,
    RAT_CAUSE_NUM//44
}RatSetCause;
#endif

//CQ 29015 begin
#define IPC_DSP_INIT_MODE           0x00
#define IPC_DSP_TDSCDMA_MODE        0x40
#define IPC_DSP_GSM_MODE        	0x50
#define IPC_DSP_LTE_TDD_MODE        0x60
#define IPC_DSP_LTE_FDD_MODE        0x70
#define IPC_DSP_WCDMA_MODE        	0x80 //cq28462, temp value by qhli for coding.
//CQ 29015 end

initialRat_te gwiL1GetRAT(UINT8 simID);
void plAMSetRAT(initialRat_te,UINT8 simID);
Bool gwiL1GetRealGSMCnf(void);


VOID plAMSetRfInStandby(BOOL flag);
BOOL plAMGetRfInStandby(void);

typedef enum multiIratModeOperOwnerTag
{
  OPERATE_BY_APLP =0,
  OPERATE_BY_HAW,
  OPERATE_BY_L1C,
  OPERATE_BY_L1A,
  OPERATE_OWNER_INVALID = 0xFF
} multiIratModeOperOwner;


#endif /* PL_W_TYPES_H */
