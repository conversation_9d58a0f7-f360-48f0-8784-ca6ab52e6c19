/**
 * This file has no copyright assigned and is placed in the Public Domain.
 * This file is part of the mingw-w64 runtime package.
 * No warranty is given; refer to the file DISCLAIMER.PD within this package.
 */
#ifndef _INC_MSDRMDEFS
#define _INC_MSDRMDEFS

typedef ULONG DRMHANDLE;
typedef ULONG DRMPUBHANDLE;
typedef ULONG DRMHSESSION;
typedef ULONG DRMENVHANDLE;
typedef ULONG DRMQUERYHANDLE;

typedef enum _DRM_STATUS_MSG {
  DRM_MSG_ACTIVATE_MACHINE = 0,
  DRM_MSG_ACTIVATE_GROUPIDENTITY,
  DRM_MSG_ACQUIRE_LICENSE,
  DRM_MSG_ACQUIRE_ISSUANCE_LICENSE_TEMPLATE,
  DRM_MSG_ACQUIRE_ADVISORY,
  DRM_MSG_SIGN_ISSUANCE_LICENSE,
  DRM_MSG_ACQUIRE_CLIENTLICENSOR 
} DRM_STATUS_MSG;

typedef enum _DRMGLOBALOPTIONS {
  DRMGLOBALOPTIONS_USE_WINHTTP                   = 0x00,
  DRMGL<PERSON><PERSON><PERSON>OPTIONS_USE_SERVERSECURITYPROCESSOR   = 0x01 
} DRMGLOBALOPTIONS;

typedef enum _DRM_DISTRIBUTION_POINT_INFO {
  DRM_DISTRIBUTION_POINT_LICENSE_ACQUISITION = 0,
  DRM_DISTRIBUTION_POINT_PUBLISHING,
  DRM_DISTRIBUTION_POINT_REFERRAL_INFO 
} DRM_DISTRIBUTION_POINT_INFO;

typedef enum _DRM_USAGEPOLICY_TYPE {
  DRM_USAGEPOLICY_TYPE_BYNAME = 0,
  DRM_USAGEPOLICY_TYPE_BYPUBLICKEY,
  DRM_USAGEPOLICY_TYPE_BYDIGEST,
  DRM_USAGEPOLICY_TYPE_OSEXCLUSION 
} DRM_USAGEPOLICY_TYPE;

typedef enum _DRMATTESTTYPE {
  DRMATTESTTYPE_FULLENVIRONMENT = 0,
  DRMATTESTTYPE_HASHONLY 
} DRMATTESTTYPE;

typedef enum _DRMENCODINGTYPE {
  DRMENCODINGTYPE_BASE64 = 0,
  DRMENCODINGTYPE_STRING,
  DRMENCODINGTYPE_LONG,
  DRMENCODINGTYPE_TIME,
  DRMENCODINGTYPE_UINT,
  DRMENCODINGTYPE_RAW 
} DRMENCODINGTYPE;

typedef enum _DRMSECURITYPROVIDERTYPE {
  DRMSECURITYPROVIDERTYPE_SOFTWARESECREP = 0
} DRMSECURITYPROVIDERTYPE;

typedef enum _DRMSPECTYPE {
  DRMSPECTYPE_UNKNOWN,
  DRMSPECTYPE_FILENAME 
} DRMSPECTYPE;

typedef enum _DRMTIMETYPE {
  DRMTIMETYPE_SYSTEMUTC = 0,
  DRMTIMETYPE_SYSTEMLOCAL 
} DRMTIMETYPE;

typedef struct _DRM_ACTSERV_INFO {
  UINT  uVersion;
  PWSTR wszPubKey;
  PWSTR wszURL;
} DRM_ACTSERV_INFO;

typedef struct _DRM_CLIENT_VERSION_INFO {
  UINT  uStructVersion;
  DWORD dwVersion[4];
  WCHAR wszHierarchy[256];
  WCHAR wszProductID[256];
  WCHAR wszProductDescription[256];
} DRM_CLIENT_VERSION_INFO;

typedef struct _DRMID {
  UINT  uVersion;
  WCHAR *wszIDType;
  WCHAR *wszID;
} DRMID;

typedef struct _DRMBOUNDLICENSEPARAMS {
  UINT      uVersion;
  DRMHANDLE hEnablingPrincipal;
  DRMHANDLE hSecureStore;
  PWSTR     wszRightsRequested;
  PWSTR     wszRightsGroup;
  DRMID     idResource;
  UINT      cAuthenticatorCount;
  DRMHANDLE *rghAuthenticators;
  PWSTR     wszDefaultEnablingPrincipalCredentials;
  DWORD     dwFlags;
} DRMBOUNDLICENSEPARAMS;

typedef HRESULT (__stdcall *DRMCALLBACK)(
    DRM_STATUS_MSG msg,
    HRESULT hr,
    VOID *pvParam,
    VOID *pvContext
);

#endif /*_INC_MSDRMDEFS*/
