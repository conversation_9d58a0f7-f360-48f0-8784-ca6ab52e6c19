@echo off
setlocal enabledelayedexpansion
@del /Q ..\bin\*

set BLD_OPT=CRANE_CUST
set INPUT_ARG=6
set SEPRATE_RW=0
set SDK_INPUT_CFG=%1

echo %SDK_INPUT_CFG% | findstr /i "NOBT" 1>nul
if !errorlevel! equ 1 (
set BT_CFG=_BLUETOOTH_BTUI
)

echo %SDK_INPUT_CFG% | findstr /i "NOWIFI" 1>nul
if !errorlevel! equ 1 (
	echo %SDK_INPUT_CFG% | findstr /i "HERON" 1>nul
	if !errorlevel! equ 1 (
		set WIFI_CFG=_WIFI_SUPPORT
	) else (
		set WIFI_CFG=_HERON_SUPPORT
	)
)

echo %SDK_INPUT_CFG% | findstr /i "NOGPS" 1>nul
if !errorlevel! equ 1 (
set AGPS_CFG=_AGPS_TP
)

set CRANE_SDK_USELIBS=3g_ps\dps ^
os\osa ^
genlib\fsm ^
ltel1a\LTEL1A ^
genlib\qmgr ^
genlib\min_max ^
CRD\CRD ^
drat\DRAT ^
hop\rm ^
hop\aam ^
hop\dma ^
hop\intc ^
hop\timer ^
hop\RTC ^
hop\pmu ^
hop\commpm ^
hop\pm ^
hop\aci ^
hop\mrd ^
hop\mmi_mat ^
nota\sulog ^
l1wlan\l1wlan ^
softutil\csw_memory ^
softutil\lzop ^
softutil\TickManager ^
softutil\fatfs ^
pcac\lwipv4v6 ^
pcac\dial ^
pcac\ci_stub

set USELIBS_FP_COMMON=volte\volte_components ^
ims\ims_components ^
hal\rndis ^
hal\GPIO ^
hop\core ^
hop\AES ^
softutil\fatsys ^
softutil\littlefs ^
softutil\yaffs2

set USELIBS_DM_COMMON=volte\volte_components ^
ims\ims_components ^
diag\diag ^
hal\camera_isp

if "%INPUT_ARG%"=="" (
:RESELECT
echo ----+-------------------------------------------
echo NUM + SDK_MODE_DETAIL
echo ----+-------------------------------------------
echo  1  + [FP][MINIGUI    ][TX    ][LTEONLY ][Z2A0][LWIP][VOLTE]
echo  2  + [FP][MINIGUI    ][TX    ][LTEGSM  ][Z2A0][LWIP][VOLTE]
echo  3  + [FP][GENERIC    ][TX    ][LTEONLY ][Z2A0][LWIP][VOLTE]
echo  4  + [FP][GENERIC    ][TX    ][LTEGSM  ][Z2A0][LWIP][VOLTE]
echo  5  + [DM][GENERIC    ][TX    ][LTEGSM  ][Z2A0][LWIP][VOLTE]
echo  6  + [FP][GENERIC    ][ALIOS ][LTEONLY ][Z2A0][LWIP][VOLTE]
echo  7  + [FP][GENERIC    ][ALIOS ][LTEGSM  ][Z2A0][LWIP][VOLTE]
echo ----+-------------------------------------------

set /p INPUT_ARG="> input your target build script NUM : "
)

if /i "%INPUT_ARG%"=="1" goto FP_MINIGUI_TX_LTEONLY_Z2A0_LWIP_VOLTE
if /i "%INPUT_ARG%"=="2" goto FP_MINIGUI_TX_LTEGSM_Z2A0_LWIP_VOLTE
if /i "%INPUT_ARG%"=="3" goto FP_GENERIC_TX_LTEONLY_Z2A0_LWIP_VOLTE
if /i "%INPUT_ARG%"=="4" goto FP_GENERIC_TX_LTEGSM_Z2A0_LWIP_VOLTE
if /i "%INPUT_ARG%"=="5" goto DM_GENERIC_TX_LTEGSM_Z2A0_LWIP_VOLTE
if /i "%INPUT_ARG%"=="6" goto FP_GENERIC_ALIOS_LTEONLY_Z2A0_LWIP_VOLTE
if /i "%INPUT_ARG%"=="7" goto FP_GENERIC_ALIOS_LTEGSM_Z2A0_LWIP_VOLTE
goto RESELECT

:FP_MINIGUI_TX_LTEGSM_Z2A0_LWIP_VOLTE
echo :FP_MINIGUI_TX_LTEGSM_Z2A0_LWIP_VOLTE
set SDK_PROJ_ARG=AESCIPHER_FOTAGSRAM
set SDK_PROD_TYPE=FP
set SDK_CUST_SKU=MINIGUI
set SDK_PS_MODE=LTEGSM
set SDK_CHIP_VER=Z2A0
set MMI_CONFIG=MINIGUI
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lg_data_r13.bld
set SDK_OS_TYPE=TX
goto CONFIG_END_1

:FP_MINIGUI_TX_LTEONLY_Z2A0_LWIP_VOLTE
echo :FP_MINIGUI_TX_LTEONLY_Z2A0_LWIP_VOLTE
set SDK_PROJ_ARG=AESCIPHER_FOTAGSRAM
set SDK_PROD_TYPE=FP
set SDK_CUST_SKU=MINIGUI
set SDK_PS_MODE=LTEONLY
set SDK_CHIP_VER=Z2A0
set MMI_CONFIG=MINIGUI
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lte_only_r13_volte_fp.bld
set SDK_OS_TYPE=TX
goto CONFIG_END_2

:FP_GENERIC_ALIOS_LTEONLY_Z2A0_LWIP_VOLTE
echo :FP_GENERIC_ALIOS_LTEONLY_Z2A0_LWIP_VOLTE
set SDK_PROJ_ARG=AESCIPHER
set SDK_PROD_TYPE=FP
set SDK_PS_MODE=LTEONLY
set SDK_CUST_SKU=GENERIC
set SDK_CHIP_VER=Z2A0
set MMI_CONFIG=FWK
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lte_only_r13_volte_fp.bld
set SDK_OS_TYPE=ALIOS
goto CONFIG_END_3

:FP_GENERIC_ALIOS_LTEGSM_Z2A0_LWIP_VOLTE
echo :FP_GENERIC_ALIOS_LTEGSM_Z2A0_LWIP_VOLTE
set SDK_PROJ_ARG=AESCIPHER
set SDK_PROD_TYPE=FP
set SDK_PS_MODE=LTEGSM
set SDK_CUST_SKU=GENERIC
set SDK_CHIP_VER=Z2A0
set MMI_CONFIG=FWK
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lg_data_r13.bld
set SDK_OS_TYPE=ALIOS
goto CONFIG_END_4

:FP_GENERIC_TX_LTEONLY_Z2A0_LWIP_VOLTE
echo :FP_GENERIC_TX_LTEONLY_Z2A0_LWIP_VOLTE
set SDK_PROJ_ARG=AESCIPHER
set SDK_PROD_TYPE=FP
set SDK_PS_MODE=LTEONLY
set SDK_CUST_SKU=GENERIC
set SDK_CHIP_VER=Z2A0
set MMI_CONFIG=FWK
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lte_only_r13_volte_fp.bld
set SDK_OS_TYPE=TX
goto CONFIG_END_5

:FP_GENERIC_TX_LTEGSM_Z2A0_LWIP_VOLTE
echo :FP_GENERIC_TX_LTEGSM_Z2A0_LWIP_VOLTE
set SDK_PROJ_ARG=AESCIPHER
set SDK_PROD_TYPE=FP
set SDK_PS_MODE=LTEGSM
set SDK_CUST_SKU=GENERIC
set SDK_CHIP_VER=Z2A0
set MMI_CONFIG=FWK
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lg_data_r13.bld
set SDK_OS_TYPE=TX
goto CONFIG_END_6

:DM_GENERIC_TX_LTEGSM_Z2A0_LWIP_VOLTE
echo :DM_GENERIC_TX_LTEGSM_Z2A0_LWIP_VOLTE
set SDK_PROJ_ARG=MODULE_CIRAM
set SDK_PROD_TYPE=DM
set SDK_PS_MODE=LTEGSM
set SDK_CUST_SKU=GENERIC
set SDK_CHIP_VER=Z2A0
set MMI_CONFIG=NOMMI
set SDK_TARGET_BLD=Crane_hsiupdlibdev_ltepsds_frbd_lwg_cat1_lg_data_r13.bld
set SDK_OS_TYPE=TX
goto CONFIG_END_7

:CONFIG_END_0
:CONFIG_END_1
:CONFIG_END_2
:CONFIG_END_3
:CONFIG_END_4
:CONFIG_END_5
:CONFIG_END_6
:CONFIG_END_7
echo SDK_PROD_TYPE   : [%SDK_PROD_TYPE%]
echo SDK_CUST_SKU    : [%SDK_CUST_SKU%]
echo SDK_PS_MODE     : [%SDK_PS_MODE%]
echo SDK_CHIP_VER    : [%SDK_CHIP_VER%]
echo SDK_OS_TYPE     : [%SDK_OS_TYPE%]
echo MMI_CONFIG      : [%MMI_CONFIG%]
echo SDK_INPUT_CFG   : [%SDK_INPUT_CFG%]

::::::::::::::::::::::::::::::::::::::::::::::::::::::::
:: [SDK_PROD_TYPE]={FP|DM}
:: NOTE: production type should be featurePhone or datamodule
::::::::::::::::::::::::::::::::::::::::::::::::::::::::
if /i "%SDK_PROD_TYPE%"=="FP" (
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% %USELIBS_FP_COMMON%
set PROJECT_ARG=XIP_LWIP_%SDK_PROJ_ARG%%SDK_INPUT_CFG%%BT_CFG%%WIFI_CFG%%AGPS_CFG%
set SDK_DISTRIBUTION_MARK=%SDK_CUST_SKU%_SDK_%SDK_DISTRIBUTION_DATE%
)
if /i "%SDK_PROD_TYPE%"=="DM" (
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% %USELIBS_DM_COMMON%
set PROJECT_ARG=XIP_LWIP_%SDK_PROJ_ARG%%SDK_INPUT_CFG%%BT_CFG%%WIFI_CFG%
set SDK_DISTRIBUTION_MARK=%SDK_CUST_SKU%_SDK_%SDK_DISTRIBUTION_DATE%
)

::::::::::::::::::::::::::::::::::::::::::::::::::::::::
:: [SDK_PS_MODE]={LTEGSM|LTEONLY}
:: NOTE: ps mode should be singlemode lteonly or dualmode lte+gsm
::::::::::::::::::::::::::::::::::::::::::::::::::::::::
if "%SDK_PS_MODE%"=="LTEGSM" (
set LG_FOR_CAT1=TRUE
set LTE_ONLY_FOR_CAT1=FALSE
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% ^
gplc\GPLC
)
if "%SDK_PS_MODE%"=="LTEONLY" (
set LG_FOR_CAT1=FALSE
set LTE_ONLY_FOR_CAT1=TRUE
)


set CRANE_LIB_DIR_PREFIX=%XROOT%\tavor\Arbel\CRANE_SDK_LIB
if "%SDK_OS_TYPE%"=="TX" (
@rem **************************
@rem keep TX CRANE_LIB_DIR the same as prevois structure
@rem **************************
set CRANE_LIB_DIR=%CRANE_LIB_DIR_PREFIX%\%SDK_PROD_TYPE%_%SDK_CUST_SKU%_%SDK_PS_MODE%
echo CRANE_LIB_DIR   : [!CRANE_LIB_DIR!]

set CBA_USE_COMMON_LIB_DIR=1
set TARGET_OS=THREADX
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% ^
os\threadx
)
if "%SDK_OS_TYPE%"=="ALIOS" (
set CRANE_LIB_DIR=%CRANE_LIB_DIR_PREFIX%\%SDK_PROD_TYPE%_%SDK_CUST_SKU%_%SDK_OS_TYPE%_%SDK_PS_MODE%
echo CRANE_LIB_DIR   : [!CRANE_LIB_DIR!]
set CBA_USE_COMMON_LIB_DIR=1
set TARGET_OS=ALIOS
set CRANE_SDK_USELIBS=%CRANE_SDK_USELIBS% ^
os\alios
)

echo %SDK_INPUT_CFG% | findstr /i "NOGPS" 1>nul
if !errorlevel! equ 1 (
       set CRANE_SDK_USELIBS=agpstp\agpstp_components ^
       %CRANE_SDK_USELIBS%
)

@rem **************************
@rem for HERON wlanhost/wlanhost libs
@rem **************************
echo %SDK_INPUT_CFG% | findstr /i "HERON" 1>nul
if !errorlevel! equ 0 (
       set CRANE_SDK_USELIBS=wlanhost\wlanhost ^
       %CRANE_SDK_USELIBS%
)

set XROOT=
set WROOT=%XROOT%
cd /d %XROOT%\tavor\Arbel\build
set TPLGSM=%WROOT%\3g_ps\rls\tplgsm
set HSIUPDLIBDEV=%TPLGSM%\bldstore\hsiupdlibdev
rmdir /q /s %TPLGSM%
compress.exe x !CRANE_LIB_DIR!\ps.7z -o%HSIUPDLIBDEV%\build\ -y

if not exist %HSIUPDLIBDEV%\test (
mkdir %HSIUPDLIBDEV%\test
)
if exist !CRANE_LIB_DIR!\hsiupdlibdev.i (
COPY /Y !CRANE_LIB_DIR!\hsiupdlibdev.i %HSIUPDLIBDEV%\test\hsiupdlibdev.i
)

call buildall PMD2NONE %SDK_INPUT_CFG%_BUILD_LWG DKBFEATURE NEZHA3_%SDK_CHIP_VER%_%PROJECT_ARG% DOUBLE_LTE

if %errorlevel% equ 1 (
exit /b 1
)

@rem # RN_PROJECT -> project name 			(CRANE)
@rem # RN_MODE    -> PS mode 				(LTE/NBIOT/CATM)
@rem # RN_BOARD   -> target HW board 			(HAPS/DKB/Z1)
@rem # RN_MEDIA   -> CP code running media 		(XIP/PSRAM)
@rem # RN_FLAG    -> some aditional special flags       (MMI/SDK/LWIP/......)
@rem # RN_BUILD_MARK -> build info mark append          (@xiaokeweng_20181106_205832)

set RN_PROJECT=CRANE_DS_%SDK_OS_TYPE%_%SDK_CHIP_VER%
set RN_MODE=_%SDK_PS_MODE%
set RN_BOARD=_DKB
set RN_MEDIA=_%PROJECT_ARG%
set RN_FLAG=_%SDK_CUST_SKU%_SDK

for /f "delims=" %%i in ('perl localdate.pl') do (set RN_DATE=%%i)
set RN_BUILD_MARK=@%USERNAME%_%RN_DATE%_%time:~0,8%
set RN_BUILD_MARK=%RN_BUILD_MARK::=%
set RN_BUILD_MARK=%RN_BUILD_MARK: =%
set RN_BUILD_MARK=%RN_BUILD_MARK:-=%
 
set RN_GENERAL_INFO=%RN_PROJECT%%RN_MODE%%RN_BOARD%%RN_FLAG%
set RENAME_STR=%RN_GENERAL_INFO%%RN_BUILD_MARK%

@rem rename map/axf/MDB/NO_TIM_bin
rename ..\bin\Arbel_PMD2NONE_MDB.txt %RENAME_STR%_MDB.txt
rename ..\bin\Arbel_PMD2NONE_*.map   %RENAME_STR%.map
rename ..\bin\Arbel_PMD2NONE_*.axf   %RENAME_STR%.axf
rename ..\bin\Arbel_PMD2NONE_*NO_TIM.bin  Arbel_PMD2NONE_NO_TIM.bin

platform -p DKB_SS -t EVB_3 -i NONE  -b ..\bin\Arbel_PMD2NONE_NO_TIM.bin -o ..\bin\%RENAME_STR%.bin
perl external_rw_region_compress.pl ..\bin\%RENAME_STR%.bin %SEPRATE_RW%
perl external_loadtable_update.pl ..\bin\%RENAME_STR%.bin %RN_GENERAL_INFO%
set LOGO_BINARY=%XROOT%\tavor\Arbel\build\logo_no_lcd.bin
perl external_logo_image_handle.pl APPEND ..\bin\%RENAME_STR%.bin %LOGO_BINARY%

del /Q ..\bin\*Arbel_PMD2NONE*
dir /B ..\bin\*%RENAME_STR%*
