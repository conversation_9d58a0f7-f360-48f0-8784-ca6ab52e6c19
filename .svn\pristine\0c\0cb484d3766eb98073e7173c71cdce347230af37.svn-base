﻿/****************************************************************************
 * * modem_main.c - implementation of modem adapter main functions
 *
 * *(C) Copyright 2019 Asr International Ltd.
 * * All Rights Reserved
 * ******************************************************************************/
#include "lv_watch.h"
#include <png.h>

#if USE_LV_WATCH_MODEM_ADAPTOR
#include "../modem/mmi_modem_adaptor_main.h"
#include "mmi_modem_adaptor_ws.h"

lv_task_t * mute_time_task_p = NULL;
static ws_nv_mute g_nvMutePeriod[WS_MUTE_MAX_COUNT] = {0};
static bool g_ws_is_mute = false;
char g_PhoneNum[18]={0};

static void *   mmi_ws_remotecam_Timer = NULL;
#if defined(__XF_WS_VENDOR_CT_ANH__)||USE_LV_WATCH_DEV_SECURITY_REGION_DETECT!=0
static ws_nv_region g_nvRegion[WS_REGION_MAX_COUNT] = {0};
#endif
#if USE_LV_WATCH_WHITENAME_LIST_TIME_REPEAT!=0
static ws_nv_whitelist_time g_nvWhiteNamePeriod;
#endif

#if USE_LV_WATCH_CT_READY_MODE!=0
static ws_nv_readymode g_nvReadyMode;
#endif

#if USE_LV_WATCH_FIXED_LOCATE_TIMERANGE_DISABLE!=0
static ws_nv_locatedisable g_nvLocateDisablePeriod[WS_LOACTE_DISBALE_LIST_MAX_COUNT] = {0};
#endif

#if USE_LV_WATCH_ZM_SYNCTASK_TIME!=0
static ws_nv_realtasktime g_nvRealTaskPeriod[WS_REALTASK_TIME_LIST_MAX_COUNT] = {0};
#endif

#if USE_LV_WATCH_POWER_PERIOD != 0
static ws_nv_power_period g_nvPowerPeriod= {0};
#endif /* USE_LV_WATCH_POWER_PERIOD */
#if USE_LV_WATCH_KAER_SWITCH != 0
static ws_nv_switch g_nvSwitch= {0};
#endif
#if USE_LV_WATCH_WS_KAER != 0
static uint8_t gCallinLimited = 0;  //bit(7)SOS是否可以呼入 bit(6)可以拨打SOS bit(5)限制呼入bit（不包括sos）(4)限制呼出bit（不包括sos）
#endif
#if USE_LV_WATCH_WS_KAER != 0
static uint8_t gFamilyNoSer[3] = {0};
#endif
#if USE_LV_WATCH_CT_STUDENT!=0
extern student_show_t gStudentInfo;
#endif

#if USE_WS_DEVICE_LOGIN_UNACTVIE!=0
static bool g_ws_is_login_unactive = false;
#endif
#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0
nv_watch_alarm_t g_poweroff_clock;
uint8_t g_powroff_valid = 0;

#endif
#if USE_LV_WATCH_REPORT_NFC_INFO != 0
char g_24G_cardid[20] = {0};
#endif

#if USE_LV_WATCH_LOCK_ICCID != 0
nv_watch_iccid_t g_cur_iccidstrs;
#endif

#if USE_LV_WATCH_GET_LAST_SAVE_LATLON != 0
nv_watch_last_latlon_t g_last_latlon;
//必须要加外部引用，防止类型不对
extern void GpsSetLatLon(float *lonf, float *latf);
#endif

static void *sfotaCheckTimer = NULL;
static void *link_lcd_8s_timer=NULL;
static int sys_volume_set_disable = 0;
static uint8_t reboot_no_link_flag = 0;
static uint8_t used_link_success_flag = 0;
#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
static uint8_t first_period_loc_flag = 0;
#endif

void WatchUtility_set_volume_disable( int  val)
{
    sys_volume_set_disable = val;
}

int sys_set_volume_disable(void)
{
    return sys_volume_set_disable;
}



int ws_FmtGMTTime(hal_rtc_t *tm, char* out, int tz)
{
    #define MIN(a, b)       ((a) < (b) ? (a) : (b))
    
    static const char *dayv[] = {"Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"};
    
    static const char *monv[] = {"Jan", "Feb", "Mar", "Apr", "May", "Jun",
                     "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"};

	char timezone[8];

	if(tz > 0){
		sprintf(timezone, "+%04d", tz);
	}else{
		sprintf(timezone, "%04d", tz);
	}

	return sprintf(out, "%s, %02u %s %u %02u:%02u:%02u %s",
			  dayv[MIN((unsigned)tm->tm_wday, ARRAY_SIZE(dayv)-1)],
			  tm->tm_mday,
			  monv[MIN((unsigned)tm->tm_mon-1, ARRAY_SIZE(monv)-1)],
			  tm->tm_year,
			  tm->tm_hour, tm->tm_min, tm->tm_sec, timezone);
}


uint32_t  UiTimeAllDay(hal_rtc_t *a)   
{     
  int   leap[12]={31,29,31,30,31,30,31,31,30,31,30,31};   
  int   noleap[12]={31,28,31,30,31,30,31,31,30,31,30,31};   
  uint32_t x,i;   
  
  uint32_t sumyear =a->tm_year-1;
  
  x=sumyear*365+sumyear/4+sumyear/400-sumyear/100;   
  
  if((((a->tm_year%4)==0)&&((a->tm_year%100)!=0))||(a->tm_year%400==0))   
  {   
    for(i=0;i<a->tm_mon;i++){   
      if(i>0)  {   
        x=x+leap[i-1];   
      }   
    }   
  } else {   
    for(i=0;i<a->tm_mon;i++)   
    {   
      if(i>0){   
        x=x+noleap[i-1];   
      }   
    }   
  }   
  x=x+a->tm_mday;   
  return  x;   
}  

uint32_t UiTimeInterval(hal_rtc_t *a,hal_rtc_t *b)   
{   
  uint32_t x,y;   
  uint32_t sum;   
  x=UiTimeAllDay(a);   
  y=UiTimeAllDay(b);   
  sum=(x-y)*24+a->tm_hour-b->tm_hour;   
  return  sum*3600 +(a->tm_min-b->tm_min)*60+(a->tm_sec - b->tm_sec);   
}   

uint32_t UiGetUnixTimeStamp(hal_rtc_t *time)
{
  hal_rtc_t startTime;

  startTime.tm_year = 1970;
  startTime.tm_mon = 1;
  startTime.tm_mday = 1;
  startTime.tm_hour = 0;
  startTime.tm_min = 0;
  startTime.tm_sec = 0;

  return UiTimeInterval(time, &startTime) - 60*GetTimeZone();
}

uint32_t GETTIMESECONDS(void)
{
    hal_rtc_t rtc_curr;
    Hal_Rtc_Gettime(&rtc_curr);
    return time_to_seconds(&rtc_curr);
}

unsigned int atoui(char* s)
{
	 unsigned int k = 0;

	 k = 0;
	 while (*s != '\0' && *s >= '0' && *s <= '9') {
	  k = 10 * k + (*s - '0');
	  s++;
	 }
	 return k;
}


char* ws_urlEncode(char* c)
{
    static char escaped[128];
    char* ptr = escaped;
    int max = strlen(c);
    int i = 0;
    for(i = 0; i < max; ++i)
    {
        if ( (c[i] >= '0' && c[i] <= '9') ||
             (c[i] >= 'a' && c[i] <= 'z') ||
             (c[i] >= 'A' && c[i] <= 'Z') ||
             (c[i] == '~' || c[i] == '!'  || 
              c[i] == '*' || c[i] == '\'' || 
              c[i] == '.' || c[i] == '-'  ||
              c[i] == ' ')
        )
        {
            if (c[i] == ' ')
                *ptr++ = '+';
            else
                *ptr++ = c[i];
        }
        else
        {
            *ptr++ = '%';
            char ascii[3];
            snprintf(ascii, 3, "%X", c[i]);
            memcpy(ptr, ascii, 2);
            ptr += 2;
        }
    }
    *ptr = '\0';
    return escaped;
}


int split_string(char* src, char ch, char* dest, int* index, int max)
{
	int i=0;
	int count=1;
	
	index[0]=0;

	if(dest==NULL)
	{
		while(*src)
		{
			if(*src == ch)
			{
				if(count<max)
					index[count]=i+1;
		
				*src=0;
				count++;
                if(count==max)
                {
                    break;
                }
			}
		
			src++;
			i++;
		}
	}
	else
	{
		while(*src)
		{
			if(*src == ch)
			{
				if(count<max)
					index[count]=i+1;
		
				*dest=0;
				count++;
                if(count==max)
                {
                    break;
                }
			}
			else
			{
				*dest = *src;
			}
		
			src++;
			dest++;
			i++;
		}
		
		*dest=0;
		
	}
	

	return count;
}

void myNVMgr_SetWsBonuses(uint32_t bonus)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_bonus), sizeof(uint32_t), (uint8_t*)&bonus);
}

uint32_t myNVMgr_GetWsBonuses(void)
{
    uint32_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_bonus), sizeof(uint32_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWsMsgIndex(uint32_t index)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_msg_index), sizeof(uint32_t), (uint8_t*)&index);
}

uint32_t myNVMgr_GetWsMsgIndex(void)
{
    uint32_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_msg_index), sizeof(uint32_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWsImsi(uint8_t *imsi)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_cur_imsi), NV_WS_STRING_LEN, imsi);
}

void myNVMgr_GetWsImsi(uint8_t *imsi)
{
    if(imsi)
    {
        UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_cur_imsi), NV_WS_STRING_LEN, imsi);
    }
}

void myNVMgr_SetWsUid(uint8_t *id)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_cur_meid), NV_WS_STRING_LEN, id);
}

void myNVMgr_GetWsUid(uint8_t *id)
{
    if(id)
    {
        UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_cur_meid), NV_WS_STRING_LEN, id);
    }
}

void myNVMgr_SetTcsUrlPort(char *Data)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mTcsUrlPort), 50, (uint8_t*)Data);
}

uint8_t myNVMgr_GetTcsUrlPort(char *Data)
{
    uint32_t len = 0;
    len = UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mTcsUrlPort), 50, (uint8_t*)Data);
	if(len < sizeof(nv_watch_watch_service_t))
		return 0;
	return 1;
}
void myNVMgr_Set_wifi_to_gps_UrlPort(char *Data)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, wifi_to_gps_urlport), 50, (uint8_t*)Data);
}

uint8_t myNVMgr_Get_wifi_to_gps_UrlPort(char *Data)
{
    uint32_t len = 0;
    len = UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, wifi_to_gps_urlport), 50, (uint8_t*)Data);
	if(len < sizeof(nv_watch_watch_service_t))
		return 0;
	return 1;
}

#if (USE_LV_WATCH_WS_KAER != 0)
void myNVMgr_SetWsUrlPort(char *Data)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mWsUrlPort), 50, (uint8_t*)Data);
}

uint8_t myNVMgr_GetWsUrlPort(char *Data)
{
    uint32_t len = 0;
    len = UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mWsUrlPort), 50, (uint8_t*)Data);
	if(len < sizeof(nv_watch_watch_service_t))
		return 0;
	return 1;
}

uint8_t myNVMgr_GetKey(char *Data)
{
    uint32_t len = 0;
    len = UI_NV_Read_Req(NV_SECTION_UI_KAERSETTINGS, NV_OFFSETOF(nv_watch_kaersettings_t, Quantong_key), 96, (uint8_t*)Data);
	if(len < sizeof(nv_watch_watch_service_t))
		return 0;
	return 1;
}

#endif

void myNVMgr_SetWsDevGpsFirst(uint32_t type)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_gps_first), sizeof(uint32_t), (uint8_t*)&type);
}

uint32_t myNVMgr_GetWsDevGpsFirst(void)
{
    uint32_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_gps_first), sizeof(uint32_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWsDevPingTime(uint32_t ping)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_ping_time), sizeof(uint32_t), (uint8_t*)&ping);
}
uint32_t myNVMgr_GetWsDevPingTime(void)
{
    uint32_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_ping_time), sizeof(uint32_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWsDevPosUpTime(uint32_t ping)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_posup_time), sizeof(uint32_t), (uint8_t*)&ping);
}
uint32_t myNVMgr_GetWsDevPosUpTime(void)
{
    uint32_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_posup_time), sizeof(uint32_t), (uint8_t*)&val);
	return val;
}


#if defined(__XF_WS_VENDOR_CT_GUIZHOU__)||(USE_LV_WATCH_CT_DEV_WORKMODE!=0)
void myNVMgr_SetWsDevPosUpMode(uint8_t mode)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, upPosMode), sizeof(uint8_t), (uint8_t*)&mode);
}
uint8_t myNVMgr_GetWsDevPosUpMode(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, upPosMode), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}
void myNVMgr_SetWsDevPosUpModeLast(uint8_t mode)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, upPosModeLast), sizeof(uint8_t), (uint8_t*)&mode);
}
uint8_t myNVMgr_GetWsDevPosUpModeLast(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, upPosModeLast), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}

#endif

#if (USE_LV_WATCH_WS_CT != 0)
void myNVMgr_SetWsDevNewSmsIntercept(uint32_t mode)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mWs_newsms_intercept), sizeof(uint8_t), (uint8_t*)&mode);
}

uint32_t myNVMgr_GetWsDevNewSmsIntercept(void)
{
    uint32_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mWs_newsms_intercept), sizeof(uint8_t), (uint8_t*)&val);
    return val;
}

#if (USE_LV_WATCH_CT_DEV_WORKMODE!=0)
uint8_t myNVMgr_GetWsDevCurrMode(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, upPosMode), sizeof(uint8_t), (uint8_t*)&val);
    return val;
}
#endif

#if USE_LV_WATCH_CT_CALL_PROFILE_CALL!=0
void myNVMgr_SetWsDevCallProfileMode(uint8_t mode)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mCall_profile_mode), sizeof(uint8_t), (uint8_t*)&mode);
}
/*bit 0: silent,bit1: ringon, bit2:limit in ,bit3:limit out*/
uint8_t myNVMgr_GetWsDevCallProfileMode(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mCall_profile_mode), sizeof(uint8_t), (uint8_t*)&val);
    return val;
}
#endif

#if USE_LV_WATCH_SOS_MO_SINGLE_AND_NOTTS!=0
void myNVMgr_SetWsSosTwoWayMode(uint8_t mode)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mSosTwoway), sizeof(uint8_t), (uint8_t*)&mode);
}
/*0: one way,1:two way*/
uint8_t myNVMgr_GetWsSosTwoWayMode(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mSosTwoway), sizeof(uint8_t), (uint8_t*)&val);
    return val;
}

#endif

#endif /*USE_LV_WATCH_WS_CT*/

#if USE_LV_YNYD_FUNCION_SET!=0
static uint8_t gFunctionConfig=0;
void myNVMgr_SetWsDevFunctionConfig(uint8_t mode)
{
	gFunctionConfig=mode;
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mfunction_menu), sizeof(uint8_t), (uint8_t*)&mode);
}
/*bit 0: aq,bit1: alipay*/
uint8_t myNVMgr_GetWsDevFunctionConfig(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mfunction_menu), sizeof(uint8_t), (uint8_t*)&val);
    return val;
}

uint8_t ws_GetWsDevFunctionConfig(void)
{
 	static bool mfunctionsetinit=false;
	if(mfunctionsetinit==false)
	{
		mfunctionsetinit=true;
		gFunctionConfig=myNVMgr_GetWsDevFunctionConfig();
	}
    return gFunctionConfig;
}
#endif

#if defined(__XF_WS_VENDOR_CT_R2__)
void myNVMgr_SetHealth_FullName(char *fullname)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, health_fullname), NV_WS_URL_LEN, (uint8_t*)fullname);
}	

uint16_t myNVMgr_Health_FullName(char *fullname)
{
    uint16_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, health_fullname), NV_WS_URL_LEN, (uint8_t*)fullname);
	return val;
}
#endif

#if USE_LV_WATCH_CT_STUDENT!=0
void myNVMgr_SetWsStudentInfo(ws_nv_student_t *mute)
{
    uint16_t   len = sizeof(ws_nv_student_t);
    if(mute)
    {
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_student), len, (uint8_t*)mute);
    }

}
void myNVMgr_GetWsStudentInfo(ws_nv_student_t *mute)
{
    uint16_t   len = sizeof(ws_nv_student_t);    
    
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_student), len, (uint8_t*)mute);
}

#endif

void myNVMgr_SetIpNum(char *url, uint16_t port)
{
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_srv_port), sizeof(uint16_t), (uint8_t*)&port);
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_srv_url), NV_WS_URL_LEN, (uint8_t*)url);
}	

uint16_t myNVMgr_GetIpNum(char *url)
{
    uint16_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_srv_port), sizeof(uint16_t), (uint8_t*)&val);
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_srv_url), NV_WS_URL_LEN, (uint8_t*)url);
	return val;
    
}

void myNVMgr_SetWhiteListStatus(uint8_t status)
{
    uint8_t onoff = status;
    
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_whilelist), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetWhiteListStatus(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_whilelist), sizeof(uint8_t), (uint8_t*)&val);
	#if USE_LV_WATCH_WS_KAER != 0
	return val;
	#else
	return (val!=0);
	#endif
}

#if (USE_LV_WATCH_WS_CT != 0)
uint8_t myNVMgr_GetWhiteListStatus2(void)
{
	uint8_t val = 0;
	#if USE_LV_WATCH_CT_CTA!=0 ||USE_LV_WATCH_CT_MT_NOLIMIT!=0 ||USE_LV_CT_FACOTRY_TEST!=0
		return 1;  /*1:no limit ,2:limit only whitelist,3:limit all no mt*/
	#else
	    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_whilelist), sizeof(uint8_t), (uint8_t*)&val);
		if((val<1)||(val>3))
			val=2;
	    return val;
	#endif
}
#endif

#if USE_LV_WATCH_WHITE_LIMIT_CALL!=0 
void myNVMgr_SetWhiteListMOStatus(uint8_t status)
{
    uint8_t onoff = status;
    
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, whlist_mo), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetWhiteListMOStatus(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, whlist_mo), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}
#endif

#if USE_LV_WATCH_OFFLINE_AT_NIGHT_ENABLE!=0
static app_adaptor_nightOffline_t g_NightOffline;
void myNVMgr_SetNightOfflineStatus(app_adaptor_nightOffline_t status)
{
    ws_nv_nightoffline nvnight;
    g_NightOffline=status;
	nvnight.is_open=g_NightOffline.is_open;
	nvnight.on_hour=g_NightOffline.on_hour;
	nvnight.on_min=g_NightOffline.on_min;
	nvnight.off_hour=g_NightOffline.off_hour;
	nvnight.off_min=g_NightOffline.off_min;
	
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_nightValue), sizeof(ws_nv_nightoffline), (uint8_t*)&nvnight);
}
void myNVMgr_GetNightOfflineStatus(app_adaptor_nightOffline_t * nightInfo)
{
	ws_nv_nightoffline nvnight;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_nightValue), sizeof(ws_nv_nightoffline), (uint8_t*)&nvnight);
	nightInfo->is_open=nvnight.is_open;
	nightInfo->on_hour=nvnight.on_hour;
	nightInfo->on_min=nvnight.on_min;
	nightInfo->off_hour=nvnight.off_hour;
	nightInfo->off_min=nvnight.off_min;
}
app_adaptor_nightOffline_t *myWs_GetNightOfflineStatus(void)
{
   	return &g_NightOffline;
}

void WatchUtility_SetNightMode(ws_night_mode *night_mode)
{
    ws_nv_nightoffline nvnight;
	nvnight.is_open=g_NightOffline.is_open = night_mode->mode;
	nvnight.on_hour=g_NightOffline.on_hour = night_mode->shour;
	nvnight.on_min=g_NightOffline.on_min = night_mode->smin;
	nvnight.off_hour=g_NightOffline.off_hour = night_mode->ehour;
	nvnight.off_min=g_NightOffline.off_min = night_mode->emin;
	
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_nightValue), sizeof(ws_nv_nightoffline), (uint8_t*)&nvnight);

    
}

#endif

void myNVMgr_SetDialPadStatus(bool on)
{
    uint8_t onoff = on;
    
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_dialpad), sizeof(uint8_t), (uint8_t*)&onoff);
}

bool myNVMgr_GetDialPadStatus(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_dialpad), sizeof(uint8_t), (uint8_t*)&val);
	return (val!=0);
}

#if USE_LV_WATCH_AUTO_ANSWER_ENABLE != 0
void myNVMgr_SetAutoAnswer(uint8_t on)
{
    uint8_t onoff = on;
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_autoAnswer), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetAutoAnswer(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_autoAnswer), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}
#endif


#if defined(__XF_WS_VENDOR_PS_HJYHEX__)
void myNVMgr_SetWarningPowerOnState(uint8_t on)
{
    uint8_t onoff = on;
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_poweron), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetWarningPowerOnState(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_poweron), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWarningPowerOffState(uint8_t on)
{
    uint8_t onoff = on;
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_poweroff), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetWarningSimAlmState(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_poweron), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWarningSimAlmState(uint8_t on)
{
    uint8_t onoff = on;
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_poweroff), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetWarningPowerOffState(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_poweroff), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWarningLowbattState(uint8_t on)
{
    uint8_t onoff = on;
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_lowbatt), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetWarningLowbattState(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_lowbatt), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}

void myNVMgr_SetWarningSosSmsState(uint8_t on)
{
    uint8_t onoff = on;
    UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_sos), sizeof(uint8_t), (uint8_t*)&onoff);
}

uint8_t myNVMgr_GetWarningSosSmsState(void)
{
    uint8_t val=0;
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, warning_sos), sizeof(uint8_t), (uint8_t*)&val);
	return val;
}
#endif

void myNVMgr_SetWsCenterNum(char *number)
{
    if(number)
    {
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_center), NV_WS_STRING_LEN, (uint8_t*)number);
    }
}

void myNVMgr_GetWsCenterNum(char *number)
{
    if(number)
    {
        UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_center), NV_WS_STRING_LEN, (uint8_t*)number);
    }
}


#if defined(__XF_WS_VENDOR_XIAOXUN__)
void myNVMgr_SetWsBindCode(char *ck)
{
    if(ck)
    {
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_bindCode), NV_WS_BIND_CODE_LEN, (uint8_t*)ck);
    }
}

void myNVMgr_GetWsBindCode(char *ck)
{
    if(ck)
    {
        UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_bindCode), NV_WS_BIND_CODE_LEN, (uint8_t*)ck);
    }
}

#endif


void WatchUtility_StrToJulianTime(uint8_t *str, hal_rtc_t *time)
{
    char temp[3] = {0};
    
    if(NULL == str || NULL == time){
        return;
    }

    int len = strlen((char*)str);
     
    strncpy((void*)temp, (void*)str, 2);
    time->tm_hour = atoi(temp);
    strncpy((void*)temp, (void*)(str+3), 2);
    time->tm_min = atoi(temp);
    if(len>6)
    {
        strncpy((void*)temp, (void*)(str+6), 2);
        time->tm_sec = atoi(temp);
    }
    
}



#if 101 //mute time range

void WatchUtility_MuteTimeTask(void * param)
{
    if(!watch_is_ready())
    {
        WS_PRINTF("WatchUtility_MuteTimeTask() not ready!");
        return;
    }
    
    WatchUtility_CheckMuteTime();

#if USE_LV_WATCH_POWER_PERIOD != 0
	WatchUtility_CheckPowerOnOffTime();
#endif /* USE_LV_WATCH_POWER_PERIOD */
	
}

void myNVMgr_SetWsMuteTime(ws_nv_mute *mute, uint16_t cnt)
{
    uint16_t   len = NV_WS_MAX_MUTE_NUM*sizeof(ws_nv_mute);
    ws_nv_mute *nv_mute = (ws_nv_mute *)malloc(len);

    if(nv_mute)
    {
        uint16_t len_in = cnt*sizeof(ws_nv_mute);
        memset(nv_mute, 0, len);
        memcpy((void*)nv_mute, (void*)mute, (len_in<=len) ? len_in : len);
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_mutes), len, (uint8_t*)nv_mute);
        free(nv_mute);
    }

}

void myNVMgr_GetWsMuteTime(ws_nv_mute *mute, uint16_t cnt)
{
    uint16_t   len = ((cnt<=NV_WS_MAX_MUTE_NUM) ? cnt : NV_WS_MAX_MUTE_NUM)*sizeof(ws_nv_mute);    
    
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_mutes), len, (uint8_t*)mute);
}

#if USE_LV_WATCH_COURSE != 0
void myNVMgr_SetWsCourse(ws_nv_course *course)
{
    if(course)
    {
		uint16_t   len = NV_WS_MAX_COURSE_CNT*sizeof(ws_nv_course);		    
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_course), len, (uint8_t*)course);
    }
}

void myNVMgr_GetWsCourse(ws_nv_course *course)
{
	uint16_t   len = NV_WS_MAX_COURSE_CNT*sizeof(ws_nv_course); 		
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_course), len, (uint8_t*)course);
}
#endif /* USE_LV_WATCH_COURSE */


#if USE_LV_WATCH_POWER_PERIOD != 0
void myNVMgr_SetWsPowerPeriod(ws_nv_power_period *period)
{
    if(period)
    {
		uint16_t   len = sizeof(ws_nv_power_period);
		memcpy(&g_nvPowerPeriod, period, len);
		
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_power_period), len, (uint8_t*)period);
    }
}

void myNVMgr_GetWsPowerPeriod(ws_nv_power_period *period)
{
	uint16_t   len = sizeof(ws_nv_power_period); 		
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_power_period), len, (uint8_t*)period);
}
#endif /* USE_LV_WATCH_POWER_PERIOD */

#if USE_LV_WATCH_KAER_SWITCH != 0
void myNVMgr_SetWsSwitch(ws_nv_switch *period)
{
    if(period)
    {
		uint16_t   len = sizeof(ws_nv_switch);
		memcpy(&g_nvSwitch, period, len);		
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_switch), len, (uint8_t*)period);
    }
}

void myNVMgr_GetWsSwitch(ws_nv_switch *period)
{
	uint16_t   len = sizeof(ws_nv_switch); 		
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_switch), len, (uint8_t*)period);
}

ws_nv_switch * WatchUtility_GetWsSwitch(void)
{
//	WS_PRINTF("WatchUtility_GetWsSwitch-- batteryLow:%d,allowClose:%d,openCloseAlert:%d", g_nvSwitch.batteryLow, g_nvSwitch.allowClose, g_nvSwitch.openCloseAlert);

    return &g_nvSwitch;
}

#endif /* USE_LV_WATCH_KAER_SWITCH */
#if USE_LV_WATCH_WS_KAER != 0
	 
void myNVMgr_SetCallinLimited(uint8_t *period)
{
    if(period)
    {
        WS_PRINTF("period:%d", *period);
        uint16_t   len = 1;
        gCallinLimited  = 	*period;	
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mCallinLimited), len, (uint8_t*)period);
    }
}

void myNVMgr_GetCallinLimited(uint8_t *period)
{
	uint16_t   len = 1; 		
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mCallinLimited), len, (uint8_t*)period);
}

uint8_t WatchUtility_GetCallinLimited(void)
{
	WS_PRINTF("WatchUtility_GetCallinLimited-- gCallinLimited:%x", gCallinLimited);

    return gCallinLimited;
}
#endif

#if USE_LV_WATCH_WHITENAME_LIST_TIME_REPEAT!=0
void myNVMgr_SetWsWhiteContactTime(ws_nv_whitelist_time *mute)
{
    uint16_t   len = sizeof(ws_nv_whitelist_time);
    if(mute)
    {
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, whitelist_period), len, (uint8_t*)mute);
    }

}
void myNVMgr_GetWsWhiteContactTime(ws_nv_whitelist_time *mute)
{
    uint16_t   len = sizeof(ws_nv_whitelist_time);    
    
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, whitelist_period), len, (uint8_t*)mute);
}

 void wsWhiteContactTime_SaveData( ws_white_list_t *src)
{
    if(NULL == src){
        return;
    }
    
    memset((void*)&g_nvWhiteNamePeriod, 0x00, sizeof(ws_nv_whitelist_time));
	
    g_nvWhiteNamePeriod.repeat = src->week;
    strcpy((char*)g_nvWhiteNamePeriod.start1, src->start_time1);
    strcpy((char*)g_nvWhiteNamePeriod.end1, src->end_time1);
	
    strcpy((char*)g_nvWhiteNamePeriod.start2, src->start_time2);
    strcpy((char*)g_nvWhiteNamePeriod.end2, src->end_time2);

	myNVMgr_SetWsWhiteContactTime(&g_nvWhiteNamePeriod);
	
}
 
bool wsWihteContactTime_isValidPeriod(hal_rtc_t *julian_date)
{
    int i=0;
    
	uint8_t week = 0;
	hal_rtc_t start1 = {0};
	hal_rtc_t end1 ={0};
	hal_rtc_t start2 = {0};
	hal_rtc_t end2 ={0};
	
    if(!julian_date)
    {
        return false;
    }

	week = (uint8_t)julian_date->tm_wday;
	
	WS_PRINTF("wsWihteContactTime_isValidPeriod-- ,repeat:%d,%s,%s,%s,%s", g_nvWhiteNamePeriod.repeat,g_nvWhiteNamePeriod.start1,g_nvWhiteNamePeriod.end1, g_nvWhiteNamePeriod.start2,g_nvWhiteNamePeriod.end2);
	if(0 == (g_nvWhiteNamePeriod.repeat & (1<<week)))
	{
		WS_PRINTF("wsWihteContactTime_isValidPeriod-- ,repeat --return false");
		return false;
	}

	WatchUtility_StrToJulianTime(g_nvWhiteNamePeriod.start1, &start1);
	WatchUtility_StrToJulianTime(g_nvWhiteNamePeriod.end1, &end1);
	WatchUtility_StrToJulianTime(g_nvWhiteNamePeriod.start2, &start2);
	WatchUtility_StrToJulianTime(g_nvWhiteNamePeriod.end2, &end2);

	if(0 <= wsMuteTime_CompareJulianTime(julian_date, &start1) && 0 < wsMuteTime_CompareJulianTime(&end1, julian_date)){
		return true;
	}	

	if(0 <= wsMuteTime_CompareJulianTime(julian_date, &start2) && 0 < wsMuteTime_CompareJulianTime(&end2, julian_date)){
		return true;
	}	
	
    return false;
}

bool wsWhiteContactPeriod_CheckNow(void)
{
    bool inValidTime=false;
    hal_rtc_t rtc_curr;
    
    wsMuteTime_init(false);
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);

	inValidTime=wsWihteContactTime_isValidPeriod(&rtc_curr);
	
	WS_PRINTF("wsWhiteContactPeriod_CheckNow inValidTime:%d", inValidTime);

    return inValidTime;

}
#endif

#if USE_WS_DEVICE_LOGIN_UNACTVIE!=0
bool WatchUtility_IsLoginUnactive(void)
{
	printf("WatchUtility_IsLoginUnactive g_ws_is_mute:%d", g_ws_is_login_unactive);
    return g_ws_is_login_unactive;
}
void WatchUtility_Set_LoginUnactive(bool flag)
{
     g_ws_is_login_unactive=flag;
	 printf("WatchUtility_Set_LoginUnactive g_ws_is_mute:%d", g_ws_is_login_unactive);
	 if(g_ws_is_login_unactive)
	 {
		lv_watch_go_home();
	 	_lv_indev_enable(LV_INDEV_TYPE_POINTER, false);
	 }
	 else
	 {
		if(!WatchUtility_IsMuteTime()) 
	 		_lv_indev_enable(LV_INDEV_TYPE_POINTER, true);
	 }
}
#endif

#if defined(__XF_WS_VENDOR_CT_ANH__)||USE_LV_WATCH_DEV_SECURITY_REGION_DETECT!=0
void myNVMgr_SetWsRegion(ws_nv_region *region, uint16_t cnt)
{
    uint16_t   len = NV_WS_MAX_REGION_NUM*sizeof(ws_nv_region);
    ws_nv_region *nv_region = (ws_nv_region *)malloc(len);

    if(nv_region)
    {
        uint16_t len_in = cnt*sizeof(ws_nv_region);
        memset(nv_region, 0, len);
        memcpy((void*)nv_region, (void*)region, (len_in<=len) ? len_in : len);
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_regions), len, (uint8_t*)nv_region);
        free(nv_region);
    }

}

void myNVMgr_GetWsRegion(ws_nv_region *region, uint16_t cnt)
{
    uint16_t   len = ((cnt<=NV_WS_MAX_REGION_NUM) ? cnt : NV_WS_MAX_REGION_NUM)*sizeof(ws_nv_region);    
    
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_regions), len, (uint8_t*)region);
}

void wsRegion_SetList(REGIONAL *region)
{
    int i;

    if(region==NULL)
    {
        return;
    }


    for(i=0; i<WS_REGION_MAX_COUNT; i++)
    {
		if(g_nvRegion[i].is_on==false)
		{
			g_nvRegion[i].is_on=true;
			g_nvRegion[i].overstate=REGIONAL_OVER_NONE;
			g_nvRegion[i].radius=region->radius;
			g_nvRegion[i].repeat=region->weekMode;
			g_nvRegion[i].circle.latitude=region->circle.latitude;
			g_nvRegion[i].circle.longitude=region->circle.longitude;
			g_nvRegion[i].type=region->type;
			g_nvRegion[i].counts=region->counts;
			g_nvRegion[i].contactid=region->contactid;
			
			#if defined(__XF_WS_VENDOR_PS_HJYHEX__)
			g_nvRegion[i].in_en=region->in_en;
			g_nvRegion[i].out_en=region->out_en;
			g_nvRegion[i].warningtype=region->warningtype;
			#endif	
			
			memcpy(g_nvRegion[i].point, region->point, sizeof(gps_point_t)*POINT_MAX);
			
			strcpy((char*)g_nvRegion[i].id, region->id);
			strcpy((char*)g_nvRegion[i].start, region->time[0].start);
			strcpy((char*)g_nvRegion[i].end, region->time[0].end);
			strcpy((char*)g_nvRegion[i].start1, region->time[1].start);
			strcpy((char*)g_nvRegion[i].end1, region->time[1].end);
			WS_PRINTF("wsRegion_SetList -(%d)success:%s",i, region->id);
			
			break;

		}
	
    }
    
    myNVMgr_SetWsRegion(g_nvRegion, WS_REGION_MAX_COUNT);
	WS_PRINTF("wsRegion_SetList -save to nvm done");

}


void wsRegion_DelList(char *id)
{
    int i;

    if(id==NULL)
    {
        return;
    }


    for(i=0; i<WS_REGION_MAX_COUNT; i++)
    {
		if(strcmp(id,g_nvRegion[i].id)==0)
		{
			g_nvRegion[i].is_on=false;
			g_nvRegion[i].overstate=REGIONAL_OVER_NONE;
			WS_PRINTF("wsRegion_DelList (i:%d)-success:%s",i,id);
			break;
		}
    }
	
    myNVMgr_SetWsRegion(g_nvRegion, WS_REGION_MAX_COUNT);
	WS_PRINTF("wsRegion_DelList -save to nvm done");

}

void wsRegion_init(void )
{
    static bool init_region_done = false;
    
    if(init_region_done==false)
    {
        //int i = 0;            
        memset((void*)&g_nvRegion[0], 0x00, WS_REGION_MAX_COUNT*sizeof(ws_nv_region));
        myNVMgr_GetWsRegion(&g_nvRegion[0], WS_REGION_MAX_COUNT);
		for(int i=0; i<WS_REGION_MAX_COUNT; i++)
		{
			g_nvRegion[i].overstate=REGIONAL_OVER_NONE;
		}
        init_region_done = true;
    }
}

bool wsRegion_CheckPonit_OverSide(gps_point_t * curr_point ,char* area_id,uint8_t* state)
{
    int i;
	bool ret= false;
    hal_rtc_t rtc_curr;
    
    wsRegion_init();
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);
	
	WS_PRINTF("wsRegion_CheckPonit_OverSide-- long:%f,lati:%f",curr_point->longitude,curr_point->latitude);

    for(i=0; i<WS_REGION_MAX_COUNT; i++)
    {
		if(g_nvRegion[i].is_on)
		{
			uint8_t week = 0;
			bool invalidTime=false;
			hal_rtc_t start1 = {0};
			hal_rtc_t end1 ={0};
			hal_rtc_t start2 = {0};
			hal_rtc_t end2 ={0};

			week = (uint8_t)rtc_curr.tm_wday;
			WS_PRINTF("wsRegion_CheckPonit_OverSide-(i:%d)- ,repeat:%d,%s,%s,%s,%s", i,g_nvRegion[i].repeat,g_nvRegion[i].start,g_nvRegion[i].end, g_nvRegion[i].start1,g_nvRegion[i].end1);
			if(0 == (g_nvRegion[i].repeat & (1<<week)))
			{
				WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,repeat -- false--continue to check next");
				continue;
			}

			WatchUtility_StrToJulianTime(g_nvRegion[i].start, &start1);
			WatchUtility_StrToJulianTime(g_nvRegion[i].end, &end1);
			WatchUtility_StrToJulianTime(g_nvRegion[i].start1, &start2);
			WatchUtility_StrToJulianTime(g_nvRegion[i].end1, &end2);

			if(0 <= wsMuteTime_CompareJulianTime(&rtc_curr, &start1) && 0 < wsMuteTime_CompareJulianTime(&end1, &rtc_curr)){
				invalidTime=true;
			}	
			if(0 <= wsMuteTime_CompareJulianTime(&rtc_curr, &start2) && 0 < wsMuteTime_CompareJulianTime(&end2, &rtc_curr)){
				invalidTime= true;
			}	
			if(invalidTime)
			{
			
				WS_PRINTF("wsRegion_CheckPonit_OverSide-- in valid time ,so compare point in round or polygon(%d)",g_nvRegion[i].type);
				if(g_nvRegion[i].type==REGIONAL_TYPE_ROUND)
				{
					WS_PRINTF("wsRegion_CheckPonit_OverSide-- round(long:%f,lati:%f)-raduis:%d",g_nvRegion[i].circle.longitude,g_nvRegion[i].circle.latitude,g_nvRegion[i].radius);
					if(Gps_Distance(curr_point->latitude,curr_point->longitude,g_nvRegion[i].circle.latitude,g_nvRegion[i].circle.longitude)>(g_nvRegion[i].radius+100))
					{
						#if defined(__XF_WS_VENDOR_PS_HJYHEX__)
							if(g_nvRegion[i].out_en==0)
								return ret;
						#endif
					  	ret=true;
						strcpy(area_id,g_nvRegion[i].id);
						*state =REGIONAL_OVER_OUT;
						WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,OVER OUT round side old state:%d",g_nvRegion[i].overstate);
						if(g_nvRegion[i].overstate!=REGIONAL_OVER_OUT)
						{
							g_nvRegion[i].overstate=REGIONAL_OVER_OUT;
							MMI_ModemAdp_WS_Region_OverSide_Event();
						}
					}
					else
					{
						#if defined(__XF_WS_VENDOR_PS_HJYHEX__)
							if(g_nvRegion[i].in_en==0)
								return ret;
						#endif
						ret=true;
						strcpy(area_id,g_nvRegion[i].id);
						*state =REGIONAL_OVER_IN;
						WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,OVER IN round side:old state:%d",g_nvRegion[i].overstate);
						if(g_nvRegion[i].overstate!=REGIONAL_OVER_IN)
						{
							g_nvRegion[i].overstate=REGIONAL_OVER_IN;
							MMI_ModemAdp_WS_Region_OverSide_Event();
							
						}
					}
				}
				else if(g_nvRegion[i].type==REGIONAL_TYPE_POLYGON)
				{
					WS_PRINTF("wsRegion_CheckPonit_OverSide--REGIONAL_TYPE_POLYGON-pcnt:%d",g_nvRegion[i].counts);
					if(!Gps_PtInPolygon(curr_point,g_nvRegion[i].point,g_nvRegion[i].counts))
					{
					  	ret=true;
						strcpy(area_id,g_nvRegion[i].id);
						*state =REGIONAL_OVER_OUT;
						WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,OVER OUT polygon side old state:%d",g_nvRegion[i].overstate);
						if(g_nvRegion[i].overstate!=REGIONAL_OVER_OUT)
						{
							g_nvRegion[i].overstate=REGIONAL_OVER_OUT;
							MMI_ModemAdp_WS_Region_OverSide_Event();
						}
					}
					else
					{
						ret=true;
						strcpy(area_id,g_nvRegion[i].id);
						*state =REGIONAL_OVER_IN;
						WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,OVER IN polygon side:old state:%d",g_nvRegion[i].overstate);
						if(g_nvRegion[i].overstate!=REGIONAL_OVER_IN)
						{
							g_nvRegion[i].overstate=REGIONAL_OVER_IN;
							MMI_ModemAdp_WS_Region_OverSide_Event();
							
						}
					}
				}
				else if(g_nvRegion[i].type==REGIONAL_TYPE_RECTANGLE)
				{
					WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,need add for rectangle");
					/*just for E...N... */
					if((g_nvRegion[i].point[0].longitude<=curr_point->longitude<=g_nvRegion[i].point[1].longitude)&&(g_nvRegion[i].point[1].latitude<=curr_point->latitude<=g_nvRegion[i].point[0].latitude))
					{
						ret=true;
						strcpy(area_id,g_nvRegion[i].id);
						*state =REGIONAL_OVER_IN;
						WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,OVER IN rectangle side:old state:%d",g_nvRegion[i].overstate);
						if(g_nvRegion[i].overstate!=REGIONAL_OVER_IN)
						{
							g_nvRegion[i].overstate=REGIONAL_OVER_IN;
							MMI_ModemAdp_WS_Region_OverSide_Event();
							
						}
					}
					else
					{
					  	ret=true;
						strcpy(area_id,g_nvRegion[i].id);
						*state =REGIONAL_OVER_OUT;
						WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,OVER OUT polygon side old state:%d",g_nvRegion[i].overstate);
						if(g_nvRegion[i].overstate!=REGIONAL_OVER_OUT)
						{
							g_nvRegion[i].overstate=REGIONAL_OVER_OUT;
							MMI_ModemAdp_WS_Region_OverSide_Event();
						}
					}

				}
			}
			else
			{
				WS_PRINTF("wsRegion_CheckPonit_OverSide-- ,out of time");
			}
		
		}
    }


	return ret;

}

bool wsRegion_Check_have_valid_data(void)
{
    int i;
    hal_rtc_t rtc_curr;
    
    wsRegion_init();
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);
	
	WS_PRINTF("wsRegion_Check_have_valid_data");

    for(i=0; i<WS_REGION_MAX_COUNT; i++)
    {
		if(g_nvRegion[i].is_on)
		{
			uint8_t week = 0;
			bool invalidTime=false;
			hal_rtc_t start1 = {0};
			hal_rtc_t end1 ={0};
			hal_rtc_t start2 = {0};
			hal_rtc_t end2 ={0};

			week = (uint8_t)rtc_curr.tm_wday;
			if(0 == (g_nvRegion[i].repeat & (1<<week)))
			{
				WS_PRINTF("wsRegion_Check_have_valid_data-- ,repeat -- false--continue to check next");
				continue;
			}

			WatchUtility_StrToJulianTime(g_nvRegion[i].start, &start1);
			WatchUtility_StrToJulianTime(g_nvRegion[i].end, &end1);
			WatchUtility_StrToJulianTime(g_nvRegion[i].start1, &start2);
			WatchUtility_StrToJulianTime(g_nvRegion[i].end1, &end2);

			if(0 <= wsMuteTime_CompareJulianTime(&rtc_curr, &start1) && 0 < wsMuteTime_CompareJulianTime(&end1, &rtc_curr)){
				invalidTime=true;
			}	
			if(0 <= wsMuteTime_CompareJulianTime(&rtc_curr, &start2) && 0 < wsMuteTime_CompareJulianTime(&end2, &rtc_curr)){
				invalidTime= true;
			}	
			if(invalidTime)
			{
			
				WS_PRINTF("wsRegion_Check_have_valid_data-- in valid time ");
				return true;
			}

		
		}
    }

	return false;

}


#endif

#if USE_LV_WATCH_CT_READY_MODE!=0
void myNVMgr_SetWsReadyMode(ws_nv_readymode *mode)
{
    uint16_t   len = sizeof(ws_nv_readymode);
    if(mode)
    {
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_readymode), len, (uint8_t*)mode);
    }

}
void myNVMgr_GetWsReadyMode(ws_nv_readymode *mode)
{
    uint16_t   len = sizeof(ws_nv_readymode);    
    
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_readymode), len, (uint8_t*)mode);
}


 ws_nv_readymode* wsGetCurrReadyMode(void)
 {
	return &g_nvReadyMode;
 }

 void wsReadyMode_SaveData( ws_nv_readymode *src,bool savetonv)
{
    if(NULL == src){
        return;
    }
    
    memset((void*)&g_nvReadyMode, 0x00, sizeof(ws_nv_readymode));
	
    g_nvReadyMode.is_on= src->is_on;
    strcpy((char*)g_nvReadyMode.start, src->start);
    strcpy((char*)g_nvReadyMode.end, src->end);
	if(savetonv)
	{
		myNVMgr_SetWsReadyMode(&g_nvReadyMode);
	}
	
}
 
bool wsReadyMode_isInValidPeriod(hal_rtc_t *julian_date)
{
    int i=0;
    
	uint8_t week = 0;
	hal_rtc_t tstart = {0};
	hal_rtc_t tend ={0};
	
    if(!julian_date)
    {
        return false;
    }

	week = (uint8_t)julian_date->tm_wday;
	
	WS_PRINTF("wsReadyMode_isInValidPeriod-- ,repeat:%d,%s,%s", g_nvReadyMode.is_on,g_nvReadyMode.start,g_nvReadyMode.end);
	if(0 == g_nvReadyMode.is_on)
	{
		WS_PRINTF("wsReadyMode_isInValidPeriod--is_on false");
		return false;
	}

	WatchUtility_StrToJulianTime(g_nvReadyMode.start, &tstart);
	WatchUtility_StrToJulianTime(g_nvReadyMode.end, &tend);

	if(0 <= wsMuteTime_CompareJulianTime(julian_date, &tstart) && 0 < wsMuteTime_CompareJulianTime(&tend, julian_date)){
		WS_PRINTF("wsReadyMode_isInValidPeriod--1111--true");
		return true;
	}	

	if(0 <= wsMuteTime_CompareJulianTime(&tstart, &tend))
	{	
		/*23:00 --06:00*/
		if(0 <= wsMuteTime_CompareJulianTime(julian_date, &tstart) || 0 < wsMuteTime_CompareJulianTime(&tend, julian_date)){
			WS_PRINTF("wsReadyMode_isInValidPeriod--2222--true");
			return true;
		}	
	}
	
    return false;
}

bool wsReadyMode_CheckNow(void)
{
    bool inValidTime=false;
    hal_rtc_t rtc_curr;
    
    wsMuteTime_init(false);
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);

	inValidTime=wsReadyMode_isInValidPeriod(&rtc_curr);
	
	WS_PRINTF("wsReadyMode_CheckNow inValidTime:%d", inValidTime);

    return inValidTime;

}

#endif

#if USE_LV_WATCH_FIXED_LOCATE_TIMERANGE_DISABLE!=0
void myNVMgr_SetWsLocateDisableTime(ws_nv_locatedisable *disable, uint16_t cnt)
{
    uint16_t   len = NV_WS_LOCATEDISABLE_NUM*sizeof(ws_nv_locatedisable);
    ws_nv_locatedisable *nv_disable = (ws_nv_locatedisable *)malloc(len);

    if(nv_disable)
    {
        uint16_t len_in = cnt*sizeof(ws_nv_locatedisable);
        memset(nv_disable, 0, len);
        memcpy((void*)nv_disable, (void*)disable, (len_in<=len) ? len_in : len);
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_locateDisables), len, (uint8_t*)nv_disable);
        free(nv_disable);
    }

}

void myNVMgr_GetWsLocateDisableTime(ws_nv_locatedisable *disable, uint16_t cnt)
{
    uint16_t   len = ((cnt<=NV_WS_LOCATEDISABLE_NUM) ? cnt : NV_WS_LOCATEDISABLE_NUM)*sizeof(ws_nv_locatedisable);    
    
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, ws_locateDisables), len, (uint8_t*)disable);
}

void wsLocateDisableTime_init(bool isForce)
{
    static bool init_locate_done = false;
    
    if(init_locate_done && !isForce)
    {
        return;
    }
    else 
    {
        //int i = 0;            
        memset((void*)&g_nvLocateDisablePeriod[0], 0x00, WS_LOACTE_DISBALE_LIST_MAX_COUNT*sizeof(ws_nv_locatedisable));
        myNVMgr_GetWsMuteTime(&g_nvLocateDisablePeriod[0], WS_LOACTE_DISBALE_LIST_MAX_COUNT);
       init_locate_done = true;

    }
}

static void wsLocateDsiableTime_CopyData(ws_nv_locatedisable *dest, ws_locatedisable *src)
{
    if(NULL == dest || NULL == src){
        return;
    }
    
    dest->is_set = TRUE;
    dest->id = src->id;
    dest->is_on = src->is_on;
    dest->repeat = src->repeat;
    strcpy((char*)dest->start, src->start);
    strcpy((char*)dest->end, src->end);
}

void wsLocateDisableTime_SetList(ws_locatedisable_list *locatedisable_list)
{
    int i;

    if(locatedisable_list==NULL)
    {
        return;
    }

    memset((void*)&g_nvLocateDisablePeriod[0], 0x00, sizeof(ws_nv_locatedisable)*WS_LOACTE_DISBALE_LIST_MAX_COUNT);

    for(i=0; i<WS_LOACTE_DISBALE_LIST_MAX_COUNT && i<locatedisable_list->count; i++)
    {
        wsLocateDsiableTime_CopyData(&g_nvLocateDisablePeriod[i], locatedisable_list->Ldisables+i);
    }
    
    myNVMgr_SetWsLocateDisableTime(g_nvLocateDisablePeriod, i);
	
#if USE_LV_WATCH_ZM_DISP_DIS_OR_SYNC_TIME!=0
	zm_timeInfo_update();
#endif
	lv_mem_free(locatedisable_list->Ldisables);

}
bool wsLocateTime_ShouldDisable(hal_rtc_t *julian_date, ws_nv_locatedisable *cell)
{
    uint8_t week = 0;
    hal_rtc_t start = {0};
    hal_rtc_t end ={0};

    if(false == cell->is_set || false == cell->is_on){
        return false;
    }
    week = (uint8_t)julian_date->tm_wday;
	
	WS_PRINTF("CWatchService--ShouldDisable week:%d,repeat:%x,%s,%s", week, cell->repeat, cell->start, cell->end);
    if(0 == (cell->repeat & (1<<week)))
    {
        return false;
    }

    WatchUtility_StrToJulianTime(cell->start, &start);
    WatchUtility_StrToJulianTime(cell->end, &end);

    if(0 <= wsMuteTime_CompareJulianTime(julian_date, &start) && 0 < wsMuteTime_CompareJulianTime(&end, julian_date)){
        return true;
    }   

	if(0 < wsMuteTime_CompareJulianTime(&start, &end))
	{	
		/*23:00 --06:00, can not have =,09:00:00 --09:00:00 will make error*/
		if(0 <= wsMuteTime_CompareJulianTime(julian_date, &start) || 0 < wsMuteTime_CompareJulianTime(&end, julian_date)){
			WS_PRINTF("wsLocateTime_ShouldDisable--2222--true");
			return true;
		}	
	}


	
    return false;
}

bool wsLocateDisable_isInTimePeriod(hal_rtc_t *julian_date)
{
    int i=0;
    
    if(!julian_date)
    {
        return false;
    }
    
    for(i=0;i<WS_LOACTE_DISBALE_LIST_MAX_COUNT;i++){
        if(true == wsLocateTime_ShouldDisable(julian_date, &g_nvLocateDisablePeriod[i])){
            //WS_PRINTF("CWatchService--wsMuteTime_isMutePeriod >> should mute", 0, 0, 0);
            return true;
        }
    }
    return false;
}

bool wsLocateIsDisableTime_CheckNow(void)
{
    bool mute=false;
    hal_rtc_t rtc_curr;
    
    wsLocateDisableTime_init(false);
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);

	mute=wsLocateDisable_isInTimePeriod(&rtc_curr);
    return mute;

}
#endif

#if USE_LV_WATCH_ZM_SYNCTASK_TIME!=0
static void wsSyncTaskTime_CopyData(ws_nv_realtasktime *dest, ws_synctask *src)
{
    if(NULL == dest || NULL == src){
        return;
    }
    
    dest->is_on = src->is_on;
    dest->repeat = src->repeat;
    strcpy((char*)dest->start, src->start);
    strcpy((char*)dest->end, src->end);
}

void wsSyncTaskTime_SetList(ws_synctask_list *realtask_time_list)
{
    int i;

    if(realtask_time_list==NULL)
    {
        return;
    }

    memset((void*)&g_nvRealTaskPeriod[0], 0x00, sizeof(ws_nv_realtasktime)*WS_REALTASK_TIME_LIST_MAX_COUNT);

    for(i=0; i<WS_REALTASK_TIME_LIST_MAX_COUNT && i<realtask_time_list->count; i++)
    {
        wsSyncTaskTime_CopyData(&g_nvRealTaskPeriod[i], realtask_time_list->tTime+i);
    }
    
    //myNVMgr_SetWsLocateDisableTime(g_nvLocateDisablePeriod, i);
	#if USE_LV_WATCH_ZM_DISP_DIS_OR_SYNC_TIME!=0
	zm_timeInfo_update();
	#endif

	lv_mem_free(realtask_time_list->tTime);

}
bool wsSyncTaskTime_ShouldEnable(hal_rtc_t *julian_date, ws_nv_realtasktime *cell)
{
    uint8_t week = 0;
    hal_rtc_t start = {0};
    hal_rtc_t end ={0};

    if(false == cell->is_on){
        return false;
    }
    week = (uint8_t)julian_date->tm_wday;
	
	WS_PRINTF("CWatchService--SyncTaskTime- week:%d,repeat:%x,%s,%s", week, cell->repeat, cell->start, cell->end);
	/*
    if(0 == (cell->repeat & (1<<week)))
    {
        return false;
    }
    */

    WatchUtility_StrToJulianTime(cell->start, &start);
    WatchUtility_StrToJulianTime(cell->end, &end);

    if(0 <= wsMuteTime_CompareJulianTime(julian_date, &start) && 0 < wsMuteTime_CompareJulianTime(&end, julian_date)){
        return true;
    }   

	if(0 < wsMuteTime_CompareJulianTime(&start, &end))
	{	
		/*23:00 --06:00, can not have =,09:00:00 --09:00:00 will make error*/
		if(0 <= wsMuteTime_CompareJulianTime(julian_date, &start) || 0 < wsMuteTime_CompareJulianTime(&end, julian_date)){
			WS_PRINTF("wsSyncTaskTime_ShouldEnable--2222--true");
			return true;
		}	
	}

    return false;
}

bool wsSyncTask_isInTimePeriod(hal_rtc_t *julian_date)
{
    int i=0;
    
    if(!julian_date)
    {
        return false;
    }
    
    for(i=0;i<WS_REALTASK_TIME_LIST_MAX_COUNT;i++){
        if(true == wsSyncTaskTime_ShouldEnable(julian_date, &g_nvRealTaskPeriod[i])){
            //WS_PRINTF("CWatchService--wsMuteTime_isMutePeriod >> should mute", 0, 0, 0);
            return true;
        }
    }
    return false;
}

bool wsSyncTaskTime_CheckNow(void)
{
    bool isInTime=false;
    hal_rtc_t rtc_curr;
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);

	isInTime=wsSyncTask_isInTimePeriod(&rtc_curr);
    return isInTime;
}
#endif
#if USE_LV_WATCH_ZM_DISP_DIS_OR_SYNC_TIME!=0
char * wsGetZM_disable_sync_time_string(uint8_t type)
{
	char *pData=NULL;
	uint8_t pos=0;
	#if USE_LV_WATCH_FIXED_LOCATE_TIMERANGE_DISABLE!=0
	if(type==1)
	{
		pData=malloc(sizeof(ws_nv_locatedisable)*WS_LOACTE_DISBALE_LIST_MAX_COUNT);
		memset(pData,0,sizeof(ws_nv_locatedisable)*WS_LOACTE_DISBALE_LIST_MAX_COUNT);
	    for(int i=0; i<WS_LOACTE_DISBALE_LIST_MAX_COUNT; i++)
	    {
	        if(strlen(g_nvLocateDisablePeriod[i].start)<=0) break;
			pos+= sprintf(pData+pos,"%s-%s\n", g_nvLocateDisablePeriod[i].start,g_nvLocateDisablePeriod[i].end);
			
	    }
	}
	#endif	
	#if USE_LV_WATCH_ZM_SYNCTASK_TIME!=0
	if(type==2)
	{
		pData=malloc(sizeof(ws_nv_realtasktime)*WS_REALTASK_TIME_LIST_MAX_COUNT);
		memset(pData,0,sizeof(ws_nv_realtasktime)*WS_REALTASK_TIME_LIST_MAX_COUNT);
	    for(int i=0; i<WS_REALTASK_TIME_LIST_MAX_COUNT; i++)
	    {
	        if(strlen(g_nvRealTaskPeriod[i].start)<=0) break;
			pos+= sprintf(pData+pos,"%s-%s\n", g_nvRealTaskPeriod[i].start,g_nvRealTaskPeriod[i].end);
			
	    }
	}
	#endif	

 	return pData;

}
#endif

static void wsMuteTime_CopyData(ws_nv_mute *dest, ws_mute *src)
{
    if(NULL == dest || NULL == src){
        return;
    }
    
    dest->is_set = TRUE;
    dest->id = src->id;
    dest->is_on = src->is_on;
    dest->repeat = src->repeat;
    strcpy((char*)dest->start, src->start);
    strcpy((char*)dest->end, src->end);
}

#if 0
static void wsMuteTime_CopyDat(ws_mute  *dest, ws_nv_mute *src)
{
    if(NULL == dest || NULL == src){
        return;
    }
    
   // dest->is_set = TRUE;
    dest->id = src->id;
    dest->is_on = src->is_on;
    dest->repeat = src->repeat;
    strcpy((char*)dest->start, src->start);
    strcpy((char*)dest->end, src->end);
}
#endif

void wsMuteTime_SetList(ws_mute_list *mute_list)
{
    int i;

    if(mute_list==NULL)
    {
        return;
    }

    memset((void*)&g_nvMutePeriod[0], 0x00, sizeof(ws_nv_mute)*WS_MUTE_MAX_COUNT);

    for(i=0; i<WS_MUTE_MAX_COUNT && i<mute_list->count; i++)
    {
        wsMuteTime_CopyData(&g_nvMutePeriod[i], mute_list->mutes+i);
    }
    
    myNVMgr_SetWsMuteTime(g_nvMutePeriod, i);

	lv_mem_free(mute_list->mutes);

}

void wsMuteTime_Clear(void)
{
    memset((void*)&g_nvMutePeriod[0], 0x00, sizeof(ws_nv_mute)*WS_MUTE_MAX_COUNT);
    
    myNVMgr_SetWsMuteTime(g_nvMutePeriod, WS_MUTE_MAX_COUNT);
}


#if 0
void wsMuteTime_GetList(ws_mute_list *mute_list, uint8_t len)
{
    int i;

    if(mute_list==NULL)
    {
        return;
    }

   // memset((void*)&g_nvMutePeriod[0], 0x00, sizeof(ws_nv_mute)*WS_MUTE_MAX_COUNT);

    for(i=0; i<WS_MUTE_MAX_COUNT && i<len; i++)
    {
        wsMuteTime_CopyDat(mute_list->mutes+i, &g_nvMutePeriod[i]);		
    }
      
}
#endif


#if USE_LV_WATCH_COURSE != 0
void wsCourse_SetList(ws_course_list *course_list)
{
    int i;

    if(course_list==NULL)
    {
        return;
    } 
    
    myNVMgr_SetWsCourse(course_list->course);

	lv_mem_free(course_list->course);

}
#endif /* USE_LV_WATCH_COURSE */




void wsMuteTime_init(bool isForce)
{
    static bool init_done = false;
    
    if(init_done && !isForce)
    {
        return;
    }
    else 
    {
        //int i = 0;            
        memset((void*)&g_nvMutePeriod[0], 0x00, WS_MUTE_MAX_COUNT*sizeof(ws_nv_mute));
        myNVMgr_GetWsMuteTime(&g_nvMutePeriod[0], WS_MUTE_MAX_COUNT);
        if(mute_time_task_p==NULL)
        {
            mute_time_task_p = lv_task_create(WatchUtility_MuteTimeTask, 5000, LV_TASK_PRIO_MID, NULL);
        }
        init_done = true;

    }
}


int wsMuteTime_CompareJulianTime(hal_rtc_t *time1, hal_rtc_t *time2)
{
	if (time1->tm_hour != time2->tm_hour) return (time1->tm_hour - time2->tm_hour);
	if (time1->tm_min != time2->tm_min) return (time1->tm_min - time2->tm_min);
	
	return 0;
}


bool wsMuteTime_ShouldMute(hal_rtc_t *julian_date, ws_nv_mute *cell)
{
    uint8_t week = 0;
    hal_rtc_t start = {0};
    hal_rtc_t end ={0};

    if(false == cell->is_set || false == cell->is_on){
        return false;
    }
    week = (uint8_t)julian_date->tm_wday;
	
//	WS_PRINTF("CWatchService-- week:%d,repeat:%x,%s,%s", week, cell->repeat, cell->start, cell->end);
    if(0 == (cell->repeat & (1<<week)))
    {
        return false;
    }

    WatchUtility_StrToJulianTime(cell->start, &start);
    WatchUtility_StrToJulianTime(cell->end, &end);

    if(0 <= wsMuteTime_CompareJulianTime(julian_date, &start) && 0 < wsMuteTime_CompareJulianTime(&end, julian_date)){
        return true;
    }   
    return false;
}


bool wsMuteTime_isMutePeriod(hal_rtc_t *julian_date)
{
    int i=0;
    
    if(!julian_date)
    {
        return false;
    }
    
    for(i=0;i<WS_MUTE_MAX_COUNT;i++){
        if(true == wsMuteTime_ShouldMute(julian_date, &g_nvMutePeriod[i])){
            //WS_PRINTF("CWatchService--wsMuteTime_isMutePeriod >> should mute", 0, 0, 0);
            return true;
        }
    }
    return false;
}

//extern int tm_mday_back;

bool wsMuteTime_CheckNow(void (*callback)(bool on))
{
    bool mute=false;
    hal_rtc_t rtc_curr;
    
    wsMuteTime_init(false);
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);

	mute=wsMuteTime_isMutePeriod(&rtc_curr);
    if(callback)
    {
        callback(mute);
    }  

    return mute;

}

/*eric add to check in watch power up start*/
void wsMuteTime_Check_For_Powerup(void)
{
    bool mute=false;
    hal_rtc_t rtc_curr;
    
	memset((void*)&g_nvMutePeriod[0], 0x00, WS_MUTE_MAX_COUNT*sizeof(ws_nv_mute));
	myNVMgr_GetWsMuteTime(&g_nvMutePeriod[0], WS_MUTE_MAX_COUNT);
    
    Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);

    mute=wsMuteTime_isMutePeriod(&rtc_curr);
	
    g_ws_is_mute = mute;
	#if USE_LV_WATCH_WS_KAER != 1
//开机后判断是课堂模式,把调用的触摸失效，给屏蔽掉，
	if(mute)
	{
		#if USE_LV_WATCH_CT_MUTETIME_TP_ENABLE!=0
		_lv_indev_enable(LV_INDEV_TYPE_POINTER, true);
		#else
		_lv_indev_enable(LV_INDEV_TYPE_POINTER, false);
		#endif
	}
	#endif

}
/*eric add to check in watch power up end*/




bool WatchUtility_CheckMuteUiChange(void)
{
	bool mute = wsMuteTime_CheckNow(NULL);
	
	//WS_PRINTF("WatchUtility_CheckMuteUiChange()g_ws_is_mute=%d mute=%d\n", g_ws_is_mute , mute);
		
	if(!watch_is_ready())
	{
		return false;
	}

	if(g_ws_is_mute != mute)
		return true;
	return false;
}

bool WatchUtility_CheckMuteTime(void)
{
    bool mute = wsMuteTime_CheckNow(NULL);

	WS_PRINTF("WatchUtility_CheckMuteTime()g_ws_is_mute=%d mute=%d\n", g_ws_is_mute , mute);
    
    if(!watch_is_ready())
    {
        return mute;
    }

    #if USE_LV_WATCH_SOS != 0
    if(SOS_OFF != get_sos_status())
    {
        return mute;
    }
    #endif
#if USE_WS_DEVICE_LOGIN_UNACTVIE!=0
	if (WatchUtility_IsLoginUnactive())
	{
		return false;
	}
#endif	

    if(g_ws_is_mute != mute)
    {
        if(mute)
        {
			if(lv_watch_get_activity_obj(ACT_ID_POWER_OFF_CHARGING)!=NULL)
			{
		        return mute;
	    	}
		
		#if USE_LV_WATCH_SLIDE_UNLOCK !=0
			if(lv_watch_get_activity_obj(ACT_ID_SLIDE_UNLOCK)!=NULL)
			{
				lv_obj_t *lock_obj = lv_watch_get_activity_obj(ACT_ID_SLIDE_UNLOCK);
				lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(lock_obj);
				if(NULL != ext->prepare_destory) {
					ext->prepare_destory(lock_obj);
				}
				lv_obj_del(lock_obj);
			}
		#endif
			#if USE_LV_WATCH_MUTE_APP != 0    
			g_ws_is_mute = mute;
			#endif

            watch_pre_entry_locked_screen(LCS_MUTE_TIME);
			
			#if USE_LV_WATCH_MUTETIME_DIALOG != 0	 
			mute_time_update_activity(mute);
			#else 
				#if !USE_LV_WATCH_MUTE_APP 
					_lv_indev_enable(LV_INDEV_TYPE_POINTER, false);
				#endif
			#endif
					    //enter mute
		    #if USE_LV_WATCH_PLATFORM_LEZHI!= 0	&& USE_LV_WATCH_PLATFORM_QUANTONG!=1
			if(ke_GetLezhiVerFlag()==2)  //jiangxi FF
			{
			    CWathcService_Mute_SetCallinLimited_JX(1);
			}
		    #endif
        }
        else
        {
            #if 1//USE_LV_WATCH_MUTETIME_ALARM != 0
            /* Do nothing...*/
            #else 
            alarm_init();
            #endif 
			
			#if USE_LV_WATCH_MUTETIME_DIALOG != 0	 
			mute_time_update_activity(mute);
			#else 				
				#if !USE_LV_WATCH_MUTE_APP 
	            	_lv_indev_enable(LV_INDEV_TYPE_POINTER, true);
				#endif
			#endif	
			#if USE_LV_WATCH_MUTE_APP != 0    
			g_ws_is_mute = mute;
			#endif

				lv_watch_go_home();
			#if USE_LV_WATCH_WS_KAER !=0
			//课堂免扰解锁后重新创建菜单
				launcher_recreate();
			#endif
						//exit mute
			#if USE_LV_WATCH_PLATFORM_LEZHI!= 0	&& USE_LV_WATCH_PLATFORM_QUANTONG!=1
			if(ke_GetLezhiVerFlag()==2)  //jiangxi FF
			{
			    CWathcService_Mute_SetCallinLimited_JX(0);
			}
			#endif
        }
        
    }
	else if (mute)
	{
#if USE_LV_WATCH_MUTETIME_DIALOG != 0    
		mute_time_update_activity(mute);
#endif  
	}
	
#if USE_LV_WATCH_CT_MUTETIME_TP_ENABLE!=0
		_lv_indev_enable(LV_INDEV_TYPE_POINTER, true);
#endif

    g_ws_is_mute = mute;
    
    return mute;
    
}

bool WatchUtility_IsMuteTime(void)
{
	#if USE_WS_DEVICE_LOGIN_UNACTVIE!=0
	if (WatchUtility_IsLoginUnactive())
	{
		printf("WatchUtility_IsMuteTime --in login unactive return false");
		return false;
	}
	#endif	

	printf("WatchUtility_IsMuteTime g_ws_is_mute:%d", g_ws_is_mute);
    return g_ws_is_mute;
}
#endif 

#if defined(__XF_MORE_FUN_SUPPORT__)
void WatchUtility_MoreFun_Proc(void)
{
    more_fun_dev_data_up("battery", Hal_Battery_Get_Status());
}
#endif

void wsWatchUtility_ParamInit(void)
{
#if USE_LV_WATCH_POWER_PERIOD != 0	
    myNVMgr_GetWsPowerPeriod(&g_nvPowerPeriod);
#endif    
#if USE_LV_WATCH_KAER_SWITCH != 0	
	myNVMgr_GetWsSwitch(&g_nvSwitch);
#endif  
#if USE_LV_WATCH_WS_KAER != 0	
		myNVMgr_GetCallinLimited(&gCallinLimited);
#endif  
#if USE_LV_WATCH_WS_KAER != 0
	myNVMgr_GetFamilyNoSer(gFamilyNoSer);
#endif
#if USE_LV_WATCH_WHITENAME_LIST_TIME_REPEAT!=0
	myNVMgr_GetWsWhiteContactTime(&g_nvWhiteNamePeriod);
#endif
#if USE_LV_WATCH_CT_READY_MODE!=0
	myNVMgr_GetWsReadyMode(&g_nvReadyMode);
#endif
#if USE_LV_WATCH_CT_STUDENT!=0
	myNVMgr_GetWsStudentInfo(&gStudentInfo);
#endif
#if USE_LV_WATCH_OFFLINE_AT_NIGHT_ENABLE!=0
	myNVMgr_GetNightOfflineStatus(&g_NightOffline);
#endif

#if USE_LV_WATCH_FIXED_LOCATE_TIMERANGE_DISABLE!=0
	wsLocateDisableTime_init(true);
#endif    
#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0
	myNVMgr_GetWsPwroffClock();
#endif


}

#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0
void myNVMgr_GetWsPwroffClock()
{
	memset((void*)&g_poweroff_clock, 0x00, sizeof(nv_watch_alarm_t));
	UI_NV_Read_Req(NV_SECTION_UI_POWEROFF_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&g_poweroff_clock);
}

void myNVMgr_SetWsPwroffClock()
{
	UI_NV_Write_Req(NV_SECTION_UI_POWEROFF_ALARM, 0, sizeof(nv_watch_alarm_t), (UINT8 *)&g_poweroff_clock);
}

void WatchUtility_SavePwroff_info(nv_watch_alarm_info_t alarm_info,uint8_t indexid,uint8_t is_clear)
{
	if(is_clear == 1)
	{
		memset((void*)&g_poweroff_clock, 0x00, sizeof(nv_watch_alarm_t));
	}
	else
	{
		g_poweroff_clock.alarm_info[indexid].hour = alarm_info.hour;
		g_poweroff_clock.alarm_info[indexid].min = alarm_info.min;
		g_poweroff_clock.alarm_info[indexid].repeat_bitmap= alarm_info.repeat_bitmap;
		g_poweroff_clock.alarm_info[indexid].repeat_bitmap |= 0x80;
		g_poweroff_clock.alarm_info[indexid].on_off = alarm_info.on_off;
		WS_PRINTF("WatchUtility_SavePwroff_info[%d]=%d,%d,repeat_bitmap=%x,on_off=%d",indexid, g_poweroff_clock.alarm_info[indexid].hour, g_poweroff_clock.alarm_info[indexid].min,g_poweroff_clock.alarm_info[indexid].repeat_bitmap,g_poweroff_clock.alarm_info[indexid].on_off);
		
	}
}

bool wsClockTime_isvalid(hal_rtc_t *julian_date, nv_watch_alarm_info_t *cell)
{
    uint8_t week = 0;

    if(0 == cell->on_off){
        return false;
    }
    week = (uint8_t)julian_date->tm_wday;
	
	WS_PRINTF("wsClockTime_isvalid-- week:%d,repeat:%x,%d,%d", week, cell->repeat_bitmap, cell->hour, cell->min);
    if(0 == (cell->repeat_bitmap & (1<<week)))
    {
        return false;
    }
    if(((uint8_t)(julian_date->tm_hour) == cell->hour) && ((uint8_t)(julian_date->tm_min) == cell->min))
	{
        return true;
    }   
    return false;
}
bool wsIsPowerOffPeriod(void)
{  
	int i = 0;
	hal_rtc_t rtc_curr;
	Hal_Rtc_Gettime(&rtc_curr);
    rtc_calc_weekday(&rtc_curr);
	for(i=0;i<NV_ALARM_MAX_ALARM_NUM;i++)
	{
        if(true == wsClockTime_isvalid(&rtc_curr, &g_poweroff_clock.alarm_info[i]))
		{
            return true;
        }
    }
    return false;
}

#elif USE_LV_WATCH_POWER_PERIOD != 0
bool wsIsPowerOffPeriod(void)
{
    hal_rtc_t rtc_curr;	
    hal_rtc_t pwoff = {0};
	ws_nv_power_period *Period= &g_nvPowerPeriod;
	
    Hal_Rtc_Gettime(&rtc_curr);    
	
	WS_PRINTF("wsIsPowerOffPeriod (hour:%d,min:%d) is_on:%d,pwon:%s,pwoff:%s",rtc_curr.tm_hour,rtc_curr.tm_min,g_nvPowerPeriod.is_on, g_nvPowerPeriod.pwon,g_nvPowerPeriod.pwoff);

    if(false == Period->is_on){
        return false;
    }

    WatchUtility_StrToJulianTime(Period->pwoff, &pwoff);

    if(rtc_curr.tm_hour == pwoff.tm_hour && rtc_curr.tm_min == pwoff.tm_min){
        return true;
    }   
    return false;
}
#endif /* USE_LV_WATCH_POWER_PERIOD */

#if USE_LV_WATCH_POWER_PERIOD != 0 || USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0
void WatchUtility_CheckPowerOnOffTime(void)
{
    bool bPwOff = wsIsPowerOffPeriod();

    WS_PRINTF("WatchUtility_CheckPowerOnOffTime() bPwOff=%d\n", bPwOff);
    
    if(!watch_is_ready())
    {
        return ;
    }

    #if USE_LV_WATCH_SOS != 0
    if(SOS_OFF != get_sos_status())
    {
        return ;
    }
    #endif

	if(bPwOff)
	{   
	#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0
		g_powroff_valid = bPwOff;
	#endif
		WatchUtility_PassivePoweroff(NULL);
	}
}

#endif 
#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0
uint8_t WatchUtility_PowerOff_Isvalid()
{
	return g_powroff_valid;
}
#endif

uint8_t WatchUtility_WeatherMatchPicTypeByString(char *type)
{	
	//strcpy(type,"雷阵雨转雨夹雪");
	#if USE_LV_WATCH_WS_DUER != 0
		char *s=strstr(type, "转");
		if (s) {
			//ws_printf("%x,%x,%x,%x,%x,%x",*(s-3),*(s-2),*(s-1),*s,*(s+1),*(s+2));
			*s = 0;
			//ws_printf("%x,%x,%x,%x,%x,%x",*(s-3),*(s-2),*(s-1),*s,*(s+1),*(s+2));
			//ws_printf("%s ,%s",__FUNCTION__,type);
		}
		
	    if(strstr(type, "晴"))
	    {
	        return WATCH_WEATHER_ID_SUNNY;
	    }
	    else if(strstr(type, "云"))
	    {
	        return WATCH_WEATHER_ID_CLOUDY;
	    }
	    else if(strstr(type, "阴"))
	    {
	        return WATCH_WEATHER_ID_OVERCAST;
	    }
	    else if(strstr(type, "雨夹雪"))
	    {
	        return WATCH_WEATHER_ID_RAIN_AND_WIND;
	    }
	    else if(strstr(type, "冰雹"))
	    {
	        return WATCH_WEATHER_ID_HAILSTONE_RAIN;
	    }
	    else if(strstr(type, "雷阵雨"))
	    {
	        return WATCH_WEATHER_ID_THUNDER;
	    }
	    else if(strstr(type, "风"))
	    {
	        return WATCH_WEATHER_ID_WIND;
	    }
	    else if(strstr(type, "沙"))
	    {
	        return WATCH_WEATHER_ID_SANDSTORM;
	    }
	    else if(strstr(type, "小雨"))
	    {
	        return WATCH_WEATHER_ID_LIGHT_RAIN;
	    }
	    else if(strstr(type, "雨"))
	    {
	        return WATCH_WEATHER_ID_HEAVY_RAIN;
	    }
	    else if(strstr(type, "小雪"))
	    {
	        return WATCH_WEATHER_ID_LITTLE_SNOW;
	    }
	    else if(strstr(type, "雪"))
	    {
	        return WATCH_WEATHER_ID_HEAVY_SNOW;
	    }
	    else if(strstr(type, "雾"))
	    {
	        return WATCH_WEATHER_ID_FOG;
	    }
	    else if(strstr(type, "霾"))
	    {
	        return WATCH_WEATHER_ID_HAZE;
	    }
	#else
	    if(strstr(type, "晴"))
	    {
	        return WATCH_WEATHER_ID_SUNNY;
	    }
	    else if(strstr(type, "云"))
	    {
	        return WATCH_WEATHER_ID_CLOUDY;
	    }
	    else if(strstr(type, "阴"))
	    {
	        return WATCH_WEATHER_ID_CLOUDY;
	    }
	    else if(strstr(type, "小雨"))
	    {
	        return WATCH_WEATHER_ID_LIGHT_RAIN;
	    }
	    else if(strstr(type, "雨"))
	    {
	        return WATCH_WEATHER_ID_HEAVY_RAIN;
	    }
	    else if(strstr(type, "小雪"))
	    {
	        return WATCH_WEATHER_ID_LITTLE_SNOW;
	    }
	    else if(strstr(type, "雪"))
	    {
	        return WATCH_WEATHER_ID_HEAVY_SNOW;
	    }
	    else if(strstr(type, "雾"))
	    {
	        return WATCH_WEATHER_ID_FOG;
	    }
	    else if(strstr(type, "霾"))
	    {
	        return WATCH_WEATHER_ID_HAZE;
	    }
	#endif
    return WATCH_WEATHER_ID_OTHERS;

}

void WatchUtility_WeatherUpdate(ws_weather *ws_wth)
{
    app_adaptor_weather_t weather={};

    memcpy(weather.city, ws_wth->cityName, strlen(ws_wth->cityName) + 1);
    weather.lowest_temperature = (int8_t)atoi(ws_wth->data[0].temL);
    weather.highest_temperature = (int8_t)atoi(ws_wth->data[0].temH);
    weather.current_temperature = (int8_t)atoi(ws_wth->data[0].tem);
    weather.weather_id = ws_wth->data[0].picType;
	memcpy(weather.wind_der, ws_wth->data[0].wind_der, strlen(ws_wth->data[0].wind_der) + 1);
	memcpy(weather.wind, ws_wth->data[0].wind, strlen(ws_wth->data[0].wind) + 1);
	memcpy(weather.date, ws_wth->update_time, strlen(ws_wth->update_time) + 1);

    //app_adaptor_set_weather_req(&weather);
#if USE_LV_WATCH_DIAL != 0
	dial_clock_update_immediately();
#endif    
}

#if USE_LV_WATCH_WS_DUER != 0
void WatchUtility_SetVoiceMsgGroups(ws_group_list *group_list)
{
	if(group_list){
        for(int i=0; i<group_list->cnt; i++){
            if(group_list->group[i].user_list){
                free(group_list->group[i].user_list);
            }
        }
		if(group_list->group){
			free(group_list->group);
		}
	}
}
#endif


#if USE_LV_WATCH_SINGLE_ALARM_LIST != 0
void WatchUtility_SetAlarm(ws_alarm *alarm)
{
    if(alarm == NULL)
    {
        return;
    }

    app_adaptor_alarm_t *new_alarm = NULL;
	int len = sizeof(app_adaptor_alarm_t);
	new_alarm = (app_adaptor_alarm_t *)lv_mem_alloc(len);
	Hal_Mem_Set(new_alarm, 0, len);

    
    if(new_alarm)
    {
        new_alarm->valid = alarm->is_on;
        new_alarm->hour = atoi(&alarm->time[0]);
        new_alarm->min = atoi(&alarm->time[3]);
        new_alarm->repeat_bitmap = alarm->repeat;//0x81;
        strncpy(new_alarm->token, alarm->token, WATCH_ALARM_TOKEN_LEN_MAX);

    	alarm_set_alarm_ext(new_alarm);
    	lv_mem_free(new_alarm);
    #if USE_LV_WATCH_ALARM_LIST != 0
        //alarm_list_update_ui();
    #endif 	
    }
}

void WatchUtility_DelAlarm(ws_alarm *alarm)
{
    if(alarm == NULL)
    {
        return;
    }
    
	//alarm_delete_alarm(alarm->token);
#if USE_LV_WATCH_ALARM_LIST != 0
    //alarm_list_update_ui();
#endif  

}

#endif



void WatchUtility_SetAlarmList(ws_alarm_list *alarm)
{
    if(alarm == NULL)
    {
		WS_PRINTF("WatchUtility_SetAlarmList --alarm ==NULL");
        return;
    }

    app_adaptor_alarm_t *alarm_list = NULL;
	if(alarm->count>0)
	{
		int len = alarm->count*sizeof(app_adaptor_alarm_t);
		alarm_list = (app_adaptor_alarm_t *)lv_mem_alloc(len);
		Hal_Mem_Set(alarm_list, 0, len);
	}

    
    if(alarm_list)
    {
        for(int i=0; i<alarm->count; i++)
        {
            alarm_list[i].on_off = alarm->alarm[i].is_on;
            alarm_list[i].hour = atoi(&alarm->alarm[i].time[0]);
            alarm_list[i].min = atoi(&alarm->alarm[i].time[3]);
            alarm_list[i].repeat_bitmap = alarm->alarm[i].repeat;//0x81;
			if((alarm_list[i].hour>=0 && alarm_list[i].hour<=23) && (alarm_list[i].min>=0 && alarm_list[i].min<=59))
            {
                alarm_list[i].valid=1;
            }else
            {
                alarm_list[i].valid=0;
            }

			#if USE_LV_WATCH_SCHEDULE_POWERON_OFF!=0 || USE_LV_WATCH_CYCLE_ALARM != 0 || USE_LV_WATCH_CYCLE_POWERON_CLOCK_ARRAY != 0
            alarm_list[i].type = alarm->alarm[i].type;
            WS_PRINTF("WatchUtility_SetAlarmList(i:%d) type:%d",i,alarm_list[i].type );
			#endif
			#if USE_LV_WATCH_ALARM_RING_OR_VIB_ENABLE!=0
            alarm_list[i].ring_en = alarm->alarm[i].ring_en;
            alarm_list[i].vib_en = alarm->alarm[i].vib_en;
			#endif
			#if USE_LV_WATCH_ALARM_S2C_NAME!=0
			strncpy(alarm_list[i].name,alarm->alarm[i].name,strlen(alarm->alarm[i].name));
			#endif
            WS_PRINTF("WatchUtility_SetAlarmList() alarm_list[%d]%d:%d %02x", i, alarm_list[i].hour,alarm_list[i].min,alarm_list[i].repeat_bitmap);
        }
    }

	alarm_set_alarm(alarm_list, alarm->count);
	lv_mem_free(alarm_list);
	lv_mem_free(alarm->alarm);

#if USE_LV_WATCH_ALARM_LIST != 0 ||USE_LV_WATCH_ALARM_SET!=0
	//alarm_list_update_ui();
#endif 	

}
void WatchUtility_Clear_All_AlarmList()
{
	app_adaptor_alarm_t *alarm_list = NULL;
	int len = NV_ALARM_MAX_ALARM_NUM*sizeof(app_adaptor_alarm_t);
	alarm_list = (app_adaptor_alarm_t *)lv_mem_alloc(len);
	Hal_Mem_Set(alarm_list, 0, len);
    alarm_set_alarm(alarm_list, NV_ALARM_MAX_ALARM_NUM);
	lv_mem_free(alarm_list);
}

#if USE_LV_WATCH_PRO_V22 != 0
void WatchUtility_Set_NewSosList(ws_sos_new_list *sos_list)
{
	int i,j;
    if(sos_list == NULL)
        return;
    uint32_t length = sizeof(nv_watch_sos_new_t);
    nv_watch_sos_new_t * nvm_sos = (nv_watch_sos_new_t *)lv_mem_alloc(length);
    memset(nvm_sos, 0, length);

    for(i=0; i<sos_list->count && i<NV_WATCH_MAX_SOS_NUM; i++)
    {
		if(strlen(sos_list->sos_list[i].name) > 0)
			strcpy(nvm_sos->info[i].name, sos_list->sos_list[i].name);
		else
			strcpy(nvm_sos->info[i].name, "SOS");
        strcpy(nvm_sos->info[i].number, sos_list->sos_list[i].sos);
        nvm_sos->info[i].contact_type = sos_list->sos_list[i].contact_type;
		nvm_sos->info[i].periods = sos_list->sos_list[i].periods;
        for(j=0;j<NV_WATCH_MAX_VALID_TIME;j++) 
        {
			nvm_sos->info[i].time_start[j] = sos_list->sos_list[i].time_start[j];
			nvm_sos->info[i].time_end[j] = sos_list->sos_list[i].time_end[j];
			WS_PRINTF("WatchUtility_Set_NewSosList[%d]=%d,%d",j,nvm_sos->info[i].time_start[j],nvm_sos->info[i].time_end[j]);
		}
		WS_PRINTF("WatchUtility_Set_NewSosList[%d]=perid=%x,ty=%d,num=%s,", i,nvm_sos->info[i].periods,nvm_sos->info[i].contact_type,nvm_sos->info[i].number);
    }
    
    if(length != UI_NV_Write_Req(NV_SECTION_UI_SOS_NEW, 0, length, (uint8_t *)nvm_sos)) {
        printf("write nvm error in sos save \n");
    }
    
    lv_mem_free(nvm_sos);

}
#endif
void WatchUtility_SetSosList(ws_sos_list *sos_list)
{
    int i;
    if(sos_list == NULL)
        return;

    if(sos_list->single < NV_WATCH_MAX_SOS_NUM)
    {
        uint32_t length = sizeof(nv_watch_phonebook_info_t);
        nv_watch_phonebook_info_t * nvm_sos = (nv_watch_phonebook_info_t *)lv_mem_alloc(length);
        memset(nvm_sos, 0, length);
        nvm_sos->name[0] = 0;
		#if USE_LV_WATCH_PHONEBOOK_SPLIT !=0
		strcpy(nvm_sos->image_url, sos_list->image_url);
		#endif
        strcpy(nvm_sos->number, sos_list->sos[i]);
        nvm_sos->contact_type = 2;
#if USE_LV_WATCH_PHONE_UI_CY50 !=0 || USE_LV_WATCH_PHONEBOOK_SPLIT !=0
		nvm_sos->portrait_id = WATCH_PORTRAIT_ID_SOS;
#else
        nvm_sos->portrait_id = WATCH_PORTRAIT_ID_OTHERS;
#endif
        ws_printf("WatchUtility_SetSosList sos_list->single%d",sos_list->single);
        if(length != UI_NV_Write_Req(NV_SECTION_UI_SOS, sos_list->single*length, length, (uint8_t *)nvm_sos)) {
            printf("write nvm error in sos save \n");
        }
        
        lv_mem_free(nvm_sos);
    }
    else 
    {
        uint32_t length = sizeof(nv_watch_sos_t);
        nv_watch_sos_t * nvm_sos = (nv_watch_sos_t *)lv_mem_alloc(length);
        memset(nvm_sos, 0, length);
		ws_printf("WatchUtility_SetSosList sos_list->count%d",sos_list->count);

        for(i=0; i<sos_list->count && i<NV_WATCH_MAX_SOS_NUM; i++)
        {
		#if USE_LV_WATCH_WS_KAER != 0
		#if defined(__XF_PRO_LIANCHUANG__)
			if(strlen(sos_list->name[i]) > 0)
		#else
			if(strlen(sos_list->name) > 0)
		#endif
				strcpy(nvm_sos->info[i].name, sos_list->name[i]);
			else
				strcpy(nvm_sos->info[i].name, "SOS");
        #else
            nvm_sos->info[i].name[0] = 0;
        #endif
			#if USE_LV_WATCH_PHONEBOOK_SPLIT !=0
			strcpy(nvm_sos->info[i].image_url, sos_list->image_url[i]);
			#endif
            strcpy(nvm_sos->info[i].number, sos_list->sos[i]);
            nvm_sos->info[i].contact_type = 2;
#if USE_LV_WATCH_PHONE_UI_CY50 !=0 || USE_LV_WATCH_PHONEBOOK_SPLIT !=0
			nvm_sos->info[i].portrait_id = WATCH_PORTRAIT_ID_SOS;
#else
            nvm_sos->info[i].portrait_id = WATCH_PORTRAIT_ID_OTHERS;
#endif
			#if USE_LV_WATCH_SOS_DIFF_LOOP != 0
			nvm_sos->info[i].contact_type = sos_list->loop_num[i];
			#endif
        }
        
        if(length != UI_NV_Write_Req(NV_SECTION_UI_SOS, 0, length, (uint8_t *)nvm_sos)) {
            printf("write nvm error in sos save \n");
        }
        
        lv_mem_free(nvm_sos);
    }
}


void WatchUtility_SetContactList(ws_contact_list *phb_list)
{
    if(phb_list == NULL)
        return;
	
    WS_PRINTF("%s,count:%d", __func__,phb_list->count );
	
#if 0//USE_LV_WATCH_PHONEBOOK_SPLIT != 0
	phonebook_set_split_type(SPLIT_FAMILY);
#endif
	#if USE_LV_WATCH_WS_KAER != 0
	WatchUtility_Close_Phonebook();//close phonebook menu
	#endif

    if(phb_list->count == 0)
    {
	#if USE_LV_WATCH_WS_KAER !=0 || defined(__XF_PRO_A06__)||defined(__XF_PRO_A316__)||defined(__XF_PRO_A16__) || defined(__XF_PRO_K16_PLUS__) || defined(__XF_PRO_K616__)
		#if USE_LV_WATCH_LC_WHITELIST_MAX_100 != 0
		if(ke_GetLianchuangVerFlag() == AH_VER)
			app_adaptor_delet_all_family_contact_list_ah();
		else
		#endif
	app_adaptor_delet_all_family_contact_list();
	#elif (USE_LV_WATCH_WS_CT!=0)
		app_adaptor_delet_all_family_contact_list();
	#else
        app_adaptor_update_phonebook_ind(NULL, 0, 0, 0);
	#endif
        return;
    }

    app_adaptor_contact_t * contact_list =  NULL;
	if(phb_list->count>0)
	{
	    contact_list =  (app_adaptor_contact_t*)lv_mem_alloc(phb_list->count * sizeof(app_adaptor_contact_t));
	}
	
    if(contact_list)
    {
        for(int i=0; i<phb_list->count; i++)
        {
            memcpy(contact_list[i].contact_name, phb_list->contact[i].name, WATCH_CONTACTS_MAX_NAME_LEN);
			#if USE_LV_WATCH_PHONEBOOK_SPLIT !=0
            memcpy(contact_list[i].contact_url , phb_list->contact[i].image_url , WS_RECORD_URL_MAX_LEN);
			#endif
            if(strlen(phb_list->contact[i].phone)>3){
                if(strncmp(phb_list->contact[i].phone, "+86", 3)==0){
                    memcpy(contact_list[i].contact_number, phb_list->contact[i].phone+3, WATCH_CONTACTS_MAX_NUMBER_LEN);
                }else if(strncmp(phb_list->contact[i].phone, "86", 2)==0){
                    memcpy(contact_list[i].contact_number, phb_list->contact[i].phone+2, WATCH_CONTACTS_MAX_NUMBER_LEN);
                }else{
                    memcpy(contact_list[i].contact_number, phb_list->contact[i].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
                }
            }else{
                memcpy(contact_list[i].contact_number, phb_list->contact[i].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
            }
            contact_list[i].contact_type = 2;
			switch(phb_list->contact[i].avatar_id)
			{
			case 1:	
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_FATHER;
				break;
			case 2: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MOTHER;
				break;
			case 3: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_PATERNAL_GRADFATHER;
				break;
			case 4: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_PATERNAL_GRADMOTHER;
				break;
			case 5: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MATERNAL_GRADFATHER;
				break;
			case 6: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MATERNAL_GRADMOTHER;
				break;
			case 7: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_UNCLE;
				break;
			case 8: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_AUNT;
				break;
#if (USE_LV_WATCH_WS_KAER != 0) || (USE_LV_WATCH_WS_CT != 0)
//借用这个区分是不是白名单
			case 9: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_FAMILY;
				#if USE_LV_WATCH_WHITELIST_SHOW_ON_PB != 0
				contact_list[i].is_show = 1;
				#endif
				break;
#endif /* USE_LV_WATCH_WS_KAER */
			default:
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_OTHERS;
				break;
			}	
            /* 根据名字二次匹配 */
            if(contact_list[i].portrait_id == WATCH_PORTRAIT_ID_OTHERS)
            {
    			if(strncmp(phb_list->contact[i].name, "爸爸",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_FATHER;
    			else if (strncmp(phb_list->contact[i].name, "妈妈",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MOTHER;
				#if 0
    			else if (strncmp(phb_list->contact[i].name, "外婆",4) == 0 ||strncmp(phb_list->contact[i].name, "姥姥",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MATERNAL_GRADMOTHER;
    			else if (strncmp(phb_list->contact[i].name, "外公",4) == 0 ||strncmp(phb_list->contact[i].name, "姥爷",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MATERNAL_GRADFATHER;
    			else if (strncmp(phb_list->contact[i].name, "爷爷",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_PATERNAL_GRADFATHER;
    			else if (strncmp(phb_list->contact[i].name, "奶奶",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_PATERNAL_GRADMOTHER;
    			else if (strncmp(phb_list->contact[i].name, "叔叔",4) == 0 || strncmp(phb_list->contact[i].name, "舅舅",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_UNCLE;
    			else if (strncmp(phb_list->contact[i].name, "阿姨",4) == 0 || strncmp(phb_list->contact[i].name, "舅妈",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_AUNT;
    			else
                	contact_list[i].portrait_id = WATCH_PORTRAIT_ID_OTHERS;
				#endif
            }

        }
        
    }
#if USE_LV_WATCH_LC_WHITELIST_MAX_100 != 0
	if(ke_GetLianchuangVerFlag() == AH_VER)
		app_adaptor_update_phonebook_ind_ah(contact_list, phb_list->count, phb_list->index_start, phb_list->index_end);
	else
#endif
	app_adaptor_update_phonebook_ind(contact_list, phb_list->count, phb_list->index_start, phb_list->index_end);

	lv_mem_free(phb_list->contact);
    
#if 0 //test only!
    app_adaptor_friend_t * friend_list =  NULL;
   	friend_list =  (app_adaptor_friend_t*)lv_mem_alloc(sizeof(app_adaptor_friend_t));
	memset(friend_list, 0, sizeof(app_adaptor_friend_t));
    if(friend_list)
    {
            strncpy(friend_list->friend_name, "Test", WATCH_CONTACTS_MAX_NAME_LEN);
            strncpy(friend_list->friend_number, "18621569516", WATCH_CONTACTS_MAX_NUMBER_LEN);
            strncpy(friend_list->friend_imei, "990091971821850", WATCH_CONTACTS_MAX_NUMBER_LEN);
            friend_list->friend_image[0]=0;
			friend_list->time = 0;
			friend_list->contact_type = 2;
            friend_list->portrait_id = WATCH_PORTRAIT_ID_OTHERS;
        
    }
    
	app_adaptor_update_friend_ind(friend_list, 1);

#endif 
}


void WatchUtility_SetContactList3(ws_contact_list *phb_list)
{
    if(phb_list == NULL)
        return;
#if defined(__XF_WS_VENDOR_PS_ZHM__)	
    WS_PRINTF("%s,count:%d", __func__,phb_list->count );
	
#if USE_LV_WATCH_PHONEBOOK_SPLIT != 0
	phonebook_set_split_type(SPLIT_FAMILY);
#endif

    if(phb_list->count == 0)
    {
	#if (USE_LV_WATCH_WS_CT!=0)
		app_adaptor_delet_all_family_contact_list();
	#else
        app_adaptor_update_phonebook_ind(NULL, 0, 0, 0);
	#endif
        return;
    }

    app_adaptor_contact_t * contact_list =  NULL;
	if(phb_list->count>0)
	{
	    contact_list =  (app_adaptor_contact_t*)lv_mem_alloc(phb_list->count * sizeof(app_adaptor_contact_t));
	}
	
    if(contact_list)
    {
        for(int i=0; i<phb_list->count; i++)
        {
            memcpy(contact_list[i].contact_name, phb_list->contact[i].name, WATCH_CONTACTS_MAX_NAME_LEN);
            if(strlen(phb_list->contact[i].phone)>3){
                if(strncmp(phb_list->contact[i].phone, "+86", 3)==0){
                    memcpy(contact_list[i].contact_number, phb_list->contact[i].phone+3, WATCH_CONTACTS_MAX_NUMBER_LEN);
                }else if(strncmp(phb_list->contact[i].phone, "86", 2)==0){
                    memcpy(contact_list[i].contact_number, phb_list->contact[i].phone+2, WATCH_CONTACTS_MAX_NUMBER_LEN);
                }else{
                    memcpy(contact_list[i].contact_number, phb_list->contact[i].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
                }
            }else{
                memcpy(contact_list[i].contact_number, phb_list->contact[i].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
            }
            contact_list[i].contact_type = 2;
			switch(phb_list->contact[i].avatar_id)
			{
			case 1:	
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_FATHER;
				break;
			case 2: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MOTHER;
				break;
			case 3: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_PATERNAL_GRADFATHER;
				break;
			case 4: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_PATERNAL_GRADMOTHER;
				break;
			case 5: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MATERNAL_GRADFATHER;
				break;
			case 6: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MATERNAL_GRADMOTHER;
				break;
			case 7: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_UNCLE;
				break;
			case 8: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_AUNT;
				break;
			//借用这个区分是不是白名单
			case 9: 
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_FAMILY;
				break;
			default:
				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_OTHERS;
				break;
			}	
            /* 根据名字二次匹配 */
            if(contact_list[i].portrait_id == WATCH_PORTRAIT_ID_OTHERS)
            {
    			if(strncmp(phb_list->contact[i].name, "爸爸",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_FATHER;
    			else if (strncmp(phb_list->contact[i].name, "妈妈",4) == 0)
    				contact_list[i].portrait_id = WATCH_PORTRAIT_ID_MOTHER;
            }

        }
        
    }
	app_adaptor_update_phonebook_ind_add(contact_list, phb_list->count, phb_list->index_start, phb_list->index_end);

	lv_mem_free(phb_list->contact);
#else
	WatchUtility_SetContactList(phb_list);
#endif
	
    
}




#if USE_LV_WHITE_LIST != 0
void WatchUtility_PB_AddList(ws_contact_list *add_phb_list)
{
    app_adaptor_contact_t *contact_list =  NULL;
    int loop = 0;
    int save_idx = 0;
    int count = 0;
	bool toAdd=false;
    WS_PRINTF("%s", __func__);
    count = add_phb_list->count;
    if(add_phb_list && (count > 0))
    {
        WS_PRINTF("add count:%d", count);

        contact_list = (app_adaptor_contact_t*)lv_mem_alloc(count * sizeof(app_adaptor_contact_t));
        memset(contact_list, 0, count * sizeof(app_adaptor_contact_t));
        if(contact_list) 
    	{
    		bool has_same=false;
	        for(loop=0; loop<count && add_phb_list->contact[loop].phone[0]; loop++)
	        {
				#if defined(__XF_WS_VENDOR_CT_JXDX__)
				has_same=false;
				/*original have same ,so not save the same number*/
				for(uint8_t j = 0; j < save_idx ; j++)
				{
					if(strcmp(contact_list[j].contact_number,add_phb_list->contact[loop].phone)==0) 
					{
							WS_PRINTF("number save continue:%s", add_phb_list->contact[loop].phone);
							has_same=true;
							break;
					}
				}
				
				char *check_nu = (char *)lv_mem_alloc(strlen(add_phb_list->contact[loop].phone)+1);
				strcpy(check_nu, add_phb_list->contact[loop].phone);

				phonebook_contact_t * contact_info =phonebook_get_contact_info(check_nu);
				if(contact_info!=NULL)
				{
					/*is phonebook already have this number ,so save again*/
					has_same=true;
					WS_PRINTF("number is existed in phonebook:%s", add_phb_list->contact[loop].phone);
					
					lv_mem_free(contact_info);
				}
				
				if(has_same) continue;
				#endif
			
	            memcpy(contact_list[save_idx].contact_name, add_phb_list->contact[loop].name, WATCH_CONTACTS_MAX_NAME_LEN);
	            memcpy(contact_list[save_idx].contact_number, add_phb_list->contact[loop].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
	            contact_list[save_idx].contact_type = 2;
	            contact_list[save_idx].portrait_id = WATCH_PORTRAIT_ID_NO_INFO;
				save_idx++;
				toAdd=true;
	            //WS_PRINTF("add number[%d]:%s", loop, contact_list[loop].contact_number);
	        }

			#if defined(__XF_WS_VENDOR_CT_JXDX__)
				count=save_idx;
				
				WS_PRINTF("toAdd:%d,add count:%d", toAdd,count);
				
				if(toAdd){
					app_adaptor_addlist_to_phonebook(contact_list, count);
				}
				else{
					app_adaptor_aphonebook_update();
				}
			#else
				app_adaptor_addlist_to_phonebook(contact_list, count);
			#endif
			
    	}
    }

    if(add_phb_list->contact)
    {
        lv_mem_free(add_phb_list->contact);
        add_phb_list->contact == NULL;
    }
}

void WatchUtility_PB_DelList(ws_contact_list *del_phb_list)
{
    app_adaptor_contact_t *contact_list =  NULL;
    int loop = 0;
    int count = 0;
    WS_PRINTF("%s", __func__);

    count = del_phb_list->count;
    if(del_phb_list && (count > 0))
    {
        WS_PRINTF("del count:%d", count);

        contact_list = (app_adaptor_contact_t*)lv_mem_alloc(count * sizeof(app_adaptor_contact_t));
        memset(contact_list, 0, count * sizeof(app_adaptor_contact_t));
        if(contact_list)
    	{
	        for(loop=0; loop<count && del_phb_list->contact[loop].phone[0]; loop++)
	        {
	            memcpy(contact_list[loop].contact_name, del_phb_list->contact[loop].name, WATCH_CONTACTS_MAX_NAME_LEN);
	            memcpy(contact_list[loop].contact_number, del_phb_list->contact[loop].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
	            contact_list[loop].contact_type = 2;
	            contact_list[loop].portrait_id = WATCH_PORTRAIT_ID_NEED_DELETE;
	            //WS_PRINTF("del number[%d]:%s", loop, contact_list[loop].contact_number);
	        }
	        app_adaptor_phonebook_remove_itemlist(contact_list, count, true);
    	}
    }

    if(del_phb_list->contact)
    {
        lv_mem_free(del_phb_list->contact);
        del_phb_list->contact == NULL;
    }
}

bool WatchUtility_SetWhiteList(uint8_t status, ws_contact_list *del_phb_list, ws_contact_list *add_phb_list)
{
    app_adaptor_contact_t * contact_list =  NULL;
    int loop = 0;
    int count = 0;
    WS_PRINTF("%s - is_on:%d, del_count:%d, add_count:%d", __func__, status, del_phb_list->count, add_phb_list->count);
	
#if !defined(__XF_WS_VENDOR_CT_ANH__)
    myNVMgr_SetWhiteListStatus(status);  /*anhui use-other cmd(20) set*/
#endif

    if(del_phb_list->count > 0)
        WatchUtility_PB_DelList(del_phb_list);

    if(add_phb_list->count > 0){
		#if USE_LV_WATCH_PHONEBOOK_SPLIT != 0
		phonebook_set_split_type(SPLIT_WHITE);
		#endif
        WatchUtility_PB_AddList(add_phb_list);
    }
	
	#if defined(__XF_WS_VENDOR_CT_JXDX__)
	if(add_phb_list->count==0)
	{
		app_adaptor_aphonebook_update();
	}
	#endif

    return true;
}
#endif

#if USE_LV_WATCH_WS_KAER != 0

char g_temp_urlport[100]={0};
uint8_t g_disconnect_counts = 0;
uint8_t MMI_ModemAdp_WS_Reconnect_Check(ws_client * pme,uint8_t need_count,uint8_t ress)
{
	WS_PRINTF("####disc_and_reconnect_origin_url count=%d,flag=%d,g_disconnect_counts=%d",need_count,ress,g_disconnect_counts);
	if(need_count == 0)
	{
		g_disconnect_counts = 0;
		return 0;
	}
	else if(ress == 2)
	{
	//when net is not connect,count times
		g_disconnect_counts++;
		if(g_disconnect_counts >= 3)
		{
			g_disconnect_counts = 0;

    		if(strlen(g_temp_urlport) > 0)
			{
				memset(g_temp_urlport,0,sizeof(g_temp_urlport));
				WS_PRINTF("\n______________disc_and_reconnect_origin_url\n");
				MMI_ModemAdp_WS_Stop_Ext();
				return 1;
			}
		}
	}
	return 0;
}

uint8_t CWatchService_SetUrl(char *Data)
{
	if(Data == NULL)
	{
		return 0;
	}
	uint8_t res = 0;
	char *Dat = Data;
	char UrlPort[100] = {0};
	strcpy(UrlPort, Dat);
		
	char *index = strstr(UrlPort, "@");
	if(index != NULL)
	{
		*index = 0;
		index++;
		uint16_t port = atoi(index);
		if(port > 0 && port < 0xffff)
		{
			memset(g_temp_urlport,0,sizeof(g_temp_urlport));
			strcpy(g_temp_urlport,Data);
		#if defined(__XF_PRO_XIAOBEN__)
			myNVMgr_SetWsUrlPort(g_temp_urlport);
		#else
			res = CWatchService_UpdateUrl(g_temp_urlport,port);
			if(res == 0)
				return 0;
		#endif
		}
		
		return 1;
	}		
}

void CWatchService_SetNewUrlPort(void)
{
	if(strlen(g_temp_urlport) > 0)
	{
		myNVMgr_SetWsUrlPort(g_temp_urlport);

		if(ke_GetPlatUrlPortLen() != 0)
		{
			ws_printf("set ke_settingsinfo.PlatUrlPort");
			UI_NV_Write_Req(NV_SECTION_UI_KAERSETTINGS, NV_OFFSETOF(nv_watch_kaersettings_t, PlatUrlPort), 48, (uint8_t*)g_temp_urlport);
		}

		memset(g_temp_urlport,0,sizeof(g_temp_urlport));
	}
}

void CWatchService_SetTempUrl(char *url)
{
	if(url != NULL)
		strcpy(url,g_temp_urlport);
}

uint8_t CWatchService_IsTempUrlValid(void)
{
	if(strlen(g_temp_urlport) > 0)
		return 1;

	return 0;
}
void CWatchService_ClearTempUrl()
{
	memset(g_temp_urlport,0,sizeof(g_temp_urlport));
}

void WatchUtility_SetWhiteList(ws_contact_list *phb_list)
{
    if(phb_list == NULL)
        return;
    app_adaptor_contact_t * contact_list =  NULL;
	#if USE_LV_WATCH_WS_KAER != 0
	WatchUtility_Close_Phonebook();//close phonebook menu
	#endif
	if(phb_list->count>0)
	{
	    contact_list =  (app_adaptor_contact_t*)lv_mem_alloc(phb_list->count * sizeof(app_adaptor_contact_t));
		#if USE_LV_WATCH_LC_WHITELIST_MAX_100 != 0
		if(contact_list)
		{
			memset(contact_list,0,sizeof(phb_list->count * sizeof(app_adaptor_contact_t)));
		}
		#endif
	}
	else	
    {
    	#if USE_LV_WATCH_LC_WHITELIST_MAX_100 != 0
		if(ke_GetLianchuangVerFlag() == AH_VER)
    		app_adaptor_delet_all_white_list_ah();
		else
		#endif
        app_adaptor_delet_all_white_list();
		#if USE_LV_WATCH_PLATFORM_LIANCHUANG !=1 && !defined(__XF_PRO_WOMQTT__)
		myNVMgr_SetWhiteListStatus(false);
		#endif
        return;
    }
	
    if(contact_list)
    {
    	ws_printf("----WatchUtility_SetWhiteList cont=%d",phb_list->count);

        for(int i=0; i<phb_list->count; i++)
        {
            memcpy(contact_list[i].contact_name, phb_list->contact[i].name, WS_CONTACT_NAME_MAX_LEN);
            memcpy(contact_list[i].contact_number, phb_list->contact[i].phone, WS_CONTACT_PHONE_MAX_LEN);
			#if USE_LV_WATCH_PHONEBOOK_SPLIT !=0
            memcpy(contact_list[i].contact_url, phb_list->contact[i].image_url, WS_RECORD_URL_MAX_LEN);
			#endif
            contact_list[i].contact_type = 2;
			contact_list[i].portrait_id = WATCH_PORTRAIT_ID_OTHERS;
			#if USE_LV_WATCH_WHITELIST_SHOW_ON_PB != 0
			contact_list[i].is_show = phb_list->contact[i].type;
			#endif
			//ws_printf("WatchUtility_SetWhiteList[%d]=%s,%d",i,contact_list[i].contact_number,strlen(contact_list[i].contact_name));
        }        
    }
	#if USE_LV_WATCH_PLATFORM_LIANCHUANG !=1 && !defined(__XF_PRO_WOMQTT__)
	myNVMgr_SetWhiteListStatus(true);
	#endif
	
#if 0//USE_LV_WATCH_PHONEBOOK_SPLIT != 0
// 	phonebook_set_split_type(SPLIT_WHITE);
#endif
	#if USE_LV_WATCH_LC_WHITELIST_MAX_100 != 0
		if(ke_GetLianchuangVerFlag() == AH_VER)
			app_adaptor_update_phonebook_ind_ah(contact_list, phb_list->count, 0, 0);
		else
	#endif

	app_adaptor_update_phonebook_ind(contact_list, phb_list->count, 0, 0);

	lv_mem_free(phb_list->contact);
    
}

void CWatchService_SendSmsCB(watch_app_adp_sms_send_result_type_t result)
{
	WS_PRINTF("\n &&&&&&&&&&& %s  : result = %d\n", __FUNCTION__, result);
}

void CWatchService_SendPduSms(char *desnumber,int desnumlen,char *smscont,int smslen)
{
	char *num = (char *)Hal_Mem_Alloc(desnumlen+1);
	memcpy(num,desnumber,desnumlen+1);

	char *dat = (char *)Hal_Mem_Alloc(smslen);
	memcpy(dat,smscont,smslen);
	WS_PRINTF("\n &&&&&&&&&&& %s  : num = %s,desnumlen=%d,smslen=%d\n", __FUNCTION__, num,desnumlen,smslen);
	app_adaptor_sms_send_with_utf16_req(num,dat,smslen,NULL,CWatchService_SendSmsCB);
}

#if USE_LV_WATCH_WORK_MODE != 0

void myNVMgr_SetWorkMode(uint8_t mode)
{
	UI_NV_Write_Req(NV_SECTION_WORK_MODE_INFO, 0, sizeof(uint8_t), (uint8_t*)&mode);
}

uint8_t myNVMgr_GetWorkMode(void)
{
	uint8_t val = 0;
	uint32_t len = 0;
	len = UI_NV_Read_Req(NV_SECTION_WORK_MODE_INFO, 0, sizeof(uint8_t), (uint8_t*)&val);
	if(len < sizeof(uint8_t))
	{
		val = WORK_MODE_DEFAULT;
	}	
	return val;
}
#endif

time_list g_location_disable_time_list;

int myNVMgr_InitLocatinInfo(void)
{
	uint32_t length = sizeof(time_list);

	if(length != UI_NV_Read_Req(NV_SECTION_LOCATIN_DISABLE_MODEL, 0, length, (uint8_t *)&g_location_disable_time_list)) {
        WS_PRINTF("read update_mode from nvm error");
        return 0;
    }

	
	WS_PRINTF("%s COUNT = %d !!",__FUNCTION__,g_location_disable_time_list.count);
}

void myNVMgr_SaveLocatinDisable(void)
{
	int len = 0;
	uint32_t length = sizeof(time_list);
    len = UI_NV_Write_Req(NV_SECTION_LOCATIN_DISABLE_MODEL, 0, length, (UINT8 *)&g_location_disable_time_list);
}

uint8_t CWatchService_IsLocatinDisable(void)
{
	int i = 0,j=0;	
	int cur_time = 0;
	hal_rtc_t rtc_curr;
	
	Hal_Rtc_Gettime(&rtc_curr);
	cur_time = rtc_curr.tm_hour*60+rtc_curr.tm_min;

	WS_PRINTF("%s SLEEP COUNT = %d !!",__FUNCTION__,g_location_disable_time_list.count);
	while(i < g_location_disable_time_list.count)
	{
		WS_PRINTF("%s time[%d]on_off=%d!!",__FUNCTION__,i,g_location_disable_time_list.timelist[i].on_off);
		if(g_location_disable_time_list.timelist[i].on_off == 1)
		{
			j = 1;
			WS_PRINTF("%s time[%d]week=%d!!",__FUNCTION__,i,g_location_disable_time_list.timelist[i].week[rtc_curr.tm_wday]);
			if(g_location_disable_time_list.timelist[i].week[rtc_curr.tm_wday] == 1)
			{
			WS_PRINTF("%s start_time =%d end_time = %d,cur_time = %d",__FUNCTION__,g_location_disable_time_list.timelist[i].start_time,
				g_location_disable_time_list.timelist[i].end_time,cur_time);
				if(g_location_disable_time_list.timelist[i].start_time<=cur_time && cur_time<=g_location_disable_time_list.timelist[i].end_time)
				{
					WS_PRINTF("%s FIND TIME OK!!",__FUNCTION__);
					return 1;
				}
			}
		}
		i++;
	}
	#if USE_LV_WATCH_PRO_V22 != 0
	if(ke_GetLezhiVerFlag() == XJ_LZ_VER)
	{
		if(j == 0)
		{
			if(CWathcService_LocatinDisable_On_Dormant() == 1)
				return 1;//no report
		}
		else if(CWathcService_Is_Dormant_Time() == 1)
		{
			return 1;//no report
		}
	}
	#endif
	return 0;
	
}

#if USE_LV_WATCH_DORMANT_APP != 0
void WatchUtility_CheckDormant_mute()
{
	lv_watch_go_home_mute();
	launcher_recreate();
}

uint8_t WatchUtility_CheckDormantTime(void)
{
	static uint8_t pre_state = 0;
	uint8_t curr_state=0;
	#if USE_LV_WATCH_DORMAT_TIME != 0
	curr_state = WatchService_IsInDormantTime();
	#elif USE_LV_WATCH_PRO_V22 != 0
	if(ke_GetLezhiVerFlag() == XJ_LZ_VER)
	{
		curr_state = CWathcService_Is_Dormant_Time();
	}
	#endif
	if(curr_state != pre_state)
	{
	
		lv_task_t *task = lv_task_create(WatchUtility_CheckDormant_mute, 100, LV_TASK_PRIO_HIGH, 0);
        lv_task_once(task);
		pre_state = curr_state;
	}
	return curr_state;
}
#endif


#if USE_LV_WATCH_DORMAT_TIME != 0

nv_watch_dormantime_t g_dormant_t;
uint16_t g_wakeup_time = 0;

nv_watch_dormantime_t *myNVMgr_GetWsDormantTime(void)
{
	uint32_t len;
    len = UI_NV_Read_Req(NV_SECTION_DORMAT_TIME_INFO, 0, sizeof(nv_watch_dormantime_t), (uint8_t*)&g_dormant_t);
	
	if(len < sizeof(nv_watch_dormantime_t) || g_dormant_t.null_flag == 0)
	{
		g_dormant_t.status = 1;
		g_dormant_t.dormt_start_h = USE_LV_WATCH_WS_OFF_HOUR*60 ;
		g_dormant_t.dormt_end_h = USE_LV_WATCH_WS_ON_HOUR*60;
		g_dormant_t.null_flag = 1;
		UI_NV_Write_Req(NV_SECTION_DORMAT_TIME_INFO, 0, sizeof(nv_watch_dormantime_t), (uint8_t*)&g_dormant_t);
	}
	
	return &g_dormant_t;
}

void myNVMgr_SetWsDormantTime(nv_watch_dormantime_t *time)
{
	if(time == NULL)
	{
		WS_PRINTF("%s time is NULL ERROR",__FUNCTION__);
		return;
	}
	g_dormant_t.status = time->status;
	g_dormant_t.dormt_start_h = time->dormt_start_h;
	g_dormant_t.dormt_end_h = time->dormt_end_h;
	g_dormant_t.null_flag = time->null_flag;
	
	UI_NV_Write_Req(NV_SECTION_DORMAT_TIME_INFO, 0, sizeof(nv_watch_dormantime_t), (uint8_t*)time);
}


void WatchService_WakeupTimeSet(uint16_t time)
{
	g_wakeup_time = time;
}

uint8_t WatchService_WakeupTimeUpdate(void)
{
	if(g_wakeup_time > 0)
		g_wakeup_time--;
}

void WatchService_SetWakeupTime(uint16_t sleep_time)
{
	g_wakeup_time = sleep_time;
	
	WS_PRINTF("WatchService_SetWakeupTime = %d", g_wakeup_time);
}

void WatchService_WakeUpDormant(void)
{
	int time = 0;
	hal_rtc_t rtc_curr; 		   // current rtc time
	int cur_time = 0;

	int start_times = 0;
	int end_times = 0;
		
	Hal_Rtc_Gettime(&rtc_curr);
	cur_time = rtc_curr.tm_hour*60+rtc_curr.tm_min;

	if(g_dormant_t.status == 1)
	{
		g_dormant_t.status = 0;
	}
	WS_PRINTF("!!!!!awaken_long_mod!!!!!!!!!! = %d", cur_time);
	start_times = USE_LV_WATCH_WS_OFF_HOUR;
	end_times = USE_LV_WATCH_WS_ON_HOUR;

	if(cur_time>(start_times*60) || cur_time<(end_times*60)) //cur time in static time
	{
		if(cur_time>(start_times*60))
			time = (end_times*60)+(((start_times+1)*60)-cur_time)+2;
		else
			time = (end_times*60)-cur_time+2;
		
		WatchService_SetWakeupTime(time*2);
	}	
}

int WatchService_IsInDormantTime(void)
{
	hal_rtc_t rtc_curr;            // current rtc time
	int cur_time = 0;
	int is_in_dormant_time = 0;
	
    Hal_Rtc_Gettime(&rtc_curr);
	cur_time = rtc_curr.tm_hour*60+rtc_curr.tm_min;
	
	WS_PRINTF("Is_Dormant_time########!!!!!!!!!!!!!!!!! dormt_end=%d,dormt_start=%d,cur_time=%d", g_dormant_t.dormt_end_h, g_dormant_t.dormt_start_h,cur_time);
	
#if USE_LV_WATCH_WORK_MODE != 0
	if(g_wakeup_time > 0)
		return 0;
	#if USE_LV_WATCH_STANDBY_OR_POSITION !=0
	if(Get_Realtime_Mode_Time()>0)
		return 0;
	#endif
#endif
#if USE_LV_WATCH_PLATFORM_ZW != 0
	if(g_dormant_t.status == 0)
		return 0;
	if(CWathcService_Is_Dorman_delay() == 0)
		;
	else
		return 0;
#endif
#if USE_LV_WATCH_DELAY_POWEROFF_FOR_DORMAT != 0
		if(get_need_link_net_flag() >0)
			return 0;
#endif

	if(g_dormant_t.status == 1)
	{
		if(g_dormant_t.dormt_end_h < g_dormant_t.dormt_start_h)
		{
			if(cur_time >= g_dormant_t.dormt_start_h || cur_time <= g_dormant_t.dormt_end_h)
			{
				is_in_dormant_time = 1;
			}
		}
		else
		{
			if(cur_time >= g_dormant_t.dormt_start_h && cur_time <= g_dormant_t.dormt_end_h)
			{	
				is_in_dormant_time = 1;
			}
		}
	}

	#if USE_LV_WATCH_PLATFORM_ZW != 1
	if(g_dormant_t.null_flag == 1)
	{
		//after awake, get next dormat time param,for second day
		if(g_dormant_t.status == 1)
			;
		else if((cur_time >= (g_dormant_t.dormt_end_h+1)) && (cur_time <= (g_dormant_t.dormt_end_h+120)))
			UI_NV_Read_Req(NV_SECTION_DORMAT_TIME_INFO, 0, sizeof(nv_watch_dormantime_t), (uint8_t*)&g_dormant_t);
	}
	#if 0//USE_LV_WATCH_PLATFORM_LIANCHUANG != 1
	if(cur_time >= USE_LV_WATCH_WS_OFF_HOUR*60 || cur_time < USE_LV_WATCH_WS_ON_HOUR*60)
		is_in_dormant_time = 1;
	#endif
	#endif //USE_LV_WATCH_PLATFORM_ZW

	return is_in_dormant_time;
}

#endif	//end of USE_LV_WATCH_DORMAT_TIME

#if USE_LV_WATCH_FENCE_ALARM != 0
ws_nv_fence_list * myNVMgr_GetFenceInfo(void)
{
    uint32_t length = sizeof(ws_nv_fence_list);
    ws_nv_fence_list * nvm = (ws_nv_fence_list *)lv_mem_alloc(length);
	if(nvm == NULL)
		return NULL;
	memset(nvm, 0, length);
    if(length != UI_NV_Read_Req(NV_SECTION_WMODE_INFO, NV_OFFSETOF(nv_watch_fencelist_t, ws_nv_fencelist), sizeof(ws_nv_fence_list), (uint8_t*)nvm)) {
        WS_PRINTF("read nvm error in myNVMgr_GetFenceInfo\n");
        lv_mem_free(nvm);
        return NULL;
    }
    return nvm;
}
#endif	//end of USE_LV_WATCH_FENCE_ALARM

void CWathcService_Vibrator_Play(void)
{
	Hal_Vibrator_Play_Onetime(NULL, 120);
}

#endif //end of USE_LV_WATCH_WS_KAER 

#if defined(__XF_WS_VENDOR_PS_HJYHEX__)
void WatchUtility_SetCABList(ws_contact_list *phb_list)
{
    if(phb_list == NULL)
        return;
    app_adaptor_contact_t * contact_list =  NULL;
	
	WS_PRINTF("%s() count=%d\n", __FUNCTION__,phb_list->count);
	if(phb_list->count>0)
	{
	    contact_list =  (app_adaptor_contact_t*)lv_mem_alloc(phb_list->count * sizeof(app_adaptor_contact_t));
	}
	else	
    {
        return;
    }
	
    if(contact_list)
    {
        for(int i=0; i<phb_list->count; i++)
        {
			WS_PRINTF("%s() i=%d, number=%s\n", __FUNCTION__, i, phb_list->contact[i].phone);
            memcpy(contact_list[i].contact_name, phb_list->contact[i].name, WATCH_CONTACTS_MAX_NAME_LEN);
            memcpy(contact_list[i].contact_number, phb_list->contact[i].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
            contact_list[i].contact_type = 2;
			contact_list[i].portrait_id = WATCH_PORTRAIT_ID_CAB;
        }        
    }
	myNVMgr_SetWhiteListStatus(true);
	app_adaptor_update_phonebook_cab_ind(contact_list, phb_list->count, 0, 0);

	lv_mem_free(phb_list->contact);
    
}
#endif /* USE_LV_WATCH_WS_KAER */

void WatchUtility_WsSms_Send(char* number, char* content)
{
    int numlen = strlen(number);
    int conlen = strlen(content);
    char* num = (char*)lv_mem_alloc(numlen+1);
    char* con = (char*)lv_mem_alloc(conlen+1);

    memcpy(num, number, numlen+1);
    memcpy(con, content, conlen+1);
	WS_PRINTF("%s() number=%s, num=%s\n", __FUNCTION__, number, num);
    app_adaptor_sms_send_req(num, con, conlen, NULL, NULL);
}

void WatchUtility_CallMonitorEvent(char *number)
{
    if(phone_is_monitor_on()){
        return;
    }
    
	if(phone_get_call_state() != MMI_MODEM_CALL_STATE_NULL)  // already have exist call
		return;
	
#if USE_LV_WATCH_SOS != 0
	if(SOS_OFF != get_sos_status())
		   return;
#endif 
	   
    int numlen = strlen(number);
    if(numlen>1) {
        #if USE_LV_WATCH_VIDEOCALL != 0
        voip_ui_end_call();
        #endif
        char* num = (char*)lv_mem_alloc(numlen+1);
        memcpy(num, number, numlen+1);
        app_adaptor_set_monitor(true);
        phone_voice_call_req(num);
    }
}


void WatchUtility_DialPadSwitch(int32_t onoff)
{
    if(onoff == 1){
        myNVMgr_SetDialPadStatus(1);
    }else if(onoff == 0){
        myNVMgr_SetDialPadStatus(0);
		#if USE_LV_WATCH_DIALPAD
        //dialpad_destroy();
		#endif
    }
}


void WatchUtility_SetVolume(uint8_t index)
{
    const uint8_t vol[7]={  HAL_AUDIO_SPK_LEVEL_0, 
                            HAL_AUDIO_SPK_LEVEL_2, 
                            HAL_AUDIO_SPK_LEVEL_3, 
                            HAL_AUDIO_SPK_LEVEL_4, 
                            HAL_AUDIO_SPK_LEVEL_5, 
                            HAL_AUDIO_SPK_LEVEL_6,
                            HAL_AUDIO_SPK_LEVEL_7};
    
    if(index>6) index = 6;
    
    WS_PRINTF("CWatchService--WatchUtility_SetVolume >>index=%d volume:%d ", index, vol[index]);
    
    UI_NV_Write_Req(NV_SECTION_UI_PHONE, 0, 1, (uint8_t *)&vol[index]);

    setting_set_ring_volume(vol[index], true, false);

    lv_obj_t * volume_obj = lv_watch_get_activity_obj(ACT_ID_SETTING_VOLUME);
    lv_obj_t * top_obj = lv_watch_get_top_activity_obj();

	if (top_obj == volume_obj){
		lv_obj_del(volume_obj);
		setting_volume_create_btn_action(NULL, LV_EVENT_CLICKED);
	}else if (volume_obj){
		lv_obj_del(volume_obj);
	}
}

void WatchUtility_PassivePoweroff(void *par)
{
    //WS_PRINTF("WatchUtility_PassivePoweroff\n");
    #if USE_LV_WATCH_VIDEOCALL != 0
    voip_ring_off_action(NULL, LV_EVENT_CLICKED);
    #endif 
	#if 1 //USE_LV_WATCH_ZTS !=0
	Wakeup_GuiTask(true);
	shutdown_confirm_btn_action(NULL, LV_EVENT_CLICKED);
	#else
    lv_watch_go_home();
    lv_task_del(lowbattery_task_p);

    Hal_Backlight_Off();
    watch_set_ready_state(false);
    watch_set_suspend_enable(false);

    Hal_Power_Off(HAL_TYPE_POWER_OFF);
	#endif
    watch_wakeup_lcd(false);
}

void WatchUtility_PassiveReboot(void *par)
{
	watch_wakeup_lcd(true);
	Wakeup_GuiTask(true);

	watch_thirdparty_exit();

#if USE_LV_WATCH_VIDEOCALL != 0
	voip_ring_off_action(NULL, LV_EVENT_CLICKED);
#endif 

	restart_yes_btn_action(NULL, LV_EVENT_CLICKED);
}

void WatchUtility_FindWatch(void)
{
#if USE_LV_WATCH_SEARCH != 0

	if(WatchUtility_IsMuteTime())
		return;
	
#if USE_LV_WATCH_UPGRADE != 0
	if(UGD_STATE_NULL != upgrade_get_state())
		return;
#endif

#if USE_LV_WATCH_SLIDE_UNLOCK !=0
	if (lv_watch_get_activity_obj(ACT_ID_SLIDE_UNLOCK))
	{
		return;
	}
#endif
    if((phone_get_call_state() != MMI_MODEM_CALL_STATE_NULL))
		return;
	
    #if USE_LV_WATCH_VIDEOCALL != 0
    if(MMI_VOIP_CALL_STATE_NULL != get_voipcall_params_call_state())
		return;
    #endif 
	
    watch_wakeup_lcd(false);
    
    lv_obj_t * tc_obj = lv_watch_get_activity_obj(ACT_ID_SEARCH);
    
    if(tc_obj == NULL)
    {
        search_create_event_cb(NULL, LV_EVENT_CLICKED);
        return;
    }
    ;
#endif
}

void WatchUtility_BonusesUpdate(uint32_t bonus)
{
    myNVMgr_SetWsBonuses(bonus);
#if USE_LV_WATCH_DIAL != 0
    dial_clock_update_immediately();
#endif
}

void WatchUtility_MakeFriendRsp(ws_make_friend *res)
{
#if USE_LV_WATCH_MAKE_FRIENDS != 0
    if(res->result == 1)
    {
        app_adapter_make_friends_rsp(MAKE_FIRIENDS_SUCCEED);
    }
    else if(res->result == -1) 
    {
        app_adapter_make_friends_rsp(MAKE_FIRIENDS_DUPLICATE);
    }
    else
    {
        app_adapter_make_friends_rsp(MAKE_FIRIENDS_FAILED);
    }
#endif    
#if USE_LV_WATCH_MAKE_FRIENDS_CODE != 0
    if(res->result == 1)
    {
        app_adapter_make_friends_code_rsp(MAKE_FIRIENDS_CODE_SUCCEED, NULL, 0, NULL);
    }
    else if(res->result == -1) 
    {
        app_adapter_make_friends_code_rsp(MAKE_FIRIENDS_CODE_DUPLICATE, NULL, 0, NULL);
    }
    else
    {
        app_adapter_make_friends_code_rsp(MAKE_FIRIENDS_CODE_FAILED, NULL, 0, NULL);
    }
#endif    
}

#if USE_LV_WATCH_MAKE_FRIENDS != 0
void WatchUtility_SetFriendsList(ws_friends_list *friends_list)
{
    if(friends_list == NULL)
        return;

    app_adaptor_friend_t * contact_list =  NULL;
    int count = 0;
	if(friends_list->cnt>0)
	{
        count = (friends_list->cnt < NV_WATCH_MAX_FRIENDS_NUM ) ? friends_list->cnt : NV_WATCH_MAX_FRIENDS_NUM;
    	contact_list =  (app_adaptor_friend_t*)lv_mem_alloc(count * sizeof(app_adaptor_friend_t));
	}
	
    if(contact_list)
    {
        for(int i=0; i < count; i++)
        {
            memcpy(contact_list[i].friend_name, friends_list->friends[i].name, WATCH_CONTACTS_MAX_NAME_LEN);
            memcpy(contact_list[i].friend_number, friends_list->friends[i].phone, WATCH_CONTACTS_MAX_NUMBER_LEN);
            memcpy(contact_list[i].friend_imei, friends_list->friends[i].imei, WATCH_CONTACTS_MAX_NUMBER_LEN);
            //memcpy(contact_list[i].friend_image, friends_list->friends[i].image, WATCH_CONTACTS_MAX_FILE_LEN);
            contact_list[i].friend_image[0]=0;
			contact_list[i].time = friends_list->friends[i].time;
			contact_list[i].contact_type = 2;			
			#if USE_LV_WATCH_VOICE_MSG != 0
            	contact_list[i].portrait_id = WATCH_PORTRAIT_ID_OTHERS;  //VOICE_MSG_PORTRAIT_ID_FRIENDS_GROUP;
			#endif
            contact_list[i].canVideo = friends_list->friends[i].canVideo;
        }
        
    }
    
#if USE_LV_WATCH_VOICE_MSG != 0
	app_adaptor_update_friend_ind(contact_list, count);
#endif /* USE_LV_WATCH_VOICE_MSG */

	lv_mem_free(friends_list->friends); 
}
#endif

#if USE_LV_WATCH_LOWBATTERY_WS_LIHAO != 0
void WatchUtility_PowerLowMode_Received(bool on)
{
    if(on){
        //MMI_ModemAdp_WS_ServiceSwitch(false);  // no need to send shutdown cmd
        WS_FPRINTF("%s():%d", __FUNCTION__, __LINE__);
		MMI_ModemAdp_WS_Stop_Ext();
    }
}
#endif


void WatchUtility_SetVCallContactList(ws_vcall_contact_list *friends_list)
{
    if(friends_list == NULL)
        return;
#if USE_LV_WATCH_VIDEOCALL != 0
	if(friends_list->cnt == 0)
	{
		app_adaptor_update_voip_phonebook_ind(NULL, 0);
	}
	else
	{
		app_adaptor_update_voip_phonebook_ind(friends_list->contacts, friends_list->cnt);
	}
#endif

    if(friends_list->contacts){
    	lv_mem_free(friends_list->contacts);
    }
    
}

int8_t WatchUtility_MakeVideoCall(ws_vcall_info  *vcallinfo)
{
    int8_t ret = -1;
#if USE_LV_WATCH_VIDEOCALL != 0   
    ws_vcall_info *vcallinfo_ws = (ws_vcall_info*)MMI_ModemAdp_WS_GetVCallInfo();
    
    memcpy(vcallinfo_ws, vcallinfo, sizeof(ws_vcall_info));
    
#if USE_LV_WATCH_VIDEOCALL_MONITOR_BAIDU != 0    
    if(!voip_phone_is_dialing() && !WatchUtility_IsVCallMoniterOn())
#else
    if(!voip_phone_is_dialing())
#endif
    {
        MMI_ModemAdp_WS_VCall_HangUp(NULL);
        return ret;
    }

    WS_PRINTF("WatchUtility_MakeVideoCall >>call_type:%d", vcallinfo->call_type);
    ret = voip_call_client_open(vcallinfo->app_username, vcallinfo->call_type, vcallinfo->appkey, vcallinfo->video_id, vcallinfo->username, vcallinfo->secret_key, vcallinfo->srv_url, vcallinfo->srv_port);
    if(ret == 0)
    {
        voip_call_wait_timeout(vcallinfo->wait_time, vcallinfo->limit_time);
    }
#endif   
    printf("%s %d ret=%d\n", __FUNCTION__, __LINE__, ret);
    return ret;
}

bool is_rat_gsm(void)
{
    MMI_MODEM_PLMN_RAT rat;

    watch_modem_get_operator_req(&rat);
    if(MMI_MODEM_PLMN_RAT_LTE == rat)
        return false;
    else
        return true;
}

void WatchUtility_VideoCallIncoming(ws_vcall_info  *           vcallinfo)
{
#if USE_LV_WATCH_VIDEOCALL != 0 
    uint8_t val;

	#if 0
    if(MMI_ModemAdp_WS_GetSettings(WS_VALUE_DISABLE_VIDEO_CALL, &val)){
        if(val){
            WS_PRINTF("WatchUtility_VideoCallIncoming >> Video call is not allowed!");
            MMI_ModemAdp_WS_VCall_Reject(vcallinfo->video_id);
            return;
        }
    }
	#endif
	
	if(SOS_OFF != get_sos_status()){
		MMI_ModemAdp_WS_VCall_Reject(vcallinfo->video_id);
		return;
	}
	
	val = LCS_MT_VCALL;

#if USE_LV_WATCH_VIDEOCALL_MONITOR_ZNSH != 0
	if (vcallinfo->video_monito == 1){
		//lv_watch_go_home();
		val = LCS_MT_VMON;
	}
#endif		

#if USE_LV_WATCH_UPGRADE != 0
	if(UGD_STATE_NULL != upgrade_get_state()){
        MMI_ModemAdp_WS_VCall_Reject(vcallinfo->video_id);
		return;
	}
#endif

#if defined(ZFB_SUPPORT) && defined(__XF_ALIPAY_QRCODE__) && USE_LV_WATCH_VIDEOCALL_MONITOR_ZNSH != 0
	if (vcallinfo->video_monito == 1){
	    lv_obj_t * top_obj = lv_watch_get_top_activity_obj();
	    if(top_obj != NULL) {
	        lv_watch_activity_ext_t * ext = lv_obj_get_ext_attr(top_obj);
	        if(ext->actId == ACT_ID_UPAY_QRCODE_SCAN) {
			    if(NULL != ext->prepare_destory) {
			        ext->prepare_destory(top_obj);
			    }
	            lv_obj_del(top_obj);
	        }
	    }
	}
#endif

    if(watch_is_locked_screen(true, val) || WatchUtility_IsRemoteCameraCtrl()\
		|| get_voipcall_params_call_state() != MMI_VOIP_CALL_STATE_NULL\
		|| MMI_MODEM_CALL_STATE_NULL != phone_get_call_state() || is_rat_gsm()
		)
    {
        WS_PRINTF("WatchUtility_VideoCallIncoming >>vcall_state:%d", get_voipcall_params_call_state());
        MMI_ModemAdp_WS_VCall_Reject(vcallinfo->video_id);
		return;
    }
		
    ws_vcall_info *vcallinfo_ws = (ws_vcall_info*)MMI_ModemAdp_WS_GetVCallInfo();

    memcpy(vcallinfo_ws, vcallinfo, sizeof(ws_vcall_info));
    WS_PRINTF("WatchUtility_VideoCallIncoming >>video_monito:%d", vcallinfo->video_monito);
    /* 根据名字匹配 */
    if(vcallinfo->relationship_img_id == 0xff)
    {
        if(strcmp(vcallinfo->relationship, "爸爸") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_FATHER;
        else if (strcmp(vcallinfo->relationship, "妈妈") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_MOTHER;
        else if (strcmp(vcallinfo->relationship, "外婆") == 0 ||strcmp(vcallinfo->relationship, "姥姥") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_MATERNAL_GRADMOTHER;
        else if (strcmp(vcallinfo->relationship, "外公") == 0 ||strcmp(vcallinfo->relationship, "姥爷") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_MATERNAL_GRADFATHER;
        else if (strcmp(vcallinfo->relationship, "爷爷") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_PATERNAL_GRADFATHER;
        else if (strcmp(vcallinfo->relationship, "奶奶") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_PATERNAL_GRADMOTHER;
        else if (strcmp(vcallinfo->relationship, "叔叔") == 0 || strcmp(vcallinfo->relationship, "舅舅") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_UNCLE;
        else if (strcmp(vcallinfo->relationship, "阿姨") == 0 || strcmp(vcallinfo->relationship, "舅妈") == 0)
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_AUNT;
        else
            vcallinfo->relationship_img_id = WATCH_PORTRAIT_ID_OTHERS;
    }
#if USE_LV_WATCH_VIDEOCALL_MONITOR_ZNSH != 0
    if (vcallinfo->video_monito == 1)
		app_adaptor_set_video_monitor(true);
#endif		
    voip_call_client_incoming(vcallinfo->call_type, vcallinfo->app_username, vcallinfo->relationship, vcallinfo->relationship_img_id, vcallinfo);

#endif    
}

void WatchUtility_VideoCallHangUpInd(char   *            vcall_id)
{
#if USE_LV_WATCH_VIDEOCALL != 0   
    ws_vcall_info *vcallinfo = (ws_vcall_info*)MMI_ModemAdp_WS_GetVCallInfo();

    if(vcallinfo==NULL) return;
    
    if(0 == strcmp(vcall_id, vcallinfo->video_id))
    {
        watch_voip_call_response_no_carrier(0, 0, NULL);
        voip_call_hangup_req(FALSE);
        vcallinfo->video_id[0] = 0;
    }

#if USE_LV_WATCH_VIDEOCALL_MONITOR_BAIDU != 0    
    WatchUtility_VCallMoniterEnd();
#endif    
#endif    
}


void WatchUtility_VideoCallFailed(char   * reason)
{
#if USE_LV_WATCH_VIDEOCALL != 0   
    ws_vcall_info *vcallinfo = (ws_vcall_info*)MMI_ModemAdp_WS_GetVCallInfo();

    watch_voip_call_response_no_carrier(0, 0, reason);
    vcallinfo->video_id[0] = 0;
#if USE_LV_WATCH_VIDEOCALL_MONITOR_BAIDU != 0    
    WatchUtility_VCallMoniterEnd();
#endif    
#endif    
}


void WatchUtility_AnswerVideoCall(char  *uri)
{
    int8_t ret = -1;
#if USE_LV_WATCH_VIDEOCALL != 0   
    ws_vcall_info *vcallinfo = (ws_vcall_info*)MMI_ModemAdp_WS_GetVCallInfo();
    
    if(vcallinfo==NULL)
    {
        return ret;
    }
    
    WS_PRINTF("WatchUtility_AnswerVideoCall >>appkey:%s", vcallinfo->appkey);
    WS_PRINTF("WatchUtility_AnswerVideoCall >>username:%s", vcallinfo->username);
    WS_PRINTF("WatchUtility_AnswerVideoCall >>app_username:%s", vcallinfo->app_username);
    WS_PRINTF("WatchUtility_AnswerVideoCall >>secret_key:%s", vcallinfo->secret_key);
    WS_PRINTF("WatchUtility_AnswerVideoCall >>video_id:%s", vcallinfo->video_id);
    WS_PRINTF("WatchUtility_AnswerVideoCall >>call_type:%d", vcallinfo->call_type);
    
    ret = voip_call_client_open(vcallinfo->app_username, vcallinfo->call_type, vcallinfo->appkey, vcallinfo->video_id, vcallinfo->username, vcallinfo->secret_key, vcallinfo->srv_url, vcallinfo->srv_port);
    if(ret == 0)
    {
        voip_call_wait_timeout(vcallinfo->wait_time, vcallinfo->limit_time);
        MMI_ModemAdp_WS_VCall_Answer(vcallinfo->video_id);
    }
    WS_PRINTF("WatchUtility_AnswerVideoCall >>ret:%d", ret);

#endif    
    return ret;
}

void WatchUtility_QuitFamilyRes(bool suc)
{
	extern lv_task_t *quit_family_task_p;
   // quit_family_result_create(suc);
    watch_wakeup_lcd(true);
	if(quit_family_task_p){
	    lv_task_ready(quit_family_task_p);
		quit_family_task_p = NULL;
	}

#if USE_LV_WATCH_WS_DUER != 0
    if(suc){
        duer_activity_close(ACT_ID_BINDING);
        watch_set_ready_state(false);
		watch_set_suspend_enable(false, ACT_ID_SHUTDOWN, 0);
        Hal_Power_Off(HAL_TYPE_REBOOT);
    }
#endif 
}


#if 102
static void Gui_RPC_RemoteCamera(uint32_t pa, int32_t pb, void *pc);

static uint32_t camera_remote_control = 0;

uint32_t WatchUtility_IsRemoteCameraCtrl(void)
{
    return camera_remote_control;
}


static void MMI_ModemAdp_RPC_RemoteCamera(uint32_t pa, int32_t pb, void *pc)
{
    watch_gui_rpc_req(Gui_RPC_RemoteCamera, pa, pb, pc); 
}

static void WatchUtility_RemoteCameraEvtInd(uint32_t param)
{
    MMI_ModemAdp_Rpc_Req(MMI_ModemAdp_RPC_RemoteCamera, param, 0, 0);
}


static void WatchUtility_RemoteCaptureTask(void * param)
{
#if USE_LV_WATCH_CAMERA != 0
    void *jpeg_buf = NULL;
    cam_capture_t cap_params;
    cap_params.width = 240;
    cap_params.height = 240;

    if(Hal_Camera_Capture_Buffer(&cap_params, &jpeg_buf) == 0 && jpeg_buf) {
#if USE_LV_WATCH_WS_BASE != 0
        MMI_ModemAdp_WS_Send_Picture(jpeg_buf, cap_params.jpeg_size, (char*)FILE_USE_FOR_REMOTE_CAPTURE);
#endif
        lv_mem_free((void *)jpeg_buf);
    }
	uos_timer_start(mmi_ws_remotecam_Timer, 50, 0, WatchUtility_RemoteCameraEvtInd, 0);    
#endif /* USE_LV_WATCH_CAMERA */
}

static void WatchUtility_RemoteCaptureDone(void * param)
{
#if USE_LV_WATCH_CAMERA != 0
    WS_PRINTF("WatchUtility_RemoteCaptureDone >> camera_remote_control=%d", camera_remote_control);
    camera_remote_control--;
    if(camera_remote_control>0){
        if(MMI_ModemAdp_WS_Is_Online()){
            WatchUtility_RemoteCaptureTask(NULL);
            return;
        }
    }

    camera_remote_control = 0;

    //camera_exist_action();
    Hal_Camera_Stop_Preview(0);

    _lv_indev_enable(LV_INDEV_TYPE_POINTER, true);
    _lv_indev_enable(LV_INDEV_TYPE_KEYPAD, true);
    if(WatchUtility_IsMuteTime())
    {
#if USE_LV_WATCH_MUTETIME_DIALOG != 0    
        mute_time_update_activity(true);
#else 
        _lv_indev_enable(LV_INDEV_TYPE_POINTER, false);
#endif
    }
    watch_set_suspend_enable(true, ACT_ID_LAUNCHER, 0);
#endif
    
}


static void Gui_RPC_RemoteCamera(uint32_t pa, int32_t pb, void *pc)
{
    WS_PRINTF("WatchUtility_RemoteCaptureGui >> status=%d", pa);
    if(pa){
        WatchUtility_RemoteCaptureTask(NULL);
    }else{
        WatchUtility_RemoteCaptureDone(NULL);
    }

}

void WatchUtility_RemoteCaptureEvent(uint32_t id)
{
#if USE_LV_WATCH_CAMERA != 0

#if USE_LV_WATCH_VIDEOCALL != 0
    if(get_voipcall_params_call_state() != MMI_VOIP_CALL_STATE_NULL)
    {
        WS_PRINTF("WatchUtility_RemoteCaptureEvent >>video call state:%d", get_voipcall_params_call_state());
        return;
    }
#endif 

    if(lv_watch_get_activity_obj(ACT_ID_CAMERA))
    {
        return;
    }
    if(lv_watch_get_activity_obj(ACT_ID_FACTORY_MODE_MAIN))
    {
        return;
    }

#if 0//defined(__XF_LEBAO_KOUDAI__)
	lebao_player_stop_playing_immediately();
	lebao_search_close(NULL);

	kd_player_stop_playing_immediately();
	kd_search_close(NULL);
#endif
    watch_set_suspend_enable(false, ACT_ID_LAUNCHER, 0);
    WS_PRINTF("WatchUtility_RemoteCaptureEvent >> camera_remote_control=%d", camera_remote_control);
    if(camera_remote_control>0)
    {
        camera_remote_control++;
        return;
    }

    camera_remote_control++;
    
    _lv_indev_enable(LV_INDEV_TYPE_POINTER, false);
    _lv_indev_enable(LV_INDEV_TYPE_KEYPAD, false);
    
#if USE_CRANE_CAMERA_MULTI
    mci_camera_set_sensor(id);
#endif
	int ret = mci_camera_openpreview();

	if (ret == 0){
		if(mmi_ws_remotecam_Timer==NULL){			 
			uos_timer_create(&mmi_ws_remotecam_Timer);
		}
		uos_timer_start(mmi_ws_remotecam_Timer, 400, 0, WatchUtility_RemoteCameraEvtInd, 1);
	}else{
		camera_remote_control = 0;
		Hal_Camera_Stop_Preview(0);
		_lv_indev_enable(LV_INDEV_TYPE_POINTER, true);
		_lv_indev_enable(LV_INDEV_TYPE_KEYPAD, true);
        watch_set_suspend_enable(false, ACT_ID_LAUNCHER, 0);
	}
#endif /* USE_LV_WATCH_CAMERA */
}


#endif 

void WatchUtility_Token_PopupWindow(char* token)
{
	#if USE_LV_WATCH_TOKEN_CODE != 0
    token_code_show();
    watch_wakeup_lcd(true);
	#endif
}

#if USE_LV_WATCH_VIDEOCALL_MONITOR_BAIDU != 0    

bool video_call_moniting = false;


bool WatchUtility_IsVCallMoniterOn(void)
{
    return video_call_moniting;
}

void WatchUtility_VCallMoniterEnd(void)
{
    if(video_call_moniting){
        video_call_moniting = false;
        camera_remote_control = 0;
        
        app_adaptor_set_monitor(false);
    	
    	//watch_set_suspend_enable(true);
    	//watch_set_sleep_lcd_only(false);
        WatchUtility_set_volume_disable(0);        
        Hal_Audio_SetVolume(query_current_volume());  
        Hal_Audio_Manage_End_Ind(AUDIO_CTRL_PRIORITY_2);
    }
}

static void VCallMoniter_audio_ctrl_callback(AUDIO_CTRL_PRIORITY priority)
{
    printf("%s,priority is %d\n", __FUNCTION__,priority);
}

void WatchUtility_VCallMonitorEvent(char *number)
{
#if USE_LV_WATCH_CAMERA != 0
#if USE_LV_WATCH_VIDEOCALL != 0
	if(phone_get_call_state() != MMI_MODEM_CALL_STATE_NULL){
		return;
	}
	
	if(SOS_OFF != get_sos_status()){
	     return;
	}

    if(get_voipcall_params_call_state() != MMI_VOIP_CALL_STATE_NULL){
        WS_PRINTF("WatchUtility_RemoteCaptureEvent >>video call state:%d", get_voipcall_params_call_state());
        return;
    }

    if(lv_watch_get_activity_obj(ACT_ID_CAMERA)){
        return;
    }
    if(lv_watch_get_activity_obj(ACT_ID_FACTORY_MODE_MAIN)){
        return;
    }    

    if(WatchUtility_IsRemoteCameraCtrl()){
        return;
    }

	//watch_thirdparty_exit();

    if(AUDIO_CTRL_PRIORITY_2 == Hal_Audio_Manage_Start_Req(AUDIO_CTRL_PRIORITY_2,VCallMoniter_audio_ctrl_callback)){
        video_call_moniting = true;
        camera_remote_control = 0xabc;

        app_adaptor_set_monitor(true);
        
        MMI_ModemAdp_WS_VCall_CallReq(MODEM_VIDEO_CALL_TYPE_VOICE, number);   
    	//watch_set_suspend_enable(false);
    	//watch_set_sleep_lcd_only(true);
    }

#endif 
#endif 
}

#endif 

#if USE_LV_WATCH_WS_SUPPORT_HEALTH_DATA != 0    
#if USE_DRV_TEMPERATURE!= 0
extern float temperature_drv_get_value_c(void);
#endif 
static void WatchUtility_ReadProcess(void *param)
{       	
    uint32_t  temp;
    float     temp_c;
    uint32_t  *data = (uint32_t *)param;
    lv_task_t *task = (lv_task_t*)data[WS_HD_UNKOWN];

    for(uint8_t i=WS_HD_HEAT_RATE; i<WS_HD_UNKOWN; i++){
        if(data[i]>0 && lv_tick_elaps(data[i])>5*1000){
            void *data_get = 0;
            if(i==WS_HD_HEAT_RATE){
                #if USE_DRV_HEART !=0
                temp = HR_get_heart_value();
				printf("%s() temp=%d", __func__, temp);
                if(temp > 20 && temp < 500) data_get = &temp;
                #endif 
            }else if(i==WS_HD_BLOOD_PRESSURE){
                #if USE_DRV_HEART !=0
                temp = HR_get_blood_low();
				printf("%s() temp=%d", __func__, temp);
                if(temp > 30 && temp < 200){
                    temp = HR_get_blood_high();
					printf("%s() temp=%d", __func__, temp);
                    if(temp > 30 && temp < 500){
                        temp = (temp<<16) + HR_get_blood_low();
                        data_get = &temp;
						printf("%s() temp=%d", __func__, temp);
                    }
                }
                #endif
            }else if(i==WS_HD_BLOOD_OXYGEN){
                #if USE_DRV_HEART !=0
                temp = HR_get_blood_spo2();
				printf("%s() temp=%d", __func__, temp);
                if(temp > 20 && temp < 101) data_get = &temp;
                #endif 
            }else if(i==WS_HD_TEMPERATURE){
                #if USE_DRV_TEMPERATURE!= 0
                temp_c = temperature_drv_get_value_c();
				printf("%s() temp=%f", __func__, temp_c);
                if(temp_c > 20.0 && temp_c < 50.0) data_get = &temp_c;
                #endif 
            }
            
            if(data_get != 0){
                MMI_ModemAdp_UploadHealthData(i, data_get, NULL);
                data[i] = 0;
            }

            if(data[i] && lv_tick_elaps(data[i])>60*1000){
                data[i] = 0;
            }
        }
    }

    //check stop sensor!
    for(uint8_t i=WS_HD_HEAT_RATE; i<WS_HD_UNKOWN; i++){
        if(data[i]!=0){
            return;
        }
    }
#if USE_DRV_HEART !=0
    HR_drv_exit();
#endif 
#if USE_DRV_TEMPERATURE!= 0
    temperature_drv_exit();
#endif

    if(task){
        lv_task_del(task);
    }
    data[WS_HD_UNKOWN] = 0;
	
}


void WatchUtility_HealthDataReq(uint16_t type, uint16_t freq)
{
    static uint32_t data[WS_HD_UNKOWN+1]={0,0,0,0,0};

    if(type >= WS_HD_UNKOWN){
        return;
    }

    if(freq == WS_HD_MEAS_CLOSE){
        return;
    }else{
        switch(type){
            case WS_HD_HEAT_RATE:
            #if USE_DRV_HEART !=0
                data[WS_HD_HEAT_RATE] = HR_drv_start_ext();
            #endif     
                break;
            case WS_HD_BLOOD_PRESSURE:
            #if USE_DRV_HEART !=0
                data[WS_HD_BLOOD_PRESSURE] = HR_drv_start_ext();
            #endif     
                break;
            case WS_HD_BLOOD_OXYGEN:
            #if USE_DRV_HEART !=0
                data[WS_HD_BLOOD_OXYGEN] = HR_drv_start_ext();
            #endif     
                break;
            case WS_HD_TEMPERATURE:
            #if USE_DRV_TEMPERATURE!= 0
                temperature_drv_start();
                data[WS_HD_TEMPERATURE] = lv_tick_get();
            #endif 
                break;
            default:
                return;
        }
    }

    if(data[WS_HD_UNKOWN] == 0){
        data[WS_HD_UNKOWN] = (uint32_t)lv_task_create(WatchUtility_ReadProcess, 1000, LV_TASK_PRIO_MID, data);
    }

}

#endif


#if USE_LV_WATCH_WS_BIND_ACTIVITY != 0
void WatchUtility_AppBind(ws_bind_ref *bind_ref)
{
    appbind_create(NULL, bind_ref->more);
}

void WatchUtility_AppBindRes(ws_bind_res *bind_res)
{
    if(bind_res->success){
        tip_content_create(NULL, WATCH_TEXT_ID_BIND_SUCCESS);
        myNVMgr_SetWsBindCode((char*)bind_res->more);
    }else{
        tip_content_create(NULL, WATCH_TEXT_ID_BIND_FAILED);
    }
}

#endif

#if USE_LV_WATCH_MAKE_FRIENDS_CODE != 0
void WatchUtility_MakeFriendsCodeRecieved(ws_friend_code *code_res)
{
    app_adapter_make_friends_code_rsp(MAKE_FIRIENDS_CODE_RECIEVED, code_res->code, code_res->expire, NULL);
}

void WatchUtility_MakeFriendsConfirmInd(char *name)
{
    app_adapter_make_friends_code_rsp(MAKE_FIRIENDS_CODE_CONFIRM_IND, NULL, 0, name);
}

void WatchUtility_MakeFriendsCodeVerify(char *name)
{
    if(name[0]){
        app_adapter_make_friends_code_rsp(MAKE_FIRIENDS_CODE_VERIFY_OK, NULL, 0, name);
    }else{
        app_adapter_make_friends_code_rsp(MAKE_FIRIENDS_CODE_VERIFY_FAILD, NULL, 0, NULL);
    }
}



#endif 

#if defined(__XF_WS_OTA__)
void WatchUtility_OTANewVersion(ws_ota_ver *ver)
{
    if(ver){
        if(ver->date){
            printf("WatchUtility_OTANewVersion() date=%s", ver->date);
            free(ver->date);
        }
        if(ver->version){
            printf("WatchUtility_OTANewVersion() version=%s", ver->version);
            free(ver->version);
        }
        if(ver->description){
            printf("WatchUtility_OTANewVersion() description=%s", ver->description);
            free(ver->description);
        }
        if(ver->url){
            printf("WatchUtility_OTANewVersion() url=%s", ver->url);
            free(ver->url);
        }
        if(ver->force){
            MMI_ModemAdp_WS_OTA_DownloadVersion(NULL);
        }else{
            //MMI_ModemAdp_WS_OTA_DownloadVersion(NULL); //test only
        }
    }
}

void WatchUtility_OTAUiStatusInd(ws_val_t *val)
{
    if(val->nval == WS_OTA_STA_DL){
        WS_PRINTF("WatchUtility_OTAUiStatusInd >> percent=%d \n", val->val);
    }
}

void WatchUtility_OTADownloadRes(uint32_t res)
{
    if(res == 0){
        WS_PRINTF("WatchUtility_OTADownloadRes >> success! \n");
        //MMI_ModemAdp_WS_OTA_InstallNow(NULL); // test only
    }else{
        WS_PRINTF("WatchUtility_OTADownloadRes >> failed! \n");
    }
}

#endif
#if USE_LV_WATCH_CUSTOM_MENU != 0
void WatchUtility_ResetMenuConfig(void)
{
    extern nv_watch_kaersettings_t ke_settingsinfo;
    ke_settingsinfo.menucfgv = 0;
    ke_settingsinfo.menucfgv = ke_MenuConfig_Init();
    ke_SetMenuConfig(ke_settingsinfo.menucfgv);
    lv_watch_go_home_mute();
    #if USE_LV_WATCH_LAUNCHER != 0
    launcher_recreate();
    #endif
}
#endif
void myNVMgr_WsResetAllData(void)
{
#if (USE_LV_WATCH_WS_KAER != 0)
		char UrlPort[50] = {0};
		myNVMgr_GetWsUrlPort(UrlPort);
#endif 
	    UI_NV_Reset_Default(NV_SECTION_UI_SETTINGS);
	    UI_NV_Reset_Default(NV_SECTION_UI_PHONEBOOK);
#if USE_LV_WATCH_LC_WHITELIST_MAX_100 != 0
		UI_NV_Reset_Default(NV_SECTION_NEW_WHITELIST_INFO);
#endif
	    UI_NV_Reset_Default(NV_SECTION_UI_SOS);
		#if USE_LV_WATCH_PRO_V22 != 0
		 UI_NV_Reset_Default(NV_SECTION_UI_SOS_NEW);
		#endif
	    UI_NV_Reset_Default(NV_SECTION_UI_WATCH_SERVICE);
		#if !defined(__XF_PRO_XIAOBEN__)
	    UI_NV_Reset_Default(NV_SECTION_UI_ALARM);
		#endif
		#if USE_LV_WATCH_POWEROFF_CLOCK_ARRAY != 0
		UI_NV_Reset_Default(NV_SECTION_UI_POWEROFF_ALARM);
		#endif

#if USE_LV_WATCH_VIDEOCALL !=0
	    UI_NV_Reset_Default(NV_SECTION_UI_VCALL_CONTACT);
#endif
#if USE_LV_WATCH_VOICE_MSG != 0
        voice_msg_delete_all();//delete database before delete friends db.....
#if USE_LV_WATCH_MAKE_FRIENDS != 0
	    UI_NV_Reset_Default(NV_SECTION_UI_FRIENDS);
#endif
#endif
	    UI_NV_Reset_Default(NV_SECTION_UI_STUDY_GAME);
#if (USE_LV_WATCH_WS_KAER != 0)||(USE_LV_WATCH_WS_CT != 0)		
	#if USE_LV_WATCH_UNREADCALL_IMG != 0
		set_unread_call_numbers(0);
	#endif

		UI_NV_Reset_Default(NV_SECTION_UI_CALLHISTORY);	
#endif
#if USE_LV_WATCH_FENCE_ALARM != 0
		UI_NV_Reset_Default(NV_SECTION_WMODE_INFO);	
#endif
        #if !defined(__XF_PRO_XIAOBEN__)
		set_alarm_on_status(0);
		#endif
	    wsMuteTime_init(true);
		wsWatchUtility_ParamInit();
	    WatchUtility_CheckMuteTime();
#if (USE_LV_WATCH_WS_KAER != 0)
		myNVMgr_SetWsUrlPort(UrlPort);
#endif

#if USE_LV_WATCH_GOHOME_WIFIMAC != 0
	CWathcService_SaveGohome_WifiMac(NULL);
	CWathcService_init_GoScene_WifiMac(1);
#endif
#if USE_LV_WATCH_LOCATION_GROUP_PERIOD == 1
	UI_NV_Reset_Default(NV_SECTION_GROUP_PERIOD);
#endif

#if USE_LV_WATCH_WS_KAER != 0
	WatchUtility_Clear_All_AlarmList();
	UI_NV_Reset_Default(NV_SECTION_UI_PEDOMETER);//sport
	ke_gsensor_factory_reset();
#if USE_LV_WATCH_SMS_MSG != 0
	sms_msg_close_all_activity();
	sms_msg_delete_all();  
#endif
		UI_NV_Reset_Default(NV_SECTION_UI_STEPS_COUNT);
#if USE_CRANE_WATCH_GSENSOR
		mmi_steps_clear();
#endif
#endif		

	write_setting_profile_status(SETTING_PPROFILE_NORMAL);
	setting_init();
	stopwatch_reset();

#if USE_LV_WATCH_PLATFORM_QUANTONG != 0
		if(ke_GetQuantongVerFlag() != TY_VER)
		{
			UI_NV_Reset_Default(NV_SECTION_CALLDUR_INFO);
			UI_NV_Reset_Default(NV_SECTION_CALLLOG_INFO);
			UI_NV_Reset_Default(NV_SECTION_PARAM_LOC_INFO);
		}
#endif

#if USE_LV_WATCH_ONLY_MTCALL_LIMIT != 0
	if((ke_GetLianchuangVerFlag() == AH_VER))
	{
		myNVMgr_SetWhiteListStatus(2);
		#if USE_LV_WATCH_SLIDE_UNLOCK !=0
		setting_slide_unlock_on_off(1);
		#endif
	}
#endif
#if USE_LV_WATCH_DORMAT_TIME != 0
	UI_NV_Reset_Default(NV_SECTION_DORMAT_TIME_INFO);
#endif
#if USE_LV_WATCH_WORK_MODE != 0
	UI_NV_Reset_Default(NV_SECTION_WORK_MODE_INFO);
#endif
#if USE_PLATFORM_SMS_UI != 0
    platform_sms_delAll();
#endif
#if USE_LV_WATCH_PLATFORM_SMS_YUYIN != 0
	platform_liuyan_sms_delAll();
#endif

	#if USE_LV_WATCH_MODE_SWITCH != 0
		if(get_flightmode_state())
		{
			set_flightmode_state(false);
		}
		else if(get_powersaving_state())
		{
			set_powersaving_state(false);
		}
	#endif	
	    if(MMI_Modem_Get_Volte_State_Req(watch_modem_get_sim_id()) == 0) {
	        watch_modem_set_volte_state_req(1);
	    }
#if USE_LV_WATCH_STUDENT_INFO != 0
	UI_NV_Reset_Default(NV_SECTION_UI_NH_STU_INFO);
#endif

#if USE_LV_WATCH_SMART_JUMPING_XB != 0
	UI_NV_Reset_Default(NV_SECTION_JUMP_BT_NAME);
	UI_NV_Reset_Default(NV_SECTION_JUMP_BT_ADDR);
#endif

#if USE_LV_WATCH_STUDENT_INFO != 0
	UI_NV_Reset_Default(NV_SECTION_UI_NH_STU_INFO);
    reset_student_info();
#endif
#if USE_LV_DM != 0
	//clear dm regist success time
	UI_NV_Reset_Default(NV_SECTION_UI_DM);
	UI_NV_Reset_Default(NV_SECTION_DM_REG_CTCC_SET);
#endif
#if ((USE_LV_WATCH_SCHOOL_CONTACT_LIST !=0) || (USE_LV_WATCH_VCODE_INFO != 0))
	UI_NV_Reset_Default(NV_SECTION_VNET_INFO);
#endif
#if USE_LV_WATCH_RESER_BIND != 0
#if USE_LV_WATCH_CUSTOM_MENU != 0
	WatchUtility_ResetMenuConfig();
#endif
    nv_watch_settings_t nvm_settings;
    uint32_t length = sizeof(nv_watch_settings_t);
    UI_NV_Read_Req(NV_SECTION_UI_SETTINGS, 0, length, (uint8_t *)&nvm_settings);
    nvm_settings.first_boot_flag = 0;
    UI_NV_Write_Req(NV_SECTION_UI_SETTINGS, 0, length, (uint8_t *)&nvm_settings);
#endif
}


void Wakeup_GuiTask(bool lcd_on)
{    
    if(0 == Hal_Pm_Get_State()) {
        Hal_Pm_WakeUp_UI_Task();
    }

    if(lcd_on)
    {
        watch_wakeup_lcd(true);
		if(0 == Hal_Pm_Get_State()) {
        Hal_Pm_WakeUp();
      
	    } else {
	    
	        watch_wakeup_time_reset();
	    }
    }
	printf("%s\n", __FUNCTION__);
}

#if USE_LV_WATCH_WS_CONTACT_FACE != 0

int img_rgb565_save_to_png(const char *path, uint16_t *img_data, uint16_t w, uint16_t h)
{
	png_byte **png_row_pointers = NULL;
	png_byte *row;
	const png_uint_16 *p;
	png_byte red, green, blue;
	png_structp png_ptr = NULL;
	png_infop info_ptr = NULL;
	png_FILE_p fp = NULL;
	size_t x, y;
	unsigned int width = w & ~1;
	unsigned int height = h & ~1;
	unsigned int bytes_per_pixel = 3; /* RGB format */
	int err = 0;

	/* Initialize the write struct. */
	png_ptr = png_create_write_struct(PNG_LIBPNG_VER_STRING,
					  NULL, NULL, NULL);
	if (png_ptr == NULL) {
		err = 12;
		goto out;
	}

	/* Initialize the info struct. */
	info_ptr = png_create_info_struct(png_ptr);
	if (info_ptr == NULL) {
		err = 12;
		goto out;
	}

	/* Set up error handling. */
	if (setjmp(png_jmpbuf(png_ptr))) {
		err = 12;
		goto out;
	}

	/* Set image attributes. */
	png_set_IHDR(png_ptr,
		     info_ptr,
		     width,
		     height,
		     8,
		     PNG_COLOR_TYPE_RGB,
		     PNG_INTERLACE_NONE,
		     PNG_COMPRESSION_TYPE_DEFAULT,
		     PNG_FILTER_TYPE_DEFAULT);


	/* Initialize rows of PNG
	 *    bytes_per_row = width * bytes_per_pixel;
	 */
	png_row_pointers = png_malloc(png_ptr,
				      height * sizeof(png_byte *));

	for (y = 0; y < height; ++y) {
		png_row_pointers[y] =
			(png_byte *) png_malloc(png_ptr,
						width * sizeof(uint8_t) *
						bytes_per_pixel);
	}
    

	p = img_data;
	for (y = 0; y < height; ++y) {

		row = png_row_pointers[y];

		for (x = 0; x < width; ++x) {

			red   = ((*p)>>11) & 0x1f;
			green = ((*p)>>5) & 0x3f;
			blue  =(*p) & 0x1f;

			*row++ = red<<3;
			*row++ = green<<2;
			*row++ = blue<<3;

			++p;
		}
	}
	/* Write the image data. */
	fp = fs_fopen(path, "wb");
    
	if (fp == NULL) {
		err = 13;
		goto out;
	}

	png_init_io(png_ptr, fp);
	png_set_rows(png_ptr, info_ptr, png_row_pointers);
	png_write_png(png_ptr, info_ptr, PNG_TRANSFORM_IDENTITY, NULL);


 out:
	/* Finish writing. */
	for (y = 0; y < height; y++) {
		png_free(png_ptr, png_row_pointers[y]);
	}
	png_free(png_ptr, png_row_pointers);

	png_destroy_write_struct(&png_ptr, &info_ptr);
	if (fp){
		fs_fclose(fp);
	}

	return 0;
}


uint8_t *jpeg_memory_resize_png(char* pre, char* id, int w, int h, uint8_t* ptr, int size)
{
    lv_img_dsc_t preview_src;
    memset(&preview_src, 0, sizeof(lv_img_dsc_t));
    preview_src.header.always_zero = 0;
    preview_src.header.cf = LV_IMG_CF_RAW;
    preview_src.header.reserved = 1;
    preview_src.header.w = w;
    preview_src.header.h = h;
    preview_src.data_size = size;
    preview_src.data = (const uint8_t *)ptr;//"C:/test.jpg";
    WS_PRINTF("%s():%d ptr=%s", __func__, __LINE__, ptr);
    lv_img_decoder_dsc_t dec_dsc;
    lv_res_t open_res = lv_img_decoder_open(&dec_dsc, (const void*)&preview_src, LV_COLOR_BLACK);

    if(open_res == LV_RES_OK) {
        uint8_t *image_ptr;
        image_ptr = lv_mem_alloc(strlen(pre)+strlen(id)+10);
        sprintf(image_ptr, "C:/%s_%s.png", pre, id);
        img_rgb565_save_to_png(image_ptr+3, dec_dsc.img_data, w, h);
        lv_img_decoder_close(&dec_dsc);
        return image_ptr;
    }

    return NULL;

}

#endif 

bool CWathcService_CheckNowTime(char *Time, int NowTime)
{
	if(Time == NULL)
	{
		return false;
	}
	
	char Index = 0;
	 char StartTime[5] = {0};
	 char EndTime[5] = {0};
	 int StartT,EndT;
	 memcpy(StartTime, Time, 4);
	 memcpy(EndTime, Time + 4, 4);
	 StartT = atoi(StartTime);
	 EndT = atoi(EndTime);
	 WS_PRINTF("%s() Time %s StartT is %d end is %d NowTime is %d", __FUNCTION__,Time, StartT, EndT, NowTime);
	 if((NowTime < StartT) || (NowTime > EndT))
	 {
	 	return false;
	 }
	 return true;
}

void CWatchService_Start_fota_task()
{
	if(call_params.state != MMI_MODEM_CALL_STATE_NULL)
		return;
	
	if(Hal_Battery_Get_Status() < 50){
		WS_PRINTF("CWatchService_StartUpData() battery <50%");
		return ;
	}
	//ke_update_fota_status();
	watch_set_suspend_enable(true, ACT_ID_UPGRADE, 0);
}
void CWatchService_Check_fota_task(uint32_t pUser)
{
	if(sfotaCheckTimer != NULL)
		uos_timer_stop(sfotaCheckTimer);
	//if(ke_fota_is_url_valid() == 1)
	{
		MMI_ModemAdp_Rpc_Req(CWatchService_Start_fota_task, 0, 0, 0);
		return;
	}
	uos_timer_start(sfotaCheckTimer, 5*TICKES_IN_SECOND, 0, CWatchService_Check_fota_task, 0);
}

void CWatchService_StartUpData(void)
{
	if(call_params.state != MMI_MODEM_CALL_STATE_NULL)
		return;
	
	if(Hal_Battery_Get_Status() < 50){
		WS_PRINTF("CWatchService_StartUpData() battery <50%");
		return ;
	}

	if((lv_watch_get_activity_obj(ACT_ID_SCHOOL_SPORT_START_MSG))
		||(lv_watch_get_activity_obj(ACT_ID_SCHOOL_SPORT_COUNT_DOWN_MSG))
		||(lv_watch_get_activity_obj(ACT_ID_SMART_JUMPING_RUNING))
		||(lv_watch_get_activity_obj(ACT_ID_SMART_JUMPING))
		||(lv_watch_get_activity_obj(ACT_ID_SMART_JUMPING_NOTICE))
		||(lv_watch_get_activity_obj(ACT_ID_SMART_JUMPING_COUNTDOWN))
		||(lv_watch_get_activity_obj(ACT_ID_SMART_JUMPING_STOP))
		||(lv_watch_get_activity_obj(ACT_ID_SMART_JUMPING_FINISH))
		||(lv_watch_get_activity_obj(ACT_ID_SCHOOL_SPORT_MSG))
		||(lv_watch_get_activity_obj(ACT_ID_SCHOOL_SPORT_OVER_MSG))
		||(lv_watch_get_activity_obj(ACT_ID_PHONE)))
	{
	
		WS_PRINTF("CWatchService_StartUpData() sport ondoing");
        return;
	}	
#if USE_LV_DM != 0
	//clear dm regist success time
	UI_NV_Reset_Default(NV_SECTION_UI_DM);
	UI_NV_Reset_Default(NV_SECTION_DM_REG_CTCC_SET);
#endif
	WS_PRINTF("CWatchService_StartUpData() start updata");
	//update upgrade flag
	ke_fota_set_start_flag(2,NULL);
	fota_app_auto_check_version();
	#if USE_LV_WATCH_MQTT_COMMON != 0
	disconnect_AliYun_MQTT();
	#endif
}

void judge_2G_reboot()
{	
	if(used_link_success_flag == 1)
	{
		reboot_no_link_flag++;


		if(reboot_no_link_flag > 15)   // 重连15-30分钟连不上，重启
		{
			reboot_no_link_flag = 0;
			if((lv_watch_get_activity_obj(ACT_ID_SCHOOL_SPORT_START_MSG))||(lv_watch_get_activity_obj(ACT_ID_SCHOOL_SPORT_COUNT_DOWN_MSG)))
			{
			    WS_PRINTF("judge_2G_reboot reboot_no_link_flag = %d! sport_restart_create_task", reboot_no_link_flag);
				lv_task_t * task = lv_task_create(sport_restart_create_task, 50, LV_TASK_PRIO_MID, NULL);
			    lv_task_once(task);
			}
			else
			{
				WS_PRINTF("judge_2G_reboot reboot_no_link_flag = %d! Reboot !!!!!!", reboot_no_link_flag);
				ke_fota_set_start_flag(2, NULL);
				WatchUtility_PassiveReboot(NULL);
			}
		}
	}
}
void set_last_link_success()
{
	used_link_success_flag = 1;
	reboot_no_link_flag = 0;
}
bool WatchUtility_is_night_mute_time(void)
{
	hal_rtc_t rtc_curr; 		   // current rtc time

	Hal_Rtc_Gettime(&rtc_curr);
	WS_PRINTF("#########%s()time %d:%d",__FUNCTION__, rtc_curr.tm_hour, rtc_curr.tm_min);
	
	if((rtc_curr.tm_hour >= 21|| rtc_curr.tm_hour < 6)) 
		return true;

	return false;
}

void dm_start_send_msg_active()
{
#if USE_LV_WATCH_CHINA_MOBILE_DM != 0
    printf("dm_start_send_msg_active 11");
    if(ke_GetAutoRegisterState() != 1)
	{
	    WS_PRINTF("AutoRegister is close ");
	    return;
	}
	static uint8_t dm_flag = 0;

	//if sim card is not china mobile,dont active dm process
	if(Get_Sim_Operator_name(0) == 1)
	{
	    WS_PRINTF("dm_start_send_msg_active 22");
#if CMCC_AUTO_REGISTER_SERVER != 1 && CTCC_AUTO_REGISTER_SERVER != 1
		// extern void dm_send_msg_active();
#endif
		//start china mobile dm 
		if(dm_flag == 0)
		{
#if CMCC_AUTO_REGISTER_SERVER != 1 && CTCC_AUTO_REGISTER_SERVER != 1
			// dm_send_msg_active();
#endif
			dm_flag++;
		}
	}
#endif	
}

static uint8_t g_poweroff_status = 0;

void WatchUtility_Set_Poweroff_status(uint8_t sflag)
{
	g_poweroff_status = sflag;
}

uint8_t WatchUtility_Get_Poweroff_status(void)
{
	return g_poweroff_status;
}

#if USE_LV_WATCH_WS_KAER != 0
	 
void myNVMgr_SetFamilyNoSer(uint8_t *period)
{
    if(period)
    {
		uint16_t   len = 3;
	
        UI_NV_Write_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mFamilyNoSer), len, (uint8_t*)period);
    }
}

void myNVMgr_GetFamilyNoSer(uint8_t *period)
{
	uint16_t   len = 3; 		
    UI_NV_Read_Req(NV_SECTION_UI_WATCH_SERVICE, NV_OFFSETOF(nv_watch_watch_service_t, mFamilyNoSer), len, (uint8_t*)period);
}

uint8_t *WatchUtility_GetFamilyNoSer(void)
{
//	WS_PRINTF("WatchUtility_GetWsSwitch-- batteryLow:%d,allowClose:%d,openCloseAlert:%d", g_nvSwitch.batteryLow, g_nvSwitch.allowClose, g_nvSwitch.openCloseAlert);

    return gFamilyNoSer;
}

#endif

void tts_platsms_alert()
{
	Hal_NFFS_File_Play_Onetime(AUDIO_DIDI, query_current_volume(), NULL, NULL);
}

#if USE_LV_DM != 0
void ctcc_autogister_handle()
{
	static int scnts = 0;

    if(ke_GetAutoRegisterState() != 1)
	{
	    WS_PRINTF("AutoRegister is close ");
	    return;
	}
	
	if(Get_Sim_Operator_name(0) == 2)
	{
		;//teltcom
	}
	else
	{
		return;
	}
	scnts++;
	if(scnts >= 4)//10)
	{
	//5min
		scnts = 0;	
		uint32_t svalue = 0;
		nv_watch_autoregist_ctcc_t  ke_datetimes;
		uint32_t snewvalue = PMIC_RTC_GetTime_Count(0);
		UI_NV_Read_Req(NV_SECTION_DATETIME_INFO, 0, sizeof(nv_watch_autoregist_ctcc_t), (uint8_t*)&ke_datetimes);
		svalue = ke_datetimes.ws_date_times;
		WS_PRINTF("+++++++++++ctcc_autogister_handlel new = %d,old=%d,chars=%d",snewvalue,svalue,(snewvalue -svalue));
		if(svalue <= 0)
		{
			ke_datetimes.ws_date_times = snewvalue;
			UI_NV_Write_Req(NV_SECTION_DATETIME_INFO, 0, sizeof(nv_watch_autoregist_ctcc_t), (uint8_t*)&ke_datetimes);	
		}
		else
		{
			if(snewvalue > svalue)
			{
				if((snewvalue -svalue) >= (30*24*3600))
					dm_re_start();
			}
		}
	}
}

void ctcc_autoregister_save_cur_time()
{
	uint32_t snewvalue = PMIC_RTC_GetTime_Count(0);
	nv_watch_autoregist_ctcc_t  ke_datetimes;
	nv_watch_autoregist_ctcc_t  ke_datetimes11;
	WS_PRINTF("------------ctcc_autoregister_save_cur_time = %d ",snewvalue );

	ke_datetimes.ws_date_times = snewvalue;
	UI_NV_Write_Req(NV_SECTION_DATETIME_INFO, 0, sizeof(nv_watch_autoregist_ctcc_t), (uint8_t*)&ke_datetimes);	
}
#endif

#if defined(__CMCC_COMPUTILITY_SUPPORT__)
void cmcc_cloud_save_cur_time()
{
	nv_watch_cmcc_sli_t sli_buf;
	uint32_t snewvalue = PMIC_RTC_GetTime_Count(0);
	memset(sli_buf.productId,0,sizeof(sli_buf.productId));
	memset(sli_buf.productKey,0,sizeof(sli_buf.productKey));
	sli_buf.dates_times = 0;
	uint32_t slen = UI_NV_Read_Req(NV_SECTION_CMCC_SLI_INFO, 0, sizeof(nv_watch_cmcc_sli_t), (uint8_t *)&sli_buf);
	sli_buf.dates_times = snewvalue;
	slen= UI_NV_Write_Req(NV_SECTION_CMCC_SLI_INFO, 0, sizeof(nv_watch_cmcc_sli_t), (uint8_t *)&sli_buf);	
	WS_PRINTF("------------cmcc_cloud_save_cur_time rett= %d ",slen );
}

void cmcc_cloud_cyc_report_handle()
{
	static int scntsr = 0;

#if 1
	if(Get_Sim_Operator_name(0) == 1)
	{
		;//cmcc
	}
	else
	{
		scntsr = 0;
		return;
	}
#endif
	scntsr++;
	if(scntsr >= 20)
	{
	// min
		scntsr = 0;	
		uint32_t svalue = 0;
		nv_watch_cmcc_sli_t sli_buf;
		memset(sli_buf.productId,0,sizeof(sli_buf.productId));
		memset(sli_buf.productKey,0,sizeof(sli_buf.productKey));
		sli_buf.dates_times = 0;
		uint32_t snewvalue = PMIC_RTC_GetTime_Count(0);
		uint32_t slen = UI_NV_Read_Req(NV_SECTION_CMCC_SLI_INFO, 0, sizeof(nv_watch_cmcc_sli_t), (uint8_t *)&sli_buf);

		svalue = (uint32_t)sli_buf.dates_times;
		WS_PRINTF("+++++++++++cmcc_cloud_cyc_report_handle new = %d,old=%d ",snewvalue,svalue );
		if(svalue <= 0)
		{
			sli_buf.dates_times = snewvalue;
			UI_NV_Write_Req(NV_SECTION_CMCC_SLI_INFO, 0, sizeof(nv_watch_cmcc_sli_t), (uint8_t *)&sli_buf);	
		}
		else
		{
			if(snewvalue > svalue)
			{
				WS_PRINTF("-------cmcc_cloud_cyc_report_handle %d ",(snewvalue -svalue) );
				if((snewvalue -svalue) >= (20*3600))
				{
					dm_cmcc_cyc_report(0);
					//save time for next report
					sli_buf.dates_times = snewvalue;
					UI_NV_Write_Req(NV_SECTION_CMCC_SLI_INFO, 0, sizeof(nv_watch_cmcc_sli_t), (uint8_t *)&sli_buf);	
				}
				else
				{
					dm_cmcc_cyc_report(1);
				}	
			}
		}
	}
}
#endif

#if USE_LV_WATCH_REPORT_NFC_INFO != 0
void myNVWatch_Get_24g_info(char *sdata)
{
	nv_watch_nfc_t cur_24gstrs;
	if(sdata ==NULL)
	{
		memset(cur_24gstrs.ws_24g,0,sizeof(cur_24gstrs.ws_24g));
		UI_NV_Read_Req(NV_SECTION_NFC_INFO, 0,sizeof(nv_watch_nfc_t), (uint8_t*)&cur_24gstrs);
		memset(g_24G_cardid,0,sizeof(g_24G_cardid));
		memcpy(g_24G_cardid,cur_24gstrs.ws_24g,strlen(cur_24gstrs.ws_24g));
	}
	else
	{
		strcpy(sdata,g_24G_cardid);
	}
	WS_PRINTF("myNVWatch_Get_24g_info,,,DAT=%s,%s",sdata,g_24G_cardid);	
}

uint32_t myNVWatch_Set_24g_info(char *iccpds)
{
	nv_watch_nfc_t cur_24gstrs;
	memset(cur_24gstrs.ws_24g,0,sizeof(cur_24gstrs.ws_24g));
	strcpy(cur_24gstrs.ws_24g,iccpds);
	uint32_t len = UI_NV_Write_Req(NV_SECTION_NFC_INFO, 0,sizeof(nv_watch_nfc_t), (uint8_t*)&cur_24gstrs);
	return len;
}

void WatchUtility_Save_24g_info(char *sptrs)
{
	char Datbuf[20]={0};
	memset(Datbuf,0,sizeof(Datbuf));
	memset(g_24G_cardid,0,sizeof(g_24G_cardid));
	if(sptrs != NULL)
	{
		strcpy(Datbuf,sptrs);
		if((Datbuf[0] >=0x30)&&(Datbuf[0] <=0x39))
		{
			if(strlen(Datbuf) <= 20)
			{
				if(strlen(Datbuf) >= 10)
				{
					memcpy(g_24G_cardid,Datbuf,10);
				}
				else
				{
					uint8_t slens = 10-strlen(Datbuf);
					memcpy(g_24G_cardid,"0000000000",slens);
					memcpy(&g_24G_cardid[slens],Datbuf,strlen(Datbuf));
				}
			}
			else
				memcpy(g_24G_cardid,Datbuf,20);
			WS_PRINTF("[%s:%s:%d]:%s\n",__FILE__,__FUNCTION__,__LINE__,g_24G_cardid);
			myNVWatch_Set_24g_info(g_24G_cardid);
		}

	}
	
}
#endif
#if USE_LV_WATCH_SAME_TIME_OPEN_STOP != 0
void CWatchService_set_sametime_open(char *openswitch)
{
	WS_PRINTF("CWatchService_set_sametime_open=%s,%d\n",openswitch,atoi(openswitch));
	uint8_t svalue = 0;
	svalue = atoi(openswitch);

	ke_Set_Fix_loc(svalue);
	UI_NV_Write_Req(NV_SECTION_UI_KAERSETTINGS, NV_OFFSETOF(nv_watch_kaersettings_t, FixedLoc), sizeof(uint8_t),(uint8_t*)&svalue);
}
#endif

void CWatchService_Lcd_8s_sleep(uint32_t pUser)
{
	watch_set_suspend_enable(true,0,0);
	WS_PRINTF("\n ->>>>>>>>>-watch_lcd_8s_sleep =%d\n",watch_get_lcd_status());
}

void CWatchService_Start_lcd_on_8s_timer(uint8_t stimer)
{
	WS_PRINTF("\n Start_lcd_on_8s_timer________=%d___",link_lcd_8s_timer)	 ;
	if(link_lcd_8s_timer == NULL)
	{
		uos_timer_create(&link_lcd_8s_timer);
	}
	else
		uos_timer_stop(link_lcd_8s_timer);
	uos_timer_start(link_lcd_8s_timer, stimer*TICKES_IN_SECOND, 0, CWatchService_Lcd_8s_sleep, 0);
}

#if USE_LV_WATCH_LOCK_ICCID != 0

void myNVWatch_Get_Cur_ICCID()
{
	memset(g_cur_iccidstrs.ws_iccids,0,sizeof(g_cur_iccidstrs.ws_iccids));
	UI_NV_Read_Req(NV_SECTION_ICCID_INFO, 0,sizeof(nv_watch_iccid_t), (uint8_t*)&g_cur_iccidstrs);
	//cplog_printf("\n !!!!!!!!!!!!!myNVWatch_Get_Cur_ICCID =%s,%d\n",g_cur_iccidstrs.ws_iccids,strlen(g_cur_iccidstrs.ws_iccids));
}

void myNVWatch_Set_Cur_ICCID(char *iccpds)
{
	if(strlen(g_cur_iccidstrs.ws_iccids)>=15)
		return;
	cplog_printf("\n myNVWatch_Set_Cur_ICCID+++++=%s\n ",iccpds);
	memset(g_cur_iccidstrs.ws_iccids,0,sizeof(g_cur_iccidstrs.ws_iccids));
	strcpy(g_cur_iccidstrs.ws_iccids,iccpds);
	UI_NV_Write_Req(NV_SECTION_ICCID_INFO, 0,sizeof(nv_watch_iccid_t), (uint8_t*)&g_cur_iccidstrs);
}

void myNVWatch_clear_Cur_ICCID()
{
	memset(g_cur_iccidstrs.ws_iccids,0,sizeof(g_cur_iccidstrs.ws_iccids));
	UI_NV_Write_Req(NV_SECTION_ICCID_INFO, 0,sizeof(nv_watch_iccid_t), (uint8_t*)&g_cur_iccidstrs);
}
uint8_t myNVwatch_is_same_iccid(char *iccpds)
{
	//cplog_printf("\n !!!!!!!!!!!!!myNVwatch_is_same_iccid =%s,%d,%s\n",g_cur_iccidstrs.ws_iccids,strlen(g_cur_iccidstrs.ws_iccids),iccpds);
	if(strlen(g_cur_iccidstrs.ws_iccids) == 0)
		return 1;
	if((strlen(g_cur_iccidstrs.ws_iccids) >=15 )&&(memcmp(g_cur_iccidstrs.ws_iccids,iccpds,strlen(g_cur_iccidstrs.ws_iccids)) == 0))
		return 1;
	else
		return 0;
}
#endif

static void *g_login_retryTimer=NULL; 

void CWathcService_Network_Reconnect()
{
	ws_client *pme = MMI_ModemAdp_WS_GetClient();
	WS_PRINTF("\n CWathcService_Network_Reconnect----");
	MMI_ModemAdp_WS_Stop_Ext();
	MMI_ModemAdp_WS_Try_To_Start(pme);
}

void CWathcService_Login_Retry_Timer_Stop()
{
	if(g_login_retryTimer!=NULL)
	{
		uos_timer_stop(g_login_retryTimer);
		g_login_retryTimer = NULL;
	}
	WS_PRINTF("\n CWathcService_Login_Retry_Timer_Stop----");
}

void CWathcService_Login_Retry_Timer_Update()
{
	CWathcService_Login_Retry_Timer_Stop();
	MMI_ModemAdp_Rpc_Req(CWathcService_Network_Reconnect, 0, 0, 0);
}
void CWathcService_Login_Retry_Timer_Create()
{
	uint32_t tickes = rand()%(10*60)+(10*60);
	
	if(g_login_retryTimer == NULL)
	{
		uos_timer_create(&g_login_retryTimer);
	}
	else
		uos_timer_stop(g_login_retryTimer);
	WS_PRINTF("\n CWathcService_Login_Retry_Timer_Create stime ticks= %d", tickes);
	uos_timer_start(g_login_retryTimer, tickes*TICKES_IN_SECOND, 0, CWathcService_Login_Retry_Timer_Update, 0);
}

#if USE_LV_WATCH_LOC_FIX_INTERVAL_TIME != 0
void CWatchService_LocalPosTimerCB(uint32_t pUser)
{
   ws_client * pme = (ws_client*)pUser;    
   ws_evt_msg_t  *cmd_info = (ws_evt_msg_t*)malloc(sizeof(ws_evt_msg_t));  /*命令数据结构*/
   memset((void *)cmd_info, 0x00, sizeof(ws_evt_msg_t));
   cmd_info->client = pme;
   cmd_info->evt = CMD_LOCAL_LOCAL_POS_TIMER_EXPIRE;
   CWatchService_NofityClient(pme, cmd_info);
   free(cmd_info);
}

uint8_t CWatchService_Get_First_Peroidloct()
{
	return first_period_loc_flag;
}

void CWatchService_Set_First_Peroidloct(uint8_t slfag)
{
	first_period_loc_flag = slfag;
}
#endif

#if USE_LV_WATCH_GET_LAST_SAVE_LATLON != 0
//开机收到平台回应后调用
void CWatchService_Get_Last_LatLon()
{
	static uint8_t sfirsts = 0;
	if(sfirsts == 0)
	{
		sfirsts = 1;
		g_last_latlon.lats = g_last_latlon.lons = 0.00;
		UI_NV_Read_Req(NV_SECTION_LAST_SAVE_LATLON_INFO, 0, sizeof(nv_watch_last_latlon_t), (uint8_t *)&g_last_latlon);
		if((g_last_latlon.lats != 0) && (g_last_latlon.lons!= 0))
		{
			GpsSetLatLon(&g_last_latlon.lons, &g_last_latlon.lats);
		}
	}
	WS_PRINTF("---- CWatchService_Get_Last_LatLon lat = %.6f,%.6f ",g_last_latlon.lats, g_last_latlon.lons);
}
//为了加快开机gps定位 ，关机时判断有经纬度保存下
void CWatchService_Save_Last_LatLon()
{
	if((g_last_latlon.lats != 0) && (g_last_latlon.lons != 0))
	{
		UI_NV_Write_Req(NV_SECTION_LAST_SAVE_LATLON_INFO, 0, sizeof(nv_watch_last_latlon_t), (uint8_t *)&g_last_latlon);
	}
}

void CWatchService_Set_latlon(float *lats,float *lons)
{
	g_last_latlon.lats=0.00;
    g_last_latlon.lons=0.00;

	g_last_latlon.lats = *lats;
	g_last_latlon.lons = *lons;
	WS_PRINTF("_______CWatchService_Set_latlon LAT=%.6f,=%.6f",g_last_latlon.lats,g_last_latlon.lons);
}
#endif

int CWathcService_GetPhoneNum_Callback(char *data,int result)
{
	WS_PRINTF("CWathcService_GetPhoneNum_Callback==%s",data);

	//*IMSLOCURI: "sip","sip:+<EMAIL>"
	//或者*IMSLOCURI: "tel","tel:+8618863138552"

	char *ptr=NULL,*tmp=NULL;
	char buf[20]={0};

	ptr=strstr(data,"sip:");
	if(ptr)
	{
	    ptr=ptr+4;
		tmp=strstr(ptr,"@");
		if(tmp)
	        strncpy(buf, ptr, tmp-ptr);
	}
	else
	{
	    ptr=strstr(data,"tel:");
		if(ptr)
		{
		    ptr=ptr+4;
			tmp=strstr(ptr,"\"");
			if(tmp)
		        strncpy(buf, ptr, tmp-ptr);
		}
	}

	if(strlen(buf>5)) 
	{
	    memset(g_PhoneNum,0,sizeof(g_PhoneNum));
		if(!strncmp(buf,"+86",3))
		    strncpy(g_PhoneNum, buf+3, 11);
		else
			strncpy(g_PhoneNum, buf, 14);
    }

	WS_PRINTF("CWathcService_GetPhoneNum_Callback g_PhoneNum==%s",g_PhoneNum);
}

char *CWathcService_Get_OwnNumber()
{
	return g_PhoneNum;
}

#if USE_LV_WATCH_VOICE_MSG_YNYD != 0
#define RES_MENBER_LEN 64
char* MMI_ModemAdp_WS_HomeSchool_Login_Failmsg(void)
{
 	ws_client * pme=MMI_ModemAdp_WS_GetClient();

    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    return userData->login_failMsg;
}
bool MMI_ModemAdp_WS_Is_HomeSchool_Has_LoginInfo(void)
{
	ws_client * pme=MMI_ModemAdp_WS_GetClient();

    ws_kaer_t *userData = (ws_kaer_t*)pme->adapter->data;
    if(strlen(userData->userID)&&strlen(userData->jcom_token))
    {
        return true;
    }
	
    return false;
}

int MMI_ModemAdp_WS_Yn_Chat_Close(char * sessId)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme))
	{
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, sessId, (double)CMD_REPORT_SESSION_ACTION_CLOSE);
		return 1;
    }
	else
	{
	 	return 0;
	}
}

int MMI_ModemAdp_WS_Yn_Chat_Get_SessionList(void)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, NULL, (double)CMD_GET_SESSION_LIST);
		return 1;
    }else{
	 	return 0;
	}
}

int MMI_ModemAdp_WS_Yn_del_session(char * sessId)
{
	WS_PRINTF("%s(sessId:%s)\n", __FUNCTION__,sessId);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, sessId, CMD_REPORT_SESSION_ACTION_DEL_SESSION);
		return 1;
    }else{
	 	return 0;
	}
}


int MMI_ModemAdp_WS_Yn_Report_Message_Read(char * sessId)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, sessId, CMD_REPORT_SESSION_ACTION_MESSAGE_READ);
		return 1;
    }else{
	 	return 0;
	}
}

int MMI_ModemAdp_WS_Yn_Report_CmdClick(char * info)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, info, CMD_REPORT_SESSION_ACTION_CMD_CLICK);
		return 1;
    }else{
	 	return 0;
	}
}


int MMI_ModemAdp_WS_Yn_Friend_Find_By_PhbNumber(char * number)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, number, CMD_GET_FRIENDS_FIND);
		return 1;
    }else{
	 	return 0;
	}
}

int MMI_ModemAdp_WS_Yn_Chat_Open(char * sessId)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, sessId, (double)CMD_REPORT_SESSION_ACTION_OPEN);
		return 1;
    }else{
	 	return 0;
	}
}



int MMI_ModemAdp_WS_Yn_del_contact(char * userId)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, userId, CMD_REPORT_SESSION_ACTION_DEL_CONTACT);
		return 1;
    }else{
	 	return 0;
	}
}

int MMI_ModemAdp_WS_Yn_Chat_Get_SessionContent(char * session_id)
{
	WS_PRINTF("%s()\n", __FUNCTION__);
    ws_client* pme = MMI_ModemAdp_WS_GetClient();
	if(CWatchService_IsOnLine(pme)){
        CWatchService_CmdProcess(pme, CMD_S2C_JCOM_HOME_SCHOOL, session_id, (double)CMD_GET_SESSION_CONTENT);
		return 1;
    }else{
	 	return 0;
	}
}

int str_get_params_ex(char *input, int start, int end, char break_point, char *split_symbols,char res[][RES_MENBER_LEN])
{
    int input_idx = start;
    int symbol_idx = 0;

    while(split_symbols)
    {
        int tmp_res_idx = 0;
        //WS_PRINTF("input[%d]:%c\n", input_idx, input[input_idx]);
        //WS_PRINTF("symbol[%d]:%c\n", symbol_idx, split_symbols[symbol_idx]);
        if(input[input_idx] == break_point)
        {
            if(input[input_idx] != 0)
                input_idx++;
            break;
        }


        while(input_idx <= end)
	    {
	        if(strchr(split_symbols,input[input_idx]))
	    	{
				//WS_PRINTF("input[%d]:%c ,break \n", input_idx, input[input_idx]);
	            break;
	    	}
			else if(input[input_idx] == break_point)
			{
	            if(input[input_idx] != 0)
	                input_idx++;
				
				return input_idx;
			}
	        else
	    	{
	            res[symbol_idx][tmp_res_idx++] = input[input_idx++];
	    	}
	    }

        input_idx++;
        symbol_idx++;
    }

    return input_idx;
}

#endif

#if USE_LV_BLUETOOTH != 0
void CWatchService_Ble_Close_Preper_Time_Exit()
{
    uint32_t length = sizeof(nv_watch_bluetooth_t);
    nv_watch_bluetooth_t * nvm = (nv_watch_bluetooth_t *)lv_mem_alloc(length);
    if(length != UI_NV_Read_Req(NV_SECTION_UI_BLUETOOTH, 0, length, (uint8_t *)nvm)) 
	{
        printf("read bluetooth from nvm error in %s\n",__FUNCTION__);
        lv_mem_free(nvm);
        return NULL;
    }
	if(nvm->on_off != 0) 
	{	
		bt_close_device();
		bt_list_destroy();
		bluetooth_nvm_save_switch_state(false);  
	}
	lv_mem_free(nvm);
}
#endif

#endif // USE_LV_WATCH_MODEM_ADAPTOR
