#ifndef UOS_H
#define UOS_H

#include "stdlib.h"
#include "stdint.h"
#include "stdbool.h"

#define MAX_FLAG_NUM            64
#define INVALID_FLAG_ID         MAX_FLAG_NUM

/* support MAX 64 message queue */
#define UOS_NB_MAX_MBX_ENV      64
#define INVALID_MSGQ_ID         UOS_NB_MAX_MBX_ENV

#define UOS_SUSPEND             0xFFFFFFFF
#define UOS_NO_SUSPEND          0

#define UOS_FLAG_AND            5
#define UOS_FLAG_AND_CLEAR      6
#define UOS_FLAG_OR             7
#define UOS_FLAG_OR_CLEAR       8

/* uos return err code, identical with OS_XXX in osa.h */
enum
{
    UOS_SUCCESS = 0,        /* 0x0 -no errors                                        */
    UOS_FAIL,               /* 0x1 -operation failed code                            */
    UOS_TIMEOUT,            /* 0x2 -Timed out waiting for a resource                 */
    UOS_NO_RESOURCES,       /* 0x3 -Internal OS resources expired                    */
    UOS_INVALID_POINTER,    /* 0x4 -0 or out of range pointer value                  */
    UOS_INVALID_REF,        /* 0x5 -invalid reference                                */
    UOS_INVALID_DELETE,     /* 0x6 -deleting an unterminated task                    */
    UOS_INVALID_PTR,        /* 0x7 -invalid memory pointer                           */
    UOS_INVALID_MEMORY,     /* 0x8 -invalid memory pointer                           */
    UOS_INVALID_SIZE,       /* 0x9 -out of range size argument                       */
    UOS_INVALID_MODE,       /* 0xA, 10 -invalid mode                                 */
    UOS_INVALID_PRIORITY,   /* 0xB, 11 -out of range task priority                   */
    UOS_UNAVAILABLE,        /* 0xC, 12 -Service requested was unavailable or in use  */
    UOS_POOL_EMPTY,         /* 0xD, 13 -no resources in resource pool                */
    UOS_QUEUE_FULL,         /* 0xE, 14 -attempt to send to full messaging queue      */
    UOS_QUEUE_EMPTY,        /* 0xF, 15 -no messages on the queue                     */
    UOS_NO_MEMORY,          /* 0x10, 16 -no memory left                              */
    UOS_DELETED,            /* 0x11, 17 -service was deleted                         */
    UOS_SEM_DELETED,        /* 0x12, 18 -semaphore was deleted                       */
    UOS_MUTEX_DELETED,      /* 0x13, 19 -mutex was deleted                           */
    UOS_MSGQ_DELETED,       /* 0x14, 20 -msg Q was deleted                           */
    UOS_MBOX_DELETED,       /* 0x15, 21 -mailbox Q was deleted                       */
    UOS_FLAG_DELETED,       /* 0x16, 22 -flag was deleted                            */
    UOS_INVALID_VECTOR,     /* 0x17, 23 -interrupt vector is invalid                 */
    UOS_NO_TASKS,           /* 0x18, 24 -exceeded max # of tasks in the system       */
    UOS_NO_FLAGS,           /* 0x19, 25 -exceeded max # of flags in the system       */
    UOS_NO_SEMAPHORES,      /* 0x1A, 26 -exceeded max # of semaphores in the system  */
    UOS_NO_MUTEXES,         /* 0x1B, 27 -exceeded max # of mutexes in the system     */
    UOS_NO_QUEUES,          /* 0x1C, 28 -exceeded max # of msg queues in the system  */
    UOS_NO_MBOXES,          /* 0x1D, 29 -exceeded max # of mbox queues in the system */
    UOS_NO_TIMERS,          /* 0x1E, 30 -exceeded max # of timers in the system      */
    UOS_NO_MEM_POOLS,       /* 0x1F, 31 -exceeded max # of mem pools in the system   */
    UOS_NO_INTERRUPTS,      /* 0x20, 32 -exceeded max # of isr's in the system       */
    UOS_FLAG_NOT_PRESENT,   /* 0x21, 33 -requested flag combination not present      */
    UOS_UNSUPPORTED,        /* 0x22, 34 -service is not supported by the OS          */
    UOS_NO_MEM_CELLS,       /* 0x23, 35 -no global memory cells                      */
    UOS_DUPLICATE_NAME,     /* 0x24, 36 -duplicate global memory cell name           */
    UOS_INVALID_PARM        /* 0x25, 37 -invalid parameter                           */
};


#ifdef __cplusplus
extern "C" {
#endif

void uos_flag_init(void);
uint8_t uos_create_flag (void);
void  uos_delete_flag(uint8_t id);
int uos_set_flag(uint8_t id, uint32_t mask, uint32_t op);
int  uos_wait_flag (uint8_t id, uint32_t mask, uint32_t op, uint32_t *flags, uint32_t timeout);


#define MAX_MUTEX_SIZE          96
#define INVALID_MUTEX_ID        MAX_MUTEX_SIZE
void  uos_mutex_init (void);
uint8_t uos_new_mutex (void);
void  uos_free_mutex (uint8_t id);
int uos_take_mutex (uint8_t id);
/* return: 0 -> mutex is free; 1 -> mutex is in use already; -1 -> operation failed */
int uos_try_take_mutex(uint8_t id);
int uos_release_mutex (uint8_t id);

typedef struct _TaskDesc
{
    void (*TaskBody)(void *);
    const char *Name;
    void *Parameter;
    uint16_t nStackSize;
    uint8_t nPriority;
} TaskDesc;

typedef void (*PTASK_ENTRY)(void * pParameter);
typedef struct _TASK_HANDLE
{
    TaskDesc sTaskDesc;
    uint8_t nTaskId;
    uint8_t nMailBoxId;
    // indicate the task's status
    uint8_t nStatus;
    uint8_t padding[4];
} TASK_HANDLE;

void uos_task_init (void);
TASK_HANDLE *uos_get_current_task_handle(void);

const char *uos_get_current_task_name (void);
TASK_HANDLE *uos_create_task(
    PTASK_ENTRY pTaskEntry,
    void *pParameter,
    uint16_t nMsgQSize,
    uint32_t nStackSize,
    uint8_t nPriority,
    const char * pTaskName
);
bool uos_suspend_task(TASK_HANDLE *pHTask);
bool uos_resume_task (TASK_HANDLE *pHTask);
void uos_sleep (uint32_t Ticks);
void uos_sleep_ms(uint32_t ms);

bool uos_delete_task(TASK_HANDLE *pHTask);

#define UOS_WAIT_FOREVER 0xFFFFFFFF
#define UOS_NO_WAIT    0x0

#define UOS_EVENT_PRI_NORMAL  0
#define UOS_EVENT_PRI_URGENT  1

#define UOS_EVENT_ILM_EXTMSG  (1<<2)

#define TASK_WITHOUT_MSGQ    0
#define TASK_MSGQ_SIZE_16    16
#define TASK_MSGQ_SIZE_32    32
#define TASK_MSGQ_SIZE_64    64
#define TASK_MSGQ_SIZE_128    128
#define TASK_MSGQ_SIZE_256    256
#define TASK_MSGQ_SIZE_512    512

#define TASK_DEFAULT_MSGQ_SIZE    TASK_MSGQ_SIZE_64

#define UOS_SEND_EVT        (1 << 0)
#define UOS_SEND_MSG        0

#define UOS_QUEUE_FIRST        (1 << 1)
#define UOS_QUEUE_LAST        0

#define UOS_SEND_ILM_EXTMSG        (1 << 2)    //OslIntMsgSendExtQueue:struct ilm_struct

typedef struct _UI_EVENT {
    uint32_t nEventId;
    uint32_t nParam1;
    uint32_t nParam2;
    uint32_t nParam3;
} UI_EVENT;

#define UOS_EVT_MBX_SIZE 4

#define OSA_TICK_FREQ_IN_MILLISEC   5   /* Tick frequency for the OS.      */
// 1 ticks = 5ms
#define MS_TO_TICKS(n) ((n) / OSA_TICK_FREQ_IN_MILLISEC ? (n) / OSA_TICK_FREQ_IN_MILLISEC : 1)
#define TASK_DEFAULT_MSGQ_SIZE    TASK_MSGQ_SIZE_64

void uos_message_queue_init (void);
uint8_t uos_new_message_queue(const char *queue_name, int message_num);
uint8_t uos_send_msg (void *Msg, uint8_t index, uint8_t MsgStatus);
void *uos_wait_msg (uint32_t *Evt, uint8_t index, uint32_t nTimeOut);
bool uos_wait_event(TASK_HANDLE *hTask, UI_EVENT* pEvent, uint32_t nTimeOut);
bool uos_send_event(TASK_HANDLE* hTask, UI_EVENT* pEvent, uint32_t nTimeOut, uint16_t nOption);
bool uos_is_event_available(TASK_HANDLE*  hTask);
uint32_t uos_get_ticks(void);
void uos_init(void);
void uos_free_msg (uint8_t Sbx);
uint32_t uos_get_msgcnt (uint8_t Sbx);
uint8_t uos_timer_create(void **pptimer);
uint8_t uos_timer_start(void *ptimer, uint32_t ticks, uint32_t repeaticks, void (*callback)(uint32_t), uint32_t param);
uint8_t uos_timer_stop(void *ptimer);
uint8_t uos_timer_delete(void *ptimer);
uint8_t uos_timer_start_ms(void * ptimer, uint32_t ms, uint32_t repeat_ms, void (*callback)(uint32_t), uint32_t param);
uint8_t uos_chgpriority_task(TASK_HANDLE *pHTask, uint8_t newpro, uint8_t *oldpro);
bool uos_in_urgent_section(void);
#ifndef DIV_ROUND_UP
#define DIV_ROUND_UP(x, y) (((x) + (y) - 1)/(y))
#endif

#ifdef __cplusplus
}
#endif

#endif
