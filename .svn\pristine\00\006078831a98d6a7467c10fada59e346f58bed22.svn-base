#------------------------------------------------------------
# (C) Copyright [2006-2008] Marvell International Ltd.
# All Rights Reserved
#------------------------------------------------------------

# ============================================================================
# File        : KEYPAD_xscale.mak
# Description :	Test make file for building the hal/KEYPAD 
#               package under the xscale environment.
#
# Notes       : This file is used to test the compilation of the 
#               KEYPAD package under the xscale environment.  
#               The ENV macro and the HOST macros require 
#               definition. This make file requires gbl_types.h,
#               gbl_config.h and gbl_trace.h to be defined in the 
#               hal/KEYPAD/test/inc directory. 
#
#               This make file generates hal_KEYPAD.lib
#               in the hal/KEYPAD/test/obj directory.
#               
# Copyright 2001, Intel Corporation, All rights reserved.
# ============================================================================

HOST = <host>
ENV  = xscale

# setup the root build directory. On win32 systems
# the vobs mount at the root of the mapped drive. 
ifeq (${HOST}, unix)
 BUILD_ROOT = /vobs
else
 BUILD_ROOT = ${CBA_ROOT}
endif

# get the environment macros and paths.
include ${BUILD_ROOT}/env/${HOST}/build/${ENV}_env.mak

# override the linker macro so the target creates 
# only the package library. ie it will not link.
LD = echo

# Target Build Paths -------------------------------------------------

TARGET_NAME    = KEYPAD_xscale
TARGET_PATH    = ${BUILD_ROOT}/hal/KEYPAD/test

# The relative path locations of source and include file directories.
LOCAL_SRC_PATH  = ../src
LOCAL_INC_PATHS = ../src ../inc

# Default target variant ---------------------------------------------
ifeq (${TARGET_VARIANT},${empty})

# local target source files, paths not required
LOCAL_SRC_FILES = 

# local target build flags for source files
# contained in this target directory
LOCAL_CFLAGS  = 
LOCAL_DFLAGS  = 

# global target build flags to be passed 
# to groups and packages.
TARGET_CFLAGS  = 
TARGET_DFLAGS  =
TARGET_LDFLAGS = 
TARGET_ARFLAGS = 

# the packages and groups to be included in the target
# syntax is <basename>/<package> and <basename>/<group>
PACKAGE_LIST = hal/KEYPAD
GROUP_LIST   = 

# package and group variants required for this target variant
# syntax is <PACKAGE>_<VARIANT> or <GROUP>_<VARIANT>.
VARIANT_LIST = 

# TRACE_LIST contains package and groups in which diagnostics/tracing
# will be compiled. Not compiling diagnostics/tracing in some modules
# will reduce code size and increase code performance.
TRACE_LIST = ${PACKAGE_LIST} ${GROUP_LIST}
TRACE_TYPE = STRING_TRACING

endif

# Variant_1 target variant -------------------------------------------
ifeq (${TARGET_VARIANT},variant_1)

# local target source files, paths not required
LOCAL_SRC_FILES = 

# local target build flags are for source files
# contained in this target directory
LOCAL_CFLAGS  = 
LOCAL_DFLAGS  = 

# global target build flags to be passed 
# to groups and packages.
TARGET_CFLAGS  = 
TARGET_DFLAGS  =
TARGET_LDFLAGS = 
TARGET_ARFLAGS = 

# the packages and groups to be included in the target
# syntax is <basename>/<package> and <basename>/<group>
PACKAGE_LIST = hal/KEYPAD
GROUP_LIST   = ^GROUP_LIST

# package and group variants required for this target variant
# syntax is <PACKAGE>_<VARIANT> or <GROUP>_<VARIANT>.
VARIANT_LIST = 

# TRACE_LIST contains package and groups in which diagnostics/tracing
# will be compiled. Not compiling diagnostics/tracing in some modules
# will reduce code size and increase code performance.
TRACE_LIST = ${PACKAGE_LIST} ${GROUP_LIST}
TRACE_TYPE = STRING_TRACING

endif

# include the standard target make file -------------------------------
include ${BUILD_ROOT}/env/${HOST}/build/target.mak







