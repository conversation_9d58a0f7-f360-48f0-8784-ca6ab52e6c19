#include "UART_HW2.h"
#include "bt_uart_api.h"
#ifndef UART_NEW_VERSION
#include "intc.h"
#include "qspi_dma.h"
#include "osa.h"
#include "bt_api.h"
#include "utilities.h"
#include "UART_HW.h"

#define MODE_X_DIV 16
bt_uart_glb_info bt_uart_info[MAX_UART_PORT];
BT_UartIsrCb BT_UartIsrCallback[MAX_UART_PORT][bt_int_max]={NULL, };
dma_callback_t BT_UartDmaCallback[MAX_UART_PORT][bt_max_dir]={NULL, };
uint32 bt_dma_mode = 0;
UINT32 bt_uart_id = 0xFF;
BOOL bt_uart_dma_preprocess = FALSE;
extern OS_HISR Modem0RxHISR;
extern OS_HISR Modem1RxHISR;

extern void* OSATaskGetCurrentRefExt(void);
extern void issue_sync_barrier(void);

extern void SetBTPmLock(unsigned char lock);
extern unsigned long INTCGetIntVirtualNum(UINT32 IRQ_NO);

void bt_ui_delay_us(unsigned int us)
{
	int cnt = 26;//0x100,0x300
	volatile int i = 0;
	while(us--) {
		i = cnt;
		while(i--);
	}
}

void bt_uart_glb_init(uint32 port_id, uint32 highspeed)
{
	uint32 data;
	switch(port_id)
	{
		case UART1_ID:
			bt_uart_info[UART1_ID].base_addr = AP_UART1_BASE;
			bt_uart_info[UART1_ID].apbc_base = APBC_UART1_CLK_RST;
			bt_uart_info[UART1_ID].irq = UART1_IRQ;
			bt_uart_info[UART1_ID].fpga = 0;
			break;
		case UART2_ID:
			bt_uart_info[UART2_ID].base_addr = AP_UART2_BASE;
			bt_uart_info[UART2_ID].apbc_base = APBC_UART2_CLK_RST;
			bt_uart_info[UART2_ID].irq = UART2_IRQ;
			bt_uart_info[UART2_ID].fpga = 0;
			break;
		case UART3_ID:
			bt_uart_info[UART3_ID].base_addr = AP_UART3_BASE;
			bt_uart_info[UART3_ID].apbc_base = APBC_UART3_CLK_RST;
			bt_uart_info[UART3_ID].irq = UART3_IRQ;
			bt_uart_info[UART3_ID].fpga = 0;
			break;
        case UART4_ID:
            bt_uart_info[UART4_ID].base_addr = AP_UART4_BASE;
            bt_uart_info[UART4_ID].apbc_base = APBC_UART4_CLK_RST;
            bt_uart_info[UART4_ID].irq = UART4_IRQ;
            bt_uart_info[UART4_ID].fpga = 0;
            break;
		default:
			break;
	}

	data = 0;
	if (highspeed)
	{
		data = read32(0xd4051024);
		data |= 0x100;
		write32(0xd4051024, data);
		data &= APBC_UART_CLK_RST_FNCLKSEL_MASK;
		data |= (APBC_UART_CLK_RST_FNCLK | APBC_UART_CLK_RST_APBCLK);
	}
	else
		data |= (APBC_UART_CLK_RST_FNCLK | APBC_UART_CLK_RST_APBCLK | 0x1 << 4);

		data |= APBC_UART_CLK_RST_RST;
		write32 (bt_uart_info[port_id].apbc_base, data);

		bt_ui_delay_us(100);

		data = read32 (bt_uart_info[port_id].apbc_base);
		data &= ~APBC_UART_CLK_RST_RST;
		write32 (bt_uart_info[port_id].apbc_base, data);

	return;
}


static void
bt_uart_base_init (uint32 port_id)
{
  volatile uint32 data = 0;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  /* cleanup fifo and interrupts */
  write32 (uart_test_base + UART_LCR, 0x0);
  write32 (uart_test_base + UART_IER, 0x0);
  write32 (uart_test_base + UART_FCR, UART_FCR_RXSR | UART_FCR_TXSR);

  /* Clear receive buffer RBR. */
  data = read32 (UART_LSR + uart_test_base);
  while (data & UART_LSR_DR)
    {
      data = read32 (UART_RBR + uart_test_base);
      data = read32 (UART_LSR + uart_test_base);
    }

  /* Clear modem status register. */
  data = read32 (UART_MSR + uart_test_base);
}

static void bt_uart_baudrate (uint32 port_id, uint32 br, uint32 uart_clk)
{
  uint32 br_div;
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  br_div = uart_clk / 16 / br;

  if (br_div == 0)
    br_div = 1;

  if(br == BAUDRATE_3686400)
    br_div = 1;
  else if(br == BAUDRATE_1842000)
    br_div = 2;
  else if(br == BAUDRATE_3000000)
    br_div = 1;

  /* Setup baudrate. 1 stopbit */
  data = read32 ((UART_LCR + uart_test_base)) | UART_LCR_DLAB;
  data &= ~(UART_LCR_STB);
  write32 ((UART_LCR + uart_test_base), data);
  write32 ((UART_DLH + uart_test_base), ((br_div >> 8) & 0xff));
  data = read32 ((UART_DLH + uart_test_base));
  write32 ((UART_DLL + uart_test_base), (br_div & 0xff));

  /* UART : regular functional */
  data = read32 ((UART_LCR + uart_test_base)) & (~UART_LCR_DLAB);
  write32 ((UART_LCR + uart_test_base), data);
}

static void
bt_uart_setparity (uint32 port_id, uint32 parity)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  /*Setup Parity */
  data = read32 ((UART_LCR + uart_test_base));
  data &= ~(UART_LCR_PEN | UART_LCR_EPS);
  data |= (parity << 3);
  write32 ((UART_LCR + uart_test_base), data);
}

static void
bt_uart_setdatawidth (uint32 port_id, uint32 datawidth)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  /*Setup data width */
  data = read32 ((UART_LCR + uart_test_base));
  data &= ~(0x3);
  data |= (datawidth << 0);
  write32 ((UART_LCR + uart_test_base), data);
}

void
bt_uart_enable (uint32 port_id, uint32 enable)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  /* UART Unit Enable */
  data = read32 (UART_IER + uart_test_base);
  if (enable)
    data |= UART_IER_UUE;
  else
    data &= ~UART_IER_UUE;
  write32 ((UART_IER + uart_test_base), data);
}

#if (defined BT_UART_DMA_TX_ENABLE) || (defined BT_UART_DMA_RX_ENABLE)
struct btuartDmaInfoStat dmaInfo = {
    .tx = {
        .totalTxCnt = 0,
        .intrCnt = 0,
        .toCnt = 0,
        .lastLoopCnt = 0,
        .totalLoopCnt = 0,
        .startTick = 0,
        .startOSTick = 0,
        .toTick = 0,
        .toOSTick = 0,
        .reg = {
            .DCSR = 0,
        }
    },
    .rx = {
        .enDmaCnt = 0,
        .dmaHisrCnt = 0,
    },
};
#endif

#ifdef BT_UART_DMA_TX_ENABLE
static OSFlagRef BTUARTDMATxDoneFlgRef = NULL;
static int BTUARTDMATxHISR(uint32_t val);
static XLLP_DMAC_CHANNEL_T bt_uart_get_tx_channel(uint32 port_id);
#endif

void
bt_uart_dma_enable (uint32 port_id, uint32 enable)
{
#if 0
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  /* UART Unit DMA Enable */
  data = read32 (UART_IER + uart_test_base);
  if (enable)
    data |= UART_IER_DMAE;
  else
    data &= ~UART_IER_DMAE;
  write32 ((UART_IER + uart_test_base), data);
#endif

  if(enable)
  {
#ifdef BT_UART_DMA_TX_ENABLE
    if(BTUARTDMATxDoneFlgRef == NULL) {
        OSAFlagCreate(&BTUARTDMATxDoneFlgRef);
    }
    dma_irq_callback_register(BTUARTDMATxHISR, bt_uart_get_tx_channel(port_id));

    {
      volatile uint32 data;
      uint32 uart_test_base;

      uart_test_base = bt_uart_info[port_id].base_addr;
      /* UART Unit DMA Enable */
      data = read32 (UART_IER + uart_test_base);
      data |= UART_IER_DMAE;
      write32 ((UART_IER + uart_test_base), data);
    }

    bt_dma_mode = 1;
#endif
  }
  else
  {
    bt_dma_mode = 0;
  }

  //appbt_uart_log("enable DMA %d\r\n", enable);
}

static void
bt_uart_highspeed_enable (uint32 port_id, uint32 enable)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  /* UART high speed mode Enable */
  data = read32 (UART_IER + uart_test_base);
  if (enable)
    data |= UART_IER_HSE;
  else
    data &= ~UART_IER_HSE;
  write32 ((UART_IER + uart_test_base), data);
}

static void
bt_uart_fifo_control (uint32 port_id, uint32 level, uint32 bwidth, uint32 trailingbyte,
		   uint32 fenable)
{
  volatile uint32 data = 0;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  data &= ~0xFF;
  //data |= (1 << 3);		//Transmitter Interrupt Level<p>0 = Interrupt/DMA request when FIFO is half empty<p>1 = Interrupt/DMA request when FIFO is empty
  data |= (level << 6) | (bwidth << 5) | (trailingbyte << 4) | (fenable);
  write32 ((UART_FCR + uart_test_base), data);
}

void
bt_uart_clear_receive_fifo (uint32 port_id)
{
  volatile uint32 data = 0;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  data = read32(UART_FCR + uart_test_base);
  data |= UART_FCR_FIFO_EN | UART_FCR_CLEAR_RCVR;
  write32 ((UART_FCR + uart_test_base), data);
}

void
bt_uart_clear_transfer_fifo (uint32 port_id)
{
  volatile uint32 data = 0;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  data = read32(UART_FCR + uart_test_base);
  data |= UART_FCR_CLEAR_XMIT | UART_FCR_FIFO_EN;
  write32 ((UART_FCR + uart_test_base), data);
}

void
bt_uart_enable_int_out (uint32 port_id, uint32 enable)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;

  data = read32 (UART_MCR + uart_test_base);
  if (enable)
    data |= UART_MCR_OUT2;
  else
    data &= ~(UART_MCR_OUT2);
  write32 ((UART_MCR + uart_test_base), data);
}

static void
bt_uart_enable_loopback (uint32 port_id, uint32 enable)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;

  data = read32 (UART_MCR + uart_test_base);
  if (enable)
    data |= UART_MCR_LOOP;
  else
    data &= ~(UART_MCR_LOOP);
  write32 ((UART_MCR + uart_test_base), data);
}

void
bt_uart_enable_flowctrl (uint32 port_id, uint32 enable)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  data = read32 (UART_MCR + uart_test_base);
  if (enable)
    data |= BIT_1 | BIT_5;
  else
    data &= ~(BIT_1 | BIT_5);
  write32 ((UART_MCR + uart_test_base), data);
}

static BOOL is_bt_uart_rts_enable(UINT32 port_id)
{
    volatile uint32 data;
    uint32 uart_test_base;

    uart_test_base = bt_uart_info[port_id].base_addr;
    data = read32 (UART_MCR + uart_test_base);

    return (data & BIT_1) ? TRUE : FALSE;
}
void
bt_uart_rts_ctl(uint32 port_id, uint32 enable)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;
  data = read32 (UART_MCR + uart_test_base);
  if (enable)
    data |= BIT_1;
  else
    data &= ~(BIT_1);
  write32 ((UART_MCR + uart_test_base), data);
}


void
bt_uart_enable_all_int (uint32 port_id, uint32 enable)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;

  data = read32 (UART_IER + uart_test_base);
  if (enable)
    data |= BIT_0 | BIT_1 | BIT_2 | BIT_3 | BIT_4;
  else
    data &= ~(BIT_0 | BIT_1 | BIT_2 | BIT_3 | BIT_4);
  write32 ((UART_IER + uart_test_base), data);
}

void
bt_uart_enable_int (uint32 port_id, uint32 enable,uint32 mask)
{
  volatile uint32 data;
  uint32 uart_test_base;

  uart_test_base = bt_uart_info[port_id].base_addr;

  data = read32 (UART_IER + uart_test_base);
  if (enable)
    data |= mask;
  else
    data &= ~(mask);
  write32 ((UART_IER + uart_test_base), data);
}

static void
bt_uart_enable_RxInt (uint32 port_id, uint32 enable)
{
    volatile uint32 data;
    uint32 uart_test_base;

    uart_test_base = bt_uart_info[port_id].base_addr;

    data = read32 (UART_IER + uart_test_base);
#ifdef SUPPORT_UART_XXXX
    if (enable)
        data |= UART_IER_RAVIE | UART_IER_RTOIE | UART_IER_RLSE | UART_IER_EORIE;
    else
        data &= ~(UART_IER_RAVIE | UART_IER_RTOIE | UART_IER_RLSE | UART_IER_EORIE);
#else
    if (enable)
        data |= (UART_IER_RAVIE | UART_IER_RTOIE | UART_IER_RLSE);
    else
        data &= ~(UART_IER_RAVIE | UART_IER_RTOIE | UART_IER_RLSE);
#endif
    write32 ((UART_IER + uart_test_base), data);
}

static void
bt_uart_enable_TxInt (uint32 port_id, uint32 enable)
{
    volatile uint32 data;
    uint32 uart_test_base;

    uart_test_base = bt_uart_info[port_id].base_addr;

    data = read32 (UART_IER + uart_test_base);
    if (enable)
        data |= UART_IER_TIE;
    else
        data &= ~(UART_IER_TIE);
    write32 ((UART_IER + uart_test_base), data);
}

int
bt_uart_putc (uint32 port_id, int c)
{
    uint32 uart_test_base;

    uart_test_base = bt_uart_info[port_id].base_addr;
    while ((read32 (uart_test_base + UART_LSR) & UART_LSR_THRE) == 0);
    write32 (uart_test_base + UART_THR, c);

    return c;
}

#ifdef BT_UART_DMA_TX_ENABLE
#define BT_UART_TX_FIFO_SIZE            (1024 + 128)
#define BT_UART_TX_HALF_FIFO_SIZE       (BT_UART_TX_FIFO_SIZE / 2)

/* DDR_NONCACHE */
#pragma arm section rwdata="UARTTx", zidata="UARTTx"
__align(8) unsigned int BTUartDMATxData[BT_UART_TX_FIFO_SIZE];
#pragma arm section rwdata, zidata

static uint32 baudrate = 0;
static uint32 calcTimeOut(uint32 counter)
{
    /** ((counter * 10 bit * 1000ms)/(br*tick) + 1) + margin **/
    return (((counter*10*1000)/(baudrate*5) + 1) + 1);
}

static XLLP_DMAC_CHANNEL_T tx_channel_map[] = {UART2_TX_CHANNEL, UART3_TX_CHANNEL, UART4_TX_CHANNEL};
static XLLP_DMAC_CHANNEL_T bt_uart_get_tx_channel(uint32 port_id)
{
    XLLP_DMAC_CHANNEL_T channel = (XLLP_DMAC_CHANNEL_T)0;
    if (port_id > 0) {
        channel = tx_channel_map[port_id - 1];
    } else {

    }
    return channel;
}

static DMAC_DRCMR_T tx_device_map[] = {BT_UART2_TX_DMA_DEV, BT_UART3_TX_DMA_DEV, BT_UART4_TX_DMA_DEV};
static DMAC_DRCMR_T bt_uart_get_tx_device(uint32 port_id)
{
    DMAC_DRCMR_T device = (DMAC_DRCMR_T)0;
    if (port_id > 0) {
        device = tx_device_map[port_id - 1];
    } else {

    }
    return device;
}

static int BTUARTDMATxHISR(uint32_t val)
{
    dmaInfo.tx.intrCnt++;
    OSA_STATUS os_status = OSAFlagSet(BTUARTDMATxDoneFlgRef, 0x01, OSA_FLAG_OR);
    return 0;
}
#endif

#if 0
static int
__bt_uart_put_data(uint32 port_id, unsigned char *data, int size)
{
    uint32 lineStatus;
    int counter;
    uint32 address = bt_uart_info[port_id].base_addr;

    uint8 trigger=0;
    DMA_CMDx_T TX_Cmd;
    volatile int timeout = 0;

    TX_Cmd.value = 0;
    TX_Cmd.bits.IncSrcAddr = 1;
    TX_Cmd.bits.IncTrgAddr = 0;
    TX_Cmd.bits.FlowSrc = 0;
    TX_Cmd.bits.FlowTrg = 1;
    TX_Cmd.bits.Width = 1;
    TX_Cmd.bits.MaxBurstSize = 1;

    {
        uint32 v, cpsr;
        cpsr = disableInterrupts();
        HW_UART_READ_IER(address, v);
        HW_UART_WRITE_IER(address, v | IER_ALL_BITS_EXCEPT_TRANS_AND_RCV_AND_MODEM);
        restoreInterrupts(cpsr);
    }
    //ENABLE_UART_INT(port_id, IER_ALL_BITS_EXCEPT_TRANS_AND_RCV_AND_MODEM);  //IER_ALL_BITS_EXCEPT_TRANS_AND_MODEM

    HW_UART_READ_FCR(address, trigger);
    HW_UART_WRITE_FCR(address, (trigger | FCR_ENABLE_FIFO  | FCR_INT_TRIGGER_L32 | FCR_TRAIL_INT));

    if(size == 0) return -1;

    HW_UART_READ_LSR(address, lineStatus);

    if(lineStatus & LSR_EMP_DATA) // 64 byte in the FIFO are free
        counter = BT_UART_TX_FIFO_SIZE;
    else if(lineStatus & LSR_EMP_TREG) // more then 32 byte in the FIFO are free
        counter = BT_UART_TX_HALF_FIFO_SIZE;
    else
        return -1;

    if (counter > size)
        counter = size;

    memcpy(BTUartDMATxData, data, counter);
    TX_Cmd.bits.Length = counter;
    //setup DMA
    XllpDmacStopTransfer(UART2_TX_CHANNEL);
    //Map Device to Channel
    XllpDmacMapDeviceToChannel(BT_UART2_TX_DMA_DEV, UART2_TX_CHANNEL);
    XllpDmacNoDescriptorFetch(UART2_TX_CHANNEL);
    //turn ON user alignment - in case buffer address is 64bit aligned
    alignChannel(UART2_TX_CHANNEL, 1);
    loadNonDescriptor((unsigned int)BTUartDMATxData, (unsigned int)((volatile UINT32 *)((address) + UART_THR_ADD)), &TX_Cmd, UART2_TX_CHANNEL);
    //Kick off DMA
    XllpDmacStartTransfer(UART2_TX_CHANNEL);

    //timer loop waiting for dma to finish
    //setup a timer to fail gracefully in case of error
    timeout = 0xFFFFF;

    //wait until the TX channel gets the stop UINT_Terrupt and the TX fifo is drained

    if(counter > 468)
    {
        //appbt_uart_log("sleep\r\n");
        OSATaskSleep(1);
    }

    while((readDmaStatusRegister(UART2_TX_CHANNEL) & XLLP_DMAC_DCSR_STOP_INTR) != XLLP_DMAC_DCSR_STOP_INTR)
    {
        //if we've waited long enough, fail
        if((timeout--) <= 0)
        {
            HW_UART_READ_LSR(address, lineStatus);
            return -1;
        }
    }

    timeout = 0xFFFFF;
    //wait until the TX fifo is drained
    while(1)
    {
        HW_UART_READ_LSR(address, lineStatus);

        if (( lineStatus & LSR_EMP_DATA   ) ||( lineStatus & LSR_EMP_TREG ))
        {
            break;
        }

        //if we've waited long enough, fail
        if((timeout--) <= 0)
        {
            return -1; //UART_RC_TX_DMA_ERR;
        }
    }

    data += counter;
    size = size - counter;

    //clear out DMA settings
    XllpDmacUnMapDeviceToChannel(BT_UART2_TX_DMA_DEV, UART2_TX_CHANNEL);
    XllpDmacUnMapDeviceToChannel(BT_UART2_RX_DMA_DEV, UART2_RX_CHANNEL);

    return counter; //UART_RC_OK;
}

void
bt_uart_put_data(uint32 port_id, unsigned char *data, int size)
{
    int ret = 0;
    int offset = 0;

    while(offset < size)
    {
        ret = __bt_uart_put_data(port_id, data + offset, size - offset);

        if(ret > 0)
        {
            offset += ret;
        }
        else if(ret < 0)
        {
            return;
        }
    }
}
#endif

int bt_uart_getc (uint32 port_id)
{
    uint32 data,err = 0;
    uint32 uart_test_base;

    uart_test_base = bt_uart_info[port_id].base_addr;
    while (1)
    {
        data = read32 (uart_test_base + UART_LSR) & 0xFF;
        if (data &
            (UART_LSR_ERR | UART_LSR_BI | UART_LSR_FE | UART_LSR_PE |
             UART_LSR_OE))
        {
            //trace ("uart get err: 0x%08X\n", data);
            err = 1;
        }
        if (data & UART_LSR_DR)
            break;
    }
    if(err)
    {
        read32 (uart_test_base + UART_RBR);
        return UART_DATA_ERROR;
    }
    else
        return read32 (uart_test_base + UART_RBR);
}

uint32 bt_uart_get_base_addr(uint32 port_id)
{
    uint32 uart_base = 0xCCCCCCCC;

    switch(port_id)
    {
        case UART1_ID:
            uart_base = AP_UART1_BASE;
            break;
        case UART2_ID:
            uart_base = AP_UART2_BASE;
            break;
        case UART3_ID:
            uart_base = AP_UART3_BASE;
            break;
        default:
            break;
    }
    return uart_base;
}

uint32 bt_uart_get_IntStatus(uint32 port_id)
{
  return read32(bt_uart_get_base_addr(port_id) + UART_IIR);
}

BOOL bt_uart_is_RxInt(uint32 port_id)
{
    if(bt_uart_get_IntStatus(port_id) & (UART_IIR_TOD | UART_IIR_RDI))
        return TRUE;
    else
        return FALSE;
}

void bt_uart_mask_RxInt(uint32 port_id, uint32 enable)
{
    INTC_InterruptSources int_no = (INTC_InterruptSources)INTCGetIntVirtualNum(bt_uart_info[port_id].irq);

    bt_uart_enable_RxInt(port_id, enable);
    if(enable)
        INTCEnable(int_no);
    else
        INTCDisable(int_no);
}

int bt_uart_flush(uint32 port_id)
{
    #define UART_FLUSH_TIMEOUT_CN	0x2000
    int i = 0;
    uint32 uart_base, data;

    uart_base = bt_uart_get_base_addr(port_id);

    do{
        data = read32(uart_base + UART_LSR);
        if(!(data & 0x1) && (data & 0x40))
            break;

        if(data & 0x1)
            read32(uart_base + UART_RBR);

        i++;
        if(i > UART_FLUSH_TIMEOUT_CN)
            break;
    }while(1);

    if(i >= UART_FLUSH_TIMEOUT_CN )
        return 1;
    else
        return 0;
}

int bt_uart_getchar_noblock (uint32 port_id)
{
    uint32 data;

    uint32 uart_test_base;

    uart_test_base = bt_uart_info[port_id].base_addr;
    while (1)
    {
        data = read32 (uart_test_base + UART_LSR) & 0xFF;
        if (data &
            (UART_LSR_ERR | UART_LSR_BI | UART_LSR_FE | UART_LSR_PE |
             UART_LSR_OE))
        {
            //trace ("uart get err: 0x%08X\n", data);
            return UART_DATA_ERROR;
        }
        if (data & UART_LSR_DR)
            break;
        else
            return UART_NONE_DATA;
    }
    return read32 (uart_test_base + UART_RBR);
}

void bt_uart_basic_init (bt_uart_cfg *port)
{
    appbt_uart_log("init baudrate %d\r\n", port->baudrate);
    appbt_uart_log("init port %d\r\n", port->port_id);

    /* Set slow UART clock source to 14.756M */
    #define SLOW_UART_CLOCK_REG     0xD4050014
    *((volatile UINT32 *)SLOW_UART_CLOCK_REG) = 0x1F3E0600;

    bt_uart_glb_init(port->port_id, port->highspeed);

    //Disable uart in case its enabled
    bt_uart_enable (port->port_id, FALSE);
    // intialize uart
    bt_uart_base_init (port->port_id);

    //configure uart
    bt_uart_setparity (port->port_id,port->parity);
    bt_uart_setdatawidth (port->port_id, port->datawidth);
    bt_uart_baudrate (port->port_id, port->baudrate, APBC_UART_CLK_FNCLK_REGULAR);
#ifdef BT_UART_DMA_TX_ENABLE
    baudrate = port->baudrate;
#endif
    //enable fifo
    if (port->dma){
        bt_uart_fifo_control (port->port_id, 1, 0, 1, TRUE);
        bt_uart_dma_enable (port->port_id, TRUE);
    }
    else if((port->fifo) && !(port->dma)){
        bt_uart_dma_enable (port->port_id, FALSE);
        bt_uart_fifo_control (port->port_id, 3, 0, 0, TRUE);
    }
    else{
        bt_uart_fifo_control (port->port_id, 0, 0, 0, FALSE);
        bt_uart_dma_enable (port->port_id, FALSE);
    }

    bt_uart_enable_all_int (port->port_id, FALSE);
    bt_uart_enable_int_out (port->port_id, FALSE);

    if (port->highspeed)
        bt_uart_highspeed_enable (port->port_id, TRUE);
    else
        bt_uart_highspeed_enable (port->port_id, FALSE);

    if (port->loopback)
        bt_uart_enable_loopback (port->port_id, TRUE);
    else
        bt_uart_enable_loopback (port->port_id, FALSE);

    if (port->flowctrl)
        bt_uart_enable_flowctrl (port->port_id, TRUE);
    else
        bt_uart_enable_flowctrl (port->port_id, FALSE);

    //enable uart
    bt_uart_enable (port->port_id, TRUE);
    return;
}

void bt_dma_transfer_request(bt_uartdir dir, uint32 port_id, uint8 *buf, uint32 size)
{
	DMA_CMDx_T cmd;
	uint32 uart_test_base;

  	uart_test_base = bt_uart_info[port_id].base_addr;

	if(dir == bt_uart_rx){
		cmd.value = 0;
		cmd.bits.IncSrcAddr = 0;
		cmd.bits.IncTrgAddr = 1;
		cmd.bits.FlowTrg = 0;
		cmd.bits.FlowSrc = 1;
		cmd.bits.Width = 1;
		cmd.bits.MaxBurstSize = 1;
		cmd.bits.Length = size;

		dma_set_reg_nf((uint32)(uart_test_base + UART_RBR), (uint32_t)buf, &cmd,DMA_DEV_UART1_RX_CH);
		dma_xfer_start(DMA_DEV_UART1_RX_CH);
		dma_stop_irq_en(DMA_DEV_UART1_RX_CH);
	}
	else{
		cmd.value = 0;
		cmd.bits.IncSrcAddr = 1;
		cmd.bits.IncTrgAddr = 0;
		cmd.bits.FlowTrg = 1;
		cmd.bits.FlowSrc = 0;
		cmd.bits.Width = 1;
		cmd.bits.MaxBurstSize = 1;
		cmd.bits.Length = size;

		dma_set_reg_nf((uint32)buf, (uint32)(uart_test_base + UART_THR), &cmd,DMA_DEV_UART1_TX_CH);
		dma_xfer_start(DMA_DEV_UART1_TX_CH);
		dma_stop_irq_en(DMA_DEV_UART1_TX_CH);
	}

	return;
}

static uint32 __bt_uart_send_data(uint32 port_id, uint8 *data, uint32 size)
{
	uint32 i = 0;
	uint32 num=0;

	if(bt_dma_mode > 0) {
#ifdef BT_UART_DMA_TX_ENABLE
        uint32 lineStatus = 0, counter = 0;
        uint32 uart_test_base = bt_uart_info[port_id].base_addr;

        lineStatus = read32 (UART_LSR + uart_test_base);

        /* 64 byte in the FIFO are free or
         * more then 32 byte in the fIFO are free
         */
        if ((lineStatus & LSR_EMP_DATA)||(lineStatus & LSR_EMP_TREG))
        {
            counter = BT_UART_TX_FIFO_SIZE;
        }
        else
        {
            appbt_uart_log("uart fifo no free(LSR : %x), MCR : %x, MSR : %x, FCR : %x, IER : %x",
                            lineStatus, read32(UART_MCR + uart_test_base),
                            read32(UART_MSR + uart_test_base), read32(UART_FCR + uart_test_base),
                            read32(UART_IER + uart_test_base));
            SetBTPmLock(BT_WAKELOCK_UART_TX_DMA);
            OSATaskSleep(1);
            ClearBTPmLock(BT_WAKELOCK_UART_TX_DMA);
            return 0;
        }

        if (counter > size)
            counter = size;

        //set uart tx dma timeout to 100ms
        uint32 timeout = 20; //20 tick
        UINT32 flag_value;
        OSA_STATUS os_status = OS_SUCCESS;

        DMA_CMDx_T TX_Cmd;
        TX_Cmd.value = 0;
        TX_Cmd.bits.IncSrcAddr = 1;
        TX_Cmd.bits.IncTrgAddr = 0;
        TX_Cmd.bits.FlowSrc = 0;
        TX_Cmd.bits.FlowTrg = 1;
        TX_Cmd.bits.Width = 1;
        TX_Cmd.bits.MaxBurstSize = 1;
        //TX_Cmd.bits.EndIRQEn = 1;
        TX_Cmd.bits.Length = counter;

        XLLP_DMAC_CHANNEL_T tx_channel = bt_uart_get_tx_channel(port_id);
        DMAC_DRCMR_T tx_device = bt_uart_get_tx_device(port_id);

        memcpy(BTUartDMATxData, data, counter);
        loadNonDescriptor((unsigned int)BTUartDMATxData, (unsigned int)((volatile UINT32 *)((uart_test_base) + UART_THR_ADD)), &TX_Cmd, tx_channel);

        //setup DMA
        XllpDmacNoDescriptorFetch(tx_channel);

        //Map Device to Channel
        XllpDmacMapDeviceToChannel(tx_device, tx_channel);

        //turn ON user alignment - in case buffer address is 64bit aligned
        alignChannel(tx_channel, 1);

        //Kick off DMA
        P_XLLP_DMAC_T pDmacHandle = XLLP_Return_PDmacHandle();
        pDmacHandle->DCSR[tx_channel] |= XLLP_DMAC_DCSR_RUN | XLLP_DMAC_DCSR_STOP_IRQ_EN;

        dmaInfo.tx.startTick = get_current_TStimer_tick();
        dmaInfo.tx.startOSTick = OSAGetTicks();

        dmaInfo.tx.totalTxCnt++;
        SetBTPmLock(BT_WAKELOCK_UART_TX_DMA);
        os_status = OSAFlagWait(BTUARTDMATxDoneFlgRef, 0x01, OSA_FLAG_OR_CLEAR, &flag_value, timeout);
        ClearBTPmLock(BT_WAKELOCK_UART_TX_DMA);

        if(os_status != OS_SUCCESS) {
            XllpDmacDisableStopIrqInterrupt(tx_channel);
            XllpDmacStopTransfer(tx_channel);

            appbt_uart_log("UART DMA Transfer ERROR %d", os_status);
            dmaInfo.tx.toCnt++;
            appbt_uart_log("LSR : %x, MCR : %x, MSR : %x, FCR : %x, IER : %x",
                        read32 (UART_LSR + uart_test_base), read32(UART_MCR + uart_test_base),
                        read32(UART_MSR + uart_test_base), read32(UART_FCR + uart_test_base),
                        read32(UART_IER + uart_test_base));
        }

        //data += counter;
        num = counter - (pDmacHandle->DDG[tx_channel].DCMD & 0x1FFF);
        dmaInfo.tx.totalLoopCnt += dmaInfo.tx.lastLoopCnt;
        appbt_uart_log("DMA info, size %d, counter %d, num %d, totalTx %d, intr %d, to %d, lastloop %d, totalLoop %d\r\n",
                        size, counter, num, dmaInfo.tx.totalTxCnt, dmaInfo.tx.intrCnt,
                        dmaInfo.tx.toCnt, dmaInfo.tx.lastLoopCnt, dmaInfo.tx.totalLoopCnt);

        dmaInfo.tx.lastLoopCnt = 0;

        //clear out DMA settings
        XllpDmacUnMapDeviceToChannel(tx_device, tx_channel);
#else
        uint32 len;
		while(size){
			if(size > DMA_MDMA_NF_XFER_SIZE)
				len = DMA_MDMA_NF_XFER_SIZE;
			else
				len = size;

			bt_dma_transfer_request(bt_uart_tx, port_id, data, len);

			if(len == DMA_MDMA_NF_XFER_SIZE)
				size -= DMA_MDMA_NF_XFER_SIZE;
			else
				size = 0;
		}
#endif
	} else {
		while(size){
			if(size > UART_FIFO_DEPTH){
				for(i=0 ; i< UART_FIFO_DEPTH; i++){
					bt_uart_putc(port_id,*data);
					data++;
					num ++;
				}
				size = size - UART_FIFO_DEPTH;
			}
			else{
				for(i=0 ; i< size; i++){
					bt_uart_putc(port_id, *data);
					data++;
					num++;
				}
				size = 0;
			}
		}
	}

	return num;
}

uint32 bt_uart_send_data(uint32 port_id, uint8 *data, uint32 size)
{
    uint8 retry = 0;
    uint32 counter = 0;

    if(appbt_le_is_dtm_mode())
    {
        if((UINT32)OSATaskGetCurrentRefExt() != (UINT32)Modem0RxHISR)
        {
            return 0;
        }
    }

    do {
        retry++;
        counter += __bt_uart_send_data(port_id, data + counter, size - counter);
        if (retry > 5) {
            appbt_uart_log("cannot completely send data(sent %ld bytes of %ld bytes)\r\n", counter, size);
            break;
        }
    } while (counter < size);
    return counter;
}

#ifdef BT_UART_DMA_RX_ENABLE
#define BT_UART_DMA_RX_DUAL_BUF_SIZE        (2048)
#define BT_UART_DMA_RX_DUAL_BUF_CNT         (4)
#pragma arm section rwdata="UARTRx", zidata="UARTRx"
__align(8) unsigned char BTUARTDMARXbuffer0[BT_UART_DMA_RX_DUAL_BUF_SIZE];
__align(8) unsigned char BTUARTDMARXbuffer1[BT_UART_DMA_RX_DUAL_BUF_SIZE];
#pragma arm section rwdata, zidata
static BOOL DualBuffSwitch = FALSE;
static uint8 CurrentbufferNum[BT_UART_DMA_RX_DUAL_BUF_CNT] = {0};
static uint32 CurBuffwpoint = 0, CurBuffrpoint = 0;
extern int copy_data_to_bt(char *src, uint32 len);

static XLLP_DMAC_CHANNEL_T rx_channel_map[] = {UART2_RX_CHANNEL, UART3_RX_CHANNEL, UART4_RX_CHANNEL};
static XLLP_DMAC_CHANNEL_T bt_uart_get_rx_channel(uint32 port_id)
{
    XLLP_DMAC_CHANNEL_T channel = (XLLP_DMAC_CHANNEL_T)0;
    if (port_id > 0) {
        channel = rx_channel_map[port_id - 1];
    } else {

    }
    return channel;
}

static DMAC_DRCMR_T rx_device_map[] = {BT_UART2_RX_DMA_DEV, BT_UART3_RX_DMA_DEV, BT_UART4_RX_DMA_DEV};
static DMAC_DRCMR_T bt_uart_get_rx_device(uint32 port_id)
{
    DMAC_DRCMR_T device = (DMAC_DRCMR_T)0;
    if (port_id > 0) {
        return rx_device_map[port_id - 1];
    } else {

    }
    return device;
}

static int BTUARTDMARxHISR(uint32_t val)
{
    volatile int timeout = 0;
    int status = 0, lenToRead = 0;
    UINT32 cpsr = 0, readsize = 0;
    UINT32 PortNumber = appbt_get_uart_id();
    XLLP_DMAC_CHANNEL_T rx_channel = bt_uart_get_rx_channel(PortNumber);
    INTC_InterruptSources int_no = (INTC_InterruptSources)INTCGetIntVirtualNum(bt_uart_info[PortNumber].irq);

    XllpDmacDisableStopIrqInterrupt(rx_channel);
    XllpDmacStopTransfer(rx_channel);

    if (!bt_uart_dma_preprocess)
    {
        bt_uart_rts_ctl(PortNumber, 0);
        HW_UART_WRITE_FCR(bt_uart_info[PortNumber].base_addr, FCR_INT_TRIGGER_L32 | FCR_ENABLE_FIFO);
    }
    else
    {
        bt_uart_dma_preprocess = FALSE;
    }

    timeout = 0xFFF;
    while(!(XLLP_Return_PDmacHandle()->DCSR[rx_channel] & CSR_STOPINTR))
    {
        if((timeout--) <= 0)
    	{
    		ErrorLogPrintf("DMA HISR: DCSR 0x%x", XLLP_Return_PDmacHandle()->DCSR[rx_channel]);
    		break;
        }
    }

#ifdef BT_UART_DMA_RX_DEBUG
    if(val & CSR_STOPINTR){
		ErrorLogPrintf("DMA HISR[0x%x]: stop int", XLLP_Return_PDmacHandle()->DCSR[rx_channel]);
		RTI_LOG("stp");
	}

	if(val & CSR_RECVENDINTR){
        ErrorLogPrintf("DMA HISR[0x%x]: rx end int", XLLP_Return_PDmacHandle()->DCSR[rx_channel]);
	}
#endif

    readsize = (CurBuffwpoint - CurBuffrpoint + BT_UART_DMA_RX_DUAL_BUF_CNT) % BT_UART_DMA_RX_DUAL_BUF_CNT;
    lenToRead = BT_UART_DMA_RX_DUAL_BUF_SIZE - (XLLP_Return_PDmacHandle()->DDG[rx_channel].DCMD & 0x1FFF);

    dmaInfo.rx.dmaHisrCnt++;

    if ((readsize == 0) || (lenToRead < 0))
    {
#ifdef BT_UART_DMA_RX_DEBUG
        ErrorLogPrintf("DMA HISR: %d %d", readsize, lenToRead);
        ASSERT(0);
#endif
        goto end;
    }

    if (CurrentbufferNum[CurBuffrpoint])
    {
        status = copy_data_to_bt((char *)BTUARTDMARXbuffer1, lenToRead);
    }
    else
    {
        status = copy_data_to_bt((char *)BTUARTDMARXbuffer0, lenToRead);
    }

    cpsr = disableInterrupts();
    CurBuffrpoint++;
    if (CurBuffrpoint >= BT_UART_DMA_RX_DUAL_BUF_CNT) {
        CurBuffrpoint = 0;
    }
    restoreInterrupts(cpsr);

end:
    //clear out DMA settings
    XllpDmacUnMapDeviceToChannel(bt_uart_get_rx_device(PortNumber), rx_channel);

    ClearBTPmLock(BT_WAKELOCK_UART_RX_DMA);

    HW_UART_IER_ENABLE_INT(bt_uart_info[PortNumber].base_addr, IER_RCV_DATA_AVAILABLE | IER_TIME_OUT); // 0xd0
    bt_uart_rts_ctl(PortNumber, 1);

    INTCEnable(int_no);


#ifdef BT_UART_DMA_RX_DEBUG
    if(status != 0)
    {
        RTI_LOG("RX Warning: %d!!!", status);
    }
#endif

    return 0;
}

void BTUARTDMARxEnable(uint32 port_id)
{
    DMA_CMDx_T RX_Cmd;
    uint32 address = bt_uart_info[port_id].base_addr;
    XLLP_DMAC_CHANNEL_T rx_channel = bt_uart_get_rx_channel(port_id);
    INTC_InterruptSources int_no = (INTC_InterruptSources)INTCGetIntVirtualNum(bt_uart_info[port_id].irq);


    /* DMA run bit already set, but DMA hasn't Moved data from UART FIFO owning to high system
       load or busy DMA channel till now. It caused UART Pending bit not clear, then we enter here again.
       If we re-start DMA transfer now, it will terminate the running DMA transfer abnormally. */
    if((XLLP_Return_PDmacHandle()->DCSR[rx_channel]) & CSR_RUN)
    {
        HW_UART_WRITE_FCR(address, FCR_INT_TRIGGER_L32 | FCR_TRAIL_DMA | FCR_ENABLE_FIFO); // 0x91
        HW_UART_IER_DISABLE_INT(address, IER_RCV_DATA_AVAILABLE);
        HW_UART_IER_ENABLE_INT(address, IER_DMA | IER_UART_UNIT | IER_TIME_OUT); // 0xd0
#ifdef BT_UART_DMA_RX_DEBUG
        ErrorLogPrintf("BT RX LISR: DMA is running, return");
#endif
        return;
    }

    /* Use DMA for Normal Rx interrupt. */
    RX_Cmd.value = 0;
    RX_Cmd.bits.IncSrcAddr = 0;
    RX_Cmd.bits.IncTrgAddr = 1;
    RX_Cmd.bits.FlowSrc = 1;
    RX_Cmd.bits.FlowTrg = 0;
    RX_Cmd.bits.Width = 1;
    RX_Cmd.bits.MaxBurstSize = 1;
    RX_Cmd.bits.Length = BT_UART_DMA_RX_DUAL_BUF_SIZE;

    //setup DMA
    XllpDmacNoDescriptorFetch(rx_channel);

    //Map Device to Channel
    XllpDmacMapDeviceToChannel(bt_uart_get_rx_device(port_id), rx_channel);

    alignChannel(rx_channel, 1);

    if(!DualBuffSwitch) {
        memset(BTUARTDMARXbuffer0, 0, BT_UART_DMA_RX_DUAL_BUF_SIZE);
        loadNonDescriptor((unsigned int)((volatile UINT32 *)((address) + UART_RHR_ADD)), (unsigned int)(BTUARTDMARXbuffer0), &RX_Cmd, rx_channel);
        DualBuffSwitch = TRUE;
        CurrentbufferNum[CurBuffwpoint] = 0;
    } else {
        memset(BTUARTDMARXbuffer1, 0, BT_UART_DMA_RX_DUAL_BUF_SIZE);
        loadNonDescriptor((unsigned int)((volatile UINT32 *)((address) + UART_RHR_ADD)), (unsigned int)(BTUARTDMARXbuffer1), &RX_Cmd, rx_channel);
        DualBuffSwitch = FALSE;
        CurrentbufferNum[CurBuffwpoint] = 1;
    }

    CurBuffwpoint++;
    if (CurBuffwpoint >= BT_UART_DMA_RX_DUAL_BUF_CNT) {
        CurBuffwpoint = 0;
    }

    // (workaround)avoid to entry uart recv interrupt(BTUARTRxByteLISR()) again and again
    HW_UART_WRITE_FCR(address, FCR_INT_TRIGGER_L32 | FCR_TRAIL_DMA | FCR_ENABLE_FIFO); // 0x91
    HW_UART_IER_DISABLE_INT(address, IER_RCV_DATA_AVAILABLE);
    HW_UART_IER_ENABLE_INT(address, IER_DMA | IER_UART_UNIT | IER_TIME_OUT); // 0xd0

    INTCDisable(int_no);

    SetBTPmLock(BT_WAKELOCK_UART_RX_DMA);

    //Kick off DMA
#ifdef BT_UART_DMA_RX_DEBUG
    ErrorLogPrintf("Start new DMA");
#endif

    bt_uart_id = port_id;

    issue_sync_barrier();

    XLLP_Return_PDmacHandle()->DCSR[rx_channel] |= (XLLP_DMAC_DCSR_RUN | XLLP_DMAC_DCSR_EOR_IRQ_EN | XLLP_DMAC_DCSR_STOP_IRQ_EN);
}

static int BTUARTRxByteLISR(uint32 port_id)
{
    dmaInfo.rx.enDmaCnt++;
    BTUARTDMARxEnable(port_id);
    return 0;
}

void bt_uart_dma_irq_preprocess(uint32 DINT)
{
    UINT32 t = 0;
    UINT32 port_id = appbt_get_uart_id();

    if(bt_uart_id == port_id)
    {
        t = bt_uart_get_rx_channel(port_id);

        if(DINT & (1<<t))
        {
            bt_uart_rts_ctl(port_id, 0);
            HW_UART_WRITE_FCR(bt_uart_info[port_id].base_addr, FCR_INT_TRIGGER_L32 | FCR_ENABLE_FIFO);

            bt_uart_dma_preprocess = TRUE;
    	}
	}
}
#endif

uint32 bt_uart_recv_data(uint32 port_id, uint8 *pbuf, uint32 size)
{
	uint32 i = 0;
	char c;
	int res;

	if(0){//if(bt_dma_mode > 0){
		uint32 len;

		while(size){
			if(size > DMA_MDMA_NF_XFER_SIZE)
				len = DMA_MDMA_NF_XFER_SIZE;
			else
				len = size;

			bt_dma_transfer_request(bt_uart_rx, port_id, pbuf, len);

			if(len == DMA_MDMA_NF_XFER_SIZE)
				size -= DMA_MDMA_NF_XFER_SIZE;
			else
				size = 0;
		}
	}
	else{
        uint32 data= 0;
        uint32 uart_test_base;
        if(size==0){
            uart_test_base = bt_uart_info[port_id].base_addr;
            data = read32 (uart_test_base + UART_LSR) & 0xFF;
            if(data & UART_LSR_DR){
                do{
                    *(pbuf+i)=read32 (uart_test_base + UART_RBR);
                    i++;
                    data = read32 (uart_test_base + UART_LSR) & 0xFF;

                }while(data & UART_LSR_DR);
                return i;
            }else{
                return 0;
            }

        }else{
            uart_test_base = bt_uart_info[port_id].base_addr;
            data = read32 (uart_test_base + UART_LSR) & 0xFF;
            if(data & UART_LSR_DR){
                do{
                    *(pbuf+i)=read32 (uart_test_base + UART_RBR);
                    i++;
                    if(i>=size){
                        break;
                    }
                    data = read32 (uart_test_base + UART_LSR) & 0xFF;

                }while(data & UART_LSR_DR);
                //appbt_uart_log("recv data %d: "
                //         "%02x %02x %02x %02x %02x %02x %02x %02x ",
                //         i,
                //         pbuf[0], pbuf[1], pbuf[2], pbuf[3],
                //         pbuf[4], pbuf[5], pbuf[6], pbuf[7]);
                return i;
            }else{
                return 0;
            }

            /*
            for(i=0; i<size; i++){
                //bt_ui_delay_us(10);
                res = bt_uart_getc(port_id);
                if(c)
                //appbt_uart_log("receive data:0x%x\r\n", c);
                pbuf[i] = c;
            }

            size = 0;
            */
        }


	}
	return size;
}

void bt_uart_set_baudrate(uint32 port_id, uint32 rate)
{
    bt_uart_enable(port_id, 0);
    OSATaskSleep(2);
    bt_uart_baudrate(port_id, rate, APBC_UART_CLK_FNCLK_REGULAR);
    bt_uart_enable(port_id, 1);
    OSATaskSleep(2);
}

void bt_uart_change_to_115200(uint32 port_id)
{
    uint32 data = 0;
    bt_uart_enable(port_id, 0);

    bt_uart_baudrate(port_id, BAUDRATE_115200, APBC_UART_CLK_FNCLK_REGULAR);

    write32(0xd401503c,0x4);
    data = ((20819 + 730) << 16) | 1000;
    OSATaskSleep(1);
    write32(PMU_SUCCR, data);
    write32(0xd401503c,0x3);
    OSATaskSleep(1);
    bt_uart_enable(port_id, 1);

#ifdef BT_UART_DMA_TX_ENABLE
    baudrate = BAUDRATE_115200;
#endif
}

void bt_uart_change_to_921600(uint32 port_id)
{
    uint32 data = 0;
    bt_uart_enable(port_id, 0);

    bt_uart_baudrate(port_id, BAUDRATE_921600, APBC_UART_CLK_FNCLK_REGULAR);

    write32(0xd401503c,0x4);
    data = ((20819 + 730) << 16) | 1000;
    OSATaskSleep(1);
    write32(PMU_SUCCR, data);
    write32(0xd401503c,0x3);
    OSATaskSleep(1);
    bt_uart_enable(port_id, 1);

#ifdef BT_UART_DMA_TX_ENABLE
    baudrate = BAUDRATE_921600;
#endif
}

void bt_uart_change_to_3000000(uint32 port_id)
{
    uint32 data = 0;
    bt_uart_enable(port_id, 0);

    bt_uart_baudrate(port_id, BAUDRATE_3000000, APBC_UART_CLK_FNCLK_REGULAR);

    data = (0x10 << 16) | 0xA;
    write32(PMU_SUCCR, data);
    OSATaskSleep(1);

    data = 0x13;
    if(port_id == 1)
    {
        // UART2_ID
        write32((0xD4015000 + 0x04), data);
    }
    else if(port_id == 3)
    {
        // UART4_ID
        write32((0xD401508C), data);
    }
    OSATaskSleep(1);

    bt_uart_enable(port_id, 1);

#ifdef BT_UART_DMA_TX_ENABLE
    baudrate = BAUDRATE_3000000;
#endif
}

void BT_UartIsrEntry(INTC_InterruptInfo interruptInfo)
{
    volatile uint32 source;
    uint32 port_id;
    uint32 uart_base;
    INTC_InterruptSources int_no;

    //appbt_uart_log("file:%s,function:%s,line:%d,int_no:%d.\r\n", __FILE__,__func__,__LINE__, interruptInfo);
    if(interruptInfo == 232)
        port_id = 1;
    else if(interruptInfo == 200)
        port_id = 0;
    else if(interruptInfo == 268)
        port_id = 2;
    else if(interruptInfo == 207)
        port_id = UART4_ID;
    else{
        //appbt_uart_log("wrong port id number!!\r\n");
        return;
    }

    int_no = (INTC_InterruptSources)INTCGetIntVirtualNum(bt_uart_info[port_id].irq);

    uart_base = bt_uart_info[port_id].base_addr;

#ifdef BT_UART_DMA_RX_DEBUG
    ErrorLogPrintf("BT int");
#endif

    /* clearing the interrupt */
    INTCClrSrc(int_no);

    /* Read interrupt Identification - indicates source of UART interrupt */
    HW_UART_READ_IIR(uart_base, source);

    /*  Interrupt Pending:
        0 = Interrupt is pending (active low)
        1 = No interrupt is pending
    */
    while(!(source & IIR_NO_PENDING_INT))
    {
        //SpuriousInt = FALSE;
        switch (source & 0x0FL )
        {
            case IIR_REC_ERROR/*0x6*/:
            {
                uint32 lsr = REG32(uart_base + UART_LSR);
                uint32 fcr = REG32(uart_base + UART_FCR);

#ifdef BT_UART_DMA_RX_DEBUG
                ErrorLogPrintf("BT Rx error: 0x%x", lsr);
#endif

                write32(uart_base + UART_FCR, fcr | UART_FCR_FIFO_EN | UART_FCR_CLEAR_RCVR | UART_FCR_CLEAR_XMIT);
                fcr = REG32(uart_base + UART_RBR);

#ifdef BT_UART_DMA_RX_ENABLE
                XLLP_DMAC_CHANNEL_T rx_channel = bt_uart_get_rx_channel(port_id);

                XllpDmacDisableStopIrqInterrupt(rx_channel);
                XllpDmacStopTransfer(rx_channel);

                bt_uart_enable_RxInt(port_id, TRUE);
                bt_uart_fifo_control(port_id, 3, 0, 0, TRUE);
#endif
                break;
            }

            case IIR_REC_DATA/*0x4*/:
			case IIR_TIME_OUT/*0xc*/:
            {
#ifdef BT_UART_DMA_RX_DEBUG
                if((source & 0x0FL ) == IIR_TIME_OUT)
                {
                    ErrorLogPrintf("BT TMO int");
                }

                if((source & 0x0FL ) == IIR_REC_DATA)
                {
                    ErrorLogPrintf("BT Rx int");
                }
#endif

                if(BT_UartIsrCallback[port_id][bt_rx_int])
                {
                    BT_UartIsrCallback[port_id][bt_rx_int](port_id);
                }
                break;
            }

            case IIR_TRANS_FIFO_REQ/*0x2*/:
            {
#ifdef BT_UART_DMA_RX_DEBUG
                ErrorLogPrintf("BT Tx int");
#endif

                /*  interrupt of Transmiter data empty TX interrupt */
                if(BT_UartIsrCallback[port_id][bt_tx_int])
                {
                    BT_UartIsrCallback[port_id][bt_tx_int](port_id);
                }
                break;
            }

            case IIR_MODEM_STATUS/*0x0*/:
            {
                uint32 lsr = REG32(uart_base + UART_LSR);

#ifdef BT_UART_DMA_RX_DEBUG
                ErrorLogPrintf("BT Modem status: 0x%x", lsr);
#endif
                break;
            }

            default:
            {
#ifdef BT_UART_DMA_RX_DEBUG
                ASSERT(0);
#endif

                break;
            }
        }

        HW_UART_READ_IIR(uart_base, source);

        if(!(source & IIR_NO_PENDING_INT))
        {
            /* After cleaning the sticky bit we check the IIR again to
               verify that no interrupt occured since the previous IIR
               read
            */
            /* clearing the interrupt */
            INTCClrSrc(int_no);

            HW_UART_READ_IIR(uart_base, source);
        }
    } // while

    return ;
}

int BT_UartRxDmaCallBackEntry(uint32 val)
{
    //appbt_uart_log("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
    if(BT_UartDmaCallback[1][bt_uart_rx])
        BT_UartDmaCallback[1][bt_uart_rx](1);
    return 0;
}

int BT_UartTxDmaCallBackEntry(uint32 val)
{
    //appbt_uart_log("file:%s,function:%s,line:%d\r\n", __FILE__,__func__,__LINE__);
    if(BT_UartDmaCallback[1][bt_uart_tx])
        BT_UartDmaCallback[1][bt_uart_tx](1);
    return 0;
}

void
bt_uart_irq_init (bt_uart_cfg *port)
{
	INTC_InterruptSources int_no;

	if(port->int_tpye){
		bt_dma_mode = 0;
		//appbt_uart_log("uart irq num:%d\r\n", bt_uart_info[port->port_id].irq);
		/*config uart interrupt*/
		//INTCPhase2Init();
		int_no = (INTC_InterruptSources)INTCGetIntVirtualNum(bt_uart_info[port->port_id].irq);
		INTCConfigure(int_no, INTC_IRQ, INTC_HIGH_LEVEL);
		INTCBind(int_no,BT_UartIsrEntry);
		INTCEnable(int_no);
		if (port->int_tpye == bt_all_int)
    	{
			BT_UartIsrCallback[port->port_id][bt_rx_int] = port->rx_callback;
			BT_UartIsrCallback[port->port_id][bt_tx_int] = port->tx_callback;
			bt_uart_enable_all_int (port->port_id ,TRUE);
			bt_uart_enable_int_out (port->port_id, TRUE);
    	}
		else if (port->int_tpye == bt_rx_int)
    	{
#ifdef BT_UART_DMA_RX_ENABLE
            BT_UartIsrCallback[port->port_id][bt_rx_int] = BTUARTRxByteLISR;
            dma_irq_callback_register(BTUARTDMARxHISR, bt_uart_get_rx_channel(port->port_id));
#else
            BT_UartIsrCallback[port->port_id][bt_rx_int] = port->rx_callback;
#endif
            BT_UartIsrCallback[port->port_id][bt_tx_int] = NULL;
			bt_uart_enable_RxInt(port->port_id, TRUE);
			bt_uart_enable_int_out (port->port_id, TRUE);
    	}
		else if(port->int_tpye == bt_tx_int)
		{
			BT_UartIsrCallback[port->port_id][bt_rx_int] = NULL;
			BT_UartIsrCallback[port->port_id][bt_tx_int] = port->tx_callback;
			bt_uart_enable_TxInt(port->port_id, TRUE);
			bt_uart_enable_int_out (port->port_id, TRUE);
		}
		else
		{
			bt_uart_enable_all_int (port->port_id, FALSE);
			bt_uart_enable_int_out (port->port_id, FALSE);
		}
	}
	else if(port->dma){
		bt_dma_mode = 1;
		//appbt_uart_log("uart work at dma mode.\r\n");
		if(port->rx_callback){
			BT_UartDmaCallback[port->port_id][bt_uart_rx] = port->rx_callback;
			dma_irq_callback_register(BT_UartRxDmaCallBackEntry, DMA_DEV_UART1_RX_CH);
			dma_map_device_to_channel(DMA_REQ_UART2_RX, DMA_DEV_UART1_RX_CH);
			set_user_aligment(DMA_DEV_UART1_RX_CH);
			dma_set_mode(DMA_MODE_NONFETCH, DMA_DEV_UART1_RX_CH);
		}
		if(port->tx_callback){
			BT_UartDmaCallback[port->port_id][bt_uart_tx] = port->tx_callback;
			dma_irq_callback_register(BT_UartTxDmaCallBackEntry, DMA_DEV_UART1_TX_CH);
			dma_map_device_to_channel(DMA_REQ_UART2_TX, DMA_DEV_UART1_TX_CH);
			set_user_aligment(DMA_DEV_UART1_TX_CH);
			dma_set_mode(DMA_MODE_NONFETCH, DMA_DEV_UART1_TX_CH);
		}
	}
	else{
		bt_dma_mode = 0;
		//appbt_uart_log("uart work at polling mode.\r\n");
	}

	return;
}


void
bt_uart_init (bt_uart_cfg *port)
{
	INTC_InterruptSources int_no;

	bt_uart_basic_init(port);
	if(port->int_tpye){
		bt_dma_mode = 0;
		//appbt_uart_log("uart irq num:%d\r\n", bt_uart_info[port->port_id].irq);
		/*config uart interrupt*/
		//INTCPhase2Init();
		int_no = (INTC_InterruptSources)INTCGetIntVirtualNum(bt_uart_info[port->port_id].irq);
		INTCConfigure(int_no, INTC_IRQ, INTC_HIGH_LEVEL);
		INTCBind(int_no,BT_UartIsrEntry);
		INTCEnable(int_no);
		if (port->int_tpye == bt_all_int)
    	{
			BT_UartIsrCallback[port->port_id][bt_rx_int] = port->rx_callback;
			BT_UartIsrCallback[port->port_id][bt_tx_int] = port->tx_callback;
			bt_uart_enable_all_int (port->port_id ,TRUE);
			bt_uart_enable_int_out (port->port_id, TRUE);
    	}
		else if (port->int_tpye == bt_rx_int)
    	{
    		BT_UartIsrCallback[port->port_id][bt_rx_int] = port->rx_callback;
			BT_UartIsrCallback[port->port_id][bt_tx_int] = NULL;
			bt_uart_enable_RxInt(port->port_id, TRUE);
			bt_uart_enable_int_out (port->port_id, TRUE);
    	}
		else if(port->int_tpye == bt_tx_int)
		{
			BT_UartIsrCallback[port->port_id][bt_rx_int] = NULL;
			BT_UartIsrCallback[port->port_id][bt_tx_int] = port->tx_callback;
			bt_uart_enable_TxInt(port->port_id, TRUE);
			bt_uart_enable_int_out (port->port_id, TRUE);
		}
		else
		{
			bt_uart_enable_all_int (port->port_id, FALSE);
			bt_uart_enable_int_out (port->port_id, FALSE);
		}
	}
	else if(port->dma){
		bt_dma_mode = 1;
		//appbt_uart_log("uart work at dma mode.\r\n");
		if(port->rx_callback){
			BT_UartDmaCallback[port->port_id][bt_uart_rx] = port->rx_callback;
			dma_irq_callback_register(BT_UartRxDmaCallBackEntry, DMA_DEV_UART1_RX_CH);
			dma_map_device_to_channel(DMA_REQ_UART2_RX, DMA_DEV_UART1_RX_CH);
			set_user_aligment(DMA_DEV_UART1_RX_CH);
			dma_set_mode(DMA_MODE_NONFETCH, DMA_DEV_UART1_RX_CH);
		}
		if(port->tx_callback){
			BT_UartDmaCallback[port->port_id][bt_uart_tx] = port->tx_callback;
			dma_irq_callback_register(BT_UartTxDmaCallBackEntry, DMA_DEV_UART1_TX_CH);
			dma_map_device_to_channel(DMA_REQ_UART2_TX, DMA_DEV_UART1_TX_CH);
			set_user_aligment(DMA_DEV_UART1_TX_CH);
			dma_set_mode(DMA_MODE_NONFETCH, DMA_DEV_UART1_TX_CH);
		}
	}
	else{
		bt_dma_mode = 0;
		//appbt_uart_log("uart work at polling mode.\r\n");
	}

	return;
}

/* remove as uart shell not nesscery for CP_PLT currently

#define UART_SHELL_PORT_ID	UART_PORT_0
#define UART_SHELL_BASE		AP_UART1_BASE

void uart_shell_putc(char ch)
{
	seagull_uart_putc(ch);
}

char uart_shell_chks(void)
{
	volatile uint32 data;

	volatile unsigned int* ualsr;

	ualsr = (volatile unsigned int*)(UART_SHELL_BASE + 0x14);

	return (*ualsr & 0x1);
}

char uart_shell_getc(void)
{
	volatile unsigned int* ualsr;
	volatile unsigned int* uathr;

	ualsr = (volatile unsigned int*)(UART_SHELL_BASE + 0x14);
	uathr = (volatile unsigned int*)(UART_SHELL_BASE);

	while (!(*ualsr & 0x1));
	return (char)*uathr;
}

void uart_shell_puts(const char *s)
{
	while(*s != '\0')
	{
		uart_shell_putc(*s);
		s++;
    }
}
*/
#else	//UART_NEW_VERSION

#include "bt_api.h"
#include "osa.h"

#define  BT_DEVICE   (0xBD)

/* DDR_NONCACHE */
#define BT_UART_DMA_RX_DUAL_BUF_SIZE        (2048)
#pragma arm section rwdata="UARTRx", zidata="UARTRx"
__align(8) unsigned char BTUARTDMARXbuffer[BT_UART_DMA_RX_DUAL_BUF_SIZE];
#pragma arm section rwdata, zidata

#define BT_UART_TX_FIFO_SIZE            (1024*5)
#pragma arm section rwdata="UARTTx", zidata="UARTTx"
__align(8) unsigned char BTUartDMATxData[BT_UART_TX_FIFO_SIZE];
#pragma arm section rwdata, zidata

BOOL bt_uart_dma_preprocess = FALSE;

/*extern variables*/
extern OS_HISR Modem0RxHISR;
extern OS_HISR Modem1RxHISR;

/*extern functions*/
extern void* OSATaskGetCurrentRefExt(void);


void bt_uart_enable (uint32 port_id, uint32 enable)
{
    uartdrv_enable(port_id, enable);
}

void bt_uart_dma_enable (uint32 port_id, uint32 enable)
{
    uartdrv_dma_enable(port_id, enable);
}

void bt_uart_enable_flowctrl (uint32 port_id, uint32 enable)
{
    uartdrv_enable_flowctrl(port_id, enable);
}

void bt_uart_rts_fifo(uint32 port_id, uint32 enable)
{
    if ((uartdrv_get_private_data(port_id) == BT_DEVICE) && (port_id == appbt_get_uart_id()))
    {
    	if (!bt_uart_dma_preprocess)
		{
		    uartdrv_rts_ctl(port_id, enable);
		    uartdrv_fifo_control(port_id, 3, 0, 0, 1);
		}
		else
		{
			bt_uart_dma_preprocess = FALSE;
		}
    }
}

void bt_uart_rts_ctl(uint32 port_id, uint32 enable)
{
    if ((uartdrv_get_private_data(port_id) == BT_DEVICE) && (port_id == appbt_get_uart_id()))
	{
        uartdrv_rts_ctl(port_id, enable);
	}
}

void bt_uart_mask_RxInt(uint32 port_id, uint32 enable)
{
    uartdrv_mask_RxInt(port_id, enable);
}

void bt_uart_dma_irq_preprocess(uint32 DINT)
{
    UINT32 t = 0;
    UINT32 port_id = appbt_get_uart_id();

    if(uartdrv_get_private_data(port_id) == BT_DEVICE)
    {
        t = uartdrv_get_dma_rx_channel(port_id);

        if(DINT & (1<<t))
        {
            uartdrv_rts_ctl(port_id, 0);
            uartdrv_fifo_control(port_id, 3, 0, 0, 1);
			bt_uart_dma_preprocess = TRUE;
    	}
	}
}

void bt_uart_basic_init (uartdrv_cfg *port)
{
    port->rx_dma_noncache_buffer = BTUARTDMARXbuffer;
    port->rx_dma_noncache_buffer_size = BT_UART_DMA_RX_DUAL_BUF_SIZE;
    port->tx_dma_noncache_buffer = BTUartDMATxData;
    port->tx_dma_noncache_buffer_size = BT_UART_TX_FIFO_SIZE;
    port->usedrvbuffer = 0;
    port->priv = BT_DEVICE;

    /* Set slow UART clock source to 14.756M */
    #define SLOW_UART_CLOCK_REG     0xD4050014
    *((volatile UINT32 *)SLOW_UART_CLOCK_REG) = 0x1F3E0600;

    uartdrv_basic_init(port);
}


uint32 bt_uart_send_data(uint32 port_id, uint8 *data, uint32 size)
{
    if(appbt_le_is_dtm_mode())
    {
        if((UINT32)OSATaskGetCurrentRefExt() != (UINT32)Modem0RxHISR)
        {
            return 0;
        }
    }

    return uartdrv_send_data(port_id, data, size);
}

uint32 bt_uart_recv_data(uint32 port_id, uint8 *pbuf, uint32 size)
{
    return uartdrv_recv_data(port_id, pbuf, size);
}

void bt_uart_set_baudrate(uint32 port_id, uint32 rate)
{
    uartdrv_set_baudrate(port_id, rate);
}

void bt_uart_change_to_115200(uint32 port_id)
{
    uartdrv_change_to_115200(port_id);
}

void bt_uart_change_to_921600(uint32 port_id)
{
    uartdrv_change_to_921600(port_id);
}

void bt_uart_change_to_3000000(uint32 port_id)
{
    uartdrv_change_to_3000000(port_id);
}

void bt_uart_irq_init (uartdrv_cfg *port)
{
    uartdrv_irq_init(port);
}

#endif	//UART_NEW_VERSION