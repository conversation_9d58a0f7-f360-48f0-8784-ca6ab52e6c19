# 1 "\\aud_sw\\AudioService\\src\\acm_audio_effect.c"
/******************************************************************************
 *
 *  (C)Copyright ASRMicro. All Rights Reserved.
 *
 *  THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF ASRMicro.
 *  The copyright notice above does not evidence any actual or intended
 *  publication of such source code.
 *  This Module contains Proprietary Information of ASRMicro and should be
 *  treated as Confidential.
 *  The information in this file is provided for the exclusive use of the
 *  licensees of ASRMicro.
 *  Such users have the right to use, modify, and incorporate this code into
 *  products for purposes authorized by the license agreement provided they
 *  include this notice and the associated copyright notice with any such
 *  product.
 *  The information in this file is provided "AS IS" without warranty.
 *
 ******************************************************************************/

# 1 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\acm_audio_effect.h"
/******************************************************************************
 *
 *  (C)Copyright ASRMicro. All Rights Reserved.
 *
 *  THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF ASRMicro.
 *  The copyright notice above does not evidence any actual or intended
 *  publication of such source code.
 *  This Module contains Proprietary Information of ASRMicro and should be
 *  treated as Confidential.
 *  The information in this file is provided for the exclusive use of the
 *  licensees of ASRMicro.
 *  Such users have the right to use, modify, and incorporate this code into
 *  products for purposes authorized by the license agreement provided they
 *  include this notice and the associated copyright notice with any such
 *  product.
 *  The information in this file is provided "AS IS" without warranty.
 *
 ******************************************************************************/

#pragma once
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
/* Copyright (C) ARM Ltd., 1999,2014 */
/* All rights reserved */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */









    /* armcc has builtin '__int64' which can be used in --strict mode */
# 27 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"
    /* armclang and non-strict armcc allow 'long long' in system headers */











# 46 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"


/*
 * 'signed' is redundant below, except for 'signed char' and if
 * the typedef is used to declare a bitfield.
 */

    /* 7.18.1.1 */

    /* exact-width signed integer types */
typedef   signed          char int8_t;
typedef   signed short     int int16_t;
typedef   signed           int int32_t;
typedef   signed       __int64 int64_t;

    /* exact-width unsigned integer types */
typedef unsigned          char uint8_t;
typedef unsigned short     int uint16_t;
typedef unsigned           int uint32_t;
typedef unsigned       __int64 uint64_t;

    /* 7.18.1.2 */

    /* smallest type of at least n bits */
    /* minimum-width signed integer types */
typedef   signed          char int_least8_t;
typedef   signed short     int int_least16_t;
typedef   signed           int int_least32_t;
typedef   signed       __int64 int_least64_t;

    /* minimum-width unsigned integer types */
typedef unsigned          char uint_least8_t;
typedef unsigned short     int uint_least16_t;
typedef unsigned           int uint_least32_t;
typedef unsigned       __int64 uint_least64_t;

    /* 7.18.1.3 */

    /* fastest minimum-width signed integer types */
typedef   signed           int int_fast8_t;
typedef   signed           int int_fast16_t;
typedef   signed           int int_fast32_t;
typedef   signed       __int64 int_fast64_t;

    /* fastest minimum-width unsigned integer types */
typedef unsigned           int uint_fast8_t;
typedef unsigned           int uint_fast16_t;
typedef unsigned           int uint_fast32_t;
typedef unsigned       __int64 uint_fast64_t;

    /* 7.18.1.4 integer types capable of holding object pointers */




typedef   signed           int intptr_t;
typedef unsigned           int uintptr_t;


    /* 7.18.1.5 greatest-width integer types */
typedef   signed     long long intmax_t;
typedef unsigned     long long uintmax_t;




    /* 7.18.2.1 */

    /* minimum values of exact-width signed integer types */





    /* maximum values of exact-width signed integer types */





    /* maximum values of exact-width unsigned integer types */





    /* 7.18.2.2 */

    /* minimum values of minimum-width signed integer types */





    /* maximum values of minimum-width signed integer types */





    /* maximum values of minimum-width unsigned integer types */





    /* 7.18.2.3 */

    /* minimum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width signed integer types */





    /* maximum values of fastest minimum-width unsigned integer types */





    /* 7.18.2.4 */

    /* minimum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding signed integer type */






    /* maximum value of pointer-holding unsigned integer type */






    /* 7.18.2.5 */

    /* minimum value of greatest-width signed integer type */


    /* maximum value of greatest-width signed integer type */


    /* maximum value of greatest-width unsigned integer type */


    /* 7.18.3 */

    /* limits of ptrdiff_t */
# 216 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of sig_atomic_t */



    /* limit of size_t */






    /* limits of wchar_t */
    /* NB we have to undef and redef because they're defined in both
     * stdint.h and wchar.h */



# 241 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"

    /* limits of wint_t */







    /* 7.18.4.1 macros for minimum-width integer constants */










    /* 7.18.4.2 macros for greatest-width integer constants */











# 305 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdint.h"






/* end of stdint.h */
# 25 "\\tavor\\Arbel\\obj_PMD2NONE\\inc\\acm_audio_effect.h"














    typedef struct eq_effect_params {
        int16_t a[3];
        int16_t b[3];
    } eq_effect_params_t;

    typedef struct audio_effect_eq {
        //bit 0 ~bit 4 is on/off of each filter.
        int16_t ctrl;
        eq_effect_params_t params[(5)];
    }audio_effect_eq_t;

    typedef struct audio_effect_config {
        uint32_t rate;
        uint32_t frame_length;
        int32_t target_gain;
        int32_t ramp_frames;
        int32_t gain_ramp_off;
        audio_effect_eq_t eq;
    }audio_effect_config_t;

    typedef uint32_t audio_effect_handle;

    void audio_effect_init(void);
    int audio_effect_create(const audio_effect_config_t* config, audio_effect_handle* handle);
    int audio_effect_run(audio_effect_handle handle, const int16_t* input, int16_t* output);
    int audio_effect_gain_ctrl(audio_effect_handle handle, int32_t gain);
    int audio_effect_mute_ctrl(audio_effect_handle handle, int32_t mute);
    int audio_effect_ramp_ctrl(audio_effect_handle handle, int32_t num);
    int audio_effect_eq_ctrl(audio_effect_handle handle, const audio_effect_eq_t* eq);
    int audio_effect_destroy(audio_effect_handle handle);

# 21 "\\aud_sw\\AudioService\\src\\acm_audio_effect.c"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"
/*
 * math.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.5
 * Copyright (C) Codemist Ltd., 1988
 * Copyright 1991-1998,2004-2006,2014 ARM Limited. All rights reserved
 */

/*
 * RCS $Revision$ Codemist 0.03
 * Checkin $Date$
 * Revising $Author: statham $
 */

/*
 * Parts of this file are based upon fdlibm:
 *
 * ====================================================
 * Copyright (C) 1993 by Sun Microsystems, Inc. All rights reserved.
 *
 * Developed at SunSoft, a Sun Microsystems, Inc. business.
 * Permission to use, copy, modify, and distribute this
 * software is freely granted, provided that this notice
 * is preserved.
 * ====================================================
 */






  /* armclang and non-strict armcc allow 'long long' in system headers */






/*
 * Some of these declarations are new in C99.  To access them in C++
 * you can use -D__USE_C99_MATH (or -D__USE_C99_ALL).
 */






# 61 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"

# 75 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"







   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain the nans function, as well as the
    * FP_NANS and FP_NANQ classification macros.
    */




/*
 * Macros for our inline functions down below.
 * unsigned& __FLT(float x) - returns the bit pattern of x
 * unsigned& __HI(double x) - returns the bit pattern of the high part of x
 *                            (high part has exponent & sign bit in it)
 * unsigned& __LO(double x) - returns the bit pattern of the low part of x
 *
 * We can assign to __FLT, __HI, and __LO and the appropriate bits get set in
 * the floating point variable used.
 *
 * __HI & __LO are affected by the endianness and the target FPU.
 */
# 112 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"





/*
 * A set of functions that we don't actually want to put in the standard
 * namespace ever.  These are all called by the C99 macros.  As they're
 * not specified by any standard they can't belong in ::std::.  The
 * macro #defines are below amongst the standard function declarations.
 * We only include these if we actually need them later on
 */





extern __attribute__((__pcs__("aapcs"))) unsigned __ARM_dcmp4(double /*x*/, double /*y*/);
extern __attribute__((__pcs__("aapcs"))) unsigned __ARM_fcmp4(float /*x*/, float /*y*/);
    /*
     * Compare x and y and return the CPSR in r0.  These means we can test for
     * result types with bit pattern matching.
     *
     * These are a copy of the declarations in rt_fp.h keep in sync.
     */

extern __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_fpclassifyf(float /*x*/);
extern __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_fpclassify(double /*x*/);
    /* Classify x into NaN, infinite, normal, subnormal, zero */
    /* Used by fpclassify macro */

static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isfinitef(float __x)
{
    return (((*(unsigned *)&(__x)) >> 23) & 0xff) != 0xff;
}
static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isfinite(double __x)
{
    return (((*(1 + (unsigned *)&(__x))) >> 20) & 0x7ff) != 0x7ff;
}
    /* Return 1 if __x is finite, 0 otherwise */
    /* Used by isfinite macro */

static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isinff(float __x)
{
    return ((*(unsigned *)&(__x)) << 1) == 0xff000000;
}
static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isinf(double __x)
{
    return (((*(1 + (unsigned *)&(__x))) << 1) == 0xffe00000) && ((*(unsigned *)&(__x)) == 0);
}
    /* Return 1 if __x is infinite, 0 otherwise */
    /* Used by isinf macro */

static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_islessgreaterf(float __x, float __y)
{
    unsigned __f = __ARM_fcmp4(__x, __y) >> 28;
    return (__f == 8) || (__f == 2); /* Just N set or Just Z set */
}
static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_islessgreater(double __x, double __y)
{
    unsigned __f = __ARM_dcmp4(__x, __y) >> 28;
    return (__f == 8) || (__f == 2); /* Just N set or Just Z set */
}
    /*
     * Compare __x and __y and return 1 if __x < __y or __x > __y, 0 otherwise
     * Used by islessgreater macro
     */

static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isnanf(float __x)
{
    return (0x7f800000 - ((*(unsigned *)&(__x)) & 0x7fffffff)) >> 31;
}
static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isnan(double __x)
{
    unsigned __xf = (*(1 + (unsigned *)&(__x))) | (((*(unsigned *)&(__x)) == 0) ? 0 : 1);
    return (0x7ff00000 - (__xf & 0x7fffffff)) >> 31;
}
    /* Return 1 if __x is a NaN, 0 otherwise */
    /* Used by isnan macro */

static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isnormalf(float __x)
{
    unsigned __xe = ((*(unsigned *)&(__x)) >> 23) & 0xff;
    return (__xe != 0xff) && (__xe != 0);
}
static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_isnormal(double __x)
{
    unsigned __xe = ((*(1 + (unsigned *)&(__x))) >> 20) & 0x7ff;
    return (__xe != 0x7ff) && (__xe != 0);
}
    /* Return 1 if __x is a normalised number, 0 otherwise */
    /* used by isnormal macro */

static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_signbitf(float __x)
{
    return (*(unsigned *)&(__x)) >> 31;
}
static __inline __declspec(__nothrow) __attribute__((__pcs__("aapcs"))) int __ARM_signbit(double __x)
{
    return (*(1 + (unsigned *)&(__x))) >> 31;
}
    /* Return signbit of __x */
    /* Used by signbit macro */








# 230 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"







  /* C99 additions */
  typedef float float_t;
  typedef double double_t;
# 251 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"



extern const int math_errhandling;
# 261 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"

extern __declspec(__nothrow) double acos(double /*x*/);
   /* computes the principal value of the arc cosine of x */
   /* a domain error occurs for arguments not in the range -1 to 1 */
   /* Returns: the arc cosine in the range 0 to Pi. */
extern __declspec(__nothrow) double asin(double /*x*/);
   /* computes the principal value of the arc sine of x */
   /* a domain error occurs for arguments not in the range -1 to 1 */
   /* and -HUGE_VAL is returned. */
   /* Returns: the arc sine in the range -Pi/2 to Pi/2. */

extern __declspec(__nothrow) __attribute__((const)) double atan(double /*x*/);
   /* computes the principal value of the arc tangent of x */
   /* Returns: the arc tangent in the range -Pi/2 to Pi/2. */

extern __declspec(__nothrow) double atan2(double /*y*/, double /*x*/);
   /* computes the principal value of the arc tangent of y/x, using the */
   /* signs of both arguments to determine the quadrant of the return value */
   /* a domain error occurs if both args are zero, and -HUGE_VAL returned. */
   /* Returns: the arc tangent of y/x, in the range -Pi to Pi. */

extern __declspec(__nothrow) double cos(double /*x*/);
   /* computes the cosine of x (measured in radians). A large magnitude */
   /* argument may yield a result with little or no significance. */
   /* a domain error occurs for infinite input (C 7.12.1 footnote 196). */
   /* Returns: the cosine value. */
extern __declspec(__nothrow) double sin(double /*x*/);
   /* computes the sine of x (measured in radians). A large magnitude */
   /* argument may yield a result with little or no significance. */
   /* a domain error occurs for infinite input (C 7.12.1 footnote 196). */
   /* Returns: the sine value. */

extern void __use_accurate_range_reduction(void);
   /* reference this to select the larger, slower, but more accurate */
   /* range reduction in sin, cos and tan */

extern __declspec(__nothrow) double tan(double /*x*/);
   /* computes the tangent of x (measured in radians). A large magnitude */
   /* argument may yield a result with little or no significance */
   /* Returns: the tangent value. */
   /*          if range error; returns HUGE_VAL. */

extern __declspec(__nothrow) double cosh(double /*x*/);
   /* computes the hyperbolic cosine of x. A range error occurs if the */
   /* magnitude of x is too large. */
   /* Returns: the hyperbolic cosine value. */
   /*          if range error; returns HUGE_VAL. */
extern __declspec(__nothrow) double sinh(double /*x*/);
   /* computes the hyperbolic sine of x. A range error occurs if the */
   /* magnitude of x is too large. */
   /* Returns: the hyperbolic sine value. */
   /*          if range error; returns -HUGE_VAL or HUGE_VAL depending */
   /*          on the sign of the argument */

extern __declspec(__nothrow) __attribute__((const)) double tanh(double /*x*/);
   /* computes the hyperbolic tangent of x. */
   /* Returns: the hyperbolic tangent value. */

extern __declspec(__nothrow) double exp(double /*x*/);
   /* computes the exponential function of x. A range error occurs if the */
   /* magnitude of x is too large. */
   /* Returns: the exponential value. */
   /*          if underflow range error; 0 is returned. */
   /*          if overflow range error; HUGE_VAL is returned. */

extern __declspec(__nothrow) double frexp(double /*value*/, int * /*exp*/) __attribute__((__nonnull__(2)));
   /* breaks a floating-point number into a normalised fraction and an */
   /* integral power of 2. It stores the integer in the int object pointed */
   /* to by exp. */
   /* Returns: the value x, such that x is a double with magnitude in the */
   /* interval 0.5 to 1.0 or zero, and value equals x times 2 raised to the */
   /* power *exp. If value is zero, both parts of the result are zero. */

extern __declspec(__nothrow) double ldexp(double /*x*/, int /*exp*/);
   /* multiplies a floating-point number by an integral power of 2. */
   /* A range error may occur. */
   /* Returns: the value of x times 2 raised to the power of exp. */
   /*          if range error; HUGE_VAL is returned. */
extern __declspec(__nothrow) double log(double /*x*/);
   /* computes the natural logarithm of x. A domain error occurs if the */
   /* argument is negative, and -HUGE_VAL is returned. A range error occurs */
   /* if the argument is zero. */
   /* Returns: the natural logarithm. */
   /*          if range error; -HUGE_VAL is returned. */
extern __declspec(__nothrow) double log10(double /*x*/);
   /* computes the base-ten logarithm of x. A domain error occurs if the */
   /* argument is negative. A range error occurs if the argument is zero. */
   /* Returns: the base-ten logarithm. */
extern __declspec(__nothrow) double modf(double /*value*/, double * /*iptr*/) __attribute__((__nonnull__(2)));
   /* breaks the argument value into integral and fraction parts, each of */
   /* which has the same sign as the argument. It stores the integral part */
   /* as a double in the object pointed to by iptr. */
   /* Returns: the signed fractional part of value. */

extern __declspec(__nothrow) double pow(double /*x*/, double /*y*/);
   /* computes x raised to the power of y. A domain error occurs if x is */
   /* zero and y is less than or equal to zero, or if x is negative and y */
   /* is not an integer, and -HUGE_VAL returned. A range error may occur. */
   /* Returns: the value of x raised to the power of y. */
   /*          if underflow range error; 0 is returned. */
   /*          if overflow range error; HUGE_VAL is returned. */
extern __declspec(__nothrow) double sqrt(double /*x*/);
   /* computes the non-negative square root of x. A domain error occurs */
   /* if the argument is negative, and -HUGE_VAL returned. */
   /* Returns: the value of the square root. */




    static __inline double _sqrt(double __x) { return sqrt(__x); }




    static __inline float _sqrtf(float __x) { return (float)sqrt(__x); }

    /* With VFP, _sqrt and _sqrtf should expand inline as the native VFP square root
     * instructions. They will not behave like the C sqrt() function, because
     * they will report unusual values as IEEE exceptions (in fpmodes which
     * support IEEE exceptions) rather than in errno. These function names
     * are not specified in any standard. */

extern __declspec(__nothrow) __attribute__((const)) double ceil(double /*x*/);
   /* computes the smallest integer not less than x. */
   /* Returns: the smallest integer not less than x, expressed as a double. */
extern __declspec(__nothrow) __attribute__((const)) double fabs(double /*x*/);
   /* computes the absolute value of the floating-point number x. */
   /* Returns: the absolute value of x. */

extern __declspec(__nothrow) __attribute__((const)) double floor(double /*d*/);
   /* computes the largest integer not greater than x. */
   /* Returns: the largest integer not greater than x, expressed as a double */

extern __declspec(__nothrow) double fmod(double /*x*/, double /*y*/);
   /* computes the floating-point remainder of x/y. */
   /* Returns: the value x - i * y, for some integer i such that, if y is */
   /*          nonzero, the result has the same sign as x and magnitude */
   /*          less than the magnitude of y. If y is zero, a domain error */
   /*          occurs and -HUGE_VAL is returned. */

    /* Additional Mathlib functions not defined by the ANSI standard.
     * Not guaranteed, and not necessarily very well tested.
     * C99 requires the user to include <math.h> to use these functions
     * declaring them "by hand" is not sufficient
     *
     * The above statement is not completely true now.  Some of the above
     * C99 functionality has been added as per the Standard, and (where
     * necessary) old Mathlib functionality withdrawn/changed.  Before
     * including this header #define __ENABLE_MATHLIB_LEGACY if you want to
     * re-enable the legacy functionality.
     */



extern __declspec(__nothrow) double acosh(double /*x*/);
    /*
     * Inverse cosh. EDOM if argument < 1.0
     */
extern __declspec(__nothrow) double asinh(double /*x*/);
    /*
     * Inverse sinh.
     */
extern __declspec(__nothrow) double atanh(double /*x*/);
    /*
     * Inverse tanh. EDOM if |argument| > 1.0
     */
extern __declspec(__nothrow) double cbrt(double /*x*/);
    /*
     * Cube root.
     */
static __inline __declspec(__nothrow) __attribute__((const)) double copysign(double __x, double __y)
    /*
     * Returns x with sign bit replaced by sign of y.
     */
{
    (*(1 + (unsigned *)&(__x))) = ((*(1 + (unsigned *)&(__x))) & 0x7fffffff) | ((*(1 + (unsigned *)&(__y))) & 0x80000000);
    return __x;
}
static __inline __declspec(__nothrow) __attribute__((const)) float copysignf(float __x, float __y)
    /*
     * Returns x with sign bit replaced by sign of y.
     */
{
    (*(unsigned *)&(__x)) = ((*(unsigned *)&(__x)) & 0x7fffffff) | ((*(unsigned *)&(__y)) & 0x80000000);
    return __x;
}
extern __declspec(__nothrow) double erf(double /*x*/);
    /*
     * Error function. (2/sqrt(pi)) * integral from 0 to x of exp(-t*t) dt.
     */
extern __declspec(__nothrow) double erfc(double /*x*/);
    /*
     * 1-erf(x). (More accurate than just coding 1-erf(x), for large x.)
     */
extern __declspec(__nothrow) double expm1(double /*x*/);
    /*
     * exp(x)-1. (More accurate than just coding exp(x)-1, for small x.)
     */



    /*
     * Classify a floating point number into one of the following values:
     */






# 479 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"


extern __declspec(__nothrow) double hypot(double /*x*/, double /*y*/);
    /*
     * sqrt(x*x+y*y), ie the length of the vector (x,y) or the
     * hypotenuse of a right triangle whose other two sides are x
     * and y. Won't overflow unless the _answer_ is too big, even
     * if the intermediate x*x+y*y is too big.
     */
extern __declspec(__nothrow) int ilogb(double /*x*/);
    /*
     * Exponent of x (returns 0 for 1.0, 1 for 2.0, -1 for 0.5, etc.)
     */
extern __declspec(__nothrow) int ilogbf(float /*x*/);
    /*
     * Like ilogb but takes a float
     */
extern __declspec(__nothrow) int ilogbl(long double /*x*/);
    /*
     * Exponent of x (returns 0 for 1.0, 1 for 2.0, -1 for 0.5, etc.)
     */







    /*
     * Returns true if x is a finite number, size independent.
     */





    /*
     * Returns true if x > y, throws no exceptions except on Signaling NaNs
     *
     * We want the C not set but the Z bit clear, V must be clear
     */





    /*
     * Returns true if x >= y, throws no exceptions except on Signaling NaNs
     *
     * We just need to see if the C bit is set or not and ensure V clear
     */





    /*
     * Returns true if x is an infinity, size independent.
     */





    /*
     * Returns true if x < y, throws no exceptions except on Signaling NaNs
     *
     * We're less than if N is set, V clear
     */





    /*
     * Returns true if x <= y, throws no exceptions except on Signaling NaNs
     *
     * We're less than or equal if one of N or Z is set, V clear
     */





    /*
     * Returns true if x <> y, throws no exceptions except on Signaling NaNs
     * Unfortunately this test is too complicated to do in a macro without
     * evaluating x & y twice.  Shame really...
     */





    /*
     * Returns TRUE if x is a NaN.
     */





    /*
     * Returns TRUE if x is a NaN.
     */





    /*
     * Returns true if x ? y, throws no exceptions except on Signaling NaNs
     * Unordered occurs if and only if the V bit is set
     */

extern __declspec(__nothrow) double lgamma (double /*x*/);
    /*
     * The log of the absolute value of the gamma function of x. The sign
     * of the gamma function of x is returned in the global `signgam'.
     */
extern __declspec(__nothrow) double log1p(double /*x*/);
    /*
     * log(1+x). (More accurate than just coding log(1+x), for small x.)
     */
extern __declspec(__nothrow) double logb(double /*x*/);
    /*
     * Like ilogb but returns a double.
     */
extern __declspec(__nothrow) float logbf(float /*x*/);
    /*
     * Like logb but takes and returns float
     */
extern __declspec(__nothrow) long double logbl(long double /*x*/);
    /*
     * Like logb but takes and returns long double
     */
extern __declspec(__nothrow) double nextafter(double /*x*/, double /*y*/);
    /*
     * Returns the next representable number after x, in the
     * direction toward y.
     */
extern __declspec(__nothrow) float nextafterf(float /*x*/, float /*y*/);
    /*
     * Returns the next representable number after x, in the
     * direction toward y.
     */
extern __declspec(__nothrow) long double nextafterl(long double /*x*/, long double /*y*/);
    /*
     * Returns the next representable number after x, in the
     * direction toward y.
     */
extern __declspec(__nothrow) double nexttoward(double /*x*/, long double /*y*/);
    /*
     * Returns the next representable number after x, in the
     * direction toward y.
     */
extern __declspec(__nothrow) float nexttowardf(float /*x*/, long double /*y*/);
    /*
     * Returns the next representable number after x, in the
     * direction toward y.
     */
extern __declspec(__nothrow) long double nexttowardl(long double /*x*/, long double /*y*/);
    /*
     * Returns the next representable number after x, in the
     * direction toward y.
     */
extern __declspec(__nothrow) double remainder(double /*x*/, double /*y*/);
    /*
     * Returns the remainder of x by y, in the IEEE 754 sense.
     */
extern __declspec(__nothrow) __attribute__((const)) double rint(double /*x*/);
    /*
     * Rounds x to an integer, in the IEEE 754 sense.
     */
extern __declspec(__nothrow) double scalbln(double /*x*/, long int /*n*/);
    /*
     * Compute x times 2^n quickly.
     */
extern __declspec(__nothrow) float scalblnf(float /*x*/, long int /*n*/);
    /*
     * Compute x times 2^n quickly.
     */
extern __declspec(__nothrow) long double scalblnl(long double /*x*/, long int /*n*/);
    /*
     * Compute x times 2^n quickly.
     */
extern __declspec(__nothrow) double scalbn(double /*x*/, int /*n*/);
    /*
     * Compute x times 2^n quickly.
     */
extern __declspec(__nothrow) float scalbnf(float /*x*/, int /*n*/);
    /*
     * Compute x times 2^n quickly.
     */
extern __declspec(__nothrow) long double scalbnl(long double /*x*/, int /*n*/);
    /*
     * Compute x times 2^n quickly.
     */




    /*
     * Returns the signbit of x, size independent macro
     */


/* C99 float versions of functions.  math.h has always reserved these
   identifiers for this purpose (7.13.4). */
extern __declspec(__nothrow) __attribute__((const)) float _fabsf(float); /* old ARM name */
static __inline __declspec(__nothrow) __attribute__((const)) float fabsf(float __f) { return _fabsf(__f); }
extern __declspec(__nothrow) float sinf(float /*x*/);
extern __declspec(__nothrow) float cosf(float /*x*/);
extern __declspec(__nothrow) float tanf(float /*x*/);
extern __declspec(__nothrow) float acosf(float /*x*/);
extern __declspec(__nothrow) float asinf(float /*x*/);
extern __declspec(__nothrow) float atanf(float /*x*/);
extern __declspec(__nothrow) float atan2f(float /*y*/, float /*x*/);
extern __declspec(__nothrow) float sinhf(float /*x*/);
extern __declspec(__nothrow) float coshf(float /*x*/);
extern __declspec(__nothrow) float tanhf(float /*x*/);
extern __declspec(__nothrow) float expf(float /*x*/);
extern __declspec(__nothrow) float logf(float /*x*/);
extern __declspec(__nothrow) float log10f(float /*x*/);
extern __declspec(__nothrow) float powf(float /*x*/, float /*y*/);
extern __declspec(__nothrow) float sqrtf(float /*x*/);
extern __declspec(__nothrow) float ldexpf(float /*x*/, int /*exp*/);
extern __declspec(__nothrow) float frexpf(float /*value*/, int * /*exp*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) __attribute__((const)) float ceilf(float /*x*/);
extern __declspec(__nothrow) __attribute__((const)) float floorf(float /*x*/);
extern __declspec(__nothrow) float fmodf(float /*x*/, float /*y*/);
extern __declspec(__nothrow) float modff(float /*value*/, float * /*iptr*/) __attribute__((__nonnull__(2)));

/* C99 long double versions of functions. */
/* (also need to have 'using' declarations below) */









/*
 * Long double versions of C89 functions can be defined
 * unconditionally, because C89 reserved these names in "future
 * library directions".
 */
__declspec(__nothrow) long double acosl(long double );
__declspec(__nothrow) long double asinl(long double );
__declspec(__nothrow) long double atanl(long double );
__declspec(__nothrow) long double atan2l(long double , long double );
__declspec(__nothrow) long double ceill(long double );
__declspec(__nothrow) long double cosl(long double );
__declspec(__nothrow) long double coshl(long double );
__declspec(__nothrow) long double expl(long double );
__declspec(__nothrow) long double fabsl(long double );
__declspec(__nothrow) long double floorl(long double );
__declspec(__nothrow) long double fmodl(long double , long double );
__declspec(__nothrow) long double frexpl(long double , int* ) __attribute__((__nonnull__(2)));
__declspec(__nothrow) long double ldexpl(long double , int );
__declspec(__nothrow) long double logl(long double );
__declspec(__nothrow) long double log10l(long double );
__declspec(__nothrow) long double modfl(long double /*x*/, long double * /*p*/) __attribute__((__nonnull__(2)));
__declspec(__nothrow) long double powl(long double , long double );
__declspec(__nothrow) long double sinl(long double );
__declspec(__nothrow) long double sinhl(long double );
__declspec(__nothrow) long double sqrtl(long double );
__declspec(__nothrow) long double tanl(long double );
__declspec(__nothrow) long double tanhl(long double );



/*
 * C99 float and long double versions of extra-C89 functions.
 */
extern __declspec(__nothrow) float acoshf(float /*x*/);
__declspec(__nothrow) long double acoshl(long double );
extern __declspec(__nothrow) float asinhf(float /*x*/);
__declspec(__nothrow) long double asinhl(long double );
extern __declspec(__nothrow) float atanhf(float /*x*/);
__declspec(__nothrow) long double atanhl(long double );
__declspec(__nothrow) long double copysignl(long double , long double );
extern __declspec(__nothrow) float cbrtf(float /*x*/);
__declspec(__nothrow) long double cbrtl(long double );
extern __declspec(__nothrow) float erff(float /*x*/);
__declspec(__nothrow) long double erfl(long double );
extern __declspec(__nothrow) float erfcf(float /*x*/);
__declspec(__nothrow) long double erfcl(long double );
extern __declspec(__nothrow) float expm1f(float /*x*/);
__declspec(__nothrow) long double expm1l(long double );
extern __declspec(__nothrow) float log1pf(float /*x*/);
__declspec(__nothrow) long double log1pl(long double );
extern __declspec(__nothrow) float hypotf(float /*x*/, float /*y*/);
__declspec(__nothrow) long double hypotl(long double , long double );
extern __declspec(__nothrow) float lgammaf(float /*x*/);
__declspec(__nothrow) long double lgammal(long double );
extern __declspec(__nothrow) float remainderf(float /*x*/, float /*y*/);
__declspec(__nothrow) long double remainderl(long double , long double );
extern __declspec(__nothrow) float rintf(float /*x*/);
__declspec(__nothrow) long double rintl(long double );



# 875 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"





# 896 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"

# 1087 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"











# 1317 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\math.h"





/* end of math.h */
# 22 "\\aud_sw\\AudioService\\src\\acm_audio_effect.c"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
/* stdlib.h: ANSI draft (X3J11 May 88) library header, section 4.10 */
/* Copyright (C) Codemist Ltd., 1988-1993.                          */
/* Copyright 1991-1998,2014 ARM Limited. All rights reserved.       */
/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: agrant $
 */
 
/*
 * stdlib.h declares four types, several general purpose functions,
 * and defines several macros.
 */






  /* armclang and non-strict armcc allow 'long long' in system headers */














  /*
   * Some of these declarations are new in C99.  To access them in C++
   * you can use -D__USE_C99_STDLIB (or -D__USE_C99ALL).
   */








# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 70 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"






   /* unconditional in non-strict C for consistency of debug info */



    typedef unsigned short wchar_t; /* see <stddef.h> */
# 91 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

typedef struct div_t { int quot, rem; } div_t;
   /* type of the value returned by the div function. */
typedef struct ldiv_t { long int quot, rem; } ldiv_t;
   /* type of the value returned by the ldiv function. */

typedef struct lldiv_t { long long quot, rem; } lldiv_t;
   /* type of the value returned by the lldiv function. */


# 112 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * an integral expression which may be used as an argument to the exit
    * function to return successful termination status to the host
    * environment.
    */

   /*
    * Defining __USE_ANSI_EXAMPLE_RAND at compile time switches to
    * the example implementation of rand() and srand() provided in
    * the ANSI C standard. This implementation is very poor, but is
    * provided for completeness.
    */
# 131 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"
   /*
    * RAND_MAX: an integral constant expression, the value of which
    * is the maximum value returned by the rand function.
    */
extern __declspec(__nothrow) int __aeabi_MB_CUR_MAX(void);

   /*
    * a positive integer expression whose value is the maximum number of bytes
    * in a multibyte character for the extended character set specified by the
    * current locale (category LC_CTYPE), and whose value is never greater
    * than MB_LEN_MAX.
    */

   /*
    * If the compiler supports signalling nans as per N965 then it
    * will define __SUPPORT_SNAN__, in which case a user may define
    * _WANT_SNAN in order to obtain a compliant version of the strtod
    * family of functions.
    */




extern __declspec(__nothrow) double atof(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) int atoi(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to int
    * representation.
    * Returns: the converted value.
    */
extern __declspec(__nothrow) long int atol(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation.
    * Returns: the converted value.
    */

extern __declspec(__nothrow) long long atoll(const char * /*nptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to
    * long long int representation.
    * Returns: the converted value.
    */


extern __declspec(__nothrow) double strtod(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to double
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling a
    * floating point constant; and a final string of one or more unrecognised
    * characters, including the terminating null character of the input string.
    * Then it attempts to convert the subject sequence to a floating point
    * number, and returns the result. A pointer to the final string is stored
    * in the object pointed to by endptr, provided that endptr is not a null
    * pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned. If the correct value is outside the range of
    *          representable values, plus or minus HUGE_VAL is returned
    *          (according to the sign of the value), and the value of the macro
    *          ERANGE is stored in errno. If the correct value would cause
    *          underflow, zero is returned and the value of the macro ERANGE is
    *          stored in errno.
    */

extern __declspec(__nothrow) float strtof(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
extern __declspec(__nothrow) long double strtold(const char * __restrict /*nptr*/, char ** __restrict /*endptr*/) __attribute__((__nonnull__(1)));
   /*
    * same as strtod, but return float and long double respectively.
    */

extern __declspec(__nothrow) long int strtol(const char * __restrict /*nptr*/,
                        char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to long int
    * representation. First it decomposes the input string into three parts:
    * an initial, possibly empty, sequence of white-space characters (as
    * specified by the isspace function), a subject sequence resembling an
    * integer represented in some radix determined by the value of base, and a
    * final string of one or more unrecognised characters, including the
    * terminating null character of the input string. Then it attempts to
    * convert the subject sequence to an integer, and returns the result.
    * If the value of base is 0, the expected form of the subject sequence is
    * that of an integer constant (described in ANSI Draft, section 3.1.3.2),
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. If the value of base is between 2 and 36, the expected form of
    * the subject sequence is a sequence of letters and digits representing an
    * integer with the radix specified by base, optionally preceded by a plus
    * or minus sign, but not including an integer suffix. The letters from a
    * (or A) through z (or Z) are ascribed the values 10 to 35; only letters
    * whose ascribed values are less than that of the base are permitted. If
    * the value of base is 16, the characters 0x or 0X may optionally precede
    * the sequence of letters and digits following the sign if present.
    * A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, LONG_MAX or LONG_MIN is returned
    *          (according to the sign of the value), and the value of the
    *          macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long int strtoul(const char * __restrict /*nptr*/,
                                       char ** __restrict /*endptr*/, int /*base*/) __attribute__((__nonnull__(1)));
   /*
    * converts the initial part of the string pointed to by nptr to unsigned
    * long int representation. First it decomposes the input string into three
    * parts: an initial, possibly empty, sequence of white-space characters (as
    * determined by the isspace function), a subject sequence resembling an
    * unsigned integer represented in some radix determined by the value of
    * base, and a final string of one or more unrecognised characters,
    * including the terminating null character of the input string. Then it
    * attempts to convert the subject sequence to an unsigned integer, and
    * returns the result. If the value of base is zero, the expected form of
    * the subject sequence is that of an integer constant (described in ANSI
    * Draft, section 3.1.3.2), optionally preceded by a '+' or '-' sign, but
    * not including an integer suffix. If the value of base is between 2 and
    * 36, the expected form of the subject sequence is a sequence of letters
    * and digits representing an integer with the radix specified by base,
    * optionally preceded by a '+' or '-' sign, but not including an integer
    * suffix. The letters from a (or A) through z (or Z) stand for the values
    * 10 to 35; only letters whose ascribed values are less than that of the
    * base are permitted. If the value of base is 16, the characters 0x or 0X
    * may optionally precede the sequence of letters and digits following the
    * sign, if present. A pointer to the final string is stored in the object
    * pointed to by endptr, provided that endptr is not a null pointer.
    * Returns: the converted value if any. If no conversion could be performed,
    *          zero is returned and nptr is stored in *endptr.
    *          If the correct value is outside the range of
    *          representable values, ULONG_MAX is returned, and the value of
    *          the macro ERANGE is stored in errno.
    */

/* C90 reserves all names beginning with 'str' */
extern __declspec(__nothrow) long long strtoll(const char * __restrict /*nptr*/,
                                  char ** __restrict /*endptr*/, int /*base*/)
                          __attribute__((__nonnull__(1)));
   /*
    * as strtol but returns a long long int value.  If the correct value is
    * outside the range of representable values,  LLONG_MAX or LLONG_MIN is
    * returned (according to the sign of the value), and the value of the
    * macro ERANGE is stored in errno.
    */
extern __declspec(__nothrow) unsigned long long strtoull(const char * __restrict /*nptr*/,
                                            char ** __restrict /*endptr*/, int /*base*/)
                                   __attribute__((__nonnull__(1)));
   /*
    * as strtoul but returns an unsigned long long int value.  If the correct
    * value is outside the range of representable values, ULLONG_MAX is returned,
    * and the value of the macro ERANGE is stored in errno.
    */

extern __declspec(__nothrow) int rand(void);
   /*
    * Computes a sequence of pseudo-random integers in the range 0 to RAND_MAX.
    * Uses an additive generator (Mitchell & Moore) of the form:
    *   Xn = (X[n-24] + X[n-55]) MOD 2^31
    * This is described in section 3.2.2 of Knuth, vol 2. It's period is
    * in excess of 2^55 and its randomness properties, though unproven, are
    * conjectured to be good. Empirical testing since 1958 has shown no flaws.
    * Returns: a pseudo-random integer.
    */
extern __declspec(__nothrow) void srand(unsigned int /*seed*/);
   /*
    * uses its argument as a seed for a new sequence of pseudo-random numbers
    * to be returned by subsequent calls to rand. If srand is then called with
    * the same seed value, the sequence of pseudo-random numbers is repeated.
    * If rand is called before any calls to srand have been made, the same
    * sequence is generated as when srand is first called with a seed value
    * of 1.
    */

struct _rand_state { int __x[57]; };
extern __declspec(__nothrow) int _rand_r(struct _rand_state *);
extern __declspec(__nothrow) void _srand_r(struct _rand_state *, unsigned int);
struct _ANSI_rand_state { int __x[1]; };
extern __declspec(__nothrow) int _ANSI_rand_r(struct _ANSI_rand_state *);
extern __declspec(__nothrow) void _ANSI_srand_r(struct _ANSI_rand_state *, unsigned int);
   /*
    * Re-entrant variants of both flavours of rand, which operate on
    * an explicitly supplied state buffer.
    */

extern __declspec(__nothrow) void *calloc(size_t /*nmemb*/, size_t /*size*/);
   /*
    * allocates space for an array of nmemb objects, each of whose size is
    * 'size'. The space is initialised to all bits zero.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void free(void * /*ptr*/);
   /*
    * causes the space pointed to by ptr to be deallocated (i.e., made
    * available for further allocation). If ptr is a null pointer, no action
    * occurs. Otherwise, if ptr does not match a pointer earlier returned by
    * calloc, malloc or realloc or if the space has been deallocated by a call
    * to free or realloc, the behaviour is undefined.
    */
extern __declspec(__nothrow) void *malloc(size_t /*size*/);
   /*
    * allocates space for an object whose size is specified by 'size' and whose
    * value is indeterminate.
    * Returns: either a null pointer or a pointer to the allocated space.
    */
extern __declspec(__nothrow) void *realloc(void * /*ptr*/, size_t /*size*/);
   /*
    * changes the size of the object pointed to by ptr to the size specified by
    * size. The contents of the object shall be unchanged up to the lesser of
    * the new and old sizes. If the new size is larger, the value of the newly
    * allocated portion of the object is indeterminate. If ptr is a null
    * pointer, the realloc function behaves like a call to malloc for the
    * specified size. Otherwise, if ptr does not match a pointer earlier
    * returned by calloc, malloc or realloc, or if the space has been
    * deallocated by a call to free or realloc, the behaviour is undefined.
    * If the space cannot be allocated, the object pointed to by ptr is
    * unchanged. If size is zero and ptr is not a null pointer, the object it
    * points to is freed.
    * Returns: either a null pointer or a pointer to the possibly moved
    *          allocated space.
    */

extern __declspec(__nothrow) int posix_memalign(void ** /*ret*/, size_t /*alignment*/, size_t /*size*/);
   /*
    * allocates space for an object of size 'size', aligned to a
    * multiple of 'alignment' (which must be a power of two and at
    * least 4).
    *
    * On success, a pointer to the allocated object is stored in
    * *ret, and zero is returned. On failure, the return value is
    * either ENOMEM (allocation failed because no suitable piece of
    * memory was available) or EINVAL (the 'alignment' parameter was
    * invalid).
    */

typedef int (*__heapprt)(void *, char const *, ...);
extern __declspec(__nothrow) void __heapstats(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                        void * /*param*/) __attribute__((__nonnull__(1)));
   /*
    * reports current heap statistics (eg. number of free blocks in
    * the free-list). Output is as implementation-defined free-form
    * text, provided via the dprint function. `param' gives an
    * extra data word to pass to dprint. You can call
    * __heapstats(fprintf,stdout) by casting fprintf to the above
    * function type; the typedef `__heapprt' is provided for this
    * purpose.
    *
    * `dprint' will not be called while the heap is being examined,
    * so it can allocate memory itself without trouble.
    */
extern __declspec(__nothrow) int __heapvalid(int (* /*dprint*/)(void * /*param*/,
                                           char const * /*format*/, ...),
                       void * /*param*/, int /*verbose*/) __attribute__((__nonnull__(1)));
   /*
    * performs a consistency check on the heap. Errors are reported
    * through dprint, like __heapstats. If `verbose' is nonzero,
    * full diagnostic information on the heap state is printed out.
    *
    * This routine probably won't work if the heap isn't a
    * contiguous chunk (for example, if __user_heap_extend has been
    * overridden).
    *
    * `dprint' may be called while the heap is being examined or
    * even in an invalid state, so it must perform no memory
    * allocation. In particular, if `dprint' calls (or is) a stdio
    * function, the stream it outputs to must already have either
    * been written to or been setvbuf'ed, or else the system will
    * allocate buffer space for it on the first call to dprint.
    */
extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
   /*
    * causes abnormal program termination to occur, unless the signal SIGABRT
    * is being caught and the signal handler does not return. Whether open
    * output streams are flushed or open streams are closed or temporary
    * files removed is implementation-defined.
    * An implementation-defined form of the status 'unsuccessful termination'
    * is returned to the host environment by means of a call to
    * raise(SIGABRT).
    */

extern __declspec(__nothrow) int atexit(void (* /*func*/)(void)) __attribute__((__nonnull__(1)));
   /*
    * registers the function pointed to by func, to be called without its
    * arguments at normal program termination. It is possible to register at
    * least 32 functions.
    * Returns: zero if the registration succeeds, nonzero if it fails.
    */
# 436 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern __declspec(__nothrow) __declspec(__noreturn) void exit(int /*status*/);
   /*
    * causes normal program termination to occur. If more than one call to the
    * exit function is executed by a program, the behaviour is undefined.
    * First, all functions registered by the atexit function are called, in the
    * reverse order of their registration.
    * Next, all open output streams are flushed, all open streams are closed,
    * and all files created by the tmpfile function are removed.
    * Finally, control is returned to the host environment. If the value of
    * status is zero or EXIT_SUCCESS, an implementation-defined form of the
    * status 'successful termination' is returned. If the value of status is
    * EXIT_FAILURE, an implementation-defined form of the status
    * 'unsuccessful termination' is returned. Otherwise the status returned
    * is implementation-defined.
    */

extern __declspec(__nothrow) __declspec(__noreturn) void _Exit(int /*status*/);
   /*
    * causes normal program termination to occur. No functions registered
    * by the atexit function are called.
    * In this implementation, all open output streams are flushed, all
    * open streams are closed, and all files created by the tmpfile function
    * are removed.
    * Control is returned to the host environment. The status returned to
    * the host environment is determined in the same way as for 'exit'.
    */     

extern __declspec(__nothrow) char *getenv(const char * /*name*/) __attribute__((__nonnull__(1)));
   /*
    * searches the environment list, provided by the host environment, for a
    * string that matches the string pointed to by name. The set of environment
    * names and the method for altering the environment list are
    * implementation-defined.
    * Returns: a pointer to a string associated with the matched list member.
    *          The array pointed to shall not be modified by the program, but
    *          may be overwritten by a subsequent call to the getenv function.
    *          If the specified name cannot be found, a null pointer is
    *          returned.
    */

extern __declspec(__nothrow) int  system(const char * /*string*/);
   /*
    * passes the string pointed to by string to the host environment to be
    * executed by a command processor in an implementation-defined manner.
    * A null pointer may be used for string, to inquire whether a command
    * processor exists.
    *
    * Returns: If the argument is a null pointer, the system function returns
    *          non-zero only if a command processor is available. If the
    *          argument is not a null pointer, the system function returns an
    *          implementation-defined value.
    */

extern  void *bsearch(const void * /*key*/, const void * /*base*/,
              size_t /*nmemb*/, size_t /*size*/,
              int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,2,5)));
   /*
    * searches an array of nmemb objects, the initial member of which is
    * pointed to by base, for a member that matches the object pointed to by
    * key. The size of each member of the array is specified by size.
    * The contents of the array shall be in ascending sorted order according to
    * a comparison function pointed to by compar, which is called with two
    * arguments that point to the key object and to an array member, in that
    * order. The function shall return an integer less than, equal to, or
    * greater than zero if the key object is considered, respectively, to be
    * less than, to match, or to be greater than the array member.
    * Returns: a pointer to a matching member of the array, or a null pointer
    *          if no match is found. If two members compare as equal, which
    *          member is matched is unspecified.
    */
# 524 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


extern  void qsort(void * /*base*/, size_t /*nmemb*/, size_t /*size*/,
           int (* /*compar*/)(const void *, const void *)) __attribute__((__nonnull__(1,4)));
   /*
    * sorts an array of nmemb objects, the initial member of which is pointed
    * to by base. The size of each object is specified by size.
    * The contents of the array shall be in ascending order according to a
    * comparison function pointed to by compar, which is called with two
    * arguments that point to the objects being compared. The function shall
    * return an integer less than, equal to, or greater than zero if the first
    * argument is considered to be respectively less than, equal to, or greater
    * than the second. If two members compare as equal, their order in the
    * sorted array is unspecified.
    */

# 553 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"

extern __declspec(__nothrow) __attribute__((const)) int abs(int /*j*/);
   /*
    * computes the absolute value of an integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */

extern __declspec(__nothrow) __attribute__((const)) div_t div(int /*numer*/, int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the resulting
    * quotient is the integer of lesser magnitude that is the nearest to the
    * algebraic quotient. If the result cannot be represented, the behaviour is
    * undefined; otherwise, quot * denom + rem shall equal numer.
    * Returns: a structure of type div_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          int quot; int rem;
    */
extern __declspec(__nothrow) __attribute__((const)) long int labs(long int /*j*/);
   /*
    * computes the absolute value of an long integer j. If the result cannot be
    * represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) ldiv_t ldiv(long int /*numer*/, long int /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type ldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long int quot; long int rem;
    */







extern __declspec(__nothrow) __attribute__((const)) long long llabs(long long /*j*/);
   /*
    * computes the absolute value of a long long integer j. If the
    * result cannot be represented, the behaviour is undefined.
    * Returns: the absolute value.
    */




extern __declspec(__nothrow) __attribute__((const)) lldiv_t lldiv(long long /*numer*/, long long /*denom*/);
   /*
    * computes the quotient and remainder of the division of the numerator
    * numer by the denominator denom. If the division is inexact, the sign of
    * the resulting quotient is that of the algebraic quotient, and the
    * magnitude of the resulting quotient is the largest integer less than the
    * magnitude of the algebraic quotient. If the result cannot be represented,
    * the behaviour is undefined; otherwise, quot * denom + rem shall equal
    * numer.
    * Returns: a structure of type lldiv_t, comprising both the quotient and the
    *          remainder. the structure shall contain the following members,
    *          in either order.
    *          long long quot; long long rem;
    */
# 634 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"


/*
 * ARM real-time divide functions for guaranteed performance
 */
typedef struct __sdiv32by16 { int quot, rem; } __sdiv32by16;
typedef struct __udiv32by16 { unsigned int quot, rem; } __udiv32by16;
   /* used int so that values return in separate regs, although 16-bit */
typedef struct __sdiv64by32 { int rem, quot; } __sdiv64by32;

__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv32by16 __rt_sdiv32by16(
     int /*numer*/,
     short int /*denom*/);
   /*
    * Signed divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __udiv32by16 __rt_udiv32by16(
     unsigned int /*numer*/,
     unsigned short /*denom*/);
   /*
    * Unsigned divide: (16-bit quot), (16-bit rem) = (32-bit) / (16-bit)
    */
__value_in_regs extern __declspec(__nothrow) __attribute__((const)) __sdiv64by32 __rt_sdiv64by32(
     int /*numer_h*/, unsigned int /*numer_l*/,
     int /*denom*/);
   /*
    * Signed divide: (32-bit quot), (32-bit rem) = (64-bit) / (32-bit)
    */


/*
 * ARM floating-point mask/status function (for both hardfp and softfp)
 */
extern __declspec(__nothrow) unsigned int __fp_status(unsigned int /*mask*/, unsigned int /*flags*/);
   /*
    * mask and flags are bit-fields which correspond directly to the
    * floating point status register in the FPE/FPA and fplib.  
    * __fp_status returns the current value of the status register,
    * and also sets the writable bits of the word
    * (the exception control and flag bytes) to:
    *
    *     new = (old & ~mask) ^ flags;
    */












/*
 * Multibyte Character Functions.
 * The behaviour of the multibyte character functions is affected by the
 * LC_CTYPE category of the current locale. For a state-dependent encoding,
 * each function is placed into its initial state by a call for which its
 * character pointer argument, s, is a null pointer. Subsequent calls with s
 * as other than a null pointer cause the internal state of the function to be
 * altered as necessary. A call with s as a null pointer causes these functions
 * to return a nonzero value if encodings have state dependency, and a zero
 * otherwise. After the LC_CTYPE category is changed, the shift state of these
 * functions is indeterminate.
 */
extern __declspec(__nothrow) int mblen(const char * /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mblen function determines the number of
    * bytes compromising the multibyte character pointed to by s. Except that
    * the shift state of the mbtowc function is not affected, it is equivalent
    * to   mbtowc((wchar_t *)0, s, n);
    * Returns: If s is a null pointer, the mblen function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mblen function either returns a 0 (if s points to a
    *          null character), or returns the number of bytes that compromise
    *          the multibyte character (if the next n of fewer bytes form a
    *          valid multibyte character), or returns -1 (they do not form a
    *          valid multibyte character).
    */
extern __declspec(__nothrow) int mbtowc(wchar_t * __restrict /*pwc*/,
                   const char * __restrict /*s*/, size_t /*n*/);
   /*
    * If s is not a null pointer, the mbtowc function determines the number of
    * bytes that compromise the multibyte character pointed to by s. It then
    * determines the code for value of type wchar_t that corresponds to that
    * multibyte character. (The value of the code corresponding to the null
    * character is zero). If the multibyte character is valid and pwc is not a
    * null pointer, the mbtowc function stores the code in the object pointed
    * to by pwc. At most n bytes of the array pointed to by s will be examined.
    * Returns: If s is a null pointer, the mbtowc function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the mbtowc function either returns a 0 (if s points to
    *          a null character), or returns the number of bytes that
    *          compromise the converted multibyte character (if the next n of
    *          fewer bytes form a valid multibyte character), or returns -1
    *          (they do not form a valid multibyte character).
    */
extern __declspec(__nothrow) int wctomb(char * /*s*/, wchar_t /*wchar*/);
   /*
    * determines the number of bytes need to represent the multibyte character
    * corresponding to the code whose value is wchar (including any change in
    * shift state). It stores the multibyte character representation in the
    * array object pointed to by s (if s is not a null pointer). At most
    * MB_CUR_MAX characters are stored. If the value of wchar is zero, the
    * wctomb function is left in the initial shift state).
    * Returns: If s is a null pointer, the wctomb function returns a nonzero or
    *          zero value, if multibyte character encodings, respectively, do
    *          or do not have state-dependent encodings. If s is not a null
    *          pointer, the wctomb function returns a -1 if the value of wchar
    *          does not correspond to a valid multibyte character, or returns
    *          the number of bytes that compromise the multibyte character
    *          corresponding to the value of wchar.
    */

/*
 * Multibyte String Functions.
 * The behaviour of the multibyte string functions is affected by the LC_CTYPE
 * category of the current locale.
 */
extern __declspec(__nothrow) size_t mbstowcs(wchar_t * __restrict /*pwcs*/,
                      const char * __restrict /*s*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of multibyte character that begins in the initial
    * shift state from the array pointed to by s into a sequence of
    * corresponding codes and stores not more than n codes into the array
    * pointed to by pwcs. No multibyte character that follow a null character
    * (which is converted into a code with value zero) will be examined or
    * converted. Each multibyte character is converted as if by a call to
    * mbtowc function, except that the shift state of the mbtowc function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by pwcs. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If an invalid multibyte character is encountered, the mbstowcs
    *          function returns (size_t)-1. Otherwise, the mbstowcs function
    *          returns the number of array elements modified, not including
    *          a terminating zero code, if any.
    */
extern __declspec(__nothrow) size_t wcstombs(char * __restrict /*s*/,
                      const wchar_t * __restrict /*pwcs*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * converts a sequence of codes that correspond to multibyte characters
    * from the array pointed to by pwcs into a sequence of multibyte
    * characters that begins in the initial shift state and stores these
    * multibyte characters into the array pointed to by s, stopping if a
    * multibyte character would exceed the limit of n total bytes or if a
    * null character is stored. Each code is converted as if by a call to the
    * wctomb function, except that the shift state of the wctomb function is
    * not affected. No more than n elements will be modified in the array
    * pointed to by s. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: If a code is encountered that does not correspond to a valid
    *          multibyte character, the wcstombs function returns (size_t)-1.
    *          Otherwise, the wcstombs function returns the number of bytes
    *          modified, not including a terminating null character, if any.
    */

extern __declspec(__nothrow) void __use_realtime_heap(void);
extern __declspec(__nothrow) void __use_realtime_division(void);
extern __declspec(__nothrow) void __use_two_region_memory(void);
extern __declspec(__nothrow) void __use_no_heap(void);
extern __declspec(__nothrow) void __use_no_heap_region(void);

extern __declspec(__nothrow) char const *__C_library_version_string(void);
extern __declspec(__nothrow) int __C_library_version_number(void);











# 892 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\stdlib.h"





/* end of stdlib.h */
# 23 "\\aud_sw\\AudioService\\src\\acm_audio_effect.c"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
/* string.h: ANSI 'C' (X3J11 Oct 88) library header, section 4.11 */
/* Copyright (C) Codemist Ltd., 1988-1993.                        */
/* Copyright 1991-1993 ARM Limited. All rights reserved.          */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 */

/*
 * string.h declares one type and several functions, and defines one macro
 * useful for manipulating character arrays and other objects treated as
 * character arrays. Various methods are used for determining the lengths of
 * the arrays, but in all cases a char * or void * argument points to the
 * initial (lowest addresses) character of the array. If an array is written
 * beyond the end of an object, the behaviour is undefined.
 */












# 38 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"


 /* unconditional in C++ and non-strict C for consistency of debug info */



    typedef unsigned int size_t;   /* see <stddef.h> */
# 54 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"




extern __declspec(__nothrow) void *memcpy(void * __restrict /*s1*/,
                    const void * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) void *memmove(void * /*s1*/,
                    const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies n characters from the object pointed to by s2 into the object
    * pointed to by s1. Copying takes place as if the n characters from the
    * object pointed to by s2 are first copied into a temporary array of n
    * characters that does not overlap the objects pointed to by s1 and s2,
    * and then the n characters from the temporary array are copied into the
    * object pointed to by s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strcpy(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string pointed to by s2 (including the terminating nul
    * character) into the array pointed to by s1. If copying takes place
    * between objects that overlap, the behaviour is undefined.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncpy(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies not more than n characters (characters that follow a null
    * character are not copied) from the array pointed to by s2 into the array
    * pointed to by s1. If copying takes place between objects that overlap,
    * the behaviour is undefined.
    * Returns: the value of s1.
    */

extern __declspec(__nothrow) char *strcat(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends a copy of the string pointed to by s2 (including the terminating
    * null character) to the end of the string pointed to by s1. The initial
    * character of s2 overwrites the null character at the end of s1.
    * Returns: the value of s1.
    */
extern __declspec(__nothrow) char *strncat(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * appends not more than n characters (a null character and characters that
    * follow it are not appended) from the array pointed to by s2 to the end of
    * the string pointed to by s1. The initial character of s2 overwrites the
    * null character at the end of s1. A terminating null character is always
    * appended to the result.
    * Returns: the value of s1.
    */

/*
 * The sign of a nonzero value returned by the comparison functions is
 * determined by the sign of the difference between the values of the first
 * pair of characters (both interpreted as unsigned char) that differ in the
 * objects being compared.
 */

extern __declspec(__nothrow) int memcmp(const void * /*s1*/, const void * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the first n characters of the object pointed to by s1 to the
    * first n characters of the object pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the object pointed to by s1 is greater than, equal to, or
    *          less than the object pointed to by s2.
    */
extern __declspec(__nothrow) int strcmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcasecmp(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2,
    * case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strncasecmp(const char * /*s1*/, const char * /*s2*/, size_t /*n*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares not more than n characters (characters that follow a null
    * character are not compared) from the array pointed to by s1 to the array
    * pointed to by s2, case-insensitively as defined by the current locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2.
    */
extern __declspec(__nothrow) int strcoll(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * compares the string pointed to by s1 to the string pointed to by s2, both
    * interpreted as appropriate to the LC_COLLATE category of the current
    * locale.
    * Returns: an integer greater than, equal to, or less than zero, according
    *          as the string pointed to by s1 is greater than, equal to, or
    *          less than the string pointed to by s2 when both are interpreted
    *          as appropriate to the current locale.
    */

extern __declspec(__nothrow) size_t strxfrm(char * __restrict /*s1*/, const char * __restrict /*s2*/, size_t /*n*/) __attribute__((__nonnull__(2)));
   /*
    * transforms the string pointed to by s2 and places the resulting string
    * into the array pointed to by s1. The transformation function is such that
    * if the strcmp function is applied to two transformed strings, it returns
    * a value greater than, equal to or less than zero, corresponding to the
    * result of the strcoll function applied to the same two original strings.
    * No more than n characters are placed into the resulting array pointed to
    * by s1, including the terminating null character. If n is zero, s1 is
    * permitted to be a null pointer. If copying takes place between objects
    * that overlap, the behaviour is undefined.
    * Returns: The length of the transformed string is returned (not including
    *          the terminating null character). If the value returned is n or
    *          more, the contents of the array pointed to by s1 are
    *          indeterminate.
    */


# 193 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) void *memchr(const void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an unsigned char) in the
    * initial n characters (each interpreted as unsigned char) of the object
    * pointed to by s.
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the object.
    */

# 209 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the first occurence of c (converted to an char) in the string
    * pointed to by s (including the terminating null character).
    * Returns: a pointer to the located character, or a null pointer if the
    *          character does not occur in the string.
    */

extern __declspec(__nothrow) size_t strcspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters not from the string pointed to by
    * s2. The terminating null character is not considered part of s2.
    * Returns: the length of the segment.
    */

# 232 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strpbrk(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of any
    * character from the string pointed to by s2.
    * Returns: returns a pointer to the character, or a null pointer if no
    *          character form s2 occurs in s1.
    */

# 247 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strrchr(const char * /*s*/, int /*c*/) __attribute__((__nonnull__(1)));

   /*
    * locates the last occurence of c (converted to a char) in the string
    * pointed to by s. The terminating null character is considered part of
    * the string.
    * Returns: returns a pointer to the character, or a null pointer if c does
    *          not occur in the string.
    */

extern __declspec(__nothrow) size_t strspn(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));
   /*
    * computes the length of the initial segment of the string pointed to by s1
    * which consists entirely of characters from the string pointed to by S2
    * Returns: the length of the segment.
    */

# 270 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"
extern __declspec(__nothrow) char *strstr(const char * /*s1*/, const char * /*s2*/) __attribute__((__nonnull__(1,2)));

   /*
    * locates the first occurence in the string pointed to by s1 of the
    * sequence of characters (excluding the terminating null character) in the
    * string pointed to by s2.
    * Returns: a pointer to the located string, or a null pointer if the string
    *          is not found.
    */

extern __declspec(__nothrow) char *strtok(char * __restrict /*s1*/, const char * __restrict /*s2*/) __attribute__((__nonnull__(2)));
extern __declspec(__nothrow) char *_strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

extern __declspec(__nothrow) char *strtok_r(char * /*s1*/, const char * /*s2*/, char ** /*ptr*/) __attribute__((__nonnull__(2,3)));

   /*
    * A sequence of calls to the strtok function breaks the string pointed to
    * by s1 into a sequence of tokens, each of which is delimited by a
    * character from the string pointed to by s2. The first call in the
    * sequence has s1 as its first argument, and is followed by calls with a
    * null pointer as their first argument. The separator string pointed to by
    * s2 may be different from call to call.
    * The first call in the sequence searches for the first character that is
    * not contained in the current separator string s2. If no such character
    * is found, then there are no tokens in s1 and the strtok function returns
    * a null pointer. If such a character is found, it is the start of the
    * first token.
    * The strtok function then searches from there for a character that is
    * contained in the current separator string. If no such character is found,
    * the current token extends to the end of the string pointed to by s1, and
    * subsequent searches for a token will fail. If such a character is found,
    * it is overwritten by a null character, which terminates the current
    * token. The strtok function saves a pointer to the following character,
    * from which the next search for a token will start.
    * Each subsequent call, with a null pointer as the value for the first
    * argument, starts searching from the saved pointer and behaves as
    * described above.
    * Returns: pointer to the first character of a token, or a null pointer if
    *          there is no token.
    *
    * strtok_r() is a common extension which works exactly like
    * strtok(), but instead of storing its state in a hidden
    * library variable, requires the user to pass in a pointer to a
    * char * variable which will be used instead. Any sequence of
    * calls to strtok_r() passing the same char ** pointer should
    * behave exactly like the corresponding sequence of calls to
    * strtok(). This means that strtok_r() can safely be used in
    * multi-threaded programs, and also that you can tokenise two
    * strings in parallel.
    */

extern __declspec(__nothrow) void *memset(void * /*s*/, int /*c*/, size_t /*n*/) __attribute__((__nonnull__(1)));
   /*
    * copies the value of c (converted to an unsigned char) into each of the
    * first n charactes of the object pointed to by s.
    * Returns: the value of s.
    */
extern __declspec(__nothrow) char *strerror(int /*errnum*/);
   /*
    * maps the error number in errnum to an error message string.
    * Returns: a pointer to the string, the contents of which are
    *          implementation-defined. The array pointed to shall not be
    *          modified by the program, but may be overwritten by a
    *          subsequent call to the strerror function.
    */
extern __declspec(__nothrow) size_t strlen(const char * /*s*/) __attribute__((__nonnull__(1)));
   /*
    * computes the length of the string pointed to by s.
    * Returns: the number of characters that precede the terminating null
    *          character.
    */

extern __declspec(__nothrow) size_t strlcpy(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * copies the string src into the string dst, using no more than
    * len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src. Thus, the operation
    * succeeded without truncation if and only if ret < len;
    * otherwise, the value in ret tells you how big to make dst if
    * you decide to reallocate it. (That value does _not_ include
    * the NUL.)
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) size_t strlcat(char * /*dst*/, const char * /*src*/, size_t /*len*/) __attribute__((__nonnull__(1,2)));
   /*
    * concatenates the string src to the string dst, using no more
    * than len bytes of dst. Always null-terminates dst _within the
    * length len (i.e. will copy at most len-1 bytes of string plus
    * a NUL), unless len is actually zero.
    * 
    * Return value is the length of the string that _would_ have
    * been written, i.e. the length of src plus the original length
    * of dst. Thus, the operation succeeded without truncation if
    * and only if ret < len; otherwise, the value in ret tells you
    * how big to make dst if you decide to reallocate it. (That
    * value does _not_ include the NUL.)
    * 
    * If no NUL is encountered within the first len bytes of dst,
    * then the length of dst is considered to have been equal to
    * len for the purposes of the return value (as if there were a
    * NUL at dst[len]). Thus, the return value in this case is len
    * + strlen(src).
    * 
    * This is a BSD-derived library extension, which we are
    * permitted to declare in a standard header because ISO defines
    * function names beginning with 'str' as reserved for future
    * expansion of <string.h>.
    */

extern __declspec(__nothrow) void _membitcpybl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpybb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpyhb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitcpywb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovebb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovehb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewl(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
extern __declspec(__nothrow) void _membitmovewb(void * /*dest*/, const void * /*src*/, int /*destoffset*/, int /*srcoffset*/, size_t /*nbits*/) __attribute__((__nonnull__(1,2)));
    /*
     * Copies or moves a piece of memory from one place to another,
     * with one-bit granularity. So you can start or finish a copy
     * part way through a byte, and you can copy between regions
     * with different alignment within a byte.
     * 
     * All these functions have the same prototype: two void *
     * pointers for destination and source, then two integers
     * giving the bit offset from those pointers, and finally the
     * number of bits to copy.
     * 
     * Just like memcpy and memmove, the "cpy" functions copy as
     * fast as they can in the assumption that the memory regions
     * do not overlap, while the "move" functions cope correctly
     * with overlap.
     *
     * Treating memory as a stream of individual bits requires
     * defining a convention about what order those bits are
     * considered to be arranged in. The above functions support
     * multiple conventions:
     * 
     *  - the "bl" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in little-endian fashion, so that the LSB comes
     *    first. (For example, membitcpybl(a,b,0,7,1) would copy
     *    the MSB of the byte at b to the LSB of the byte at a.)
     * 
     *  - the "bb" functions consider the unit of memory to be the
     *    byte, and consider the bits within each byte to be
     *    arranged in big-endian fashion, so that the MSB comes
     *    first.
     * 
     *  - the "hl" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in little-endian fashion.
     * 
     *  - the "hb" functions consider the unit of memory to be the
     *    16-bit halfword, and consider the bits within each word
     *    to be arranged in big-endian fashion.
     * 
     *  - the "wl" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in little-endian fashion.
     * 
     *  - the "wb" functions consider the unit of memory to be the
     *    32-bit word, and consider the bits within each word to be
     *    arranged in big-endian fashion.
     */







# 502 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\string.h"



/* end of string.h */

# 24 "\\aud_sw\\AudioService\\src\\acm_audio_effect.c"
# 1 "\\aud_sw\\AuC\\inc\\basic_op_audio.h"
#pragma once
# 1 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
/* dspfns.h
 *
 * Copyright 2001 ARM Limited. All rights reserved.
 *
 * RCS $Revision: 149794 $
 * Checkin $Date: 2009-11-26 17:03:19 +0000 (Thu, 26 Nov 2009) $
 * Revising $Author: agrant $
 */

/* ----------------------------------------------------------------------
 * This header file provides a set of DSP-type primitive
 * operations, such as 16-bit and 32-bit saturating arithmetic. The
 * operations it provides are similar to the ones used by the ITU
 * for publishing specifications of DSP algorithms.
 */




# 26 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
# 1 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"
/* assert.h: ANSI 'C' (X3J11 Oct 88) library header section 4.2 */
/* Copyright (C) Codemist Ltd., 1988-1993                       */
/* Copyright 1991-1993 ARM Limited. All rights reserved.        */
/* version 0.04 */

/*
 * RCS $Revision$
 * Checkin $Date$
 * Revising $Author: drodgman $
 */

/*
 * The assert macro puts diagnostics into programs. When it is executed,
 * if its argument expression is false, it writes information about the
 * call that failed (including the text of the argument, the name of the
 * source file, and the source line number - the latter are respectively
 * the values of the preprocessing macros __FILE__ and __LINE__) on the
 * standard error stream. It then calls the abort function.
 * If its argument expression is true, the assert macro returns no value.
 */

/*
 * Note that <assert.h> may be included more that once in a program with
 * different setting of NDEBUG. Hence the slightly unusual first-time
 * only flag.
 */

# 43 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"
    extern __declspec(__nothrow) __declspec(__noreturn) void abort(void);
    extern __declspec(__nothrow) __declspec(__noreturn) void __aeabi_assert(const char *, const char *, int) __attribute__((__nonnull__(1,2)));
# 53 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"

# 77 "C:\\Program Files\\DS-5 v5.26.2\\sw\\ARMCompiler5.06u4\\bin\\..\\include\\assert.h"





/* end of assert.h */

# 27 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

# 34 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

# 44 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

# 54 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

/* Define this to 1 if you do not need add() etc. to set the saturation flag */




/* Define this to 1 if you believe all shift counts are in the range [-255,255] */










#pragma recognize_itu_functions /* enable vectorization of ITU functions */



typedef union {
  struct {
    int _dnm:27;
    int Q:1;
    int V:1;
    int C:1;
    int Z:1;
    int N:1;
  } b;
  unsigned int word;
} _ARM_PSR;

# 102 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

register _ARM_PSR _apsr_for_q __asm("apsr");


# 185 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"

static __forceinline int *_arm_global_carry(void) {
    static int c;
    return &c;
}



/*
 * Convert a 32-bit signed integer into a 16-bit signed integer by
 * saturation.
 */
static __forceinline int16_t saturate(int32_t x)
{

    return (int16_t)__ssat(x, 16);
# 207 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
}

/*
 * Add two 16-bit signed integers with saturation.
 */
static __forceinline int16_t add(int16_t x, int16_t y)
{

    return (int16_t)__qadd16(x, y);



}

/*
 * Subtract one 16-bit signed integer from another with saturation.
 */
static __forceinline int16_t sub(int16_t x, int16_t y)
{

    return (int16_t)__qsub16(x, y);



}

/*
 * Absolute value of a 16-bit signed integer. Saturating, so
 * abs(-0x8000) becomes +0x7FFF.
 */
static __forceinline int16_t abs_s(int16_t x)
{
    if (x >= 0)
        return x;

    return (int16_t)__qsub16(0, x);
# 249 "\\aud_sw\\AuC\\inc\\dspfns_copy.h"
}

/*
 * Shift a 16-bit signed integer left (or right, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int16_t shl(int16_t x, int16_t shift)
{
    if (shift <= 0 || x == 0) {



        return (int16_t) (x >> (-shift));
    }
    if (shift > 15)
        shift = 16;
    return saturate(x << shift);
}

/*
 * Shift a 16-bit signed integer right (or left, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int16_t shr(int16_t x, int16_t shift)
{
    if (shift >= 0 || x == 0) {



        return (int16_t) (x >> shift);
    }
    if (shift < -15)
        shift = -16;
    return saturate(x << (-shift));
}

/*
 * Multiply two 16-bit signed integers, shift the result right by
 * 15 and saturate it. (Saturation is only necessary if both inputs
 * were -0x8000, in which case the result "should" be 0x8000 and is
 * saturated to 0x7FFF.)
 */
static __forceinline int16_t mult(int16_t x, int16_t y)
{
    return (int16_t)(__qdbl(x*y) >> 16);
}

/*
 * Multiply two 16-bit signed integers to give a 32-bit signed
 * integer. Shift left by one, and saturate the result. (Saturation
 * is only necessary if both inputs were -0x8000, in which case the
 * result "should" be 0x40000000 << 1 = +0x80000000, and is
 * saturated to +0x7FFFFFFF.)
 */
static __forceinline int32_t L_mult(int16_t x, int16_t y)
{
    return __qdbl(x*y);
}

/*
 * Negate a 16-bit signed integer, with saturation. (Saturation is
 * only necessary when the input is -0x8000.)
 */
static __forceinline int16_t negate(int16_t x)
{

    return (int16_t)__qsub16(0, x);





}

/*
 * Return the top 16 bits of a 32-bit signed integer.
 */
static __forceinline int16_t extract_h(int32_t x)
{
    return (int16_t) (x >> 16);
}

/*
 * Return the bottom 16 bits of a 32-bit signed integer, with no
 * saturation, just coerced into a two's complement 16 bit
 * representation.
 */
static __forceinline int16_t extract_l(int32_t x)
{
    return (int16_t) x;
}

/*
 * Divide a 32-bit signed integer by 2^16, rounding to the nearest
 * integer (round up on a tie). Equivalent to adding 0x8000 with
 * saturation, then shifting right by 16.
 */
static __forceinline int16_t round(int32_t x)
{
    return extract_h(__qadd(x, 0x8000));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and add to
 * another 32-bit integer with saturation.
 * 
 * Note the intermediate saturation operation in the definition:
 * 
 *    L_mac(-1, -0x8000, -0x8000)
 * 
 * will give 0x7FFFFFFE and not 0x7FFFFFFF:
 *    the unshifted product is:   0x40000000
 *    shift left with saturation: 0x7FFFFFFF
 *    add to -1 with saturation:  0x7FFFFFFE
 */
static __forceinline int32_t L_mac(int32_t accumulator, int16_t x, int16_t y)
{
    return __qadd(accumulator, __qdbl(x*y));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and subtract
 * from another 32-bit integer with saturation.
 * 
 * Note the intermediate saturation operation in the definition:
 * 
 *    L_msu(1, -0x8000, -0x8000)
 * 
 * will give 0x80000002 and not 0x80000001:
 *    the unshifted product is:         0x40000000
 *    shift left with saturation:       0x7FFFFFFF
 *    subtract from 1 with saturation:  0x80000002
 */
static __forceinline int32_t L_msu(int32_t accumulator, int16_t x, int16_t y)
{
    return __qsub(accumulator, __qdbl(x*y));
}

/*
 * Add two 32-bit signed integers with saturation.
 */
static __forceinline int32_t L_add(int32_t x, int32_t y)
{
    return __qadd(x, y);
}

/*
 * Subtract one 32-bit signed integer from another with saturation.
 */
static __forceinline int32_t L_sub(int32_t x, int32_t y)
{
    return __qsub(x, y);
}


/*
 * Negate a 32-bit signed integer, with saturation. (Saturation is
 * only necessary when the input is -0x80000000.)
 */
static __forceinline int32_t L_negate(int32_t x)
{
    return __qsub(0, x);
}

/*
 * Multiply two 16-bit signed integers, shift the result right by
 * 15 with rounding, and saturate it. (Saturation is only necessary
 * if both inputs were -0x8000, in which case the result "should"
 * be 0x8000 and is saturated to 0x7FFF.)
 */
static __forceinline int16_t mult_r(int16_t x, int16_t y)
{
    return (int16_t)(__qdbl(x*y + 0x4000) >> 16);
}

/*
 * Return the number of bits of left shift needed to arrange for a
 * 16-bit signed integer to have value >= 0x4000 or <= -0x4001.
 * 
 * Returns 0 if x is zero (following C reference implementation).
 */
static __forceinline int16_t norm_s(int16_t x)
{
    return __clz(x ^ ((int32_t)x << 17)) & 15;
}

/*
 * Return the number of bits of left shift needed to arrange for a
 * 32-bit signed integer to have value >= 0x40000000 (if +ve)
 * or <= -0x40000001 (if -ve).
 * 
 * Returns 0 if x is zero (following C reference implementation).
 */
static __forceinline int16_t norm_l(int32_t x)
{
    return __clz(x ^ (x << 1)) & 31;
}

/*
 * Shift a 32-bit signed integer left (or right, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int32_t L_shl(int32_t x, int16_t shift)
{
    if (shift <= 0) {



        return x >> (-shift);
    }
    if (shift <= norm_l(x) || x == 0)
        return x << shift;
    return __qdbl((x < 0) ? (~0x7fffffff) : 2147483647);
}

/*
 * Shift a 32-bit signed integer right (or left, if the shift count
 * is negative). Saturate on overflow.
 */
static __forceinline int32_t L_shr(int32_t x, int16_t shift)
{
    if (shift >= 0) {



        return x >> shift;
    }
    if ((-shift) <= norm_l(x) || x == 0)
        return x << (-shift);
    return __qdbl((x < 0) ? (~0x7fffffff) : 2147483647);
}

/*
 * Shift a 16-bit signed integer right, with rounding. Shift left
 * with saturation if the shift count is negative.
 */
static __forceinline int16_t shr_r(int16_t x, int16_t shift)
{
    if (shift == 0 || x == 0)
        return (int16_t)x;
    if (shift > 0) {



        return (int16_t) (((x >> (shift-1)) + 1) >> 1);
    }
    if (shift < -15)
        shift = -16;
    return saturate(x << (-shift));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and add to
 * another 32-bit integer with saturation (like L_mac). Then shift
 * the result right by 15 bits with rounding (like round).
 */
static __forceinline int16_t mac_r(int32_t accumulator, int16_t x, int16_t y)
{
    return round(L_mac(accumulator, x, y));
}

/*
 * Multiply two 16-bit signed integers together to give a 32-bit
 * signed integer, shift left by one with saturation, and subtract
 * from another 32-bit integer with saturation (like L_msu). Then
 * shift the result right by 15 bits with rounding (like round).
 */
static __forceinline int16_t msu_r(int32_t accumulator, int16_t x, int16_t y)
{
    return round(L_msu(accumulator, x, y));
}

/*
 * Shift a 16-bit signed integer left by 16 bits to generate a
 * 32-bit signed integer. The bottom 16 bits are zeroed.
 */
static __forceinline int32_t L_deposit_h(int16_t x)
{
    return ((int32_t)x) << 16;
}

/*
 * Sign-extend a 16-bit signed integer by 16 bits to generate a
 * 32-bit signed integer.
 */
static __forceinline int32_t L_deposit_l(int16_t x)
{
    return (int32_t)x;
}

/*
 * Shift a 32-bit signed integer right, with rounding. Shift left
 * with saturation if the shift count is negative.
 */
static __forceinline int32_t L_shr_r(int32_t x, int16_t shift)
{
    if (shift == 0 || x == 0)
        return x;
    if (shift > 0) {



        int32_t x2 = x >> (shift-1);

        return (x2 >> 1) + (x2 & 1);
    }
    if (-shift <= norm_l(x) || x == 0)
        return x << (-shift);
    return __qdbl((x < 0) ? (~0x7fffffff) : 2147483647);
}

/*
 * Absolute value of a 32-bit signed integer. Saturating, so
 * abs(-0x80000000) becomes +0x7FFFFFFF.
 */
static __forceinline int32_t L_abs(int32_t x)
{
    if (x >= 0)
        return x;
    else
        return __qsub(0, x);
}

/*
 * Return a saturated value appropriate to the most recent carry-
 * affecting operation (L_add_c, L_macNs, L_sub_c, L_msuNs).
 * 
 * In other words: return the argument if the Q flag is clear.
 * Otherwise, return -0x80000000 or +0x7FFFFFFF depending on
 * whether the Carry flag is set or clear (respectively).
 */
static __forceinline int32_t L_sat(int32_t x)
{
    if (_apsr_for_q . b . Q) {
        _apsr_for_q . b . Q = 0;
        x = (int32_t)((uint32_t)2147483647 + (*_arm_global_carry()));
        (*_arm_global_carry()) = 0;
    }
    return x;
}





# 6 "\\aud_sw\\AuC\\inc\\basic_op_audio.h"
# 25 "\\aud_sw\\AudioService\\src\\acm_audio_effect.c"







typedef struct eq_effect_state {
    int16_t x0;
    int16_t x1;
    int32_t y1;
    int32_t y2;
} eq_effect_state_t;

typedef struct audio_effect {
    audio_effect_config_t config;
    eq_effect_state_t eq_state[(5)];
    int32_t target_gain_db;
    int32_t target_gain_factor;
    int32_t gain_ramp_step;
    int32_t ramp_step;
    int32_t ramp_frame_num;
    int32_t gain_ramp_level;
    int32_t mute_ramp_level;
    uint8_t gain_ramping;
    uint8_t mute_ramping;
    uint8_t muted;
}audio_effect_t;

static const int32_t gain_factor_tbl[((100) - (1) + 1)][2] = {
    { 0x00000207,    0xffffdc00 },
    { 0x00000225,    0xffffdc7d },
    { 0x00000244,    0xffffdcf9 },
    { 0x00000266,    0xffffdd75 },
    { 0x00000289,    0xffffddf1 },
    { 0x000002ae,    0xffffde6d },
    { 0x000002d5,    0xffffdee9 },
    { 0x000002ff,    0xffffdf65 },
    { 0x0000032b,    0xffffdfe1 },
    { 0x0000035a,    0xffffe05e },
    { 0x0000038b,    0xffffe0da },
    { 0x000003bf,    0xffffe156 },
    { 0x000003f6,    0xffffe1d2 },
    { 0x00000431,    0xffffe24e },
    { 0x0000046e,    0xffffe2ca },
    { 0x000004af,    0xffffe346 },
    { 0x000004f4,    0xffffe3c2 },
    { 0x0000053d,    0xffffe43f },
    { 0x0000058a,    0xffffe4bb },
    { 0x000005db,    0xffffe537 },
    { 0x00000631,    0xffffe5b3 },
    { 0x0000068d,    0xffffe62f },
    { 0x000006ed,    0xffffe6ab },
    { 0x00000753,    0xffffe727 },
    { 0x000007be,    0xffffe7a3 },
    { 0x00000830,    0xffffe820 },
    { 0x000008a8,    0xffffe89c },
    { 0x00000928,    0xffffe918 },
    { 0x000009ae,    0xffffe994 },
    { 0x00000a3d,    0xffffea10 },
    { 0x00000ad3,    0xffffea8c },
    { 0x00000b72,    0xffffeb08 },
    { 0x00000c1a,    0xffffeb84 },
    { 0x00000ccc,    0xffffec00 },
    { 0x00000d88,    0xffffec7d },
    { 0x00000e4f,    0xffffecf9 },
    { 0x00000f22,    0xffffed75 },
    { 0x00001000,    0xffffedf1 },
    { 0x000010eb,    0xffffee6d },
    { 0x000011e4,    0xffffeee9 },
    { 0x000012eb,    0xffffef65 },
    { 0x00001401,    0xffffefe1 },
    { 0x00001527,    0xfffff05e },
    { 0x0000165e,    0xfffff0da },
    { 0x000017a7,    0xfffff156 },
    { 0x00001902,    0xfffff1d2 },
    { 0x00001a72,    0xfffff24e },
    { 0x00001bf6,    0xfffff2ca },
    { 0x00001d91,    0xfffff346 },
    { 0x00001f44,    0xfffff3c2 },
    { 0x0000210f,    0xfffff43f },
    { 0x000022f5,    0xfffff4bb },
    { 0x000024f7,    0xfffff537 },
    { 0x00002716,    0xfffff5b3 },
    { 0x00002955,    0xfffff62f },
    { 0x00002bb4,    0xfffff6ab },
    { 0x00002e37,    0xfffff727 },
    { 0x000030de,    0xfffff7a3 },
    { 0x000033ac,    0xfffff820 },
    { 0x000036a3,    0xfffff89c },
    { 0x000039c6,    0xfffff918 },
    { 0x00003d18,    0xfffff994 },
    { 0x00004099,    0xfffffa10 },
    { 0x0000444f,    0xfffffa8c },
    { 0x0000483b,    0xfffffb08 },
    { 0x00004c60,    0xfffffb84 },
    { 0x000050c3,    0xfffffc00 },
    { 0x00005566,    0xfffffc7d },
    { 0x00005a4d,    0xfffffcf9 },
    { 0x00005f7c,    0xfffffd75 },
    { 0x000064f7,    0xfffffdf1 },
    { 0x00006ac3,    0xfffffe6d },
    { 0x000070e4,    0xfffffee9 },
    { 0x0000775f,    0xffffff65 },
    { 0x00007e39,    0xffffffe1 },
    { 0x00008578,    0x0000005d },
    { 0x00008d22,    0x000000d9 },
    { 0x0000953c,    0x00000155 },
    { 0x00009dcd,    0x000001d1 },
    { 0x0000a6dd,    0x0000024d },
    { 0x0000b071,    0x000002c9 },
    { 0x0000ba92,    0x00000345 },
    { 0x0000c548,    0x000003c1 },
    { 0x0000d09b,    0x0000043e },
    { 0x0000dc95,    0x000004ba },
    { 0x0000e93f,    0x00000536 },
    { 0x0000f6a3,    0x000005b2 },
    { 0x000104cb,    0x0000062e },
    { 0x000113c4,    0x000006aa },
    { 0x00012399,    0x00000726 },
    { 0x00013456,    0x000007a2 },
    { 0x0001460a,    0x0000081f },
    { 0x000158c1,    0x0000089b },
    { 0x00016c8c,    0x00000917 },
    { 0x00018179,    0x00000993 },
    { 0x0001979b,    0x00000a0f },
    { 0x0001af01,    0x00000a8b },
    { 0x0001c7bf,    0x00000b07 },
    { 0x0001e1e9,    0x00000b83 },
    { 0x0001fd93,    0x00000c00 },
};

void audio_effect_init(void) {
# 165 "\\aud_sw\\AudioService\\src\\acm_audio_effect.c"
}

static int32_t mult32(int32_t v1, int32_t v2, int32_t q) {
    int32_t result = (int32_t)(((int64_t)v1 * v2 + (int64_t)(1 << (q - 1))) >> q);
    return result;
}

static void biquard(int16_t* data, const eq_effect_params_t* params, eq_effect_state_t* state, uint32_t frame_length) {
    int16_t shift = params->a[0];
    int16_t x0 = state->x0;
    int16_t x1 = state->x1;
    int32_t y1 = state->y1;
    int32_t y2 = state->y2;
    uint32_t i = 0;
    for (i = 0; i < frame_length; i++) {
        int32_t  acc0, acc1;
        acc0 = params->b[0] * data[i] + params->b[1] * x0 + params->b[2] * x1;
        acc0 -= ((y1 >> 16) * params->a[1] + (y2 >> 16) * params->a[2]);
        acc1 = ((y1 & 0xFFFF) * params->a[1] + (y2 & 0xFFFF) * params->a[2]);
        acc1 >>= 16;
        acc0 -= acc1;
        acc0 <<= (shift + 1);

        x1 = x0;
        x0 = data[i];
        y2 = y1;
        y1 = acc0;
        data[i] = saturate(acc0 >> 15);
    }

    state->x1 = x1;
    state->x0 = x0;
    state->y2 = y2;
    state->y1 = y1;
}

int audio_effect_create(const audio_effect_config_t* config, audio_effect_handle* handle) {
    if (!handle || !config || config->rate == 0 || config->frame_length == 0)
        return -1;

    audio_effect_t* effect = (audio_effect_t*)malloc(sizeof(audio_effect_t));
    if (effect) {
        memset(effect, 0, sizeof(audio_effect_t));
        memcpy(&effect->config, config, sizeof(audio_effect_config_t));
        audio_effect_ramp_ctrl((audio_effect_handle)effect, config->ramp_frames);
        audio_effect_gain_ctrl((audio_effect_handle)effect, config->target_gain);
        if (config->gain_ramp_off) {
            effect->gain_ramping = 0;
        }

        *handle = (audio_effect_handle)effect;
        return 0;
    }

    return -2;
}

int audio_effect_run(audio_effect_handle handle, const int16_t* input, int16_t* output) {
    if (handle && input && output) {
        audio_effect_t* effect = (audio_effect_t*)handle;
        int32_t gain_factor = effect->target_gain_factor;
        uint32_t i = 0, frame_length = effect->config.frame_length;

        // eq
        memcpy(output, input, sizeof(int16_t) * frame_length);
        if (effect->config.eq.ctrl > 0) {
            int i = 0;
            for (i = 0; i < (5); i++) {
                if (effect->config.eq.ctrl & (1 << i)) {
                    biquard(output, &effect->config.eq.params[i], &effect->eq_state[i], frame_length);
                }
            }
        }

        // volume
        if (effect->gain_ramping) {
            for (i = 0; i < frame_length; i++) {
                int32_t v = mult32(output[i], effect->gain_ramp_level, (15));
                int16_t sample_value_s16 = saturate(v);
                output[i] = sample_value_s16;

                if (effect->gain_ramp_level < gain_factor) {
                    effect->gain_ramp_level += effect->gain_ramp_step;
                    if (effect->gain_ramp_level > gain_factor) {
                        effect->gain_ramp_level = gain_factor;
                        effect->gain_ramping = 0;
                    }
                }
                else {
                    effect->gain_ramp_level -= effect->gain_ramp_step;
                    if (effect->gain_ramp_level < gain_factor) {
                        effect->gain_ramp_level = gain_factor;
                        effect->gain_ramping = 0;
                    }
                }
            }
        }
        else {
            for (i = 0; i < frame_length; i++) {
                int32_t v = mult32(output[i], gain_factor, (15));
                int16_t sample_value_s16 = saturate(v);
                output[i] = sample_value_s16;
            }
        }

        // mute
        if (effect->mute_ramping) {
            for (i = 0; i < frame_length; i++) {
                output[i] = mult32(output[i], effect->mute_ramp_level, (15));
                if (effect->muted) {
                    effect->mute_ramp_level -= effect->ramp_step;
                    if (effect->mute_ramp_level < 0) {
                        effect->mute_ramp_level = 0;
                        effect->mute_ramping = 0;
                    }
                }
                else {
                    effect->mute_ramp_level += effect->ramp_step;
                    if (effect->mute_ramp_level > (1 << (15))) {
                        effect->mute_ramp_level = (1 << (15));
                        effect->mute_ramping = 0;
                    }
                }
            }
        }

        if (!effect->mute_ramping && effect->muted) {
            memset(output, 0, frame_length * sizeof(int16_t));
        }
        return 0;
    }

    return -1;
}

int audio_effect_gain_ctrl(audio_effect_handle handle, int32_t gain) {
    if (handle) {
        audio_effect_t* effect = (audio_effect_t*)handle;
        size_t gain_index = 0;
        int32_t gain_set = 0;
        for (gain_index = 0; gain_index < sizeof(gain_factor_tbl) / sizeof(gain_factor_tbl[0]); gain_index++) {
            if (gain_factor_tbl[gain_index][1] >= gain)
                break;
        }
        if (gain_index >= sizeof(gain_factor_tbl) / sizeof(gain_factor_tbl[0]))
            gain_index = sizeof(gain_factor_tbl) / sizeof(gain_factor_tbl[0]) - 1;

        gain_set = gain_factor_tbl[gain_index][1];
        effect->target_gain_db = gain_set;
        effect->target_gain_factor = gain_factor_tbl[gain_index][0];
        if (effect->ramp_frame_num <= 0)
            effect->gain_ramp_step = (effect->target_gain_factor - effect->gain_ramp_level) / ((2) * effect->config.frame_length);
        else
            effect->gain_ramp_step = (effect->target_gain_factor - effect->gain_ramp_level) / (effect->ramp_frame_num * effect->config.frame_length);
        if (effect->gain_ramp_step == 0)
            effect->gain_ramp_step = 1;
        effect->gain_ramping = 1;
        return 0;
    }

    return -1;
}

int audio_effect_mute_ctrl(audio_effect_handle handle, int32_t mute) {
    if (handle) {
        audio_effect_t* effect = (audio_effect_t*)handle;
        if (effect->ramp_frame_num <= 0)
            effect->ramp_step = (1 << (15)) / ((2) * effect->config.frame_length);
        else
            effect->ramp_step = (1 << (15)) / (effect->ramp_frame_num * effect->config.frame_length);
        if (effect->ramp_step == 0)
            effect->ramp_step = 1;
        if (mute) {
            effect->mute_ramp_level = (1 << (15));
        }
        else {
            effect->mute_ramp_level = 0;
        }
        effect->muted = (mute ? 1 : 0);
        effect->mute_ramping = 1;
        return 0;
    }

    return -1;
}

int audio_effect_ramp_ctrl(audio_effect_handle handle, int32_t num) {
    if (handle) {
        audio_effect_t* effect = (audio_effect_t*)handle;
        effect->ramp_frame_num = num;
        return 0;
    }

    return -1;
}

int audio_effect_eq_ctrl(audio_effect_handle handle, const audio_effect_eq_t* eq) {
    if (handle) {
        audio_effect_t* effect = (audio_effect_t*)handle;
        memcpy(&effect->config.eq, eq, sizeof(audio_effect_eq_t));
        memset(&effect->eq_state, 0, sizeof(eq_effect_state_t));
        return 0;
    }

    return -1;
}

int audio_effect_destroy(audio_effect_handle handle) {
    if (handle) {
        audio_effect_t* effect = (audio_effect_t*)handle;
        free(effect);
    }

    return -1;
}
