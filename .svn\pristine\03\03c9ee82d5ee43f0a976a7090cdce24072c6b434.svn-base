/******************************************************************************
 *
 *  (C)Copyright ASRMicro. All Rights Reserved.
 *
 *  THIS IS UNPUBLISHED PROPRIETARY SOURCE CODE OF ASRMicro.
 *  The copyright notice above does not evidence any actual or intended
 *  publication of such source code.
 *  This Module contains Proprietary Information of ASRMicro and should be
 *  treated as Confidential.
 *  The information in this file is provided for the exclusive use of the
 *  licensees of ASRMicro.
 *  Such users have the right to use, modify, and incorporate this code into
 *  products for purposes authorized by the license agreement provided they
 *  include this notice and the associated copyright notice with any such
 *  product.
 *  The information in this file is provided "AS IS" without warranty.
 *
 ******************************************************************************/

#include <string.h>
#include <stdlib.h>
#include "audio_file.h"
#include "FDI_EXT.h"
#include "FDI_TYPE.h"
#include "FDI_FILE.h"
#include "utils.h"
#include "audio_def.h"
#include "gbl_types.h"

typedef struct {
    FILE_ID mFlashFp;
    unsigned int mSdFp;
    int mIsSdFile;
}COMMON_FILE_ID;

#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
extern unsigned int  FAT_fopen(const char *filename_ptr, const char *mode);
extern size_t FAT_fread(void *buffer_ptr, size_t element_size, size_t count, unsigned int stream);
extern int FAT_fseek(unsigned int stream, long offset, int wherefrom);
extern int FAT_fclose(unsigned int stream);
extern size_t FAT_fwrite(const void *buffer_ptr, size_t element_size, size_t count, unsigned int stream);
#endif

AUDIO_FILE_ID common_fopen(const char *filename_ptr, const char *mode)
{
    COMMON_FILE_ID* pFp = 0;

    pFp = (COMMON_FILE_ID*)malloc(sizeof(COMMON_FILE_ID));
    if (!pFp) { return 0; }
    char device = 'C';	//default device FLASH

    pFp->mFlashFp = 0;
    pFp->mSdFp = 0;
    pFp->mIsSdFile = 0;

    if ((filename_ptr[1] == ':') && ((filename_ptr[2] == '/') || (filename_ptr[2] == '\\'))) {
        device = filename_ptr[0];
    }
    if (('D' == device) || ('d' == device)) {
        pFp->mIsSdFile = 1;
    }

    // if flash
    if (0 == pFp->mIsSdFile) {
        pFp->mFlashFp = FDI_fopen(filename_ptr, mode);
        if (!pFp->mFlashFp) {
            free(pFp);
            return 0;
        }
    }
#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
    else {
        pFp->mSdFp = FAT_fopen(filename_ptr, mode);
        if (!pFp->mSdFp) {
            free(pFp);
            return 0;
        }

    }
#endif
    return (AUDIO_FILE_ID)pFp;
}

int common_fclose(AUDIO_FILE_ID id)
{
    COMMON_FILE_ID* stream = (COMMON_FILE_ID*)id;
    if (!stream) {
        ASSERT(0);
        return -1;
    }

    if (0 == stream->mIsSdFile) {
        FDI_fclose(stream->mFlashFp);
    }
#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
    else {
        FAT_fclose(stream->mSdFp);
    }
#endif
    free(stream);
    return 0;
}

size_t common_fread(void *buffer_ptr, size_t element_size, size_t count, AUDIO_FILE_ID id)
{
    COMMON_FILE_ID* stream = (COMMON_FILE_ID*)id;
    if (!stream) {
        ASSERT(0);
        return 0;
    }

    if (0 == stream->mIsSdFile) {
        return FDI_fread(buffer_ptr, element_size, count, stream->mFlashFp);
    }
#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
    else {
        return FAT_fread(buffer_ptr, element_size, count, stream->mSdFp);
    }
#else
    return 0;
#endif
}

size_t common_fwrite(const void *buffer_ptr, size_t element_size, size_t count, AUDIO_FILE_ID id)
{
    COMMON_FILE_ID* stream = (COMMON_FILE_ID*)id;
    if (!stream) {
        ASSERT(0);
        return 0;
    }

    if (0 == stream->mIsSdFile) {
        return FDI_fwrite(buffer_ptr, element_size, count, stream->mFlashFp);
    }
#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
    else {
        return FAT_fwrite(buffer_ptr, element_size, count, stream->mSdFp);
    }
#else
    return 0;
#endif

}

int common_fseek(AUDIO_FILE_ID id, long offset, int wherefrom)
{
    COMMON_FILE_ID* stream = (COMMON_FILE_ID*)id;
    if (!stream) {
        ASSERT(0);
        return -1;
    }

    if (0 == stream->mIsSdFile) {
        return FDI_fseek(stream->mFlashFp, offset, wherefrom);
    }
#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
    else {
        int ret = FAT_fseek(stream->mSdFp, offset, wherefrom);
        if (ret > 0) { ret = 0; }
        return ret;
    }
#else
    return -1;
#endif
}

bool common_access(const char *name) {
    FILE_INFO fdiFileInfo;
    if (FDI_findfirst((char *)name, &fdiFileInfo) == 0) {
        FILE_ID fileID = FDI_fopen(name, "rb");
        if (fileID) {
            FDI_fclose(fileID);
            return true;
        }
    }

#if INTERNAL_CONFIG_SUPPORT_SDCARD_FILE == 1
    UINT32 hdl = 0;
    hdl = FAT_fopen(name, "rb");

    if (hdl) {
        FAT_fclose(hdl);
        return true;
    }
#endif

    return false;
}
